#!/bin/bash
set -ex

# 初始化环境变量
init_env() {
    # ************ 应用自定义配置 start ************
    APP_KEY=waimai_service_customer_server
    APP_PORT=8430
    BOOTSTRAP_MAIN=com.sankuai.meituan.waimai.customer.boot.CustomerServer

    APP_BASE=/opt/meituan/apps
    LOG_HOME=/var/sankuai/logs
    LOGFILE=/var/sankuai/logs/$APP_KEY.log
    APP_WORK_HOME=$APP_BASE/$APP_KEY/work
    HOST_NAME=`hostname`
    HOST_IP=`hostname -i | awk -F ' ' '{if(NF==1) print $1;else print $2 end}'`
    # ************ 应用自定义配置 end ************
}

# 解压jar包
unpack_jar(){
    if [ -z "$JAVA_VERSION" ]; then
        JAVA_HOME="/usr/local/java" #系统默认的java地址
    else
        JAVA_HOME="/usr/local/$JAVA_VERSION"
    fi

    mkdir -p $APP_WORK_HOME/classes && cd $APP_WORK_HOME/classes
    $JAVA_HOME/bin/jar xf $APP_WORK_HOME/*.jar

    echo "copy database.properties `pwd`"
    if [[ $HOST_NAME == dx-* || $HOST_NAME == set-dx-* ]]; then
        cat database.properties.production.dx | sed 's/${database}/waimai/g' > conf/database.properties
    elif [[ $HOST_NAME == yf-* || $HOST_NAME == set-yf-* ]]; then
        cat database.properties.production.yf | sed 's/${database}/waimai/g' > conf/database.properties
    else
        cat database.properties.production | sed 's/${database}/waimai/g' > conf/database.properties
    fi

    mkdir -p $APP_WORK_HOME/conf
    cat $APP_WORK_HOME/classes/log4j2.xml > $APP_WORK_HOME/log4j2.xml

    cd ..
}

# 前台启动程序
start_app() {
    echo "start app"

    VM_LOG=''
    VM_LOG=$VM_LOG" -Xloggc:$LOG_HOME/$APP_KEY.gc.log.$(date +%Y%m%d%H%M)"
    VM_LOG=$VM_LOG" -XX:ErrorFile=$LOG_HOME/$APP_KEY.vmerr.log.$(date +%Y%m%d%H%M)"
    VM_LOG=$VM_LOG" -XX:HeapDumpPath=$LOG_HOME/$APP_KEY.heaperr.log.$(date +%Y%m%d%H%M)"
    VM_PARAM='-server -Xmx8g -Xms8g -XX:SurvivorRatio=8 -XX:NewRatio=1 -XX:MetaspaceSize=640M -XX:MaxMetaspaceSize=1024M -XX:+DisableExplicitGC -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintCommandLineFlags -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:ParallelCMSThreads=4 -XX:+CMSClassUnloadingEnabled -XX:+UseCMSCompactAtFullCollection -XX:CMSFullGCsBeforeCompaction=1 -XX:CMSInitiatingOccupancyFraction=50 -XX:-OmitStackTraceInFastThrow'
    cpath=$APP_WORK_HOME/classes
    for file in `ls $APP_WORK_HOME/lib/*.jar`
    do
        cpath=$cpath:$file
    done

    exec $JAVA_HOME/bin/java $VM_LOG $VM_PARAM -Denvironment=$ENVIRONMENT -Dworkdir=$APP_WORK_HOME -Dapp.key=$APP_KEY -Dapp.type=webapp -Dapp.host=$HOST_NAME -Dapp.ip=$HOST_IP -Dapp.port=$APP_PORT -Dfile.encoding=UTF-8 -cp $cpath $BOOTSTRAP_MAIN >> $LOGFILE 2>&1

}

# main
init_env
unpack_jar
start_app
