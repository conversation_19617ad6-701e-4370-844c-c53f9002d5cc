<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--mtthrift连接池-->
    <bean id="wmEPushThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="40"/>
        <property name="minIdle" value="4"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="wmInboxSendThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="wmEPushThriftPoolConfig"/>
        <!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.waimai.thrift.push.client.inbox.service.WmInboxSendThriftService"/>
        <!-- service接口名 -->
        <property name="timeout" value="500"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="com.sankuai.waimai.e.customer"/>
        <!--设置octo上AppKey-->
        <property name="remoteAppkey" value="com.sankuai.waimai.push"/>
        <property name="remoteServerPort" value="9097"/>
    </bean>

</beans>