<?xml version="1.0" encoding="UTF-8" ?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <import resource="classpath:database/datasources.xml"/><!-- 引入datasource的相关配置 -->

    <bean id="multipleDataSource" class="com.sankuai.meituan.waimai.datasource.multi.TimedMultipleDataSource">
        <property name="targetDataSources">
            <map key-type="java.lang.String"> <!-- 数据源配置 -->
                <entry key="dbContractDataSource" value-ref="dbContractDataSource"/>
                <entry key="bladeDataSource" value-ref="bladeDataSource"/>
            </map>
        </property>
        <property name="defaultTargetDataSource" ref="dbContractDataSource"/> <!-- 默认数据源 -->
    </bean>


    <bean id="multiSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <!--dataSource配置文件-->
        <property name="dataSource" ref="multipleDataSource"/>
        <!-- 配置扫描Mapper XML的位置,如果没有的可以删除,Mapper XML的暂时不支持 -->
        <property name="mapperLocations" value="classpath*:mybatis/**/*.xml"/>
        <!--支持对应package下的类起别名-->
        <!--配置后支持在对应的domain下的类添加@Alias注解，在Mapper中的resultType可以指定alias-->
        <property name="typeAliasesPackage" value="com.sankuai.meituan.waimai.customer.domain"/>
        <!-- <property name="configLocation" value="classpath:mybatis-filter.xml"></property> -->
        <property name="plugins">
            <array>
                <bean name="pageHelper" class="com.github.pagehelper.PageHelper">
                    <property name="properties">
                        <value>
                            dialect=mysql
                            reasonable=true
                        </value>
                    </property>
                </bean>
                <bean name="masterSlaveHelper" class="com.sankuai.meituan.waimai.customer.util.MasterSlaveHelper"/>
                <bean name="encryptionExecutorInterceptor" class="com.sankuai.meituan.waimai.customer.service.sign.encryption.EncryptionExecutorInterceptor"/>
                <bean name="encryptionResultInterceptor" class="com.sankuai.meituan.waimai.customer.service.sign.encryption.EncryptionResultInterceptor"/>
                <bean name="settleResultSetInterceptor" class="com.sankuai.meituan.waimai.customer.settle.interceptor.SettleResultSetInterceptor"/>
            </array>
        </property>
    </bean>

    <!--sqlSessionFactory代理-->
    <bean id="sqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg ref="multiSqlSessionFactory"/>
    </bean>

    <!-- 配置扫描Mapper接口的包路径 -->
    <bean class="com.dianping.zebra.dao.mybatis.ZebraMapperScannerConfigurer">
        <!-- 如果是多个包名可用",; \t\n"中任意符号分隔开，详见：MapperScannerConfigurer[269行] -->
        <property name="basePackage" value="com.sankuai.meituan.waimai.customer.dao,com.sankuai.meituan.waimai.customer.contract.dao,com.sankuai.meituan.waimai.customer.mq.dao, com.sankuai.meituan.waimai.customer.settle.dao"/>
        <property name="sqlSessionTemplateBeanName" value="sqlSessionTemplate"/>
    </bean>

    <!--事务-->
    <bean id="transactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager"
          p:dataSource-ref="dbContractDataSource"/>
    <tx:annotation-driven transaction-manager="transactionManager"/>

    <bean id="transactionTemplate"
          class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager">
            <ref bean="transactionManager"/>
        </property>
    </bean>
</beans>