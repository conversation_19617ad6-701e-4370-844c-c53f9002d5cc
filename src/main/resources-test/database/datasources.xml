<?xml version="1.0" encoding="UTF-8" ?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <!-- 合同新库 -->
    <bean id="dbContractDataSource" class="com.dianping.zebra.shard.jdbc.ShardDataSource" init-method="init" destroy-method="close">
		<property name="ruleName" value="waimai_sign_test"/>
		<!-- 必配。指定唯一确定数据库的key -->
		<property name="defaultDatasource" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('database.dbContract.jdbcRef', 'waimai_poi_waimai_test')}" />
		<!-- 选配。指定底层使用的连接池类型，支持"c3p0","tomcat-jdbc","druid","hikaricp","dbcp2"和"dbcp"，推荐使用"tomcat-jdbc"，默认值为"c3p0" -->
		<property name="poolType" value="druid" />
		<!-- 选配。指定连接池的最小连接数，默认值是5。 -->
		<property name="minPoolSize" value="5" />
		<!-- 选配。指定连接池的最大连接数，默认值是20。 -->
		<property name="maxPoolSize" value="100" />
		<!-- 选配。指定连接池的初始化连接数，默认值是5。 -->
		<property name="initialPoolSize" value="5" />
		<!-- 选配。指定连接池的获取连接的超时时间，默认值是1000。 -->
		<property name="checkoutTimeout" value="1000" />
		<!--以下配置全部可以选配 -->
		<property name="maxIdleTime" value="1800" />
		<property name="idleConnectionTestPeriod" value="60" />
		<property name="acquireRetryAttempts" value="3" />
		<property name="acquireRetryDelay" value="300" />
		<property name="maxStatements" value="0" />
		<property name="maxStatementsPerConnection" value="100" />
		<property name="numHelperThreads" value="6" />
		<property name="maxAdministrativeTaskTime" value="5" />
		<property name="preferredTestQuery" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('jdbc.pool.validationQuery', 'select 1')}" />
		<property name="isJdbcrefGroup" value="false" />
    </bean>

	<!-- blade -->
	<bean id="bladeDataSource" class="com.dianping.zebra.group.blade.jdbc.BladeDataSource" init-method="init" destroy-method="close">
		<!-- 必配。指定唯一确定数据库的key -->
		<property name="jdbcRef" value="inf_blade_blade2function2_blade_wm_global_econtract_info_test" />
		<!-- 选配。指定底层使用的连接池类型，支持"c3p0","tomcat-jdbc","druid","hikaricp","dbcp2"和"dbcp"，推荐使用"tomcat-jdbc"，默认值为"c3p0" -->
		<property name="poolType" value="druid" />
		<!-- 以下配置全部为选配 -->
		<property name="initialPoolSize" value="5" />
		<property name="minPoolSize" value="5" />
		<property name="maxPoolSize" value="20" />
		<!-- 连接池的获取连接的超时时间，默认1000 -->
		<property name="checkoutTimeout" value="1000" />
		<property name="maxIdleTime" value="1800" />
		<property name="idleConnectionTestPeriod" value="60" />
		<property name="acquireRetryAttempts" value="3" />
		<property name="acquireRetryDelay" value="300" />
		<property name="maxStatements" value="0" />
		<property name="maxStatementsPerConnection" value="100" />
		<property name="numHelperThreads" value="6" />
		<property name="maxAdministrativeTaskTime" value="5" />
		<property name="preferredTestQuery" value="SELECT 1" />
		<property name="lazyInit" value="false"/>
		<property name="extraJdbcUrlParams" value="useUnicode=true&amp;zeroDateTimeBehavior=convertToNull&amp;characterEncoding=UTF-8&amp;connectTimeout=10000&amp;socketTimeout=10000&amp;useAffectedRows=true&amp;testOnBorrow=true&amp;validationQuery=select 1" />
	</bean>

</beans>
