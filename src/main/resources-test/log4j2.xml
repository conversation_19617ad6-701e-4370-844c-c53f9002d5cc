<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%d{yyyy/MM/dd HH:mm:ss.SSS} %t [%p] %c{1} (%F:%L) %msg%n %ex"/>
        </Console>

        <!--XMDFile异步磁盘日志配置示例-->
        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件。-->
        <!--目录为/opt/logs/${appkey} 或 /data/applogs/${appkey}，优先选用磁盘挂载目录-->
        <!--注意：fileName前会自动增加文件路径，只配置文件名即可-->
        <!--可选配置：ERROR日志、WARN日志单独输出到一个文件-->
        <XMDFile name="ERROR-LOG" fileName="${sys:app.key}.err.log" xmdFilePath="/var/sankuai/logs"
                 addAppkeyToFilePath="false">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>


        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，非阻塞模式-->
        <XMDFile name="file" fileName="${sys:app.key}.log" xmdFilePath="/var/sankuai/logs" includeLocation="true"
                 sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <!-- jmonitor相关日志，打印到单独的文件中，并使用异步日志 -->
        <XMDFile name="jmonitorappender" fileName="${sys:app.key}.jmonitor.log" xmdFilePath="/var/sankuai/logs"
                 timeBasedTriggeringInterval="1" addAppkeyToFilePath="false">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>
        <XMDFile name="jmonitorlogstoreappender" fileName="${sys:app.key}.jmonitor.logstore.log"
                 xmdFilePath="/var/sankuai/logs" timeBasedTriggeringInterval="1" addAppkeyToFilePath="false">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>


        <!--异常日志远程上报-->
        <Async name="AsyncErrorLog" blocking="false">  <!-- 当 blocking 为 false 时，超过最高 qps 时，不阻塞，直接抛弃数据。-->
            <AppenderRef ref="remoteErrorLog"/>
        </Async>
        <Scribe name="remoteErrorLog">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <LcLayout/>
        </Scribe>


        <Scribe name="ScribeAppender">
            <!-- 远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可以选择用如下方式自定义scribeCategory。scribeCategory优先级高于appkey-->
            <LcLayout/>
        </Scribe>
        <Async name="ScribeAsyncAppender" blocking="false">
            <AppenderRef ref="ScribeAppender"/>
        </Async>

        <!--        上报修改签约人KP信息埋点日志-->
        <AsyncScribe name="AsyncScribeAppender_com.sankuai.waimai.customer.kp.pc.update.signer" blocking="false">
            <Property name="scribeCategory">com.sankuai.waimai.customer.kp.pc.update.signer</Property>
            <LcLayout/>
        </AsyncScribe>

    </appenders>

    <loggers>
        <!-- 单独打印jmonitor -->
        <logger name="jmonitor" additivity="false" level="info">
            <appender-ref ref="jmonitorappender" />
        </logger>

        <logger name="jmonitorlogstore" additivity="false" level="info">
            <appender-ref ref="jmonitorlogstoreappender" />
        </logger>

        <!--        上报修改签约人KP信息埋点日志-->
        <!--想要上报到org.com.sankuai.waimai.customer.kp.pc.update.signer这个topic，代码中必须使用 private static final Logger scribelogger = LoggerFactory.getLogger("logger_com.sankuai.waimai.customer.kp.pc.update.signer"); -->
        <logger name="logger_com.sankuai.waimai.customer.kp.pc.update.signer" level="info" additivity="false">
            <appender-ref ref="AsyncScribeAppender_com.sankuai.waimai.customer.kp.pc.update.signer"/>
        </logger>

        <root level="info">
            <appender-ref ref="ERROR-LOG" />                <!--ERROR日志单独输出到一个文件-->
            <appender-ref ref="ScribeAsyncAppender" />          <!--日志传入远程日志中心 -->

            <appender-ref ref="AsyncErrorLog" />      <!--异常日志上报要添加到root-->
            <appender-ref ref="file" />
        </root>
    </loggers>
</configuration>