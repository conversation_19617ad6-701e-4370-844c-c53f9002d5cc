<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <!-- 行政区服务 -->
    <bean id="adminDivisionClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.gis.remote.service.thrift.TAdminDivisionService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.cos.mtgis"/> <!-- 目标 Server Appkey -->
        <!--实际环境服务发现-->
        <!--<property name="remoteServerPort" value="9004" />-->
        <!--DEV环境指定端口-->
        <property name="serverIpPorts" value="************:9104" />
        <property name="timeout" value="1000"/><!--超时时间ms-->
    </bean>

</beans>