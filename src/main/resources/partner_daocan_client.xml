<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="accountPoiSearchService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.nibmp.infra.amp.authorize.lib.service.AccountPoiSearchService"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.nibmerchant.amp.authorize"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="9000"/>
        <property name="timeout" value="5000"/>
    </bean>

    <bean id="coopQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.mtcoop.thrift.service.CoopQueryService"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.cos.mtcoop"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="9018" />
        <property name="timeout" value="5000"/>
    </bean>

    <bean id="frameCoopService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.mtcoop.thrift.service.FrameCoopService"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.cos.mtcoop"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="9009" />
        <property name="timeout" value="5000"/>
    </bean>

    <bean id="commonCoopService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.mtcoop.thrift.service.CommonCoopService"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.cos.mtcoop"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="9015"/>
        <property name="timeout" value="5000"/>
    </bean>


</beans>
