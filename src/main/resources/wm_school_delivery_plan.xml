<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <!-- 校园交付相关Bean -->
    <bean id="resourceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.waimai.saas.rotate.api.service.ResourceThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.wmcrmplatform.saas.resources"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="resourceRotateService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.waimai.saas.rotate.api.service.ResourceRotateService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.wmcrmplatform.saas.resources"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="deliverThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.DeliverThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.wmcrmplatform.saas.deliver"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="schoolInfoClient" class="com.sankuai.waimai.saas.rotate.campus.sdk.client.SchoolInfoClient">
        <constructor-arg index="0" ref="resourceThriftService"/>
        <constructor-arg index="1" ref="resourceRotateService"/>
    </bean>

    <bean id="contractApplyClient" class="com.sankuai.waimai.saas.rotate.campus.sdk.client.ContractApplyClient">
        <constructor-arg index="0" ref="resourceThriftService"/>
        <constructor-arg index="1" ref="resourceRotateService"/>
    </bean>

</beans>