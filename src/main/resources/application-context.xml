<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:crane="http://code.dianping.com/schema/crane"
       xmlns:pigeon="http://code.dianping.com/schema/pigeon"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
						   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
						   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
						   http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
                           http://code.dianping.com/schema/crane http://code.dianping.com/schema/crane/crane-1.0.xsd
                           http://code.dianping.com/schema/pigeon
                           http://code.dianping.com/schema/pigeon/pigeon-service-2.0.xsd">
    <aop:aspectj-autoproxy expose-proxy="true"/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <aop:config>
        <aop:aspect ref="tairLockAspect">
            <aop:pointcut id="tairLock"
                          expression="execution(@com.sankuai.meituan.waimai.customer.aspect.TairLock * com.sankuai.meituan.waimai..*(..))"/>
            <aop:around pointcut-ref="tairLock" method="doAround"/>
        </aop:aspect>
    </aop:config>

    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:/conf/database.properties</value>
                <value>classpath:/conf/config.properties</value>
                <value>classpath:/conf/common.properties</value>
            </list>
        </property>
    </bean>

    <bean class="com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil"/>
    <bean class="com.sankuai.meituan.waimai.util.jmonitor.JmonitorSpringBeanUtil"/>
    <!--强制确保jm所需各个资源的加载顺序-->
    <bean class="com.sankuai.meituan.waimai.customer.util.JmonitorInitializingBean">
        <property name="configName" value="jmonitor.properties"></property>
    </bean>

    <!--init SpringContext-->
    <bean id="springContext" class="com.sankuai.meituan.waimai.customer.service.sign.encryption.SpringContext"></bean>


    <util:properties id="dataSourceProps" location="classpath:/conf/database.properties"/>
    <context:component-scan base-package="com.sankuai.meituan"/>
    <crane:annotation-driven/>
    <pigeon:annotation package="com.sankuai.meituan"/>

    <import resource="classpath:database/multi-datasource.xml"/>
<!--    <import resource="classpath:waimaiConfig/waimai_service_notice_client.xml"/>-->
    <import resource="classpath:waimai_service_poi_client.xml"/>
<!--    <import resource="classpath:waimai_service_poi_flowline_client.xml"/>-->
<!--    <import resource="classpath:waimai_service_poimanager_client.xml"/>-->
    <import resource="classpath:waimai_service_poisearch_client.xml"/>
    <import resource="classpath:waimai_service_poiquery_client.xml"/>
    <import resource="classpath:waimaiConfig/waimai_service_infra_client.xml"/>

<!--    <import resource="classpath:waimai_service_poiaudit_client.xml"/>-->
    <import resource="classpath:group-m-tair.xml"/>
    <import resource="classpath:group-b-tair.xml"/>
<!--    <import resource="classpath:waimai_service_contractmanager_client.xml"/>-->
    <import resource="classpath:waimai_service_contract_client.xml"/>
<!--    <import resource="classpath:sg-api-sdk-context.xml"/>-->
    <import resource="classpath:spring-bean/statemachine.xml"/>
<!--    <import resource="classpath:waimai_service_audit_client.xml"/>-->
<!--    <import resource="classpath:waimaiConfig/waimai_service_agent_client.xml"/>-->
<!--    <import resource="classpath:waimai_service_poilogistics_client.xml"/>-->
    <!-- 千鹭-配送 -->
<!--    <import resource="classpath:waimai_service_herologistics_client.xml"/>-->
<!--    <import resource="classpath:waimaiConfig/waimai_service_oplog_client.xml"/>-->
<!--    <import resource="classpath:waimai_service_scm_bizsettle_client.xml"/>-->
<!--    <import resource="classpath:waimai_service_bizme_client.xml"/>-->
<!--    <import resource="classpath:waimai_service_poibrand_client.xml"/>-->
    <!--phf合同-->
    <import resource="classpath:tsp_phf_poi_contract_client.xml"/>

    <import resource="classpath:shangou_merchant_charging_client.xml"/>

    <import resource="classpath:spring-databus.xml"/>
    <!-- redis cache -->
    <import resource="classpath:spring-bean/spring-cache.xml"/>

    <import resource="spring-wallet.xml"/>
    <import resource="GisService-client.xml"/>
    <import resource="BankService-client.xml"/>
    <import resource="PushMessService-client.xml"/>
    <import resource="classpath:spring-bean/spring-mafka.xml"/>
    <import resource="classpath:sms-pegion.xml"/>
    <import resource="classpath:waimai_service_poibizflow_client.xml"/>

    <!--门店服务防腐层bean配置-->
    <import resource="classpath:poi_adaptor_client.xml"/>
    <!--合同服务防腐层bean配置-->
    <import resource="classpath:contract_adaptor_client.xml"/>
    <!--合同服务防腐层bean配置-->
    <import resource="classpath:logistic_adaptor_client.xml"/>
    <!--外部服务防腐层bean配置-->
    <import resource="classpath:util_adaptor_client.xml"/>
    <!-- 资质服务防腐层bean配置-->
<!--    <import resource="classpath:conf/waimai_service_qualification_client.xml"/>-->
    <import resource="classpath:waimai-heron-settle-client.xml"/>
<!--    <import resource="classpath:waimai_service_bizuser_client.xml"/>-->
<!--    <import resource="classpath:wm-poi-on-offline.xml"/>-->
    <!--灯塔，2个RPC服务-->
    <import resource="classpath:waimai-beacon-state-center-client.xml"/>
    <!-- 任务系统服务 -->
<!--    <import resource="classpath:waimaiConfig/waimai_service_crm_ticket_client.xml"/>-->
    <!--   com.sankuai.waimai.poimanager 一个rpc服务，无需改造 -->
    <import resource="classpath:waimai_service_poi_process_control_client.xml"/>
    <!--    todo end-->
<!--    <import resource="classpath:waimaiConfig/waimai_service_m_bizaggr_client.xml"/>-->
<!--    <import resource="classpath:waimai_service_poibaseinfo_client.xml"/>-->
<!--    <import resource="classpath:waimai_service_settle_query_client.xml"/>-->
    <!--斑马服务-->
    <import resource="classpath:banmaConfig/banma_open_poi_client.xml"/>

    <!-- 白名单运营配置-->
    <import resource="classpath:waimai_e_kayle_client.xml"/>

    <!--需查询外卖门店信息-->
    <import resource="classpath:waimai_service_poiquery_client.xml"/>

    <!--需查询门店经营品类信息-->
    <import resource="classpath:waimai_service_poiquery_extend_client.xml"/>

    <import resource="classpath:rateApproval/waimai_service_audit_rate_client.xml"/>

<!--    <import resource="classpath:banma_service_deliverycontract_adminbiz-sdk.xml"/>-->

    <!--标签-->
<!--    <import resource="classpath:waimai_e_operation_label_client.xml"/>-->

<!--    <import resource="classpath:shangou-e-msgmanager-client.xml"/>-->

    <!--账户中心bizauth-->
<!--    <import resource="classpath:waimai_service_bizauth_client.xml"/>-->
    <!--品牌服务-->
<!--    <import resource="classpath:waimai_e_scm_brand_client.xml"/>-->
    <!-- 新合同服务 -->
<!--    <import resource="classpath:waimai_e_heron_logistics_contract_client.xml"/>-->

    <!--校园交付服务-->
    <!-- <import resource="classpath:wm_school_delivery_plan.xml"/> -->
    <!--闪购服务-->
    <import resource="classpath:shangou_client.xml"/>
    <!-- UDB服务 -->
    <import resource="classpath:udb-open-thrift.xml"/>
    <!-- 大象通信服务 -->
    <import resource="classpath:service-ginfo-open.xml"/>

    <!--配送售卖网关服务-->
    <import resource="classpath:contract_gateway_client.xml"/>
    <import resource="classpath:partner_daocan_client.xml"/>
    <!--履约侧地图服务-->
    <import resource="classpath:banmaConfig/banma_service_aoi_support_set_client.xml"/>
    <!--履约侧聚合站点服务-->
    <import resource="classpath:banmaConfig/banma_service_dilverystaff_station_label_open.xml"/>
    <!--基础费率信息-->
    <import resource="classpath:waimai-heron-config-service-client.xml"/>
    <!--门店品类服务-->
    <import resource="classpath:wm_poi_category_config.xml"/>

    <import resource="classpath:database/multi-datasource-common.xml"/>
    <!-- 闪购低费率接口 -->
    <import resource="classpath:shangou_sgmerchant_compliance_sdk.xml"/>

    <import resource="classpath:phf_client.xml" />

    <bean id="kmsAuthDataSource" class="com.meituan.service.inf.kms.client.KmsAuthDataSource">
        <property name="appkey" value="com.sankuai.waimai.e.customer"/>
    </bean>
    <bean id="defaultAuthHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultAuthHandler">
        <property name="authDataSource" ref="kmsAuthDataSource"/>
    </bean>

    <bean id="customerProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
          destroy-method="close">
        <property name="namespace" value="waimai"/>
        <property name="appkey" value="com.sankuai.waimai.e.customer"/>
        <property name="topic"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('customer_state_notice_topic_name', 'customer.state.notice_dev')}"/>
    </bean>

    <bean id="wmCustomerKpThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerKpThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8430"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerContractThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8431"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerOplogThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerOplogThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerOplogThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8432"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8433"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerBrandThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerBrandThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerBrandThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8434"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerPoiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerPoiThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8435"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerContractVersionThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractVersionThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerContractVersionThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8436"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmEcontractSignThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmEcontractSignThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8437"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmEcontractSignManagerThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignManagerThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmEcontractSignManagerThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8438"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!-- 电子合同平台API -->
    <bean id="econtractAPIService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="maxResponseMessageBytes" value="32768000"/>
        <property name="remoteServerPort" value="9001"/>
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>
    <bean id="frameContractConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.econtrct.client.service.FrameContractConfigThriftService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="maxResponseMessageBytes" value="32768000"/>
        <property name="remoteServerPort" value="9008"/>
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>
    <!-- 电子合同系统H5页面信息 -->
    <bean id="econtractBizService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="maxResponseMessageBytes" value="32768000"/>
        <property name="remoteServerPort" value="9002"/>
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>

    <bean id="wmCustomerSyncThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerSyncThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerSyncThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8441"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerSettleThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettleThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettleThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8439"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmSettleManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettleManagerThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettleManagerThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8440"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerCommonThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerCommonThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerCommonThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8442"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>
    <!-- 由于AuthUserOperateFilter(模拟用户)使用到upmService, 所以需配置 -->
    <bean id="upmConfig" class="com.sankuai.meituan.auth.vo.UpmConfig">
        <property name="clientId" value="xianfu_waimai"/>
        <property name="secret" ref="kms.upmAuthService.secret"/>
        <property name="timeout" value="1000"/>  <!-- pigeon 超时（ms） -->
        <property name="timeoutRetry" value="true"/>  <!-- pigeon 超时重试开关 -->
        <property name="retries" value="5"/>  <!-- pigeon 超时重试次数 -->
    </bean>
    <bean id="upmService" class="com.sankuai.meituan.auth.service.UpmService">
        <property name="upmConfig" ref="upmConfig"/>
    </bean>

    <bean id="kms.upmAuthService.secret" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="waimai_m_beekeeper"/>
        <property name="name" value="upmAuthService"/>
    </bean>
    <bean id="wmCustomerEsThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerEsThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerEsThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8443"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmSettleWalletManagerThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettleWalletManagerThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettleWalletManagerThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8444"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>


    <bean class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.pay.mwallet.proxy.thrift.MwalletBizEventNotifyService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettleWalletChangeNotifyHandleService"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8445"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="cmsTemplateValidService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.cms.biz.CMSTemplateValidService"/>
        <property name="interfaceName" value="com.dianping.cms.biz.CMSTemplateValidService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="cmsService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/cmsService/cmsRemoteService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.cms.biz.CMSService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.conch.certify.tokenaccess.thrift.TokenAccessThriftService"/><!-- 接口名 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/> <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.conch.certify.tokenaccess"/> <!-- 目标Server Appkey -->
        <property name="remoteServerPort" value="3406"/>
        <property name="timeout" value="1000"/> <!-- 调用批量接口建议适当延长超时时间，具体根据一个批次的数据量而定 -->
    </bean>

    <bean id="wmSettlePoiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettlePoiThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettlePoiThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8446"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="nibCustomer" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibcus.inf.customer.client.service.CustomerThriftService"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.nibcus.inf.customer"/>
        <property name="timeout" value="3000"/>
        <property name="remoteUniProto" value="true"/>
        <property name="maxResponseMessageBytes" value="********"/>
        <property name="remoteServerPort" value="9000"/>
    </bean>

    <bean id="businessCustomerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibcus.inf.customer.client.service.BusinessCustomerThriftService"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.nibcus.inf.customer"/>
        <property name="timeout" value="3000"/>
        <property name="remoteUniProto" value="true"/>
        <property name="maxResponseMessageBytes" value="********"/>
        <property name="remoteServerPort" value="9000"/>
    </bean>

    <bean id="customerGovernThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.nibcus.inf.customer.client.service.CustomerGovernThriftService"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.nibcus.inf.customer"/>
        <property name="remoteServerPort" value="9000"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="3000"/>
    </bean>

    <bean id="wmCustomerContractExternalThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.external.WmCustomerContractExternalThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerContractExternalThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8448"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmEcontractSignExternalThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.external.WmEcontractSignExternalThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmEcontractSignExternalThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8449"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmAgreementThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.agreement.WmAgreementThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmAgreementThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8450"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerComplianceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerComplianceThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerComplianceThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8451"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--学校的处理类-->
    <bean id="wmSchoolThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSchoolThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8452"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--食堂的处理类-->
    <bean id="wmCanteenThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCanteenThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8453"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--交付的处理类-->
    <bean id="newWmSchoolDeliverThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.NewWmSchoolDeliverThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="newWmSchoolDeliverThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8467"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--承包商的处理类-->
    <bean id="wmContractorThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmContractorThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmContractorThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8454"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--公共方法的处理类-->
    <bean id="wmScCommonThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmScCommonThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmScCommonThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8455"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--日志方法的处理类-->
    <bean id="wmScLogThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmScLogThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmScLogThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8456"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerKpPoiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpPoiThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerKpPoiThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8457"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--食堂门店绑定的处理类-->
    <bean id="wmCanteenPoiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenPoiThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCanteenPoiThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8458"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--食堂门店对外暴露接口-->
    <bean id="wmScOuterThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmScOuterThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmScOuterThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8459"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--学校交付管理对外暴露接口-->
    <bean id="wmSchoolDeliveryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolDeliveryThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSchoolDeliveryThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8461"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--食堂档口管理对外暴露接口-->
    <bean id="wmCanteenStallThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenStallThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCanteenStallThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8462"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!--食堂引用公私海接口-->
    <bean id="schoolyardThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.waimaisales.highsea.client.schoolyard.service.SchoolyardThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/> <!--client自己的appkey -->
        <property name="remoteAppkey" value="com.sankuai.waimaisales.highsea"/>
        <property name="remoteServerPort" value="8001"/>
    </bean>


    <!-- 客户预认证接口bizId生成接入leaf -->
    <bean id="authIDGenService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.inf.leaf.thrift.IDGen"/> <!--leaf的 接口名 -->
        <property name="appKey"
                  value="com.sankuai.waimai.contractmanager"/> <!-- 本地appkey，目前使用contractmanager申请的leaf -->
        <property name="remoteAppkey" value="com.sankuai.inf.leaf.waimai"/>  <!-- leaf Appkey  -->
        <property name="timeout" value="500"/>  <!-- 超时时间为50ms  -->
    </bean>

    <!-- https://km.sankuai.com/page/16428777 -->
    <bean id="upm" class="com.sankuai.meituan.upm.pigeon.invoker.api.UPM">
        <constructor-arg name="clientId" value="xianfu_waimai"/>
        <constructor-arg name="secret" ref="kms.upmAuthService.secret"/>
        <constructor-arg name="timeout" value="1000"/>  <!-- pigeon 超时（ms） -->
        <constructor-arg name="timeoutRetry" value="true"/>  <!-- pigeon 超时重试开关 -->
        <constructor-arg name="retries" value="5"/>  <!-- pigeon 超时重试次数 -->
    </bean>

    <bean id="campusAndHcArchivesThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.sunflower.client.campus.thrift.service.CampusAndHcArchivesThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.sunflowerserver"/>
        <property name="remoteServerPort" value="8444"/>
    </bean>

    <bean id="elicenceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.zcm.elicence.thrift.TElicenceService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.zc.cos.elicence"/>
        <property name="remoteServerPort" value="8901"/>
    </bean>

    <bean id="wmAgentInfoService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.waimai.agent.otter.enterprise.service.agentinfo.WmAgentInfoService"/> <!-- 接口名 -->
        <property name="remoteServerPort" value="8502"/>
        <property name="appKey" value="com.sankuai.waimai.e.customerweb"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.agentotter"/>  <!-- 目标Server Appkey  -->
        <property name="timeout" value="20000"/>
    </bean>

    <bean id="authenticateRoleService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.waimai.crm.authenticate.client.service.AuthenticateRoleService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.waimaiinfra.authenticate.server"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteServerPort" value="8686"/>
    </bean>

    <bean id="authenticateService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.waimai.crm.authenticate.client.service.AuthenticateService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.waimaiinfra.authenticate.server"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteServerPort" value="8686"/>
    </bean>

    <bean id="teamThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.waimai.crm.team.client.service.TeamThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.wmcrmplatform.usermanage "/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteServerPort" value="8888"/>
    </bean>

    <!--泛天网-->
    <bean id="iCQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.fsp.cap.skyzfg.iface.service.ICQueryService"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.fsp.supervise.skyzfg"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="10000"/>
    </bean>

    <bean id="settledContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="hmcThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.health.merchant.center.thrift.client.SettledContractThriftService"/>
        <property name="filterByServiceName" value="true"/> <!-- 默认false -->
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.health.merchant.center"/>
        <property name="timeout" value="10000"/>
<!--        <property name="remoteServerPort" value="8411"/>-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="hmcThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="50"/>
        <property name="minIdle" value="20"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="tHealthContractQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.healthm.health.operation.contract.thrift.service.THealthContractQueryService"/> <!-- 接口名 -->
        <property name="timeout" value="5000"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.healthm.contract"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="8411"/>
    </bean>

    <!-- 合同服务 -->
    <bean id="WmAgentFwContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.heron.agentcontract.service.WmAgentFwContractThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="30000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value=""/>  <!-- your appkey -->
        <property name="remoteAppkey" value="com.sankuai.heron.agentcontract"/>
        <property name="remoteServerPort" value="9002"/>
    </bean>

    <!--合作商-->
    <bean id="contractThrift" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy">
        <property name="serviceInterface" value="com.meituan.waimai.agent.yak.contract.client.service.ContractThrift"/>
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="remoteAppkey" value="com.sankuai.waimaiagent.yak"/>
        <property name="remoteServerPort" value="9007"/>
    </bean>

    <bean id="openYthBmWmDataMappingQueryFacade" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.banma.thrift.deliverycrm.customer.facade.iface.yitihua.monitor.OpenYthBmWmDataMappingQueryFacade"/> <!-- service接口名 -->
        <property name="timeout" value="20000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.deliverycrm.customer.facade"/>
        <property name="remoteServerPort" value="9105"/>
    </bean>

    <bean id="multiSerivcePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceProcessorMap"> <!-- 配置单端口多服务 -->
            <map>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.gray.WmCustomerGrayThriftService"
                       value-ref="wmCustomerGrayThriftServiceBean"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpCleanDataThriftService"
                       value-ref="wmCustomerKpCleanDataThriftServiceBean"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerDataCleanThriftService"
                       value-ref="wmCustomerCleanDataThriftServiceBean"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerRecoveryToolThriftService"
                       value-ref="wmCustomerRecoveryToolThriftServiceBean"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.sc.WmScDataCleanThriftService"
                       value-ref="wmScDataCleanThriftServiceBean"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerGlobalEcontractThriftService"
                       value-ref="wmCustomerGlobalEcontractThriftService"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerFrameContractConfigThriftService"
                       value-ref="wmCustomerFrameContractConfigThriftService"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.contract.WmPartnerCustomerContractThriftService"
                       value-ref="wmPartnerCustomerContractThriftService"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.contract.WmCustomerFrameContractThriftService"
                       value-ref="wmCustomerFrameContractThriftService"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.customer.WmCustomerFrameThriftService"
                       value-ref="wmCustomerFrameThriftServiceBean"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerOwnerThriftService"
                       value-ref="wmCustomerOwnerThriftServiceBean"/>
            </map>
        </property>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8466"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="multiMonitorServicePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceProcessorMap"> <!-- 配置单端口多服务 -->
            <map>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.monitor.WmCustomerTaskMonitorService"
                       value-ref="wmCustomerTaskMonitorServiceBean"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.monitor.CustomerQuaComMonitorService"
                       value-ref="customerQuaComMonitorServiceBean"/>
                <entry key="com.sankuai.meituan.waimai.thrift.customer.service.stressTest.CustomerStressTestService"
                       value-ref="customerStressTestServiceBean"/>
            </map>
        </property>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8689"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler"/> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!-- 客户灰度服务 -->
    <bean id="wmCustomerGrayThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerGrayThriftServiceImpl"/>
    </bean>

    <!-- 客户KP数据清洗服务 -->
    <bean id="wmCustomerKpCleanDataThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerKpCleanDataThriftServiceImpl"/>
    </bean>
    <!-- 客户KP数据清洗服务 -->
    <bean id="wmCustomerCleanDataThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerCleanDataThriftServiceImpl"/>
    </bean>
    <!-- 客户恢复工具服务 -->
    <bean id="wmCustomerRecoveryToolThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerRecoveryToolThriftServiceImpl"/>
    </bean>

    <!-- 客户任务监控服务 -->
    <bean id="wmCustomerTaskMonitorServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerTaskMonitorServiceImpl"/>
    </bean>

    <!-- 校园食堂数据清洗服务 -->
    <bean id="wmScDataCleanThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmScDataCleanThriftServiceImpl"/>
    </bean>

    <!-- 客户与门店资质共用标签监控服务 -->
    <bean id="customerQuaComMonitorServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerQuaComMonitorServiceImpl"/>
    </bean>

    <!-- 客户压测专用服务 -->
    <bean id="customerStressTestServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerStressTestServiceImpl"/>
    </bean>

    <!-- 客户前端专用服务-网关接口 -->
    <bean id="wmCustomerFrameThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerFrameThriftServiceImpl"/>
    </bean>
    <!-- 客户前端专用服务-网关接口 -->
    <bean id="wmCustomerOwnerThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerOwnerThriftServiceImpl"/>
    </bean>

    <bean id="wmPoiStatusMainThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.service.WmPoiStatusMainThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.poioperation.status"/>
        <property name="remoteServerPort" value="8807"/>
    </bean>

    <bean id="bmContractWmThriftIface"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="thriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.banma.thrift.deliverycontract.adminbiz.thrift.BmContractWmThriftIface"/>
        <property name="timeout" value="5000"/>
        <property name="remoteAppkey" value="com.sankuai.deliverycontract.adminbiz"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
    </bean>

    <bean id="bmLbsAoiExtThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.banma.aoi.thrift.iface.BmLbsAoiExtThriftIface"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.deliverylbs.aoisupport"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="9820"/>
    </bean>
    <bean id="wmPoiserviceCommonThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.service.server.WmPoiServiceCommomThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.poioperation.info"/>
        <property name="remoteServerPort" value="8896"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="bmContractSettleSignThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.thrift.BmContractSettleSignThriftIface"/>
        <!-- 超时时间自定义，默认2s -->
        <property name="timeout" value="20000"/>
        <property name="remoteAppkey" value="#{T(com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.constant.ThriftConstants).REMOTE_APP_KEY}"/>
    </bean>

    <!-- 全局合同相关 -->
    <bean id="wmCustomerGlobalEcontractThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerGlobalEcontractThriftServiceImpl"/>
    </bean>

    <bean id="wmCustomerFrameContractConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerFrameContractConfigThriftServiceImpl" />
    </bean>

    <bean id="wmPartnerCustomerContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmPartnerCustomerContractThriftServiceImpl" />
    </bean>

    <bean id="wmCustomerFrameContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="wmCustomerFrameContractThriftServiceImpl" />
    </bean>

    <!-- 以"从kms获取uacSecret、uacClientId、uacHost"为例 -->

    <bean id="uacAuthRemoteService" class="com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService">
        <property name="uacHost" ref="uacHost" />
        <property name="uacClientId" ref="uacClientId" />
        <property name="uacSecret" ref="uacSecret" />
    </bean>

    <bean id="uacRoleRemoteService" class="com.sankuai.meituan.uac.sdk.service.UacRoleRemoteService">
        <property name="uacHost" ref="uacHost" />
        <property name="uacClientId" ref="uacClientId" />
        <property name="uacSecret" ref="uacSecret" />
    </bean>

    <bean id="uacHost" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${sg.app.key}" />
        <property name="name" value="uacHost" />
        <property name="retryCount" value="10" />
    </bean>

    <bean id="uacClientId" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${sg.app.key}" />
        <property name="name" value="uacClientId" />
        <property name="retryCount" value="10" />
    </bean>

    <bean id="uacSecret" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${sg.app.key}" />
        <property name="name" value="uacSecret" />
        <property name="retryCount" value="10" />
    </bean>

    <!-- 生成短链 -->
    <bean id="operateServiceThrift" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.dianping.mobileossapi.service.operate.OperateServiceThrift"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/> <!-- 本地appkey -->
        <property name="remoteServerPort" value="5485"/>
        <property name="remoteAppkey" value="mobile-oss-operation-service"/>
    </bean>

    <!-- 文枢 ApiUploadFileService -->
    <bean id="apiUploadFileClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <!-- 接口名 -->
        <property name="serviceInterface" value="com.meituan.sec.distributeplatform.thrift.ApiUploadFileService"/>
        <!-- 本地appkey -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <!-- 目标Server Appkey  -->
        <property name="remoteAppkey" value="com.sankuai.sec.distribute.platform"/>
        <property name="timeout" value="10000"/>
        <property name="remoteServerPort" value="9002"/>
    </bean>


    <bean id="merchantSettleQueryProxyService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="timeout" value="2000"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.pay.merchantproduct.mwallet"/>
        <property name="remoteServerPort" value="3443"/>
        <property name="serviceInterface" value="com.meituan.pay.mwallet.thrift.service.MerchantSettleQueryProxyService"/>
    </bean>

    <!--特批提审服务-->
    <bean id="approvalCoreApi" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.api.approval.outer.ApprovalCoreApi"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.audit"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="5000"/>
    </bean>

    <bean id="brandMetadataOuterService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.scmbrand.thrift.service.metadataplatform.BrandMetadataOuterService"/> <!-- service接口名 -->
        <property name="timeout" value="5000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaipoi.brand"/>
        <property name="remoteServerPort" value="8548"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="gravityThriftService" class="com.sankuai.meituan.gravity.thrift.GravityThriftServiceImpl">
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 填写本地appkey -->
        <property name="timeout" value="5000"/> <!-- 填写接口超时时长 -->
        <property name="isEnableSwimlane" value="true" /> <!-- 是否开启泳道，非必填，未使用到泳道的情况下不用设置此值 -->
    </bean>

    <bean id="campusContactThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.thrift.contact.CampusContactThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaisales.saas.lead"/>
        <property name="remoteServerPort" value="8490"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="campusPartnerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.thrift.partner.CampusPartnerThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaisales.saas.lead"/>
        <property name="remoteServerPort" value="8490"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="campusContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.thrift.contract.CampusContractThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaisales.saas.lead"/>
        <property name="remoteServerPort" value="8493"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="deliverThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.DeliverThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.wmcrmplatform.saas.deliver"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmOpenCityService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.gis.client.thrift.service.WmOpenCityService"/> <!-- service接口名 -->
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="timeout" value="3000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.waimaiinfra.gis.server"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmGisUniAorService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.gis.client.thrift.service.WmGisUniAorService"/> <!-- 接口名 -->
        <property name="timeout" value="3000"/> <!-- thrift rpc超时申请 -->
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.waimaiinfra.gis.server"/>  <!-- 目标 Server Appkey  -->
        <property name="filterByServiceName" value="true"/>
<!--        <property name="remoteServerPort" value="8485"/>-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="poiOuterCreateService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.wdc.poi.flow.action.service.PoiOuterCreateService"/> <!-- service接口名 -->
        <property name="timeout" value="5000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.wdcpoiactionflowapi"/>
        <property name="remoteServerPort" value="9209"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmHighseasThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.service.WmHighseasThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="5000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaisales.poicluecenter"/>
        <property name="remoteServerPort" value="8723"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="waimaiLeadRotateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.waimai.cd.crm.waimai.thrift.WaimaiLeadRotateThriftService" />
        <property name="timeout" value="3000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaisales.poicluecenter"/>
        <property name="remoteServerPort" value="8413"/>
    </bean>

    <bean id="wdcPoiLabelChangeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.wdcn.api.service.WdcPoiLabelChangeThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaiwdc.wdcn.api"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="wdcPoiLabelQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.wdcn.api.service.WdcPoiLabelQueryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaiwdc.wdcn.api"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="wdcPoiQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.wdc.domain.thrift.service.WdcPoiQueryService"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.wdcpoidomain"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="5000"/>
    </bean>

    <bean id="wdcRelationClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.wdc.service.IRelationService"/> <!-- service接口名 -->
        <property name="timeout" value="5000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.poiapi"/>
        <property name="remoteServerPort" value="8351"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <!-- 站内信 -->
    <bean id="tSenderService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibmp.infra.msg.sender.thrift.iface.TSenderService"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.message.sender"/>
        <property name="remoteServerPort" value="9000"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="customerIdMappingService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.nibcus.inf.idmapping.client.service.CustomerIdMappingService"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.nibcus.inf.idmapping"/>
        <property name="timeout" value="3000"/>
        <property name="remoteUniProto" value="true"/>
        <property name="remoteServerPort" value="9000"/>
    </bean>

    <!-- 到餐门店服务 -->
    <bean id="agentPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.waimai.agent.otter.enterprise.service.agentpoi.AgentPoiService"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.agentotter"/>
        <property name="timeout" value="10000"/>
        <property name="remoteUniProto" value="true"/>
        <property name="remoteServerPort" value="8903"/>
    </bean>

    <!-- 外卖门店服务 -->
    <bean id="wmAgentPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.waimai.agent.otter.enterprise.service.agentpoi.WmAgentPoiService"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.agentotter"/>
        <property name="timeout" value="10000"/>
        <property name="remoteUniProto" value="true"/>
        <property name="remoteServerPort" value="8507"/>
    </bean>

    <bean id="customerBaseService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.customer.thrift.service.CustomerBaseService"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.meishi.customer"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="customerPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.customer.thrift.service.CustomerPoiService"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.meishi.customer"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <!-- pecker-trace -->
    <bean id="peckerTraceBeanAutoConfiguration" class="com.sankuai.tsp.pecker.trace.infrastructure.config.PeckerTraceBeanAutoConfiguration">

    </bean>

    <bean id="defaultSignHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultSignHandler" >
        <property name="authDataSource" ref="kmsAuthDataSource" />
    </bean>

    <!--到餐-权限系统-账号相关-->
    <bean id="accountService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibmp.infra.amp.attribute.lib.IAccountService"/>
        <property name="remoteAppkey" value="com.sankuai.nibmerchant.amp.attribute"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="5000"/>
        <property name="signHandler" ref="defaultSignHandler"/>
    </bean>

    <!-- poi供给服务 -->
    <bean id="mapOpenApiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.map.open.platform.api.MapOpenApiService"/>
        <property name="remoteAppkey" value="com.sankuai.apigw.map.facadecenter"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="5000"/>
    </bean>

    <bean id="econtractManagerService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.m.contractjob"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="9004" />
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>

    <bean id="customerAggregateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibcus.inf.customer.client.service.CustomerAggregateThriftService"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.nibcus.inf.customer"/>
        <property name="timeout" value="3000"/>
        <property name="remoteUniProto" value="true"/>
        <property name="maxResponseMessageBytes" value="********"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <!-- 到家调用到店 -->
    <bean class="com.sankuai.tsp.route.spring.Routing2GrayLiteSetBeanFactoryPostProcessor">
        <!-- 到店appkey及对应的灰度liteset名称的映射关系 -->
        <constructor-arg name="remoteAppkeyLiteSetNameMap">
            <map>
                <entry key="com.sankuai.nibcus.inf.idmapping" value="gray-release-nib-st"/>
                <entry key="com.sankuai.nibmerchant.amp.attribute" value="gray-release-nib-st"/>
                <entry key="com.sankuai.cos.mtcoop" value="gray-release-nib-st"/>
                <entry key="com.sankuai.nibmerchant.amp.authorize" value="gray-release-nib-st"/>
                <entry key="com.sankuai.nibcus.inf.customer" value="gray-release-nib-st"/>
                <entry key="com.sankuai.meishi.customer" value="gray-release-nib-st"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="wmEnterpriseAgentElephantService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.agent.enterprise.rpc.service.WmEnterpriseAgentElephantService"/> <!-- service接口名 -->
        <property name="timeout" value="5000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.enterpriserpc"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

</beans>



