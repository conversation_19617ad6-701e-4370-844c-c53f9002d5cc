<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <!--    com.sankuai.waimai.contractmanager，服务已下线，后续可删除以下bean-->
    <bean id="wmContractVersionManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.contract.thrift.service.WmContractVersionManagerThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.contractmanager"/>
        <property name="remoteServerPort" value="8494"/>
    </bean>


    <bean id="wmContractAuditManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.contract.thrift.service.WmContractAuditManagerThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="60000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.contractmanager"/>
        <property name="remoteServerPort" value="8496"/>
    </bean>

    <bean id="wmContractMcertifyThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.contract.thrift.service.WmContractMcertifyThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.contractmanager"/>
        <property name="remoteServerPort" value="8509"/>
    </bean>


    <!--com.sankuai.waimai.contract-->
    <bean id="wmSettleThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.contract.thrift.service.WmSettleThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.contract"/>
        <property name="remoteServerPort" value="8475"/>
    </bean>

    <bean id="wmSettleAuditedThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.contract.thrift.service.WmSettleAuditedThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.contract"/>
        <property name="remoteServerPort" value="8476"/>
    </bean>

    <bean id="wmContractSignerAuditedThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.contract.thrift.service.WmContractSignerAuditedThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.contract"/>
        <property name="remoteServerPort" value="8483"/>
    </bean>

    <bean id="wmContractPoiAuditedThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.contract.thrift.service.WmContractPoiAuditedThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.contract"/>
        <property name="remoteServerPort" value="8474"/>
    </bean>

    <bean id="wmLogisticsNewChannelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.logistics.contract.client.service.newchannel.WmLogisticsNewChannelThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="60000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.heron.logistics.contract"/>
        <property name="remoteServerPort" value="8455"/>
    </bean>



</beans>