<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiRelExtensionMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiRelExtension" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT" />
    <result column="biz_type" property="bizType" jdbcType="INTEGER" />
    <result column="biz_id" property="bizId" jdbcType="BIGINT" />
    <result column="ctime" property="ctime" jdbcType="INTEGER" />
    <result column="utime" property="utime" jdbcType="INTEGER" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, wm_poi_id, biz_type, biz_id, ctime, utime, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wm_customer_poi_rel_extension
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wm_customer_poi_rel_extension
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiRelExtension" >
    insert into wm_customer_poi_rel_extension (id, wm_poi_id, biz_type, 
      biz_id, ctime, utime, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{wmPoiId,jdbcType=BIGINT}, #{bizType,jdbcType=INTEGER}, 
      #{bizId,jdbcType=BIGINT}, #{ctime,jdbcType=INTEGER}, #{utime,jdbcType=INTEGER}, 
      #{valid,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiRelExtension" >
    insert into wm_customer_poi_rel_extension
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="wmPoiId != null" >
        wm_poi_id,
      </if>
      <if test="bizType != null" >
        biz_type,
      </if>
      <if test="bizId != null" >
        biz_id,
      </if>
        ctime,
        utime,
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="wmPoiId != null" >
        #{wmPoiId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null" >
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null" >
        #{bizId,jdbcType=BIGINT},
      </if>
      unix_timestamp(),
      unix_timestamp(),
      <if test="valid != null" >
        #{valid,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiRelExtension" >
    update wm_customer_poi_rel_extension
    <set >
      <if test="wmPoiId != null" >
        wm_poi_id = #{wmPoiId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null" >
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null" >
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=INTEGER},
      </if>
      <if test="utime != null" >
        utime = #{utime,jdbcType=INTEGER},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiRelExtension" >
    update wm_customer_poi_rel_extension
    set wm_poi_id = #{wmPoiId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=INTEGER},
      biz_id = #{bizId,jdbcType=BIGINT},
      ctime = #{ctime,jdbcType=INTEGER},
      utime = #{utime,jdbcType=INTEGER},
      valid = #{valid,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getExistRelWmPoiIdList" resultType="java.lang.Long" parameterType="map">
     select wm_poi_id from wm_customer_poi_rel_extension
     where wm_poi_id in
     <foreach collection="wmPoiIdList" item="item" open="(" close=")" separator=",">
        #{item}
     </foreach>
     and biz_type = #{bizType}
     and valid = 1
  </select>

  <select id="getExistRelWmPoiIdListMaster" resultType="java.lang.Long" parameterType="map">
    /*master*/select wm_poi_id from wm_customer_poi_rel_extension
    where wm_poi_id in
    <foreach collection="wmPoiIdList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and biz_type = #{bizType}
    and valid = 1
  </select>

  <select id="selectByWmPoiIdListAndBizType" resultMap="BaseResultMap" parameterType="map">
    select
    <include refid="Base_Column_List" />
    from wm_customer_poi_rel_extension
    where wm_poi_id in
    <foreach collection="wmPoiIdList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and biz_type = #{bizType}
    and valid = 1
  </select>

  <select id="selectByWmPoiIdAndBizType" resultMap="BaseResultMap" parameterType="map">
    select
    <include refid="Base_Column_List" />
    from wm_customer_poi_rel_extension
    where wm_poi_id = #{wmPoiId}
    and biz_type = #{bizType}
    and valid = 1
    order by id desc limit 1
  </select>


</mapper>