<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.settle.dao.WmSettleWalletCleanLogDBMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleWalletCleanLogDB" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <id column="city_code" property="cityCode" jdbcType="VARCHAR" />
    <result column="bd_mis_id" property="bdMisId" jdbcType="VARCHAR" />
    <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT" />
    <result column="wm_settle_id" property="wmSettleId" jdbcType="INTEGER" />
    <result column="wm_poi_version" property="wmPoiVersion" jdbcType="INTEGER" />
    <result column="customer_id" property="customerId" jdbcType="VARCHAR" />
    <result column="op_uid" property="opUid" jdbcType="INTEGER" />
    <result column="result" property="result" jdbcType="TINYINT" />
    <result column="exception" property="exception" jdbcType="TINYINT" />
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
    <result column="ctime" property="ctime" jdbcType="INTEGER" />
    <result column="utime" property="utime" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, city_code, bd_mis_id, wm_poi_id, wm_settle_id, wm_poi_version, customer_id, op_uid, result, exception, reason, valid, ctime, utime
  </sql>

  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleWalletCleanLogDB" useGeneratedKeys = "true" keyProperty = "id">
    insert into wm_settle_wallet_clean_log (city_code, bd_mis_id, wm_poi_id, wm_settle_id,
    wm_poi_version, customer_id, op_uid, result, exception, 
    reason, valid, ctime, utime)
    values (#{cityCode,jdbcType=VARCHAR}, #{bdMisId,jdbcType=VARCHAR}, #{wmPoiId,jdbcType=BIGINT}, #{wmSettleId,jdbcType=INTEGER}, #{wmPoiVersion,jdbcType=INTEGER},
      #{customerId,jdbcType=VARCHAR}, #{opUid,jdbcType=VARCHAR}, #{result,jdbcType=INTEGER}, #{exception,jdbcType=INTEGER}, 
      #{reason,jdbcType=VARCHAR}, #{valid,jdbcType=TINYINT}, unix_timestamp() , unix_timestamp())
  </insert>

  <update id="update" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleWalletCleanLogDB">
    update wm_settle_wallet_clean_log set result = #{result,jdbcType=INTEGER}, exception = #{exception,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR}, utime = unix_timestamp()
      where wm_settle_id = #{wmSettleId,jdbcType=INTEGER} AND valid = 1
  </update>

  <delete id="delete" parameterType="java.lang.Long">
    delete from wm_settle_wallet_clean_log
    where wm_poi_id = #{wmPoiId,jdbcType=BIGINT}
  </delete>

  <select id="getWmSettleWalletCleanLogByCityCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    wm_settle_wallet_clean_log
    where city_code = #{cityCode,jdbcType=VARCHAR}
    AND valid = 1
  </select>

  <select id="getWmSettleWalletCleanLogByBdMisId" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    wm_settle_wallet_clean_log
    where bd_mis_id = #{bdMisId,jdbcType=VARCHAR}
    AND valid = 1
  </select>

  <select id="getWmSettleWalletCleanLogByWmPoiIdList" parameterType="java.util.List" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    wm_settle_wallet_clean_log
    where wm_poi_id in
    <foreach item="item" index="index" collection="wmPoiIdList" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND valid = 1
  </select>
  
</mapper>