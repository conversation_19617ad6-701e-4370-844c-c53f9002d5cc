<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.settle.dao.WmSettleProtocolDBMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolDB">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="customer_id" property="wmCustomerId" jdbcType="INTEGER"/>
        <result column="supplemental_url" property="supplementalUrl" jdbcType="VARCHAR"/>
        <result column="qdb_url" property="qdbUrl" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>

    </resultMap>
    <sql id="Base_Column_List">
        id, customer_id, supplemental_url, qdb_url, ctime, utime
    </sql>
    <select id="selectByCustomer" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from wm_settle_protocol
        where customer_id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByCustomerMaster" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        /*master*/select
        <include refid="Base_Column_List"/>
        from wm_settle_protocol
        where customer_id = #{id,jdbcType=INTEGER}
    </select>

    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolDB">
    insert into wm_settle_protocol (id, customer_id, supplemental_url, qdb_url, ctime, utime)
    values (#{id,jdbcType=INTEGER}, #{wmCustomerId,jdbcType=INTEGER},#{supplementalUrl,jdbcType=VARCHAR},
            #{qdbUrl,jdbcType=VARCHAR}, unix_timestamp(), unix_timestamp())
  </insert>

    <update id="updateWmSettleProtocol" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolDB">
        update wm_settle_protocol
        set supplemental_url=#{supplementalUrl,jdbcType=VARCHAR}, qdb_url=#{qdbUrl,jdbcType=VARCHAR}, utime=unix_timestamp()
        where customer_id=#{wmCustomerId,jdbcType=INTEGER}
    </update>

</mapper>