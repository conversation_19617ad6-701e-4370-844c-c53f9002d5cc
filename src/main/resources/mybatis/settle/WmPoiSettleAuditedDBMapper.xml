<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleAuditedDBMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="wm_poi_id" property="wm_poi_id" jdbcType="INTEGER" />
    <result column="wm_contract_id" property="wm_contract_id" jdbcType="INTEGER" />
    <result column="wm_settle_id" property="wm_settle_id" jdbcType="INTEGER" />
    <result column="ctime" property="ctime" jdbcType="INTEGER" />
    <result column="utime" property="utime" jdbcType="INTEGER" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, wm_poi_id, wm_contract_id, wm_settle_id, ctime, utime, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from wm_poi_settle_audited
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="getWmSettleIdsByContractAndPoiId" resultType="java.lang.Integer">
    select wm_settle_id from wm_poi_settle_audited
    where wm_contract_id = #{wmContractId, jdbcType=INTEGER}
    <if test="wmPoiIdList != null and wmPoiIdList.size() > 0">
      and wm_poi_id in
      <foreach collection="wmPoiIdList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and valid = 1
  </select>
  <select id="getByContractIdAndWmPoiIdList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM wm_poi_settle_audited
    WHERE wm_contract_id = #{wmContractId, jdbcType=INTEGER}
    <if test="wmPoiIdList != null and wmPoiIdList.size() > 0">
      AND wm_poi_id IN
      <foreach collection="wmPoiIdList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    AND valid = 1
  </select>

  <select id="getByContractIdAndWmPoiIdListMaster" resultMap="BaseResultMap">
    /*master*/SELECT
    <include refid="Base_Column_List" />
    FROM wm_poi_settle_audited
    WHERE wm_contract_id = #{wmContractId, jdbcType=INTEGER}
    <if test="wmPoiIdList != null and wmPoiIdList.size() > 0">
      AND wm_poi_id IN
      <foreach collection="wmPoiIdList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    AND valid = 1
  </select>

  <select id="getAllByContractIdAndWmPoiIdList" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List"/>
    FROM
        wm_poi_settle_audited
    WHERE
        wm_contract_id = #{wmContractId, jdbcType=INTEGER}
    <if test="wmPoiIdList != null and wmPoiIdList.size() > 0">
      AND wm_poi_id IN
      <foreach collection="wmPoiIdList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="getByWmContractIdAndWmPoiIdMaster" resultMap="BaseResultMap">
    /*master*/SELECT
    <include refid="Base_Column_List" />
    FROM wm_poi_settle_audited
    WHERE wm_contract_id = #{wmContractId, jdbcType=INTEGER}
    AND wm_poi_id = #{wmPoiId, jdbcType=INTEGER}
    AND valid = 1
  </select>


  <select id="batchQueryPoiSettleAudited" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM wm_poi_settle_audited
    WHERE valid=1
    AND wm_poi_id IN
    <foreach collection="poiList" item="poiId" open="(" close=")"
             separator=",">
      #{poiId}
    </foreach>
  </select>



  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    update wm_poi_settle_audited set valid=0, utime=unix_timestamp()
    where id = #{id,jdbcType=INTEGER} and valid = 1
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB" >
    insert into wm_poi_settle_audited (id, wm_poi_id, wm_contract_id, 
      wm_settle_id, ctime, utime, 
      valid)
    values (#{id,jdbcType=INTEGER}, #{wm_poi_id,jdbcType=INTEGER}, #{wm_contract_id,jdbcType=INTEGER}, 
      #{wm_settle_id,jdbcType=INTEGER}, #{ctime,jdbcType=INTEGER}, #{utime,jdbcType=INTEGER}, 
      #{valid,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB" useGeneratedKeys = "true" keyProperty = "id">
    insert into wm_poi_settle_audited
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <!--<if test="id != null" >-->
        <!--id,-->
      <!--</if>-->
      <if test="wm_poi_id != null" >
        wm_poi_id,
      </if>
      <if test="wm_contract_id != null" >
        wm_contract_id,
      </if>
      <if test="wm_settle_id != null" >
        wm_settle_id,
      </if>
        ctime,
        utime,
        valid,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <!--<if test="id != null" >-->
        <!--#{id,jdbcType=INTEGER},-->
      <!--</if>-->
      <if test="wm_poi_id != null" >
        #{wm_poi_id,jdbcType=INTEGER},
      </if>
      <if test="wm_contract_id != null" >
        #{wm_contract_id,jdbcType=INTEGER},
      </if>
      <if test="wm_settle_id != null" >
        #{wm_settle_id,jdbcType=INTEGER},
      </if>
      unix_timestamp(),
      unix_timestamp(),
      1,
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB" >
    update wm_poi_settle_audited
    <set >
      <if test="wm_poi_id != null" >
        wm_poi_id = #{wm_poi_id,jdbcType=INTEGER},
      </if>
      <if test="wm_contract_id != null" >
        wm_contract_id = #{wm_contract_id,jdbcType=INTEGER},
      </if>
      <if test="wm_settle_id != null" >
        wm_settle_id = #{wm_settle_id,jdbcType=INTEGER},
      </if>
        utime = unix_timestamp(),
      <if test="valid != null" >
        valid = #{valid,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB" >
    update wm_poi_settle_audited
    set wm_poi_id = #{wm_poi_id,jdbcType=INTEGER},
      wm_contract_id = #{wm_contract_id,jdbcType=INTEGER},
      wm_settle_id = #{wm_settle_id,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=INTEGER},
      utime = #{utime,jdbcType=INTEGER},
      valid = #{valid,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getBySettleIdList" parameterType="list" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    wm_poi_settle_audited
    where wm_settle_id IN
    <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND valid = 1
  </select>

  <select id="getBySettleIdListAndWmContractId" parameterType="map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    wm_poi_settle_audited
    where wm_settle_id IN
    <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND wm_contract_id = #{wmContractId}
    AND valid = 1
  </select>

  <select id="getWmPoiIdListByWmSettleIdList" parameterType="list" resultType="int">
    SELECT wm_poi_id
    FROM wm_poi_settle_audited
    WHERE wm_settle_id IN
    <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND valid = 1
  </select>

  <select id="getWmSettleIdListBySettleAndPoi" parameterType="map" resultType="int">
    SELECT wm_settle_id
    FROM wm_poi_settle_audited
    WHERE wm_settle_id IN
    <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND wm_poi_id IN
    <foreach collection="wmPoiIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND valid = 1
  </select>

  <select id="getWmSettleIdListByWmPoiId" parameterType="int" resultType="int">
    SELECT wm_settle_id
    FROM wm_poi_settle_audited
    WHERE wm_poi_id = #{wmPoiId}
    AND valid = 1
  </select>

  <select id="getWmSettleIdListByWmPoiIdAndWmContractId" parameterType="map" resultType="int">
    SELECT wm_settle_id
    FROM wm_poi_settle_audited
    WHERE wm_contract_id = #{wmContractId}
      AND wm_poi_id = #{wmPoiId}
      AND valid = 1
  </select>

  <select id="batchGetEffectiveWmPoiIdList" parameterType="list" resultType="long">
    SELECT wm_poi_id
    FROM wm_poi_settle_audited
    WHERE wm_poi_id IN
    <foreach collection="wmPoiIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND valid = 1
  </select>

  <update id="deleteByWmSettleIdList" parameterType="list">
    UPDATE wm_poi_settle_audited
    SET valid = 0,utime = unix_timestamp()
    WHERE wm_settle_id IN
    <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND valid = 1
  </update>

  <update id="deleteByWmSettleIdListAndWmPoiIdList" parameterType="map">
    UPDATE wm_poi_settle_audited
    SET valid = 0,utime = unix_timestamp()
    WHERE wm_settle_id IN
    <foreach collection="wmSettleIdList" item="wmSettleId" open="(" separator="," close=")">
      #{wmSettleId}
    </foreach>
    AND wm_poi_id IN
    <foreach collection="wmPoiIdList" item="wmPoiId" open="(" separator="," close=")">
      #{wmPoiId}
    </foreach>
    AND valid = 1
  </update>

  <select id="getByWmSettleIdListAndWmPoiIdList" parameterType="map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM wm_poi_settle_audited
    WHERE wm_settle_id IN
    <foreach collection="wmSettleIdList" item="wmSettleId" open="(" separator="," close=")">
      #{wmSettleId}
    </foreach>
    AND wm_poi_id IN
    <foreach collection="wmPoiIdList" item="wmPoiId" open="(" separator="," close=")">
      #{wmPoiId}
    </foreach>
    AND valid = 1
  </select>

  <update id="deleteUnnecessaryOnlineRelByWmCustomerIdAndWmPoiIdList" parameterType="map">
    update wm_poi_settle_audited
    set valid = 0,utime =unix_timestamp()
    where wm_contract_id != #{wmCustomerId}
    and wm_poi_id IN
    <foreach collection="wmPoiIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and valid = 1
  </update>

  <select id="getUnnecessaryOnlineRelByWmCustomerIdAndWmPoiIdListMaster"
    parameterType="map" resultMap="BaseResultMap">
    /*master*/select
    <include refid="Base_Column_List" />
    FROM wm_poi_settle_audited
    where wm_contract_id != #{wmCustomerId}
    AND wm_poi_id IN
    <foreach collection="wmPoiIdList" item="wmPoiId" open="(" separator="," close=")">
      #{wmPoiId}
    </foreach>
    AND valid = 1
  </select>

  <select id="getCountByWmContractIdAndWmSettleIdMaster"
    parameterType="map" resultType="int">
    /*master*/select count(1)
    FROM wm_poi_settle_audited
    where wm_contract_id = #{wmContractId}
    and wm_settle_id = #{wmSettleId}
    and valid = 1
  </select>



</mapper>