<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.settle.dao.WmSettlePoiWalletDBMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.settle.domain.WmSettlePoiWalletDB" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="wm_poi_id" property="wm_poi_id" jdbcType="INTEGER" />
    <result column="wm_settle_id" property="wm_settle_id" jdbcType="INTEGER" />
    <result column="wm_wallet_id" property="wm_wallet_id" jdbcType="BIGINT" />
    <result column="ctime" property="ctime" jdbcType="INTEGER"/>
    <result column="utime" property="utime" jdbcType="INTEGER"/>
    <result column="valid" property="valid" jdbcType="TINYINT"/>
  </resultMap>

  <sql id="Base_Column_List" >
    id, wm_poi_id, wm_settle_id, wm_wallet_id, ctime, utime, valid
  </sql>

  <select id="getWalletIdListByWmPoiId" resultType="java.lang.Long" parameterType="java.lang.Integer" >
    select
      wm_wallet_id
    from
      wm_settle_poi_wallet_rel_history
    where
      wm_poi_id = #{wmPoiId, jdbcType=INTEGER}
    and
      valid = 1
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into wm_settle_poi_wallet_rel_history (
        wm_poi_id,
        wm_settle_id,
        wm_wallet_id,
        ctime,
        utime,
        valid
    ) values
      <foreach collection="list" item="item" index="index" separator=",">
          (
            #{item.wm_poi_id,jdbcType=INTEGER},
            #{item.wm_settle_id,jdbcType=INTEGER},
            #{item.wm_wallet_id,jdbcType=BIGINT},
            #{item.ctime,jdbcType=INTEGER},
            #{item.utime,jdbcType=INTEGER},
            #{item.valid,jdbcType=TINYINT})
      </foreach>
  </insert>

  <select id="getByWalletIdAndWmPoiId" resultType="java.lang.Integer" parameterType="java.util.List" >
    select
      wm_poi_id
    from
      wm_settle_poi_wallet_rel_history
    where
      wm_wallet_id = #{walletId, jdbcType=BIGINT}
    and
      wm_poi_id
    in
    <foreach item="item" index="index" collection="wmPoiIdList" open="(" separator="," close=")">
      #{item}
    </foreach>
    and
      valid = 1
  </select>
</mapper>