<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiLogisticsSubjectDBMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiLogisticsSubjectDB">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="customer_id" property="customerId" jdbcType="INTEGER"/>
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT"/>
        <result column="part_b_num" property="partBNum" jdbcType="INTEGER"/>
        <result column="part_logistics_num" property="partLogisticsNum" jdbcType="INTEGER"/>
        <result column="effect_time" property="effectTime" jdbcType="INTEGER"/>
        <result column="tag" property="tag" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, customer_id, wm_poi_id, part_b_num, part_logistics_num, effect_time, tag, valid,
    ctime, utime
    </sql>

    <insert id="batchInsertCustomerPoiSubject">
        insert into wm_customer_poi_logistics_subject (customer_id,
        wm_poi_id,part_b_num,part_logistics_num,effect_time,tag,valid,ctime,utime)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.customerId},
            #{item.wmPoiId},#{item.partBNum},#{item.partLogisticsNum},#{item.effectTime},#{item.tag},#{item.valid},UNIX_TIMESTAMP(),UNIX_TIMESTAMP())
        </foreach>
    </insert>

    <insert id="insertCustomerPoiSubject" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerOplogWithBLOBs" keyProperty="id" useGeneratedKeys="true">
        insert into wm_customer_poi_logistics_subject (customer_id,
        wm_poi_id,part_b_num,part_logistics_num,effect_time,tag,valid,ctime,utime)
        values (#{customerId,jdbcType=INTEGER}, #{wmPoiId,jdbcType=BIGINT},
        #{partBNum,jdbcType=INTEGER}, #{partLogisticsNum,jdbcType=INTEGER}, #{effectTime,jdbcType=INTEGER},
        #{tag,jdbcType=INTEGER}, #{valid,jdbcType=INTEGER},
        UNIX_TIMESTAMP(),UNIX_TIMESTAMP())
    </insert>

    <update id="updateCustomerPoiSubject" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiLogisticsSubjectDB" >
        update wm_customer_poi_logistics_subject set part_b_num = #{partBNum},part_logistics_num = #{partLogisticsNum},utime = UNIX_TIMESTAMP()
        where id = #{id}
    </update>

    <select id="getLatestPoiSubject" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wm_customer_poi_logistics_subject
        where customer_id = #{customerId} and wm_poi_id = #{wmPoiId} and valid = 1
        order by id desc limit 1
    </select>

</mapper>