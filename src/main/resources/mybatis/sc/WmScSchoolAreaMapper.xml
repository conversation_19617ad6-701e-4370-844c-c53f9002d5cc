<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolAreaMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="school_primary_id" jdbcType="INTEGER" property="schoolPrimaryId" />
    <result column="aoi_id" jdbcType="BIGINT" property="aoiId" />
    <result column="aoi_name" jdbcType="VARCHAR" property="aoiName" />
    <result column="aoi_mode" jdbcType="INTEGER" property="aoiMode" />
    <result column="auto_sync" jdbcType="INTEGER" property="autoSync" />
    <result column="cr_source" jdbcType="INTEGER" property="crSource" />
    <result column="up_source" jdbcType="INTEGER" property="upSource" />
    <result column="valid" jdbcType="TINYINT" property="valid" />
    <result column="cuid" jdbcType="BIGINT" property="cuid" />
    <result column="muid" jdbcType="BIGINT" property="muid" />
    <result column="ctime" jdbcType="INTEGER" property="ctime" />
    <result column="utime" jdbcType="INTEGER" property="utime" />
    <result column="area" jdbcType="LONGVARCHAR" property="area" />
  </resultMap>

  <sql id="Base_Column_List">
    id, school_primary_id, aoi_id, aoi_name, aoi_mode, auto_sync, cr_source, up_source, valid, cuid, muid, ctime, utime, area
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wm_sc_school_area
    where id = #{id,jdbcType=BIGINT} and valid = 1
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wm_sc_school_area
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO">
    insert into wm_sc_school_area (id, school_primary_id, aoi_id,
                                   aoi_name, aoi_mode, auto_sync,
                                   cr_source, up_source, valid,
                                   cuid, muid, ctime, utime, area)
    values (#{id,jdbcType=BIGINT}, #{schoolPrimaryId,jdbcType=INTEGER}, #{crSource,jdbcType=INTEGER},
            #{aoiName,jdbcType=INTEGER}, #{aoiMode,jdbcType=INTEGER}, #{autoSync,jdbcType=INTEGER},
            #{crSource,jdbcType=INTEGER}, #{upSource,jdbcType=INTEGER}, #{valid,jdbcType=TINYINT},
            #{cuid,jdbcType=BIGINT}, #{muid,jdbcType=BIGINT}, #{ctime,jdbcType=INTEGER},
            #{utime,jdbcType=INTEGER}, #{area,jdbcType=LONGVARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO"
          useGeneratedKeys="true" keyProperty="id">
    insert into wm_sc_school_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      ctime, utime,
      <if test="id != null">
        id,
      </if>
      <if test="schoolPrimaryId != null">
        school_primary_id,
      </if>
      <if test="aoiId != null">
        aoi_id,
      </if>
      <if test="aoiName != null">
        aoi_name,
      </if>
      <if test="aoiMode != null">
        aoi_mode,
      </if>
      <if test="autoSync != null">
        auto_sync,
      </if>
      <if test="crSource != null">
        cr_source,
      </if>
      <if test="upSource != null">
        up_source,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="cuid != null">
        cuid,
      </if>
      <if test="muid != null">
        muid,
      </if>
      <if test="area != null">
        area,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <choose>
        <when test="ctime != null ">
          #{ctime,jdbcType=INTEGER},
        </when>
        <otherwise>
          UNIX_TIMESTAMP(),
        </otherwise>
      </choose>
      <choose>
        <when test="utime != null">
          #{utime,jdbcType=INTEGER},
        </when>
        <otherwise>
          UNIX_TIMESTAMP(),
        </otherwise>
      </choose>
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="schoolPrimaryId != null">
        #{schoolPrimaryId,jdbcType=INTEGER},
      </if>
      <if test="aoiId != null">
        #{aoiId,jdbcType=BIGINT},
      </if>
      <if test="aoiName != null">
        #{aoiName,jdbcType=VARCHAR},
      </if>
      <if test="aoiMode != null">
        #{aoiMode,jdbcType=TINYINT},
      </if>
      <if test="autoSync != null">
        #{autoSync,jdbcType=TINYINT},
      </if>
      <if test="crSource != null">
        #{crSource,jdbcType=INTEGER},
      </if>
      <if test="upSource != null">
        #{upSource,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=TINYINT},
      </if>
      <if test="cuid != null">
        #{cuid,jdbcType=BIGINT},
      </if>
      <if test="muid != null">
        #{muid,jdbcType=BIGINT},
      </if>
      <if test="area != null">
        #{area,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO">
    update wm_sc_school_area
    <set>
      utime = UNIX_TIMESTAMP(),
      <if test="schoolPrimaryId != null">
        school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
      </if>
      <if test="aoiId != null">
        aoi_id = #{aoiId,jdbcType=BIGINT},
      </if>
      <if test="aoiName != null">
        aoi_name = #{aoiName,jdbcType=VARCHAR},
      </if>
      <if test="aoiMode != null">
        aoi_mode = #{aoiMode,jdbcType=INTEGER},
      </if>
      <if test="autoSync != null">
        auto_sync = #{autoSync,jdbcType=INTEGER},
      </if>
      <if test="crSource != null">
        cr_source = #{crSource,jdbcType=INTEGER},
      </if>
      <if test="upSource != null">
        up_source = #{upSource,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=TINYINT},
      </if>
      <if test="cuid != null">
        cuid = #{cuid,jdbcType=BIGINT},
      </if>
      <if test="muid != null">
        muid = #{muid,jdbcType=BIGINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=INTEGER},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO">
    update wm_sc_school_area
    set school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
      cr_source = #{crSource,jdbcType=INTEGER},
      up_source = #{upSource,jdbcType=INTEGER},
      valid = #{valid,jdbcType=TINYINT},
      cuid = #{cuid,jdbcType=BIGINT},
      muid = #{muid,jdbcType=BIGINT},
      ctime = #{ctime,jdbcType=INTEGER},
      utime = #{utime,jdbcType=INTEGER},
      area = #{area,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO">
    update wm_sc_school_area
    set school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
      cr_source = #{crSource,jdbcType=INTEGER},
      up_source = #{upSource,jdbcType=INTEGER},
      valid = #{valid,jdbcType=TINYINT},
      cuid = #{cuid,jdbcType=BIGINT},
      muid = #{muid,jdbcType=BIGINT},
      ctime = #{ctime,jdbcType=INTEGER},
      utime = #{utime,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectBySchoolId"
          parameterType="java.lang.Long"
          resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from
        wm_sc_school_area
    where
        school_primary_id = #{schoolId,jdbcType=INTEGER}
    AND
        valid=1;
  </select>

  <select id="selectBySchoolPrimaryIdAndAoiId"
          resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from
        wm_sc_school_area
    where
        valid = 1
    and
        school_primary_id = #{schoolPrimaryId ,jdbcType=INTEGER}
    and
        aoi_id = #{aoiId ,jdbcType=BIGINT};
  </select>

  <select id="selectByCondition"  parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaSearchCondition"
            resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from wm_sc_school_area
      <where>
        valid=1
        <if test="id != null and id!= 0">
          AND id=#{id}
        </if>
        <if test="schoolPrimaryId != null and schoolPrimaryId!= 0">
          AND school_primary_id=#{schoolPrimaryId}
        </if>
      </where>
      order by id asc
      <if test="pageFrom != null and pageSize!= null">
        limit #{pageFrom},#{pageSize}
      </if>
  </select>

  <select id="selectAoiIdListBySchoolPrimaryId"
          parameterType="java.lang.Integer"
          resultType="java.lang.Long">
    select
        aoi_id
    from
        wm_sc_school_area
    where
        valid = 1
    and
        school_primary_id = #{schoolPrimaryId, jdbcType=INTEGER}
    and
        aoi_id > 0;
  </select>

  <update id="invalidByPrimaryKey"
          parameterType="java.lang.Long">
    update
      wm_sc_school_area
    set
      utime = UNIX_TIMESTAMP(),
      valid = 0,
      muid = #{muid,jdbcType=BIGINT}
    where
      id = #{id,jdbcType=BIGINT}
  </update>

</mapper>