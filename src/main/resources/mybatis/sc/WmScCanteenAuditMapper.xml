<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenAuditMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="canteen_id" jdbcType="INTEGER" property="canteenId" />
    <result column="school_id" jdbcType="INTEGER" property="schoolId" />
    <result column="contractor_id" jdbcType="INTEGER" property="contractorId" />
    <result column="canteen_name" jdbcType="VARCHAR" property="canteenName" />
    <result column="canteen_type" jdbcType="INTEGER" property="canteenType" />
    <result column="canteen_attribute" jdbcType="INTEGER" property="canteenAttribute" />
    <result column="grade" jdbcType="INTEGER" property="grade" />
    <result column="canteen_client" jdbcType="VARCHAR" property="canteenClient" />
    <result column="manager" jdbcType="VARCHAR" property="manager" />
    <result column="manager_phone" jdbcType="VARCHAR" property="managerPhone" />
    <result column="canteen_status" jdbcType="INTEGER" property="canteenStatus" />
    <result column="stall_num" jdbcType="INTEGER" property="stallNum" />
    <result column="store_num" jdbcType="INTEGER" property="storeNum" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="responsible_person" jdbcType="VARCHAR" property="responsiblePerson" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="school_name" jdbcType="VARCHAR" property="schoolName" />
    <result column="contractor_name" jdbcType="VARCHAR" property="contractorName" />
    <result column="utime" jdbcType="INTEGER" property="utime" />
    <result column="ctime" jdbcType="INTEGER" property="ctime" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
    <result column="card_type" jdbcType="INTEGER" property="cardType" />
    <result column="card_no" jdbcType="VARCHAR" property="cardNo" />
    <result column="effective" jdbcType="INTEGER" property="effective" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="audit_time" jdbcType="INTEGER" property="auditTime" />
    <result column="audit_result" jdbcType="VARCHAR" property="auditResult" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="audit_user_id" jdbcType="BIGINT" property="auditUserId" />
    <result column="manager_phone_encryption" property="managerPhoneEncryption" jdbcType="VARCHAR"/>
    <result column="manager_phone_token" property="managerPhoneToken" jdbcType="VARCHAR"/>
    <result column="card_no_encryption" property="cardNoEncryption" jdbcType="VARCHAR"/>
    <result column="card_no_token" property="cardNoToken" jdbcType="VARCHAR"/>
    <result column="offline_biz_stall_num" property="offlineBizStallNum" jdbcType="INTEGER"/>
    <result column="pre_online_stall_num" property="preOnlineStallNum" jdbcType="INTEGER"/>
    <result column="canteen_video" property="canteenVideo" jdbcType="VARCHAR"/>
    <result column="stall_num_change_reason" property="stallNumChangeReason" jdbcType="TINYINT"/>
    <result column="stall_num_change_info" property="stallNumChangeInfo" jdbcType="VARCHAR"/>
    <result column="offline_biz_stall_num_change_reason" property="offlineBizStallNumChangeReason" jdbcType="TINYINT"/>
    <result column="offline_biz_stall_num_change_info" property="offlineBizStallNumChangeInfo" jdbcType="VARCHAR"/>
    <result column="gravity_id" property="gravityId" jdbcType="VARCHAR"/>
    <result column="audit_task_type" property="auditTaskType" jdbcType="TINYINT"/>
    <result column="audit_progress_type" property="auditProgressType" jdbcType="TINYINT"/>
    <result column="audit_node" property="auditNode" jdbcType="TINYINT"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, canteen_id, school_id, contractor_id, canteen_name, canteen_type, canteen_attribute,
    grade, canteen_client, manager, manager_phone, canteen_status, stall_num, store_num, 
    city_name, responsible_person, user_id, user_name, school_name, contractor_name, 
    utime, ctime, ext, valid, card_type, card_no, effective, audit_status, audit_time, 
    audit_result, task_id, audit_user_id, manager_phone_encryption,manager_phone_token,card_no_encryption,card_no_token,
    offline_biz_stall_num, pre_online_stall_num, canteen_video, stall_num_change_reason, stall_num_change_info,
    offline_biz_stall_num_change_reason, offline_biz_stall_num_change_info, gravity_id, audit_task_type, audit_progress_type, audit_node
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
        <include refid="Base_Column_List" />
    from
        wm_sc_canteen_audit
    where
        id = #{id,jdbcType=INTEGER}
  </select>


  <select id="selectBySchoolIdAndAuditStatus"
          parameterType="java.lang.Integer"
          resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from
        wm_sc_canteen_audit
    where
        school_id = #{schoolId,jdbcType=INTEGER}
    and
        audit_status = #{auditStatus,jdbcType=INTEGER}
    and
        valid = 1
  </select>

  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO"
          useGeneratedKeys="true" keyProperty="id">
    insert into wm_sc_canteen_audit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="canteenId != null">
        canteen_id,
      </if>
      <if test="schoolId != null">
        school_id,
      </if>
      <if test="contractorId != null">
        contractor_id,
      </if>
      <if test="canteenName != null">
        canteen_name,
      </if>
      <if test="canteenType != null">
        canteen_type,
      </if>
      <if test="canteenAttribute != null">
        canteen_attribute,
      </if>
      <if test="grade != null">
        grade,
      </if>
      <if test="canteenClient != null">
        canteen_client,
      </if>
      <if test="manager != null">
        manager,
      </if>
      <if test="managerPhone != null and notSaveManagerPhone!=1">
        manager_phone,
      </if>
      <if test="canteenStatus != null">
        canteen_status,
      </if>
      <if test="stallNum != null">
        stall_num,
      </if>
      <if test="storeNum != null">
        store_num,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="responsiblePerson != null">
        responsible_person,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="schoolName != null">
        school_name,
      </if>
      <if test="contractorName != null">
        contractor_name,
      </if>
      <if test="utime != null">
        utime,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="cardNo != null  and notSaveCardNo!=1">
        card_no,
      </if>
      <if test="effective != null">
        effective,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="auditResult != null">
        audit_result,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="auditUserId != null">
        audit_user_id,
      </if>
      <if test="managerPhoneEncryption != null ">
        manager_phone_encryption,
      </if>
      <if test="managerPhoneToken != null ">
        manager_phone_token,
      </if>
      <if test="cardNoEncryption != null ">
        card_no_encryption,
      </if>
      <if test="cardNoToken != null ">
        card_no_token,
      </if>
      <if test="offlineBizStallNum != null">
        offline_biz_stall_num,
      </if>
      <if test="preOnlineStallNum != null">
        pre_online_stall_num,
      </if>
      <if test="canteenVideo != null">
        canteen_video,
      </if>
      <if test="stallNumChangeReason != null">
        stall_num_change_reason,
      </if>
      <if test="stallNumChangeInfo != null">
        stall_num_change_info,
      </if>
      <if test="offlineBizStallNumChangeReason != null">
        offline_biz_stall_num_change_reason,
      </if>
      <if test="offlineBizStallNumChangeInfo != null">
        offline_biz_stall_num_change_info,
      </if>
      <if test="gravityId != null">
        gravity_id,
      </if>
      <if test="auditTaskType != null">
        audit_task_type,
      </if>
      <if test="auditProgressType != null">
        audit_progress_type,
      </if>
      <if test="auditNode != null">
        audit_node,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="canteenId != null">
        #{canteenId,jdbcType=INTEGER},
      </if>
      <if test="schoolId != null">
        #{schoolId,jdbcType=INTEGER},
      </if>
      <if test="contractorId != null">
        #{contractorId,jdbcType=INTEGER},
      </if>
      <if test="canteenName != null">
        #{canteenName,jdbcType=VARCHAR},
      </if>
      <if test="canteenType != null">
        #{canteenType,jdbcType=INTEGER},
      </if>
      <if test="canteenAttribute != null">
        #{canteenAttribute,jdbcType=INTEGER},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=INTEGER},
      </if>
      <if test="canteenClient != null">
        #{canteenClient,jdbcType=VARCHAR},
      </if>
      <if test="manager != null">
        #{manager,jdbcType=VARCHAR},
      </if>
      <if test="managerPhone != null and notSaveManagerPhone!=1">
        #{managerPhone,jdbcType=VARCHAR},
      </if>
      <if test="canteenStatus != null">
        #{canteenStatus,jdbcType=INTEGER},
      </if>
      <if test="stallNum != null">
        #{stallNum,jdbcType=INTEGER},
      </if>
      <if test="storeNum != null">
        #{storeNum,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="responsiblePerson != null">
        #{responsiblePerson,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="schoolName != null">
        #{schoolName,jdbcType=VARCHAR},
      </if>
      <if test="contractorName != null">
        #{contractorName,jdbcType=VARCHAR},
      </if>
      <if test="utime != null">
        #{utime,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=INTEGER},
      </if>
      <if test="cardNo != null and notSaveCardNo!=1">
        #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="effective != null">
        #{effective,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=INTEGER},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="auditUserId != null">
        #{auditUserId,jdbcType=BIGINT},
      </if>
      <if test="managerPhoneEncryption != null ">
        #{managerPhoneEncryption},
      </if>
      <if test="managerPhoneToken != null ">
        #{managerPhoneToken},
      </if>
      <if test="cardNoEncryption != null ">
        #{cardNoEncryption},
      </if>
      <if test="cardNoToken != null ">
        #{cardNoToken},
      </if>
      <if test="offlineBizStallNum != null">
        #{offlineBizStallNum},
      </if>
      <if test="preOnlineStallNum != null">
        #{preOnlineStallNum},
      </if>
      <if test="canteenVideo != null">
        #{canteenVideo,jdbcType=VARCHAR},
      </if>
      <if test="stallNumChangeReason != null">
        #{stallNumChangeReason,jdbcType=TINYINT},
      </if>
      <if test="stallNumChangeInfo != null">
        #{stallNumChangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="offlineBizStallNumChangeReason != null">
        #{offlineBizStallNumChangeReason,jdbcType=TINYINT},
      </if>
      <if test="offlineBizStallNumChangeInfo != null">
        #{offlineBizStallNumChangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="gravityId != null">
        #{gravityId,jdbcType=VARCHAR},
      </if>
      <if test="auditTaskType != null">
        #{auditTaskType,jdbcType=TINYINT},
      </if>
      <if test="auditProgressType != null">
        #{auditProgressType,jdbcType=TINYINT},
      </if>
      <if test="auditNode != null">
        #{auditNode,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>


  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO">
    update wm_sc_canteen_audit
    <set>
      <if test="canteenId != null">
        canteen_id = #{canteenId,jdbcType=INTEGER},
      </if>
      <if test="schoolId != null">
        school_id = #{schoolId,jdbcType=INTEGER},
      </if>
      <if test="contractorId != null">
        contractor_id = #{contractorId,jdbcType=INTEGER},
      </if>
      <if test="canteenName != null">
        canteen_name = #{canteenName,jdbcType=VARCHAR},
      </if>
      <if test="canteenType != null">
        canteen_type = #{canteenType,jdbcType=INTEGER},
      </if>
      <if test="canteenAttribute != null">
        canteen_attribute = #{canteenAttribute,jdbcType=INTEGER},
      </if>
      <if test="grade != null">
        grade = #{grade,jdbcType=INTEGER},
      </if>
      <if test="canteenClient != null">
        canteen_client = #{canteenClient,jdbcType=VARCHAR},
      </if>
      <if test="manager != null">
        manager = #{manager,jdbcType=VARCHAR},
      </if>
      <if test="managerPhone != null and notSaveManagerPhone!=1">
        manager_phone = #{managerPhone,jdbcType=VARCHAR},
      </if>
      <if test="canteenStatus != null">
        canteen_status = #{canteenStatus,jdbcType=INTEGER},
      </if>
      <if test="stallNum != null">
        stall_num = #{stallNum,jdbcType=INTEGER},
      </if>
      <if test="storeNum != null">
        store_num = #{storeNum,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="responsiblePerson != null">
        responsible_person = #{responsiblePerson,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="schoolName != null">
        school_name = #{schoolName,jdbcType=VARCHAR},
      </if>
      <if test="contractorName != null">
        contractor_name = #{contractorName,jdbcType=VARCHAR},
      </if>
      <if test="utime != null">
        utime = #{utime,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=INTEGER},
      </if>
      <if test="cardNo != null and notSaveCardNo!=1">
        card_no = #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="effective != null">
        effective = #{effective,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=INTEGER},
      </if>
      <if test="auditResult != null">
        audit_result = #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="auditUserId != null">
        audit_user_id = #{auditUserId,jdbcType=BIGINT},
      </if>
      <if test="managerPhoneEncryption != null ">
        manager_phone_encryption = #{managerPhoneEncryption},
      </if>
      <if test="managerPhoneToken != null ">
        manager_phone_token = #{managerPhoneToken},
      </if>
      <if test="cardNoEncryption != null ">
        card_no_encryption = #{cardNoEncryption},
      </if>
      <if test="cardNoToken != null ">
        card_no_token = #{cardNoToken},
      </if>
      <if test="offlineBizStallNum != null">
        offline_biz_stall_num = #{offlineBizStallNum},
      </if>
      <if test="preOnlineStallNum != null">
        pre_online_stall_num = #{preOnlineStallNum},
      </if>
      <if test="canteenVideo != null">
        canteen_video = #{canteenVideo,jdbcType=VARCHAR},
      </if>
      <if test="stallNumChangeReason != null">
        stall_num_change_reason = #{stallNumChangeReason,jdbcType=TINYINT},
      </if>
      <if test="stallNumChangeInfo != null">
        stall_num_change_info = #{stallNumChangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="offlineBizStallNumChangeReason != null">
        offline_biz_stall_num_change_reason = #{offlineBizStallNumChangeReason,jdbcType=TINYINT},
      </if>
      <if test="offlineBizStallNumChangeInfo != null">
        offline_biz_stall_num_change_info = #{offlineBizStallNumChangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="gravityId != null">
        gravity_id = #{gravityId,jdbcType=VARCHAR},
      </if>
      <if test="auditTaskType != null">
        audit_task_type = #{auditTaskType,jdbcType=TINYINT},
      </if>
      <if test="auditProgressType != null">
        audit_progress_type = #{auditProgressType,jdbcType=TINYINT},
      </if>
      <if test="auditNode != null">
        audit_node = #{auditNode,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByCanteenId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from
        wm_sc_canteen_audit
    where
        canteen_id = #{canteenId,jdbcType=INTEGER}
    and
        valid = 1
  </select>


  <select id="selectLatestByCanteenPrimaryId"
          parameterType="java.lang.Integer"
          resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from
        wm_sc_canteen_audit
    where
        canteen_id = #{canteenPrimaryId,jdbcType=INTEGER}
    order by
        id desc
    limit 1
  </select>

  <select id="selectByTaskId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wm_sc_canteen_audit
    where task_id = #{taskId,jdbcType=INTEGER}
  </select>

  <update id="invalidByCanteenId" parameterType="java.lang.Integer">
    update wm_sc_canteen_audit
    set valid = 0
    where canteen_id = #{canteenId,jdbcType=INTEGER}
  </update>

</mapper>