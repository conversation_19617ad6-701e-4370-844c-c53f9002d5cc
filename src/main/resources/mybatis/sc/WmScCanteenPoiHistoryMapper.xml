<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiHistoryMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiHistoryDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="canteen_id_from" property="canteenIdFrom" jdbcType="INTEGER"/>
        <result column="canteen_id_to" property="canteenIdTo" jdbcType="INTEGER"/>
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="operate_type" property="operateType" jdbcType="TINYINT"/>
        <result column="operate_source" property="operateSource" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, canteen_id_from, canteen_id_to, wm_poi_id, ctime, user_id, user_name, operate_type, 
    operate_source
  </sql>


    <insert id="batchInsert">
        <if test="items.get(0) != null">
            insert into wm_sc_canteen_poi_history (canteen_id_from,canteen_id_to, wm_poi_id, ctime,
            user_id, user_name,operate_type,operate_source)
            values
            <foreach collection="items" item="item" index="index" separator=",">
                (#{item.canteenIdFrom,jdbcType=INTEGER},
                #{item.canteenIdTo,jdbcType=INTEGER},#{item.wmPoiId,jdbcType=BIGINT}, #{item.ctime,jdbcType=INTEGER},
                #{item.userId,jdbcType=INTEGER}, #{item.userName,jdbcType=VARCHAR},#{item.operateType,jdbcType=TINYINT},
                #{item.operateSource,jdbcType=TINYINT})
            </foreach>
        </if>
    </insert>

    <insert id="batchInsertBindPoiHistory">
        <if test="items.get(0) != null">
            insert into wm_sc_canteen_poi_history (canteen_id_to, wm_poi_id, ctime,
            user_id, user_name,operate_type,operate_source)
            values
            <foreach collection="items" item="item" index="index" separator=",">
                (#{item.canteenIdTo,jdbcType=INTEGER},#{item.wmPoiId,jdbcType=BIGINT}, #{item.ctime,jdbcType=INTEGER},
                #{item.userId,jdbcType=INTEGER}, #{item.userName,jdbcType=VARCHAR},#{item.operateType,jdbcType=TINYINT},
                #{item.operateSource,jdbcType=TINYINT})
            </foreach>
        </if>
    </insert>

    <select id="selectByCondition"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiHistorySearchCondition"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_history
        where 1=1
        <if test="canteenIdFrom != null">
            and canteen_id_from = #{canteenIdFrom}
        </if>
        <if test="canteenIdTo != null">
            and canteen_id_to = #{canteenIdTo}
        </if>
        <if test="wmPoiId != null">
            and wm_poi_id = #{wmPoiId}
        </if>
        <if test="operateType != null">
            and operate_type = #{operateType}
        </if>
        <if test="operateSource != null">
            and operate_source = #{operateSource}
        </if>
    </select>

    <select id="getAddTagNum" resultType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiHistoryBO">
        select wm_poi_id as wmPoiId,count(1) as addTagNum
        from wm_sc_canteen_poi_history
        where 1=1
        <!--<if test="operateType != null">
            and operate_type = #{operateType}
        </if>-->
        and
        operate_type in (1, 2)
        <if test="wmPoiIdList != null and wmPoiIdList.size() > 0">
            and wm_poi_id in
            <foreach collection="wmPoiIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by wm_poi_id
    </select>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiHistoryDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_sc_canteen_poi_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            <if test="id != null">
                id,
            </if>
            <if test="canteenIdFrom != null">
                canteen_id_from,
            </if>
            <if test="canteenIdTo != null">
                canteen_id_to,
            </if>
            <if test="wmPoiId != null">
                wm_poi_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="operateType != null">
                operate_type,
            </if>
            <if test="operateSource != null">
                operate_source,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="canteenIdFrom != null">
                #{canteenIdFrom,jdbcType=INTEGER},
            </if>
            <if test="canteenIdTo != null">
                #{canteenIdTo,jdbcType=INTEGER},
            </if>
            <if test="wmPoiId != null">
                #{wmPoiId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                #{operateType,jdbcType=TINYINT},
            </if>
            <if test="operateSource != null">
                #{operateSource,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

</mapper>