<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScLogRecordMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScLogRecordDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="business_id" jdbcType="BIGINT" property="businessId"/>
        <result column="log_category" jdbcType="TINYINT" property="logCategory"/>
        <result column="operate_type" jdbcType="VARCHAR" property="operateType"/>
        <result column="op_id" jdbcType="BIGINT" property="opId"/>
        <result column="op_name" jdbcType="VARCHAR" property="opName"/>
        <result column="ctime" jdbcType="INTEGER" property="ctime"/>
        <result column="log_message" jdbcType="LONGVARCHAR" property="logMessage"/>
        <result column="log_diff_value" jdbcType="LONGVARCHAR" property="logDiffValue"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, business_id, log_category, log_message,log_diff_value,operate_type, op_id, op_name, ctime
  </sql>


    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScLogRecordDO">
        insert into wm_sc_log_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            <if test="id != null">
                id,
            </if>
            <if test="businessId != null">
                business_id,
            </if>
            <if test="logCategory != null">
                log_category,
            </if>
            <if test="operateType != null">
                operate_type,
            </if>
            <if test="opId != null">
                op_id,
            </if>
            <if test="opName != null">
                op_name,
            </if>
            <if test="logMessage != null">
                log_message,
            </if>
            <if test="logDiffValue != null">
                log_diff_value,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id},
            </if>
            <if test="businessId != null">
                #{businessId},
            </if>
            <if test="logCategory != null">
                #{logCategory},
            </if>
            <if test="operateType != null">
                #{operateType,jdbcType=VARCHAR},
            </if>
            <if test="opId != null">
                #{opId},
            </if>
            <if test="opName != null">
                #{opName,jdbcType=VARCHAR},
            </if>
            <if test="logMessage != null">
                #{logMessage},
            </if>
            <if test="logDiffValue != null">
                #{logDiffValue},
            </if>
        </trim>
    </insert>

</mapper>