<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiTaskAuditMinutiaMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="canteen_poi_task_id" property="canteenPoiTaskId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
        <result column="audit_node" property="auditNode" jdbcType="TINYINT"/>
        <result column="audit_segment" property="auditSegment" jdbcType="VARCHAR"/>
        <result column="audit_system_type" property="auditSystemType" jdbcType="TINYINT"/>
        <result column="audit_system_id" property="auditSystemId" jdbcType="VARCHAR"/>
        <result column="audit_result" property="auditResult" jdbcType="TINYINT"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="audit_remark" property="auditRemark" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, canteen_poi_task_id, user_id, user_name, ctime, utime, audit_node, audit_segment, 
    audit_system_type, audit_system_id, audit_result, valid,audit_remark
  </sql>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into wm_sc_canteen_poi_task_audit_minutia
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="canteenPoiTaskId != null">
                canteen_poi_task_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="utime != null">
                utime,
            </if>
            <if test="auditNode != null">
                audit_node,
            </if>
            <if test="auditSegment != null">
                audit_segment,
            </if>
            <if test="auditSystemType != null">
                audit_system_type,
            </if>
            <if test="auditSystemId != null">
                audit_system_id,
            </if>
            <if test="auditResult != null">
                audit_result,
            </if>
            <if test="valid != null">
                valid,
            </if>
            <if test="auditRemark != null">
                audit_remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="canteenPoiTaskId != null">
                #{canteenPoiTaskId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="ctime != null">
                #{ctime,jdbcType=INTEGER},
            </if>
            <if test="utime != null">
                #{utime,jdbcType=INTEGER},
            </if>
            <if test="auditNode != null">
                #{auditNode,jdbcType=TINYINT},
            </if>
            <if test="auditSegment != null">
                #{auditSegment,jdbcType=VARCHAR},
            </if>
            <if test="auditSystemType != null">
                #{auditSystemType,jdbcType=TINYINT},
            </if>
            <if test="auditSystemId != null">
                #{auditSystemId,jdbcType=VARCHAR},
            </if>
            <if test="auditResult != null">
                #{auditResult,jdbcType=TINYINT},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=TINYINT},
            </if>
            <if test="auditRemark != null">
                #{auditRemark,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaDO">
        update wm_sc_canteen_poi_task_audit_minutia
        <set>
            <if test="canteenPoiTaskId != null">
                canteen_poi_task_id = #{canteenPoiTaskId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=INTEGER},
            </if>
            utime = UNIX_TIMESTAMP(),
            <if test="auditNode != null">
                audit_node = #{auditNode,jdbcType=TINYINT},
            </if>
            <if test="auditSegment != null">
                audit_segment = #{auditSegment,jdbcType=VARCHAR},
            </if>
            <if test="auditSystemType != null">
                audit_system_type = #{auditSystemType,jdbcType=TINYINT},
            </if>
            <if test="auditSystemId != null">
                audit_system_id = #{auditSystemId,jdbcType=VARCHAR},
            </if>
            <if test="auditResult != null">
                audit_result = #{auditResult,jdbcType=TINYINT},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            <if test="auditRemark != null">
                audit_remark = #{auditRemark,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByCanPoiTaskId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_task_audit_minutia
        where canteen_poi_task_id = #{taskId,jdbcType=BIGINT}
        and valid =1
    </select>
    <select id="selectByCondition" resultMap="BaseResultMap"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaSearchCondition">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_task_audit_minutia
        where valid=1
        <if test="canteenPoiTaskId != null">
            and canteen_poi_task_id=#{canteenPoiTaskId}
        </if>
        <if test="auditSystemType != null">
            and audit_system_type=#{auditSystemType}
        </if>
        <if test="auditSystemId != null">
            and audit_system_id=#{auditSystemId}
        </if>
        <if test="auditResult != null">
            and audit_result=#{auditResult}
        </if>
        <if test="auditNode != null">
            and audit_node=#{auditNode}
        </if>
    </select>

    <select id="selectByAuditSystem" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_task_audit_minutia
        where audit_system_type = #{auditSystemType}
        and audit_system_id = #{auditSystemId}
        and valid =1
        order by id desc
        limit 1
    </select>

    <update id="updateUnValidByTaskId"  parameterType="java.lang.Long">
       update wm_sc_canteen_poi_task_audit_minutia set valid = 0 where canteen_poi_task_id=#{canteenPoiTaskId}
    </update>
</mapper>