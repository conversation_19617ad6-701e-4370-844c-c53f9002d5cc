<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmScDisplayFieldsMapper">
    <!-- BaseResultMap -->
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmScDisplayFieldsDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="display_field" property="displayField" jdbcType="VARCHAR"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- Base_Column_List -->
    <sql id="Base_Column_List">
        id, user_id, display_field, add_time, update_time
    </sql>

    <!-- 根据userId查询 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_sc_display_fields
        WHERE user_id = #{userId,jdbcType=BIGINT}
    </select>

    <!-- 新增记录 -->
    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmScDisplayFieldsDO">
        INSERT INTO wm_sc_display_fields
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="displayField != null">
                display_field,
            </if>
            add_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="displayField != null">
                #{displayField,jdbcType=VARCHAR},
            </if>
            NOW(),
            NOW()
        </trim>
    </insert>

    <!-- 更新记录 -->
    <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmScDisplayFieldsDO">
        UPDATE wm_sc_display_fields
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="displayField != null">
                display_field = #{displayField,jdbcType=VARCHAR},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

</mapper>