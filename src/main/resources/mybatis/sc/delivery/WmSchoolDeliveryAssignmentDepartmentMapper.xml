<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryAssignmentDepartmentMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDepartmentDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="delivery_id" property="deliveryId" jdbcType="INTEGER"/>
        <result column="school_primary_id" property="schoolPrimaryId" jdbcType="INTEGER"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="department_intension" property="departmentIntension" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="muid" property="muid" jdbcType="BIGINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, delivery_id, school_primary_id, department_name, department_intension, valid, cuid, muid, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_school_delivery_assignment_department
        where
            id = #{id,jdbcType=BIGINT}
        and
            valid = 1
    </select>

    <select id="selectBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_assignment_department
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>

    <select id="selectByDeliveryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_assignment_department
        where
            delivery_id = #{deliveryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>

    <insert id="batchInsertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDepartmentDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_school_delivery_assignment_department
        (ctime, utime, delivery_id, school_primary_id, department_name, department_intension, cuid, muid)
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            #{item.deliveryId,jdbcType=INTEGER},
            #{item.schoolPrimaryId,jdbcType=INTEGER},
            #{item.departmentName,jdbcType=VARCHAR},
            #{item.departmentIntension,jdbcType=INTEGER},
            #{item.cuid,jdbcType=INTEGER},
            #{item.muid,jdbcType=INTEGER}
        )
        </foreach>
    </insert>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDepartmentDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_school_delivery_assignment_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id,
            </if>
            <if test="departmentName != null">
                department_name,
            </if>
            <if test="departmentIntension != null">
                department_intension,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deliveryId != null">
                #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="departmentName != null">
                #{departmentName,jdbcType=VARCHAR},
            </if>
            <if test="departmentIntension != null">
                #{departmentIntension,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDepartmentDO">
        update
            wm_school_delivery_assignment_department
        <set>
            utime = UNIX_TIMESTAMP(),
            <if test="deliveryId != null">
                delivery_id = #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="departmentName != null">
                department_name = #{departmentName,jdbcType=VARCHAR},
            </if>
            <if test="departmentIntension != null">
                department_intension = #{departmentIntension,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=BIGINT},
            </if>
        </set>
        where
            id = #{id,jdbcType=BIGINT}
    </update>


    <update id="deleteByPrimaryIdList"
            parameterType="java.util.List">
        update
            wm_school_delivery_assignment_department
        set
            valid = 0
        where
            id in
            <foreach collection="primaryIdList" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
    </update>
</mapper>