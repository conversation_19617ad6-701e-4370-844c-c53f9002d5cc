<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryGoalSetMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryGoalSetDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="delivery_id" property="deliveryId" jdbcType="INTEGER"/>
        <result column="school_primary_id" property="schoolPrimaryId" jdbcType="INTEGER"/>
        <result column="contact_user_ids" property="contactUserIds" jdbcType="VARCHAR"/>
        <result column="insc_online_sign_exptime" property="inscOnlineSignExptime" jdbcType="VARCHAR"/>
        <result column="insc_offline_build_exptime" property="inscOfflineBuildExptime" jdbcType="VARCHAR"/>
        <result column="build_off_campus_station" property="buildOffCampusStation" jdbcType="INTEGER"/>
        <result column="outsc_online_sign_exptime" property="outscOnlineSignExptime" jdbcType="VARCHAR"/>
        <result column="outsc_offline_build_exptime" property="outscOfflineBuildExptime" jdbcType="VARCHAR"/>
        <result column="ontime_rate_target" property="ontimeRateTarget" jdbcType="VARCHAR"/>
        <result column="ontime_rate_target_exptime" property="ontimeRateTargetExptime" jdbcType="VARCHAR"/>
        <result column="avg_delivery_time_target" property="avgDeliveryTimeTarget" jdbcType="VARCHAR"/>
        <result column="avg_delivery_time_target_exptime" property="avgDeliveryTimeTargetExptime" jdbcType="VARCHAR"/>
        <result column="first_stall_bulid_exptime" property="firstStallBulidExptime" jdbcType="VARCHAR"/>
        <result column="first_stall_online_exptime" property="firstStallOnlineExptime" jdbcType="VARCHAR"/>
        <result column="first_online_stall_num" property="firstOnlineStallNum" jdbcType="INTEGER"/>
        <result column="online_penerate_target" property="onlinePenerateTarget" jdbcType="VARCHAR"/>
        <result column="online_penerate_target_exptime" property="onlinePenerateTargetExptime" jdbcType="VARCHAR"/>
        <result column="daily_order_target" property="dailyOrderTarget" jdbcType="VARCHAR"/>
        <result column="daily_order_target_exptime" property="dailyOrderTargetExptime" jdbcType="VARCHAR"/>
        <result column="meal_penerate_target" property="mealPenerateTarget" jdbcType="VARCHAR"/>
        <result column="meal_penerate_target_exptime" property="mealPenerateTargetExptime" jdbcType="VARCHAR"/>
        <result column="daily_yield_target" property="dailyYieldTarget" jdbcType="VARCHAR"/>
        <result column="daily_yield_target_exptime" property="dailyYieldTargetExptime" jdbcType="VARCHAR"/>
        <result column="data_version" property="dataVersion" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="muid" property="muid" jdbcType="BIGINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, delivery_id, school_primary_id, contact_user_ids, insc_online_sign_exptime, insc_offline_build_exptime,
        build_off_campus_station, outsc_online_sign_exptime, outsc_offline_build_exptime, ontime_rate_target,
        ontime_rate_target_exptime, avg_delivery_time_target, avg_delivery_time_target_exptime, first_stall_bulid_exptime,
        first_stall_online_exptime, first_online_stall_num, online_penerate_target, online_penerate_target_exptime,
        daily_order_target, daily_order_target_exptime, meal_penerate_target, meal_penerate_target_exptime, daily_yield_target,
        daily_yield_target_exptime, data_version, valid, cuid, muid, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_school_delivery_goalset
        where
            id = #{id,jdbcType=BIGINT}
        and
            valid = 1
    </select>

    <select id="selectBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_goalset
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>

    <select id="selectByDeliveryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_goalset
        where
            delivery_id = #{deliveryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryGoalSetDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_school_delivery_goalset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id,
            </if>
            <if test="contactUserIds != null">
                contact_user_ids,
            </if>
            <if test="inscOnlineSignExptime != null">
                insc_online_sign_exptime,
            </if>
            <if test="inscOfflineBuildExptime != null">
                insc_offline_build_exptime,
            </if>
            <if test="buildOffCampusStation != null">
                build_off_campus_station,
            </if>
            <if test="outscOnlineSignExptime != null">
                outsc_online_sign_exptime,
            </if>
            <if test="outscOfflineBuildExptime != null">
                outsc_offline_build_exptime,
            </if>
            <if test="ontimeRateTarget != null">
                ontime_rate_target,
            </if>
            <if test="ontimeRateTargetExptime != null">
                ontime_rate_target_exptime,
            </if>
            <if test="avgDeliveryTimeTarget != null">
                avg_delivery_time_target,
            </if>
            <if test="avgDeliveryTimeTargetExptime != null">
                avg_delivery_time_target_exptime,
            </if>
            <if test="firstStallBulidExptime != null">
                first_stall_bulid_exptime,
            </if>
            <if test="firstStallOnlineExptime != null">
                first_stall_online_exptime,
            </if>
            <if test="firstOnlineStallNum != null">
                first_online_stall_num,
            </if>
            <if test="onlinePenerateTarget != null">
                online_penerate_target,
            </if>
            <if test="onlinePenerateTargetExptime != null">
                online_penerate_target_exptime,
            </if>
            <if test="dailyOrderTarget != null">
                daily_order_target,
            </if>
            <if test="dailyOrderTargetExptime != null">
                daily_order_target_exptime,
            </if>
            <if test="mealPenerateTarget != null">
                meal_penerate_target,
            </if>
            <if test="mealPenerateTargetExptime != null">
                meal_penerate_target_exptime,
            </if>
            <if test="dailyYieldTarget != null">
                daily_yield_target,
            </if>
            <if test="dailyYieldTargetExptime != null">
                daily_yield_target_exptime,
            </if>
            <if test="dataVersion != null">
                data_version,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deliveryId != null">
                #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="contactUserIds != null">
                #{contactUserIds,jdbcType=VARCHAR},
            </if>
            <if test="inscOnlineSignExptime != null">
                #{inscOnlineSignExptime,jdbcType=VARCHAR},
            </if>
            <if test="inscOfflineBuildExptime != null">
                #{inscOfflineBuildExptime,jdbcType=VARCHAR},
            </if>
            <if test="buildOffCampusStation != null">
                #{buildOffCampusStation,jdbcType=INTEGER},
            </if>
            <if test="outscOnlineSignExptime != null">
                #{outscOnlineSignExptime,jdbcType=VARCHAR},
            </if>
            <if test="outscOfflineBuildExptime != null">
                #{outscOfflineBuildExptime,jdbcType=VARCHAR},
            </if>
            <if test="ontimeRateTarget != null">
                #{ontimeRateTarget,jdbcType=VARCHAR},
            </if>
            <if test="ontimeRateTargetExptime != null">
                #{ontimeRateTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="avgDeliveryTimeTarget != null">
                #{avgDeliveryTimeTarget,jdbcType=VARCHAR},
            </if>
            <if test="avgDeliveryTimeTargetExptime != null">
                #{avgDeliveryTimeTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="firstStallBulidExptime != null">
                #{firstStallBulidExptime,jdbcType=VARCHAR},
            </if>
            <if test="firstStallOnlineExptime != null">
                #{firstStallOnlineExptime,jdbcType=VARCHAR},
            </if>
            <if test="firstOnlineStallNum != null">
                #{firstOnlineStallNum,jdbcType=INTEGER},
            </if>
            <if test="onlinePenerateTarget != null">
                #{onlinePenerateTarget,jdbcType=VARCHAR},
            </if>
            <if test="onlinePenerateTargetExptime != null">
                #{onlinePenerateTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="dailyOrderTarget != null">
                #{dailyOrderTarget,jdbcType=VARCHAR},
            </if>
            <if test="dailyOrderTargetExptime != null">
                #{dailyOrderTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="mealPenerateTarget != null">
                #{mealPenerateTarget,jdbcType=VARCHAR},
            </if>
            <if test="mealPenerateTargetExptime != null">
                #{mealPenerateTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="dailyYieldTarget != null">
                #{dailyYieldTarget,jdbcType=VARCHAR},
            </if>
            <if test="dailyYieldTargetExptime != null">
                #{dailyYieldTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="dataVersion != null">
                #{dataVersion,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryGoalSetDO">
        update
            wm_school_delivery_goalset
        <set>
            utime = UNIX_TIMESTAMP(),
            <if test="deliveryId != null">
                delivery_id = #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="contactUserIds != null">
                contact_user_ids = #{contactUserIds,jdbcType=VARCHAR},
            </if>
            <if test="inscOnlineSignExptime != null">
                insc_online_sign_exptime = #{inscOnlineSignExptime,jdbcType=VARCHAR},
            </if>
            <if test="inscOfflineBuildExptime != null">
                insc_offline_build_exptime = #{inscOfflineBuildExptime,jdbcType=VARCHAR},
            </if>
            <if test="buildOffCampusStation != null">
                build_off_campus_station = #{buildOffCampusStation,jdbcType=INTEGER},
            </if>
            <if test="outscOnlineSignExptime != null">
                outsc_online_sign_exptime = #{outscOnlineSignExptime,jdbcType=VARCHAR},
            </if>
            <if test="outscOfflineBuildExptime != null">
                outsc_offline_build_exptime = #{outscOfflineBuildExptime,jdbcType=VARCHAR},
            </if>
            <if test="ontimeRateTarget != null">
                ontime_rate_target = #{ontimeRateTarget,jdbcType=VARCHAR},
            </if>
            <if test="ontimeRateTargetExptime != null">
                ontime_rate_target_exptime = #{ontimeRateTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="avgDeliveryTimeTarget != null">
                avg_delivery_time_target = #{avgDeliveryTimeTarget,jdbcType=VARCHAR},
            </if>
            <if test="avgDeliveryTimeTargetExptime != null">
                avg_delivery_time_target_exptime = #{avgDeliveryTimeTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="firstStallBulidExptime != null">
                first_stall_bulid_exptime = #{firstStallBulidExptime,jdbcType=VARCHAR},
            </if>
            <if test="firstStallOnlineExptime != null">
                first_stall_online_exptime = #{firstStallOnlineExptime,jdbcType=VARCHAR},
            </if>
            <if test="firstOnlineStallNum != null">
                first_online_stall_num = #{firstOnlineStallNum,jdbcType=INTEGER},
            </if>
            <if test="onlinePenerateTarget != null">
                online_penerate_target = #{onlinePenerateTarget,jdbcType=VARCHAR},
            </if>
            <if test="onlinePenerateTargetExptime != null">
                online_penerate_target_exptime = #{onlinePenerateTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="dailyOrderTarget != null">
                daily_order_target = #{dailyOrderTarget,jdbcType=VARCHAR},
            </if>
            <if test="dailyOrderTargetExptime != null">
                daily_order_target_exptime = #{dailyOrderTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="mealPenerateTarget != null">
                meal_penerate_target = #{mealPenerateTarget,jdbcType=VARCHAR},
            </if>
            <if test="mealPenerateTargetExptime != null">
                meal_penerate_target_exptime = #{mealPenerateTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="dailyYieldTarget != null">
                daily_yield_target = #{dailyYieldTarget,jdbcType=VARCHAR},
            </if>
            <if test="dailyYieldTargetExptime != null">
                daily_yield_target_exptime = #{dailyYieldTargetExptime,jdbcType=VARCHAR},
            </if>
            <if test="dataVersion != null">
                data_version = #{dataVersion,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=BIGINT},
            </if>
        </set>
        where
            id = #{id,jdbcType=BIGINT}
    </update>

</mapper>