<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryAuditTaskMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="delivery_id" property="deliveryId" jdbcType="INTEGER"/>
        <result column="school_primary_id" property="schoolPrimaryId" jdbcType="INTEGER"/>
        <result column="gravity_id" property="gravityId" jdbcType="VARCHAR"/>
        <result column="delivery_node_type" property="deliveryNodeType" jdbcType="INTEGER"/>
        <result column="audit_task_type" property="auditTaskType" jdbcType="INTEGER"/>
        <result column="audit_node" property="auditNode" jdbcType="INTEGER"/>
        <result column="last_audit_status" property="lastAuditStatus" jdbcType="INTEGER"/>
        <result column="audit_result" property="auditResult" jdbcType="INTEGER"/>
        <result column="data_version" property="dataVersion" jdbcType="INTEGER"/>
        <result column="last_data_version" property="lastDataVersion" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="cmis" property="cmis" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, delivery_id, gravity_id, school_primary_id, delivery_node_type, audit_task_type, audit_node, last_audit_status,
            audit_result, data_version, last_data_version, valid, cuid, cmis, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_school_delivery_audit_task
        where
            id = #{id,jdbcType=BIGINT}
        and
            valid = 1
    </select>

    <select id="selectBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_audit_task
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>

    <select id="selectAuditingTaskBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_audit_task
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
        and
            audit_result = 0
    </select>


    <select id="selectAuditingTaskByDeliveryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_audit_task
        where
            delivery_id = #{deliveryId,jdbcType=INTEGER}
        and
            valid = 1
        and
            audit_result = 0
    </select>

    <select id="selectLastFollowUpAuditTaskBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_audit_task
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
        and
            audit_task_type in (5,6)
        order by
            id desc
        limit 1;
    </select>

    <select id="selectByDeliveryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_audit_task
        where
            delivery_id = #{deliveryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>

    <select id="selectLastestAuditTaskByDeliveryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_audit_task
        where
            delivery_id = #{deliveryId,jdbcType=INTEGER}
        and
            valid = 1
        order by
            id desc
        limit 1
    </select>

    <select id="selectAuditingTaskByDeliveryNodeType"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_audit_task
        where
            delivery_node_type = #{deliveryNodeType,jdbcType=INTEGER}
        and
            audit_result = 0
        and
            valid = 1;
    </select>


    <select id="selectLatestAuditTaskByDeliveryIdAndDeliveryNodeType"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_audit_task
        where
            delivery_node_type = #{deliveryNodeType,jdbcType=INTEGER}
        and
            delivery_id = #{deliveryId,jdbcType=INTEGER}
        and
            valid = 1
        order by
            id desc
        limit 1;
    </select>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_school_delivery_audit_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id,
            </if>
            <if test="gravityId != null">
                gravity_id,
            </if>
            <if test="deliveryNodeType != null">
                delivery_node_type,
            </if>
            <if test="auditTaskType != null">
                audit_task_type,
            </if>
            <if test="auditNode != null">
                audit_node,
            </if>
            <if test="lastAuditStatus != null">
                last_audit_status,
            </if>
            <if test="auditResult != null">
                audit_result,
            </if>
            <if test="dataVersion != null" >
                data_version,
            </if>
            <if test="lastDataVersion != null">
                last_data_version,
            </if>
            <if test="valid != null">
                valid,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="cmis != null">
                cmis,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deliveryId != null">
                #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="gravityId != null">
                #{gravityId,jdbcType=VARCHAR},
            </if>
            <if test="deliveryNodeType != null">
                #{deliveryNodeType,jdbcType=INTEGER},
            </if>
            <if test="auditTaskType != null">
                #{auditTaskType,jdbcType=INTEGER},
            </if>
            <if test="auditNode != null">
                #{auditNode,jdbcType=INTEGER},
            </if>
            <if test="lastAuditStatus != null">
                #{lastAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="auditResult != null">
                #{auditResult,jdbcType=INTEGER},
            </if>
            <if test="dataVersion != null" >
                #{dataVersion,jdbcType=INTEGER},
            </if>
            <if test="lastDataVersion != null">
                #{lastDataVersion,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=BIGINT},
            </if>
            <if test="cmis != null">
                #{cmis,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskDO">
        update
            wm_school_delivery_audit_task
        <set>
            utime = UNIX_TIMESTAMP(),
            <if test="deliveryId != null">
                delivery_id = #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="gravityId != null">
                gravity_id = #{gravityId,jdbcType=VARCHAR},
            </if>
            <if test="deliveryNodeType != null">
                delivery_node_type = #{deliveryNodeType,jdbcType=INTEGER},
            </if>
            <if test="auditTaskType != null">
                audit_task_type = #{auditTaskType,jdbcType=INTEGER},
            </if>
            <if test="auditNode != null">
                audit_node = #{auditNode,jdbcType=INTEGER},
            </if>
            <if test="lastAuditStatus != null">
                last_audit_status = #{lastAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="auditResult != null">
                audit_result = #{auditResult,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=BIGINT},
            </if>
            <if test="cmis != null">
                cmis = #{cmis,jdbcType=VARCHAR},
            </if>
        </set>
        where
            id = #{id,jdbcType=BIGINT}
    </update>

</mapper>