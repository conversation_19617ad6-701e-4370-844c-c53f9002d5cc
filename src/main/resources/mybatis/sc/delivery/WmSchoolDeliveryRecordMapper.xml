<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryRecordMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryRecord">
    <id column="id" jdbcType="BIGINT" property="deliverId" />
    <result column="deep_delivery_finish_timestamp" jdbcType="BIGINT" property="deepDeliveryFinishTimestamp" />
    <result column="order_60_target_completion_timestamp" jdbcType="BIGINT" property="order60TargetCompletionTimestamp" />
    <result column="order_90_target_completion_timestamp" jdbcType="BIGINT" property="order90TargetCompletionTimestamp" />
  </resultMap>

  <sql id="Base_Column_List">
    id, deep_delivery_finish_timestamp, order_60_target_completion_timestamp, order_90_target_completion_timestamp
  </sql>

  <select id="selectByIds" parameterType="java.util.List" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wm_school_delivery_record
    where id in
    <foreach collection="ids" separator="," item="item" close=")" open="(">
      #{item}
    </foreach>
  </select>

  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryRecord">
    insert into wm_school_delivery_record (id, deep_delivery_finish_timestamp, order_60_target_completion_timestamp, 
      order_90_target_completion_timestamp)
    values (#{deliverId,jdbcType=BIGINT}, #{deepDeliveryFinishTimestamp,jdbcType=BIGINT}, #{order60TargetCompletionTimestamp,jdbcType=BIGINT},
      #{order90TargetCompletionTimestamp,jdbcType=BIGINT})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryRecord">
    update wm_school_delivery_record
    <set>
      <if test="deepDeliveryFinishTimestamp != null">
        deep_delivery_finish_timestamp = #{deepDeliveryFinishTimestamp,jdbcType=BIGINT},
      </if>
      <if test="order60TargetCompletionTimestamp != null">
        order_60_target_completion_timestamp = #{order60TargetCompletionTimestamp,jdbcType=BIGINT},
      </if>
      <if test="order90TargetCompletionTimestamp != null">
        order_90_target_completion_timestamp = #{order90TargetCompletionTimestamp,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{deliverId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryRecord">
    update wm_school_delivery_record
    set deep_delivery_finish_timestamp = #{deepDeliveryFinishTimestamp,jdbcType=BIGINT},
      order_60_target_completion_timestamp = #{order60TargetCompletionTimestamp,jdbcType=BIGINT},
      order_90_target_completion_timestamp = #{order90TargetCompletionTimestamp,jdbcType=BIGINT}
    where id = #{deliverId,jdbcType=BIGINT}
  </update>
</mapper>