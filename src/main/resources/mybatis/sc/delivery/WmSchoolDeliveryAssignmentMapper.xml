<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryAssignmentMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="delivery_id" property="deliveryId" jdbcType="INTEGER"/>
        <result column="school_primary_id" property="schoolPrimaryId" jdbcType="INTEGER"/>
        <result column="initiator_uid" property="initiatorUid" jdbcType="BIGINT"/>
        <result column="initiation_time" property="initiationTime" jdbcType="INTEGER"/>
        <result column="key_decision_user_id" property="keyDecisionUserId" jdbcType="VARCHAR"/>
        <result column="sign_partner_user_id" property="signPartnerUserId" jdbcType="VARCHAR"/>
        <result column="extra_support" property="extraSupport" jdbcType="VARCHAR"/>
        <result column="public_stall_num" property="publicStallNum" jdbcType="INTEGER"/>
        <result column="deliverable_stall_num" property="deliverableStallNum" jdbcType="INTEGER"/>
        <result column="need_food_cabinet" property="needFoodCabinet" jdbcType="INTEGER"/>
        <result column="need_food_cabinet_num" property="needFoodCabinetNum" jdbcType="INTEGER"/>
        <result column="material_demand" property="materialDemand" jdbcType="VARCHAR"/>
        <result column="need_printer_num" property="needPrinterNum" jdbcType="INTEGER"/>
        <result column="need_display_rack_num" property="needDisplayRackNum" jdbcType="INTEGER"/>
        <result column="need_sunshade_num" property="needSunshadeNum" jdbcType="INTEGER"/>
        <result column="need_trash_can_num" property="needTrashCanNum" jdbcType="INTEGER"/>
        <result column="other_material_demand" property="otherMaterialDemand" jdbcType="VARCHAR"/>
        <result column="other_cooperation_demand" property="otherCooperationDemand" jdbcType="VARCHAR"/>
        <result column="csm_uid" property="csmUid" jdbcType="BIGINT"/>
        <result column="aorm_uid" property="aormUid" jdbcType="BIGINT"/>
        <result column="acm_uid" property="acmUid" jdbcType="BIGINT"/>
        <result column="data_version" property="dataVersion" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="muid" property="muid" jdbcType="BIGINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, delivery_id, school_primary_id, initiator_uid, initiation_time, key_decision_user_id, sign_partner_user_id,
        extra_support, public_stall_num, deliverable_stall_num, need_food_cabinet, need_food_cabinet_num,
        material_demand, need_printer_num, need_display_rack_num, need_sunshade_num, need_trash_can_num, other_material_demand,
        other_cooperation_demand, csm_uid, aorm_uid, acm_uid, data_version, valid, cuid, muid, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_school_delivery_assignment
        where
            id = #{id,jdbcType=BIGINT}
        and
            valid = 1
    </select>

    <select id="selectBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_assignment
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>

    <select id="selectByDeliveryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_assignment
        where
            delivery_id = #{deliveryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>

    <select id="selectByCSMUidList"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_assignment
        where
            csm_uid in
            <foreach collection="csmUidList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        and
            valid = 1;
    </select>

    <select id="selectByACMUidList"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_assignment
        where
                acm_uid in
            <foreach collection="acmUidList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        and
            valid = 1;
    </select>

    <select id="selectByAORMUidList"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_assignment
        where
            aorm_uid in
            <foreach collection="aormUidList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        and
            valid = 1;
    </select>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_school_delivery_assignment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id,
            </if>
            <if test="initiatorUid != null">
                initiator_uid,
            </if>
            <if test="initiationTime != null">
                initiation_time,
            </if>
            <if test="keyDecisionUserId != null and keyDecisionUserId != ''">
                key_decision_user_id,
            </if>
            <if test="signPartnerUserId != null and signPartnerUserId != ''">
                sign_partner_user_id,
            </if>
            <if test="extraSupport != null and extraSupport != ''">
                extra_support,
            </if>
            <if test="publicStallNum != null">
                public_stall_num,
            </if>
            <if test="deliverableStallNum != null">
                deliverable_stall_num,
            </if>
            <if test="needFoodCabinet != null">
                need_food_cabinet,
            </if>
            <if test="needFoodCabinetNum != null">
                need_food_cabinet_num,
            </if>
            <if test="materialDemand != null">
                material_demand,
            </if>
            <if test="needPrinterNum != null">
                need_printer_num,
            </if>
            <if test="needDisplayRackNum != null">
                need_display_rack_num,
            </if>
            <if test="needSunshadeNum != null">
                need_sunshade_num,
            </if>
            <if test="needTrashCanNum != null">
                need_trash_can_num,
            </if>
            <if test="otherMaterialDemand != null">
                other_material_demand,
            </if>
            <if test="otherCooperationDemand != null and otherCooperationDemand != ''">
                other_cooperation_demand,
            </if>
            <if test="csmUid != null">
                csm_uid,
            </if>
            <if test="aormUid != null">
                aorm_uid,
            </if>
            <if test="acmUid != null">
                acm_uid,
            </if>
            <if test="dataVersion != null">
                data_version,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deliveryId != null">
                #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="initiatorUid != null">
                #{initiatorUid,jdbcType=BIGINT},
            </if>
            <if test="initiationTime != null">
                #{initiationTime,jdbcType=INTEGER},
            </if>
            <if test="keyDecisionUserId != null and keyDecisionUserId != ''">
                #{keyDecisionUserId,jdbcType=VARCHAR},
            </if>
            <if test="signPartnerUserId != null and signPartnerUserId != ''">
                #{signPartnerUserId,jdbcType=VARCHAR},
            </if>
            <if test="extraSupport != null and extraSupport != ''">
                #{extraSupport,jdbcType=VARCHAR},
            </if>
            <if test="publicStallNum != null">
                #{publicStallNum,jdbcType=INTEGER},
            </if>
            <if test="deliverableStallNum != null">
                #{deliverableStallNum,jdbcType=INTEGER},
            </if>
            <if test="needFoodCabinet != null">
                #{needFoodCabinet,jdbcType=INTEGER},
            </if>
            <if test="needFoodCabinetNum != null">
                #{needFoodCabinetNum,jdbcType=INTEGER},
            </if>
            <if test="materialDemand != null">
                #{materialDemand,jdbcType=VARCHAR},
            </if>
            <if test="needPrinterNum != null">
                #{needPrinterNum,jdbcType=INTEGER},
            </if>
            <if test="needDisplayRackNum != null">
                #{needDisplayRackNum,jdbcType=INTEGER},
            </if>
            <if test="needSunshadeNum != null">
                #{needSunshadeNum,jdbcType=INTEGER},
            </if>
            <if test="needTrashCanNum != null">
                #{needTrashCanNum,jdbcType=INTEGER},
            </if>
            <if test="otherMaterialDemand != null">
                #{otherMaterialDemand,jdbcType=VARCHAR},
            </if>
            <if test="otherCooperationDemand != null and otherCooperationDemand != ''">
                #{otherCooperationDemand,jdbcType=VARCHAR},
            </if>
            <if test="csmUid != null">
                #{csmUid,jdbcType=INTEGER},
            </if>
            <if test="aormUid != null">
                #{aormUid,jdbcType=INTEGER},
            </if>
            <if test="acmUid != null">
                #{acmUid,jdbcType=INTEGER},
            </if>
            <if test="dataVersion != null">
                #{dataVersion,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDO">
        update
            wm_school_delivery_assignment
        <set>
            utime = UNIX_TIMESTAMP(),
            <if test="deliveryId != null">
                delivery_id = #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="initiatorUid != null">
                initiator_uid = #{initiatorUid,jdbcType=BIGINT},
            </if>
            <if test="initiationTime != null">
                initiation_time = #{initiationTime,jdbcType=INTEGER},
            </if>
            <if test="keyDecisionUserId != null and keyDecisionUserId != ''">
                key_decision_user_id = #{keyDecisionUserId,jdbcType=VARCHAR},
            </if>
            <if test="signPartnerUserId != null and signPartnerUserId != ''">
                sign_partner_user_id = #{signPartnerUserId,jdbcType=VARCHAR},
            </if>
            <if test="extraSupport != null and extraSupport != ''">
                extra_support = #{extraSupport,jdbcType=VARCHAR},
            </if>
            <if test="publicStallNum != null">
                public_stall_num = #{publicStallNum,jdbcType=INTEGER},
            </if>
            <if test="deliverableStallNum != null">
                deliverable_stall_num = #{deliverableStallNum,jdbcType=INTEGER},
            </if>
            <if test="needFoodCabinet != null">
                need_food_cabinet = #{needFoodCabinet,jdbcType=INTEGER},
            </if>
            <if test="needFoodCabinetNum != null">
                need_food_cabinet_num = #{needFoodCabinetNum,jdbcType=INTEGER},
            </if>
            <if test="materialDemand != null">
                material_demand = #{materialDemand,jdbcType=VARCHAR},
            </if>
            <if test="needPrinterNum != null">
                need_printer_num = #{needPrinterNum,jdbcType=INTEGER},
            </if>
            <if test="needDisplayRackNum != null">
                need_display_rack_num = #{needDisplayRackNum,jdbcType=INTEGER},
            </if>
            <if test="needSunshadeNum != null">
                need_sunshade_num = #{needSunshadeNum,jdbcType=INTEGER},
            </if>
            <if test="needTrashCanNum != null">
                need_trash_can_num = #{needTrashCanNum,jdbcType=INTEGER},
            </if>
            <if test="otherMaterialDemand != null">
                other_material_demand = #{otherMaterialDemand,jdbcType=VARCHAR},
            </if>
            <if test="otherCooperationDemand != null and otherCooperationDemand != ''">
                other_cooperation_demand = #{otherCooperationDemand,jdbcType=VARCHAR},
            </if>
            <if test="csmUid != null">
                csm_uid = #{csmUid,jdbcType=BIGINT},
            </if>
            <if test="aormUid != null">
                aorm_uid = #{aormUid,jdbcType=BIGINT},
            </if>
            <if test="acmUid != null">
                acm_uid = #{acmUid,jdbcType=BIGINT},
            </if>
            <if test="dataVersion != null">
                data_version = #{dataVersion,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=BIGINT},
            </if>
        </set>
        where
            id = #{id,jdbcType=BIGINT}
    </update>

</mapper>