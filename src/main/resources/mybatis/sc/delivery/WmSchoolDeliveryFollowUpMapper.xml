<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryFollowUpMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryFollowUpDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="delivery_id" property="deliveryId" jdbcType="INTEGER"/>
        <result column="school_primary_id" property="schoolPrimaryId" jdbcType="INTEGER"/>

        <result column="initiator_uid" property="initiatorUid" jdbcType="BIGINT"/>
        <result column="initiation_time" property="initiationTime" jdbcType="VARCHAR"/>

        <result column="insc_online_sign_achieve" property="inscOnlineSignAchieve" jdbcType="INTEGER"/>
        <result column="insc_online_sign_fintime" property="inscOnlineSignFintime" jdbcType="VARCHAR"/>
        <result column="insc_online_sign_status" property="inscOnlineSignStatus" jdbcType="INTEGER"/>
        <result column="insc_online_sign_exception" property="inscOnlineSignException" jdbcType="VARCHAR"/>

        <result column="insc_offline_build_achieve" property="inscOfflineBuildAchieve" jdbcType="INTEGER"/>
        <result column="insc_offline_build_fintime" property="inscOfflineBuildFintime" jdbcType="VARCHAR"/>
        <result column="insc_offline_build_status" property="inscOfflineBuildStatus" jdbcType="INTEGER"/>
        <result column="insc_offline_build_exception" property="inscOfflineBuildException" jdbcType="VARCHAR"/>

        <result column="outsc_online_sign_achieve" property="outscOnlineSignAchieve" jdbcType="INTEGER"/>
        <result column="outsc_online_sign_fintime" property="outscOnlineSignFintime" jdbcType="VARCHAR"/>
        <result column="outsc_online_sign_status" property="outscOnlineSignStatus" jdbcType="INTEGER"/>
        <result column="outsc_online_sign_exception" property="outscOnlineSignException" jdbcType="VARCHAR"/>

        <result column="outsc_offline_build_achieve" property="outscOfflineBuildAchieve" jdbcType="INTEGER"/>
        <result column="outsc_offline_build_fintime" property="outscOfflineBuildFintime" jdbcType="VARCHAR"/>
        <result column="outsc_offline_build_status" property="outscOfflineBuildStatus" jdbcType="INTEGER"/>
        <result column="outsc_offline_build_exception" property="outscOfflineBuildException" jdbcType="VARCHAR"/>

        <result column="first_stall_online_achieve" property="firstStallOnlineAchieve" jdbcType="INTEGER"/>
        <result column="first_stall_online_fintime" property="firstStallOnlineFintime" jdbcType="VARCHAR"/>
        <result column="first_stall_online_status" property="firstStallOnlineStatus" jdbcType="INTEGER"/>
        <result column="first_stall_online_exception" property="firstStallOnlineException" jdbcType="VARCHAR"/>

        <result column="online_operation_plan" property="onlineOperationPlan" jdbcType="VARCHAR"/>

        <result column="life_cycle" property="lifeCycle" jdbcType="INTEGER"/>
        <result column="online_penerate" property="onlinePenerate" jdbcType="VARCHAR"/>
        <result column="online_penerate_achieve" property="onlinePenerateAchieve" jdbcType="INTEGER"/>
        <result column="online_penerate_status" property="onlinePenerateStatus" jdbcType="INTEGER"/>
        <result column="online_penerate_exception" property="onlinePenerateException" jdbcType="VARCHAR"/>

        <result column="ontime_rate" property="ontimeRate" jdbcType="VARCHAR"/>
        <result column="ontime_rate_achieve" property="ontimeRateAchieve" jdbcType="INTEGER"/>
        <result column="ontime_rate_status" property="ontimeRateStatus" jdbcType="INTEGER"/>
        <result column="ontime_rate_exception" property="ontimeRateException" jdbcType="VARCHAR"/>

        <result column="avg_delivery_time" property="avgDeliveryTime" jdbcType="VARCHAR"/>
        <result column="avg_delivery_time_achieve" property="avgDeliveryTimeAchieve" jdbcType="INTEGER"/>
        <result column="avg_delivery_time_status" property="avgDeliveryTimeStatus" jdbcType="INTEGER"/>
        <result column="avg_delivery_time_exception" property="avgDeliveryTimeException" jdbcType="VARCHAR"/>

        <result column="daily_order" property="dailyOrder" jdbcType="VARCHAR"/>
        <result column="daily_order_achieve" property="dailyOrderAchieve" jdbcType="INTEGER"/>
        <result column="daily_order_status" property="dailyOrderStatus" jdbcType="INTEGER"/>
        <result column="daily_order_exception" property="dailyOrderException" jdbcType="VARCHAR"/>

        <result column="meal_penerate" property="mealPenerate" jdbcType="VARCHAR"/>
        <result column="meal_penerate_achieve" property="mealPenerateAchieve" jdbcType="INTEGER"/>
        <result column="meal_penerate_status" property="mealPenerateStatus" jdbcType="INTEGER"/>
        <result column="meal_penerate_exception" property="mealPenerateException" jdbcType="VARCHAR"/>

        <result column="daily_yield" property="dailyYield" jdbcType="VARCHAR"/>
        <result column="daily_yield_achieve" property="dailyYieldAchieve" jdbcType="INTEGER"/>
        <result column="daily_yield_status" property="dailyYieldStatus" jdbcType="INTEGER"/>
        <result column="daily_yield_exception" property="dailyYieldException" jdbcType="VARCHAR"/>

        <result column="csm_uid" property="csmUid" jdbcType="BIGINT"/>
        <result column="aorm_uid" property="aormUid" jdbcType="BIGINT"/>
        <result column="acm_uid" property="acmUid" jdbcType="BIGINT"/>

        <result column="data_version" property="dataVersion" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="muid" property="muid" jdbcType="BIGINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, delivery_id, school_primary_id, initiator_uid, initiation_time,
        insc_online_sign_achieve, insc_online_sign_fintime, insc_online_sign_status, insc_online_sign_exception,
        insc_offline_build_achieve, insc_offline_build_fintime, insc_offline_build_status, insc_offline_build_exception,
        outsc_online_sign_achieve, outsc_online_sign_fintime, outsc_online_sign_status, outsc_online_sign_exception,
        outsc_offline_build_achieve, outsc_offline_build_fintime, outsc_offline_build_status, outsc_offline_build_exception,
        first_stall_online_achieve, first_stall_online_fintime, first_stall_online_status, first_stall_online_exception,
        online_operation_plan, life_cycle,
        online_penerate, online_penerate_achieve, online_penerate_status, online_penerate_exception,
        ontime_rate, ontime_rate_achieve, ontime_rate_status, ontime_rate_exception,
        avg_delivery_time, avg_delivery_time_achieve, avg_delivery_time_status, avg_delivery_time_exception,
        daily_order, daily_order_achieve, daily_order_status, daily_order_exception,
        meal_penerate, meal_penerate_achieve, meal_penerate_status, meal_penerate_exception,
        daily_yield, daily_yield_achieve, daily_yield_status, daily_yield_exception,
        csm_uid, aorm_uid, acm_uid, data_version, valid, cuid, muid, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_school_delivery_followup
        where
            id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_followup
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
    </select>

    <select id="selectByDeliveryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_followup
        where
            delivery_id = #{deliveryId,jdbcType=INTEGER}
        and
            valid = 1
    </select>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryFollowUpDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_school_delivery_followup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id,
            </if>

            <if test="initiatorUid != null">
                initiator_uid,
            </if>
            <if test="initiationTime != null">
                initiation_time,
            </if>

            <if test="inscOnlineSignAchieve != null">
                insc_online_sign_achieve,
            </if>
            <if test="inscOnlineSignFintime != null">
                insc_online_sign_fintime,
            </if>
            <if test="inscOnlineSignStatus != null">
                insc_online_sign_status,
            </if>
            <if test="inscOnlineSignException != null">
                insc_online_sign_exception,
            </if>

            <if test="inscOfflineBuildAchieve != null">
                insc_offline_build_achieve,
            </if>
            <if test="inscOfflineBuildFintime != null">
                insc_offline_build_fintime,
            </if>
            <if test="inscOfflineBuildStatus != null">
                insc_offline_build_status,
            </if>
            <if test="inscOfflineBuildException != null">
                insc_offline_build_exception,
            </if>

            <if test="outscOnlineSignAchieve != null">
                outsc_online_sign_achieve,
            </if>
            <if test="outscOnlineSignFintime != null">
                outsc_online_sign_fintime,
            </if>
            <if test="outscOnlineSignStatus != null">
                outsc_online_sign_status,
            </if>
            <if test="outscOnlineSignException != null">
                outsc_online_sign_exception,
            </if>

            <if test="outscOfflineBuildAchieve != null">
                outsc_offline_build_achieve,
            </if>
            <if test="outscOfflineBuildFintime != null">
                outsc_offline_build_fintime,
            </if>
            <if test="outscOfflineBuildStatus != null">
                outsc_offline_build_status,
            </if>
            <if test="outscOfflineBuildException != null">
                outsc_offline_build_exception,
            </if>

            <if test="firstStallOnlineAchieve != null">
                first_stall_online_achieve,
            </if>
            <if test="firstStallOnlineFintime != null">
                first_stall_online_fintime,
            </if>
            <if test="firstStallOnlineStatus != null">
                first_stall_online_status,
            </if>
            <if test="firstStallOnlineException != null">
                first_stall_online_exception,
            </if>

            <if test="onlineOperationPlan != null">
                online_operation_plan,
            </if>

            <if test="lifeCycle != null">
                life_cycle,
            </if>

            <if test="onlinePenerate != null">
                online_penerate,
            </if>
            <if test="onlinePenerateAchieve != null">
                online_penerate_achieve,
            </if>
            <if test="onlinePenerateStatus != null">
                online_penerate_status,
            </if>
            <if test="onlinePenerateException != null">
                online_penerate_exception,
            </if>

            <if test="ontimeRate != null">
                ontime_rate,
            </if>
            <if test="ontimeRateAchieve != null">
                ontime_rate_achieve,
            </if>
            <if test="ontimeRateStatus != null">
                ontime_rate_status,
            </if>
            <if test="ontimeRateException != null">
                ontime_rate_exception,
            </if>

            <if test="avgDeliveryTime != null">
                avg_delivery_time,
            </if>
            <if test="avgDeliveryTimeAchieve != null">
                avg_delivery_time_achieve,
            </if>
            <if test="avgDeliveryTimeStatus != null">
                avg_delivery_time_status,
            </if>
            <if test="avgDeliveryTimeException != null">
                avg_delivery_time_exception,
            </if>

            <if test="dailyOrder != null">
                daily_order,
            </if>
            <if test="dailyOrderAchieve != null">
                daily_order_achieve,
            </if>
            <if test="dailyOrderStatus != null">
                daily_order_status,
            </if>
            <if test="dailyOrderException != null">
                daily_order_exception,
            </if>

            <if test="mealPenerate != null">
                meal_penerate,
            </if>
            <if test="mealPenerateAchieve != null">
                meal_penerate_achieve,
            </if>
            <if test="mealPenerateStatus != null">
                meal_penerate_status,
            </if>
            <if test="mealPenerateException != null">
                meal_penerate_exception,
            </if>

            <if test="dailyYield != null">
                daily_yield,
            </if>
            <if test="dailyYieldAchieve != null">
                daily_yield_achieve,
            </if>
            <if test="dailyYieldStatus != null">
                daily_yield_status,
            </if>
            <if test="dailyYieldException != null">
                daily_yield_exception,
            </if>

            <if test="csmUid != null">
                csm_uid,
            </if>
            <if test="aormUid != null">
                aorm_uid,
            </if>
            <if test="acmUid != null">
                acm_uid,
            </if>

            <if test="dataVersion != null">
                data_version,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deliveryId != null">
                #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                #{schoolPrimaryId,jdbcType=INTEGER},
            </if>

            <if test="initiatorUid != null">
                #{initiatorUid,jdbcType=BIGINT},
            </if>
            <if test="initiationTime != null">
                #{initiationTime,jdbcType=VARCHAR},
            </if>

            <if test="inscOnlineSignAchieve != null">
                #{inscOnlineSignAchieve,jdbcType=INTEGER},
            </if>
            <if test="inscOnlineSignFintime != null">
                #{inscOnlineSignFintime,jdbcType=VARCHAR},
            </if>
            <if test="inscOnlineSignStatus != null">
                #{inscOnlineSignStatus,jdbcType=INTEGER},
            </if>
            <if test="inscOnlineSignException != null">
                #{inscOnlineSignException,jdbcType=VARCHAR},
            </if>

            <if test="inscOfflineBuildAchieve != null">
                #{inscOfflineBuildAchieve,jdbcType=INTEGER},
            </if>
            <if test="inscOfflineBuildFintime != null">
                #{inscOfflineBuildFintime,jdbcType=VARCHAR},
            </if>
            <if test="inscOfflineBuildStatus != null">
                #{inscOfflineBuildStatus,jdbcType=INTEGER},
            </if>
            <if test="inscOfflineBuildException != null">
                #{inscOfflineBuildException,jdbcType=VARCHAR},
            </if>

            <if test="outscOnlineSignAchieve != null">
                #{outscOnlineSignAchieve,jdbcType=INTEGER},
            </if>
            <if test="outscOnlineSignFintime != null">
                #{outscOnlineSignFintime,jdbcType=VARCHAR},
            </if>
            <if test="outscOnlineSignStatus != null">
                #{outscOnlineSignStatus,jdbcType=INTEGER},
            </if>
            <if test="outscOnlineSignException != null">
                #{outscOnlineSignException,jdbcType=VARCHAR},
            </if>

            <if test="outscOfflineBuildAchieve != null">
                #{outscOfflineBuildAchieve,jdbcType=INTEGER},
            </if>
            <if test="outscOfflineBuildFintime != null">
                #{outscOfflineBuildFintime,jdbcType=VARCHAR},
            </if>
            <if test="outscOfflineBuildStatus != null">
                #{outscOfflineBuildStatus,jdbcType=INTEGER},
            </if>
            <if test="outscOfflineBuildException != null">
                #{outscOfflineBuildException,jdbcType=VARCHAR},
            </if>

            <if test="firstStallOnlineAchieve != null">
                #{firstStallOnlineAchieve,jdbcType=INTEGER},
            </if>
            <if test="firstStallOnlineFintime != null">
                #{firstStallOnlineFintime,jdbcType=VARCHAR},
            </if>
            <if test="firstStallOnlineStatus != null">
                #{firstStallOnlineStatus,jdbcType=INTEGER},
            </if>
            <if test="firstStallOnlineException != null">
                #{firstStallOnlineException,jdbcType=VARCHAR},
            </if>

            <if test="onlineOperationPlan != null">
                #{onlineOperationPlan,jdbcType=VARCHAR},
            </if>
            <if test="lifeCycle != null">
                #{lifeCycle,jdbcType=INTEGER},
            </if>

            <if test="onlinePenerate != null">
                #{onlinePenerate,jdbcType=VARCHAR},
            </if>
            <if test="onlinePenerateAchieve != null">
                #{onlinePenerateAchieve,jdbcType=INTEGER},
            </if>
            <if test="onlinePenerateStatus != null">
                #{onlinePenerateStatus,jdbcType=INTEGER},
            </if>
            <if test="onlinePenerateException != null">
                #{onlinePenerateException,jdbcType=VARCHAR},
            </if>

            <if test="ontimeRate != null">
                #{ontimeRate,jdbcType=VARCHAR},
            </if>
            <if test="ontimeRateAchieve != null">
                #{ontimeRateAchieve,jdbcType=INTEGER},
            </if>
            <if test="ontimeRateStatus != null">
                #{ontimeRateStatus,jdbcType=INTEGER},
            </if>
            <if test="ontimeRateException != null">
                #{ontimeRateException,jdbcType=VARCHAR},
            </if>

            <if test="avgDeliveryTime != null">
                #{avgDeliveryTime,jdbcType=VARCHAR},
            </if>
            <if test="avgDeliveryTimeAchieve != null">
                #{avgDeliveryTimeAchieve,jdbcType=INTEGER},
            </if>
            <if test="avgDeliveryTimeStatus != null">
                #{avgDeliveryTimeStatus,jdbcType=INTEGER},
            </if>
            <if test="avgDeliveryTimeException != null">
                #{avgDeliveryTimeException,jdbcType=VARCHAR},
            </if>

            <if test="dailyOrder != null">
                #{dailyOrder,jdbcType=VARCHAR},
            </if>
            <if test="dailyOrderAchieve != null">
                #{dailyOrderAchieve,jdbcType=INTEGER},
            </if>
            <if test="dailyOrderStatus != null">
                #{dailyOrderStatus,jdbcType=INTEGER},
            </if>
            <if test="dailyOrderException != null">
                #{dailyOrderException,jdbcType=VARCHAR},
            </if>

            <if test="mealPenerate != null">
                #{mealPenerate,jdbcType=VARCHAR},
            </if>
            <if test="mealPenerateAchieve != null">
                #{mealPenerateAchieve,jdbcType=INTEGER},
            </if>
            <if test="mealPenerateStatus != null">
                #{mealPenerateStatus,jdbcType=INTEGER},
            </if>
            <if test="mealPenerateException != null">
                #{mealPenerateException,jdbcType=VARCHAR},
            </if>

            <if test="dailyYield != null">
                #{dailyYield,jdbcType=VARCHAR},
            </if>
            <if test="dailyYieldAchieve != null">
                #{dailyYieldAchieve,jdbcType=INTEGER},
            </if>
            <if test="dailyYieldStatus != null">
                #{dailyYieldStatus,jdbcType=INTEGER},
            </if>
            <if test="dailyYieldException != null">
                #{dailyYieldException,jdbcType=VARCHAR},
            </if>

            <if test="csmUid != null">
                #{csmUid,jdbcType=INTEGER},
            </if>
            <if test="aormUid != null">
                #{aormUid,jdbcType=INTEGER},
            </if>
            <if test="acmUid != null">
                #{acmUid,jdbcType=INTEGER},
            </if>

            <if test="dataVersion != null">
                #{dataVersion,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryFollowUpDO">
        update
            wm_school_delivery_followup
        <set>
            utime = UNIX_TIMESTAMP(),
            <if test="deliveryId != null">
                delivery_id = #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
            </if>

            <if test="initiatorUid != null">
                initiator_uid = #{initiatorUid,jdbcType=BIGINT},
            </if>
            <if test="initiationTime != null">
                initiation_time = #{initiationTime,jdbcType=VARCHAR},
            </if>

            <if test="inscOnlineSignAchieve != null">
                insc_online_sign_achieve = #{inscOnlineSignAchieve,jdbcType=INTEGER},
            </if>
            <if test="inscOnlineSignFintime != null">
                insc_online_sign_fintime = #{inscOnlineSignFintime,jdbcType=VARCHAR},
            </if>
            <if test="inscOnlineSignStatus != null">
                insc_online_sign_status = #{inscOnlineSignStatus,jdbcType=INTEGER},
            </if>
            <if test="inscOnlineSignException != null">
                insc_online_sign_exception = #{inscOnlineSignException,jdbcType=VARCHAR},
            </if>

            <if test="inscOfflineBuildAchieve != null">
                insc_offline_build_achieve = #{inscOfflineBuildAchieve,jdbcType=INTEGER},
            </if>
            <if test="inscOfflineBuildFintime != null">
                insc_offline_build_fintime = #{inscOfflineBuildFintime,jdbcType=VARCHAR},
            </if>
            <if test="inscOfflineBuildStatus != null">
                insc_offline_build_status = #{inscOfflineBuildStatus,jdbcType=INTEGER},
            </if>
            <if test="inscOfflineBuildException != null">
                insc_offline_build_exception = #{inscOfflineBuildException,jdbcType=VARCHAR},
            </if>

            <if test="outscOnlineSignAchieve != null">
                outsc_online_sign_achieve = #{outscOnlineSignAchieve,jdbcType=INTEGER},
            </if>
            <if test="outscOnlineSignFintime != null">
                outsc_online_sign_fintime = #{outscOnlineSignFintime,jdbcType=VARCHAR},
            </if>
            <if test="outscOnlineSignStatus != null">
                outsc_online_sign_status = #{outscOnlineSignStatus,jdbcType=INTEGER},
            </if>
            <if test="outscOnlineSignException != null">
                outsc_online_sign_exception = #{outscOnlineSignException,jdbcType=VARCHAR},
            </if>

            <if test="outscOfflineBuildAchieve != null">
                outsc_offline_build_achieve = #{outscOfflineBuildAchieve,jdbcType=INTEGER},
            </if>
            <if test="outscOfflineBuildFintime != null">
                outsc_offline_build_fintime = #{outscOfflineBuildFintime,jdbcType=VARCHAR},
            </if>
            <if test="outscOfflineBuildStatus != null">
                outsc_offline_build_status = #{outscOfflineBuildStatus,jdbcType=INTEGER},
            </if>
            <if test="outscOfflineBuildException != null">
                outsc_offline_build_exception = #{outscOfflineBuildException,jdbcType=VARCHAR},
            </if>

            <if test="firstStallOnlineAchieve != null">
                first_stall_online_achieve = #{firstStallOnlineAchieve,jdbcType=INTEGER},
            </if>
            <if test="firstStallOnlineFintime != null">
                first_stall_online_fintime = #{firstStallOnlineFintime,jdbcType=VARCHAR},
            </if>
            <if test="firstStallOnlineStatus != null">
                first_stall_online_status = #{firstStallOnlineStatus,jdbcType=INTEGER},
            </if>
            <if test="firstStallOnlineException != null">
                first_stall_online_exception = #{firstStallOnlineException,jdbcType=VARCHAR},
            </if>

            <if test="onlineOperationPlan != null">
                online_operation_plan = #{onlineOperationPlan,jdbcType=VARCHAR},
            </if>
            <if test="lifeCycle != null">
                life_cycle = #{lifeCycle,jdbcType=INTEGER},
            </if>

            <if test="onlinePenerate != null">
                online_penerate = #{onlinePenerate,jdbcType=VARCHAR},
            </if>
            <if test="onlinePenerateAchieve != null">
                online_penerate_achieve = #{onlinePenerateAchieve,jdbcType=INTEGER},
            </if>
            <if test="onlinePenerateStatus != null">
                online_penerate_status = #{onlinePenerateStatus,jdbcType=INTEGER},
            </if>
            <if test="onlinePenerateException != null">
                online_penerate_exception = #{onlinePenerateException,jdbcType=VARCHAR},
            </if>

            <if test="ontimeRate != null">
                ontime_rate = #{ontimeRate,jdbcType=VARCHAR},
            </if>
            <if test="ontimeRateAchieve != null">
                ontime_rate_achieve = #{ontimeRateAchieve,jdbcType=INTEGER},
            </if>
            <if test="ontimeRateStatus != null">
                ontime_rate_status = #{ontimeRateStatus,jdbcType=INTEGER},
            </if>
            <if test="ontimeRateException != null">
                ontime_rate_exception = #{ontimeRateException,jdbcType=VARCHAR},
            </if>

            <if test="avgDeliveryTime != null">
                avg_delivery_time = #{avgDeliveryTime,jdbcType=VARCHAR},
            </if>
            <if test="avgDeliveryTimeAchieve != null">
                avg_delivery_time_achieve = #{avgDeliveryTimeAchieve,jdbcType=INTEGER},
            </if>
            <if test="avgDeliveryTimeStatus != null">
                avg_delivery_time_status = #{avgDeliveryTimeStatus,jdbcType=INTEGER},
            </if>
            <if test="avgDeliveryTimeException != null">
                avg_delivery_time_exception = #{avgDeliveryTimeException,jdbcType=VARCHAR},
            </if>

            <if test="dailyOrder != null">
                daily_order = #{dailyOrder,jdbcType=VARCHAR},
            </if>
            <if test="dailyOrderAchieve != null">
                daily_order_achieve = #{dailyOrderAchieve,jdbcType=INTEGER},
            </if>
            <if test="dailyOrderStatus != null">
                daily_order_status = #{dailyOrderStatus,jdbcType=INTEGER},
            </if>
            <if test="dailyOrderException != null">
                daily_order_exception = #{dailyOrderException,jdbcType=VARCHAR},
            </if>

            <if test="mealPenerate != null">
                meal_penerate = #{mealPenerate,jdbcType=VARCHAR},
            </if>
            <if test="mealPenerateAchieve != null">
                meal_penerate_achieve = #{mealPenerateAchieve,jdbcType=INTEGER},
            </if>
            <if test="mealPenerateStatus != null">
                meal_penerate_status = #{mealPenerateStatus,jdbcType=INTEGER},
            </if>
            <if test="mealPenerateException != null">
                meal_penerate_exception = #{mealPenerateException,jdbcType=VARCHAR},
            </if>

            <if test="dailyYield != null">
                daily_yield = #{dailyYield,jdbcType=VARCHAR},
            </if>
            <if test="dailyYieldAchieve != null">
                daily_yield_achieve = #{dailyYieldAchieve,jdbcType=INTEGER},
            </if>
            <if test="dailyYieldStatus != null">
                daily_yield_status = #{dailyYieldStatus,jdbcType=INTEGER},
            </if>
            <if test="dailyYieldException != null">
                daily_yield_exception = #{dailyYieldException,jdbcType=VARCHAR},
            </if>

            <if test="csmUid != null">
                csm_uid = #{csmUid,jdbcType=BIGINT},
            </if>
            <if test="aormUid != null">
                aorm_uid = #{aormUid,jdbcType=BIGINT},
            </if>
            <if test="acmUid != null">
                acm_uid = #{acmUid,jdbcType=BIGINT},
            </if>

            <if test="dataVersion != null">
                data_version = #{dataVersion,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=BIGINT},
            </if>
        </set>
        where
            id = #{id,jdbcType=BIGINT}
    </update>


    <update id="deleteByPrimaryId"
            parameterType="java.lang.Integer">
        update
            wm_school_delivery_followup
        set
            valid = 0
        where
            id = #{id,jdbcType=INTEGER}
    </update>


</mapper>