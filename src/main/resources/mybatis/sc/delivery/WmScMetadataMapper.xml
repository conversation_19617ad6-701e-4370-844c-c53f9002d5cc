<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScMetadataMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.metadata.WmScMetadataDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="scene" property="scene" jdbcType="INTEGER"/>
        <result column="business_id" property="businessId" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="data_json" property="dataJson" jdbcType="VARCHAR"/>
        <result column="data_version" property="dataVersion" jdbcType="INTEGER"/>
        <result column="template_version" property="templateVersion" jdbcType="INTEGER"/>
        <result column="op_type" property="opType" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="muid" property="muid" jdbcType="BIGINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, scene, business_id, business_type, data_json, data_version, template_version, op_type, valid, cuid, muid, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_sc_metadata
        where
            id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByBusinessId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_sc_metadata
        where
            business_id = #{businessId,jdbcType=BIGINT}
        and
            valid = 1
    </select>

    <select id="selectByBussinesstTypeAndDataVersionList"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_sc_metadata
        where
            business_type = #{bussinessType,jdbcType=INTEGER}
        and
            data_version in
            <foreach collection="dataVersionList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        and
            valid = 1
    </select>

    <select id="selectLatestVersionDataByBusinessTypeAndBusinessId"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_sc_metadata
        where
            business_type = #{bussinessType,jdbcType=INTEGER}
        and
            business_id = #{bussinessId,jdbcType=INTEGER}
        and
            valid = 1
        order by
            data_version desc
        limit 1
    </select>

    <select id="selectByBusinessIdAndDataVersion"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_sc_metadata
        where
            business_id = #{businessId,jdbcType=BIGINT}
        and
            data_version = #{dataVersion,jdbcType=INTEGER}
        and
            valid = 1
    </select>

    <select id="selectByBusinessIdAndBusinessTypeAndDataVersion"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_sc_metadata
        where
            business_id = #{businessId,jdbcType=BIGINT}
        and
            business_type = #{businessType,jdbcType=INTEGER}
        and
            data_version = #{dataVersion,jdbcType=INTEGER}
        and
            valid = 1
    </select>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.metadata.WmScMetadataDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_sc_metadata
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="scene != null">
                scene,
            </if>
            <if test="businessId != null">
                business_id,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="dataJson != null">
                data_json,
            </if>
            <if test="dataVersion != null">
                data_version,
            </if>
            <if test="templateVersion != null">
                template_version,
            </if>
            <if test="opType != null">
                op_type,
            </if>
            <if test="valid != null">
                valid,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="scene != null">
                #{scene,jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="dataJson != null">
                #{dataJson,jdbcType=VARCHAR},
            </if>
            <if test="dataVersion != null">
                #{dataVersion,jdbcType=INTEGER},
            </if>
            <if test="templateVersion != null">
                #{templateVersion,jdbcType=INTEGER},
            </if>
            <if test="opType != null">
                #{opType,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateDataJsonByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.metadata.WmScMetadataDO">
        update
            wm_sc_metadata
        set
            data_json = #{dataJson,jdbcType=VARCHAR}
        where
            id = #{id,jdbcType=BIGINT}
    </update>


</mapper>