<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiTaskDetailMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskDetailDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="canteen_poi_task_id" property="canteenPoiTaskId" jdbcType="BIGINT"/>
        <result column="canteen_id" property="canteenId" jdbcType="INTEGER"/>
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, canteen_poi_task_id, canteen_id, wm_poi_id, utime, ctime, valid
  </sql>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskDetailDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into wm_sc_canteen_poi_task_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="canteenPoiTaskId != null">
                canteen_poi_task_id,
            </if>
            <if test="canteenId != null">
                canteen_id,
            </if>
            <if test="wmPoiId != null">
                wm_poi_id,
            </if>
            utime,
            ctime,
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="canteenPoiTaskId != null">
                #{canteenPoiTaskId,jdbcType=BIGINT},
            </if>
            <if test="canteenId != null">
                #{canteenId,jdbcType=INTEGER},
            </if>
            <if test="wmPoiId != null">
                #{wmPoiId,jdbcType=BIGINT},
            </if>
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="valid != null">
                #{valid,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_task_detail
        where valid=1
        <if test="canteenPoiTaskIdList != null and canteenPoiTaskIdList.size>0">
            and canteen_poi_task_id in
            <foreach collection="canteenPoiTaskIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="wmPoiIdList != null and wmPoiIdList.size>0">
            and wm_poi_id in
            <foreach collection="wmPoiIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectConTaskDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_task_detail
        where valid=1
        and canteen_poi_task_id in(
        select id from wm_sc_canteen_poi_task where valid=1
        <if test="taskType != null">
            and task_type = #{taskType}
        </if>
        <if test="canteenIdFrom != null">
            and canteen_id_from = #{canteenIdFrom}
        </if>
        <if test="canteenIdTo != null">
            and canteen_id_to = #{canteenIdTo}
        </if>
        <if test="auditStatusList != null and auditStatusList.size>0">
            and audit_status in
            <foreach collection="auditStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        )
        <if test="wmPoiIdList != null and wmPoiIdList.size>0">
            and wm_poi_id in
            <foreach collection="wmPoiIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by id desc
    </select>

    <select id="selectByCanteenPoiTaskId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_task_detail
        where valid=1 and canteen_poi_task_id = #{canteenPoiTaskId}
    </select>

</mapper>