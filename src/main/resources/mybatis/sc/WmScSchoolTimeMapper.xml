<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolTimeMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="school_primary_id" jdbcType="INTEGER" property="schoolPrimaryId"/>
        <result column="cr_source" jdbcType="INTEGER" property="crSource"/>
        <result column="up_source" jdbcType="INTEGER" property="upSource"/>
        <result column="tm_id" jdbcType="BIGINT" property="tmId"/>
        <result column="year_begin" jdbcType="INTEGER" property="yearBegin"/>
        <result column="year_end" jdbcType="INTEGER" property="yearEnd"/>
        <result column="wv_time_begin" jdbcType="VARCHAR" property="wvTimeBegin"/>
        <result column="wv_time_end" jdbcType="VARCHAR" property="wvTimeEnd"/>
        <result column="sv_time_beign" jdbcType="VARCHAR" property="svTimeBeign"/>
        <result column="sv_time_end" jdbcType="VARCHAR" property="svTimeEnd"/>
        <result column="valid" jdbcType="TINYINT" property="valid"/>
        <result column="cuid" jdbcType="BIGINT" property="cuid"/>
        <result column="muid" jdbcType="BIGINT" property="muid"/>
        <result column="ctime" jdbcType="INTEGER" property="ctime"/>
        <result column="utime" jdbcType="INTEGER" property="utime"/>
        <result column="cl_time_json" jdbcType="LONGVARCHAR" property="clTimeJson"/>
        <result column="term" jdbcType="TINYINT" property="term"/>
        <result column="term_begin_situation" jdbcType="TINYINT" property="termBeginSituation"/>
        <result column="open_not_pic" jdbcType="VARCHAR" property="openNotPic"/>
        <result column="open_not_pdf" jdbcType="VARCHAR" property="openNotPdf"/>
        <result column="open_not_info_source" jdbcType="INTEGER" property="openNotInfoSource"/>
        <result column="open_not_remark" jdbcType="VARCHAR" property="openNotRemark"/>
        <result column="term_begin_data" jdbcType="LONGVARCHAR" property="termBeginData"/>
        <result column="term_end_data" jdbcType="LONGVARCHAR" property="termEndData"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, school_primary_id, cr_source, up_source, tm_id, year_begin, year_end, wv_time_begin, 
    wv_time_end, sv_time_beign, sv_time_end, valid, cuid, muid, ctime, utime, cl_time_json,
    term, term_begin_situation, open_not_pic, open_not_pdf, open_not_info_source, open_not_remark,
    term_begin_data, term_end_data
  </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school_time
        where id = #{id,jdbcType=BIGINT} and valid = 1
    </select>

    <select id="selectBySchoolId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
            wm_sc_school_time
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=BIGINT}
        and
            valid = 1
        order by
            year_begin,term
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wm_sc_school_time
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into wm_sc_school_time
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id,
            </if>
            <if test="crSource != null">
                cr_source,
            </if>
            <if test="upSource != null">
                up_source,
            </if>
            <if test="tmId != null">
                tm_id,
            </if>
            <if test="yearBegin != null">
                year_begin,
            </if>
            <if test="yearEnd != null">
                year_end,
            </if>
            <if test="wvTimeBegin != null">
                wv_time_begin,
            </if>
            <if test="wvTimeEnd != null">
                wv_time_end,
            </if>
            <if test="svTimeBeign != null">
                sv_time_beign,
            </if>
            <if test="svTimeEnd != null">
                sv_time_end,
            </if>
            <if test="valid != null">
                valid,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
            ctime,
            utime,
            <if test="clTimeJson != null">
                cl_time_json,
            </if>
            <if test="term != null">
                term,
            </if>
            <if test="termBeginSituation != null">
                term_begin_situation,
            </if>
            <if test="openNotPic != null">
                open_not_pic,
            </if>
            <if test="openNotPdf != null">
                open_not_pdf,
            </if>
            <if test="openNotInfoSource != null">
                open_not_info_source,
            </if>
            <if test="openNotRemark != null">
                open_not_remark,
            </if>
            <if test="termBeginData != null">
                term_begin_data,
            </if>
            <if test="termEndData != null">
                term_end_data,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="schoolPrimaryId != null">
                #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="crSource != null">
                #{crSource,jdbcType=INTEGER},
            </if>
            <if test="upSource != null">
                #{upSource,jdbcType=INTEGER},
            </if>
            <if test="tmId != null">
                #{tmId,jdbcType=BIGINT},
            </if>
            <if test="yearBegin != null">
                #{yearBegin,jdbcType=INTEGER},
            </if>
            <if test="yearEnd != null">
                #{yearEnd,jdbcType=INTEGER},
            </if>
            <if test="wvTimeBegin != null">
                #{wvTimeBegin,jdbcType=VARCHAR},
            </if>
            <if test="wvTimeEnd != null">
                #{wvTimeEnd,jdbcType=VARCHAR},
            </if>
            <if test="svTimeBeign != null">
                #{svTimeBeign,jdbcType=VARCHAR},
            </if>
            <if test="svTimeEnd != null">
                #{svTimeEnd,jdbcType=VARCHAR},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=TINYINT},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=BIGINT},
            </if>
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="clTimeJson != null">
                #{clTimeJson,jdbcType=LONGVARCHAR},
            </if>
            <if test="term != null">
                #{term},
            </if>
            <if test="termBeginSituation != null">
                #{termBeginSituation},
            </if>
            <if test="openNotPic != null">
                #{openNotPic},
            </if>
            <if test="openNotPdf != null">
                #{openNotPdf},
            </if>
            <if test="openNotInfoSource != null">
                #{openNotInfoSource},
            </if>
            <if test="openNotRemark != null">
                #{openNotRemark},
            </if>
            <if test="termBeginData != null">
                #{termBeginData},
            </if>
            <if test="termEndData != null">
                #{termEndData},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into wm_sc_school_time (school_primary_id, cr_source,
        up_source, tm_id, year_begin, year_end, wv_time_begin, wv_time_end, sv_time_beign, sv_time_end, valid,
        cuid, muid, ctime, utime, cl_time_json,
        term,term_begin_situation,open_not_pic,open_not_remark,term_begin_data,term_end_data)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            <if test="item.schoolPrimaryId != null">
                #{item.schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="item.crSource != null">
                #{item.crSource,jdbcType=INTEGER},
            </if>
            <if test="item.upSource != null">
                #{item.upSource,jdbcType=INTEGER},
            </if>
            <if test="item.tmId != null">
                #{item.tmId,jdbcType=BIGINT},
            </if>
            <if test="item.yearBegin != null">
                #{item.yearBegin,jdbcType=INTEGER},
            </if>
            <if test="item.yearEnd != null">
                #{item.yearEnd,jdbcType=INTEGER},
            </if>
            <if test="item.wvTimeBegin != null">
                #{item.wvTimeBegin,jdbcType=VARCHAR},
            </if>
            <if test="item.wvTimeEnd != null">
                #{item.wvTimeEnd,jdbcType=VARCHAR},
            </if>
            <if test="item.svTimeBeign != null">
                #{item.svTimeBeign,jdbcType=VARCHAR},
            </if>
            <if test="item.svTimeEnd != null">
                #{item.svTimeEnd,jdbcType=VARCHAR},
            </if>
            <if test="item.valid != null">
                #{item.valid,jdbcType=TINYINT},
            </if>
            <if test="item.cuid != null">
                #{item.cuid,jdbcType=BIGINT},
            </if>
            <if test="item.muid != null">
                #{item.muid,jdbcType=BIGINT},
            </if>
            <if test="item.ctime != null">
                #{item.ctime,jdbcType=INTEGER},
            </if>
            <if test="item.utime != null">
                #{item.utime,jdbcType=INTEGER},
            </if>
            <if test="item.clTimeJson != null">
                #{item.clTimeJson,jdbcType=LONGVARCHAR},
            </if>
            <if test="term != null">
                #{item.term},
            </if>
            <if test="termBeginSituation != null">
                #{item.termBeginSituation},
            </if>
            <if test="openNotPic != null">
                #{item.openNotPic},
            </if>
            <if test="openNotRemark != null">
                #{item.openNotRemark},
            </if>
            <if test="termBeginData != null">
                #{item.termBeginData},
            </if>
            <if test="termEndData != null">
                #{item.termEndData}
            </if>
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeDO">
        update wm_sc_school_time
        <set>
            <if test="schoolPrimaryId != null">
                school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="crSource != null">
                cr_source = #{crSource,jdbcType=INTEGER},
            </if>
            <if test="upSource != null">
                up_source = #{upSource,jdbcType=INTEGER},
            </if>
            <if test="tmId != null">
                tm_id = #{tmId,jdbcType=BIGINT},
            </if>
            <if test="yearBegin != null">
                year_begin = #{yearBegin,jdbcType=INTEGER},
            </if>
            <if test="yearEnd != null">
                year_end = #{yearEnd,jdbcType=INTEGER},
            </if>
            <if test="wvTimeBegin != null">
                wv_time_begin = #{wvTimeBegin,jdbcType=VARCHAR},
            </if>
            <if test="wvTimeEnd != null">
                wv_time_end = #{wvTimeEnd,jdbcType=VARCHAR},
            </if>
            <if test="svTimeBeign != null">
                sv_time_beign = #{svTimeBeign,jdbcType=VARCHAR},
            </if>
            <if test="svTimeEnd != null">
                sv_time_end = #{svTimeEnd,jdbcType=VARCHAR},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=BIGINT},
            </if>
            utime = UNIX_TIMESTAMP(),
            <if test="clTimeJson != null">
                cl_time_json = #{clTimeJson,jdbcType=LONGVARCHAR},
            </if>
            <if test="term != null">
                term = #{term},
            </if>
            <if test="termBeginSituation != null">
                term_begin_situation = #{termBeginSituation},
            </if>
            <if test="openNotPic != null">
                open_not_pic = #{openNotPic},
            </if>
            <if test="openNotPdf != null">
                open_not_pdf = #{openNotPdf},
            </if>
            <if test="openNotRemark != null">
                open_not_remark = #{openNotRemark},
            </if>
            <if test="openNotInfoSource != null">
                open_not_info_source = #{openNotInfoSource},
            </if>
            <if test="termBeginData != null">
                term_begin_data = #{termBeginData},
            </if>
            <if test="termEndData != null">
                term_end_data = #{termEndData},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectNewest" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school_time
        WHERE `tm_id` = (SELECT max(`tm_id`) FROM wm_sc_school_time) group by tm_id
    </select>

    <select id="selectByCondition" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeCondition"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school_time
        where  valid = 1
        <if test="schoolPrimaryId != null">
            and school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        </if>
        <if test="yearBegin != null">
            and year_begin = #{yearBegin,jdbcType=INTEGER}
        </if>
        <if test="yearEnd != null">
            and year_end = #{yearEnd,jdbcType=INTEGER}
        </if>
        <if test="term != null">
            and term =  #{term}
        </if>
        order by year_begin,term
    </select>

    <update id="unValidSchoolTimeInfo">
        update wm_sc_school_time set valid = 0,utime = UNIX_TIMESTAMP(),muid = #{muid} where
        id = #{id}
    </update>

    <update id="unValidSchoolTimeInfoBySchoolPrimaryId" parameterType="java.lang.Long">
        update wm_sc_school_time set valid = 0,utime = UNIX_TIMESTAMP() where
        school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
    </update>
</mapper>