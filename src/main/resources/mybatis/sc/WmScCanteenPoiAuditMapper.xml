<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiAuditMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDB">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="canteen_id" property="canteenId" jdbcType="INTEGER"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="electronic_id" property="electronicId" jdbcType="VARCHAR"/>
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="ext" property="ext" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, canteen_id, task_id, electronic_id, audit_status, valid, user_id, user_name, 
    utime, ctime, ext
  </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        /*master*/select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_audit
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDB"
            useGeneratedKeys="true" keyProperty="id">
        insert into wm_sc_canteen_poi_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="canteenId != null">
                canteen_id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="electronicId != null">
                electronic_id,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
            <if test="valid != null">
                valid,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="utime != null">
                utime,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="ext != null">
                ext,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="canteenId != null">
                #{canteenId,jdbcType=INTEGER},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="electronicId != null">
                #{electronicId,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="utime != null">
                #{utime,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                #{ctime,jdbcType=INTEGER},
            </if>
            <if test="ext != null">
                #{ext,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDB">
        update wm_sc_canteen_poi_audit
        <set>
            <if test="canteenId != null">
                canteen_id = #{canteenId,jdbcType=INTEGER},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="electronicId != null">
                electronic_id = #{electronicId,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="utime != null">
                utime = #{utime,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=INTEGER},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDB">
    update wm_sc_canteen_poi_audit
    set canteen_id = #{canteenId,jdbcType=INTEGER},
      task_id = #{taskId,jdbcType=BIGINT},
      electronic_id = #{electronicId,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      valid = #{valid,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      user_name = #{userName,jdbcType=VARCHAR},
      utime = #{utime,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=INTEGER},
      ext = #{ext,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <!-- 根据valid状态查询最新的审核信息 -->
    <select id="getLatestCanteenPoiAudit" parameterType="map" resultMap="BaseResultMap">
        /*master*/select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_audit
        where canteen_id = #{canteenId,jdbcType=INTEGER}
        <if test="valid != null">
            and valid = #{valid,jdbcType=INTEGER}
        </if>
        order by id desc limit 1
    </select>

    <select id="getLatestCanteenPoiEffect" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_audit
        where canteen_id = #{canteenId,jdbcType=INTEGER}
        and valid = 1
        and audit_status = 3
        order by id desc limit 1
    </select>

    <select id="selectByPrimaryKeyList"
            resultMap="BaseResultMap"
            parameterType="list">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_audit
        <where>
            <if test="list != null and list.size > 0">
                id in
                <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateAuditUnValid">
        update wm_sc_canteen_poi_audit
        set valid = 0,utime=unix_timestamp()
        where id = #{id}
    </update>
</mapper>