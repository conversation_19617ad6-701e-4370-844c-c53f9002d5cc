<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallAuditTaskMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="gravity_id" property="gravityId" jdbcType="VARCHAR"/>
        <result column="canteen_primary_id" property="canteenPrimaryId" jdbcType="INTEGER"/>
        <result column="proof_picture" property="proofPicture" jdbcType="VARCHAR"/>
        <result column="audit_task_type" property="auditTaskType" jdbcType="TINYINT"/>
        <result column="abnormal_reason" property="abnormalReason" jdbcType="TINYINT"/>
        <result column="audit_node" property="auditNode" jdbcType="INTEGER"/>
        <result column="audit_status" property="auditStatus" jdbcType="TINYINT"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="cmis" property="cmis" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, gravity_id, canteen_primary_id, proof_picture, audit_task_type, abnormal_reason, audit_node, audit_status,
        valid, cuid, cmis, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_canteen_stall_audit_task
        where
            id = #{id,jdbcType=INTEGER}
        and
            valid = 1
    </select>


    <select id="selectByPrimaryKeyByMaster"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        /*master*/select
            <include refid="Base_Column_List"/>
        from
            wm_canteen_stall_audit_task
        where
            id = #{id,jdbcType=INTEGER}
        and
            valid = 1
    </select>


    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_canteen_stall_audit_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="canteenPrimaryId != null">
                canteen_primary_id,
            </if>
            <if test="gravityId != null">
                gravity_id,
            </if>
            <if test="proofPicture != null">
                proof_picture,
            </if>
            <if test="auditTaskType != null">
                audit_task_type,
            </if>
            <if test="abnormalReason != null">
                abnormal_reason,
            </if>
            <if test="auditNode != null">
                audit_node,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="cmis != null">
                cmis,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="canteenPrimaryId != null">
                #{canteenPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="gravityId != null">
                #{gravityId,jdbcType=VARCHAR},
            </if>
            <if test="proofPicture != null">
                #{proofPicture,jdbcType=VARCHAR},
            </if>
            <if test="auditTaskType != null">
                #{auditTaskType,jdbcType=INTEGER},
            </if>
            <if test="abnormalReason != null">
                #{abnormalReason,jdbcType=INTEGER},
            </if>
            <if test="auditNode != null">
                #{auditNode,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=INTEGER},
            </if>
            <if test="cmis != null">
                #{cmis,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO">
        update
            wm_canteen_stall_audit_task
        <set>
            utime = UNIX_TIMESTAMP(),
            <if test="canteenPrimaryId != null">
                canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="gravityId != null">
                gravity_id = #{gravityId,jdbcType=VARCHAR},
            </if>
            <if test="proofPicture != null">
                proof_picture = #{proofPicture,jdbcType=VARCHAR},
            </if>
            <if test="auditTaskType != null">
                audit_task_type = #{auditTaskType,jdbcType=INTEGER},
            </if>
            <if test="abnormalReason != null">
                abnormal_reason = #{abnormalReason,jdbcType=INTEGER},
            </if>
            <if test="auditNode != null">
                audit_node = #{auditNode,jdbcType=INTEGER},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=INTEGER},
            </if>
            <if test="cmis != null">
                cmis = #{cmis,jdbcType=VARCHAR},
            </if>
        </set>
        where
            id = #{id,jdbcType=BIGINT}
    </update>

</mapper>