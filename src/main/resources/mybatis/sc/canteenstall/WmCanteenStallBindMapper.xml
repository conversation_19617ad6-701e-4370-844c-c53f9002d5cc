<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="canteen_primary_id" property="canteenPrimaryId" jdbcType="INTEGER"/>
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT"/>
        <result column="wdc_clue_id" property="wdcClueId" jdbcType="BIGINT"/>
        <result column="clue_generate_status" property="clueGenerateStatus" jdbcType="TINYINT"/>
        <result column="clue_generate_fail_reason" property="clueGenerateFailReason" jdbcType="VARCHAR"/>
        <result column="clue_bind_status" property="clueBindStatus" jdbcType="TINYINT"/>
        <result column="clue_bind_fail_reason" property="clueBindFailReason" jdbcType="VARCHAR"/>
        <result column="wm_poi_bind_status" property="wmPoiBindStatus" jdbcType="TINYINT"/>
        <result column="wm_poi_bind_fail_reason" property="wmPoiBindFailReason" jdbcType="VARCHAR"/>
        <result column="clue_follow_up_status" property="clueFollowUpStatus" jdbcType="TINYINT"/>
        <result column="audit_status" property="auditStatus" jdbcType="TINYINT"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="INTEGER"/>
        <result column="muid" property="muid" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, canteen_primary_id, wm_poi_id, wdc_clue_id, clue_generate_status, clue_generate_fail_reason,
        clue_bind_status, clue_bind_fail_reason, wm_poi_bind_status, wm_poi_bind_fail_reason, clue_follow_up_status,
        audit_status, valid, cuid, muid, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        /*master*/select
            <include refid="Base_Column_List"/>
        from
            wm_canteen_stall_bind
        where
            id = #{id,jdbcType=INTEGER}
        and
            valid = 1
    </select>


    <select id="selectByBindIdList"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_canteen_stall_bind
        where
            id in
            <foreach collection="bindIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        and
            valid = 1
        order by
            id desc
    </select>


    <update id="updateAuditStatusByPrimaryIdList">
        update
            wm_canteen_stall_bind
        set
            audit_status = #{auditStatus,jdbcType=TINYINT}
        where
            valid = 1
        and
            id in
            <foreach collection="bindIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </update>


    <update id="updateClueFollowUpStatusByPrimaryIdList">
        update
            wm_canteen_stall_bind
        set
            clue_follow_up_status = #{clueFollowUpStatus,jdbcType=TINYINT}
        where
            valid = 1
        and
            id in
            <foreach collection="bindIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </update>


    <select id="selectByWdcClueIdWithClueGenerating"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_canteen_stall_bind
        where
            wdc_clue_id = #{wdcClueId,jdbcType=BIGINT}
        and
            valid = 1
        and
            clue_generate_status = 10
    </select>


    <select id="selectCountBySearchBO"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindQueryBO"
            resultType="java.lang.Integer">
        select
            count(id)
        from
            wm_canteen_stall_bind
            <where>
                valid != 0
                <if test="bindId != null">
                    and id = #{bindId,jdbcType=INTEGER}
                </if>

                <if test="bindIdList != null and bindIdList.size > 0">
                    and id in
                    <foreach collection="bindIdList" item="item" separator="," open="(" close=")">
                        #{item,jdbcType=INTEGER}
                    </foreach>
                </if>

                <if test="canteenPrimaryId != null and canteenPrimaryId != 0">
                    and canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
                </if>

                <if test="wmPoiId != null">
                    and wm_poi_id = #{wmPoiId,jdbcType=BIGINT}
                </if>

                <if test="wdcClueId != null">
                    and wdc_clue_id = #{wdcClueId,jdbcType=BIGINT}
                </if>

                <if test="clueGenerateStatus != null">
                    and clue_generate_status = #{clueGenerateStatus,jdbcType=INTEGER}
                </if>

                <if test="clueBindStatus != null">
                    and clue_bind_status = #{clueBindStatus,jdbcType=INTEGER}
                </if>

                <if test="wmPoiBindStatus != null">
                    and wm_poi_bind_status = #{wmPoiBindStatus,jdbcType=INTEGER}
                </if>

                <if test="clueFollowUpStatus != null">
                    and clue_follow_up_status = #{clueFollowUpStatus,jdbcType=INTEGER}
                </if>

                <if test="dslQuery != null and dslQuery != ''">
                    and ${dslQuery}
                </if>
            </where>
    </select>


    <select id="selectBindListBySearchBO"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindQueryBO"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_canteen_stall_bind
        <where>
            valid != 0
            <if test="bindId != null">
                and id = #{bindId,jdbcType=INTEGER}
            </if>

            <if test="bindIdList != null and bindIdList.size > 0">
                and id in
                <foreach collection="bindIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>

            <if test="canteenPrimaryId != null and canteenPrimaryId != 0">
                and canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
            </if>

            <if test="wmPoiId != null">
                and wm_poi_id = #{wmPoiId,jdbcType=BIGINT}
            </if>

            <if test="wdcClueId != null">
                and wdc_clue_id = #{wdcClueId,jdbcType=BIGINT}
            </if>

            <if test="clueGenerateStatus != null">
                and clue_generate_status = #{clueGenerateStatus,jdbcType=INTEGER}
            </if>

            <if test="clueBindStatus != null">
                and clue_bind_status = #{clueBindStatus,jdbcType=INTEGER}
            </if>

            <if test="wmPoiBindStatus != null">
                and wm_poi_bind_status = #{wmPoiBindStatus,jdbcType=INTEGER}
            </if>

            <if test="clueFollowUpStatus != null">
                and clue_follow_up_status = #{clueFollowUpStatus,jdbcType=INTEGER}
            </if>

            <if test="dslQuery != null and dslQuery != ''">
                and ${dslQuery}
            </if>
        </where>
        order by
            id desc
        limit #{pageFrom},#{pageSize}
    </select>

    <select id="selectByWmPoiId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wm_poi_id = #{wmPoiId,jdbcType=BIGINT}
        and
            valid = 1
    </select>

    <select id="selectByWdcCludId"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wdc_clue_id = #{wdcClueId,jdbcType=BIGINT}
        and
            valid = 1
    </select>

    <select id="selectByCanteenPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
            valid = 1;
    </select>


    <select id="selectByCanteenPrimaryIdWithClueBindingOrBindSuccess"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
        and
            clue_bind_status in (10,30)
    </select>


    <select id="selectByCanteenPrimaryIdListWithClueBindingOrBindSuccess"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            canteen_primary_id in
            <foreach collection="canteenPrimaryIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        and
            valid = 1
        and
            clue_bind_status in (10,30)
    </select>


    <select id="selectByCanteenPrimaryIdWithWmPoiBindSuccess"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
        and
            wm_poi_bind_status = 30
    </select>

    <select id="selectByCanteenPrimaryIdWithWmPoiBindingStatus"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        wm_canteen_stall_bind
        where
        canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
        valid = 1
        and
        wm_poi_bind_status in (30,40,50)
    </select>



    <select id="selectByCanteenPrimaryIdListWithWmPoiBindSuccess"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            canteen_primary_id in
            <foreach collection="canteenPrimaryIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        and
            valid = 1
        and
            wm_poi_bind_status = 30
    </select>


    <select id="selectByWdcClueIdWithClueBindingOrBindSuccess"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wdc_clue_id = #{wdcClueId,jdbcType=BIGINT}
        and
            valid = 1
        and
            clue_bind_status in (10,30)
    </select>


    <select id="selectByWdcClueIdList"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wdc_clue_id in
            <foreach collection="wdcClueIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        and
            valid = 1
    </select>

    <select id="selectByWmPoiIdList"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wm_poi_id in
            <foreach collection="wmPoiIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        and
            valid = 1
    </select>


    <select id="selectByWdcClueIdWithClueBindSuccess"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wdc_clue_id = #{wdcClueId,jdbcType=BIGINT}
        and
            valid = 1
        and
            clue_bind_status = 30
    </select>

    <select id="selectByCanteenPrimaryIdWithSpecificBindStatus"
            parameterType="map"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        wm_canteen_stall_bind
        where
        canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
        valid = 1
        and
        clue_bind_status in
        <foreach collection="statusList" item="status" separator="," open="(" close=")">
            #{status,jdbcType=TINYINT}
        </foreach>
    </select>

    <select id="selectByWmPoiIdWithRebind"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        wm_canteen_stall_bind
        where
        wm_poi_id in
        <foreach collection="wmPoiIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and
        (wm_poi_bind_status = 40 or wm_poi_bind_status = 50)
        and
        valid = 1
    </select>


    <select id="selectByWdcClueId"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wdc_clue_id = #{wdcClueId,jdbcType=BIGINT}
        and
            valid = 1
    </select>


    <select id="selectByWdcClueIdAndCanteenPrimaryId"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wdc_clue_id = #{wdcClueId,jdbcType=BIGINT}
        and
            canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
    </select>


    <select id="selectByWmPoiIdAndCanteenPrimaryId"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_canteen_stall_bind
        where
            wm_poi_id = #{wmPoiId,jdbcType=BIGINT}
        and
            canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
    </select>


    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_canteen_stall_bind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="canteenPrimaryId != null">
                canteen_primary_id,
            </if>
            <if test="wmPoiId != null">
                wm_poi_id,
            </if>
            <if test="wdcClueId != null">
                wdc_clue_id,
            </if>
            <if test="clueGenerateStatus != null">
                clue_generate_status,
            </if>
            <if test="clueGenerateFailReason != null">
                clue_generate_fail_reason,
            </if>
            <if test="clueBindStatus != null">
                clue_bind_status,
            </if>
            <if test="clueBindFailReason != null">
                clue_bind_fail_reason,
            </if>
            <if test="wmPoiBindStatus != null">
                wm_poi_bind_status,
            </if>
            <if test="wmPoiBindFailReason != null">
                wm_poi_bind_fail_reason,
            </if>
            <if test="clueFollowUpStatus != null">
                clue_follow_up_status,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="canteenPrimaryId != null">
                #{canteenPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="wmPoiId != null">
                #{wmPoiId,jdbcType=BIGINT},
            </if>
            <if test="wdcClueId != null">
                #{wdcClueId,jdbcType=BIGINT},
            </if>
            <if test="clueGenerateStatus != null">
                #{clueGenerateStatus,jdbcType=INTEGER},
            </if>
            <if test="clueGenerateFailReason != null">
                #{clueGenerateFailReason,jdbcType=VARCHAR},
            </if>
            <if test="clueBindStatus != null">
                #{clueBindStatus,jdbcType=INTEGER},
            </if>
            <if test="clueBindFailReason != null">
                #{clueBindFailReason,jdbcType=VARCHAR},
            </if>
            <if test="wmPoiBindStatus != null">
                #{wmPoiBindStatus,jdbcType=INTEGER},
            </if>
            <if test="wmPoiBindFailReason != null">
                #{wmPoiBindFailReason,jdbcType=VARCHAR},
            </if>
            <if test="clueFollowUpStatus != null">
                #{clueFollowUpStatus,jdbcType=INTEGER},
            </if>
            <if test="auditStatus != null">
                #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=INTEGER},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO">
        update
            wm_canteen_stall_bind
        <set>
            utime = UNIX_TIMESTAMP(),
            <if test="canteenPrimaryId != null">
                canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="wmPoiId != null">
                wm_poi_id = #{wmPoiId,jdbcType=BIGINT},
            </if>
            <if test="wdcClueId != null">
                wdc_clue_id = #{wdcClueId,jdbcType=BIGINT},
            </if>
            <if test="clueGenerateStatus != null">
                clue_generate_status = #{clueGenerateStatus,jdbcType=INTEGER},
            </if>
            <if test="clueGenerateFailReason != null">
                clue_generate_fail_reason = #{clueGenerateFailReason,jdbcType=VARCHAR},
            </if>
            <if test="clueBindStatus != null">
                clue_bind_status = #{clueBindStatus,jdbcType=INTEGER},
            </if>
            <if test="clueBindFailReason != null">
                clue_bind_fail_reason = #{clueBindFailReason,jdbcType=VARCHAR},
            </if>
            <if test="wmPoiBindStatus != null">
                wm_poi_bind_status = #{wmPoiBindStatus,jdbcType=INTEGER},
            </if>
            <if test="wmPoiBindFailReason != null">
                wm_poi_bind_fail_reason = #{wmPoiBindFailReason,jdbcType=VARCHAR},
            </if>
            <if test="clueFollowUpStatus != null">
                clue_follow_up_status = #{clueFollowUpStatus,jdbcType=INTEGER},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=INTEGER},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=INTEGER},
            </if>
        </set>
        where
            id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateWmPoiBindStatusByBindIdList">
        update
        wm_canteen_stall_bind
        set
        wm_poi_bind_status = #{wmPoiBindStatus,jdbcType=TINYINT},
        utime = UNIX_TIMESTAMP()
        where
        id in
        <foreach collection="bindIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateWmPoiBindStatusByWmPoiIdList">
        update
        wm_canteen_stall_bind
        set
        wm_poi_bind_status = #{wmPoiBindStatus,jdbcType=TINYINT},
        utime = UNIX_TIMESTAMP()
        where
        wm_poi_id in
        <foreach collection="wmPoiIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>


    <select id="selectByWmPoiIdsAndCanteenPriIdWitchStatus"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        wm_canteen_stall_bind
        where
        wm_poi_id in
        <foreach collection="wmPoiIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and
        wm_poi_bind_status = #{wmAuditStatus,jdbcType=TINYINT}
        and
        canteen_primary_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
        valid = 1
    </select>


</mapper>