<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="kp_id" property="kpId" jdbcType="INTEGER"/>
        <result column="state" property="state" jdbcType="TINYINT"/>
        <result column="kp_type" property="kpType" jdbcType="TINYINT"/>
        <result column="signer_type" property="signerType" jdbcType="TINYINT"/>
        <result column="compellation" property="compellation" jdbcType="VARCHAR"/>
        <result column="cert_type" property="certType" jdbcType="TINYINT"/>
        <result column="cert_number" property="certNumber" jdbcType="VARCHAR"/>
        <result column="phone_num" property="phoneNum" jdbcType="VARCHAR"/>
        <result column="bank_id" property="bankId" jdbcType="INTEGER"/>
        <result column="bank_name" property="bankName" jdbcType="VARCHAR"/>
        <result column="credit_card" property="creditCard" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="agent_auth" property="agentAuth" jdbcType="VARCHAR"/>
        <result column="agent_front_idcard" property="agentFrontIdcard" jdbcType="VARCHAR"/>
        <result column="agent_back_idcard" property="agentBackIdcard" jdbcType="VARCHAR"/>
        <result column="special_reason" property="specialReason" jdbcType="VARCHAR"/>
        <result column="special_attachment" property="specialAttachment" jdbcType="VARCHAR"/>
        <result column="fail_reason" property="failReason" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="sign_task_type" property="signTaskType" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="bank_id_temp" jdbcType="INTEGER" property="bankIdTemp"/>
        <result column="phone_num_encryption" property="phoneNumEncryption" jdbcType="VARCHAR"/>
        <result column="phone_num_token"  property="phoneNumToken" jdbcType="VARCHAR"/>
        <result column="credit_card_encryption" property="creditCardEncryption" jdbcType="VARCHAR"/>
        <result column="credit_card_token"  property="creditCardToken" jdbcType="VARCHAR"/>
        <result column="cert_number_encryption" property="certNumberEncryption" jdbcType="VARCHAR"/>
        <result column="cert_number_token"  property="certNumberToken" jdbcType="VARCHAR"/>
        <result column="have_agent_auth" jdbcType="TINYINT" property="haveAgentAuth"/>
        <result column="legal_idcard_copy"  property="legalIdcardCopy" jdbcType="VARCHAR"/>
        <result column="legal_auth_type" jdbcType="INTEGER" property="legalAuthType"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, kp_id, state, kp_type, signer_type, compellation, cert_type, cert_number, phone_num,
    bank_id, bank_name, credit_card, email, agent_auth, agent_front_idcard, agent_back_idcard,
    special_reason, special_attachment, fail_reason, ctime, utime, valid, sign_task_type, remark,bank_id_temp,phone_num_encryption,phone_num_token,
    credit_card_encryption,credit_card_token,cert_number_encryption,cert_number_token,have_agent_auth,legal_idcard_copy,legal_auth_type
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from wm_customer_kp_temp
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByKpId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from wm_customer_kp_temp
        where kp_id = #{kpId,jdbcType=INTEGER}
        and valid=1
    </select>

    <select id="selectByKpIdMaster" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        /*master*/select
        <include refid="Base_Column_List"/>
        from wm_customer_kp_temp
        where kp_id = #{kpId,jdbcType=INTEGER}
        and valid=1
    </select>

    <update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    update wm_customer_kp_temp set valid=0
    where id = #{id,jdbcType=INTEGER}
  </update>

    <update id="deleteByKpIdList" parameterType="java.util.List">
        update wm_customer_kp_temp set valid = 0 where kp_id in
        <foreach collection="kpIdList" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </update>

    <insert id="insert" parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp"
            keyProperty="id" useGeneratedKeys="true">
    insert into wm_customer_kp_temp (kp_id, state,
      kp_type, signer_type, compellation,
      cert_type,
        <if test="certNumber != null and notSaveCertNumber !=1">
            cert_number,
        </if>
        <if test="phoneNum != null and notSavePhoneNum !=1">
            phone_num,
        </if>
      bank_id, bank_name,
        <if test="creditCard != null and notSaveCreditCard !=1">
            credit_card,
        </if>
      email, agent_auth, agent_front_idcard,
      agent_back_idcard, special_reason, special_attachment,
      fail_reason, ctime, utime,
      valid, sign_task_type, remark,
        <if test="phoneNumEncryption != null ">
            phone_num_encryption,
        </if>
        <if test="phoneNumToken != null ">
            phone_num_token,
        </if>
        <if test="creditCardEncryption != null ">
            credit_card_encryption,
        </if>
        <if test="creditCardToken != null ">
            credit_card_token,
        </if>

        <if test="certNumberEncryption != null ">
            cert_number_encryption,
        </if>
        <if test="certNumberToken != null ">
            cert_number_token,
        </if>
        <if test="haveAgentAuth != null ">
            have_agent_auth,
        </if>
        <if test="legalIdcardCopy != null ">
            legal_idcard_copy,
        </if>
        <if test="legalAuthType != null ">
            legal_auth_type,
        </if>
      bank_id_temp)
    values (#{kpId,jdbcType=INTEGER}, #{state,jdbcType=TINYINT},
      #{kpType,jdbcType=TINYINT}, #{signerType,jdbcType=TINYINT}, #{compellation,jdbcType=VARCHAR},
      #{certType,jdbcType=TINYINT},
        <if test="certNumber != null and notSaveCertNumber !=1">
            #{certNumber},
        </if>
        <if test="phoneNum != null and notSavePhoneNum !=1">
            #{phoneNum},
        </if>
      #{bankId,jdbcType=INTEGER}, #{bankName,jdbcType=VARCHAR},
        <if test="creditCard != null and notSaveCreditCard !=1">
            #{creditCard},
        </if>
      #{email,jdbcType=VARCHAR}, #{agentAuth,jdbcType=VARCHAR}, #{agentFrontIdcard,jdbcType=VARCHAR},
      #{agentBackIdcard,jdbcType=VARCHAR}, #{specialReason,jdbcType=VARCHAR}, #{specialAttachment,jdbcType=VARCHAR},
      #{failReason,jdbcType=VARCHAR}, UNIX_TIMESTAMP(), #{utime,jdbcType=INTEGER},
      #{valid,jdbcType=TINYINT}, #{signTaskType,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        <if test="phoneNumEncryption != null ">
            #{phoneNumEncryption},
        </if>
        <if test="phoneNumToken != null ">
            #{phoneNumToken},
        </if>
        <if test="creditCardEncryption != null ">
            #{creditCardEncryption},
        </if>
        <if test="creditCardToken != null ">
            #{creditCardToken},
        </if>

        <if test="certNumberEncryption != null ">
            #{certNumberEncryption},
        </if>
        <if test="certNumberToken != null ">
            #{certNumberToken},
        </if>
        <if test="haveAgentAuth != null ">
            #{haveAgentAuth},
        </if>
        <if test="legalIdcardCopy != null ">
            #{legalIdcardCopy},
        </if>
        <if test="legalAuthType != null ">
            #{legalAuthType},
        </if>
      #{bankIdTemp,jdbcType=INTEGER})
  </insert>
    <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp" keyProperty="id" useGeneratedKeys="true">
        insert into wm_customer_kp_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="kpId != null">
                kp_id,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="kpType != null">
                kp_type,
            </if>
            <if test="signerType != null">
                signer_type,
            </if>
            <if test="compellation != null">
                compellation,
            </if>
            <if test="certType != null">
                cert_type,
            </if>
            <if test="certNumber != null and notSaveCertNumber !=1">
                cert_number,
            </if>
            <if test="phoneNum != null and notSavePhoneNum !=1">
                phone_num,
            </if>
            <if test="bankId != null">
                bank_id,
            </if>
            <if test="bankName != null">
                bank_name,
            </if>
            <if test="creditCard != null and notSaveCreditCard !=1">
                credit_card,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="agentAuth != null">
                agent_auth,
            </if>
            <if test="agentFrontIdcard != null">
                agent_front_idcard,
            </if>
            <if test="agentBackIdcard != null">
                agent_back_idcard,
            </if>
            <if test="specialReason != null">
                special_reason,
            </if>
            <if test="specialAttachment != null">
                special_attachment,
            </if>
            <if test="failReason != null">
                fail_reason,
            </if>
            ctime,
            <if test="utime != null">
                utime,
            </if>
            <if test="valid != null">
                valid,
            </if>
            <if test="signTaskType != null">
                sign_task_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="bankIdTemp != null">
                bank_id_temp,
            </if>
            <if test="phoneNumEncryption != null ">
                phone_num_encryption,
            </if>
            <if test="phoneNumToken != null ">
                phone_num_token,
            </if>
            <if test="creditCardEncryption != null ">
                credit_card_encryption,
            </if>
            <if test="creditCardToken != null ">
                credit_card_token,
            </if>
            <if test="certNumberEncryption != null ">
                cert_number_encryption,
            </if>
            <if test="certNumberToken != null ">
                cert_number_token,
            </if>
            <if test="haveAgentAuth != null ">
                have_agent_auth,
            </if>
            <if test="legalIdcardCopy != null ">
                legal_idcard_copy,
            </if>
            <if test="legalAuthType != null ">
                legal_auth_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="kpId != null">
                #{kpId,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="kpType != null">
                #{kpType,jdbcType=TINYINT},
            </if>
            <if test="signerType != null">
                #{signerType,jdbcType=TINYINT},
            </if>
            <if test="compellation != null">
                #{compellation,jdbcType=VARCHAR},
            </if>
            <if test="certType != null">
                #{certType,jdbcType=TINYINT},
            </if>
            <if test="certNumber != null and notSaveCertNumber !=1">
                #{certNumber,jdbcType=VARCHAR},
            </if>
            <if test="phoneNum != null and notSavePhoneNum !=1">
                #{phoneNum,jdbcType=VARCHAR},
            </if>
            <if test="bankId != null">
                #{bankId,jdbcType=INTEGER},
            </if>
            <if test="bankName != null">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="creditCard != null and notSaveCreditCard !=1">
                #{creditCard,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="agentAuth != null">
                #{agentAuth,jdbcType=VARCHAR},
            </if>
            <if test="agentFrontIdcard != null">
                #{agentFrontIdcard,jdbcType=VARCHAR},
            </if>
            <if test="agentBackIdcard != null">
                #{agentBackIdcard,jdbcType=VARCHAR},
            </if>
            <if test="specialReason != null">
                #{specialReason,jdbcType=VARCHAR},
            </if>
            <if test="specialAttachment != null">
                #{specialAttachment,jdbcType=VARCHAR},
            </if>
            <if test="failReason != null">
                #{failReason,jdbcType=VARCHAR},
            </if>
            UNIX_TIMESTAMP(),
            <if test="utime != null">
                #{utime,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=TINYINT},
            </if>
            <if test="signTaskType != null">
                #{signTaskType,jdbcType=VARCHAR} ,
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR} ,
            </if>
            <if test="bankIdTemp != null">
                #{bankIdTemp,jdbcType=INTEGER},
            </if>
            <if test="phoneNumEncryption != null ">
                #{phoneNumEncryption},
            </if>
            <if test="phoneNumToken != null ">
                #{phoneNumToken},
            </if>
            <if test="creditCardEncryption != null ">
                #{creditCardEncryption},
            </if>
            <if test="creditCardToken != null ">
                #{creditCardToken},
            </if>
            <if test="certNumberEncryption != null ">
                #{certNumberEncryption},
            </if>
            <if test="certNumberToken != null ">
                #{certNumberToken},
            </if>
            <if test="haveAgentAuth != null ">
                #{haveAgentAuth},
            </if>
            <if test="legalIdcardCopy != null ">
                #{legalIdcardCopy},
            </if>
            <if test="legalAuthType != null ">
                #{legalAuthType},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp">
        update wm_customer_kp_temp
        <set>
            <if test="kpId != null">
                kp_id = #{kpId,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="kpType != null">
                kp_type = #{kpType,jdbcType=TINYINT},
            </if>
            <if test="signerType != null">
                signer_type = #{signerType,jdbcType=TINYINT},
            </if>
            <if test="compellation != null">
                compellation = #{compellation,jdbcType=VARCHAR},
            </if>
            <if test="certType != null">
                cert_type = #{certType,jdbcType=TINYINT},
            </if>
            <if test="certNumber != null and notSaveCertNumber !=1">
                cert_number = #{certNumber,jdbcType=VARCHAR},
            </if>
            <if test="phoneNum != null and notSavePhoneNum !=1">
                phone_num = #{phoneNum,jdbcType=VARCHAR},
            </if>
            <if test="bankId != null">
                bank_id = #{bankId,jdbcType=INTEGER},
            </if>
            <if test="bankName != null">
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="creditCard != null and notSaveCreditCard !=1">
                credit_card = #{creditCard,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="agentAuth != null">
                agent_auth = #{agentAuth,jdbcType=VARCHAR},
            </if>
            <if test="agentFrontIdcard != null">
                agent_front_idcard = #{agentFrontIdcard,jdbcType=VARCHAR},
            </if>
            <if test="agentBackIdcard != null">
                agent_back_idcard = #{agentBackIdcard,jdbcType=VARCHAR},
            </if>
            <if test="specialReason != null">
                special_reason = #{specialReason,jdbcType=VARCHAR},
            </if>
            <if test="specialAttachment != null">
                special_attachment = #{specialAttachment,jdbcType=VARCHAR},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason,jdbcType=VARCHAR},
            </if>
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=INTEGER},
            </if>
            utime = UNIX_TIMESTAMP(),
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            <if test="signTaskType != null">
                sign_task_type = #{signTaskType,jdbcType=VARCHAR} ,
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR} ,
            </if>
            <if test="bankIdTemp != null">
                bank_id_temp = #{bankIdTemp,jdbcType=INTEGER},
            </if>
            <if test="phoneNumEncryption != null ">
                phone_num_encryption = #{phoneNumEncryption},
            </if>
            <if test="phoneNumToken != null">
                phone_num_token = #{phoneNumToken},
            </if>
            <if test="certNumberEncryption != null ">
                cert_number_encryption=#{certNumberEncryption},
            </if>
            <if test="certNumberToken != null ">
                cert_number_token= #{certNumberToken},
            </if>
            <if test="creditCardEncryption != null">
                credit_card_encryption = #{creditCardEncryption},
            </if>
            <if test="creditCardToken != null ">
                credit_card_token = #{creditCardToken},
            </if>
            <if test="haveAgentAuth != null ">
                have_agent_auth = #{haveAgentAuth},
            </if>
            <if test="legalIdcardCopy != null ">
                legal_idcard_copy = #{legalIdcardCopy},
            </if>
            <if test="legalAuthType != null ">
                legal_auth_type = #{legalAuthType}
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp">
    update wm_customer_kp_temp
    set kp_id = #{kpId,jdbcType=INTEGER},
      state = #{state,jdbcType=TINYINT},
      kp_type = #{kpType,jdbcType=TINYINT},
      signer_type = #{signerType,jdbcType=TINYINT},
      compellation = #{compellation,jdbcType=VARCHAR},
      cert_type = #{certType,jdbcType=TINYINT},
        <if test="certNumber != null and notSaveCertNumber !=1">
            cert_number = #{certNumber},
        </if>
        <if test="certNumberEncryption != null ">
            cert_number_encryption=#{certNumberEncryption},
        </if>
        <if test="certNumberToken != null ">
            cert_number_token= #{certNumberToken},
        </if>
        <if test="phoneNum != null and notSavePhoneNum !=1">
            phone_num = #{phoneNum},
        </if>
        <if test="phoneNumEncryption != null ">
            phone_num_encryption = #{phoneNumEncryption},
        </if>
        <if test="phoneNumToken != null">
            phone_num_token = #{phoneNumToken},
        </if>
      bank_id = #{bankId,jdbcType=INTEGER},
      bank_name = #{bankName,jdbcType=VARCHAR},
        <if test="creditCard != null and notSaveCreditCard !=1">
            credit_card = #{creditCard},
        </if>
        <if test="creditCardEncryption != null">
            credit_card_encryption = #{creditCardEncryption},
        </if>
        <if test="creditCardToken != null ">
            credit_card_token = #{creditCardToken},
        </if>
        <if test="haveAgentAuth != null ">
            have_agent_auth = #{haveAgentAuth},
        </if>
        <if test="legalIdcardCopy != null ">
            legal_idcard_copy = #{legalIdcardCopy},
        </if>
        <if test="legalAuthType != null ">
            legal_auth_type = #{legalAuthType},
        </if>
      email = #{email,jdbcType=VARCHAR},
      agent_auth = #{agentAuth,jdbcType=VARCHAR},
      agent_front_idcard = #{agentFrontIdcard,jdbcType=VARCHAR},
      agent_back_idcard = #{agentBackIdcard,jdbcType=VARCHAR},
      special_reason = #{specialReason,jdbcType=VARCHAR},
      special_attachment = #{specialAttachment,jdbcType=VARCHAR},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=INTEGER},
      utime = UNIX_TIMESTAMP(),
      valid = #{valid,jdbcType=TINYINT},
      sign_task_type = #{signTaskType,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      bank_id_temp = #{bankIdTemp,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>


    <select id="selectByIdListByKpType" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_customer_kp_temp
        where kp_id in
        <foreach collection="idList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and kp_type = #{kpType}
        and valid = 1
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_customer_kp_temp
        where kp_id in
        <foreach collection="idList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and valid = 1
    </select>


    <select id="selectByCondition" resultMap="BaseResultMap"   parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTempSearchCondition">
        select
        <include refid="Base_Column_List"/>
        from wm_customer_kp_temp
        <include refid="Example_Where_Clause"/>
    </select>

    <sql id="Example_Where_Clause">
        where 1=1
        <if test="valid != null ">
            and valid = #{valid}
        </if>
        <if test="minId != null ">
            and id > #{minId}
        </if>
        <if test="maxId != null ">
            and id &lt;= #{maxId}
        </if>
        <if test="kpType != null ">
            and kp_type = #{kpType}
        </if>
        <if test="signerType != null ">
            and signer_type = #{signerType}
        </if>
        <if test="kpIdList != null and kpIdList.size >0">
            and kp_id in
            <foreach collection="kpIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="state != null ">
            and state = #{state}
        </if>
        order by id asc
        <if test="limit != null ">
            limit #{limit}
        </if>
    </sql>

    <update id="updateKp"
            parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp">
        update wm_customer_kp_temp
        <set>
            <if test="phoneNum != null and notSavePhoneNum!=1">
                phone_num = #{phoneNum},
            </if>
            <if test="phoneNumEncryption != null ">
                phone_num_encryption = #{phoneNumEncryption},
            </if>
            <if test="phoneNumToken != null">
                phone_num_token = #{phoneNumToken},
            </if>
            <if test="certNumber != null and notSaveCertNumber!=1">
                cert_number = #{certNumber,jdbcType=VARCHAR},
            </if>
            <if test="creditCard != null and notSaveCreditCard !=1">
                credit_card = #{creditCard,jdbcType=VARCHAR},
            </if>
            <if test="certNumberEncryption != null ">
                cert_number_encryption=#{certNumberEncryption},
            </if>
            <if test="certNumberToken != null ">
                cert_number_token= #{certNumberToken},
            </if>
            <if test="creditCardEncryption != null">
                credit_card_encryption = #{creditCardEncryption},
            </if>
            <if test="creditCardToken != null ">
                credit_card_token = #{creditCardToken},
            </if>
            <if test="haveAgentAuth != null ">
                have_agent_auth = #{haveAgentAuth},
            </if>
            <if test="legalIdcardCopy != null ">
                legal_idcard_copy = #{legalIdcardCopy},
            </if>
            <if test="legalAuthType != null ">
                legal_auth_type = #{legalAuthType},
            </if>
            <!-- utime无需判空 -->
            utime = UNIX_TIMESTAMP()
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>


    <update id="updateHaveAgentAuth" parameterType="java.lang.Integer" >
        update wm_customer_kp_temp set have_agent_auth=#{haveAgentAuth} where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateLegalAuthTypeToPaper">
        update wm_customer_kp_temp
        set
        legal_auth_type = 1,
        utime = UNIX_TIMESTAMP()
        where
        id = #{id}
    </update>

    <select id="getCustomerKpTempByKpIdFromRT" resultMap="BaseResultMap">
        /*master*/select
        <include refid="Base_Column_List"/>
        from wm_customer_kp_temp
        where kp_id=#{kpId} and valid=1
    </select>

</mapper>
