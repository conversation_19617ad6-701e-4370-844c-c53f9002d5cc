<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractRelMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractRel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="wm_templet_contract_id" jdbcType="BIGINT" property="wm_templet_contract_id"/>
        <result column="contract_biz_id" jdbcType="BIGINT" property="contract_biz_id"/>
        <result column="biz_type" jdbcType="INTEGER" property="biz_type"/>
        <result column="valid" jdbcType="TINYINT" property="valid"/>
        <result column="ctime" jdbcType="INTEGER" property="ctime"/>
        <result column="utime" jdbcType="INTEGER" property="utime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, wm_templet_contract_id, contract_biz_id, biz_type, valid, ctime, utime
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_templet_contract_rel
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByWmContractIdAndBizType"
            resultType="com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractRel">
        select
        <include refid="Base_Column_List"/>
        from wm_templet_contract_rel
        where wm_templet_contract_id = #{wmContractId,jdbcType=BIGINT}
        and biz_type = #{bizType,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wm_templet_contract_rel
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractRel">
    insert into wm_templet_contract_rel (id, wm_templet_contract_id, contract_biz_id,
      biz_type, valid, ctime,
      utime)
    values (#{id,jdbcType=BIGINT}, #{wm_templet_contract_id,jdbcType=BIGINT}, #{contract_biz_id,jdbcType=BIGINT},
      #{biz_type,jdbcType=INTEGER}, #{valid,jdbcType=TINYINT}, #{ctime,jdbcType=INTEGER},
      #{utime,jdbcType=INTEGER})
  </insert>
    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractRel">
        insert into wm_templet_contract_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="wm_templet_contract_id != null">
                wm_templet_contract_id,
            </if>
            <if test="contract_biz_id != null">
                contract_biz_id,
            </if>
            <if test="biz_type != null">
                biz_type,
            </if>
            valid,
            ctime,
            utime,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="wm_templet_contract_id != null">
                #{wm_templet_contract_id,jdbcType=BIGINT},
            </if>
            <if test="contract_biz_id != null">
                #{contract_biz_id,jdbcType=BIGINT},
            </if>
            <if test="biz_type != null">
                #{biz_type,jdbcType=INTEGER},
            </if>
            1,
            unix_timestamp(),
            unix_timestamp(),
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractRel">
        update wm_templet_contract_rel
        <set>
            <if test="wm_templet_contract_id != null">
                wm_templet_contract_id = #{wm_templet_contract_id,jdbcType=BIGINT},
            </if>
            <if test="contract_biz_id != null">
                contract_biz_id = #{contract_biz_id,jdbcType=BIGINT},
            </if>
            <if test="biz_type != null">
                biz_type = #{biz_type,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            utime = unix_timestamp(),
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractRel">
    update wm_templet_contract_rel
    set wm_templet_contract_id = #{wm_templet_contract_id,jdbcType=BIGINT},
      contract_biz_id = #{contract_biz_id,jdbcType=BIGINT},
      biz_type = #{biz_type,jdbcType=INTEGER},
      valid = #{valid,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=INTEGER},
      utime = #{utime,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <update id="updateValidByContractIdAndBizType">
        update wm_templet_contract_rel
        set valid = #{valid,jdbcType=TINYINT}
        where wm_templet_contract_id = #{wmContractId,jdbcType=BIGINT}
        and biz_type = #{bizType,jdbcType=INTEGER}
        and valid = 1
    </update>

    <update id="updateByWmContractId"
            parameterType="com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractRel">
        update wm_templet_contract_rel
        <set>
            <if test="contract_biz_id != null">
                contract_biz_id = #{contract_biz_id,jdbcType=BIGINT},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            utime = unix_timestamp()
        </set>
        where wm_templet_contract_id = #{wm_templet_contract_id,jdbcType=BIGINT}
        and biz_type = #{biz_type,jdbcType=INTEGER}
    </update>
</mapper>