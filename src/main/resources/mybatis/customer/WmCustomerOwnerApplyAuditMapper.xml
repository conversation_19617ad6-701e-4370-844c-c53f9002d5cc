<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerOwnerApplyAuditMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="apply_id" jdbcType="INTEGER" property="applyId"/>
        <result column="task_id" jdbcType="INTEGER" property="taskId"/>
        <result column="audit_status" jdbcType="TINYINT" property="auditStatus"/>
        <result column="audit_uid" jdbcType="INTEGER" property="auditUid"/>
        <result column="audit_result" jdbcType="VARCHAR" property="auditResult"/>
        <result column="audit_time" jdbcType="INTEGER" property="auditTime"/>
        <result column="valid" jdbcType="INTEGER" property="valid"/>
        <result column="ctime" jdbcType="INTEGER" property="ctime"/>
        <result column="utime" jdbcType="INTEGER" property="utime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, apply_id, task_id, audit_status, audit_uid, audit_time,valid, ctime, utime,audit_result
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.task.WmCustomerTask"
            useGeneratedKeys="true" keyProperty="id">
        insert into wm_customer_owner_apply_audit (apply_id, task_id, audit_status, audit_uid, audit_time,valid, ctime,
        utime)
        values
        (#{applyId,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, #{auditStatus,jdbcType=TINYINT},
        #{auditUid,jdbcType=INTEGER}, #{auditTime,jdbcType=INTEGER},#{valid,jdbcType=INTEGER},
        unix_timestamp(), unix_timestamp())
    </insert>

    <select id="getByTaskId" parameterType="int" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_customer_owner_apply_audit
        where task_id = #{taskId}
        and valid = 1
    </select>

    <select id="getByApplyId" parameterType="int" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_customer_owner_apply_audit
        where apply_id = #{applyId}
        and valid = 1
    </select>

    <select id="getByApplyIdRT" parameterType="int" resultMap="BaseResultMap">
        /*master*/
        select
        <include refid="Base_Column_List"/>
        from wm_customer_owner_apply_audit
        where apply_id = #{applyId}
        and valid = 1
    </select>

    <update id="updateAuditResultByUpdateBo"
            parameterType="com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAuditBO">
        update wm_customer_owner_apply_audit
        set audit_status = #{status},
        audit_uid = #{auditUid},
        audit_time = #{auditTime},
        <if test="auditResult != null">
            audit_result = #{auditResult},
        </if>
        utime = unix_timestamp(),
        valid = 0
        where apply_id = #{applyId}
    </update>

    <update id="updateTaskIdById">
        update wm_customer_owner_apply_audit
        set task_id = #{taskId},
        utime = unix_timestamp()
        where id = #{id}
    </update>
    <update id="update2UnValidById">
        update wm_customer_owner_apply_audit
        set valid = 0,
        utime = unix_timestamp()
        where id = #{id}
        and valid=1
    </update>
    <update id="update2UnValidByApplyId">
        update wm_customer_owner_apply_audit
        set valid = 0,
        utime = unix_timestamp()
        where apply_id = #{applyId}
        and valid=1
    </update>

    <select id="getByApplyIdIgnoreValid" parameterType="int" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_customer_owner_apply_audit
        where apply_id = #{applyId}
    </select>

</mapper>