<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerCommonQuaDBMapper" >

  <insert id="batchInsertCommonQua">
    insert into wm_customer_common_qua (customer_id, pic_url,ctime,utime,type)
    values
    <foreach collection ="set" item="item" index= "index" separator =",">
      (#{customerId}, #{item},UNIX_TIMESTAMP(),UNIX_TIMESTAMP(),1)
    </foreach >
  </insert>

  <delete id="deleteCommonQua" parameterType="java.lang.Integer">
    delete from wm_customer_common_qua where customer_id=#{customerId} and type = 1
  </delete>


  <select id="selectCommonQuaList" parameterType="java.lang.Integer" resultType="java.lang.String">
    select pic_url from wm_customer_common_qua where customer_id=#{customerId} and type = 1
  </select>

  <insert id="batchInsertCommonQuaOther">
    insert into wm_customer_common_qua (customer_id, pic_url,ctime,utime,type)
    values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (#{customerId}, #{item},UNIX_TIMESTAMP(),UNIX_TIMESTAMP(),2)
    </foreach >
  </insert>

  <delete id="deleteCommonQuaOther" parameterType="java.lang.Integer">
    delete from wm_customer_common_qua where customer_id=#{customerId} and type = 2
  </delete>


  <select id="selectCommonQuaOtherList" parameterType="java.lang.Integer" resultType="java.lang.String">
    select pic_url from wm_customer_common_qua where customer_id=#{customerId} and type = 2
  </select>

  <select id="selectCommonQuaOtherListMaster" parameterType="java.lang.Integer" resultType="java.lang.String">
    /*master*/select pic_url from wm_customer_common_qua where customer_id=#{customerId} and type = 2
  </select>

</mapper>