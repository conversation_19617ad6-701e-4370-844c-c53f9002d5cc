<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper" >
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="batch_state" property="batchState" jdbcType="VARCHAR" />
        <result column="batch_context" property="batchContext" jdbcType="VARCHAR" />
        <result column="record_key" property="recordKey" jdbcType="VARCHAR" />
        <result column="valid" property="valid" jdbcType="INTEGER" />
        <result column="ctime" property="ctime" jdbcType="INTEGER" />
        <result column="utime" property="utime" jdbcType="INTEGER" />
        <result column="version" property="version" jdbcType="INTEGER" />
        <result column="customer_id" property="customerId" jdbcType="INTEGER" />
        <result column="commit_uid" property="commitUid" jdbcType="INTEGER" />
        <result column="object_name" property="objectName" jdbcType="VARCHAR" />
        <result column="pack_id" property="packId" jdbcType="BIGINT" />
        <result column="notify_info" property="notifyInfo" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, batch_state AS batchState, batch_context AS batchContext, record_key AS recordKey, valid, ctime, utime, version, pack_id, notify_info
    </sql>

    <sql id="Base_Origin_Column_List">
        id, batch_state, batch_context, record_key, valid, ctime, utime, version, customer_id, commit_uid, pack_id, notify_info
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        wm_econtract_sign_batch
        (batch_state, batch_context, record_key, valid, ctime, utime, version, customer_id, pack_id, notify_info
            <if test="commitUid != null and commitUid != 0">
                ,commit_uid
            </if>
        )
        VALUES
        (#{batchState}, #{batchContext}, #{recordKey}, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, #{customerId}, #{packId}, #{notifyInfo}
            <if test="commitUid != null and commitUid != 0">
                , #{commitUid}
            </if>
        )
    </insert>

    <update id="update" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB">
        UPDATE wm_econtract_sign_batch
        <set>
            <if test="batchState != null and batchState != ''">
                batch_state = #{batchState},
            </if>
            <if test="batchContext != null and batchContext != ''">
                batch_context = #{batchContext},
            </if>
            <if test="recordKey != null and recordKey != ''">
                record_key = #{recordKey},
            </if>
            <if test="customerId != null and customerId != 0">
                customer_id = #{customerId},
            </if>
            <if test="commitUid != null and commitUid != ''">
                commit_uid = #{commitUid},
            </if>
            <if test="packId != null and packId != 0">
                pack_id = #{packId},
            </if>
            <if test="notifyInfo != null and notifyInfo != ''">
                notify_info = #{notifyInfo},
            </if>
            utime = UNIX_TIMESTAMP(),
            version = version + 1
        </set>
        WHERE id = #{id}
    </update>

    <select id="batchQueryByBatchId" resultMap="BaseResultMap" parameterType="list">
        SELECT
        <include refid="Base_Origin_Column_List" />
        FROM
        wm_econtract_sign_batch
        WHERE id IN
        <foreach collection="batchIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>

    <update id="updateBigBatchContextById">
        update wm_econtract_sign_batch
        set object_name = #{objectName},
        utime = UNIX_TIMESTAMP()
        where id = #{id}
        and valid = 1
    </update>

    <select id="queryWithParam" parameterType="com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam"
      resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Origin_Column_List" />
        FROM
        wm_econtract_sign_batch
        WHERE customer_id = #{customerId}

        <if test="commitUid != null and commitUid != 0">
            AND commit_uid = #{commitUid}
        </if>

        <if test="startTime !=null and endTime !=null">
            AND ctime BETWEEN #{startTime} AND #{endTime}
        </if>

        AND valid = 1
        ORDER BY id DESC
        limit 30
    </select>

    <select id="queryWithParamWithoutPack" parameterType="com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Origin_Column_List" />
        FROM
        wm_econtract_sign_batch
        WHERE customer_id = #{customerId}

        <if test="commitUid != null and commitUid != 0">
            AND commit_uid = #{commitUid}
        </if>

        <if test="startTime !=null and endTime !=null">
            AND ctime BETWEEN #{startTime} AND #{endTime}
        </if>

        AND pack_id = 0
        AND valid = 1
        ORDER BY id DESC
        limit 30
    </select>

    <select id="queryIdWithParam" parameterType="com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam"
      resultType="java.lang.Long">
        SELECT id
        FROM
        wm_econtract_sign_batch
        WHERE customer_id = #{customerId}

        <if test="commitUid != null and commitUid != 0">
            AND commit_uid = #{commitUid}
        </if>

        <if test="startTime !=null and endTime !=null">
            AND ctime BETWEEN #{startTime} AND #{endTime}
        </if>

        AND valid = 1
        ORDER BY id DESC
        <if test="maxLimit !=null and maxLimit &gt; 0">
          limit #{maxLimit}
        </if>
    </select>

    <select id="queryIdWithParamWithoutPack" parameterType="com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam"
            resultType="java.lang.Long">
        SELECT id
        FROM
        wm_econtract_sign_batch
        WHERE customer_id = #{customerId}

        <if test="commitUid != null and commitUid != 0">
            AND commit_uid = #{commitUid}
        </if>

        <if test="startTime !=null and endTime !=null">
            AND ctime BETWEEN #{startTime} AND #{endTime}
        </if>

        AND pack_id = 0
        AND valid = 1
        ORDER BY id DESC
        <if test="maxLimit !=null and maxLimit &gt; 0">
            limit #{maxLimit}
        </if>
    </select>

    <select id="querySignBatchTask"
            parameterType="com.sankuai.meituan.waimai.customer.domain.contract.SignContractTaskParam"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Origin_Column_List"/>
        FROM wm_econtract_sign_batch
        WHERE customer_id = #{customerId}
        <if test="startTime !=null and endTime !=null">
            AND utime BETWEEN #{startTime} AND #{endTime}
        </if>
        AND batch_state = #{batchStatus}
        AND valid = 1
        ORDER BY utime DESC

        <choose>
            <when test="maxLimit !=null and maxLimit &gt; 0">
                limit #{maxLimit}
            </when>
            <otherwise>
                limit 200
            </otherwise>
        </choose>

    </select>

    <select id="queryPackIdsWithParam" parameterType="com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam"
            resultType="java.lang.Long">
        SELECT distinct pack_id
        FROM
        wm_econtract_sign_batch
        WHERE customer_id = #{customerId}

        <if test="commitUid != null and commitUid != 0">
            AND commit_uid = #{commitUid}
        </if>

        <if test="startTime !=null and endTime !=null">
            AND ctime BETWEEN #{startTime} AND #{endTime}
        </if>

        AND pack_id > 0
        AND valid = 1
        ORDER BY id DESC
        <if test="maxLimit !=null and maxLimit &gt; 0">
            limit #{maxLimit}
        </if>
    </select>

    <select id="queryEntityListWithLabel4Encryption" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wm_econtract_sign_batch
        where id &gt; #{lastId}
        ORDER BY id ASC
        LIMIT #{size}
    </select>

    <update id="updateOriginalRecordById" parameterType="java.lang.Long">
    update wm_econtract_sign_batch
    set batch_context = ""
    where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdateOriginalRecordByIds" parameterType="java.util.List">
        update wm_econtract_sign_batch
        set batch_context = ""
        where id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateStateAndNotifyInfo">
        update wm_econtract_sign_batch
        set batch_state = #{state},
        notify_info = #{notifyInfo},
        utime = UNIX_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <update id="batchUpdateStateByIdList">
        update wm_econtract_sign_batch
        set batch_state = #{state},
        utime = UNIX_TIMESTAMP()
        where id in
        <foreach collection="idList" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateStatusForSettleNew" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB">
        update wm_econtract_sign_batch
        set batch_state = #{batchState},
            utime = #{utime}
        where id = #{id} and valid = 1
    </update>

    <update id="updateStatusWithContext" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB">
        update wm_econtract_sign_batch
        set batch_state = #{batchState},
            batch_context = #{batchContext},
            utime = #{utime}
        where id = #{id} and valid = 1
    </update>

    <select id="queryOverTimeBatch" parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchOverTimeQueryThriftParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Origin_Column_List" />
        from wm_econtract_sign_batch
        where ctime BETWEEN #{startTime} AND #{endTime}
        <if test="lastId !=null and lastId &gt; 0">
            AND id > #{lastId}
        </if>
        AND valid = 1
        AND batch_state = 'in_processing'
        AND record_key != '' ORDER BY id ASC
        limit #{size}
    </select>

    <select id="queryOverTimeBatchWithoutPackIndexCtime"
            parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchOverTimeQueryThriftParam"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Origin_Column_List"/>
        from wm_econtract_sign_batch force index(idx_ctime)
        where ctime BETWEEN #{startTime} AND #{endTime}
        <if test="lastId !=null and lastId &gt; 0">
            AND id > #{lastId}
        </if>
        AND valid = 1
        AND batch_state = 'in_processing'
        AND pack_id = 0
        AND record_key != ''
        ORDER BY id ASC
        limit #{size}
    </select>

    <select id="queryOverTimeBatchWithoutPack"
            parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchOverTimeQueryThriftParam"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Origin_Column_List"/>
        from wm_econtract_sign_batch
        where ctime BETWEEN #{startTime} AND #{endTime}
        <if test="lastId !=null and lastId &gt; 0">
            AND id > #{lastId}
        </if>
        AND valid = 1
        AND batch_state = 'in_processing'
        AND pack_id = 0
        AND record_key != ''
        ORDER BY id ASC
        limit #{size}
    </select>

    <select id="queryOverTimeBatchWithPack" parameterType="com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchOverTimeQueryThriftParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Origin_Column_List" />
        from wm_econtract_sign_batch
        where ctime BETWEEN #{startTime} AND #{endTime}
        <if test="lastId !=null and lastId &gt; 0">
            AND id > #{lastId}
        </if>
        AND valid = 1
        AND batch_state = 'in_processing'
        AND pack_id > 0
        AND record_key != ''
        ORDER BY id ASC
        limit #{size}
    </select>

    <select id="querySignBatchListByPackIdAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Origin_Column_List" />
        from wm_econtract_sign_batch
        where pack_id = #{packId}
        and batch_state = #{batchStatus}
        and valid = 1
        order by id desc
    </select>

    <select id="querySignBatchListByPackId" resultMap="BaseResultMap">
        select
        <include refid="Base_Origin_Column_List" />
        from wm_econtract_sign_batch
        where pack_id = #{packId}
        and valid = 1
        order by id desc
    </select>

    <select id="queryBatchListByPackId" resultMap="BaseResultMap">
        select
        <include refid="Base_Origin_Column_List"/>
        from wm_econtract_sign_batch
        where pack_id = #{packId}
        and valid = 1
    </select>


    <select id="queryBatchListByPackIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Origin_Column_List"/>
        from wm_econtract_sign_batch
        where pack_id IN
        <foreach collection="packIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid = 1
        order by id desc
    </select>

    <select id="queryBatchListByPackIdMaster" resultMap="BaseResultMap">
        /*master*/select
        <include refid="Base_Origin_Column_List"/>
        from wm_econtract_sign_batch
        where pack_id = #{packId}
        and valid = 1
    </select>

    <select id="queryByRecordKeys" resultMap="BaseResultMap" parameterType="list">
        SELECT
        <include refid="Base_Origin_Column_List" />
        FROM
        wm_econtract_sign_batch
        WHERE record_key IN
        <foreach collection="recordKeys" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>

    <select id="queryByRecordKeysMaster" resultMap="BaseResultMap" parameterType="list">
        /*master*/SELECT
        <include refid="Base_Origin_Column_List" />
        FROM
        wm_econtract_sign_batch
        WHERE record_key IN
        <foreach collection="recordKeys" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>
    <select id="querySignBatchListByPackIdAndStatusMaster" resultMap="BaseResultMap">
        /*master*/select
        <include refid="Base_Origin_Column_List" />
        from wm_econtract_sign_batch
        where pack_id = #{packId}
        and batch_state = #{batchStatus}
        and valid = 1
        order by id desc
    </select>

</mapper>