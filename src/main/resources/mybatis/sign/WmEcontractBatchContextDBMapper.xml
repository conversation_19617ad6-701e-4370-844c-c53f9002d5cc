<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchContextDBMapper" >
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchContextDB" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="batch_id" property="batchId" jdbcType="BIGINT" />
        <result column="context" property="context" jdbcType="VARCHAR" />
        <result column="valid" property="valid" jdbcType="INTEGER" />
        <result column="ctime" property="ctime" jdbcType="INTEGER" />
        <result column="utime" property="utime" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, batch_id AS batchId, context, valid, ctime, utime
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchContextDB" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        wm_econtract_sign_batch_context
        (batch_id, context, valid, ctime, utime)
        VALUES
        (#{batchId}, #{context}, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
    </insert>

    <select id="queryByBatchId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        wm_econtract_sign_batch_context
        WHERE batch_id = #{batchId}
        AND valid = 1
    </select>

    <select id="queryByBatchIdMaster" resultMap="BaseResultMap">
        /*master*/SELECT
        <include refid="Base_Column_List" />
        FROM
        wm_econtract_sign_batch_context
        WHERE batch_id = #{batchId}
        AND valid = 1
    </select>

    <select id="queryByBatchIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        wm_econtract_sign_batch_context
        WHERE batch_id IN
        <foreach collection="batchIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>

    <select id="queryByBatchIdListMaster" resultMap="BaseResultMap">
        /*master*/SELECT
        <include refid="Base_Column_List" />
        FROM
        wm_econtract_sign_batch_context
        WHERE batch_id IN
        <foreach collection="batchIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>

    <select id="queryById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        wm_econtract_sign_batch_context
        WHERE id = #{id}
        AND valid = 1
    </select>

    <update id="deleteByBatchId">
        UPDATE wm_econtract_sign_batch_context
        SET valid = 0, utime = unix_timestamp()
        WHERE batch_id = #{batchId}
        AND valid = 1
    </update>

    <select id="queryEntityListWithLabel4Encryption" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wm_econtract_sign_batch_context
        where id &gt; #{lastId}
        ORDER BY id ASC
        LIMIT #{size}
    </select>

    <update id="updateOriginalRecordById" parameterType="java.lang.Long">
    update wm_econtract_sign_batch_context
    set context = ""
    where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdateOriginalRecordByIds" parameterType="java.util.List">
        update wm_econtract_sign_batch_context
        set context = ""
        where id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>