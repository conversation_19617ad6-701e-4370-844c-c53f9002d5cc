<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskAreaDBMapper" >
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskAreaDB">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="task_id" property="taskId" jdbcType="BIGINT" />
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT" />
        <result column="area_type" property="areaType" jdbcType="VARCHAR" />
        <result column="area" property="area" jdbcType="VARCHAR" />
        <result column="ctime" property="ctime" jdbcType="BIGINT" />
        <result column="utime" property="utime" jdbcType="BIGINT" />
        <result column="valid" property="valid" jdbcType="TINYINT" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, wm_poi_id, area_type, area, ctime, utime, valid
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskAreaDB">
    INSERT INTO wm_econtract_sign_task_area
           (task_id, wm_poi_id, area_type, area, ctime, utime, valid)
    VALUES
          (#{taskId,jdbcType=BIGINT}, #{wmPoiId,jdbcType=BIGINT},#{areaType,jdbcType=VARCHAR},
          #{area,jdbcType=VARCHAR}, unix_timestamp(), unix_timestamp(), 1)
  </insert>

    <insert id="batchInsert">
        INSERT INTO wm_econtract_sign_task_area
        (task_id, wm_poi_id, area_type, area, ctime, utime, valid)
        VALUES
        <foreach collection="signTaskAreaDBList" item="item" index="index" separator="," >
            (#{item.taskId}, #{item.wmPoiId}, #{item.areaType}, #{item.area}, unix_timestamp(), unix_timestamp(), 1)
        </foreach>
    </insert>

    <select id="selectByTaskIdAndWmPoiIds" resultMap="BaseResultMap">
        /*master*/
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_econtract_sign_task_area
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        AND wm_poi_id IN
        <foreach collection="wmPoiIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>