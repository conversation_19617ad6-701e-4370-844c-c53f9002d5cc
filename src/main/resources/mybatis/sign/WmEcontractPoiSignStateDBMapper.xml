<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmEcontractPoiSignStateDBMapper" >
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmEcontractPoiSignStateDB">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="customer_id" property="customerId" jdbcType="INTEGER" />
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT" />
        <result column="sign_state" property="signState" jdbcType="VARCHAR" />
        <result column="valid" property="valid" jdbcType="INTEGER" />
        <result column="ctime" property="ctime" jdbcType="INTEGER" />
        <result column="utime" property="utime" jdbcType="INTEGER" />
        <result column="version" property="version" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, customer_id AS customerId, wm_poi_id AS wmPoiId, sign_state AS signState, valid, ctime, utime, version
    </sql>

    <select id="getByCustomerIdAndWmPoiIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        wm_econtract_poi_sign_state
        WHERE customer_id = #{customerId}
        AND wm_poi_id IN
        <foreach collection="wmPoiIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>

    <select id="getByCustomerIdAndWmPoiIdListMaster" resultMap="BaseResultMap">
        /*master*/SELECT
        <include refid="Base_Column_List" />
        FROM
        wm_econtract_poi_sign_state
        WHERE customer_id = #{customerId}
        AND wm_poi_id IN
        <foreach collection="wmPoiIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>

    <insert id="batchInsert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractPoiSignStateDB">
        INSERT INTO
        wm_econtract_poi_sign_state
        (
        customer_id,
        wm_poi_id,
        sign_state,
        valid,
        ctime,
        utime,
        version
        )
        VALUES
        <foreach collection="stateDBList" index="index" item="item" separator=",">
            (
            #{item.customerId},
            #{item.wmPoiId},
            #{item.signState},
            1,
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            1
            )
        </foreach>
    </insert>

    <update id="updateByCustomerIdAndWmPoiId" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractPoiSignStateDB">
        UPDATE wm_econtract_poi_sign_state
        <set>
            <if test="signState != null">
                sign_state = #{signState},
            </if>
            utime = UNIX_TIMESTAMP(),
            version = version + 1
            WHERE customer_id = #{customerId}
            AND wm_poi_id = #{wmPoiId}
            AND valid = 1
        </set>
    </update>

    <update id="deleteByCustomerIdAndWmPoiIdList">
        UPDATE wm_econtract_poi_sign_state
        SET
        valid = 0,
        version = version + 1,
        utime = UNIX_TIMESTAMP()
        WHERE customer_id = #{customerId}
        AND wm_poi_id IN
        <foreach collection="wmPoiIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </update>

</mapper>