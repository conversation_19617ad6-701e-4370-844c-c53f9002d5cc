<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmEcontractBizInfoDBMapper" >
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmEcontractBizInfoDB" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <id column="biz_id" property="bizId" jdbcType="BIGINT" />
        <id column="biz_type" property="bizType" jdbcType="VARCHAR" />
        <id column="ctime" property="ctime" jdbcType="INTEGER" />
        <id column="utime" property="utime" jdbcType="INTEGER" />
        <id column="valid" property="valid" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, biz_id AS bizId, biz_type AS bizType, ctime, utime, valid
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractBizInfoDB" >
    insert into wm_econtract_biz_info (id, biz_id, biz_type, ctime, utime, valid)
    values (#{id,jdbcType=BIGINT}, #{bizId,jdbcType=BIGINT}, #{bizType,jdbcType=VARCHAR},
      #{ctime,jdbcType=INTEGER}, #{utime,jdbcType=INTEGER}, #{valid,jdbcType=INTEGER})
  </insert>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractBizInfoDB" >
        insert into wm_econtract_biz_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="bizId != null" >
                biz_id,
            </if>
            <if test="bizType != null" >
                biz_type,
            </if>
            ctime,
            utime,
            valid
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bizId != null" >
                #{bizId,jdbcType=BIGINT},
            </if>
            <if test="bizType != null" >
                #{bizType,jdbcType=VARCHAR},
            </if>
            unix_timestamp(),
            unix_timestamp(),
            1
        </trim>
    </insert>

    <update id="batchDeleteByBizIdAndBizType">
        update wm_econtract_biz_info
        set valid = 0,utime = UNIX_TIMESTAMP()
        where biz_id IN
        <foreach collection="bizIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and biz_type = #{bizType,jdbcType=VARCHAR}
        and valid = 1
    </update>

    <select id="selectByBizIdAndBizTypeRT" resultMap="BaseResultMap">
        /*master*/select <include refid="Base_Column_List" />
        from wm_econtract_biz_info
        where biz_id = #{bizId,jdbcType=BIGINT}
        and biz_type = #{bizType,jdbcType=VARCHAR}
        and valid = 1
    </select>

</mapper>