<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmEcontractDelayProcessDBMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmEcontractDelayProcessDB" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="biz_type" property="bizType" jdbcType="VARCHAR" />
    <result column="biz_id" property="bizId" jdbcType="VARCHAR" />
    <result column="ctime" property="ctime" jdbcType="INTEGER" />
    <result column="utime" property="utime" jdbcType="INTEGER" />
    <result column="to_process_time" property="toProcessTime" jdbcType="INTEGER" />
    <result column="count" property="count" jdbcType="INTEGER" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, biz_type, biz_id, ctime, utime, to_process_time, count, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wm_econtract_delay_process
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wm_econtract_delay_process
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractDelayProcessDB" >
    insert into wm_econtract_delay_process (id, biz_type, biz_id, 
      ctime, utime, to_process_time, 
      count, valid)
    values (#{id,jdbcType=BIGINT}, #{bizType,jdbcType=VARCHAR}, #{bizId,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=INTEGER}, #{utime,jdbcType=INTEGER}, #{toProcessTime,jdbcType=INTEGER}, 
      #{count,jdbcType=INTEGER}, #{valid,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractDelayProcessDB" >
    insert into wm_econtract_delay_process
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="bizType != null" >
        biz_type,
      </if>
      <if test="bizId != null" >
        biz_id,
      </if>
        ctime,
        utime,
      <if test="toProcessTime != null" >
        to_process_time,
      </if>
      <if test="count != null" >
        count,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizType != null" >
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null" >
        #{bizId,jdbcType=VARCHAR},
      </if>
      unix_timestamp(),
      unix_timestamp(),
      <if test="toProcessTime != null" >
        #{toProcessTime,jdbcType=INTEGER},
      </if>
      <if test="count != null" >
        #{count,jdbcType=INTEGER},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractDelayProcessDB" >
    update wm_econtract_delay_process
    <set >
      <if test="bizType != null" >
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null" >
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=INTEGER},
      </if>
        utime = unix_timestamp(),
      <if test="toProcessTime != null" >
        to_process_time = #{toProcessTime,jdbcType=INTEGER},
      </if>
      <if test="count != null" >
        count = #{count,jdbcType=INTEGER},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractDelayProcessDB" >
    update wm_econtract_delay_process
    set biz_type = #{bizType,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=INTEGER},
      utime = #{utime,jdbcType=INTEGER},
      to_process_time = #{toProcessTime,jdbcType=INTEGER},
      count = #{count,jdbcType=INTEGER},
      valid = #{valid,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryExpiredRecordWithLimit" parameterType="map"
    resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wm_econtract_delay_process
    where to_process_time &lt; #{expireTime}
    and biz_type = #{bizType}
    and valid = 1 limit #{limit}
  </select>

</mapper>