<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.mapper.WmTemplateContractSignMapper">

    <select id="queryByTemplateContractIdList"
            resultType="com.sankuai.meituan.waimai.bo.WmTemplateContractSignBo">
        select id,
               wm_templet_contract_id  as wmTempletContractId,
               sign_type               as signType,
               sign_id                 as signId,
               sign_name               as signName,
               sign_people             as signPeople,
               opuid,
               ctime,
               utime,
               valid
        from wm_templet_contract_sign
        where
        wm_templet_contract_id in
        <foreach collection="contractIdList" index="index" item="contractId" open="(" separator="," close=")">
            #{contractId}
        </foreach>
        and valid = 1;
    </select>
</mapper>