<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmEcontractSignPackDBMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="record_batch_id" property="recordBatchId" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
    <result column="ctime" property="ctime" jdbcType="BIGINT" />
    <result column="utime" property="utime" jdbcType="BIGINT" />
    <result column="customer_id" property="customerId" jdbcType="INTEGER" />
    <result column="task_num" property="taskNum" jdbcType="INTEGER" />
    <result column="sms_param_info" property="smsParamInfo" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, record_batch_id, status, type, valid, ctime, utime, customer_id, task_num, sms_param_info
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wm_econtract_sign_pack
    where id = #{id,jdbcType=BIGINT}
    and valid = 1
  </select>
  <select id="selectByPrimaryKeyMaster" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    /*master*/select
    <include refid="Base_Column_List" />
    from wm_econtract_sign_pack
    where id = #{id,jdbcType=BIGINT}
    and valid = 1
  </select>
  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB"
          useGeneratedKeys="true" keyProperty="id">
    insert into wm_econtract_sign_pack (id, record_batch_id, status, 
      type, valid, ctime, utime, customer_id, task_num, sms_param_info)
    values (#{id,jdbcType=BIGINT}, #{recordBatchId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, 
      #{type,jdbcType=TINYINT}, 1, unix_timestamp(),unix_timestamp(), #{customerId,jdbcType=INTEGER},
      #{taskNum,jdbcType=INTEGER}, #{smsParamInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB"
          useGeneratedKeys="true" keyProperty="id">
    insert into wm_econtract_sign_pack
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="recordBatchId != null" >
        record_batch_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="type != null" >
        type,
      </if>
        valid,
        ctime,
        utime,
      <if test="customerId != null" >
        customer_id,
      </if>
      <if test="taskNum != null" >
        task_num,
      </if>
      <if test="smsParamInfo != null" >
        sms_param_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="recordBatchId != null" >
        #{recordBatchId,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null" >
        #{type,jdbcType=TINYINT},
      </if>
      1,
      unix_timestamp(),
      unix_timestamp(),
      <if test="customerId != null" >
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="taskNum != null" >
        #{taskNum,jdbcType=INTEGER},
      </if>
      <if test="smsParamInfo != null" >
        #{smsParamInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB" >
    update wm_econtract_sign_pack
    <set >
      <if test="recordBatchId != null" >
        record_batch_id = #{recordBatchId,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=TINYINT},
      </if>
        utime = unix_timestamp(),
      <if test="customerId != null" >
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="taskNum != null" >
        task_num = #{taskNum,jdbcType=INTEGER},
      </if>
      <if test="smsParamInfo != null" >
        sms_param_info = #{smsParamInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB" >
    update wm_econtract_sign_pack
    set record_batch_id = #{recordBatchId,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      valid = #{valid,jdbcType=TINYINT},
      utime = unix_timestamp(),
      customer_id = #{customerId,jdbcType=INTEGER},
      task_num = #{taskNum,jdbcType=INTEGER},
      sms_param_info = #{smsParamInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateRecordBatchById">
    update wm_econtract_sign_pack
    set record_batch_id = #{recordBatchId},
    utime = unix_timestamp()
    where id = #{id}
  </update>
  <update id="updateStatusByRecordId">
    update wm_econtract_sign_pack
    set status = #{status},
    utime = unix_timestamp()
    where id = #{id}
  </update>
  <select id="queryPackIdByStatusList" resultType="java.lang.Long">
    select id
    from wm_econtract_sign_pack
    where id in
    <foreach collection="packIdList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and status in
    <foreach collection="statusList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and valid = 1
  </select>
    <select id="selectByPrimaryKeyBatch" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from wm_econtract_sign_pack
      where id in
      <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
        #{item}
      </foreach>
      and valid = 1
    </select>

  <select id="selectByRecordBatchId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from wm_econtract_sign_pack
    where record_batch_id = #{recordBatchId}
    and valid = 1
  </select>
</mapper>