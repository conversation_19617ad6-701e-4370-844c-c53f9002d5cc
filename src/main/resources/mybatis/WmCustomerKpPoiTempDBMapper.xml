<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerKpPoiTempDBMapper">

    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmCustomerKpPoiDB">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="kp_id" property="kpId" jdbcType="INTEGER"/>
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT"/>
        <result column="valid" property="valid" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" javaType="INTEGER"/>
        <result column="utime" property="utime" javaType="INTEGER"/>
    </resultMap>


    <insert id="batchInsert" parameterType="java.util.List">
        insert into wm_kp_poi_rel_temp (kp_id, wm_poi_id, valid, ctime, utime)
        values
        <foreach collection="wmCustomerKpPoiDBList" item="item" index="index" separator=",">
            (#{item.kpId}, #{item.wmPoiId}, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
        </foreach>
    </insert>

    <update id="deleteByKpIdList" parameterType="java.util.List">
        update wm_kp_poi_rel_temp set valid = 0, utime = UNIX_TIMESTAMP() where kp_id in
        <foreach collection="kpIdList" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        and valid = 1
    </update>

    <update id="deleteByKpIdAndWmPoiIdList">
        update wm_kp_poi_rel_temp set valid = 0, utime = UNIX_TIMESTAMP() where kp_id = #{kpId} and wm_poi_id in
        <foreach collection="wmPoiIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and valid = 1
    </update>

    <update id="deleteByKpIdListAndWmPoiIdList">
        update wm_kp_poi_rel_temp set valid = 0, utime = UNIX_TIMESTAMP() where kp_id in
        <foreach collection="kpIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and wm_poi_id in
        <foreach collection="wmPoiIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and valid = 1
    </update>

    <update id="updateByKpId" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerKpPoiDB">
        update wm_kp_poi_rel_temp set
        <if test="id != null">
            id = #{id},
        </if>
        <if test="kpId != null">
            kp_id = #{kpId},
        </if>
        <if test="wmPoiId != null">
            wm_poi_id = #{wmPoiId},
        </if>
        <if test="ctime != null">
            ctime = #{ctime},
        </if>
        <if test="valid != null">
            valid = #{valid},
        </if>
        utime = UNIX_TIMESTAMP()
        where id = #{id}
    </update>

    <update id="batchUpdateByKpId">
        update wm_kp_poi_rel_temp
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="kp_id = case" suffix="end,">
                <foreach collection="KpPoiDBList" item="item" index="index">
                    <if test="item.kpId != null">
                        when id = #{item.id}
                        then #{item.kpId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="wm_poi_id = case" suffix="end,">
                <foreach collection="KpPoiDBList" item="item" index="index">
                    <if test="item.wmPoiId != null">
                        when id = #{item.id}
                        then #{item.wmPoiId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ctime = case" suffix="end,">
                <foreach collection="KpPoiDBList" item="item" index="index">
                    <if test="item.ctime != null">
                        when id = #{item.id}
                        then #{item.ctime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="utime = case" suffix="end,">
                <foreach collection="KpPoiDBList" item="item" index="index">
                    <if test="item.utime != null">
                        when id = #{item.id}
                        then UNIX_TIMESTAMP()
                    </if>
                </foreach>
            </trim>
            <trim prefix="valid = case" suffix="end,">
                <foreach collection="KpPoiDBList" item="item" index="index">
                    <if test="item.valid != null">
                        when id = #{item.id}
                        then #{item.valid}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="KpPoiDBList" item="item" separator="or" index="index">
            id=#{item.id}
        </foreach>
    </update>

    <select id="selectByKpId" resultMap="BaseResultMap">
        select id, kp_id, wm_poi_id from wm_kp_poi_rel_temp where kp_id = #{kpId} and valid = 1;
    </select>

    <select id="selectRelPoiInfo" resultType="java.lang.Integer">
        select count(1) from wm_kp_poi_rel_temp where kp_id != #{kpId}
        and wm_poi_id in
        <foreach collection="wmPoiIdList" item="poiId" index="index"
                 open="(" close=")" separator=",">
            #{poiId}
        </foreach>
        and valid = 1
    </select>

    <select id="selectByKpIdList" resultMap="BaseResultMap">
        select id, kp_id, wm_poi_id from wm_kp_poi_rel_temp where kp_id in
        <foreach collection="kpIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and valid = 1;
    </select>

    <select id="selectWmPoiNumByKpId" resultType="java.lang.Integer">
        select count(1) from wm_kp_poi_rel_temp where kp_id = #{kpId} and valid = 1
    </select>
</mapper>