<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerKpPoiDBMapper">

    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmCustomerKpPoiDB">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="kp_id" property="kpId" jdbcType="INTEGER"/>
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT"/>
    </resultMap>


    <insert id="batchInsert" parameterType="java.util.List">
        insert into wm_kp_poi_rel (kp_id, wm_poi_id, valid, ctime, utime)
        values
        <foreach collection="wmCustomerKpPoiDBList" item="item" index="index" separator=",">
            (#{item.kpId}, #{item.wmPoiId}, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
        </foreach>
    </insert>

    <update id="deleteByKpId" parameterType="java.lang.Integer">
      update wm_kp_poi_rel set valid = 0, utime = UNIX_TIMESTAMP() where kp_id = #{kpId} and valid = 1
    </update>

    <update id="deleteByKpIdAndWmPoiIdList">
        update wm_kp_poi_rel set valid = 0, utime = UNIX_TIMESTAMP() where kp_id = #{kpId} and wm_poi_id in
        <foreach collection="wmPoiIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and valid = 1
    </update>

    <update id="deleteByKpIdListAndWmPoiIdList">
        update wm_kp_poi_rel set valid = 0, utime = UNIX_TIMESTAMP() where kp_id in
        <foreach collection="kpIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and wm_poi_id in
        <foreach collection="wmPoiIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and valid = 1
    </update>

    <select id="selectByKpId" resultMap="BaseResultMap">
      select id, kp_id, wm_poi_id from wm_kp_poi_rel where kp_id = #{kpId}  and valid = 1;
    </select>

    <select id="selectWmPoiNumByKpId" resultType="java.lang.Integer">
      select count(1) from wm_kp_poi_rel where kp_id = #{kpId} and valid = 1
    </select>

    <select id="selectByWmPoiIdList" resultMap="BaseResultMap">
        select id, kp_id, wm_poi_id from wm_kp_poi_rel where
        wm_poi_id in
        <foreach collection="wmPoiIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and valid = 1;
    </select>

    <select id="selectRelPoiInfo" resultType="java.lang.Integer">
        select count(1) from wm_kp_poi_rel where kp_id != #{kpId}
        and wm_poi_id in
        <foreach collection="wmPoiIdList" item="poiId" index="index"
                 open="(" close=")" separator=",">
            #{poiId}
        </foreach>
        and valid = 1
    </select>

    <update id="deleteByKpIdList" parameterType="java.util.List">
        update wm_kp_poi_rel set valid = 0, utime = UNIX_TIMESTAMP() where kp_id in
        <foreach collection="kpIdList" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        and valid = 1
    </update>
</mapper>