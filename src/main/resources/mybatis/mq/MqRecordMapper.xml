<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.mq.dao.MqRecordMapper" >
  <sql id="mqRecordField">
    id,
    customer_id AS customerId,
    topic AS topic,
    state AS state,
    content AS content,
    failed_times AS failedTimes,
    retry_time AS retryTime,
    ctime AS ctime,
    utime AS utime
  </sql>

  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.mq.domain.MqRecord" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO
    wm_customer_mq_record
      (
        customer_id,
        topic,
        state,
        content,
        retry_time,
        ctime,
        utime
      )
    VALUES
      (
        #{customerId},
        #{topic},
        'PENDING',
        #{content},
        #{retryTime},
        unix_timestamp(),
        unix_timestamp()
        )
  </insert>


  <update id ="updateSuccess">
    UPDATE wm_customer_mq_record
    <set >
      state = 'SUCCESS',
      retry_time = #{retryTime},
      utime = unix_timestamp()
    </set>
    WHERE
    id = #{id}
  </update>

  <update id ="updateFailed">
    UPDATE wm_customer_mq_record
    <set >
      failed_times = failed_times + 1,
      retry_time = #{retryTime},
      utime = unix_timestamp()
    </set>
    WHERE
    id = #{id}
  </update>


  <select id="batchSelectPendingIds" resultType="int">
    SELECT
    id
    FROM wm_customer_mq_record
    WHERE
    state = "PENDING"
    AND retry_time &lt; #{currentTime}
    AND failed_times &lt; 10
    limit 200
  </select>

  <select id="selectPendingRecordByIdList" resultType="com.sankuai.meituan.waimai.customer.mq.domain.MqRecord">
    SELECT
    <include refid="mqRecordField"/>
    FROM
    wm_customer_mq_record
    WHERE
    id IN
    <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
    AND state = "PENDING"
  </select>

</mapper>