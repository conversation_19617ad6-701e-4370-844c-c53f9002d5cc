<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--钱包-->
    <bean id="paymentAgentServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="timeout" value="10000"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="retryRequest" value="true"/>
        <!-- 请求失败后是否重试, 默认为 true -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <!-- 本地 appkey, MIX/OCTO 模式下必须配置 -->
        <property name="remoteAppkey" value="com.sankuai.pay.merchantproduct.mwallet"/>
        <property name="remoteServerPort" value="3405"/>
        <!-- 目标 Server Appkey, MIX/OCTO 模式下必须配置  -->
        <property name="serviceInterface" value="com.meituan.pay.mwallet.proxy.thrift.MwalletProxyService"/>
        <!-- service接口名, ZK/MIX 模式下不需配置 -->
        <property name="isImplFacebookService" value="true" />
    </bean>

    <bean id="merchantEntryInfoThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="timeout" value="2000"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.pay.merchantproduct.mwallet"/>
        <property name="remoteServerPort" value="3437"/>
        <property name="serviceInterface" value="com.meituan.pay.mwallet.proxy.thrift.merchantentry.MerchantEntryProxyService"/>
    </bean>

</beans>