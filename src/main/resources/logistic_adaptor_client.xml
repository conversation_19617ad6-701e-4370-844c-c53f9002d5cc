<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

<!--    com.sankuai.waimai.poilogistics-->
    <bean id="wmPoiLogisticsInfoForPDFThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsInfoForPDFThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="5000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poilogistics"/>
        <property name="remoteServerPort" value="8507"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmLogisticsFeeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.poilogistics.thrift.service.WmLogisticsFeeThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poilogistics"/>
        <property name="remoteServerPort" value="8434"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmPoiLogisticsPoiAgreementThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsPoiAgreementThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poilogistics"/>
        <property name="remoteServerPort" value="8623"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmPoiLogisticsSignThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsSignThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="50000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poilogistics"/>
        <property name="remoteServerPort" value="8530"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmPoiLogisticsFeeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsFeeThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poilogistics"/>
        <property name="remoteServerPort" value="8436"/>
        <property name="remoteUniProto" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmPoiLogisticsThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.poilogistics.thrift.service.WmLogisticsThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poilogistics"/>
        <property name="remoteServerPort" value="8430"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmPoiLogisticsProcessInfoThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsProcessInfoThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poilogistics"/>
        <property name="remoteServerPort" value="8451"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmPoiLogisticsVasFeeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsVasFeeThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poilogistics"/>
        <property name="remoteServerPort" value="8509"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <!-- 配送侧合同同步服务 -->
    <bean id="wmPoiLogisticsBmContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.WmPoiLogisticsBmContractThriftService"/> <!-- 指定service接口类 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.heron.poilogistics"/>
        <property name="remoteServerPort" value="8458"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <!--合同平台服务-->
    <bean id="wmHeronContractPlatformThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.contractplatform.WmContractPlatformThriftService"/> <!-- 指定service接口类 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.heron.poilogistics"/>
        <property name="remoteServerPort" value="8478"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <!--    com.sankuai.heron.poilogistics-->
    <bean id="wmPoiLogisticsAbilityThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.WmPoiLogisticsAbilityThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.heron.poilogistics"/>
        <property name="remoteServerPort" value="8431"/>
    </bean>

    <!-- 企客配送服务 -->
    <bean id="wmPoiLogisticsQikeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.WmPoiLogisticsQikeThriftService"/> <!-- 指定service接口类 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.heron.poilogistics"/>
        <property name="remoteServerPort" value="8459"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <!--    com.sankuai.heron.logistics.contract-->
    <bean id="wmContractPlatformThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.logistics.contract.client.service.contractplatform.WmContractPlatformThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="60000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.heron.logistics.contract"/>
        <property name="remoteServerPort" value="8451"/>
    </bean>

    <bean id="settlementPriceContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.waimai.innovative.supplychain.client.contract.iface.SettlementPriceContractThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.wmddatamining.innovative.supplychain"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true" />
    </bean>




</beans>