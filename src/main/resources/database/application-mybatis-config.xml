<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="false"/>
        <setting name="lazyLoadingEnabled" value="false"/>
        <setting name="aggressiveLazyLoading" value="false"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <setting name="defaultStatementTimeout" value="25000"/>
        <setting name="mapUnderscoreToCamelCase" value="false"/>
        <!-- 是否使用插入数据后自增主键的值，需要配合keyProperty使用 -->
        <setting name="useGeneratedKeys" value="true"/>
        <setting name="logImpl" value="LOG4J"/>
    </settings>

    <plugins>
        <plugin interceptor="com.sankuai.meituan.waimai.dbsecure.interceptor.SecureExecutorInterceptor"></plugin>
        <plugin interceptor="com.sankuai.meituan.waimai.dbsecure.interceptor.SecureResultSetInterceptor"></plugin>
    </plugins>
</configuration>
