package com.sankuai.meituan.waimai.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description: wm_econtract_sms_deal 发送短信记录表
 * @author: liuyunjie05
 * @create: 2023/12/15 15:36
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EcontractSmsDealBo {

    private Integer id;

    private Integer econtractRecordId;

    private Integer taskId;

    private String dealVersion;

    private String clientId;

    private String smsTemplateId;

    private String mobileList;

    private Date ctime;

    //签约链接失效时间（utime时间戳）
    private Long expireTime;

    private Byte valid;


}
