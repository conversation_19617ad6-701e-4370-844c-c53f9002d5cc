package com.sankuai.meituan.waimai.service.poi;

import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;


/**
 * <AUTHOR>
 * @project waimai
 * @date 7/13/16
 */
@Service
public class WmPoiQueryThriftAdapterService {
    private static final String PARA_COUNT_THRESHOLD="ParameterCountThreshold";//入参最大值
    private static final int DEFAULT_PARA_COUNT=300;//入参最大值

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    public WmPoiAggre getWmPoiAggreByWmPoiIdWithSpecificField(long wmPoiId, Set<String> needFields) throws TException, WmServerException {
        WmPoiAggre wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(wmPoiId, needFields);
        if (null != wmPoiAggre) {//防止接下来实现,有坑
            wmPoiAggre.setWm_poi_id(wmPoiId);
            wmPoiAggre.setId(wmPoiAggre.getWm_poi_id());
        }
        return wmPoiAggre;
    }

}
