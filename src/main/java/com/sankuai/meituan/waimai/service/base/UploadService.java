package com.sankuai.meituan.waimai.service.base;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.nibcus.inf.customer.client.dto.ContextDTO;
import com.sankuai.nibcus.inf.customer.client.enums.ErrorCodeEnum;
import com.sankuai.nibcus.inf.customer.client.enums.OperatorTypeEnum;
import com.sankuai.nibcus.inf.customer.client.request.FileUploadRequest;
import com.sankuai.nibcus.inf.customer.client.response.FileUploadResponse;
import com.sankuai.nibcus.inf.customer.client.service.CustomerThriftService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 文件上传
 */
@Service
public class UploadService {

    private static Logger log = LoggerFactory.getLogger(UploadService.class);

    @Resource
    private CustomerThriftService customerThriftService;


    /**
     * 图片信息上传到美团客户平台
     *
     * @param fileId
     * @param fileContent
     * @return
     */
    public boolean uploadImgToMtCustomer(String fileId, String fileContent) {
        log.info("uploadToMtCustomer begin, fileId={}", fileId);

        fileId = fileId.replaceFirst("/", "");

        FileUploadRequest fileUploadRequest = new FileUploadRequest();
        fileUploadRequest.setFileContent(fileContent);
        fileUploadRequest.setFileId(fileId);
        ContextDTO contextDTO = new ContextDTO();
        int userId = UserUtils.getUser().getId();
        contextDTO.setOperatorId((long) userId);
        contextDTO.setOperatorType(OperatorTypeEnum.EMPLOYEE);
        fileUploadRequest.setContext(contextDTO);

        //防止网络抖动等，失败后自动重试10次
        for (int cnt = 0; cnt < 3; cnt++) {
            try {

                FileUploadResponse fileUploadResponse = customerThriftService.uploadFile(fileUploadRequest);
                log.info("fileUploadResponse={}", JSONObject.toJSONString(fileUploadResponse));

                if (fileUploadResponse.getCode().getCode() == ErrorCodeEnum.SUCCESS.getCode()) {
                    log.info("uploadToMtCustomer success, fileId={}", fileId);
                    return true;
                }
            } catch (Exception e) {
                log.info("uploadToMtCustomer failed , e={}", e);
            }
        }
        log.error("uploadToMtCustomer failed ,fileId={}", fileId);
        return false;
    }
}
