package com.sankuai.meituan.waimai.service.brand;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.exception.RemoteServiceException;
import com.sankuai.meituan.waimai.service.WmUploadFileService;
import com.sankuai.meituan.waimai.thrift.util.ExcelExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;

@Service
@Slf4j
public class BrandExportService {

    @Autowired
    private WmUploadFileService wmUploadFileService;

    /**
     * 导出空excel方法
     *
     * @param excelUnit
     * @param os
     */
    public String exportEmptyExcel(Class excelUnit, ByteArrayOutputStream os, User user, String fileName) throws RemoteServiceException {
        try {
            log.info("exportServiceBrandListInfo 用户无导出数据权限");
            ExcelExportUtil.excelExport(Collections.emptyList(), excelUnit, os);
        } catch (Exception e) {
            log.warn("##exportServiceBrandListInfo, 导出空excel异常， e = ", e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("[exportEmptyExcel][IOException]", e);
                }
            }
        }
        return wmUploadFileService.uploadFile(os, user,fileName);
    }


    /**
     * 导出excel
     *
     * @param data
     * @param excelUnit
     * @param os
     * @param user
     * @return
     * @throws RemoteServiceException
     */
    public String exportExcel(Collection<?> data, Class excelUnit, ByteArrayOutputStream os, User user, String fileName)
            throws RemoteServiceException {
        if (CollectionUtils.isEmpty(data)) {
            throw new RemoteServiceException("无导出数据");
        }
        try {
            ExcelExportUtil.excelExport(data, excelUnit, os);
        } catch (Exception e) {
            log.warn("##exportServiceBrandListInfo, 导出excel异常， e = ", e);
        }
        return wmUploadFileService.uploadFile(os, user, fileName);
    }


}
