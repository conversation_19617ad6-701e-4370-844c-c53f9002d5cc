package com.sankuai.meituan.waimai.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.banma.aoi.thrift.vo.result.BmAoiAttrView;
import com.sankuai.meituan.banma.aoi.thrift.vo.result.TrafficType;
import com.sankuai.meituan.banma.aoi.thrift.vo.view.BmPoint;
import com.sankuai.meituan.waimai.domain.sc.WmScMetadataDeliveryAssignmentBO;
import com.sankuai.meituan.waimai.domain.sc.WmScMetadataDeliveryFollowUpBO;
import com.sankuai.meituan.waimai.domain.sc.WmScMetadataDeliveryGoalSetBO;
import com.sankuai.meituan.waimai.domain.sc.WmScPoint;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.OptTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolAoiModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolLifecycleEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditNodeStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryOpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScSchoolAoiDTO;
import com.sankuai.meituan.waimai.vo.sc.*;
import com.sankuai.meituan.waimai.vo.sc.canteenstall.*;
import com.sankuai.meituan.waimai.vo.sc.delivery.*;
import com.sankuai.meituan.waimai.vo.sc.delivery.WmSchoolDeliveryAuditProgressVO;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contact.ContactDto;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.partner.PartnerInfoDto;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.enums.ContactDepartmentEnum;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.enums.ContactPositionEnum;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.enums.ContactTypeEnum;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.req.contact.ContactReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.service.sc.auth.WmScAuthService.*;

/**
 * @program: scm
 * @description: 校园食堂类的转换
 * @author: jianghuimin02
 * @create: 2020-04-24 15:49
 **/
@Slf4j
@Service
public class WmScTransUtil {

    public static final int SCHOOL_ID_BEGIN = 10000;

    public static final int CANTEEN_ID_BEGIN = 100000;

    /**
     * 保卫处名称
     */
    private static final String SECURITY_DEPARTMENT_NAME = "保卫处";
    /**
     * 后勤处名称
     */
    private static final String LOGISTICS_DEPARTMENT_NAME = "后勤处";
    /**
     * 学生处名称
     */
    private static final String STUDENT_DEPARTMENT_NAME = "学生处";
    /**
     * 财务处名称
     */
    private static final String FINANCE_DEPARTMENT_NAME = "财务处";
    /**
     * 资产处名称
     */
    private static final String ASSET_DEPARTMENT_NAME = "资产处";

    /**
     * 食堂档口管理列表页码默认值
     */
    private static final int CANTEEN_STALL_MANAGE_LIST_PAGE_NUM = 1;
    /**
     * 食堂档口管理列表页面数量默认值
     */
    private static final int CANTEEN_STALL_MANAGE_LIST_PAGE_SIZE = 30;

    /**
     * 食堂档口绑定列表页码默认值
     */
    private static final int CANTEEN_STALL_BIND_LIST_PAGE_NUM = 1;
    /**
     * 食堂档口绑定列表页面数量默认值
     */
    private static final int CANTEEN_STALL_BIND_LIST_PAGE_SIZE = 30;

    public static List<SchoolListVo> schoolBoToVo(List<SchoolBo> schoolBos){
        List<SchoolListVo> schoolListVos = Lists.newArrayList();
        for (SchoolBo schoolBo :schoolBos ) {
            SchoolListVo schoolListVo = new SchoolListVo();
            BeanUtils.copyProperties(schoolBo, schoolListVo);
            schoolListVo.setWdcClueId(schoolBo.getWdcClueId() == null ? "" : schoolBo.getWdcClueId().toString());
            schoolListVos.add(schoolListVo);
        }
        return schoolListVos;
    }

    /**
     * 学校BO对象列表转为VO对象列表
     * @param schoolBos 学校BO对象列表
     * @return 学校VO对象列表
     */
    public static List<SchoolListDelSensitiveFiledVo> schoolBoToNoSensitiveVo(List<SchoolBo> schoolBos) {
        List<SchoolListDelSensitiveFiledVo> schoolListDelSensitiveFiledVos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(schoolBos)) {
            return schoolListDelSensitiveFiledVos;
        }

        for (SchoolBo schoolBo : schoolBos) {
            SchoolListDelSensitiveFiledVo schoolListDelSensitiveFiledVo = new SchoolListDelSensitiveFiledVo();
            BeanUtils.copyProperties(schoolBo, schoolListDelSensitiveFiledVo);
            schoolListDelSensitiveFiledVo.setStallTotal(schoolBo.getStallTotal());
            schoolListDelSensitiveFiledVo.setCoStallTotal(schoolBo.getCoStallTotal());
            schoolListDelSensitiveFiledVo.setWdcClueId(schoolBo.getWdcClueId() == null ? "" : schoolBo.getWdcClueId().toString());
            // 创建时间
            if (schoolBo.getCtime() > 0) {
                schoolListDelSensitiveFiledVo.setCtimeStr(DateUtil.seconds2TimeFormat(schoolBo.getCtime(), DateUtil.DefaultLongFormat));
            } else {
                schoolListDelSensitiveFiledVo.setCtimeStr("-");
            }
            // 食堂数量校验
            if (schoolListDelSensitiveFiledVo.getCanteenNum() < 0) {
                log.error("[schoolBoToNoSensitiveVo] 学校食堂数量<0不合法, 学校ID = {}, 食堂数量 = {}", schoolListDelSensitiveFiledVo.getSchoolId(), schoolListDelSensitiveFiledVo.getCanteenNum());
                schoolListDelSensitiveFiledVo.setCanteenNum(0);
            }
            // 设置学校标签
            schoolListDelSensitiveFiledVo.setSchoolLabels(WmScSchoolLabelVO.transWmScSchoolLabelDtoToVo(schoolBo.getSchoolLabels()));
            schoolListDelSensitiveFiledVos.add(schoolListDelSensitiveFiledVo);
        }
        return schoolListDelSensitiveFiledVos;
    }

    public static List<CanteenListVo> canteenBoToVo(List<CanteenBo> canteenBos) {
        List<CanteenListVo> canteenListVos = Lists.newArrayList();
        for (CanteenBo canteenBo : canteenBos) {
            CanteenListVo canteenListVo = new CanteenListVo();
            BeanUtils.copyProperties(canteenBo, canteenListVo);
            // 设置创建时间
            if (canteenBo.getCtime() > 0) {
                canteenListVo.setCtimeStr(DateUtil.seconds2TimeFormat(canteenBo.getCtime(), DateUtil.DefaultLongFormat));
            } else {
                canteenListVo.setCtimeStr("-");
            }
            // 食堂经理
            if (StringUtils.isBlank(canteenListVo.getManager())) {
                canteenListVo.setManager("-");
            }
            // 食堂经理联系电话
            if (StringUtils.isBlank(canteenListVo.getManagerPhone())) {
                canteenListVo.setManagerPhone("-");
            }
            // 食堂承包商客户
            if (StringUtils.isBlank(canteenListVo.getContractorName())) {
                canteenListVo.setContractorName("-");
            }
            // 供给分级
            if (StringUtils.isBlank(canteenListVo.getGradeDesc())) {
                canteenListVo.setGradeDesc("空");
            }
            canteenListVos.add(canteenListVo);
        }
        return canteenListVos;
    }

    public static List<ScLogListVo> scLogBoToVo(List<WmScOplogBo> scOplogBos){
        List<ScLogListVo> scLogListVos = Lists.newArrayList();
        for (WmScOplogBo wmScOplogBo : scOplogBos) {
            ScLogListVo scLogListVo = new ScLogListVo();
            BeanUtils.copyProperties(wmScOplogBo, scLogListVo);

            scLogListVo.setOpUname(wmScOplogBo.getOpUser());
            scLogListVo.setOpType(wmScOplogBo.getOpType());
            scLogListVo.setLog(wmScOplogBo.getContent());
            scLogListVo.setModuleType(wmScOplogBo.getModuleType());
            scLogListVo.setCtime(wmScOplogBo.getLogTimeInterval());
            setDesc(scLogListVo);
            scLogListVos.add(scLogListVo);
        }
        return scLogListVos;
    }

    public static void setDesc(ScLogListVo scLogListVo) {
        int opType = scLogListVo.getOpType();
        OptTypeEnum type = OptTypeEnum.getByType(opType);
        scLogListVo.setOpTypeDesc(type == null ? "" : type.getName());
    }

    public static List<WmCanPoiQueryVo> canteenPoiBoToQueryVo(List<WmCanPoiBo> canPoiBos) {
        if (CollectionUtils.isEmpty(canPoiBos)) {
            return Collections.emptyList();
        }
        List<WmCanPoiQueryVo> result = Lists.newArrayListWithCapacity(canPoiBos.size());
        for (WmCanPoiBo bo : canPoiBos) {
            if (StringUtils.isEmpty(bo.getWmPoiName())){
                continue;
            }
            WmCanPoiQueryVo vo = WmCanPoiQueryVo.builder()
                    .id(bo.getWmPoiId())
                    .name(bo.getWmPoiName())
                    .wmPoiStatus(bo.getWmPoiStatus())
                    .wmPoiStatusDes(bo.getWmPoiStatusDes())
                    .build();
            result.add(vo);
        }
        return result;
    }

    /**
     * 根据学校ID得到学校主键ID
     * @param schoolId 学校ID
     * @return 学校主键ID
     */
    public static Integer getSchoolPrimaryIdBySchoolId(Integer schoolId) {
        return schoolId - SCHOOL_ID_BEGIN;
    }

    /**
     * 根据学校主键ID得到学校ID
     * @param schoolPrimaryId 学校主键ID
     * @return 学校ID
     */
    public static Integer getSchoolIdBySchoolPrimaryId(Integer schoolPrimaryId) {
        return schoolPrimaryId + SCHOOL_ID_BEGIN;
    }

    /**
     * 根据食堂ID得到食堂主键ID
     * @param canteenId 食堂ID
     * @return 食堂主键ID
     */
    public static Integer getCanteenPrimaryIdByCanteenId(Integer canteenId) {
        return canteenId - CANTEEN_ID_BEGIN;
    }

    /**
     * 根据食堂主键ID得到食堂ID
     * @param canteenPrimaryId 食堂主键ID
     * @return 食堂ID
     */
    public static Integer getCanteenIdByCanteenPrimaryId(Integer canteenPrimaryId) {
        return canteenPrimaryId + CANTEEN_ID_BEGIN;
    }

    /**
     * 学校交付状态展示Tab转换
     * @param tabStatusDTO 展示TabDTO
     * @return 展示TabVO
     */
    public static WmSchoolDeliveryTabStatusVO transSchoolDeliveryTabStatusDTOToVO(WmSchoolDeliveryTabStatusDTO tabStatusDTO) {
        WmSchoolDeliveryTabStatusVO tabStatusVO = new WmSchoolDeliveryTabStatusVO();
        if (tabStatusDTO == null) {
            return tabStatusVO;
        }

        tabStatusVO.setDeliveryAssignmentTabStatus(tabStatusDTO.getDeliveryAssignmentTabStatus());
        tabStatusVO.setDeliveryGoalSetTabStatus(tabStatusDTO.getDeliveryGoalSetTabStatus());
        tabStatusVO.setDeliveryFollowUpTabStatus(tabStatusDTO.getDeliveryFollowUpTabStatus());
        tabStatusVO.setDeliveryFinishTabStatus(tabStatusDTO.getDeliveryFinishTabStatus());
        return tabStatusVO;
    }

    public static WmSchoolDeliveryAuditStreamVO transAuditStreamDTOToVO(WmSchoolDeliveryAuditStreamDTO auditStreamDTO) {
        WmSchoolDeliveryAuditStreamVO auditStreamVO = new WmSchoolDeliveryAuditStreamVO();
        if (auditStreamDTO == null) {
            return auditStreamVO;
        }

        auditStreamVO.setAuditTaskType(auditStreamDTO.getAuditTaskType());
        SchoolDeliveryAuditTaskTypeEnum auditTaskTypeEnum = SchoolDeliveryAuditTaskTypeEnum.getByType(auditStreamDTO.getAuditTaskType());
        auditStreamVO.setAuditTaskTypeDesc(auditTaskTypeEnum == null ? "" : auditTaskTypeEnum.getName());
        auditStreamVO.setAuditTaskSystemId(auditStreamDTO.getAuditTaskSystemId());
        auditStreamVO.setCurrentAuditStatus(auditStreamDTO.getCurrentAuditStatus());
        SchoolDeliveryAuditNodeStatusEnum nodeStatusEnum = SchoolDeliveryAuditNodeStatusEnum.of(auditStreamDTO.getCurrentAuditStatus());
        auditStreamVO.setCurrentAuditStatusDesc(nodeStatusEnum == null ? "" : nodeStatusEnum.getName());
        auditStreamVO.setCurrentNodeCode(auditStreamDTO.getCurrentNodeCode());
        auditStreamVO.setAuditProgressList(transAuditProgressDTOsToVOs(auditStreamDTO.getAuditProgressList()));

        return auditStreamVO;
    }

    public static List<WmSchoolDeliveryAuditProgressVO> transAuditProgressDTOsToVOs(List<WmSchoolDeliveryAuditProgressDTO> auditProgressDTOList) {
        List<WmSchoolDeliveryAuditProgressVO> auditProgressVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(auditProgressDTOList)) {
            return auditProgressVOList;
        }

        for (WmSchoolDeliveryAuditProgressDTO progressDTO : auditProgressDTOList) {
            WmSchoolDeliveryAuditProgressVO progressVO = new WmSchoolDeliveryAuditProgressVO();
            progressVO.setOrder(progressDTO.getOrder());
            progressVO.setAuditNode(progressDTO.getAuditNode());
            progressVO.setAuditNodeDesc(progressDTO.getAuditNodeDesc());
            progressVO.setAuditRemark(progressDTO.getAuditRemark());
            progressVO.setAuditResult(progressDTO.getAuditResult());
            progressVO.setAuditResultDesc(progressDTO.getAuditResultDesc());
            progressVO.setAuditTime(progressDTO.getAuditTime());
            progressVO.setAuditorUid(progressDTO.getAuditorUid());
            progressVO.setAuditorMis(progressDTO.getAuditorMis());
            progressVO.setAuditorName(progressDTO.getAuditorName());

            auditProgressVOList.add(progressVO);
        }

        return auditProgressVOList;
    }

    public static WmSchoolDeliveryHistoryVO transSchoolDeliveryHistoryDTOToVO(WmSchoolDeliveryHistoryDTO historyDTO, Map<Integer, WmEmploy> userMap) {
        WmSchoolDeliveryHistoryVO historyVO = new WmSchoolDeliveryHistoryVO();
        if (historyDTO == null) {
            return historyVO;
        }

        historyVO.setId(historyDTO.getId());
        historyVO.setDeliveryId(historyDTO.getDeliveryId());
        historyVO.setSchoolPrimaryId(historyDTO.getSchoolPrimaryId());
        historyVO.setDeliveryEndTime(historyDTO.getDeliveryEndTime());
        historyVO.setAgreementTimeStart(historyDTO.getAgreementTimeStart());
        historyVO.setAgreementTimeEnd(historyDTO.getAgreementTimeEnd());

        historyVO.setSchoolOwnerUid(historyDTO.getSchoolOwnerUid());
        WmEmploy schoolOwner = userMap.get(historyDTO.getSchoolOwnerUid());
        historyVO.setSchoolOwnerMis(schoolOwner == null ? "" : schoolOwner.getMisId());
        historyVO.setSchoolOwnerName(schoolOwner == null ? "" : schoolOwner.getName());

        historyVO.setAcmUid(historyDTO.getAcmUid().intValue());
        WmEmploy acm = userMap.get(historyDTO.getAcmUid().intValue());
        historyVO.setAcmMis(acm == null ? "" : acm.getMisId());
        historyVO.setAcmName(acm == null ? "" : acm.getName());

        historyVO.setCsmUid(historyDTO.getCsmUid().intValue());
        WmEmploy csm = userMap.get(historyDTO.getCsmUid().intValue());
        historyVO.setCsmMis(csm == null ? "" : csm.getMisId());
        historyVO.setCsmName(csm == null ? "" : csm.getName());

        historyVO.setAormUid(historyDTO.getAormUid().intValue());
        WmEmploy aorm = userMap.get(historyDTO.getAormUid().intValue());
        historyVO.setAormMis(aorm == null ? "" : aorm.getMisId());
        historyVO.setAormName(aorm == null ? "" : aorm.getName());
        return historyVO;
    }

    public static List<WmSchoolDeliveryContactUserVO> transContactUserDTOListToVOList(List<WmSchoolDeliveryContactUserDTO> contactUserDTOList) {
        List<WmSchoolDeliveryContactUserVO> contactUserVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(contactUserDTOList)) {
            return contactUserVOList;
        }

        for (WmSchoolDeliveryContactUserDTO contactUserDTO : contactUserDTOList) {
            WmSchoolDeliveryContactUserVO userVO = new WmSchoolDeliveryContactUserVO();
            userVO.setId(contactUserDTO.getId());
            userVO.setName(contactUserDTO.getName());
            userVO.setDuty(contactUserDTO.getDuty());
            ContactPositionEnum positionEnum = ContactPositionEnum.ofCodeNullable(contactUserDTO.getDuty());
            userVO.setDutyDesc(positionEnum == null ? "" : positionEnum.getMsg());
            userVO.setOtherDutyName(contactUserDTO.getOtherDutyName());
            userVO.setContactRole(contactUserDTO.getContactRole());
            userVO.setPosition(contactUserDTO.getPosition());
            userVO.setPhoneNum(encryptPhoneNumber(contactUserDTO.getPhoneNum()));
            userVO.setDepartment(contactUserDTO.getDepartment());
            ContactDepartmentEnum contactDepartmentEnum = ContactDepartmentEnum.ofCodeNullable(contactUserDTO.getDepartment());
            userVO.setDepartmentDesc(contactDepartmentEnum == null ? "-" : contactDepartmentEnum.getMsg());
            userVO.setOtherDepartmentName(contactUserDTO.getOtherDepartmentName());
            userVO.setBenefitAnalysis(contactUserDTO.getBenefitAnalysis());
            userVO.setManagementStyle(contactUserDTO.getManagementStyle());

            contactUserVOList.add(userVO);
        }
        return contactUserVOList;
    }

    public static String encryptPhoneNumber(String phoneNumber) {
        return phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    public static WmSchoolDeliveryContactUserVO transContactDtoToContactUserVO(ContactDto contactDto) {
        WmSchoolDeliveryContactUserVO contactUserVO = new WmSchoolDeliveryContactUserVO();
        if (contactDto == null) {
            return contactUserVO;
        }

        contactUserVO.setId(contactDto.getId());
        contactUserVO.setContactRole(contactDto.getRole());
        contactUserVO.setManagementStyle(contactDto.getManagementStyle());
        contactUserVO.setName(contactDto.getName());
        contactUserVO.setDepartment(contactDto.getDepartment());
        contactUserVO.setOtherDepartmentName(contactDto.getOtherDepartmentName());
        contactUserVO.setDuty(contactDto.getPosition());
        contactUserVO.setOtherDutyName(contactDto.getOtherPositionName());
        contactUserVO.setBenefitAnalysis(contactDto.getBenefitAnalysis());
        contactUserVO.setPhoneNum(contactDto.getPhone());
        contactUserVO.setPosition(contactDto.getStandpoint());
        return contactUserVO;
    }

    public static List<WmSchoolDeliveryContactUserVO> transContactDtoListToContactUserVOList(List<ContactDto> contactDtoList) {
        List<WmSchoolDeliveryContactUserVO> contactUserVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(contactDtoList)) {
            return contactUserVOList;
        }

        for (ContactDto contactDto : contactDtoList) {
            WmSchoolDeliveryContactUserVO contactUserVO = transContactDtoToContactUserVO(contactDto);
            contactUserVOList.add(contactUserVO);
        }
        return contactUserVOList;
    }

    public static WmSchoolDeliveryCompleteVO transDeliveryCompleteDTOToVO(WmSchoolDeliveryCompleteDTO completeDTO) {
        WmSchoolDeliveryCompleteVO completeVO = new WmSchoolDeliveryCompleteVO();
        if (completeDTO == null) {
            return completeVO;
        }

        completeVO.setDeliveryId(completeDTO.getDeliveryId());
        completeVO.setDeliveryInitiationTime(completeDTO.getInitiationTime());
        completeVO.setAgreementTimeStart(completeDTO.getAgreementTimeStart());
        completeVO.setAgreementTimeEnd(completeDTO.getAgreementTimeEnd());

        completeVO.setLifeCycle(completeDTO.getLifeCycle());
        SchoolLifecycleEnum lifecycleEnum = SchoolLifecycleEnum.getByType(completeDTO.getLifeCycle());
        completeVO.setLifeCycleDesc(lifecycleEnum == null ? "-" : lifecycleEnum.getName());
        completeVO.setDeliveryCompleteTime(completeDTO.getDeliveryCompleteTime());
        // 交付发起人
        completeVO.setDeliveryInitiatorUid(completeDTO.getInitiatorUid().intValue());
        completeVO.setDeliveryInitiatorName(completeDTO.getInitiatorName());
        completeVO.setDeliveryInitiatorMis(completeDTO.getInitiatorMis());
        // 交付负责人
        completeVO.setCsmUid(completeDTO.getCsmUid().intValue());
        completeVO.setCsmName(completeDTO.getCsmName());
        completeVO.setCsmMis(completeDTO.getCsmMis());
        return completeVO;
    }

    public static WmSchoolDeliveryStatusVO transSchoolDeliveryStatusDTOToVO(WmSchoolDeliveryStatusDTO deliveryStatusDTO) {
        WmSchoolDeliveryStatusVO statusVO = new WmSchoolDeliveryStatusVO();
        if (deliveryStatusDTO == null) {
            return statusVO;
        }
        statusVO.setEffective(deliveryStatusDTO.getEffectStatus());
        statusVO.setAuditStatus(deliveryStatusDTO.getAuditStatus());
        statusVO.setAuditTaskId(deliveryStatusDTO.getAuditTaskId());
        statusVO.setAuditSystemId(deliveryStatusDTO.getAuditSystemId());
        statusVO.setDeliveryId(deliveryStatusDTO.getDeliveryId());
        statusVO.setSchoolPrimaryId(deliveryStatusDTO.getSchoolPrimaryId());
        return statusVO;
    }

    public static WmSchoolDeliverySpecialDataVO transSchoolDeliveryAssignmentDTOToSpecialDataVO(WmSchoolDeliveryAssignmentDTO assignmentDTO) {
        WmSchoolDeliverySpecialDataVO specialDataVO = new WmSchoolDeliverySpecialDataVO();
        if (assignmentDTO == null) {
            return specialDataVO;
        }
        specialDataVO.setOtherMaterialDemand(StringUtils.isBlank(assignmentDTO.getOtherMaterialDemand()) ? null : assignmentDTO.getOtherMaterialDemand());
        specialDataVO.setNeedDisplayRackNum(assignmentDTO.getNeedDisplayRackNum() == -1 ? null : assignmentDTO.getNeedDisplayRackNum());
        specialDataVO.setNeedSunshadeNum(assignmentDTO.getNeedSunshadeNum() == -1 ? null : assignmentDTO.getNeedSunshadeNum());
        specialDataVO.setNeedTrashCanNum(assignmentDTO.getNeedTrashCanNum() == -1 ? null : assignmentDTO.getNeedTrashCanNum());
        specialDataVO.setNeedPrinterNum(assignmentDTO.getNeedPrinterNum() == -1 ? null : assignmentDTO.getNeedPrinterNum());
        return specialDataVO;
    }

    public static ContactReq transContactUserInfoToContactReq(WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetContactInfo contactInfo, Integer schoolPrimaryId) {
        ContactReq contactReq = new ContactReq();
        if (contactInfo == null) {
            return contactReq;
        }

        contactReq.setId(contactInfo.getId());
        contactReq.setName(contactInfo.getName());
        contactReq.setDepartment(contactInfo.getDepartment());
        contactReq.setOtherDepartmentName(contactInfo.getOtherDepartmentName());
        contactReq.setRole(contactInfo.getContactRole());
        contactReq.setPosition(contactInfo.getDuty());
        contactReq.setOtherPositionName(contactInfo.getOtherDutyName());
        contactReq.setManagementStyle(contactInfo.getManagementStyle());
        contactReq.setPhone(contactInfo.getPhoneNum());
        contactReq.setStandpoint(contactInfo.getPosition());
        contactReq.setContactType(ContactTypeEnum.SCHOOL.getCode());
        contactReq.setBenefitAnalysis(contactInfo.getBenefitAnalysis());
        contactReq.setRelatedSchoolId(getSchoolIdBySchoolPrimaryId(schoolPrimaryId).longValue());
        return contactReq;
    }

    public static List<WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetContactInfo> transContactDtoListToMetaDataContactList(List<ContactDto> contactDtoList) {
        List<WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetContactInfo> resList = new ArrayList<>();
        if (CollectionUtils.isEmpty(contactDtoList)) {
            return resList;
        }

        for (ContactDto contactDto : contactDtoList) {
            WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetContactInfo contactInfo = new WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetContactInfo();
            contactInfo.setId(contactDto.getId());
            contactInfo.setName(contactDto.getName());
            contactInfo.setDepartment(contactDto.getDepartment());
            contactInfo.setOtherDepartmentName(contactDto.getOtherDepartmentName());
            contactInfo.setManagementStyle(contactDto.getManagementStyle());
            contactInfo.setBenefitAnalysis(contactDto.getBenefitAnalysis());
            contactInfo.setPhoneNum(contactDto.getPhone());
            contactInfo.setContactRole(contactDto.getRole());
            contactInfo.setDuty(contactDto.getPosition());
            contactInfo.setOtherDutyName(contactDto.getOtherPositionName());
            contactInfo.setPosition(contactDto.getStandpoint());
            resList.add(contactInfo);
        }
        return resList;
    }

    public static WmSchoolDeliveryOpUserVO transDeliveryOpUserDTOToVO(WmSchoolDeliveryOpUserDTO opUserDTO) {
        WmSchoolDeliveryOpUserVO opUserVO = new WmSchoolDeliveryOpUserVO();
        if (opUserDTO == null) {
            return null;
        }
        opUserVO.setOpUserUid(opUserDTO.getOpUserUid());
        opUserVO.setOpTime(opUserDTO.getOpTime());
        opUserVO.setOpType(opUserDTO.getOpType());
        SchoolDeliveryOpTypeEnum opTypeEnum = SchoolDeliveryOpTypeEnum.getByType(opUserDTO.getOpType());
        opUserVO.setOpTypeDesc(opTypeEnum == null ? "" : opTypeEnum.getName());
        return opUserVO;
    }

    public static WmSchoolDeliveryAssignmentDTO transMetadataDeliveryAssignmentBOToDTO(WmScMetadataDeliveryAssignmentBO deliveryAssignmentBO, Integer schoolPrimaryId, WmSchoolDeliverySpecialDataVO specialDataVO) {
        WmSchoolDeliveryAssignmentDTO assignmentDTO = new WmSchoolDeliveryAssignmentDTO();
        if (deliveryAssignmentBO == null) {
            return assignmentDTO;
        }
        assignmentDTO.setSchoolPrimaryId(schoolPrimaryId);
        // 交付人员指定基本信息
        WmScMetadataDeliveryAssignmentBO.SchoolDeliveryAssignmentBasicInfo basicInfo = deliveryAssignmentBO.getSchoolDeliveryAssignmentBasicInfo();
        assignmentDTO.setInitiationTime(basicInfo.getInitiationTime() == null ? "" : basicInfo.getInitiationTime());
        assignmentDTO.setDeliveryId(basicInfo.getDeliveryId() == null ? 0 : basicInfo.getDeliveryId());
        assignmentDTO.setInitiatorUid(basicInfo.getInitiatorUid() == null ? 0 : basicInfo.getInitiatorUid());

        // 合作信息
        WmScMetadataDeliveryAssignmentBO.SchoolDeliveryAssignmentCooperationInfo cooperationInfo = deliveryAssignmentBO.getSchoolDeliveryAssignmentCooperationInfo();
        assignmentDTO.setKeyDecisionUserId(cooperationInfo.getKeyDecisionUserId() == null ? "" : cooperationInfo.getKeyDecisionUserId());
        assignmentDTO.setSignPartnerUserId(cooperationInfo.getSignPartnerUserId() == null ? "" : cooperationInfo.getSignPartnerUserId());
        assignmentDTO.setExtraSupport(cooperationInfo.getExtraSupport() == null ? "" : cooperationInfo.getExtraSupport());

        List<WmSchoolDepartmentIntensionDTO> departmentIntensionDTOList = new ArrayList<>();
        // 校方部门摸排
        WmScMetadataDeliveryAssignmentBO.SchoolDeliveryAssignmentDepartmentInfo departmentInfo = deliveryAssignmentBO.getSchoolDeliveryAssignmentDepartmentInfo();
        WmSchoolDepartmentIntensionDTO securityIntensionDTO = new WmSchoolDepartmentIntensionDTO();
        securityIntensionDTO.setSchoolPrimaryId(schoolPrimaryId);
        securityIntensionDTO.setDeliveryId(basicInfo.getDeliveryId());
        securityIntensionDTO.setDepartmentName(SECURITY_DEPARTMENT_NAME);
        securityIntensionDTO.setDepartmentIntension(departmentInfo.getSecurityOfficeIntension() == null ? 0 : departmentInfo.getSecurityOfficeIntension());

        WmSchoolDepartmentIntensionDTO logisticIntensionDTO = new WmSchoolDepartmentIntensionDTO();
        logisticIntensionDTO.setSchoolPrimaryId(schoolPrimaryId);
        logisticIntensionDTO.setDeliveryId(basicInfo.getDeliveryId());
        logisticIntensionDTO.setDepartmentName(LOGISTICS_DEPARTMENT_NAME);
        logisticIntensionDTO.setDepartmentIntension(departmentInfo.getLogisticsOfficeIntension() == null ? 0 : departmentInfo.getLogisticsOfficeIntension());

        WmSchoolDepartmentIntensionDTO studentIntensionDTO = new WmSchoolDepartmentIntensionDTO();
        studentIntensionDTO.setSchoolPrimaryId(schoolPrimaryId);
        studentIntensionDTO.setDeliveryId(basicInfo.getDeliveryId());
        studentIntensionDTO.setDepartmentName(STUDENT_DEPARTMENT_NAME);
        studentIntensionDTO.setDepartmentIntension(departmentInfo.getStudentOfficeIntension() == null ? 0 : departmentInfo.getStudentOfficeIntension());

        WmSchoolDepartmentIntensionDTO financeIntensionDTO = new WmSchoolDepartmentIntensionDTO();
        financeIntensionDTO.setSchoolPrimaryId(schoolPrimaryId);
        financeIntensionDTO.setDeliveryId(basicInfo.getDeliveryId());
        financeIntensionDTO.setDepartmentName(FINANCE_DEPARTMENT_NAME);
        financeIntensionDTO.setDepartmentIntension(departmentInfo.getFinanceOfficeIntension() == null ? 0 : departmentInfo.getFinanceOfficeIntension());

        WmSchoolDepartmentIntensionDTO assetIntensionDTO = new WmSchoolDepartmentIntensionDTO();
        assetIntensionDTO.setSchoolPrimaryId(schoolPrimaryId);
        assetIntensionDTO.setDeliveryId(basicInfo.getDeliveryId());
        assetIntensionDTO.setDepartmentName(ASSET_DEPARTMENT_NAME);
        assetIntensionDTO.setDepartmentIntension(departmentInfo.getAssetOfficeIntension() == null ? 0 : departmentInfo.getAssetOfficeIntension());

        departmentIntensionDTOList.add(securityIntensionDTO);
        departmentIntensionDTOList.add(logisticIntensionDTO);
        departmentIntensionDTOList.add(studentIntensionDTO);
        departmentIntensionDTOList.add(financeIntensionDTO);
        departmentIntensionDTOList.add(assetIntensionDTO);

        // 自定义校方部门摸排
        List<WmScMetadataDeliveryAssignmentBO.SchoolDeliveryAssignmentCustomDepartmentInfo> customDepartmentInfoList = deliveryAssignmentBO.getSchoolDeliveryAssignmentCustomDepartmentInfoList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(customDepartmentInfoList)) {
            for (WmScMetadataDeliveryAssignmentBO.SchoolDeliveryAssignmentCustomDepartmentInfo info : customDepartmentInfoList) {
                WmSchoolDepartmentIntensionDTO intensionDTO = new WmSchoolDepartmentIntensionDTO();
                intensionDTO.setSchoolPrimaryId(schoolPrimaryId);
                intensionDTO.setDeliveryId(basicInfo.getDeliveryId());
                intensionDTO.setDepartmentIntension(info.getCustomDepartmentIntension());
                intensionDTO.setDepartmentName(info.getCustomDepartmentName() == null ? "-custom" : info.getCustomDepartmentName() + "-custom");
                departmentIntensionDTOList.add(intensionDTO);
            }
        }

        assignmentDTO.setDepartmentIntensionDTOList(departmentIntensionDTOList);

        // 交付需求
        WmScMetadataDeliveryAssignmentBO.SchoolDeliveryAssignmentRequirementInfo requirementInfo = deliveryAssignmentBO.getSchoolDeliveryAssignmentRequirementInfo();
        assignmentDTO.setPublicStallNum(requirementInfo.getPublicStallNum() == null ? -1 : requirementInfo.getPublicStallNum());
        assignmentDTO.setDeliverableStallNum(requirementInfo.getDeliverableStallNum() == null ? -1 : requirementInfo.getDeliverableStallNum());
        assignmentDTO.setNeedFoodCabinet(requirementInfo.getNeedFoodCabinet() == null ? 0 : requirementInfo.getNeedFoodCabinet());
        assignmentDTO.setNeedFoodCabinetNum(requirementInfo.getNeedFoodCabinetNum() == null ? -1 : requirementInfo.getNeedFoodCabinetNum());
        assignmentDTO.setOtherCooperationDemand(requirementInfo.getOtherCooperationDemand() == null ? "" : requirementInfo.getOtherCooperationDemand());
        assignmentDTO.setMaterialDemand(requirementInfo.getMaterialDemand() == null ? "" : requirementInfo.getMaterialDemand());
        assignmentDTO.setNeedTrashCanNum(specialDataVO.getNeedTrashCanNum() == null ? -1 : specialDataVO.getNeedTrashCanNum());
        assignmentDTO.setNeedSunshadeNum(specialDataVO.getNeedSunshadeNum() == null ? -1 : specialDataVO.getNeedSunshadeNum());
        assignmentDTO.setNeedPrinterNum(specialDataVO.getNeedPrinterNum() == null ? -1 : specialDataVO.getNeedPrinterNum());
        assignmentDTO.setNeedDisplayRackNum(specialDataVO.getNeedDisplayRackNum() == null ? -1 : specialDataVO.getNeedDisplayRackNum());
        assignmentDTO.setOtherMaterialDemand(specialDataVO.getOtherMaterialDemand() == null ? "" : specialDataVO.getOtherMaterialDemand());

        // 分配交付负责人
        WmScMetadataDeliveryAssignmentBO.SchoolDeliveryAssignmentManagerInfo managerInfo = deliveryAssignmentBO.getSchoolDeliveryAssignmentManagerInfo();
        assignmentDTO.setCsmUid(managerInfo.getCsmUid() == null ? 0 : managerInfo.getCsmUid());
        assignmentDTO.setAormUid(managerInfo.getAormUid() == null ? 0 : managerInfo.getAormUid());
        assignmentDTO.setAcmUid(managerInfo.getAcmUid() == null ? 0 : managerInfo.getAcmUid());

        return assignmentDTO;
    }


    public static WmSchoolDeliveryFollowUpDTO transMetadataDeliveryFollowUpBOToDTO(WmScMetadataDeliveryFollowUpBO followUpBO, Integer schoolPrimaryId) {
        WmSchoolDeliveryFollowUpDTO followUpDTO = new WmSchoolDeliveryFollowUpDTO();
        if (followUpBO == null) {
            return followUpDTO;
        }

        followUpDTO.setSchoolPrimaryId(schoolPrimaryId);
        // 1-交付跟进基本信息
        WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpBasicInfo basicInfo = followUpBO.getSchoolDeliveryFollowUpBasicInfo();
        followUpDTO.setDeliveryId(basicInfo.getDeliveryId());
        followUpDTO.setInitiatorUid(basicInfo.getDeliveryInitiatorUid());
        followUpDTO.setInitiationTime(basicInfo.getDeliveryInitiationTime());
        followUpDTO.setCsmUid(basicInfo.getCsmUid());

        // 2-聚合配送模块
        WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpAggreDeliveryInfo aggreDeliveryInfo = followUpBO.getSchoolDeliveryFollowUpAggreDeliveryInfo();
        followUpDTO.setAcmUid(aggreDeliveryInfo.getAcmUid());
        // 2-1 校内站线上签约
        setFollowUpDTOInscOnlineSign(followUpDTO, aggreDeliveryInfo);
        // 2-2 校内站线下建站
        setFollowUpDTOInscOfflineBuild(followUpDTO, aggreDeliveryInfo);
        followUpDTO.setBuildOffCampusStation(aggreDeliveryInfo.getBuildOffCampusStation() == null ? 0 : aggreDeliveryInfo.getBuildOffCampusStation());
        // 2-3 校外站线上签约
        setFollowUpDTOOutscOnlineSign(followUpDTO, aggreDeliveryInfo);
        // 2-4 校外站线下建站
        setFollowUpDTOOutscOfflineBuild(followUpDTO, aggreDeliveryInfo);

        // 3-食堂档口模块
        WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpCanteenStallInfo canteenStallInfo = followUpBO.getSchoolDeliveryFollowUpCanteenStallInfo();
        followUpDTO.setAormUid(canteenStallInfo.getAormUid());
        // 3-1 首批档口上线
        setFollowUpDTOFirstStallOnline(followUpDTO, canteenStallInfo);

        // 4-运营效果监控
        WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpOperationMonitorInfo monitorInfo = followUpBO.getSchoolDeliveryFollowUpOperationMonitorInfo();
        followUpDTO.setOnlineOperationPlan(monitorInfo.getOnlineOperationPlan());
        // 4-1 在线档口渗透率监控
        setFollowUpDTOOnlinePenerateMonitor(followUpDTO, monitorInfo);
        // 4-2 配送指标监控-相对准时率
        setFollowUpDTOOntimeRateMonitor(followUpDTO, monitorInfo);
        // 4-3 配送指标监控-平均配送时长
        setFollowUpDTOAvgDeliveryTimeMonitor(followUpDTO, monitorInfo);
        // 4-4 订单指标监控-日均订单量
        setFollowUpDTODailyOrderMonitor(followUpDTO, monitorInfo);
        // 4-5 订单指标监控-人顿渗透率
        setFollowUpDTOMealPenerateMonitor(followUpDTO, monitorInfo);
        // 4-6 订单指标监控-日均店单产
        setFollowUpDTODailyYieldMonitor(followUpDTO, monitorInfo);

        return followUpDTO;
    }

    public static void setFollowUpDTOInscOnlineSign(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpAggreDeliveryInfo aggreDeliveryInfo) {
        followUpDTO.setInscOnlineSignExptime(aggreDeliveryInfo.getInscOnlineSignExptime() == null ? "" : aggreDeliveryInfo.getInscOnlineSignExptime());
        followUpDTO.setInscOnlineSignStatus(aggreDeliveryInfo.getInscOnlineSignStatus() == null ? 0 : aggreDeliveryInfo.getInscOnlineSignStatus());
        followUpDTO.setInscOnlineSignAchieve(aggreDeliveryInfo.getInscOnlineSignAchieve() == null ? 0 : aggreDeliveryInfo.getInscOnlineSignAchieve());
        followUpDTO.setInscOnlineSignFintime(aggreDeliveryInfo.getInscOnlineSignFintime() == null ? "" : aggreDeliveryInfo.getInscOnlineSignFintime());
        followUpDTO.setInscOnlineSignException(aggreDeliveryInfo.getInscOnlineSignException() == null ? "" : aggreDeliveryInfo.getInscOnlineSignException());
    }

    public static void setFollowUpDTOInscOfflineBuild(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpAggreDeliveryInfo aggreDeliveryInfo) {
        followUpDTO.setInscOfflineBuildExptime(aggreDeliveryInfo.getInscOfflineBuildExptime() == null ? "" : aggreDeliveryInfo.getInscOfflineBuildExptime());
        followUpDTO.setInscOfflineBuildStatus(aggreDeliveryInfo.getInscOfflineBuildStatus() == null ? 0 : aggreDeliveryInfo.getInscOfflineBuildStatus());
        followUpDTO.setInscOfflineBuildAchieve(aggreDeliveryInfo.getInscOfflineBuildAchieve() == null ? 0 : aggreDeliveryInfo.getInscOfflineBuildAchieve());
        followUpDTO.setInscOfflineBuildFintime(aggreDeliveryInfo.getInscOfflineBuildFintime() == null ? "" : aggreDeliveryInfo.getInscOfflineBuildFintime());
        followUpDTO.setInscOfflineBuildException(aggreDeliveryInfo.getInscOfflineBuildException() == null ? "" : aggreDeliveryInfo.getInscOfflineBuildException());
    }

    public static void setFollowUpDTOOutscOnlineSign(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpAggreDeliveryInfo aggreDeliveryInfo) {
        followUpDTO.setOutscOnlineSignExptime(aggreDeliveryInfo.getOutscOnlineSignExptime() == null ? "" : aggreDeliveryInfo.getOutscOnlineSignExptime());
        followUpDTO.setOutscOnlineSignStatus(aggreDeliveryInfo.getOutscOnlineSignStatus() == null ? 0 : aggreDeliveryInfo.getOutscOnlineSignStatus());
        followUpDTO.setOutscOnlineSignAchieve(aggreDeliveryInfo.getOutscOnlineSignAchieve() == null ? 0 : aggreDeliveryInfo.getOutscOnlineSignAchieve());
        followUpDTO.setOutscOnlineSignFintime(aggreDeliveryInfo.getOutscOnlineSignFintime() == null ? "" : aggreDeliveryInfo.getOutscOnlineSignFintime());
        followUpDTO.setOutscOnlineSignException(aggreDeliveryInfo.getOutscOnlineSignException() == null ? "" : aggreDeliveryInfo.getOutscOnlineSignException());
    }

    public static void setFollowUpDTOOutscOfflineBuild(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpAggreDeliveryInfo aggreDeliveryInfo) {
        followUpDTO.setOutscOfflineBuildExptime(aggreDeliveryInfo.getOutscOfflineBuildExptime() == null ? "" : aggreDeliveryInfo.getOutscOfflineBuildExptime());
        followUpDTO.setOutscOfflineBuildStatus(aggreDeliveryInfo.getOutscOfflineBuildStatus() == null ? 0 : aggreDeliveryInfo.getOutscOfflineBuildStatus());
        followUpDTO.setOutscOfflineBuildAchieve(aggreDeliveryInfo.getOutscOfflineBuildAchieve() == null ? 0 : aggreDeliveryInfo.getOutscOfflineBuildAchieve());
        followUpDTO.setOutscOfflineBuildFintime(aggreDeliveryInfo.getOutscOfflineBuildFintime() == null ? "" : aggreDeliveryInfo.getOutscOfflineBuildFintime());
        followUpDTO.setOutscOfflineBuildException(aggreDeliveryInfo.getOutscOfflineBuildException() == null ? "" : aggreDeliveryInfo.getOutscOfflineBuildException());
    }

    public static void setFollowUpDTOFirstStallOnline(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpCanteenStallInfo canteenStallInfo) {
        followUpDTO.setFirstStallOnlineExptime(canteenStallInfo.getFirstStallOnlineExptime() == null ? "" : canteenStallInfo.getFirstStallOnlineExptime());
        followUpDTO.setFirstStallOnlineStatus(canteenStallInfo.getFirstStallOnlineStatus() == null ? 0 : canteenStallInfo.getFirstStallOnlineStatus());
        followUpDTO.setFirstStallOnlineAchieve(canteenStallInfo.getFirstStallOnlineAchieve() == null ? 0 : canteenStallInfo.getFirstStallOnlineAchieve());
        followUpDTO.setFirstStallOnlineFintime(canteenStallInfo.getFirstStallOnlineFintime() == null ? "" : canteenStallInfo.getFirstStallOnlineFintime());
        followUpDTO.setFirstStallOnlineException(canteenStallInfo.getFirstStallOnlineException() == null ? "" : canteenStallInfo.getFirstStallOnlineException());
    }

    public static void setFollowUpDTOOnlinePenerateMonitor(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpOperationMonitorInfo monitorInfo) {
        followUpDTO.setLifeCycle(monitorInfo.getLifeCycle() == null ? 0 : monitorInfo.getLifeCycle());
        followUpDTO.setOnlinePenerateTarget(monitorInfo.getOnlinePenerateTarget() == null ? "" : monitorInfo.getOnlinePenerateTarget());
        followUpDTO.setOnlinePenerateTargetExptime(monitorInfo.getOnlinePenerateTargetExptime() == null ? "" : monitorInfo.getOnlinePenerateTargetExptime());
        followUpDTO.setOnlinePenerate(monitorInfo.getOnlinePenerate().equals("-") ? "" : monitorInfo.getOnlinePenerate());
        followUpDTO.setOnlinePenerateAchieve(monitorInfo.getOnlinePenerateAchieve() == null ? 0 : monitorInfo.getOnlinePenerateAchieve());
        followUpDTO.setOnlinePenerateStatus(monitorInfo.getOnlinePenerateStatus() == null ? 0 : monitorInfo.getOnlinePenerateStatus());
        followUpDTO.setOnlinePenerateException(monitorInfo.getOnlinePenerateException() == null ? "" : monitorInfo.getOnlinePenerateException());
    }

    public static void setFollowUpDTOOntimeRateMonitor(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpOperationMonitorInfo monitorInfo) {
        followUpDTO.setOntimeRateTarget(monitorInfo.getOntimeRateTarget() == null ? "" : monitorInfo.getOntimeRateTarget());
        followUpDTO.setOntimeRateTargetExptime(monitorInfo.getOntimeRateTargetExptime() == null ? "" : monitorInfo.getOntimeRateTargetExptime());
        followUpDTO.setOntimeRate(monitorInfo.getOntimeRate().equals("-") ? "" : monitorInfo.getOntimeRate());
        followUpDTO.setOntimeRateAchieve(monitorInfo.getOntimeRateAchieve() == null ? 0 : monitorInfo.getOntimeRateAchieve());
        followUpDTO.setOntimeRateStatus(monitorInfo.getOntimeRateStatus() == null ? 0 : monitorInfo.getOntimeRateStatus());
        followUpDTO.setOntimeRateException(monitorInfo.getOntimeRateException() == null ? "" : monitorInfo.getOntimeRateException());
    }

    public static void setFollowUpDTOAvgDeliveryTimeMonitor(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpOperationMonitorInfo monitorInfo) {
        followUpDTO.setAvgDeliveryTimeTarget(monitorInfo.getAvgDeliveryTimeTarget() == null ? "" : monitorInfo.getAvgDeliveryTimeTarget());
        followUpDTO.setAvgDeliveryTimeTargetExptime(monitorInfo.getAvgDeliveryTimeExptime() == null ? "" : monitorInfo.getAvgDeliveryTimeExptime());
        followUpDTO.setAvgDeliveryTime(monitorInfo.getAvgDeliveryTime().equals("-") ? "" : monitorInfo.getAvgDeliveryTime());
        followUpDTO.setAvgDeliveryTimeAchieve(monitorInfo.getAvgDeliveryTimeAchieve() == null ? 0 : monitorInfo.getAvgDeliveryTimeAchieve());
        followUpDTO.setAvgDeliveryTimeStatus(monitorInfo.getAvgDeliveryTimeStatus() == null ? 0 : monitorInfo.getAvgDeliveryTimeStatus());
        followUpDTO.setAvgDeliveryTimeException(monitorInfo.getAvgDeliveryTimeException() == null ? "" : monitorInfo.getAvgDeliveryTimeException());
    }

    public static void setFollowUpDTODailyOrderMonitor(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpOperationMonitorInfo monitorInfo) {
        followUpDTO.setDailyOrderTarget(monitorInfo.getDailyOrderTarget() == null ? "" : monitorInfo.getDailyOrderTarget());
        followUpDTO.setDailyOrderTargetExptime(monitorInfo.getDailyOrderExptime() == null ? "" : monitorInfo.getDailyOrderExptime());
        followUpDTO.setDailyOrder(monitorInfo.getDailyOrder().equals("-") ? "" : monitorInfo.getDailyOrder());
        followUpDTO.setDailyOrderAchieve(monitorInfo.getDailyOrderAchieve() == null ? 0 : monitorInfo.getDailyOrderAchieve());
        followUpDTO.setDailyOrderStatus(monitorInfo.getDailyOrderStatus() == null ? 0 : monitorInfo.getDailyOrderStatus());
        followUpDTO.setDailyOrderException(monitorInfo.getDailyOrderException() == null ? "" : monitorInfo.getDailyOrderException());
    }

    public static void setFollowUpDTOMealPenerateMonitor(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpOperationMonitorInfo monitorInfo) {
        followUpDTO.setMealPenerateTarget(monitorInfo.getOnlinePenerateTarget() == null ? "" : monitorInfo.getOnlinePenerateTarget());
        followUpDTO.setMealPenerateTargetExptime(monitorInfo.getOnlinePenerateTargetExptime() == null ? "" : monitorInfo.getOnlinePenerateTargetExptime());
        followUpDTO.setMealPenerate(monitorInfo.getMealPenerate().equals("-") ? "" : monitorInfo.getMealPenerate());
        followUpDTO.setMealPenerateAchieve(monitorInfo.getMealPenerateAchieve() == null ? 0 : monitorInfo.getMealPenerateAchieve());
        followUpDTO.setMealPenerateStatus(monitorInfo.getMealPenerateStatus() == null ? 0 : monitorInfo.getMealPenerateStatus());
        followUpDTO.setMealPenerateException(monitorInfo.getMealPenerateException() == null ? "" : monitorInfo.getMealPenerateException());
    }

    public static void setFollowUpDTODailyYieldMonitor(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScMetadataDeliveryFollowUpBO.SchoolDeliveryFollowUpOperationMonitorInfo monitorInfo) {
        followUpDTO.setDailyYieldTarget(monitorInfo.getDailyYieldTarget() == null ? "" : monitorInfo.getDailyYieldTarget());
        followUpDTO.setDailyYieldTargetExptime(monitorInfo.getDailyYieldExptime() == null ? "" : monitorInfo.getDailyYieldExptime());
        followUpDTO.setDailyYield(monitorInfo.getDailyYield().equals("-") ? "" : monitorInfo.getDailyYield());
        followUpDTO.setDailyYieldStatus(monitorInfo.getDailyYieldStatus() == null ? 0 : monitorInfo.getDailyYieldStatus());
        followUpDTO.setDailyYieldAchieve(monitorInfo.getDailyYieldAchieve() == null ? 0 : monitorInfo.getDailyYieldAchieve());
        followUpDTO.setDailyYieldException(monitorInfo.getDailyYieldException() == null ? "" : monitorInfo.getDailyYieldException());
    }


    public static WmSchoolDeliveryGoalSetDTO transMetadataDeliveryGoalSetBOToDTO(WmScMetadataDeliveryGoalSetBO goalSetBO, Integer schoolPrimaryId, List<String> contactUserIds) {
        WmSchoolDeliveryGoalSetDTO goalSetDTO = new WmSchoolDeliveryGoalSetDTO();
        goalSetDTO.setSchoolPrimaryId(schoolPrimaryId);
        if (goalSetBO == null) {
            return goalSetDTO;
        }

        // 基本信息
        WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetBasicInfo basicInfo = goalSetBO.getSchoolDeliveryGoalSetBasicInfo();
        goalSetDTO.setDeliveryId(basicInfo.getDeliveryId());

        // 普遍客户关系梳理
        goalSetDTO.setContactUserIds(StringUtils.join(contactUserIds, ","));

        // 配送上线模块
        WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetDeliveryOnlineInfo deliveryOnlineInfo = goalSetBO.getSchoolDeliveryGoalSetDeliveryOnlineInfo();
        goalSetDTO.setInscOnlineSignExptime(deliveryOnlineInfo.getInscOnlineSignExptime() == null ? "" : deliveryOnlineInfo.getInscOnlineSignExptime());
        goalSetDTO.setInscOfflineBuildExptime(deliveryOnlineInfo.getInscOfflineBuildExptime() == null ? "" : deliveryOnlineInfo.getInscOfflineBuildExptime());

        goalSetDTO.setBuildOffCampusStation(deliveryOnlineInfo.getBuildOffCampusStation() == null ? 0 : deliveryOnlineInfo.getBuildOffCampusStation());
        goalSetDTO.setOutscOnlineSignExptime(deliveryOnlineInfo.getOutscOnlineSignExptime() == null ? "" : deliveryOnlineInfo.getOutscOnlineSignExptime());
        goalSetDTO.setOutscOfflineBuildExptime(deliveryOnlineInfo.getOutscOfflineBuildExptime() == null ? "" : deliveryOnlineInfo.getOutscOfflineBuildExptime());

        goalSetDTO.setOntimeRateTarget(deliveryOnlineInfo.getOntimeRateTarget() == null ? "" : deliveryOnlineInfo.getOntimeRateTarget());
        goalSetDTO.setOntimeRateTargetExptime(deliveryOnlineInfo.getOntimeRateTargetExptime() == null ? "" : deliveryOnlineInfo.getOntimeRateTargetExptime());

        goalSetDTO.setAvgDeliveryTimeTarget(deliveryOnlineInfo.getAvgDeliveryTimeTarget() == null ? "" : deliveryOnlineInfo.getAvgDeliveryTimeTarget());
        goalSetDTO.setAvgDeliveryTimeTargetExptime(deliveryOnlineInfo.getAvgDeliveryTimeTargetExptime() == null ? "" : deliveryOnlineInfo.getAvgDeliveryTimeTargetExptime());

        // 档口上线模块
        WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetStallOnlineInfo stallOnlineInfo = goalSetBO.getSchoolDeliveryGoalSetStallOnlineInfo();
        goalSetDTO.setFirstOnlineStallNum(stallOnlineInfo.getFirstOnlineStallNum() == null ? -1 : stallOnlineInfo.getFirstOnlineStallNum());
        goalSetDTO.setFirstStallBulidExptime(stallOnlineInfo.getFirstStallBulidExptime() == null ? "" : stallOnlineInfo.getFirstStallBulidExptime());
        goalSetDTO.setFirstStallOnlineExptime(stallOnlineInfo.getFirstStallOnlineExptime() == null ? "" : stallOnlineInfo.getFirstStallOnlineExptime());

        // 运营指标模块
        WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetOperationIndexInfo indexInfo = goalSetBO.getSchoolDeliveryGoalSetOperationIndexInfo();
        goalSetDTO.setOnlinePenerateTarget(indexInfo.getOnlinePenerateTarget() == null ? "" : indexInfo.getOnlinePenerateTarget());
        goalSetDTO.setOnlinePenerateTargetExptime(indexInfo.getOnlinePenerateTargetExptime() == null ? "" : indexInfo.getOnlinePenerateTargetExptime());

        goalSetDTO.setDailyOrderTarget(indexInfo.getDailyOrderTarget() == null ? "" : indexInfo.getDailyOrderTarget());
        goalSetDTO.setDailyOrderTargetExptime(indexInfo.getDailyOrderTargetExptime() == null ? "" : indexInfo.getDailyOrderTargetExptime());

        goalSetDTO.setMealPenerateTarget(indexInfo.getMealPenerateTarget() == null ? "" : indexInfo.getMealPenerateTarget());
        goalSetDTO.setMealPenerateTargetExptime(indexInfo.getMealPenerateTargetExptime() == null ? "" : indexInfo.getMealPenerateTargetExptime());

        goalSetDTO.setDailyYieldTarget(indexInfo.getDailyYieldTarget() == null ? "" : indexInfo.getDailyYieldTarget());
        goalSetDTO.setDailyYieldTargetExptime(indexInfo.getDailyYieldTagetExptime() == null ? "" : indexInfo.getDailyYieldTagetExptime());
        return goalSetDTO;
    }

    public static WmSchoolDeliveryContactUserVO transPartnerInfoDTOToContactUserVO(PartnerInfoDto partnerInfoDto) {
        WmSchoolDeliveryContactUserVO userVO = new WmSchoolDeliveryContactUserVO();
        if (partnerInfoDto == null) {
            return userVO;
        }
        userVO.setId(partnerInfoDto.getPartnerId());
        userVO.setName(partnerInfoDto.getPartnerName());
        userVO.setPhoneNum(partnerInfoDto.getMobile());
        return userVO;
    }

    /**
     * 学校合作平台VO转SaveDTO
     *
     * @param wmScSchoolCoPlatformVO wmScSchoolCoPlatformVO
     * @return WmScSchoolCoPlatformSaveDTO
     */
    public static WmScSchoolCoPlatformSaveDTO transWmScCoPlatformVOToSaveDTO(WmScSchoolCoPlatformVO wmScSchoolCoPlatformVO,
                                                                             WmEmploy currentEmploy) throws WmSchCantException {
        WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO = new WmScSchoolCoPlatformSaveDTO();
        if (wmScSchoolCoPlatformVO == null) {
            return wmScSchoolCoPlatformSaveDTO;
        }

        wmScSchoolCoPlatformSaveDTO.setId(wmScSchoolCoPlatformVO.getId());
        wmScSchoolCoPlatformSaveDTO.setSchoolPrimaryId(wmScSchoolCoPlatformVO.getSchoolPrimaryId());
        wmScSchoolCoPlatformSaveDTO.setCooperationPlatform(wmScSchoolCoPlatformVO.getCooperationPlatform());
        wmScSchoolCoPlatformSaveDTO.setPlatformName(wmScSchoolCoPlatformVO.getPlatformName());
        wmScSchoolCoPlatformSaveDTO.setSchoolInPoiOrderCount(wmScSchoolCoPlatformVO.getSchoolInPoiOrderCount());
        wmScSchoolCoPlatformSaveDTO.setSchoolOutPoiOrderCount(wmScSchoolCoPlatformVO.getSchoolOutPoiOrderCount());
        wmScSchoolCoPlatformSaveDTO.setSchoolInOnlinePoiCount(wmScSchoolCoPlatformVO.getSchoolInOnlinePoiCount());
        wmScSchoolCoPlatformSaveDTO.setSchoolOutOnlinePoiCount(wmScSchoolCoPlatformVO.getSchoolOutOnlinePoiCount());
        wmScSchoolCoPlatformSaveDTO.setDeliveryFeeTypeInfo(wmScSchoolCoPlatformVO.getDeliveryFeeTypeInfo());
        wmScSchoolCoPlatformSaveDTO.setCompareCooperationPlatform(wmScSchoolCoPlatformVO.getCompareCooperationPlatform());
        wmScSchoolCoPlatformSaveDTO.setCompareCooperationPlatformInfo(wmScSchoolCoPlatformVO.getCompareCooperationPlatformInfo());
        wmScSchoolCoPlatformSaveDTO.setPlatformAllowToSchool(wmScSchoolCoPlatformVO.getPlatformAllowToSchool());
        wmScSchoolCoPlatformSaveDTO.setSupportFoodUpstairs(wmScSchoolCoPlatformVO.getSupportFoodUpstairs());
        wmScSchoolCoPlatformSaveDTO.setSupportFoodUpstairsInfo(wmScSchoolCoPlatformVO.getSupportFoodUpstairsInfo());
        wmScSchoolCoPlatformSaveDTO.setFoodUpstairsFee(wmScSchoolCoPlatformVO.getFoodUpstairsFee());
        wmScSchoolCoPlatformSaveDTO.setFoodUpstairsReason(wmScSchoolCoPlatformVO.getFoodUpstairsReason());
        wmScSchoolCoPlatformSaveDTO.setFoodUpstairsReasonInfo(wmScSchoolCoPlatformVO.getFoodUpstairsReasonInfo());
        wmScSchoolCoPlatformSaveDTO.setPlatformEstablishTime(wmScSchoolCoPlatformVO.getPlatformEstablishTime());
        wmScSchoolCoPlatformSaveDTO.setPlatformEstablishAdvantage(wmScSchoolCoPlatformVO.getPlatformEstablishAdvantage());
        wmScSchoolCoPlatformSaveDTO.setAdvantageAddedServiceInfo(wmScSchoolCoPlatformVO.getAdvantageAddedServiceInfo());
        wmScSchoolCoPlatformSaveDTO.setAdvantageGoodExperienceInfo(wmScSchoolCoPlatformVO.getAdvantageGoodExperienceInfo());
        wmScSchoolCoPlatformSaveDTO.setAdvantageAttractionInfo(wmScSchoolCoPlatformVO.getAdvantageAttractionInfo());
        wmScSchoolCoPlatformSaveDTO.setAdvantagePropagandaInfo(wmScSchoolCoPlatformVO.getAdvantagePropagandaInfo());
        wmScSchoolCoPlatformSaveDTO.setAdvantageExtraInfo(wmScSchoolCoPlatformVO.getAdvantageExtraInfo());
        wmScSchoolCoPlatformSaveDTO.setSupplyDistribution(wmScSchoolCoPlatformVO.getSupplyDistribution());
        wmScSchoolCoPlatformSaveDTO.setMonopolyForm(wmScSchoolCoPlatformVO.getMonopolyForm());
        wmScSchoolCoPlatformSaveDTO.setExtraInfo(wmScSchoolCoPlatformVO.getExtraInfo());
        wmScSchoolCoPlatformSaveDTO.setUserId(currentEmploy.getUid());
        wmScSchoolCoPlatformSaveDTO.setUserName(currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
        buildDeliveryFeeTypeVOToSaveDTO(wmScSchoolCoPlatformVO, wmScSchoolCoPlatformSaveDTO);
        buildWmScSchoolCoPlatformMarketSaveDTOList(wmScSchoolCoPlatformVO, wmScSchoolCoPlatformSaveDTO);

        return wmScSchoolCoPlatformSaveDTO;
    }

    /**
     * 转换合作平台的收费方式相关信息
     *
     * @param wmScSchoolCoPlatformVO      vo参数
     * @param wmScSchoolCoPlatformSaveDTO dto参数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private static void buildDeliveryFeeTypeVOToSaveDTO(WmScSchoolCoPlatformVO wmScSchoolCoPlatformVO, WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO) throws WmSchCantException {
        List<WmScSchoolCoPlatformFeeTypeSaveDTO> wmScSchoolCoPlatformFeeTypeSaveDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformVO.getDeliveryFeeType())) {
            return;
        }
        for (WmScSchoolCoPlatformFeeTypeVO wmScSchoolCoPlatformFeeTypeVO : wmScSchoolCoPlatformVO.getDeliveryFeeType()) {
            if (wmScSchoolCoPlatformFeeTypeVO == null) {
                continue;
            }
            WmScSchoolCoPlatformFeeTypeSaveDTO wmScSchoolCoPlatformFeeTypeSaveDTO = new WmScSchoolCoPlatformFeeTypeSaveDTO();
            wmScSchoolCoPlatformFeeTypeSaveDTO.setOption(wmScSchoolCoPlatformFeeTypeVO.getOption());
            wmScSchoolCoPlatformFeeTypeSaveDTO.setMode(wmScSchoolCoPlatformFeeTypeVO.getMode());
            wmScSchoolCoPlatformFeeTypeSaveDTO.setValue(wmScSchoolCoPlatformFeeTypeVO.getValue());
            wmScSchoolCoPlatformFeeTypeSaveDTOList.add(wmScSchoolCoPlatformFeeTypeSaveDTO);
        }
        wmScSchoolCoPlatformSaveDTO.setDeliveryFeeType(wmScSchoolCoPlatformFeeTypeSaveDTOList);
    }

    /**
     * 转换合作平台营销活动相关信息
     *
     * @param wmScSchoolCoPlatformVO      vo参数
     * @param wmScSchoolCoPlatformSaveDTO dto参数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private static void buildWmScSchoolCoPlatformMarketSaveDTOList(WmScSchoolCoPlatformVO wmScSchoolCoPlatformVO, WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO) throws WmSchCantException {
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformVO.getSchoolPlatMarketList())) {
            return;
        }
        List<WmScSchoolCoPlatformMarketSaveDTO> wmScSchoolCoPlatformMarketSaveDTOList = new ArrayList<>();
        for (WmScSchoolCoPlatformMarketVO wmScSchoolCoPlatformMarketVO : wmScSchoolCoPlatformVO.getSchoolPlatMarketList()) {
            if (wmScSchoolCoPlatformMarketVO == null) {
                continue;
            }
            WmScSchoolCoPlatformMarketSaveDTO wmScSchoolCoPlatformMarketSaveDTO = new WmScSchoolCoPlatformMarketSaveDTO();
            wmScSchoolCoPlatformMarketSaveDTO.setId(wmScSchoolCoPlatformMarketVO.getId());
            wmScSchoolCoPlatformMarketSaveDTO.setPlatformPrimaryId(wmScSchoolCoPlatformVO.getId());
            wmScSchoolCoPlatformMarketSaveDTO.setPlatformMarketingActivity(wmScSchoolCoPlatformMarketVO.getPlatformMarketingActivity());
            wmScSchoolCoPlatformMarketSaveDTO.setPlatformMarketingActivityInfo(wmScSchoolCoPlatformMarketVO.getPlatformMarketingActivityInfo());
            wmScSchoolCoPlatformMarketSaveDTO.setActivityRuleDescription(wmScSchoolCoPlatformMarketVO.getActivityRuleDescription());
            wmScSchoolCoPlatformMarketSaveDTO.setActivityPic(StringUtils.join(wmScSchoolCoPlatformMarketVO.getActivityPic(), ","));
            wmScSchoolCoPlatformMarketSaveDTO.setActivityCostSharingType(wmScSchoolCoPlatformMarketVO.getActivityCostSharingType());
            wmScSchoolCoPlatformMarketSaveDTO.setActivityCostSharingPoiMin(wmScSchoolCoPlatformMarketVO.getActivityCostSharingPoiMin());
            wmScSchoolCoPlatformMarketSaveDTO.setActivityCostSharingPoiMax(wmScSchoolCoPlatformMarketVO.getActivityCostSharingPoiMax());
            wmScSchoolCoPlatformMarketSaveDTO.setActivityCostSharingPlatformMin(wmScSchoolCoPlatformMarketVO.getActivityCostSharingPlatformMin());
            wmScSchoolCoPlatformMarketSaveDTO.setActivityCostSharingPlatformMax(wmScSchoolCoPlatformMarketVO.getActivityCostSharingPlatformMax());
            wmScSchoolCoPlatformMarketSaveDTO.setUserId(wmScSchoolCoPlatformSaveDTO.getUserId());
            wmScSchoolCoPlatformMarketSaveDTO.setUserName(wmScSchoolCoPlatformSaveDTO.getUserName());
            wmScSchoolCoPlatformMarketSaveDTOList.add(wmScSchoolCoPlatformMarketSaveDTO);
        }
        wmScSchoolCoPlatformSaveDTO.setWmScSchoolCoPlatformMarketSaveDTOList(wmScSchoolCoPlatformMarketSaveDTOList);
    }

    /**
     * 学校合作平台列表DTO转VO
     *
     * @param wmScSchoolCoPlatformDTOList wmScSchoolCoPlatformDTOList
     * @return WmScSchoolCoPlatformSaveDTO
     */
    public static List<WmScSchoolCoPlatformVO> transSchoolCoPlatformDTOListToVOList(List<WmScSchoolCoPlatformDTO> wmScSchoolCoPlatformDTOList) throws WmSchCantException {
        List<WmScSchoolCoPlatformVO> wmScSchoolCoPlatformVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformDTOList)) {
            return wmScSchoolCoPlatformVOList;
        }

        for (WmScSchoolCoPlatformDTO wmScSchoolCoPlatformDTO : wmScSchoolCoPlatformDTOList) {
            if (wmScSchoolCoPlatformDTO == null) {
                continue;
            }
            WmScSchoolCoPlatformVO wmScSchoolCoPlatformVO = new WmScSchoolCoPlatformVO();
            wmScSchoolCoPlatformVO.setId(wmScSchoolCoPlatformDTO.getId());
            wmScSchoolCoPlatformVO.setSchoolPrimaryId(wmScSchoolCoPlatformDTO.getSchoolPrimaryId());
            wmScSchoolCoPlatformVO.setCooperationPlatform(wmScSchoolCoPlatformDTO.getCooperationPlatform());
            wmScSchoolCoPlatformVO.setPlatformName(wmScSchoolCoPlatformDTO.getPlatformName());
            wmScSchoolCoPlatformVO.setSchoolInPoiOrderCount(wmScSchoolCoPlatformDTO.getSchoolInPoiOrderCount());
            wmScSchoolCoPlatformVO.setSchoolOutPoiOrderCount(wmScSchoolCoPlatformDTO.getSchoolOutPoiOrderCount());
            wmScSchoolCoPlatformVO.setSchoolInOnlinePoiCount(wmScSchoolCoPlatformDTO.getSchoolInOnlinePoiCount());
            wmScSchoolCoPlatformVO.setSchoolOutOnlinePoiCount(wmScSchoolCoPlatformDTO.getSchoolOutOnlinePoiCount());
            wmScSchoolCoPlatformVO.setDeliveryFeeTypeInfo(wmScSchoolCoPlatformDTO.getDeliveryFeeTypeInfo());
            wmScSchoolCoPlatformVO.setCompareCooperationPlatform(wmScSchoolCoPlatformDTO.getCompareCooperationPlatform());
            wmScSchoolCoPlatformVO.setCompareCooperationPlatformInfo(wmScSchoolCoPlatformDTO.getCompareCooperationPlatformInfo());
            wmScSchoolCoPlatformVO.setPlatformAllowToSchool(wmScSchoolCoPlatformDTO.getPlatformAllowToSchool());
            wmScSchoolCoPlatformVO.setSupportFoodUpstairs(wmScSchoolCoPlatformDTO.getSupportFoodUpstairs());
            wmScSchoolCoPlatformVO.setSupportFoodUpstairsInfo(wmScSchoolCoPlatformDTO.getSupportFoodUpstairsInfo());
            wmScSchoolCoPlatformVO.setFoodUpstairsFee(wmScSchoolCoPlatformDTO.getFoodUpstairsFee());
            wmScSchoolCoPlatformVO.setFoodUpstairsReason(wmScSchoolCoPlatformDTO.getFoodUpstairsReason());
            wmScSchoolCoPlatformVO.setFoodUpstairsReasonInfo(wmScSchoolCoPlatformDTO.getFoodUpstairsReasonInfo());
            wmScSchoolCoPlatformVO.setPlatformEstablishTime(wmScSchoolCoPlatformDTO.getPlatformEstablishTime());
            wmScSchoolCoPlatformVO.setPlatformEstablishAdvantage(wmScSchoolCoPlatformDTO.getPlatformEstablishAdvantage());
            wmScSchoolCoPlatformVO.setAdvantageAddedServiceInfo(wmScSchoolCoPlatformDTO.getAdvantageAddedServiceInfo());
            wmScSchoolCoPlatformVO.setAdvantageGoodExperienceInfo(wmScSchoolCoPlatformDTO.getAdvantageGoodExperienceInfo());
            wmScSchoolCoPlatformVO.setAdvantageAttractionInfo(wmScSchoolCoPlatformDTO.getAdvantageAttractionInfo());
            wmScSchoolCoPlatformVO.setAdvantagePropagandaInfo(wmScSchoolCoPlatformDTO.getAdvantagePropagandaInfo());
            wmScSchoolCoPlatformVO.setAdvantageExtraInfo(wmScSchoolCoPlatformDTO.getAdvantageExtraInfo());
            wmScSchoolCoPlatformVO.setSupplyDistribution(wmScSchoolCoPlatformDTO.getSupplyDistribution());
            wmScSchoolCoPlatformVO.setMonopolyForm(wmScSchoolCoPlatformDTO.getMonopolyForm());
            wmScSchoolCoPlatformVO.setExtraInfo(wmScSchoolCoPlatformDTO.getExtraInfo());
            buildDeliveryFeeTypeDTOToVO(wmScSchoolCoPlatformDTO, wmScSchoolCoPlatformVO);
            buildWmScSchoolCoPlatformMarketVOList(wmScSchoolCoPlatformDTO, wmScSchoolCoPlatformVO);

            wmScSchoolCoPlatformVOList.add(wmScSchoolCoPlatformVO);
        }
        return wmScSchoolCoPlatformVOList;
    }

    /**
     * 转换合作平台的收费方式相关信息
     *
     * @param wmScSchoolCoPlatformDTO dto参数
     * @param wmScSchoolCoPlatformVO  vo参数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private static void buildDeliveryFeeTypeDTOToVO(WmScSchoolCoPlatformDTO wmScSchoolCoPlatformDTO, WmScSchoolCoPlatformVO wmScSchoolCoPlatformVO) throws WmSchCantException {
        if (wmScSchoolCoPlatformDTO == null || org.apache.commons.collections4.CollectionUtils.isEmpty(wmScSchoolCoPlatformDTO.getWmScSchoolCoPlatformFeeTypeDTOList())) {
            wmScSchoolCoPlatformVO.setDeliveryFeeType(new ArrayList<WmScSchoolCoPlatformFeeTypeVO>());
        }
        List<WmScSchoolCoPlatformFeeTypeVO> wmScSchoolCoPlatformFeeTypeVOList = new ArrayList<>();
        for (WmScSchoolCoPlatformFeeTypeDTO wmScSchoolCoPlatformFeeTypeDTO : wmScSchoolCoPlatformDTO.getWmScSchoolCoPlatformFeeTypeDTOList()) {
            if (wmScSchoolCoPlatformFeeTypeDTO == null) {
                continue;
            }
            WmScSchoolCoPlatformFeeTypeVO wmScSchoolCoPlatformFeeTypeVO = new WmScSchoolCoPlatformFeeTypeVO();
            wmScSchoolCoPlatformFeeTypeVO.setOption(wmScSchoolCoPlatformFeeTypeDTO.getOption());
            wmScSchoolCoPlatformFeeTypeVO.setMode(wmScSchoolCoPlatformFeeTypeDTO.getMode());
            wmScSchoolCoPlatformFeeTypeVO.setValue(wmScSchoolCoPlatformFeeTypeDTO.getValue());
            wmScSchoolCoPlatformFeeTypeVOList.add(wmScSchoolCoPlatformFeeTypeVO);
        }
        wmScSchoolCoPlatformVO.setDeliveryFeeType(wmScSchoolCoPlatformFeeTypeVOList);
    }

    /**
     * 转换合作平台营销活动相关信息
     *
     * @param wmScSchoolCoPlatformDTO dto参数
     * @param wmScSchoolCoPlatformVO  vo参数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private static void buildWmScSchoolCoPlatformMarketVOList(WmScSchoolCoPlatformDTO wmScSchoolCoPlatformDTO, WmScSchoolCoPlatformVO wmScSchoolCoPlatformVO) throws WmSchCantException {
        if (wmScSchoolCoPlatformDTO == null) {
            return;
        }
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformDTO.getWmScSchoolCoPlatformMarketDTOList())) {
            wmScSchoolCoPlatformVO.setSchoolPlatMarketList(new ArrayList<WmScSchoolCoPlatformMarketVO>());
        }
        List<WmScSchoolCoPlatformMarketVO> wmScSchoolCoPlatformMarketVOList = new ArrayList<>();
        for (WmScSchoolCoPlatformMarketDTO wmScSchoolCoPlatformMarketDTO : wmScSchoolCoPlatformDTO.getWmScSchoolCoPlatformMarketDTOList()) {
            WmScSchoolCoPlatformMarketVO wmScSchoolCoPlatformMarketVO = new WmScSchoolCoPlatformMarketVO();
            wmScSchoolCoPlatformMarketVO.setId(wmScSchoolCoPlatformMarketDTO.getId());
            wmScSchoolCoPlatformMarketVO.setPlatformPrimaryId(wmScSchoolCoPlatformMarketDTO.getPlatformPrimaryId());
            wmScSchoolCoPlatformMarketVO.setPlatformMarketingActivity(wmScSchoolCoPlatformMarketDTO.getPlatformMarketingActivity());
            wmScSchoolCoPlatformMarketVO.setPlatformMarketingActivityInfo(wmScSchoolCoPlatformMarketDTO.getPlatformMarketingActivityInfo());
            wmScSchoolCoPlatformMarketVO.setActivityRuleDescription(wmScSchoolCoPlatformMarketDTO.getActivityRuleDescription());
            wmScSchoolCoPlatformMarketVO.setActivityCostSharingType(wmScSchoolCoPlatformMarketDTO.getActivityCostSharingType());
            wmScSchoolCoPlatformMarketVO.setActivityCostSharingPoiMin(wmScSchoolCoPlatformMarketDTO.getActivityCostSharingPoiMin());
            wmScSchoolCoPlatformMarketVO.setActivityCostSharingPoiMax(wmScSchoolCoPlatformMarketDTO.getActivityCostSharingPoiMax());
            wmScSchoolCoPlatformMarketVO.setActivityCostSharingPlatformMin(wmScSchoolCoPlatformMarketDTO.getActivityCostSharingPlatformMin());
            wmScSchoolCoPlatformMarketVO.setActivityCostSharingPlatformMax(wmScSchoolCoPlatformMarketDTO.getActivityCostSharingPlatformMax());
            // 活动截图
            List<String> activityPicList = Arrays.asList(wmScSchoolCoPlatformMarketDTO.getActivityPic().split(","));
            List<String> newActivityPicList = new ArrayList<>();
            for (String pic : activityPicList) {
                if (StringUtils.isNotBlank(pic)) {
                    newActivityPicList.add(pic);
                }
            }
            wmScSchoolCoPlatformMarketVO.setActivityPic(newActivityPicList);
            wmScSchoolCoPlatformMarketVOList.add(wmScSchoolCoPlatformMarketVO);
        }
        wmScSchoolCoPlatformVO.setSchoolPlatMarketList(wmScSchoolCoPlatformMarketVOList);
    }

    /**
     * 学校履约管控信息VO转SaveDTO
     *
     * @param vo wmScSchoolPerformanceVO
     * @return WmScSchoolPerformanceSaveDTO
     */
    public static WmScSchoolPerformanceSaveDTO transSchoolPerformanceVOToSaveDTO(WmScSchoolPerformanceVO vo, WmEmploy currentEmploy) {
        WmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO = new WmScSchoolPerformanceSaveDTO();
        if (vo == null) {
            return wmScSchoolPerformanceSaveDTO;
        }

        wmScSchoolPerformanceSaveDTO.setId(vo.getId());
        wmScSchoolPerformanceSaveDTO.setSchoolPrimaryId(vo.getSchoolPrimaryId());
        wmScSchoolPerformanceSaveDTO.setUserId(currentEmploy.getUid());
        wmScSchoolPerformanceSaveDTO.setUserName(currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
        List<WmScSchoolPerformanceUnitSaveDTO> wmScSchoolPerformanceUnitSaveDTOList = new ArrayList<>();
        for (WmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO : vo.getSchoolPerformanceUnitList()) {
            WmScSchoolPerformanceUnitSaveDTO wmScSchoolPerformanceUnitSaveDTO = new WmScSchoolPerformanceUnitSaveDTO();
            wmScSchoolPerformanceUnitSaveDTO.setId(wmScSchoolPerformanceUnitVO.getId());
            wmScSchoolPerformanceUnitSaveDTO.setPerformancePrimaryId(vo.getId());
            wmScSchoolPerformanceUnitSaveDTO.setSchoolInDeliveryType(wmScSchoolPerformanceUnitVO.getSchoolInDeliveryType());
            wmScSchoolPerformanceUnitSaveDTO.setPoiSelfDeliveryType(wmScSchoolPerformanceUnitVO.getPoiSelfDeliveryType());
            wmScSchoolPerformanceUnitSaveDTO.setPoiSelfDeliveryInfo(wmScSchoolPerformanceUnitVO.getPoiSelfDeliveryInfo());
            wmScSchoolPerformanceUnitSaveDTO.setDeliverySpecificLocation(wmScSchoolPerformanceUnitVO.getDeliverySpecificLocation());
            wmScSchoolPerformanceUnitSaveDTO.setDeliveryNotGateReason(wmScSchoolPerformanceUnitVO.getDeliveryNotGateReason());
            wmScSchoolPerformanceUnitSaveDTO.setDeliveryNotGateInfo(wmScSchoolPerformanceUnitVO.getDeliveryNotGateInfo());
            wmScSchoolPerformanceUnitSaveDTO.setDeliveryNotEnterReason(wmScSchoolPerformanceUnitVO.getDeliveryNotEnterReason());
            wmScSchoolPerformanceUnitSaveDTO.setDeliveryNotEnterInfo(wmScSchoolPerformanceUnitVO.getDeliveryNotEnterInfo());
            wmScSchoolPerformanceUnitSaveDTO.setDeliveryEnterReason(wmScSchoolPerformanceUnitVO.getDeliveryEnterReason());
            wmScSchoolPerformanceUnitSaveDTO.setDeliveryEnterInfo(wmScSchoolPerformanceUnitVO.getDeliveryEnterInfo());
            wmScSchoolPerformanceUnitSaveDTO.setDeliveryUpstairsReason(wmScSchoolPerformanceUnitVO.getDeliveryUpstairsReason());
            wmScSchoolPerformanceUnitSaveDTO.setDeliveryUpstairsInfo(wmScSchoolPerformanceUnitVO.getDeliveryUpstairsInfo());
            wmScSchoolPerformanceUnitSaveDTO.setUserId(currentEmploy.getUid());
            wmScSchoolPerformanceUnitSaveDTO.setUserName(currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
            wmScSchoolPerformanceUnitSaveDTOList.add(wmScSchoolPerformanceUnitSaveDTO);
        }
        wmScSchoolPerformanceSaveDTO.setWmScSchoolPerformanceUnitSaveDTOList(wmScSchoolPerformanceUnitSaveDTOList);
        wmScSchoolPerformanceSaveDTO.setSchoolAllowDelivery(vo.getSchoolAllowDelivery());
        wmScSchoolPerformanceSaveDTO.setSchoolAllowDeliveryInfo(vo.getSchoolAllowDeliveryInfo());


        return wmScSchoolPerformanceSaveDTO;
    }

    /**
     * 学校履约管控信息DTO转VO
     *
     * @param dto wmScSchoolPerformanceDTO
     * @return WmScSchoolPerformanceVO
     */
    public static WmScSchoolPerformanceVO transSchoolPerformanceDTOToVO(WmScSchoolPerformanceDTO dto) {
        WmScSchoolPerformanceVO wmScSchoolPerformanceVO = new WmScSchoolPerformanceVO();
        if (dto == null) {
            return wmScSchoolPerformanceVO;
        }
        wmScSchoolPerformanceVO.setId(dto.getId());
        wmScSchoolPerformanceVO.setSchoolPrimaryId(dto.getSchoolPrimaryId());
        wmScSchoolPerformanceVO.setSchoolAllowDelivery(dto.getSchoolAllowDelivery());
        wmScSchoolPerformanceVO.setSchoolAllowDeliveryInfo(dto.getSchoolAllowDeliveryInfo());

        List<WmScSchoolPerformanceUnitDTO> wmScSchoolPerformanceUnitDTOList = dto.getWmScSchoolPerformanceUnitDTOList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmScSchoolPerformanceUnitDTOList)) {
            List<WmScSchoolPerformanceUnitVO> wmScSchoolPerformanceUnitVOList = new ArrayList<>();
            for (WmScSchoolPerformanceUnitDTO wmScSchoolPerformanceUnitDTO : dto.getWmScSchoolPerformanceUnitDTOList()) {
                WmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO = new WmScSchoolPerformanceUnitVO();
                wmScSchoolPerformanceUnitVO.setId(wmScSchoolPerformanceUnitDTO.getId());
                wmScSchoolPerformanceUnitVO.setPerformancePrimaryId(wmScSchoolPerformanceUnitDTO.getPerformancePrimaryId());
                wmScSchoolPerformanceUnitVO.setSchoolInDeliveryType(wmScSchoolPerformanceUnitDTO.getSchoolInDeliveryType());
                wmScSchoolPerformanceUnitVO.setPoiSelfDeliveryType(wmScSchoolPerformanceUnitDTO.getPoiSelfDeliveryType());
                wmScSchoolPerformanceUnitVO.setPoiSelfDeliveryInfo(wmScSchoolPerformanceUnitDTO.getPoiSelfDeliveryInfo());
                wmScSchoolPerformanceUnitVO.setDeliverySpecificLocation(wmScSchoolPerformanceUnitDTO.getDeliverySpecificLocation());
                wmScSchoolPerformanceUnitVO.setDeliveryNotGateReason(wmScSchoolPerformanceUnitDTO.getDeliveryNotGateReason());
                wmScSchoolPerformanceUnitVO.setDeliveryNotGateInfo(wmScSchoolPerformanceUnitDTO.getDeliveryNotGateInfo());
                wmScSchoolPerformanceUnitVO.setDeliveryNotEnterReason(wmScSchoolPerformanceUnitDTO.getDeliveryNotEnterReason());
                wmScSchoolPerformanceUnitVO.setDeliveryNotEnterInfo(wmScSchoolPerformanceUnitDTO.getDeliveryNotEnterInfo());
                wmScSchoolPerformanceUnitVO.setDeliveryEnterReason(wmScSchoolPerformanceUnitDTO.getDeliveryEnterReason());
                wmScSchoolPerformanceUnitVO.setDeliveryEnterInfo(wmScSchoolPerformanceUnitDTO.getDeliveryEnterInfo());
                wmScSchoolPerformanceUnitVO.setDeliveryUpstairsReason(wmScSchoolPerformanceUnitDTO.getDeliveryUpstairsReason());
                wmScSchoolPerformanceUnitVO.setDeliveryUpstairsInfo(wmScSchoolPerformanceUnitDTO.getDeliveryUpstairsInfo());
                wmScSchoolPerformanceUnitVOList.add(wmScSchoolPerformanceUnitVO);
            }
            wmScSchoolPerformanceVO.setSchoolPerformanceUnitList(wmScSchoolPerformanceUnitVOList);
        } else {
            List<WmScSchoolPerformanceUnitVO> wmScSchoolPerformanceUnitVOList = new ArrayList<>();
            wmScSchoolPerformanceVO.setSchoolPerformanceUnitList(wmScSchoolPerformanceUnitVOList);
        }

        return wmScSchoolPerformanceVO;
    }

    /**
     * 批量创建线索上传Excel VO列表转DTO列表
     * @param clueExcelVOList VO列表
     * @return DTO列表
     */
    public static List<WmCanteenStallClueExcelDTO> transCanteenStallClueExcelVOsToDTOs(List<WmCanteenStallClueExcelVO> clueExcelVOList) {
        List<WmCanteenStallClueExcelDTO> clueExcelDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(clueExcelVOList)) {
            return clueExcelDTOList;
        }

        for (WmCanteenStallClueExcelVO clueExcelVO : clueExcelVOList) {
            WmCanteenStallClueExcelDTO clueExcelDTO = new WmCanteenStallClueExcelDTO();
            clueExcelDTO.setLineNum(clueExcelVO.getLineNum());
            clueExcelDTO.setCluePoiAddress(clueExcelVO.getCluePoiAddress());
            clueExcelDTO.setCluePoiCate(clueExcelVO.getCluePoiCate());
            clueExcelDTO.setCluePoiName(clueExcelVO.getCluePoiName());
            clueExcelDTO.setSecondCityName(clueExcelVO.getSecondCityName());
            clueExcelDTO.setThirdCityName(clueExcelVO.getThirdCityName());
            clueExcelDTO.setCluePoiPhoneNum(clueExcelVO.getCluePoiPhoneNum());

            clueExcelDTOList.add(clueExcelDTO);
        }
        return clueExcelDTOList;
    }

    public static List<String> transClueErrorReasonListToDesc(List<Integer> errorReasonList) {
        List<String> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(errorReasonList)) {
            return resultList;
        }

        for (Integer errorReason : errorReasonList) {
            CanteenStallClueParseErrorEnum errorEnum = CanteenStallClueParseErrorEnum.getByType(errorReason);
            String result = errorEnum == null ? "" : errorEnum.getName();
            resultList.add(result);
        }

        return resultList;
    }

    /**
     * 食堂档口列表DTO转VO
     * @param clueListDTO 食堂档口列表DTO
     * @return WmCanteenStallClueListVO
     */
    public static WmCanteenStallClueListVO transCanteenStallClueListDTOToVO(WmCanteenStallClueListDTO clueListDTO) {
        WmCanteenStallClueListVO clueListVO = new WmCanteenStallClueListVO();
        if (clueListDTO == null) {
            return clueListVO;
        }

        clueListVO.setManageId(clueListDTO.getManageId());
        clueListVO.setSchoolPrimaryId(clueListDTO.getSchoolPrimaryId());
        clueListVO.setSchoolName(clueListDTO.getSchoolName());
        clueListVO.setCanteenPrimaryId(clueListDTO.getCanteenPrimaryId());
        clueListVO.setCanteenName(clueListDTO.getCanteenName());
        clueListVO.setCanteenStallClueList(transCanteenStallClueDTOsToVOs(clueListDTO.getClueDTOList()));
        clueListVO.setSchoolAddress(clueListDTO.getSchoolAddress());

        return clueListVO;
    }

    /**
     * 食堂档口线索详情DTO列表转VO列表
     * @param clueDetailDTOList  clueDetailDTOList
     * @return List<WmCanteenStallClueVO>
     */
    public static List<WmCanteenStallClueVO> transCanteenStallClueDTOsToVOs(List<WmCanteenStallClueDTO> clueDetailDTOList) {
        List<WmCanteenStallClueVO> detailVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(clueDetailDTOList)) {
            return detailVOList;
        }

        for (WmCanteenStallClueDTO detailDTO : clueDetailDTOList) {
            WmCanteenStallClueVO detailVO = new WmCanteenStallClueVO();
            detailVO.setId(detailDTO.getId());
            detailVO.setManageId(detailDTO.getManageId());
            detailVO.setBindId(detailDTO.getBindId());
            detailVO.setCanteenPrimaryId(detailDTO.getCanteenPrimaryId());
            detailVO.setCluePoiName(detailDTO.getCluePoiName());
            detailVO.setCluePoiAddress(detailDTO.getCluePoiAddress());
            detailVO.setClueLeafCateId(String.valueOf(detailDTO.getClueLeafCateId()));
            detailVO.setCluePoiCate(detailDTO.getCluePoiCate());
            detailVO.setSecondCityId(detailDTO.getSecondCityId());
            detailVO.setSecondCityName(detailDTO.getSecondCityName());
            detailVO.setCluePoiPhoneNum(detailDTO.getCluePoiPhoneNum());
            detailVO.setThirdCityId(detailDTO.getThirdCityId());
            detailVO.setThirdCityName(detailDTO.getThirdCityName());
            detailVO.setCluePoiCoordinate(detailDTO.getCluePoiCoordinate());
            detailVOList.add(detailVO);
        }
        return detailVOList;
    }


    /**
     * 学校AOI信息列表转范围列表
     * @param schoolAoiDTOList 学校AOI信息列表
     * @return 范围列表
     */
    public static List<String> transSchoolAoiDTOListToAreaList(List<WmScSchoolAoiDTO> schoolAoiDTOList) {
        List<String> areaList = new ArrayList<>();
        if (CollectionUtils.isEmpty(schoolAoiDTOList)) {
            return areaList;
        }

        for (WmScSchoolAoiDTO aoiDTO : schoolAoiDTOList) {
            areaList.add(aoiDTO.getAoiArea());
        }
        return areaList;
    }

    /**
     * 食堂档口线索详情VO转DTO
     * @param detailVO 食堂档口线索详情VO
     * @return WmCanteenStallClueDTO
     */
    public static WmCanteenStallClueDTO transCanteenStallClueVOToDTO(WmCanteenStallClueVO detailVO) {
        WmCanteenStallClueDTO detailDTO = new WmCanteenStallClueDTO();
        if (detailVO == null) {
            return detailDTO;
        }

        detailDTO.setId(detailVO.getId());
        detailDTO.setManageId(detailVO.getManageId());
        detailDTO.setCanteenPrimaryId(detailVO.getCanteenPrimaryId());
        detailDTO.setCluePoiName(detailVO.getCluePoiName());
        detailDTO.setCluePoiCoordinate(detailVO.getCluePoiCoordinate());
        detailDTO.setCluePoiPhoneNum(detailVO.getCluePoiPhoneNum());
        detailDTO.setCluePoiAddress(detailVO.getCluePoiAddress());
        detailDTO.setSecondCityId(detailVO.getSecondCityId());
        detailDTO.setSecondCityName(detailVO.getSecondCityName());
        detailDTO.setThirdCityId(detailVO.getThirdCityId());
        detailDTO.setThirdCityName(detailVO.getThirdCityName());
        detailDTO.setClueLeafCateId(Long.valueOf(detailVO.getClueLeafCateId()));
        detailDTO.setCluePoiCate(detailVO.getCluePoiCate());

        return detailDTO;
    }


    public static WmCanteenStallManageQueryDTO transCanteenStallManageQueryVOToDTO(WmCanteenStallManageQueryVO queryVO, Integer userId) {
        WmCanteenStallManageQueryDTO queryDTO = new WmCanteenStallManageQueryDTO();
        if (queryVO == null) {
            return queryDTO;
        }

        queryDTO.setManageId(queryVO.getManageId());
        queryDTO.setSchoolPrimaryId(queryVO.getSchoolPrimaryId());
        queryDTO.setCanteenPrimaryId(queryVO.getCanteenPrimaryId());
        queryDTO.setCuid(queryVO.getCuid());
        queryDTO.setCtimeStart(queryVO.getCtimeStart());
        queryDTO.setCtimeEnd(queryVO.getCtimeEnd());
        queryDTO.setWmPoiId(queryVO.getWmPoiId());
        queryDTO.setWdcClueId(queryVO.getWdcClueId());
        queryDTO.setTaskType(queryVO.getTaskType());
        queryDTO.setSubmitStatus(queryVO.getSubmitStatus());
        queryDTO.setUserId(userId);
        queryDTO.setPageNum(queryVO.getPageNum() == null ? CANTEEN_STALL_MANAGE_LIST_PAGE_NUM : queryVO.getPageNum());
        queryDTO.setPageSize(queryVO.getPageSize() == null ? CANTEEN_STALL_MANAGE_LIST_PAGE_SIZE : queryVO.getPageSize());

        return queryDTO;
    }


    public static WmCanteenStallManageListVO transCanteenStallManageListDTOToVO(WmCanteenStallManageListDTO manageListDTO, Map<String, Boolean> authMap) {
        WmCanteenStallManageListVO listVO = new WmCanteenStallManageListVO();
        if (manageListDTO == null) {
            return listVO;
        }

        listVO.setManageList(transCanteenStallManageDTOsToVOs(manageListDTO.getManageDTOList()));
        listVO.setPageInfo(transPageInfoDTOToVO(manageListDTO.getPageInfoDTO()));
        listVO.setBatchCreateClueButtonAuth(authMap.get(BATCH_CREATE_CLUE_BUTTON_OPERATION_CODE) ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
        listVO.setWmPoiBindButtonAuth(authMap.get(WM_POI_BIND_BUTTON) ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
        listVO.setWdcClueBindButtonAuth(authMap.get(WDC_BIND_BUTTON) ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
        return listVO;
    }


    public static WmScPageInfoVO transPageInfoDTOToVO(WmScPageInfoDTO pageInfoDTO) {
        WmScPageInfoVO pageInfoVO = new WmScPageInfoVO();
        if (pageInfoDTO == null) {
            pageInfoVO.setPageNum(0);
            pageInfoVO.setPageSize(0);
            pageInfoVO.setTotal(0L);
            return pageInfoVO;
        }

        pageInfoVO.setPageNum(pageInfoDTO.getPageNum() == null ? 0 : pageInfoDTO.getPageNum());
        pageInfoVO.setPageSize(pageInfoDTO.getPageSize() == null ? 0 : pageInfoDTO.getPageSize());
        pageInfoVO.setTotal(pageInfoDTO.getTotal() == null ? 0L : pageInfoDTO.getTotal());
        return pageInfoVO;
    }

    public static List<WmCanteenStallManageVO> transCanteenStallManageDTOsToVOs(List<WmCanteenStallManageDTO> manageDTOList) {
        List<WmCanteenStallManageVO> voList = new ArrayList<>();
        if (CollectionUtils.isEmpty(manageDTOList)) {
            return voList;
        }

        for (WmCanteenStallManageDTO manageDTO : manageDTOList) {
            WmCanteenStallManageVO vo = new WmCanteenStallManageVO();
            vo.setManageId(manageDTO.getId());
            vo.setCanteenName(manageDTO.getCanteenName());
            vo.setCanteenPrimaryId(manageDTO.getCanteenPrimaryId());
            vo.setSchoolName(manageDTO.getSchoolName());
            vo.setSchoolPrimaryId(manageDTO.getSchoolPrimaryId());
            vo.setSchoolClueId(manageDTO.getSchoolClueId().toString());
            vo.setCuid(manageDTO.getCuid());
            vo.setCmis(manageDTO.getCmis());
            vo.setCname(manageDTO.getCname());
            vo.setCtime(manageDTO.getCtime());
            vo.setTaskType(manageDTO.getTaskType());
            CanteenStallManageTaskTypeEnum taskTypeEnum = CanteenStallManageTaskTypeEnum.getByType(manageDTO.getTaskType());
            vo.setTaskTypeDesc(taskTypeEnum == null ? "-" : taskTypeEnum.getName());

            vo.setSubmitStatus(manageDTO.getSubmitStatus());
            CanteenStallManageSubmitStatusEnum submitStatusEnum = CanteenStallManageSubmitStatusEnum.getByType(manageDTO.getSubmitStatus());
            vo.setSubmitStatusDesc(submitStatusEnum == null ? "-" : submitStatusEnum.getName());

            vo.setViewDetailButtonAuth(manageDTO.getViewDetailButtonAuth());
            vo.setEditCoordinateButtonAuth(manageDTO.getEditCoordinateButtonAuth());

            voList.add(vo);
        }
        return voList;
    }


    public static WmCanteenStallBindQueryDTO transCanteenStallBindQueryVOToDTO(WmCanteenStallBindQueryVO bindQueryVO, Integer userId) {
        WmCanteenStallBindQueryDTO queryDTO = new WmCanteenStallBindQueryDTO();
        if (bindQueryVO == null) {
            return queryDTO;
        }

        queryDTO.setBindId(bindQueryVO.getBindId());
        queryDTO.setCanteenPrimaryId(bindQueryVO.getCanteenPrimaryId());
        queryDTO.setUserId(userId);
        queryDTO.setManageId(bindQueryVO.getManageId());
        queryDTO.setClueBindStatus(bindQueryVO.getClueBindStatus());
        queryDTO.setWmPoiBindStatus(bindQueryVO.getWmPoiBindStatus());
        queryDTO.setClueGenerateStatus(bindQueryVO.getClueGenerateStatus());
        queryDTO.setClueFollowUpStatus(bindQueryVO.getClueFollowUpStatus());
        queryDTO.setWmPoiId(bindQueryVO.getWmPoiId());
        queryDTO.setWdcClueId(bindQueryVO.getWdcClueId());
        queryDTO.setPageSize(bindQueryVO.getPageSize() == null ? CANTEEN_STALL_BIND_LIST_PAGE_SIZE : bindQueryVO.getPageSize());
        queryDTO.setPageNum(bindQueryVO.getPageNum() == null ? CANTEEN_STALL_BIND_LIST_PAGE_NUM : bindQueryVO.getPageNum());

        return queryDTO;
    }


    public static WmCanteenStallBindListVO transCanteenStallBindListDTOToVO(WmCanteenStallBindListDTO bindListDTO) {
        WmCanteenStallBindListVO listVO = new WmCanteenStallBindListVO();
        if (bindListDTO == null) {
            return listVO;
        }

        listVO.setSchoolPrimaryId(bindListDTO.getSchoolPrimaryId());
        listVO.setSchoolName(bindListDTO.getSchoolName());
        listVO.setCanteenPrimaryId(bindListDTO.getCanteenPrimaryId());
        listVO.setCanteenName(bindListDTO.getCanteenName());
        listVO.setBindList(transCanteenStallBindDTOsToVOs(bindListDTO.getBindDTOList()));
        listVO.setPageInfo(transPageInfoDTOToVO(bindListDTO.getPageInfoDTO()));
        return listVO;
    }

    public static List<WmCanteenStallBindVO> transCanteenStallBindDTOsToVOs(List<WmCanteenStallBindDTO> bindDTOList) {
        List<WmCanteenStallBindVO> bindVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(bindDTOList)) {
            return bindVOList;
        }

        for (WmCanteenStallBindDTO dto : bindDTOList) {
            WmCanteenStallBindVO bindVO = new WmCanteenStallBindVO();
            bindVO.setBindId(dto.getId());
            bindVO.setWmPoiId(dto.getWmPoiId().equals(0L) ? "-" : String.valueOf(dto.getWmPoiId()));
            bindVO.setWmPoiName(dto.getWmPoiName());
            bindVO.setWdcClueId(dto.getWdcClueId().equals(0L) ? "-" : String.valueOf(dto.getWdcClueId()));

            bindVO.setClueBindStatus(dto.getClueBindStatus());
            CanteenStallClueBindStatusEnum bindStatusEnum = CanteenStallClueBindStatusEnum.getByType(dto.getClueBindStatus());
            bindVO.setClueBindStatusDesc(bindStatusEnum == null ? "-" : bindStatusEnum.getName());
            bindVO.setClueBindFailReason(dto.getClueBindFailReason());

            bindVO.setClueGenerateStatus(dto.getClueGenerateStatus());
            CanteenStallClueGenerateStatusEnum generateStatusEnum = CanteenStallClueGenerateStatusEnum.getByType(dto.getClueGenerateStatus());
            bindVO.setClueGenerateStatusDesc(generateStatusEnum == null ? "-" : generateStatusEnum.getName());
            bindVO.setClueGenerateFailReason(dto.getClueGenerateFailReason());

            bindVO.setWmPoiBindStatus(dto.getWmPoiBindStatus());
            CanteenStallWmPoiBindStatusEnum wmPoiBindStatusEnum = CanteenStallWmPoiBindStatusEnum.getByType(dto.getWmPoiBindStatus());
            bindVO.setWmPoiBindStatusDesc(wmPoiBindStatusEnum == null ? "-" : wmPoiBindStatusEnum.getName());
            bindVO.setWmPoiBindFailReason(dto.getWmPoiBindFailReason());

            bindVO.setClueFollowUpStatus(dto.getClueFollowUpStatus());
            CanteenStallClueFollowUpStatusEnum followUpStatusEnum = CanteenStallClueFollowUpStatusEnum.getByType(dto.getClueFollowUpStatus());
            bindVO.setClueFollowUpStatusDesc(followUpStatusEnum == null ? "-" : followUpStatusEnum.getName());

            bindVO.setClueFollowUpOwnerUid(dto.getClueFollowUpOwnerUid());
            bindVO.setClueFollowUpOwnerMis(dto.getClueFollowUpOwnerMis());
            bindVO.setClueFollowUpOwnerName(dto.getClueFollowUpOwnerName());

            bindVO.setAuditStatus(dto.getAuditStatus());
            bindVO.setCtime(dto.getCtime());
            bindVO.setUtime(dto.getUtime());

            bindVO.setUnbindButtonAuth(dto.getUnbindButtonAuth());
            bindVO.setNormalButtonAuth(dto.getNormalButtonAuth());
            bindVO.setAbnormalButtonAuth(dto.getAbnormalButtonAuth());
            bindVO.setLogButtonAuth(dto.getLogButtonAuth());
            bindVO.setAuditingButtonAuth(dto.getAuditingButtonAuth());
            bindVO.setPlaceOrderButtonAuth(dto.getPlaceOrderButtonAuth());

            if (dto.getUnbindOperationCount() != null) {
                bindVO.setUnbindOperationCount(dto.getUnbindOperationCount());
            }
            if (dto.getApprovalProgressUrl() != null) {
                bindVO.setApprovalProgressUrl(dto.getApprovalProgressUrl());
            }
            if (dto.getTaskId() != null) {
                bindVO.setTaskId(dto.getTaskId());
            }
            if (dto.getTicketId() != null) {
                bindVO.setTicketId(dto.getTicketId());
            }
            if(dto.getUnbindOperationCount() != null){
                bindVO.setUnbindOperationCount(dto.getUnbindOperationCount());
            }
            bindVO.setIsUnbindAllowed(dto.getUnbindAllowed());


            // 线索生成失败原因
            if (StringUtils.isBlank(bindVO.getClueGenerateFailReason())) {
                bindVO.setClueGenerateFailReason("-");
            }
            // 线索绑定失败原因
            if (StringUtils.isBlank(bindVO.getClueBindFailReason())) {
                bindVO.setClueBindFailReason("-");
            }
            // 外卖门店绑定失败原因
            if (StringUtils.isBlank(bindVO.getWmPoiBindFailReason())) {
                bindVO.setWmPoiBindFailReason("-");
            }

            //解绑换绑按钮信息
            bindVO.setChangebindButtonAuth(dto.getChangebindButtonAuth());
            bindVO.setUnbindButtonAuthReason(dto.getUnbindButtonAuthReason());
            bindVO.setChangebindButtonAuthReason(dto.getChangebindButtonAuthReason());

            bindVOList.add(bindVO);
        }

        return bindVOList;
    }


    public static WmCanteenStallAuditSubmitDTO transCanteenStallAuditSubmitVOToDTO(WmCanteenStallAuditSubmitVO submitVO, WmEmploy currentEmploy) {
        WmCanteenStallAuditSubmitDTO submitDTO = new WmCanteenStallAuditSubmitDTO();
        submitDTO.setUserId(currentEmploy.getUid());
        submitDTO.setUserName(currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
        submitDTO.setAuditTaskType(submitVO.getAuditTaskType());
        submitDTO.setBindIdList(submitVO.getBindIdList());
        submitDTO.setProofPicture(submitVO.getProofPicture());
        submitDTO.setAbnormalReason(submitVO.getAbnormalReason());
        submitDTO.setCanteenPrimaryId(submitVO.getCanteenPrimaryId());

        return submitDTO;
    }


    public static WmCanteenStallAuditStreamVO transCanteenStallAuditStreamDTOToVO(WmCanteenStallAuditStreamDTO auditStreamDTO) {
        WmCanteenStallAuditStreamVO auditStreamVO = new WmCanteenStallAuditStreamVO();
        if (auditStreamDTO == null) {
            return auditStreamVO;
        }

        auditStreamVO.setAuditTaskType(auditStreamDTO.getAuditTaskType());
        CanteenStallAuditTaskTypeEnum auditTaskTypeEnum = CanteenStallAuditTaskTypeEnum.getByType(auditStreamDTO.getAuditTaskType());
        auditStreamVO.setAuditTaskTypeDesc(auditTaskTypeEnum == null ? "" : auditTaskTypeEnum.getName());
        auditStreamVO.setAuditTaskSystemId(auditStreamDTO.getAuditTaskSystemId());
        auditStreamVO.setCurrentAuditStatus(auditStreamDTO.getCurrentAuditStatus());
        CanteenStallAuditNodeStatusEnum nodeStatusEnum = CanteenStallAuditNodeStatusEnum.of(auditStreamDTO.getCurrentAuditStatus());
        auditStreamVO.setCurrentAuditStatusDesc(nodeStatusEnum == null ? "" : nodeStatusEnum.getName());
        auditStreamVO.setCurrentNodeCode(auditStreamDTO.getCurrentNodeCode());
        auditStreamVO.setAuditProgressList(transCanteenStallAuditProgressDTOsToVOs(auditStreamDTO.getAuditProgressList()));

        return auditStreamVO;
    }

    public static WmCanteenStallAuditTaskVO transCanteenStallAuditTaskDTOToVO(WmCanteenStallAuditTaskDTO auditTaskDTO) {
        WmCanteenStallAuditTaskVO taskVO = new WmCanteenStallAuditTaskVO();
        if (auditTaskDTO == null) {
            return taskVO;
        }

        taskVO.setAuditTaskType(auditTaskDTO.getAuditTaskType());
        taskVO.setId(auditTaskDTO.getId());
        taskVO.setCanteenPrimaryId(auditTaskDTO.getCanteenPrimaryId());
        taskVO.setProofPictureList(auditTaskDTO.getProofPictureList());
        taskVO.setAbnormalReason(auditTaskDTO.getAbnormalReason());
        CanteenStallClueAbnormalReasonEnum abnormalReasonEnum = CanteenStallClueAbnormalReasonEnum.getByType(auditTaskDTO.getAbnormalReason());
        taskVO.setAbnormalReasonDesc(abnormalReasonEnum == null ? "-" : abnormalReasonEnum.getName());
        return taskVO;
    }

    public static WmCanteenPoiSimpleStreamVO transCanteenPoiSimpleProgressDTOToVO(WmCanteenPoiSimpleStreamDTO streamDTO) {
        WmCanteenPoiSimpleStreamVO streamVO = new WmCanteenPoiSimpleStreamVO();
        if (streamDTO == null) {
            return streamVO;
        }
        streamVO.setAuditTaskType(streamDTO.getAuditTaskType());
        streamVO.setAuditTaskSystemId(streamDTO.getAuditTaskSystemId());
        streamVO.setCurrentNodeCode(streamDTO.getCurrentNodeCode());
        streamVO.setCurrentAuditStatus(streamDTO.getCurrentAuditStatus());
        streamVO.setAuditTaskTypeDesc(streamDTO.getAuditTaskTypeDesc());
        // 处理auditProgressList的转换
        if (streamDTO.getAuditProgressList() != null) {
            List<WmCanteenPoiSimpleProgressVO> progressVOList = streamDTO.getAuditProgressList().stream()
                    .map(progressDTO -> transCanteenPoiSimpleProgressDTOToVO(progressDTO))
                    .collect(Collectors.toList());
            streamVO.setAuditProgressList(progressVOList);
        }
        return streamVO;
    }

    /**
     * 将WmCanteenPoiSimpleProgressDTO转换为WmCanteenPoiSimpleProgressVO的辅助方法。
     * @param progressDTO 要转换的WmCanteenPoiSimpleProgressDTO对象
     * @return 转换后的WmCanteenPoiSimpleProgressVO对象
     */
    private static WmCanteenPoiSimpleProgressVO transCanteenPoiSimpleProgressDTOToVO(WmCanteenPoiSimpleProgressDTO progressDTO) {
        WmCanteenPoiSimpleProgressVO progressVO = new WmCanteenPoiSimpleProgressVO();

        progressVO.setAuditNode(progressDTO.getAuditNode());
        progressVO.setAuditNodeDesc(progressDTO.getAuditNodeDesc());
        progressVO.setOrder(progressDTO.getOrder());
        progressVO.setAuditorUid(progressDTO.getAuditorUid());
        progressVO.setAuditorMis(progressDTO.getAuditorMis());
        progressVO.setAuditorName(progressDTO.getAuditorName());
        progressVO.setAuditTime(progressDTO.getAuditTime());
        progressVO.setAuditResult(progressDTO.getAuditResult());
        progressVO.setAuditResultDesc(progressDTO.getAuditResultDesc());
        progressVO.setAuditRemark(progressDTO.getAuditRemark());

        return progressVO;
    }

    public static List<WmCanteenStallAuditProgressVO> transCanteenStallAuditProgressDTOsToVOs(List<WmCanteenStallAuditProgressDTO> auditProgressDTOList) {
        List<WmCanteenStallAuditProgressVO> auditProgressVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(auditProgressDTOList)) {
            return auditProgressVOList;
        }

        for (WmCanteenStallAuditProgressDTO progressDTO : auditProgressDTOList) {
            WmCanteenStallAuditProgressVO progressVO = new WmCanteenStallAuditProgressVO();
            progressVO.setOrder(progressDTO.getOrder());
            progressVO.setAuditNode(progressDTO.getAuditNode());
            progressVO.setAuditResult(progressDTO.getAuditResult());
            progressVO.setAuditNodeDesc(progressDTO.getAuditNodeDesc());
            progressVO.setAuditRemark(progressDTO.getAuditRemark());
            progressVO.setAuditResultDesc(progressDTO.getAuditResultDesc());
            progressVO.setAuditTime(progressDTO.getAuditTime());
            progressVO.setAuditorUid(progressDTO.getAuditorUid());
            progressVO.setAuditorMis(progressDTO.getAuditorMis());
            progressVO.setAuditorName(progressDTO.getAuditorName());

            auditProgressVOList.add(progressVO);
        }

        return auditProgressVOList;
    }


    public static WmCanteenStallBindSubmitDTO transCanteenStallBindSubmitVOToDTO(WmCanteenStallBindSubmitVO submitVO) {
        WmCanteenStallBindSubmitDTO submitDTO = new WmCanteenStallBindSubmitDTO();
        if (submitVO == null) {
            return submitDTO;
        }

        submitDTO.setCanteenPrimaryId(submitVO.getCanteenPrimaryId());
        submitDTO.setWdcClueIdList(submitVO.getWdcClueIdList());
        submitDTO.setWmPoiIdList(submitVO.getWmPoiIdList());
        return submitDTO;
    }

    public static List<WmCanteenStallWdcErrorVO> transCanteenStallCheckFailDTOsTOWdcErrorVOs(List<WmCanteenStallCheckFailDTO> checkFailDTOList) {
        List<WmCanteenStallWdcErrorVO> errorVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(checkFailDTOList)) {
            return errorVOList;
        }

        for (WmCanteenStallCheckFailDTO failDTO : checkFailDTOList) {
            WmCanteenStallWdcErrorVO errorVO = new WmCanteenStallWdcErrorVO();
            errorVO.setWdcClueId(failDTO.getId());
            errorVO.setFailReason(failDTO.getFailReason());
            errorVOList.add(errorVO);
        }
        return errorVOList;
    }

    public static List<WmCanteenStallWmPoiErrorVO> transCanteenStallCheckFailDTOListToWmPoiErrorVOList(List<WmCanteenStallCheckFailDTO> checkFailDTOList) {
        List<WmCanteenStallWmPoiErrorVO> errorVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(checkFailDTOList)) {
            return errorVOList;
        }

        for (WmCanteenStallCheckFailDTO failDTO : checkFailDTOList) {
            WmCanteenStallWmPoiErrorVO errorVO = new WmCanteenStallWmPoiErrorVO();
            errorVO.setWmPoiId(failDTO.getId());
            errorVO.setFailReason(failDTO.getFailReason());
            errorVOList.add(errorVO);
        }
        return errorVOList;
    }

    /**
     * 将履约侧AOI信息转换为学校AOI VO
     * @param bmAoiAttrView 履约侧AOI信息
     * @return 学校AOI VO
     */
    public static WmScSchoolAoiDetailVO transBmAoiAttrViewToSchoolAoiDetailVO(BmAoiAttrView bmAoiAttrView) {
        WmScSchoolAoiDetailVO wmScSchoolAoiDetailVO = new WmScSchoolAoiDetailVO();
        if (bmAoiAttrView == null) {
            return wmScSchoolAoiDetailVO;
        }
        wmScSchoolAoiDetailVO.setAoiId(bmAoiAttrView.getBmAoiId());
        wmScSchoolAoiDetailVO.setAoiName(bmAoiAttrView.getName());
        TrafficType trafficType = bmAoiAttrView.getDetailedTrafficType();
        wmScSchoolAoiDetailVO.setAoiMode(trafficType == null ? SchoolAoiModeEnum.NULL.getType() : trafficType.getValue());
        // 设置通行属性枚举值描述
        SchoolAoiModeEnum schoolAoiModeEnum = SchoolAoiModeEnum.getByType(trafficType == null ? null : trafficType.getValue());
        wmScSchoolAoiDetailVO.setAoiModeDesc(schoolAoiModeEnum == null ? "-" : schoolAoiModeEnum.getName());
        wmScSchoolAoiDetailVO.setAoiArea(transBmPointListToAreaStr(bmAoiAttrView.getPolygon()));
        return wmScSchoolAoiDetailVO;
    }

    /**
     * 将履约侧坐标点列表转换为学校侧坐标点
     * @param bmPointList 履约侧坐标点列表
     * @return 转换为学校侧坐标点str
     */
    public static String transBmPointListToAreaStr(List<BmPoint> bmPointList) {
        List<WmScPoint> wmScPointList = new ArrayList<>();
        if (deps.redis.clients.util.CollectionUtils.isEmpty(bmPointList)) {
            return wmScPointList.toString();
        }

        for (BmPoint bmPoint : bmPointList) {
            WmScPoint wmScPoint = new WmScPoint();
            wmScPoint.setX(bmPoint.getLat());
            wmScPoint.setY(bmPoint.getLng());
            wmScPointList.add(wmScPoint);
        }
        return JSON.toJSONString(wmScPointList);
    }
}
