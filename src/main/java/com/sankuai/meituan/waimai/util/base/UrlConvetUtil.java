package com.sankuai.meituan.waimai.util.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.util.StringUtil;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.*;

/**
 * Created by ji<PERSON><PERSON><PERSON><PERSON> on 16/3/28.
 */
public class UrlConvetUtil {

  private static final Logger log = LoggerFactory.getLogger(UrlConvetUtil.class);
  private static final int HTTP_SOCKET_TIMEOUT = 5000;
  private static final int HTTP_CONNECT_TIMEOUT = 2000;
  private static final int HTTP_CONNECTION_REQUEST_TIMEOUT = 1000;
  private static final String API_URL = "http://pigeon.dper.com/mobile-oss-server/invoke.json";


    /**
     * @param longUrl 要跳转的H5页面longUrl
     * @return  string  与长链接对应的短链接
     * */
    public static String postForShortUrl(String longUrl,String api_url) throws ParseException,IOException{
      if (longUrl == null){
        return null;
      }
      if(StringUtil.isBlank(api_url)){
        api_url = API_URL;
      }
      HttpPost httpPost = new HttpPost(api_url);
      Map<String,String> params = new HashMap<String,String>();
      params.put("url", "http://service.dianping.com/memberOSSService/OperateService_1.0.0");
      params.put("method", "createShortUrl");
      params.put("parameterTypes", "java.lang.String");
      params.put("parameters", longUrl);
        List<NameValuePair> kvs = new ArrayList<NameValuePair>();
        Iterator<Map.Entry<String,String>> it = params.entrySet().iterator();
        while(it.hasNext()){
          Map.Entry<String,String> entry = it.next();
          kvs.add(new BasicNameValuePair(entry.getKey(),entry.getValue()));
        }
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        httpPost.setEntity(new UrlEncodedFormEntity(kvs, StandardCharsets.UTF_8));
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(HTTP_SOCKET_TIMEOUT).setConnectionRequestTimeout(HTTP_CONNECTION_REQUEST_TIMEOUT).setConnectTimeout(HTTP_CONNECT_TIMEOUT).build();
        httpPost.setConfig(requestConfig);
      CloseableHttpClient httpClient = HttpClients.createDefault();
      CloseableHttpResponse response = null;
      String result = "";
      String shortUrl = "";
      try {
        response = httpClient.execute(httpPost);
        result = EntityUtils.toString(response.getEntity());
        JSONObject resultAll =  JSON.parseObject(result);
        String dataJsonStr = resultAll.get("data").toString();
        JSONObject data = JSON.parseObject(dataJsonStr);
        shortUrl = data.get("shortUrl").toString();
        log.info("#长连接{}，短链接{}", longUrl, shortUrl);
        // 因为遇到https被运营商劫持的问题，先临时改成http方式。点评侧推动商务与运营商沟通
        shortUrl = getValidShortUrl(shortUrl);
        
        return shortUrl;
      }catch (Exception e){
        log.warn("postForShortUrl()发生异常，longUrl={}, e={}",longUrl,e);
      }finally {
        if (response!= null){
          response.close();
        }
        httpPost.releaseConnection();
        httpClient.close();
      }
      return "";
    }
    
    /**
     * 获取转义过的http短链接
     * 
     * @param shortUrl
     * @return
     * <AUTHOR> @create_time 2017年1月5日
     */
    public static String getValidShortUrl(String shortUrl) {
        if(StringUtil.isNotBlank(shortUrl)) {
            if(shortUrl.matches("^https://.+$")) { // 短链接以https开头，进行替换
                return shortUrl.replaceFirst("https://", "http://");
            }
        }
        return shortUrl;
    }
}
