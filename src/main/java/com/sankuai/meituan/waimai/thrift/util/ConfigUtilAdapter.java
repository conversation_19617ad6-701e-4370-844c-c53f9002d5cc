package com.sankuai.meituan.waimai.thrift.util;

import com.netflix.config.*;
import org.apache.commons.configuration.AbstractConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @created 13-1-25
 */
public class ConfigUtilAdapter {
    private static final Logger LOG = LoggerFactory.getLogger(ConfigUtilAdapter.class);

    private ConfigUtilAdapter() {
    }


    public static void setValue(String key,  String value) {
        AbstractConfiguration instance = ConfigurationManager.getConfigInstance();
        if (instance instanceof ConcurrentCompositeConfiguration) {
            ConcurrentCompositeConfiguration composeConfig = (ConcurrentCompositeConfiguration) instance;
            for (AbstractConfiguration configuration : composeConfig.getConfigurations()) {
                configuration.setProperty(key, value);
            }
        } else {
            ConfigurationManager.getConfigInstance().setProperty(key, value);
        }
    }

    public static DynamicPropertyFactory getFactory() {
        //autoInstall();
        return DynamicPropertyFactory.getInstance();
    }

    public static AbstractConfiguration getGolbalConfig() {
        //autoInstall();
        return ConfigurationManager.getConfigInstance();
    }

    public static boolean getBoolean(String key) {
        return getDynamicBool(key, false).get();
    }

    public static boolean getBoolean(String key, boolean defaultValue) {
        return getDynamicBool(key, defaultValue).get();
    }

    public static int getInt(String key) {
        return getDynamicInt(key, 0).get();
    }

    public static int getInt(String key, int defaultValue) {
        return getDynamicInt(key, defaultValue).get();
    }

    public static long getLong(String key) {
        return getDynamicLong(key, 0L).get();
    }

    public static long getLong(String key, long defaultValue) {
        return getDynamicLong(key, defaultValue).get();
    }

    public static String getString(String key) {
        return getDynamicString(key, null).get();
    }

    public static String getString(String key, String defaultValue) {
        return getDynamicString(key, defaultValue).get();
    }

    public static String[] getStringArray(String key) {
        return getGolbalConfig().getStringArray(key);
    }

    public static DynamicBooleanProperty getDynamicBool(String key, boolean defaultValue) {
        return getFactory().getInstance().getBooleanProperty(key, defaultValue);
    }

    public static DynamicIntProperty getDynamicInt(String key, int defaultValue) {
        return getFactory().getInstance().getIntProperty(key, defaultValue);
    }

    public static DynamicLongProperty getDynamicLong(String key, long defaultValue) {
        return getFactory().getLongProperty(key, defaultValue);
    }

    public static DynamicStringProperty getDynamicString(String key, String defaultValue) {
        return getFactory().getStringProperty(key, defaultValue);
    }

    /*
    private static volatile boolean tryAutoLoad = false;

    private static void autoInstall() {
        if (!ConfigurationManager.isConfigurationInstalled() && !tryAutoLoad) {
            try {
                tryAutoLoad = true;
                autoLoad();
                LOG.info("autoInstall");
            } catch (Exception e) {
                LOG.warn("autoLoad fail...", e);
            }
        }
    }

    private static String zookeeperConnectStringKey = "core.zookeeper";
    private static String zookeeperDefaultConfigPath = "/sankuai/config";

    public static void autoLoad() throws Exception {
        final ConcurrentCompositeConfiguration compositeConfig = new ConcurrentCompositeConfiguration();
        // load system config
        AbstractConfiguration systemConfig = ConfigUtils.loadFromSystem();
        LOG.debug("load systemConfig " + systemConfig.getKeys());
        compositeConfig.addConfiguration(systemConfig, "system configuration");
        // load zookeeper config first system then mtsg
        String zookeeperConnectString = (systemConfig.getString(zookeeperConnectStringKey) != null ? systemConfig.getString(zookeeperConnectStringKey) : loadFromWeb(systemConfig.getString("mtsg_host")));
        if (zookeeperConnectString != null) {
            AbstractConfiguration zookeeperConfig = ZookeeperLoader.loadFromZookeeperAndWatch(zookeeperConnectString, zookeeperDefaultConfigPath);
            LOG.debug("load zookeeperConfig " + zookeeperConfig.getKeys());
            compositeConfig.addConfiguration(zookeeperConfig, "zookeeper configuration");
        }
        LOG.debug("load compositeConfig " + compositeConfig.getProperties());
        ConfigurationManager.install(compositeConfig);
    }
    */
    
}
