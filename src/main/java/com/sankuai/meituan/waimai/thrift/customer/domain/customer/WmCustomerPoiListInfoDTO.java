package com.sankuai.meituan.waimai.thrift.customer.domain.customer;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

@ThriftStruct
public class WmCustomerPoiListInfoDTO {

    private Integer id;
    /**
     * 客户切换任务id
     */
    private Long switchTaskId;
    /**
     * 绑定状态
     */
    private Integer relationStatus;
    /**
     * 门店绑定客户关系是否有效
     */
    private Integer valid;
    /**
     * 外卖门店ID
     */
    private Long wmPoiId;
    /**
     * 门店名称
     */
    private String shopName;
    /**
     * 门店地址
     */
    private String address;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 客户平台id
     */
    private Long mtCustomerId;
    /**
     * 运营经理所在kpID
     */
    private Integer opManagerId;
    /**
     * 运营经理姓名
     */
    private String opManagerName;
    /**
     * 基本信息状态
     */
    private Integer baseStatus;
    /**
     * 资质信息状态
     */
    private Integer quaStatus;
    /**
     * 配送信息状态
     */
    private Integer deliveryStatus;
    /**
     * 结算信息状态
     */
    private Integer settleStatus;
    /**
     * 营业信息状态
     */
    private Integer serviceStatus;
    /**
     * 商品状态
     */
    private Integer productStatus;
    /**
     * 门店上单状态
     */
    private Integer poiStatus;
    /**
     * 是否被逻辑删除
     */
    private Integer isDelete;
    /**
     * 业务品牌
     */
    private Integer brandId;
    /**
     * 门店责任人
     */
    private Integer ownerUid;

    /**
     * 是否子门店
     */
    @FieldDoc(description = "是否子门店,使用枚举值 com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerPoiChildEnum")
    private Integer childPoiFlag;


    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField
    public void setId(Integer id) {
        this.id = id;
    }

    @ThriftField(2)
    public Long getSwitchTaskId() {
        return switchTaskId;
    }

    @ThriftField
    public void setSwitchTaskId(Long switchTaskId) {
        this.switchTaskId = switchTaskId;
    }

    @ThriftField(3)
    public Integer getRelationStatus() {
        return relationStatus;
    }

    @ThriftField
    public void setRelationStatus(Integer relationStatus) {
        this.relationStatus = relationStatus;
    }

    @ThriftField(4)
    public Integer getValid() {
        return valid;
    }

    @ThriftField
    public void setValid(Integer valid) {
        this.valid = valid;
    }

    @ThriftField(5)
    public Long getWmPoiId() {
        return wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(6)
    public String getShopName() {
        return shopName;
    }

    @ThriftField
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    @ThriftField(7)
    public String getAddress() {
        return address;
    }

    @ThriftField
    public void setAddress(String address) {
        this.address = address;
    }

    @ThriftField(8)
    public Long getCustomerId() {
        return customerId;
    }

    @ThriftField
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    @ThriftField(9)
    public Long getMtCustomerId() {
        return mtCustomerId;
    }

    @ThriftField
    public void setMtCustomerId(Long mtCustomerId) {
        this.mtCustomerId = mtCustomerId;
    }

    @ThriftField(10)
    public Integer getOpManagerId() {
        return opManagerId;
    }

    @ThriftField
    public void setOpManagerId(Integer opManagerId) {
        this.opManagerId = opManagerId;
    }

    @ThriftField(11)
    public String getOpManagerName() {
        return opManagerName;
    }

    @ThriftField
    public void setOpManagerName(String opManagerName) {
        this.opManagerName = opManagerName;
    }

    @ThriftField(12)
    public Integer getBaseStatus() {
        return baseStatus;
    }

    @ThriftField
    public void setBaseStatus(Integer baseStatus) {
        this.baseStatus = baseStatus;
    }

    @ThriftField(13)
    public Integer getQuaStatus() {
        return quaStatus;
    }

    @ThriftField
    public void setQuaStatus(Integer quaStatus) {
        this.quaStatus = quaStatus;
    }

    @ThriftField(14)
    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    @ThriftField
    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    @ThriftField(15)
    public Integer getSettleStatus() {
        return settleStatus;
    }

    @ThriftField
    public void setSettleStatus(Integer settleStatus) {
        this.settleStatus = settleStatus;
    }

    @ThriftField(16)
    public Integer getServiceStatus() {
        return serviceStatus;
    }

    @ThriftField
    public void setServiceStatus(Integer serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    @ThriftField(17)
    public Integer getProductStatus() {
        return productStatus;
    }

    @ThriftField
    public void setProductStatus(Integer productStatus) {
        this.productStatus = productStatus;
    }

    @ThriftField(18)
    public Integer getPoiStatus() {
        return poiStatus;
    }

    @ThriftField
    public void setPoiStatus(Integer poiStatus) {
        this.poiStatus = poiStatus;
    }


    @ThriftField(19)
    public Integer getIsDelete() {
        return isDelete;
    }

    @ThriftField
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @ThriftField(20)
    public Integer getBrandId() { return brandId; }

    @ThriftField
    public void setBrandId(Integer brandId) { this.brandId = brandId; }

    @ThriftField(21)
    public Integer getOwnerUid() { return ownerUid; }

    @ThriftField
    public void setOwnerUid(Integer ownerUid) { this.ownerUid = ownerUid; }

    @ThriftField(22)
    public Integer getChildPoiFlag() {
        return childPoiFlag;
    }

    @ThriftField
    public void setChildPoiFlag(Integer childPoiFlag) {
        this.childPoiFlag = childPoiFlag;
    }
}
