package com.sankuai.meituan.waimai.thrift.customer.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.meituan.waimai.thrift.customer.domain.ModuleDetailStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

@ThriftService
public interface WmCustomerCommonThriftService {

    @ThriftMethod
    ModuleDetailStatus getModuleDetailStatusByCustomerId(Integer customerId) throws TException, WmCustomerException;

    @ThriftMethod
    WmCustomerCommonBo saveWmCustomerModulesInfo(WmCustomerInfoAggre wmCustomerInfoAggre, int opUid, String opName) throws WmCustomerException, TException;

    @ThriftMethod
    BooleanResult saveCustomerBlackWhiteList(CustomerBlackWhiteBo bo) throws TException, WmCustomerException;

    @ThriftMethod
    BooleanResult deleteCustomerBlackWhiteList(CustomerBlackWhiteParam param) throws TException, WmCustomerException;

    @ThriftMethod
    BooleanResult queryInCustomerBlackWhiteList(CustomerBlackWhiteParam param) throws TException, WmCustomerException;

    @ThriftMethod
    BooleanResult isInWalletGreyList(Integer customerId) throws TException, WmCustomerException;

    /**
     * 钱包迁移灰度控制
     * @param bizId 业务id:3.0结算为客户ID,代理商合同为代理商合同ID
     * @param bizType 业务类型："SETTLE":结算模块;"AGENT":代理商模块
     * @return true:使用新钱包流程,发送mafka消息; false:使用老钱包流程,发送rabbitmq消息
     * @throws TException
     * @throws WmCustomerException
     */
    @ThriftMethod
    BooleanResult walletMigrationControl(Integer bizId,String bizType) throws TException, WmCustomerException;

    /**
     * 统一客户异步消息发送
     * @param msg WmCustomerBizAsyncNotifyMsg对象json序列化的结果
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @ThriftMethod
    BooleanResult sendCustomerAsyncNotify(String msg) throws TException, WmCustomerException;

    @ThriftMethod
    BooleanResult saveCustomerContract(WmCustomerContractSaveParam saveParam) throws TException, WmCustomerException;

    @ThriftMethod
    BooleanResult sendCMSToCustomerKP(SendCmsToKpParam param) throws TException, WmCustomerException;

    @ThriftMethod
    BooleanResult sendCMSToWmPoiContact(SendCmsToWmPoiContactParam param) throws TException, WmCustomerException;

}
