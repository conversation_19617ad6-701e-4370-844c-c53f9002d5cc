package com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.util.List;

/**
 * 学校交付目标制定信息DTO
 * <AUTHOR>
 * @date 2024/03/12
 * @email <EMAIL>
 */
@ThriftStruct
public class WmSchoolDeliveryGoalSetDTO {

    @FieldDoc(
            description = "主键ID",
            example = {"12"}
    )
    private Integer id;

    @FieldDoc(
            description = "交付编号ID",
            example = {"123"}
    )
    private Integer deliveryId;

    @FieldDoc(
            description = "学校主键ID",
            example = {"1401"}
    )
    private Integer schoolPrimaryId;

    @FieldDoc(
            description = "普遍客户梳理人员ID列表",
            example = {"123,432"}
    )
    private String contactUserIds;

    @FieldDoc(
            description = "校内站线上签约预计完成时间",
            example = {"2024-04-12"}
    )
    private String inscOnlineSignExptime;

    @FieldDoc(
            description = "校内站线下建站预计完成时间",
            example = {"2024-04-12"}
    )
    private String inscOfflineBuildExptime;

    @FieldDoc(
            description = "是否建立校外校外建站",
            example = {"1"},
            rule = "com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryBuildOffCampusEnum"
    )
    private Integer buildOffCampusStation;

    @FieldDoc(
            description = "校外站线上签约预计完成时间",
            example = {"2024-04-12"}
    )
    private String outscOnlineSignExptime;

    @FieldDoc(
            description = "校外站线下建站预计完成时间",
            example = {"2024-04-12"}
    )
    private String outscOfflineBuildExptime;

    @FieldDoc(
            description = "相对准时率目标(小于等于100的两位小数)",
            example = {"12.2"}
    )
    private String ontimeRateTarget;

    @FieldDoc(
            description = "相对准时率预计完成时间",
            example = {"2024-04-12"}
    )
    private String ontimeRateTargetExptime;

    @FieldDoc(
            description = "平均配送时长目标(≤40的非负数字，允许两位小数)",
            example = {"12.3"}
    )
    private String avgDeliveryTimeTarget;

    @FieldDoc(
            description = "平均配送时长目标预计完成时间",
            example = {"2024-04-12"}
    )
    private String avgDeliveryTimeTargetExptime;

    @FieldDoc(
            description = "首批档口建店计划时间",
            example = {"2024-04-12"}
    )
    private String firstStallBulidExptime;

    @FieldDoc(
            description = "首批档口上线计划时间",
            example = {"2024-04-12"}
    )
    private String firstStallOnlineExptime;

    @FieldDoc(
            description = "首批上线档口数(非负整数，需≤可交付档口数)",
            example = {"12"}
    )
    private Integer firstOnlineStallNum;

    @FieldDoc(
            description = "在线渗透率目标(小于等于100的两位小数)",
            example = {"12.2"}
    )
    private String onlinePenerateTarget;

    @FieldDoc(
            description = "在线渗透率目标预计完成时间",
            example = {"2024-04-12"}
    )
    private String onlinePenerateTargetExptime;

    @FieldDoc(
            description = "日均订单量目标(只可输入非负整数)",
            example = {"421"}
    )
    private String dailyOrderTarget;

    @FieldDoc(
            description = "日均订单量目标预计完成时间",
            example = {"2024-04-12"}
    )
    private String dailyOrderTargetExptime;

    @FieldDoc(
            description = "人顿渗透率目标(小于等于100的两位小数)",
            example = {"11.3"}
    )
    private String mealPenerateTarget;

    @FieldDoc(
            description = "人顿渗透率目标预计完成时间",
            example = {"2024-04-12"}
    )
    private String mealPenerateTargetExptime;

    @FieldDoc(
            description = "日均店单产目标(允许两位小数)",
            example = {"11.3"}
    )
    private String dailyYieldTarget;

    @FieldDoc(
            description = "日均店单产目标预计完成时间",
            example = {"2024-04-12"}
    )
    private String dailyYieldTargetExptime;

    @FieldDoc(
            description = "数据版本号",
            example = {"12"}
    )
    private Integer dataVersion;


    @ThriftField(1)
    public Integer getId() {
        return id;
    }

    @ThriftField
    public void setId(Integer id) {
        this.id = id;
    }

    @ThriftField(2)
    public Integer getDeliveryId() {
        return deliveryId;
    }

    @ThriftField
    public void setDeliveryId(Integer deliveryId) {
        this.deliveryId = deliveryId;
    }

    @ThriftField(3)
    public Integer getSchoolPrimaryId() {
        return schoolPrimaryId;
    }

    @ThriftField
    public void setSchoolPrimaryId(Integer schoolPrimaryId) {
        this.schoolPrimaryId = schoolPrimaryId;
    }

    @ThriftField(4)
    public String getContactUserIds() {
        return contactUserIds;
    }

    @ThriftField
    public void setContactUserIds(String contactUserIds) {
        this.contactUserIds = contactUserIds;
    }

    @ThriftField(5)
    public String getInscOnlineSignExptime() {
        return inscOnlineSignExptime;
    }

    @ThriftField
    public void setInscOnlineSignExptime(String inscOnlineSignExptime) {
        this.inscOnlineSignExptime = inscOnlineSignExptime;
    }

    @ThriftField(6)
    public String getInscOfflineBuildExptime() {
        return inscOfflineBuildExptime;
    }

    @ThriftField
    public void setInscOfflineBuildExptime(String inscOfflineBuildExptime) {
        this.inscOfflineBuildExptime = inscOfflineBuildExptime;
    }

    @ThriftField(7)
    public Integer getBuildOffCampusStation() {
        return buildOffCampusStation;
    }

    @ThriftField
    public void setBuildOffCampusStation(Integer buildOffCampusStation) {
        this.buildOffCampusStation = buildOffCampusStation;
    }

    @ThriftField(8)
    public String getOutscOnlineSignExptime() {
        return outscOnlineSignExptime;
    }

    @ThriftField
    public void setOutscOnlineSignExptime(String outscOnlineSignExptime) {
        this.outscOnlineSignExptime = outscOnlineSignExptime;
    }

    @ThriftField(9)
    public String getOutscOfflineBuildExptime() {
        return outscOfflineBuildExptime;
    }

    @ThriftField
    public void setOutscOfflineBuildExptime(String outscOfflineBuildExptime) {
        this.outscOfflineBuildExptime = outscOfflineBuildExptime;
    }

    @ThriftField(10)
    public String getOntimeRateTarget() {
        return ontimeRateTarget;
    }

    @ThriftField
    public void setOntimeRateTarget(String ontimeRateTarget) {
        this.ontimeRateTarget = ontimeRateTarget;
    }

    @ThriftField(11)
    public String getOntimeRateTargetExptime() {
        return ontimeRateTargetExptime;
    }

    @ThriftField
    public void setOntimeRateTargetExptime(String ontimeRateTargetExptime) {
        this.ontimeRateTargetExptime = ontimeRateTargetExptime;
    }

    @ThriftField(12)
    public String getAvgDeliveryTimeTarget() {
        return avgDeliveryTimeTarget;
    }

    @ThriftField
    public void setAvgDeliveryTimeTarget(String avgDeliveryTimeTarget) {
        this.avgDeliveryTimeTarget = avgDeliveryTimeTarget;
    }

    @ThriftField(13)
    public String getAvgDeliveryTimeTargetExptime() {
        return avgDeliveryTimeTargetExptime;
    }

    @ThriftField
    public void setAvgDeliveryTimeTargetExptime(String avgDeliveryTimeTargetExptime) {
        this.avgDeliveryTimeTargetExptime = avgDeliveryTimeTargetExptime;
    }

    @ThriftField(14)
    public String getFirstStallBulidExptime() {
        return firstStallBulidExptime;
    }

    @ThriftField
    public void setFirstStallBulidExptime(String firstStallBulidExptime) {
        this.firstStallBulidExptime = firstStallBulidExptime;
    }

    @ThriftField(15)
    public String getFirstStallOnlineExptime() {
        return firstStallOnlineExptime;
    }

    @ThriftField
    public void setFirstStallOnlineExptime(String firstStallOnlineExptime) {
        this.firstStallOnlineExptime = firstStallOnlineExptime;
    }

    @ThriftField(16)
    public Integer getFirstOnlineStallNum() {
        return firstOnlineStallNum;
    }

    @ThriftField
    public void setFirstOnlineStallNum(Integer firstOnlineStallNum) {
        this.firstOnlineStallNum = firstOnlineStallNum;
    }

    @ThriftField(17)
    public String getOnlinePenerateTarget() {
        return onlinePenerateTarget;
    }

    @ThriftField
    public void setOnlinePenerateTarget(String onlinePenerateTarget) {
        this.onlinePenerateTarget = onlinePenerateTarget;
    }

    @ThriftField(18)
    public String getOnlinePenerateTargetExptime() {
        return onlinePenerateTargetExptime;
    }

    @ThriftField
    public void setOnlinePenerateTargetExptime(String onlinePenerateTargetExptime) {
        this.onlinePenerateTargetExptime = onlinePenerateTargetExptime;
    }

    @ThriftField(19)
    public String getDailyOrderTarget() {
        return dailyOrderTarget;
    }

    @ThriftField
    public void setDailyOrderTarget(String dailyOrderTarget) {
        this.dailyOrderTarget = dailyOrderTarget;
    }

    @ThriftField(20)
    public String getDailyOrderTargetExptime() {
        return dailyOrderTargetExptime;
    }

    @ThriftField
    public void setDailyOrderTargetExptime(String dailyOrderTargetExptime) {
        this.dailyOrderTargetExptime = dailyOrderTargetExptime;
    }

    @ThriftField(21)
    public String getMealPenerateTarget() {
        return mealPenerateTarget;
    }

    @ThriftField
    public void setMealPenerateTarget(String mealPenerateTarget) {
        this.mealPenerateTarget = mealPenerateTarget;
    }

    @ThriftField(22)
    public String getMealPenerateTargetExptime() {
        return mealPenerateTargetExptime;
    }

    @ThriftField
    public void setMealPenerateTargetExptime(String mealPenerateTargetExptime) {
        this.mealPenerateTargetExptime = mealPenerateTargetExptime;
    }

    @ThriftField(23)
    public String getDailyYieldTarget() {
        return dailyYieldTarget;
    }

    @ThriftField
    public void setDailyYieldTarget(String dailyYieldTarget) {
        this.dailyYieldTarget = dailyYieldTarget;
    }

    @ThriftField(24)
    public String getDailyYieldTargetExptime() {
        return dailyYieldTargetExptime;
    }

    @ThriftField
    public void setDailyYieldTargetExptime(String dailyYieldTargetExptime) {
        this.dailyYieldTargetExptime = dailyYieldTargetExptime;
    }

    @ThriftField(25)
    public Integer getDataVersion() {
        return dataVersion;
    }

    @ThriftField
    public void setDataVersion(Integer dataVersion) {
        this.dataVersion = dataVersion;
    }
}
