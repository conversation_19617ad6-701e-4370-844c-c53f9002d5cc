package com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenAttributeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.EffectiveStatusEnum;

import java.util.List;

/**
 * 食堂信息管理审批流程举值
 * <AUTHOR>
 * @date 2024/05/10
 * @email <EMAIL>
 */
public enum CanteenInfoAuditProgressEnum {

    /**
     * 新建食堂-无需审批
     */
    NO_AUDIT(0, "无需审批",
            CanteenInfoAuditTaskTypeEnum.SAVE_CANTEEN_AUDIT.getType(),
            Lists.newArrayList(
                    CanteenInfoAuditNodeTypeEnum.AUDIT_SUCCESS.getType())
    ),

    /**
     * 总部审批
     */
    HUMMING_BIRD_AUDIT(1, "总部审批-完成审批",
            CanteenInfoAuditTaskTypeEnum.SAVE_CANTEEN_AUDIT.getType(),
            Lists.newArrayList(
                    CanteenInfoAuditNodeTypeEnum.HUMMING_BIRD_AUDIT.getType(),
                    CanteenInfoAuditNodeTypeEnum.AUDIT_SUCCESS.getType())
    ),

    /**
     * 学校责任人审批
     */
    SKA_AUDIT(2, "学校责任人审批-完成审批",
            CanteenInfoAuditTaskTypeEnum.EDIT_CANTEEN_AUDIT.getType(),
            Lists.newArrayList(
                    CanteenInfoAuditNodeTypeEnum.SCHOOL_KA.getType(),
                    CanteenInfoAuditNodeTypeEnum.AUDIT_SUCCESS.getType())
    ),

    /**
     * 学校责任人审批-总部审批
     */
    SKA_HUMMING_BIRD_AUDIT(3, "学校责任人审批-总部审批-完成审批",
            CanteenInfoAuditTaskTypeEnum.EDIT_CANTEEN_AUDIT.getType(),
            Lists.newArrayList(
                    CanteenInfoAuditNodeTypeEnum.SCHOOL_KA.getType(),
                    CanteenInfoAuditNodeTypeEnum.HUMMING_BIRD_AUDIT.getType(),
                    CanteenInfoAuditNodeTypeEnum.AUDIT_SUCCESS.getType())
    ),

    /**
     * 食堂责任人上级审批-学校责任人审批-总部审批
     */
    BDM_SKA_HUMMING_BIRD_AUDIT(4, "食堂责任人上级审批-学校责任人审批-总部审批-完成审批",
            CanteenInfoAuditTaskTypeEnum.EDIT_CANTEEN_AUDIT.getType(),
            Lists.newArrayList(
                    CanteenInfoAuditNodeTypeEnum.BDM.getType(),
                    CanteenInfoAuditNodeTypeEnum.SCHOOL_KA.getType(),
                    CanteenInfoAuditNodeTypeEnum.HUMMING_BIRD_AUDIT.getType(),
                    CanteenInfoAuditNodeTypeEnum.AUDIT_SUCCESS.getType())
    );


    private int code;

    private String name;

    private int auditTaskType;

    private List<Integer> progress;


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAuditTaskType() {
        return auditTaskType;
    }

    public void setAuditTaskType(int auditTaskType) {
        this.auditTaskType = auditTaskType;
    }

    public List<Integer> getProgress() {
        return progress;
    }

    public void setProgress(List<Integer> progress) {
        this.progress = progress;
    }

    CanteenInfoAuditProgressEnum(int code, String name, int auditTaskType, List<Integer> progress) {
        this.code = code;
        this.name = name;
        this.auditTaskType = auditTaskType;
        this.progress = progress;
    }


    public static CanteenInfoAuditProgressEnum getByCode(int code) {
        for (CanteenInfoAuditProgressEnum progressEnum : values()) {
            if (progressEnum.getCode() == code) {
                return progressEnum;
            }
        }
        return null;
    }

    public static CanteenInfoAuditProgressEnum getByParam(int effectiveStatus, int canteenAttribute, boolean updateStallNum, boolean downBeyondThreshold) {
        if (effectiveStatus == EffectiveStatusEnum.UNEFFECTIVE.getType()) {
            // 未生效-新建食堂审批
            if (canteenAttribute == CanteenAttributeEnum.SINGLE_CANTEEN.getType()) {
                return NO_AUDIT;
            } else {
                return HUMMING_BIRD_AUDIT;
            }
        } else {
            // 生效-修改食堂审批
            if (canteenAttribute == CanteenAttributeEnum.SINGLE_CANTEEN.getType()) {
                // 单店食堂
                return SKA_AUDIT;
            } else {
                // 非单店食堂
                if (updateStallNum) {
                    // 修改了档口数量等
                    if (downBeyondThreshold) {
                        // 档口数下调超过阈值
                        return BDM_SKA_HUMMING_BIRD_AUDIT;
                    } else {
                        // 档口数下调未超过阈值
                        return SKA_HUMMING_BIRD_AUDIT;
                    }
                } else {
                    // 未修改档口数量等
                    return SKA_AUDIT;
                }
            }
        }
    }

}
