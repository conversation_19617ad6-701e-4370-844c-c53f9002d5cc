package com.sankuai.meituan.waimai.thrift.customer.domain.contract;


import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 详细每一个属性与Enum的对应关系详情
 * 见WmContractSectionEnum注解
 */
@Data
@AllArgsConstructor
public class WmTempletContractTypeBo {

    /**
     * 合同类型
     * 1:C1合同, 2:C2合同, 3:C3合同, 4:优惠申请书
     */
    public static final Integer TYPE_C1 = 1;
    public static final Integer TYPE_C2 = 2;
    public static final Integer TYPE_EXCLUSIVE = 4;
    public static final Integer TYPE_BUSINESS_CUSTOMER = 6;

    /**
     * 签约形式 1:纸质, 2:电子
     */
    public static final Integer SIGNTYPE_UNKNOWN = 0;
    public static final Integer SIGNTYPE_PAPER = 1;
    public static final Integer SIGNTYPE_E = 2;

    /**
     * 合作模式 1:直营, 2:代理, 3:资质属实商家承诺函, 4:门店推广技术服务合同,  6:企客配送服务合同, 7:配送服务合同, 8:配送站点合同,
     * * 9:广告年框合同, 10:品牌广告发布合同, 11:广告推广订单，12:拼好饭结算合同, 13:团餐补充协议, 14:打包(袋)服务合作协议,
     * * 15:医药保证金协议, 16:美食城承诺书,17:医药分单补充协议
     */
    public static final Integer COOPERATEMODE_UNKNOWN = 0;
    public static final Integer COOPERATEMODE_C1 = 1;
    public static final Integer COOPERATEMODE_C2 = 2;
    public static final Integer COOPERATEMODE_QUA_REAL_LETTER = 3;
    public static final Integer COOPERATEMODE_POI_PROMOTION_SERVICE = 4;
    public static final Integer COOPERATEMODE_BUSINESS_CUSTOMER = 6;
    public static final Integer COOPERATEMODE_DELIVERY_SERVICE_CONTRACT = 7;
    public static final Integer COOPERATEMODE_DELIVERY_SITE_CONTRACT = 8;
    public static final Integer COOPERATEMODE_AD_ANNUAL_FRAMEWORK_CONTRACT = 9;
    public static final Integer COOPERATEMODE_BRAND_AD_CONTRACT = 10;
    public static final Integer COOPERATEMODE_AD_ORDER = 11;
    public static final Integer COOPERATEMODE_PHF_CHARGE = 12;
    public static final Integer COOPERATEMODE_GROUP_MEAL = 13;
    public static final Integer COOPERATEMODE_BAG_SERVICE = 14;
    public static final Integer MED_DEPOSIT_AGREEMENT = 15;
    public static final Integer COOPERATEMODE_FOODCITY_STATEMENT = 16;
    public static final Integer COOPERATEMODE_MEDIC_ORDER_SPLIT = 17;

    public static final Integer COOPERATEMODE_INTERIM_SELF = 19;
    public static final Integer COOPERATEMODE_SUBJECT_CHANGE_SUPPLEMENT = 20;

    /**
     * 四轮履约补充协议
     */
    public static final Integer COOPERATEMODE_FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT = 21;

    /**
     * 极速达合作协议
     */
    public static final Integer COOPERATEMODE_SPEEDY_DELIVERY_COOPERATION_AGREEMENT = 22;

    /**
     * 到餐C1\C2合同
     */
    public static final Integer COOPERATEMODE_DAOCAN_SERVICE_C1_CONTRACT = 23;
    public static final Integer COOPERATEMODE_DAOCAN_SERVICE_C2_CONTRACT = 24;

    /**
     * 国补采购协议
     */
    public static final Integer NATIONAL_SUBSIDY_PURCHASE = 25;


    /**
     * 附属合同类型
     */
    private int type;
    /**
     * 签约形式
     */
    private int signType;
    /**
     * 合作模式(直营、代理商)
     */
    private int cooperateMode;

    /**
     * 根据枚举类，分析各属性值
     */
    public WmTempletContractTypeBo(WmTempletContractTypeEnum sectionEnum) {
        this.type = sectionEnum.getCode() % 100;
        this.signType = sectionEnum.getCode() / 100 % 10;
        this.cooperateMode = sectionEnum.getCode() / 1000;
    }

    /**
     * 根据code，分析各属性值
     */
    public WmTempletContractTypeBo(Integer typeCode) {
        this.type = typeCode % 100;
        this.signType = typeCode / 100 % 10;
        this.cooperateMode = typeCode / 1000;
    }

    /**
     * 根据各属性类型，找到对应的枚举类
     */
    public WmTempletContractTypeEnum getTypeEnum() {
        return WmTempletContractTypeEnum.getByCode(getTypeCode());
    }

    /**
     * 根据各属性类型，找到对应的枚举类
     */
    public Integer getTypeCode() {
        return cooperateMode * 1000 + signType * 100 + type;
    }

}
