package com.sankuai.meituan.waimai.thrift.customer.domain.sign;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * @description: 拼好饭履约服务费
 * @author: zhangyuanhao02
 * @create: 2024/12/10 15:10
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EcontractDeliveryPhfPerInfoBo {

    /**
     * key为收费类型
     */
    @FieldDoc(description = "履约服务费，key参考com.sankuai.meituan.waimai.thrift.customer.constant.CChargeTypeEnum")
    private Map<String, EcontractdeliveryPhfChargeInfoBo> chargePerInfo;

}
