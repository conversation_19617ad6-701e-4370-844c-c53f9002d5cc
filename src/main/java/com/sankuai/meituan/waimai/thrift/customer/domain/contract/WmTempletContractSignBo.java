package com.sankuai.meituan.waimai.thrift.customer.domain.contract;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

@ThriftStruct
public class WmTempletContractSignBo {

    /**
     * id
     */
    private long id;
    /**
     * 对应的模板合同的id
     */
    private int templetContractId;
    /**
     * 类型 A:甲方,B:乙方
     */
    private String signType;
    /**
     * 签约人id
     */
    private int signId;
    /**
     * 签约方
     */
    private String signName;
    /**
     * 签约人
     */
    private String signPeople;
    /**
     * 联系方式
     */
    private String signPhone;
    /**
     * 签约时间
     */
    private String signTime;

    @ThriftField(1)
    public long getId() {
        return id;
    }

    @ThriftField
    public void setId(long id) {
        this.id = id;
    }

    @ThriftField(2)
    public int getTempletContractId() {
        return templetContractId;
    }

    @ThriftField
    public void setTempletContractId(int templetContractId) {
        this.templetContractId = templetContractId;
    }

    @ThriftField(3)
    public String getSignType() {
        return signType;
    }

    @ThriftField
    public void setSignType(String signType) {
        this.signType = signType;
    }

    @ThriftField(4)
    public int getSignId() {
        return signId;
    }

    @ThriftField
    public void setSignId(int signId) {
        this.signId = signId;
    }

    @ThriftField(5)
    public String getSignName() {
        return signName;
    }

    @ThriftField
    public void setSignName(String signName) {
        this.signName = signName;
    }

    @ThriftField(6)
    public String getSignPeople() {
        return signPeople;
    }

    @ThriftField
    public void setSignPeople(String signPeople) {
        this.signPeople = signPeople;
    }

    @ThriftField(7)
    public String getSignPhone() {
        return signPhone;
    }

    @ThriftField
    public void setSignPhone(String signPhone) {
        this.signPhone = signPhone;
    }

    @ThriftField(8)
    public String getSignTime() {
        return signTime;
    }

    @ThriftField
    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }
}
