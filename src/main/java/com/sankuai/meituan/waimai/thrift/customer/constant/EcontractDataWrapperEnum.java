package com.sankuai.meituan.waimai.thrift.customer.constant;

import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.AgreementNameConstant;

public enum EcontractDataWrapperEnum {
    /**
     * 电子合同数据包装枚举
     */
    C1_CONTRACT(1, "c1合同"),
    C2_CONTRACT(2, "c2合同"),
    SETTLE(3, "结算"),
    DELIVERY(4, "配送信息"),
    DELIVERY_WHOLE_CITY(40001, "配送全城送"),
    DELIVERY_AGGREGATION(40002, "配送聚合配送"),
    DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE(40003, "配送企客远距离"),
    DELIVERY_AGENT_NEW(40004, "配送代理商新费率"),
    DELIVERY_PER_DISCOUNT(40005, "配送服务费折扣协议"),
    CANCEL_CONFIRM(5, "取消确认"),
    KP_CONFIRM(6, "kp变更确认"),
    BATCH_DELIVERY(7,"批量配送信息"),
    BATCH_DELIVERY_WHOLE_CITY(70001,"批量配送全城送"),
    BATCH_DELIVERY_AGGREGATION(70002,"批量配送聚合配送"),
    BATCH_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE(70003,"批量配送企客远距离"),
    BATCH_DELIVERY_AGENT_NEW(70004,"批量配送代理商新费率"),
    BATCH_DELIVERY_PER_DISCOUNT(70005,"批量配送服务费折扣协议"),
    BATCH_POI(8,"批量门店生成"),
    ADDED_SERVICE_DISCOUNT(9,"增值服务费优惠"),
    QUA_REAL_LETTER(10,"资质属实商家承诺函"),
    SHANGOU_REBATE(11,"闪购佣金返点"),
    POI_PROMOTION_SERVICE(12,"门店推广技术服务合同"),
    OPERATION_MANAGER_KP_CONFIRM(13,"运营经理KP确认"),
    DELIVERY_SERVICE_CONTRACT(14,"配送服务合同"),
    DELIVERY_SITE_CONTRACT(15,"配送站点合同"),
    AD_ANNUAL_FRAMEWORK_CONTRACT(16, "广告年框合同"),
    BRAND_AD_CONTRACT(17, "品牌广告发布合同"),
    AD_ORDER(18, "广告推广订单"),
    BUSINESS_CUSTOMER_E_CONTRACT(19, "企客配送服务合同"),
    PHF_CHARGE(20, "拼好饭结算合同"),
    GROUP_MEAL(21, "企业订餐补充协议"),
    WM_POI_BASE_TAG_SIGN(22, "品类调整"),
    BAG_SERVICE(23, "打包(袋)服务合作协议"),
    MED_DEPOSIT(24, "医药保证金协议"),
    FOODCITY_STATEMENT(25, "美食城承诺书"),
    FOODCITY_POI_TABLE(26, "客户绑定门店"),
    AGENT_SIGNER_AUTH(27, "代理签约人授权"),
    INTERIM_SELF(28, "配送产品临时调整补充协议"),
    MEDIC_ORDER_SPLIT(29, "医药分单补充协议"),
    SUBJECT_CHANGE_SUPPLEMENT(30, "主体变更补充协议"),

    BATCH_POI_BASEINFO(31,"批量门店基本信息生成"),
    AGENT_SQS_STANDARD(32, "代理神抢手收费标准"),

    FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT(33, "四轮履约补充协议"),
    SPEEDY_DELIVERY_COOPERATION_AGREEMENT(34, "极速达模式合作协议"),
    DRONE_DELIVERY(35, "无人机配送协议-佣金协议"),
    DRONE_PERFORMANCE_SERVICE_CONTRACT(36, "无人机配送服务费"),
    FRUIT_TOGETHER_DELIVERY(37, "班次送收费协议-佣金协议"),
    FRUIT_TOGETHER_PERFORMANCE_SERVICE_CONTRACT(38, "班次送配送服务费"),
    DAOCAN_SERVICE_C1_CONTRACT(39, "美团与客户合同（到餐）"),
    DAOCAN_SERVICE_C2_CONTRACT(40, "合作商与客户合同（到餐）"),
    VIP_CARD(48, "会员卡-佣金协议"),

    // 拼好饭相关
    PHF_VIRTUAL(41, "拼好送基础版"),
    PHF_VIRTUAL_SELF(42, "拼好送基础版-自配"),
    PHF_FORMAL_TECH(43, "拼好送正式版-入驻合同"),
    PHF_FORMAL_PER(44, "拼好送正式版-配送合同"),
    PHF_FORMAL_SELF(45, "拼好送正式版-自配"),
    JHS(46, "聚好送"),
    PHF_AGENT_SPECIAL_PRICE(47, "代理商保底金额"),

    NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY_PERFORMANCE_SERVICE_FEE(49, "国补—经销商配送服务费"),
    NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY_WHOLE_CITY(50, "国补—经销商全城送协议"),
    NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY_LONG_DISTANCE(51, "国补—经销商企客远距离协议"),
    NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY_PERFORMANCE_SERVICE_FEE(52, "国补—总部配送服务费"),
    NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY_WHOLE_CITY(53, "国补—总部全城送协议"),
    NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY_LONG_DISTANCE(54, "国补—总部企客远距离协议"),

    NATIONAL_SUBSIDY_PURCHASE(55, AgreementNameConstant.NATIONAL_SUBSIDY_PURCHASE),

    COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT(99, "配置化框架合同类型"),
    ;

    private int code;
    private String msg;

    EcontractDataWrapperEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
