package com.sankuai.meituan.waimai.thrift.customer.constant.sc;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @description: 订单转化率≥60%完成时长枚举
 * @author: CatPaw
 **/
public enum Order60TargetCompletionDurationEnum implements ScCommonEnum {
    /**
     * 小于15天
     */
    LESS_THAN_15((byte)1, "＜15天"),
    /**
     * 15天到30天
     */
    BETWEEN_15_AND_30((byte)2, "15（含）-30天"),
    /**
     * 30天到60天
     */
    BETWEEN_30_AND_60((byte)3, "30（含）-60天"),
    /**
     * 60天到90天
     */
    BETWEEN_60_AND_90((byte)4, "60（含）-90天"),
    /**
     * 大于等于90天
     */
    MORE_THAN_90((byte)5, "≥90天");

    private byte type;
    private String name;

    Order60TargetCompletionDurationEnum(byte type, String name) {
        this.type = type;
        this.name = name;
    }

    private static Map<Byte, Order60TargetCompletionDurationEnum> enumByTypeMap = Maps.newHashMap();
    private static Map<String, Order60TargetCompletionDurationEnum> enumByNameMap = Maps.newHashMap();

    static {
        for (Order60TargetCompletionDurationEnum order60TargetCompletionDurationEnum : values()) {
            enumByTypeMap.put(order60TargetCompletionDurationEnum.getType(), order60TargetCompletionDurationEnum);
            enumByNameMap.put(order60TargetCompletionDurationEnum.getName(), order60TargetCompletionDurationEnum);
        }
    }

    public static Order60TargetCompletionDurationEnum getByType(int type) {
        return enumByTypeMap.get((byte)type);
    }

    public static Order60TargetCompletionDurationEnum getByName(String name) {
        return enumByNameMap.get(name);
    }

    @Override
    public byte getType() {
        return this.type;
    }

    @Override
    public void setType(byte type) {
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
    }
}