package com.sankuai.meituan.waimai.thrift.customer.domain.sc;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.List;

@ThriftStruct
public class WmScCanteenPoiAttributeRefreshResultBO {

    /**
     * 食堂主键ID
     */
    private Integer canteenPrimaryId;

    /**
     * 刷新时新增的门店
     */
    private List<Long> newWmPoiId;

    /**
     * 刷新时更新的门店
     */
    private List<Long> updateWmPoiId;

    /**
     * 刷新时删除的门店
     */
    private List<Long> deleteWmPoiId;


    @ThriftField(1)
    public Integer getCanteenPrimaryId() {
        return canteenPrimaryId;
    }

    @ThriftField
    public void setCanteenPrimaryId(Integer canteenPrimaryId) {
        this.canteenPrimaryId = canteenPrimaryId;
    }

    @ThriftField(2)
    public List<Long> getNewWmPoiId() {
        return newWmPoiId;
    }

    @ThriftField
    public void setNewWmPoiId(List<Long> newWmPoiId) {
        this.newWmPoiId = newWmPoiId;
    }

    @ThriftField(3)
    public List<Long> getUpdateWmPoiId() {
        return updateWmPoiId;
    }

    @ThriftField
    public void setUpdateWmPoiId(List<Long> updateWmPoiId) {
        this.updateWmPoiId = updateWmPoiId;
    }

    @ThriftField(4)
    public List<Long> getDeleteWmPoiId() {
        return deleteWmPoiId;
    }

    @ThriftField
    public void setDeleteWmPoiId(List<Long> deleteWmPoiId) {
        this.deleteWmPoiId = deleteWmPoiId;
    }
}
