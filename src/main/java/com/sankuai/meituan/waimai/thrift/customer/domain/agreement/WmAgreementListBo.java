package com.sankuai.meituan.waimai.thrift.customer.domain.agreement;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

@ThriftStruct
public class WmAgreementListBo {

    private long id;

    private long accountId;

    private long wmPoiId;

    private int agreementId;

    private int agreementType;

    private int status;

    private int ctime;

    private int utime;

    private byte valid;

    @ThriftField(1)
    public long getId() {
        return id;
    }
    @ThriftField
    public void setId(long id) {
        this.id = id;
    }
    @ThriftField(2)
    public long getAccountId() {
        return accountId;
    }
    @ThriftField
    public void setAccountId(long accountId) {
        this.accountId = accountId;
    }
    @ThriftField(3)
    public long getWmPoiId() {
        return wmPoiId;
    }
    @ThriftField
    public void setWmPoiId(long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }
    @ThriftField(4)
    public int getAgreementId() {
        return agreementId;
    }
    @ThriftField
    public void setAgreementId(int agreementId) {
        this.agreementId = agreementId;
    }
    @ThriftField(5)
    public int getAgreementType() {
        return agreementType;
    }
    @ThriftField
    public void setAgreementType(int agreementType) {
        this.agreementType = agreementType;
    }
    @ThriftField(6)
    public int getStatus() {
        return status;
    }
    @ThriftField
    public void setStatus(int status) {
        this.status = status;
    }
    @ThriftField(7)
    public int getCtime() {
        return ctime;
    }
    @ThriftField
    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
    @ThriftField(8)
    public int getUtime() {
        return utime;
    }
    @ThriftField
    public void setUtime(int utime) {
        this.utime = utime;
    }
    @ThriftField(9)
    public byte getValid() {
        return valid;
    }
    @ThriftField
    public void setValid(byte valid) {
        this.valid = valid;
    }
}
