package com.sankuai.meituan.waimai.domain;

import com.sankuai.meituan.waimai.thrift.domain.WmAgentPoiMsgSendRecord;

import java.util.List;

/**
 * desc:
 * author: kou<PERSON><PERSON><PERSON>
 * date:   17/1/5
 * e-mail: kou<PERSON><PERSON><PERSON>@meituan.com
 */
public class ProcessData {

    // 所有已发送的门店列表
    private List<WmAgentPoiMsgSendRecord> allPoiMsgRecordList;

    // 需要处理的代理商列表
    private List<Integer> agentIdList;

    public List<WmAgentPoiMsgSendRecord> getAllPoiMsgRecordList() {
        return allPoiMsgRecordList;
    }

    public void setAllPoiMsgRecordList(List<WmAgentPoiMsgSendRecord> allPoiMsgRecordList) {
        this.allPoiMsgRecordList = allPoiMsgRecordList;
    }

    public List<Integer> getAgentIdList() {
        return agentIdList;
    }

    public void setAgentIdList(List<Integer> agentIdList) {
        this.agentIdList = agentIdList;
    }
}
