package com.sankuai.meituan.waimai.vo.sc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: scm
 * @description: 查询结果
 * @author: jianghuimin02
 * @create: 2020-04-16 15:23
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CanteenListVo {
    /**
     * 物理主键
     */
    private int id;
    /**
     * 食堂ID
     */
    private int canteenId;
    /**
     * 食堂名称
     */
    private String canteenName;
    /**
     * 食堂类型：校园食堂：1。预留字段：未来会扩展美食城：2、白领食堂：3
     */
    private int canteenType;
    /**
     * 类型描述
     */
    private String typeDesc;
    /**
     * 食堂属性：学校直营：1、承包商承包：2
     */
    private int canteenAttribute;
    /**
     * 属性描述
     */
    private String attributeDesc;
    /**
     * 供给分级：SKR直营：1、KR直营：2、食堂直营：3、SKR承包：4、KR承包：5、食堂承包：6
     */
    private int grade;
    /**
     * 分级描述
     */
    private String gradeDesc;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 食堂承包商客户
     */
    private String contractorName;
    /**
     * 食堂经理
     */
    private String manager;
    /**
     * 经理电话
     */
    private String managerPhone;
    /**
     * 合作状态
     * {@link CanteenStatusEnum}
     */
    private Integer canteenStatus;
    /**
     * 合作状态描述
     */
    private String statusDesc;
    /**
     * 档口数量
     */
    private int stallNum;
    /**
     * 门店数量
     */
    private int storeNum;
    /**
     * 食堂责任人mis
     */
    private String responsiblePerson;
    /**
     * 食堂责任人姓名
     */
    private String responsiblePersonName;
    /**
     * 所属学校ID
     */
    private int schoolId;
    /**
     * 承包商ID
     */
    private int contractorId;
    /**
     * 平台客户ID
     */
    private Long mtCustomerId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户ID
     */
    private int userId;
    /**
     * 更新时间
     */
    private int utime;
    /**
     * 创建时间
     */
    private int ctime;
    /**
     * 创建时间，如"2022-10-14 15:24:56"
     */
    private String ctimeStr;
    /**
     * 额外字段json-map，使用方式:
     * key新增属性：value新增属性对应的值
     */
    private String ext;
    /**
     * 是否灰度个别按钮
     */
    private int gray;
    /**
     * 审核明细状态
     */
    private Integer auditDetailStatus;
    /**
     * 审核明细状态描述
     */
    private String auditDetailStatusDesc;
    /**
     * 生效状态
     */
    private Integer effective;
    /**
     * 生效状态描述
     */
    private String effectiveDesc;

    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 审核状态描述
     */
    private String auditStatusDesc;
    /**
     * 学校责任人
     */
    private String schoolResponsiblePerson;
    /**
     * 当前用户mis
     */
    private String curMis;
    /**
     * 线索Id
     */
    private String wdcClueId;
    /**
     * 线下营业档口数量
     */
    private Integer offlineBizStallNum;
    /**
     * 可上线档口数量
     */
    private Integer preOnlineStallNum;
    /**
     * 修改按钮权限
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer editButtonAuth;
    /**
     * 操作记录按钮权限
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer logButtonAuth;
    /**
     * 分配责任人按钮权限
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer assignOwnerButtonAuth;
    /**
     * 取消合作按钮权限
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer uncooperateButtonAuth;
    /**
     * 删除按钮权限
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer deleteButtonAuth;

    /**
     * 删除按钮权限原因
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private String deleteButtonAuthReason;

    private Integer aorType;

    /**
     * 食堂品类，餐饮:1、闪购:2、寝室便利店:3
     */
    private Integer category;

    /**
     * 食堂品类描述
     */
    private String categoryDesc;
}
