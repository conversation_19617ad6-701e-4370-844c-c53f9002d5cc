package com.sankuai.meituan.waimai.vo.sc.delivery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 学校交付按钮权限信息VO
 * <AUTHOR>
 * @date 2024/02/27
 * @email <EMAIL>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmSchoolDeliveryButtonAuthVO {
    /**
     * 提交审批按钮
     */
    private Integer commitButtonAuth;
    /**
     * 暂存按钮
     */
    private Integer tempSaveButtonAuth;
    /**
     * 撤回审批按钮
     */
    private Integer cancelAuditButtonAuth;
    /**
     * 查看审批信息按钮
     */
    private Integer viewAuditButtonAuth;
    /**
     * 提交审批&暂存按钮置灰提示
     */
    private String commitAndTempSaveButtonTips;
    /**
     * 编辑按钮(仅交付跟进用)
     */
    private Integer editButtonAuth;
}
