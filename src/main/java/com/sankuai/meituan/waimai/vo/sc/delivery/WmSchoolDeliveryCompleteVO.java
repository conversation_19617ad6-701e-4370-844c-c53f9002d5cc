package com.sankuai.meituan.waimai.vo.sc.delivery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolLifecycleEnum;
import lombok.Data;

/**
 * 学校交付完成信息VO
 * <AUTHOR>
 * @date 2024/02/10
 * @email <EMAIL>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmSchoolDeliveryCompleteVO {
    /**
     * 交付编号ID
     */
    private Integer deliveryId;
    /**
     * 交付发起人UID
     */
    private Integer deliveryInitiatorUid;
    /**
     * 交付发起人姓名
     */
    private String deliveryInitiatorName;
    /**
     * 交付发起人MIS
     */
    private String deliveryInitiatorMis;
    /**
     * 客户成功经理(交付负责人)UID
     */
    private Integer csmUid;
    /**
     * 客户成功经理(交付负责人)姓名
     */
    private String csmName;
    /**
     * 客户成功经理(交付负责人)MIS
     */
    private String csmMis;
    /**
     * 交付发起日期
     */
    private Integer deliveryInitiationTime;
    /**
     * 交付完成日期
     */
    private Integer  deliveryCompleteTime;
    /**
     * 合作时间-开始
     */
    private Integer agreementTimeStart;
    /**
     * 合作时间-结束
     */
    private Integer agreementTimeEnd;
    /**
     * 生命周期
     * {@link SchoolLifecycleEnum}
     */
    private Integer lifeCycle;
    /**
     * 生命周期描述
     */
    private String lifeCycleDesc;

}
