package com.sankuai.meituan.waimai.vo.sc.delivery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.meituan.scmbrand.thrift.domain.apply.BrandApplyMetadataTabResultDTO;
import lombok.Data;

import java.util.List;

/**
 * 学校交付生效页面信息VO
 * <AUTHOR>
 * @date 2024/02/27
 * @email <EMAIL>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmSchoolDeliveryEffectiveInfoVO {
    /**
     * 元数据信息
     */
    private List<BrandApplyMetadataTabResultDTO> tabs;
    /**
     * 交付状态信息(生效信息、审批信息等)
     */
    private WmSchoolDeliveryStatusVO deliveryStatus;
    /**
     * 学校交付按钮权限
     */
    private WmSchoolDeliveryButtonAuthVO buttonAuth;
    /**
     * 特殊字段(单独处理逻辑)
     */
    private WmSchoolDeliverySpecialDataVO specialData;
    /**
     * 元数据表单版本号
     */
    private Integer templateVersion;
}
