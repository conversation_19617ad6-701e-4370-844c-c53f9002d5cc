package com.sankuai.meituan.waimai.vo.sc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmCanteenPoiSimpleStreamVO {

    /**
     * 审批任务类型
     * 枚举值对应为CanteenPoiTaskTypeEnum
     */
    private Integer auditTaskType;

    /**
     * 审批任务系统ID
     */
    private String auditTaskSystemId;


    /**
     * 审批任务类型描述
     * 换绑任务
     * com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiTaskTypeEnum
     * */
    private String auditTaskTypeDesc;


    /**
     * 当前节点code
     */
    private Integer currentNodeCode;

    /**
     * 当前审批状态
     * 枚举值对应为CanteenPoiAuditStatusV2Enum
     */
    private Integer currentAuditStatus;

    /**
     * 审批流各节点信息
     */
    private List<WmCanteenPoiSimpleProgressVO> auditProgressList;
}
