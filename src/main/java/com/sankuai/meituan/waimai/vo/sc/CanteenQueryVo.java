package com.sankuai.meituan.waimai.vo.sc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 食堂查询参数
 * @program: scm
 * @author: jianghuimin02
 * @date: 2020-04-16 15:58
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CanteenQueryVo {
    /**
     * 食堂ID
     */
    private int canteenId;
    /**
     * 食堂名称
     */
    private String canteenName;
    /**
     * 食堂属性。学校直营：1、承包商承包：2。
     */
    private int canteenAttribute;
    /**
     * 属性描述
     */
    private String attributeDesc;
    /**
     * 供给分级。SKR直营：1、KR直营：2、食堂直营：3、SKR承包：4、KR承包：5、食堂承包：6
     */
    private int grade;
    /**
     * 分级描述
     */
    private String gradeDesc;
    /**
     * 食堂责任人
     */
    private String responsiblePerson;
    /**
     * 学校名称
     */
    @Deprecated
    private String schoolName;
    /**
     * 学校ID
     */
    private int schoolId;
    /**
     * 食堂承包商名称
     */
    @Deprecated
    private String clientName;
    /**
     * 承包商ID
     */
    private int contractorId;
    /**
     * 页面大小
     */
    private Integer pageSize;
    /**
     * 页面号
     */
    private Integer pageNo;
    /**
     * 食堂状态。已合作:1、未合作:0
     */
    private Integer canteenStatus;
    /**
     * 有效性。0-未生效，1-生效
     */
    private Integer effective;
    /**
     * 门店ID
     */
    private Long wmPoiId;
    /**
     * 合作档口数量-左值
     */
    private Integer storeNumStart;
    /**
     * 合作档口数量-右值
     */
    private Integer storeNumEnd;
    /**
     * 蜂窝类型
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolAorTypeEnum}
     */
    private Integer aorType;

    /**
     * 食堂品类，餐饮:1、闪购:2、寝室便利店:3
     */
    private int category;
}
