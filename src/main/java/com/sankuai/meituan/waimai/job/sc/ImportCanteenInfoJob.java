package com.sankuai.meituan.waimai.job.sc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.meituan.waimai.bo.CanteenImportCreatorInfoBo;
import com.sankuai.meituan.waimai.bo.CanteenImportInfoBo;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_IS_VALID;

/**
 * @description: 批量导入食堂信息
 * @author: chenyihao04
 * @create: 2024-11-14 17:29
 */
@Slf4j
@Service
public class ImportCanteenInfoJob {

    public static final int MAXSTALL = 4;
    public static final int MAXNAME = 100;
    public static final int MAXKPNAME = 50;
    public static final int MAXPHONE = 11;

    @Resource
    private WmCanteenThriftService canteenThriftService;

    public void execute() {
        String canteenImportInfo = MccConfig.getCanteenImportInfo();
        String creatorInfo = MccConfig.getAgentImportCreatorInfo();

        List<CanteenImportInfoBo> canteenInfoList = JSON.parseObject(canteenImportInfo, new TypeReference<List<CanteenImportInfoBo>>() {
        });

        List<CanteenImportCreatorInfoBo> creatorInfoList = JSON.parseObject(creatorInfo, new TypeReference<List<CanteenImportCreatorInfoBo>>() {
        });
        CanteenImportCreatorInfoBo creatorInfoBo = creatorInfoList.get(0);

        Map<Integer, Integer> schoolIdToCanteenPrimaryIdMap = new HashMap<>();
        for (CanteenImportInfoBo canteenImportInfoBo : canteenInfoList) {
            try {
                checkCanteenFormVo(canteenImportInfoBo);
                CanteenBo canteenBo = new CanteenBo();
                BeanUtils.copyProperties(canteenImportInfoBo, canteenBo);
                canteenBo.setStallNum(Integer.parseInt(canteenImportInfoBo.getStallNum()));
                canteenBo.setValid(SC_IS_VALID);
                canteenBo.setCanteenStatus((int) CanteenStatusEnum.UNCOOPERATE.getType());
                canteenBo.setCanteenVideo(MccConfig.getCanteenImportVideo());
                log.info("ImportCanteenInfoJob saveCanteenTicket param: {}", JSON.toJSONString(canteenBo));
                int canteenPrimaryId = canteenThriftService.saveCanteenTicket(canteenBo, creatorInfoBo.getUserId(), creatorInfoBo.getUserName());
                log.info("ImportCanteenInfoJob saveCanteenTicket param: {}, result: {}", JSON.toJSONString(canteenBo), canteenPrimaryId);
                schoolIdToCanteenPrimaryIdMap.put(canteenImportInfoBo.getSchoolId(), canteenPrimaryId);
            } catch (Exception e) {
                schoolIdToCanteenPrimaryIdMap.put(canteenImportInfoBo.getSchoolId(), -1);
                log.error("ImportCanteenInfoJob error, 执行异常, param: {}", JSON.toJSONString(canteenImportInfoBo));
            }
        }

        List<Integer> failSchoolIdList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : schoolIdToCanteenPrimaryIdMap.entrySet()) {
            if (entry.getValue() < 0) {
                failSchoolIdList.add(entry.getKey());
            }
        }
        log.info("ImportCanteenInfoJob 执行完成，此次共执行{}个食堂导入, 其中有{}个导入失败, 对应学校id分别为: {}", canteenInfoList.size(), failSchoolIdList.size(), JSON.toJSONString(failSchoolIdList));
        log.info("ImportCanteenInfoJob 执行完成, 执行结果: {}", JSON.toJSONString(schoolIdToCanteenPrimaryIdMap));
    }


    private void checkCanteenFormVo(CanteenImportInfoBo canteenFormVo) throws WmSchCantException, TException {
        if (canteenFormVo == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "参数为空");
        }
        // 校验食堂档口数量、线下营业档口数量和可上线档口数量
        checkCanteenStallNum(canteenFormVo);

        if (StringUtil.isBlank(canteenFormVo.getCanteenName())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂名称为空");
        }

        if (canteenFormVo.getCanteenName().length() > MAXNAME) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "食堂名称超过" + MAXNAME + "字");
        }

        if (canteenFormVo.getCanteenType() == 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂类型为空");
        }

        if (canteenFormVo.getCanteenAttribute() == 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂属性为空");
        }

        if (canteenFormVo.getSchoolId() <= 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校信息为空");
        }

        if (canteenFormVo.getStallNum().length() > MAXSTALL) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "档口数量最多可录入" + MAXSTALL + "位数字");
        }

        if (StringUtil.isNotBlank(canteenFormVo.getManager()) && canteenFormVo.getManager().length() > MAXKPNAME) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "食堂经理姓名不可超过" + MAXKPNAME + "个汉字");
        }

        if (StringUtil.isNotBlank(canteenFormVo.getManagerPhone()) && canteenFormVo.getManagerPhone().length() != MAXPHONE) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "联系电话不是" + MAXPHONE + "位，不可保存");
        }
    }

    private void checkCanteenStallNum(CanteenImportInfoBo canteenFormVo) throws WmSchCantException, TException {

        if (canteenFormVo.getOfflineBizStallNum() == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "线下营业档口数量为必填项");
        }

        if (canteenFormVo.getPreOnlineStallNum() == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "可上线档口数量为必填项");
        }

        if (canteenFormVo.getOfflineBizStallNum() < 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "线下营业档口数量必须是非负整数");
        }

        if (StringUtil.isBlank(canteenFormVo.getStallNum())
                || canteenFormVo.getStallNum().contains("-")
                || canteenFormVo.getStallNum().contains(".")) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "档口数量需输入非负整数");
        }

        if (canteenFormVo.getOfflineBizStallNum() > Integer.parseInt(canteenFormVo.getStallNum())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "线下营业档口数量应小于等于档口数量");
        }

        if (canteenFormVo.getPreOnlineStallNum() < 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "可上线档口数量不能为负数");
        }

        if (canteenFormVo.getPreOnlineStallNum() > canteenFormVo.getOfflineBizStallNum()) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "可上线档口数量应小于等于线下营业档口数量");
        }
    }
}