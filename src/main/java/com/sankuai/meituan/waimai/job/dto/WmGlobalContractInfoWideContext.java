package com.sankuai.meituan.waimai.job.dto;

import com.sankuai.meituan.waimai.bo.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @description: 全局合同信息上下文
 * @author: liuyunjie05
 * @create: 2023/12/7 17:58
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WmGlobalContractInfoWideContext {

    /**
     * wm_econtract_global_info 同一个recordKey
     */
    private List<EcontractGlobalInfoBo> econtractGlobalInfoBoList;

    /**
     * wm_econtract_record
     */
    private EcontractRecordBo econtractRecordBo;

    private EcontractTaskBo econtractTaskBo;

    /**
     * wm_econtract_sign_batch
     */
    private EcontractSignBatchBo econtractSignBatchBo;

    private WmEcontractSignPackBo econtractSignPackBo;


    /**
     * wm_econtract_sign_task key: applyType 即: EcontractGlobalInfoBo的pdfType
     */
    private Map<String, EcontractSignTaskBo> econtractSignTaskBoMap;


    /**
     * key: wm_contract_version的transaction_id，即wm_econtract_sign_task的id
     */
    private Map<Long, WmContractVersionBo> wmContractVersionBoMap;

    /**
     * key: id, 即wm_contract_version的wm_contract_id
     */
    private Map<Long, WmTemplateContractBo> wmTemplateContractBoMap;

    /**
     * key: wmTempletContractId, 即WmTemplateContractBo的id
     */
    private Map<Integer, List<WmTemplateContractSignBo>> templateContractSignMap;

    /**
     * key: sign_task_id
     */
    private Map<Long, WmEcontractSignManualBatchBo> manualBatchBoMap;

    /**
     * key: sign_batch_id  value: sign_task_id的List
     */
    private Map<Long, List<Long>> manualBatchTaskIdMap;

}
