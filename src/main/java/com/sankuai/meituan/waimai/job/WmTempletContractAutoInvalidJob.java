package com.sankuai.meituan.waimai.job;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.netty.utils.SleepUtils;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractBasicBo;
import com.sankuai.meituan.waimai.c2contract.service.WmC2ContractThriftService;
import com.sankuai.meituan.waimai.contract.thrift.vo.BooleanResult;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.templetcontract.thrift.domain.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.templetcontract.thrift.service.WmTempletContractBasicAuditedThriftService;
import com.sankuai.meituan.waimai.templetcontract.thrift.service.WmTempletContractThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmContract;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmContractThriftService;
import com.sankuai.meituan.waimai.util.CatUtil;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.meituan.waimai.util.DatesUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.templetcontract.constant.WmTempletContractTypeEnum.isC1ExclusiveAgreement;
import static com.sankuai.meituan.waimai.templetcontract.constant.WmTempletContractTypeEnum.isC2ExclusiveAgreement;

@Service
public class WmTempletContractAutoInvalidJob implements CommonJob {
    private static final Logger log = LoggerFactory.getLogger(WmTempletContractAutoInvalidJob.class);

    @Autowired
    private WmTempletContractBasicAuditedThriftService.Iface wmTempletContractBasicAuditedThriftService;

    @Autowired
    private WmTempletContractThriftService.Iface wmTempletContractThriftService;

    @Autowired
    private WmContractThriftService.Iface wmContractThriftService;

    @Autowired
    private WmC2ContractThriftService.Iface wmC2ContractThriftService;

    @Autowired
    private WmEmployService.Iface wmEmployService;
    int opUid = 0;
    String opName = "模板合同自动失效定时任务";

    String templte = "【优惠申请书已经到期】合同编号%s 优惠申请书已经到期，请及时与商家沟通是否续签。";

    String sender = "<EMAIL>";

    int pageSize = 200;

    @Override
    @Crane("contract.WmTempletContractAutoInvalidJob.job")
    public void execute() {
        Long start = System.currentTimeMillis();
        String today = DatesUtil.Date2String(new Date());
        int todayUnix = DateUtil.day2Unixtime(today);
        int successCount = 0;
        int allCount = 0;
        List<WmTempletContractBasicBo> contractBasicBos;
        int startPageNum = 1;
        while ((contractBasicBos = getInvalidBasicList(todayUnix, startPageNum++)) != null) {
            log.info(opName + "获取到有{}个过期合同需要自动失效", allCount = contractBasicBos.size());
            if (contractBasicBos.size() == 0) {
                break;
            }
            Map<Integer, ContractMisIdInfo> c1IdAndOwnerEmailMap = getC1ContractEmailMap(contractBasicBos);
            Map<Integer, ContractMisIdInfo> c2IdAndOwnerEmailMap = getC2ContractEmailMap(contractBasicBos);

            for (WmTempletContractBasicBo basicBo : contractBasicBos) {
                if (invalidContract(basicBo).value) {
                    successCount++;
                    noticeOwner(c1IdAndOwnerEmailMap, c2IdAndOwnerEmailMap, basicBo);
                }
            }
            SleepUtils.sleepHalfSecond();
        }
        log.info(opName + "{}个过期合同,自动失效成功。 总数：{}", successCount, allCount);
        log.info(opName + "执行完成，耗时：{}", System.currentTimeMillis() - start);
        CatUtil.logWmTempletContractAutoInvalidJobSuccess();

    }

    private void noticeOwner(Map<Integer, ContractMisIdInfo> c1IdAndOwnerEmailMap, Map<Integer,
            ContractMisIdInfo> c2IdAndOwnerEmailMap, WmTempletContractBasicBo basicBo) {
        if (isC1ExclusiveAgreement(basicBo.getType()) && c1IdAndOwnerEmailMap.containsKey(basicBo.getParentId())) {
            ContractMisIdInfo contractMisIdInfo = c1IdAndOwnerEmailMap.get(basicBo.getParentId());
            pushDX(contractMisIdInfo);
        }
        if (isC2ExclusiveAgreement(basicBo.getType()) && c2IdAndOwnerEmailMap.containsKey(basicBo.getParentId())) {
            ContractMisIdInfo contractMisIdInfo = c2IdAndOwnerEmailMap.get(basicBo.getParentId());
            pushDX(contractMisIdInfo);
        }
    }

    private void pushDX(ContractMisIdInfo contractMisIdInfo) {
        if (contractMisIdInfo.misId != null) {
            String msg = String.format(templte, contractMisIdInfo.contractNum);
            log.info("sender={}, msg={}, receiver={}", sender, msg, contractMisIdInfo.misId);
            DaxiangUtilV2.push(msg, contractMisIdInfo.misId);
        }
    }

    private Map<Integer, ContractMisIdInfo> getC1ContractEmailMap(List<WmTempletContractBasicBo> contractBasicBos) {
        List<Integer> c1ParentIds = getC1ParentIds(contractBasicBos);
        List<WmContract> contractList;
        Map<Integer, ContractMisIdInfo> idAndOwnerEmailMap = Maps.newHashMap();
        try {
            contractList = wmContractThriftService.getContractListByIds(c1ParentIds, opUid, opName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return idAndOwnerEmailMap;
        }
        for (WmContract contract : Objects
                .firstNonNull(contractList, Lists.<WmContract>newArrayList())) {
            WmEmploy byId;
            try {
                byId = wmEmployService.getById(contract.getOwner_uid());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                continue;
            }
            String email = byId == null ? null : byId.getMisId();
            idAndOwnerEmailMap.put(contract.getId(), new ContractMisIdInfo(email, contract.getContract_number()));
        }
        return idAndOwnerEmailMap;
    }

    private Map<Integer, ContractMisIdInfo> getC2ContractEmailMap(List<WmTempletContractBasicBo> contractBasicBos) {
        List<Integer> c2ParentIds = getC2ParentIds(contractBasicBos);
        List<WmC2ContractBasicBo> contractList;
        Map<Integer, ContractMisIdInfo> idAndOwnerEmailMap = Maps.newHashMap();
        try {
            contractList = wmC2ContractThriftService.batchGetByC2ContractIdList(c2ParentIds);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return idAndOwnerEmailMap;
        }
        for (WmC2ContractBasicBo contract : Objects
                .firstNonNull(contractList, Lists.<WmC2ContractBasicBo>newArrayList())) {
            WmEmploy byId;
            try {
                byId = wmEmployService.getById(contract.getOwnerUid());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                continue;
            }
            String email = byId == null ? null : byId.getMisId();
            idAndOwnerEmailMap.put(contract.getId(), new ContractMisIdInfo(email, contract.getContractNum()));
        }
        return idAndOwnerEmailMap;
    }

    private List<Integer> getC1ParentIds(List<WmTempletContractBasicBo> contractBasicBos) {
        List<Integer> parentIdList = Lists.newArrayList();
        for (WmTempletContractBasicBo bo : contractBasicBos) {
            if (isC1ExclusiveAgreement(bo.getType())) {
                parentIdList.add(bo.getParentId());
            }
        }
        return parentIdList;
    }

    private List<Integer> getC2ParentIds(List<WmTempletContractBasicBo> contractBasicBos) {
        List<Integer> parentIdList = Lists.newArrayList();
        for (WmTempletContractBasicBo bo : contractBasicBos) {
            if (isC2ExclusiveAgreement(bo.getType())) {
                parentIdList.add(bo.getParentId());
            }
        }
        return parentIdList;
    }

    private BooleanResult invalidContract(WmTempletContractBasicBo basicBo) {
        log.info("优惠申请书到期自动失效，templetId:{}", basicBo.getTempletContractId());
//        BooleanResult booleanResult = new BooleanResult();
//        try {
//            booleanResult = wmTempletContractThriftService.invalidContract(basicBo.getTempletContractId(), opUid, opName);
//        } catch (TException | WmServerException e) {
//            log.error("合同id:{} 自动失效失败", basicBo.getTempletContractId(), e);
//        }
//        return booleanResult;
        return new BooleanResult(true);
    }

    private List<WmTempletContractBasicBo> getInvalidBasicList(int today, int startPageNum) {
        try {
            List<WmTempletContractBasicBo> dueDateAfter = wmTempletContractBasicAuditedThriftService
                    .getBasicListWhenDueDateEquals(today, startPageNum, pageSize, opUid, opName);
            if (CollectionUtils.isEmpty(dueDateAfter)) {
                return null;
            }
            return dueDateAfter;
        } catch (WmServerException | TException e) {
            log.error("获取过期合同id列表异常了", e);
        }
        return null;
    }

    static class ContractMisIdInfo {
        private String misId;
        private String contractNum;

        public ContractMisIdInfo(String misId, String contractNum) {
            this.misId = misId;
            this.contractNum = contractNum;
        }
    }

}