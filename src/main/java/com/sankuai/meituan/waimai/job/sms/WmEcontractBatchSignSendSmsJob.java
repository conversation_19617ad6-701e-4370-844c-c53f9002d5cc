package com.sankuai.meituan.waimai.job.sms;

import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.job.CommonJob;
import com.sankuai.meituan.waimai.service.EcontractSmsActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;

/**
 * @description: 支持打包任务发送签约短信
 * @author: liuyunjie05
 * @create: 2024/8/24 09:34
 */
@Service
@Slf4j
public class WmEcontractBatchSignSendSmsJob implements CommonJob {

    @Resource
    private EcontractSmsActivityService econtractSmsActivityService;

    @Override
    public void execute() {
        if (!MccConfig.isSupportSendBatchSignTaskSms()) {
            log.info("WmEcontractBatchSignSendSmsJob未开启");
            return;
        }
        log.info("WmEcontractBatchSignSendSmsJob#execute, 发送打包签约短信, trace: {}", Tracer.id());
        StopWatch totalWatch = new StopWatch();
        totalWatch.start();
        econtractSmsActivityService.sendBatchSignTaskSms();
        totalWatch.stop();
        log.info("WmEcontractBatchSignSendSmsJob#execute, 批量保存结束, 耗时: {}秒", totalWatch.getTotalTimeSeconds());
    }
}
