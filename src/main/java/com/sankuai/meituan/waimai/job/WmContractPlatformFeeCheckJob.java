package com.sankuai.meituan.waimai.job;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.common.log.LogConstants;
import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.meituan.waimai.alertEngine.type.AbstractCheck;
import com.sankuai.meituan.waimai.alertEngine.type.PlatformFeeCheck;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.contract.thrift.service.WmContractAuditedThriftService;
import com.sankuai.meituan.waimai.service.WmPlatformFeeDetailCheckService;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmContractThriftService;
import com.sankuai.meituan.waimai.util.CatUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * 平台使用费wmContractAudited表校验job
 * 执行时间：每天凌晨00:15:00
 * cron：0 15 0 * * ?
 */
@Service
public class WmContractPlatformFeeCheckJob {

    private static final Logger log = LoggerFactory.getLogger(LogConstants.Crane);

    private static final String TABLE_NAME = "wmContractAudited";

    @Autowired
    private WmContractAuditedThriftService.Iface wmContractAuditedThriftService;

    @Autowired
    private WmContractThriftService.Iface wmContractThriftService;

    @Autowired
    private WmPlatformFeeDetailCheckService wmPlatformFeeDetailCheckService;

    @Crane("contract.platformfeecheck.wmContractAudited")
    public void checkWmContractAudited() {
        log.info("checkWmContractAudited job 开始。。。");
        List<String> misIdList = MccConfig.getAlertPushMisId();
        //查询平台使用费不一致的合同id
        List<Integer> wmContractIdList = new ArrayList<>();
        try {
            if (ConfigUtilAdapter.getBoolean("getPlatformFeeDiff_offline", false)) {

            } else {
                wmContractIdList = wmContractAuditedThriftService.getPlatformFeeDiff();
            }
        } catch (TException e) {
            log.error("调用contract服务化获取平台使用费diff失败", e);

            //大象与短信通知
            AbstractCheck check = PlatformFeeCheck.errorCheck(misIdList, "wmContractAudited");
            check.exeute();
            return;
        }

        //大象与短信通知(仅存在不一致数据时才发短信)
        wmPlatformFeeDetailCheckService.detailCheck(TABLE_NAME, CollectionUtils.size(wmContractIdList));

        if (CollectionUtils.size(wmContractIdList) > 0) {//若合同id数目大于1则报警，并设置为生效
            log.info("【平台使用费监控】平台使用费wmContractAudited表监控,不一致合同:{}", JSON.toJSONString(wmContractIdList));
            if (MccConfig.isAutoFix()) {
                for (Integer wmContractId : wmContractIdList) {
                    try {
                        if (ConfigUtilAdapter.getBoolean("setPlatformFeeEffect_offline", false)) {

                        } else {
                            wmContractThriftService.setPlatformFeeEffect(wmContractId);
                        }
                    } catch (WmServerException | TException e) {
                        log.error("设置平台使用费生效失败, wmContractId = " + wmContractId, e);
                        continue;
                    }
                    log.info("【平台使用费监控】更新合同wmContractId = {}", wmContractId);
                }
            }
        }

        CatUtil.logWmContractPlatformFeeCheckJobSuccess();
    }
}
