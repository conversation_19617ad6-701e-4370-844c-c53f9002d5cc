package com.sankuai.meituan.waimai.web.controller.sc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.pay.common.exception.BusinessException;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.scmbrand.thrift.domain.common.MetadataFieldElResultDTO;
import com.sankuai.meituan.waimai.config.MccScConfig;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.service.WmCustomerCheck;
import com.sankuai.meituan.waimai.service.adaptor.WmBrandMetadataServiceAdapter;
import com.sankuai.meituan.waimai.service.adaptor.WmLabelServiceAdapter;
import com.sankuai.meituan.waimai.service.adaptor.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.service.handler.school.SchoolExcelBatchApplication;
import com.sankuai.meituan.waimai.service.sc.auth.WmScAuthService;
import com.sankuai.meituan.waimai.service.sc.WmScCheckService;
import com.sankuai.meituan.waimai.service.sc.WmScSchoolService;
import com.sankuai.meituan.waimai.service.sc.WmSchoolDeliveryService;
import com.sankuai.meituan.waimai.service.sc.canteenstall.WmScCanteenStallService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolTimeInfoSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermBeginPlanEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermBeginSituationEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermEndPlanEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolDeliveryThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.CalculateWeekUtil;
import com.sankuai.meituan.waimai.util.base.WmRestReturnUtil;
import com.sankuai.meituan.waimai.vo.customerswitch.QueryEnumVo;
import com.sankuai.meituan.waimai.vo.sc.*;
import com.sankuai.meituan.waimai.vo.sc.delivery.*;
import com.sankuai.meituan.waimai.vo.sc.time.SchoolTimeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @program: scm
 * @description: 学校处理的controller
 * @author: jianghuimin02
 * @create: 2020-04-23 10:20
 **/
@InterfaceDoc(
        name = "学校处理的controller",
        displayName = "学校处理的controller",
        type = "restful",
        scenarios = "先富系统的客户操作界面调用此接口",
        description = "学校的新增、查询、删除、修改等操作",
        host = ""


)
@Slf4j
@Controller
@RequestMapping(value = {"/customer/v1/w/sc", "/customer/ba/customer/v1/w/sc"})
public class WmScSchoolController {

    @Autowired
    private WmScAuthService wmScAuthService;

    @Autowired
    private WmScSchoolService wmScSchoolService;

    @Autowired
    private WmScCheckService wmScCheckService;

    @Autowired
    private WmCustomerCheck wmCustomerCheck;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmLabelServiceAdapter wmLabelServiceAdapter;

    @Autowired
    private WmSchoolDeliveryService wmSchoolDeliveryService;

    @Autowired
    private WmBrandMetadataServiceAdapter wmBrandMetadataServiceAdapter;

    @Autowired
    private WmSchoolDeliveryThriftService wmSchoolDeliveryThriftService;


    @Autowired
    private WmScCanteenStallService wmScCanteenStallService;

    @Autowired
    private SchoolExcelBatchApplication schoolExcelBatchApplication;

    /**
     * 保存学校信息
     *
     * @param schoolFormVo
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "保存学校信息",
            description = "保存学校信息,保存学校信息,保存学校信息,保存学校信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "功能鉴权逻辑：获取访问该接口用户的UID，根据用户UID获取用户所拥有的UPM角色,只有用户拥有相关角色才可以保存学校信息。"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @RequestMapping("/school/saveSchoolV2")
    @ResponseBody
    public Object saveSchoolV2(@RequestBody SchoolFormVo schoolFormVo) throws Exception {
        log.info("[WmScSchoolController.saveSchoolV2] input param: schoolFormVo = {}", JSONObject.toJSONString(schoolFormVo));
        try {
            // 是否具有新建学校权限
            wmScAuthService.saveSchoolAuth();
            // 设置学校信息默认值
            schoolFormVo = wmScSchoolService.setSchoolDefaultValue(schoolFormVo);
            // 校验保存时的入参
            wmScCheckService.checkSchoolFormVoWhenNewSchool(schoolFormVo);
            SchoolBo result = wmScSchoolService.saveSchoolReturn(schoolFormVo);
            log.info("校园食堂项目:保存学校saveSchool保存返回:result:{}:schoolFormVo:{}", result, JSONObject.toJSONString(schoolFormVo));
            return WmRestReturnUtil.success(result);
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("校园食堂项目:保存学校saveSchool保存异常:schoolFormVo:{}", JSONObject.toJSONString(schoolFormVo), e);
            return WmRestReturnUtil.fail("保存学校异常");
        }
    }

    /**
     * 修改学校信息
     *
     * @param schoolFormVo
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "修改学校信息",
            description = "修改学校信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：参数id, 只有该学校的学校责任人才能修改学校信息"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @RequestMapping("/school/editSchool/{id}")
    @ResponseBody
    public Object editSchool(
            @PathVariable(value = "id") int id,
            @RequestBody SchoolFormVo schoolFormVo) throws Exception {
        log.info("[WmScSchoolController.editSchool] schoolFormVo:{}", JSONObject.toJSONString(schoolFormVo));
        try {
            // 设置学校信息默认值
            schoolFormVo = wmScSchoolService.setSchoolDefaultValue(schoolFormVo);
            boolean result = wmScSchoolService.editSchool(schoolFormVo);
            log.info("校园食堂项目:修改学校editSchool修改返回:result:{}:schoolFormVo:{}", result, JSONObject.toJSONString(schoolFormVo));
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.warn("校园食堂项目:修改学校editSchool修改异常:schoolFormVo:{}", JSONObject.toJSONString(schoolFormVo), e);
            return WmRestReturnUtil.fail("修改学校异常");
        }
        return WmRestReturnUtil.success();
    }

    /**
     * 删除学校
     *
     * @param schoolId
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "删除学校",
            description = "删除学校",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "功能鉴权逻辑：获取访问该接口用户的UID，根据用户UID获取用户所拥有的UPM角色,只有用户拥有相关角色才可以删除学校。"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @RequestMapping("/school/deleteSchool")
    @ResponseBody
    public Object deleteSchool(@RequestParam(value = "schoolId", defaultValue = "0") int schoolId) throws Exception {
        log.info("校园食堂项目:删除学校deleteSchool入参:schoolId:{}", schoolId);
        try {
            wmScAuthService.deleteSchoolAuth();
            wmScSchoolService.deleteSchool(schoolId);
            log.info("校园食堂项目:删除学校成功:schoolId:{}", schoolId);
            return WmRestReturnUtil.success();
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.warn("校园食堂项目:删除学校异常:schoolId:{}", schoolId, e);
            return WmRestReturnUtil.fail("删除学校异常");
        }
    }

    /**
     * 获取单个学校信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "获取单个学校信息",
            description = "获取单个学校信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：参数id, 只能查看自己有权限查看的学校信息"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_STATUS",
                            content = "存在漏洞，排期中"
                    ),
            }
    )
    @RequestMapping("/school/getSchool/{id}")
    @ResponseBody
    public Object getSchool(@PathVariable(value = "id") int id) throws Exception {
        try {
            wmScAuthService.searchAuth();
            SchoolListVo schoolListVo = wmScSchoolService.getSchool(id);
            return WmRestReturnUtil.success(schoolListVo);
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("校园食堂项目:查询学校getSchool查询异常:id:{}", id, e);
            return WmRestReturnUtil.fail("查询学校异常");
        }
    }

    /**
     * 获取学校列表
     *
     * @param queryVo
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "获取学校信息列表",
            description = "获取学校信息列表",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：参数schoolId，只有拥有该学校的操作权限的用户才能查看该学校"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_STATUS",
                            content = "存在漏洞，排期中"
                    ),
            }
    )
    @RequestMapping("/school/list")
    @ResponseBody
    public Object getSchoolList(@RequestBody SchoolQueryVo queryVo) throws Exception {
        try {
            wmScAuthService.searchAuth();
            JSONObject jsonObject = wmScSchoolService.getSchoolList(queryVo);
            return WmRestReturnUtil.success(jsonObject);
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("校园食堂项目:查询学校getSchoolList查询异常:queryVo:{}", JSONObject.toJSONString(queryVo), e);
            return WmRestReturnUtil.fail("查询学校异常");
        }
    }

    /**
     * 根据学校名称模糊搜获学校
     * @param schoolName 学校名称
     * @return 学校列表
     * @throws Exception
     */
    @MethodDoc(
            displayName = "模糊搜获学校",
            description = "模糊搜获学校",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolWithoutPage")
    @ResponseBody
    public Object getSchoolWithoutPage(String schoolName) throws Exception {
        try {
            wmScAuthService.searchAuth();
            wmScCheckService.checkQuerySchoolWithoutPage(schoolName);
            List<SchoolListDelSensitiveFiledVo> schoolWithoutPage = wmScSchoolService.getSchoolWithoutPage(schoolName);
            return WmRestReturnUtil.success(schoolWithoutPage);
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.warn("校园食堂项目:查询学校getSchoolWithoutPage查询异常:schoolName:{}", schoolName, e);
            return WmRestReturnUtil.fail("查询学校异常");
        }
    }

    /**
     * 获取学校供给分级
     *
     * @param type
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "获取学校供给分级",
            description = "获取学校供给分级",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无需鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolGrade")
    @ResponseBody
    public Object getSchoolGrade(int type) throws Exception {
        try {
            wmScAuthService.searchAuth();
            String result = wmScSchoolService.getSchoolGrade(type);
            return WmRestReturnUtil.success(result);
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("校园食堂项目:查询学校getSchoolGrade查询异常:schoolName:{}", type, e);
            return WmRestReturnUtil.fail("查询学校异常");
        }
    }

    /**
     * 用户权限详情
     *
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "用户权限详情-学校相关",
            description = "获取学校相关的用户权限详情",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/userAuthDetail")
    @ResponseBody
    public Object userAuthDetail() throws Exception {
        Map<String, Object> data = Maps.newHashMap();
        data.put("canEditSchoolCode", wmCustomerCheck.canEditSchoolCode(UserUtils.getUser()));
        return WmRestReturnUtil.success(data);
    }


    /**
     * 分配学校责任人
     *
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "分配学校责任人",
            description = "分配学校责任人",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权：参数id,如果用户为超管或者该学校责任人才可以分配学校责任人"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @RequestMapping("/school/responsiblePerson")
    @ResponseBody
    public Object responsiblePerson(@RequestBody RpVo rpVo) throws Exception {
        try {
            log.info("[WmScSchoolController.responsiblePerson] input param: rpVo = {}", JSONObject.toJSONString(rpVo));
            WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
            if (currentEmploy == null) {
                return WmRestReturnUtil.fail("查询用户信息异常");
            }
            // 用户操作鉴权
            wmScAuthService.checkSchoolBindRpButtonAuth(rpVo.getId(), currentEmploy.getUid());
            String userName = currentEmploy.name + "(" + currentEmploy.misId + ")";
            boolean result = wmScSchoolService.bindResponsiblePerson(rpVo.getModifyName(), rpVo.getId(), currentEmploy.uid, userName);
            return WmRestReturnUtil.success(result, "保存成功");
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("校园食堂项目:responsiblePerson查询异常:id:{}:modifyName:{}", rpVo.getId(), rpVo.getModifyName(), e);
            return WmRestReturnUtil.fail("绑定食堂责任人异常");
        }
    }


    /**
     * 创建学校时查询学校线索
     *
     * @param clueId
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "创建学校时查询学校线索",
            description = "创建学校时查询学校线索",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/clueDetail")
    @ResponseBody
    public Object clueDetail(Long clueId) {
        log.info("clueDetail::clueId = {}", clueId);
        try {
            WmScSchoolClueDetailBO result = wmScSchoolService.clueDetail(clueId);
            return WmRestReturnUtil.success(result);
        } catch (WmSchCantException e) {
            log.info("clueDetail::clueId = {},code={},msg={}", clueId, e.getCode(), e.getMessage());
            return WmRestReturnUtil.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("clueDetail::clueId = {}", clueId, e);
            return WmRestReturnUtil.fail("系统繁忙请稍后充实");
        }
    }

    /**
     * 根据学校Id或学校name查询学校列表
     *
     * @param schoolIdOrName
     * @return
     * @throws Exception
     */
    @MethodDoc(
            displayName = "根据学校Id或学校name查询学校列表",
            description = "根据学校Id或学校name查询学校列表",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolsByIdOrName")
    @ResponseBody
    public Object getSchoolsByIdOrName(@RequestParam(value = "schoolIdOrName", required = true) String schoolIdOrName) throws Exception {
        if (StringUtils.isEmpty(schoolIdOrName)) {
            return WmRestReturnUtil.fail("必填参数缺失");
        }
        try {
            wmScAuthService.searchAuth();
            List<SchoolListQueryVo> result = wmScSchoolService.getSchoolsByIdOrName(schoolIdOrName);
            return WmRestReturnUtil.success(result);
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("校园食堂项目:根据学校Id或学校name查询学校列表:schoolIdOrName:{}", JSONObject.toJSONString(schoolIdOrName), e);
            return WmRestReturnUtil.fail("查询学校异常");
        }
    }

    /**
     * 根据学校主键ID查询学校范围
     * @param schoolPrimaryId 学校主键ID
     * @param type 查看/编辑类型
     * @return SchoolAreaAoiVO
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "根据学校id查询学校范围",
            description = "根据学校id查询学校范围",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：参数schoolId，拥有该学校访问权限的用户可以查询学校范围"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_STATUS",
                            content = "存在漏洞，待排期"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolAreaByIdV2")
    @ResponseBody
    public Object getSchoolAreaByIdV2(@RequestParam(value = "schoolPrimaryId", required = true) int schoolPrimaryId,
                                      @RequestParam(value = "type", required = true) int type) throws Exception {
        try {
            wmScAuthService.searchAuth();
            WmScSchoolAoiVO wmScSchoolAoiVO = wmScSchoolService.getSchoolAreaByIdV2(schoolPrimaryId, type);
            return WmRestReturnUtil.success(wmScSchoolAoiVO);
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.getSchoolAreaByIdV2] schoolPrimaryId = {}, type = {}", schoolPrimaryId, type, wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolAreaByIdV2] schoolPrimaryId = {}, type = {}", schoolPrimaryId, type, e);
            return WmRestReturnUtil.fail("查询学校范围异常");
        }
    }

    /**
     * 根据aoiId查询aoi相关信息
     * @param aoiId aoiId
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "根据aoiId查询aoi相关信息",
            description = "根据aoiId查询aoi相关信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：参数schoolId，拥有该学校访问权限的用户可以查询学校范围"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_STATUS",
                            content = "存在漏洞，待排期"
                    ),
            }
    )
    @RequestMapping("/school/getAoiInfoByAoiId")
    @ResponseBody
    public Object getAoiInfoByAoiId(@RequestParam(value = "aoiId", required = true) Long aoiId) throws Exception {
        try {
            log.info("[WmScSchoolController.getAoiInfoByAoiId] input param: aoiId = {}", aoiId);
            WmScSchoolAoiDetailVO wmScSchoolAoiDetailVO = wmScSchoolService.getAoiInfoByAoiId(aoiId);
            if (wmScSchoolAoiDetailVO == null || wmScSchoolAoiDetailVO.getAoiId() == null) {
                return WmRestReturnUtil.success(new ArrayList<>());
            }
            List<WmScSchoolAoiDetailVO> wmScSchoolAoiDetailVOList = new ArrayList<>();
            wmScSchoolAoiDetailVOList.add(wmScSchoolAoiDetailVO);
            return WmRestReturnUtil.success(wmScSchoolAoiDetailVOList);
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.getAoiInfoByAoiId] WmSchCantException. aoiId = {}", aoiId, wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getAoiInfoByAoiId] Exception. aoiId = {}", aoiId, e);
            return WmRestReturnUtil.fail("查询AOI信息异常");
        }
    }

    /**
     * AOI弹窗-保存学校AOI信息前置校验
     * @param wmScSchoolSaveAoiInfoVO wmScSchoolSaveAoiInfoVO
     * @return success
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "AOI弹窗-保存学校AOI信息",
            description = "AOI弹窗-保存学校AOI信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/checkSchoolAoiIdList",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/checkSchoolAoiIdList")
    @ResponseBody
    public Object checkSchoolAoiIdList(@RequestBody WmScSchoolSaveAoiInfoVO wmScSchoolSaveAoiInfoVO) throws Exception {
        try {
            if (wmScSchoolSaveAoiInfoVO == null
                    || wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId() == null
                    || wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId() <= 0) {
                return WmRestReturnUtil.fail("学校ID不存在");
            }
            wmScAuthService.searchAuth();
            wmScSchoolService.checkSchoolAoiIdList(wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId(), wmScSchoolSaveAoiInfoVO.getAoiIdList());
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.checkSchoolAoiIdList] WmSchCantException. wmScSchoolSaveAoiInfoVO = {}",
                    JSONObject.toJSONString(wmScSchoolSaveAoiInfoVO), wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), "", wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.checkSchoolAoiIdList] Exception. wmScSchoolSaveAoiInfoVO = {}",
                    JSONObject.toJSONString(wmScSchoolSaveAoiInfoVO), e);
            return WmRestReturnUtil.fail("保存学校AOI信息异常");
        }
        return WmRestReturnUtil.success();
    }


    /**
     * AOI弹窗-保存学校AOI信息
     * @param wmScSchoolSaveAoiInfoVO wmScSchoolSaveAoiInfoVO
     * @return success
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "AOI弹窗-保存学校AOI信息",
            description = "AOI弹窗-保存学校AOI信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/saveSchoolAoiInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/saveSchoolAoiInfo")
    @ResponseBody
    public Object saveSchoolAoiInfo(@RequestBody WmScSchoolSaveAoiInfoVO wmScSchoolSaveAoiInfoVO) throws Exception {
        try {
            log.info("[WmScSchoolController.saveSchoolAoiInfo] input param: swmScSchoolSaveAoiInfoVO = {}", JSONObject.toJSONString(wmScSchoolSaveAoiInfoVO));
            if (wmScSchoolSaveAoiInfoVO == null
                    || wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId() == null
                    || wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId() <= 0) {
                return WmRestReturnUtil.fail("学校ID不存在");
            }
            wmScAuthService.searchAuth();
            wmScSchoolService.saveSchoolAoiInfo(wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId(), wmScSchoolSaveAoiInfoVO.getAoiIdList());
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.saveSchoolAoiInfo] WmSchCantException. wmScSchoolSaveAoiInfoVO = {}", JSONObject.toJSONString(wmScSchoolSaveAoiInfoVO), wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.saveSchoolAoiInfo] Exception. wmScSchoolSaveAoiInfoVO = {}", JSONObject.toJSONString(wmScSchoolSaveAoiInfoVO), e);
            return WmRestReturnUtil.fail("保存学校AOI信息异常");
        }
        return WmRestReturnUtil.success("", "保存学校AOI信息成功");
    }


    /**
     * 学校范围页面-同步学校AOI信息
     * @param wmScSchoolSaveAoiInfoVO wmScSchoolSaveAoiInfoVO
     * @return 同步学校AOI信息成功
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校范围页面-同步学校AOI信息",
            description = "学校范围页面-同步学校AOI信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/syncSchoolAoiInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/syncSchoolAoiInfo")
    @ResponseBody
    public Object syncSchoolAoiInfo(@RequestBody WmScSchoolSaveAoiInfoVO wmScSchoolSaveAoiInfoVO) throws Exception {
        try {
            log.info("[WmScSchoolController.syncSchoolAoiInfo] input param: WmScSchoolSaveAoiInfoVO = {}", JSONObject.toJSONString(wmScSchoolSaveAoiInfoVO));
            if (wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId() == null || wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId() <= 0) {
                return WmRestReturnUtil.fail("学校ID不存在");
            }
            wmScAuthService.searchAuth();
            wmScSchoolService.syncSchoolAoiInfo(wmScSchoolSaveAoiInfoVO.getSchoolPrimaryId());
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.syncSchoolAoiInfo] WmSchCantException. wmScSchoolSaveAoiInfoVO = {}", JSONObject.toJSONString(wmScSchoolSaveAoiInfoVO), wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.syncSchoolAoiInfo] Exception. wmScSchoolSaveAoiInfoVO = {}", JSONObject.toJSONString(wmScSchoolSaveAoiInfoVO), e);
            return WmRestReturnUtil.fail("同步学校AOI信息异常");
        }
        return WmRestReturnUtil.success("", "同步学校AOI信息成功");
    }

    /**
     * 保存学校范围信息-前置校验
     * @param schoolAreaSubmitVO schoolAreaSubmitVO
     */
    @MethodDoc(
            displayName = "保存学校范围信息-前置校验",
            description = "保存学校范围信息-前置校验",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "功能鉴权：只有拥有修改学校范围权限的用户才能修改学校"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @RequestMapping("/school/checkSchoolAreaList")
    @ResponseBody
    public Object checkSchoolAreaList(@RequestBody SchoolAreaSubmitVO schoolAreaSubmitVO) throws Exception {
        try {
            log.info("[WmScSchoolController.checkSchoolAreaList] input param: schoolAreaSubmitVO = {}",
                    JSONObject.toJSONString(schoolAreaSubmitVO));
            List<String> resList = wmScSchoolService.checkSchoolAreaList(schoolAreaSubmitVO);
            if (CollectionUtils.isEmpty(resList)) {
                return WmRestReturnUtil.success();
            }
            return WmRestReturnUtil.constructResponseJSON(WmScCodeConstants.SAVE_SCHOOL_AREA_PRE_CHECK_ERROR,
                    "校验不通过", resList);
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.checkSchoolAreaList] wmSchCantException. schoolAreaBo = {},code={},msg={}",
                    JSONObject.toJSONString(schoolAreaSubmitVO), wmSchCantException.getCode(), wmSchCantException.getMessage());
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.checkSchoolAreaList] Exception. schoolAreaSubmitVO = {}",
                    JSONObject.toJSONString(schoolAreaSubmitVO), e);
            return WmRestReturnUtil.fail("保存学校范围信息异常");
        }
    }

    /**
     * 保存学校范围信息V2
     * @param schoolAreaSubmitVO schoolAreaSubmitVO
     */
    @MethodDoc(
            displayName = "保存学校范围信息V2",
            description = "保存学校范围信息V2",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "功能鉴权：只有拥有修改学校范围权限的用户才能修改学校"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @RequestMapping("/school/submitSchoolAreaV2")
    @ResponseBody
    public Object submitSchoolAreaV2(@RequestBody SchoolAreaSubmitVO schoolAreaSubmitVO) throws Exception {
        try {
            log.info("[WmScSchoolController.submitSchoolAreaV2] input param: schoolAreaSubmitVO = {}", JSONObject.toJSONString(schoolAreaSubmitVO));
            wmScAuthService.searchAuth();

            WmModifyScAreaCallDo wmModifyScAreaCallDo = wmScSchoolService.submitSchoolAreaV2(schoolAreaSubmitVO);
            if (wmModifyScAreaCallDo.getCode() == WmRestReturnUtil.SUCCESS_CODE){
                return WmRestReturnUtil.success(wmModifyScAreaCallDo.getDataOrMsg());
            }
            return WmRestReturnUtil.constructResponseJSON(wmModifyScAreaCallDo.getCode(), wmModifyScAreaCallDo.getDataOrMsg(),"");
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.submitSchoolAreaV2] wmSchCantException. schoolAreaBo = {},code={},msg={}", JSONObject.toJSONString(schoolAreaSubmitVO), wmSchCantException.getCode(), wmSchCantException.getMessage());
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.submitSchoolAreaV2] Exception. schoolAreaSubmitVO = {}", JSONObject.toJSONString(schoolAreaSubmitVO), e);
            return WmRestReturnUtil.fail("保存学校范围信息异常");
        }
    }

    /**
     * 获取学校楼宇信息
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "获取学校楼宇信息",
            description = "获取学校楼宇信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：拥有该学校访问权限的用户可以访问楼宇信息"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_STATUS",
                            content = "存在漏洞，待排期"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolBuilding")
    @ResponseBody
    public Object getSchoolBuilding(@RequestBody WmScSchoolBuildingQueryVO wmScSchoolBuildingQueryVO) throws Exception {
        try {
            wmScAuthService.searchAuth();
            WmScSchoolBuildingListVO wmScSchoolBuildingListVO = wmScSchoolService.getSchoolBuildingList(wmScSchoolBuildingQueryVO);
            return WmRestReturnUtil.success(wmScSchoolBuildingListVO);
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.getSchoolBuilding] WmSchCantException. SchoolBuildingQueryVO = {}", JSONObject.toJSONString(wmScSchoolBuildingQueryVO), wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolBuilding] Exception. SchoolBuildingQueryVO = {}", JSONObject.toJSONString(wmScSchoolBuildingQueryVO), e);
            return WmRestReturnUtil.fail("查询学校楼宇信息异常");
        }
    }

    /**
     * 学校楼宇信息页面-同步学校楼宇POI信息
     * @param wmScSchoolCheckBuildingInfoVO wmScSchoolCheckBuildingInfoVO
     * @return success
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校楼宇信息页面-同步学校楼宇POI信息",
            description = "学校楼宇信息页面-同步学校楼宇POI信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/syncSchoolBuildingPoiInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/syncSchoolBuildingPoiInfo")
    @ResponseBody
    public Object syncSchoolBuildingPoiInfo(@RequestBody WmScSchoolCheckBuildingInfoVO wmScSchoolCheckBuildingInfoVO) throws Exception {
        try {
            if (wmScSchoolCheckBuildingInfoVO.getSchoolPrimaryId() == null || wmScSchoolCheckBuildingInfoVO.getSchoolPrimaryId() <= 0) {
                return WmRestReturnUtil.fail("学校ID不存在");
            }
            wmScSchoolService.syncSchoolBuildingPoiInfo(wmScSchoolCheckBuildingInfoVO.getSchoolPrimaryId());
        } catch (WmSchCantException wmSchCantException) {
            log.info("[WmScSchoolController.syncSchoolBuildingPoiInfo] WmSchCantException. wmScSchoolCheckBuildingInfoVO = {}",
                    JSONObject.toJSONString(wmScSchoolCheckBuildingInfoVO), wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.syncSchoolBuildingPoiInfo] Exception. wmScSchoolCheckBuildingInfoVO = {}",
                    JSONObject.toJSONString(wmScSchoolCheckBuildingInfoVO), e);
            return WmRestReturnUtil.fail("同步学校楼宇POI信息异常");
        }
        return WmRestReturnUtil.success("", "同步学校楼宇POI信息成功");
    }

    /**
     * 获取学年信息列表
     *
     * @return
     * @throws Exception
     */
    @MethodDoc(
            description = "查询学校详情--其他信息",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：参数schoolId，拥有该学校访问权限的用户可以获取学年信息列表"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_STATUS",
                            content = "存在漏洞，待排期"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolYearInfo")
    @ResponseBody
    @Deprecated
    public Object getSchoolYearInfo() throws Exception {
        try {
            wmScAuthService.searchAuth();
            List<SchoolTimeBeginEndBo> result = wmScSchoolService.getSchoolYearInfo();
            return WmRestReturnUtil.success(result);
        } catch (WmSchCantException wmSchCantException) {
            log.info("校园食堂项目:获取学年信息列表异常::code={},msg={}", wmSchCantException.getCode(), wmSchCantException.getMessage());
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("校园食堂项目:获取学年信息列表:{}", e);
            return WmRestReturnUtil.fail("获取学年信息列表异常");
        }
    }

    /**
     * 根据日期计算周
     *
     * @param calTime
     * @return
     * @throws Exception
     */
    @MethodDoc(
            description = "根据日期计算周",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/calculateWeek")
    @ResponseBody
    public Object calculateWeek(@RequestParam(value = "calTime", required = true) String calTime) throws Exception {
        try {
            if (StringUtils.isBlank(calTime)) {
                return WmRestReturnUtil.fail("日期为空");
            }
            String week = CalculateWeekUtil.getWeek(calTime);
            return WmRestReturnUtil.success(week);
        } catch (ParseException e) {
            log.warn("根据日期计算周日期转换异常:calTime={}", calTime, e);
            return WmRestReturnUtil.fail("根据日期计算周日期转换异常");
        } catch (Exception e) {
            log.error("根据日期计算周异常:calTime={}", calTime, e);
            return WmRestReturnUtil.fail("根据日期计算周异常");
        }
    }


    /**
     * 获取时间信息枚举
     *
     * @return
     * @throws Exception
     */
    @MethodDoc(
            description = "获取时间信息枚举",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getTimeEnum")
    @ResponseBody
    public Object getTimeEnum() {
        try {
            Map<String, List<QueryEnumVo>> map = Maps.newHashMap();
            // 学期
            List<QueryEnumVo> schoolTermEnums = Lists.newArrayList();
            for (SchoolTermEnum codeEnum : SchoolTermEnum.values()) {
                if (codeEnum.getCode() <= QueryEnumVo.IGNORE_CODE) {
                    continue;
                }
                schoolTermEnums.add(new QueryEnumVo(codeEnum.getCode(), codeEnum.getDesc()));
            }
            map.put("term", schoolTermEnums);
            // 开学情况
            List<QueryEnumVo> schoolTermBeginSituationEnums = Lists.newArrayList();
            for (SchoolTermBeginSituationEnum codeEnum : SchoolTermBeginSituationEnum.values()) {
                if (codeEnum.getCode() <= QueryEnumVo.IGNORE_CODE) {
                    continue;
                }
                schoolTermBeginSituationEnums.add(new QueryEnumVo(codeEnum.getCode(), codeEnum.getDesc()));
            }
            map.put("termBeginSituation", schoolTermBeginSituationEnums);
            // 开学安排
            List<QueryEnumVo> choolTermBeginPlanEnums = Lists.newArrayList();
            for (SchoolTermBeginPlanEnum codeEnum : SchoolTermBeginPlanEnum.values()) {
                if (codeEnum.getCode() <= QueryEnumVo.IGNORE_CODE
                        || codeEnum.getCode() == SchoolTermBeginPlanEnum.OPENING.getCode()) {
                    continue;
                }
                choolTermBeginPlanEnums.add(new QueryEnumVo(codeEnum.getCode(), codeEnum.getDesc()));
            }
            map.put("termBeginPlan", choolTermBeginPlanEnums);
            // 放假安排
            List<QueryEnumVo> schoolTermEndPlanEnums = Lists.newArrayList();
            for (SchoolTermEndPlanEnum codeEnum : SchoolTermEndPlanEnum.values()) {
                if (codeEnum.getCode() <= QueryEnumVo.IGNORE_CODE
                        || codeEnum.getCode() == SchoolTermEndPlanEnum.ENDING.getCode()) {
                    continue;
                }
                schoolTermEndPlanEnums.add(new QueryEnumVo(codeEnum.getCode(), codeEnum.getDesc()));
            }
            map.put("termEndPlan", schoolTermEndPlanEnums);
            // 资料来源
            List<QueryEnumVo> infoSourceEnums = Lists.newArrayList();
            for (SchoolTimeInfoSourceEnum codeEnum : SchoolTimeInfoSourceEnum.values()) {
                if ((int) codeEnum.getType() <= QueryEnumVo.IGNORE_CODE) {
                    continue;
                }
                infoSourceEnums.add(new QueryEnumVo((int) codeEnum.getType(), codeEnum.getName()));
            }
            map.put("infoSourceEnum", infoSourceEnums);
            // 开学安排V2
            List<QueryEnumVo> schoolTermBeginPlanEnumsV2 = Lists.newArrayList();
            for (SchoolTermBeginPlanEnum codeEnum : SchoolTermBeginPlanEnum.values()) {
                if (codeEnum.getCode() <= QueryEnumVo.IGNORE_CODE
                        || codeEnum.getCode() == SchoolTermBeginPlanEnum.UNIFIED_OPENING.getCode()
                        || codeEnum.getCode() == SchoolTermBeginPlanEnum.STARTS_IN_BATCHES.getCode()) {
                    continue;
                }
                schoolTermBeginPlanEnumsV2.add(new QueryEnumVo(codeEnum.getCode(), codeEnum.getDesc()));
            }
            Collections.reverse(schoolTermBeginPlanEnumsV2);
            map.put("termBeginPlanV2", schoolTermBeginPlanEnumsV2);
            // 放假安排V2
            List<QueryEnumVo> schoolTermEndPlanEnumsV2 = Lists.newArrayList();
            for (SchoolTermEndPlanEnum codeEnum : SchoolTermEndPlanEnum.values()) {
                if (codeEnum.getCode() <= QueryEnumVo.IGNORE_CODE
                        || codeEnum.getCode() == SchoolTermEndPlanEnum.UNIFIED_ENDING.getCode()
                        || codeEnum.getCode() == SchoolTermEndPlanEnum.ENDINGS_IN_BATCHES.getCode()) {
                    continue;
                }
                schoolTermEndPlanEnumsV2.add(new QueryEnumVo(codeEnum.getCode(), codeEnum.getDesc()));
            }
            Collections.reverse(schoolTermEndPlanEnumsV2);
            map.put("termEndPlanV2", schoolTermEndPlanEnumsV2);

            return WmRestReturnUtil.success(map);
        } catch (Exception e) {
            log.error("获取时间信息枚举异常", e);
            return WmRestReturnUtil.fail("获取时间信息枚举异常");
        }
    }


    /**
     * 时间列表信息查询
     * @param schoolId 学校主键ID
     */
    @MethodDoc(
            description = "时间信息查询",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolTimeInfo")
    @ResponseBody
    public Object getSchoolTimeInfo(@RequestParam(value = "schoolId", required = true) int schoolId) throws Exception {
        log.info("[getSchoolTimeInfo] schoolPrimaryId={}", schoolId);
        try {
            Map<String, Object> map =  wmScSchoolService.getSchoolTimeInfo(schoolId);
            return WmRestReturnUtil.success(map);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("时间信息查询异常wmSchCantException:schoolId={}", schoolId, wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("时间信息查询异常Exception:schoolId={}", schoolId, e);
            return WmRestReturnUtil.fail("时间信息查询异常");
        }
    }

    /**
     * 获取学年信息列表
     *
     * @return
     * @throws Exception
     */
    @MethodDoc(
            description = "查询学校详情--时间信息",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：参数schoolId，拥有该学校访问权限的用户可以获取学年信息列表"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_STATUS",
                            content = "存在漏洞，待排期"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolYear")
    @ResponseBody
    public Object getSchoolYear() {
        try {
            wmScAuthService.searchAuth();
            return WmRestReturnUtil.success(wmScSchoolService.getSchoolYear());
        } catch (WmSchCantException wmSchCantException) {
            log.info("校园食堂项目:获取学年信息列表异常::code={},msg={}", wmSchCantException.getCode(), wmSchCantException.getMessage());
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("校园食堂项目:获取学年信息列表:{}", e);
            return WmRestReturnUtil.fail("获取学年信息列表异常");
        }
    }

    /**
     * 时间信息单条查询
     * @param id 时间信息主键ID
     */
    @MethodDoc(
            description = "时间信息单条查询",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolTimeSingle")
    @ResponseBody
    public Object getSchoolTimeSingle(@RequestParam(value = "id", required = true) long id) throws Exception {
        try {
            SchoolTimeVo schoolTimeVo = wmScSchoolService.getSchoolTimeSingle(id);
            log.info("getSchoolTimeSingle id={},schoolTimeVo={}", id, JSONObject.toJSONString(schoolTimeVo));
            return WmRestReturnUtil.success(schoolTimeVo);
        } catch (WmSchCantException e) {
            log.warn("时间信息单个查询异常:id={}", id, e);
            return WmRestReturnUtil.fail("时间信息单个查询异常:"+e.getMsg());
        } catch (Exception e) {
            log.error("时间信息单个查询异常:id={}", id, e);
            return WmRestReturnUtil.fail("时间信息单个查询异常");
        }
    }

    @MethodDoc(
            displayName = "删除学校时间信息",
            description = "删除学校时间信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "功能鉴权逻辑：获取访问该接口用户的UID，根据用户UID获取用户所拥有的UPM角色,只有用户拥有相关角色才可以删除学校。"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @RequestMapping("/school/deleteSchoolTime")
    @ResponseBody
    public Object deleteSchoolTime(@RequestParam(value = "id", defaultValue = "0") Long id) throws Exception {
        log.info("校园食堂项目:deleteSchoolTime:id:{}", id);
        if (id == null || id.intValue() <= 0) {
            return WmRestReturnUtil.fail("删除学校时间异常：参数不合法");
        }
        try {
            wmScSchoolService.deleteSchoolTime(id);
            return WmRestReturnUtil.success();
        } catch (WmSchCantException wmSchCantException) {
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.warn("校园食堂项目:删除学校时间信息:id:{}", id, e);
            return WmRestReturnUtil.fail("删除学校时间信息");
        }
    }

    @MethodDoc(
            displayName = "时间信息单条保存",
            description = "时间信息单条保存",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "功能鉴权逻辑：获取访问该接口用户的UID，根据用户UID获取用户所拥有的UPM角色,只有用户拥有相关角色才可以保存学校信息。"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @RequestMapping("/school/saveSchoolTimeSingle")
    @ResponseBody
    public Object saveSchoolTimeSingle(@RequestBody SchoolTimeVo schoolTimeVo) throws Exception {
        log.info("[saveSchoolTimeSingle][input] schoolTimeVo = {}", JSONObject.toJSONString(schoolTimeVo));
        try {
            JSONObject object = new JSONObject();
            Integer schoolTimeId = wmScSchoolService.saveSchoolTime(schoolTimeVo);
            object.put("id", schoolTimeId);
            return WmRestReturnUtil.success(object);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[saveSchoolTimeSingle] WmSchCantException, schoolTimeVo = {}", JSONObject.toJSONString(schoolTimeVo), wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[saveSchoolTimeSingle] Exception, schoolTimeVo = {}", JSONObject.toJSONString(schoolTimeVo), e);
            return WmRestReturnUtil.fail("保存学校时间信息异常");
        }
    }

    /**
     * 获取学校标签下拉框列表
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "获取学校标签下拉框列表",
            description = "获取学校标签下拉框列表",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolLabelList")
    @ResponseBody
    public Object getSchoolLableList() throws Exception {
        JSONObject object = new JSONObject();
        Integer userId = UserUtils.getUser().getId();
        try {
            List<WmScSchoolLabelTypeVO> wmScSchoolLabelList = wmLabelServiceAdapter.getSchoolLabelList(userId);
            object.put("schoolLabelList", wmScSchoolLabelList);
        } catch (WmSchCantException wmSchCantException) {
            log.error("[getCustomerLableList] 获取学校标签列表异常, userId = {}", userId, wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            return WmRestReturnUtil.fail("获取学校标签列表异常");
        }
        return WmRestReturnUtil.success(object);
    }

    /**
     * 获取学校区域下拉框列表
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "获取学校区域下拉框列表",
            description = "获取学校区域下拉框列表",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/getSchoolArea",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolAreaList")
    @ResponseBody
    public Object getSchoolAreaList(String content) throws Exception {
        log.info("[getSchoolAreaList] content = {}", content);
        try {
            List<WmScSchoolAreaDTO> wmScSchoolAreaDTOList = wmVirtualOrgServiceAdaptor.getSchoolAreaList(content);
            return WmRestReturnUtil.success(WmScSchoolAreaVO.transWmScSchoolAreaDtoToVo(wmScSchoolAreaDTOList));
        } catch (WmSchCantException wmSchCantException) {
            log.error("[getSchoolAreaList] 查询学校区域异常, content = {}", content, wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            return WmRestReturnUtil.fail("查询学校区域异常");
        }
    }

    /**
     * 获取学校城市团队下拉框列表
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "获取学校城市团队下拉框列表",
            description = "获取学校城市团队下拉框列表",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/getSchoolCityTeamList",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolCityTeamList")
    @ResponseBody
    public Object getSchoolCityTeamList(String content) throws Exception {
        log.info("[getSchoolCityTeamList] content = {}", content);
        try {
            List<WmScSchoolCityTeamDTO> wmScSchoolCityTeamDTOList = wmVirtualOrgServiceAdaptor.getSchoolCityTeamList(content);
            return WmRestReturnUtil.success(WmScSchoolCityTeamVO.transWmScSchoolCityTeamDtoToVo(wmScSchoolCityTeamDTOList));
        } catch (WmSchCantException wmSchCantException) {
            log.error("[getSchoolCityTeamList] 查询学校城市团队异常, content = {}", content, wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            return WmRestReturnUtil.fail("查询学校城市团队异常");
        }
    }

    /**
     * 保存学校平台合作信息(单条)V2
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "保存学校平台合作信息(单条)V2",
            description = "保存学校平台合作信息(单条)V2",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/saveSchoolPlatformV2",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/saveSchoolPlatformV2")
    @ResponseBody
    public Object saveSchoolPlatformV2(@RequestBody WmScSchoolCoPlatformVO wmScSchoolCoPlatformVO) throws Exception {
        try {
            // 设置平台信息默认值
            wmScSchoolService.setSchoolPlatformDefaultValueV2(wmScSchoolCoPlatformVO);
            wmScSchoolService.saveSchoolPlatformV2(wmScSchoolCoPlatformVO);
            return WmRestReturnUtil.success("", "保存成功");
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.saveSchoolPlatformV2] WmSchCantException. wmScSchoolCoPlatformVO = {}", JSONObject.toJSONString(wmScSchoolCoPlatformVO));
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.saveSchoolPlatformV2] Exception. wmScSchoolCoPlatformVO = {}", JSONObject.toJSONString(wmScSchoolCoPlatformVO), e);
            return WmRestReturnUtil.fail("保存平台合作信息异常");
        }
    }

    /**
     * 删除学校平台合作信息(单条)V2
     *
     * @param id              学校平台合作信息主键ID
     * @param schoolPrimaryId 学校主键ID
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "删除学校平台合作信息(单条)V2",
            description = "删除学校平台合作信息(单条)V2",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/deleteSchoolPlatformByIdV2",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/deleteSchoolPlatformByIdV2")
    @ResponseBody
    public Object deleteSchoolPlatformByIdV2(@RequestParam(value = "id", defaultValue = "0") long id,
                                           @RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            wmScSchoolService.deleteSchoolPlatformByIdV2((int) id, schoolPrimaryId);
            return WmRestReturnUtil.success(null, "删除成功");
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.deleteSchoolPlatformByIdV2] WmSchCantException. id = {}, schoolPrimaryId = {}", id, schoolPrimaryId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.deleteSchoolPlatformByIdV2] Exception. id = {}, schoolPrimaryId = {}", id, schoolPrimaryId, e);
            return WmRestReturnUtil.fail("删除平台合作信息异常");
        }
    }

    /**
     * 查询学校平台合作信息列表V2
     *
     * @param schoolPrimaryId 学校主键ID
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "查询学校平台合作信息列表V2",
            description = "查询学校平台合作信息列表V2",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/" +
                    "getSchoolPlatformListV2",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolPlatformListV2")
    @ResponseBody
    public Object getSchoolPlatformListV2(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            JSONObject jsonObject = wmScSchoolService.getSchoolPlatformListV2(schoolPrimaryId);
            return WmRestReturnUtil.success(jsonObject);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getSchoolPlatformListV2] WmSchCantException. schoolPrimaryId = {}", schoolPrimaryId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolPlatformListV2] Exception. schoolPrimaryId = {}", schoolPrimaryId, e);
            return WmRestReturnUtil.fail("查询平台合作信息异常");
        }
    }

    /**
     * 批量修改学校责任人
     * @param wmScSchoolRpModifyQueryVO wmScSchoolRpModifyQueryVO
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "批量修改学校责任人",
            description = "批量修改学校责任人",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/batchUpdateSchoolResponsiblePerson",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/batchUpdateSchoolResponsiblePerson")
    @ResponseBody
    public Object batchUpdateSchoolResponsiblePerson(@RequestBody WmScSchoolRpModifyQueryVO wmScSchoolRpModifyQueryVO) throws Exception {
        try {
            wmScSchoolService.batchUpdateSchoolResponsiblePerson(wmScSchoolRpModifyQueryVO);
            return WmRestReturnUtil.success();
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.batchUpdateSchoolResponsiblePerson] WmSchCantException. wmScSchoolRpModifyQueryVO = {}", JSONObject.toJSONString(wmScSchoolRpModifyQueryVO));
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), "", wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.batchUpdateSchoolResponsiblePerson] Exception. wmScSchoolRpModifyQueryVO = {}", JSONObject.toJSONString(wmScSchoolRpModifyQueryVO), e);
            return WmRestReturnUtil.fail("批量修改学校责任人异常");
        }
    }

    /**
     * 查询学校基础费率信息列表
     * @param wmScSchoolBasicFeeQueryVO wmScSchoolBasicFeeQueryVO
     * @return 基础费率信息列表
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "查询学校基础费率信息列表",
            description = "查询学校基础费率信息列表",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/getSchoolBasicFeeList",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolBasicFeeList")
    @ResponseBody
    public Object getSchoolBasicFeeList(WmScSchoolBasicFeeQueryVO wmScSchoolBasicFeeQueryVO) throws Exception {
        try {
            WmScSchoolBasicFeeListVO wmScSchoolBasicFeeListVO = wmScSchoolService.getSchoolBasicFeeList(wmScSchoolBasicFeeQueryVO);
            return WmRestReturnUtil.success(wmScSchoolBasicFeeListVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getSchoolBasicFeeList] WmSchCantException. wmScSchoolBasicFeeQueryVO = {}",
                    JSONObject.toJSONString(wmScSchoolBasicFeeQueryVO), wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolBasicFeeList] Exception. wmScSchoolBasicFeeQueryVO = {}",
                    JSONObject.toJSONString(wmScSchoolBasicFeeQueryVO), e);
            return WmRestReturnUtil.fail("查询学校基础费率信息异常");
        }
    }

    /**
     * 删除单条学校楼宇信息
     * @param buildingId 楼宇主键ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "删除单条学校楼宇信息",
            description = "删除单条学校楼宇信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/deleteSchoolBuildingById",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/deleteSchoolBuildingById")
    @ResponseBody
    public Object deleteSchoolBuildingById(@RequestParam(value = "buildingId", defaultValue = "0") int buildingId,
                                           @RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            wmScSchoolService.deleteSchoolBuildingById(buildingId, schoolPrimaryId);
            return WmRestReturnUtil.success();
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.deleteSchoolBuildingSingle] WmSchCantException. buildingId = {}", buildingId, wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.deleteSchoolBuildingSingle] Exception. buildingId = {}", buildingId, e);
            return WmRestReturnUtil.fail("查询学校基础费率信息异常");
        }
    }

    /**
     * 添加/保存单条学校楼宇信息
     * @param wmScSchoolBuildingEditVO 楼宇信息
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "添加/保存单条学校楼宇信息",
            description = "添加/保存单条学校楼宇信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/saveSchoolBuildingSingle",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/saveSingleSchoolBuilding")
    @ResponseBody
    public Object saveSingleSchoolBuilding(@RequestBody WmScSchoolBuildingEditVO wmScSchoolBuildingEditVO) throws Exception {
        try {
            if (wmScSchoolBuildingEditVO == null
                    || wmScSchoolBuildingEditVO.getSchoolPrimaryId() == null
                    || wmScSchoolBuildingEditVO.getSchoolPrimaryId() <= 0) {
                return WmRestReturnUtil.fail("学校ID不存在");
            }
            wmScSchoolService.saveSingleSchoolBuilding(wmScSchoolBuildingEditVO);
            return WmRestReturnUtil.success();
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.saveSingleSchoolBuilding] WmSchCantException. wmScSchoolBuildingEditVO = {}",
                    JSONObject.toJSONString(wmScSchoolBuildingEditVO), wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.saveSingleSchoolBuilding] Exception. wmScSchoolBuildingEditVO = {}",
                    JSONObject.toJSONString(wmScSchoolBuildingEditVO), e);
            return WmRestReturnUtil.fail("保存学校楼宇异常");
        }
    }

    /**
     * 获取学校用户权限
     * @param schoolPrimaryId 学校主键ID
     * @param schoolModuleId 学校模块ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "获取学校用户权限",
            description = "获取学校用户权限",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/getSchoolUserAuth",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolUserAuth")
    @ResponseBody
    public Object getSchoolUserAuth(@RequestParam(value = "schoolPrimaryId", required = true) Integer schoolPrimaryId,
                                    @RequestParam(value = "schoolModuleId", required = true) Integer schoolModuleId) throws Exception {
        try {
            if (schoolPrimaryId == null || schoolModuleId == null || schoolPrimaryId <= 0) {
                return WmRestReturnUtil.fail("查询用户权限入参为空");
            }
            WmScSchoolAuthVO authVO = wmScAuthService.getSchoolUserAuth(schoolPrimaryId, schoolModuleId, UserUtils.getUser());
            return WmRestReturnUtil.success(authVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getSchoolUserAuth] WmSchCantException. schoolPrimaryId = {}, schoolModuleId = {}",
                    schoolPrimaryId, schoolModuleId, wmSchCantException);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolUserAuth] Exception. schoolPrimaryId = {}, schoolModuleId = {}",
                    schoolPrimaryId, schoolModuleId, e);
            return WmRestReturnUtil.fail("查询用户权限异常");
        }
    }

    /**
     * 合作平台模糊搜索接口
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "合作平台模糊搜索接口",
            description = "合作平台模糊搜索接口",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/getSchoolPlatformInfoByFuzzySearch",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping(value = "/school/getSchoolPlatformInfoByFuzzySearch", method = RequestMethod.GET)
    @ResponseBody
    public Object getSchoolPlatformInfoByFuzzySearch(String content) throws Exception {
        try {
            List<WmScSchoolPlatformFuzzySearchVO> voList = wmScSchoolService.getSchoolPlatformInfoByFuzzySearch(content);
            return WmRestReturnUtil.success(voList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolPlatformInfoByFuzzySearch] Exception.", e);
            return WmRestReturnUtil.fail("学校合作平台查询异常");
        }
    }

    /**
     * 判断合作平台选择其他时平台名称是否存在接口
     *
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "判断合作平台选择其他时平台名称是否存在接口",
            description = "判断合作平台选择其他时平台名称是否存在接口",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/isPlatformNameInPlatform",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping(value = "/school/isPlatformNameInPlatform", method = RequestMethod.GET)
    @ResponseBody
    public Object isPlatformNameInPlatform(@RequestParam(value = "content", defaultValue = "") String content) throws Exception {
        try {
            List<WmScSchoolPlatformFuzzySearchVO> voList = wmScSchoolService.getSchoolPlatformInfoByPlatformName(content);
            if (CollectionUtils.isEmpty(voList)) {
                return WmRestReturnUtil.success(false);
            }
            return WmRestReturnUtil.success(true, "该名称在“合作平台”中已有，请重新在“合作平台”中进行搜索");
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolPlatformInfoByFuzzySearch] Exception.", e);
            return WmRestReturnUtil.fail("学校合作平台查询异常");
        }
    }

    /**
     * 学校履约管控信息查询接口V2
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "学校履约管控信息查询接口V2",
            description = "学校履约管控信息查询接口V2",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/getSchoolPerformanceInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/getSchoolPerformanceInfo")
    @ResponseBody
    public Object getSchoolPerformanceInfo(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            WmScSchoolPerformanceVO wmScSchoolPerformanceVO = wmScSchoolService.getSchoolPerformanceInfo(schoolPrimaryId);
            return WmRestReturnUtil.success(wmScSchoolPerformanceVO);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolPerformanceInfo] Exception.", e);
            return WmRestReturnUtil.fail("学校履约管控信息查询异常");
        }
    }

    /**
     * 保存学校履约管控信息V2
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "保存学校履约管控信息V2",
            description = "保存学校履约管控信息V2",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/saveSchoolPerformanceInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/saveSchoolPerformanceInfo")
    @ResponseBody
    public Object saveSchoolPerformanceInfo(@RequestBody WmScSchoolPerformanceVO wmScSchoolPerformanceVO) throws Exception {
        try {
            wmScSchoolService.setSchoolPerformanceDefaultValue(wmScSchoolPerformanceVO);
            wmScSchoolService.saveSchoolPerformanceInfo(wmScSchoolPerformanceVO);
            return WmRestReturnUtil.success("", "保存成功");
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.saveSchoolPerformanceInfo] WmSchCantException. wmScSchoolPerformanceVO = {}", JSONObject.toJSONString(wmScSchoolPerformanceVO));
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.saveSchoolPerformanceInfo] Exception. wmScSchoolPerformanceVO = {}", JSONObject.toJSONString(wmScSchoolPerformanceVO), e);
            return WmRestReturnUtil.fail("保存履约管控信息异常");
        }
    }

    /**
     * 灰度接口 - 学校平台合作信息
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "灰度接口 - 学校平台合作信息",
            description = "灰度接口 - 学校平台合作信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/isSchoolInPlatformGrayList",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping(value = "/school/isSchoolInPlatformGrayList", method = RequestMethod.GET)
    @ResponseBody
    public Object isSchoolInPlatformGrayList(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            return WmRestReturnUtil.success(true);
        } catch (Exception e) {
            log.error("[WmScSchoolController.isSchoolInPlatformGrayList] Exception.", e);
            return WmRestReturnUtil.fail("查询学校灰度信息异常");
        }
    }

    /**
     * 灰度接口 - 学校履约管控信息
     * @throws Exception 异常
     */
    @MethodDoc(
            displayName = "灰度接口 - 学校履约管控信息",
            description = "灰度接口 - 学校履约管控信息",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/v1/w/sc/school/isSchoolInPerformanceGrayList",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping(value = "/school/isSchoolInPerformanceGrayList", method = RequestMethod.GET)
    @ResponseBody
    public Object isSchoolInPerformanceGrayList(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            return WmRestReturnUtil.success(true);
        } catch (Exception e) {
            log.error("[WmScSchoolController.isSchoolInPerformanceGrayList] Exception.", e);
            return WmRestReturnUtil.fail("查询学校灰度信息异常");
        }
    }

    /**
     * 查询学校交付页面Tab展示
     * @param schoolPrimaryId 学校主键ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "查询学校交付页面Tab展示",
            description = "查询学校交付页面Tab展示",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getTabStatus",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getTabStatus")
    @ResponseBody
    public Object getTabStatus(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            WmSchoolDeliveryTabStatusVO tabStatusVO = wmSchoolDeliveryService.getTabStatus(schoolPrimaryId);
            return WmRestReturnUtil.success(tabStatusVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getTabStatus] WmSchCantException. schoolPrimaryId = {}", schoolPrimaryId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getTabStatus] Exception. schoolPrimaryId = {}", schoolPrimaryId, e);
            return WmRestReturnUtil.fail("查询学校交付Tab信息异常");
        }
    }

    /**
     * 查询学校交付历史页面Tab展示
     * @param deliveryId 交付编号ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "查询学校交付历史页面Tab展示",
            description = "查询学校交付历史页面Tab展示",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getHistoryTabStatus",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getHistoryTabStatus")
    @ResponseBody
    public Object getHistoryTabStatus(@RequestParam(value = "deliveryId", defaultValue = "0") int deliveryId) throws Exception {
        try {
            WmSchoolDeliveryTabStatusVO tabStatusVO = wmSchoolDeliveryService.getHistoryTabStatus(deliveryId);
            return WmRestReturnUtil.success(tabStatusVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getHistoryTabStatus] WmSchCantException. deliveryId = {}", deliveryId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getHistoryTabStatus] Exception. deliveryId = {}", deliveryId, e);
            return WmRestReturnUtil.fail("查询学校交付历史Tab信息异常");
        }
    }

    /**
     * 根据姓名模糊搜索人员信息(CRM)
     * @param content 模糊搜索内容
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "根据姓名模糊搜索人员信息(CRM) for 元数据(交付人员指定、交付目标制定)",
            description = "根据姓名模糊搜索人员信息(CRM) for 元数据(交付人员指定、交付目标制定)",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getContactUserByFuzzySearch",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping( "/school/delivery/getContactUserByFuzzySearch")
    @ResponseBody
    public Object getContactUserByFuzzySearch(@RequestParam(value = "content", defaultValue = "") String content,
                                              @RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            List<WmSchoolDeliveryContactUserVO> resList = wmSchoolDeliveryService.getContactUserByFuzzySearch(content, schoolPrimaryId);
            return WmRestReturnUtil.success(resList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getContactUserByFuzzySearch] Exception. content = {}", content, e);
            return WmRestReturnUtil.fail("模糊搜索人员信息异常");
        }
    }

    @MethodDoc(
            displayName = "根据学校主键ID查询学校合伙人信息 for 元数据(交付人员指定)",
            description = "根据学校主键ID查询学校合伙人信息 for 元数据(交付人员指定)",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getSignPartnerInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping( "/school/delivery/getSignPartnerInfo")
    @ResponseBody
    public Object getSignPartnerInfo(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId) throws Exception {
        try {
            List<WmSchoolDeliveryContactUserVO> contactUserVOList = wmSchoolDeliveryService.getSignPartnerInfo(schoolPrimaryId);
            return WmRestReturnUtil.success(contactUserVOList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getContactUserByFuzzySearch] Exception. schoolPrimaryId = {}", schoolPrimaryId, e);
            return WmRestReturnUtil.fail("查询学校签约合伙人信息异常");
        }
    }

    @MethodDoc(
            displayName = "根据学校主键ID查询学校蜂窝负责人列表信息 for 元数据(交付人员指定)",
            description = "根据学校主键ID查询学校蜂窝负责人列表信息 for 元数据(交付人员指定)",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getSchoolAormList",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping( "/school/delivery/getSchoolAormList")
    @ResponseBody
    public Object getSchoolAormList(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") int schoolPrimaryId,
                                    @RequestParam(value = "content", defaultValue = "") String content) throws Exception {
        try {
            List<WmScMetadataCommonVO> commonVOList = wmSchoolDeliveryService.getSchoolAormList(schoolPrimaryId, content);
            return WmRestReturnUtil.success(commonVOList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolAormList] Exception. schoolPrimaryId = {}", schoolPrimaryId, e);
            return WmRestReturnUtil.fail("查询学校蜂窝负责人信息异常");
        }
    }

    /**
     * 交付完成-信息页面
     * @param schoolPrimaryId 学校主键ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "交付完成-信息页面",
            description = "交付完成-信息页面",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getDeliveryCompleteInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getDeliveryCompleteInfo")
    @ResponseBody
    public Object getDeliveryCompleteInfo(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") Integer schoolPrimaryId) throws Exception {
        try {
            WmSchoolDeliveryCompleteVO completeVO = wmSchoolDeliveryService.getDeliveryCompleteInfo(schoolPrimaryId);
            return WmRestReturnUtil.success(completeVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getDeliveryCompleteInfo] WmSchCantException. schoolPrimaryId = {}", schoolPrimaryId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), null, wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getDeliveryCompleteInfo] Exception. schoolPrimaryId = {}", schoolPrimaryId, e);
            return WmRestReturnUtil.fail("查询学校交付完成信息异常");
        }
    }

    /**
     * 交付历史信息页面
     * @param schoolPrimaryId 学校主键ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "交付历史-信息页面",
            description = "交付历史-信息页面",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getDeliveryCompleteInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getDeliveryHistoryList")
    @ResponseBody
    public Object getDeliveryHistoryList(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") Integer schoolPrimaryId) throws Exception {
        try {
            List<WmSchoolDeliveryHistoryVO> wmSchoolDeliveryHistoryVOList = wmSchoolDeliveryService.getDeliveryHistoryList(schoolPrimaryId);
            return WmRestReturnUtil.success(wmSchoolDeliveryHistoryVOList);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getDeliveryHistoryList] WmSchCantException. schoolPrimaryId = {}", schoolPrimaryId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getDeliveryHistoryList] Exception. schoolPrimaryId = {}", schoolPrimaryId, e);
            return WmRestReturnUtil.fail("查询学校交付历史信息异常");
        }
    }

    /**
     * 学校交付生效信息页面
     * @param schoolPrimaryId 学校主键ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校交付生效信息页面",
            description = "学校交付生效信息页面",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getEffectiveDeliveryInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getEffectiveDeliveryInfo")
    @ResponseBody
    public Object getEffectiveDeliveryInfo(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") Integer schoolPrimaryId,
                                           @RequestParam(value = "deliveryStreamNode", defaultValue = "0") Integer deliveryStreamNode) throws Exception {
        try {
            WmSchoolDeliveryEffectiveInfoVO effectiveInfoVO = wmSchoolDeliveryService.getEffectivePageDeliveryInfo(schoolPrimaryId, deliveryStreamNode);
            return WmRestReturnUtil.success(effectiveInfoVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getEffectiveDeliveryInfo] WmSchCantException. schoolPrimaryId = {}, deliveryStreamNode = {}", schoolPrimaryId, deliveryStreamNode);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getEffectiveDeliveryInfo] Exception. schoolPrimaryId = {}, deliveryStreamNode = {}", schoolPrimaryId, deliveryStreamNode, e);
            return WmRestReturnUtil.fail("查询学校交付生效信息异常");
        }
    }

    /**
     * 学校交付暂存信息页面
     * @param schoolPrimaryId 学校主键ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校交付暂存信息页面",
            description = "学校交付暂存信息页面",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getTempDeliveryInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getTempDeliveryInfo")
    @ResponseBody
    public Object getTempDeliveryInfo(@RequestParam(value = "schoolPrimaryId", defaultValue = "0") Integer schoolPrimaryId,
                                      @RequestParam(value = "deliveryStreamNode", defaultValue = "0") Integer deliveryStreamNode) throws Exception {
        try {
            WmSchoolDeliveryTempInfoVO tempInfoVO = wmSchoolDeliveryService.getTempPageDeliveryInfo(schoolPrimaryId, deliveryStreamNode);
            return WmRestReturnUtil.success(tempInfoVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getTempDeliveryInfo] WmSchCantException. schoolPrimaryId = {}, deliveryStreamNode = {}", schoolPrimaryId, deliveryStreamNode);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getTempDeliveryInfo] Exception. schoolPrimaryId = {}, deliveryStreamNode = {}", schoolPrimaryId, deliveryStreamNode, e);
            return WmRestReturnUtil.fail("查询学校交付暂存信息异常");
        }
    }

    /**
     * 学校交付历史信息页面
     * @param deliveryId 交付编号ID
     * @param deliveryStreamNode 交付节点
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校交付历史信息页面",
            description = "学校交付历史信息页面",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getHistoryDeliveryInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getHistoryDeliveryInfo")
    @ResponseBody
    public Object getHistoryDeliveryInfo(@RequestParam(value = "deliveryId", defaultValue = "0") Integer deliveryId,
                                         @RequestParam(value = "deliveryStreamNode", defaultValue = "0") Integer deliveryStreamNode) throws Exception {
        try {
            WmSchoolDeliveryHistoryInfoVO historyInfoVO = wmSchoolDeliveryService.getHistoryPageDeliveryInfo(deliveryId, deliveryStreamNode);
            return WmRestReturnUtil.success(historyInfoVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getHistoryDeliveryInfo] WmSchCantException. deliveryId = {}, deliveryStreamNode = {}", deliveryId, deliveryStreamNode);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getHistoryDeliveryInfo] Exception. deliveryId = {}, deliveryStreamNode = {}", deliveryId, deliveryStreamNode, e);
            return WmRestReturnUtil.fail("查询学校交付历史信息异常");
        }
    }

    /**
     * 学校交付审批信息页面
     * @param auditSystemId 审批任务系统ID
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校交付审批信息页面",
            description = "学校交付审批信息页面",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getHistoryDeliveryInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getAuditingDeliveryInfo")
    @ResponseBody
    public Object getAuditingDeliveryInfo(@RequestParam(value = "auditSystemId", defaultValue = "") String auditSystemId) throws Exception {
        try {
            WmSchoolDeliveryAuditInfoVO auditInfoVO = wmSchoolDeliveryService.getAuditPageDeliveryInfo(auditSystemId);
            return WmRestReturnUtil.success(auditInfoVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getAuditingDeliveryInfo] WmSchCantException. auditSystemId = {}", auditSystemId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getAuditingDeliveryInfo] Exception. auditSystemId = {}", auditSystemId, e);
            return WmRestReturnUtil.fail("查询学校交付审批信息异常");
        }
    }

    /**
     * 学校交付信息暂存
     * @param wmSchoolDeliverySaveVO wmSchoolDeliverySaveVO
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校交付信息暂存",
            description = "学校交付信息暂存",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/tempSaveDeliveryInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/tempSaveDeliveryInfo")
    @ResponseBody
    public Object tempSaveDeliveryInfo(@RequestBody WmSchoolDeliverySaveVO wmSchoolDeliverySaveVO) throws Exception {
        try {
            wmSchoolDeliveryService.tempSaveDeliveryInfo(wmSchoolDeliverySaveVO);
            return WmRestReturnUtil.success("", "暂存成功");
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.tempSaveDeliveryInfo] WmSchCantException. wmSchoolDeliverySaveVO = {}", JSONObject.toJSONString(wmSchoolDeliverySaveVO));
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.tempSaveDeliveryInfo] Exception. wmSchoolDeliverySaveVO = {}", JSONObject.toJSONString(wmSchoolDeliverySaveVO), e);
            return WmRestReturnUtil.fail("学校交付信息暂存异常");
        }
    }

    /**
     * 学校交付信息-提交审批
     * @param wmSchoolDeliverySaveVO wmSchoolDeliverySaveVO
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校交付信息-提交审批",
            description = "学校交付信息-提交审批",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/commitDeliveryInfo",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/submitDeliveryInfo")
    @ResponseBody
    public Object commitDeliveryInfo(@RequestBody WmSchoolDeliverySaveVO wmSchoolDeliverySaveVO) throws Exception {
        try {
            wmSchoolDeliveryService.submitDeliveryInfo(wmSchoolDeliverySaveVO);
            return WmRestReturnUtil.success("", "提交审批成功");
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.commitDeliveryInfo] WmSchCantException. wmSchoolDeliverySaveVO = {}", JSONObject.toJSONString(wmSchoolDeliverySaveVO));
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.commitDeliveryInfo] Exception. wmSchoolDeliverySaveVO = {}", JSONObject.toJSONString(wmSchoolDeliverySaveVO), e);
            return WmRestReturnUtil.fail("学校交付信息提交审批异常");
        }
    }

    /**
     * 学校交付信息-撤回审批
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "学校交付信息-提交审批",
            description = "学校交付信息-提交审批",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/cancelDeliveryAuditTask",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/cancelDeliveryAuditTask")
    @ResponseBody
    public Object commitDeliveryInfo(@RequestParam(value = "auditTaskId", defaultValue = "") Integer auditTaskId) throws Exception {
        try {
            wmSchoolDeliveryService.cancelDeliveryAuditTask(auditTaskId);
            return WmRestReturnUtil.success("", "撤回审批成功");
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.cancelDeliveryAuditTask] WmSchCantException. auditTaskId = {}", auditTaskId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.cancelDeliveryAuditTask] Exception. auditTaskId = {}", auditTaskId, e);
            return WmRestReturnUtil.fail("学校交付信息撤回审批异常");
        }
    }

    /**
     * 查看普遍客户关系梳理人员手机号
     * @throws Exception java.lang.Exception
     */
    @MethodDoc(
            displayName = "查看普遍客户关系梳理人员手机号",
            description = "查看普遍客户关系梳理人员手机号",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getSchoolContactUserPhoneNum",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getSchoolContactUserPhoneNum")
    @ResponseBody
    public Object getSchoolContactUserPhoneNum(@RequestParam(value = "userId", defaultValue = "") String userId,
                                               @RequestParam(value = "schoolPrimaryId", defaultValue = "") Integer schoolPrimaryId) throws Exception {
        try {
            WmSchoolDeliveryContactUserVO contactUserVO = wmSchoolDeliveryService.getSchoolContactUserInfoByUserId(userId, schoolPrimaryId);
            return WmRestReturnUtil.success(contactUserVO);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.getSchoolContactUserPhoneNum] WmSchCantException. userId = {}, schoolPrimaryId = {}", userId, schoolPrimaryId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSchoolContactUserPhoneNum] Exception. userId = {}, schoolPrimaryId = {}", userId, schoolPrimaryId, e);
            return WmRestReturnUtil.fail("查看普遍客户手机号异常");
        }
    }

    @MethodDoc(
            displayName = "学校交付管理页面规则校验 for 元数据",
            description = "学校交付管理页面规则校验 for 元数据",
            parameters = {
                    @ParamDoc(
                            name = "multiId",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/validateRules",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/metadata/validateRules")
    @ResponseBody
    public Object validateRules(@RequestParam(value = "multiId", defaultValue = "") String multiId) throws Exception {
        try {
            List<MetadataFieldElResultDTO> resultDTOList = wmBrandMetadataServiceAdapter.validateRules(multiId);
            return WmRestReturnUtil.success(resultDTOList);
        } catch (WmSchCantException wmSchCantException) {
            log.warn("[WmScSchoolController.validateRules] WmSchCantException. multiId = {}", multiId);
            return WmRestReturnUtil.fail(wmSchCantException.getCode(), wmSchCantException.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.validateRules] Exception. multiId = {}", multiId, e);
            return WmRestReturnUtil.fail("数据信息校验异常");
        }
    }

    @MethodDoc(
            displayName = "获取学校交付目标制定-联系人部门枚举值",
            description = "获取学校交付目标制定-联系人部门枚举值"
    )
    @RequestMapping("/school/delivery/getContactDepartmentEnum")
    @ResponseBody
    public Object getContactDepartmentEnum() throws Exception {
        try {
            List<WmScMetadataCommonVO> commonVOList = wmSchoolDeliveryService.getContactDepartmentEnum();
            return WmRestReturnUtil.success(commonVOList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getContactDepartmentEnum] Exception. commonVOList.");
            return WmRestReturnUtil.fail("获取枚举值异常");
        }
    }

    @MethodDoc(
            displayName = "获取学校交付目标制定-联系人职务枚举值",
            description = "获取学校交付目标制定-联系人职务枚举值"
    )
    @RequestMapping("/school/delivery/getContactDutyEnum")
    @ResponseBody
    public Object getContactDutyEnum() throws Exception {
        try {
            List<WmScMetadataCommonVO> commonVOList = wmSchoolDeliveryService.getContactDutyEnum();
            return WmRestReturnUtil.success(commonVOList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getContactDutyEnum] Exception. commonVOList.");
            return WmRestReturnUtil.fail("获取枚举值异常");
        }
    }

    @MethodDoc(
            displayName = "获取学校交付目标制定-联系人角色枚举值",
            description = "获取学校交付目标制定-联系人角色枚举值"
    )
    @RequestMapping("/school/delivery/getContactRoleEnum")
    @ResponseBody
    public Object getContactRoleEnum() throws Exception {
        try {
            List<WmScMetadataCommonVO> commonVOList = wmSchoolDeliveryService.getContactRoleEnum();
            return WmRestReturnUtil.success(commonVOList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getContactRoleEnum] Exception. commonVOList.");
            return WmRestReturnUtil.fail("获取枚举值异常");
        }
    }

    @MethodDoc(
            displayName = "获取学校交付目标制定-联系人立场枚举值",
            description = "获取学校交付目标制定-联系人立场枚举值"
    )
    @RequestMapping("/school/delivery/getContactPositionEnum")
    @ResponseBody
    public Object getContactPositionEnum() throws Exception {
        try {
            List<WmScMetadataCommonVO> commonVOList = wmSchoolDeliveryService.getContactPositionEnum();
            return WmRestReturnUtil.success(commonVOList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getContactPositionEnum] Exception. commonVOList.");
            return WmRestReturnUtil.fail("获取枚举值异常");
        }
    }

    @MethodDoc(
            displayName = "获取学校交付目标制定-联系人利益分析枚举值",
            description = "获取学校交付目标制定-联系人利益分析枚举值"
    )
    @RequestMapping("/school/delivery/getContactBenefitAnaysisEnum")
    @ResponseBody
    public Object getContactBenefitAnaysisEnum() throws Exception {
        try {
            List<WmScMetadataCommonVO> commonVOList = wmSchoolDeliveryService.getContactBenefitAnaysisEnum();
            return WmRestReturnUtil.success(commonVOList);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getContactBenefitAnaysisEnum] Exception. commonVOList.");
            return WmRestReturnUtil.fail("获取枚举值异常");
        }
    }

    @MethodDoc(
            displayName = "获取学校交付审批任务链接(BD任务系统链接)",
            description = "获取学校交付审批任务链接(BD任务系统链接)",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getAuditTaskTicketSystemLink",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getAuditTaskTicketSystemLink")
    @ResponseBody
    public Object getAuditTaskTicketSystemLink(@RequestParam(value = "auditSystemId", defaultValue = "") String auditSystemId)throws Exception {
        try {
            String ticketSystemLink = wmSchoolDeliveryService.getAuditTaskTicketSystemLink(auditSystemId);
            return WmRestReturnUtil.success(ticketSystemLink, "复制链接成功");
        } catch (WmSchCantException e) {
            log.warn("[WmScSchoolController.getAuditTaskTicketSystemLink] WmSchCantException. auditSystemId = {}", auditSystemId);
            return WmRestReturnUtil.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getAuditTaskTicketSystemLink] Exception. auditSystemId = {}", auditSystemId);
            return WmRestReturnUtil.fail("获取审批链接异常");
        }
    }

    @MethodDoc(
            displayName = "根据实际完成时间和预计完成时间计算交付跟进目标当前状态",
            description = "根据实际完成时间和预计完成时间计算交付跟进目标当前状态",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "/customer/v1/w/sc/school/delivery/getDeliveryFollowUpGoalStatus",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/delivery/getDeliveryFollowUpGoalStatus")
    @ResponseBody
    public Object getDeliveryFollowUpGoalStatus(@RequestParam(value = "expTime", defaultValue = "") String expTime,
                                                @RequestParam(value = "finTime", defaultValue = "") String finTime)throws Exception {
        try {
            Integer goalStatus = wmSchoolDeliveryThriftService.getDeliveryFollowUpGoalStatusByExpTimeAndFinTime(expTime, finTime);
            return WmRestReturnUtil.success(goalStatus, "查询成功");
        } catch (WmSchCantException e) {
            log.warn("[WmScSchoolController.getDeliveryFollowUpGoalStatus] WmSchCantException. expTime = {}, finTime= {}", expTime, finTime, e);
            return WmRestReturnUtil.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("[WmScSchoolController.getDeliveryFollowUpGoalStatus] Exception. expTime = {}, finTime = {}", expTime, finTime, e);
            return WmRestReturnUtil.fail("查询交付跟进当前状态异常");
        }
    }

    @MethodDoc(
            description = "学校支持代理商蜂窝降级开关"
    )
    @RequestMapping("/school/getSupportAgentAorSwitch")
    @ResponseBody
    public Object getSupportAgentAorSwitch() throws Exception {
        try {
            return WmRestReturnUtil.success(true);
        } catch (Exception e) {
            log.error("[WmScSchoolController.getSupportAgentAorSwitch] Exception.", e);
            return WmRestReturnUtil.fail("查询灰度异常");
        }
    }


    @MethodDoc(
            description = "学校分类上传文件进行初始化",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping("/school/uploadSchoolInfoList")
    @ResponseBody
    public Object uploadSchoolInfoList(HttpServletRequest request) {
        log.info("[WmScSchoolController.uploadSchoolInfoList] start");

        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("用户未登录");
        }
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request,
                MultipartHttpServletRequest.class);
        MultipartFile file = multipartRequest.getFile("file");
        if (null == file) {
            return WmRestReturnUtil.fail(1, "参数不合法", "");
        }

        if (file.getSize() > MccScConfig.getCanteenVideoMaxSize() * 1024) {
            return WmRestReturnUtil.fail(1, "", "上传文件过大，文件大小不能超过" + MccScConfig.getCanteenVideoMaxSize()/1024 + "MB");
        }

        // 文件格式校验
//        if (!FilenameUtils.isExtension(file.getOriginalFilename().toLowerCase(), new String[]{"mp4"})) {
//            return WmRestReturnUtil.fail(1, "", "视频格式仅支持mp4");
//        }

        try  {
            byte[] bytes = file.getBytes();
            return schoolExcelBatchApplication.batchAssign(bytes);
        } catch (Exception e) {
            log.info("[WmScCanteenController.uploadCanteenInfoList] 读取文件失败", e);
            throw new BusinessException("[WmScCanteenController.uploadCanteenInfoList] 读取文件失败");
        }
    }
}
