package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.waimai.thrift.constatnt.config.DimensionTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditRejectReason;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditTask;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.rateApproval.constant.ApprovalStatusEnum;
import com.sankuai.meituan.waimai.thrift.rateApproval.constant.ApprovalTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.rateApproval.service.WmProcessApprovalThriftService;
import com.sankuai.meituan.waimai.thrift.rateApproval.vo.request.ProcessApprovalV2Request;
import com.sankuai.meituan.waimai.thrift.rateApproval.vo.response.ProcessApprovalV2Response;
import com.sankuai.meituan.waimai.thrift.service.WmAuditRejectReasonService;
import com.sankuai.meituan.waimai.thrift.service.WmAuditTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WmAuditServiceAdaptor {

    @Autowired
    private WmAuditTaskService.Iface wmAuditTaskService;

    @Autowired
    private WmAuditRejectReasonService.Iface wmAuditRejectReasonService;

    @Autowired
    private WmProcessApprovalThriftService wmProcessApprovalThriftService;


    /**
     * 根据任务ID查询蜂鸟任务信息
     * @param auditTaskId 任务ID
     * @return WmAuditTask
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public WmAuditTask getTaskById(Integer auditTaskId) throws WmSchCantException, TException {
        try {
            log.info("[WmAuditServiceAdaptor.getTaskById] input param: auditTaskId = {}", auditTaskId);
            WmAuditTask auditTask = wmAuditTaskService.getTaskByID(auditTaskId);
            log.info("[WmAuditServiceAdaptor.getTaskById] auditTask = {}", JSONObject.toJSONString(auditTask));

            return auditTask;
        } catch (WmServerException e) {
            log.error("[WmAuditServiceAdaptor.getTaskById] WmServerException. auditTaskId = {}", auditTaskId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询蜂鸟任务信息异常");
        }
    }


    /**
     * 根据业务id和业务类型获取蜂鸟审核任务
     *
     * @param bizId
     * @param bizType WmAuditTaskBizTypeConstant
     */
    public WmAuditTask getTask(int bizId, int bizType) {
        log.info("getTask bizId={},bizType={}", bizId, bizType);
        try {
            return wmAuditTaskService.getNewsetTask(bizId, bizType);
        } catch (WmServerException e) {
            log.warn("wmAuditTaskService.getNewsetTask 失败 bizId={},bizType={}", bizId, bizType, e);
        } catch (Exception e) {
            log.error("wmAuditTaskService.getNewsetTask 失败 bizId={},bizType={}", bizId, bizType, e);
        }
        return null;
    }

    /**
     * 根据蜂鸟审核任务id获取驳回原因
     *
     * @param taskId
     * @return
     */
    public List<WmAuditRejectReason> getRejectReson(int taskId) {
        try {
            return wmAuditRejectReasonService.getBytaskId(taskId);
        } catch (WmServerException e) {
            log.warn("wmAuditRejectReasonService.getBytaskId 失败 taskId={}", taskId, e);
        } catch (Exception e) {
            log.warn("wmAuditRejectReasonService.getBytaskId 失败 taskId={}", taskId, e);
        }
        return Lists.newArrayList();
    }

    /**
     * 查询法人授权特批任务
     *
     * @param businessId 营业执照编号
     * @return
     */
    public Long getLegalAuthSpecialTask(String businessId) {
        try {
            ProcessApprovalV2Request processApprovalV2Request = new ProcessApprovalV2Request();
            processApprovalV2Request.setBusinessId(businessId);
            processApprovalV2Request.setDimensionId(DimensionTypeEnum.LICENSE);
            processApprovalV2Request.setStatus(Lists.newArrayList(ApprovalStatusEnum.APPROVED));
            processApprovalV2Request.setTaskType(Lists.newArrayList(ApprovalTaskTypeEnum.AGENT_SIGN));
            List<ProcessApprovalV2Response> responses =
                    wmProcessApprovalThriftService.queryProcessApprovalListV2(Lists.newArrayList(processApprovalV2Request));
            log.info("getLegalAuthSpecialTask,processApprovalV2Request={},responses={}", JSON.toJSONString(processApprovalV2Request), JSON.toJSONString(responses));
            if (CollectionUtils.isEmpty(responses)) {
                return null;
            }
            int currDateTime = TimeUtil.unixtime();
            for (ProcessApprovalV2Response processApprovalV2Response : responses) {
                if (processApprovalV2Response.getEndDate() > currDateTime) {
                    return processApprovalV2Response.getTaskId();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("getLegalAuthSpecialTask，查询法人授权特批任务接口发生异常,businessId={}", businessId, e);
        }
        return null;
    }

}
