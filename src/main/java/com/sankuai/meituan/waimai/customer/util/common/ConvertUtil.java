package com.sankuai.meituan.waimai.customer.util.common;

import com.sankuai.meituan.waimai.customer.exception.GateWayException;
import java.lang.reflect.Field;
import org.apache.commons.lang3.StringUtils;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2022/11/18 8:50 PM
 */
public  class ConvertUtil {

    private ConvertUtil(){
        // construct
    }

    public static GateWayException wrapGateWayException(Exception ex) {
        if (ex instanceof GateWayException) {
            return (GateWayException) ex;
        } else if (ex.getCause() instanceof GateWayException) {
            return (GateWayException) ex.getCause();
        }
        return new GateWayException(getExceptionMessage(ex), ex);
    }

    public static String getExceptionMessage(Exception ex) {
        String msg = ex.getMessage();
        if (StringUtils.isEmpty(msg)) {
            try {
                Field[] fields = ex.getClass().getDeclaredFields();

                for (int i = 0; i < fields.length; ++i) {
                    if ("msg".equals(fields[i].getName())) {
                        fields[i].setAccessible(true);
                        Object obj = fields[i].get(ex);
                        msg = obj == null ? "" : obj.toString();
                        break;
                    }
                }
            } catch (Exception var5) {
                throw new RuntimeException(var5);
            }
        }

        if (StringUtils.isEmpty(msg)) {
            for (Throwable cause = ex.getCause(); cause != null; cause = cause.getCause()) {
                msg = cause.getMessage();
                if (!StringUtils.isEmpty(msg)) {
                    break;
                }
            }
        }

        if (StringUtils.isEmpty(msg)) {
            msg = ex.getClass().getName();
        }

        return msg;
    }

    public static String getMsgByException(Exception ex) {
        String msg = ex.getMessage();
        if (StringUtils.isEmpty(msg)) {
            try {
                Field[] fields = ex.getClass().getDeclaredFields();

                for (int i = 0; i < fields.length; ++i) {
                    if ("msg".equals(fields[i].getName())) {
                        fields[i].setAccessible(true);
                        Object obj = fields[i].get(ex);
                        msg = obj == null ? "" : obj.toString();
                        break;
                    }
                }
            } catch (Exception var5) {
                throw new RuntimeException(var5);
            }
        }

        if (StringUtils.isEmpty(msg)) {
            for (Throwable cause = ex.getCause(); cause != null; cause = cause.getCause()) {
                msg = cause.getMessage();
                if (!StringUtils.isEmpty(msg)) {
                    break;
                }
            }
        }

        if (StringUtils.isEmpty(msg)) {
            msg = ex.getClass().getName();
        }

        return msg;
    }
}
