/*
* Copyright (c) 2016 meituan.com. All Rights Reserved.
*/
package com.sankuai.meituan.waimai.customer.mq.service;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mafka.client.producer.AsyncProducerResult;
import com.meituan.mafka.client.producer.FutureCallback;
import com.sankuai.meituan.waimai.customer.mq.TopicConstants;
import com.sankuai.meituan.waimai.customer.mq.domain.MqRecord;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Author:jinhu
 * Date:2017/3/16
 * Time:上午11:40
 */
@Service
public class MafkaMessageSendManager implements InitializingBean {
    private static final Logger log = LoggerFactory.getLogger(MafkaMessageSendManager.class);

    private Map<String, MafkaProducer> mafkaProducerMap = new ConcurrentHashMap<>();

    @Autowired
    private List<MafkaProducer> customerProducerList;

    @Override
    public void afterPropertiesSet() throws Exception {
        for (MafkaProducer mafkaProducer : customerProducerList) {
            mafkaProducerMap.put(mafkaProducer.getTopic(), mafkaProducer);
        }
    }

    @Resource
    private MqRecordService mqRecordService;

    private void sendToBroker(List<MqRecord> mqRecordList) {
        if (CollectionUtils.isEmpty(mqRecordList)) {
            return;
        }
        for (final MqRecord mqRecord : mqRecordList) {
            try {
                MafkaProducer producer = mafkaProducerMap.get(mqRecord.getTopic());
                if (producer == null) {
                    log.warn("Producer未注册，topic：{}", mqRecord.getTopic());
                    continue;
                }
                // 异步发送消息
                producer.sendAsyncMessage(mqRecord.getContent(), new FutureCallback() {
                    MqRecord currentMqRecord = mqRecord;

                    @Override
                    public void onSuccess(AsyncProducerResult result) {
                        log.info("mafka2 sendAsyncMessage succ! mqRecord = {}", JSON.toJSONString(currentMqRecord));
                        mqRecordService.updateSuccess(currentMqRecord);
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        log.info("mafka消息发送失败,下次重试;消息Id={}", currentMqRecord.getId(), t);
                        mqRecordService.updateFailed(currentMqRecord);
                    }
                });

            } catch (Exception e) {
                log.error("mafka消息发送异常：", e);
            }
        }
    }

    public void send(Integer bizId, String topic, List<String> contentList) {
        List<MqRecord> mqRecordList = wrapMqRecord(bizId, topic, contentList);
        mqRecordService.batchInsert(mqRecordList);
        this.sendToBroker(mqRecordList);
    }

    private void send(Integer bizId, String topic, String content) {
        send(bizId, topic, Arrays.asList(content));
    }

    public void send(CustomerMQBody mqBody) {
        log.info("mqBody = {}", JSON.toJSONString(mqBody));
        send(mqBody.getCustomerId(), ConfigUtilAdapter.getString("customer_state_notice_topic_name", "customer.state.notice_dev"), JSON.toJSONString(mqBody));
    }

    public void send(List<CustomerMQBody> mqBodyList) {
        if (CollectionUtils.isEmpty(mqBodyList)) {
            return;
        }
        for (CustomerMQBody mqBody : mqBodyList) {
            send(mqBody);
        }
    }

    @Crane("customer_MQ_messageRetry")
    public void messageRetry() {
        List<Integer> pendingIds = mqRecordService.getPendingIds();
        log.info("待发送Ids:{}", pendingIds);

        if (CollectionUtils.isEmpty(pendingIds)) {
            return;
        }
        if (pendingIds.size() > 100) {
            log.error("Mafka消息堆积过多,条数是:{}", pendingIds.size());
        }

        List<List<Integer>> pendingLists = Lists.partition(pendingIds, 50);
        for (List<Integer> pendingList : pendingLists) {
            List<MqRecord> mqRecordList = mqRecordService.getRecordsByIdList(pendingList);
            this.sendToBroker(mqRecordList);
        }
    }

    private List<MqRecord> wrapMqRecord(Integer customerId, String topic, List<String> contentList) {
        List<MqRecord> mqRecordList = Lists.newArrayList();
        for (String content : contentList) {
            MqRecord mqRecord = new MqRecord();
            mqRecord.setCustomerId(customerId);
            mqRecord.setTopic(topic);
            mqRecord.setContent(content);
            int retryTime = (int) (System.currentTimeMillis() / 1000 + 5);
            mqRecord.setRetryTime(retryTime);
            mqRecord.setFailedTimes(0);
            mqRecordList.add(mqRecord);
        }
        return mqRecordList;
    }

}