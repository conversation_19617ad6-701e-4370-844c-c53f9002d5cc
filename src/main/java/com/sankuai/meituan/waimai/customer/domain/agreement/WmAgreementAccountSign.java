package com.sankuai.meituan.waimai.customer.domain.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-10-26 21:40
 * Email: <EMAIL>
 * Desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WmAgreementAccountSign {

    private Long id;

    private Long accountId;

    private Integer agreementId;

    private Integer agreementType;

    private Integer status;

    private Integer ctime;

    private Integer utime;

    private Byte valid;
}
