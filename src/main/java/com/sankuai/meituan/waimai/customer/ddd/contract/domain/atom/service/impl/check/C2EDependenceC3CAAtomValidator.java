package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.waimai.agent.yak.contract.client.exception.ContractException;
import com.meituan.waimai.agent.yak.contract.client.request.common.AgentIdRequest;
import com.meituan.waimai.agent.yak.contract.client.response.contract.ContractContextResponse;
import com.meituan.waimai.agent.yak.contract.client.service.ContractThrift;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.contract.thrift.domain.WmAgentContract;
import com.sankuai.meituan.waimai.contract.thrift.service.WmAgentContractThriftService;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.IContractValidator;
import com.sankuai.meituan.waimai.heron.agentcontract.dto.contract.WmAgentFwContractQueryByAgentIdRequestDTO;
import com.sankuai.meituan.waimai.heron.agentcontract.dto.contract.WmAgentFwContractQueryResponseDTO;
import com.sankuai.meituan.waimai.heron.agentcontract.exception.WmAgentFwException;
import com.sankuai.meituan.waimai.heron.agentcontract.service.WmAgentFwContractThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class C2EDependenceC3CAAtomValidator implements IContractValidator {

    private static Logger logger = LoggerFactory.getLogger(C2EDependenceC3CAAtomValidator.class);

    @Autowired
    private WmAgentContractThriftService.Iface wmAgentContractThriftService;

    @Autowired
    private WmAgentFwContractThriftService wmAgentFwContractThriftService;

    @Autowired
    private ContractThrift contractThrift;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {
        if (WmTempletContractTypeEnum.getByCode(contractBo.getBasicBo().getType()) != WmTempletContractTypeEnum.C2_E) {
            return true;
        }
        WmTempletContractSignBo partyBSigner = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyBSignerBo();
        WmAgentContract agentContract;
        try {
            if (ConfigUtilAdapter.getBoolean("getWmAgentContractOfflineByAgentId_replace", false)) {
                //接口迁移，迁移无异常getByWmAgentId_migrate使用默认值，false为降级值
                if(ConfigUtilAdapter.getBoolean("getByWmAgentId_migrate", true) && MccConfig.agentTransferProp()){
                    AgentIdRequest agentIdRequest = new AgentIdRequest();
                    agentIdRequest.setWmAgentId(partyBSigner.getSignId());
                    agentIdRequest.setEffective(1);
                    ContractContextResponse response = contractThrift.getByWmAgentId(agentIdRequest);
                    logger.info("C2EDependenceC3CAAtomValidator getByWmAgentId resp:{}", JSON.toJSONString(response));
                    if (response == null) {
                        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "对应的美团与合作商C3合同查询失败");
                    }
                }else{
                    WmAgentFwContractQueryByAgentIdRequestDTO requestDTO = new WmAgentFwContractQueryByAgentIdRequestDTO();
                    requestDTO.setWmAgentId(partyBSigner.getSignId());
                    requestDTO.setEffective(1);
                    WmAgentFwContractQueryResponseDTO responseDTO = wmAgentFwContractThriftService.getByWmAgentId(requestDTO);
                    if (responseDTO == null) {
                        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "对应的美团与合作商C3合同查询失败");
                    }
                }
            } else {
                agentContract = wmAgentContractThriftService.getWmAgentContractOfflineByAgentId(partyBSigner.getSignId());
                if (agentContract == null || agentContract.getEstatus() == 0) {
                    logger.info("WmCustomerContractBo error ,contractBo={}", JSONObject.toJSONString(contractBo));
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "对应的美团与合作商合同CA认证不成功");
                }
            }
        } catch (WmServerException e) {
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (TException e) {
            logger.error(e.getMessage(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "系统繁忙，请稍后重试");
        } catch (WmAgentFwException e) {
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch(ContractException e){
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        }

        return true;
    }

}
