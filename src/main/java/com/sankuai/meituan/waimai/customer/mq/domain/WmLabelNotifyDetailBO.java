package com.sankuai.meituan.waimai.customer.mq.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 20230920
 * @desc 标签变更消息详细对象
 */
@Data
public class WmLabelNotifyDetailBO {
    /**
     * 对象ID
     * 标签类型为门店的话这就是门店ID，标签类型为客户的话这就是客户ID
     */
    @JSONField(name = "subject_id")
    Integer subjectId;
    /**
     * 对象类型
     */
    @JSONField(name = "subject_type")
    Integer subjectType;
    /**
     * 操作人ID
     */
    @JSONField(name = "op_uid")
    Integer opUid;
    /**
     * 操作人名称
     */
    @JSONField(name = "op_uname")
    String opUname;
    /**
     * 标签ID
     */
    @JSONField(name = "wm_label_id")
    Integer wmLabelId;
}
