package com.sankuai.meituan.waimai.customer.adapter.daocan;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.mtcoop.thrift.dto.*;
import com.sankuai.meituan.mtcoop.thrift.exception.TCoopException;
import com.sankuai.meituan.mtcoop.thrift.exception.TIllegalArgumentException;
import com.sankuai.meituan.mtcoop.thrift.service.FrameCoopService;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.request.QueryWmCustomerContractReq;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/7 20:27
 */
@Slf4j
@Service
public class FrameCoopServiceAdapter {

    @Resource
    private FrameCoopService.Iface frameCoopService;

    public List<TFrameCoop> getFrameCoopsByPartnerId(int dcCustomerId) {
        try {
            log.info("FrameCoopServiceAdapter#getFrameCoopsByPartnerId, dcCustomerId: {}", dcCustomerId);
            List<TFrameCoop> frameCoopList = frameCoopService.getFrameCoopsByPartnerId(dcCustomerId);
            log.info("FrameCoopServiceAdapter#getFrameCoopsByPartnerId, frameCoopList: {}", JSON.toJSONString(frameCoopList));
            return frameCoopList;
        } catch (Exception e) {
            log.error("FrameCoopServiceAdapter#getFrameCoopsByPartnerId, error", e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询到餐合同状态
     *
     * @param contractProof 合同凭证
     * @return 合同状态
     * @throws WmCustomerException 异常
     */
    public int getFrameCoopStatus(String contractProof) throws WmCustomerException {
        try {
            log.info("FrameCoopServiceAdapter#getFrameCoopStatus, contractProof: {}", contractProof);
            TFrameCoop frameCoop = frameCoopService.getFrameCoop(contractProof);
            log.info("FrameCoopServiceAdapter#getFrameCoopStatus, frameCoop: {}", JSON.toJSONString(frameCoop));
            return frameCoop.getTFrame().getStatus();
        } catch (Exception e) {
            log.error("FrameCoopServiceAdapter#getFrameCoopStatus, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询到餐侧合同状态异常");
        }
    }

    /**
     * 判断是否有到餐写权限(用于到餐C2相关操作和取消到餐C1的权限校验)
     *
     * @param dcCustomerId 到餐客户ID
     * @param ssoid
     * @return
     * @throws TException
     * @throws TCoopException
     * @throws TIllegalArgumentException
     */
    public Boolean hasWriteAuthority(int dcCustomerId, int ssoid) throws TException, TCoopException, TIllegalArgumentException {
        log.info("FrameCoopServiceAdapter#hasWriteAuthority, dcCustomerId: {}, ssoid: {}", dcCustomerId, ssoid);
        TResult tResult = frameCoopService.isBdInPartnerPoiPrivateSea(dcCustomerId, ssoid);
        log.info("hasWriteAuthority tResult:{}", JSON.toJSON(tResult));
        return tResult.status;
    }

    /**
     * @param dcCustomerId 到餐客户ID
     * @param ssoid        操作人uid
     * @return 是否有权限创建到餐C1合同
     */
    public boolean hasCreateDcC1ContractAuth(int dcCustomerId, int ssoid) {
        try {
            log.info("FrameCoopServiceAdapter#hasCreateDcC1ContractAuth, dcCustomerId: {}, ssoid: {}", dcCustomerId, ssoid);
            TResult tResult = frameCoopService.canBdCreateFrameCoop(dcCustomerId, ssoid);
            log.info("FrameCoopServiceAdapter#hasCreateDcC1ContractAuth tResult:{}", JSON.toJSON(tResult));
            return tResult.status;
        } catch (Exception e) {
            log.error("FrameCoopServiceAdapter#hasCreateDcC1ContractAuth, error", e);
            return false;
        }
    }

    /**
     * 获取到餐C1合同列表
     *
     * @param dcCustomerId 到餐客户ID
     * @return
     * @throws TException
     * @throws TCoopException
     * @throws TIllegalArgumentException
     */
    public List<TFrameCoop> getDcC1ContractList(int dcCustomerId) throws TException, TCoopException, TIllegalArgumentException {
        List<TFrameCoop> frameCoops = frameCoopService.getFrameCoopsByPartnerId(dcCustomerId);
        if (CollectionUtils.isEmpty(frameCoops)) {
            return Collections.emptyList();
        }

        return frameCoops;
    }

}
