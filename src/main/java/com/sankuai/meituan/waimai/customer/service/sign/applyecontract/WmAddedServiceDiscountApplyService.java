package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractAddedServiceDiscountSmsWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.thrift.TException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 增值服务优惠
 */
@Service
public class WmAddedServiceDiscountApplyService extends AbstractWmEcontractApplyAdapterService{
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WmAddedServiceDiscountApplyService.class);

    private static final String TYPE_ADDED_SERVICE_DISCOUNT_CONFIRM = "type_added_service_discount_confirm";

    private static final String FLOW_ADDED_SERVICE_DISCOUNT_CONFIRM = "added_service_discount_confirm";

    private static List<String> flowList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_ADDED_SERVICE_DISCOUNT_CONFIRM);

        dataWrapperMap.put(FLOW_ADDED_SERVICE_DISCOUNT_CONFIRM, EcontractDataWrapperEnum.ADDED_SERVICE_DISCOUNT);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractAddedServiceDiscountSmsWrapperService wmEcontractAddedServiceDiscountSmsWrapperService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        LOGGER.info("#WmAddedServiceDiscountApplyService#wrapEcontractBo,batchContextBo={}",JSON.toJSONString(batchContextBo));
        
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractAddedServiceDiscountSmsWrapperService.wrap(batchContextBo));

        LOGGER.info("#WmAddedServiceDiscountApplyService#wrapEcontractBo,batchInfoBoList={}",JSON.toJSONString(batchInfoBoList));

        return new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(TYPE_ADDED_SERVICE_DISCOUNT_CONFIRM)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .callBackUrl(getCallbackUrl()).econtractBatchSource(getSource(batchContextBo))
                .build();
    }
}
