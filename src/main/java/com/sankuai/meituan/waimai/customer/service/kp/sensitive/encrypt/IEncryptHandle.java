package com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
public interface IEncryptHandle {

    /**
     * 执行策略类型
     *
     * @return
     */
    KmsKeyNameEnum handleType();

    /**
     * 策略具体执行方法
     *
     * @param keyEncrypt
     * @return
     */
    EncryptResult execute(KeyEncrypt keyEncrypt) throws WmCustomerException;
}
