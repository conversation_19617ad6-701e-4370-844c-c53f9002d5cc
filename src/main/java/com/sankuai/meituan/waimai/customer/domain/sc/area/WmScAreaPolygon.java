package com.sankuai.meituan.waimai.customer.domain.sc.area;

import java.awt.*;

public class WmScAreaPolygon {

    public static int GRID_SHIFT = 4;
    /**
     * 经纬度被划分的份数
     */
    public static int GRID_GAP_NUM = 1 << GRID_SHIFT;
    /**
     * 记录格子状态需要的数组长度
     */
    public static int ARRAY_LENGTH = 1 << (GRID_SHIFT * 2 - 6);
    /**
     * 每个long型能记录的格子状态数量(1 << ARRAY_SHIFT)
     */
    public static int ARRAY_SHIFT = 6;
    /**
     * 划分的格子总数
     */
    public static int GRID_NUM = 1 << (GRID_SHIFT * 2);

    private final int[] coordinates;
    /**
     * 保存格子状态
     */
    private long[] gridStatus;
    /**
     * 格子状态是否计算完毕
     */
    private volatile boolean ready = false;

    public WmScAreaPolygon(int[] coordinates) {
        this.coordinates = coordinates;
    }

    public int[] getCoordinates() {
        return coordinates;
    }


    public boolean isReady() {
        return ready;
    }

    public void setReady(boolean ready) {
        this.ready = ready;
    }

    /**
     * 判断一个点是否在一个多边形内部
     * @param x 点的纬度
     * @param y 点的经度
     * @return 如果点在多边形内部，返回true；否则返回false
     */
    public boolean contains(int x, int y) {
        // 如果多边形没有准备好或者不是多边形，则直接返回false
        if (!checkPolygon())
            return false;
        // 获取多边形的坐标数组
        int[] tmpCoordinates = coordinates;
        // 记录射线与多边形的交点个数
        int hits = 0;
        // 获取多边形的边数
        int length = tmpCoordinates.length;
        // 获取多边形最后一个点的坐标
        int lastx = tmpCoordinates[length - 2];
        int lasty = tmpCoordinates[length - 1];
        int curx, cury;
        // 遍历多边形的每条边
        for (int i = 0; i < length; lastx = curx, lasty = cury, i += 2) {
            // 获取当前点的坐标
            curx = tmpCoordinates[i];
            cury = tmpCoordinates[i + 1];
            // 如果当前点与上一个点的纬度相同，则说明这条边是水平的，跳过
            if (cury == lasty) {
                continue;
            }
            // 判断射线是否与边相交
            if (cury < lasty) {
                if (y < cury || y >= lasty) {
                    continue;
                }
            } else {
                if (y < lasty || y >= cury) {
                    continue;
                }
            }

            if (curx < lastx) {
                if (x >= lastx) {
                    continue;
                }
                if (x < curx) {
                    hits++;
                    continue;
                }
            } else {
                if (x >= curx) {
                    continue;
                }
                if (x < lastx) {
                    hits++;
                    continue;
                }
            }
            // 计算射线与边的交点的横坐标
            double test1, test2;
            if (cury < lasty) {
                test1 = x - curx;
                test2 = y - cury;
            } else {
                test1 = x - lastx;
                test2 = y - lasty;
            }
            if (test1 < (test2 / (lasty - cury) * (lastx - curx))) {
                hits++;
            }
        }
        // 如果射线与多边形的交点个数为奇数，则说明点在多边形内部，返回true；否则返回false
        return ((hits & 1) != 0);
    }

    public void calcGridStatus(final int minLat, final int minLng, final int maxLat, final int maxLng) {

        this.ready = false;

        if (checkPolygon()) {
            calcGridStatusInner(minLat, minLng, maxLat, maxLng);
        }
        this.ready = true;
    }

    private boolean checkPolygon() {
        int[] tmpCoordinates = this.coordinates;
        if (tmpCoordinates == null || tmpCoordinates.length < 6) {
            return false;
        }
        if ((tmpCoordinates.length & 1) == 1) {
            return false;
        }
        return true;
    }

    private void calcGridStatusInner(int minLat, int minLng, int maxLat, int maxLng) {

        int latGap = (maxLat - minLat) >> GRID_SHIFT;
        int lngGap = (maxLng - minLng) >> GRID_SHIFT;
        if (latGap <= 0 || lngGap <= 0) {
            return;
        }

        int[] tmpCoordinates = this.coordinates;
        long[] gridStatus = new long[ARRAY_LENGTH * 2];

        int[] pointx = new int[tmpCoordinates.length >> 1];
        int[] pointy = new int[tmpCoordinates.length >> 1];
        for (int i = 0, j = 0; i < tmpCoordinates.length; i += 2, j++) {
            pointx[j] = tmpCoordinates[i];
            pointy[j] = tmpCoordinates[i + 1];
        }

        Polygon polygon = new Polygon(pointx, pointy, pointx.length);

        Rectangle rectangleGrid = new Rectangle();
        //计算每个格子的状态
        for (int i = 0; i < GRID_GAP_NUM; i++) {
            for (int j = 0; j < GRID_GAP_NUM; j++) {
                int x = minLat + j * latGap;
                int y = minLng + i * lngGap;
                int width = latGap;
                int height = lngGap;
                //最后一个格子做特殊处理，防止不能整除的情况
                if (j == GRID_GAP_NUM - 1) {
                    width = maxLat - (minLat + j * latGap);
                }
                if (i == GRID_GAP_NUM - 1) {
                    height = maxLng - (minLng + i * lngGap);
                }
                rectangleGrid.reshape(x, y, width, height);
                int gridIndex = i * GRID_GAP_NUM + j;
                int arrayIndex = gridIndex >> ARRAY_SHIFT;
                long shiftResult = 1L << gridIndex;
                //根据包含和交叉关系，分别设置对应位置的bit状态
                //00表示完全在配送范围外，10表示有交叉，11表示完全在配送范围里
                //由于大部分格子属于11的情况，所以先计算包含关系
                if (polygon.contains(rectangleGrid)) {
                    gridStatus[arrayIndex] |= shiftResult;
                    gridStatus[arrayIndex + ARRAY_LENGTH] |= shiftResult;
                } else if (polygon.intersects(rectangleGrid)) {
                    gridStatus[arrayIndex] |= shiftResult;
                }
            }
        }
        this.gridStatus = gridStatus;
        this.ready = true;
    }

    /**
     * 判断一个点是否在一个矩形内部
     * @param x 点的纬度
     * @param y 点的经度
     * @param minLat 矩形左下角的纬度
     * @param minLng 矩形左下角的经度
     * @param maxLat 矩形右上角的纬度
     * @param maxLng 矩形右上角的经度
     * @return 如果点在矩形内部，返回true；否则返回false
     */
    public boolean contains(int x, int y, int minLat, int minLng, int maxLat, int maxLng) {
        // 如果多边形没有准备好或者不是多边形，则直接调用另一个contains方法进行判断
        if (ready && checkPolygon()) {
            // 计算点所在的网格索引
            int r = calcGridIndex(x, y, minLat, minLng, maxLat, maxLng);
            if (r < 0) {
                // 如果点在多边形外部，则返回false
                return false;
            } else if (r > 0) {
                // 如果点在多边形内部，则返回true
                return true;
            }
            // 如果点在矩形内部，但是网格中没有多边形，则继续调用另一个contains方法进行判断
            return contains(x, y);
        }
        return contains(x, y);
    }

    /**
     * 计算一个点在网格中的索引
     * @param x 点的纬度
     * @param y 点的经度
     * @param minLat 矩形左下角的纬度
     * @param minLng 矩形左下角的经度
     * @param maxLat 矩形右上角的纬度
     * @param maxLng 矩形右上角的经度
     * @return 如果点在矩形外部，返回-1；如果点在矩形内部但是网格中没有多边形，返回0；如果点在矩形内部且网格中有多边形，返回1
     */
    public int calcGridIndex(int x, int y, int minLat, int minLng, int maxLat, int maxLng) {
        // 获取网格状态数组
        long[] gridStatus = this.gridStatus;
        if (gridStatus == null || gridStatus.length == 0) {
            return 0;
        }
        // 如果点在矩形外部，则返回0
        if (x <= minLat || x >= maxLat || y <= minLng || y >= maxLng) {
            return 0;
        }
        // 计算网格的行数和列数
        int latGap = (maxLat - minLat) >> GRID_SHIFT;
        int lngGap = (maxLng - minLng) >> GRID_SHIFT;
        // 如果网格的行数或列数小于等于0，则返回0
        if (latGap <= 0 || lngGap <= 0) {
            return 0;
        }
        int diffx = x - minLat;
        int diffy = y - minLng;
        // 计算点在网格中的行数和列数
        int xGap = diffx / latGap;
        int yGap = diffy / lngGap;
        // 计算网格的索引
        int gridIndex = xGap + yGap * GRID_GAP_NUM;
        // 如果网格的行数或列数超出范围，或者网格的索引超出范围，则返回0
        if (xGap >= GRID_GAP_NUM || yGap >= GRID_GAP_NUM || gridIndex >= GRID_NUM) {
            return 0;
        }
        // 计算网格状态数组中的索引和位移
        int arrayIndex = gridIndex >> ARRAY_SHIFT;
        long shiftResult = 1L << gridIndex;
        // 如果网格状态数组中的对应位为0，则返回-1
        if ((gridStatus[arrayIndex] & shiftResult) == 0) {
            return -1;
        }
        // 如果网格状态数组中的对应位为1，则返回1
        if ((gridStatus[arrayIndex + ARRAY_LENGTH] & shiftResult) == 0) {
            return 0;
        } else {
            return 1;
        }
    }
}
