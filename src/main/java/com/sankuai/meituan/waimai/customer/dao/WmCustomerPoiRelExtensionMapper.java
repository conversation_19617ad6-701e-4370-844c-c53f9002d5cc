package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiRelExtension;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface WmCustomerPoiRelExtensionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WmCustomerPoiRelExtension record);

    int insertSelective(WmCustomerPoiRelExtension record);

    WmCustomerPoiRelExtension selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmCustomerPoiRelExtension record);

    int updateByPrimaryKey(WmCustomerPoiRelExtension record);

    List<Long> getExistRelWmPoiIdList(@Param("wmPoiIdList") List<Long> wmPoiIdList,@Param("bizType")int bizType);

    List<Long> getExistRelWmPoiIdListMaster(@Param("wmPoiIdList") List<Long> wmPoiIdList,@Param("bizType")int bizType);

    List<WmCustomerPoiRelExtension> selectByWmPoiIdListAndBizType(@Param("wmPoiIdList") List<Long> wmPoiIdList,@Param("bizType")int bizType);

    WmCustomerPoiRelExtension selectByWmPoiIdAndBizType(@Param("wmPoiId") long wmPoiId,@Param("bizType")int bizType);
}