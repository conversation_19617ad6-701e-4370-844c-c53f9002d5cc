package com.sankuai.meituan.waimai.customer.service.sign.applytask.context;

import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WmEcontractWrapFactory {

    @Resource
    private WmEcontractCustomerWrapService wmEcontractCustomerWrapService;

    @Resource
    private WmEcontractPoiWrapService wmEcontractPoiWrapService;

    public EcontractTaskContextBo wrap(EcontractTaskApplyBo applyBo,EcontractTaskContextBo contextBo) throws WmCustomerException {
        AssertUtil.assertObjectNotNull(contextBo, "申请信息");

        if (EcontractTaskApplyBizTypeEnum.CUSTOMER_ID.equals(contextBo.getBizTypeEnum())) {
            return wmEcontractCustomerWrapService.wrap(applyBo,contextBo);
        } else if (EcontractTaskApplyBizTypeEnum.WM_POI_ID.equals(contextBo.getBizTypeEnum())) {
            return wmEcontractPoiWrapService.wrap(applyBo,contextBo);
        }
        return contextBo;
    }

}
