package com.sankuai.meituan.waimai.customer.bo.brand;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2023/4/25 6:06 PM
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandQueryResultBO {

    private Long id;
    private String brandName;
    private Integer brandLevel;
    private Integer brandType;
    private Integer certifyStatus;

}
