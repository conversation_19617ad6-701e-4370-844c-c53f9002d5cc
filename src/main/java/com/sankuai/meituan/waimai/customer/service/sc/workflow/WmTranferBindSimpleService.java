package com.sankuai.meituan.waimai.customer.service.sc.workflow;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.crm.ticket.thrift.exception.WmTicketServerException;
import com.sankuai.meituan.waimai.crm.ticket.thrift.service.WmCrmTicketThriftService;
import com.sankuai.meituan.waimai.customer.adapter.WdcRelationServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.constant.sc.WmChangeBindPoiLogTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallWmPoiBindBO;
import com.sankuai.meituan.waimai.customer.service.sc.WmScLogService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTagV2Service;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTairService;
import com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallBindService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallManageService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.check.WmCanteenStallCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallClueBindService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallPoiBindService;
import com.sankuai.meituan.waimai.customer.service.sc.check.WmCanteenPoiCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenInfoService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiTaskSimpleService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueGenerateStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallManageTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskBaseBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskSumBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScCanIllegalPoi;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallBindSubmitDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.util.ObjectMapperUtils;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.service.sc.workflow.WmCanteenPoiBindService.CANTEEN_POI_TASK_LOCKKEY_PREFIX;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_CANTEEN_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_CANTEEN_STALL_BIND_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

@Slf4j
@Service
public class WmTranferBindSimpleService implements WmCanteenPoiTaskSimpleService{


    @Autowired
    private WmCanteenPoiCheckService wmCanteenPoiCheckService;

    @Autowired
    private WmScCanteenPoiTaskMapper wmScCanteenPoiTaskMapper;

    @Autowired
    private WmCanteenPoiBindService wmCanteenPoiBindService;

    @Autowired
    private WmCrmTicketThriftService.Iface wmCrmTicketThriftService;

    @Autowired
    private WmAuditPersonService wmAuditPersonService;

    @Autowired
    private WmScCanteenInfoService wmScCanteenInfoService;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    @Autowired
    private WmScCanteenPoiTaskDetailMapper wmScCanteenPoiTaskDetailMapper;

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    @Autowired
    private WmScCanteenPoiTaskSimpleService wmScCanteenPoiTaskSimpleService;

    @Autowired
    private WmCanteenStallBindService wmCanteenStallBindService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private WmScCanteenPoiService wmScCanteenPoiService;


    @Autowired
    private WmScCanteenPoiAuditDetailMapper wmScCanteenPoiAuditDetailMapper;

    @Autowired
    private WmScCanteenPoiAuditMapper wmScCanteenPoiAuditMapper;


    @Autowired
    private WmScCanteenPoiLabelMapper wmScCanteenPoiLabelMapper;

    @Autowired
    private WmScTagV2Service wmScTagV2Service;


    @Autowired
    private WmScCanteenPoiHistoryMapper wmScCanteenPoiHistoryMapper;

    @Autowired
    private WmPoiAttributesMapper wmPoiAttributesMapper;


    @Autowired
    private WmScLogService wmScLogService;

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Autowired
    private WmCanteenStallManageService wmCanteenStallManageService;

    @Autowired
    private WdcRelationServiceAdapter wdcRelationServiceAdapter;

    @Autowired
    private WmCanteenStallClueBindService wmCanteenStallClueBindService;

    @Autowired
    private WmCanteenStallPoiBindService wmCanteenStallPoiBindService;


    @Autowired
    private WmCanteenStallCheckService wmCanteenStallCheckService;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmScTairService wmScTairService;

    private static final int IS_IN_GRAY=1;

    // 门店删除
    public static final int WM_POI_ID_IS_DELETE = 1;



    @Override
    public CanteenPoiTaskTypeEnum getTaskType() {
        return CanteenPoiTaskTypeEnum.TRANSFER_BIND;
    }

    @Override
    public void checkTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("WmTranferBindSimpleService valid wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        wmCanteenPoiCheckService.tranferBindvalidSubmit(wmCanteenPoiTaskBO);
    }

    /**
     * 食堂换绑门店创建食堂门店审核任务
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @return 任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public long createTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("[WmCanteenPoiTranferBindService.createTask] wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));

        // 第一步: 在食堂门店任务主表(wm_sc_canteen_poi_task)中新增一条数据, 返回主键ID即任务ID
        long taskId = insertWmCanteenPoiTask(wmCanteenPoiTaskBO);
        wmCanteenPoiTaskBO.setId(taskId);

        // 第二步: 在食堂门店任务子表(wm_sc_canteen_poi_task_detail)中新增N条数据, N = 绑定门店数
        wmCanteenPoiBindService.insertWmCanteenPoiTaskDetail(wmCanteenPoiTaskBO);

        // 第三步: 获取任务节点信息
        Integer nextAuditNode = CanteenAuditProgressEnum.getNextNode(wmCanteenPoiTaskBO.getAuditNodeTypeEnum(), null);
        // 第四步：创建任务
        createCanteenTaskWorkflow(wmCanteenPoiTaskBO, nextAuditNode);

        //  第五步： 提交成功后更新状态 1、更新任务主表的任务审核进度状态
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);
        if (taskId > 0 && auditNodeEnum != null) {
            wmScCanteenPoiTaskMapper.updateAuditStatusByPrimaryKey(taskId, auditNodeEnum.getAuditStatus());
        } else {
            log.error("createTask 出现异常任务 wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        }

        // 第六步：更新档口绑定任务的外卖门店绑定状态
        wmCanteenStallBindService.updateWmBindIdListBindStatus(wmCanteenPoiTaskBO.getBindIdList(),CanteenStallWmPoiBindStatusEnum.REBINDING.getType());

        // 第七步：记录日志
        transfePublisCanteenPoiLog(wmCanteenPoiTaskBO);
        return taskId;
    }
    /**
     * 在食堂门店审核任务主表(wm_sc_canteen_poi_task)中, 新增一条数据
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @return 主键ID, 即任务ID
     */
    public long insertWmCanteenPoiTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        log.info("[WmCanteenPoiTranferBindService.insertWmCanteenPoiTask] wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = new WmScCanteenPoiTaskDO();
        wmScCanteenPoiTaskDO.setCanteenIdFrom(wmCanteenPoiTaskBO.getCanteenIdFrom());
        wmScCanteenPoiTaskDO.setCanteenIdTo(wmCanteenPoiTaskBO.getCanteenIdTo());
        wmScCanteenPoiTaskDO.setAuditStatus(CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT.getCode());
        wmScCanteenPoiTaskDO.setAuditNodeType(wmCanteenPoiTaskBO.getAuditNodeTypeEnum().getCode());
        wmScCanteenPoiTaskDO.setValid(ValidEnum.VALID.getTypeInt());
        wmScCanteenPoiTaskDO.setTaskType(wmCanteenPoiTaskBO.getTaskType());
        wmScCanteenPoiTaskDO.setTaskReasonType(wmCanteenPoiTaskBO.getTaskReasonType());
        wmScCanteenPoiTaskDO.setTaskReason(wmCanteenPoiTaskBO.getTaskReason());
        wmScCanteenPoiTaskDO.setUserId(wmCanteenPoiTaskBO.getUserId());
        wmScCanteenPoiTaskDO.setUserName(wmCanteenPoiTaskBO.getUserName());
        wmScCanteenPoiTaskDO.setProofMaterialImage(wmCanteenPoiTaskBO.getProofMaterialImage().toString());
        wmScCanteenPoiTaskMapper.insertSelective(wmScCanteenPoiTaskDO);
        return wmScCanteenPoiTaskDO.getId();
    }

    public void transfePublisCanteenPoiLog(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        new Thread(new TraceRunnable(() -> {
            try {
                transfePublisCanteenPoiLogStart(wmCanteenPoiTaskBO);
            } catch (Exception e) {
                log.error("[WmCanteenStallManageService.bindCanteenPoiByWmPoiBindAsync] Exception. wmCanteenPoiTaskBO = {}",
                        JSONObject.toJSONString(wmCanteenPoiTaskBO), e);
            }
        })).start();
    }
    public void transfePublisCanteenPoiLogStart(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        log.info("transfePublisCanteenPoiLogStart wmCanteenPoiTaskBO",JSONObject.toJSONString(wmCanteenPoiTaskBO));
        List<Long> wmPoiIdList = wmCanteenPoiTaskBO.getWmPoiIdList().stream().collect(Collectors.toList());
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindMapper.selectByWmPoiIdsAndCanteenPriIdWitchStatus((int) CanteenStallWmPoiBindStatusEnum.REBINDING.getType(),wmPoiIdList,wmCanteenPoiTaskBO.getCanteenFrom().getId());
        if (bindDOList == null) {
            bindDOList = Collections.emptyList();
        }
        for (WmCanteenStallBindDO bindDO : bindDOList) {
            // 判断wmPoiId是否存在,如果不存在统一放到一个列表里面
            if (bindDO.getWmPoiId() == null || bindDO.getWmPoiId() <= 0) {

            } else {
                String logInfo = "操作：提交门店换绑申请\\n" + "门店ID：" + bindDO.getWmPoiId() + "\\n" + "档口绑定任务ID：" + bindDO.getId() + "\\n"
                        + "[字段变更] 外卖门店绑定状态：" + CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getName() + "=>换绑中\\n";
                //* "[字段变更] 档口绑定任务：" + statusEnum.getName() + "=>绑定成功\\n" + *//*

                // 档口绑定任务操作日志
                wmScLogService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG,
                        bindDO.getId(), wmCanteenPoiTaskBO.getUserId(), wmCanteenPoiTaskBO.getUserName(), logInfo, "");

                // 食堂信息操作日志
                wmScLogService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, wmCanteenPoiTaskBO.getCanteenFrom().getId(),
                        wmCanteenPoiTaskBO.getUserId(), wmCanteenPoiTaskBO.getUserName(), logInfo, "");

                // 目标食堂信息操作日志
                wmScLogService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, wmCanteenPoiTaskBO.getCanteenTo().getId(),
                        wmCanteenPoiTaskBO.getUserId(), wmCanteenPoiTaskBO.getUserName(), logInfo, "");
            }
        }
    }


    /**
     * 创建食堂门店审核任务流程
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @param nextAuditNode 下一个任务节点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void createCanteenTaskWorkflow(WmCanteenPoiTaskBO wmCanteenPoiTaskBO, Integer nextAuditNode) throws WmSchCantException {
        log.info("[WmTranferBindSimpleService.createCanteenTaskWorkflow] input param: wmCanteenPoiTaskBO = {}, nextAuditNode = {}",
                JSONObject.toJSONString(wmCanteenPoiTaskBO), nextAuditNode);
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);
        if (nextAuditNode == null || auditNodeEnum == CanteenAuditNodeEnum.UNKNOW) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "换绑食堂信息任务未找到流程节点");
        }
        // 构建相关信息
        WmAuditPersonCondition condition = new WmAuditPersonCondition();
        condition.setAuditNodeEnum(auditNodeEnum);
        condition.setUserId(wmCanteenPoiTaskBO.getUserId());
        condition.setWmCanteenDB(wmCanteenPoiTaskBO.getCanteenTo());
        condition.setWmSchoolDB(wmCanteenPoiTaskBO.getSchoolTo());
        wmCanteenPoiTaskBO.setAuditNode(CanteenAuditNodeEnum.of(nextAuditNode));
        wmCanteenPoiTaskBO.setNextNode(auditNodeEnum);
        wmCanteenPoiTaskBO.setAuditStatus(auditNodeEnum.getAuditStatus());
        wmCanteenPoiTaskBO.setTaskTypeEnum(CanteenPoiTaskTypeEnum.TRANSFER_BIND);

        //任务系统创建任务
        createTaskInTicketSystem(wmCanteenPoiTaskBO);
    }


    /**
     * 在任务系统中创建任务
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @return 任务系统中的任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String createTaskInTicketSystem(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("createTaskInTicketSystem wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        if (wmCanteenPoiTaskBO.getNextNode().getSystemType() != CanteenAuditSystemEnum.TASK_SYSTEM.getCode()) {
            return null;
        }
        try {
            WmTicketDto wmTicketDto = buildWmTicketDto(wmCanteenPoiTaskBO);
            log.info("createTaskInTicketSystem 调用任务系统发起任务参数:{}", JSONObject.toJSONString(wmTicketDto));
            int ticketId = wmCrmTicketThriftService.createTicket(wmTicketDto);
            log.info("createTaskInTicketSystem 调用任务系统发起任务结果:taskId={},ticketId={}", wmCanteenPoiTaskBO.getId(), ticketId);

            //关联任务和任务系统父任务的id
            WmScCanteenPoiTaskDO taskDO = new WmScCanteenPoiTaskDO();
            taskDO.setId(wmCanteenPoiTaskBO.getId());
            taskDO.setParentTicketId((long) ticketId);
            taskDO.setCurrentNodeCode(String.valueOf(wmCanteenPoiTaskBO.getNextNode().getCode()));
            wmScCanteenPoiTaskMapper.updateByPrimaryKeySelective(taskDO);

            wmCanteenPoiTaskBO.setNextNodeAuditSystemId(String.valueOf(ticketId));
            return String.valueOf(ticketId);
        } catch (WmTicketServerException e) {
            log.error("createTaskInTicketSystem 调用任务系统创建任务异常 wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO), e);
            throw new WmSchCantException(WmScCodeConstants.TICKET_EXCEPTION, "调用任务系统创建任务异常");
        } catch (Exception e) {
            log.error("createTaskInTicketSystem 调用任务系统创建任务异常 wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO), e);
            throw new WmSchCantException(WmScCodeConstants.TICKET_EXCEPTION, "调用任务系统创建任务异常");
        }
    }


    private WmTicketDto buildWmTicketDto(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        log.info("任务系统buildWmTicketDto wmCanteenPoiTaskBO={}",JSONObject.toJSONString(wmCanteenPoiTaskBO));
        CanteenPoiTaskTypeEnum taskTypeEnum = wmCanteenPoiTaskBO.getTaskTypeEnum();

        WmTicketDto dto = new WmTicketDto();
        //dto.setType(taskTypeEnum.getTaskType());// 任务类型id，参见注册的任务类型
        dto.setType(MccScConfig.getTransferMainTaskAuditNodeId());// 任务类型id，参见注册的任务类型
        //dto.setSource(MccConfig.getCanteenPoiAuditSource());//任务来源, 按照约定配置
        //待处理一级状态
        dto.setStatus(CanteenPoiAuditStatusEnum.AUDITING.getCode());//审核中在任务系统表示待处理
        //待处理二级状态
        dto.setStage(1000);//任务二级状态码（业务方自定义）
        dto.setCreateUid(wmCanteenPoiTaskBO.getUserId());//创建人uid（0表示系统）
        dto.setCreateUname(wmCanteenPoiTaskBO.getUserName());
        //任务审批人
        dto.setOpUid(wmCanteenPoiTaskBO.getUserId());//任务经办人uid,外卖城市负责人
        dto.setOpUname(wmCanteenPoiTaskBO.getUserName());
        // 设置任务说明
        dto.setRemark(wmCanteenPoiTaskBO.getTaskReason());

        dto.setTitle("门店换绑食堂审批：");
        int time = TimeUtil.unixtime();
        dto.setUnixCtime(time);
        dto.setUnixUtime(time);
        long businessKey = Long.valueOf(String.format("%s%s", wmCanteenPoiTaskBO.getNextNode().getCode(), wmCanteenPoiTaskBO.getId()));
        //业务id
        dto.setBusinessKey(businessKey);
        dto.setIdempotencyKey(String.format("sc_canteen_poi_transfer_%s", businessKey));
        dto.setTicketPriority(1);
        Map<String, String> param = Maps.newHashMap();
        param.put("taskType", String.valueOf(wmCanteenPoiTaskBO.getTaskType()));
        param.put("taskId", String.valueOf(wmCanteenPoiTaskBO.getId()));
        param.put("isGray",String.valueOf(IS_IN_GRAY));

        // 审批人设置
        List<WmEmploy> auditPersonList = wmCanteenPoiTaskBO.getAuditPersonList();
        List<Integer> uidList = auditPersonList.stream()
                .map(WmEmploy::getUid)
                .collect(Collectors.toList());
        List<String> auditPersonUidList = uidList.stream()
                .map(String::valueOf) // 将每个 Integer 转换为 String
                .collect(Collectors.toList());

        param.put("auditNodeCodeNum",String.valueOf(uidList.size()));
        String auditPersonUidListStr = JSONObject.toJSONString(auditPersonUidList).replace("\"", "'");
        param.put("auditPersonUidList", auditPersonUidListStr);
        dto.setParams(ObjectMapperUtils.parseString(param));//嵌入页面或跳转页面参数


        Map<Integer, String> targetUidMap = new LinkedHashMap<>();
        Integer[] keys = {MccScConfig.getTransferFirstSubTaskAuditNodeId(), MccScConfig.getTransferSecondSubTaskAuditNodeId()};
        for (int i = 0; i < Math.min(auditPersonUidList.size(), keys.length); i++) {
            targetUidMap.put(keys[i], auditPersonUidList.get(i));
        }
        Map<String,String> processVariables = new HashMap<>();
        processVariables.put("processRoutingKey", String.valueOf(MccScConfig.getTransferMainTaskAuditNodeId()));
        // 使用JSONObject.toJSONString时，确保结果字符串中的引号是单引号
        String targetUidStr = JSONObject.toJSONString(targetUidMap).replace("\"", "'");
        processVariables.put("targetUid", targetUidStr);
        dto.setProcessVariables(processVariables);


        log.info("buildWmTicketDto  WmTicketDto={}",JSONObject.toJSONString(dto));
        return dto;
    }




    //获取食堂信息
    @Override
    public void getAuditInfo(WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO, WmCanPoiTaskSumBo wmCanPoiTaskSumBo) throws WmSchCantException, TException {
        wmCanPoiTaskSumBo.setTaskType(wmScCanteenPoiTaskDO.getTaskType());
        //通过食堂ID 获取换绑前的食堂 学校 蜂窝信息
        int canteenFrom = wmScCanteenPoiTaskDO.getCanteenIdFrom();
        WmCanPoiTaskBaseBo beforeTaskVo = wmScCanteenInfoService.buildPoiTaskBaseBo(canteenFrom, false);
        wmCanPoiTaskSumBo.setWmCanTaskBeforeVo(beforeTaskVo);
        //通过食堂ID 获取换绑后的食堂 学校 蜂窝信息
        int canteenTo = wmScCanteenPoiTaskDO.getCanteenIdTo();
        WmCanPoiTaskBaseBo afterTaskVo = wmScCanteenInfoService.buildPoiTaskBaseBo(canteenTo, true);
        wmCanPoiTaskSumBo.setWmCanTaskAfterVo(afterTaskVo);

        //获取当前绑定门店信息
        List<WmScCanteenPoiAttributeDO> canteenPoiAttributeDOList = wmScCanteenPoiAttributeService.selectEffectByCanteenPrimaryId(canteenTo);
        List<Long> hasWmPoiIdList = canteenPoiAttributeDOList.stream().map((item) -> {
            return item.getWmPoiId();
        }).collect(Collectors.toList());
        List<WmCanPoiTaskPoiInfoBo> hasPoiInfoList = wmScCanteenInfoService.buildPoiInfoBos(hasWmPoiIdList);


        //获取将要添加的门店信息
        List<WmScCanteenPoiTaskDetailDO> taskDetailDOList = wmScCanteenPoiTaskDetailMapper.selectByCanteenPoiTaskId(wmScCanteenPoiTaskDO.getId());
        List<Long> addWmPoiIdList = taskDetailDOList.stream().map((item) -> {
            return item.getWmPoiId();
        }).collect(Collectors.toList());
        List<WmCanPoiTaskPoiInfoBo> addPoiInfoList = wmScCanteenInfoService.buildPoiInfoBos(addWmPoiIdList);

        List<Long> findWmPoiIdList = new ArrayList<>();
        findWmPoiIdList.addAll(addWmPoiIdList);
        findWmPoiIdList.addAll(hasWmPoiIdList);
        // 对findWmPoiIdList去重
        findWmPoiIdList = findWmPoiIdList.stream().distinct().collect(Collectors.toList());
        // 解换绑操作次数
        Map<Long,Integer> RemainingTransferCountMap = wmCanteenPoiCheckService.getRemainingTransferCount(findWmPoiIdList);
        for (WmCanPoiTaskPoiInfoBo wmCanPoiTaskPoiInfoBo : addPoiInfoList) {
            wmCanPoiTaskPoiInfoBo.setUnbindOperationCount(RemainingTransferCountMap.get(wmCanPoiTaskPoiInfoBo.getWmPoiId()));
            wmCanPoiTaskPoiInfoBo.setMaxUnbindOperationCount(MccConfig.getMaxUnbindRebind0perationsSuggest());
        }
        for(WmCanPoiTaskPoiInfoBo wmCanPoiTaskPoiInfoBo : hasPoiInfoList){
            wmCanPoiTaskPoiInfoBo.setUnbindOperationCount(RemainingTransferCountMap.get(wmCanPoiTaskPoiInfoBo.getWmPoiId()));
        }

        wmCanPoiTaskSumBo.setHasWmPoiVos(hasPoiInfoList);
        wmCanPoiTaskSumBo.setAddWmPoiVos(addPoiInfoList);

        String proofMaterialImage = wmScCanteenPoiTaskDO.getProofMaterialImage();
        if (StringUtils.isNotBlank(proofMaterialImage)) {
            // 去掉字符串开头和结尾的方括号
            proofMaterialImage = proofMaterialImage.substring(1, proofMaterialImage.length() - 1);
            // 按逗号分割，并去除每个元素的前后空格
            List<String> proofMaterialImageList = Arrays.stream(proofMaterialImage.split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
            wmCanPoiTaskSumBo.setProofMaterialImage(proofMaterialImageList);
        }
    }


    //生效任务
    @Override
    public void effectTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException, TException {
        log.info("[WmCanteenPoiTranferBindService.effectTask] input param: wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));

        List<Long> wmPoiIdList = wmCanteenPoiTaskBO.getWmPoiIdList();
        List<Long> validPoiIdList = Lists.newArrayList(wmPoiIdList);
        List<Long> invalidPoiIdList = Lists.newArrayList();

        WmCanApprovedPoiResultBo validAndValidPoisBO = new WmCanApprovedPoiResultBo();
        // 获得合法和不合法的门店
        try{
            validAndValidPoisBO = getValidAndInvalidPoiIdList(wmCanteenPoiTaskBO);
            validAndValidPoisBO.setValidPoiIdList(removeDuplicates(validAndValidPoisBO.getValidPoiIdList()));
            validAndValidPoisBO.setInvalidPoiIdList(removeDuplicates(validAndValidPoisBO.getInvalidPoiIdList()));
        }catch (Exception e){
            log.info("换绑审批后出现问题，进行兜底处理，门店绑定状态恢复原状");
            invalidPoiIdList.addAll(wmPoiIdList);
            validPoiIdList.clear();
            validAndValidPoisBO.setValidPoiIdList(validPoiIdList);
            validAndValidPoisBO.setInvalidPoiIdList(invalidPoiIdList);
        }

        // 兼容旧流程
        // 处理合法门店
        handleValidPois(wmCanteenPoiTaskBO, validAndValidPoisBO);

        // 处理非法门店
        handleInvalidPois(wmCanteenPoiTaskBO, validAndValidPoisBO.getInvalidPoiIdList(),InvalidDataSourceEnum.VALIDATION_FAILURE.getCode());
    }

    // 去重处理
    private List<Long> removeDuplicates(List<Long> poiIdList) {
        return new ArrayList<>(new HashSet<>(poiIdList));
    }


    /**
     * 外卖门店绑定提交
     * @param submitDTO 提交DTO
     * @return 档口管理ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Integer transferEffectValidPoi(WmCanteenStallBindSubmitDTO submitDTO,WmCanteenPoiTaskBO taskDeteilBO,WmCanApprovedPoiResultBo validAndValidPoisBO) throws WmSchCantException, TException {
        log.info("[WmCanteenStallService.submitByWmPoiBind] input param: submitDTO = {}", JSONObject.toJSONString(submitDTO));
        if (submitDTO == null || submitDTO.getCanteenPrimaryId() == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }
        if(CollectionUtils.isEmpty(submitDTO.getWmPoiIdList())){
            return null;
        }

        // 1-创建档口管理任务(提交状态为"已提交")
        Integer manageId = wmCanteenStallManageService.createCanteenStallManageByWmPoiBind(submitDTO.getCanteenPrimaryId(), submitDTO.getUserId(),(int) CanteenStallManageTaskTypeEnum.TRANSFER_BIND.getType());

        // 2-档口绑定任务(key->wmPoiId val->bindDO)
        Map<Long, WmCanteenStallBindDO> bindDOMap = wmCanteenStallBindService.upsertCanteenTranferBindListByWmPoiBind(submitDTO, manageId);

        // 3-外卖门店绑定流程(异步执行)
        transferBindCanteenPoiByWmPoiBindAsync(bindDOMap, submitDTO.getUserId(), submitDTO.getUserName());

        // 4-对于没有通过验证的门店也需要有个任务
        wmCanteenStallBindService.setInvalidIdAndStallBindTask(submitDTO,validAndValidPoisBO.getInvalidPoiIdList(), manageId);
        return manageId;
    }


    /**
     * 外卖门店绑定流程(换绑——异步执行)
     * @param userId 用户ID
     * @param userName 用户名称
     * @param bindDOMap key->wmPoiId val->bindDO
     */
    private void transferBindCanteenPoiByWmPoiBindAsync(Map<Long, WmCanteenStallBindDO> bindDOMap, Integer userId, String userName) throws WmSchCantException {
        WmCanteenStallWmPoiBindBO wmPoiBindBO = new WmCanteenStallWmPoiBindBO();
        wmPoiBindBO.setUserId(userId);
        wmPoiBindBO.setUserName(userName);
        wmPoiBindBO.setBindDOMap(bindDOMap);
        wmPoiBindBO.setOperation(CanteenStallManageTaskTypeEnum.TRANSFER_BIND.getName());

        wmCanteenStallPoiBindService.transferBindByWmPoiBind(wmPoiBindBO);
        // 单起一个异步线程进行处理
/*        new Thread(new TraceRunnable(() -> {
            try {
                wmCanteenStallPoiBindService.transferBindByWmPoiBind(wmPoiBindBO);
            } catch (Exception e) {
                log.error("[WmCanteenStallManageService.bindCanteenPoiByWmPoiBindAsync] Exception. bindDOMap = {}",
                        JSONObject.toJSONString(bindDOMap), e);
            }
        })).start();*/
    }

    /**
     * 换绑中针对原有档口线索和外卖门店处理 getCanteenIdFrom
     */
    private List<Long> unBindOldClueAndWmPoiWhenTransfer(WmCanteenPoiTaskBO taskDeteilBO,List<Long> validPoiIdList) throws WmSchCantException{
        List<Long> FailPoiIdList = new ArrayList<>();
        for(Long wmPoiId:validPoiIdList){
            // 对每个门店门店解绑
            Boolean isRebindSuccess = wmCanteenStallBindService.unBindOldClueAndWmPoiWhenTransfer(wmPoiId, Long.valueOf(taskDeteilBO.getCanteenFrom().getId()), taskDeteilBO.getUserId(), taskDeteilBO.getUserName(), "换绑");
            if (!isRebindSuccess) {
                FailPoiIdList.add(wmPoiId);
            }
        }
        return FailPoiIdList;
    }

    /**
     * 任务审核驳回
     */
    @Override
    public void rejectTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException{
        log.info("rejectTask start,wmCanteenPoiTaskBO={}",JSONObject.toJSONString(wmCanteenPoiTaskBO));
        handleInvalidPois(wmCanteenPoiTaskBO,wmCanteenPoiTaskBO.getWmPoiIdList(),InvalidDataSourceEnum.REVIEW_REJECTION.getCode());
    }

    @Override
    public void cancelTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException{
        log.info("cancelTask start,wmCanteenPoiTaskBO={}",JSONObject.toJSONString(wmCanteenPoiTaskBO));
        handleInvalidPois(wmCanteenPoiTaskBO,wmCanteenPoiTaskBO.getWmPoiIdList(),InvalidDataSourceEnum.APPLICATION_TERMINATION.getCode());
    }

    /**
     * 处理合法门店
     * @param taskDeteilBO       记录任务详情
     * @throws WmSchCantException WmSchCantException
     * @throws TException         TException
     */
    private List<Long> handleValidPois(WmCanteenPoiTaskBO taskDeteilBO,WmCanApprovedPoiResultBo validAndValidPoisBO)
            throws WmSchCantException, TException {

        log.info("[WmTranferBindSimpleService.handleValidPois] wmCanteenPoiTaskBO = {}, validAndValidPoisBO = {}",
                JSONObject.toJSONString(taskDeteilBO), JSONObject.toJSONString(validAndValidPoisBO));

        List<Long> validPoiIdList = validAndValidPoisBO.getValidPoiIdList();
        List<Long> FailPoiIdList = validAndValidPoisBO.getInvalidPoiIdList();
        // 1. 获取合法的门店列表
        // 1.1 如果合法门店列表为空，则不进行后续操作
        if (CollectionUtils.isEmpty(validPoiIdList)) {
            log.info("[WmTranferBindSimpleService.handleValidPois] validPoiIdList is empty.");
            return null;
        }

        // 2. 获取原食堂和目标食堂的信息
        // 一、对原有绑定门店解绑
        List<Long> getFailPoiIdList = unBindOldClueAndWmPoiWhenTransfer(taskDeteilBO,validPoiIdList);
        // 二、一些解绑失败的处理  从validPoiIdList去掉FailPoiIdList
        validPoiIdList.removeAll(getFailPoiIdList);
        FailPoiIdList.addAll(getFailPoiIdList);

        WmCanteenStallBindSubmitDTO submitDTO = new WmCanteenStallBindSubmitDTO();
        submitDTO.setCanteenPrimaryId(taskDeteilBO.getCanteenTo().getId());
        submitDTO.setUserId(taskDeteilBO.getUserId());
        submitDTO.setUserName(taskDeteilBO.getUserName());
        submitDTO.setWmPoiIdList(validPoiIdList);

        // 三、执行绑定行为
        transferEffectValidPoi(submitDTO,taskDeteilBO,validAndValidPoisBO);

        // 四. 增加解换绑次数
        if(!validPoiIdList.isEmpty()){
            wmPoiAttributesMapper.increaseRebindCountOrInsert(validPoiIdList);
        }


        validAndValidPoisBO.setValidPoiIdList(validPoiIdList);
        validAndValidPoisBO.setInvalidPoiIdList(FailPoiIdList);

        return getFailPoiIdList;
    }


    /**
     * 处理非法门店
     * @param taskDeteilBO       记录任务详情
     * @param invalidPoiIdList 非法门店
     */
    private void handleInvalidPois(WmCanteenPoiTaskBO taskDeteilBO, List<Long> invalidPoiIdList,Integer invalidDataSourceCode) {
        // 1. 获取不合法的门店列表
        // 1.1 如果不合法门店列表为空，则不进行后续操作
        if (invalidPoiIdList == null ||  invalidPoiIdList.isEmpty()) {
            log.info("[WmTranferBindSimpleService.handleValidPois] validPoiIdList is empty. invalidPoiIdList = {}",
                    JSONObject.toJSONString(invalidPoiIdList));
            return;
        }
        log.info("handleInvalidPois taskDeteilBO ={},invalidPoiIdList:{}",JSONObject.toJSONString(taskDeteilBO),JSONObject.toJSONString(invalidPoiIdList));

        // 只对仍处于换绑的流程做处理
        List<WmCanteenStallBindDO> stallList = wmCanteenStallBindMapper.selectByWmPoiIdsAndCanteenPriIdWitchStatus((int) CanteenStallWmPoiBindStatusEnum.REBINDING.getType(),invalidPoiIdList,taskDeteilBO.getCanteenFrom().getId());
        Set<Long> existingWmPoiIds = stallList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .collect(Collectors.toSet());

        List<Long> bindIdList = new ArrayList<>();
        for(WmCanteenStallBindDO bindDO:stallList){
            bindIdList.add(Long.valueOf(bindDO.getId()));
        }
        // 没有处理的wmpoid
        List<Long> missingWmPoiIds = invalidPoiIdList.stream()
                .filter(wmPoiId -> !existingWmPoiIds.contains(wmPoiId))
                .collect(Collectors.toList());
        log.info("审批最终没有处理的门店：{}",JSONObject.toJSONString(missingWmPoiIds));

        if(CollectionUtils.isEmpty(bindIdList)){
            return;
        }
        log.info("handleInvalidPois handleInvalidPois bindIdList：{}",JSONObject.toJSONString(bindIdList));
        wmCanteenStallBindService.updateWmBindIdListBindStatus(bindIdList,CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());

        // 添加日志
        for(WmCanteenStallBindDO bindDO: stallList){
            WmCanteenStallWmPoiBindBO wmPoiBindBO = new WmCanteenStallWmPoiBindBO();
            wmPoiBindBO.setUserId(taskDeteilBO.getUserId());
            wmPoiBindBO.setUserName(taskDeteilBO.getUserName());
            wmPoiBindBO.setWmPoiId(bindDO.getWmPoiId());
            wmPoiBindBO.setCanteenToID(Long.valueOf(taskDeteilBO.getCanteenTo().getId()));
            wmCanteenStallPoiBindService.transferRecordBindCanteenPoiLogReject(wmPoiBindBO, Long.valueOf(bindDO.getId()),invalidDataSourceCode);
        }

    }




    @Override
    public CanteenPoiAuditStatusV2Enum commitTask(WmCanteenPoiTaskAuditMinutiaBO wmCanteenPoiTaskAuditMinutiaBO) throws WmSchCantException {

        //当前节点审批状态需要写入任务主表里面——wm_sc_canteen_poi_task  audit_status 这个审批状态对应CanteenPoiAuditStatusV2Enum枚举类型 可以通过getNextNode方法并通过节点个数来更新  需要判断是否有二级审批嘛
        log.info("[WmTranferBindSimpleService.commitTask] input param: wmCanteenPoiTaskAuditMinutiaBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskAuditMinutiaBO));
        CanteenPoiAuditStatusV2Enum resultStauts = null;
        switch (ProcessSecondStatusEnum.of(wmCanteenPoiTaskAuditMinutiaBO.getAuditResult())) {
            case APPROVED:
                // AuditResult值 == 1,任务系统审批通过
                resultStauts =  setNextNodeCodeApprove(wmCanteenPoiTaskAuditMinutiaBO);
                break;
            case REJECTED:
                // AuditResult值 == 2,任务系统审批终止
                resultStauts =  setNextNodeCodeReject(wmCanteenPoiTaskAuditMinutiaBO,CanteenAuditNodeEnum.TASK_TERMINATED.getCode());
                break;
            case TERMINATED:
                // AuditResult值 == 3,任务系统审批驳回
                resultStauts =  setNextNodeCodeReject(wmCanteenPoiTaskAuditMinutiaBO,CanteenAuditNodeEnum.REJECT_AUDITING.getCode());
                break;
            default:
                throw new WmSchCantException(WmScCodeConstants.TICKET_EXCEPTION, "无法识别的任务系统回调状态");
        }
        return resultStauts;
    }


    /**
     * canteenPoiAuditStatus是当前业务审核状态，
     * 需要从单个任务的任务状态（ProcessSecondStatusEnum）向整体业务状态（CanteenPoiAuditStatusV2Enum）转化
     * **/
    public CanteenPoiAuditStatusV2Enum setNextNodeCodeApprove(WmCanteenPoiTaskAuditMinutiaBO minutiaBO) throws WmSchCantException {
        log.info("setNextNodeCode中minutiaBO:{}",JSONObject.toJSONString(minutiaBO));
        WmTicketDto sonTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(minutiaBO.getAuditSystemId()));
        if (sonTicketDto == null) {
            log.error("[WmTranferBindSimpleService.getAuditStreamByAuditSystemId] ticketDto is null. auditSystemId = {}", minutiaBO.getAuditSystemId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }

        String tairLockKey = CANTEEN_POI_TASK_LOCKKEY_PREFIX + "_" + sonTicketDto.getParentTicketId();
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

        // 1.2 获取任务信息（本地）
        WmScCanteenPoiTaskDO taskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTicketId(sonTicketDto.getParentTicketId());
        if (taskDO == null) {
            log.error("[WmTranferBindSimpleService.getAuditStreamByAuditSystemId] taskDO is null. auditSystemId = {}", sonTicketDto.getParentTicketId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }

        // 额外添加幂等运算
        if (!wmScCanteenPoiTaskSimpleService.isAuditNodeToBeProcessed(minutiaBO.getTicketType(), taskDO.getAuditStatus())) {
            log.info("[食堂管理]任务系统消息通知未找到审批信息，不处理,minutiaBO={}", JSONObject.toJSONString(minutiaBO));
            return CanteenPoiAuditStatusV2Enum.NO_ACTION;
        }

        //获取节点类型
        Integer nextAuditNode = CanteenAuditProgressEnum.getNextNode(CanteenAuditProgressEnum.of(taskDO.getAuditNodeType()), Integer.valueOf(taskDO.getCurrentNodeCode()));
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);


        WmScCanteenPoiTaskDO updateTaskDO = new WmScCanteenPoiTaskDO();
        updateTaskDO.setParentTicketId(sonTicketDto.getParentTicketId());
        updateTaskDO.setId(taskDO.getId());
        updateTaskDO.setCurrentNodeCode(String.valueOf(nextAuditNode));
        updateTaskDO.setAuditStatus(auditNodeEnum.getAuditStatus());
        log.info("setNextNodeCode中updateTaskDO:{}",JSONObject.toJSONString(updateTaskDO));
        wmScCanteenPoiTaskMapper.updateByPrimaryKeySelective(updateTaskDO);


        wmScTairService.unLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);
        return null;
    }

    /**
     * canteenPoiAuditStatus是当前业务审核状态，
     * 需要从单个任务的任务状态（ProcessSecondStatusEnum）向整体业务状态（CanteenPoiAuditStatusV2Enum）转化的
     * **/
    public CanteenPoiAuditStatusV2Enum setNextNodeCodeReject(WmCanteenPoiTaskAuditMinutiaBO minutiaBO,Integer canteenPoiAuditStatus) throws WmSchCantException {
        log.info("setNextNodeCode中minutiaBO:{}",JSONObject.toJSONString(minutiaBO));
        WmTicketDto sonTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(minutiaBO.getAuditSystemId()));
        if (sonTicketDto == null) {
            log.error("[WmTranferBindSimpleService.getAuditStreamByAuditSystemId] ticketDto is null. auditSystemId = {}", minutiaBO.getAuditSystemId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }

        String tairLockKey = CANTEEN_POI_TASK_LOCKKEY_PREFIX + "_" + sonTicketDto.getParentTicketId();
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

        // 1.2 获取任务信息（本地）
        WmScCanteenPoiTaskDO taskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTicketId(sonTicketDto.getParentTicketId());
        if (taskDO == null) {
            log.error("[WmTranferBindSimpleService.getAuditStreamByAuditSystemId] taskDO is null. auditSystemId = {}", sonTicketDto.getParentTicketId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }

        if (!wmScCanteenPoiTaskSimpleService.isAuditNodeToBeProcessed(minutiaBO.getTicketType(), taskDO.getAuditStatus())) {
            log.info("[食堂管理]任务系统消息通知未找到审批信息，不处理,minutiaBO={}", JSONObject.toJSONString(minutiaBO));
            return CanteenPoiAuditStatusV2Enum.NO_ACTION;
        }


        WmScCanteenPoiTaskDO updateTaskDO = new WmScCanteenPoiTaskDO();
        updateTaskDO.setParentTicketId(sonTicketDto.getParentTicketId());
        updateTaskDO.setId(taskDO.getId());
        // 这里CurrentNode不应该发生变化，因为还是在那个审批节点
        //updateTaskDO.setCurrentNodeCode(String.valueOf(CanteenAuditNodeEnum.REJECT_AUDITING.getCode()));
        updateTaskDO.setAuditStatus(canteenPoiAuditStatus);
        log.info("setNextNodeCode中updateTaskDO:{}",JSONObject.toJSONString(updateTaskDO));
        wmScCanteenPoiTaskMapper.updateByPrimaryKeySelective(updateTaskDO);

        wmScTairService.unLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);
        return null;
    }


    /**
     * 获取合法和非法门店列表（下线状态，或者处于校外的门店是非法门店）
     * @param wmCanteenPoiTaskBO 记录任务详情
     * @throws WmSchCantException WmSchCantException
     */
    private WmCanApprovedPoiResultBo getValidAndInvalidPoiIdList(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("[WmCanteenPoiBindService.getValidAndInvalidPoiIdList] wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        List<Long> wmPoiIdList = wmCanteenPoiTaskBO.getWmPoiIdList();
        // 获取合法的门店列表
        List<Long> validPoiIdList = Lists.newArrayList(wmPoiIdList);
        WmCanApprovedPoiResultBo wmCanApprovedPoiResultBo = new WmCanApprovedPoiResultBo();
        // 获取非法门店ID列表
        List<Long> invalidPoiIdList = Lists.newArrayList();
        List<WmScCanIllegalPoi> wmScCanIllegalPois = getIllegalCanPoisV2(wmPoiIdList, wmCanteenPoiTaskBO.getSchoolTo().getId());
        if (CollectionUtils.isNotEmpty(wmScCanIllegalPois)) {
            invalidPoiIdList = wmScCanIllegalPois.stream()
                    .map(WmScCanIllegalPoi::getWmPoiId)
                    .collect(Collectors.toList());
            log.info("getValidAndInvalidPoiIdList invalidPoiIdList={}", JSONObject.toJSONString(invalidPoiIdList));
            validPoiIdList.removeAll(invalidPoiIdList);
            wmCanApprovedPoiResultBo.setInvalidPoiIdList(invalidPoiIdList);
        }


        List<Long> bindIdList = wmCanteenPoiTaskBO.getBindIdList();
        //Map<Long, Long> wmPoiIdMap = wmCanteenPoiCheckService.getWmPoiIdsAndvalidateStallBindingIds(bindIdList);

        // 1. 剩余可换绑和解绑次数的校验
        List<Long> invalidList = wmCanteenPoiCheckService.validateRemainingTransferCount(wmPoiIdList);
        invalidPoiIdList.addAll(invalidList);


        // 1-食堂信息未生效或审批中校验(暂时去掉)
        WmCanteenDB canteenDB = wmCanteenMapper.selectCanteenById(wmCanteenPoiTaskBO.getCanteenTo().getId());
        if (canteenDB == null) {
            invalidPoiIdList.addAll(wmPoiIdList);
            wmPoiIdList.removeAll(invalidPoiIdList);
            wmCanApprovedPoiResultBo.setValidPoiIdList(wmPoiIdList);
            wmCanApprovedPoiResultBo.setInvalidPoiIdList(invalidPoiIdList);
            return wmCanApprovedPoiResultBo;
        }
        String canteenStatusCheckMsg = wmCanteenStallCheckService.checkCanteenStatusAuditingOrInsertReject(canteenDB.getId());
        if (StringUtils.isNotBlank(canteenStatusCheckMsg)) {
            invalidPoiIdList.addAll(wmPoiIdList);
            wmPoiIdList.removeAll(invalidPoiIdList);
            wmCanApprovedPoiResultBo.setValidPoiIdList(wmPoiIdList);
            wmCanApprovedPoiResultBo.setInvalidPoiIdList(invalidPoiIdList);
            return wmCanApprovedPoiResultBo;
        }


        Integer CanteenToPrimaryId = wmCanteenPoiTaskBO.getCanteenTo().getId();

        // 3-外卖门店ID是否存在  （外卖门店状态是否处于换绑中放到绑定时验证，由于产品要求使用新的档口绑定任务，这里为了避免一些其他情况）
        List<Long> failWmPoiIdList1 = wmCanteenStallCheckService.checkWmPoiListValid(wmPoiIdList);
        invalidPoiIdList.addAll(failWmPoiIdList1);

        // 6-外卖门店所处蜂窝是否与食堂关联学校蜂窝一致
        List<Long> failWmPoiIdList5 = wmCanteenStallCheckService.checkWmPoiListAor(wmPoiIdList, CanteenToPrimaryId);
        invalidPoiIdList.addAll(failWmPoiIdList5);

        // 7-外卖门店坐标是否在学校范围内
        List<Long> failWmPoiIdList6 = wmCanteenStallCheckService.checkWmPoiListCoordinate(wmPoiIdList, CanteenToPrimaryId);
        invalidPoiIdList.addAll(failWmPoiIdList6);


        // 2-校验食堂档口数量  放在最后
        String canteenNumCheckMsg = wmCanteenStallCheckService.checkCanteenStallNumLimit(CanteenToPrimaryId, validPoiIdList.size());
        if (StringUtils.isNotBlank(canteenNumCheckMsg)) {
            invalidPoiIdList.addAll(wmPoiIdList);
        }

        // 将所有门店的不合法门店去掉
        wmPoiIdList.removeAll(invalidPoiIdList);
        wmCanApprovedPoiResultBo.setValidPoiIdList(wmPoiIdList);
        wmCanApprovedPoiResultBo.setInvalidPoiIdList(invalidPoiIdList);

        return wmCanApprovedPoiResultBo;
    }



    @Override
    public WmCanteenPoiTaskBO buildSimpleTaskBO(long taskId) throws WmSchCantException {
        // 实现食堂换绑门店任务详情查询流程
        // 1. 查询任务主表信息，获取基本信息
        // 2. 查询换绑后的食堂信息，包括食堂所属学校及学校区域信息
        // 3. 查询任务详情表，获取绑定的门店ID列表
        // 4. 根据门店ID列表查询门店聚合信息，获取门店详细信息


        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = new WmCanteenPoiTaskBO();
        // 1. 根据任务ID查询任务主表信息
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskMapper.selectByPrimaryKey(taskId);
        wmCanteenPoiTaskBO.setId(wmScCanteenPoiTaskDO.getId());
        wmCanteenPoiTaskBO.setUserId(wmScCanteenPoiTaskDO.getUserId());
        wmCanteenPoiTaskBO.setUserName(wmScCanteenPoiTaskDO.getUserName());
        wmCanteenPoiTaskBO.setTaskType(wmScCanteenPoiTaskDO.getTaskType());

        // 1.1 如果存在换绑前的食堂ID，查询换绑前的食堂信息(如果食堂为空不阻塞流程)
        if (wmScCanteenPoiTaskDO.getCanteenIdFrom() != null && wmScCanteenPoiTaskDO.getCanteenIdFrom() > 0) {
            WmCanteenDB canteenDBFrom = wmCanteenMapper.selectCanteenById(wmScCanteenPoiTaskDO.getCanteenIdFrom());
            if (canteenDBFrom == null) {
                log.error("[WmTranferBindSimpleService.buildSimpleTaskBO] canteenDB is null. getCanteenIdFrom = {}", wmScCanteenPoiTaskDO.getCanteenIdFrom());
            }
            wmCanteenPoiTaskBO.setCanteenFrom(canteenDBFrom);
        }

        // 2. 查询换绑后的食堂信息(如果食堂为空不阻塞流程)
        if(wmScCanteenPoiTaskDO.getCanteenIdTo() != null && wmScCanteenPoiTaskDO.getCanteenIdTo() > 0){
            WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(wmScCanteenPoiTaskDO.getCanteenIdTo());
            if (wmCanteenDB == null) {
                log.info("[WmTranferBindSimpleService.buildSimpleTaskBO] canteenDB is null. canteenPrimaryKey = {}", wmScCanteenPoiTaskDO.getCanteenIdTo());
            }else{
                // 2.1 查询换绑后食堂所属的学校信息
                WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());
                if (wmSchoolDB == null) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "加工任务参数异常:待绑食堂学校未找到");
                }
                wmCanteenPoiTaskBO.setSchoolTo(wmSchoolDB);
            }
            wmCanteenPoiTaskBO.setCanteenTo(wmCanteenDB);
        }

        // 3. 查询任务详情表，获取绑定的门店ID列表
        List<WmScCanteenPoiTaskDetailDO> detailDOList = wmScCanteenPoiTaskDetailMapper.selectByCanteenPoiTaskId(taskId);
        if (CollectionUtils.isEmpty(detailDOList)) {
            return wmCanteenPoiTaskBO;
        }
        List<Long> wmPoiIdList = detailDOList.stream()
                .map(WmScCanteenPoiTaskDetailDO::getWmPoiId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return wmCanteenPoiTaskBO;
        }

        // 4. 根据门店ID列表查询门店聚合信息
        wmCanteenPoiTaskBO.setWmPoiIdList(wmPoiIdList);
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, Sets.newHashSet(
                WM_POI_FIELD_WM_POI_ID,
                WM_POI_FIELD_NAME,
                WM_POI_FIELD_NAME,
                WM_POI_FIELD_VALID,
                WM_POI_FIELD_IS_DELETE,
                WM_POI_FIELD_AOR_ID,
                WM_POI_FIELD_OWNER_UID,
                WM_POI_FIELD_FIRST_TAG,
                WM_POI_FIELD_LATITUDE,
                WM_POI_FIELD_LONGITUDE));
        if (CollectionUtils.isNotEmpty(wmPoiAggreList)) {
            wmCanteenPoiTaskBO.setWmPoiAggreList(wmPoiAggreList);
        }

        return wmCanteenPoiTaskBO;
    }


    /**
     * 获取不合法门店列表: 门店下线\门店食堂\门店坐标不在学校范围内
     * @param wmPoiIdList 需要校验的门店ID列表
     * @param schoolPrimaryId 门店所处学校主键id
     * @return List<WmScCanIllegalPoi>
     */
    public List<WmScCanIllegalPoi> getIllegalCanPoisV2(List<Long> wmPoiIdList, int schoolPrimaryId) {
        log.info("getIllegalCanPois wmPoiIdList={},schoolPrimaryId={}", JSONObject.toJSONString(wmPoiIdList), schoolPrimaryId);
        List<WmScCanIllegalPoi> wmScCanIllegalPoiList = Lists.newLinkedList();
        // 1. 获取门店信息
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList,
                Sets.newHashSet(
                        WM_POI_FIELD_WM_POI_ID,
                        WM_POI_FIELD_NAME,
                        WM_POI_FIELD_VALID,
                        WM_POI_FIELD_LATITUDE,
                        WM_POI_FIELD_LONGITUDE,
                        WM_POI_FIELD_IS_DELETE
                )
        );
        // 如果门店信息为空，则直接返回空列表
        if (com.dianping.lion.client.util.CollectionUtils.isEmpty(wmPoiAggreList)) {
            return wmScCanIllegalPoiList;
        }
        // 2. 校验门店状态
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            WmScCanIllegalPoi wmScCanIllegalPoi = new WmScCanIllegalPoi();
            wmScCanIllegalPoi.setWmPoiId(wmPoiAggre.getWm_poi_id());
            wmScCanIllegalPoi.setPoiName(wmPoiAggre.getName());
            wmScCanIllegalPoi.setSchoolPrimaryId(schoolPrimaryId);
            // 2.1 校验门店是否下线
            if (wmPoiAggre.getValid() == CanteenPoiStatusEnum.OFFLINE.getCode()) {
                wmScCanIllegalPoi.setIllegalPoiType(WmIllegalCanPoiTypeEnum.POI_OFFLINE.getCode());
                wmScCanIllegalPoiList.add(wmScCanIllegalPoi);
                continue;
            }
            // 2.2 校验门店是否已删除
            if (wmPoiAggre.getIs_delete() == WM_POI_ID_IS_DELETE) {
                wmScCanIllegalPoi.setIllegalPoiType(WmIllegalCanPoiTypeEnum.POI_DELETED.getCode());
                wmScCanIllegalPoiList.add(wmScCanIllegalPoi);
                continue;
            }
        }
        // 返回不合法的门店列表
        return wmScCanIllegalPoiList;
    }






    /**
     * 创建档口管理任务并更新档口绑定任务信息
     * 1. 创建档口管理任务（提交状态为"已提交"）
     * 2. 更新或创建档口绑定任务
     *
     * @param taskDeteilBO 任务详情
     * @param validAndInValidPoisBO 合法和非法门店信息
     * @throws WmSchCantException 异常
     * @throws TException 异常
     */
    public void createAndUpdateStallTasks(WmCanteenPoiTaskBO taskDeteilBO,WmCanApprovedPoiResultBo validAndInValidPoisBO)
            throws WmSchCantException, TException {
        WmScCanteenPoiTaskDO auditTaskDO = wmScCanteenPoiTaskMapper.selectByPrimaryKey(taskDeteilBO.getId());
        if (auditTaskDO == null) {
            log.error("[WmTranferBindSimpleService.updateCanteenStallBindTaskInfo] taskDO is null. id = {}", taskDeteilBO.getId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }

        WmCanteenDB wmCanteenDBTo = taskDeteilBO.getCanteenTo();

        // 删除原来的档口管理任务和档口管理任务的关系
       // todo：驱动原来的线索解绑  首先驱动原来的线索解绑 然后再往下执行
        // 1-创建档口管理任务(提交状态为"已提交")
        Integer manageId = wmCanteenStallManageService.createCanteenStallManageByWmPoiBind(wmCanteenDBTo.getId(), auditTaskDO.getUserId(), (int) CanteenStallManageTaskTypeEnum.TRANSFER_BIND.getType());

        // 2-更新/创建档口绑定任务(key->wmPoiId val->bindDO)
        Map<Long, WmCanteenStallBindDO> bindDOMap = upsertCanteenStallBindListByWmPoiBind(taskDeteilBO,auditTaskDO, manageId);


        //wmCanteenStallBindService.updateWmPoiListBindStatus(wmCanteenPoiTaskBO.getBindIdList(),CanteenStallWmPoiBindStatusEnum.UNBIND.getType());

    }

    public Map<Long, WmCanteenStallBindDO> upsertCanteenStallBindListByWmPoiBind(WmCanteenPoiTaskBO taskDeteilBO,WmScCanteenPoiTaskDO auditTaskDO,Integer manageId)
            throws WmSchCantException {
        log.info("[WmCanteenStallBindService.upsertCanteenStallBindListByWmPoiBind] input param: auditTaskDO = {}, manageId = {}",
                JSONObject.toJSONString(auditTaskDO), manageId);
        // 1-根据外卖门店ID查询线索ID
        Map<Long, Long> wdcClueMap = wdcRelationServiceAdapter.getWdcClueIdMapByWmPoiIdList(taskDeteilBO.getWmPoiIdList());

        Map<Long, WmCanteenStallBindDO> bindDOMap = new HashMap<>();
        for (Long wmPoiId : taskDeteilBO.getWmPoiIdList()) {

            WmCanteenStallBindDO oldBindDO = new WmCanteenStallBindDO();
            Long wdcClueId = wdcClueMap.get(wmPoiId);
            List<WmCanteenStallBindDO> bindDOListByWmPoiId = wmCanteenStallBindMapper.selectByWmPoiId(wmPoiId);
            // 注意这个是原来的
            oldBindDO = wmCanteenStallBindMapper.selectByWmPoiIdAndCanteenPrimaryId(wmPoiId, taskDeteilBO.getCanteenIdFrom());
            WmCanteenStallBindDO insertBindDO = new WmCanteenStallBindDO();
            //insertBindDO.setId(oldBindDO.getId());
            insertBindDO.setCanteenPrimaryId(taskDeteilBO.getCanteenIdTo());
            insertBindDO.setWmPoiId(wmPoiId);
            // 外卖门店绑定状态为"未绑定"  todo:这里改成了绑定成功
            insertBindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());
            insertBindDO.setValid(1);
            insertBindDO.setCuid(taskDeteilBO.getUserId());
            insertBindDO.setMuid(taskDeteilBO.getUserId());
            if(wdcClueId != null){ // 有线索
                //clueBindDO.setClueFollowUpStatus((int) CanteenStallClueFollowUpStatusEnum.FOLLOW_UP.getType())
                insertBindDO.setWdcClueId(wdcClueId);
                // 线索生成状态为“生成成功”
                insertBindDO.setClueGenerateStatus((int) CanteenStallClueGenerateStatusEnum.GENERATE_SUCCESS.getType());
                // 线索绑定状态初始化为“绑定中”
                insertBindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BINDING.getType());
                int result = wmCanteenStallBindMapper.insertSelective(insertBindDO);
                if (result <= 0 || insertBindDO.getId() == null) {
                    log.error("[WmCanteenStallBindService.createStallBindByBatchCreateClue] error");
                    throw new WmSchCantException(SERVER_ERROR, "创建档口绑定任务异常");
                }
                // 驱动线索绑定状态更新为"绑定成功"(异步)
                wmCanteenStallClueBindService.updateClueBindStatusSuccess(insertBindDO, taskDeteilBO.getUserId(), taskDeteilBO.getUserName(), CanteenStallManageTaskTypeEnum.WM_POI_BIND.getName());
            }else{
                wmCanteenStallBindMapper.updateByPrimaryKeySelective(insertBindDO);
            }
            // 4-创建管理档口关联关系
            wmCanteenStallManageService.createStallManageBindRel(oldBindDO.getId(), manageId);
            bindDOMap.put(wmPoiId, insertBindDO);
        }

        log.info("[WmCanteenStallBindService.upsertCanteenStallBindListByWmPoiBind] bindDOMap = {}", JSONObject.toJSONString(bindDOMap));
        return bindDOMap;
    }

    private void sendMsgAndWriteLog(WmCanteenPoiTaskBO wmCanteenPoiTaskBO,Map<Long, Long> wmPoiIdMap,List<Long> failWmPoiIdList1,String failReason) {
        if (CollectionUtils.isEmpty(failWmPoiIdList1)) {
            return;
        }
        try {
            WmEmploy responsiblePerson = wmScEmployAdaptor.getById(wmCanteenPoiTaskBO.getUserId());
            if (responsiblePerson != null) {
                String misId = responsiblePerson.getMisId();
                String reciver = String.format("%<EMAIL>", misId);
                String msg = composeMsg(wmCanteenPoiTaskBO,wmPoiIdMap, failWmPoiIdList1, failReason);
                DaxiangUtilV2.push(msg, reciver);
            }
            //wmScLogService.saveIllegalCanteenPoiLog(canteenId, wmScCanIllegalPois);
        } catch (WmSchCantException e) {
            log.warn("sendMsgAndWriteLog 失败 wmCanteenPoiTaskBO={},failWmPoiIdList1={},e={}", JSONObject.toJSONString(wmCanteenPoiTaskBO), JSONObject.toJSONString(failWmPoiIdList1),e);
        } catch (Exception e) {
            log.error("sendMsgAndWriteLog 失败 wmCanteenPoiTaskBO={},failWmPoiIdList1={},e={}", JSONObject.toJSONString(wmCanteenPoiTaskBO), JSONObject.toJSONString(failWmPoiIdList1),e);
        }
    }

    private String composeMsg(WmCanteenPoiTaskBO wmCanteenPoiTaskBO,Map<Long, Long> wmPoiIdMap,List<Long> failWmPoiIdList1,String failReason) {
        String msg = failWmPoiIdList1.toString() + "换绑失败";
        return msg;
    }

    private List<Long> getStallListByStatusAndCanteenPriID(Integer wmAuditStatus,List<Long> wmPoiIdList,Integer canteenPrimaryId) {
        List<WmCanteenStallBindDO> stallList = wmCanteenStallBindMapper.selectByWmPoiIdsAndCanteenPriIdWitchStatus(wmAuditStatus,wmPoiIdList,canteenPrimaryId);
        Set<Long> existingWmPoiIds = stallList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .collect(Collectors.toSet());
        // 筛选出 wmPoiIdList 中没有在 stallList 中的 wmPoiId
        List<Long> missingWmPoiIds = wmPoiIdList.stream()
                .filter(wmPoiId -> !existingWmPoiIds.contains(wmPoiId))
                .collect(Collectors.toList());
        return missingWmPoiIds;
    }
}
