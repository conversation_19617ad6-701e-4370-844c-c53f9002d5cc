package com.sankuai.meituan.waimai.customer.service.sign.applytask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.bizsso.thrift.PoiAcct;
import com.sankuai.meituan.waimai.customer.adapter.BizUserServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmInboxSendMessageAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmC1EContractTempletService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.util.DateUtil;
import com.sankuai.meituan.waimai.customer.util.ServiceEnvUtils;
import com.sankuai.meituan.waimai.customer.util.jsonSchema.JsonSchemaValidatorUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.logistics.contract.client.constants.C1AutoRenewalSectionEnum;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.*;

/**
 * C1到期自动续签service
 * C1合同到期前，换签配送合同费率，自动触发C1合同续签（延期1年）
 * limingxuan
 * 2024-01-08
 */
@Service
public class WmEcontractC1AutoRenewalService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractC1AutoRenewalService.class);
    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(5, 10, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(10000), Executors.defaultThreadFactory()));
    @Resource
    private WmEcontractSignBzService wmEcontractSignBzService;
    @Resource
    private WmC1EContractTempletService wmC1EContractTempletService;
    @Resource
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;
    @Resource
    private WmContractService wmContractService;
    @Resource
    private BizUserServiceAdapter bizUserServiceAdapter;
    @Resource
    private WmInboxSendMessageAdapter wmInboxSendMessageAdapter;
    @Resource
    private WmPoiClient wmPoiClient;
    private final static String AUTO_RENEWAL = "autoRenewal";

    /**
     * 1.如果不是单门店客户就直接发起配送合同的签约任务
     * 2.如果是单门店客户就创建C1合同的续签任务（按当前日前延期一年），并和配送合同任务一起发起打包签约
     *
     * @param manualTaskApplyBo * manualTaskId 配送合同待打包签约任务id
     */
    public LongResult applyAutoRenewalTask(ManualTaskApplyBo manualTaskApplyBo) throws WmCustomerException, TException {
        Integer poiCnt = WmCustomerPoiAggre.Factory.make().countCustomerPoi(manualTaskApplyBo.getCustomerId());
        if (poiCnt == 1) {
            List<Long> manualTaskIds = new ArrayList<>();
            manualTaskIds.add(manualTaskApplyBo.getManualTaskId());
            try{
                //发起配送合同和C1合同的打包签约任务
                WmCustomerContractBo contractBo = wmContractService.getEffectC1ContractUpdate(manualTaskApplyBo.getCustomerId(), -1, "");
                if (contractBo == null) {
                    LOGGER.warn("WmEcontractC1AutoRenewalService#applyAutoRenewalTask 无生效的C1合同 manualTaskApplyBo:{}", JSON.toJSONString(manualTaskApplyBo));
                    throw new WmCustomerException(500, "无生效的C1合同");
                }
                Long todayNight = DateUtil.toNight(new Date()).getTime() / 1000L;
                contractBo.getBasicBo().setDueDate(todayNight + 365L * 24L * 60L * 60L);
                contractBo.setPackWay(SignPackWay.WAIT_HAND_PACK.getCode());
                wmC1EContractTempletService.startSign(contractBo, manualTaskApplyBo.getCommitUid(), "自动续签");
                WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB = wmEcontractManualTaskBizService.getManualTaskInfoByCustomerIdAndModuleRT(manualTaskApplyBo.getCustomerId(), EcontractTaskApplyTypeEnum.C1CONTRACT.getName());
                if (wmEcontractSignManualTaskDB == null) {
                    LOGGER.warn("WmEcontractC1AutoRenewalService#applyAutoRenewalTask 发起C1合同续签失败 manualTaskApplyBo:{}", JSON.toJSONString(manualTaskApplyBo));
                    throw new WmCustomerException(500, "发起C1合同续签失败");
                }
                manualTaskIds.add(wmEcontractSignManualTaskDB.getId());
            } catch (Exception e){
                LOGGER.warn("WmEcontractC1AutoRenewalService#applyAutoRenewalTask  打包发起C1合同续签失败 manualTaskApplyBo:{}", JSON.toJSONString(manualTaskApplyBo));
                DaxiangUtil.push("<EMAIL>", ServiceEnvUtils.getEnv()+"打包发起C1合同续签失败,wmPoiId:"+manualTaskApplyBo.getBizId(),MccConfig.getContractAlarmMisIdList());
            }
            LOGGER.info("WmEcontractC1AutoRenewalService#applyAutoRenewalTask 发起打包签约任务 manualTaskApplyBo:{},manualTaskIds:{}", JSON.toJSONString(manualTaskApplyBo), JSON.toJSONString(manualTaskIds));
            return wmEcontractSignBzService.applyManualPack(manualTaskIds, manualTaskApplyBo.getCommitUid(), "OTHER");
        } else {
            //发起配送合同的签约任务
            LOGGER.info("WmEcontractC1AutoRenewalService#applyAutoRenewalTask 发起配送合同的签约任务 manualTaskApplyBo:{}", JSON.toJSONString(manualTaskApplyBo));
            return wmEcontractSignBzService.applyManualPack(Collections.singletonList(manualTaskApplyBo.getManualTaskId()), manualTaskApplyBo.getCommitUid(), "OTHER");
        }
    }

    /**
     * 判断是否是C1合同到期续签任务
     *
     * @param batchContextBo
     * @return
     */
    public boolean isC1AutoRenewalBatch(EcontractBatchContextBo batchContextBo) {
        if (batchContextBo.getBatchTypeEnum() != EcontractBatchTypeEnum.BATCH_DELIVERY
                && batchContextBo.getBatchTypeEnum() != EcontractBatchTypeEnum.BATCH_CD
                && batchContextBo.getBatchTypeEnum() != EcontractBatchTypeEnum.DELIVERY) {
            return false;
        }
        if (CollectionUtils.isEmpty(batchContextBo.getTaskIdAndTaskMap())) {
            return false;
        }
        List<Long> manualBatchIdIds = new ArrayList<>();
        for (Map.Entry<Long, EcontractTaskBo> map : batchContextBo.getTaskIdAndTaskMap().entrySet()) {
            EcontractTaskBo econtractTaskBo = map.getValue();
            if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(econtractTaskBo.getApplyType())
                    || EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(econtractTaskBo.getApplyType())) {
                if (econtractTaskBo.getManualBatchId() != null && econtractTaskBo.getManualBatchId() > 0) {
                    manualBatchIdIds.add(econtractTaskBo.getManualBatchId());
                }
            }
        }
        LOGGER.info("pushMessage isC1AutoRenewalBatch,customerId:{},manualBatchIdIds:{}", batchContextBo.getCustomerId(), JSON.toJSONString(manualBatchIdIds));
        if (CollectionUtils.isEmpty(manualBatchIdIds)) {
            return false;
        }
        List<WmEcontractSignManualTaskDB> wmEcontractSignManualTaskDBList = wmEcontractManualTaskBizService.batchGetByManualBatchIdsRT(manualBatchIdIds);
        for (WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB : wmEcontractSignManualTaskDBList) {
            if (isC1AutoRenewalApplyContext(wmEcontractSignManualTaskDB.getApplyContext())) {
                LOGGER.info("isC1AutoRenewalBatch is ture,wmEcontractSignManualTaskDB:{}", JSON.toJSONString(wmEcontractSignManualTaskDB));
                return true;
            }
        }
        return false;
    }

    /**
     * 推送商家端C1到期续签push
     *
     * @param batchContextBo
     */
    public void sendPushForC1AutoRenewal(EcontractBatchContextBo batchContextBo, EcontractNotifyBo notifyBo) throws WmCustomerException {
        List<Long> wmPoiIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(batchContextBo.getWmPoiIdMap())) {
            if (!CollectionUtils.isEmpty(batchContextBo.getWmPoiIdMap().get(EcontractTaskApplyTypeEnum.POIFEE.getName()))) {
                wmPoiIdList.addAll(batchContextBo.getWmPoiIdMap().get(EcontractTaskApplyTypeEnum.POIFEE.getName()));
            }
            if (!CollectionUtils.isEmpty(batchContextBo.getWmPoiIdMap().get(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName()))) {
                wmPoiIdList.addAll(batchContextBo.getWmPoiIdMap().get(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName()));
            }
        } else if (!CollectionUtils.isEmpty(batchContextBo.getWmPoiIdList())) {
            wmPoiIdList.addAll(batchContextBo.getWmPoiIdList());
        }
        if (CollectionUtils.isEmpty(wmPoiIdList) || wmPoiIdList.size() > 1) {
            LOGGER.warn("sendPushForC1AutoRenewal C1合同续签任务门店数不为1,wmPoiIdList:{},batchContextBo:{}", JSON.toJSONString(wmPoiIdList), JSON.toJSONString(batchContextBo));
            throw new WmCustomerException(500, "C1合同续签任务门店数不为1");
        }
        Long wmPoiId = wmPoiIdList.get(0);
        PoiAcct poiAcct = null;
        try {
            poiAcct = bizUserServiceAdapter.getPoiAcctByPoiId(wmPoiId);
        } catch (Exception e) {
            LOGGER.warn("sendPushForC1AutoRenewal 未找到门店绑定的200账号,wmPoiId{}", wmPoiId);
        }
        if (poiAcct == null) {
            LOGGER.warn("sendPushForC1AutoRenewal 门店未绑定商家端账号，不推动C1续签push,wmPoiId{}", wmPoiId);
            return;
        }
        String autoRenewalType = getAutoRenewalType(batchContextBo);
        List<String> publishMessTemplate = getPublishMessageTemplate(autoRenewalType);
        WmPoiDomain wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
        Map<String, String> paramMap = buildSignPushMessageBase(batchContextBo.getCustomerId(), wmPoiId, poiAcct, wmPoiDomain, notifyBo);
        LOGGER.info("sendPushForC1AutoRenewal 推送合同续签push,wmPoiId:{},autoRenewalType:{},poiAcct:{},paramMap:{},publishMessTemplate:{}", wmPoiId,autoRenewalType, JSON.toJSONString(poiAcct), JSON.toJSONString(paramMap), JSON.toJSON(publishMessTemplate));
        wmInboxSendMessageAdapter.sendAccountAndPoiMessage(poiAcct.getAcctId(), wmPoiId, paramMap, publishMessTemplate);
    }

    private List<String> getPublishMessageTemplate(String autoRenewalType) {
        if (StringUtils.equals(autoRenewalType, C1AutoRenewalSectionEnum.FINAL_RENEWAL.name())) {
            return MccConfig.getC1AutoRenewalFinalMessageTemplate();
        } else if (StringUtils.equals(autoRenewalType, C1AutoRenewalSectionEnum.NON_FINAL_RENEWAL.name())) {
            return MccConfig.getC1AutoRenewalNonFinalMessageTemplate();
        } else if (StringUtils.equals(autoRenewalType, C1AutoRenewalSectionEnum.EXPIRED_NON_FINAL_RENEWAL.name())) {
            return MccConfig.getC1AutoRenewalExpiredNonFinalMessageTemplate();
        } else if (StringUtils.equals(autoRenewalType, C1AutoRenewalSectionEnum.EXPIRED_FINAL_RENEWAL.name())) {
            return MccConfig.getC1AutoRenewalExpiredFinalMessageTemplate();
        }
        return null;
    }

    /**
     * 获取C1自动续签打包签约任务类型
     *
     * @param batchContextBo
     * @return
     */
    private String getAutoRenewalType(EcontractBatchContextBo batchContextBo) {
        List<Long> manualBatchIdIds = new ArrayList<>();
        for (Map.Entry<Long, EcontractTaskBo> map : batchContextBo.getTaskIdAndTaskMap().entrySet()) {
            EcontractTaskBo econtractTaskBo = map.getValue();
            if (econtractTaskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.POIFEE.getName()) || econtractTaskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName())) {
                if (econtractTaskBo.getManualBatchId() != null && econtractTaskBo.getManualBatchId() > 0) {
                    manualBatchIdIds.add(econtractTaskBo.getManualBatchId());
                }
            }
        }
        List<WmEcontractSignManualTaskDB> wmEcontractSignManualTaskDBList = wmEcontractManualTaskBizService.batchGetByManualBatchIdsRT(manualBatchIdIds);
        for (WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB : wmEcontractSignManualTaskDBList) {
            if (isC1AutoRenewalApplyContext(wmEcontractSignManualTaskDB.getApplyContext())) {
                JSONObject json = JSONObject.parseObject(wmEcontractSignManualTaskDB.getApplyContext());
                if(json.get(AUTO_RENEWAL) != null){
                    return json.get(AUTO_RENEWAL).toString();
                }
            }
        }
        return Strings.EMPTY;
    }

    /**
     * 判断是否是C1自动续签打包签约任务
     *
     * @param applyContext
     * @return
     */
    public boolean isC1AutoRenewalApplyContext(String applyContext) {
        if (StringUtils.isNotBlank(applyContext) && JsonSchemaValidatorUtil.isJson(applyContext)) {
            JSONObject json = JSONObject.parseObject(applyContext);
            if(json.get(AUTO_RENEWAL) == null){
                return false;
            }
            String autoRenewal = json.get(AUTO_RENEWAL).toString();
            return StringUtils.isNotBlank(autoRenewal);
        }
        return false;
    }

    /**
     * 构建商家端push消息参数
     *
     * @param customerId
     * @param wmPoiId
     * @param poiAcct
     * @param wmPoiDomain
     * @param notifyBo
     * @return
     * @throws WmCustomerException
     */
    private static Map<String, String> buildSignPushMessageBase(Integer customerId, Long wmPoiId, PoiAcct poiAcct, WmPoiDomain wmPoiDomain, EcontractNotifyBo notifyBo) throws WmCustomerException {
        try {
            LOGGER.info("buildSignPushMessage customerId:{},wmPoiId:{},poiAcct:{},wmPoiDomain:{},notifyBo:{}", customerId, wmPoiId, JSON.toJSONString(poiAcct), JSON.toJSONString(wmPoiDomain), JSON.toJSONString(notifyBo));
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("customerId", String.valueOf(customerId));
            paramMap.put("accName", poiAcct.getName());
            paramMap.put("accId", String.valueOf(poiAcct.getAcctId()));
            paramMap.put("wmPoiId", String.valueOf(wmPoiId));
            paramMap.put("wmPoiName", wmPoiDomain.getName());
            paramMap.put("recordKey", notifyBo.getRecordKey());
            String callbackUrl = "?recordKey=" + URLEncoder.encode(notifyBo.getRecordKey() , "UTF-8") + "&wmPoiName=" +
                    URLEncoder.encode(wmPoiDomain.getName() , "UTF-8") + "&wmPoiId=" + wmPoiId + "&accId=" +
                    poiAcct.getAcctId() + "&accName=" + URLEncoder.encode(poiAcct.getName() , "UTF-8");
            callbackUrl = MccConfig.getEcontractHostKey() + MccConfig.getC1AutoRenewalPushCallbackUrl() + callbackUrl;
            paramMap.put("callbackUrl", callbackUrl);
            //拼装签约协议链接
            String downLoadUrl = notifyBo.getDownLoadUrl();
            if (StringUtils.isNotBlank(downLoadUrl)) {
                Map<String, String> protocolMap = new HashMap<>();
                String domainName = "https://econtract.meituan.com";
                if (downLoadUrl.endsWith(".pdf")) {
                    protocolMap.put("delivery", domainName + downLoadUrl);
                } else {
                    JSONObject downLoadUrlJson = JSON.parseObject(downLoadUrl);
                    for (String key : downLoadUrlJson.keySet()) {
                        String url = downLoadUrlJson.getString(key);
                        protocolMap.put(key, domainName + url);
                    }
                }
                StringBuilder protocolVersionUrlStr = new StringBuilder();
                for (EcontractPdfTypeEnum pdfTypeEnum : EcontractPdfTypeEnum.values()) {
                    String url = protocolMap.get(pdfTypeEnum.getName());
                    if (StringUtils.isNotBlank(url)) {
                        protocolVersionUrlStr.append("<a target=\"_blank\" href=\"").append(url).append("\">").append(pdfTypeEnum.getDesc()).append("</a>").append("、");
                    }
                }
                if (protocolVersionUrlStr.length() > 0) { // 确保 StringBuilder 不是空的
                    protocolVersionUrlStr.setLength(protocolVersionUrlStr.length() - 1); // 设置长度减 1 来移除最后一个字符、
                }
                paramMap.put("protocolVersionUrlStr", protocolVersionUrlStr.toString());
            }
            LOGGER.info("buildSignPushMessage paramMap={}", JSON.toJSONString(paramMap));
            return paramMap;
        } catch (Exception e) {
            LOGGER.error("buildSignPushMessageBase error",e);
            throw new  WmCustomerException(500, "构建商家端push消息参数异常");
        }
    }
}