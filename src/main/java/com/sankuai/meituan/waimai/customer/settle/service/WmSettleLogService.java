package com.sankuai.meituan.waimai.customer.settle.service;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.settle.constant.WmSettleOpLogProp;
import com.sankuai.meituan.waimai.customer.util.diff.BeanDiffUtil;
import com.sankuai.meituan.waimai.customer.util.trans.TransUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.OplogSearchPageBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.OplogBoPageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class WmSettleLogService {
    private static Logger LOGGER = LoggerFactory.getLogger(WmSettleLogService.class);

    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;
    @Autowired
    private WmPoiClient wmPoiClient;

    public long addWmSettleLog(int wmCustomerId, WmSettle wmSettle, WmSettle store, int opUid, String opUname) {
        WmCustomerOplogBo.OpType opType;
        String diff = "";
        if (wmSettle == null && store == null) {//都为空不允许
            return 0;
        } else if (wmSettle == null) {//新增为空：删除操作
            opType = WmCustomerOplogBo.OpType.DELETE;
            wmSettle = new WmSettle();
            wmSettle.setId(store.getId());
            diff += "===删除结算" + store.getId() + "===\n";
        } else if (store == null) {//原存储为空：新增操作
            opType = WmCustomerOplogBo.OpType.INSERT;
            store = new WmSettle();
            diff += "===新增结算===\n";
        } else {//都不为空：修改操作
            opType = WmCustomerOplogBo.OpType.UPDATE;
        }

        long result;
        try {
            diff += diffForObject(store, wmSettle, WmSettleOpLogProp.SETTLE_PROP_DESC_MAP, opType);

            diff += diffForBindingPoiInfo(store,wmSettle,opType);

            diff = StringUtils.isBlank(diff) ? "结算" + wmSettle.getId() + "没有变更" : diff;
            result = insertWmSettleLog(wmCustomerId, opType, opUid, opUname, diff);
        } catch (WmCustomerException e) {
            LOGGER.error("记操作日志出错,wmSettleId = " + wmSettle.getId(), e);
            return 0;
        }
        return result;
    }

    /**
     * 记录绑定门店的diff
     * @param store 老数据
     * @param wmSettle 新数据
     * @return
     */
    private String diffForBindingPoiInfo(WmSettle store, WmSettle wmSettle,WmCustomerOplogBo.OpType opType) {
        StringBuilder sb = new StringBuilder("");
        //新增结算
        if (opType == WmCustomerOplogBo.OpType.INSERT) {
//            新增结算关联门店：麦当劳a店 123456、麦当劳b店 237182、麦当劳c店 372812
            if (CollectionUtils.isNotEmpty(wmSettle.getWmPoiIdList())) {
                sb.append("新增结算关联门店:");
                sb.append(genWmPoiNamesStr(wmSettle.getWmPoiIdList())).append("</br>");
            }
        } else if (opType == WmCustomerOplogBo.OpType.DELETE) {
            // 删除无特殊处理
        } else if (opType == WmCustomerOplogBo.OpType.UPDATE) {
//            新增结算关联门店：【门店名称】 【门店id】
//            删除结算关联门店：【门店名称】 【门店id】
            List<Integer> oldWmPoiList = MoreObjects
                .firstNonNull(store.getWmPoiIdList(), Lists.<Integer>newArrayList());
            List<Integer> newWmPoiList = MoreObjects
                .firstNonNull(wmSettle.getWmPoiIdList(), Lists.<Integer>newArrayList());

            //新增结算关联门店
            List<Integer> addList = BeanDiffUtil.genAddList(newWmPoiList, oldWmPoiList);
            //删除结算关联门店
            List<Integer> deleteList = BeanDiffUtil.genDeleteList(newWmPoiList, oldWmPoiList);

            if (CollectionUtils.isNotEmpty(addList)) {
                sb.append("新增结算关联门店:");
                sb.append(genWmPoiNamesStr(addList)).append("</br>");
            }

            if (CollectionUtils.isNotEmpty(deleteList)) {
                sb.append("删除结算关联门店:");
                sb.append(genWmPoiNamesStr(deleteList)).append("</br>");
            }
        }
        return sb.toString();
    }

    public String genWmPoiNamesStr(List<Integer> wmPoiIdList) {
        List<WmPoiDomain> wmPoiDomainList = Lists.newArrayList();
        try {
            wmPoiDomainList = wmPoiClient
                .pageGetWmPoiByWmPoiIdList(TransUtil.IntegerList2Long(wmPoiIdList));
        } catch (WmCustomerException e) {
            LOGGER.error("pageGetWmPoiByWmPoiIdList异常", e);
        }
        StringBuilder sb = new StringBuilder("");
        WmPoiDomain temp = null;
        for (int k = 0; k < wmPoiDomainList.size(); k++) {
            temp = wmPoiDomainList.get(k);
            if (k == wmPoiDomainList.size() - 1) {
                sb.append(temp.getName()).append(" ").append(temp.getWmPoiId());
            } else {
                sb.append(temp.getName()).append(" ").append(temp.getWmPoiId()).append("、");
            }
        }
        return sb.toString();
    }


    private <T> String diffForObject(T before, T after, Map<String, BeanDiffUtil.PropertyDesc> propDescMap, WmCustomerOplogBo.OpType opType) throws WmCustomerException {
        if (after == null || before == null || propDescMap == null) {
            return "";
        }
        return BeanDiffUtil.diffContentByMapWithId(before, after, propDescMap, opType.getDesc());
    }


    public long insertWmSettleLog(Integer customerId, WmCustomerOplogBo.OpType opType, int opUid, String opName, String log) {
        WmCustomerOplogBo customerOplogBo = new WmCustomerOplogBo()
                .setCustomerId(customerId)
                .setModuleType(WmCustomerOplogBo.OpModuleType.SETTLE.type)
                .setModuleId(customerId)
                .setOpType(opType.type)
                .setLog(log)
                .setOpUid(opUid)
                .setOpUname(opName);
        try {
            return wmCustomerOplogService.insert(customerOplogBo);
        } catch (WmCustomerException e) {
            LOGGER.error(e.getMsg(), e);
        }
        return 0;
    }

    public String getFailReason(int customerId) {
        OplogSearchPageBo query = new OplogSearchPageBo();
        query.setCustomerId(customerId);
        query.setModuleType(WmCustomerOplogBo.OpModuleType.SETTLE.type);
        query.setModuleId(customerId);
        query.setOpType(WmCustomerOplogBo.OpType.CHANGESTATUS.type);
        query.setPageSize(1);
        String result = "";
        OplogBoPageData pageDate = null;
        try {
            pageDate = wmCustomerOplogService.search(query, 0, "");
        } catch (WmCustomerException | TException e) {
            LOGGER.error("wmCustomerOplogService异常", e);
            return result;
        }
        if (CollectionUtils.isNotEmpty(pageDate.getList())) {
            WmCustomerOplogBo customerOplogBo = pageDate.getList().get(0);
            result = customerOplogBo.getLog();
//            String log = customerOplogBo.getLog();
//            if (log != null && log.length() >= 3) {
//                result = log.substring(log.lastIndexOf("原因：") + 3);
//            }
        }
        return result;
    }

    public WmCustomerOplogBo getLatestSettleModifyLog(int customerId) {
        OplogSearchPageBo query = new OplogSearchPageBo();
        query.setCustomerId(customerId);
        query.setModuleType(WmCustomerOplogBo.OpModuleType.SETTLE.type);
        query.setModuleId(customerId);
        query.setOpType(WmCustomerOplogBo.OpType.UPDATE.type);
        query.setPageSize(1);
        OplogBoPageData pageDate = null;
        try {
            pageDate = wmCustomerOplogService.search(query, 0, "");
        } catch (WmCustomerException | TException e) {
            LOGGER.error("wmCustomerOplogService异常", e);
            return null;
        }
        if (CollectionUtils.isNotEmpty(pageDate.getList())) {
            WmCustomerOplogBo customerOplogBo = pageDate.getList().get(0);
            return customerOplogBo;
        }
        return null;
    }

}
