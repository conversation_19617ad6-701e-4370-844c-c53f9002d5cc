package com.sankuai.meituan.waimai.customer.service.sc.aop;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.thrift.util.ObjectMapperUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;

/**
 * 服务参数日志
 */
@Aspect
@Component
@Slf4j
public class ServiceParamLogAspect {

    @Pointcut(value = "execution(* com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenPoiThriftService..*(..))")
    public void pointcut() {
    }

    @Before(value = "pointcut()")
    public void around(JoinPoint joinPoint) {
        try {
            LinkedHashMap<String, String> map = Maps.newLinkedHashMap();
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            Object[] paramValues = joinPoint.getArgs();
            String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
            String cmName = getShortClassName(className) +"."+methodName;
            for (int i = 0; i < paramNames.length; i++) {
                map.put(paramNames[i], ObjectMapperUtils.parseString(paramValues[i]));
            }
            log.info("[食堂管理]:请求类名.方法名:{},方法入参:{}", cmName, map);
        } catch (Exception e) {
           log.info("ServiceParamLogAspect Error",e);
        }
    }

    public static String getShortClassName(String className) {
        int index = className.lastIndexOf(".");
        return className.substring(index + 1);
    }
}
