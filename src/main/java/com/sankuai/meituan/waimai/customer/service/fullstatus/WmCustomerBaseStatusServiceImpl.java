package com.sankuai.meituan.waimai.customer.service.fullstatus;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.adapter.WmAorServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.MscPoiAggreConstant;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealTypeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.dto.CustomerMscUsedPoiDetailDTO;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.infra.domain.WmUniAor;
import com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum;
import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerMscUsedPoiDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModule;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModuleStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.StatusEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiQualificationInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.constant.customer.MscPoiAggreConstant.MSC_POI_AGGRE_FIELDS_QUA;

/**
 * 获取客户基础信息状态
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WmCustomerBaseStatusServiceImpl implements WmCustomerModuleStatusSevice {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerRealTypeService wmCustomerRealTypeService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;
    @Autowired
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Override
    public boolean on() {
        return ConfigUtilAdapter.getBoolean("WmCustomerBaseStatusServiceImpl.switch.on", true);
    }

    @Override
    public CustomerModule getModule() {
        return CustomerModule.CUSTOMER_BASE;
    }

    @Override
    public CustomerModuleStatus getStatus(int customerId, long wmPoiId) throws WmCustomerException {
        CustomerModuleStatus customerModuleStatus = new CustomerModuleStatus();
        WmCustomerDB wmCustomerDB;

        //查询客户平台客户信息进行生效模块校验
        wmCustomerDB = wmCustomerService.selectPlatformCustomerByIdFromSlave(customerId);
        if (wmCustomerDB == null) {
            customerModuleStatus.setStatusEnum(StatusEnum.IN_EFFECTIVE);
            customerModuleStatus.setReason("客户未找到");
            return customerModuleStatus;
        }
        if (wmCustomerDB.getEffective() == CustomerConstants.UNEFFECT) {
            customerModuleStatus.setStatusEnum(StatusEnum.IN_EFFECTIVE);
            customerModuleStatus.setReason("客户未生效");
        } else if (wmCustomerDB.getEffective() == CustomerConstants.EFFECT) {
            //如果是营业执照，需要判断有效期
            if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode() && wmCustomerDB.getValidateDate() != 0 && wmCustomerDB.getValidateDate() < DateUtil.unixTime()) {
                customerModuleStatus.setStatusEnum(StatusEnum.IN_EFFECTIVE);
                customerModuleStatus.setReason("客户有效期小于当前日期");
            } else if (MccCustomerConfig.getCheckRuleSwitch() && !wmCustomerRealTypeService.validMatchBizOrgCodeForCustomerAndPoiForPoi(wmPoiId, wmCustomerDB.getBizOrgCode())) {
                // 门店业务线和客户业务线一致性的校验
                customerModuleStatus.setStatusEnum(StatusEnum.IN_EFFECTIVE);
                customerModuleStatus.setReason("门店品类和客户类型不匹配，请修改后再上线");
            } else {
                //门店类型是外卖、客户类型是美食城且命中灰度，执行美食城档口上限校验
                WmCustomerBasicBo wmCustomerBasicBo = getMscCustomerByCustomerNumberAndHitGray(wmPoiId);
                if (wmCustomerBasicBo != null){
                    // 通过资质命中的美食城在灰度范围内 20241203
                    String mscCheckPoiCntResult = checkMscPoiCntRuleV2(wmCustomerBasicBo, wmPoiId);
                    //校验不通过返回文案
                    if (StringUtils.isNotBlank(mscCheckPoiCntResult)) {
                        customerModuleStatus.setStatusEnum(StatusEnum.IN_EFFECTIVE);
                        customerModuleStatus.setReason(mscCheckPoiCntResult);
                    } else {
                        customerModuleStatus.setStatusEnum(StatusEnum.EFFECTIVE);
                    }
                    return customerModuleStatus;
                }
                // 此逻辑全量之后可以下掉
                if (CustomerRealTypeEnum.MEISHICHENG.getValue() == wmCustomerDB.getCustomerRealType() && !MccCustomerConfig.getMscPoiCntCheckNewSwitch()) {
                    String mscCheckPoiCntResult = mscCheckChildPoiResult(wmCustomerDB, wmPoiId);
                    //校验不通过返回文案
                    if (StringUtils.isNotBlank(mscCheckPoiCntResult)) {
                        customerModuleStatus.setStatusEnum(StatusEnum.IN_EFFECTIVE);
                        customerModuleStatus.setReason(mscCheckPoiCntResult);
                    } else {
                        customerModuleStatus.setStatusEnum(StatusEnum.EFFECTIVE);
                    }
                } else {
                    customerModuleStatus.setStatusEnum(StatusEnum.EFFECTIVE);
                }
            }
        }
        return customerModuleStatus;
    }

    private WmCustomerBasicBo getMscCustomerByCustomerNumberAndHitGray(Long wmPoiId) throws WmCustomerException {
        try{
            log.info("上线检查点-根据门店资质编码获取美食城客户信息:{}",wmPoiId);
            WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggre(wmPoiId,MSC_POI_AGGRE_FIELDS_QUA);
            if (wmPoiAggre == null || CollectionUtils.isEmpty(wmPoiAggre.getWm_poi_qua_info_list())) {
                // 门店不存在或门店资质列表不存在
                return null;
            }

            if (PoiOrgEnum.WAI_MAI.getCode() != wmPoiAggre.getBiz_org_code()) {
                // 非外卖门店，不校验美食城档口上限
                return null;
            }

            // 取类型为营业执照的资质信息
            WmPoiQualificationInfo wmPoiQuaInfo = wmPoiAggre.getWm_poi_qua_info_list().stream()
                    .filter(quaInfo -> quaInfo.getType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSubType())
                    .findFirst()
                    .orElse(null);
            if (wmPoiQuaInfo == null) {
                // 营业执照资质信息不存在
                return null;
            }
            // 根据门店资质编码获取美食城客户信息
            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerByNumberAndTypeAndBizCode(wmPoiQuaInfo.getNumber(),CustomerType.CUSTOMER_TYPE_BUSINESS.getCode(), CustomerBizOrgEnum.WAI_MAI.getCode());
            if (wmCustomerBasicBo == null || wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue() || wmCustomerBasicBo.getEffective() != CustomerConstants.EFFECT) {
                return null;
            }
            log.info("上线检查点-根据门店资质编码获取美食城客户信息结果:{}",JSON.toJSONString(wmCustomerBasicBo));
            if (!wmCustomerGrayService.isGrayMscPoiCntCheckNew(wmCustomerBasicBo.getOwnerUid())){
                return null;
            }
            return wmCustomerBasicBo;
        }catch (Exception e){
            log.error("上线检查点-根据门店资质编码获取美食城客户信息系统异常：{}",wmPoiId,e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"获取上线检查点异常");
        }

    }


    /**
     * 校验美食城子门店档口数返回结果
     *
     * @param wmCustomerDB
     * @param wmPoiId
     * @return
     */
    private String mscCheckChildPoiResult(WmCustomerDB wmCustomerDB, Long wmPoiId) throws WmCustomerException {
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId);
        if (wmPoiAggre == null) {
            return "门店未找到";
        }

        //有资质共用客户标签则不校验
        Boolean hasQuaComCustomerTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
        if (hasQuaComCustomerTag) {
            return null;
        }

        List<Integer> wmPoiLabels = Arrays.stream(wmPoiAggre.getLabel_ids().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        //子门店直接返回
        if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
            if (!WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(wmPoiAggre.getSub_wm_poi_type())) {
                return null;
            }
        } else {
            if (StringUtils.isNotBlank(wmPoiAggre.getLabel_ids())
                    && !Collections.disjoint(wmPoiLabels, MccCustomerConfig.getSubPoiTagId())) {
                return null;
            }
        }

        //命中档口数新规则使用新校验
        if (wmCustomerGrayService.checkHitMscCustomerUsedPoiGray()) {
            return checkMscPoiCntRule(wmCustomerDB,wmPoiId);
        }

        String mscSpInfoStr = wmCustomerDB.getCustomerRealTypeSpInfo();
        if (StringUtils.isBlank(mscSpInfoStr)
                && !MccCustomerConfig.getMscAllowNoPoiCntSwitch()) {
            return "客户未填写档口数或填写的档口数未生效，无法上线";
        }
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSON.parseObject(mscSpInfoStr, CustomerRealTypeSpInfoBo.class);
        if ((customerRealTypeSpInfoBo == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() == null)
                && !MccCustomerConfig.getMscAllowNoPoiCntSwitch()) {
            return "客户未填写档口数或填写的档口数未生效，无法上线";
        }

        //已占用档口数>=档口数 则不允许上线
        if (customerRealTypeSpInfoBo != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() != null) {
            CustomerMscUsedPoiDTO customerMscUsedPoiDTO = wmCustomerService.getMscUsedPoiDTO(wmCustomerDB.getId());
            if (customerMscUsedPoiDTO != null && customerMscUsedPoiDTO.getUsedPoiCnt() != null
                    && (customerMscUsedPoiDTO.getUsedPoiCnt() >= customerRealTypeSpInfoBo.getFoodCityPoiCount())) {
                return "美食城客户可用档口数不足，无法上线";
            }
        }
        return null;
    }

    private String checkMscPoiCntRuleV2(WmCustomerBasicBo wmCustomerBasicBo , Long wmPoiId) throws WmCustomerException {
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId);
        if (wmPoiAggre == null) {
            return "门店未找到";
        }
        List<Integer> wmPoiLabels = Arrays.stream(wmPoiAggre.getLabel_ids().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        //子门店直接返回
        if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
            if (!WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(wmPoiAggre.getSub_wm_poi_type())) {
                return null;
            }
        } else {
            if (StringUtils.isNotBlank(wmPoiAggre.getLabel_ids())
                    && !Collections.disjoint(wmPoiLabels, MccCustomerConfig.getSubPoiTagId())) {
                return null;
            }
        }

        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = wmCustomerBasicBo.getCustomerRealTypeSpInfoBo();
        if (customerRealTypeSpInfoBo == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() <= 0){
            return "申请上线门店的资质对应的美食城客户档口数量未维护，不可上线，对应美食城客户ID为" + wmCustomerBasicBo.getMtCustomerId();
        }
        //美食城档口数已维护场景
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerBasicBo.getId());
        Integer afterOnLineUsedPoiCnt = 0;
        if (mscUsedPoiDetailDTO.getMscPoiInfoDTOList() != null){
            //判断当前要上线的门店是否包含在已占用档口数中
            if (mscUsedPoiDetailDTO.getMscPoiInfoDTOList().stream().anyMatch(mscPoiInfoDTO -> mscPoiInfoDTO.getPoiId().equals(wmPoiId))){
                // 如果已占用档口数中已经包含了当前的门店ID，则当前门店上线之后的已占用档口数不变
                afterOnLineUsedPoiCnt = mscUsedPoiDetailDTO.getUsedPoiCnt();
            }else {
                // 如果已占用档口数中没有包含当前的门店ID，则当前门店上线之后的已占用档口数需要 +1
                afterOnLineUsedPoiCnt = mscUsedPoiDetailDTO.getUsedPoiCnt() + 1;
            }
        }
        // 判断当前门店上线之后的已占用档口数是否超过美食城客户对应的档口数
        if (afterOnLineUsedPoiCnt > customerRealTypeSpInfoBo.getFoodCityPoiCount()) {
            return "申请上线门店对应的美食城客户档口数量已超上限或上线后将超过上限，不许门店上线，对应美食城客户ID为" + wmCustomerBasicBo.getMtCustomerId();
        }
        // 判断资质共用标签
        boolean hasQuaComCustomerTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerBasicBo.getMtCustomerId());
        if (!hasQuaComCustomerTag) {
            // 没有资质共用标签，直接返回
            return null;
        }
        // 查询蜂窝信息，判断二级物理城市是否为北京
        // 如果有资质共用标签，根据蜂窝判断物理城市
        WmUniAor wmUniAor = wmAorServiceAdapter.getAorInfoById(customerRealTypeSpInfoBo.getFoodCityAorId());
        if (wmUniAor == null || wmUniAor.getLocationInfos() == null || CollectionUtils.isEmpty(wmUniAor.getLocationInfos().getLevel2CityIds())){
            return null;
        }
        Map<Integer,Integer> mapSpecialCityMap = MccCustomerConfig.getSpecialCityPoiCntLimit();
        if (mapSpecialCityMap.containsKey(wmUniAor.getLocationInfos().getLevel2CityIds().get(0))){
            // 北京地区，校验档口数是不是大于3
            if (afterOnLineUsedPoiCnt >mapSpecialCityMap.get(wmUniAor.getLocationInfos().getLevel2CityIds().get(0))){
                return "申请上线门店的资质对应的客户为“资质共用特殊场景”的美食城客户，且客户物理城市为“北京”，档口数量上限为3。本次门店上线将超过此数量，不可上线";

            }
        }
        return null;
    }

    /**
     * 美食城档口数新规则校验
     * 1：档口数没维护允许上线
     * 2：档口数维护的话，已占用档口数>=维度的档口数，则不允许上线
     *
     * @param wmCustomerDB
     * @return
     */
    private String checkMscPoiCntRule(WmCustomerDB wmCustomerDB , Long wmPoiId) throws WmCustomerException {
        String mscSpInfoStr = wmCustomerDB.getCustomerRealTypeSpInfo();
        //美食城信息未维护则允许上线不拦截
        if (StringUtils.isBlank(mscSpInfoStr)) {
            return null;
        }
        //美食城信息对象判空
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSON.parseObject(mscSpInfoStr, CustomerRealTypeSpInfoBo.class);
        if (customerRealTypeSpInfoBo == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() == null) {
            return null;
        }
        //美食城档口数已维护场景
        if (customerRealTypeSpInfoBo.getFoodCityPoiCount() != null) {
            CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerDB.getId());
            Integer afterOnLineUsedPoiCnt = 0;
            if (mscUsedPoiDetailDTO.getMscPoiInfoDTOList() != null){
                //判断当前要上线的门店是否包含在已占用档口数中
                if (mscUsedPoiDetailDTO.getMscPoiInfoDTOList().stream().anyMatch(mscPoiInfoDTO -> mscPoiInfoDTO.getPoiId().equals(wmPoiId))){
                    // 如果已占用档口数中已经包含了当前的门店ID，则当前门店上线之后的已占用档口数不变
                    afterOnLineUsedPoiCnt = mscUsedPoiDetailDTO.getUsedPoiCnt();
                }else {
                    // 如果已占用档口数中没有包含当前的门店ID，则当前门店上线之后的已占用档口数需要 +1
                    afterOnLineUsedPoiCnt = mscUsedPoiDetailDTO.getUsedPoiCnt() + 1;
                }
            }
            // 判断当前门店上线之后的已占用档口数是否超过美食城客户对应的档口数
            if (afterOnLineUsedPoiCnt > customerRealTypeSpInfoBo.getFoodCityPoiCount()) {
                return "申请上线门店对应的美食城客户档口数量已超上限或上线后将超过上限，不许门店上线，对应美食城客户ID为" + wmCustomerDB.getMtCustomerId();
            }
        }
        return null;
    }

}
