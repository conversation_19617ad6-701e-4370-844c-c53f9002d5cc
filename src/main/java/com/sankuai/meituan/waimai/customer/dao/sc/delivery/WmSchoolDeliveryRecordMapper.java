package com.sankuai.meituan.waimai.customer.dao.sc.delivery;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmSchoolDeliveryRecordMapper {

    int insert(WmSchoolDeliveryRecord record);

    List<WmSchoolDeliveryRecord> selectByIds(@Param("ids") List<Long> ids);

    void updateByPrimaryKeySelective(WmSchoolDeliveryRecord record);

    void updateByPrimaryKey(WmSchoolDeliveryRecord record);
}