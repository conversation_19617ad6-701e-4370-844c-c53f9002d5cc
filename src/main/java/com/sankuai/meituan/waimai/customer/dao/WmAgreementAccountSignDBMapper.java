package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementAccountSign;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementSignLogDB;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-10-26 21:36
 * Email: <EMAIL>
 * Desc:
 */
public interface WmAgreementAccountSignDBMapper {

    int deleteByPrimaryKey(Long id);

    int insert(WmAgreementAccountSign record);

    int insertSelective(WmAgreementAccountSign record);

    WmAgreementAccountSign selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmAgreementAccountSign record);

    int updateByPrimaryKey(WmAgreementAccountSign record);

    List<WmAgreementAccountSign> queryRecordByAggrementIdAndAccountId(@Param("aggremengIds") List<Integer> agreementIds, @Param("acctId") Long acctId);

    int batchInsert(@Param("accountSignList")List<WmAgreementAccountSign> accountSignList);

    int batchUpdateById(@Param("accountSignList")List<WmAgreementAccountSign> accountSignList);

    List<WmAgreementAccountSign> batchQueryByAccountIdAndAggrementTypes(@Param("aggrementTypes") List<Integer> aggrementTypes, @Param("acctId") Long acctId);

}
