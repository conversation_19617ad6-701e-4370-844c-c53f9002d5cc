package com.sankuai.meituan.waimai.customer.dao;


import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchContextDB;

@Component
public interface WmEcontractBatchContextDBMapper {

    public static final String TABLE_NAME = "wm_econtract_sign_batch_context";
    public static final String SELECT_KEYS = "id, batch_id AS batchId, context, valid, ctime, utime";

    /**
     * 保存批量签约冷数据
     */
    int insert(WmEcontractSignBatchContextDB batchContextDB);

    /**
     * 根据batchId获取批量签约冷数据
     * @param batchId
     * @return
     */
    WmEcontractSignBatchContextDB queryByBatchId(@Param("batchId") Long batchId);

    /**
     * 根据batchId获取批量签约冷数据(主库)
     * @param batchId
     * @return
     */
    WmEcontractSignBatchContextDB queryByBatchIdMaster(@Param("batchId") Long batchId);

    /**
     * 根据batchId列表批量获取批量签约冷数据
     * @return
     */
    List<WmEcontractSignBatchContextDB> queryByBatchIdList(@Param("batchIdList") List<Long> batchIdList);

    /**
     * 根据batchId列表批量获取批量签约冷数据(主库)
     * @return
     */
    List<WmEcontractSignBatchContextDB> queryByBatchIdListMaster(@Param("batchIdList") List<Long> batchIdList);

    /**
     * 根据主键Id获取批量签约冷数据
     * @param id
     * @return
     */
    WmEcontractSignBatchContextDB queryById(@Param("id") Long id);

    /**
     * 根据batchId删除批量签约冷数据
     * @param batchId
     */
    void deleteByBatchId(@Param("batchId") Long batchId);

    /**
     * 翻页获取打包签约任务（已完成大文本改造）
     */
    List<WmEcontractSignBatchContextDB> queryEntityListWithLabel4Encryption(@Param("lastId") long lastId, @Param("size") int size);

    int updateOriginalRecordById(Long id);

    void batchUpdateOriginalRecordByIds(@Param("ids") List<Long> ids);
}
