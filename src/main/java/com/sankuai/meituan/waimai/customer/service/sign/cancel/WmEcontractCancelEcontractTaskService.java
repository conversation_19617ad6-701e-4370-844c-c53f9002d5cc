package com.sankuai.meituan.waimai.customer.service.sign.cancel;

import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.WmEcontractBatchApplyService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 从电子合同平台撤销
 */
@Service
public class WmEcontractCancelEcontractTaskService extends WmEcontractCancelBzService {

    @Resource
    private WmEcontractBatchApplyService wmEcontractBatchApplyService;

    @Resource
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    public Boolean cancel(EcontractTaskBo taskBo) throws WmCustomerException, TException {
        //更新poistate
        cancelPoiState(taskBo);
        //更新task信息
        cancelTask(taskBo);
        //更新batch状态
        cancelBatch(taskBo);
        //更新关联信息
        cancelRel(taskBo);
        //从电子合同平台撤回
        WmEcontractSignBatchDB batchDB = wmEcontractBatchBaseService.queryByBatchId(taskBo.getBatchId());
        return wmEcontractBatchApplyService.cancelEContract(batchDB).isRes();
    }
}
