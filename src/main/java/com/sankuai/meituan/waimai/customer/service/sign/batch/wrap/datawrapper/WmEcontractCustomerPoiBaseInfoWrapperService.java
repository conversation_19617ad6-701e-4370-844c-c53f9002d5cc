package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_POI_BASEINFO)
public class WmEcontractCustomerPoiBaseInfoWrapperService implements IWmEcontractDataWrapperService {

    static final Map<String, String> subjectEContractTypeMap = Maps.newHashMap();

    @PostConstruct
    public void init(){
        subjectEContractTypeMap.put(ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc(), PdfConstant.MT_SIGNKEY);
        subjectEContractTypeMap.put(ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc(), PdfConstant.MT_SH_SIGNKEY);
        subjectEContractTypeMap.put(ContractSignSubjectEnum.SHENZHEN_SHOUKANG.getDesc(), PdfConstant.SZ_BS_JK_SIGNKEY);
    }

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCH_POI_BASEINFO_GENERATE_PDF);
        log.info("taskBo = {}", JSON.toJSONString(taskBo));
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("BATCH_POIBASEINFO_TEMPLATE_ID", 152)); // 指定模版
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("BATCH_POIBASEINFO_TEMPLATE_VERSION")); // (可选)指定版本，不指定或传null、传0的话则默认为当前已发布的版本
        pdfInfoBo.setPdfMetaContent(generatePdfObjectMeta(taskBo));
        pdfInfoBo.setPdfBizContent(generatePdfObjectBiz(taskBo));
        return Lists.newArrayList(pdfInfoBo);
    }

    private Map<String, String> generatePdfObjectMeta(EcontractTaskBo taskBo) {
        EcontractBatchPoiBaseInfoBo poiBaseInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchPoiBaseInfoBo.class);
        Map<String, String> metaMap = Maps.newHashMap();
        metaMap.put("partAName", poiBaseInfoBo.getEcontractContractInfoBo().getPartAName());
        metaMap.put("partBName", poiBaseInfoBo.getEcontractContractInfoBo().getPartBName());
        metaMap.put("contractNum", poiBaseInfoBo.getEcontractContractInfoBo().getContractNum());
        metaMap.put("partASignTime", poiBaseInfoBo.getEcontractContractInfoBo().getPartASignTime());
        metaMap.put("partBSignTime", poiBaseInfoBo.getEcontractContractInfoBo().getPartBSignTime());
        metaMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        metaMap.put("partBEstamp", subjectEContractTypeMap.getOrDefault(poiBaseInfoBo.getEcontractContractInfoBo().getPartBName(), PdfConstant.MT_SIGNKEY));
        log.info("metaMap = {}", JSON.toJSONString(metaMap));
        return metaMap;
    }

    private List<Map<String, String>> generatePdfObjectBiz(EcontractTaskBo taskBo) throws WmCustomerException, IllegalAccessException {
        EcontractBatchPoiBaseInfoBo poiBaseInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchPoiBaseInfoBo.class);
        List<EcontractPoiBaseInfoBo> econtractPoiBaseInfoBoList = poiBaseInfoBo.getEcontractPoiBaseInfoBoList();
        List<Map<String, String>> bizList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(econtractPoiBaseInfoBoList)){
            for(EcontractPoiBaseInfoBo bo : econtractPoiBaseInfoBoList){
                Map<String, String> subMap = MapUtil.Object2Map(bo);
                bizList.add(subMap);
            }
        }
        log.info("bizList = {}", JSON.toJSONString(bizList));
        return bizList;
    }
}
