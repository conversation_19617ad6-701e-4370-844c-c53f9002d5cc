package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.aspect.AreaDataWrapper;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-07-13 16:16
 * Email: <EMAIL> Desc:
 */
@Slf4j
@Service
@AreaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_DELIVERY_WHOLE_CITY)
public class WmEcontractBatchDeliveryWholeCityAreaDataWrapperService implements IWmEcontractAreaDataWrapperService {

    public static final String SUPPORT_MARK = "support";

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {

        List<EcontractContentBo> result = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = null;

        try {
            econtractBatchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        } catch (Exception e) {
            log.warn("数据解析异常", e);
            return result;
        }

        if (econtractBatchDeliveryInfoBo == null) {
            return result;
        }

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = econtractBatchDeliveryInfoBo.getEcontractDeliveryInfoBoList();

        if (CollectionUtils.isEmpty(econtractDeliveryInfoBoList)) {
            return result;
        }

        EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBoTemp = null;
        EcontractDeliveryWholeCityInfoBo econtractDeliveryWholeCityInfoBoTemp = null;
        for (EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {
            econtractDeliveryWholeCityInfoBoTemp = temp.getEcontractDeliveryWholeCityInfoBo();
            if (econtractDeliveryWholeCityInfoBoTemp == null) {
                continue;
            }
            if (SUPPORT_MARK.equals(econtractDeliveryWholeCityInfoBoTemp.getSupportSLA())
                    && StringUtils.isNotEmpty(econtractDeliveryWholeCityInfoBoTemp.getDeliveryArea())) {
                econtractWmPoiSpAreaBoTemp = JSONArray.parseObject(econtractDeliveryWholeCityInfoBoTemp.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
                if (econtractWmPoiSpAreaBoTemp == null) {
                    continue;
                }
                econtractWmPoiSpAreaBoTemp.setSlaText(WmEcontractConstant.DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT);
                result.add(WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(econtractWmPoiSpAreaBoTemp));
            }
        }
        return result;
    }
}
