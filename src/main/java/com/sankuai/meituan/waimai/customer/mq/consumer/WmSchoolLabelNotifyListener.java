package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.mq.domain.WmSchoolLabelNotifyBO;
import com.sankuai.meituan.waimai.customer.mq.domain.WmSchoolLabelNotifyDetailBO;
import com.sankuai.meituan.waimai.customer.service.sc.WmScLogService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService;
import com.sankuai.meituan.waimai.operation.label.constants.LabelSubjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 学校-标签关系(打标、换标、掉标)监听消费者
 * -@author: houjikang
 * -@date: 2023/03/28
 * -@email: <EMAIL>
 */
@Slf4j
@Service
public class WmSchoolLabelNotifyListener implements IMessageListener {

    @Autowired
    private WmScSchoolService wmScSchoolService;

    @Autowired
    private WmScLogService wmScLogService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        log.info("[WmSchoolLabelNotifyListener] 监听到标签侧变更消息, mafkaMessage = {}, partition = {}", mafkaMessage.getBody(), mafkaMessage.getParttion());
        if (null == mafkaMessage.getBody() || StringUtils.isBlank(mafkaMessage.getBody().toString())) {
            log.error("[WmSchoolLabelNotifyListener] 标签侧变更消息为空, 不进行处理");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            // 对消费的消息进行解析
            String json = mafkaMessage.getBody().toString();
            WmSchoolLabelNotifyBO wmSchoolLabelNotifyBO = JSONObject.parseObject(json, WmSchoolLabelNotifyBO.class);
            log.info("[WmSchoolLabelNotifyListener] 解析得到wmSchoolLabelNotifyBO = {}", JSON.toJSONString(wmSchoolLabelNotifyBO));
            if (wmSchoolLabelNotifyBO == null) {
                log.error("[WmSchoolLabelNotifyListener] 解析消息异常, 解析json = {}", json);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 表名过滤
            if (wmSchoolLabelNotifyBO.getTableName() != null && !wmSchoolLabelNotifyBO.getTableName().contains("wm_poi_label_rel")) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 获取消息体中data
            WmSchoolLabelNotifyDetailBO wmSchoolLabelNotifyDetailBO = wmSchoolLabelNotifyBO.getData();
            if (wmSchoolLabelNotifyDetailBO == null) {
                log.error("[WmSchoolLabelNotifyListener] 消息体中data为空, wmSchoolLabelNotifyBO = {}", JSONObject.toJSONString(wmSchoolLabelNotifyBO));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 对象类型过滤
            if (wmSchoolLabelNotifyDetailBO.getSubjectType() != null && !wmSchoolLabelNotifyDetailBO.getSubjectType().equals(LabelSubjectTypeEnum.SCHOOL.getCode())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Integer schoolId = wmSchoolLabelNotifyDetailBO.getSubjectId();
            Integer opUid = wmSchoolLabelNotifyDetailBO.getOpUid();
            String opUname = wmSchoolLabelNotifyDetailBO.getOpUname();
            Integer wmLabelId = wmSchoolLabelNotifyDetailBO.getWmLabelId();
            // 同步学校-标签变更到学校表(wm_sc_school)中
            wmScSchoolService.syncSchoolLabelToWmScSchoolDb(schoolId, opUid);
            // 保存本次操作日志到学校食堂操作日志表(wm_sc_oplog)中
            wmScLogService.saveSchoolLabelChangeLog(schoolId, wmLabelId, opUid, opUname, wmSchoolLabelNotifyBO.getType());
        } catch (Exception e) {
            log.error("[WmSchoolLabelNotifyListener] 处理学校标签变更时出现异常, 消息体 = {}", mafkaMessage.getBody().toString(), e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}

