package com.sankuai.meituan.waimai.customer.aspect;

import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.TairLockGrayPercentKeyEnum;
import com.sankuai.meituan.waimai.customer.constant.TairLockStatusEnum;
import com.sankuai.meituan.waimai.customer.util.TairLocker;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import javassist.NotFoundException;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.mortbay.util.ajax.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;

@Aspect
@Component
public class TairLockAspect {

    private static Logger logger = LoggerFactory.getLogger(TairLockAspect.class);

    private static String tairLockKeyPrefix = "customer_";

    private static final String METRIC_NAME_PREFIX = "tair.lock.";

    @Autowired
    private TairLocker tairLocker;

    /**
     * 防重处理逻辑
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        TairLock tairLock = method.getAnnotation(TairLock.class);

        if (tairLock == null) {
            return joinPoint.proceed();
        }

        String currentKey = null;
        try {
            Map<String, Object> argMap = getArgMap(joinPoint, methodSignature);
            if (!shouldIntercept(argMap, tairLock)) {
                logger.info("singleSubmit:{} 不需要进行防重复提交拦截 whenExp == false", JSON.toString(tairLock));
                return joinPoint.proceed();
            }
            if (tairLock.expire() <= 0) {
                logger.info("singleSubmit:{} 不需要进行防重复提交拦截 expire <= 0", JSON.toString(tairLock));
                return joinPoint.proceed();
            }
            if (!grayCheckNeedLock(argMap, tairLock)) {
                logger.info("singleSubmit:{} 不需要进行防重复提交拦截  grayCheckNeedLock", JSON.toString(tairLock));
                return joinPoint.proceed();
            }
            String key = createKey(argMap, tairLock);
            lock(tairLock, key);
            currentKey = key;

        } catch (WmCustomerException e) {
            logger.warn("tairLock doAround error ", e);
            throw e;
        } catch (Throwable e) {
            logger.error("tairLock doAround error ", e);
            unlock(currentKey);
            throw e;
        }

        if (StringUtils.isBlank(currentKey)) {
            return joinPoint.proceed();
        }

        return proceedAndUnlock(joinPoint, currentKey);


    }

    /**
     * 获取方法参数
     *
     * @param pjp
     * @param method
     * @return
     * @throws NotFoundException
     */
    private Map<String, Object> getArgMap(ProceedingJoinPoint pjp, MethodSignature method) throws NotFoundException {
        String[] parameterNames;
        if (AopUtils.isJdkDynamicProxy(pjp.getThis())) {
            parameterNames = ReflectionUtil.getParameterNames(pjp.getTarget().getClass(), method.getMethod().getName());
        } else {
            parameterNames = method.getParameterNames();
        }
        Object[] pjpArgs = pjp.getArgs();

        Map<String, Object> argMap = Maps.newHashMap();

        if (parameterNames != null) {
            for (int i = 0; i < parameterNames.length; i++) {
                argMap.put(parameterNames[i], pjpArgs[i]);
            }
        }
        return argMap;
    }

    /**
     * 判断是否可以插入防重处理
     *
     * @param argMap
     * @param tairLock
     * @return
     */
    private boolean shouldIntercept(Map<String, Object> argMap, TairLock tairLock) {
        if (tairLock != null && StringUtils.isNotBlank(tairLock.whenExp())) {
            Expression expression = AviatorEvaluator.compile(tairLock.whenExp(), true);
            return (boolean) expression.execute(argMap);
        }
        return true;
    }

    /**
     * 灰度判断是否需要加锁
     *
     * @param argMap
     * @param tairLock
     * @return
     */
    private boolean grayCheckNeedLock(Map<String, Object> argMap, TairLock tairLock) {
        if (StringUtils.isBlank(tairLock.grayKey()) || tairLock.grayPercentKey() == TairLockGrayPercentKeyEnum.UN_SETTING) {
            return true;
        }
        try {
            Expression expressionGrayKey = AviatorEvaluator.compile(tairLock.grayKey(), true);

            Long grayKey = Long.valueOf(String.valueOf(expressionGrayKey.execute(argMap)));
            Map<String, Integer> grayPercentMap = MccCustomerConfig.getPreventDuplicationLockGrayPercent();
            if (grayPercentMap.get(tairLock.grayPercentKey().getKey()) == null) {
                return false;
            }
            int grayPercent = grayPercentMap.get(tairLock.grayPercentKey().getKey());
            if (grayKey % 100 < grayPercent) {
                return true;
            }
        } catch (Exception e) {
            logger.error("分布式锁灰度控制异常", e);
        }
        return false;

    }

    /**
     * 生成key
     *
     * @param argMap
     * @param tairLock
     * @return
     */
    private String createKey(Map<String, Object> argMap, TairLock tairLock) {
        try {
            Expression expression = AviatorEvaluator.compile(tairLock.seedExp(), true);
            String seedVal = String.valueOf(expression.execute(argMap));
            return tairLockKeyPrefix + tairLock.group().getGroup() + "_" + seedVal;
        } catch (Exception e) {
            logger.error("createKey 失败 key={}", tairLock.seedExp(), e);
        }
        MetricHelper.build().name(METRIC_NAME_PREFIX + "createKey").tag("status", "FAIL").count();
        return null;
    }

    /**
     * 进行加锁
     *
     * @param tairLock
     * @param key
     * @throws WmCustomerException
     */
    private void lock(TairLock tairLock, String key) throws WmCustomerException {
        if (StringUtils.isBlank(key)) {
            return;
        }
        logger.info("尝试获取全局锁, key: {}", key);
        TairLockStatusEnum lockStatusEnum = tairLocker.tryLockWithReturn(key, tairLock.expire());
        MetricHelper.build().name(METRIC_NAME_PREFIX + tairLock.group().getGroup()).tag("status", lockStatusEnum.getDesc()).count();
        if (lockStatusEnum == TairLockStatusEnum.SUCC) {
            return;
        } else {
            if (!tairLock.needWait()) {
                if (lockStatusEnum == TairLockStatusEnum.FAIL && MccCustomerConfig.getPreventDuplicationLockErrorStopBusinessOperateSwitch()) {
                    repeatCommitException(key);
                    return;
                } else {
                    return;
                }
            }
            wait(tairLock.expire(), key);
        }
    }

    /**
     * 防重异常
     *
     * @param key
     * @throws WmCustomerException
     */
    private void repeatCommitException(String key) throws WmCustomerException {
        logger.info("任务处理中，请不要重复提交. key:{}", key);
        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "任务处理中，请不要重复提交。");
    }

    /**
     * 执行完业务流程进行锁释放
     *
     * @param pjp
     * @param lockedKey
     * @return
     * @throws Throwable
     */
    private Object proceedAndUnlock(ProceedingJoinPoint pjp, String lockedKey) throws Throwable {
        try {
            Object obj = pjp.proceed();
            return obj;
        } finally {
            unlock(lockedKey);
        }
    }

    /**
     * 释放锁
     *
     * @param key
     */
    private void unlock(String key) {
        try {
            if (StringUtils.isBlank(key)) {
                return;
            }
            tairLocker.unLock(key);
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        logger.info("退出全局锁, key: {}", JSON.toString(key));
    }

    /**
     * 重试获取锁
     *
     * @param expire
     * @param key
     * @throws WmCustomerException
     */
    private void wait(int expire, String key) throws WmCustomerException {
        long waitTime = 0;
        boolean isSucc = false;
        while (waitTime < expire) {
            sleep(1000);
            waitTime += 1;
            logger.info("再次尝试获取全局锁, key: {}, 等待时间: {}/{} s", key, waitTime, expire);
            isSucc = tairLocker.tryLock(key, expire);
            if (isSucc) {
                break;
            }
        }
        if (!isSucc) {
            logger.error("重试获取锁{}次仍旧失败，请人工介入排查问题 key:{}", waitTime, key);
            repeatCommitException(key);
        }
    }

    /**
     * 休息指定时间
     *
     * @param millis
     */
    public static void sleep(final long millis) {
        try {
            Thread.sleep(millis);
        } catch (final InterruptedException ex) {
            logger.warn("tairLock sleep error ", ex);
            Thread.currentThread().interrupt();
        }
    }

}
