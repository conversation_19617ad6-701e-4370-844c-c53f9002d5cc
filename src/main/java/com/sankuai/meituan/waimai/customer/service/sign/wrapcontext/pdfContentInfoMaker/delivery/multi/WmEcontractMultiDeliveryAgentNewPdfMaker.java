package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.multi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-08-30 15:32
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.DELIVERY_MULTI_AGENT_NEW)
@Slf4j
@Service
public class WmEcontractMultiDeliveryAgentNewPdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker
        implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();

        EcontractDeliveryAgentNewInfoBo econtractDeliveryAgentNewInfoBo = null;
        Map<String, String> subMap = null;
        for (EcontractDeliveryInfoBo temp : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            econtractDeliveryAgentNewInfoBo = temp.getEcontractDeliveryAgentNewInfoBo();
            if (econtractDeliveryAgentNewInfoBo == null) {
                continue;
            }
            subMap = MapUtil.Object2Map(econtractDeliveryAgentNewInfoBo);
            pdfBizContent.add(subMap);
        }

        //pdf中的非业务动态字段
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("partA", StringUtils.defaultIfEmpty(originContext.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMetaContent.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("NEWAGENT_TEMPLATE_ID", 64));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("NEWAGENT_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        log.info("#WmEcontractMultiDeliveryAgentNewPdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }
}
