package com.sankuai.meituan.waimai.customer.service.sign.biz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskAreaDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBigContextManageService;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpRequest;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-07-22 21:21
 * Email: <EMAIL>
 * Desc:
 */
@Service
@Slf4j
public class WmEcontractBigTaskParseService {

    @Autowired
    private WmEcontractTaskDBMapper wmEcontractTaskDBMapper;
    @Autowired
    private WmEcontractTaskAreaService wmEcontractTaskAreaService;
    @Autowired
    private WmEcontractSignBigContextManageService wmEcontractSignBigContextManageService;

    public Long insert(WmEcontractSignTaskDB taskDB) {
        if (wmEcontractSignBigContextManageService.taskGarySwitch(taskDB)) {
            // 大文本上传
            String bigTaskContext = wmEcontractSignBigContextManageService.taskBigContextRemove(taskDB);
            wmEcontractTaskDBMapper.insert(taskDB);
            String objectName = parseTaskObjectName(taskDB);
            wmEcontractSignBigContextManageService.uploadObject(objectName, taskDB.getApplyContext());
            // 记录S3文本信息
            wmEcontractTaskDBMapper.updateBigApplyContextById(objectName, taskDB.getId());
            // 上传完成后将大文本回写
            wmEcontractSignBigContextManageService.taskBigContextFill(taskDB, bigTaskContext);
        } else if (isAreaSeperateSave(taskDB)) {
            // task数据过滤配送范围，同时组装task数据配送范围对象
            List<WmEcontractSignTaskAreaDB> signTaskAreaDBList = extractAreaDataList(taskDB);
            // 保存task数据
            wmEcontractTaskDBMapper.insert(taskDB);
            // task数据配送范围对象填充冷数据主键ID
            signTaskAreaDBList.stream().forEach(item -> item.setTaskId(Long.valueOf(taskDB.getId())));
            // 保存task数据配送范围对象列表
            wmEcontractTaskAreaService.batchInsert(taskDB.getId(), signTaskAreaDBList);
        } else {
            // 正常存储
            wmEcontractTaskDBMapper.insert(taskDB);
        }
        return taskDB.getId();
    }

    public WmEcontractSignTaskDB getById(Long id) {
        WmEcontractSignTaskDB taskInfo;
        if (readFromS3AndDB()) {
            taskInfo = wmEcontractTaskDBMapper.getById(id);
            fillTaskInfo(taskInfo);
        } else {
            taskInfo = wmEcontractTaskDBMapper.getById(id);
            fillTaskContextInfo(taskInfo);
            if (isAreaSeperateSave(taskInfo)) {
                assemblyAreaData(taskInfo);
            }
        }
        return taskInfo;
    }

    public WmEcontractSignTaskDB getByIdAndType(Long id, String type) {
        WmEcontractSignTaskDB taskInfo;
        if (readFromS3AndDB()) {
            taskInfo = wmEcontractTaskDBMapper.getByIdAndType(id, type);
            fillTaskInfo(taskInfo);
        } else {
            taskInfo = wmEcontractTaskDBMapper.getByIdAndType(id, type);
            fillTaskContextInfo(taskInfo);
            if (isAreaSeperateSave(taskInfo)) {
                assemblyAreaData(taskInfo);
            }
        }
        return taskInfo;
    }

    public WmEcontractSignTaskDB getByIdMaster(Long id) {
        WmEcontractSignTaskDB taskInfo;
        if (readFromS3AndDB()) {
            taskInfo = wmEcontractTaskDBMapper.getByIdMaster(id);
            fillTaskInfo(taskInfo);
        } else {
            taskInfo = wmEcontractTaskDBMapper.getByIdMaster(id);
            fillTaskContextInfo(taskInfo);
            if (isAreaSeperateSave(taskInfo)) {
                assemblyAreaData(taskInfo);
            }
        }
        return taskInfo;
    }

    public List<WmEcontractSignTaskDB> getTaskByCustomerAndTypeAndState(int customerId, String type, String state){
        List<WmEcontractSignTaskDB> signTaskDBList = wmEcontractTaskDBMapper.queryTaskByCustomerAndTypeAndState(customerId, type, state);
        fillTaskInfoList(signTaskDBList);
        return signTaskDBList;
    }

    public List<WmEcontractSignTaskDB> getByIdList(List<Long> idList) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getByIdList(idList);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getByIdList(idList);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getByIdListMaster(List<Long> idList) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getByIdListMaster(idList);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getByIdListMaster(idList);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getByCustomerIdAndState(Integer customerId, List<String> stateList) {
        return getByCustomerIdAndState(customerId,stateList,true);
    }

    public List<WmEcontractSignTaskDB> getByCustomerIdAndState(Integer customerId, List<String> stateList,boolean withAreaSeperateSave) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getByCustomerIdAndState(customerId, stateList);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getByCustomerIdAndState(customerId, stateList);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (withAreaSeperateSave && isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getByCustomerIdAndStateMaster(Integer customerId, List<String> stateList) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getByCustomerIdAndStateMaster(customerId, stateList);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getByCustomerIdAndStateMaster(customerId, stateList);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getWmTaskByCustomerIdAndStateMaster(Integer customerId, List<String> stateList) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getWmTaskByCustomerIdAndStateMaster(customerId, stateList);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getWmTaskByCustomerIdAndStateMaster(customerId, stateList);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        log.info("WmEcontractBigTaskParseService#getWmTaskByCustomerIdAndStateMaster, taskInfoList: {}", JSON.toJSONString(taskInfoList));
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getByCustomerIdAndType(Integer customerId, String type) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getByCustomerIdAndType(customerId, type);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getByCustomerIdAndType(customerId, type);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    /**
     * 根据客户ID和任务类型查询流程中的任务
     *
     * @param customerId 客户ID
     * @param type       任务类型
     * @return 任务
     */
    public List<WmEcontractSignTaskDB> getInProcessingTaskByCustomerIdAndType(Integer customerId, String type) {
        List<WmEcontractSignTaskDB> taskInfoList = wmEcontractTaskDBMapper.getInProcessingTaskByCustomerIdAndType(customerId, type);
        batchFillTaskContextInfo(taskInfoList);
        for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
            if (isAreaSeperateSave(taskInfo)) {
                assemblyAreaData(taskInfo);
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getByBatchId(Long batchId) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getByBatchId(batchId);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getByBatchId(batchId);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> queryWithParam(SignBatchQueryParam signBatchQueryParam) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.queryWithParam(signBatchQueryParam);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.queryWithParam(signBatchQueryParam);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getTaskByManualBatchId(Long manualBatchId) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getTaskByManualBatchId(manualBatchId);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getTaskByManualBatchId(manualBatchId);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getTaskByManualBatchIdFromMaster(Long manualBatchId) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getTaskByManualBatchIdFromMaster(manualBatchId);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getTaskByManualBatchIdFromMaster(manualBatchId);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> getValidTaskByManualBatchId(Long manualBatchId) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.getValidTaskByManualBatchId(manualBatchId);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.getValidTaskByManualBatchId(manualBatchId);
            batchFillTaskContextInfo(taskInfoList);
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> querySignTaskListByCustomerAndBatchId(List<WmEcontractSignTaskDB> wmEcontractSignTaskDBList) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.querySignTaskListByCustomerAndBatchId(wmEcontractSignTaskDBList);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.querySignTaskListByCustomerAndBatchId(wmEcontractSignTaskDBList);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> queryTaskItemByParamWithLabel(EcontractBatchOpRequest request, long lastId, int limit) {
        List<WmEcontractSignTaskDB> taskInfoList;
        if (readFromS3AndDB()) {
            taskInfoList = wmEcontractTaskDBMapper.queryTaskItemByParamWithLabel(request, lastId, limit);
            fillTaskInfoList(taskInfoList);
        } else {
            taskInfoList = wmEcontractTaskDBMapper.queryTaskItemByParamWithLabel(request, lastId, limit);
            batchFillTaskContextInfo(taskInfoList);
            for (WmEcontractSignTaskDB taskInfo : taskInfoList) {
                if (isAreaSeperateSave(taskInfo)) {
                    assemblyAreaData(taskInfo);
                }
            }
        }
        return taskInfoList;
    }

    public List<WmEcontractSignTaskDB> queryEntityListWithLabel4Encryption(long lastId, int size) {
        List<WmEcontractSignTaskDB> signTaskDBList = wmEcontractTaskDBMapper.queryEntityListWithLabel4Encryption(lastId, size);
        batchFillTaskContextInfo(signTaskDBList);
        for (WmEcontractSignTaskDB signTaskDB : signTaskDBList) {
            if (isAreaSeperateSave(signTaskDB)) {
                assemblyAreaData(signTaskDB);
            }
        }
        return signTaskDBList;
    }

    private void fillTaskContextInfo(WmEcontractSignTaskDB taskDB) {
        // 如果task记录上下文为空，则重新获取
        if (MccConfig.isRetryIfTaskContextIsEmpty() && StringUtils.isEmpty(taskDB.getApplyContext())) {
            int retryTimes = ConfigUtilAdapter.getInt("query_task_context_retry_times", 3);
            for (int times = 0; times < retryTimes; times++) {
                try {
                    Thread.sleep(50);
                } catch (Exception e) {
                    log.error("重试获取task上下文 延迟异常 taskId:{}", taskDB.getId(), e);
                }
                WmEcontractSignTaskDB taskDBTemp = wmEcontractTaskDBMapper.getById(taskDB.getId());
                if (StringUtils.isNotEmpty(taskDBTemp.getApplyContext())) {// 查询到数据则跳出for循环
                    taskDB.setApplyContext(taskDBTemp.getApplyContext());
                    break;
                }
            }
        }
    }

    private void batchFillTaskContextInfo(List<WmEcontractSignTaskDB> taskDBList) {
        if (!MccConfig.isRetryIfTaskContextIsEmpty() || CollectionUtils.isEmpty(taskDBList)) {
            return;
        }
        for (WmEcontractSignTaskDB taskDB : taskDBList) {
            if (StringUtils.isEmpty(taskDB.getApplyContext())) {
                int retryTimes = ConfigUtilAdapter.getInt("query_task_context_retry_times", 3);
                for (int times = 0; times < retryTimes; times++) {
                    try {
                        Thread.sleep(50);
                    } catch (Exception e) {
                        log.error("重试获取task上下文 延迟异常 taskId:{}", taskDB.getId(), e);
                    }
                    WmEcontractSignTaskDB taskDBTemp = wmEcontractTaskDBMapper.getById(taskDB.getId());
                    if (StringUtils.isNotEmpty(taskDBTemp.getApplyContext())) {// 查询到数据则跳出for循环
                        taskDB.setApplyContext(taskDBTemp.getApplyContext());
                        break;
                    }
                }
            }
        }
    }

    private void fillTaskInfo(WmEcontractSignTaskDB wmEcontractSignTaskDB) {
        if (null == wmEcontractSignTaskDB) {
            return;
        }
        // ApplyContext为空再从S3获取，不为空说明没有命中大文本逻辑
        if (StringUtils.isNotEmpty(wmEcontractSignTaskDB.getApplyContext())) {
            return;
        }
        byte[] contextBytes = wmEcontractSignBigContextManageService.getObject(wmEcontractSignTaskDB.getObjectName());
        String context = Base64.getEncoder().encodeToString(contextBytes);
        wmEcontractSignTaskDB.setApplyContext(context);
    }

    private void fillTaskInfoList(List<WmEcontractSignTaskDB> wmEcontractSignTaskDBList) {
        if (CollectionUtils.isEmpty(wmEcontractSignTaskDBList)) {
            return;
        }
        // todo:dxm，调研s3批量接口
        wmEcontractSignTaskDBList.stream().forEach(taskInfo -> fillTaskInfo(taskInfo));
    }

    private boolean readFromS3AndDB() {
        return ConfigUtilAdapter.getBoolean("econtract_bigcontext_read_switch", false);//默认关闭
    }

    private String parseTaskObjectName(WmEcontractSignTaskDB taskDB){
        StringBuilder sb = new StringBuilder();
        sb.append(taskDB.getApplyType());
        sb.append("_");
        sb.append(taskDB.getCustomerId());
        sb.append("_");
        sb.append(taskDB.getId());
        return sb.toString();
    }

    private boolean isAreaSeperateSave(WmEcontractSignTaskDB taskDB) {
        if (taskDB == null) {
            return false;
        }
        if (!EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(taskDB.getApplyType())) {
            return false;
        }
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskDB.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        return batchDeliveryInfoBo.getIsAreaSeperateSave() != null && batchDeliveryInfoBo.getIsAreaSeperateSave();
    }

    /**
     * 提取一个签约任务下的所有配送范围信息(门店维度拆分)
     * 同时将对应上下文数据的配送范围信息过滤
     * @param taskDB
     * @return
     */
    private List<WmEcontractSignTaskAreaDB> extractAreaDataList(WmEcontractSignTaskDB taskDB) {
        if (taskDB == null || StringUtils.isEmpty(taskDB.getApplyContext())) {
            return Lists.newArrayList();
        }
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskDB.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        if (batchDeliveryInfoBo == null || CollectionUtils.isEmpty(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList())) {
            return Lists.newArrayList();
        }

        List<WmEcontractSignTaskAreaDB> signTaskAreaDBList = Lists.newArrayList();
        for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            List<WmEcontractSignTaskAreaDB> subSignTaskAreaDBList = generateSignTaskArea(deliveryInfoBo);
            if (CollectionUtils.isNotEmpty(subSignTaskAreaDBList)) {
                signTaskAreaDBList.addAll(subSignTaskAreaDBList);
            }
        }
        // 重置task上下文
        taskDB.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        return signTaskAreaDBList;
    }

    /**
     * 生成签约任务的配送范围信息
     */
    private List<WmEcontractSignTaskAreaDB> generateSignTaskArea(EcontractDeliveryInfoBo deliveryInfoBo) {
        List<WmEcontractSignTaskAreaDB> areaDBList = Lists.newArrayList();
        CollectionUtils.addIgnoreNull(areaDBList, clearAndExtractAreaData(deliveryInfoBo, AreaOperateEnum.DELIVERY));
        CollectionUtils.addIgnoreNull(areaDBList, clearAndExtractAreaData(deliveryInfoBo, AreaOperateEnum.DELIVERY_WHOLE_CITY));
        CollectionUtils.addIgnoreNull(areaDBList, clearAndExtractAreaData(deliveryInfoBo, AreaOperateEnum.DELIVERY_AGGREGATION));
        CollectionUtils.addIgnoreNull(areaDBList, clearAndExtractAreaData(deliveryInfoBo, AreaOperateEnum.DELIVERY_LONG_DISTANCE));
        return areaDBList;
    }

    /**
     * 如果配送范围存在，则清空配送范围，并返回，加入到List中
     * 否则不处理
     */
    private WmEcontractSignTaskAreaDB clearAndExtractAreaData(EcontractDeliveryInfoBo deliveryInfoBo, AreaOperateEnum areaOperateEnum) {
        if (areaOperateEnum.getHasFunction().test(deliveryInfoBo) && areaOperateEnum.getHasAreaFunction().test(deliveryInfoBo)) {
            String area = areaOperateEnum.getGetFunction().apply(deliveryInfoBo);
            areaOperateEnum.getSetFunction().accept(deliveryInfoBo, StringUtils.EMPTY);
            return assemblyTaskAreaDB(Long.valueOf(deliveryInfoBo.getWmPoiId()), areaOperateEnum.pdfTypeEnum.getName(), area);
        } else {
            return null;
        }
    }
    
    private WmEcontractSignTaskAreaDB assemblyTaskAreaDB(long wmPoiId, String areaType, String AreaStr) {
        WmEcontractSignTaskAreaDB signTaskAreaDB = new WmEcontractSignTaskAreaDB();
        signTaskAreaDB.setWmPoiId(wmPoiId);
        signTaskAreaDB.setAreaType(areaType);
        signTaskAreaDB.setArea(AreaStr);
        return signTaskAreaDB;
    }

    /**
     * 拼装最终读取使用的数据
     */
    private void assemblyAreaData(WmEcontractSignTaskDB taskDB) {
        List<WmEcontractSignTaskAreaDB> signTaskAreaDBList = wmEcontractTaskAreaService.queryAreaDBListByTask(taskDB);
        if (CollectionUtils.isEmpty(signTaskAreaDBList)) {
            return;
        }
        Map<Long, List<WmEcontractSignTaskAreaDB>> signTaskAreaDBMap = signTaskAreaDBList.stream().collect(Collectors.groupingBy(WmEcontractSignTaskAreaDB::getWmPoiId));
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskDB.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            List<WmEcontractSignTaskAreaDB> subSignTaskAreaDBList = signTaskAreaDBMap.get(Long.valueOf(deliveryInfoBo.getWmPoiId()));
            if (CollectionUtils.isEmpty(subSignTaskAreaDBList)) {
                continue;
            }
            Map<String, WmEcontractSignTaskAreaDB> subSignTaskAreaDBMap = subSignTaskAreaDBList.stream()
                    .collect(Collectors.toMap(WmEcontractSignTaskAreaDB::getAreaType, Function.identity(),
                            (key1, key2) -> key2));
            // 常规配送类型
            assemblyDeliveryArea(deliveryInfoBo, subSignTaskAreaDBMap, AreaOperateEnum.DELIVERY);
            // 全城送配送类型
            assemblyDeliveryArea(deliveryInfoBo, subSignTaskAreaDBMap, AreaOperateEnum.DELIVERY_WHOLE_CITY);
            // 聚合配送类型
            assemblyDeliveryArea(deliveryInfoBo, subSignTaskAreaDBMap, AreaOperateEnum.DELIVERY_AGGREGATION);
            // 企客远距离配送类型
            assemblyDeliveryArea(deliveryInfoBo, subSignTaskAreaDBMap, AreaOperateEnum.DELIVERY_LONG_DISTANCE);
        }
        taskDB.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
    }

    /**
     * 若配送合同存在且对应的配送范围存在，则直接把范围写到EcontractDeliveryInfoBo对象里面
     */
    private void assemblyDeliveryArea(EcontractDeliveryInfoBo deliveryInfoBo, Map<String, WmEcontractSignTaskAreaDB>  signTaskAreaDBMap, AreaOperateEnum areaOperateEnum) {
        WmEcontractSignTaskAreaDB areaDB = signTaskAreaDBMap.get(areaOperateEnum.pdfTypeEnum.getName());
        if (areaOperateEnum.getHasFunction().test(deliveryInfoBo) && areaDB != null) {
            areaOperateEnum.getSetFunction().accept(deliveryInfoBo, areaDB.getArea());
        }
    }

    private static Predicate<EcontractDeliveryInfoBo> hasDeliveryFunction = Objects::nonNull;

    private static Predicate<EcontractDeliveryInfoBo> hasWholeCityDeliveryFunction = deliveryInfoBo -> deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo() != null;

    private static Predicate<EcontractDeliveryInfoBo> hasAggregationDeliveryFunction = deliveryInfoBo -> deliveryInfoBo.getEcontractDeliveryAggregationInfoBo() != null;

    private static Predicate<EcontractDeliveryInfoBo> hasLongDistanceDeliveryFunction = deliveryInfoBo -> deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo() != null;

    private static Predicate<EcontractDeliveryInfoBo> hasDeliveryAreaFunction = deliveryInfoBo -> StringUtils.isNotEmpty(deliveryInfoBo.getDeliveryArea());

    private static Predicate<EcontractDeliveryInfoBo> hasWholeCityDeliveryAreaFunction = deliveryInfoBo -> StringUtils.isNotEmpty(deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo().getDeliveryArea());

    private static Predicate<EcontractDeliveryInfoBo> hasAggregationDeliveryAreaFunction = deliveryInfoBo -> StringUtils.isNotEmpty(deliveryInfoBo.getEcontractDeliveryAggregationInfoBo().getDeliveryArea());

    private static Predicate<EcontractDeliveryInfoBo> hasLongDistanceDeliveryAreaFunction = deliveryInfoBo -> StringUtils.isNotEmpty(deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo().getDeliveryArea());

    private static BiConsumer<EcontractDeliveryInfoBo, String> setDeliveryAreaFunction = EcontractDeliveryInfoBo::setDeliveryArea;

    private static BiConsumer<EcontractDeliveryInfoBo, String> setWholeCityDeliveryAreaFunction = (deliveryInfoBo, area) -> deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo().setDeliveryArea(area);

    private static BiConsumer<EcontractDeliveryInfoBo, String> setAggregationDeliveryAreaFunction = (deliveryInfoBo, area) -> deliveryInfoBo.getEcontractDeliveryAggregationInfoBo().setDeliveryArea(area);

    private static BiConsumer<EcontractDeliveryInfoBo, String> setLongDistanceDeliveryAreaFunction = (deliveryInfoBo, area) -> deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo().setDeliveryArea(area);

    private static Function<EcontractDeliveryInfoBo, String> getDeliveryAreaFunction = EcontractDeliveryInfoBo::getDeliveryArea;

    private static Function<EcontractDeliveryInfoBo, String> getWholeCityDeliveryAreaFunction = deliveryInfoBo -> deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo().getDeliveryArea();

    private static Function<EcontractDeliveryInfoBo, String> getAggregationDeliveryAreaFunction = deliveryInfoBo -> deliveryInfoBo.getEcontractDeliveryAggregationInfoBo().getDeliveryArea();

    private static Function<EcontractDeliveryInfoBo, String> getLongDistanceDeliveryAreaFunction = deliveryInfoBo -> deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo().getDeliveryArea();


    public enum AreaOperateEnum {
        /*
         * 主配送方式对应的范围
         */
        DELIVERY(hasDeliveryFunction, hasDeliveryAreaFunction, setDeliveryAreaFunction, getDeliveryAreaFunction, EcontractPdfTypeEnum.DELIVERY),

        /*
         * 全城送对应的范围
         */
        DELIVERY_WHOLE_CITY(hasWholeCityDeliveryFunction, hasWholeCityDeliveryAreaFunction, setWholeCityDeliveryAreaFunction, getWholeCityDeliveryAreaFunction, EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY),

        /*
         * 聚合配对应的范围
         */
        DELIVERY_AGGREGATION(hasAggregationDeliveryFunction, hasAggregationDeliveryAreaFunction, setAggregationDeliveryAreaFunction, getAggregationDeliveryAreaFunction, EcontractPdfTypeEnum.DELIVERY_AGGREGATION),

        /*
         * 聚合配对应的范围
         */
        DELIVERY_LONG_DISTANCE(hasLongDistanceDeliveryFunction, hasLongDistanceDeliveryAreaFunction, setLongDistanceDeliveryAreaFunction, getLongDistanceDeliveryAreaFunction, EcontractPdfTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE);

        /*
         * 判断是否有配送合同的函数
         */
        private Predicate<EcontractDeliveryInfoBo> hasFunction;

        /*
         * 判断配送合同中是否有范围的函数
         */
        private Predicate<EcontractDeliveryInfoBo> hasAreaFunction;

        /*
         * 上下文中设置配送范围的函数
         */
        private BiConsumer<EcontractDeliveryInfoBo, String> setFunction;

        /*
         * 上线文中读取配送范围的函数
         */
        private Function<EcontractDeliveryInfoBo, String> getFunction;

        /*
         * 申请PDF的类型
         */
        private EcontractPdfTypeEnum pdfTypeEnum;

        AreaOperateEnum(Predicate<EcontractDeliveryInfoBo> hasFunction,
                        Predicate<EcontractDeliveryInfoBo> hasAreaFunction,
                        BiConsumer<EcontractDeliveryInfoBo, String> setFunction,
                        Function<EcontractDeliveryInfoBo, String> getFunction,
                        EcontractPdfTypeEnum pdfTypeEnum) {
            this.hasFunction = hasFunction;
            this.hasAreaFunction = hasAreaFunction;
            this.setFunction = setFunction;
            this.getFunction = getFunction;
            this.pdfTypeEnum = pdfTypeEnum;
        }

        public Predicate<EcontractDeliveryInfoBo> getHasFunction() {
            return hasFunction;
        }

        public void setHasFunction(Predicate<EcontractDeliveryInfoBo> hasFunction) {
            this.hasFunction = hasFunction;
        }

        public Predicate<EcontractDeliveryInfoBo> getHasAreaFunction() {
            return hasAreaFunction;
        }

        public void setHasAreaFunction(Predicate<EcontractDeliveryInfoBo> hasAreaFunction) {
            this.hasAreaFunction = hasAreaFunction;
        }

        public Function<EcontractDeliveryInfoBo, String> getGetFunction() {
            return getFunction;
        }

        public void setGetFunction(Function<EcontractDeliveryInfoBo, String> getFunction) {
            this.getFunction = getFunction;
        }

        public BiConsumer<EcontractDeliveryInfoBo, String> getSetFunction() {
            return setFunction;
        }

        public void setSetFunction(BiConsumer<EcontractDeliveryInfoBo, String> setFunction) {
            this.setFunction = setFunction;
        }

        public EcontractPdfTypeEnum getPdfTypeEnum() {
            return pdfTypeEnum;
        }

        public void setPdfTypeEnum(EcontractPdfTypeEnum pdfTypeEnum) {
            this.pdfTypeEnum = pdfTypeEnum;
        }
    }
}
