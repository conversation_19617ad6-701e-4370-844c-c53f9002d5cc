package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.StampConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignFlowConstant.*;

/**
 * 确定签约流程
 */
@Service
@Slf4j
public class WmEcontractDataSignTypeCollector implements IWmEcontractDataCollector {

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext,
            EcontractBatchBo targetContext) throws WmCustomerException {
        log.info("WmEcontractDataSignTypeCollector#collect, batchTypeEnum: {}", originContext.getBatchTypeEnum());
        if (EcontractBatchTypeEnum.isNationalSubsidyDeliveryType(originContext.getBatchTypeEnum())) {
            targetContext.setEcontractType(NATIONAL_SUBSIDY_PERFORMANCE_DELIVERY_FLOW_TEMPLATE);
            return;
        }
        List<SignTemplateEnum> pdfEnumList = middleContext.getPdfEnumList();

        Set<String> stampSet = Sets.newHashSet();
        List<String> stampList = null;
        for (SignTemplateEnum temp : pdfEnumList) {
            stampList = temp.getStampList();
            if (CollectionUtils.isNotEmpty(stampList)) {
                stampSet.addAll(stampList);
            }
        }
        stampList = Lists.newArrayList(stampSet);

        log.info("WmEcontractDataSignTypeCollector stampList:[{}]", JSON.toJSONString(stampList));
        String signType = null;
        if (stampList.size() == 0) {
            signType = NO_STAMP;
        } else if (stampList.size() == 1) {
            if (stampList.contains(StampConstant.POI_STAMP)) {
                signType = POI_STAMP;
            }
        } else if (stampList.size() == 2) {
            if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)) {
                signType = POI_MT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.QDB_STAMP)) {
                signType = POI_QDB_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)) {
                signType = POI_SH_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.MED_B2C_STAMP)) {
                signType = POI_B2C_STAMP;
            }
        } else if (stampList.size() == 3) {
            if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.QDB_STAMP)
                    && stampList.contains(StampConstant.SANKUAI_STAMP)) {
                signType = POI_QDB_MT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.QDB_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)) {
                signType = POI_QDB_SH_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.QDB_STAMP)
                    && stampList.contains(StampConstant.MED_B2C_STAMP)) {
                signType = POI_B2C_QDB_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)) {
                signType = POI_MT_SHANGHAISANKUAI_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.SHENZHENBAISHOU_STAMP)) {
                signType = POI_MT_SHENZHENBAISHOU_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.AGENT_STAMP)
                    && stampList.contains(StampConstant.MED_B2C_STAMP)) {
                signType = POI_B2C_AGENT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.AGENT_STAMP)) {
                signType = POI_MT_AGENT_STAMP;
            }
        } else if (stampList.size() == 4) {
            if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.QDB_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)) {
                signType = POI_QDB_MT_SHANGHAISANKUAI_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.QDB_STAMP)
                    && stampList.contains(StampConstant.SHENZHENBAISHOU_STAMP)) {
                signType = POI_QDB_MT_SHENZHENBAISHOU_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)
                    && stampList.contains(StampConstant.AGENT_STAMP)) {
                signType = POI_MT_SHANGHAISANKUAI_AGENT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.SHENZHENBAISHOU_STAMP)
                    && stampList.contains(StampConstant.AGENT_STAMP)) {
                signType = POI_MT_SHENZHENBAISHOU_AGENT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.SHENZHENBAISHOU_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)) {
                signType = POI_MT_SHANGHAISANKUAI_SHENZHENBAISHOU_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.MED_B2C_STAMP)
                    && stampList.contains(StampConstant.QDB_STAMP) && stampList.contains(StampConstant.AGENT_STAMP)) {
                signType = POI_B2C_AGENT_QDB_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.AGENT_STAMP)) {
                signType = POI_QDB_MT_AGENT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP)
                    && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)
                    && stampList.contains(StampConstant.HNLX_STAMP)){
                signType = POI_BJSK_SHSK_HNLX_STAMP;
            }
        } else if (stampList.size() == 5) {
            if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.QDB_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)
                    && stampList.contains(StampConstant.AGENT_STAMP)) {
                signType = POI_QDB_MT_SHANGHAISANKUAI_AGENT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.QDB_STAMP)
                    && stampList.contains(StampConstant.SHENZHENBAISHOU_STAMP)
                    && stampList.contains(StampConstant.AGENT_STAMP)) {
                signType = POI_QDB_MT_SHENZHENBAISHOU_AGENT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.QDB_STAMP)
                    && stampList.contains(StampConstant.SHENZHENBAISHOU_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)) {
                signType = POI_QDB_MT_SHANGHAISANKUAI_SHENZHENBAISHOU_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP) && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.AGENT_STAMP)
                    && stampList.contains(StampConstant.SHENZHENBAISHOU_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)) {
                signType = POI_MT_SHANGHAISANKUAI_SHENZHENBAISHOU_AGENT_STAMP;
            } else if (stampList.contains(StampConstant.POI_STAMP)
                    && stampList.contains(StampConstant.AGENT_STAMP)
                    && stampList.contains(StampConstant.SANKUAI_STAMP)
                    && stampList.contains(StampConstant.SHANGHAI_SANKUAI_STAMP)
                    && stampList.contains(StampConstant.HNLX_STAMP)){
                signType = POI_AGENT_BJSK_SHSK_HNLX_STAMP;
            }
        } else if (stampList.size() == 6) {
            signType = POI_QDB_MT_SHANGHAISANKUAI_SHENZHENBAISHOU_AGENT_STAMP;
        }

        if (StringUtils.isEmpty(signType)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "无法识别签约流程");
        }

        log.info("#WmEcontractDataSignTypeCollector,signType={}", JSONObject.toJSONString(signType));
        targetContext.setEcontractType(signType);
    }
}
