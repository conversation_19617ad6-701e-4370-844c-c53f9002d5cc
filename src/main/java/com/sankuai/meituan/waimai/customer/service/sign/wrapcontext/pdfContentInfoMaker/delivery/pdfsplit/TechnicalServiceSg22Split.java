package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.MccUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22)
public class TechnicalServiceSg22Split implements DeliveryPdfSplit {

    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));


        if (feeMode == LogisticsFeeModeEnum.SHANGOU_2_2
               && CollectionUtils.isNotEmpty(tabPdfMap.get(TAB_DELIVERY))
               && tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.TECHNICAL_SERVICE_SG_22)
                && MccUtil.isGrayTemplateEnum(SignTemplateEnum.TECHNICAL_SERVICE_SG_22)) {

            List<String> technicalServiceSg22List = pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName());
            if (CollectionUtils.isEmpty(technicalServiceSg22List)) {
                technicalServiceSg22List = Lists.newArrayList();
                technicalServiceSg22List.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                technicalServiceSg22List.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName(), technicalServiceSg22List);
            log.info("ADD TO TECHNICAL_SERVICE_SG_22，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}