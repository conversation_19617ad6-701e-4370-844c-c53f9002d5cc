package com.sankuai.meituan.waimai.customer.service.sign.callback.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerRelMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.common.MtriceService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignMsgService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsDataService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigTaskParseService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.resolver.WmEcontractResolverDispatcher;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
import com.sankuai.meituan.waimai.customer.util.trans.PdfUrlTransUtils;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchPdfInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCallbackBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerKPBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignVersionBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 处理成功Handler
 * 判断当前的stageName与executeName
 * 若包含executeName则表示是stage中的一个execute执行成功
 * 若没有executeName则表示stage都执行成功，则通知各个业务
 */
@Service
public class WmEcontractCallbackSuccessHandler implements WmEcontractCallbackHandler {

    private static Logger logger = LoggerFactory.getLogger(WmEcontractCallbackSuccessHandler.class);

    @Resource
    private WmPoiLogisticsClient wmPoiLogisticsClient;

    @Resource
    private WmContractService wmContractService;

    @Resource
    private WmEcontractResolverDispatcher wmEcontractResolverDispatcher;

    @Resource
    private WmSettleManagerService wmSettleManagerService;

    @Resource
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Resource
    private WmCustomerPoiService wmCustomerPoiService;

    @Resource
    private WmEcontractSmsDataService wmEcontractSmsDataService;

    @Resource
    private ShanGouRebateServiceClient shanGouRebateServiceClient;

    @Resource
    private WmEcontractSignMsgService wmEcontractSignMsgService;

    @Resource
    private WmPoiBaseClient wmPoiBaseClient;

    @Resource
    private WmPoiHealthClient wmPoiHealthClient;

    @Autowired
    private MtriceService mtriceService;

    @Resource
    private BmContractServiceAdapter bmContractServiceAdapter;

    @Resource
    private WmCustomerRelMapper wmCustomerRelMapper;

    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;

    @Resource
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;


    private static Map<String, EcontractTaskStateEnum> stageNameAndTaskStateMap = Maps.newHashMap();

    static {
        stageNameAndTaskStateMap.put(WmEcontractConstant.CREATE_PDF, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.CA_QDB, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.CA_POI, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.CA_MT, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.CA_AGENT, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.SMS_POI, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.REALNAME_AUTH_AGENT, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.STAMP_POI, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.STAMP_MT, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.STAMP_AGENT, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.STAMP_QDB, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.STAMP_B2C, EcontractTaskStateEnum.IN_PROCESSING);
        stageNameAndTaskStateMap.put(WmEcontractConstant.EFFECT, EcontractTaskStateEnum.SUCCESS);
    }

    private static final List<EcontractBatchTypeEnum> DAO_CAN_PUSH_MSG_TYPE_LIST = Lists.newArrayList(
            EcontractBatchTypeEnum.DAOCAN_SERVICE_C1_CONTRACT,
            EcontractBatchTypeEnum.DAOCAN_SERVICE_C2_CONTRACT
    );

    /**
     * 回调处理成功
     * @param batchContextDB
     * @param notifyBo
     * @return
     */
    @Override
    public Boolean handleCallback(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
        throws TException, WmCustomerException {
        return handleSuccess(batchContextDB, notifyBo);
    }

    private Boolean handleSuccess(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
        throws TException, WmCustomerException {
        if (StringUtils.isNotEmpty(notifyBo.getExecuteName())) {
            return handleExecuteTaskSuccess(batchContextDB, notifyBo);
        } else {
            return handleStageTaskSuccess(batchContextDB, notifyBo);
        }
    }

    private Boolean handleStageTaskSuccess(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
        throws TException, WmCustomerException {
        Set<String> set = Sets.newHashSet();
        for (Map.Entry<Long, EcontractTaskBo> entry : batchContextDB.getTaskIdAndTaskMap().entrySet()) {
            set.add(entry.getValue().getApplyType());
        }
        return callback(notifyBo, batchContextDB, Lists.newArrayList(set));
    }

    private Boolean handleExecuteTaskSuccess(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
        throws TException, WmCustomerException {
        return callback(notifyBo, batchContextDB, Lists.newArrayList(notifyBo.getExecuteName()));
    }

    private Boolean callback(EcontractNotifyBo notifyBo, EcontractBatchContextBo batchContextBo, List<String> executiveList) throws TException, WmCustomerException {
        logger.info("WmEcontractCallbackSuccessHandler#callback batchContextBo:{}", JSONObject.toJSONString(batchContextBo));
        String applyConext = Strings.EMPTY;
        String stageName = notifyBo.getStageName();
        String pdfUrl = notifyBo.getDownLoadUrl();
        String extMsg = notifyBo.getExtMsg();
        for (Map.Entry<Long, EcontractTaskBo> entry : batchContextBo.getTaskIdAndTaskMap().entrySet()) {
            if (executiveList.contains(entry.getValue().getApplyType())) {
                try {
                    callbackSuccess(entry.getKey(), entry.getValue(), stageName, pdfUrl, batchContextBo.getEcontractSignVersionBo(), batchContextBo.getKpBo());
                    if (batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.C1_CONTRACT)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.C2_CONTRACT)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.QUA_REAL_LETTER)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.GROUP_MEAL)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.POI_PROMOTION_SERVICE)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.MED_DEPOSIT)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BAG_SERVICE)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.FOODCITY_STATEMENT)
                        || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.MEDIC_ORDER_SPLIT)) {
                        applyConext = entry.getValue().getApplyContext();
                    }
                } catch (Exception e) {
                    logger.error("处理成功回调失败, taskKey = " + entry.getKey(), e);
                }
            }
        }

        if (batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.C1_CONTRACT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.C2_CONTRACT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.SETTLE)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.DELIVERY)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_CSD)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_CS)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_CD)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_CDH)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_SD)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_DELIVERY)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.ADDEDSERVICE_DISCOUNT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.QUA_REAL_LETTER)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.GROUP_MEAL)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.POI_PROMOTION_SERVICE)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.MED_DEPOSIT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BAG_SERVICE)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.FOODCITY_STATEMENT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.MEDIC_ORDER_SPLIT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_PURCHASE)
                || EcontractBatchTypeEnum.isNationalSubsidyDeliveryType(batchContextBo.getBatchTypeEnum())
        ) {
            wmEcontractSmsDataService.pushMessage(batchContextBo, notifyBo, applyConext);
        }

        if (DAO_CAN_PUSH_MSG_TYPE_LIST.contains(batchContextBo.getBatchTypeEnum())) {
            wmEcontractSmsDataService.sendDaoCanPushMsg(batchContextBo, notifyBo, applyConext);
        }

        wmEcontractSignMsgService.handleContractMsg(batchContextBo, stageName);
        return Boolean.TRUE;
    }

    /**
     * 回调处理成功
     * @param taskId 任务id
     * @param taskBo 任务详情
     * @param stageName 执行步骤名称(对应电子合同平台)
     * @param econtractSignVersionBo
     */
    public Boolean callbackSuccess(Long taskId, EcontractTaskBo taskBo, String stageName, String pdfUrl,
            EcontractSignVersionBo econtractSignVersionBo, EcontractCustomerKPBo kpBo) throws TException, WmCustomerException {

        String executeName = taskBo.getApplyType();
        if (!wmEcontractResolverDispatcher.isCallback(executeName, stageName)) {
            return Boolean.TRUE;
        }

        if(StringUtils.isNotEmpty(pdfUrl)){
            pdfUrl = parsePdfUrl(pdfUrl, executeName);
        }

        //构建callback对象
        EcontractCallbackBo callbackBo = new EcontractCallbackBo.Builder()
            .taskId(taskId)
            .state(analysisState(stageName))
            .stageName(stageName)
            .pdfUrl(pdfUrl)
            .applyContractType(executeName)
            .build();
        logger.info("callbackSuccess taskId = {}, executeName = {}, stageName = {},pdfUrl = {}", taskId, executeName, stageName, pdfUrl);
        mtriceService.metricContractEffect(executeName);
        switch (EcontractTaskApplyTypeEnum.getByName(executeName)) {
            case POIFEE:
            case BATCHPOIFEE:
            case DRONE_DELIVERY:
            case FRUIT_TOGETHER_DELIVERY:
            case VIP_CARD_CONTRACT_AGREEMENT:
            case PHF_DELIVERY:
            case NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY:
            case NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY:
                return wmPoiLogisticsClient.confirm(callbackBo, econtractSignVersionBo);
            case AGENT_SQS_STANDARD:
                wmLogisticsContractThriftServiceAdapter.confirmSignByChannel(callbackBo, taskBo);
                return Boolean.TRUE;
            case SETTLE:
                wmSettleManagerService.wmSettleConfirmFlowCallback(callbackBo,kpBo);
                return Boolean.TRUE;
            case C1CONTRACT:
            case C2CONTRACT:
            case QUA_REAL_LETTER:
            case POI_PROMOTION_SERVICE:
            case DELIVERY_SERVICE_CONTRACT:
            case DELIVERY_SITE_CONTRACT:
            case AD_ANNUAL_FRAMEWORK_CONTRACT:
            case BRAND_AD_CONTRACT:
            case AD_ORDER:
            case PHF_CHARGE:
            case BUSINESS_CUSTOMER_E_CONTRACT:
            case GROUP_MEAL:
            case BAG_SERVICE:
            case FOODCITY_STATEMENT:
            case MEDIC_ORDER_SPLIT:
            case INTERIM_SELF:
            case SUBJECT_CHANGE_SUPPLEMENT:
            case FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT:
            case SPEEDY_DELIVERY_COOPERATION_AGREEMENT:
            case COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT:
            case DAOCAN_SERVICE_C1_CONTRACT:
            case DAOCAN_SERVICE_C2_CONTRACT:
            case NATIONAL_SUBSIDY_PURCHASE:
                return wmContractService.econtractCallback(callbackBo);
            case CUSTOMER:
                wmCustomerPoiService.customerPoiUnbindCallBack(callbackBo);
                return Boolean.TRUE;
            case KP:
            case AGENT_SIGNER_AUTH:
                wmCustomerKpAuditService.signerKpAuthCallback(taskId.intValue(), EcontractTaskStateEnum.SUCCESS);
                return Boolean.TRUE;
            case OPERATION_MANAGER_KP_CONFIRM:
                wmCustomerKpAuditService.operationManagerKpAuthCallback(callbackBo);
                return Boolean.TRUE;
            case BATCH_POI_GENERATE_PDF:
            case BATCH_POI_BASEINFO_GENERATE_PDF:
                return wmCustomerPoiService.customerPoiPdfCallBack(callbackBo);
            case ADDEDSERVICEDISCOUNT:
                return wmPoiLogisticsClient.handleCallBackForVasFeeActivity(callbackBo,taskBo);
            case SHANGOU_REBATE:
                return shanGouRebateServiceClient.handleCallBackForShanGouRebate(callbackBo);
            case WM_POI_BASE_TAG_SIGN:
                wmPoiBaseClient.handleCallBack(callbackBo, kpBo, null);
                return Boolean.TRUE;
            case FOODCITY_POI_TABLE:
                wmCustomerPoiService.customerPoiPreBindCallBack(callbackBo);
                return Boolean.TRUE;
            case MED_DEPOSIT:
                return wmPoiHealthClient.handleCallBack(callbackBo);
            default:
                //其他类型不处理
        }
        return Boolean.FALSE;
    }

    /**
     * 处理普通模块
     * @param pdfUrl
     * @param executeName
     * @return
     */
    private String parsePdfUrlCommon(String pdfUrl, String executeName) {
        // 多模块json形式
        if (PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)) {
            JSONObject jo = JSON.parseObject(pdfUrl);
            pdfUrl = jo.getString(executeName);
        }
        return pdfUrl;
    }

    /**
     * 处理无人机模块
     *
     * @param pdfUrl
     * @param executeName
     * @return
     */
    private String parsePdfUrlDrone(String pdfUrl, String executeName) {
        if (!PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)) {
            return null;
        }
        if (!EcontractTaskApplyTypeEnum.DRONE_DELIVERY.getName().equals(executeName)) {
            return null;
        }
        BatchPdfInfo batchPdfInfo = new BatchPdfInfo();
        // 多模块json形式
        BatchPdfInfo batchPdfInfoTemp = JSONObject.parseObject(pdfUrl, BatchPdfInfo.class);
        if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDrone())) {
            batchPdfInfo.setDrone(batchPdfInfoTemp.getDrone());
        }
        if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDrone_performance_service_fee())) {
            batchPdfInfo.setDrone_performance_service_fee(batchPdfInfoTemp.getDrone_performance_service_fee());
        }
        pdfUrl = JSONObject.toJSONString(batchPdfInfo);
        return pdfUrl;
    }

    /**
     * 解析pdf路径
     *
     * @param pdfUrl
     * @param executeName
     * @return
     */
    protected String parsePdfUrl(String pdfUrl, String executeName) {
        String finalPdfUrl = pdfUrl;
        String pdfForDelivery = parsePdfUrlForDelivery(pdfUrl, executeName);
        String pdfForSettle = parsePdfUrlForSettle(pdfUrl, executeName);
        String pdfForAgentSqsStandard = parsePdfUrlForAgentSqsStandard(pdfUrl, executeName);
        String pdfDroneDelivery = parsePdfUrlDrone(pdfUrl, executeName);
        String pdfFruitTogetherDelivery = parsePdfUrlFruitTogether(pdfUrl, executeName);
        String vipCardPdfUrl = parseVipCardPdfUrl(pdfUrl, executeName);
        String nationalSubsidyPdfUrl = parseNationalSubsidyPdfUrl(pdfUrl, executeName);
        String pdfForCommon = parsePdfUrlCommon(pdfUrl, executeName);
        if (StringUtils.isNotEmpty(pdfForDelivery)) {
            finalPdfUrl = pdfForDelivery;
        } else if (StringUtils.isNotEmpty(pdfForSettle)) {
            finalPdfUrl = pdfForSettle;
        } else if (StringUtils.isNotEmpty(pdfForAgentSqsStandard)) {
            finalPdfUrl = pdfForAgentSqsStandard;
        } else if (StringUtils.isNotEmpty(pdfDroneDelivery)) {
            finalPdfUrl = pdfDroneDelivery;
        } else if (StringUtils.isNotEmpty(pdfFruitTogetherDelivery)) {
            finalPdfUrl = pdfFruitTogetherDelivery;
        } else if (StringUtils.isNotEmpty(vipCardPdfUrl)) {
            finalPdfUrl = vipCardPdfUrl;
        } else if (StringUtils.isNotEmpty(nationalSubsidyPdfUrl)) {
            finalPdfUrl = nationalSubsidyPdfUrl;
        } else {
            finalPdfUrl = pdfForCommon;
        }
        return finalPdfUrl;
    }

    private String parseNationalSubsidyPdfUrl(String pdfUrl, String executeName) {
        if (!EcontractTaskApplyTypeEnum.isNationalSubsidyDeliveryTaskType(EcontractTaskApplyTypeEnum.getByName(executeName))) {
            return null;
        }
        BatchPdfInfo batchPdfInfo = new BatchPdfInfo();
        // 多模块json形式
        if (PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)) {
            return pdfUrl;
        } else {
            batchPdfInfo.setNational_subsidy_delivery_performance_service(pdfUrl);
            return JSONObject.toJSONString(batchPdfInfo);
        }
    }

    /**
     * 处理会员卡模块
     */
    private String parseVipCardPdfUrl(String pdfUrl, String executeName) {
        if (!EcontractTaskApplyTypeEnum.VIP_CARD_CONTRACT_AGREEMENT.getName().equals(executeName)) {
            return null;
        }
        if (PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)) {
            logger.warn("WmEcontractCallbackSuccessHandler#parseVipCardPdfUrl, 会员卡费率不应该是多模块json形式");
            return null;
        }
        BatchPdfInfo batchPdfInfo = new BatchPdfInfo();
        batchPdfInfo.setVip_card_contract_agreement(pdfUrl);
        return JSONObject.toJSONString(batchPdfInfo);
    }

    /**
     * 处理班次送模块
     *
     * @param pdfUrl
     * @param executeName
     * @return
     */
    private String parsePdfUrlFruitTogether(String pdfUrl, String executeName) {
        if (!PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)) {
            return null;
        }
        if (!EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY.getName().equals(executeName)) {
            return null;
        }
        BatchPdfInfo batchPdfInfo = new BatchPdfInfo();
        // 多模块json形式
        BatchPdfInfo batchPdfInfoTemp = JSONObject.parseObject(pdfUrl, BatchPdfInfo.class);
        if (StringUtils.isNotEmpty(batchPdfInfoTemp.getFruit_together())) {
            batchPdfInfo.setFruit_together(batchPdfInfoTemp.getFruit_together());
        }
        if (StringUtils.isNotEmpty(batchPdfInfoTemp.getFruit_together_performance_service_fee())) {
            batchPdfInfo.setFruit_together_performance_service_fee(batchPdfInfoTemp.getFruit_together_performance_service_fee());
        }
        pdfUrl = JSONObject.toJSONString(batchPdfInfo);
        return pdfUrl;
    }

    /**
     * 解析结算模块pdf
     * @param pdfUrl
     * @param executeName
     * @return 非结算模块返回null
     */
    private String parsePdfUrlForSettle(String pdfUrl,String executeName) {
        if (EcontractTaskApplyTypeEnum.SETTLE.getName().equals(executeName)){
            BatchPdfInfo batchPdfInfo = new BatchPdfInfo();
            //多模块json形式
            if(PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)){
                BatchPdfInfo batchPdfInfoTemp = JSONObject.parseObject(pdfUrl,BatchPdfInfo.class);
                if(StringUtils.isNotEmpty(batchPdfInfoTemp.getSettle())){
                    batchPdfInfo.setSettle(batchPdfInfoTemp.getSettle());
                }
                if(StringUtils.isNotEmpty(batchPdfInfoTemp.getSettle_businessloans())){
                    batchPdfInfo.setSettle_businessloans(batchPdfInfoTemp.getSettle_businessloans());
                }
            }else{
                batchPdfInfo.setSettle(pdfUrl);
            }
            pdfUrl = JSONObject.toJSONString(batchPdfInfo);
            return pdfUrl;
        }else{
            return null;
        }
    }

    /**
     * 解析配送模块pdf
     * @param pdfUrl
     * @param executeName
     * @return 非配送模块返回null
     */
    private String parsePdfUrlForDelivery(String pdfUrl,String executeName) {
        if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(executeName)
                || EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(executeName)) {
            BatchPdfInfo batchPdfInfo = new BatchPdfInfo();
            // 多模块json形式
            if (PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)) {
                BatchPdfInfo batchPdfInfoTemp = JSONObject.parseObject(pdfUrl, BatchPdfInfo.class);
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery())) {
                    batchPdfInfo.setDelivery(batchPdfInfoTemp.getDelivery());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_preferential_policy())) {
                    batchPdfInfo.setDelivery_preferential_policy(batchPdfInfoTemp.getDelivery_preferential_policy());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_preferential_application())) {
                    batchPdfInfo.setDelivery_preferential_application(
                            batchPdfInfoTemp.getDelivery_preferential_application());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_performance_service())) {
                    batchPdfInfo.setDelivery_performance_service(batchPdfInfoTemp.getDelivery_performance_service());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_whole_city())) {
                    batchPdfInfo.setDelivery_whole_city(batchPdfInfoTemp.getDelivery_whole_city());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_aggregation())) {
                    batchPdfInfo.setDelivery_aggregation(batchPdfInfoTemp.getDelivery_aggregation());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_company_customer_long_distance())) {
                    batchPdfInfo.setDelivery_company_customer_long_distance(
                            batchPdfInfoTemp.getDelivery_company_customer_long_distance());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_agent_new())) {
                    batchPdfInfo.setDelivery_agent_new(batchPdfInfoTemp.getDelivery_agent_new());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_per_discount())) {
                    batchPdfInfo.setDelivery_per_discount(batchPdfInfoTemp.getDelivery_per_discount());
                }
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getDelivery_cold_chain())) {
                    batchPdfInfo.setDelivery_cold_chain(batchPdfInfoTemp.getDelivery_cold_chain());
                }
            }else{
                batchPdfInfo.setDelivery(pdfUrl);
            }
            pdfUrl = JSONObject.toJSONString(batchPdfInfo);
            return pdfUrl;
        } else {
            return null;
        }
    }

    private String parsePdfUrlForAgentSqsStandard(String pdfUrl,String executeName) {
        if (EcontractTaskApplyTypeEnum.AGENT_SQS_STANDARD.getName().equals(executeName)) {
            BatchPdfInfo batchPdfInfo = new BatchPdfInfo();
            // 多模块json形式
            if (PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)) {
                BatchPdfInfo batchPdfInfoTemp = JSONObject.parseObject(pdfUrl, BatchPdfInfo.class);
                if (StringUtils.isNotEmpty(batchPdfInfoTemp.getAgent_sqs_standard())) {
                    batchPdfInfo.setAgent_sqs_standard(batchPdfInfoTemp.getAgent_sqs_standard());
                }
            } else {
                batchPdfInfo.setAgent_sqs_standard(pdfUrl);
            }
            pdfUrl = JSONObject.toJSONString(batchPdfInfo);
            return pdfUrl;
        } else {
            return null;
        }
    }

    private EcontractTaskStateEnum analysisState(String stageName) {
        EcontractTaskStateEnum stateEnum = stageNameAndTaskStateMap.get(stageName);
        return stateEnum == null?EcontractTaskStateEnum.IN_PROCESSING:stateEnum;
    }
}
