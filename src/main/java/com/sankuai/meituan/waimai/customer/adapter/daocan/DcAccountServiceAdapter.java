package com.sankuai.meituan.waimai.customer.adapter.daocan;

import com.sankuai.nibmp.infra.amp.attribute.lib.IAccountService;
import com.sankuai.nibmp.infra.amp.attribute.lib.dto.AccountInfoDTO;
import com.sankuai.nibmp.infra.amp.attribute.lib.dto.PageDTO;
import com.sankuai.nibmp.infra.amp.attribute.lib.enums.AccountStatusSearchEnum;
import com.sankuai.nibmp.infra.amp.attribute.lib.param.CustomerIdPageParam;
import com.sankuai.nibmp.infra.amp.attribute.lib.param.PageParam;
import com.sankuai.nibmp.infra.amp.attribute.lib.result.AccountInfoListPageResult;
import com.sankuai.nibmp.infra.amp.common.contant.BizLineEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class DcAccountServiceAdapter {

    @Resource
    private IAccountService accountService;

    public List<AccountInfoDTO> getAccountByCustomerId(Long mtCustomerId) {
        try {
            log.info("DcAccountServiceAdapter#getAccountByCustomerId, mtCustomerId: {}", mtCustomerId);
            List<AccountInfoDTO> accountInfoDTOList = new ArrayList<>();
            int currentPage = 1;
            int totalPage = 0;
            do {
                AccountInfoListPageResult accountInfoResult = accountService.getAccountByCustomerId(buildCustomerIdPageParam(mtCustomerId, currentPage++));
                PageDTO pageDTO = accountInfoResult.getPageDTO();
                totalPage = pageDTO.getTotalPage();
                accountInfoDTOList.addAll(accountInfoResult.getAccountInfoList());
            } while (currentPage <= totalPage);

            log.info("DcAccountServiceAdapter#getAccountByCustomerId, accountInfoDTO size: {}", accountInfoDTOList.size());
            return accountInfoDTOList;
        } catch (Exception e) {
            log.error("DcAccountServiceAdapter#getAccountByCustomerId, error", e);
            return Collections.emptyList();
        }
    }

    private CustomerIdPageParam buildCustomerIdPageParam(Long customerId, int currentPage) {
        CustomerIdPageParam customerIdPageParam = new CustomerIdPageParam();
        customerIdPageParam.setCustomerId(customerId);
        customerIdPageParam.setBizLine(BizLineEnum.FOOD.getCode());
        PageParam pageParam = PageParam.buildDefault();
        pageParam.setPageNum(currentPage);
        customerIdPageParam.setPageParam(pageParam);
        customerIdPageParam.setStatusSearchEnum(AccountStatusSearchEnum.ENABLE);
        return customerIdPageParam;
    }
}
