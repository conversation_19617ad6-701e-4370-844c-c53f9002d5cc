package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 确定签约大模块
 */
@Slf4j
@Service
public class WmEcontractDataFlowCollector implements IWmEcontractDataCollector {

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext) {
        log.info("#WmEcontractDataFlowCollector");
        //模板->签章->流程
        List<SignTemplateEnum> pdfEnumList = middleContext.getPdfEnumList();
        Multimap<String, SignTemplateEnum> multimap = ArrayListMultimap.create();
        for (SignTemplateEnum temp : pdfEnumList) {
            multimap.put(temp.getTab(),temp);
        }
        List<String> flowList = Lists.newArrayList(multimap.keySet());
        middleContext.setTabPdfMap(multimap.asMap());
        log.info("WmEcontractDataFlowCollector#collect, flowList: {}", JSONObject.toJSONString(flowList));
        targetContext.setFlowList(flowList);
    }

}
