package com.sankuai.meituan.waimai.customer.service.sign.compare;

import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;


/**
 * @description: StageBatchInfoBo比较器
 * @author: zhangyuanhao02
 * @create: 2025/1/5 18:37
 */
public interface StageBatchInfoBoComparator {
    String compare(StageBatchInfoBo source, StageBatchInfoBo target);

    default String concatMsg(Object source, Object target) {
        StringBuilder msg = new StringBuilder("对比结果: [ ");
        msg.append("当前链路数据: ").append(source == null ? "null" : source.toString())
                .append(", 旧链路数据: ").append(target == null ? "null" : target.toString())
                .append(" ] \n");

        return msg.toString();
    }
}
