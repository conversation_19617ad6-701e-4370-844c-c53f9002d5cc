package com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList;

import com.sankuai.meituan.waimai.customer.constant.customer.WmPoiRelTableDbusEnum;

import java.util.Map;

public interface IPoiRelTableEvent {

    WmPoiRelTableDbusEnum getTable();

    String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson);

    String handleInsert(Map<String, String> metaJsonData, String dataMapJson);

    String handleDelete(Map<String, String> metaJsonData, String dataMapJson);
}

