package com.sankuai.meituan.waimai.customer.contract.domain;


import com.sankuai.meituan.waimai.customer.statemachine.api.FlowTrait;

public class WmTempletContractDB extends FlowTrait {
    private Long id;

    /**
     * 关联的合同id
     */
    private Long parentId;

    /**
     * 合同类型
     */
    private Integer type;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 合同编号
     */
    private String number;

    /**
     * 生效日期
     */
    private Integer effectiveDate;

    /**
     * 到期日
     */
    private Integer dueDate;

    private Integer ctime;

    private Integer utime;

    private Integer opuid;

    private Integer version;

    /**
     * 其他条款
     */
    private String otherItem;

    private Byte valid;

    /**
     * 预计生效日期
     */
    private Integer expectEffectiveDate;

    /**
     * 履约服务费主体
     */
    private String logisticsSubject;

    /**
     * 业务属性- JSON格式，目前主要存放wmPoiId
     */
    private String bizData;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number == null ? null : number.trim();
    }

    public Integer getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Integer effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Integer getDueDate() {
        return dueDate;
    }

    public void setDueDate(Integer dueDate) {
        this.dueDate = dueDate;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public Integer getOpuid() {
        return opuid;
    }

    public void setOpuid(Integer opuid) {
        this.opuid = opuid;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getOtherItem() {
        return otherItem;
    }

    public void setOtherItem(String otherItem) {
        this.otherItem = otherItem == null ? null : otherItem.trim();
    }

    public Byte getValid() {
        return valid;
    }

    public void setValid(Byte valid) {
        this.valid = valid;
    }

    public Integer getExpectEffectiveDate() {
        return expectEffectiveDate;
    }

    public void setExpectEffectiveDate(Integer expectEffectiveDate) {
        this.expectEffectiveDate = expectEffectiveDate;
    }

    @Override
    public String getFlowId() {
        return getId() == null ? "" : getId().toString();
    }

    @Override
    public String currentState() {
        return getStatus() == null ? "" : getStatus().toString();
    }

    @Override
    protected void setToState(String toState) {
        this.status = new Integer(toState);
    }

    public String getLogisticsSubject() {
        return logisticsSubject;
    }

    public void setLogisticsSubject(String logisticsSubject) {
        this.logisticsSubject = logisticsSubject;
    }

    public String getBizData() {
        return bizData;
    }

    public void setBizData(String bizData) {
        this.bizData = bizData;
    }
}