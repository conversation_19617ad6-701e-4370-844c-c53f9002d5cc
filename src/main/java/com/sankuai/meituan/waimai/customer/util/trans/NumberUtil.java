package com.sankuai.meituan.waimai.customer.util.trans;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

/**
 * Created by IntelliJ IDEA.
 * User: zy
 * Date: 14-4-16
 * Time: 下午1:34
 * To change this template use File | Settings | File Templates.
 */
public class NumberUtil {
    private static final Logger log = LoggerFactory.getLogger(NumberUtil.class);

    public static double formatPrice(Double str, Integer scaleNum) {
        try {
            BigDecimal b = new BigDecimal(str);
            return b.setScale(scaleNum, BigDecimal.ROUND_HALF_UP).doubleValue();
        } catch (Exception e) {
            log.warn("formatPrice error input = " + str, e);
            return 0;
        }
    }

    public static Byte formatByte(String str, Byte defaultValue) {
        if (str == null || "".equals(str) || "null".equals(str)) {
            return defaultValue;
        }
        try {
            Byte result = Byte.parseByte(str.trim());
            return result;
        } catch (Exception e) {
            log.warn("formatByte error input = " + str, e);
            return defaultValue;
        }
    }

    public static Short formatShort(String str, Short defaultValue) {
        if (str == null || "".equals(str) || "null".equals(str)) {
            return defaultValue;
        }
        try {
            Short result = Short.parseShort(str.trim());
            return result;
        } catch (Exception e) {
            log.warn("formatShort error input = " + str, e);
            return defaultValue;
        }
    }

    public static String minus(String str, String str2) {
        BigDecimal b1 = new BigDecimal(str);
        BigDecimal b2 = new BigDecimal(str2);
        BigDecimal res = b1.subtract(b2);
        res = res.setScale(2, BigDecimal.ROUND_HALF_UP);
        return res.toString();

    }

    public static BigDecimal formatBigDecimal(String str) {
        try {
            BigDecimal b1 = new BigDecimal(str);
            return b1;
        } catch (Exception e) {
            log.warn("formatBigDecimal error input = " + str, e);
            return null;
        }
    }

    public static long formatLong(String str, long defaultValue) {
        if (str == null || "".equals(str) || "null".equals(str))
            return defaultValue;
        try {
            Long result = Long.parseLong(str.trim());
            return result;
        } catch (Exception e) {
            log.warn("formatLong error input = " + str, e);
            return defaultValue;
        }
    }

    public static Integer formatInteger(BigDecimal bg) {
        bg = bg.setScale(2, BigDecimal.ROUND_HALF_UP);
        bg = bg.movePointRight(2);
        Integer val = bg.intValue();
        return val;
    }

    public static String ybToRMB(Integer yb) {
        if (yb == null || yb <= 0) {
            return "0";
        }
        BigDecimal bd = new BigDecimal(yb.toString());
        bd = bd.movePointLeft(2);
        return bd.toString();
    }

    public static Integer formatNumber(String str, Integer defaultValue) {
        if (str == null || "".equals(str) || "null".equals(str))
            return defaultValue;
        try {
            BigDecimal b1 = new BigDecimal(str);
            return b1.intValue();
        } catch (Exception e) {
            log.warn("formatNumber error input = " + str, e);
            return defaultValue;
        }
    }

    public static double formatDouble(String str, Double defaultValue) {
        if (str == null || "".equals(str) || "null".equals(str))
            return defaultValue;
        try {
            Double result = Double.parseDouble(str.trim());
            return result;
        } catch (Exception e) {
            log.warn("formatDouble error input = " + str, e);
            return defaultValue;
        }
    }

    public static float formatFolat(String str, Float defaultValue) {
        if (str == null || "".equals(str) || "null".equals(str))
            return defaultValue;
        try {
            Float result = Float.parseFloat(str.trim());
            return result;
        } catch (Exception e) {
            log.warn("formatFolat error input = " + str, e);
            return defaultValue;
        }
    }

    public static int numberLength(Long number) {
        int count = 0;
        while (number >= 1) {
            number /= 10;
            count++;
        }
        return count;
    }

    public static void main(String[] a) {

//        System.out.println(formatPrice(re));
        System.out.println(formatNumber(null, 0));


    }


}
