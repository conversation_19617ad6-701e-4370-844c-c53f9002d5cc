package com.sankuai.meituan.waimai.customer.service.kp.operatorv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.*;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-01 17:56
 * Email: <EMAIL>
 * Desc:
 */
public abstract class WmCustomerKpOperate {

    @Autowired
    public KpBlackListVerify kpBlackListVerify;

    @Autowired
    public KpDataVerify kpDataVerify;

    @Autowired
    public KpTypeNumVerify kpTypeNumVerify;

    @Autowired
    public SignerCertTypeVerify signerCertTypeVerify;

    @Autowired
    public SignerKpOperateVerify signerKpOperateVerify;

    @Autowired
    public OpmanagerRelPoiVerify opmanagerRelPoiVerify;

    @Autowired
    public OpmanagerEffectiveUpdateVerify opmanagerEffectiveUpdateVerify;

    @Autowired
    public KpRealNameVerify kpRealNameVerify;

    @Autowired
    public OpmanagerStateVerify opmanagerStateVerify;

    @Autowired
    public VisitkpDBOperator visitkpDBOperator;

    @Autowired
    public SignerDBOperator signerDBOperator;

    @Autowired
    public OtherDBOperator otherDBOperator;

    @Autowired
    public OpmanagerDBOperator opmanagerDBOperator;

    @Autowired
    public LegalDBOperator legalDBOperator;

    @Autowired
    public SignerAuditStatusVerify signerAuditStatusVerify;

    @Autowired
    public SignerTypeChangeVerify signerTypeChangeVerify;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    public KpWdcClueIdVerify kpWdcClueIdVerify;

    @Autowired
    public AgentAuthPreVerify agentAuthPreVerify;

    public static List<String> fieldList;

    static {
        fieldList = Arrays.asList("id", "customerId", "state", "signerType", "compellation", "certType", "certNumber",
                "phoneNum", "bankId", "bankName", "creditCard", "email", "agentAuth", "agentFrontIdcard", "agentBackIdcard",
                "specialReason", "specialAttachment", "failReason", "valid", "ctime", "utime", "effective", "brandIds",
                "visitKPPro", "signTaskType", "remark", "relPoiInfo", "haveAgentAuth", "legalIdcardCopy");
    }

    // 新增
    abstract void insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException;

    // 更新
    abstract void update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException;

    // 删除
    abstract void delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException;

    public List<WmCustomerKp> kpTransList(WmCustomerKp operateKp) {
        return Arrays.asList(operateKp);
    }

    public boolean isDiff(WmCustomerKp oldKp, WmCustomerKp newKp) throws WmCustomerException {
        if (DiffUtil.compare(oldKp, newKp, fieldList)) {
            return true;
        }
        return false;
    }

    /**
     * 企客同步时需要,非签约人KP生效变更时,发出变更事件
     * @param wmCustomer
     */
    public void sendEffectiveMq(WmCustomerDB wmCustomer){
        if(wmCustomer == null){
            return;
        }
        mafkaMessageSendManager.send(new CustomerMQBody(wmCustomer.getId(), CustomerMQEventEnum.CUSTOMER_OTHER_KP_EFFECTIVE, ""));
    }
}
