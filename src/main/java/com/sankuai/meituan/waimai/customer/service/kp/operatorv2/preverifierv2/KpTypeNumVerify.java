package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpTypeNumPreVerify;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-02 14:11
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class KpTypeNumVerify extends KpPreverify {

    @Autowired
    private KpTypeNumPreVerify kpTypeNumPreVerify;


    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        // 适配旧方法，参数转换
        Map<String, List<WmCustomerKp>> transMap = parameterTrans(insertKp, updateKp, deleteKp);
        kpTypeNumPreVerify.verify(wmCustomer, oldCustomerKpList, transMap.get("insert"), transMap.get("delete"), transMap.get("update"));// 参数顺序适配
        return new Object();// 暂且返回object，方便后续扩展
    }
}
