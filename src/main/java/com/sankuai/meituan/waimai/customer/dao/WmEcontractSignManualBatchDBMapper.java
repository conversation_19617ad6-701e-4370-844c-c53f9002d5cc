package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualBatchDB;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmEcontractSignManualBatchDBMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WmEcontractSignManualBatchDB record);

    int insertSelective(WmEcontractSignManualBatchDB record);

    WmEcontractSignManualBatchDB selectByPrimaryKey(Long id);

    WmEcontractSignManualBatchDB selectByPrimaryKeyMaster(Long id);

    int updateByPrimaryKeySelective(WmEcontractSignManualBatchDB record);

    int updateByPrimaryKey(WmEcontractSignManualBatchDB record);

    int deleteByCustomerId(Integer customerId);

    WmEcontractSignManualBatchDB queryByCustomerId(@Param("customerId") Integer customerId, @Param("packId") Long packId);

    List<WmEcontractSignManualBatchDB> batchQueryById(@Param("idList")List<Long> idList);
}