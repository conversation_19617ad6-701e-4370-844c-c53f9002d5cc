package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.bind;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiRelConstants;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.poirel.flow.CustomerPoiRelFlowEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240109
 * @desc 聚合配送商、绑定策略
 */
@Service
@Slf4j
@Rule
public class AggDistributeBindRule {

    /**
     * 条件判断是否单店类客户绑定
     *
     * @param context
     * @return
     */
    @Condition
    public boolean when(@Fact("context") CustomerPoiBindFlowContext context) {
        return CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue() == context.getWmCustomerDB().getCustomerRealType();
    }

    /**
     * 根据策略执行
     *
     * @return
     */
    @Action
    public void execute(@Fact("context") CustomerPoiBindFlowContext context) throws WmCustomerException {
        IBindCheckStrategy bindCheckStrategy = (IBindCheckStrategy) SpringBeanUtil.getBean("aggDistributeBindCheckStrategy");

        BindStrategy strategyContext = BindStrategy.builder()
                .bindCheckStrategy(bindCheckStrategy)
                .bindPreCoreStrategy(null)
                .bindCoreStrategy(null)
                .bindAfterStrategy(null)
                .build();

        BindFlowStrategy bindFlowStrategy = BindFlowStrategy.builder()
                .flowEnum(CustomerPoiRelFlowEnum.REJECT_BIND)
                .wmPoiIdSet(context.getWmPoiIdSet())
                .bindStrategy(strategyContext)
                .build();

        context.setBindFlowStrategyList(Lists.newArrayList(bindFlowStrategy));
    }


    @Priority
    public int getPriority() {
        return 1;
    }
}