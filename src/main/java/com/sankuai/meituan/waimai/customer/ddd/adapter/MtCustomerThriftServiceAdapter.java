package com.sankuai.meituan.waimai.customer.ddd.adapter;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerMultiplexDTO;
import com.sankuai.meituan.waimai.thrift.exception.MtCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.dto.CustomerDTO;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.nibcus.inf.customer.client.request.BusinessCustomerExtendAttributeSaveRequest;
import com.sankuai.nibcus.inf.customer.client.request.QualificationNumTypeGetCustomerIdRequest;
import com.sankuai.nibcus.inf.customer.client.response.BaseResponse;
import com.sankuai.nibcus.inf.customer.client.response.PoiIdsResponse;
import org.apache.thrift.TException;

import java.util.List;
import java.util.Map;

/**
 * 美团客户平台访问的adapter类
 */
public interface MtCustomerThriftServiceAdapter {

    /**
     * 在客户平台创建客户
     *
     * @param wmCustomerDB
     * @param isUpdateImg  是否在方法内上传图片到客户平台
     * @return
     * @throws MtCustomerException
     */
    long createMtCustomer(WmCustomerDB wmCustomerDB, boolean isUpdateImg) throws MtCustomerException;


    /**
     * 在客户平台创建客户，用来同步历史数据
     *
     * @param wmCustomerDB
     * @return
     * @throws MtCustomerException
     */
    @Deprecated
    long createMtCustomerForHistory(WmCustomerDB wmCustomerDB) throws MtCustomerException;

    /**
     * 更新客户平台中的客户信息</br>
     * 如果原来在客户不太不存在，则插入数据到美团客户平台。
     *
     * @param wmCustomerDB
     * @return mtCustomerId
     * @throws MtCustomerException
     */
    long updateMtCustomer(WmCustomerDB wmCustomerDB, boolean isUploadImg) throws MtCustomerException;

    /**
     * 更新客户（复用客户第一次审核生效时调用，其他情况不允许调用）
     * 限定使用场景：复用客户新增审核通过时调用即复用客户未生效-》已生效
     *
     * @param wmCustomerDB
     * @param isUploadImg
     * @return
     * @throws MtCustomerException
     */
    long updateReuseCustomerQualification(WmCustomerDB wmCustomerDB, boolean isUploadImg) throws MtCustomerException;

    /**
     * 修改客户平台数据状态
     *
     * @param mtCustomerId
     * @param valid
     * @return
     */
    boolean updateCustomerValid(Long mtCustomerId, int valid);

    /**
     * 查询客户平台的customer信息customer
     *
     * @param mtCustomerId
     * @return
     */
    WmCustomerDB getCustomerByMtCustomerId(Long mtCustomerId) throws TException;

    /**
     * 根据传入对象的mtCustomerId查询客户平台的customer信息，并且用客户平台中的信息替换wmCustomerDB中的信息。
     *
     * @param wmCustomerDB
     * @return
     */
    WmCustomerDB getCustomerByMtCustomerId(WmCustomerDB wmCustomerDB) throws TException;

    /**
     * 批量获取客户信息from客户平台
     */
    void getCustomersByMtCustomerIds(List<Long> mtCustomerIds, List<WmCustomerDB> customerList) throws TException;

    /**
     * 根据编号查询复用客户（复用客户）
     *
     * @param number
     * @return
     */
    List<WmCustomerMultiplexDTO> getMultiplexCustomer(String number) throws WmCustomerException;


    /**
     * 根据复用池客户ID查询复用客户信息（复用客户）
     *
     * @param multiplexCustomerId 复用池客户ID
     * @param isRevertSigner      是否转换签约人信息
     * @return
     */
    WmCustomerMultiplexDTO getMultiplexCustomer(Long multiplexCustomerId, Long businessId, boolean isRevertSigner) throws WmCustomerException;

    /**
     * 判断客户是否复用客户（复用客户）
     *
     * @param customerId
     * @return
     */
    boolean isMultiplexCustomer(Long customerId);

    /**
     * 修改客户的扩展属性
     *
     * @param mtCustomerId
     * @param bizOrgCode
     * @return
     */
    Boolean saveMtCustomerExtendAttribute(Long mtCustomerId, Integer bizOrgCode) throws MtCustomerException;

    /**
     * 修改客户的扩展属性
     *
     * @param mtCustomerId
     * @param bizOrgCode
     * @return
     */
    long saveMtCustomerBizOrgCode(Long mtCustomerId, Integer bizOrgCode);

    /**
     * 只查询扩展字段
     *
     * @param mtCustomerId
     */
    void refreshCustomerExtendInfoByMtCustomerId(Long mtCustomerId) throws TException;

    /**
     * 根据客户ID和业务线
     * @return
     */
    WmCustomerDB getCustomerByIdAndBusinessLine(Long mtCustomerId,Long businessLineId) throws WmCustomerException;

    /**
     * 根据资质编号查询客户平台所有业务线
     * 返回结果：Map<客户ID，业务线集合>
     */
    List<Long> getCustomerIdByQualificationNum(QualificationNumTypeGetCustomerIdRequest request) throws TException, WmCustomerException;

    List<Long> getMtPoiIdByMtCustomerId(Long mtCustomerId, Long businessLineId) throws WmCustomerException;

    List<Long> getCustomerIdsByPoiId(Long poiId, BusinessLineEnum businessLineEnum) throws WmCustomerException;

    /**
     * 根据客户平台ID集合批量查询客户信息
     * @param mtCustomerIdList
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    List<WmCustomerDB> getCustomerByMtCustomerIds(List<Long> mtCustomerIdList,BusinessLineEnum businessLineEnum)throws TException,WmCustomerException;

    /**
     * 后续可替换getCustomerByMtCustomerId
     * @return
     */
    WmCustomerDB getCustomerByMtCustomerIdV2(WmCustomerDB wmCustomerDB) throws WmCustomerException;



}
