package com.sankuai.meituan.waimai.customer.service.kp.preverifier;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.DifferentCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.KPSource;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 签约人KP证件类型与客户主体证件类型一致性校验
 *
 * <AUTHOR>
 */
@Component
public class SignerCertTypePreVerify implements KpPreverifier {

    private static final Logger LOGGER = LoggerFactory.getLogger(SignerCertTypePreVerify.class);

    private static final ImmutableSet<Integer> SIGNER_TYPES_CUSTOMER_TYPE = ImmutableSet.of(CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode(), CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());


    private static final ImmutableSet<Byte> BUSINESS_LICENE_SIGNER_TYPES = ImmutableSet.of(KpSignerTypeEnum.SIGNER.getType(), KpSignerTypeEnum.AGENT.getType());

    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;

    @Autowired
    private KpDataPreVerify kpDataPreVerify;

    /**
     * 签约人的签约类型选择需与客户主体选择一致：
     * 若客户主体类型选择营业执照/海外营业执照，则签约人可选择法人签约或代理人签约，若选择法人签约，则需校验签约人姓名与营业执照法人姓名一致；
     * 若客户主体类型选择个人证件，则签约人仅可选择个人签约，若选择个人签约，则需校验签约人姓名与个人证件的姓名一致
     * 若签约人姓名与客户证件姓名不一致，则签约人仅能选择代理人，且需要上传授权书，且签章为签约人的那个
     */
    @Override
    public void verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldWmCustomerKpList, List<WmCustomerKp> addKpList, List<WmCustomerKp> deleteKpList, List<WmCustomerKp> upgradeKpList) throws WmCustomerException {
        if (CollectionUtils.isEmpty(addKpList) && CollectionUtils.isEmpty(upgradeKpList)) {
            return;
        }
        List<WmCustomerKp> allVerifyKpList;
        if (!CollectionUtils.isEmpty(addKpList)) {
            allVerifyKpList = Lists.newArrayList(addKpList);
        } else {
            allVerifyKpList = new ArrayList<>();
        }
        if (!CollectionUtils.isEmpty(upgradeKpList)) {
            allVerifyKpList.addAll(upgradeKpList);
        }
        for (WmCustomerKp wmCustomerKp : allVerifyKpList) {
            if (CertTypeEnum.DRIVING_LICENCE.getType() == wmCustomerKp.getCertType()) {
                ThrowUtil.throwClientError("KP的证件类型不能为驾驶证");
            }
            if (KpSignerTypeEnum.AGENT.getType() != wmCustomerKp.getSignerType() &&
                    (CertTypeEnum.ID_CARD_COPY.getType() == wmCustomerKp.getCertType() || CertTypeEnum.ID_CARD_TEMP.getType() == wmCustomerKp.getCertType())) {
                ThrowUtil.throwClientError("证件类型不可选择临时身份证或身份证复印件");
            }
        }

        Integer customerType = wmCustomer.getCustomerType();
        //新增的签约人KP
        WmCustomerKp signerKp = differentCustomerKpService.getSignerKp(addKpList);
        if (signerKp == null) {
            signerKp = differentCustomerKpService.getSignerKp(upgradeKpList);
        }
        LOGGER.info("SignerCertTypePreVerify wmCustomer={}, signerKp={}", JSON.toJSONString(wmCustomer), JSON.toJSONString(signerKp));
        if (signerKp != null) {
            // 海外法人姓名英文

            if (SIGNER_TYPES_CUSTOMER_TYPE.contains(customerType)) {
                //客户主体类型为营业执照/海外营业执照，签约类型必须是法人代表或代理人
                if (!BUSINESS_LICENE_SIGNER_TYPES.contains(signerKp.getSignerType())) {
                    ThrowUtil.throwClientError("签约人签约类型与客户主体类型不匹配");
                }
                if (signerKp.getSignerType() == KpSignerTypeEnum.SIGNER.getType()) {
                    if (!signerKp.getCompellation().equals(wmCustomer.getLegalPerson())) {
                        ThrowUtil.throwClientError("签约人姓名与客户法人姓名不一致，签约类型不可为：法人代表");
                    }
                } else if (signerKp.getKpType() == KpSignerTypeEnum.AGENT.getType()) {
                    if (StringUtil.isBlank(signerKp.getAgentFrontIdcard()) || StringUtil.isBlank(signerKp.getAgentBackIdcard())) {
                        ThrowUtil.throwClientError("代理人手持身份证图片不能为空！");
                    }
                }
            } else if (CustomerType.CUSTOMER_TYPE_IDCARD.getCode() == customerType) {
                //客户主体类型为身份证，签约类型必须为个人
                if (signerKp.getSignerType() != KpSignerTypeEnum.LEGAL.getType()) {
                    ThrowUtil.throwClientError("签约人签约类型必须为个人");
                }
                if (!signerKp.getCompellation().equals(wmCustomer.getCustomerName())) {
                    ThrowUtil.throwClientError("签约人姓名与客户证件姓名不一致");
                }
            }

            // 校验银行流水非空
            kpDataPreVerify.checkKpBankStatement(signerKp, wmCustomer);
        }
    }

    @Override
    public int order() {
        return 3;
    }

}
