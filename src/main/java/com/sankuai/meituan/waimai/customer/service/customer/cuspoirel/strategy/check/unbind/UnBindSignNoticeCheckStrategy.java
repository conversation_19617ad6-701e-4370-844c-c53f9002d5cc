package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.unbind;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindSignNoticeDTO;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IUnBindCheckStrategy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerSmsTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240124
 * @desc 签约解绑回调通知校验策略
 */
@Service
@Slf4j
public class UnBindSignNoticeCheckStrategy implements IUnBindCheckStrategy {

    @Autowired
    private WmCustomerService wmCustomerService;

    /**
     * 门店解绑校验策略
     *
     * @param context
     * @throws WmCustomerException
     */
    @Override
    public void checkUnBindByParams(CustomerPoiUnBindFlowContext context) throws WmCustomerException, TException {

        log.info("UnBindSignNoticeCheckStrategy.checkUnBindByParams,context={}", JSONObject.toJSONString(context));
        UnBindSignNoticeDTO unBindSignNoticeDTO = context.getUnBindSignNoticeDTO();

        Integer customerId = context.getCustomerId();
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = unBindSignNoticeDTO.getWmCustomerPoiSmsRecordDB();
        //绑定签约短信记录非空校验
        if (wmCustomerPoiSmsRecordDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "绑定签约短信记录不能为空");
        }
        if (wmCustomerPoiSmsRecordDB.getType() != WmCustomerSmsTypeEnum.OLD_CUSTOMER_UNBIND.getCode()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "关联签约短信记录非原客户确认解绑任务");
        }
        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }

    }
}
