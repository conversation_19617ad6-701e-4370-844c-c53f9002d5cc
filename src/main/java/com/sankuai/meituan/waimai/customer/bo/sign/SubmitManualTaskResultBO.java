package com.sankuai.meituan.waimai.customer.bo.sign;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2022/12/4 9:59 AM
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitManualTaskResultBO {

    private List<Long> manualBatchIdList;

    /**
     * @see com.sankuai.meituan.waimai.customer.constant.contract.ManualTaskSubmitStatusEnum;
     */
    private Integer status;

    /**
     * 操作时间
     */
    private Long actionTime;

}
