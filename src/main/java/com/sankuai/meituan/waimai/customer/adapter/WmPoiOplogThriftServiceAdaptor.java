package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.oplog.thrift.domain.WmPoiOplog;
import com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogThriftService;
import com.sankuai.meituan.waimai.poi.domain.WmPoiOpLog;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WmPoiOplogThriftServiceAdaptor {

    @Autowired
    private WmPoiOplogThriftService.Iface wmPoiOplogThriftService;

    public void insertPoiOpLog(Long wmPoiId, String diff, Integer uid, String uName, String before, String after) {
        try {
            WmPoiOplog wmPoiOplog = new WmPoiOplog();
            wmPoiOplog.setWm_poi_id(wmPoiId);
            wmPoiOplog.setOp_type(WmPoiOpLog.OpType.POI_CUSTOMER_CHANGE.getId());
            wmPoiOplog.setDiff(StringUtils.defaultString(diff));
            wmPoiOplog.setOp_uid(uid);
            wmPoiOplog.setOp_uname(uName);
            wmPoiOplog.setPre_value(before);
            wmPoiOplog.setCurrent_value(after);
            log.debug(wmPoiOplog.toString());
            wmPoiOplogThriftService.insertWithoutMQByAsync(wmPoiOplog);
        } catch (Exception e) {
            log.error("插入门店日志异常 wmPoiId={},diff={},uid={},uName={},before={},after={}", wmPoiId,
                    diff, uid, uName, before, after, e);
        }
    }


    /**
     * 门店关联食堂操作日志
     * @param wmPoiId 门店ID
     * @param userId 用户ID
     * @param userName 用户名称
     * @param logInfo 日志信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void insertCanteenPoiOpLogWithoutMQByAsync(Long wmPoiId, Integer userId, String userName, String logInfo) throws WmSchCantException {
        try {
            WmPoiOplog wmPoiOplog = new WmPoiOplog();
            wmPoiOplog.setDiff(logInfo);
            wmPoiOplog.setOp_uid(userId);
            wmPoiOplog.setOp_uname(userName);
            wmPoiOplog.setWm_poi_id(wmPoiId);
            wmPoiOplog.setOp_type(WmPoiOpLog.OpType.POI_CANTEEN_REL.getId());
            wmPoiOplog.setCtime(TimeUtil.unixtime());

            log.info("[WmPoiOplogThriftServiceAdaptor.insertCanteenPoiOpLogWithoutMQByAsync] wmPoiOplog = {}", JSONObject.toJSONString(wmPoiOplog));
            wmPoiOplogThriftService.insertWithoutMQByAsync(wmPoiOplog);
        } catch (Exception e) {
            log.info("[WmPoiOplogThriftServiceAdaptor.insertCanteenPoiOpLogWithoutMQByAsync] wmPoiId = {}, userId = {}, userName = {}, logInfo = {}",
                    wmPoiId, userId, userName, logInfo);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "新增门店操作日志异常");
        }
    }
}
