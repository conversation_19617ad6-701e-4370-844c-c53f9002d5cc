package com.sankuai.meituan.waimai.customer.service.sc.flow.machine;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmSchoolDeliveryStatusEnum;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmSchoolDeliveryStatusMachineEvent;
import com.sankuai.meituan.waimai.customer.service.sc.flow.entity.WmSchoolDeliveryStatusMachineContext;
import lombok.extern.slf4j.Slf4j;
import org.squirrelframework.foundation.fsm.impl.AbstractStateMachine;

/**
 * 学校交付人员指定状态机
 * <AUTHOR>
 * @date 2024/02/12
 * @email <EMAIL>
 */
@Slf4j
public class WmSchoolDeliveryAssignmentStatusMachine extends AbstractStateMachine<WmSchoolDeliveryAssignmentStatusMachine, WmSchoolDeliveryStatusEnum, WmSchoolDeliveryStatusMachineEvent, WmSchoolDeliveryStatusMachineContext> {

    /**
     * 异常处理
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void afterTransitionCausedException(WmSchoolDeliveryStatusEnum fromState,
                                                  WmSchoolDeliveryStatusEnum toState,
                                                  WmSchoolDeliveryStatusMachineEvent event,
                                                  WmSchoolDeliveryStatusMachineContext context) {
        log.info("[WmSchoolDeliveryAssignmentStatusMachine.afterTransitionCausedException] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        super.afterTransitionCausedException(fromState, toState, event, context);
    }

    /**
     * 每次流转完成时更新表中状态
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void afterTransitionCompleted(WmSchoolDeliveryStatusEnum fromState,
                                            WmSchoolDeliveryStatusEnum toState,
                                            WmSchoolDeliveryStatusMachineEvent event,
                                            WmSchoolDeliveryStatusMachineContext context) {
        log.info("[WmSchoolDeliveryAssignmentStatusMachine.afterTransitionCompleted] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        super.afterTransitionCompleted(fromState, toState, event, context);
    }

    /**
     * 流程实例初始节点开始前
     * @param fromState 流转前状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void beforeTransitionBegin(WmSchoolDeliveryStatusEnum fromState,
                                         WmSchoolDeliveryStatusMachineEvent event,
                                         WmSchoolDeliveryStatusMachineContext context) {
        log.info("[WmSchoolDeliveryAssignmentStatusMachine.beforeTransitionBegin] fromState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 流程实例初始节点流转后
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void afterTransitionEnd(WmSchoolDeliveryStatusEnum fromState,
                                      WmSchoolDeliveryStatusEnum toState,
                                      WmSchoolDeliveryStatusMachineEvent event,
                                      WmSchoolDeliveryStatusMachineContext context) {
        log.info("[WmSchoolDeliveryAssignmentStatusMachine.afterTransitionEnd] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 每一个action执行前
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void beforeActionInvoked(WmSchoolDeliveryStatusEnum fromState,
                                       WmSchoolDeliveryStatusEnum toState,
                                       WmSchoolDeliveryStatusMachineEvent event,
                                       WmSchoolDeliveryStatusMachineContext context) {
        log.info("[WmSchoolDeliveryAssignmentStatusMachine.beforeActionInvoked] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 每一个action执行后
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void afterActionInvoked(WmSchoolDeliveryStatusEnum fromState,
                                      WmSchoolDeliveryStatusEnum toState,
                                      WmSchoolDeliveryStatusMachineEvent event,
                                      WmSchoolDeliveryStatusMachineContext context) {
        log.info("[WmSchoolDeliveryAssignmentStatusMachine.afterActionInvoked] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    @Override
    public WmSchoolDeliveryStatusEnum getCurrentState() {
        return super.getCurrentState();
    }

    @Override
    public void start() {
        super.start();
    }

}
