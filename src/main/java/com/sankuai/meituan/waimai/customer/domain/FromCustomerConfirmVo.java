package com.sankuai.meituan.waimai.customer.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class FromCustomerConfirmVo {
    /**
     * 切换任务id
     */
    private Long taskId;

    /**
     * 原客户id
     */
    private Integer fromCustomerId;

    /**
     * 目标客户id
     */
    private Integer toCustomerId;

    /**
     * 门店列表
     */
    private List<Long> wmPoiIdList;

    /**
     * 操作人id
     */
    private Integer opUid;

    /**
     * 操作人姓名
     */
    private String opName;

    /**
     * 执行步骤版本
     */
    private Integer stepVersion;
}
