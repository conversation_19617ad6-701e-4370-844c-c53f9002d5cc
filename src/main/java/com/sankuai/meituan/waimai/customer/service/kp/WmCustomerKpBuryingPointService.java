package com.sankuai.meituan.waimai.customer.service.kp;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.kp.KpUpdateBuryingPointVO;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.util.DesensitizeUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.EffectiveStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.waimai.thrift.push.client.inbox.exception.WmReachPlatformException;
import com.sankuai.waimai.thrift.push.client.inbox.param.NoticeSendParam;
import com.sankuai.waimai.thrift.push.client.inbox.result.InboxMessagesResult;
import com.sankuai.waimai.thrift.push.client.inbox.service.WmInboxSendThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.EFFECTIVE;
import static com.sankuai.meituan.waimai.thrift.util.DiffUtil.ENUM_FIELD_CONVERTER_MAP;

/**
 * @Author: xhwang
 * @Date: 2022/8/31
 * @Description: 埋点使用
 **/
@Slf4j
@Service
public class WmCustomerKpBuryingPointService {


	private static final ExecutorService EXECUTOR = new ThreadPoolExecutor(10, 40, 10000, TimeUnit.MILLISECONDS,
			new ArrayBlockingQueue<Runnable>(100), new ThreadPoolExecutor.CallerRunsPolicy());

	@Autowired
	private DifferentCustomerKpService differentCustomerKpService;

	@Autowired
	private WmInboxSendThriftService.Iface wmInboxSendThriftService;

	@Autowired
	private WmCustomerDBMapper wmCustomerDBMapper;

	@Autowired
	private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

	@Autowired
	private WmPoiClient wmPoiClient;

	@Autowired
	private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;


	public static final String NEXT_SPLITTER = "，";

	/**
	 * BD修改属性 需要发送消息
	 * 签约类型、姓名、证件类型、证件号码、银行卡号、手机号码
	 */
	private static Map<String, String> needPushMess4KpUpdateInfoMap = new HashMap<>();

	/**
	 * 需要脱敏处理的字段
	 */
	private static Map<String, String> needPushMess4SecretMap = new HashMap<>();

	static {
		needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.SIGNER_TYPE, KpConstants.KpFields.SIGNER_TYPE_DES);
		needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.COMPELLATION, KpConstants.KpFields.COMPELLATION_DESC);
		needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.CERT_TYPE, KpConstants.KpFields.CERT_TYPE_DES);
		needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.CERT_NUMBER, KpConstants.KpFields.CERT_NUMBER_DES);
		needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.CREDIT_CARD, KpConstants.KpFields.CREDIT_CARD_DES);
		needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.PHONE_NUM, KpConstants.KpFields.PHONE_NUM_DES);
		needPushMess4SecretMap.put(KpConstants.KpFields.CERT_NUMBER, KpConstants.KpFields.CERT_NUMBER_DES);
		needPushMess4SecretMap.put(KpConstants.KpFields.CREDIT_CARD, KpConstants.KpFields.CREDIT_CARD_DES);
		needPushMess4SecretMap.put(KpConstants.KpFields.PHONE_NUM, KpConstants.KpFields.PHONE_NUM_DES);
	}

	/**
	 * 埋点用日志
	 */
	private static final Logger logger = LoggerFactory.getLogger("logger_com.sankuai.waimai.customer.kp.pc.update.signer");

	/**
	 * 商家端和BD修改完成后业务处理（数据埋点和发送消息）
	 * @param newSyncCustomerKp
	 * @param oldSyncCustomerKp
	 */
	public void afterSingKp(List<WmCustomerDiffCellBo> diffCellBos, int uid, WmCustomerKp newSyncCustomerKp, WmCustomerKp oldSyncCustomerKp) {
		log.info("执行埋点和发送消息逻辑");
		WmCustomerKp newCustomerKp = new WmCustomerKp();
		WmCustomerKp oldCustomerKp = new WmCustomerKp();
		if(newSyncCustomerKp != null) {
			BeanUtils.copyProperties(newSyncCustomerKp, newCustomerKp);
		}
		if(oldSyncCustomerKp != null){
			BeanUtils.copyProperties(oldSyncCustomerKp, oldCustomerKp);
		}

		EXECUTOR.submit(new TraceRunnable(() -> {
			/**
			 * 非签约人不处理
			 */
			if (KpTypeEnum.SIGNER.getType() != newCustomerKp.getKpType()) {
				return;
			}
			//判断是否生效
			if (newCustomerKp.getEffective() == EFFECTIVE) {
				if(oldCustomerKp.getOperateSource() == CustomerDeviceType.UNKNOWN.getCode()){
					log.warn("修改KP未知操作来源，请排查,customerKpId:{}", oldCustomerKp.getId());
					return;
				}
				//判断来源 商家端
				if (oldCustomerKp.getOperateSource() == CustomerDeviceType.BUSINESS_PC.getCode()
						|| oldCustomerKp.getOperateSource() == CustomerDeviceType.BUSINESS_APP.getCode()) {
					effectiveBuryingPoint(uid, newCustomerKp, oldCustomerKp, true);
					//修改操作入口为默认
				} else {
					//非商家端
					pushMessAfterUpdateSingerKp(diffCellBos, uid, newCustomerKp, oldCustomerKp);
				}
			} else {
				if (oldCustomerKp.getOperateSource() == CustomerDeviceType.BUSINESS_PC.getCode()
						|| oldCustomerKp.getOperateSource() == CustomerDeviceType.BUSINESS_APP.getCode()) {
					effectiveBuryingPoint(uid, newCustomerKp, oldCustomerKp, false);
				}
			}
		}));
	}

	/**
	 * BD修改签约人KP完成之后发送消息到商家
	 */
	public void pushMessAfterUpdateSingerKp(List<WmCustomerDiffCellBo> diffCellBos, int uid, WmCustomerKp newCustomerKp, WmCustomerKp oldCustomerKp) {
		log.info("修改完签约人信息后，开始执行Push逻辑, diffCellBos:{}, uid:{}, newCustomerKp:{}, oldCustomerKp:{}",
				JSON.toJSONString(diffCellBos), uid, JSON.toJSONString(newCustomerKp), JSON.toJSONString(oldCustomerKp));
		if(oldCustomerKp == null || newCustomerKp == null){
			log.error("因修改前或修改后信息为空，发送Push消息失败");
			return;
		}
		if(KpTypeEnum.SIGNER.equals(newCustomerKp.getKpType())){
			log.info("客户KP不是签约人类型，不发送消息，KpId:{}", newCustomerKp.getId());
			return;
		}
		//单店客户类型
		Integer customerId = oldCustomerKp.getCustomerId();
		WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
		if(wmCustomerDB == null){
			log.info("未查询到有效客户信息，不发送Push消息, customerId:{}", customerId);
			return;
		}
		if(CustomerRealTypeEnum.DANDIAN.equals(wmCustomerDB.getCustomerRealType())){
			log.info("客户不是单店类型，不发送消息, customerId:{}", customerId);
			return;
		}
		//查询门店数量
		Integer countCustomerPoi = wmCustomerPoiDBMapper.countCustomerPoi(customerId);
		if(countCustomerPoi != 1){
			log.info("客户关联的门店数量不等于1,不推送消息");
			return;
		}
		//查询门店信息
		WmCustomerPoiDB wmCustomerPoiRel = wmCustomerPoiDBMapper.selectOnlyRelByCustomerId(customerId);
		if(wmCustomerPoiRel == null){
			log.info("未查询到绑定门店相关信息, customerId:{}", customerId);
			return;
		}
		Long wmPoiId = wmCustomerPoiRel.getWmPoiId();
		WmPoiDomain poiDomain = null;
		try {
			poiDomain = wmPoiClient.getWmPoiById(wmPoiId);
		} catch (WmCustomerException e) {
			log.error("查询门店信息失败, 发送消息失败, customerId:{},wmPoiId:{} ", customerId, wmPoiId, e);
			return;
		}
		if(poiDomain == null || poiDomain.getCityId() < 1){
			return;
		}

		//是否启用此功能
		if(!MccConfig.getGrayPoiCitySwitch()){
			return;
		}
		//不在灰度范围
		if(CollectionUtils.isNotEmpty(MccConfig.getGrayPoiCityList())
				&& !MccConfig.getGrayPoiCityList().contains(poiDomain.getCityId())){
			log.info("不符合灰度逻辑, 不发送消息, customerId:{},wmPoiId:{} ", customerId, wmPoiId);
			return;
		}

		wmCustomerSensitiveWordsService.readKpWhenSelect(oldCustomerKp);

		if (CollectionUtils.isEmpty(diffCellBos)) {
			//通过diff查询
			try {
				diffCellBos = DiffUtil.compare(oldCustomerKp, newCustomerKp, needPushMess4KpUpdateInfoMap);
			} catch (WmCustomerException e) {
				log.error("diff 异常, oldCustomerKp={}, newCustomerKp={}", JSON.toJSONString(oldCustomerKp), JSON.toJSONString(newCustomerKp), e);
				return;
			}
		}
		//过滤需要发信息的属性
		diffCellBos = getDiffListMess(diffCellBos);

		if(CollectionUtils.isNotEmpty(diffCellBos)){
			String diffLog = getDiffMess(diffCellBos, oldCustomerKp, newCustomerKp);

			/**
			 * 发送Push消息模板名称
			 */
			String publishMessTemplate = MccConfig.getPushMessTemplate();
			NoticeSendParam noticeSendParam = new NoticeSendParam();
			noticeSendParam.setPubType(1);
			noticeSendParam.setTemplateName(publishMessTemplate);
			//业务ID使用uid
			noticeSendParam.setBizId(wmPoiId);
			noticeSendParam.setPoiId(wmPoiId);
			Map<String, String> paramMap = new HashMap<>();
			paramMap.put("mess", diffLog);
			noticeSendParam.setParamMap(paramMap);
			log.info("修改完签约人信息后，向门店：{} 发送Push消息:{}", wmPoiId, JSON.toJSONString(noticeSendParam));
			try {
				InboxMessagesResult inboxMessagesResult = wmInboxSendThriftService.sendMessage(Lists.newArrayList(noticeSendParam));
				log.info("发送Push消息完成, inboxMessagesResult:{}", JSON.toJSONString(inboxMessagesResult));
			} catch (WmReachPlatformException e) {
				log.warn("发送Push消息失败", e);
			} catch (TException e) {
				log.error("向账号发送消息失败, uid:{}, wmPoiId:{}", uid, wmPoiId, e);
			}
			return;
		}
		log.info("不发送Push消息，没有修改五项内容之一或发送人信息无效，uid:{}, wmPoiId:{}, customerId:{}", uid, wmPoiId, customerId);
	}


	private List<WmCustomerDiffCellBo> getDiffListMess(List<WmCustomerDiffCellBo> collectList){
		if(CollectionUtils.isEmpty(collectList)) {
			return Lists.newArrayList();
		}
		List<WmCustomerDiffCellBo> boList = new ArrayList<>();
		for(WmCustomerDiffCellBo diffCellBo : collectList){
			if(needPushMess4KpUpdateInfoMap.containsKey(diffCellBo.getField())){
				boList.add(diffCellBo);
			}
		}
		return boList;
	}

	/**
	 * 根据KP修改内容，拼装Push消息需要的内容
	 * @param collect
	 * @return
	 */
	private String getDiffMess(List<WmCustomerDiffCellBo> collect, WmCustomerKp oldCustomerKp, WmCustomerKp newCustomerKp){
		if (org.springframework.util.CollectionUtils.isEmpty(collect)) {
			return StringUtils.EMPTY;
		}
		StringBuilder sb = new StringBuilder();
		for (WmCustomerDiffCellBo diffCellBo : collect) {
			DiffUtil.EnumFieldConverter converter = ENUM_FIELD_CONVERTER_MAP.get(new DiffUtil.ConvertClassAndField(diffCellBo.getClz(), diffCellBo.getField()));
			if(StringUtils.isNotBlank(sb.toString())){
				sb.append(NEXT_SPLITTER);
			}
			if (converter != null) {
				sb.append(diffCellBo.getDesc()).append("从").append(converter.convert(diffCellBo.getPre())).append("改为").append(converter.convert(diffCellBo.getAft()));
			} else {
				//发送push脱敏
				if(needPushMess4SecretMap.containsKey(diffCellBo.getField())){
					//身份证号或其他证件号
					if(KpConstants.KpFields.CERT_NUMBER.equals(diffCellBo.getField())){
						if(oldCustomerKp != null && CertTypeEnum.getOtherDesensitizeMap().containsKey(oldCustomerKp.getCertType())){
							diffCellBo.setPre(DesensitizeUtil.desensitizeOtherCode(diffCellBo.getPre()));
						}else{
							diffCellBo.setPre(DesensitizeUtil.desensitizeCode(diffCellBo.getPre()));
						}
						if(newCustomerKp != null && CertTypeEnum.getOtherDesensitizeMap().containsKey(newCustomerKp.getCertType())){
							diffCellBo.setAft(DesensitizeUtil.desensitizeOtherCode(diffCellBo.getAft()));
						}else {
							diffCellBo.setAft(DesensitizeUtil.desensitizeCode(diffCellBo.getAft()));
						}
					}
					//手机号
					if(KpConstants.KpFields.PHONE_NUM.equals(diffCellBo.getField())){
						diffCellBo.setPre(DesensitizeUtil.desensitizePhone(diffCellBo.getPre()));
						diffCellBo.setAft(DesensitizeUtil.desensitizePhone(diffCellBo.getAft()));
					}
					//银行卡号
					if(KpConstants.KpFields.CREDIT_CARD.equals(diffCellBo.getField())){
						diffCellBo.setPre(DesensitizeUtil.desensitizeBankNum(diffCellBo.getPre()));
						diffCellBo.setAft(DesensitizeUtil.desensitizeBankNum(diffCellBo.getAft()));
					}
				}

				if(StringUtils.isBlank(diffCellBo.getPre())){
					diffCellBo.setPre("无");
				}
				if(StringUtils.isBlank(diffCellBo.getAft())){
					diffCellBo.setAft("无");
				}
				sb.append(diffCellBo.getDesc()).append("从").append(diffCellBo.getPre()).append("改为").append(diffCellBo.getAft());
			}
		}
		return sb.toString();
	}



	/**
	 * 数据修改生效埋点
	 * flag 是否生效, true 生效，false 未失效
	 */
	public void effectiveBuryingPoint(int uid, WmCustomerKp newSyncCustomerKp, WmCustomerKp oldSyncCustomerKp, boolean flag) {
		log.info("数据埋点, 是否生效:{}, uid:{},newSyncCustomerKp:{}, oldSyncCustomerKp:{}",
				flag, uid, JSON.toJSONString(newSyncCustomerKp), JSON.toJSONString(oldSyncCustomerKp));
		WmCustomerKp newCustomerKp = new WmCustomerKp();
		WmCustomerKp oldCustomerKp = new WmCustomerKp();
		if(newSyncCustomerKp != null) {
			BeanUtils.copyProperties(newSyncCustomerKp, newCustomerKp);
		}
		if(oldSyncCustomerKp != null){
			BeanUtils.copyProperties(oldSyncCustomerKp, oldCustomerKp);
		}

		EXECUTOR.submit(new TraceRunnable(() -> {
			List<WmCustomerDiffCellBo> diffCellBos =  new ArrayList<>();
			String diffLog = "";
			try {
				diffCellBos = DiffUtil.compare(oldCustomerKp, newCustomerKp, needPushMess4KpUpdateInfoMap);
				diffLog = getDiffMess(diffCellBos, oldCustomerKp, newCustomerKp);
			} catch (WmCustomerException e) {
				log.error("diff 异常, oldCustomerKp={}, newCustomerKp={}", JSON.toJSONString(oldCustomerKp), JSON.toJSONString(newCustomerKp), e);
			}

			log.info("生效数据埋点, uid:{}, 修改内容 diffLog:{}", uid, diffLog);
			KpUpdateBuryingPointVO vo = new KpUpdateBuryingPointVO();
			vo.setKpId(oldCustomerKp.getId());
			vo.setAccId(uid);
			vo.setContent(diffLog);
			vo.setCtime(System.currentTimeMillis()/1000);
			if(flag){
				vo.setIsSuccess(EffectiveStatusEnum.EFFECTIVE.getType());
			}else {
				vo.setIsSuccess(EffectiveStatusEnum.UNEFFECTIVE.getType());
			}
			log.info("商家端修改签约人KP,生效埋点:{}", JSON.toJSONString(vo));
			logger.info(JSON.toJSONString(vo));
		}));
	}
}
