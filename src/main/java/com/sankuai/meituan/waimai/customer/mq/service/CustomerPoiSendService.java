package com.sankuai.meituan.waimai.customer.mq.service;


import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerPoiReleaseMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客户门店发送消息服务
 */
@Slf4j
@Service
public class CustomerPoiSendService {

    @Resource(name = "customerPoiRelaseProducer")
    private MafkaProducer customerPoiRelaseProducer;


    /**
     * 客户切换不下线-原客户确认结果同步
     *
     * @param msg
     */
    public void sendCustomerPoiRelaseNotify(CustomerPoiReleaseMsg msg) {
        String message = JSONObject.toJSONString(msg);
        try {
            customerPoiRelaseProducer.sendMessage(message);
        } catch (Exception e) {
            log.error("sendCustomerPoiRelaseNotify 异常：data={}", message, e);
        }
    }

}
