package com.sankuai.meituan.waimai.customer.domain.sc;

import com.sankuai.meituan.waimai.customer.constant.sc.WmChangeBindPoiLogTypeEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import lombok.Data;

import java.util.List;

/**
 * 食堂门店换绑操作记录BO
 * <AUTHOR>
 * @date 2023/07/19
 * @email <EMAIL>
 */
@Data
public class WmScCanteenPoiChangeBindLogBO {
    /**
     * 换绑日志类型：包含发起，成功，失败
     * {@link com.sankuai.meituan.waimai.customer.constant.sc.WmChangeBindPoiLogTypeEnum}
     */
    private WmChangeBindPoiLogTypeEnum wmChangeBindPoiLogTypeEnum;
    /**
     * 任务ID
     */
    private Long canteenPoiTaskId;
    /**
     * 来源食堂ID
     */
    private Integer canteenIdTo;
    /**
     * 目标食堂ID
     */
    private Integer canteenIdFrom;
    /**
     * 换绑门店信息列表
     */
    private List<WmPoiAggre> changePoiAggreList;
    /**
     * 用户ID
     */
    private Integer opUid;
    /**
     * 用户名称
     */
    private String opUname;

    public static final class Builder {

        private WmChangeBindPoiLogTypeEnum wmChangeBindPoiLogTypeEnum;

        private Long canteenPoiTaskId;

        private Integer canteenIdTo;

        private Integer canteenIdFrom;

        private List<WmPoiAggre> changePoiAggreList;

        private Integer opUid;

        private String opUname;

        public Builder() {
        }

        public WmScCanteenPoiChangeBindLogBO.Builder wmChangeBindPoiLogTypeEnum(WmChangeBindPoiLogTypeEnum val) {
            this.wmChangeBindPoiLogTypeEnum = val;
            return this;
        }

        public WmScCanteenPoiChangeBindLogBO.Builder canteenPoiTaskId(Long val) {
            this.canteenPoiTaskId = val;
            return this;
        }

        public WmScCanteenPoiChangeBindLogBO.Builder canteenIdTo(Integer val) {
            this.canteenIdTo = val;
            return this;
        }

        public WmScCanteenPoiChangeBindLogBO.Builder canteenIdFrom(Integer val) {
            this.canteenIdFrom = val;
            return this;
        }

        public WmScCanteenPoiChangeBindLogBO.Builder changePoiAggreList(List<WmPoiAggre> val) {
            this.changePoiAggreList = val;
            return this;
        }

        public WmScCanteenPoiChangeBindLogBO.Builder opUid(Integer val) {
            this.opUid = val;
            return this;
        }

        public WmScCanteenPoiChangeBindLogBO.Builder opUname(String val) {
            this.opUname = val;
            return this;
        }

        public WmScCanteenPoiChangeBindLogBO build() {
            WmScCanteenPoiChangeBindLogBO wmScCanteenPoiChangeBindLogBO = new WmScCanteenPoiChangeBindLogBO();
            wmScCanteenPoiChangeBindLogBO.setCanteenPoiTaskId(this.canteenPoiTaskId);
            wmScCanteenPoiChangeBindLogBO.setCanteenIdTo(this.canteenIdTo);
            wmScCanteenPoiChangeBindLogBO.setCanteenIdFrom(this.canteenIdFrom);
            wmScCanteenPoiChangeBindLogBO.setChangePoiAggreList(this.changePoiAggreList);
            wmScCanteenPoiChangeBindLogBO.setWmChangeBindPoiLogTypeEnum(this.wmChangeBindPoiLogTypeEnum);
            wmScCanteenPoiChangeBindLogBO.setOpUid(this.opUid);
            wmScCanteenPoiChangeBindLogBO.setOpUname(this.opUname);
            return wmScCanteenPoiChangeBindLogBO;
        }
    }

    @Override
    public String toString() {
        return "WmScCanteenPoiChangeBindLogBO{" +
                "wmChangeBindPoiLogTypeEnum=" + wmChangeBindPoiLogTypeEnum +
                ", canteenPoiTaskId=" + canteenPoiTaskId +
                ", canteenIdTo=" + canteenIdTo +
                ", canteenIdFrom=" + canteenIdFrom +
                ", changePoiAggreList=" + changePoiAggreList +
                ", opUid=" + opUid +
                ", opUname='" + opUname + '\'' +
                '}';
    }
}
