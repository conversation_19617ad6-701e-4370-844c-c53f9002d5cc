package com.sankuai.meituan.waimai.customer.contract.config;

import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ConfigContractQueryRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.CustomerTypeInfoResponse;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/19 15:56
 */
public interface WmFrameContractConfigService {

    List<CustomerTypeInfoResponse> getAllCustomerTypeInfo();

    List<ContractConfigInfo> queryConfigFrameContract(ConfigContractQueryRequestDTO requestDTO) throws TException, WmCustomerException;

    ContractConfigInfo queryContractConfigInfo(Integer contractId) throws WmCustomerException;

    ContractConfigInfo queryContractConfigInfo(String contractCode) throws WmCustomerException;

    List<ContractConfigInfo> allConfigFrameContract();

    String handleManualTaskModule(WmEcontractSignManualTaskDB manualTaskDB);

    String handleSignTaskApplyType(WmEcontractSignTaskDB taskDB);
}
