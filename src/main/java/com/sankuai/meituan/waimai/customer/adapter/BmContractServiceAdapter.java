package com.sankuai.meituan.waimai.customer.adapter;


import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.banma.thrift.deliverycontract.adminbiz.request.WaimaiNotifyDeliveryTransferOrderAbolishSuccessParam;
import com.sankuai.meituan.banma.thrift.deliverycontract.adminbiz.request.WaimaiNotifyDeliveryTransferOrderSignSuccessParam;
import com.sankuai.meituan.banma.thrift.deliverycontract.adminbiz.response.WaimaiNotifyDeliveryTransferOrderAbolishSuccessResp;
import com.sankuai.meituan.banma.thrift.deliverycontract.adminbiz.response.WaimaiNotifyDeliveryTransferOrderSignSuccessResp;
import com.sankuai.meituan.banma.thrift.deliverycontract.adminbiz.thrift.BmContractWmThriftIface;
import com.sankuai.meituan.waimai.customer.bo.sign.BmDeliveryContractNoticeBO;
import com.sankuai.meituan.waimai.customer.constant.common.CommonConst;
import com.sankuai.meituan.waimai.customer.util.common.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * -@author: huangjianlou
 * -@description:
 * -@date: 2022/12/28 10:57 AM
 */
@Service
@Slf4j
public class BmContractServiceAdapter {

    @Resource
    private BmContractWmThriftIface bmContractWmThriftIface;

    public void notifyMedicOrderSplitSignSuccess(BmDeliveryContractNoticeBO bmDeliveryContractNoticeBO
    ) {
        WaimaiNotifyDeliveryTransferOrderSignSuccessParam waimaiNotifyDeliveryTransferOrderSignSuccessParam =
                new WaimaiNotifyDeliveryTransferOrderSignSuccessParam();
        waimaiNotifyDeliveryTransferOrderSignSuccessParam.setPsCustomerId(bmDeliveryContractNoticeBO.getPsCustomerId());
        waimaiNotifyDeliveryTransferOrderSignSuccessParam.setSignSuccessTime(bmDeliveryContractNoticeBO.getSignSuccessTime());
        log.info("#notifyMedicOrderSplitSignSuccess#param={}",
                JSON.toJSONString(waimaiNotifyDeliveryTransferOrderSignSuccessParam));
        //如果response返回的code不是成功code，则进行重试
        WaimaiNotifyDeliveryTransferOrderSignSuccessResp notifyResp = RetryUtil.call(CommonConst.RETRY_TIME,
                true,
                resp -> {
                    if (resp == null) {
                        return true;
                    }
                    //如果response返回的code不是成功code，则进行重试
                    return !Objects.equals(resp.getCode(), CommonConst.SUCCESS);
                },
                () -> bmContractWmThriftIface.waimaiNotifyDeliveryTransferOrderSignSuccess(waimaiNotifyDeliveryTransferOrderSignSuccessParam));
        log.info("notifyResp={}", JSON.toJSONString(notifyResp));

    }

    public void notifyMedicOrderSplitSignAbolish(BmDeliveryContractNoticeBO bmDeliveryContractNoticeBO) {

        WaimaiNotifyDeliveryTransferOrderAbolishSuccessParam waimaiNotifyDeliveryTransferOrderAbolishSuccessParam =
                new WaimaiNotifyDeliveryTransferOrderAbolishSuccessParam();
        waimaiNotifyDeliveryTransferOrderAbolishSuccessParam.setPsCustomerId(bmDeliveryContractNoticeBO.getPsCustomerId());
        log.info("#notifyMedicOrderSplitSignAbolish#param={}",
                JSON.toJSONString(waimaiNotifyDeliveryTransferOrderAbolishSuccessParam));

        //如果response返回的code不是成功code，则进行重试
        WaimaiNotifyDeliveryTransferOrderAbolishSuccessResp notifyResp = RetryUtil.call(CommonConst.RETRY_TIME,
                true,
                resp -> {
                    if (resp == null) {
                        return true;
                    }
                    //如果response返回的code不是成功code，则进行重试
                    return !Objects.equals(resp.getCode(), CommonConst.SUCCESS);
                },
                () -> bmContractWmThriftIface.waimaiNotifyDeliveryTransferOrderAbolishSuccess(waimaiNotifyDeliveryTransferOrderAbolishSuccessParam));
        log.info("notifyResp={}", JSON.toJSONString(notifyResp));

    }
}
