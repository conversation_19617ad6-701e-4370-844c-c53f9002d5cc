package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.multi;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.bizsso.thrift.Bool;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.MULTI_TECHNICAL_SERVICE_FEE_V2)
@Slf4j
@Service
public class WmEcontractMultiTechnicalServiceFeeV2PdfMaker extends
        AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Autowired
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext)
            throws WmCustomerException, IllegalAccessException, TException {
        log.info("#WmEcontractMultiTechnicalServiceFeeV2PdfMaker");
        SignTemplateEnum signTemplateEnum = extractSignTemplateEnum();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.BATCHPOIFEE);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<EcontractDeliveryInfoBo> deliveryInfoList;
        if(wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(originContext.getCustomerId())){
            deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT.getName());
        } else{
            deliveryInfoList = batchDeliveryInfoBo.getEcontractDeliveryInfoBoList();
        }
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();

        //按照是否支持新费率，分模板展示
        //不支持新费率模式数据集
        List<EcontractDeliveryInfoBo> notSupportNewFeeModelInfoBoList = Lists.newArrayList();
        //支持新费率模式数据集
        List<EcontractDeliveryInfoBo> supportNewFeeModelInfoBoList = Lists.newArrayList();
        for(EcontractDeliveryInfoBo temp : deliveryInfoList){
            if(SUPPORT_MARK.equals(temp.getSupportNewModle())){
                supportNewFeeModelInfoBoList.add(temp);
                //是否支持新模式表格
                pdfMetaContent.put("tableSupportNewModel",SUPPORT_MARK);
            }else{
                notSupportNewFeeModelInfoBoList.add(temp);
                //是否支持旧模式表格
                pdfMetaContent.put("tableSupportOldModel",SUPPORT_MARK);
            }
        }

        assembleData(supportNewFeeModelInfoBoList,pdfBizContent,pdfMetaContent,"A");
        assembleData(notSupportNewFeeModelInfoBoList,pdfBizContent,pdfMetaContent,"B");

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        pdfInfoBo.setPdfBizContent(pdfBizContent);

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("signTime", org.apache.commons.lang.StringUtils.defaultIfEmpty(signTime, org.apache.commons.lang.StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", org.apache.commons.lang.StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext),
                org.apache.commons.lang.StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        log.info("#WmEcontractMultiTechnicalServiceFeeV2PdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }

    /**
     * @param infoBoList 业务数据
     * @param pdfBizContent pdf可遍历数据
     * @param pdfMetaContent pdf非遍历数据
     * @param template 所属模板
     */
    private void assembleData(List<EcontractDeliveryInfoBo> infoBoList,
            List<Map<String, String>> pdfBizContent, Map<String, String> pdfMetaContent, String template) throws IllegalAccessException {
        if(CollectionUtils.isEmpty(infoBoList)){
            return;
        }
        Multimap<Integer, Map<String, String>> multimap = ArrayListMultimap.create();
        boolean hasSupport = false;
        boolean hasSLASupport = false;
        for (EcontractDeliveryInfoBo temp : infoBoList) {
            temp.setDeliveryArea(null);
            temp.setEcontractDeliveryWholeCityInfoBo(null);
            temp.setEcontractDeliveryAggregationInfoBo(null);
            multimap.put(Integer.valueOf(temp.getChargingDesc()), MapUtil.Object2Map(temp));
            if (SUPPORT_MARK.equals(temp.getSupportExclusive())) {
                hasSupport = true;
            }
            if (SUPPORT_MARK.equals(temp.getSupportSLA())) {
                hasSLASupport = true;
            }
        }
        List<Integer> sortKeys = Lists.newArrayList(multimap.keySet());
        Collections.sort(sortKeys);
        int count = 1;
        List<Map<String, String>> toModifyList = Lists.newArrayList();
        for (Integer temp : sortKeys) {
            toModifyList = (List<Map<String, String>>) multimap.get(temp);
            for (Map<String, String> mapTemp : toModifyList) {
                mapTemp.put("chargingDesc", count + "");
            }
            pdfBizContent.addAll(toModifyList);
            count++;
        }
        if (hasSupport) {
            pdfMetaContent.put(template+"_hasSupport", "hasSupport");
        }
        if (hasSLASupport) {
            pdfMetaContent.put(template+"_hasSLASupport", "hasSupport");
        }
        pdfMetaContent.put(template+"_sortKeys", Joiner.on(",").join(sortKeys));
    }
}
