package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.mq.domain.SaveDcCustomerMsgBO;
import com.sankuai.meituan.waimai.customer.service.customer.DcCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

@Service
@Slf4j
public class DcCustomerListener implements IMessageListener {

    @Autowired
    private DcCustomerService dcCustomerService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        String msg = mafkaMessage.getBody().toString();
        log.info("监听到餐客户绑定下沉门店-msg:{}",msg);
        SaveDcCustomerMsgBO saveDcCustomerMsgBO = JSONObject.parseObject(msg,SaveDcCustomerMsgBO.class);
        if (!saveDcCustomerMsgBO.isAgentCustomer()){
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            dcCustomerService.saveDcCustomer(saveDcCustomerMsgBO.getCustomerId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }catch (Exception e){
            log.error("监听到餐客户绑定下沉门店-处理异常-msg:{}",msg,e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }
}
