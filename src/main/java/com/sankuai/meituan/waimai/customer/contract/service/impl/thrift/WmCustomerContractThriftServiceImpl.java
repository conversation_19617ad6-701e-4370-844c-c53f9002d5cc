package com.sankuai.meituan.waimai.customer.contract.service.impl.thrift;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractBasicBo;
import com.sankuai.meituan.waimai.c2contract.service.WmC2ContractAuditedThriftService;
import com.sankuai.meituan.waimai.contract.thrift.service.WmContractAuditManagerThriftService;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.ContractLogService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmBusinessCustomerTempletService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractAgentService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractMonitorService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractOnlineService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractPoiProduceService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractSignService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractValidCallbackService;
import com.sankuai.meituan.waimai.customer.ddd.contract.DDDGrayUtil;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractValidCallbackAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.dservice.IWmContractReadDomainService;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.dservice.IWmContractWriteDomainService;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiSubjectService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.common.GrayConfigHelper;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerPoiLogisticsSubjectBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.OpResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.CommonResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.companycustomer.request.QkTriggerSignRequestParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.request.CustomerContractExpireRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.request.CustomerContractInvalidRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.request.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.response.ContractQueryResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.response.StartSignByPoiResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmContractAudited;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.service.WmContractThriftService;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.sankuai.meituan.waimai.customer.contract.service.TempletServiceRouter.getService;

@Slf4j
@Service
public class WmCustomerContractThriftServiceImpl implements WmCustomerContractThriftService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerContractThriftServiceImpl.class);

    @Autowired
    WmTempletContractDBMapper wmTempletContractDBMapper;

    @Autowired
    WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    @Resource(name = "customerEffectiveValidator")
    IContractValidator customerEffectiveValidator;

    @Autowired
    ContractLogService contractLogService;

    @Autowired
    WmContractService wmContractService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    WmContractSignService wmContractSignService;

    @Autowired
    WmContractThriftService.Iface wmContractThriftService;

    @Autowired
    WmC2ContractAuditedThriftService.Iface wmC2ContractAuditedThriftService;

    @Autowired
    WmContractAuditManagerThriftService.Iface wmContractAuditManagerThriftService;

    @Autowired
    WmContractPoiProduceService wmContractPoiProduceService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    static final String show3_0PdfForPoiSwitch = "show3_0PdfForPoiSwitch";

    @Autowired
    IWmContractWriteDomainService wmContractWriteDomainService;

    @Autowired
    WmCrmNoticeService wmCrmNoticeService;
    @Autowired
    WmContractOnlineService wmContractOnlineService;

    @Autowired
    WmContractValidCallbackService wmContractValidCallbackService;

    @Autowired
    private WmCustomerPoiSubjectService wmCustomerPoiSubjectService;

    @Autowired
    IWmContractReadDomainService contractReadDomainService;

    @Autowired
    WmContractMonitorService wmContractMonitorService;

    @Autowired
    WmContractAgentService wmContractAgentService;

    @Autowired
    private WmBusinessCustomerTempletService wmBusinessCustomerTempletService;

    @Autowired
    IWmCustomerRealService wmLeafCustomerRealService;

    @Autowired
    GrayConfigHelper grayConfigHelper;

    @Override
    public WmTempletContractBasicBo getBasicById(long templetId, boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.getBasicById(templetId, isEffective, opUid, opName);
        }
        return wmContractService.getBasicById(templetId, isEffective, opUid, opName);
    }

    @Override
    public WmCustomerContractBo getWmCustomerContractBoById(long templetId, boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.getWmCustomerContractBoById(templetId, isEffective, opUid, opName);
        }
        return wmContractService.getWmCustomerContractBoById(templetId, isEffective, opUid, opName);
    }

    @Override
    public ContractBoPageData getWmCustomerContractBoListByCusId(int customerId, int pageNo, int pageSize, int opUid, String opName)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.getWmCustomerContractBoListByCusId(customerId, pageNo, pageSize, opUid, opName);
        }
        return wmContractService.getWmCustomerContractBoListByCusId(customerId, pageNo, pageSize, opUid, opName);
    }

    @Override
    public ContractBoPageData queryWmCustomerContractBoList(QueryWmCustomerContractReq req) throws WmCustomerException, TException {
        return wmContractService.queryWmCustomerContractBoList(req);
    }

    @Override
    public List<WmTempletContractBasicBo> listWmCustomerContract(ListWmCustomerContractReq req) throws WmCustomerException, TException {
        log.info("listWmCustomerContract req:{}", JSON.toJSONString(req));
        List<WmTempletContractBasicBo> result = wmContractService.listWmTempletContract(req);
        log.info("listWmCustomerContract result:{}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public List<WmTempletContractBasicBo> getWmCustomerContractBoListByPoiIdAndType(long wmPoiId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.getWmCustomerContractBasicListByPoiIdIdAndTypes(wmPoiId, types, opUid, opName);
        }
        return wmContractService.getWmCustomerContractBasicListByPoiIdIdAndTypes(wmPoiId, types, opUid, opName);
    }

    @Override
    public List<WmTempletContractBasicBo> getAuditedContractBasicListByPoiIdAndTypeRT(long wmPoiId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        //此方法无DDD实现
        return wmContractService.getAuditedContractBasicListByPoiIdAndTypeRT(wmPoiId, types, opUid, opName);
    }

    @Override
    public List<WmTempletContractBasicBo> getAuditedContractBasicListByPoiIdAndType(long wmPoiId,
                                                                                    List<Integer> types, int opUid, String opName) throws WmCustomerException, TException {
        //此方法无DDD实现
        return wmContractService.getAuditedContractBasicListByPoiIdAndType(wmPoiId, types, opUid, opName);
    }

    private Integer getTypeByTempletId(long templetId)
            throws WmCustomerException, TException {
        //此方法无DDD实现
        return wmTempletContractDBMapper.selectTypeByPrimaryKey(templetId);
    }

    @Override
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        return wmContractService.save(contractBo, opUid, opName);
    }

    @Override
    public Integer update(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return wmContractWriteDomainService.update(contractBo, opUid, opName);
        }
        return wmContractService.update(contractBo, opUid, opName);
    }

    @Override
    public Integer commitAudit(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return wmContractWriteDomainService.saveAndCommitAudit(contractBo, opUid, opName);
        }
        return wmContractService.saveAndCommitAudit(contractBo, opUid, opName);
    }

    @Override
    public Integer commitAuditForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        return wmContractService.commitAuditOnly(contractBo, opUid, opName);
    }

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        return wmContractService.startSign(contractBo, opUid, opName);
    }

    @Override
    public BaseResponse<Boolean> sgResignLowFee(SgResignLowFeeParam sgResignLowFeeParam) {
        log.info("WmCustomerContractThriftServiceImpl#sgResignLowFee, sgResignLowFeeParam: {}", JSON.toJSONString(sgResignLowFeeParam));
        try {
            Boolean b = wmContractService.sgResignLowFee(sgResignLowFeeParam);
            return BaseResponse.success(b);
        } catch (WmCustomerException e) {
            log.error("WmCustomerContractThriftServiceImpl#sgResignLowFee error", e);
            return BaseResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerContractThriftServiceImpl#sgResignLowFee error", e);
            return BaseResponse.error(-1, "换签失败");
        }
    }

    @Override
    public Integer updateAndSignContract(WmCustomerContractBatchBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        return wmContractService.updateAndSignContract(contractBo, opUid, opName);
    }

    @Override
    public Integer signForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        return wmContractService.startSignOnly(contractBo, opUid, opName);
    }

    @Override
    public LongResult saveAndSignWithExtraData(ContractSaveBo contractSaveBo, Integer opUid, String opUname) throws WmCustomerException, TException {
        return wmContractService.saveAndSignWithExtraData(contractSaveBo, opUid, opUname);
    }

    @Override
    public RetrySmsResponse resendMsgWithResponse(Long contractId, Integer opUid, String opUname) throws WmCustomerException, TException {
        return wmContractService.resendMsgWithResponse(contractId, opUid, opUname);
    }

    @Override
    public BooleanResult cancelSign(Long contractId, Integer opUid, String opUname) throws TException, WmCustomerException {
        return wmContractService.cancelSign(contractId, opUid, opUname);
    }

    @Override
    public List<WmTempletContractBasicBo> getBasicBoListByIdList(List<Long> contractIdList, boolean isEffective, Integer opUid, String opUname) throws TException, WmCustomerException {
        return wmContractService.getBasicBoListByIdList(contractIdList, isEffective, opUid, opUname);
    }

    @Override
    public List<WmTempletContractBasicBo> getBasicBoListByIdListWithoutValid(List<Long> contractIdList, boolean isEffective, Integer opUid, String opUname)
            throws TException, WmCustomerException {
        return wmContractService.getBasicBoListByIdListWithoutValid(contractIdList, isEffective, opUid, opUname);
    }

    @Override
    public BooleanResult invalidByIdList(List<Long> idList, Integer opUid, String opUname) throws TException, WmCustomerException {
        return wmContractService.invalidByIdList(idList, opUid, opUname);
    }


    @Override
    public void handleBusinessCustomerContract(Integer wmCustomerId) throws TException, WmCustomerException {
        wmContractService.handleBusinessCustomerContract(wmCustomerId);
    }

    @Override
    public void saveBusinessCustomerContractExtraData(Long wmCustomerId, String contractExtraContent)
            throws TException, WmCustomerException {
        wmContractService.saveBusinessCustomerContractExtraData(wmCustomerId, contractExtraContent, true);
    }

    @Override
    public void businessCustomerContractApplySign(Long wmCustomerId) throws TException, WmCustomerException {
        wmContractService.businessCustomerContractApplySign(wmCustomerId);
    }

    @Override
    public void pushBusinessContractEffectResult(Long wmCustomerId, Long psContractVersionId) throws TException, WmCustomerException {
        wmContractService.pushBusinessContractEffectResult(wmCustomerId, psContractVersionId);
    }

    @Override
    public void pushAbolishBusinessCustomerContractResult(Long wmCustomerId, Boolean abolishResult, String failMsg)
            throws TException, WmCustomerException {
        wmContractService.pushAbolishBusinessCustomerContractResult(wmCustomerId, abolishResult, failMsg);
    }

    @Override
    public Long queryPsContractVersionId(Integer wmCustomerId) throws TException, WmCustomerException {
        return wmContractService.queryPsContractVersionId(wmCustomerId);
    }

    @Override
    public Long pushBusinessCustomerPaperContractInfo(Long wmCustomerId, Long psContractVersionId) throws TException, WmCustomerException {
        PushBusinessCustomerPaperContractInfoV2 req = new PushBusinessCustomerPaperContractInfoV2();
        req.setWmCustomerId(wmCustomerId);
        req.setPsContractVersionId(psContractVersionId);
        return wmContractService.pushBusinessCustomerPaperContractInfo(req);
    }

    @Override
    public Long pushBusinessCustomerPaperContractInfoV2(PushBusinessCustomerPaperContractInfoV2 req) throws TException, WmCustomerException {
        log.info("pushBusinessCustomerPaperContractInfoV2 req:{}", JSON.toJSONString(req));
        return wmContractService.pushBusinessCustomerPaperContractInfo(req);
    }

    @Override
    public Long getWmContractIdByWmCustomerId(Long wmCustomerId) throws TException, WmCustomerException {
        return wmContractService.getWmContractIdByWmCustomerId(wmCustomerId);
    }

    @Override
    public Integer queryC1ContractEndDate(Long wmCustomerId) throws WmCustomerException, TException {
        return wmContractService.queryC1ContractEndDate(wmCustomerId);
    }

    @Override
    public void pushC1ContractEffectInfo(Integer wmContractId) throws WmCustomerException, TException {
        wmContractService.pushC1ContractEffectInfo(wmContractId);
    }

    @Override
    public void cancelBusinessCustomerSign(Integer wmContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        wmContractService.cancelBusinessCustomerSign(wmContractId, failReason, opUid, opUname);
    }

    @Override
    public void contractToEffectMointor() throws WmCustomerException, TException {
        wmContractMonitorService.contractToEffectMointor();
    }

    @Override
    public List<AgentBo> queryAgentsInfo(Integer uid, String keyword) throws Exception {
        return wmContractAgentService.queryAgentsInfo(uid, keyword);
    }

    @Override
    public void retractBussinessContract(Long contractId, Long customerId, Integer opUid, String opName) throws WmCustomerException, TException {
        wmBusinessCustomerTempletService.retractBussinessContract(contractId, customerId, opUid, opName);
    }

    @Override
    public void triggerQkContractSign(QkTriggerSignRequestParam requsetParam) throws WmCustomerException, TException {
        logger.info("triggerQkContractSign入参:{}", JSON.toJSONString(requsetParam));
        //记录合同生效时间&配送入参信息
        wmContractService.saveBusinessCustomerContractExtraData(requsetParam.getWmCustomerId(),
                JSON.toJSONString(requsetParam),
                false);
        //更新企客合同乙方签约人信息
        wmBusinessCustomerTempletService.updatePartBSignNameInfo(requsetParam.getWmCustomerId(),
                requsetParam.getQkCaInfoBo().getPartBSubjectName());
        //发起签约
        businessCustomerContractApplySign(requsetParam.getWmCustomerId());
    }

    @Override
    public StartSignByPoiResp startSignByPoi(StartSignByPoiReqParam requestParam) throws TException {
        logger.info("startSignByPoi入参:{}", JSON.toJSONString(requestParam));
        return wmContractService.startSignByPoi(requestParam);
    }

    @Override
    public List<WmCustomerContractBo> getWmCustomerContractBoByPoiId(QueryContractByPoiIdReqParam requestParam) throws WmCustomerException, TException {
        logger.info("getWmCustomerContractBoByPoiId入参:{}", JSON.toJSONString(requestParam));
        AssertUtil.assertLongMoreThan0(requestParam.getPoiId(), "门店id");
        AssertUtil.assertCollectionNotEmpty(requestParam.getContractTypes(), "合同信息");
        return wmContractService.getWmCustomerContractBoByPoiId(requestParam);
    }

    @Override
    public Boolean approve(long templetContractId, int opUid, String opUname)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return WmContractValidCallbackAggre.Factory.make(templetContractId).approve(opUid, opUname);
        }
        return wmContractService.approve(templetContractId, opUid, opUname);
    }

    @Override
    public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return WmContractValidCallbackAggre.Factory.make(templetContractId).reject(rejectReason, opUid, opUname);
        }
        return wmContractService.reject(templetContractId, rejectReason, opUid, opUname);
    }

    @Override
    public Boolean canAddPaperContract(int customerId, int opUid, String opUname)
            throws WmCustomerException, TException {
        //此方法无DDD实现
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        contractBo.setBasicBo(new WmTempletContractBasicBo());
        contractBo.getBasicBo().setParentId(customerId);
        contractBo.getBasicBo().setType(WmTempletContractTypeEnum.C1_PAPER.getCode());
        return customerEffectiveValidator.valid(contractBo, opUid, opUname);
    }

    @Override
    public Integer getAuditedContractSignType(long wmPoiId, int type, int opUid, String opName) throws WmCustomerException, TException {
        //此方法无DDD实现
        logger.debug("商家查询已生效合同签约类型  wmPoiId：{}  type：{}  opUid：{}  opUname：{}", wmPoiId, type, opUid, opName);
        Integer signType;
        //C1
        if (type == 1) {
            signType = getC1ContractSignType(wmPoiId);
        } else {
            //C2
            signType = getC2ContractSignType(wmPoiId);
        }
        logger.info("商家查询已生效合同签约类型  wmPoiId：{}  signType：{}", wmPoiId, signType);
        return signType;
    }

    private Integer getC2ContractSignType(long wmPoiId)
            throws TException, WmCustomerException {
        if (ConfigUtilAdapter.getBoolean(show3_0PdfForPoiSwitch, true)) {
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
            if (wmCustomerDB != null && wmCustomerDB.getEffective() == 1) {
                List<WmTempletContractBasicBo> contractBasicBos = wmContractService
                        .selectAuditedContractBasicByParentIdAndTypes(wmCustomerDB.getId().longValue(),
                                Lists.newArrayList(
                                        WmTempletContractTypeEnum.C2_E.getCode(),
                                        WmTempletContractTypeEnum.C2_PAPER.getCode()
                                ));
                if (CollectionUtils.isNotEmpty(contractBasicBos)) {
                    List<Integer> agentIds = getAgentIdsByPoiId(wmPoiId);
                    if (CollectionUtils.isNotEmpty(agentIds)) {
                        for (WmTempletContractBasicBo basicBo : contractBasicBos) {
                            WmTempletContractSignBo auditedPartyBSigner = wmContractSignService
                                    .getAuditedPartyBSignerWithOutSignPhone(basicBo.getTempletContractId());
                            if (auditedPartyBSigner != null && agentIds.contains(auditedPartyBSigner.getSignId())) {
                                logger.info("存在3.0生效C2合同  templetId:{}  合作商id：{}",
                                        contractBasicBos.get(0).getTempletContractId(), auditedPartyBSigner.getSignId());
                                return new WmTempletContractTypeBo(basicBo.getType()).getSignType();
                            }
                        }
                    }
                }
            }
        }
        logger.info("不存在3.0生效C2合同  wmPoiId:{} ", wmPoiId);
        if (!ConfigUtilAdapter.getBoolean("offline_c2_contract_switch", true)) {
            return get2_0C2ContractSignType((int) wmPoiId);
        }
        return null;
    }

    private Integer get2_0C2ContractSignType(int wmPoiId) throws TException {
        try {
            WmC2ContractBasicBo contractBasicBo = wmC2ContractAuditedThriftService
                    .getC2ContractBasicAuditedByWmPoiId(wmPoiId);
            return contractBasicBo == null ? null : Integer.valueOf(contractBasicBo.getType());
        } catch (com.sankuai.meituan.waimai.thrift.exception.WmServerException e) {
            logger.error(e.getMsg(), e);
        }
        return null;
    }

    private List<Integer> getAgentIdsByPoiId(long wmPoiId) {
        List<Integer> agentIds = null;
        try {
            agentIds = wmContractAgentService.getAgentIdByWmPoiId(wmPoiId);
        } catch (Exception e) {
            logger.error("WmCustomerContractThriftServiceImpl#getAgentIdsByPoiId, error", e);
        }
        return agentIds;
    }

    private Integer getC1ContractSignType(long wmPoiId)
            throws WmCustomerException, TException {
        if (ConfigUtilAdapter.getBoolean(show3_0PdfForPoiSwitch, true)) {
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
            if (wmCustomerDB != null && wmCustomerDB.getEffective() == 1) {
                List<WmTempletContractBasicBo> contractBasicBos = wmContractService
                        .selectAuditedContractBasicByParentIdAndTypes(wmCustomerDB.getId().longValue(),
                                Lists.newArrayList(
                                        WmTempletContractTypeEnum.C1_E.getCode(),
                                        WmTempletContractTypeEnum.C1_PAPER.getCode()
                                ));
                if (CollectionUtils.isNotEmpty(contractBasicBos)) {
                    logger.debug("存在3.0生效C1合同  templetId:{}", contractBasicBos.get(0).getTempletContractId());
                    return new WmTempletContractTypeBo(contractBasicBos.get(0).getType()).getSignType();
                }
            }
        }
        logger.debug("不存在3.0生效C1合同 wmPoiId:{}", wmPoiId);
        return get2_0C1ContractSignType((int) wmPoiId);
    }

    private Integer get2_0C1ContractSignType(int wmPoiId) throws TException {
        try {
            if (ConfigUtilAdapter.getBoolean("getWmContractAuditedByWmPoiId_offline", false)) {
                return null;
            } else {
                WmContractAudited contractAudited = wmContractThriftService.getWmContractAuditedByWmPoiId(wmPoiId);
                return contractAudited == null ? null : Integer.valueOf(contractAudited.getType());
            }
        } catch (com.sankuai.meituan.waimai.thrift.exception.WmServerException e) {
            logger.error(e.getMsg(), e);
        }
        return null;
    }

    /**
     * 3.0的门店如果是纸质合同不返回PDF
     *
     * @param wmPoiId
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public String getC1PaperContractPdf(long wmPoiId, int opUid, String opName) throws WmCustomerException, TException {
        //此方法无DDD实现
        if (ConfigUtilAdapter.getBoolean(show3_0PdfForPoiSwitch, true)) {
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
            if (wmCustomerDB != null && wmCustomerDB.getEffective() == 1) {
                List<WmTempletContractBasicBo> contractBasicBos = wmContractService
                        .selectAuditedContractBasicByParentIdAndTypes(wmCustomerDB.getId().longValue(),
                                Lists.newArrayList(
                                        WmTempletContractTypeEnum.C1_PAPER.getCode()
                                ));
                if (CollectionUtils.isNotEmpty(contractBasicBos)) {
                    logger.debug("存在3.0生效纸质合同  templetId:{}", contractBasicBos.get(0).getTempletContractId());
                    return null;
                }
            }
        }
        try {
            //这里发现一个隐藏很多年的bug，底层如果是上单3.0的门店，则不会查询pdfurl，因此这里返回始终都是空字符串。
            logger.debug("不存在3.0生效纸质合同  wmPoiId:{}", wmPoiId);
            if (ConfigUtilAdapter.getBoolean("offline_get_pdfurl", true)) {
                return StringUtils.EMPTY;
            } else {
                return wmContractAuditManagerThriftService.getPdfUrl((int) wmPoiId);
            }
        } catch (com.sankuai.meituan.waimai.thrift.exception.WmServerException e) {
            logger.error(e.getMsg(), e);
        }
        return null;
    }

    @Override
    public Boolean invalidContract(int customerId, List<Long> poiIds, int opUid, String opName)
            throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return WmContractAggre.Factory.makeWithCustomerId(customerId).invalidContract(poiIds, opUid, opName);
        }
        logger.info("删除合同  客户id：{} opUid:{} opUname:{}", customerId, opUid, opName);
        return wmContractService.invalidContract(customerId, poiIds, opUid, opName);
    }


    @Override
    public void logPoiProduceForPoiDEL(int customerId, List<Long> poiIds, int opUid, String opUname) {
        //此方法未使用DDD实现
        logger.info("删除门店，通知商家合同状态，客户id：{} poiIds：{} opUid:{} opUname:{}", customerId, poiIds, opUid, opUname);
        wmContractPoiProduceService.logPoiProduceForPoiDEL(customerId, poiIds, opUid, opUname);
    }

    @Override
    public List<Long> getWmPoiIdByCheckExistWmPoiBindWmContract(List<Long> wmPoiIds, int opUid, String opUname) throws WmCustomerException, TException {
        //此方法未使用DDD实现
        Set<Integer> wmCustomerIds = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiIds));
        //不存在3.0客户
        if (CollectionUtils.isEmpty(wmCustomerIds)) {
            return wmPoiIds;
        }

        List<Long> wmCustomerIdList = Lists.transform(Lists.newArrayList(wmCustomerIds), new Function<Integer, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable Integer input) {
                return input.longValue();
            }
        });
        List<WmTempletContractDB> templetContractDBList = wmTempletContractAuditedDBMapper.selectByParentIdsAndTypes(wmCustomerIdList,
                Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(), WmTempletContractTypeEnum.C1_PAPER.getCode()));
        //不存在3.0合同
        if (CollectionUtils.isEmpty(templetContractDBList)) {
            return wmPoiIds;
        }

        List<Long> existContractCustomerIdList = Lists.newArrayList();
        for (WmTempletContractDB wmTempletContractDB : templetContractDBList) {
            existContractCustomerIdList.add(wmTempletContractDB.getParentId());
        }
        List<Long> existContractWmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIds(Lists.transform(existContractCustomerIdList, new Function<Long, Integer>() {
            @Nullable
            @Override
            public Integer apply(@Nullable Long input) {
                return input.intValue();
            }
        }));

        wmPoiIds.removeAll(existContractWmPoiIdList);

        logger.info("wmPoiIds = {}", wmPoiIds);
        return wmPoiIds;
    }

    @Override
    public WmTempletContractBasicBo getSubsidiaryContract(int customerId, int subsidiaryType, int opUid, String opUname)
            throws WmCustomerException, TException {
        //此方法无DDD实现
        logger.info("获取附属合同类型  customerId:{} subsidiaryType:{} opUid:{} opUname:{}", customerId, subsidiaryType, opUid, opUname);
        WmTempletContractTypeEnum templetContractTypeEnum = getWmTempletContractTypeEnum(subsidiaryType);
        List<WmTempletContractDB> contractDBList = wmTempletContractDBMapper
                .selectByParentIdAndType((long) customerId, templetContractTypeEnum.getCode());
        if (CollectionUtils.isEmpty(contractDBList)) {
            return null;
        }
        return WmTempletContractTransUtil.templetContractBasicDbToBo(contractDBList.get(0));
    }

    private WmTempletContractTypeEnum getWmTempletContractTypeEnum(int subsidiaryType) throws WmCustomerException {
        WmTempletContractTypeEnum templetContractTypeEnum = null;
        if (subsidiaryType == WmTempletContractTypeEnum.EXCLUSIVE_AGREEMENT_PAPER.getCode() % 100) {
            templetContractTypeEnum = WmTempletContractTypeEnum.EXCLUSIVE_AGREEMENT_PAPER;
        }
        if (subsidiaryType == WmTempletContractTypeEnum.DELIVERY_PAPER.getCode() % 100) {
            templetContractTypeEnum = WmTempletContractTypeEnum.DELIVERY_PAPER;
        }
        if (templetContractTypeEnum == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,
                    "不支持的附属合同类型（" + subsidiaryType + "）");
        }
        return templetContractTypeEnum;
    }

    @Override
    public void effectiveSubsidiaryContract(int customerId, int subsidiaryType, int opUid, String opUname)
            throws WmCustomerException, TException {
        //此方法没有DDD实现
        logger.info("附属合同生效  customerId:{} subsidiaryType:{} opUid:{} opUname:{}", customerId, subsidiaryType, opUid, opUname);
        WmTempletContractBasicBo subsidiaryContract = getSubsidiaryContract(customerId, subsidiaryType, opUid, opUname);
        if (subsidiaryContract == null) {
            logger.warn("附属合同已经被删除或者不存在， customerId:{}, subsidiaryType：{} opUid：{} opUname：{}", customerId, subsidiaryType, opUid, opUname);
            return;
        }
        getService(subsidiaryContract.getType())
                .effect(subsidiaryContract.getTempletContractId(), opUid, opUname);
    }

    @Override
    public List<WmTempletContractBasicBo> getContractBoListByCusIdAndType(int customerId, List<Integer> types, int opUid, String opName) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return WmContractAggre.Factory.makeWithCustomerId(customerId).getContractBasicBoListByCusIdAndType(types, opUid, opName);
        }
        return wmContractService.getContractBasicBoListByCusIdAndType(customerId, types, opUid, opName);
    }

    @Override
    public Integer createWmCustomerContract(CreateWmCustomerContractReq req) throws WmCustomerException, TException {
        return wmContractService.createWmCustomerContract(req);
    }

    @Override
    public List<WmTempletContractBasicBo> getAuditedContractBasicBoListByCusIdAndType(int customerId,
                                                                                      List<Integer> types, int opUid, String opName) throws WmCustomerException, TException {
        return wmContractService.getAuditedContractBasicBoListByCusIdAndType(customerId, types, opUid, opName);
    }

    @Override
    public List<WmCustomerContractBo> getAuditedContractBoListByCusIdAndTypeRT(long customerId, List<Integer> types, int opUid, String opName) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.getAuditedContractBoListByCusIdAndTypeRT(customerId, types, opUid, opName);
        }
        return wmContractService.getAuditedContractBoListByCusIdAndTypeRT(customerId, types, opUid, opName);
    }

    @Override
    public void cancelC1SignByWaitingSign(Integer customerId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            wmContractWriteDomainService.cancelC1SignByWaitingSign(customerId, failReason, opUid, opUname);
            return;
        }
        wmContractService.cancelC1SignByWaitingSign(customerId, failReason, opUid, opUname);
    }

    @Override
    public void cancelSignByWaitingSign(Integer customerId, Long contractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        wmContractService.cancelSignByWaitingSign(customerId, contractId, failReason, opUid, opUname);
    }

    @Override
    public List<WmTempletContractBasicBo> getBasicListWhenDueDateEquals(int unixTime, int startPageNum, int pageSize) throws WmCustomerException, TException {
        //此方法无DDD实现
        return wmContractService.getBasicListWhenDueDateEquals(unixTime, (startPageNum - 1) * pageSize, pageSize);
    }

    @Override
    public Boolean needC2ForPoiOnline(Long wmPoiId) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.needC2ForPoiOnline(wmPoiId);
        }
        return wmContractOnlineService.needC2ForPoiOnline(wmPoiId);
    }

    @Override
    public Map<Long, Boolean> needC2ForPoisOnline(List<Long> wmPoiIds) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.needC2ForPoisOnline(wmPoiIds);
        }
        return wmContractOnlineService.needC2ForPoisOnline(wmPoiIds);
    }

    @Override
    public List<WmCustomerContractBo> getCusContractBoListByCusIdAndType(int customerId, List<Integer> types, int opUid, String opName) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.getCusContractBoListByCusIdAndType(customerId, types, opUid, opName);
        }
        return wmContractService.getCusContractBoListByCusIdAndType(customerId, types, opUid, opName);
    }

    @Override
    public Boolean hasAuditedC2ContractForAgentAndWmPoi(long wmPoiId, int agentId) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.hasAuditedC2ContractForAgentAndWmPoi(wmPoiId, agentId);
        }
        return wmContractService.hasAuditedC2ContractForAgentAndWmPoi(wmPoiId, agentId);
    }

    @Override
    public Map<Long, Boolean> hasAuditedC2ContractForAgentAndWmPoiBatch(Map<Long, Integer> wmPoiIdAndAgentIdMap) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.hasAuditedC2ContractForAgentAndWmPoiBatch(wmPoiIdAndAgentIdMap);
        }
        return wmContractService.hasAuditedC2ContractForAgentAndWmPoiBatch(wmPoiIdAndAgentIdMap);
    }

    @Override
    public void invalidC2ContractForAgentAndWmPoi(long wmPoiId, int agentId, int opUid, String opUname) throws WmCustomerException, TException {

        if (DDDGrayUtil.useNewService()) {
            wmContractWriteDomainService.invalidC2ContractForAgentAndWmPoi(wmPoiId, agentId, opUid, opUname);
            return;
        }
        wmContractService.invalidC2ContractForAgentAndWmPoi(wmPoiId, agentId, opUid, opUname);
    }

    @Override
    public void invalidC2ContractForContract(long contractId, int opUid, String opUname) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            wmContractWriteDomainService.invalidC2ContractForContract(contractId, opUid, opUname);
            return;
        }
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidBusinessCustomerContract(long contractId, int opUid, String opUname) throws WmCustomerException, TException {
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidGroupMealContract(long contractId, int opUid, String opUname) throws WmCustomerException, TException {
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidBagServiceContract(long contractId, int opUid, String opUname) throws WmCustomerException,
            TException {
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidFoodcityStatement(long contractId, int opUid, String opUname) throws WmCustomerException,
            TException {
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidMedicOrderSplitStatement(long contractId, int opUid, String opUname) throws WmCustomerException, TException {
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidSubjectChangeSupplement(long contractId, int opUid, String opUname) throws WmCustomerException, TException {
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidFourWheelPerformanceSupportAgreement(long contractId, int opUid, String opUname) throws WmCustomerException, TException {
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidSpeedyDeliveryCooperationAgreement(long contractId, int opUid, String opUname) throws WmCustomerException, TException {
        wmContractValidCallbackService.invalid(contractId, opUid, opUname);
    }

    @Override
    public void invalidConfigContract(InvalidContractRequestDTO requestDTO) throws WmCustomerException, TException {
        wmContractValidCallbackService.invalidConfigContract(requestDTO);
    }

    @Override
    public List<Integer> getC2PoiIdListByWmAgentId(int agentId) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.getC2PoiIdListByWmAgentId(agentId);
        }
        return wmContractService.getC2PoiIdListByWmAgentId(agentId);
    }

    @Override
    public LongResult getC1ContractStartSignTime(long wmPoiId) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return contractReadDomainService.getC1ContractStartSignTime(wmPoiId);
        }
        return wmContractService.getC1ContractStartSignTime(wmPoiId);
    }

    @Override
    public OpResultBo saveAndSignContract(long wmPoiId, int type, int signSubjectCode, int opUid, String opName) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            OpResultBo opResultBo = wmContractWriteDomainService.saveAndSignContract(wmPoiId, type, signSubjectCode, opUid, opName);
            logger.info("opResultBo = {}", JSON.toJSONString(opResultBo));
            return opResultBo;
        }
        OpResultBo opResultBo = wmContractService.saveAndSignContract(wmPoiId, type, signSubjectCode, opUid, opName);
        logger.info("opResultBo = {}", JSON.toJSONString(opResultBo));
        return opResultBo;
    }

    @Override
    public long getLatestC1ClosingDate(Set<Long> customerIdSet, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("getLatestC1ClosingDate(), customerIdSet={}, opUid={}, opName={}", JSONObject.toJSONString(customerIdSet), opUid, opName);
        long retVal = 0;
        if (CollectionUtils.isEmpty(customerIdSet)) {
            return retVal;
        }
        retVal = wmContractService.getLatestC1ClosingDate(customerIdSet);
        logger.info("getLatestC1ClosingDate(),customerIdSet={},retVal={}", customerIdSet, retVal);
        return retVal;

    }

    @Override
    public void executeContractToEffect(int unixTime) {
        wmContractService.executeContractToEffect(unixTime);
    }

    @Override
    public Map<Long, WmCustomerContractBo> getWmCustomerContractBoByWmPoiIds(List<Long> wmPoiIds, List<Integer> types) throws WmCustomerException, TException {
        logger.info("getWmCustomerContractBoByWmPoiIds wmPoiIds = {}, types = {}", JSON.toJSONString(wmPoiIds), JSON.toJSONString(types));
        if (wmPoiIds != null && wmPoiIds.size() > MccConfig.getWmPoiIdNum_For_BatchQueryCustomerContractBoMap()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "批量查询不能大于" + MccConfig.getWmPoiIdNum_For_BatchQueryCustomerBoMap() + "个门店");
        }
        return wmContractService.getWmCustomerContractBoByWmPoiIds(wmPoiIds, types);
    }

    @Override
    public void batchSwitchPoiSubject(String wmPoiIds, Integer effectTime) throws WmCustomerException, TException {
        logger.info("batchSwitchPoiSubject wmPoiIds={},effectTime={}", wmPoiIds, effectTime);
        wmContractService.batchSwitchPoiSubject(wmPoiIds, effectTime);
    }

    @Override
    public void batchAddWmCustomerPoiLogisticsSubjectWithTag(List<Long> wmPoiIds, int tagId,
                                                             int effectTime) throws WmCustomerException, TException {
        logger.info("batchAddWmCustomerPoiLogisticsSubjectWithTag wmPoiIds={},tagId={},effectTime={}", wmPoiIds, tagId, effectTime);
        wmContractService.batchAddWmCustomerPoiLogisticsSubjectWithTag(wmPoiIds, tagId, effectTime);
    }

    @Override
    public WmPoiSignSubjectBo getCustomerPoiSignSubject(int customerId, int contractType) throws WmCustomerException, TException {
        return wmContractService.getSignSubjectBo(customerId, contractType);
    }

    @Override
    public Boolean checkSubjectConsistency(int customerId, long wmPoiId, int partB, int partLogistics) throws WmCustomerException, TException {
        return wmContractService.checkSubjectConsistency(customerId, wmPoiId, partB, partLogistics);
    }

    @Override
    public Boolean hasAuditedC2Contract(long wmPoiId, int agentId) throws WmCustomerException, TException {
        return wmContractService.hasAuditedC2Contract(wmPoiId, agentId);
    }

    @Override
    public BooleanResult fixSubject(WmCustomerPoiLogisticsSubjectBo wmCustomerPoiLogisticsSubjectBo, Integer opId,
                                    String opName) throws TException, WmCustomerException {
        logger.info("fixSubject wmCustomerPoiLogisticsSubjectBo={},opId={},opName={}",
                JSON.toJSONString(wmCustomerPoiLogisticsSubjectBo),
                opId,
                opName);
        return wmContractService.fixSubject(wmCustomerPoiLogisticsSubjectBo);
    }

    @Override
    public Map<Long, CustomerContractStatus> queryInterimSelfAgreementStatusByWmPoiIds(List<Long> wmPoiIdList) throws WmCustomerException, TException {
        logger.info("#queryInterimSelfAgreementStatusByWmPoiIds wmPoiIdList={}", wmPoiIdList);
        return wmContractService.queryContractStatusByWmPoiIdAndType(wmPoiIdList,
                WmTempletContractTypeEnum.INTERIM_SELF_E.getCode());
    }

    @Override
    public CommonResult applyInterimSelfAgreementByWmPoiId(Long wmPoiId, Long bizId) throws WmCustomerException,
            TException {
        logger.info("#applyInterimSelfAgreementByWmPoiId wmPoiId={}, bizId={}", wmPoiId, bizId);
        return wmContractService.applyInterimSelfAgreementByWmPoiId(wmPoiId, bizId);
    }

    @Override
    public ContractQueryResp getAuditedContractBoListByCusIdAndType(ContractQueryReqParam reqParam) throws WmCustomerException, TException {
        logger.info("#getAuditedContractBoListByCusIdAndType reqParam={}", JSON.toJSONString(reqParam));
        AssertUtil.assertLongMoreThan0(reqParam.getWmPoiId(), "门店id");
        AssertUtil.assertCollectionNotEmpty(reqParam.getTypes(), "合同类型");
        //门店转客户
        Integer wmCustomerId = wmLeafCustomerRealService.selectWmCustomerIdByWmPoiId(reqParam.getWmPoiId());
        if (wmCustomerId == null) {
            logger.info("#getAuditedContractBoListByCusIdAndType 门店未关联客户, 门店ID:{}", reqParam.getWmPoiId());
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_BUSINESS_ERROR, "门店未关联有效客户");
        }
        try {
            //查询合同
            List<WmCustomerContractBo> contractList = wmContractService.getAuditedContractBoListByCusIdAndType(wmCustomerId, reqParam.getTypes(), 0, "");
            ContractQueryResp resp = ContractQueryResp.builder().contractList(CollectionUtils.isEmpty(contractList) ? Lists.newArrayList() : contractList).build();
            logger.info("#getAuditedContractBoListByCusIdAndType response={}", JSON.toJSONString(resp));
            return resp;
        } catch (Exception e) {
            logger.error("#getAuditedContractBoListByCusIdAndType error, reqParam={}", JSON.toJSONString(reqParam), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询合同异常");
        }
    }

    @Override
    public BaseResponse<Boolean> invalidCustomerContract(CustomerContractInvalidRequestDTO requestDTO) {
        return wmContractService.invalidCustomerContract(requestDTO);
    }

    @Override
    public Map<Integer, Boolean> expireCustomerContract(CustomerContractExpireRequestDTO requestDTO) throws WmCustomerException {
        return wmContractService.expireCustomerContract(requestDTO);
    }
}
