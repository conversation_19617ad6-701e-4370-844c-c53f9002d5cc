package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CChargeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDeliveryTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.joda.time.DateTime;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 拼好饭PDF数据组装类
 * @author: zhangyuanhao02
 * @create: 2024/12/19 19:40
 */
@Slf4j
public abstract class AbstarctWmEcontractPhfDataWrapperService {

    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 生成pdfMetaContentMap
     * @param customerInfoBo
     * @param infoBo
     * @return
     * @throws WmCustomerException
     */
    abstract public  Map<String, String> generatePdfMetaContent(EcontractCustomerInfoBo customerInfoBo,
                                                                EcontractDeliveryPhfInfoBo infoBo) throws WmCustomerException;

    /**
     * 填充基础信息
     * @param pdfMap
     * @param customerInfoBo
     * @param infoBo
     */
    public void fillBaseInfo(Map<String, String> pdfMap, EcontractCustomerInfoBo customerInfoBo, EcontractDeliveryPhfInfoBo infoBo) {
        String signTime = dateToStr(new Date(), DATE_FORMAT);

        // 商家主体
        pdfMap.put("partAName", customerInfoBo.getCustomerName());
        // 商家签章
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        // 商家签约日期
        pdfMap.put("partASignTime", signTime);

        // 美团北京主体
        pdfMap.put("partBName", ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc());
        // 美团北京签章
        pdfMap.put("partBEstamp", PdfConstant.MT_SIGNKEY);
        // 美团北京签约日期
        pdfMap.put("partBSignTime", signTime);

        // 美团上海主体
        pdfMap.put("partCName", ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc());
        // 美团上海签章
        pdfMap.put("partCEstamp", PdfConstant.MT_SH_SIGNKEY);
        // 美团上海签约日期
        pdfMap.put("partCSignTime", signTime);

        pdfMap.put("wmPoiName", infoBo.getWmPoiName());
        pdfMap.put("wmPoiId", String.valueOf(infoBo.getWmPoiId()));

        // 全局合同使用
        String wmPoiInfo = infoBo.getWmPoiName() + " ID " + infoBo.getWmPoiId();
        pdfMap.put("poiInfo", StringUtils.defaultIfEmpty(wmPoiInfo, ""));
    }

    /**
     * 填充技术服务费
     * @param pdfMap
     * @param infoBo
     */
    public void fillTechFeeInfo(Map<String, String> pdfMap, EcontractDeliveryPhfInfoBo infoBo) throws WmCustomerException {
        Map<Integer, EcontractDeliveryPhfTechInfoBo> techInfo = infoBo.getTechInfo();
        Set<Map.Entry<Integer, EcontractDeliveryPhfTechInfoBo>> entries = techInfo.entrySet();

        log.info("AbstarctWmEcontractPhfDataWrapperService#fillTechFeeInfo infoBo:{}", JSONObject.toJSONString(infoBo));
        for (Map.Entry<Integer, EcontractDeliveryPhfTechInfoBo> entry : entries) {
            EcontractDeliveryPhfTechInfoBo techInfoBo = entry.getValue();
            EcontractDeliveryTypeEnum typeEnum = EcontractDeliveryTypeEnum.getByType(entry.getKey());
            if (typeEnum == null) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "拼好饭合同技术费率类型异常");
            }

            String logiticsType = getLogiticsType(typeEnum);
            // 配送方式名称
            pdfMap.put(logiticsType + "_logisticsType", getLogisticsDesc(typeEnum));
            // 基础收费
            pdfMap.put(logiticsType + "_techServiceFee_base", StringUtils.defaultIfEmpty(techInfoBo.getBaseFee(),StringUtils.EMPTY));
            // 基础收费补贴
            pdfMap.put(logiticsType + "_techServiceFee_baseSubsidy", StringUtils.defaultIfEmpty(techInfoBo.getBaseSubsidy(),StringUtils.EMPTY));
        }
    }

    /**
     * 填充履约服务费
     * @param pdfMap
     * @param infoBo
     */
    public void fillPerformanceFeeInfo(Map<String, String> pdfMap, EcontractDeliveryPhfInfoBo infoBo) throws WmCustomerException {
        Map<Integer, EcontractDeliveryPhfPerInfoBo> perInfo = infoBo.getPerInfo();
        Set<Map.Entry<Integer, EcontractDeliveryPhfPerInfoBo>> entries = perInfo.entrySet();

        log.info("AbstarctWmEcontractPhfDataWrapperService#fillPerformanceFeeInfo infoBo:{}", JSONObject.toJSONString(infoBo));
        for (Map.Entry<Integer, EcontractDeliveryPhfPerInfoBo> entry : entries) {
            EcontractDeliveryTypeEnum typeEnum = EcontractDeliveryTypeEnum.getByType(entry.getKey());
            if (typeEnum == null) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "拼好饭合同履约费率类型异常");
            }

            String logiticsType = getLogiticsType(typeEnum);
            EcontractDeliveryPhfPerInfoBo perInfoBo = entry.getValue();
            wrapPerFeeInfo(pdfMap, logiticsType, perInfoBo);
        }
    }

    public void wrapPerFeeInfo(Map<String, String> pdfMap, String logiticsType, EcontractDeliveryPhfPerInfoBo perInfoBo) throws WmCustomerException {
        Map<String, EcontractdeliveryPhfChargeInfoBo> chargePerInfos = perInfoBo.getChargePerInfo();
        Set<Map.Entry<String, EcontractdeliveryPhfChargeInfoBo>> entries = chargePerInfos.entrySet();

        for (Map.Entry<String, EcontractdeliveryPhfChargeInfoBo> entry : entries) {
            CChargeTypeEnum cChargeTypeEnum = CChargeTypeEnum.findByCode(entry.getKey());
            if (cChargeTypeEnum == null) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "拼好饭合同履约服务费类型异常");
            }

            String preKey = logiticsType + "_" + entry.getKey();
            StringBuilder otherFee = new StringBuilder();
            EcontractdeliveryPhfChargeInfoBo perChargeInfoBo = entry.getValue();

            // 履约服务费基础收费
            pdfMap.put(preKey + "_performanceServiceFee_base", StringUtils.defaultIfEmpty(perChargeInfoBo.getBaseFee(),StringUtils.EMPTY));

            // 履约服务费季节收费
            String seasonFee = perChargeInfoBo.getSeasonFee();
            pdfMap.put(preKey + "_performanceServiceFee_season", StringUtils.defaultIfEmpty(seasonFee, StringUtils.EMPTY));
            if (StringUtils.isNotEmpty(seasonFee)) {
                otherFee.append("+季节收费").append("+");
            }

            // 履约服务费时段收费
            String timeFee = perChargeInfoBo.getTimeFee();
            pdfMap.put(preKey + "_performanceServiceFee_timeInterval", StringUtils.defaultIfEmpty(timeFee, StringUtils.EMPTY));
            if (StringUtils.isNotEmpty(timeFee)) {
                otherFee.append("特殊时段收费").append("+");
            }

            // 履约服务费距离收费
            String distanceFee = perChargeInfoBo.getDistanceFee();
            pdfMap.put(preKey + "_performanceServiceFee_distance", StringUtils.defaultIfEmpty(distanceFee, StringUtils.EMPTY));
            if (StringUtils.isNotEmpty(distanceFee)) {
                otherFee.append("远距离收费").append("+");
            }

            //履约服务费补贴
            String baseSubsidy = perChargeInfoBo.getBaseSubsidy();
            pdfMap.put(preKey + "_performanceServiceFee_baseSubsidy", StringUtils.defaultIfEmpty(baseSubsidy, StringUtils.EMPTY));

            if (otherFee.length() > 0 && otherFee.charAt(otherFee.length() - 1) == '+') {
                otherFee.deleteCharAt(otherFee.length() - 1);
            }

            pdfMap.put(preKey + "_performanceServiceFee_other", StringUtils.defaultIfEmpty(otherFee.toString(), StringUtils.EMPTY));

            // 履约服务费封顶值
            pdfMap.put(preKey + "_performanceServiceFee_top", StringUtils.defaultIfEmpty(perChargeInfoBo.getTopFee(), StringUtils.EMPTY));
        }
    }

    public static String dateToStr(Date date, String format) {
        if (date == null || Strings.isNullOrEmpty(format)) {
            return StringUtils.EMPTY;
        }
        DateTime dateTime = new DateTime(date);
        return dateTime.toString(format);
    }

    /**
     * 返回拼好饭配送方式，与合同模版保持一致
     * @param deliveryTypeEnum
     * @return
     */
    public static String getLogiticsType(EcontractDeliveryTypeEnum deliveryTypeEnum) {
        if (deliveryTypeEnum == EcontractDeliveryTypeEnum.PHF_SELF) {
            return "SELF";
        } else if (deliveryTypeEnum == EcontractDeliveryTypeEnum.PHF_SCHOOL_AGGR) {
            return "SCHOOL_AGGR";
        } else if (deliveryTypeEnum == EcontractDeliveryTypeEnum.PHS_VIRTUAL) {
            return "VIRTUAL_PHS";
        } else if (deliveryTypeEnum == EcontractDeliveryTypeEnum.JHS) {
            return "JHS";
        } else if (deliveryTypeEnum == EcontractDeliveryTypeEnum.PHS_FORMAL) {
            return "PHS";
        }

        return StringUtils.EMPTY;
    }

    /**
     * 返回拼好饭配送方式描述
     * @param deliveryTypeEnum
     * @return
     */
    public static String getLogisticsDesc(EcontractDeliveryTypeEnum deliveryTypeEnum) {
        if (deliveryTypeEnum == EcontractDeliveryTypeEnum.PHF_SELF) {
            return "商家自配";
        } else if (deliveryTypeEnum == EcontractDeliveryTypeEnum.PHF_SCHOOL_AGGR) {
            return "校园聚合配";
        } else if (deliveryTypeEnum == EcontractDeliveryTypeEnum.PHS_VIRTUAL) {
            return "拼好送基础版";
        } else if (deliveryTypeEnum == EcontractDeliveryTypeEnum.JHS) {
            return "聚好送";
        } else if (deliveryTypeEnum == EcontractDeliveryTypeEnum.PHS_FORMAL) {
            return "拼好送正式版";
        }

        return StringUtils.EMPTY;
    }

    /**
     * 提取门店信息分组列表
     * @param econtractDeliveryInfoBoList
     * @param wmPoiIdGroupList
     * @return
     */
    public List<List<EcontractDeliveryInfoBo>> extractWmPoiInfoBoGroupList(List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList, List<List<Long>> wmPoiIdGroupList) throws WmCustomerException {
        List<List<EcontractDeliveryInfoBo>> wmPoiInfoBoGroupList = new ArrayList<>();
        Map<String, EcontractDeliveryInfoBo> wmPoiId2InfoBoMap = econtractDeliveryInfoBoList.stream()
                .collect(Collectors.toMap(EcontractDeliveryInfoBo::getWmPoiId, Function.identity()));

        for (List<Long> wmPoiIdList : wmPoiIdGroupList) {
            List<EcontractDeliveryInfoBo> wmPoiInfoBoList = new ArrayList<>();
            for (Long wmPoiId : wmPoiIdList) {
                if (!wmPoiId2InfoBoMap.containsKey(String.valueOf(wmPoiId))) {
                    log.error("extractWmPoiInfoBoGroupList wmPoiId:{}, 未找到对应的InfoBo", wmPoiId);
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "原始数据缺失，pdf封装失败");
                }

                wmPoiInfoBoList.add(wmPoiId2InfoBoMap.get(String.valueOf(wmPoiId)));
            }

            wmPoiInfoBoGroupList.add(wmPoiInfoBoList);
        }

        log.info("extractWmPoiInfoBoGroupList wmPoiInfoBoGroupList:{}", JSONObject.toJSONString(wmPoiInfoBoGroupList));
        return wmPoiInfoBoGroupList;
    }

    /**
     * 填充WmPoiId和wmPoiInfo（全局合同使用）
     * @param pdfMap
     * @param wmPoiInfoBoList
     */
    public void fillWmPoiIdAndInfo(Map<String, String> pdfMap, List<EcontractDeliveryInfoBo> wmPoiInfoBoList) {
        if (Objects.isNull(pdfMap) || CollectionUtils.isEmpty(wmPoiInfoBoList)) {
            return;
        }
        log.info("fillWmPoiIdAndInfo start pdfMap:{}", JSONObject.toJSONString(pdfMap));
        List<String> wmPoiIds = new ArrayList<>();
        List<String> poiInfoList = new ArrayList<>();

        for (int i = 0;i < wmPoiInfoBoList.size();i ++) {
            EcontractDeliveryInfoBo econtractDeliveryInfoBo = wmPoiInfoBoList.get(i);
            EcontractDeliveryPhfInfoBo phfInfoBo = econtractDeliveryInfoBo.getEcontractDeliveryPhfInfoBo();

            wmPoiIds.add(phfInfoBo.getWmPoiId());
            String poiInfo = phfInfoBo.getWmPoiName() + " ID " + econtractDeliveryInfoBo.getWmPoiId();
            poiInfoList.add(poiInfo);
        }

        pdfMap.put("wmPoiId", Joiner.on(",").join(wmPoiIds));
        pdfMap.put("poiInfo", Joiner.on(",").join(poiInfoList));
        log.info("fillWmPoiIdAndInfo end pdfMap:{}", JSONObject.toJSONString(pdfMap));
    }
}
