package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;


import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBagServiceInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.BAG_SERVICE_E})
public class WmBagServiceEContractTempletAtomService extends AbstractWmEContractTempletAtomService {

    private static Logger logger = LoggerFactory.getLogger(WmBagServiceEContractTempletAtomService.class);

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.BAG_SERVICE);

        EcontractBagServiceInfoBo bagServiceInfoBo = new EcontractBagServiceInfoBo();

        WmTempletContractSignBo partyASignerBo = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyASignerBo();
        bagServiceInfoBo.setPartAName(partyASignerBo.getSignName());
        String signTime = partyASignerBo.getSignTime();
        if (StringUtils.isNotEmpty(signTime)) {
            bagServiceInfoBo.setSignTime(partyASignerBo.getSignTime());
        } else {
            bagServiceInfoBo.setSignTime(DateUtil.secondsToString(DateUtil.unixTime()));
        }


        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(bagServiceInfoBo));
        return applyBo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String bagServiceENum = ContractNumberUtil.genBagServiceENum(insertId);
        contractBo.getBasicBo().setContractNum(bagServiceENum);
        logger.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, bagServiceENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), bagServiceENum);
        return insertId;
    }

}
