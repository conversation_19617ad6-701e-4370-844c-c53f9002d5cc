package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.flow;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiRelConstants;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindSignNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.bind.SignFailBindRule;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.bind.SignSucBindRule;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.jeasy.rules.api.RulesEngineParameters;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240117
 * @desc 预绑定签约回调通知流程服务
 */
@Service
@Slf4j
public class BindSignNoticeFlowService {
    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    /**
     * 绑定签约回调使用的rule引擎
     */
    private static volatile Rules bindSignNoticeRules = null;

    private static final Object lockObject = new Object();

    /**
     * 根据流程以及规则的绑定流程
     *
     * @param smsRecordDB
     * @param callBackResult
     */
    public void signBindNoticeFlow(WmCustomerPoiSmsRecordDB smsRecordDB, Integer callBackResult)
            throws WmCustomerException, TException {

        //步骤1：构建上下文
        CustomerPoiBindFlowContext context = buildSignBindCallContext(smsRecordDB, callBackResult);
        //步骤2：初始化构建绑定规则
        initPreBindNoticeRule();
        //步骤3：执行绑定签约回调规则
        executeBindSignNoticeRule(context);
        //步骤4：获取规则返回的策略并执行
        List<BindFlowStrategy> bindFlowStrategyList = context.getBindFlowStrategyList();
        //步骤4.1 如果匹配到有效策略则根据策略开始执行
        if (checkMatchBindSignCallStrategy(bindFlowStrategyList)) {
            log.info("signBindNoticeFlow,根据规则匹配到有效绑定签约回调策略,bindFlowStrategyList={}", JSON.toJSONString(bindFlowStrategyList));
            //遍历绑定流程策略列表，执行绑定规则策略。
            for (BindFlowStrategy bindFlowStrategy : bindFlowStrategyList) {
                BindStrategy poiRelFlowStrategy = bindFlowStrategy.getBindStrategy();
                poiRelFlowStrategy.execute(context);
            }
            return;
        }

        //步骤4.2 如果没匹配到有效策略则执行原流程，并告警
        log.info("signBindNoticeFlow,根据规则未匹配到有效绑定签约回调策略,执行原绑定签约回调流程,customerId={},wmPoiIdSet={}",
                context.getCustomerId(), JSON.toJSONString(context.getWmPoiIdSet()));
        //添加告警
        Cat.logEvent(CustomerPoiRelConstants.BIND_RULE_STRATEGY_EVENT, CustomerPoiRelConstants.BIND_SIGN_CALL_BACK_NO_MATCH_STRATEGY);
        //签约回调原处理逻辑
        executeOldBindSignCallBack(callBackResult, smsRecordDB.getTaskId());
    }


    /**
     * 构建绑定签约回调上下文
     *
     * @param smsRecordDB
     * @param callBackResult
     * @return
     * @throws WmCustomerException
     */
    private CustomerPoiBindFlowContext buildSignBindCallContext(WmCustomerPoiSmsRecordDB smsRecordDB,
                                                                Integer callBackResult) throws WmCustomerException {
        //构建绑定签约回调上下文
        CustomerPoiBindFlowContext context = CustomerPoiBindFlowContext.builder()
                .customerId(smsRecordDB.getCustomerId())
                .bindSignNoticeDTO(BindSignNoticeDTO.builder()
                        .signBindRelTaskId(smsRecordDB.getTaskId())
                        .signResult(callBackResult)
                        .wmCustomerPoiSmsRecordDB(smsRecordDB)
                        .build())
                .opUid(0)
                .opName("签约回调")
                .poiOplogSourceTypeEnum(WmCustomerPoiOplogSourceTypeEnum.PRE_BIND)
                .build();

        log.info("buildSignBindCallContext,预绑定签约回调通知根据规则判断执行流程,context={}", JSON.toJSONString(context));
        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(context.getCustomerId());
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID不存在");
        }

        //设置客户信息属性到上下文
        context.setWmCustomerDB(wmCustomerDB);
        //根据短信记录计算绑定的门店ID列表
        Set<Long> wmPoiIdSet = convertWmPoiIds(context.getBindSignNoticeDTO().getWmCustomerPoiSmsRecordDB().getWmPoiIds());
        context.setWmPoiIdSet(wmPoiIdSet);
        return context;
    }

    /**
     * 签约回调原处理逻辑
     *
     * @param callBackResult
     * @param taskId
     * @throws WmCustomerException
     * @throws TException
     */
    private void executeOldBindSignCallBack(Integer callBackResult, Long taskId) throws WmCustomerException, TException {
        if (callBackResult == EcontractTaskStateEnum.SUCCESS.getType()) {
            wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.SUCCESS.getType(), taskId);
            wmCustomerPoiService.confirmPreBind(taskId);
        } else if (callBackResult == EcontractTaskStateEnum.FAIL.getType()) {
            wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.FAIL.getType(), taskId);
            wmCustomerPoiService.cancelPreBind(taskId, CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT, CustomerTaskStatusEnum.FAIL.getCode());
        } else if (callBackResult == EcontractTaskStateEnum.CANCEL.getType()) {
            wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.CANCEL.getType(), taskId);
            wmCustomerPoiService.cancelPreBind(taskId, CustomerConstants.CUSTOMER_LOG_OP_NAME_BD, CustomerTaskStatusEnum.CANCEL.getCode());
        }
    }

    /**
     * 校验是否匹配到有效策略
     *
     * @param bindFlowStrategyList
     * @return
     */
    private boolean checkMatchBindSignCallStrategy(List<BindFlowStrategy> bindFlowStrategyList) {
        if (CollectionUtils.isEmpty(bindFlowStrategyList)) {
            return false;
        }
        //遍历绑定流程策略列表，执行绑定规则策略。
        for (BindFlowStrategy bindFlowStrategy : bindFlowStrategyList) {
            BindStrategy poiRelFlowStrategy = bindFlowStrategy.getBindStrategy();
            if (poiRelFlowStrategy == null || poiRelFlowStrategy.getBindCheckStrategy() == null) {
                return false;
            }
        }
        return true;
    }


    /**
     * 初始化绑定签约回调规则
     */
    private void initPreBindNoticeRule() {
        //规则非空则直接返回
        if (bindSignNoticeRules != null) {
            return;
        }
        //初始化注册规则-注册规则
        synchronized (lockObject) {
            if (bindSignNoticeRules != null) {
                return;
            }
            bindSignNoticeRules = new Rules();
            //预绑定通知成功-处理策略
            bindSignNoticeRules.register(new SignSucBindRule());
            //预绑定通知失败或取消-处理策略
            bindSignNoticeRules.register(new SignFailBindRule());
        }
    }

    /**
     * 执行绑定签约回调策略
     *
     * @param context
     */
    private void executeBindSignNoticeRule(CustomerPoiBindFlowContext context) {
        RulesEngineParameters parameters = new RulesEngineParameters().skipOnFirstAppliedRule(true);
        RulesEngine rulesEngine = new DefaultRulesEngine(parameters);
        //执行规则
        Facts facts = new Facts();
        facts.put("context", context);
        rulesEngine.fire(bindSignNoticeRules, facts);
    }

    /**
     * 字符串形式的wmPoiIds转成Set
     *
     * @param wmPoiIds
     * @return
     * @throws WmCustomerException
     */
    private Set<Long> convertWmPoiIds(String wmPoiIds) throws WmCustomerException {
        if (StringUtils.isEmpty(wmPoiIds)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "当前任务ID的门店为空");
        }
        String[] wmPoiIdArray = wmPoiIds.split(CustomerConstants.SPLIT_SYMBOL);
        Set<Long> wmPoiIdSet = Sets.newHashSet();
        for (String wmPoiId : wmPoiIdArray) {
            wmPoiIdSet.add(Long.parseLong(wmPoiId));
        }
        return wmPoiIdSet;
    }


}
