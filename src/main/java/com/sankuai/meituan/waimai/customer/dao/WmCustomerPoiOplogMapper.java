package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiOplogBo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiOplogDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmCustomerPoiOplogMapper {

    int insert(WmCustomerPoiOplogDB record);

    int batchInsert(@Param("list") List<WmCustomerPoiOplogDB> list);

    List<WmCustomerPoiOplogDB> selectByCondition(WmCustomerPoiOplogBo wmCustomerPoiOplogBo);

}