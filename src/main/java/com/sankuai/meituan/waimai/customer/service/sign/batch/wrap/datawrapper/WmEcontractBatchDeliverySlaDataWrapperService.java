package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.aspect.SlaDataWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.signTemplet.SignTempletManagerService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractSignTempletConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignVersionBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractWmPoiSpAreaBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@SlaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_DELIVERY)
public class WmEcontractBatchDeliverySlaDataWrapperService implements IWmEcontractSlaDataWrapperService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractBatchDeliverySlaDataWrapperService.class);

    public static final String SUPPORT_MARK = "support";

    @Autowired
    private SignTempletManagerService signTempletManagerService;

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws WmCustomerException {

        List<EcontractContentBo> result = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = null;

        try {
            econtractBatchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        } catch (Exception e) {
            LOGGER.warn("数据解析异常", e);
            return result;
        }

        if (econtractBatchDeliveryInfoBo == null) {
            return result;
        }

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = econtractBatchDeliveryInfoBo.getEcontractDeliveryInfoBoList();

        if (CollectionUtils.isEmpty(econtractDeliveryInfoBoList)) {
            return result;
        }

        EcontractSignVersionBo econtractSignVersionBo = contextBo.getEcontractSignVersionBo();
        String document = "";
        if (econtractSignVersionBo != null && StringUtils.isNotEmpty(econtractSignVersionBo.getDeliverySignVersion())) {
            document = signTempletManagerService.getDocument(EcontractSignTempletConstant.BIZ_TYPE_DELIVERY,
                    econtractSignVersionBo.getDeliverySignVersion());
        }

        EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBoTemp = null;
        for (EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {
            if (SUPPORT_MARK.equals(temp.getSupportSLA()) && StringUtils.isNotEmpty(temp.getDeliveryArea())) {
                econtractWmPoiSpAreaBoTemp = JSONArray.parseObject(temp.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
                if (econtractWmPoiSpAreaBoTemp == null) {
                    continue;
                }
                econtractWmPoiSpAreaBoTemp.setSlaText(document);
                result.add(WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(econtractWmPoiSpAreaBoTemp));
            }
        }
        return result;
    }
}