package com.sankuai.meituan.waimai.customer.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 外卖侧客户资质主体类型及个人证件类型
 *
 * <AUTHOR>
 * @since 15 八月 2022
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WmCustomerQuaTypeBo {

    /**
     * 资质主体类型
     */
    private Integer customerType;

    /**
     * 个人证件类型
     */
    private Integer customerSecondType;
}
