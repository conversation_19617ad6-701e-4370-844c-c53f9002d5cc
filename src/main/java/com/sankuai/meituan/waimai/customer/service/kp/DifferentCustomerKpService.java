package com.sankuai.meituan.waimai.customer.service.kp;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class DifferentCustomerKpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DifferentCustomerKpService.class);

    private static final Map<String, String> SIGNER_KP_UPDATE_FIELDS_MAP = Maps.newHashMap();

    private static final Map<String, String> KP_LEGAL_UPDATE_FIELDS_MAP = Maps.newHashMap();

    private static final Map<String, String> needPushMess4KpUpdateInfoMap = new HashMap<>();

    static {
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.SIGNER_TYPE, KpFields.SIGNER_TYPE_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.COMPELLATION, KpFields.COMPELLATION_DESC);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.CERT_TYPE, KpFields.CERT_TYPE_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.CERT_NUMBER, KpFields.CERT_NUMBER_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.PHONE_NUM, KpFields.PHONE_NUM_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.BANK_NAME, KpFields.BANK_NAME_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.CREDIT_CARD, KpFields.CREDIT_CARD_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.SPECIAL_ATTACHMENT, KpFields.SPECIAL_ATTACHMENT_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.AGENT_FRONT_IDCARD, KpFields.AGENT_FRONT_IDCARD_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.AGENT_BACK_IDCARD, KpFields.AGENT_BACK_IDCARD_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.EMAIL, KpFields.EMAIL_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.AGENT_AUTH, KpFields.AGENT_AUTH_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.VISIT_KP_BRAND_IDS, KpFields.VISIT_KP_BRAND_IDS_DES);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.VISIT_KP_PRO, KpFields.VISIT_KP_PRO_DESC);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.SIGN_TASK_TYPE,KpFields.SIGN_TASK_TYPE_DESC);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.LEGAL_IDCARD_COPY,KpFields.LEGAL_IDCARD_COPY_DESC);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.HAVE_AGENT_AUTH,KpFields.HAVE_AGENT_AUTH_DESC);
        SIGNER_KP_UPDATE_FIELDS_MAP.put(KpFields.LEGAL_AUTH_TYPE,KpFields.LEGAL_AUTH_TYPE_DESC);
    }

    static {
        needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.SIGNER_TYPE, KpConstants.KpFields.SIGNER_TYPE_DES);
        needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.COMPELLATION, KpConstants.KpFields.COMPELLATION_DESC);
        needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.CERT_TYPE, KpConstants.KpFields.CERT_TYPE_DES);
        needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.CERT_NUMBER, KpConstants.KpFields.CERT_NUMBER_DES);
        needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.CREDIT_CARD, KpConstants.KpFields.CREDIT_CARD_DES);
        needPushMess4KpUpdateInfoMap.put(KpConstants.KpFields.PHONE_NUM, KpConstants.KpFields.PHONE_NUM_DES);
    }

    static {
        KP_LEGAL_UPDATE_FIELDS_MAP.put(KpFields.COMPELLATION, KpFields.COMPELLATION_DESC);
        KP_LEGAL_UPDATE_FIELDS_MAP.put(KpFields.CERT_TYPE, KpFields.CERT_TYPE_DES);
        KP_LEGAL_UPDATE_FIELDS_MAP.put(KpFields.CERT_NUMBER, KpFields.CERT_NUMBER_DES);
        KP_LEGAL_UPDATE_FIELDS_MAP.put(KpFields.PHONE_NUM, KpFields.PHONE_NUM_DES);
        KP_LEGAL_UPDATE_FIELDS_MAP.put(KpFields.BANK_NAME, KpFields.BANK_NAME_DES);
        KP_LEGAL_UPDATE_FIELDS_MAP.put(KpFields.CREDIT_CARD, KpFields.CREDIT_CARD_DES);
    }



    /**
     * 获取待更新的KP列表
     */
    public Collection<WmCustomerKp> getUpgradeKpList(List<WmCustomerKp> wmCustomerKpList, final Collection<WmCustomerKp> deleteKpList) {
        return Collections2.filter(wmCustomerKpList, new Predicate<WmCustomerKp>() {
            @Override
            public boolean apply(@Nullable WmCustomerKp input) {
                return input == null ? false : (input.getId() > 0 && !deleteKpList.contains(input));
            }
        });
    }

    /**
     * 获取待添加的KP列表
     */
    public Collection<WmCustomerKp> getAddKpList(List<WmCustomerKp> wmCustomerKpList) {
        return Collections2.filter(wmCustomerKpList, new Predicate<WmCustomerKp>() {
            @Override
            public boolean apply(@Nullable WmCustomerKp input) {
                return input == null ? false : input.getId() == 0;
            }
        });
    }

    /**
     * 获取删除的KP列表
     */
    public Collection<WmCustomerKp> getDeleteKpList(List<WmCustomerKp> oldWmCustomerKpList, List<WmCustomerKp> wmCustomerKpList) {
        final Collection<Integer> kpIdList = Collections2.transform(wmCustomerKpList, new Function<WmCustomerKp, Integer>() {
            @Nullable
            @Override
            public Integer apply(@Nullable WmCustomerKp input) {
                return input == null ? 0 : input.getId();
            }
        });

        return Collections2.filter(oldWmCustomerKpList, new Predicate<WmCustomerKp>() {
            @Override
            public boolean apply(@Nullable WmCustomerKp input) {
                return input == null ? false : !kpIdList.contains(input.getId());
            }
        });
    }

    public int getListSize(Collection collection) {
        if (CollectionUtils.isEmpty(collection)) {
            return 0;
        }
        return collection.size();
    }

    /**
     * 获取签约人类型的KP
     */
    public WmCustomerKp getSignerKp(List<WmCustomerKp> wmCustomerKpList) {
        if (CollectionUtils.isEmpty(wmCustomerKpList)) {
            return null;
        }
        for (WmCustomerKp wmCustomerKp : wmCustomerKpList) {
            if (KpTypeEnum.SIGNER.getType() == wmCustomerKp.getKpType()) {
                return wmCustomerKp;
            }
        }
        return null;
    }

    /**
     * 根据KP类型获取KP
     *
     * @param wmCustomerKpList
     * @param kpType
     * @return
     */
    public List<WmCustomerKp> getKpListByKpType(List<WmCustomerKp> wmCustomerKpList, final byte kpType) {
        return Lists.newArrayList(Collections2.filter(wmCustomerKpList, new Predicate<WmCustomerKp>() {
            @Override
            public boolean apply(@Nullable WmCustomerKp input) {
                return input == null ? false : kpType == input.getKpType();
            }
        }));
    }

    /**
     * 获取签约人KP变更字段信息
     *
     * @param signerKpOld      旧的签约人对象
     * @param signerKpByUpdate 待更新的签约人对象
     * @return 变更字段信息列表对象
     */
    public List<WmCustomerDiffCellBo> getSignerKpUpdateFields(WmCustomerKp signerKpOld, WmCustomerKp signerKpByUpdate) {
        try {
            List<WmCustomerDiffCellBo> diffResult = DiffUtil.compare(signerKpOld, signerKpByUpdate, SIGNER_KP_UPDATE_FIELDS_MAP);
            LOGGER.info("getSignerKpUpdateFields, diffResult={}", JSON.toJSONString(diffResult));
            return diffResult;
        } catch (WmCustomerException e) {
            LOGGER.error("DiffUtil.compare异常", e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取KP法人变更字段信息
     *
     * @param oldLegalKp      旧的KP法人对象
     * @param updateLegalKp 待更新的KP法人对象
     * @return 变更字段信息列表对象
     */
    public List<WmCustomerDiffCellBo> getKpLegalUpdateFields(WmCustomerKp oldLegalKp, WmCustomerKp updateLegalKp) {
        try {
            List<WmCustomerDiffCellBo> diffResult = DiffUtil.compare(oldLegalKp, updateLegalKp, KP_LEGAL_UPDATE_FIELDS_MAP);
            LOGGER.info("getKpLegalUpdateFields, diffResult={}", JSON.toJSONString(diffResult));
            return diffResult;
        } catch (WmCustomerException e) {
            LOGGER.error("DiffUtil.compare异常", e);
        }
        return Collections.emptyList();
    }

    public Map<String, String> getKpDiffFieldsMap() {
        return SIGNER_KP_UPDATE_FIELDS_MAP;
    }

    public Map<String, String> getKpMessDiffFieldsMap(){
        return needPushMess4KpUpdateInfoMap;
    }

}
