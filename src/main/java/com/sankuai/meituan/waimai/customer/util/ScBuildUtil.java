package com.sankuai.meituan.waimai.customer.util;

import com.sankuai.meituan.waimai.thrift.customer.constant.WmCoStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @program: scm
 * @description: 构造方法
 * @author: jianghuimin02
 * @create: 2020-05-14 14:49
 **/
public class ScBuildUtil {

    /**
     * 设置枚举值的描述，列表入口
     */
    public static void setListDesc(List<SchoolBo> schoolBos){
        for (SchoolBo schoolBo : schoolBos){
            setBeanDesc(schoolBo);
        }
    }

    /**
     * 设置学校信息枚举值描述
     * @param schoolBo schoolBo
     */
    public static void setBeanDesc(SchoolBo schoolBo) {
        // 学校分级
        if (schoolBo.getGrade() != 0) {
            SchoolGradeEnum schoolGradeEnum = SchoolGradeEnum.getByType(schoolBo.getGrade());
            schoolBo.setGradeDesc(schoolGradeEnum == null ? "" : schoolGradeEnum.getName());
        }
        // 学校类型
        if (schoolBo.getSchoolType() != 0) {
            SchoolTypeEnum schoolTypeEnum = SchoolTypeEnum.getByType(schoolBo.getSchoolType());
            schoolBo.setTypeDesc(schoolTypeEnum == null ? "" : schoolTypeEnum.getName());
        }
        // 学校状态
        if (schoolBo.getSchoolStatus() != 0 && SchoolStatusEnum.getByType(schoolBo.getSchoolStatus()) != null) {
            SchoolStatusEnum schoolStatusEnum = SchoolStatusEnum.getByType(schoolBo.getSchoolStatus());
            schoolBo.setStatusDesc(schoolStatusEnum == null ? "" : schoolStatusEnum.getName());
        }
        // 合作状态
        if (schoolBo.getWmCoStatus() != null) {
            schoolBo.setWmCoStatusDesc(WmCoStatusEnum.getDescByCode(schoolBo.getWmCoStatus()));
        }
        // 配送到校内枚举
        if (schoolBo.getOutDeliveryIn() != null) {
            OutDeliveryInEnum outDeliveryInEnum = OutDeliveryInEnum.getByType(schoolBo.getOutDeliveryIn());
            schoolBo.setOutDeliveryInDesc(outDeliveryInEnum == null ? "" : outDeliveryInEnum.getName());
        }
        // 配送状况
        if (schoolBo.getDeliveryStatus() != 0) {
            DeliveryStatusEnum deliveryStatusEnum = DeliveryStatusEnum.getByType(schoolBo.getDeliveryStatus());
            schoolBo.setDeliveryStatusDesc(deliveryStatusEnum == null ? "" : deliveryStatusEnum.getName());
        }
        // 学校开发方式
        if (schoolBo.getSchoolDevType() != null) {
            SchoolDevTypeEnum schoolDevTypeEnum = SchoolDevTypeEnum.getByType(schoolBo.getSchoolDevType());
            schoolBo.setSchoolDevTypeDesc(schoolDevTypeEnum == null ? "" : schoolDevTypeEnum.getName());
        }
        // 学校合作方式
        if (schoolBo.getSchoolCooperateType() != null) {
            SchoolCooperateTypeEnum schoolCooperateTypeEnum = SchoolCooperateTypeEnum.getByType(schoolBo.getSchoolCooperateType());
            schoolBo.setSchoolCooperateTypeDesc(schoolCooperateTypeEnum == null ? "" : schoolCooperateTypeEnum.getName());
        }
        // 合同/授权编号类型
        if (schoolBo.getAgreementType() != null) {
            SchoolAgreementTypeEnum schoolAgreementTypeEnum = SchoolAgreementTypeEnum.getByType(schoolBo.getAgreementType());
            schoolBo.setAgreementTypeDesc(schoolAgreementTypeEnum == null ? "" : schoolAgreementTypeEnum.getName());
        }
        // 学校分级
        if (schoolBo.getSchoolLevel() != null) {
            SchoolLevelEnum schoolLevelEnum = SchoolLevelEnum.getByType(schoolBo.getSchoolLevel());
            schoolBo.setSchoolLevelDesc(schoolLevelEnum == null ? "" : schoolLevelEnum.getName());
        }
        // 学校生命周期
        if (schoolBo.getSchoolLifecycle() != null) {
            SchoolLifecycleEnum schoolLifecycleEnum = SchoolLifecycleEnum.getByType(schoolBo.getSchoolLifecycle());
            schoolBo.setSchoolLifecycleDesc(schoolLifecycleEnum == null ? "" : schoolLifecycleEnum.getName());
        }
        // 蜂窝类型
        if (schoolBo.getAorType() != null) {
            SchoolAorTypeEnum aorTypeEnum = SchoolAorTypeEnum.getByType(schoolBo.getAorType());
            schoolBo.setAorTypeDesc(StringUtils.defaultIfEmpty(aorTypeEnum.getName(), Strings.EMPTY));
        }
    }
}
