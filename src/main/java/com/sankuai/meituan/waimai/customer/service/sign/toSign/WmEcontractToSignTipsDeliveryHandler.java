package com.sankuai.meituan.waimai.customer.service.sign.toSign;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigTaskParseService;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmLogistics;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmLogisticsThriftService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @description: 待签约页顶部提示合同类型-C1
 * @author: lixuepeng
 * @create: 2021-12-29
 **/
@Service
public class WmEcontractToSignTipsDeliveryHandler implements WmEcontractToSignTipsHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractToSignTipsDeliveryHandler.class);

    @Autowired
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;
    @Autowired
    private WmLogisticsThriftService.Iface wmLogisticsThriftService;

    @Override
    public String getName() {
        return "《配送信息》";
    }

    @Override
    public Boolean isNeedSign(int customerId, long wmPoiId) {
        return true;
    }

    @Override
    public Boolean hasEffectiveRecord(int customerId, long wmPoiId) {
        // 非批量配送合同
        List<WmEcontractSignTaskDB> perTaskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndType(customerId, EcontractTaskApplyTypeEnum.POIFEE.getName());
        if (CollectionUtils.isNotEmpty(perTaskDBList)) {
            for (WmEcontractSignTaskDB taskDB : perTaskDBList) {
                if (!EcontractTaskStateEnum.SUCCESS.getName().equals(taskDB.getApplyState())
                        && !EcontractTaskStateEnum.IN_PROCESSING.getName().equals(taskDB.getApplyState())) {
                    continue;
                }
                EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskDB.getApplyContext(), EcontractDeliveryInfoBo.class);
                if (deliveryInfoBo != null && deliveryInfoBo.getWmPoiId() != null && Long.valueOf(deliveryInfoBo.getWmPoiId()) == wmPoiId) {
                    return true;
                }
            }
        }

        // 批量配送合同
        List<WmEcontractSignTaskDB> batchTaskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndType(customerId, EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName());
        if (CollectionUtils.isNotEmpty(batchTaskDBList)) {
            for (WmEcontractSignTaskDB taskDB : batchTaskDBList) {
                if (!EcontractTaskStateEnum.SUCCESS.getName().equals(taskDB.getApplyState())
                        && !EcontractTaskStateEnum.IN_PROCESSING.getName().equals(taskDB.getApplyState())) {
                    continue;
                }
                EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskDB.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
                if (batchDeliveryInfoBo != null && CollectionUtils.isNotEmpty(batchDeliveryInfoBo.getWmPoiIdList())
                        && batchDeliveryInfoBo.getWmPoiIdList().contains(wmPoiId)) {
                    return true;
                }
            }
        }

        // 判断是否有有效的配送信息（兼容纸质等场景）
        try {
            List<WmLogistics> wmLogisticsList = wmLogisticsThriftService.getLogisticsByWmPoiId(Long.valueOf(wmPoiId).intValue());
            if (CollectionUtils.isNotEmpty(wmLogisticsList)) { // 存在配送方式，则认为存在生效配送数据
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("#hasEffectiveRecord#根据门店id获取配送方式异常 wmPoiId:{}", wmPoiId, e);
        }

        return false;
    }
}
