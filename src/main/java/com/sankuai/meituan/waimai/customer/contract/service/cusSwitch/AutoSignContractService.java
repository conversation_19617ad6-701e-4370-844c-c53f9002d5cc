package com.sankuai.meituan.waimai.customer.contract.service.cusSwitch;

import com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.view.WmContractAutoSignView;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractAgentService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmContractAgentInfo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.DateUtils;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService;
import com.sankuai.meituan.waimai.thrift.agent.domain.WmCommercialAgentInfo;
import com.sankuai.meituan.waimai.thrift.agent.service.WmCommercialAgentInfoThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchModulePackInfo;
import com.sankuai.meituan.waimai.poibizflow.constant.customerSwitch.CustomerSwitchModuleTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import javax.annotation.Resource;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/4/1 15:44
 */
@Slf4j
@Service
public class AutoSignContractService {

    @Resource
    private WmCustomerService wmCustomerService;

    @Resource
    private WmCustomerKpService wmCustomerKpService;

    @Resource
    private EmpServiceAdaptor empServiceAdaptor;

    @Resource
    private WmContractService wmContractService;

    @Resource
    private WmPoiSwitchThriftService wmPoiSwitchThriftService;

    @Resource
    protected WmContractAgentService wmContractAgentService;

    public void autoSignContract(WmContractAutoSignView autoSignView) {
        try {
            log.info("AutoSignContractService#autoSignContract, autoSignView:{}", JSON.toJSONString(autoSignView));
            if (!preCheck(autoSignView)) {
                syncToPoiSwitch(Collections.singletonList(-1L), autoSignView);
                return;
            }
            List<Long> manualTaskIdList = getManualTaskIdList(autoSignView);
            if (CollectionUtils.isEmpty(manualTaskIdList)) {
                syncToPoiSwitch(Collections.singletonList(-1L), autoSignView);
                return;
            }
            syncToPoiSwitch(manualTaskIdList, autoSignView);
        } catch (Exception e) {
            log.error("AutoSignContractService#autoSignContract, error", e);
        }
    }

    private List<Long> getManualTaskIdList(WmContractAutoSignView autoSignView) {
        List<Long> manualTaskIdList = new ArrayList<>();
        Long c1ContractManualTaskId = getC1ContractManualTaskId(autoSignView);
        if (c1ContractManualTaskId != null) {
            manualTaskIdList.add(c1ContractManualTaskId);
        }
        List<Long> c2ContractManualTaskIdList = getC2ContractManualTaskIdList(autoSignView);
        if (CollectionUtils.isNotEmpty(c2ContractManualTaskIdList)) {
            manualTaskIdList.addAll(c2ContractManualTaskIdList);
        }
        return manualTaskIdList;
    }


    /**
     * 是否已经签约C1-线上or线下表存在即可
     */
    private boolean hasSignC1Contract(WmContractAutoSignView autoSignView) {
        try {
            List<WmCustomerContractBo> wmCustomerContractBos = wmContractService.getContractBoListByCusIdAndType((long) autoSignView.getCustomerId(),
                    Lists.newArrayList(WmTempletContractTypeEnum.C1_PAPER.getCode(), WmTempletContractTypeEnum.C1_E.getCode()),
                    autoSignView.getOpUid(), autoSignView.getOpName());
            if (CollectionUtils.isNotEmpty(wmCustomerContractBos)) {
                return true;
            }
        } catch (TException e) {
            log.warn("AutoSignContractService#hasSignC1Contract, TException", e);
        } catch (WmCustomerException e) {
            log.warn("AutoSignContractService#hasSignC1Contract, WmCustomerException", e);
        } catch (Exception e) {
            log.error("AutoSignContractService#hasSignC1Contract, error", e);
        }
        return false;
    }

    private Long getC1ContractManualTaskId(WmContractAutoSignView autoSignView) {
        try {
            if (hasSignC1Contract(autoSignView)) {
                return null;
            }
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(autoSignView.getCustomerId());
            WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(autoSignView.getCustomerId());
            WmPoiSignSubjectBo signSubjectBo = wmContractService.getSignSubjectBo(autoSignView.getCustomerId(), WmTempletContractTypeEnum.C1_E.getCode());
            WmCustomerContractBo contractBo = initSaveC1ContractBo(wmCustomerDB, wmCustomerKp, signSubjectBo, autoSignView);
            if (contractBo == null) {
                return null;
            } else {
                return wmContractService.saveAndStartSignForManualPack(contractBo, autoSignView.getOpUid(), autoSignView.getOpName());
            }
        } catch (TException e) {
            log.warn("AutoSignContractService#getC1ContractManualTaskId, TException", e);
        } catch (WmCustomerException e) {
            log.warn("AutoSignContractService#getC1ContractManualTaskId, WmCustomerException", e);
        } catch (Exception e) {
            log.error("AutoSignContractService#getC1ContractManualTaskId, error", e);
        }
        return null;
    }

    private boolean preCheck(WmContractAutoSignView autoSignView) {
        try {
            //基本参数校验
            Preconditions.checkArgument(autoSignView.getCustomerId() > 0
                    && CollectionUtils.isNotEmpty(autoSignView.getWmPoiIds())
                    && StringUtils.isNotEmpty(autoSignView.getOpName()), "参数不合法");
            // 客户类型校验
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(autoSignView.getCustomerId());
            Preconditions.checkArgument(wmCustomerDB.getSignMode() == 2, "新客户签约模式不为电子签约");
            // 签约KP校验
            WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(autoSignView.getCustomerId());
            Preconditions.checkArgument(wmCustomerKp != null, "签约KP人信息为空");
            return true;
        } catch (WmCustomerException e) {
            log.warn("AutoSignContractService#preCheck, WmCustomerException", e);
        } catch (IllegalArgumentException e) {
            log.warn("AutoSignContractService#preCheck, IllegalArgumentException", e);
        } catch (Exception e) {
            log.error("AutoSignContractService#preCheck, error", e);
        }
        return false;
    }

    private WmCustomerContractBo initSaveC1ContractBo(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp,
                                                      WmPoiSignSubjectBo signSubjectBo, WmContractAutoSignView autoSignView) {
        log.info("AutoSignContractService#initSaveC1ContractBo, customerId:{}", autoSignView.getCustomerId());
        try {
            WmCustomerContractBo contractBo = new WmCustomerContractBo();

            //基础Bo
            WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
            basicBo.setContractNum("电子合同保存后自动生成编号");
            basicBo.setDueDate(DateUtils.getNYearLastSecond(1));//有效期为1年
            CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
            basicBo.setExtStr(JSON.toJSONString(paperContractRemarkBo));
            basicBo.setLogisticsSubject(signSubjectBo.getPartlogisticsName());
            basicBo.setParentId(autoSignView.getCustomerId());
            basicBo.setType(WmTempletContractTypeEnum.C1_E.getCode());

            //签约甲乙方Bo
            List<WmTempletContractSignBo> signList = Lists.newArrayList();
            //甲方
            WmTempletContractSignBo partyA = new WmTempletContractSignBo();
            partyA.setSignId(autoSignView.getCustomerId());
            partyA.setSignName(wmCustomerDB.getCustomerName());
            partyA.setSignPeople(wmCustomerKp.getCompellation());
            partyA.setSignPhone(wmCustomerKp.getPhoneNum());
            partyA.setSignTime(DateUtils.getNDay(0));
            partyA.setSignType("A");
            signList.add(partyA);
            //乙方
            WmTempletContractSignBo partyB = new WmTempletContractSignBo();
            partyB.setSignId(0);
            partyB.setSignName(signSubjectBo.getPartBName());
            partyB.setSignPeople(autoSignView.getOpName());
            partyB.setSignPhone(empServiceAdaptor.getPhone(autoSignView.getOpUid()));
            partyB.setSignTime(DateUtils.getNDay(0));
            partyB.setSignType("B");
            signList.add(partyB);

            //封装
            contractBo.setBasicBo(basicBo);
            contractBo.setSignBoList(signList);
            contractBo.setIgnoreExistAnotherSignTypeContract(true);
            contractBo.setPackWay(SignPackWay.WAIT_HAND_PACK.getCode());

            return contractBo;
        } catch (Exception e) {
            log.error("AutoSignContractService#initSaveC1ContractBo, error", e);
        }
        return null;
    }

    private List<Long> getC2ContractManualTaskIdList(WmContractAutoSignView autoSignView) {
        try {
            if (CollectionUtils.isEmpty(autoSignView.getAgentIds())) {
                log.warn("AutoSignContractService#getC1ContractManualTaskId, 门店列表对应无对应的代理商信息");
                return null;
            }
            if (new HashSet<>(autoSignView.getExistAgentIds()).containsAll(autoSignView.getAgentIds())) {
                log.warn("AutoSignContractService#getC1ContractManualTaskId, 门店对应的代理商合同全都已签署");
                return null;
            }
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(autoSignView.getCustomerId());
            WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(autoSignView.getCustomerId());
            return startSignC2Contract(autoSignView, wmCustomerDB, wmCustomerKp);
        } catch (Exception e) {
            log.error("AutoSignContractService#getC1ContractManualTaskId, error", e);
        }
        return null;
    }

    private List<Long> startSignC2Contract(WmContractAutoSignView autoSignView, WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp) {
        List<Long> taskIds = Lists.newArrayList();
        for (Integer agentId : autoSignView.getAgentIds()) {
            // 只处理未签约
            if (!autoSignView.getExistAgentIds().contains(agentId)) {
                log.info("自动发起C2合同-填充数据，需要发起签约的代理商:{}", agentId);
                Long singleC2Contract = startSignSingleC2Contract(autoSignView, wmCustomerDB, wmCustomerKp, agentId);
                if (singleC2Contract != null) {
                    taskIds.add(singleC2Contract);
                }
            }
        }
        return taskIds;
    }

    private Long startSignSingleC2Contract(WmContractAutoSignView autoSignView, WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, Integer agentId) {
        try {
            WmCustomerContractBo contractBo = initSaveC2ContractBo(wmCustomerDB, wmCustomerKp, agentId, autoSignView);
            if (contractBo == null) {
                return null;
            }
            return wmContractService.saveAndStartSignForManualPack(contractBo, autoSignView.getOpUid(), autoSignView.getOpName());
        } catch (TException e) {
            log.warn("AutoSignContractService#startSignC2Contract, TException", e);
        } catch (WmCustomerException e) {
            log.warn("AutoSignContractService#startSignC2Contract, WmCustomerException", e);
        } catch (Exception e) {
            log.error("AutoSignContractService#startSignC2Contract, error", e);
        }
        return null;
    }

    private WmCustomerContractBo initSaveC2ContractBo(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp,
                                                      Integer agentId, WmContractAutoSignView autoSignView) {
        try {
            WmCustomerContractBo contractBo = new WmCustomerContractBo();

            //基础Bo
            WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
            basicBo.setContractNum("电子合同保存后自动生成编号");
            basicBo.setDueDate(0); // C2合同长期有效
            CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
            basicBo.setExtStr(JSON.toJSONString(paperContractRemarkBo));
            basicBo.setParentId(autoSignView.getCustomerId());
            basicBo.setType(WmTempletContractTypeEnum.C2_E.getCode());

            //签约甲乙方Bo
            List<WmTempletContractSignBo> signList = Lists.newArrayList();
            //甲方
            WmTempletContractSignBo partyA = new WmTempletContractSignBo();
            partyA.setSignId(autoSignView.getCustomerId());
            partyA.setSignName(wmCustomerDB.getCustomerName());
            partyA.setSignPeople(wmCustomerKp.getCompellation());
            partyA.setSignPhone(wmCustomerKp.getPhoneNum());
            partyA.setSignTime(DateUtils.getNDay(0));
            partyA.setSignType("A");
            signList.add(partyA);
            //乙方
            WmTempletContractSignBo partyB = new WmTempletContractSignBo();
            partyB.setSignId(agentId);
            WmContractAgentInfo agentInfo = wmContractAgentService.queryAgentInfoById(agentId);
            partyB.setSignName(agentInfo.getName());
            partyB.setSignPeople(agentInfo.getLegalPerson());
            partyB.setSignPhone("");
            partyB.setSignTime(DateUtils.getNDay(0));
            partyB.setSignType("B");
            signList.add(partyB);

            // 封装
            contractBo.setBasicBo(basicBo);
            contractBo.setSignBoList(signList);
            contractBo.setIgnoreExistAnotherSignTypeContract(true);
            contractBo.setPackWay(SignPackWay.WAIT_HAND_PACK.getCode());

            return contractBo;
        } catch (WmCustomerException e) {
            log.warn("AutoSignContractService#initSaveC2ContractBo, warn", e);
        } catch (Exception e) {
            log.error("AutoSignContractService#initSaveC2ContractBo, error", e);
        }
        return null;
    }

    private void syncToPoiSwitch(List<Long> resultList, WmContractAutoSignView autoSignView) {
        try {
            SwitchModulePackInfo modulePackInfo = assemblyInfo(resultList, autoSignView);
            log.info("AutoSignContractService#syncToPoiSwitch, modulePackInfo: {}", JSON.toJSONString(modulePackInfo));
            wmPoiSwitchThriftService.saveSwitchModulePack(modulePackInfo);
        } catch (Exception e) {
            log.error("AutoSignContractService#syncToPoiSwitch, autoSignView: {} error", JSON.toJSONString(autoSignView), e);
        }
    }

    private SwitchModulePackInfo assemblyInfo(List<Long> resultList, WmContractAutoSignView autoSignView) {
        SwitchModulePackInfo switchModulePackInfo = new SwitchModulePackInfo();
        switchModulePackInfo.setSwitchBasicId(autoSignView.getSwitchId());
        switchModulePackInfo.setModuleId(CustomerSwitchModuleTypeEnum.C1_CONTRACT.getCode());
        switchModulePackInfo.setTaskIds(StringUtils.join(resultList, ","));
        switchModulePackInfo.setOpUid(autoSignView.getOpUid());
        switchModulePackInfo.setOpName(autoSignView.getOpName());
        return switchModulePackInfo;
    }


}
