package com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.read;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
@Data
public class CanteenRead {

    /**
     * 字段
     */
    private KmsKeyNameEnum keyName;

    /**
     * KP原字段值
     */
    private String encryptionValue;


    private WmCanteenDB canteen;

    private WmScCanteenAuditDO canteenAudit;

}
