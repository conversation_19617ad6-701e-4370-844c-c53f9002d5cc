package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.common.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 构造闪购新企客需要的合同动态参数，以及模版参数，佣金协议
 *
 * @Author: wangyongfang
 * @Date: 2023-12-08
 */

@Slf4j
@Service
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE)
public class TechnicalServiceShanGouNewQikePdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker {

    /**
     * 闪购新企客的默认模版ID
     */
    private static final Integer SG_NEW_ENTERPRISE_CUSTOMER_PDF_TEMPLATE_ID = 163;

    /**
     * 闪购新企客的默认模版版本号，0表示最新生效版本
     */
    private static final Integer SG_NEW_ENTERPRISE_CUSTOMER_PDF_TEMPLATE_VERSION = 0;

    /**
     * 生效时间
     */
    private static final String SIGN_TIME = "signTime";

    /**
     * 甲方签章关键字
     */
    private static final String PART_A_ESTAMP = "partAEstamp";

    /**
     * 甲方名称
     */
    private static final String PART_A_STAMPNAME = "partAStampName";

    /**
     * 模版ID MCC KEY
     */
    private static final String TECHNICAL_SERVICE_SGV2_2NEC_FEEMODE_ID = "TECHNICAL_SERVICE_SGV2_2NEC_FEEMODE_ID";

    /**
     * 模版版本 MCC KEY
     */
    private static final String TECHNICAL_SERVICE_SGV2_2NEC_FEEMODE_VERSION = "TECHNICAL_SERVICE_SGV2_2NEC_FEEMODE_VERSION";

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {
        log.info("technicalServiceShanGouNewQikePdfMaker, customerId:{}, batchId:{}", originContext.getCustomerId(), originContext.getBatchId());
        List<EcontractDeliveryInfoBo> deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE.getName());
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();
        Map<String, String> subMap;
        for (EcontractDeliveryInfoBo infoBo : deliveryInfoList) {
            infoBo.setDeliveryArea(null);
            infoBo.setEcontractDeliveryWholeCityInfoBo(null);
            infoBo.setEcontractDeliveryAggregationInfoBo(null);
            subMap = MapUtil.Object2Map(infoBo);
            pdfBizContent.add(subMap);
        }

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put(SIGN_TIME, StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMetaContent.put(PART_A_STAMPNAME, StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContent.put(PART_A_ESTAMP, PdfConstant.POI_SIGNKEY);

        //基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt(TECHNICAL_SERVICE_SGV2_2NEC_FEEMODE_ID, SG_NEW_ENTERPRISE_CUSTOMER_PDF_TEMPLATE_ID));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt(TECHNICAL_SERVICE_SGV2_2NEC_FEEMODE_VERSION, SG_NEW_ENTERPRISE_CUSTOMER_PDF_TEMPLATE_VERSION));
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        log.info("technicalServiceShanGouNewQikePdfMaker, pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }
}
