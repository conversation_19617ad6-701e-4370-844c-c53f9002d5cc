package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.datafactor;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.adapter.WmCustomerGrayServiceAdapter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.IWmEcontractSignDataFactorCollector;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.ContractSignEstampSubjectEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * c1合同pdf影响因子
 */
@Service
public class WmEcontractC1ContractDataFactorCollector implements IWmEcontractSignDataFactorCollector {

    @Autowired
    private WmCustomerGrayServiceAdapter wmCustomerGrayServiceAdapter;

    @Override
    public void collect(EcontractBatchContextBo originContext,
                        EcontractSignDataFactor econtractSignDataFactor) throws WmCustomerException {

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.C1CONTRACT);

        EcontractContractInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractContractInfoBo.class);

        //乙A
        ContractSignEstampSubjectEnum contractSignEstampSubjectEnumA = ContractSignEstampSubjectEnum.getByValue
                (StringUtils.defaultIfEmpty(contractInfoBo.getPartBName(), StringUtils.EMPTY));
        //乙B
        ContractSignEstampSubjectEnum contractSignEstampSubjectEnumB = ContractSignEstampSubjectEnum.getByValue
                (StringUtils.defaultIfEmpty(contractInfoBo.getPerformanceServiceFeeName(), StringUtils.EMPTY));

        if (ContractSignEstampSubjectEnum.SHANGHAI_ZHISONG.getDesc().equals(contractSignEstampSubjectEnumA.getDesc())) {
            econtractSignDataFactor.setAShSanKuaiBoolean(true);
        }
        if (ContractSignEstampSubjectEnum.BEIJING_SANKUAI.getDesc().equals(contractSignEstampSubjectEnumA.getDesc())) {
            econtractSignDataFactor.setABjSanKuaiBoolean(true);
        }
        if (ContractSignEstampSubjectEnum.SHENZHEN_SHOUKANG.getDesc().equals(contractSignEstampSubjectEnumA.getDesc())) {
            econtractSignDataFactor.setASzBaiShouJianKangBoolean(true);
        }
        if (ContractSignEstampSubjectEnum.KANGAROO_DELIVERY_LIMITED.getDesc().equals(contractSignEstampSubjectEnumA.getDesc())) {
            econtractSignDataFactor.setB2cMedBoolean(true);
        }


        if(contractSignEstampSubjectEnumB==null){
            //默认北京三快
            econtractSignDataFactor.setBBjSanKuaiBoolean(true);
            return;
        }

        if (ContractSignEstampSubjectEnum.SHANGHAI_ZHISONG.getDesc().equals(contractSignEstampSubjectEnumB.getDesc())) {
            econtractSignDataFactor.setBShSanKuaiBoolean(true);
        }
        if (ContractSignEstampSubjectEnum.BEIJING_SANKUAI.getDesc().equals(contractSignEstampSubjectEnumB.getDesc())) {
            econtractSignDataFactor.setBBjSanKuaiBoolean(true);
        }
        if (ContractSignEstampSubjectEnum.SHENZHEN_SHOUKANG.getDesc().equals(contractSignEstampSubjectEnumB.getDesc())) {
            econtractSignDataFactor.setBSzBaiShouJianKangBoolean(true);
        }

        if(contractInfoBo.isMedicineTypeCustomer()){
            econtractSignDataFactor.setAMedicineBoolean(true);
        }
        //是否包含钱袋宝签章
        econtractSignDataFactor.setQdbStampBoolean(!wmCustomerGrayServiceAdapter.isGrayCustomer(taskBo.getCustomerId()));

    }
}
