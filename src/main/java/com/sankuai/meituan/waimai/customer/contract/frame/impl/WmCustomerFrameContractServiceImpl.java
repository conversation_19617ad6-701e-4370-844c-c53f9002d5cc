package com.sankuai.meituan.waimai.customer.contract.frame.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.mtcoop.thrift.dto.TQueryCoopPreviewHtmlRequest;
import com.sankuai.meituan.mtcoop.thrift.enumtype.TCoopType;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.agent.CustomerIdMappingServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.config.FrameContractConfigThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.daocan.CommonCoopServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.sg.NationalSubsidySupplierThriftAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.contract.CooperateModeConstant;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.contract.config.dto.ContractConfigAllInfoDTO;
import com.sankuai.meituan.waimai.customer.contract.frame.CooperateModeAuthority;
import com.sankuai.meituan.waimai.customer.contract.frame.WmCustomerFrameContractAuthService;
import com.sankuai.meituan.waimai.customer.contract.frame.WmCustomerFrameContractService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractAgentService;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.WmAndDcCustomerRelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.common.GrayConfigHelper;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBusinessEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageDataDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ConfigContractQueryRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.request.ContractSignSubjectInfoQueryRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.response.ContractSignSubjectInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerFormBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerListBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPageDate;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.shangou.partner.sdk.dto.NationalSubsidySelfOpEntityDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/2 17:08
 */
@Service
@Slf4j
public class WmCustomerFrameContractServiceImpl implements WmCustomerFrameContractService {

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    @Autowired
    private Map<String, CooperateModeAuthority> cooperateModeAuthorityMap;

    @Resource
    private WmContractAgentService wmContractAgentService;

    @Resource
    private WmCustomerFrameContractAuthService wmCustomerFrameContractAuthService;

    @Resource
    private CommonCoopServiceAdapter commonCoopServiceAdapter;

    @Resource
    private WmAndDcCustomerRelService wmAndDcCustomerRelService;

    @Resource
    private CustomerIdMappingServiceAdapter customerIdMappingServiceAdapter;

    @Autowired
    private IWmCustomerRealService wmLeafCustomerRealService;

    @Resource
    private WmCustomerService wmCustomerService;

    @Resource
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Resource
    private WmEmployClient wmEmployClient;

    @Resource
    private GrayConfigHelper grayConfigHelper;

    @Resource
    private NationalSubsidySupplierThriftAdapter nationalSubsidySupplierThriftAdapter;

    private final String WAIMAI_BEE = "waimai_bee";

    /**
     * 获取可以查看的业务线
     * @param requestDTO
     * @return
     * @throws WmCustomerException
     */
    @Override
    public List<BusinessGroupInfo> getAuthBusinessGroupLine(AuthGroupLineQueryRequestDTO requestDTO) throws WmCustomerException, TException {
        log.info("getAuthBusinessGroupLine requestDTO:{}", JSON.toJSONString(requestDTO));
        if (Objects.isNull(requestDTO)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"入参不能为空");
        }
        if (Objects.isNull(requestDTO.getUid())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"uid不能为空");
        }
        if (Objects.isNull(requestDTO.getWmCustomerId())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"客户ID不能为空");
        }

        Long uid = requestDTO.getUid();
        Integer wmCustomerId = requestDTO.getWmCustomerId();

        List<BusinessGroupInfo> allBusinessGroupLine = getAllBusinessGroupLine();
        List<BusinessGroupInfo> authBusinessGroupLine = new ArrayList<>();
        for (BusinessGroupInfo businessGroupInfo : allBusinessGroupLine) {
            Integer businessGroupLine = businessGroupInfo.getBusinessGroupLine();
            if (checkBusinessGroupLine(businessGroupLine, uid, wmCustomerId)) {
                authBusinessGroupLine.add(businessGroupInfo);
            }
        }

        return authBusinessGroupLine;
    }

    /**
     * 默认走PC端，如果包含WAIMAI_BEE则走APP端
     * @param cooperateModeQueryRequestDTO
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public List<CooperateModeQueryRespDTO> getAllCooperateMode(CooperateModeQueryRequestDTO cooperateModeQueryRequestDTO) throws WmCustomerException, TException {
        log.info("getAllCooperateMode cooperateModeQueryRequestDTO:{}", JSON.toJSON(cooperateModeQueryRequestDTO));
        if (Objects.isNull(cooperateModeQueryRequestDTO)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"入参不能为空");
        }
        if (Objects.isNull(cooperateModeQueryRequestDTO.getWmCustomerId())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"客户ID不能为空");
        }
        if (Objects.isNull(cooperateModeQueryRequestDTO.getUid())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"uid不能为空");
        }

        Integer businessGroupLine = checkBuninessGroupLine(cooperateModeQueryRequestDTO.getBusinessGroupLine());
        cooperateModeQueryRequestDTO.setBusinessGroupLine(businessGroupLine);
        Long uid = cooperateModeQueryRequestDTO.getUid();

        String userAgent = cooperateModeQueryRequestDTO.getUserAgent();
        if (StringUtils.contains(userAgent,WAIMAI_BEE)) {
            return getCooperateModeList(uid, cooperateModeQueryRequestDTO, CustomerDeviceType.APP);
        }

        return getCooperateModeList(uid, cooperateModeQueryRequestDTO, CustomerDeviceType.PC);
    }

    @Override
    public List<TaskTypeQueryRespDTO> getAllTaskType(TaskTypeQueryRequestDTO taskTypeQueryRequestDTO) throws WmCustomerException, TException {
        log.info("getAllTaskType taskTypeQueryRequestDTO:{}", JSON.toJSON(taskTypeQueryRequestDTO));

        Integer businessGroupLine = checkBuninessGroupLine(taskTypeQueryRequestDTO.getBusinessGroupLine());
        return getAllTaskTypeList(businessGroupLine);
    }


    @Override
    public List<AgentDTO> queryAgent(AgentQueryRequestDTO agentQueryRequestDTO) throws WmCustomerException, TException {
        log.info("queryAgent agentQueryRequestDTO:{}", JSON.toJSON(agentQueryRequestDTO));
        if (Objects.isNull(agentQueryRequestDTO)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"入参不能为空");
        }
        if (Objects.isNull(agentQueryRequestDTO.getUid())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"uid不能为空");
        }

        Integer businessGroupLine = checkBuninessGroupLine(agentQueryRequestDTO.getBusinessGroupLine());
        Integer uid = agentQueryRequestDTO.getUid();
        try {
            List<AgentBo> agentBos;
            if (businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())) {
                agentBos = wmContractAgentService.queryAgentsInfoDc(uid, agentQueryRequestDTO.getKeyword());
            } else {
                agentBos = wmContractAgentService.queryAgentsInfo(uid, agentQueryRequestDTO.getKeyword());
            }
            return trans2Vo(agentBos);

        } catch (Exception e) {
            log.warn("queryAgent error:{}",e.getMessage());
            throw new WmCustomerException(WmContractErrorCodeConstant.SYSTEM_ERROR,"系统异常");
        }
    }

    @Override
    public DcCustomerInfoPageData getDcContractCustomerInfo(DcCustomerInfoPageQueryRequestDTO requestDTO) throws WmCustomerException, TException {
        log.info("getDcContractCustomerInfo requestDTO:{}", JSON.toJSON(requestDTO));
        Integer uid = requestDTO.getUid();
        Integer pageNo = requestDTO.getPageNo();
        Integer pageSize = requestDTO.getPageSize();
        if (Objects.isNull(uid)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"uid不能为空");
        }

        if (!wmCustomerFrameContractAuthService.authDcUAC(uid)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.AUTHORIZING, "无权限");
        }
        if (Objects.isNull(pageNo)) {
            pageNo = 1;
        }
        if (Objects.isNull(pageSize)) {
            pageSize = 20;
        }

        return queryDcCustomerInfoPageData(uid, pageNo, pageSize);
    }

    @Override
    public String getContractRouteRef(ContractRouteRefRequestDTO requestDTO) throws WmCustomerException {
        checkContractRouteRefParam(requestDTO);
        AuthDcRequestDTO authDcRequestDTO = new AuthDcRequestDTO();
        authDcRequestDTO.setUid(requestDTO.getOperatorId());
        authDcRequestDTO.setWmCustomerId(requestDTO.getWmCustomerId().intValue());

        boolean hasDcWriteAuthority = wmCustomerFrameContractAuthService.hasDcWriteAuthority(authDcRequestDTO);
        if (!hasDcWriteAuthority) {
            throw new WmCustomerException(WmContractErrorCodeConstant.NO_PERMISSION_ERROR, "没有操作权限");
        }
        return commonCoopServiceAdapter.queryCoopPreviewHtml(buildHtmlRequest(requestDTO.getContractProof(), requestDTO.getCoopType()));
    }

    @Override
    public DcCustomerIdQueryRespDTO queryDcCustomerId(DcCustomerIdQueryDTO requestDTO) throws WmCustomerException, TException {
        log.info("queryDcCustomerId requestDTO:{}", JSON.toJSON(requestDTO));

        Long dcPlatformId = getMtCustomerId(requestDTO);
        Long dcCustomerId = customerIdMappingServiceAdapter.getOriginCustomerIdByMtCustomerId(dcPlatformId);
        DcCustomerIdQueryRespDTO dcCustomerIdQueryRespDTO = new DcCustomerIdQueryRespDTO();
        dcCustomerIdQueryRespDTO.setDcCustomerId(dcCustomerId);

        return dcCustomerIdQueryRespDTO;
    }

    @Override
    public String queryDcC1PdfUrl(DcC1PdfUrlRequestDTO requestDTO) throws WmCustomerException {
        log.info("queryDcC1PdfUrl DcC1PdfUrlRequestDTO: {}", JSON.toJSON(requestDTO));
        if (Strings.isEmpty(requestDTO.getContractProof())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "缺少合同信息");
        }
        if (requestDTO.getCoopType() == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "缺少签约信息");
        }
        if (requestDTO.getIsNewSign() == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "缺少是否换签信息");
        }

        return commonCoopServiceAdapter.queryCoopPreviewHtml(buildPdfUrlRequest(requestDTO));
    }

    @Override
    public DcContractCustomerVo queryDcCustomerInfo(DcCustomerInfoRequestDTO requestDTO) throws WmCustomerException {
        log.info("queryDcCustomerInfo requestDTO:{}",JSON.toJSON(requestDTO));
        Long mtCustomerId = requestDTO.getMtCustomerId();
        if (Objects.isNull(mtCustomerId)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"缺少平台客户ID");
        }

        WmCustomerDB customerDB = mtCustomerThriftServiceAdapter.getCustomerByIdAndBusinessLine(mtCustomerId, BusinessLineEnum.NIB_FOOD.getCode());
        log.info("queryDcCustomerInfo customer:{}", JSON.toJSON(customerDB));
        return convertToDcContractCustomerVo(customerDB);
    }

    @Override
    public boolean hasCreateDcC1ContractAuth(DcContractAuthRequestDTO requestDTO) throws WmCustomerException {
        checkCreateDcC1AuthParam(requestDTO);
        return wmCustomerFrameContractAuthService.hasCreateDcC1ContractAuth(requestDTO.getMtCustomerId(), requestDTO.getUid());
    }

    private void checkCreateDcC1AuthParam(DcContractAuthRequestDTO requestDTO) throws WmCustomerException {
        if (requestDTO == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "参数异常");
        }
        if (requestDTO.getMtCustomerId() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "缺少平台客户ID");
        }
        if (requestDTO.getUid() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "缺少操作人ID");
        }
    }

    private DcContractCustomerVo convertToDcContractCustomerVo(WmCustomerDB customerDB) {
        if (Objects.isNull(customerDB)) {
            return null;
        }
        DcContractCustomerVo dcContractCustomerVo = new DcContractCustomerVo();
        dcContractCustomerVo.setCustomerName(customerDB.getCustomerName());

        return dcContractCustomerVo;
    }

    private TQueryCoopPreviewHtmlRequest buildPdfUrlRequest(DcC1PdfUrlRequestDTO requestDTO) {
        TQueryCoopPreviewHtmlRequest request = new TQueryCoopPreviewHtmlRequest();
        request.setCoopId(requestDTO.getContractProof());
        request.setCoopType(TCoopType.findByValue(requestDTO.getCoopType()));
        request.setIsNewSign(requestDTO.getIsNewSign());
        request.setNeedBeforeResignUrl(false);
        return request;
    }

    private TQueryCoopPreviewHtmlRequest buildHtmlRequest(String contractProof, Integer coopType) {
        TQueryCoopPreviewHtmlRequest request = new TQueryCoopPreviewHtmlRequest();
        request.setCoopId(contractProof);
        request.setCoopType(TCoopType.findByValue(coopType));
        request.setIsNewSign(false);
        request.setNeedBeforeResignUrl(true);
        return request;
    }

    private void checkContractRouteRefParam(ContractRouteRefRequestDTO requestDTO) throws WmCustomerException {
        if (requestDTO.getWmCustomerId() == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "缺少客户信息");
        }
        if (Strings.isEmpty(requestDTO.getContractProof())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "缺少合同信息");
        }
        if (requestDTO.getCoopType() == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "缺少签约信息");
        }
    }

    /**
     * 通过用户、请求DTO和访问设备类型获取合同类型列表；
     * @param uid
     * @param requestDTO
     * @param deviceType
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private List<CooperateModeQueryRespDTO> getCooperateModeList(Long uid, CooperateModeQueryRequestDTO requestDTO, CustomerDeviceType deviceType) throws TException, WmCustomerException {
        Integer wmCustomerId = requestDTO.getWmCustomerId();
        Integer businessGroupLine = requestDTO.getBusinessGroupLine();
        // 根据业务线和访问设备获取合同类型（划分到家/到餐）
        Map<Integer, String> cooperateModeMap = CooperateModeConstant.getCooperateModeMap(businessGroupLine, deviceType);
        if (Objects.isNull(cooperateModeMap)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"未知的业务线标识");
        }
        log.info("getCooperateModeList, cooperateModeMap:{}",JSON.toJSON(cooperateModeMap));
        // 通过Bean的名称获取具体的合同类型鉴权服务Bean（包含PC端/APP端）
        CooperateModeAuthority cooperateModeAuthority = cooperateModeAuthorityMap.get(CooperateModeAuthority.getBeanNameByDeviceType(deviceType));
        List<CooperateModeQueryRespDTO> respDTOs = new ArrayList<>();
        for (Map.Entry<Integer, String> entry : cooperateModeMap.entrySet()) {
            if (cooperateModeAuthority.isCooperateModeAuthority(entry.getKey(), uid, wmCustomerId, businessGroupLine)) {
                CooperateModeQueryRespDTO respDTO = new CooperateModeQueryRespDTO();
                respDTO.setValue(entry.getKey());
                respDTO.setName(entry.getValue());
                respDTO.setContractSource(ContractSourceEnum.CODE.getCode());
                respDTO.setBusinessGroupLine(businessGroupLine);
                respDTOs.add(respDTO);
            }
        }

        // 到餐不支持配置合同
        if (businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())) {
            return respDTOs;
        }

        ConfigContractQueryRequestDTO configRequestDTO = new ConfigContractQueryRequestDTO();
        configRequestDTO.setCustomerId(wmCustomerId);
        configRequestDTO.setOperatorId(uid.intValue());
        configRequestDTO.setDeviceType(deviceType.name());
        return addConfigContract(respDTOs, configRequestDTO);
    }

    /**
     * 添加配置合同，仅到家支持
     * @param respDTOs
     * @param requestDTO
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private List<CooperateModeQueryRespDTO> addConfigContract(List<CooperateModeQueryRespDTO> respDTOs, ConfigContractQueryRequestDTO requestDTO) throws TException, WmCustomerException {
        List<ContractConfigInfo> configInfoList = wmFrameContractConfigService.queryConfigFrameContract(requestDTO);
        List<ContractConfigAllInfoDTO> contractConfigAllInfoDTOS = configInfoList.stream()
                .map(FrameContractConfigThriftServiceAdapter::convertConfigToDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        for (ContractConfigAllInfoDTO configAllInfoDTO : contractConfigAllInfoDTOS) {
            CooperateModeQueryRespDTO respDTO = new CooperateModeQueryRespDTO();
            respDTO.setValue(configAllInfoDTO.getContractId());
            respDTO.setName(configAllInfoDTO.getContractName());
            respDTO.setContractSource(ContractSourceEnum.CONFIG.getCode());
            respDTO.setBusinessGroupLine(BusinessGroupLineEnum.DAOJIA_DELIVERY.getGroupLine());
            respDTOs.add(respDTO);
        }

        return respDTOs;
    }


    /**
     * 获取所有合同签约任务类型
     * @param businessGroupLine
     * @return
     */
    private List<TaskTypeQueryRespDTO> getAllTaskTypeList(Integer businessGroupLine) {
        List<TaskTypeQueryRespDTO> respDTOs = new ArrayList<>();
        Map<Integer,String> taskTypeMap;
        if (businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())){
            taskTypeMap = WmEcontractBatchConstant.TASK_TYPE_MENU_NAME_MAP_DC;
        } else {
            taskTypeMap = WmEcontractBatchConstant.TASK_TYPE_MENU_NAME_MAP;
        }

        for (Map.Entry<Integer, String> entry : taskTypeMap.entrySet()) {
            TaskTypeQueryRespDTO respDto = new TaskTypeQueryRespDTO();
            respDto.setName(entry.getValue());
            respDto.setValue(entry.getKey());
            respDto.setContractSource(ContractSourceEnum.CODE.getCode());
            respDto.setBusinessGroupLine(businessGroupLine);
            respDTOs.add(respDto);
        }

        // 到餐无配置合同类型任务
        if (businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())){
            return respDTOs;
        }

        return addConfigTaskType(respDTOs);
    }

    /**
     * 添加配置合同的任务类型
     * @param respDTOs
     * @return
     */
    private List<TaskTypeQueryRespDTO> addConfigTaskType(List<TaskTypeQueryRespDTO> respDTOs) {
        List<ContractConfigInfo> configInfoList = wmFrameContractConfigService.allConfigFrameContract();
        List<ContractConfigAllInfoDTO> contractConfigAllInfoDTOS = configInfoList.stream()
                .map(FrameContractConfigThriftServiceAdapter::convertConfigToDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        for (ContractConfigAllInfoDTO configAllInfoDTO : contractConfigAllInfoDTOS) {
            TaskTypeQueryRespDTO taskTypeQueryRespDTO = new TaskTypeQueryRespDTO();
            taskTypeQueryRespDTO.setValue(configAllInfoDTO.getContractId());
            taskTypeQueryRespDTO.setName(configAllInfoDTO.getContractName());
            taskTypeQueryRespDTO.setContractSource(ContractSourceEnum.CONFIG.getCode());
            taskTypeQueryRespDTO.setBusinessGroupLine(BusinessGroupLineEnum.DAOJIA_DELIVERY.getGroupLine());
            respDTOs.add(taskTypeQueryRespDTO);
        }

        return respDTOs;
    }

    /**
     * 获取所有业务线
     * @return
     * @throws WmCustomerException
     */
    private List<BusinessGroupInfo> getAllBusinessGroupLine() throws WmCustomerException {
        List<BusinessGroupInfo> businessGroupInfos = Arrays.stream(BusinessGroupLineEnum.values())
                .map(e -> new BusinessGroupInfo(e.getGroupLine(),e.getDesc()))
                .collect(Collectors.toList());

        log.info("getAllBusinessGroupLine businessGroupInfos:{}", JSON.toJSON(businessGroupInfos));
        return businessGroupInfos;
    }

    /**
     * 默认走到家业务线
     * @param businessGroupLine
     * @return
     */
    private Integer checkBuninessGroupLine(Integer businessGroupLine) {
        if (businessGroupLine == null) {
            return BusinessGroupLineEnum.DAOJIA_DELIVERY.getGroupLine();
        }
        return businessGroupLine;
    }

    private List<AgentDTO> trans2Vo(List<AgentBo> agentBoList) {
        return Lists.newArrayList(Sets.newHashSet(Lists.transform(agentBoList, new Function<AgentBo, AgentDTO>() {
            @Override
            public AgentDTO apply(AgentBo input) {
                AgentDTO agentDTO = new AgentDTO();
                agentDTO.setId(input.getId() == null ? 0 : input.getId());
                agentDTO.setLegalPerson(input.getLegalPerson());
                agentDTO.setName(input.getName());
                agentDTO.setOrgId(input.getOrgId() == null ? 0 : input.getOrgId());
                agentDTO.setBusinessLicenseName(input.getBusinessLicenseName());
                return agentDTO;
            }
        })));
    }


    private DcCustomerInfoPageData queryDcCustomerInfoPageData(int uid, int pageNo, int pageSize) throws TException, WmCustomerException {
        WmCustomerFormBo wmCustomerFormBo = new WmCustomerFormBo();
        wmCustomerFormBo.setPageNo(pageNo);
        wmCustomerFormBo.setPageSize(pageSize);
        wmCustomerFormBo.setBusiness(CustomerBusinessEnum.DAOCAN.getCode());
        wmCustomerFormBo.setOpUid(uid);
        WmCustomerPageDate dcCustomerList = wmLeafCustomerRealService.getDcCustomerList(wmCustomerFormBo);

        return convertToPageData(dcCustomerList);
    }


    private DcCustomerInfoPageData convertToPageData(WmCustomerPageDate dcCustomerList) throws WmCustomerException {
        DcCustomerInfoPageData pageData = new DcCustomerInfoPageData();
        PageDataDTO pageInfo = new PageDataDTO();

        Map<String, Long> pageInfoMap = dcCustomerList.getPageInfo();
        pageInfo.setPageNo(pageInfoMap.get("pageNo"));
        pageInfo.setPages(pageInfoMap.get("pages"));
        pageInfo.setTotal(pageInfoMap.get("total"));

        List<WmCustomerListBo> list = dcCustomerList.getList();
        pageData.setDcContractCustomerVoList(convertToDcContractCustomerVo(list));

        pageData.setPageInfo(pageInfo);
        return pageData;
    }

    private List<DcContractCustomerVo> convertToDcContractCustomerVo(List<WmCustomerListBo> list ) throws WmCustomerException {
        List<DcContractCustomerVo> customerList = new ArrayList<>();

        for (WmCustomerListBo wmCustomerListBo : list) {
            DcContractCustomerVo customerVo = new DcContractCustomerVo();
            customerVo.setWmCustomerId(wmCustomerListBo.getId());
            customerVo.setCustomerNumber(wmCustomerListBo.getCustomerNumber());
            customerVo.setCustomerName(wmCustomerListBo.getCustomerName());
            customerList.add(customerVo);
        }

        return customerList;
    }

    /**
     * 校验是否展示到家业务线
     * @return
     */
    private Boolean authDjBusinessGroupLine(Integer wmCustomerId) throws WmCustomerException {
        Set<Long> customerBusinessLineSet = wmCustomerService.getCustomerBusinessLineById(wmCustomerId);
        log.info("authDjBusinessGroupLine customerBusinessLineSet:{}",JSON.toJSON(customerBusinessLineSet));
        // 校验是否有生效的KP
        List<WmCustomerKp> customerKpList = wmCustomerKpService.getCustomerKpList(wmCustomerId);
        if (CollectionUtils.isEmpty(customerKpList)) {
            log.info("authDjBusinessGroupLine 没有生效的KP, wmCustomerId:{}", wmCustomerId);
            return false;
        }
        // 校验经营形式
        if (CollectionUtils.isEmpty(customerBusinessLineSet) ||
                customerBusinessLineSet.contains(BusinessLineEnum.WAI_MAI.getCode())) {
            return true;
        }

        return false;
    }

    /**
     * 业务线展示降级开关
     * @param businessGroupLine
     * @return
     */
    private boolean isShowBusinessGroupLine(Integer businessGroupLine) {
        if (businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())) {
            return MccConfig.isSupportShowDcBusinessGroupLine();
        }

        return true;
    }

    /**
     * 获取到餐平台客户ID
     * @param requestDTO
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private Long getMtCustomerId(DcCustomerIdQueryDTO requestDTO) throws TException, WmCustomerException {
        log.info("getMtCustomerId requestDTO:{}", JSON.toJSON(requestDTO));
        Long mtCustomerId = requestDTO.getMtCustomerId();
        Long wmCustomerId = requestDTO.getWmCustomerId();
        if (Objects.nonNull(mtCustomerId) && mtCustomerId > 0) {
            return mtCustomerId;
        } else if (Objects.nonNull(wmCustomerId)) {
            List<Long> dcPlatformIdList = wmAndDcCustomerRelService.getDcPlatformIdByWmCustomerId(wmCustomerId.intValue());
            if (CollectionUtils.isEmpty(dcPlatformIdList) || dcPlatformIdList.size() != 1) {
                log.warn("queryDcCustomerId dcPlatformIdList:{}",JSON.toJSON(dcPlatformIdList));
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询平台客户ID异常");
            }

            return dcPlatformIdList.get(0);
        }

        throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "客户ID不能为空");
    }

    /**
     * 校验业务线
     * @param businessGroupLine
     * @param uid
     * @param wmCustomerId
     * @return
     */
    private boolean checkBusinessGroupLine(Integer businessGroupLine, Long uid, Integer wmCustomerId) throws WmCustomerException, TException {
        // 到家特殊处理
        try {
            if (businessGroupLine.equals(BusinessGroupLineEnum.DAOJIA_DELIVERY.getGroupLine())) {
                return authDjBusinessGroupLine(wmCustomerId);
            } else {
                String userMisId = getUserMisId(Math.toIntExact(uid));
                if (!grayConfigHelper.isWdDcTabGray(userMisId)) {
                    log.info("WmCustomerFrameContractServiceImpl#checkBusinessGroupLine, 未满足WD到餐Tab页面灰度配置, uid: {}, misId: {}, wmCustomerId: {}", uid, userMisId, wmCustomerId);
                    return false;
                }

                final int readOnlyCode = WmCustomerAuthTypeEnum.READ_ONLY.getCode();
                boolean readAuth = wmCustomerFrameContractAuthService.auth(uid, businessGroupLine, wmCustomerId, readOnlyCode);
                return readAuth && isShowBusinessGroupLine(businessGroupLine);
            }
        } catch (Exception e) {
            log.error("checkBusinessGroupLine error, businessGroupLine:{}, uid:{}, wmCustomerId:{}", businessGroupLine, uid, wmCustomerId);
        }
        return false;
    }

    private String getUserMisId(Integer userId) {
        WmEmploy employById = wmEmployClient.getEmployById(userId);
        if (employById == null) {
            log.warn("WmGlobalEcontractQueryService#getUserMisId, user: {}, error", userId);
            return "";
        }
        return employById.getMisId();
    }

    @Override
    public List<ContractSignSubjectInfo> queryContractSignSubjectInfo(Integer customerId) throws WmCustomerException {
        List<NationalSubsidySelfOpEntityDTO> entityDTOList = nationalSubsidySupplierThriftAdapter.queryNationalSubsidySubject(Long.valueOf(customerId));
        return entityDTOList.stream().map(this::convertToContractSignSubjectInfo).collect(Collectors.toList());
    }

    private ContractSignSubjectInfo convertToContractSignSubjectInfo(NationalSubsidySelfOpEntityDTO entityDTO) {
        ContractSignSubjectInfo contractSignSubjectInfo = new ContractSignSubjectInfo();
        contractSignSubjectInfo.setSignSubjectId(Math.toIntExact(entityDTO.getEntityCustomerId()));
        contractSignSubjectInfo.setSignSubjectName(entityDTO.getEntityName());
        return contractSignSubjectInfo;
    }

}
