package com.sankuai.meituan.waimai.customer.service.sign.callback.resolver;

import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.service.sign.callback.WmEcontractResolver;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 配送信息回调策略
 */
@Service
public class WmEcontractBaseResolver implements WmEcontractResolver {

    private static Map<String, Boolean> stageMap = Maps.newHashMap();

    static {
        stageMap.put(WmEcontractConstant.CREATE_PDF, Boolean.FALSE);

        stageMap.put(WmEcontractConstant.CA_AGENT, Boolean.FALSE);
        stageMap.put(WmEcontractConstant.CA_MT, Boolean.FALSE);
        stageMap.put(WmEcontractConstant.CA_POI, Boolean.FALSE);
        stageMap.put(WmEcontractConstant.CA_QDB, Boolean.FALSE);

        stageMap.put(WmEcontractConstant.SMS_POI, Boolean.FALSE);

        stageMap.put(WmEcontractConstant.REALNAME_AUTH_AGENT, Boolean.FALSE);

        stageMap.put(WmEcontractConstant.STAMP_POI, Boolean.FALSE);
        stageMap.put(WmEcontractConstant.STAMP_AGENT, Boolean.FALSE);
        stageMap.put(WmEcontractConstant.STAMP_MT, Boolean.FALSE);
        stageMap.put(WmEcontractConstant.STAMP_QDB, Boolean.FALSE);

        stageMap.put(WmEcontractConstant.EFFECT, Boolean.TRUE);
        stageMap.put(WmEcontractConstant.NOT_SIGN, Boolean.TRUE);

    }

    @Override
    public Boolean isCallback(String stageName) throws WmCustomerException {
        return stageMap.get(stageName) == null ? Boolean.FALSE : stageMap.get(stageName);
    }
}
