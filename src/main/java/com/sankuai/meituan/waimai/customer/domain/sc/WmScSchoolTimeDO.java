package com.sankuai.meituan.waimai.customer.domain.sc;

/**
 * 学校时间信息DO
 * -@author: yin<PERSON><PERSON><PERSON>
 * -@date: 2021/06/09
 */
public class WmScSchoolTimeDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 创建渠道来源 1-校园食堂，2-向日葵
     */
    private Integer crSource;
    /**
     * 修改渠道来源 1-校园食堂，2-向日葵
     */
    private Integer upSource;
    /**
     * 时间业务ID
     */
    private Long tmId;
    /**
     * 学年开始年份
     */
    private Integer yearBegin;
    /**
     * 学年结束年份
     */
    private Integer yearEnd;
    /**
     * 寒假开始时间，例如2020-01-30
     */
    private String wvTimeBegin;
    /**
     * 寒假结束时间，例如2020-02-28
     */
    private String wvTimeEnd;
    /**
     * 暑假开始时间，例如2020-07-11
     */
    private String svTimeBeign;
    /**
     * 暑假结束时间
     */
    private String svTimeEnd;
    /**
     * 是否有效（是否已删除） 0-无效，1-有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
    /**
     * 封校时间
     */
    private String clTimeJson;
    /**
     * 学期
     */
    private Integer term;
    /**
     * 开学情况
     */
    private Integer termBeginSituation;
    /**
     * 未开学相关图片
     */
    private String openNotPic;
    /**
     * 未开学相关PDF
     */
    private String openNotPdf;
    /**
     * 未开学备注
     */
    private String openNotRemark;
    /**
     * 未开学资料来源
     */
    private Integer openNotInfoSource;
    /**
     * 开学信息
     */
    private String termBeginData;
    /**
     * 放假信息
     */
    private String termEndData;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSchoolPrimaryId() {
        return schoolPrimaryId;
    }

    public void setSchoolPrimaryId(Integer schoolPrimaryId) {
        this.schoolPrimaryId = schoolPrimaryId;
    }

    public Integer getCrSource() {
        return crSource;
    }

    public void setCrSource(Integer crSource) {
        this.crSource = crSource;
    }

    public Integer getUpSource() {
        return upSource;
    }

    public void setUpSource(Integer upSource) {
        this.upSource = upSource;
    }

    public Long getTmId() {
        return tmId;
    }

    public void setTmId(Long tmId) {
        this.tmId = tmId;
    }

    public Integer getYearBegin() {
        return yearBegin;
    }

    public void setYearBegin(Integer yearBegin) {
        this.yearBegin = yearBegin;
    }

    public Integer getYearEnd() {
        return yearEnd;
    }

    public void setYearEnd(Integer yearEnd) {
        this.yearEnd = yearEnd;
    }

    public String getWvTimeBegin() {
        return wvTimeBegin;
    }

    public void setWvTimeBegin(String wvTimeBegin) {
        this.wvTimeBegin = wvTimeBegin == null ? null : wvTimeBegin.trim();
    }

    public String getWvTimeEnd() {
        return wvTimeEnd;
    }

    public void setWvTimeEnd(String wvTimeEnd) {
        this.wvTimeEnd = wvTimeEnd == null ? null : wvTimeEnd.trim();
    }

    public String getSvTimeBeign() {
        return svTimeBeign;
    }

    public void setSvTimeBeign(String svTimeBeign) {
        this.svTimeBeign = svTimeBeign == null ? null : svTimeBeign.trim();
    }

    public String getSvTimeEnd() {
        return svTimeEnd;
    }

    public void setSvTimeEnd(String svTimeEnd) {
        this.svTimeEnd = svTimeEnd == null ? null : svTimeEnd.trim();
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Long getCuid() {
        return cuid;
    }

    public void setCuid(Long cuid) {
        this.cuid = cuid;
    }

    public Long getMuid() {
        return muid;
    }

    public void setMuid(Long muid) {
        this.muid = muid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public String getClTimeJson() {
        return clTimeJson;
    }

    public void setClTimeJson(String clTimeJson) {
        this.clTimeJson = clTimeJson == null ? null : clTimeJson.trim();
    }

    public Integer getTerm() {
        return term;
    }

    public void setTerm(Integer term) {
        this.term = term;
    }

    public Integer getTermBeginSituation() {
        return termBeginSituation;
    }

    public void setTermBeginSituation(Integer termBeginSituation) {
        this.termBeginSituation = termBeginSituation;
    }

    public String getOpenNotPic() {
        return openNotPic;
    }

    public void setOpenNotPic(String openNotPic) {
        this.openNotPic = openNotPic;
    }

    public String getOpenNotPdf() {
        return openNotPdf;
    }

    public void setOpenNotPdf(String openNotPdf) {
        this.openNotPdf = openNotPdf;
    }

    public Integer getOpenNotInfoSource() {
        return openNotInfoSource;
    }

    public void setOpenNotInfoSource(Integer openNotInfoSource) {
        this.openNotInfoSource = openNotInfoSource;
    }

    public String getOpenNotRemark() {
        return openNotRemark;
    }

    public void setOpenNotRemark(String openNotRemark) {
        this.openNotRemark = openNotRemark;
    }

    public String getTermBeginData() {
        return termBeginData;
    }

    public void setTermBeginData(String termBeginData) {
        this.termBeginData = termBeginData;
    }

    public String getTermEndData() {
        return termEndData;
    }

    public void setTermEndData(String termEndData) {
        this.termEndData = termEndData;
    }
}