package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.waimai.agent.otter.enterprise.response.agentinfo.AgentInfoTo;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.dao.*;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.contract.exception.VersionNotAgreedException;
import com.sankuai.meituan.waimai.customer.contract.service.IWmContractTempletService;
import com.sankuai.meituan.waimai.customer.contract.service.TempletServiceRouter;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.BusinessCustomerDeliveryValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerRelMapper;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignManualTaskDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.common.MtriceService;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.IWmEcontractTaskApplyService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.statemachine.api.FlowStateMachineManager;
import com.sankuai.meituan.waimai.customer.statemachine.api.OperationInfo;
import com.sankuai.meituan.waimai.customer.statemachine.core.exceptions.TargetStateUnreachableException;
import com.sankuai.meituan.waimai.customer.util.AppContext;
import com.sankuai.meituan.waimai.customer.util.TairLocker;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.WmPoiLogisticsBmContractThriftService;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.WmSupplyChainDataQueryThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.WmSupplyChainDataSyncThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.agent.domain.WmCommercialAgentInfo;
import com.sankuai.meituan.waimai.thrift.agent.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.agent.service.WmCommercialAgentInfoThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.request.CreateWmCustomerContractReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.ContractStatusUpdateMsg;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpRequest;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.service.WmAuditApiService;
import com.sankuai.meituan.waimai.thrift.service.WmPoiFlowlineLabelThriftService;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

public abstract class AbstractWmContractTempletService implements IWmContractTempletService {

    private static Logger logger = LoggerFactory.getLogger(AbstractWmContractTempletService.class);

    protected static List<Integer> TEMPLET_CONTRACT_STATUS = Lists.newArrayList();

    protected static List<Integer> TEMPLET_VERSION_ENDPOINT_STATUS = Lists.newArrayList();

    static {
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.WAITING_SIGN.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.SIGNING.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.AUDITING.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.REJECT.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.EFFECT.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.TO_EFFECT.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.SIGN_FAIL.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.INVALID.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.COMMIT.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.ABOLISH_ING.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.ABOLISH_FAIL.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.STAGE.getCode());
        TEMPLET_CONTRACT_STATUS.add(CustomerContractStatus.EXPIRED.getCode());
        //版本终结状态
        TEMPLET_VERSION_ENDPOINT_STATUS.add(CustomerContractStatus.REJECT.getCode());
        TEMPLET_VERSION_ENDPOINT_STATUS.add(CustomerContractStatus.EFFECT.getCode());
        TEMPLET_VERSION_ENDPOINT_STATUS.add(CustomerContractStatus.SIGN_FAIL.getCode());
        TEMPLET_VERSION_ENDPOINT_STATUS.add(CustomerContractStatus.INVALID.getCode());
        TEMPLET_VERSION_ENDPOINT_STATUS.add(CustomerContractStatus.ABOLISH_FAIL.getCode());
    }

    @Resource(name = "contractStateMachine")
    FlowStateMachineManager contractStateMachine;

    @Autowired
    WmContractVersionService wmContractVersionService;

    @Autowired
    WmTempletContractDBMapper wmTempletContractDBMapper;

    @Autowired
    WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    @Autowired
    WmTempletContractSignDBMapper wmTempletContractSignDBMapper;

    @Autowired
    WmTempletContractSignAuditedDBMapper wmTempletContractSignAuditedDBMapper;

    @Autowired
    WmEcontractSignManualTaskDBMapper wmEcontractSignManualTaskDBMapper;

    @Autowired
    WmContractVersionService contractVersionService;

    @Autowired
    IWmEcontractTaskApplyService wmEcontractTaskApplyService;

    @Autowired
    WmAuditApiService.Iface wmAuditApiService;

    @Autowired
    WmContractSignService wmContractSignService;

    @Autowired
    ContractLogService contractLogService;

    @Autowired
    WmContractService wmContractService;

    @Autowired
    WmCommercialAgentInfoThriftService.Iface wmCommercialAgentInfoThriftService;

    @Resource
    WmContractAgentService wmContractAgentService;

    @Autowired
    WmCustomerService wmCustomerService;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    WmCustomerBlackWhiteListService wmCustomerBlackWhiteListService;

    @Autowired
    WmPoiClient wmPoiClient;

    @Autowired
    WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    WmCustomerKpService wmCustomerKpService;

    @Autowired
    WmSupplyChainDataSyncThriftService wmSupplyChainDataSyncThriftService;

    @Autowired
    WmTempletContractRelMapper wmTempletContractRelMapper;

    @Autowired
    WmTempletContractExtensionMapper wmTempletContractExtensionMapper;

    @Autowired
    WmCustomerTransService wmCustomerTransService;

    @Autowired
    WmCustomerRelMapper wmCustomerRelMapper;

    @Autowired
    WmPoiFlowlineLabelThriftService.Iface wmPoiFlowlineLabelThriftService;

    @Autowired
    WmEmployService.Iface wmEmployService;

    @Autowired
    WmSupplyChainDataQueryThriftService wmSupplyChainDataQueryThriftService;

    @Autowired
    IWmCustomerRealService wmLeafCustomerRealService;

    @Autowired
    BusinessCustomerDeliveryValidator businessCustomerDeliveryValidator;

    @Autowired
    WmPoiLogisticsBmContractThriftService wmPoiLogisticsBmContractThriftService;

    @Autowired
    TairLocker tairLocker;


    @Autowired
    MtriceService mtriceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("AbstractWmContractTempletService#save, 合同录入保存开始, contractBo: {}, opUid : {}, opUname : {}", JSON.toJSONString(contractBo), opUid, opName);
        VersionCheckUtil.versionCheck(contractBo.getBasicBo().getParentId(), 0);
        ContractCheckFilter
                .contractSaveValidFilter(WmTempletContractTypeEnum.getByCode(contractBo.getBasicBo().getType()), contractBo.getBasicBo().getContractSource())
                .filter(contractBo, opUid, opName);
        if (contractBo.getBasicBo().getTempletContractId() > 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,
                    "插入时不允许指定合同ID（" + contractBo.getBasicBo().getTempletContractId() + "）");
        }
        contractBo.getBasicBo().setStatus(CustomerContractStatus.STAGE.getCode());
        WmTempletContractDB wmTempletContractDB = WmTempletContractTransUtil.templetContractBasicBoToDb(contractBo.getBasicBo(), contractBo.getSignBoList());
        wmTempletContractDB.setOpuid(opUid);
        List<WmTempletContractSignDB> wmTempletContractSignDBList = WmTempletContractTransUtil.templetSignBoToDbList(contractBo.getSignBoList());
        wmTempletContractDBMapper.insertSelective(wmTempletContractDB);
        for (WmTempletContractSignDB db : wmTempletContractSignDBList) {
            db.setWmTempletContractId(wmTempletContractDB.getId().intValue());
            db.setOpuid(opUid);
        }
        saveOrUpdateSigner(wmTempletContractSignDBList);
        contractBo.getBasicBo().setTempletContractId(wmTempletContractDB.getId());
        logger.info("AbstractWmContractTempletService#save, 合同录入保存结束  contractId : {} opUid : {}  opUname : {}", wmTempletContractDB.getId(), opUid, opName);
        metricContractSave(contractBo.getBasicBo());
        return wmTempletContractDB.getId().intValue();
    }

    private void metricContractSave(WmTempletContractBasicBo basicBo) {
        try {
            if (basicBo.getConfigContractInfo() != null) {
                mtriceService.metricContractSave(basicBo.getConfigContractInfo().getContractName());
            } else {
                mtriceService.metricContractSave(WmTempletContractTypeEnum.getByCode(basicBo.getType()).getMsg());
            }
        } catch (Exception e) {
            logger.error("AbstractWmContractTempletService#metricContractSave, error", e);
        }
    }

    @Override
    @Transactional
    public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("AbstractWmContractTempletService#update, contractBo : {} opUid : {}  opUname : {}", JSON.toJSONString(contractBo), opUid, opName);
        VersionCheckUtil.versionCheck(contractBo.getBasicBo().getParentId(), 0);

        ContractCheckFilter.contractUpdateValidFilter(WmTempletContractTypeEnum.getByCode(contractBo.getBasicBo().getType()), contractBo.getBasicBo().getContractSource()).filter(contractBo, opUid, opName);

        WmTempletContractDB contractInDb = wmTempletContractDBMapper.selectByPrimaryKey(contractBo.getBasicBo().getTempletContractId());
        WmTempletContractDB wmTempletContractDB = WmTempletContractTransUtil.templetContractBasicBoToDb(contractBo.getBasicBo(), contractBo.getSignBoList());
        wmTempletContractDB.setOpuid(opUid);
        //合同状态通过状态机更新
        wmTempletContractDB.setStatus(null);
        wmTempletContractDB.setVersion(contractInDb.getVersion());
        List<WmTempletContractSignDB> wmTempletContractSignDBList = WmTempletContractTransUtil.templetSignBoToDbList(contractBo.getSignBoList());
        for (WmTempletContractSignDB db : wmTempletContractSignDBList) {
            db.setOpuid(opUid);
        }
        wmTempletContractDBMapper.updateByPrimaryKeySelective(wmTempletContractDB);
        saveOrUpdateSigner(wmTempletContractSignDBList);

        logger.info("AbstractWmContractTempletService#update, contractId : {} opUid : {}  opUname : {}", contractBo.getBasicBo().getTempletContractId(), opUid, opName);
        if (ContractSourceEnum.isConfigSource(contractBo.getBasicBo().getContractSource())) {
            mtriceService.metricContractUpdate(contractBo.getBasicBo().getConfigContractInfo().getContractName());
        } else {
            mtriceService.metricContractUpdate(WmTempletContractTypeEnum.getByCode(contractBo.getBasicBo().getType()).getMsg());
        }
        return (int) contractBo.getBasicBo().getTempletContractId();
    }

    private void saveOrUpdateSigner(List<WmTempletContractSignDB> wmTempletContractSignDBList) {
        List<WmTempletContractSignDB> toInsertList = Lists.newArrayList();
        List<WmTempletContractSignDB> toUpdateList = Lists.newArrayList();
        for (WmTempletContractSignDB toSave : wmTempletContractSignDBList) {
            if (checkSigner(toSave)) {
                toInsertList.add(toSave);
            } else {
                toUpdateList.add(toSave);
            }
        }
        logger.info("修改签约人信息toUpdateList:{},toInsertList:{}", JSON.toJSONString(toUpdateList), JSON.toJSONString(toInsertList));
        if (!CollectionUtils.isEmpty(toUpdateList)) {
            for (WmTempletContractSignDB signDB : toUpdateList) {
                wmTempletContractSignDBMapper.updateByTempleteIdAndType(signDB);
            }
        }
        if (!CollectionUtils.isEmpty(toInsertList)) {
            //补充空字段
            wmCustomerService.fillContractSignInfo(toInsertList);
            if (MccConfig.isEncryptionInsertBySingle()) {
                for (WmTempletContractSignDB signDB : toInsertList) {
                    wmTempletContractSignDBMapper.insert(signDB);
                }
            } else {
                wmTempletContractSignDBMapper.batchInsert(toInsertList);
            }
        }
    }

    private Boolean checkSigner(WmTempletContractSignDB signDB) {
        List<WmTempletContractSignDB> list = wmTempletContractSignDBMapper.selectByWmTempletContractId((long) signDB.getWmTempletContractId());
        return CollectionUtils.isEmpty(list);
    }

    Integer insertOrUpdate(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        Integer contractId = (int) contractBo.getBasicBo().getTempletContractId();
        //通过获取代理类访问本地方法，这样才能让本地方法上的事务注解生效
        IWmContractTempletService currentProxyService = TempletServiceRouter.getService(contractBo.getBasicBo().getType(), contractBo.getBasicBo().getContractSource());
        if (contractBo.getBasicBo().getTempletContractId() <= 0) {
            contractId = currentProxyService.save(contractBo, opUid, opName);
        } else {
            currentProxyService.update(contractBo, opUid, opName);
        }
        return contractId;
    }

    public void toNextStatus(int contractId, int toStatus, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("#toNextStatus# contractId:{} toStatus:{} opUid:{} opUname:{}", contractId, toStatus, opUid, opUname);
        toNextContractStatus(contractId, toStatus, opUname);
        toNextContractVersionStatus(contractId, toStatus);
    }

    protected void toNextContractVersionStatus(int contractId, int toStatus) throws WmCustomerException {
        //待生效不是状态机终态，但是业务要求可以展示PDF，特殊处理
        if (!TEMPLET_VERSION_ENDPOINT_STATUS.contains(toStatus)) {
            if (toStatus != CustomerContractStatus.TO_EFFECT.getCode()) {
                return;
            }
        }
        WmContractVersionDB contractVersionDB = wmContractVersionService.getByIdAndTypeMaster(contractId,
                CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
        logger.info("查询版本信息：id:{}  contractVersionDB:{}", contractId, JSON.toJSON(contractVersionDB));
        //发起了签约流程才会产生新的合同版本
        if (contractVersionDB != null
                && !TEMPLET_VERSION_ENDPOINT_STATUS.contains((int) contractVersionDB.getStatus())) {
            logger.info("#toNextStatus#更新合同版本状态#WmContractId={} fromStatus:{}  toStatus",
                    contractId, contractVersionDB.getStatus(), toStatus);
            contractVersionDB.setStatus((byte) toStatus);
            wmContractVersionService.updateStatus(contractVersionDB);
        } else {
            logger.info("#toNextStatus#没有合同版本#WmContractId={}", contractId);
        }
    }

    void toNextContractStatus(final int contractId, int toStatus, final String opUname) throws WmCustomerException {
        try {
            OperationInfo operationInfo = new OperationInfo();
            operationInfo.setOperatorName(opUname);
            if (TEMPLET_CONTRACT_STATUS.contains(toStatus)) {
                contractStateMachine.changeState(String.valueOf(contractId), String.valueOf(toStatus), operationInfo);
            }
        } catch (TargetStateUnreachableException e) {
            logger.warn("状态不可达异常 msg:{}", e.getMessage(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,
                    "合同状态已变更为【" + CustomerContractStatus.getByCode(toStatus).getDesc() + "】");
        } catch (VersionNotAgreedException e) {
            logger.warn("版本不一致", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        }
        if (AppContext.isLazyProcess()) {
            AppContext.offerLazyTask(new AppContext.LazyTask() {
                @Override
                public void lazyProcess() {
                    //因为是异步延迟执行，所以这里查主库，否则可能延迟执行的时候主从问题导致查不到数据
                    WmTempletContractDB db = wmTempletContractDBMapper.getByIdMaster((long) contractId);
                    sendStatusChangeMq(db);
                }

                @Override
                public String taskDesc() {
                    return "自入驻绑定门店至3.0，发送合同状态变更消息";
                }
            });
        } else {
            WmTempletContractDB db = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
            // 如果从库查询为空，再查一次主库
            if (db == null) {
                db = wmTempletContractDBMapper.getByIdMaster((long) contractId);
            }
            sendStatusChangeMq(db);
        }
    }

    private void sendStatusChangeMq(WmTempletContractDB db) {
        logger.info("合同状态变更，发送消息  toStatus：{}  templetId:{}", db.getStatus(), db.getId());
        String extraData = JSON.toJSONString(new ContractStatusUpdateMsg(db.getParentId().intValue(), db.getId().intValue(),
                db.getType(), CustomerContractStatus.getByCode(db.getStatus())));
        mafkaMessageSendManager.send(new CustomerMQBody(db.getParentId().intValue(),
                CustomerMQEventEnum.CUSTOMER_CONTRACT_STATUS_CHANGE, extraData));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean effect(long templetContractId, int opUid, String opUname)
            throws WmCustomerException, TException {
        logger.info("合同生效 templetContractId:{}, opUid:{} opUname:{}", templetContractId, opUid, opUname);
        WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey(templetContractId);
        if (offlineContract == null || CustomerContractStatus.isInvalid(offlineContract.getStatus())) {
            logger.info("合同不存在或者已经失效 contractId:{},opUid:{} ,  opUname:{}",
                    templetContractId, opUid, opUname);
            return true;
        }
        int oldStatus = offlineContract.getStatus();
        toNextStatus((int) templetContractId, CustomerContractStatus.EFFECT.getCode(), opUid, opUname);
        offlineContract.setStatus(CustomerContractStatus.EFFECT.getCode());
        copyContractFromOffToOnline(templetContractId, opUid, offlineContract);
        invalidAnotherSignTypeContract(templetContractId, opUid, opUname, offlineContract);
        contractLogService.logStatusChange(oldStatus, WmTempletContractTransUtil.templetContractBasicDbToBo(offlineContract), opUid, opUname);
        logger.info("templetContractId:{} effect结束时间:{}",templetContractId,System.currentTimeMillis());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean toEffect(long templetContractId, int opUid, String opUname)
            throws WmCustomerException, TException {
        logger.info("合同待生效 templetContractId:{}, opUid:{} opUname:{}", templetContractId, opUid, opUname);
        WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey(templetContractId);
        if (offlineContract == null || CustomerContractStatus.isInvalid(offlineContract.getStatus())) {
            logger.info("合同不存在或者已经失效 contractId:{},opUid:{} ,  opUname:{}",
                    templetContractId, opUid, opUname);
            return true;
        }
        int oldStatus = offlineContract.getStatus();
        toNextStatus((int) templetContractId, CustomerContractStatus.TO_EFFECT.getCode(), opUid, opUname);
        offlineContract.setStatus(CustomerContractStatus.TO_EFFECT.getCode());
        contractLogService.logStatusChange(oldStatus, WmTempletContractTransUtil.templetContractBasicDbToBo(offlineContract), opUid, opUname);
        return true;
    }

    private void copyContractFromOffToOnline(long templetContractId, int opUid, WmTempletContractDB offlineContract) {
        offlineContract.setOpuid(opUid);
        WmTempletContractDB onlineContract = wmTempletContractAuditedDBMapper.selectByPrimaryKey(templetContractId);
        if (onlineContract == null) {
            wmTempletContractAuditedDBMapper.insertSelective(offlineContract);
        } else {
            offlineContract.setVersion(onlineContract.getVersion());
            wmTempletContractAuditedDBMapper.updateByPrimaryKeySelective(offlineContract);
        }
        List<WmTempletContractSignDB> wmTempletContractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractId(templetContractId);
        wmTempletContractSignAuditedDBMapper.invalid(templetContractId, opUid);
        doSaveContractSignAuditedInfo(wmTempletContractSignDBList);
    }

    private void invalidAnotherSignTypeContract(long templetContractId, int opUid, String opUname, WmTempletContractDB wmTempletContractDB) {
        WmTempletContractTypeBo templetContractTypeBo = toAnotherSignType(wmTempletContractDB.getType());
        List<WmTempletContractDB> oldContractList = wmTempletContractDBMapper
                .selectValidByParentIdAndType(wmTempletContractDB.getParentId(), templetContractTypeBo.getTypeCode());
        if (CollectionUtils.isEmpty(oldContractList)) {
            return;
        }
        WmTempletContractSignBo newPartyBSigner = wmContractSignService.getPartyBSignerWithOutSignPhone(templetContractId);
        WmTempletContractTypeBo typeBo = new WmTempletContractTypeBo(wmTempletContractDB.getType());
        for (WmTempletContractDB oldContract : oldContractList) {
            logger.info("合同生效，作废原先旧合同  原合同id：{}  新合同id：{} opUid:{} opUname:{}",
                    oldContract.getId(), templetContractId, opUid, opUname);
            if (typeBo.getCooperateMode() == WmTempletContractTypeBo.TYPE_C2) {
                WmTempletContractSignBo oldPartyBSigner = wmContractSignService.getPartyBSignerWithOutSignPhone(oldContract.getId());
                //不是同一个合作商，不处理（客户可以绑定多个合作商的C2合同）
                if (oldPartyBSigner != null && newPartyBSigner.getSignId() != oldPartyBSigner.getSignId()) {
                    logger.info("C2合同，不是同一个合作商不删除  原合同合作商id：{}  新合同合作商id：{} opUid:{} opUname:{}", oldPartyBSigner.getSignId(), newPartyBSigner.getSignId(), opUid, opUname);
                    continue;
                }
            }
            wmTempletContractDBMapper.invalidContract(oldContract.getId(), opUid);
            wmTempletContractAuditedDBMapper.invalidContract(oldContract.getId(), opUid);
            wmTempletContractSignDBMapper.invalid(oldContract.getId(), opUid);
            wmTempletContractSignAuditedDBMapper.invalid(oldContract.getId(), opUid);
        }
    }

    private WmTempletContractTypeBo toAnotherSignType(int contractType) {
        WmTempletContractTypeBo contractTypeForAnotherSignType = new WmTempletContractTypeBo(contractType);
        int anotherSignType = (contractTypeForAnotherSignType.getSignType() + 1) / contractTypeForAnotherSignType.getSignType();
        contractTypeForAnotherSignType.setSignType(anotherSignType);
        return contractTypeForAnotherSignType;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("废除合同  contractId:{} opUid:{} opUname:{}", contractId, opUid, opUname);
        WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
        if (offlineContract == null) {
            logger.info("废除合同  contractId:{} 不存在该合同", contractId);
            return false;
        }
        logger.info("废除合同  contractId:{} 存在生效合同", contractId);
        wmTempletContractAuditedDBMapper.invalidContract((long) contractId, opUid);
        wmTempletContractSignAuditedDBMapper.invalid((long) contractId, opUid);

        contractLogService.logInvalid(offlineContract.getParentId().intValue(), contractId, offlineContract.getNumber(), opUid, opUname);
        WmTempletContractBasicBo newBo = WmTempletContractTransUtil.templetContractBasicDbToBo(offlineContract);
        newBo.setStatus(CustomerContractStatus.INVALID.getCode());

        toNextStatus(contractId, CustomerContractStatus.INVALID.getCode(), opUid, opUname);
        //调换顺序
        contractLogService.logStatusChange(offlineContract.getStatus(), newBo, opUid, opUname);
        return true;
    }

    @Override
    public Map<Integer, Boolean> expire(List<Integer> contractIdList, Integer opUid, String opUname) throws WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同类型不支持过期");
    }

    public boolean expireSingleContract(int contractId, Integer opUid, String opUname) throws WmCustomerException, TException {
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
        wmCustomerContractBo.getBasicBo().setTempletContractId(contractId);

        ContractCheckFilter.getContractExpireFilter().filter(wmCustomerContractBo, opUid, opUname);
        return executeExpire(contractId, opUid, opUname);
    }

    private boolean executeExpire(int contractId, Integer opUid, String opUname) throws WmCustomerException, TException {
        WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
        if (offlineContract == null) {
            logger.info("AbstractWmContractTempletService#executeExpire, contractId: {} 不存在该合同", contractId);
            return false;
        }
        logger.info("AbstractWmContractTempletService#executeExpire, contractId:{} 存在生效合同", contractId);
        wmTempletContractAuditedDBMapper.expiredContract(contractId, opUid);

        contractLogService.logExpire(offlineContract.getParentId().intValue(), contractId, offlineContract.getNumber(), opUid, opUname);
        WmTempletContractBasicBo newBo = WmTempletContractTransUtil.templetContractBasicDbToBo(offlineContract);
        newBo.setStatus(CustomerContractStatus.EXPIRED.getCode());

        toNextStatus(contractId, CustomerContractStatus.EXPIRED.getCode(), opUid, opUname);
        // 调换顺序
        contractLogService.logStatusChange(offlineContract.getStatus(), newBo, opUid, opUname);
        return true;
    }

    @Override
    public Long startSignForManualPack(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        return -1l;
    }

    @Override
    public LongResult applyManualTaskForBatchPlatform(EcontractBatchOpRequest opRequest) throws WmCustomerException, TException{
        logger.warn("当前任务类型不支持从批量平台创建待打包任务，opRequest:{}", JSON.toJSONString(opRequest));
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前任务类型不支持从批量平台创建待打包任务");
    }

    @Override
    public Integer createWmCustomerContract(CreateWmCustomerContractReq req) throws WmCustomerException, TException {
        logger.warn("当前合同类型不支持直接通过后端接口创建，req:{}", JSON.toJSONString(req));
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同类型不支持直接通过后端接口创建");
    }

    protected boolean canInvalidC2Contract(int contractId, int opUid) throws WmCustomerException, TException {
        WmTempletContractDB templetContractDB = ifHasContract(contractId);

        WmTempletContractSignBo partyBSignerBo = wmContractSignService.getPartyBSignerWithOutSignPhone(templetContractDB.getId());

        ifHasOnlinePoi(templetContractDB, partyBSignerBo);

        if (opUid <= 0 || WmEmployUtils.isHQ(opUid)) {
            return true;
        }

        ifCustomerOwnerForOpUid(opUid, templetContractDB);
        ifAgentEmployeeForOpUid(opUid, partyBSignerBo);
        return true;
    }


    /**
     * 是否有合同
     *
     * @param contractId
     * @return
     * @throws WmCustomerException
     */
    public WmTempletContractDB ifHasContract(int contractId) throws WmCustomerException {
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
        if (wmTempletContractDB == null) {
            logger.info("废除合同  contractId:{} 不存在合同", contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在合同，不可废除。");
        }
        return wmTempletContractDB;
    }

    /**
     * 操作人是否是客户责任人
     *
     * @param opUid
     * @param wmTempletContractDB
     * @throws WmCustomerException
     */
    private void ifCustomerOwnerForOpUid(int opUid, WmTempletContractDB wmTempletContractDB) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmTempletContractDB.getParentId().intValue());
        if (wmCustomerDB.getOwnerUid() == null || wmCustomerDB.getOwnerUid() != opUid) {
            logger.info("废除合同  contractId:{} 非客户责任人 opUid:{}  ownerUid:{}", wmTempletContractDB.getId(), opUid, wmCustomerDB.getOwnerUid());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "非客户责任人，无权限操作。");
        }
    }

    /**
     * 操作人是不是合作商BD
     *
     * @param opUid
     * @param partyBSignerBo
     * @throws TException
     * @throws WmCustomerException
     */
    private void ifAgentEmployeeForOpUid(int opUid, WmTempletContractSignBo partyBSignerBo) throws TException, WmCustomerException {
        if (MccConfig.replaceAgentService()) {
            AgentInfoTo agentInfoTo = wmContractAgentService.queryAgentByUidAndId(opUid, partyBSignerBo.getSignId());
            if (agentInfoTo == null || agentInfoTo.getId() <= 0) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "非合作商人员，无权限操作。");
            }
        } else {
            WmCommercialAgentInfo wmCommercialAgentInfo = null;
            try {
                if (partyBSignerBo != null) {
                    wmCommercialAgentInfo = wmCommercialAgentInfoThriftService.queryAgentByUidAndId(opUid, partyBSignerBo.getSignId());
                }
            } catch (WmServerException e) {
                logger.warn("查询合作商接口失败了", e);
            }
            if (wmCommercialAgentInfo == null || wmCommercialAgentInfo.getId() <= 0) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "非合作商人员，无权限操作。");
            }
        }
    }

    /**
     * 在该客户下，是否有属于该合作商已上线的门店
     *
     * @param wmTempletContractDB
     * @param partyBSignerBo
     * @throws WmCustomerException
     */
    private void ifHasOnlinePoi(WmTempletContractDB wmTempletContractDB, WmTempletContractSignBo partyBSignerBo) throws WmCustomerException {
        List<Long> wmPoiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(wmTempletContractDB.getParentId().intValue());
        if (CollectionUtils.isEmpty(wmPoiIds)) {
            return;
        }
        List<WmPoiDomain> poiDomainList = wmPoiClient.getWmPoiByIds(wmPoiIds);
        for (WmPoiDomain domain : poiDomainList) {
            if (domain.getValid() == 1 && domain.getIsDelete() == 0 && domain.getAgentId() == partyBSignerBo.getSignId()) {
                logger.info(domain.getName() + "（" + domain.getWmPoiId() + "）门店，属于该合作商，不可废除该合同。");
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该合作商下有上线中的门店，不可废除该合同。");
            }
        }
    }

    private void doSaveContractSignAuditedInfo(List<WmTempletContractSignDB> signDBList) {
        if (MccConfig.isEncryptionInsertBySingle()) {
            for (WmTempletContractSignDB signDB : signDBList) {
                wmTempletContractSignAuditedDBMapper.insert(signDB);
            }
        } else {
            wmTempletContractSignAuditedDBMapper.batchInsertOrUpdate(signDBList);
        }
    }

    /**
     * 查询客户kp是否全部生效
     *
     * @param wmCustomerId
     * @return
     * @throws WmCustomerException
     */
    protected boolean isEffectiveCustomerAndKp(int wmCustomerId) throws WmCustomerException {
        // 查询生效客户
        WmCustomerDB wmCustomerDB = wmCustomerService.selectEffectCustomerById(wmCustomerId);
        if (null == wmCustomerDB) {
            return false;
        }
        // 查询生效kp
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmCustomerId);
        if (null == wmCustomerKp) {
            return false;
        }
        return true;
    }
}
