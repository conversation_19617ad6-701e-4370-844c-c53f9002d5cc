package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY;
@Slf4j
@Service
@PdfDeleter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT)
public class TechnicalServiceDefaultDelete implements DeliveryPdfDelete {
    @Override
    public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {

        if (CollectionUtils.isEmpty(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT.getName()))) {
            Collection<SignTemplateEnum> tabList = tabPdfMap.get(TAB_DELIVERY);
            if (CollectionUtils.isNotEmpty(tabList)) {
                tabList.removeIf(value -> value.equals(SignTemplateEnum.TECHNICAL_SERVICE_FEE)
                        || value.equals(SignTemplateEnum.MULTI_TECHNICAL_SERVICE_FEE)
                        || value.equals(SignTemplateEnum.TECHNICAL_SERVICE_FEE_V2)
                        || value.equals(SignTemplateEnum.MULTI_TECHNICAL_SERVICE_FEE_V2)
                        || value.equals(SignTemplateEnum.TECHNICAL_SERVICE_FEE_V2_1)
                        || value.equals(SignTemplateEnum.MULTI_TECHNICAL_SERVICE_FEE_V2_1));
            }
        }
    }
}
