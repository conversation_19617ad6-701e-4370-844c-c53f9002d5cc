package com.sankuai.meituan.waimai.customer.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 脱敏工具
 */
public class DesensitizeUtil {

    private final static int PHONE_LEFT = 3;
    private final static int PHONE_RIGHT = 4;

    private final static int CODE_LEFT = 5;
    private final static int CODE_RIGHT = 3;

    private final static int OTHER_CODE_LEFT = 3;
    private final static int OTHER_CODE_RIGHT = 2;

    private final static int BANK_NUM_LEFT = 6;
    private final static int BANK_NUM_RIGHT = 4;

    /**
	 * 手机号码脱敏
     * @param phone
	 * @return
	 */
    public static String desensitizePhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return phone;
        }
        return desensitize(phone, PHONE_LEFT, PHONE_RIGHT);
    }

    /**
     * 银行卡号脱敏
     *
     * @param bankNum
     * @return
     */
    public static String desensitizeBankNum(String bankNum) {
        return desensitize(bankNum, BANK_NUM_LEFT, BANK_NUM_RIGHT);
    }

    private static String desensitize(String str, int left, int right) {
        if (StringUtils.isEmpty(str)) {
            return str;
        }
        StringBuilder builder = new StringBuilder(str);
        for (int i = left; i < str.length() - right; ++i) {
            builder.setCharAt(i, '*');
        }
        return builder.toString();
    }

    /**
     * 编号脱敏、身份证号脱敏
     *
     * @param code
     * @return
     */
    public static String desensitizeCode(String code) {
        if (code == null || code.isEmpty()) {
            return code;
        }
        int left = CODE_LEFT;
        int right = CODE_RIGHT;
        if (code.length() >= 4 && code.length() <= 8) {
            left = code.length() / 3;
            right = 2;
        } else if (code.length() < 4) {
            left = 0;
            right = 1;
        }
        return desensitize(code, left, right);
    }

    /**
     * 护照，港澳台等证件信息脱敏
     *
     * @param code
     * @return
     */
    public static String desensitizeOtherCode(String code) {
        if (code == null || code.isEmpty()) {
            return code;
        }
        int left = OTHER_CODE_LEFT;
        int right = OTHER_CODE_RIGHT;
        if (code.length() >= 4 && code.length() <= 8) {
            left = code.length() / 3;
            right = 2;
        } else if (code.length() < 4) {
            left = 0;
            right = 1;
        }
        return desensitize(code, left, right);
    }




    /**
     * 个人名字脱敏
     *
     * @param name
     * @return
     */
    public static String desensitizePersonalName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        if (name.length() == 2) {
            return desensitize(name, 1, 0);
        }
        if (name.length() >= 3) {
            return desensitize(name, 1, 1);
        }
        return name;
    }

    /**
     * 企业名字脱敏
     *
     * @param number
     * @return
     */
    public static String desensitizeEnterpriseNumber(String number) {
        if (StringUtils.isBlank(number)) {
            return number;
        }
        int left = 4;
        int right = 4;
        if (number.length() >= 4 && number.length() <= 8) {
            left = number.length() / 3;
            right = 2;
        } else if (number.length() < 4) {
            left = 0;
            right = 1;
        }
        return desensitize(number, left, right);
    }

    /**
     * 地址脱敏
     *
     * @param address
     * @return
     */
    public static String desensitizeAddress(String address) {
        if (StringUtils.isBlank(address)) {
            return address;
        }
        StringBuilder builder = new StringBuilder(address);
        for (int i = 0; i < address.length(); i++) {
            if (builder.charAt(i) >= '0' && builder.charAt(i) <= '9') {
                builder.setCharAt(i, '*');
            }
        }
        return builder.toString();
    }
}
