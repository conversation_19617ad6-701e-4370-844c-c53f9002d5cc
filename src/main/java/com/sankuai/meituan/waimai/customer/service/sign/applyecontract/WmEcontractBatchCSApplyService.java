package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.sign.common.WmEcontractApplyTrans;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import javax.annotation.Resource;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class WmEcontractBatchCSApplyService extends AbstractWmEcontractApplyAdapterService{
    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractBatchCSApplyService.class);

    @Resource
    private WmEcontractBatchCSmApplyService wmEcontractBatchCSmApplyService;
    @Resource
    private WmEcontractBatchCSgApplyService wmEcontractBatchCSgApplyService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil
                .selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.SETTLE);
        List<EcontractSettleInfoBo> settleInfoBo = JSON
                .parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);
        if (WmEcontractApplyTrans.isSupportWallet(settleInfoBo)) {
            return wmEcontractBatchCSmApplyService.wrapEcontractBo(batchContextBo);
        } else {
            return wmEcontractBatchCSgApplyService.wrapEcontractBo(batchContextBo);
        }
    }
}
