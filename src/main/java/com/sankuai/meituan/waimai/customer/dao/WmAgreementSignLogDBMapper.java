package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementSignLogDB;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface WmAgreementSignLogDBMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WmAgreementSignLogDB record);

    int insertSelective(WmAgreementSignLogDB record);

    WmAgreementSignLogDB selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmAgreementSignLogDB record);

    int updateByPrimaryKey(WmAgreementSignLogDB record);

    int batchInsertSelective(@Param("signLogList") List<WmAgreementSignLogDB> signLogList);

    List<WmAgreementSignLogDB> batchQueryByAggrementTypeAndAccountId(@Param("aggrementTypes") List<Integer> aggrementTypes,
                                                                     @Param("accountId")  String accountId);
}