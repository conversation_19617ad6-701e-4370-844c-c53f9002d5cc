package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.c1contract;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker
        .IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.C1CONTRACT_INFO_V4_ONLY_SHSANKUAI)
@Slf4j
@Service
public class WmEcontractC1ContractV4OnLyShSanKuaiPdfMaker implements IWmEcontractPdfContentInfoBoMaker {


    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo
            middleContext)
            throws WmCustomerException {
        log.info("#WmEcontractC1ContractV4OnLyShSanKuaiPdfMaker");
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.C1CONTRACT_INFO_V4_ONLY_SHSANKUAI;

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum
                .C1CONTRACT);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        pdfInfoBo.setPdfMetaContent(new WmEcontractC1ContractContentGenerate().generatePdfObject(originContext,
                taskBo));
        pdfInfoBo.setPdfBizContent(Lists.newArrayList());
        return pdfInfoBo;
    }


}
