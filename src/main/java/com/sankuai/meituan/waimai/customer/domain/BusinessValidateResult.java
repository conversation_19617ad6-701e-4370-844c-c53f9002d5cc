package com.sankuai.meituan.waimai.customer.domain;

import com.sankuai.meituan.waimai.thrift.customer.constant.ValidateStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateField;

import java.util.List;

public class BusinessValidateResult {
    ValidateStatus validateStatus;//校验状态
    public int licenseStatus;//营业执照状态
    List<ValidateField> validateFieldList;//对比字段

    public ValidateStatus getValidateStatus() {
        return validateStatus;
    }

    public void setValidateStatus(ValidateStatus validateStatus) {
        this.validateStatus = validateStatus;
    }

    public int getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(int licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public List<ValidateField> getValidateFieldList() {
        return validateFieldList;
    }

    public void setValidateFieldList(List<ValidateField> validateFieldList) {
        this.validateFieldList = validateFieldList;
    }
}
