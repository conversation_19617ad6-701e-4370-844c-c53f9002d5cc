package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.dianping.lion.client.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class WmCustomerRuleRunner extends DefaultRuleRunner {

    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        String validStr = binlogRawData.getColumnInfoMap().get("valid").getNewValue().toString();
        if (StringUtils.isBlank(validStr)) {
            return false;
        }

        try {
            Integer val = Integer.parseInt(validStr);
            if (val == null) {
                return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        }

    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        Integer customerId = Integer.valueOf(binlogRawData.getColumnInfoMap().get("id").getNewValue().toString());
        Integer customerRealTypeOld = null;
        if (binlogRawData.getColumnInfoMap().get("customer_real_type").getOldValue() != null) {
            customerRealTypeOld = Integer.valueOf(binlogRawData.getColumnInfoMap().get("customer_real_type").getOldValue().toString());
        }
        String customerRealTypeNew = binlogRawData.getColumnInfoMap().get("customer_real_type").getNewValue().toString();
        if (StringUtils.isBlank(customerRealTypeNew)) {
            return String.format("客户信息变更失败:%s非法的客户类型", customerId);
        }
        Integer auditStatusOld = null;
        if (binlogRawData.getColumnInfoMap().get("audit_status").getOldValue() != null) {
            auditStatusOld = Integer.valueOf(binlogRawData.getColumnInfoMap().get("audit_status").getOldValue().toString());
        }
        String auditStatusNew = binlogRawData.getColumnInfoMap().get("audit_status").getNewValue().toString();
        if (StringUtils.isBlank(auditStatusNew)) {
            return String.format("客户信息变更失败:%s非法的审核状态", customerId);
        }
        String validStr = binlogRawData.getColumnInfoMap().get("valid").getNewValue().toString();
        if (StringUtils.isBlank(validStr)) {
            return String.format("客户信息变更失败:%s非法的有效状态", customerId);
        }
        if (binlogRawData.getDmlType() == DmlType.DELETE) {
            validStr = "0";
        }
        boolean deleteFlag = false;
        if (validStr.equals("0")) {
            deleteFlag = true;
        }
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("customerId", customerId);
            params.put("customerRealTypeOld", customerRealTypeOld);
            params.put("customerRealTypeNew", Integer.valueOf(customerRealTypeNew));
            params.put("auditStatusOld", auditStatusOld);
            params.put("auditStatusNew", Integer.valueOf(auditStatusNew));
            params.put("deleteFlag", deleteFlag);
            RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService",
                    "com.sankuai.waimai.e.customer", 10000, null, "8433");
            String result = rpcService.invoke("monitorCustomerChange",
                    Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerDTO"),
                    Lists.newArrayList(JsonUtils.toJson(params)));
            if (!StringUtils.isBlank(result) && !"\"\"".equals(result) && !"null".equals(result)) {
                return result;
            }
        } catch (Exception e) {
            BcpLoggerUtils.info("类型转换失败:msg={}" + JacksonUtils.serialize(binlogRawData) + ";异常日志:" + e.getStackTrace());    // 打印日志
        }
        return null;
    }


    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }

}
