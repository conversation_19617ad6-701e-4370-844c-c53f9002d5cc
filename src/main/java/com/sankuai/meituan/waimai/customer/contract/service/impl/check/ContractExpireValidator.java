package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/5/27 14:35
 */
@Service
@Slf4j
public class ContractExpireValidator implements IContractValidator {

    @Resource
    private WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    private static final List<Integer> SUPPORT_EXPIRE_CONTRACT_TYPE = Lists.newArrayList(
            WmTempletContractTypeEnum.NATIONAL_SUBSIDY_PURCHASE_AGREEMENT.getCode()
    );

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        Long contractId = contractBo.getBasicBo().getTempletContractId();
        WmTempletContractDB wmTemplateContractPo = wmTempletContractAuditedDBMapper.selectByPrimaryKey(contractId);
        if (wmTemplateContractPo == null) {
            log.warn("ContractExpireValidator#valid, contractId: {}, 不存在合同", contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在合同, 不可操作过期");
        }
        if (!SUPPORT_EXPIRE_CONTRACT_TYPE.contains(wmTemplateContractPo.getType())) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,
                    WmTempletContractTypeEnum.getByCode(wmTemplateContractPo.getType()).getMsg() + "不支持过期操作");
        }
        if (wmTemplateContractPo.getStatus() == CustomerContractStatus.EFFECT.getCode()) {
            return true;
        }
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态不支持过期操作");
    }
}
