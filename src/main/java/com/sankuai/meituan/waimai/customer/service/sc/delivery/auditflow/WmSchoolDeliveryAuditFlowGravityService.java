package com.sankuai.meituan.waimai.customer.service.sc.delivery.auditflow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.gravity.thrift.VariableUtils;
import com.sankuai.meituan.gravity.thrift.server.*;
import com.sankuai.meituan.waimai.customer.adapter.GravityThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskDO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditResultEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

@Slf4j
@Service
public class WmSchoolDeliveryAuditFlowGravityService {

    @Autowired
    private GravityThriftServiceAdapter gravityThriftServiceAdapter;

    /**
     * 创建Gravity审批流程实例
     * @param taskDO taskDO
     * @return gravity流程实例ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String createAuditGravityInstance(WmSchoolDeliveryAuditTaskDO taskDO) throws WmSchCantException {
        ProcessInstanceCreateRequest request = new ProcessInstanceCreateRequest();
        // 业务ID = 审批任务主键ID
        request.setBusinessKey(String.valueOf(taskDO.getId()));
        request.setProcessDefinitionKey(SchoolDeliveryAuditTaskTypeEnum.getByType(taskDO.getAuditTaskType()).getFlowDefiniteKey());

        ProcessResultResponse resultResponse = gravityThriftServiceAdapter.createProcessInstanceWithReturn(request);
        if (!resultResponse.isSuccess() || resultResponse.getData() == null) {
            log.error("[WmSchoolDeliveryAuditFlowGravityService.createDeliveryAuditGravityInstance] request = {}, resultResponse = {}",
                    JSON.toJSONString(request), JSON.toJSONString(resultResponse));
            throw new WmSchCantException(SERVER_ERROR, "Gravity流程实例创建失败");
        }
        return resultResponse.getData().getId();
    }

    /**
     * 根据GravityId查询流程实例获取当前审批节点
     * @param gravityId GravityId流程实例ID
     * @return 当前审批节点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public SchoolDeliveryAuditNodeTypeEnum getGravityAuditNodeByGravityId(String gravityId) throws WmSchCantException {
        List<TaskResponse> taskResponseList = gravityThriftServiceAdapter.getTasksByProcessInstanceIds(gravityId);
        log.info("[WmSchoolDeliveryAuditFlowGravityService.getGravityAuditNodeByGravityId] taskResponseList = {}", taskResponseList);
        if (taskResponseList.size() > 1) {
            log.error("[WmSchoolDeliveryService.getGravityTaskResponseByGravityId] taskResponseList error. gravityId = {}", gravityId);
            throw new WmSchCantException(SERVER_ERROR, "查询活动任务失败");
        }

        if (CollectionUtils.isNotEmpty((taskResponseList))) {
            return SchoolDeliveryAuditNodeTypeEnum.getByActivityId(taskResponseList.get(0).getTaskDefinitionKey());
        }

        ProcessInstanceResponse instanceResponse = gravityThriftServiceAdapter.getProcessInstanceById(gravityId);
        if (instanceResponse == null) {
            log.error("[WmSchoolDeliveryService.getGravityTaskResponseByGravityId] instanceResponse is null. gravityId = {}", gravityId);
            throw new WmSchCantException(SERVER_ERROR, "查询活动任务失败");
        }
        log.info("[WmSchoolDeliveryAuditFlowGravityService.getGravityAuditNodeByGravityId] instanceResponse = {}", instanceResponse.toString());
        return SchoolDeliveryAuditNodeTypeEnum.getByActivityId(instanceResponse.getActivityId());
    }

    /**
     * 驱动Gravity对流程撤回结束
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void driveGravityProcessByAuditCancel(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException {
        log.info("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditCancel] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryAuditTaskDO auditTaskDO = auditTaskBO.getTaskDO();
        // 1-查询Gravity流程的正在活动中任务
        TaskResponse taskResponse = gravityThriftServiceAdapter.getSingleTask(auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
        if (taskResponse == null) {
            log.error("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditCancel] taskResponse is NULL. gravityId = {}, avtivityId = {}",
                    auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程任务不存在");
        }

        // 2-驱动Gravity流程进行撤回结束
        TaskActionRequest taskActionRequest = new TaskActionRequest();
        taskActionRequest.setAssignee(String.valueOf(auditTaskBO.getOpUserUid()));
        taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("auditResult", (int) SchoolDeliveryAuditResultEnum.AUDIT_END.getType()));
        ResultResponse resultResponse = gravityThriftServiceAdapter.completeTaskWithReturn(taskResponse.getId(), taskActionRequest);
        if (!resultResponse.isSuccess()) {
            log.error("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditCancel] resultResponse not success. gravityId = {}, avtivityId = {}",
                    auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程驱动失败");
        }
    }

    /**
     * 驱动Gravity对流程驳回结束
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void driveGravityProcessByAuditReject(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException {
        log.info("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditReject] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryAuditTaskDO auditTaskDO = auditTaskBO.getTaskDO();
        // 1-查询Gravity流程的正在活动中任务
        TaskResponse taskResponse = gravityThriftServiceAdapter.getSingleTask(auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
        if (taskResponse == null) {
            log.error("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditReject] taskResponse is NULL. gravityId = {}, avtivityId = {}",
                    auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程任务不存在");
        }

        // 2-驱动Gravity流程进行撤回结束
        TaskActionRequest taskActionRequest = new TaskActionRequest();
        taskActionRequest.setAssignee(String.valueOf(auditTaskBO.getOpUserUid()));
        taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("auditResult", (int) SchoolDeliveryAuditResultEnum.AUDIT_REJECT.getType()));
        ResultResponse resultResponse = gravityThriftServiceAdapter.completeTaskWithReturn(taskResponse.getId(), taskActionRequest);
        if (!resultResponse.isSuccess()) {
            log.error("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditReject] resultResponse not success. gravityId = {}, avtivityId = {}",
                    auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程驱动失败");
        }
    }

    /**
     * 驱动Gravity对流程审批通过
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void driveGravityProcessByAuditPass(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException {
        log.info("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditPass] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryAuditTaskDO auditTaskDO = auditTaskBO.getTaskDO();
        // 1-查询Gravity流程的正在活动中任务
        TaskResponse taskResponse = gravityThriftServiceAdapter.getSingleTask(auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
        if (taskResponse == null) {
            log.error("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditPass] taskResponse is NULL. gravityId = {}, avtivityId = {}",
                    auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程任务不存在");
        }

        // 2-驱动Gravity流程进行通过结束
        TaskActionRequest taskActionRequest = new TaskActionRequest();
        taskActionRequest.setAssignee(String.valueOf(auditTaskBO.getOpUserUid()));
        taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("auditResult", (int) SchoolDeliveryAuditResultEnum.AUDIT_PASS.getType()));
        ResultResponse resultResponse = gravityThriftServiceAdapter.completeTaskWithReturn(taskResponse.getId(), taskActionRequest);
        if (!resultResponse.isSuccess()) {
            log.error("[WmSchoolDeliveryAuditFlowGravityService.driveGravityProcessByAuditPass] resultResponse not success. gravityId = {}, avtivityId = {}",
                    auditTaskDO.getGravityId(), SchoolDeliveryAuditNodeTypeEnum.getByType(auditTaskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程驱动失败");
        }
    }


}
