package com.sankuai.meituan.waimai.customer.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.nibcus.inf.customer.client.enums.CertificateTypeEnum;
import com.sankuai.nibcus.inf.customer.client.enums.SignerTypeEnum;
import lombok.Data;

/**
 * 到餐客户签约人信息
 */
@Data
public class DcCustomerKp {

    /**
     * kpId
     */
    private Long id;

    /**
     * 签约人姓名
     */
    private String name;

    /**
     * 签约人类型
     */
    private SignerTypeEnum type;

    /**
     * 证件号码
     */
    private String identityCardNumber;

    /**
     * 证件类型
     */
    private CertificateTypeEnum certificateType;

    /**
     * 手机号
     */
    private String mobileNo;

    /**
     * 生效状态
     * 1有效，0无效
     */
    private Integer status;

    /**
     * kp对应的平台客户ID
     */
    private Long dcPlatformId;

}
