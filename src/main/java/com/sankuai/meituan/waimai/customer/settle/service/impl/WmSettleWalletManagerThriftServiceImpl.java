package com.sankuai.meituan.waimai.customer.settle.service.impl;

import com.sankuai.meituan.waimai.customer.settle.service.wallet.WmSettleWalletService;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmOpenWalletParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmOpenWalletResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettlePoiWalletBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmSettleWalletManagerThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WmSettleWalletManagerThriftServiceImpl implements WmSettleWalletManagerThriftService {

  private static final Logger logger = LoggerFactory.getLogger(WmSettleWalletManagerThriftServiceImpl.class);

  @Autowired
  private WmSettleWalletService wmSettleWalletService;

  @Override
  public WmOpenWalletResult openWallet(WmOpenWalletParam wmOpenWalletParam)
      throws WmCustomerException, TException {
    return null;
  }

  @Override
  public List<Long> getWalletIdListByWmPoiId(int wmPoiId) throws WmCustomerException, TException {
    return wmSettleWalletService.getWalletIdListByWmPoiId(wmPoiId);
  }

  @Override
  public void saveWmSettlePoiWalletRel(WmSettlePoiWalletBo wmSettlePoiWalletBo) {
    wmSettleWalletService.saveWmSettlePoiWalletRel(wmSettlePoiWalletBo);
  }
}
