package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant.FLOW_WITH_POI_MT_STAMP;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractSlaDataWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractSpInteractionWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 批量费率信息申请电子合同平台 费率信息流程:
 *
 * 生成pdf -> 发短信 -> 签章-> 完成合同
 */
@Service
public class WmEcontractBatchDeliverySupportApplyService extends AbstractWmEcontractApplyAdapterService {

    private static final String FLOW_BATCH_DELIVERY = "delivery";

    /** 批量费率信息申请 */
    private static final String BATCH_POIFEE_TYPE = "batch_poifee_type";

    private static List<String> flowList = Lists.newArrayList();
    private static List<String> poiStampList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_BATCH_DELIVERY);

        poiStampList.add(FLOW_BATCH_DELIVERY);

        dataWrapperMap.put(FLOW_BATCH_DELIVERY, EcontractDataWrapperEnum.BATCH_DELIVERY);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Autowired
    private WmEcontractSlaDataWrapperService wmEcontractDeliverySlaDataWrapperService;
    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Resource
    private WmEcontractSpInteractionWrapperService wmEcontractSpInteractionWrapperService;

    @Resource
    private WmEcontractCAMTWrapperService wmEcontractCAMTWrapperService;

    @Resource
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo, EcontractSignDataFactor econtractSignDataFactor)
            throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        List<String> currentFlowList = Lists.newArrayList(flowList);
        List<String> currentPoiStampList = Lists.newArrayList();
        List<String> currentMtStampList = Lists.newArrayList();
        String currentEcontractType = BATCH_POIFEE_TYPE;
        Map<String, EcontractDataWrapperEnum> currentDataWrapperMap = Maps.newHashMap(dataWrapperMap);

        if (econtractSignDataFactor.isDeliverySupportExclusive()) {
            currentPoiStampList.addAll(poiStampList);
        }

        if (econtractSignDataFactor.isDeliverySupportWholeCity()) {
            currentFlowList.add(EcontractTaskApplySubTypeEnum.DELIVERY_WHOLE_CITY.getName());
            currentDataWrapperMap.put(EcontractTaskApplySubTypeEnum.DELIVERY_WHOLE_CITY.getName(), EcontractDataWrapperEnum.BATCH_DELIVERY_WHOLE_CITY);
        }

        if (econtractSignDataFactor.isDeliverySupportAggregation()) {
            currentFlowList.add(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName());
            currentPoiStampList.add(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName());
            currentDataWrapperMap.put(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName(), EcontractDataWrapperEnum.BATCH_DELIVERY_AGGREGATION);
            currentMtStampList.add(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName());
            // 增加美团CA和美团签章数据
            batchInfoBoList.add(wmEcontractCAMTWrapperService.wrap(batchContextBo));
            batchInfoBoList.add(wmEcontractStampMTWrapperService.wrap(batchContextBo, currentMtStampList));
            // 签约流程为商家签章+美团签章
            currentEcontractType = FLOW_WITH_POI_MT_STAMP;
        }

        if (econtractSignDataFactor.isDeliverySupportCompanyCustomerLongDistanceDelivery()) {
            currentFlowList.add(EcontractTaskApplySubTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName());
            currentDataWrapperMap.put(EcontractTaskApplySubTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName(), EcontractDataWrapperEnum.BATCH_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE);
        }

        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, currentDataWrapperMap));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, currentPoiStampList));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        wmEcontractDeliverySlaDataWrapperService.addContext(batchInfoBoList, batchContextBo, EcontractDataWrapperEnum.BATCH_DELIVERY);
        wmEcontractDeliverySlaDataWrapperService.addContext(batchInfoBoList, batchContextBo, EcontractDataWrapperEnum.BATCH_DELIVERY_WHOLE_CITY);
        wmEcontractSpInteractionWrapperService.addContext(batchInfoBoList, batchContextBo, EcontractDataWrapperEnum.BATCH_DELIVERY);

        return new EcontractBatchBo.Builder().token(getToken()).econtractBizId(getBizId(batchContextBo))
                .econtractType(currentEcontractType).stageInfoBoList(batchInfoBoList).flowList(currentFlowList)
                .callBackUrl(getCallbackUrl()).build();

    }
}
