package com.sankuai.meituan.waimai.customer.service.sc;

import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.*;
import com.sankuai.meituan.waimai.infra.domain.builder.WmOrgSearchParamBuilder;
import com.sankuai.meituan.waimai.infra.service.WmOrgService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.sankuai.meituan.waimai.infra.constants.WmOrgConstant.Position.*;

@Slf4j
@Service
public class WmScEmployeeService {
    @Autowired
    private WmOrgService.Iface wmOrgService;

    /**
     * 根据uid查询城市负责人ID
     * @param userId 蜂窝负责人ID、副负责人ID
     * @return
     * @throws WmSchCantException
     */
    public WmUserVirtualOrgRel getCmUid(Integer userId) throws WmSchCantException {
        // 蜂窝负责人、副负责人、城市负责人
        List<WmOrgResult> orgResultList = getWmOrgResult(userId,  Lists.newArrayList(WM_ORG_CITY_MANAGER,WM_AOR_MAJOR,WM_AOR_MINOR));

        if(!isAorRes(orgResultList)) {
            throw new WmSchCantException(WmScCodeConstants.ORG_EXCEPTION,"登录人岗位不符合要求，不可保存。");
        }

        WmUserVirtualOrgRel wmCM = isWmCm(orgResultList);
        if(wmCM == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "上级外卖城市负责人不存在，不可保存。");
        }

        return wmCM;
    }

    /**
     * 查询外卖城市负责人
     * @param orgResultList orgResultList
     * @return  WmUserVirtualOrgRel
     */
    private WmUserVirtualOrgRel isWmCm(List<WmOrgResult> orgResultList) {
        for (WmOrgResult wmOrgResult : orgResultList) {
            WmVirtualOrg virtualOrg = wmOrgResult.getVirtualOrg();
            // 必须要是城市
            if (WmOrgConstant.OrgType.WM_ORG_CITY != virtualOrg.getOrgType()) {
                continue;
            }

            boolean isSchoolType = isSchoolType(wmOrgResult);
            if(!isSchoolType) {
                continue;
            }

            WmUserVirtualOrgRel user = getUser(wmOrgResult);
            if(user != null) {
                return user;
            }
        }
        return null;
    }

    private boolean isAorRes(List<WmOrgResult> orgResultList) {
        for (WmOrgResult wmOrgResult : orgResultList) {
            WmVirtualOrg virtualOrg = wmOrgResult.getVirtualOrg();
            // 必须要是蜂窝
            if (WmOrgConstant.OrgType.WM_AOR != virtualOrg.getOrgType()) {
                continue;
            }

            boolean isSchoolType = isSchoolType(wmOrgResult);
            if(!isSchoolType) {
                continue;
            }

            boolean isAorRes = isAorResponse(wmOrgResult);
            if(isAorRes) {
               return true;
            }
        }
        return false;
    }

    private boolean isAorResponse(WmOrgResult wmOrgResult) {
        List<WmVirtualOrgPositionUserVo> positionUserList = wmOrgResult.getPositionUsers();
        for (WmVirtualOrgPositionUserVo positionUserVo : positionUserList) {
            if (WM_AOR_MAJOR == positionUserVo.getPositonId()
                    || WM_AOR_MINOR == positionUserVo.getPositonId()) {
                return true;
            }
        }
        return false;
    }

    private WmUserVirtualOrgRel getUser(WmOrgResult wmOrgResult) {
        List<WmVirtualOrgPositionUserVo> positionUserList = wmOrgResult.getPositionUsers();
        for (WmVirtualOrgPositionUserVo positionUserVo : positionUserList) {
            if (WM_ORG_CITY_MANAGER != positionUserVo.getPositonId()) {
                continue;
            }
            List<WmUserVirtualOrgRel> users = positionUserVo.getUsers();
            if(CollectionUtils.isEmpty(users)) {
                continue;
            }
            return users.get(0);
        }
        return null;
    }
    /**
     * 团队类型：校园
     * @param wmOrgResult 组织结构信息
     * @return true：是 false：否
     */
    private boolean isSchoolType(WmOrgResult wmOrgResult) {
        List<WmVirtualOrgProperty> properties = wmOrgResult.getProperties();
        for(WmVirtualOrgProperty property : properties) {
            // 团队类型：校园
            if(WmOrgConstant.Property.WM_TEAM_TYPE != property.getId()){
                continue;
            }
            List<WmVirtualOrgPropertyValue> values = property.getValues();
            for(WmVirtualOrgPropertyValue value : values) {
                if("1".equals(value.getValue())){
                    return true;
                }
            }
        }
        return false;
    }

    private List<WmOrgResult> getWmOrgResult(Integer userId, List<Integer> positions) throws WmSchCantException {
        WmOrgSearchParam searchParam = new WmOrgSearchParamBuilder().source(WmVirtualOrgSourceEnum.WAIMAI.getSource())
                .currentUid(userId)
                .needPositionUser(positions)
                .recursive(WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP.getType())
                .needProperty(WmOrgConstant.Property.WM_TEAM_TYPE)
                .pageNo(1).pageSize(100).build();
        WmOrgListResult result;
        try {
            log.info("getWmOrgResult param:{}",searchParam);
            result = wmOrgService.search(searchParam);
            log.info("getWmOrgResult result = {}", result);
        } catch (WmServerException | TException e) {
            log.error("查询提交人组织结构信息异常.userId={}",userId);
            throw new WmSchCantException(WmScCodeConstants.ORG_EXCEPTION,"查询提交人组织结构信息异常.");
        }
        if(result == null) throw new WmSchCantException(WmScCodeConstants.ORG_EXCEPTION,"您当前岗位，不能操作食堂关联门店!");

        return result.getOrgResults();
    }

    /**
     * 根据uid查询蜂窝负责人ID
     * @param userId 学校责任人ID、食堂承包商责任人ID
     * @return
     * @throws WmSchCantException
     */
    public WmUserVirtualOrgRel getWmAorAudit(Integer userId) throws WmSchCantException {
        // 学校负责人、蜂窝负责人、外卖联络点负责人
        List<WmOrgResult> orgResultList = null;
        //  Thrift RPC 远程调用
//        orgResultList = getWmOrgResult(userId,  Lists.newArrayList(WM_ORG_CITY_MANAGER,WM_AOR_MAJOR,WM_AOR_MINOR));

        if(!isAorRes(orgResultList)) {
            throw new WmSchCantException(WmScCodeConstants.ORG_EXCEPTION,"登录人岗位不符合要求，不可保存。");
        }

        // 获取蜂窝负责人
        return null;
    }

    /**
     * 根据uid查询联络点负责人ID
     * @param userId 学校责任人ID、食堂承包商责任人ID
     * @return
     * @throws WmSchCantException
     */
    public WmUserVirtualOrgRel getWmContactPointAudit(Integer userId) throws WmSchCantException {
        // 学校负责人、蜂窝负责人、外卖联络点负责人
        List<WmOrgResult> orgResultList = null;
        //  Thrift RPC 远程调用
//        orgResultList = getWmOrgResult(userId,  Lists.newArrayList(WM_ORG_CITY_MANAGER,WM_AOR_MAJOR,WM_AOR_MINOR));

        if(!isAorRes(orgResultList)) {
            throw new WmSchCantException(WmScCodeConstants.ORG_EXCEPTION,"登录人岗位不符合要求，不可保存。");
        }

        // 获取外卖联络点负责人
        return null;
    }
}
