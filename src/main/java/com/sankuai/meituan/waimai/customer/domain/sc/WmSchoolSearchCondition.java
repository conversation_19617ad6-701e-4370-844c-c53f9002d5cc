package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.Data;

import java.util.List;

/**
 *
 */
@Data
public class WmSchoolSearchCondition {

    /**
     * 学校是否逻辑删除，1：生效、0：逻辑删除
     */
    private Integer valid;
    /**
     * 物理主键集合
     */
    private List<Integer> idList;
    /**
     * 学校ID集合
     */
    private List<Integer> schoolIdList;

    /**
     * 合同编号
     */
    private String contractNum;

    /**
     * 线索ID
     */
    private Long wdcClueId;

    /**
     * 线索iD集合
     */
    private List<Long> wdcClueIdList;

    /**
     * 合作状态
     */
    private Integer wmCoStatus;

    /**
     * 物理主键ID
     */
    private Integer id;

    /**
     * 查询条件-物理主键开始ID
     */
    private Integer beginId;

    /**
     * 查询条件-物理主键结束ID
     */
    private Integer endId;

    private Integer  pageSize;

    /**
     * 从0开始
     */
    private Integer pageFrom;

    /**
     * 主键排序规则
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.IdOrderByEnum}
     */
    private Integer idOrderBy;




}
