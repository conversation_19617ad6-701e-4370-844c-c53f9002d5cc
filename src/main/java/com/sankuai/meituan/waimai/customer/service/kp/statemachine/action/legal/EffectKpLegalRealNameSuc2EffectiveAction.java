package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.legal;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.CustomerKpBusinessService;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.SignerDBOperator;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpLegalAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpLegalEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpLegalStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpLegalStatusSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpLegalStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20230821
 * @desc 已生效KP法人实名认证通过事件
 */
@Service
@Slf4j
public class EffectKpLegalRealNameSuc2EffectiveAction extends KpLegalAbstractAction {

    @Autowired
    private CustomerKpBusinessService customerKpBusinessService;

    @Autowired
    private SignerDBOperator signerDBOperator;

    /**
     * KP法人实名认证通过
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpLegalStatusSM
     */
    @Override
    public void execute(KpLegalStateMachine from, KpLegalStateMachine to, KpLegalEventEnum eventEnum,
                        KpLegalStatusMachineContext context, KpLegalStatusSM kpLegalStatusSM) {
        try {
            //重置更新KP的状态
            context.getWmCustomerKp().setState(KpLegalStateMachine.EFFECT.getState());
            //重置上下文中KP状态
            customerKpBusinessService.updateEffectKp2Effective(context);
            //驱动客户签约人KP流程
            signerDBOperator.legalKpEffectiveDrivenSignerKpFlow(context.getWmCustomer());
        } catch (Exception e) {
            log.error("KpLegalInitCreateAction.updateEffectKp2REffective,已生效KP法人流转到实名认证成功直接生效异常,context={}", JSON.toJSONString(context), e);
            throw new StatusMachineException("已生效KP法人实名成功直接生效事件异常");
        }

    }
}
