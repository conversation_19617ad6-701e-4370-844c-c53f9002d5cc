package com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskBO;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmSchoolDeliveryStatusMachineEvent;
import com.sankuai.meituan.waimai.customer.service.sc.flow.entity.WmSchoolDeliveryStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.sc.flow.trigger.WmSchoolDeliveryMachineTrigger;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WmSchoolDeliveryFlowAbility {

    @Autowired
    private WmSchoolDeliveryMachineTrigger wmSchoolDeliveryMachineTrigger;

    /**
     * 学校交付人员指定-提交审批
     * 状态流转: 待提审/审批通过/审批驳回 -> 审批中
     */
    public void submitDeliveryAssignment(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.commitDeliveryAssignment] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryAssignmentStateMachine(WmSchoolDeliveryStatusMachineEvent.SUBMIT, context);
    }

    /**
     * 学校交付人员指定-审批通过
     */
    public void auditPassDeliveryAssignment(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditPassDeliveryAssignment] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryAssignmentStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_PASS, context);
    }

    /**
     * 学校交付人员指定-审批驳回
     */
    public void auditRejectDeliveryAssignment(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditRejectDeliveryAssignment] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryAssignmentStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_REJECT, context);
    }

    /**
     * 学校交付人员指定-审批终止
     */
    public void auditStopDeliveryAssignment(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditEndDeliveryAssignment] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryAssignmentStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_STOP, context);
    }

    private WmSchoolDeliveryStatusMachineEvent getCancelAuditBackEventByLastAuditStatus(Integer lastAuditStatus) {
        SchoolDeliveryAuditStatusEnum lastAuditStatusEnum = SchoolDeliveryAuditStatusEnum.getByType(lastAuditStatus);
        switch (lastAuditStatusEnum) {
            case PASS:
                return WmSchoolDeliveryStatusMachineEvent.BACK_TO_PASS;
            case PENDING:
                return WmSchoolDeliveryStatusMachineEvent.BACK_TO_PENDING;
            case REJECT:
                return WmSchoolDeliveryStatusMachineEvent.BACK_TO_REJECT;
            default:
                return null;
        }
    }

    /**
     * 学校交付人员指定-撤回审批回退至待提审/审批通过/审批驳回
     */
    public void auditCancelDeliveryAssignment(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditCancelDeliveryAssignment] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        WmSchoolDeliveryStatusMachineEvent event = getCancelAuditBackEventByLastAuditStatus(auditTaskBO.getTaskDO().getLastAuditStatus());
        wmSchoolDeliveryMachineTrigger.triggerDeliveryAssignmentStateMachine(event, context);
    }

    /**
     * 学校交付目标制定-提交审批
     */
    public void submitDeliveryGoalSet(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.commitDeliveryGoalSet] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryGoalSetStateMachine(WmSchoolDeliveryStatusMachineEvent.SUBMIT, context);
    }

    /**
     * 学校交付目标制定-审批通过
     */
    public void auditPassDeliveryGoalSet(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditPassDeliveryGoalSet] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryGoalSetStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_PASS, context);
    }

    /**
     * 学校交付目标制定-审批驳回
     */
    public void auditRejectDeliveryGoalSet(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditRejectDeliveryGoalSet] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryGoalSetStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_REJECT, context);
    }

    /**
     * 学校交付目标制定-审批终止
     */
    public void auditStopDeliveryGoalSet(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditEndDeliveryGoalSet] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryGoalSetStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_STOP, context);
    }

    /**
     * 学校交付目标制定-撤回审批回退至待提审/审批通过/审批驳回
     */
    public void auditCancelDeliveryGoalSet(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditCancelDeliveryGoalSet] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        WmSchoolDeliveryStatusMachineEvent event = getCancelAuditBackEventByLastAuditStatus(auditTaskBO.getTaskDO().getLastAuditStatus());
        wmSchoolDeliveryMachineTrigger.triggerDeliveryGoalSetStateMachine(event, context);
    }

    /**
     * 学校交付跟进-提交审批
     */
    public void submitDeliveryFollowUp(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.commitDeliveryFollowUp] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryFollowUpStateMachine(WmSchoolDeliveryStatusMachineEvent.SUBMIT, context);
    }

    /**
     * 学校交付跟进-审批通过
     */
    public void auditPassDeliveryFollowUp(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditPassDeliveryFollowUp] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryFollowUpStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_PASS, context);
    }

    /**
     * 学校交付跟进-审批驳回
     */
    public void auditRejectDeliveryFollowUp(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditRejectDeliveryFollowUp] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryFollowUpStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_REJECT, context);
    }

    /**
     * 学校交付跟进-审批终止
     */
    public void auditStopDeliveryFollowUp(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditEndDeliveryFollowUp] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryFollowUpStateMachine(WmSchoolDeliveryStatusMachineEvent.CRM_TICKET_STOP, context);
    }

    /**
     * 学校交付跟进-撤回审批回退至待提审/审批通过/审批驳回
     */
    public void auditCancelDeliveryFollowUp(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.auditCancelDeliveryFollowUp] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        WmSchoolDeliveryStatusMachineEvent event = getCancelAuditBackEventByLastAuditStatus(auditTaskBO.getTaskDO().getLastAuditStatus());
        wmSchoolDeliveryMachineTrigger.triggerDeliveryFollowUpStateMachine(event, context);
    }

    /**
     * 学校交付跟进-上游数据变更
     */
    public void dataChangeDeliveryFollowUp(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        log.info("[WmSchoolDeliveryFlowAbility.dataChangeDeliveryFollowUp] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryStatusMachineContext context = new WmSchoolDeliveryStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmSchoolDeliveryMachineTrigger.triggerDeliveryFollowUpStateMachine(WmSchoolDeliveryStatusMachineEvent.UPSTREAM_DATE_CHANGE, context);
    }

}
