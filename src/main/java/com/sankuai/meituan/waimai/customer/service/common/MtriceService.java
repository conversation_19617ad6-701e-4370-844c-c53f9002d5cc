package com.sankuai.meituan.waimai.customer.service.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.util.ServiceEnvUtils;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-12-07 20:32
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@Slf4j
@Service
public class MtriceService {


    /**
     * 合同保存打点
     *
     * @param contractType
     */
    public void metricContractSave(String contractType) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_SAVE_COUNT)
                    .tag("contractType", contractType)
                    .count();
        } catch (Exception e) {
            log.warn("metricContractSaveError error", e);
        }
    }

    /**
     * 合同更新打点
     *
     * @param contractType
     */
    public void metricContractUpdate(String contractType) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_UPDATE_COUNT)
                    .tag("contractType", contractType)
                    .count();
        } catch (Exception e) {
            log.warn("metricContractUpdateError error", e);
        }
    }

    /**
     * 合同提审打点
     *
     * @param contractType
     */
    public void metricContractCommitaudit(String contractType) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_COMMITAUDIT_COUNT)
                    .tag("contractType", contractType)
                    .count();
        } catch (Exception e) {
            log.warn("metricContractCommitauditError error", e);
        }
    }

    /**
     * 合同审核驳回打点
     *
     * @param contractType
     */
    public void metricContractReject(String contractType) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_REJECT_COUNT)
                    .tag("contractType", contractType)
                    .count();
        } catch (Exception e) {
            log.warn("metricContractRejectError error", e);
        }
    }

    /**
     * 合同发起签约打点
     *
     * @param contractType
     */
    public void metricContractApplySign(String contractType) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_APPLYSIGN_COUNT)
                    .tag("contractType", contractType)
                    .count();
        } catch (Exception e) {
            log.warn("metricContractApplySignError error", e);
        }
    }

    /**
     * 合同发起签约打点
     *
     * @param signType
     */
    public void metricContractApplySignFail(String signType) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_APPLYSIGN_FAIL_COUNT)
                    .tag("applySignType", signType)
                    .count();
        } catch (Exception e) {
            log.warn("metricContractApplysignFailError error", e);
        }
    }


    /**
     * 合同生效打点
     *
     * @param applyType
     */
    public void metricContractEffect(String applyType) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_EFFECT_COUNT)
                    .tag("contractType", applyType)
                    .count();
        } catch (Exception e) {
            log.warn("metricContractEffectError error", e);
        }
    }

    /**
     * 客户多店签约打点
     *
     * @param applyBo
     */
    public void mtriceApplyTaskBigCustomer(EcontractTaskApplyBo applyBo) {
        Integer poiNum = 0;
        try {
            if (applyBo == null || StringUtils.isEmpty(applyBo.getApplyInfoBo())) {
                return;
            }
            if (EcontractTaskApplyTypeEnum.BATCHPOIFEE.equals(applyBo.getApplyTypeEnum())) {
                EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSONObject.parseObject(applyBo.getApplyInfoBo(), EcontractBatchDeliveryInfoBo.class);
                poiNum = batchDeliveryInfoBo.getWmPoiIdList().size();
            } else if (EcontractTaskApplyTypeEnum.SETTLE.equals(applyBo.getApplyTypeEnum())) {
                JSONArray jsonArray = JSONArray.parseArray(applyBo.getApplyInfoBo());
                if (jsonArray != null) {
                    for (Object jsonObject : jsonArray) {
                        EcontractSettleInfoBo settleInfoBo = JSONObject.parseObject(jsonObject.toString(), EcontractSettleInfoBo.class);
                        poiNum += settleInfoBo.getPoiInfoBoList().size();
                    }
                }
            } else if (EcontractTaskApplyTypeEnum.CUSTOMER.equals(applyBo.getApplyTypeEnum())) {
                EcontractCancelAuthInfoBo cancelAuthInfoBo = JSONObject.parseObject(applyBo.getApplyInfoBo(), EcontractCancelAuthInfoBo.class);
                poiNum = cancelAuthInfoBo.getWmPoiIdList().size();
            } else if (EcontractTaskApplyTypeEnum.BATCH_POI_GENERATE_PDF.equals(applyBo.getApplyTypeEnum())) {
                EcontractBatchPoiInfoExtBo batchPoiInfoExtBo = JSONObject.parseObject(applyBo.getApplyInfoBo(), EcontractBatchPoiInfoExtBo.class);
                poiNum = batchPoiInfoExtBo.getEcontractPoiInfoExtBoList().size();
            } else {
                return;
            }

        } catch (Exception e) {
            log.warn("mtriceApplyTaskBigCustomer获取业务数据失败，不阻塞签约流程，bizId:{}，applyType:{}",
                    applyBo.getBizId(), applyBo.getApplyTypeEnum(), e);
            return;
        }
        String poiNumRange = calculatePoiNumRange(poiNum);
        String applyTaskType = applyBo.getApplyTypeEnum().getDesc();
        String tagValue = applyTaskType + "_" + poiNumRange;
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_BIGCUSTOMER_APPLYSIGN_COUNT)
                    .tag("applyType", tagValue)
                    .count();
        } catch (Exception e) {
            log.warn("mtriceApplyTaskBigCustomer error", e);
        }
        log.info("applyTaskBigCustomer，{}", tagValue + applyBo.getWmCustomerId());

        //推送消息
        if(poiNum > MccSignConfig.getBigCustomerSignPushPoiNumLimit()){
            String receivers = MccSignConfig.getBigCustomerSignMsgReceiver();
            String env = ServiceEnvUtils.getEnv();
            StringBuilder sb = new StringBuilder(env);
            sb.append("【大客户签约】：客户ID：\n");
            sb.append(applyBo.getWmCustomerId());
            sb.append("，任务类型：");
            sb.append(tagValue);
            DaxiangUtilV2.push(sb.toString(), receivers.split(","));
            log.info("大客户签约，{}", sb.toString());
        }
    }

    private String calculatePoiNumRange(Integer poiNum) {
        String poiNumRange;
        if (0 < poiNum && poiNum <= 5) {
            poiNumRange = "0-5";
        } else if (5 < poiNum && poiNum <= 10) {
            poiNumRange = "5-10";
        } else if (10 < poiNum && poiNum <= 20) {
            poiNumRange = "10-20";
        } else if (20 < poiNum && poiNum <= 50) {
            poiNumRange = "20-50";
        } else if (50 < poiNum && poiNum <= 100) {
            poiNumRange = "50-100";
        } else if (100 < poiNum && poiNum <= 200) {
            poiNumRange = "100-200";
        } else if (200 < poiNum && poiNum <= 300) {
            poiNumRange = "200-300";
        } else if (300 < poiNum && poiNum <= 400) {
            poiNumRange = "300-400";
        } else if (400 < poiNum && poiNum <= 500) {
            poiNumRange = "400-500";
        } else {
            poiNumRange = "大于500";
        }
        return poiNumRange;
    }

    /**
     * 防重过滤记录
     */
    public void metricDuplicateSign(String bizType) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CONTRACT_DUPLICATE_SIGN_COUNT)
                    .tag("bizType", bizType)
                    .count();
        } catch (Exception e) {
            log.warn("metricDuplicateSign error", e);
        }
    }

    /**
     * 全局合同相关埋点
     */
    public void metricGlobalParam(String metricName, String param) {
        try {
            MetricHelper.build()
                    .name(metricName)
                    .tag("param", param)
                    .count();
        } catch (Exception e) {
            log.warn("MetricService#metricGlobalSearchParam, warn", e);
        }
    }

    /**
     * 到餐合同保存失败埋点
     */
    public void metricFailSaveDaoCanContract(String scene) {
        try {
            MetricHelper.build()
                    .name(MetricConstant.DAO_CAN_CONTRACT_SAVE_FAIl_TAG)
                    .tag("scene", scene)
                    .count();
        } catch (Exception e) {
            log.warn("MetricService#metricFailSaveDaoCanContract, warn", e);
        }
    }

}
