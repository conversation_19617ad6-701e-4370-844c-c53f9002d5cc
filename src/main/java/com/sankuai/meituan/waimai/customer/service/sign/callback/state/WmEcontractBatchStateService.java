package com.sankuai.meituan.waimai.customer.service.sign.callback.state;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 修改batch信息
 */
@Service
public class WmEcontractBatchStateService extends AbstractWmEcontractStateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractBatchStateService.class);

    @Resource
    private WmEcontractTaskBizService wmEcontractTaskService;

    @Resource
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    public Boolean changeState(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo) {
        List<EcontractTaskBo> taskBoList = wmEcontractTaskService.getByIdListMaster(Lists.newArrayList(batchContextDB.getTaskIdAndTaskMap().keySet()));
        Long batchId = CollectionUtils.size(taskBoList) > 0 ? taskBoList.get(0).getBatchId() : 0;
        LOGGER.info("callback taskBoList = {}", JSON.toJSONString(taskBoList));
        if (isAllPass(taskBoList)) {
            return wmEcontractBatchBaseService.updateStateAndNotifyInfo(batchId, EcontractTaskStateEnum.SUCCESS.getName(), JSON.toJSONString(notifyBo));
        } else if (isFail(taskBoList)) {
            return wmEcontractBatchBaseService.updateStateAndNotifyInfo(batchId, EcontractTaskStateEnum.FAIL.getName(), JSON.toJSONString(notifyBo));
        } else if (isCancel(taskBoList)) {
            return wmEcontractBatchBaseService.updateStateAndNotifyInfo(batchId, EcontractTaskStateEnum.CANCEL.getName(), JSON.toJSONString(notifyBo));
        }
        return Boolean.FALSE;
    }


    private boolean isAllPass(List<EcontractTaskBo> taskBoList) {
        for (EcontractTaskBo taskBo:taskBoList) {
            if (!EcontractTaskStateEnum.SUCCESS.getName().equals(taskBo.getApplyState())) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    private boolean isFail(List<EcontractTaskBo> taskBoList) {
        for (EcontractTaskBo taskBo:taskBoList) {
            if (EcontractTaskStateEnum.IN_PROCESSING.getName().equals(taskBo.getApplyState())) {
                return Boolean.FALSE;
            }
            if (EcontractTaskStateEnum.FAIL.getName().equals(taskBo.getApplyState())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private boolean isCancel(List<EcontractTaskBo> taskBoList) {
        for (EcontractTaskBo taskBo:taskBoList) {
            if (EcontractTaskStateEnum.IN_PROCESSING.getName().equals(taskBo.getApplyState())) {
                return Boolean.FALSE;
            }
            if (EcontractTaskStateEnum.CANCEL.getName().equals(taskBo.getApplyState())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
