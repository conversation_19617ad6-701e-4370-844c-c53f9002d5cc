package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.action.after.oplog;

import com.sankuai.meituan.waimai.customer.adapter.WmPoiOplogThriftServiceAdaptor;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 202401117
 */
@Service
public class WmPoiOpLogAfterService {

    @Autowired
    private WmPoiOplogThriftServiceAdaptor wmPoiOplogThriftServiceAdaptor;

    /**
     * 门店绑定客户添加"门店"维度的操作记录
     *
     * @param wmCustomerDB
     * @param wmPoiIdSet
     */
    public void poiBindCustomerAddOpLogOnPoi(WmCustomerDB wmCustomerDB, Set<Long> wmPoiIdSet, Integer opUid, String opName) {

        //每个门店ID分别记录绑定操作记录在门店侧
        for (Long wmPoiId : wmPoiIdSet) {
            wmPoiOplogThriftServiceAdaptor.insertPoiOpLog(wmPoiId, String.format(CustomerConstants.POI_LOG_CHANGE_CUSTOMER, "", wmCustomerDB.getMtCustomerId() + wmCustomerDB.getCustomerName()),
                    opUid, opName, "", String.valueOf(wmCustomerDB.getMtCustomerId()));
        }

    }
}
