package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.unbind;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IUnBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.util.DateUtil;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poimanager.domain.WmPoiExtendStatusVo;
import com.sankuai.meituan.waimai.poimanager.service.WmPoiExtendStatusThriftService;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiControl;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiFlowlineQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 20240122
 * @desc 门店解绑客户校验策略
 */
@Service
@Slf4j
public class PoiUnBindCheckStrategy implements IUnBindCheckStrategy {

    @Autowired
    private WmPoiFlowlineQueryThriftService.Iface wmPoiFlowlineQueryThriftService;

    @Autowired
    private WmPoiExtendStatusThriftService.Iface wmPoiExtendStatusThriftService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    protected WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;


    protected static final Set<String> WM_POI_FIELDS = Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_TAG,
            WmPoiFieldQueryConstant.WM_POI_FIELD_POI_BIZ_TYPES,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID
    );

    /**
     * 门店解绑校验策略
     *
     * @param context
     * @throws WmCustomerException
     */
    @Override
    public void checkUnBindByParams(CustomerPoiUnBindFlowContext context) throws WmCustomerException, TException {

        log.info("PoiUnBindCheckStrategy.checkUnBindByParams,context={}", JSONObject.toJSONString(context));
        Integer customerId = context.getCustomerId();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        // 参数校验
        if (customerId == null || customerId == 0 || CollectionUtils.isEmpty(wmPoiIdSet)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }
        // 门店版本校验
        VersionCheckUtil.versionCheck(Lists.<Integer>newArrayList(customerId), Lists.<Long>newArrayList(wmPoiIdSet));
        // 门店存在性校验
        checkNotExistWmPoiId(wmPoiIdSet);
        // 门店上线状态校验
        validWmPoiStatus(wmPoiIdSet);
        // 门店是否在切换中校验
        validateInCustomerSwitchTask(wmPoiIdSet);
        // 美食城预绑定失败解绑校验
        validPreBindFail(wmPoiIdSet, customerId);
        //非预绑定失败解绑需要添加客户门店已绑定状态校验
        if (!context.isConfirmFailedToUnBindFlag()) {
            // 门店是否与传入客户绑定，且不在解绑流程中
            validCustomerUnbindPoi(wmPoiIdSet, customerId, context.isRebuildUnBindFlag());
        }
    }

    private void checkNotExistWmPoiId(Set<Long> wmPoiIdSet) throws WmCustomerException, TException {
        List<Long> notExistList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            try {
                WmPoiControl wmPoiControl = wmPoiFlowlineQueryThriftService.getWmPoiControlByWmPoiIdRT(wmPoiId);
                if (wmPoiControl == null) {
                    log.info("PoiUnBindCheckStrategy.checkNotExistWmPoiId,客户绑定门店,校验门店ID{}不存在", wmPoiId);
                    notExistList.add(wmPoiId);
                } else {
                    if (wmPoiControl.getIs_delete() == 1) {
                        log.info("PoiUnBindCheckStrategy.checkNotExistWmPoiId,客户绑定门店,校验门店ID{}已删除", wmPoiId);
                        notExistList.add(wmPoiId);
                    }
                }
            } catch (WmServerException e) {
                log.error("PoiUnBindCheckStrategy.checkNotExistWmPoiId,查询门店是否删除异常,wmPoiId={}", wmPoiId, e);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询门店是否删除异常");
            }
        }
        if (!CollectionUtils.isEmpty(notExistList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                    String.format("门店ID:%s不存在", StringUtils.join(notExistList, CustomerConstants.SPLIT_SYMBOL)));
        }
    }

    /**
     * 校验门店状态（上线门店不可解绑）
     *
     * @return
     */
    public void validWmPoiStatus(Set<Long> wmPoiIdSet) throws WmCustomerException {
        log.info("PoiUnBindCheckStrategy.validWmPoiStatus,wmPoiIdSet={}", JSONObject.toJSONString(wmPoiIdSet));
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggreList(Lists.newArrayList(wmPoiIdSet), WM_POI_FIELDS);
        Map<Long, WmPoiAggre> poiMapAggre = wmPoiAggreList.stream().collect(Collectors.toMap(wmPoiAggre -> wmPoiAggre.getWm_poi_id(), wmPoiAggre -> wmPoiAggre));
        for (Long wmPoiId : wmPoiIdSet) {
            WmPoiAggre wmPoiAggre = poiMapAggre.get(wmPoiId);
            if (wmPoiAggre == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("门店ID:%s不存在", wmPoiId));
            } else {
                if (wmPoiAggre.getValid() == WmPoiValidEnum.ONLINE.getValue()) {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                            String.format("上线门店不可解绑（门店id：%s）", wmPoiId));
                }
            }
        }
    }

    /**
     * 验证是否在正进行的客户切换任务中
     */
    public void validateInCustomerSwitchTask(Set<Long> wmPoiIdSet) throws WmCustomerException {
        try {
            List<WmPoiExtendStatusVo> statusVoList = new ArrayList<>();
            List<List<Long>> wmPoiIdListAll = Lists.partition(new ArrayList<>(wmPoiIdSet), 200);
            for (List<Long> wmPoiIdPart : wmPoiIdListAll) {
                //在客户切换任务中的任务
                List<WmPoiExtendStatusVo> retList = wmPoiExtendStatusThriftService.batchGetDoingPoiStatusInfo(wmPoiIdPart);
                statusVoList.addAll(retList);
            }

            log.info("PoiUnBindCheckStrategy.validateInCustomerSwitchTask,是否在客户切换的任务中：statusVoList={}", JSONObject.toJSONString(statusVoList));
            if (!CollectionUtils.isEmpty(statusVoList)) {
                List<Long> list = new ArrayList();
                for (WmPoiExtendStatusVo statusVo : statusVoList) {
                    list.add(statusVo.getWmPoiId());
                }
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("当前门店%s正在切换中，不可操作解绑or关联", list));
            }
        } catch (WmServerException e) {
            log.error("PoiUnBindCheckStrategy.validateInCustomerSwitchTask,查询是否在客户切换任务中异常， wmPoiId={}, e={}", wmPoiIdSet, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询是否在客户切换任务中异常");
        } catch (TException e) {
            log.error("PoiUnBindCheckStrategy.validateInCustomerSwitchTask,查询是否在客户切换任务中异常， wmPoiId={}, e={}", wmPoiIdSet, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询是否在客户切换任务中异常");
        }

    }

    /**
     * 批量解绑场景-暂不支持美食城门店预绑定状态的批量解绑,仅支持预绑定单门店失败解绑
     *
     * @param wmPoiIdSet
     */
    private boolean validPreBindFail(Set<Long> wmPoiIdSet, Integer customerId) throws WmCustomerException {
        log.info("PoiUnBindCheckStrategy.validPreBindFail wmPoiIdSet={},customerId={}", JSONObject.toJSONString(wmPoiIdSet), customerId);
        //批量超过1个门店的解绑,校验不允许预绑定流程中的门店解绑
        if (wmPoiIdSet != null && wmPoiIdSet.size() > 1) {
            Set<Long> existPreBindPoi = wmCustomerPoiDBMapper.selectExistPoiByWmPoiIdForPreBind(wmPoiIdSet);
            if (!CollectionUtils.isEmpty(existPreBindPoi)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("绑定状态非“已绑定”不支持批量解绑。（门店id：%s）", StringUtils.join(existPreBindPoi, CustomerConstants.SPLIT_SYMBOL)));
            }
        }
        //仅有1个门店解绑
        if (wmPoiIdSet != null && wmPoiIdSet.size() == 1) {
            List<WmCustomerPoiDB> wmCustomerPoiForPreBindInConfirmBinding = wmCustomerPoiDBMapper.getWmCustomerPoiForPreBind(customerId, Lists.newArrayList(wmPoiIdSet), Lists.newArrayList(CustomerRelationStatusEnum.CONFIRM_BINDING.getCode()));
            if (!CollectionUtils.isEmpty(wmCustomerPoiForPreBindInConfirmBinding)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("绑定状态非“已绑定”不支持批量解绑。（门店id：%s）", StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)));
            }
            List<WmCustomerPoiDB> wmCustomerPoiForPreBind = wmCustomerPoiDBMapper.getWmCustomerPoiForPreBind(customerId, Lists.newArrayList(wmPoiIdSet), Lists.newArrayList(CustomerRelationStatusEnum.CONFIRM_BINDING_FAIL.getCode(), CustomerRelationStatusEnum.TO_APPLY_BIND.getCode()));
            for (WmCustomerPoiDB temp : wmCustomerPoiForPreBind) {
                if (temp.getSwitchTaskId() != null && temp.getSwitchTaskId() > 0L) {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店在客户切换流程中,请在切换系统中操作");
                }
            }
            if (!CollectionUtils.isEmpty(wmCustomerPoiForPreBind)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查门店是否已绑定，并且是绑定的这个客户,且锁定这个任务
     */
    public void validCustomerUnbindPoi(Set<Long> wmPoiIdSet, Integer customerId, boolean rebuildForceUnbind) throws WmCustomerException {
        log.info("PoiUnBindCheckStrategy.validCustomerUnbindPoi wmPoiIdSet={},customerId={}", JSONObject.toJSONString(wmPoiIdSet), customerId);
        // 校验门店是否都存在绑定关系
        Set<Long> existWmPoiIds = wmCustomerPoiDBMapper.selectExistPoiByWmPoiId(wmPoiIdSet);
        if (existWmPoiIds != null && existWmPoiIds.size() != wmPoiIdSet.size()) {
            Set<Long> diff = Sets.difference(wmPoiIdSet, existWmPoiIds);
            log.info("PoiUnBindCheckStrategy.validCustomerUnbindPoi,{}没有绑定客户无法解绑", StringUtils.join(diff, CustomerConstants.SPLIT_SYMBOL));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                    String.format("门店ID:%s,没有绑定客户无法解绑", StringUtils.join(diff, CustomerConstants.SPLIT_SYMBOL)));
        }
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(customerId);
        if (!CollectionUtils.isEmpty(wmCustomerPoiDBList)) {
            List<Long> bindList = Lists.newArrayList();
            List<Long> unbindingList = Lists.newArrayList();
            for (WmCustomerPoiDB wmCustomerPoiDB : wmCustomerPoiDBList) {
                if (wmCustomerPoiDB.getIsUnbinding() == CustomerConstants.IS_UNBINDING_YES) {
                    unbindingList.add(wmCustomerPoiDB.getWmPoiId());
                } else {
                    bindList.add(wmCustomerPoiDB.getWmPoiId());
                }
            }
            List<Long> errorPoiIds = Lists.newArrayList();
            List<Long> unBindingIds = Lists.newArrayList();
            for (Long wmPoiId : wmPoiIdSet) {
                if (unbindingList.contains(wmPoiId)) {
                    unBindingIds.add(wmPoiId);
                    continue;
                }
                if (!bindList.contains(wmPoiId)) {
                    errorPoiIds.add(wmPoiId);
                }
            }
            if (!errorPoiIds.isEmpty()) {
                log.info("PoiUnBindCheckStrategy.validCustomerUnbindPoi,门店ID:{},绑定的不是该客户", StringUtils.join(errorPoiIds, CustomerConstants.SPLIT_SYMBOL));
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, String.format("门店ID:%s,绑定的不是该客户",
                        StringUtils.join(errorPoiIds, CustomerConstants.SPLIT_SYMBOL)));
            }
            if (!unBindingIds.isEmpty() && !rebuildForceUnbind) {
                log.info("PoiUnBindCheckStrategy.validCustomerUnbindPoi,门店ID:{},正在解绑中", StringUtils.join(unBindingIds, CustomerConstants.SPLIT_SYMBOL));
                if (unBindingIds.size() == 1) {
                    Map<Integer, List<Long>> poiCreateTime = wmCustomerPoiService.getSmsRecordCtimeBycustomerId(customerId, unBindingIds);
                    if (poiCreateTime == null || poiCreateTime.isEmpty()) {
                        log.error("PoiUnBindCheckStrategy.validCustomerUnbindPoi[校验客户绑定门店异常]，存在解绑中的门店找不到相应的短信签约任务，wmPoiIdSet={}, customerId={}", JSON.toJSONString(unBindingIds), customerId);
                        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                                String.format("门店ID:%s,正在解绑中", StringUtils.join(unBindingIds, CustomerConstants.SPLIT_SYMBOL)));
                    } else {
                        String dateString = DateUtil.date2StringSec(poiCreateTime.keySet().iterator().next());
                        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                                String.format("门店ID:%s,正在解绑中，解绑签约任务创建时间" + dateString, StringUtils.join(unBindingIds, CustomerConstants.SPLIT_SYMBOL)));
                    }
                } else {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                            String.format("门店ID:%s,正在解绑中", StringUtils.join(unBindingIds, CustomerConstants.SPLIT_SYMBOL)));
                }
            }
        } else {
            log.info("PoiUnBindCheckStrategy.validCustomerUnbindPoi,客户ID:{},没有绑定门店", customerId);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, String.format("客户ID:%s,没有绑定门店", customerId));
        }
    }
}
