package com.sankuai.meituan.waimai.customer.domain;

import lombok.Data;

@Data
public class WmCustomerQueryVo {
    /**
     * 客户id
     */
    private Integer customerId;
    /**
     * 最小客户id
     */
    private Integer minCustomerId;
    /**
     * 责任人id
     */
    private Integer ownerUid;
    /**
     * 是否末级客户
     */
    private Integer isLeaf;
    /**
     * 客户类型
     */
    private Integer customerRealType;
    /**
     * 证件类型
     */
    private Integer customerType;
    /**
     * 客户状态
     */
    private Integer effective;

    /**
     * 上级客户id
     */
    private Integer superCustomerId;
    /**
     * 签约类型
     */
    private Integer signMode;
    /**
     * 获取数据量大小
     */
    private int pageSize = 10;

    /**
     * 最大客户id
     */
    private Integer maxCustomerId;

    /**
     * 获取偏移量
     */
    private Integer pageFrom;

}
