package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY_AGGREGATION;
import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_2)
public class AggregationInfoSG22Split implements DeliveryPdfSplit {
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));

        if (feeMode == LogisticsFeeModeEnum.SHANGOU_2_2
                && SUPPORT_MARK.equals(deliveryInfoBo.getSupportAggregationDelivery())
                && tabPdfMap.get(TAB_DELIVERY_AGGREGATION).contains(SignTemplateEnum.DELIVERY_MULTI_AGGREGATION_INFO_SG22)) {
            List<String> aggregationDefault = pdfDataMap.get(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_2.getName());
            if (CollectionUtils.isEmpty(aggregationDefault)) {
                aggregationDefault = Lists.newArrayList();
                aggregationDefault.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                aggregationDefault.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_2.getName(), aggregationDefault);
            log.info("ADD TO AGGREGATION_DELIVERY_SHANGOUV2_2，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());

        }
    }
}
