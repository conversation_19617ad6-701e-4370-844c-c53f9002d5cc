package com.sankuai.meituan.waimai.customer.service.customer.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.MscUsedPoiDetailDTO;
import lombok.Data;

import java.util.List;

/**
 * 美食城客户已占用档口数
 */
@Data
public class CustomerMscUsedPoiDetailDTO {

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 总档口数
     */
    private Integer allPoiCnt;
    /**
     * 已占用档口数
     */
    private Integer usedPoiCnt;


    /**
     * 已占用档口的门店信息列表
     */
    private List<MscPoiInfoDTO> mscPoiInfoDTOList;
}
