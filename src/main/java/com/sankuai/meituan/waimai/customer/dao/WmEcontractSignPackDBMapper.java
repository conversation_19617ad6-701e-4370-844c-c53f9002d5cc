package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public interface WmEcontractSignPackDBMapper {

    int insert(WmEcontractSignPackDB record);

    int insertSelective(WmEcontractSignPackDB record);

    WmEcontractSignPackDB selectByPrimaryKey(Long id);

    List<WmEcontractSignPackDB> selectByPrimaryKeyBatch(@Param("idList") List<Long> idList);

    WmEcontractSignPackDB selectByPrimaryKeyMaster(Long id);

    List<Long> queryPackIdByStatusList(@Param("packIdList") List<Long> packIdList, @Param("statusList") List<Integer> statusList);

    int updateByPrimaryKeySelective(WmEcontractSignPackDB record);

    int updateByPrimaryKey(WmEcontractSignPackDB record);

    int updateRecordBatchById(@Param("id") Long id, @Param("recordBatchId") Long recordBatchId);

    int updateStatusByRecordId(@Param("id") Long id, @Param("status") Integer status);

    WmEcontractSignPackDB selectByRecordBatchId(@Param("recordBatchId") Long recordBatchId);

}