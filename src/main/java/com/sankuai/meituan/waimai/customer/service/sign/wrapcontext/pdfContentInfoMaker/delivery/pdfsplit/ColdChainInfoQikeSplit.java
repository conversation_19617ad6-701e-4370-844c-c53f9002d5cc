package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/6/28 11:02
 */
@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.COLD_CHAIN_INFO_QIKE)
public class ColdChainInfoQikeSplit implements DeliveryPdfSplit {
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {

        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();

        // 是否有冷链
        if (deliveryInfoBo.getColdChainInfoBo() != null) {
            List<String> coldChainList = pdfDataMap.get(DeliveryPdfDataTypeEnum.COLD_CHAIN_INFO_QIKE.getName());
            if (CollectionUtils.isEmpty(coldChainList)) {
                // 未加入任何门店，则初始化为空数组
                coldChainList = Lists.newArrayList();
                coldChainList.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                // 加入当前门店
                coldChainList.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.COLD_CHAIN_INFO_QIKE.getName(), coldChainList);
            log.info("ColdChainInfoQikeSplit#split，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }

    }
}
