package com.sankuai.meituan.waimai.customer.service.sc.delivery;

import com.meituan.gecko.boot.domain.PageVo;
import com.meituan.gecko.boot.util.Assert;
import com.meituan.gecko.boot.util.Bssert;
import com.meituan.gecko.boot.util.JacksonUtils;
import com.sankuai.meituan.waimai.customer.adapter.DeliverThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmCampusContactServiceAdapter;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryMetricMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryRecordMapper;
import com.sankuai.meituan.waimai.customer.domain.EsSearchResult;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.customer.service.common.ExcelExportService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService;
import com.sankuai.meituan.waimai.customer.service.sc.thrift.WmSchoolDeliverAssemble;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.rice.eagle.utils.EagleSearchModelConverter;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmScDeliveryPlanHandlerDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.NewWmSchoolDeliverThriftService;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contract.ContractApplyInfoDto;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverInstanceDto;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverItemDto;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学校交付系统新服务
 *
 * <p>主要功能:
 * <ul>
 *   <li>交付列表的搜索和分页查询</li>
 *   <li>交付计划聚合数据的构建和索引</li>
 *   <li>交付数据的导出处理</li>
 *   <li>流程节点数据的导出</li>
 * </ul>
 *
 * <p>主要职责:
 * <ul>
 *   <li>处理交付列表的搜索请求,返回分页结果</li>
 *   <li>根据交付ID构建交付计划聚合对象</li>
 *   <li>处理交付数据的导出,生成Excel文件</li>
 *   <li>处理流程节点数据的导出,生成Excel文件</li>
 *   <li>维护交付计划的ES索引</li>
 * </ul>
 *
 * <p>使用场景:
 * <ul>
 *   <li>学校交付列表页面的搜索和展示</li>
 *   <li>交付数据的导出下载</li>
 *   <li>流程节点数据的导出下载</li>
 *   <li>交付计划数据的索引维护</li>
 * </ul>
 */
@Slf4j
@Service
public class WmSchoolDeliveryNewService {

    @Resource
    private WmSchoolDeliverySearchService wmSchoolDeliverySearchService;

    @Resource
    private ExcelExportService excelExportService;

    @Resource
    private WmScSchoolService wmScSchoolService;

    @Resource
    private WmCampusContactServiceAdapter wmCampusContactServiceAdapter;

    @Resource
    private DeliverThriftServiceAdapter deliverThriftServiceAdapter;

    @Resource
    private WmSchoolDeliveryRecordMapper wmSchoolDeliveryRecordMapper;

    @Resource
    private NewWmSchoolDeliverThriftService wmScDeliveryPlanAggThriftService;

    @Resource
    private WmSchoolDeliverAssemble wmSchoolDeliverAssemble;

    @Resource
    private WmSchoolDeliveryMetricMapper wmSchoolDeliveryMetricMapper;

    public PageVo<WmSchoolDeliveryPlanAggr> search(WmSchoolDeliverySearchModel searchParams) {

        Assert.throwIfNull(searchParams, "searchParams不能为null");
        Bssert.throwIfNull(searchParams.getPageNum(), "分页参数pageNum不能为null");
        Bssert.throwIfNull(searchParams.getPageSize(), "分页参数pageSize不能为null");

        log.info("搜索校园交付列表, searchModel={}", JacksonUtils.toJson(searchParams));
        SearchSourceBuilder sourceBuilder = EagleSearchModelConverter.convert2QueryBuilder(searchParams);
        EsSearchResult<Long> searchResult = wmSchoolDeliverySearchService.searchForId(sourceBuilder);

        List<WmSchoolDeliveryPlanAggr> datas = searchResult.getData().stream()
                .map(this::buildPlaneAggrById)
                .collect(Collectors.toList());

        PageVo<WmSchoolDeliveryPlanAggr> result = new PageVo<>();
        result.setPage(Long.valueOf(searchResult.getTotal()).intValue(), searchParams.getPageNum(), searchParams.getPageSize(), datas);
        return result;
    }

    /**
     * 根据ID构建校园交付计划索引
     * @param deliveryId
     */
    public void indexDeliveryPlanById(Long deliveryId) {

        if (Objects.isNull(deliveryId)) {
            return;
        }

        log.info("构建校园交付计划索引, deliveryId={}", deliveryId);
        WmSchoolDeliveryPlanAggr wmSchoolDeliveryPlanAggr = buildPlaneAggrById(deliveryId);

        if (Objects.isNull(wmSchoolDeliveryPlanAggr)) {
            log.warn("未查询到交付计划信息, deliveryId={}", deliveryId);
            return;
        }

        wmSchoolDeliverySearchService.upsert(wmSchoolDeliveryPlanAggr);
    }

    /**
     * 生成Excel下载链接
     *
     * @param wmSchoolDeliveryPlanAggrs
     * @param user
     * @return
     */
    public String exportAndUploadExcel(List<WmSchoolDeliveryPlanAggr> wmSchoolDeliveryPlanAggrs, SSOUtil.SsoUser user) {

        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            List<WmSchoolDeliveryPlanExcelModel> excelInfoDTOList = convertToExcelInfoDTOList(wmSchoolDeliveryPlanAggrs);
            return excelExportService.exportExcel(excelInfoDTOList, WmSchoolDeliveryPlanExcelModel.class, os, user);
        } catch (Exception e) {
            log.error("WmSchoolDeliveryNewService#exportAndUploadExcel, error", e);
            return null;
        }
    }

    /**
     * 导出流程节点相关信息
     */
    public String exportItemExcel(List<WmSchoolDeliveryPlanAggr> wmSchoolDeliveryPlanAggrs, SSOUtil.SsoUser user) {
        if (CollectionUtils.isEmpty(wmSchoolDeliveryPlanAggrs)) {
            return null;
        }
        wmSchoolDeliveryPlanAggrs = wmSchoolDeliveryPlanAggrs.stream()
                .filter(aggr -> aggr.getDeliverInstance().getDeliverStatus() == 2)
                .collect(Collectors.toList());

        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            List<WmSchoolItemExportModel> excelInfoDTOList = convertToItemExcelInfoDTOList(wmSchoolDeliveryPlanAggrs);
            return excelExportService.exportExcel(excelInfoDTOList, WmSchoolItemExportModel.class, os, user);
        } catch (Exception e) {
            log.error("WmGlobalEcontractOperationService#exportAndUploadExcel, error", e);
            return null;
        }
    }

    private List<WmSchoolItemExportModel> convertToItemExcelInfoDTOList(List<WmSchoolDeliveryPlanAggr> aggrs) {
        if (CollectionUtils.isEmpty(aggrs)) {
            return Collections.emptyList();
        }

        return aggrs.stream().map(this::convert2ItemModel).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<WmSchoolDeliveryPlanExcelModel> convertToExcelInfoDTOList(List<WmSchoolDeliveryPlanAggr> aggrs) {
        if (CollectionUtils.isEmpty(aggrs)) {
            return Collections.emptyList();
        }

        return aggrs.stream().map(this::convert).collect(Collectors.toList());
    }

    private WmSchoolDeliveryPlanExcelModel convert(WmSchoolDeliveryPlanAggr aggr) {
        if (Objects.isNull(aggr)) {
            return null;
        }
        //TODO: 待补齐
        WmSchoolDeliveryPlanExcelModel excelModel = new WmSchoolDeliveryPlanExcelModel();
        try {
            WmScDeliveryPlanHandlerDTO handlerDTO = wmSchoolDeliverAssemble.convert(aggr);
            BeanUtils.copyProperties(handlerDTO, excelModel);
            convertDeliveryExcelModel(handlerDTO, excelModel);
        } catch (Exception e) {
            log.error("WmSchoolDeliveryNewService#convert, error", e);
        }
        return excelModel;
    }

    private void convertDeliveryExcelModel(WmScDeliveryPlanHandlerDTO handlerDTO, WmSchoolDeliveryPlanExcelModel excelModel) {
        //遍历excelModel对象的属性，如果属性有值为null,更改该值为““
        Field[] fields = excelModel.getClass().getDeclaredFields();
        for (Field field : fields) {
            // 跳过 JaCoCo 相关字段
            if (field.getName().contains("$jacoco")) {
                continue;
            }

            field.setAccessible(true);
            try {
                if (Objects.isNull(field.get(excelModel))) {
                    field.set(excelModel, "");
                }
            } catch (Exception e) {
                log.error("WmSchoolDeliveryNewService#convertDeliveryExcelModel, error", e);
            }
        }
        //deliverId
        excelModel.setDeliverId(handlerDTO.getDeliverId() != null ? String.valueOf(handlerDTO.getDeliverId()) : "");
        //schoolId
        excelModel.setSchoolId(handlerDTO.getSchoolId() != null ? String.valueOf(handlerDTO.getSchoolId()) : "");
        //OnlineStallPercent
        excelModel.setOnlineStallPercent(handlerDTO.getOnlineStallPercent() != null ? String.valueOf(handlerDTO.getOnlineStallPercent()) : "");
        //onlineStallPercentIn7Days
        excelModel.setOnlineStallPercentIn7Days(handlerDTO.getOnlineStallPercentIn7Days() != null ? String.valueOf(handlerDTO.getOnlineStallPercentIn7Days()) : "");
        //finishDaysCount
        excelModel.setFinishDaysCount(handlerDTO.getFinishDaysCount() != null ? String.valueOf(handlerDTO.getFinishDaysCount()) : "");
        //endTimes
        excelModel.setEndTimes(handlerDTO.getEndTimes() != null ? String.valueOf(handlerDTO.getEndTimes()) : "");
        //deepFinishDaysCount
        excelModel.setDeepFinishDaysCount(handlerDTO.getDeepFinishDaysCount() != null ? String.valueOf(handlerDTO.getDeepFinishDaysCount()) : "");
        //order60TargetCompletionDaysCount
        excelModel.setOrder60TargetCompletionDaysCount(handlerDTO.getOrder60TargetCompletionDaysCount() != null ? String.valueOf(handlerDTO.getOrder60TargetCompletionDaysCount()) : "");
        //order90TargetCompletionDaysCount
        excelModel.setOrder90TargetCompletionDaysCount(handlerDTO.getOrder90TargetCompletionDaysCount() != null ? String.valueOf(handlerDTO.getOrder90TargetCompletionDaysCount()) : "");
        //firstBatchDeliveryNumber
        excelModel.setFirstBatchDeliveryNumber(handlerDTO.getFirstBatchDeliveryNumber() != null ? String.valueOf(handlerDTO.getFirstBatchDeliveryNumber()) : "");
        //secondBatchDeliveryNumber
        excelModel.setSecondBatchDeliveryNumber(handlerDTO.getSecondBatchDeliveryNumber() != null ? String.valueOf(handlerDTO.getSecondBatchDeliveryNumber()) : "");
        //totalDeliveryNumber
        excelModel.setTotalDeliveryNumber(handlerDTO.getTotalDeliveryNumber() != null ? String.valueOf(handlerDTO.getTotalDeliveryNumber()) : "");
        //offlineStallNum
        excelModel.setOfflineStallNum(handlerDTO.getOfflineStallNum() != null ? String.valueOf(handlerDTO.getOfflineStallNum()) : "");
        //onlineStallNum
        excelModel.setOnlineStallNum(handlerDTO.getOnlineStallNum() != null ? String.valueOf(handlerDTO.getOnlineStallNum()) : "");
        //onlineStallNumIn7Days
        excelModel.setOnlineStallNumIn7Days(handlerDTO.getOnlineStallNumIn7Days() != null ? String.valueOf(handlerDTO.getOnlineStallNumIn7Days()) : "");
        //totalOrderYesterday
        excelModel.setTotalOrderYesterday(handlerDTO.getTotalOrderYesterday() != null ? String.valueOf(handlerDTO.getTotalOrderYesterday()) : "");
        //totalOrderLastWeek
        excelModel.setTotalOrderLastWeek(handlerDTO.getTotalOrderLastWeek() != null ? String.valueOf(handlerDTO.getTotalOrderLastWeek()) : "");
    }

    private WmSchoolItemExportModel convert2ItemModel(WmSchoolDeliveryPlanAggr aggr) {
        //TODO: 待补齐
        WmSchoolItemExportModel excelModel = new WmSchoolItemExportModel();
        try {
            WmScDeliveryPlanHandlerDTO handlerDTO = wmSchoolDeliverAssemble.convert(aggr);

            DeliverInstanceDto deliverInstance = aggr.getDeliverInstance();

            excelModel.setSchoolId(String.valueOf(handlerDTO.getSchoolId()));
            excelModel.setSchoolName(handlerDTO.getSchoolName());
            excelModel.setCampusKa(handlerDTO.getCampusKa());
            excelModel.setCampusKaTeam(handlerDTO.getCampusKaTeam());
            excelModel.setChannel(handlerDTO.getChannel());
            excelModel.setChannelTeam(handlerDTO.getChannelTeam());
            excelModel.setBd(handlerDTO.getBd());
            excelModel.setBdTeam(handlerDTO.getBdTeam());
            excelModel.setDeliverCompleteDate(Objects.nonNull(deliverInstance.getFinishTimestamp()) ? wmSchoolDeliverAssemble.calculateAfterDateWithIgnore(deliverInstance.getFinishTimestamp()).toString() : null);
            excelModel.setDeliverCompleteTime(String.valueOf(wmSchoolDeliverAssemble.calculateBetweenDaysWithIgnore(deliverInstance.getCreateTimestamp(), deliverInstance.getFinishTimestamp())));

            //对应各个事项信息导出
            excelModel.setCreateTargetDate(getItemFinishTime(deliverInstance, "createTarget").toString());
            excelModel.setCreateTargetTime(String.valueOf(getItemFinishDays(deliverInstance, "createTarget")));

            excelModel.setSendCommitmentDate(getItemFinishTime(deliverInstance, "sendCommitment").toString());
            excelModel.setSendCommitmentTime(String.valueOf(getItemFinishDays(deliverInstance, "sendCommitment")));

            excelModel.setCollectInfoDate(getItemFinishTime(deliverInstance, "collectInfo").toString());
            excelModel.setCollectInfoTime(String.valueOf(getItemFinishDays(deliverInstance, "collectInfo")));

            excelModel.setReturnCommitmentDate(getItemFinishTime(deliverInstance, "returnCommitment").toString());
            excelModel.setReturnCommitmentTime(String.valueOf(getItemFinishDays(deliverInstance, "returnCommitment")));

            excelModel.setCreateLeadDate(getItemFinishTime(deliverInstance, "createLead").toString());
            excelModel.setCreateLeadTime(String.valueOf(getItemFinishDays(deliverInstance, "createLead")));

            excelModel.setOrderDate(getItemFinishTime(deliverInstance, "order").toString());
            excelModel.setOrderTime(String.valueOf(getItemFinishDays(deliverInstance, "order")));

            excelModel.setOpenSiteDate(getItemFinishTime(deliverInstance, "openSite").toString());
            excelModel.setOpenSiteTime(String.valueOf(getItemFinishDays(deliverInstance, "openSite")));

            excelModel.setSendTargetEmailDate(getItemFinishTime(deliverInstance, "sendTargetEmail").toString());
            excelModel.setSendTargetEmailTime(String.valueOf(getItemFinishDays(deliverInstance, "sendTargetEmail")));

            excelModel.setReturnTargetDate(getItemFinishTime(deliverInstance, "returnTarget").toString());
            excelModel.setReturnTargetTime(String.valueOf(getItemFinishDays(deliverInstance, "returnTarget")));

            excelModel.setPartnerSignDate(getItemFinishTime(deliverInstance, "partnerSign").toString());
            excelModel.setPartnerSignTime(String.valueOf(getItemFinishDays(deliverInstance, "partnerSign")));

            excelModel.setPoiTrainDate(getItemFinishTime(deliverInstance, "poiTrain").toString());
            excelModel.setPoiTrainTime(String.valueOf(getItemFinishDays(deliverInstance, "poiTrain")));

            excelModel.setPostageEntryDate(getItemFinishTime(deliverInstance, "postageEntry").toString());
            excelModel.setPostageEntryTime(String.valueOf(getItemFinishDays(deliverInstance, "postageEntry")));

            excelModel.setCapacityReserveDate(getItemFinishTime(deliverInstance, "capacityReserve").toString());
            excelModel.setCapacityReserveTime(String.valueOf(getItemFinishDays(deliverInstance, "capacityReserve")));

            excelModel.setOnlineDate(getItemFinishTime(deliverInstance, "online").toString());
            excelModel.setOnlineTime(String.valueOf(getItemFinishDays(deliverInstance, "online")));

        } catch (Exception e) {
            log.error("WmSchoolDeliveryNewService#convert2ItemModel, error", e);
        }
        return excelModel;
    }

    private LocalDate getItemFinishTime(DeliverInstanceDto deliverInstance, String itemCode) {
        DeliverItemDto itemDto = deliverInstance.getDeliverItemDtoList().stream()
                .filter(item -> Objects.equals(item.getItemCode(), itemCode))
                .findFirst().orElseThrow(() -> new RuntimeException("查找不到对应事项实例"));

        if (!Objects.equals(itemDto.getItemFinishStatus(), 1)) {
            long timestampInSeconds = 0; // Unix时间戳为0代表1970年1月1日

            // 转换为LocalDate
            LocalDate date = Instant.ofEpochSecond(timestampInSeconds)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        }

        return wmSchoolDeliverAssemble.calculateAfterDateWithIgnore(itemDto.getFinishTimestamp());
    }

    private Integer getItemFinishDays(DeliverInstanceDto deliverInstance, String itemCode) {
        DeliverItemDto itemDto = deliverInstance.getDeliverItemDtoList().stream()
                .filter(item -> Objects.equals(item.getItemCode(), itemCode))
                .findFirst().orElseThrow(() -> new RuntimeException("查找不到对应事项实例"));

        if (!Objects.equals(itemDto.getItemFinishStatus(), 1)) {
            return -1;
        }

        return wmSchoolDeliverAssemble.calculateBetweenDaysWithIgnore(deliverInstance.getCreateTimestamp(), itemDto.getFinishTimestamp());
    }

    @SneakyThrows
    public WmSchoolDeliveryPlanAggr buildPlaneAggrById(Long deliveryId) {
        if (Objects.isNull(deliveryId)) {
            return null;
        }

        DeliverInstanceDto deliverInstanceDto = deliverThriftServiceAdapter.findDeliverInstanceById(deliveryId);
        if (Objects.isNull(deliverInstanceDto)) {
            log.error("根据id未找到交付信息, 交付id:{}", deliveryId);
            return null;
        }

        List<ContractApplyInfoDto> resp = wmCampusContactServiceAdapter.getSchoolContractAppliesByOpportunity(deliverInstanceDto.getOpportunityId());
        if (CollectionUtils.isEmpty(resp)) {
            log.error("根据商机未找到合同申请信息, 商机id:{}", deliverInstanceDto.getOpportunityId());
            return null;
        }
        ContractApplyInfoDto contractApplyDto = resp.get(0);

        WmSchoolDB wmSchoolDB = wmScSchoolService.findByIdList(contractApplyDto.getSchoolId().intValue() - 10000);
        if (Objects.isNull(wmSchoolDB)) {
            log.error("未找到学校相关信息, 学校id:{}", contractApplyDto.getSchoolId());
            return null;
        }

        List<WmSchoolDeliveryRecord> records = wmSchoolDeliveryRecordMapper.selectByIds(Collections.singletonList(deliveryId));
        WmSchoolDeliveryRecord record = records.stream().findFirst().orElse(null);
        log.info("records:{}", JacksonUtils.toJson(record));

        List<WmSchoolDeliveryMetric> wmSchoolDeliveryMetrics = wmSchoolDeliveryMetricMapper.selectBySchoolId(contractApplyDto.getSchoolId());
        WmSchoolDeliveryCurrentSituation situation = wmSchoolDeliveryMetrics.stream().findFirst().map(metric -> {
            return WmSchoolDeliveryCurrentSituation.builder()
                    .deliverId(deliveryId)
                    .onlineStallNum(metric.getPoiNumOpen())
                    .onlineStallNumIn7Days(metric.getPoiNumOpen7d())
                    .totalOrder4Yesterday(metric.getOrdNum())
                    .ordNum7dAvg(metric.getOrdNum7dAvg())
                    .build();
        }).orElse(null);
        log.info("situation: {}", JacksonUtils.toJson(situation));

        return WmSchoolDeliveryPlanAggr.builder()
                .deliverInstance(deliverInstanceDto)
                .contractApplyDto(contractApplyDto)
                .wmSchoolDB(wmSchoolDB)
                .wmSchoolDeliveryRecord(record)
                .wmSchoolDeliveryCurrentSituation(situation)
                .build();
    }

}
