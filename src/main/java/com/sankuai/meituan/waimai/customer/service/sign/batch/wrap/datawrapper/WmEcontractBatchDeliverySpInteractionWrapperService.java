package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.aspect.SpInteractionWrapper;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.ExtendDataBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.H5ActionConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@SpInteractionWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_DELIVERY)
public class WmEcontractBatchDeliverySpInteractionWrapperService implements IWmEcontractSpInteractionWrapperService {

    public static final String SUPPORT_MARK = "support";

    @Override
    public ExtendDataBo wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {

        ExtendDataBo result = null;

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = null;

        try {
            econtractBatchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        } catch (Exception e) {
            log.warn("数据解析异常", e);
            return result;
        }

        if (econtractBatchDeliveryInfoBo == null) {
            return result;
        }

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = econtractBatchDeliveryInfoBo.getEcontractDeliveryInfoBoList();

        if (CollectionUtils.isEmpty(econtractDeliveryInfoBoList)) {
            return result;
        }

        for (EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {
            if (SUPPORT_MARK.equals(temp.getSupportSLA())) {
                List<String> actionLimit = temp.getActionLimit();
                if (CollectionUtils.isNotEmpty(actionLimit) && actionLimit.contains(H5ActionConstants.MERCHANT_CANCEL_PERMISSION_DENY)) {
                    result = new ExtendDataBo();
                    result.setSignForce(true);
                    result.setCancleTipText(
                            ConfigUtilAdapter.getString("delivery_cancel_deny_tips", WmEcontractConstant.DEFAULT_DELIVERY_CANCEL_DENY_TIPS)
                                    .replace("{date}", MoreObjects.firstNonNull(temp.getSlaValidate(), "")));
                    break;
                }
            }
        }
        return result;
    }
}
