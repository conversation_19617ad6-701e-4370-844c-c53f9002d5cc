package com.sankuai.meituan.waimai.customer.service.customer;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerMscUsedPoiDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Service
public class WmCustomerPoiRelService {

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;
    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private CustomerTaskService customerTaskService;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("CUSTOMER_POI_REL_POOL_%d").build();
    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(10, 30, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(200), THREAD_FACTORY,
                    new RejectedExecutionHandler() {
                        @Override
                        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                            if (!executorService.isShutdown()) {
                                try {
                                    executor.getQueue().put(r);
                                } catch (InterruptedException e) {
                                    throw new RejectedExecutionException("Reject from " + executor.toString());
                                }
                            } else {
                                throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                            }
                        }
                    }));

    // 确认解绑中-客户切换不下线任务发起
    public void confirmUnBind(Set<Long> wmPoiIdSet, Long switchTaskId, Integer customerId, Map<Long, Integer> poiAndTaskMaps) {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = MccCustomerConfig.getBatchDealCustomerPoiCount();
                    List<Set<Long>> list = Lists.newArrayList();
                    List<List<WmCustomerPoiDB>> lists = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        list.add(wmPoiIdSet);
                        List<WmCustomerPoiDB> poiDBList = Lists.newArrayList();
                        for (Long wmPoiId : wmPoiIdSet) {
                            Integer bizTaskId = (CollectionUtils.isEmpty(poiAndTaskMaps) || poiAndTaskMaps.get(wmPoiId) == null) ? 0 : poiAndTaskMaps.get(wmPoiId);
                            WmCustomerPoiDB wmCustomerPoiDB = getConfirmUnBindingDB(customerId, wmPoiId, switchTaskId, bizTaskId);
                            poiDBList.add(wmCustomerPoiDB);
                        }
                        lists.add(poiDBList);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        List<WmCustomerPoiDB> batchList = Lists.newArrayList();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            Integer bizTaskId = (CollectionUtils.isEmpty(poiAndTaskMaps) || poiAndTaskMaps.get(wmPoiId) == null) ? 0 : poiAndTaskMaps.get(wmPoiId);
                            WmCustomerPoiDB wmCustomerPoiDB = getConfirmUnBindingDB(customerId, wmPoiId, switchTaskId, bizTaskId);
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                                batchList = Lists.newArrayList();
                            }
                            batchSet.add(wmPoiId);
                            batchList.add(wmCustomerPoiDB);
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                                lists.add(batchList);
                            }
                            num++;
                        }
                    }
                    //门店任务关联MAP为空则走原更新方法
                    if (CollectionUtils.isEmpty(poiAndTaskMaps)) {
                        for (Set<Long> set : list) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelation(set, CustomerConstants.IS_UNBINDING_YES, CustomerRelationStatusEnum.CONFIRM_UNBINDING.getCode(), switchTaskId, customerId);
                            Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                        }
                    } else {
                        for (List<WmCustomerPoiDB> poiDBList : lists) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationByList(poiDBList);
                            Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                        }
                    }
                } catch (Exception e) {
                    log.error("confirmUnBind失败 wmPoiIdSet={},customerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), customerId, switchTaskId, e);
                }
            }
        }));
    }

    /**
     * 拒绝解绑/确认解绑失败
     *
     * @param wmPoiIdSet
     * @param customerId
     * @param switchTaskId
     */
    public void rejectUnBind(Set<Long> wmPoiIdSet, Integer customerId, Long switchTaskId) {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = 100;
                    List<Set<Long>> list = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        list.add(wmPoiIdSet);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                            }
                            batchSet.add(wmPoiId);
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                            }
                            num++;
                        }
                    }

                    for (Set<Long> set : list) {
                        int isUnbindingStatus = CustomerConstants.IS_UNBINDING_NO;
                        if (switchTaskId != null) {
                            isUnbindingStatus = CustomerConstants.IS_UNBINDING_YES;
                        }
                        wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelation(set, isUnbindingStatus, CustomerRelationStatusEnum.BIND.getCode(), switchTaskId, customerId);
                        Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);

                        //更新客户门店解绑任务-取消状态
                        customerTaskService.updateStatusByCustomerIdAndPoiIdAndType(customerId, set, switchTaskId,
                                CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode(), CustomerTaskStatusEnum.CANCEL.getCode());
                    }
                } catch (Exception e) {
                    log.error("rejectUnBind失败 wmPoiIdSet={},customerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), customerId, switchTaskId, e);
                }
            }
        });
    }

    // 原客户确认成功
    public void fromSwitchSucc(Set<Long> wmPoiIdSet, Integer fromCustomerId, Integer toCustomerId, Long switchTaskId,
                               Map<Long, Integer> poiAndTaskBindMaps, Map<Long, Integer> poiAndTaskUnBindMaps) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = 100;
                    List<Set<Long>> list = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        list.add(wmPoiIdSet);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                            }
                            batchSet.add(wmPoiId);
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                            }
                            num++;
                        }
                    }
                    for (Set<Long> set : list) {
                        List<WmCustomerPoiDB> readyList = Lists.newArrayList();
                        List<WmCustomerPoiDB> readyUnBindList = Lists.newArrayList();
                        for (Long wmPoiId : set) {
                            WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
                            wmCustomerPoiDB.setCustomerId(toCustomerId);
                            wmCustomerPoiDB.setWmPoiId(wmPoiId);
                            wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
                            wmCustomerPoiDB.setValid(CustomerConstants.UNVALID);
                            wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.READY_BIND.getCode());
                            wmCustomerPoiDB.setSwitchTaskId(switchTaskId);
                            Integer taskId = (CollectionUtils.isEmpty(poiAndTaskBindMaps) || poiAndTaskBindMaps.get(wmPoiId) == null) ? 0 : poiAndTaskBindMaps.get(wmPoiId);
                            wmCustomerPoiDB.setBizTaskId(taskId);
                            readyList.add(wmCustomerPoiDB);

                            //待解绑客户门店关系对象
                            WmCustomerPoiDB wmCustomerPoiDBUnBind = getReadyUnBindDBRel(fromCustomerId, wmPoiId, switchTaskId, taskId);
                            readyUnBindList.add(wmCustomerPoiDBUnBind);
                        }
                        log.info("readyList={},readyUnBindList={}", JSONObject.toJSONString(readyList), JSONObject.toJSONString(readyUnBindList));
                        wmCustomerPoiDBMapper.batchInsertCustomerPoiWithBizTaskId(readyList);
                        //新增或者修改客户门店属性
                        //wmCustomerPoiAttributeService.upsertCustomerPoiAttribute(toCustomerId, set);
                        Uninterruptibles.sleepUninterruptibly(1000, TimeUnit.MILLISECONDS);
                        if (CollectionUtils.isEmpty(poiAndTaskUnBindMaps)) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelation(set, CustomerConstants.IS_UNBINDING_YES, CustomerRelationStatusEnum.READY_UNBIND.getCode(), switchTaskId, fromCustomerId);
                        } else {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationByList(readyUnBindList);
                        }
                    }
                } catch (Exception e) {
                    log.error("fromSwitchSucc wmPoiIdSet={},fromCustomerId={},toCustomerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), fromCustomerId, toCustomerId, switchTaskId, e);
                }
            }
        });
    }

    /**
     * 老客门店状态置为解绑中
     *
     * @param wmPoiIdSet
     * @param fromCustomerId
     * @param switchTaskId
     */
    public void oldCustomerToUnbinding(Set<Long> wmPoiIdSet, Integer fromCustomerId, Long switchTaskId,
                                       Map<Long, Integer> poiAndTaskUnBindMaps) {
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = MccCustomerConfig.getBatchDealCustomerPoiCount();
                    List<Set<Long>> list = Lists.newArrayList();
                    List<List<WmCustomerPoiDB>> readyUnBindList = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        List<WmCustomerPoiDB> readyUnBind = Lists.newArrayList();
                        list.add(wmPoiIdSet);
                        for (Long wmPoiId : wmPoiIdSet) {
                            Integer taskId = (CollectionUtils.isEmpty(poiAndTaskUnBindMaps) || poiAndTaskUnBindMaps.get(wmPoiId) == null) ? 0 : poiAndTaskUnBindMaps.get(wmPoiId);
                            WmCustomerPoiDB wmCustomerPoiDB = getReadyUnBindDBRel(fromCustomerId, wmPoiId, switchTaskId, taskId);
                            readyUnBind.add(wmCustomerPoiDB);
                        }
                        readyUnBindList.add(readyUnBind);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        List<WmCustomerPoiDB> readyUnBind = Lists.newArrayList();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            Integer taskId = (CollectionUtils.isEmpty(poiAndTaskUnBindMaps) || poiAndTaskUnBindMaps.get(wmPoiId) == null) ? 0 : poiAndTaskUnBindMaps.get(wmPoiId);
                            WmCustomerPoiDB wmCustomerPoiDB = getReadyUnBindDBRel(fromCustomerId, wmPoiId, switchTaskId, taskId);
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                                readyUnBind = Lists.newArrayList();
                            }
                            batchSet.add(wmPoiId);
                            readyUnBind.add(wmCustomerPoiDB);
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                                readyUnBindList.add(readyUnBind);
                            }
                            num++;
                        }
                    }

                    //为空则走原逻辑
                    if (CollectionUtils.isEmpty(poiAndTaskUnBindMaps)) {
                        for (Set<Long> set : list) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelation(set, CustomerConstants.IS_UNBINDING_YES, CustomerRelationStatusEnum.READY_UNBIND.getCode(), switchTaskId, fromCustomerId);
                            Uninterruptibles.sleepUninterruptibly(1000, TimeUnit.MILLISECONDS);
                        }
                    } else {
                        for (List<WmCustomerPoiDB> readyUnBind : readyUnBindList) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationByList(readyUnBind);
                            Uninterruptibles.sleepUninterruptibly(1000, TimeUnit.MILLISECONDS);
                        }
                    }
                } catch (Exception e) {
                    log.error("oldCustomerToUnbinding wmPoiIdSet={},fromCustomerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), fromCustomerId, switchTaskId, e);
                }
            }
        }));
    }

    /**
     * 新客到待绑定状态
     *
     * @param wmPoiIdSet
     * @param toCustomerId
     * @param switchTaskId
     */
    public void newCustomerToReadyBind(Set<Long> wmPoiIdSet, Integer toCustomerId, Long switchTaskId) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = 100;
                    List<Set<Long>> list = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        list.add(wmPoiIdSet);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                            }
                            batchSet.add(wmPoiId);
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                            }
                            num++;
                        }
                    }
                    for (Set<Long> set : list) {
                        Uninterruptibles.sleepUninterruptibly(1000, TimeUnit.MILLISECONDS);
                        wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationToReadyBind(set, CustomerConstants.IS_UNBINDING_NO, switchTaskId, toCustomerId);
                    }
                } catch (Exception e) {
                    log.error("newCustomerToReadyBind wmPoiIdSet={},toCustomerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), toCustomerId, switchTaskId, e);
                }
            }
        });
    }


    // 取消
    public void cancelSwitch(Set<Long> wmPoiIdSet, Integer fromCustomerId, Integer toCustomerId, long switchTaskId) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = 100;
                    List<Set<Long>> list = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        list.add(wmPoiIdSet);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                            }
                            batchSet.add(wmPoiId);
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                            }
                            num++;
                        }
                    }
                    for (Set<Long> set : list) {
                        if (toCustomerId != null) {
                            wmCustomerPoiDBMapper.deleteReadyBindPois(set, toCustomerId, switchTaskId);
                            Uninterruptibles.sleepUninterruptibly(1000, TimeUnit.MILLISECONDS);
                        }
                        wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelation(set, CustomerConstants.IS_UNBINDING_NO, CustomerRelationStatusEnum.BIND.getCode(), 0L, fromCustomerId);

                        //客户切换任务更新为「取消」
                        customerTaskService.updateStatusByCustomerIdAndPoiIdAndType(fromCustomerId, set, switchTaskId, CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode(), CustomerTaskStatusEnum.CANCEL.getCode());
                        customerTaskService.updateStatusByCustomerIdAndPoiIdAndType(toCustomerId, set, switchTaskId, CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode(), CustomerTaskStatusEnum.CANCEL.getCode());
                    }
                } catch (Exception e) {
                    log.error("cancelSwitch wmPoiIdSet={},fromCustomerId={},toCustomerId={}", JSONObject.toJSONString(wmPoiIdSet), fromCustomerId, toCustomerId, e);
                }
            }
        });
    }

    /**
     * 批量添加门店和客户的关系-带有客户任务ID参数
     */
    public void batchInsertCustomerPoiWithBizTaskId(Integer customerId, Set<Long> wmPoiIdSet, Map<Long, Integer> poiAndTaskMaps) {
        List<WmCustomerPoiDB> opList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
            wmCustomerPoiDB.setCustomerId(customerId);
            wmCustomerPoiDB.setWmPoiId(wmPoiId);
            wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
            wmCustomerPoiDB.setValid(CustomerConstants.VALID);
            wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.BIND.getCode());
            wmCustomerPoiDB.setSwitchTaskId(0L);
            if (!CollectionUtils.isEmpty(poiAndTaskMaps) && poiAndTaskMaps.get(wmPoiId) != null) {
                Integer taskId = poiAndTaskMaps.get(wmPoiId);
                wmCustomerPoiDB.setBizTaskId(taskId);
            } else {
                wmCustomerPoiDB.setBizTaskId(0);
            }
            opList.add(wmCustomerPoiDB);
        }
        wmCustomerPoiDBMapper.batchInsertCustomerPoiWithBizTaskId(opList);
    }

    /**
     * 添加/更新门店和客户的关系-带有客户任务ID参数
     */
    public void insertUpdateCustomerPoiWithBizTaskId(Integer customerId, Set<Long> wmPoiIdSet, Map<Long, Integer> poiAndTaskMaps) throws WmCustomerException {
        log.info("InsertCustomerPoiWithBizTaskId customerId={},wmPoiIdSet={},poiAndTaskMaps={}", customerId, JSON.toJSONString(wmPoiIdSet), JSON.toJSONString(poiAndTaskMaps));
        for (Long wmPoiId : wmPoiIdSet) {
            WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
            wmCustomerPoiDB.setCustomerId(customerId);
            wmCustomerPoiDB.setWmPoiId(wmPoiId);
            wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
            wmCustomerPoiDB.setValid(CustomerConstants.VALID);
            wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.BIND.getCode());
            wmCustomerPoiDB.setSwitchTaskId(0L);
            if (!CollectionUtils.isEmpty(poiAndTaskMaps) && poiAndTaskMaps.get(wmPoiId) != null) {
                Integer taskId = poiAndTaskMaps.get(wmPoiId);
                wmCustomerPoiDB.setBizTaskId(taskId);
            } else {
                wmCustomerPoiDB.setBizTaskId(0);
            }
            List<Integer> ids = wmCustomerPoiDBMapper.selectCustomerPoiRT(wmCustomerPoiDB.getCustomerId(), wmCustomerPoiDB.getWmPoiId());
            if (ids.size() == 0) {
                wmCustomerPoiDBMapper.insertCustomerPoiWithBizTaskId(wmCustomerPoiDB);
            } else if (ids.size() == 1) {
                wmCustomerPoiDB.setId(ids.get(0));
                wmCustomerPoiDBMapper.updateCustomerPoiWithBizTaskId(wmCustomerPoiDB);
            } else {
                log.error("添加/更新门店和客户的关系（带有客户任务ID参数异常）客户门店关系表客户门店绑定关系不唯一， customerId={},wmPoiIdSet={},poiAndTaskMaps={}", customerId, JSON.toJSONString(wmPoiIdSet), JSON.toJSONString(poiAndTaskMaps));
            }
        }
    }

    /**
     * 批量添加门店和客户的关系
     */
    public void batchInsertCustomerPoi(Integer customerId, Set<Long> wmPoiIdSet) {
        List<WmCustomerPoiDB> opList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
            wmCustomerPoiDB.setCustomerId(customerId);
            wmCustomerPoiDB.setWmPoiId(wmPoiId);
            wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
            wmCustomerPoiDB.setValid(CustomerConstants.VALID);
            wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.BIND.getCode());
            wmCustomerPoiDB.setSwitchTaskId(0L);
            opList.add(wmCustomerPoiDB);
        }
        wmCustomerPoiDBMapper.batchInsertCustomerPoi(opList);
    }
    /**
     * 添加/更新门店和客户的关系
     */
    public void insertUpdateCustomerPoi(Integer customerId, Set<Long> wmPoiIdSet) throws WmCustomerException {
        log.info("insertUpdateCustomerPoi customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));
        for (Long wmPoiId : wmPoiIdSet) {
            WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
            wmCustomerPoiDB.setCustomerId(customerId);
            wmCustomerPoiDB.setWmPoiId(wmPoiId);
            wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
            wmCustomerPoiDB.setValid(CustomerConstants.VALID);
            wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.BIND.getCode());
            wmCustomerPoiDB.setSwitchTaskId(0L);
            List<Integer> ids = wmCustomerPoiDBMapper.selectCustomerPoiRT(wmCustomerPoiDB.getCustomerId(), wmCustomerPoiDB.getWmPoiId());
            if(ids.size() == 0){
                wmCustomerPoiDBMapper.insertCustomerPoi(wmCustomerPoiDB);
            } else if (ids.size() == 1) {
                wmCustomerPoiDB.setId(ids.get(0));
                wmCustomerPoiDBMapper.updateCustomerPoi(wmCustomerPoiDB);
            } else {
                log.error("添加/更新门店和客户的关系异常，客户门店关系表客户门店绑定关系不唯一 customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));
            }
        }
    }

    public void batchInsertCustomerPoiForPreBind(Integer customerId, Set<Long> wmPoiIdSet, Long switchTaskId) {
        List<WmCustomerPoiDB> opList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
            wmCustomerPoiDB.setCustomerId(customerId);
            wmCustomerPoiDB.setWmPoiId(wmPoiId);
            wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
            wmCustomerPoiDB.setValid(CustomerConstants.UNVALID);
            wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.TO_APPLY_BIND.getCode());
            wmCustomerPoiDB.setSwitchTaskId(switchTaskId == null ? 0L : switchTaskId);
            opList.add(wmCustomerPoiDB);
        }
        wmCustomerPoiDBMapper.batchInsertCustomerPoi(opList);
    }

    /**
     * 执行预绑定-带客户任务ID
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param switchTaskId
     * @param poiAndTaskMaps
     */
    public void batchInsertCustomerPoiForPreBindWithBizTaskId(Integer customerId, Set<Long> wmPoiIdSet, Long switchTaskId,
                                                              Map<Long, Integer> poiAndTaskMaps) {
        List<WmCustomerPoiDB> opList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
            wmCustomerPoiDB.setCustomerId(customerId);
            wmCustomerPoiDB.setWmPoiId(wmPoiId);
            wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
            wmCustomerPoiDB.setValid(CustomerConstants.UNVALID);
            wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.TO_APPLY_BIND.getCode());
            wmCustomerPoiDB.setSwitchTaskId(switchTaskId == null ? 0L : switchTaskId);
            if (!CollectionUtils.isEmpty(poiAndTaskMaps) && poiAndTaskMaps.get(wmPoiId) != null) {
                wmCustomerPoiDB.setBizTaskId(poiAndTaskMaps.get(wmPoiId));
            } else {
                wmCustomerPoiDB.setBizTaskId(0);
            }
            opList.add(wmCustomerPoiDB);
        }
        wmCustomerPoiDBMapper.batchInsertCustomerPoiWithBizTaskId(opList);
    }

    /**
     * relation变更到 6:确认绑定中
     *
     * @param wmPoiIdSet
     * @param switchTaskId
     * @param customerId
     */
    public void applyPreBind(Set<Long> wmPoiIdSet, Long switchTaskId, Integer customerId, Map<Long, Integer> poiTaskIds) {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = MccCustomerConfig.getBatchDealCustomerPoiCount();
                    List<Set<Long>> list = Lists.newArrayList();
                    List<List<WmCustomerPoiDB>> readyBindList = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        list.add(wmPoiIdSet);
                        //处理待绑定列表对象
                        List<WmCustomerPoiDB> readyBind = Lists.newArrayList();
                        for (Long wmPoiId : wmPoiIdSet) {
                            Integer bizTaskId = (CollectionUtils.isEmpty(poiTaskIds) || poiTaskIds.get(wmPoiId) == null) ? 0 : poiTaskIds.get(wmPoiId);
                            WmCustomerPoiDB wmCustomerPoiDB = getReadyBindDBRel(customerId, wmPoiId, switchTaskId, bizTaskId);
                            readyBind.add(wmCustomerPoiDB);
                        }
                        readyBindList.add(readyBind);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        List<WmCustomerPoiDB> readyBind = Lists.newArrayList();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                                readyBind = Lists.newArrayList();
                            }
                            batchSet.add(wmPoiId);
                            Integer bizTaskId = (CollectionUtils.isEmpty(poiTaskIds) || poiTaskIds.get(wmPoiId) == null) ? 0 : poiTaskIds.get(wmPoiId);
                            readyBind.add(getReadyBindDBRel(customerId, wmPoiId, switchTaskId, bizTaskId));
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                                readyBindList.add(readyBind);
                            }
                            num++;
                        }
                    }

                    //为空则走原更新流程
                    if (CollectionUtils.isEmpty(poiTaskIds)) {
                        for (Set<Long> set : list) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationToPreBinding(set, CustomerConstants.IS_UNBINDING_NO, switchTaskId, customerId);
                            Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                        }
                    } else {
                        for (List<WmCustomerPoiDB> readyBind : readyBindList) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationToPreBindingList(readyBind);
                            Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                        }
                    }
                } catch (Exception e) {
                    log.error("applyPreBind失败 wmPoiIdSet={},customerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), customerId, switchTaskId, e);
                }
            }
        }));
    }

    /**
     * relation变更到 4:已绑定 && valid设置为1
     *
     * @param wmPoiIdSet
     * @param switchTaskId
     * @param customerId
     */
    public void confirmPreBind(Set<Long> wmPoiIdSet, Long switchTaskId, Integer customerId) {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = 100;
                    List<Set<Long>> list = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        list.add(wmPoiIdSet);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                            }
                            batchSet.add(wmPoiId);
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                            }
                            num++;
                        }
                    }

                    for (Set<Long> set : list) {
                        wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationFromPreBindingToBind(set, CustomerConstants.IS_UNBINDING_NO, switchTaskId, customerId);
                        Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                    }
                } catch (Exception e) {
                    log.error("applyPreBind失败 wmPoiIdSet={},customerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), customerId, switchTaskId, e);
                }
            }
        });
    }

    public void cancelPreBind(Set<Long> wmPoiIdSet, Long switchTaskId, Integer customerId) {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = 100;
                    List<Set<Long>> list = Lists.newArrayList();
                    if (wmPoiIdSet.size() <= batchUpdateMax) {
                        list.add(wmPoiIdSet);
                    } else {
                        Set<Long> batchSet = Sets.newHashSet();
                        int num = 0;
                        for (Long wmPoiId : wmPoiIdSet) {
                            if (num % batchUpdateMax == 0) {
                                batchSet = Sets.newHashSet();
                            }
                            batchSet.add(wmPoiId);
                            if (num % batchUpdateMax == (batchUpdateMax - 1) || num == wmPoiIdSet.size() - 1) {
                                list.add(batchSet);
                            }
                            num++;
                        }
                    }

                    for (Set<Long> set : list) {
                        wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationToCancelPreBinding(set, CustomerConstants.IS_UNBINDING_NO, switchTaskId, customerId);
                        Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                    }
                } catch (Exception e) {
                    log.error("cancelPreBind失败 wmPoiIdSet={},customerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), customerId, switchTaskId, e);
                }
            }
        });
    }

    /**
     * 获取确认解绑中对象
     *
     * @param customerId
     * @param wmPoiId
     * @param switchTaskId
     * @param bizTaskId
     * @return
     */
    private WmCustomerPoiDB getConfirmUnBindingDB(Integer customerId, Long wmPoiId, Long switchTaskId, Integer bizTaskId) {
        WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
        wmCustomerPoiDB.setWmPoiId(wmPoiId);
        wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_YES);
        wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.CONFIRM_UNBINDING.getCode());
        wmCustomerPoiDB.setSwitchTaskId(switchTaskId);
        wmCustomerPoiDB.setBizTaskId(bizTaskId);
        wmCustomerPoiDB.setCustomerId(customerId);
        return wmCustomerPoiDB;
    }

    /**
     * 创建待解绑客户门店关系
     *
     * @param customerId
     * @param wmPoiId
     * @param switchTaskId
     * @param bizTaskId
     * @return
     */
    private WmCustomerPoiDB getReadyUnBindDBRel(Integer customerId, Long wmPoiId, Long switchTaskId, Integer bizTaskId) {
        WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
        wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_YES);
        wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.READY_UNBIND.getCode());
        wmCustomerPoiDB.setWmPoiId(wmPoiId);
        wmCustomerPoiDB.setCustomerId(customerId);
        wmCustomerPoiDB.setBizTaskId(bizTaskId);
        wmCustomerPoiDB.setSwitchTaskId(switchTaskId);
        return wmCustomerPoiDB;
    }

    /**
     * 更新状态到绑定确认中-底层代码写死了6
     *
     * @param customerId
     * @param wmPoiId
     * @param switchTaskId
     * @param bizTaskId
     * @return
     */
    private WmCustomerPoiDB getReadyBindDBRel(Integer customerId, Long wmPoiId, Long switchTaskId, Integer bizTaskId) {
        WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
        wmCustomerPoiDB.setCustomerId(customerId);
        wmCustomerPoiDB.setWmPoiId(wmPoiId);
        wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
        wmCustomerPoiDB.setSwitchTaskId(switchTaskId);
        wmCustomerPoiDB.setBizTaskId(bizTaskId);
        return wmCustomerPoiDB;
    }

    public int countCustomerPoi(int customerId) {

        if (customerId % 100 < MccCustomerConfig.bigCustomerQueryPercent()) {
            List<Long> wmPoiIds = this.selectWmPoiIdsByCustomerId(customerId);
            return wmPoiIds == null ? 0 :wmPoiIds.size();
        }
        return wmCustomerPoiDBMapper.countCustomerPoi(customerId);
    }

    public List<Long> selectWmPoiIdsByCustomerId(Integer customerId) {
        if (customerId % 100 < MccCustomerConfig.bigCustomerQueryPercent()) {
            log.info("selectWmPoiIdsByCustomerId customerId={},start", customerId);
            int pageSize = MccCustomerConfig.getPoiByCustomerIdPageSize();
            List<Long> allPoiIds = Lists.newArrayList();
            List<Long> wmPoiIds = wmCustomerPoiDBMapper.getWmPoiIdsByCustomerIdFromIndexId(customerId,0,pageSize);
            allPoiIds.addAll(wmPoiIds);

            //如果数量满足页数大小，证明还有数据
            while (wmPoiIds.size() == pageSize) {
                //定位id根据客户id和门店id查询id
                Integer index = wmCustomerPoiDBMapper.selectIdByWmPoiIdAndCustomerId(customerId,wmPoiIds.get(wmPoiIds.size()-1));
                 wmPoiIds = wmCustomerPoiDBMapper.getWmPoiIdsByCustomerIdFromIndexId(customerId,index,pageSize);
                 //查询为空直接跳出循环
                 if(CollectionUtil.isEmpty(wmPoiIds)){
                     break;
                 }
                 allPoiIds.addAll(wmPoiIds);
            }
            log.info("selectWmPoiIdsByCustomerId customerId={},end", customerId);
            //流量对比
            if(MccCustomerConfig.enableBigCustomerQueryDiffCompare()){
                List<Long> oldAllPoiIds = wmCustomerPoiDBMapper.selectWmPoiIdsByCustomerId(customerId);
                log.info("selectWmPoiIdsByCustomerId 查询开启流量对比功能 customerId={}", customerId);
                if(oldAllPoiIds != null && oldAllPoiIds.size() != allPoiIds.size()){
                    log.error("WmCustomerPoiRelService.selectWmPoiIdsByCustomerId 服务流量对比异常,客户id:{}历史数量:{}当前数量:{}",customerId,oldAllPoiIds,allPoiIds);
                    return oldAllPoiIds;
                }
                log.info("selectWmPoiIdsByCustomerId 查询开启流量对比功能 customerId={} ,oldSize:{},newSize:{}", customerId,oldAllPoiIds==null?0:oldAllPoiIds.size(), allPoiIds.size());
            }

            return allPoiIds;
        } else {
            return wmCustomerPoiDBMapper.selectWmPoiIdsByCustomerId(customerId);
        }
    }

    /**
     * 解绑客户门店关系
     * @param wmPoiId
     * @param customerId
     * @param bizTaskId
     */
    public void unBindCustomerPoi(Long wmPoiId, Integer customerId,Integer bizTaskId){
        wmCustomerPoiDBMapper.deleteCustomerPoi(wmPoiId, customerId, bizTaskId);
    }

    /**
     * 预绑定失败解除客户门店关系
     * @param wmPoiId
     * @param customerId
     */
    public void unBindPreBindFail(Long wmPoiId, Integer customerId) {
        wmCustomerPoiDBMapper.unBindPreBindFail(wmPoiId, customerId);
    }

    /**
     * 查询一批门店不是指定客户的关系记录
     * @param wmPoiIdSet
     * @param customerId
     * @return
     */
    public List<WmCustomerPoiDB> selectNotCustomerIdByWmPoiIdsRT(Set<Long> wmPoiIdSet, Integer customerId) {
        return wmCustomerPoiDBMapper.selectNotCustomerIdByWmPoiIdsRT(wmPoiIdSet, customerId);
    }

    /**
     *
     * @param wmPoiIdSet
     * @return
     */
    public Set<Long> selectExistPoiByWmPoiId(Set<Long> wmPoiIdSet) {
        return wmCustomerPoiDBMapper.selectExistPoiByWmPoiId(wmPoiIdSet);
    }


    /**
     * 根据客户ID查询绑定中非切换门店列表
     *
     * @param customerId 客户ID
     * @return
     */
    public List<Long> getBindingNotSwitchPoi(Integer customerId) {
        return wmCustomerPoiDBMapper.listBindingNotSwitchPoi(customerId);
    }

    /**
     * 根据客户ID查询已绑定非切换门店列表
     *
     * @param customerId 客户ID
     * @return
     */
    public List<Long> getBindNotSwitchPoi(Integer customerId) {
        return wmCustomerPoiDBMapper.listBindNotSwitchPoi(customerId);
    }

    public Boolean haveMoreThanOnePoi(long customerId) {
        int size = MccCustomerConfig.checkCustomerHaveMoreThanOnePoiSize();
        List<Long> poiIds = wmCustomerPoiDBMapper.getWmPoiIdsByCustomerIdFromIndexId((int)customerId,0,size);
        return  CollectionUtil.isEmpty(poiIds)?false: poiIds.size()>1;
    }

    /**
     * 查询客户下绑定或绑定中的门店数量
     *
     * @param customerId
     * @return
     * @throws WmCustomerException
     */
    public Integer cntBindOrBindingPoiByCustomerId(Integer customerId) throws WmCustomerException {
        return wmCustomerPoiDBMapper.countBindingOrBindPoiByCustomerId(customerId.intValue());
    }
}
