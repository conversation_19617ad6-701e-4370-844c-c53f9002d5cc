package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryWholeCityInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 配送全城送信息生成pdf
 */
@Service
@Slf4j
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_DELIVERY_WHOLE_CITY)
public class WmEcontractBatchDeliveryWholeCityDataWrapperService implements IWmEcontractDataWrapperService{

    private String BATCH_WHOLE_CITE_TEMPLATE_NAME = "delivery_multi_whole_city_info";

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo)
        throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);
        List<PdfContentInfoBo> result = Lists.newArrayList();
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        EcontractDeliveryWholeCityInfoBo wholeCityInfoBo = null;
        for (EcontractDeliveryInfoBo temp : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            wholeCityInfoBo = temp.getEcontractDeliveryWholeCityInfoBo();
            if(wholeCityInfoBo == null){
                continue;
            }
            wholeCityInfoBo.setDeliveryArea(null);
            pdfBizContent.add(MapUtil.Object2Map(wholeCityInfoBo));
        }
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(BATCH_WHOLE_CITE_TEMPLATE_NAME);
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        result.add(pdfInfoBo);
        return result;
    }

}
