package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerAuditDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerAuditDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.mq.domain.SpecialAuditResultBO;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.AbstractWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerAuditService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.constatnt.config.DimensionTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerAuditStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerSceneInfoBO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.rateApproval.constant.ApprovalStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20240228
 * @desc 特批审核结果同步消息
 */
@Service
@Slf4j
public class SpecialAuditResultListener implements IMessageListener {

    @Autowired
    private WmCustomerAuditService wmCustomerAuditService;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerAuditDBMapper wmCustomerAuditDBMapper;

    @Autowired
    private IWmCustomerRealService wmLeafCustomerRealService;

    @Autowired
    private WmEmployeeService wmEmployeeService;

    @Autowired
    private WmCustomerLabelService wmCustomerLabelService;

    /**
     * 特批审核结果事件
     */
    private static final String specialAuditResultEvent = "specialAuditResultEvent";

    /**
     * 特批审核成功继续走提审流程异常
     */
    private static final String auditSuc2ContinueError = "auditSuc2ContinueError";

    /**
     * 需要关注的审核状态集合
     */
    private static List<Integer> careStateList = Lists.newArrayList(ApprovalStatusEnum.APPROVED.getCode(), ApprovalStatusEnum.REJECT.getCode(),
            ApprovalStatusEnum.TIMEOUT.getCode(), ApprovalStatusEnum.REVOKE.getCode());

    /**
     * 特批失败状态集
     */
    private static List<Integer> auditFailStatusList = Lists.newArrayList(ApprovalStatusEnum.REJECT.getCode(),
            ApprovalStatusEnum.TIMEOUT.getCode(), ApprovalStatusEnum.REVOKE.getCode());

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        log.info("SpecialAuditResultListener,监听到特批审核结果同步消息, mafkaMessage = {}, partition = {}", message.getBody(), message.getParttion());
        if (null == message.getBody() || StringUtils.isBlank(message.getBody().toString())) {
            log.error("SpecialAuditResultListener,监听特批审核结果同步消息对象为空，请及时关注,message={}", JSON.toJSONString(message));
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            //转换为审核结果对象
            SpecialAuditResultBO specialAuditResultBO = JSON.parseObject(message.getBody().toString(), SpecialAuditResultBO.class);
            if (specialAuditResultBO == null) {
                log.error("SpecialAuditResultListener,监听特批审核结果同步消息转换对象specialAuditResultBO为空，请及时关注,message={}", JSON.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //特批任务类型过滤
            if (specialAuditResultBO.getType() == null
                    || specialAuditResultBO.getType().intValue() != MccCustomerConfig.getSingleCustomerSceneSpecialTaskType().intValue()) {
                log.warn("SpecialAuditResultListener,任务类型为空或非个人资质客户场景不需要关注,message={}", JSON.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //任务维度ID过滤
            if (specialAuditResultBO.getDimensionId() == null
                    || specialAuditResultBO.getDimensionId().intValue() != DimensionTypeEnum.CUSTOMER_ID.getDimensionTypeId().intValue()) {
                log.warn("SpecialAuditResultListener,任务维度为空或非客户场景不需要关注,message={}", JSON.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Integer state = specialAuditResultBO.getState();
            //消息状态非关注状态，则直接过滤
            if (!careStateList.contains(state)) {
                log.info("SpecialAuditResultListener,消息状态字段非关注状态，不处理,message={}", JSON.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //根据特批ID查询审核记录
            List<WmCustomerAuditDB> wmCustomerAuditDBList = wmCustomerAuditService.getSpecialAuditingByBizTaskId(specialAuditResultBO.getApplicationId());
            if (CollectionUtils.isEmpty(wmCustomerAuditDBList)) {
                log.info("SpecialAuditResultListener,根据特批ID未查询到特批中的记录，不处理,message={}", JSON.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //遍历循环处理客户特批消息
            for (WmCustomerAuditDB wmCustomerAuditDB : wmCustomerAuditDBList) {
                //开始处理业务特批结果
                dealSpecialAuditResult(specialAuditResultBO, wmCustomerAuditDB);
                log.info("SpecialAuditResultListener,同步特批任务结果完成,customerId={},specialAuditResultBO={}", wmCustomerAuditDB.getCustomerId(), JSON.toJSONString(specialAuditResultBO));
            }

        } catch (Exception e) {
            log.error("SpecialAuditResultListener,同步特批任务结果完成发生异常, message={}", message.toString(), e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 根据特批结果处理客户特批审核通知流程
     *
     * @param specialAuditResultBO
     * @param wmCustomerAuditDB
     * @return
     */
    private void dealSpecialAuditResult(SpecialAuditResultBO specialAuditResultBO, WmCustomerAuditDB wmCustomerAuditDB) throws WmCustomerException, TException {
        Integer customerId = wmCustomerAuditDB.getCustomerId();
        Integer bizTaskId = specialAuditResultBO.getApplicationId();
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdFromSlave(customerId);
        if (wmCustomerDB == null || wmCustomerDB.getAuditStatus() != CustomerAuditStatus.CUSTOMER_SPECIAL_AUDITING.getCode()) {
            log.info("dealSpecialAuditResult,当前客户不存在或状态不是特批中，不需要处理,bizTaskId={},customerId={}", bizTaskId, customerId);
            return;
        }
        //默认状态是特批通过
        Integer auditNewStatus = CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode();
        String opLog = "特批通过，特批ID为" + specialAuditResultBO.getApplicationId();
        String auditResultStr = getSpecialAuditResult(specialAuditResultBO.getState(), specialAuditResultBO.getLastOperateReason());
        //如果特批状态是审核通过，则重置为 待提审
        if (auditFailStatusList.contains(specialAuditResultBO.getState())) {
            auditNewStatus = CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode();
            opLog = String.format("特批驳回，原因为%s，特批ID为%s", auditResultStr, specialAuditResultBO.getApplicationId());
        }

        //审核最新记录
        WmCustomerAuditDB wmCustomerAuditDBUpdate = new WmCustomerAuditDB();
        wmCustomerAuditDBUpdate.setAuditStatus(auditNewStatus);
        wmCustomerAuditDBUpdate.setAuditResult(auditResultStr);
        wmCustomerAuditDBUpdate.setId(wmCustomerAuditDB.getId());
        wmCustomerAuditDBUpdate.setAuditTime(specialAuditResultBO.getLastOperateTime());
        //审核通过需要将审核记录更新为无效
        if (specialAuditResultBO.getState() == ApprovalStatusEnum.APPROVED.getCode()) {
            wmCustomerAuditDBUpdate.setValid(CustomerConstants.UNVALID);
        }

        //更新客户记录审核状态信息
        wmCustomerService.updateAuditStatusByCustomerId(customerId, auditNewStatus);
        //更新客户审核记录的审核状态以及结果
        wmCustomerAuditDBMapper.updateAuditResultById(wmCustomerAuditDBUpdate);


        //给客户打场景标签
        addCustomerSceneTag(wmCustomerDB, specialAuditResultBO.getState());
        //新增特批审核操作记录
        addSpecialAuditOpLog(customerId, specialAuditResultBO, opLog);
        //继续客户提审流程
        continueCustomerAudit(customerId, auditNewStatus);

    }

    /**
     * 继续客户提审流程
     *
     * @param customerId
     */
    private void continueCustomerAudit(Integer customerId, Integer auditNewStatus) {
        try {
            if (auditNewStatus != CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode()) {
                log.info("continueCustomerAudit,特批审核后流转状态非待提审，不需要继续走提审流程,customerId={},auditNewStatus={}", customerId, auditNewStatus);
                return;
            }
            //检查门店资质是否可以直接提审
            wmLeafCustomerRealService.checkPoiQuaForCustomerAudit(customerId, 0, "系统");
        } catch (Exception e) {
            log.error("continueCustomerAudit,特批审核通过继续走提审流程发生异常,customerId={},auditNewStatus={}", customerId, auditNewStatus, e);
            Cat.logEvent("customerAuditFail", auditSuc2ContinueError);
        }
    }

    /**
     * 特批审核处理完成添加操作记录
     *
     * @param customerId
     * @param specialAuditResultBO
     * @param opLog
     */
    private void addSpecialAuditOpLog(Integer customerId, SpecialAuditResultBO specialAuditResultBO, String opLog) {
        try {
            //添加操作记录
            wmLeafCustomerRealService.insertCustomerOpLog(customerId, specialAuditResultBO.getLastOperateUserId(),
                    getOpName(specialAuditResultBO.getLastOperateUserId()), WmCustomerOplogBo.OpType.CHANGESTATUS, opLog);
        } catch (Exception e) {
            log.error("addSpecialAuditOpLog,新增特批审核结果操作记录发生异常,customerId={},specialAuditResultBO={}", customerId, JSON.toJSONString(specialAuditResultBO), e);
        }
    }

    /**
     * 给客户打场景标签
     *
     * @param wmCustomerDB
     */
    private void addCustomerSceneTag(WmCustomerDB wmCustomerDB, Integer specialResult) {
        //客户为空或客户场景信息为空则直接返回
        if (wmCustomerDB == null || StringUtils.isBlank(wmCustomerDB.getSceneInfo())) {
            return;
        }
        //特批状态非通过则不处理
        if (specialResult != ApprovalStatusEnum.APPROVED.getCode()) {
            return;
        }

        //转换为客户场景对象BO
        CustomerSceneInfoBO customerSceneInfoBO = JSON.parseObject(wmCustomerDB.getSceneInfo(), CustomerSceneInfoBO.class);
        if (customerSceneInfoBO == null) {
            return;
        }

        //给客户打场景标签
        wmCustomerLabelService.addWmSingleCustomerSceneTag(wmCustomerDB.getMtCustomerId(), customerSceneInfoBO.getSceneType(), 0, "系统");
    }

    /**
     * 根据操作人uid获取名称
     *
     * @param opUid
     * @return
     */
    private String getOpName(Integer opUid) {
        try {
            //如果操作人ID为空或者小于等于0则操作人默认为特批系统
            if (opUid == null || opUid <= 0) {
                return "特批系统";
            }
            WmEmploy operator = wmEmployeeService.getWmEmployById(opUid);
            if (operator == null) {
                return null;
            }
            return operator.getName();
        } catch (Exception e) {
            log.error("getOpName,根据opUid获取操作人名称发生异常,opUid={}", opUid, e);
        }
        return null;
    }

    /**
     * 获取审核结果文案
     *
     * @param auditStatus
     * @return
     */
    private String getSpecialAuditResult(Integer auditStatus, String reject) {
        if (auditStatus == ApprovalStatusEnum.APPROVED.getCode()) {
            return "特批审核通过";
        }
        if (auditStatus == ApprovalStatusEnum.REJECT.getCode()) {
            if (reject.length() > WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_CUSTOMER) {
                log.info("getSpecialAuditResult,客户信息审核回调结果太长 auditResult={}", reject);
                reject = reject.substring(0, WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_CUSTOMER - WmCustomerConstant.TOO_LONG_MSG.length()) + WmCustomerConstant.TOO_LONG_MSG;
            }
            return StringUtils.isBlank(reject) ? "空" : reject;
        }
        if (auditStatus == ApprovalStatusEnum.REVOKE.getCode()) {
            return "撤销特批";
        }
        if (auditStatus == ApprovalStatusEnum.TIMEOUT.getCode()) {
            return "超时失效";
        }
        return null;
    }
}
