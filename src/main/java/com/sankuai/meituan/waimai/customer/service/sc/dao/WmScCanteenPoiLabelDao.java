package com.sankuai.meituan.waimai.customer.service.sc.dao;

import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiLabelMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDBExample;
import com.sankuai.meituan.waimai.thrift.constants.WmYesNoCons;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WmScCanteenPoiLabelDao {

    @Autowired
    private WmScCanteenPoiLabelMapper wmScCanteenPoiLabelMapper;

    public int batchSave(List<WmScCanteenPoiLabelDB> labelDB) {
        if (CollectionUtils.isEmpty(labelDB)) {
            return 0;
        }
        return wmScCanteenPoiLabelMapper.batchInsert(labelDB);
    }

    public List<WmScCanteenPoiLabelDB> getLabelsByCanteenId(Integer canteenId) {
        WmScCanteenPoiLabelDBExample example = new WmScCanteenPoiLabelDBExample();
        example.createCriteria().andCanteenIdEqualTo(canteenId).andValidEqualTo(WmYesNoCons.YES.getValue());
        return wmScCanteenPoiLabelMapper.selectByExample(example);
    }

    public List<WmScCanteenPoiLabelDB> getLabelsByCanteenIdAndLabelType(Integer canteenId, Integer labelType) {
        WmScCanteenPoiLabelDBExample example = new WmScCanteenPoiLabelDBExample();
        example.createCriteria().
                andCanteenIdEqualTo(canteenId).
                andLabelTypeEqualTo(labelType).
                andValidEqualTo(WmYesNoCons.YES.getValue());
        return wmScCanteenPoiLabelMapper.selectByExample(example);
    }

    public List<WmScCanteenPoiLabelDB> getLabelsByCanteenIdAndPoiId(Integer canteenId, Long poiId) {
        WmScCanteenPoiLabelDBExample example = new WmScCanteenPoiLabelDBExample();
        example.createCriteria()
                .andCanteenIdEqualTo(canteenId)
                .andValidEqualTo(WmYesNoCons.YES.getValue())
                .andWmPoiIdEqualTo(poiId);
        return wmScCanteenPoiLabelMapper.selectByExample(example);
    }

    public void batchDeleteLable(List<WmScCanteenPoiLabelDB> labelDBs) {
        if (CollectionUtils.isEmpty(labelDBs)) {
            return;
        }

        for(WmScCanteenPoiLabelDB labelDB : labelDBs) {
            wmScCanteenPoiLabelMapper.deleteByPrimaryKey(labelDB.getId());
        }
    }
}
