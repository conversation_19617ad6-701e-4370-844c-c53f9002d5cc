package com.sankuai.meituan.waimai.customer.domain;


import com.sankuai.meituan.waimai.customer.annotation.Encryption;
import com.sankuai.meituan.waimai.customer.constant.EncryptionTypeConstant;

import lombok.Data;

/**
 * 打包签约
 */
@Data
@Encryption(fields = {"context"}, recordType = EncryptionTypeConstant.ECONTRACT_SIGN_BATCH_CONTEXT_RECORD, isJSON = true)
public class WmEcontractSignBatchContextDB {

    private Long id;

    private Long batchId;

    private String context;

    private Byte valid;

    private Long ctime;

    private Long utime;
}
