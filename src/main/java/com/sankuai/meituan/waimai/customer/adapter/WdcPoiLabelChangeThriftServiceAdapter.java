package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.wdcn.api.dto.WdcPoiLabelChangeDTO;
import com.sankuai.meituan.wdcn.api.request.BaseRequest;
import com.sankuai.meituan.wdcn.api.request.label.WdcPoiLabelChangeRequest;
import com.sankuai.meituan.wdcn.api.response.WdcPoiLabelChangeResponse;
import com.sankuai.meituan.wdcn.api.service.WdcPoiLabelChangeThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 公海标签打标和掉标服务
 * <AUTHOR>
 * @date 2024/05/30
 * @email <EMAIL>
 */
@Service
@Slf4j
public class WdcPoiLabelChangeThriftServiceAdapter {

    @Autowired
    private WdcPoiLabelChangeThriftService wdcPoiLabelChangeThriftService;


    public final String SYSTEM_APPKEY = "com.sankuai.waimai.e.customer";

    /**
     * 新增标签
     */
    public final int ADD_LABEL = 1;
    /**
     * 删除标签
     */
    public final int DELETE_LABEL = 0;

    /**
     * 公海线索打上标签
     * @param wdcClueIdList 线索ID
     * @param labelId 标签ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void addWdcClueListLabel(List<Long> wdcClueIdList, Integer labelId) throws WmSchCantException {
        log.info("[WdcPoiLabelChangeThriftServiceAdapter.addWdcClueListLabel] wdcClueIdList = {}, labelId = {}",
                JSONObject.toJSONString(wdcClueIdList), labelId);
        if (CollectionUtils.isEmpty(wdcClueIdList)) {
            return;
        }

        List<WdcPoiLabelChangeDTO> wdcPoiLabelChangeList = new ArrayList<>();
        for (Long wdcClueId : wdcClueIdList) {
            WdcPoiLabelChangeDTO changeDTO = new WdcPoiLabelChangeDTO();
            // 标签ID
            changeDTO.setLabelId(labelId);
            // 线索ID
            changeDTO.setWdcId(wdcClueId);
            // 待变更的标签值(默认标签值为1, 掉标为0)
            changeDTO.setLabelValue(ADD_LABEL);
            wdcPoiLabelChangeList.add(changeDTO);
        }

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setAppKey(SYSTEM_APPKEY);

        WdcPoiLabelChangeRequest request = new WdcPoiLabelChangeRequest();
        request.setBaseRequest(baseRequest);
        request.setWdcPoiLabelChangeList(wdcPoiLabelChangeList);

        try {
            log.info("[WmWdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel] request = {}", JSONObject.toJSONString(request));
            WdcPoiLabelChangeResponse response = wdcPoiLabelChangeThriftService.changeWdcPoiLabel(request);
            log.info("[WmWdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel] response = {}", JSONObject.toJSONString(response));

            if (response == null || CollectionUtils.isEmpty(response.getSuccessWdcIdList())
                    || response.getSuccessWdcIdList().size() != wdcClueIdList.size()) {
                log.error("[WmWdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel] response is null. request = {}", JSONObject.toJSONString(request));
            }

        } catch (Exception e) {
            log.error("[WmWdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel] Exception. wdcClueIdList = {}, labelId = {}",
                    JSONObject.toJSONString(wdcClueIdList), labelId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "公海线索打标失败");
        }
    }


    /**
     * 公海线索打上标签
     * @param wdcClueId 线索ID
     * @param labelId 标签ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void addWdcClueLabel(Long wdcClueId, Integer labelId) throws WmSchCantException {
        List<WdcPoiLabelChangeDTO> wdcPoiLabelChangeList = new ArrayList<>();
        WdcPoiLabelChangeDTO changeDTO = new WdcPoiLabelChangeDTO();
        // 标签ID
        changeDTO.setLabelId(labelId);
        // 线索ID
        changeDTO.setWdcId(wdcClueId);
        // 待变更的标签值(默认标签值为1, 掉标为0)
        changeDTO.setLabelValue(ADD_LABEL);
        wdcPoiLabelChangeList.add(changeDTO);

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setAppKey(SYSTEM_APPKEY);

        WdcPoiLabelChangeRequest request = new WdcPoiLabelChangeRequest();
        request.setBaseRequest(baseRequest);
        request.setWdcPoiLabelChangeList(wdcPoiLabelChangeList);
        try {
            log.info("[WmWdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel] request = {}", JSONObject.toJSONString(request));
            WdcPoiLabelChangeResponse response = wdcPoiLabelChangeThriftService.changeWdcPoiLabel(request);
            log.info("[WmWdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel] response = {}", JSONObject.toJSONString(response));

            if (response == null || !response.getSuccessWdcIdList().contains(wdcClueId)) {
                log.error("[WmWdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel] response is null. request = {}",
                        JSONObject.toJSONString(request));
            }
        } catch (Exception e) {
            log.error("[WmWdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel] Exception. wdcClueId = {}, labelId = {}",
                    wdcClueId, labelId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "公海线索打标失败");
        }
    }

    /**
     * 公海线索掉标签
     * @param wdcClueId 线索ID
     * @param labelId 标签ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void deleteWdcClueLabel(Long wdcClueId, Integer labelId) throws WmSchCantException {
        List<WdcPoiLabelChangeDTO> wdcPoiLabelChangeList = new ArrayList<>();
        WdcPoiLabelChangeDTO changeDTO = new WdcPoiLabelChangeDTO();
        // 待变更的标签值(默认标签值为1, 掉标为0)
        changeDTO.setLabelValue(DELETE_LABEL);
        // 标签ID
        changeDTO.setLabelId(labelId);
        // 线索ID
        changeDTO.setWdcId(wdcClueId);
        wdcPoiLabelChangeList.add(changeDTO);

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setAppKey(SYSTEM_APPKEY);

        WdcPoiLabelChangeRequest request = new WdcPoiLabelChangeRequest();
        request.setBaseRequest(baseRequest);
        request.setWdcPoiLabelChangeList(wdcPoiLabelChangeList);
        try {
            log.info("[WmWdcPoiLabelChangeThriftServiceAdapter.deleteWdcClueLabel] request = {}", JSONObject.toJSONString(request));
            WdcPoiLabelChangeResponse response = wdcPoiLabelChangeThriftService.changeWdcPoiLabel(request);
            log.info("[WmWdcPoiLabelChangeThriftServiceAdapter.deleteWdcClueLabel] response = {}", JSONObject.toJSONString(response));

            if (response == null || !response.getSuccessWdcIdList().contains(wdcClueId)) {
                log.error("[WmWdcPoiLabelChangeThriftServiceAdapter.deleteWdcClueLabel] response is null. request = {}",
                        JSONObject.toJSONString(request));
            }
        } catch (Exception e) {
            log.error("[WmWdcPoiLabelChangeThriftServiceAdapter.deleteWdcClueLabel] Exception. wdcClueId = {}, labelId = {}",
                    wdcClueId, labelId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "公海线索掉标失败");
        }
    }




}
