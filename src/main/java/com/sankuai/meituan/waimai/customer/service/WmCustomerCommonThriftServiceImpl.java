package com.sankuai.meituan.waimai.customer.service;

import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.service.common.WmCustomerCommonWrapService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBlackWhiteListService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.ModuleDetailStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerCommonThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class WmCustomerCommonThriftServiceImpl implements WmCustomerCommonThriftService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerCommonThriftServiceImpl.class);

    @Autowired
    private WmCustomerCommonService wmCustomerCommonService;

    @Autowired
    private WmCustomerCommonWrapService wmCustomerCommonWrapService;

    @Autowired
    private WmCustomerBlackWhiteListService wmCustomerBlackWhiteListService;


    @Override
    public ModuleDetailStatus getModuleDetailStatusByCustomerId(Integer customerId) throws TException, WmCustomerException {
        return wmCustomerCommonService.getModuleDetailStatusByCustomerId(customerId);
    }

    @Override
    public WmCustomerCommonBo saveWmCustomerModulesInfo(WmCustomerInfoAggre wmCustomerInfoAggre, int opUid, String opName) throws WmCustomerException, TException {
        try {
            return wmCustomerCommonService.saveWmCustomerModulesInfo(wmCustomerInfoAggre, opUid, opName);
        } catch (WmCustomerException e) {
            logger.warn("saveWmCustomerModulesInfo, code = {}, msg = {}", e.getCode(), e.getMessage());
            WmCustomerCommonBo wmCustomerCommonBo = wmCustomerCommonWrapService.wrapWmCustomerCommonBo(CustomerConstants.RESULT_CODE_ERROR, e.getMsg(), 0);
            if (WmCustomerConstant.RESULT_CODE_CUSTOMER_VERIFY_ERROR == e.getCode()) {
                wmCustomerCommonBo.setExistOldCustomer(true);
            }
            if (WmCustomerConstant.RESULT_CODE_PAPER_CUSTOMER_VERIFY_ERROR == e.getCode()) {
                wmCustomerCommonBo.setPaperCustomer(true);
                wmCustomerCommonBo.setExistOldCustomer(true);
            }
            return wmCustomerCommonBo;
        } catch (Exception e) {
            logger.error("调用saveWmCustomerModulesInfo保存客户模块信息异常", e);
            return wmCustomerCommonWrapService.wrapWmCustomerCommonBo(CustomerConstants.RESULT_CODE_ERROR,
                    "调用saveWmCustomerModulesInfo保存客户模块信息异常", 0);
        }
    }

    @Override
    public BooleanResult saveCustomerBlackWhiteList(CustomerBlackWhiteBo bo)
            throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(bo);
        return wmCustomerBlackWhiteListService.saveCustomerBlackWhiteList(bo);
    }

    @Override
    public BooleanResult deleteCustomerBlackWhiteList(CustomerBlackWhiteParam param)
            throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(param);
        return wmCustomerBlackWhiteListService.deleteCustomerBlackWhiteList(param);
    }

    @Override
    public BooleanResult queryInCustomerBlackWhiteList(CustomerBlackWhiteParam param)
            throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(param);
        return wmCustomerBlackWhiteListService.queryInCustomerBlackWhiteList(param);
    }

    @Override
    public BooleanResult isInWalletGreyList(Integer customerId)
            throws TException, WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(customerId);
        return wmCustomerBlackWhiteListService.isInWalletGreyList(customerId);
    }

    @Override
    public BooleanResult walletMigrationControl(Integer bizId, String bizType)
            throws TException, WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(bizId);
        AssertUtil.assertStringNotEmpty(bizType,"业务类型");
        return wmCustomerBlackWhiteListService.walletMigrationControl(bizId,bizType);
    }

    @Override
    public BooleanResult sendCustomerAsyncNotify(String msg)
            throws TException, WmCustomerException {
        AssertUtil.assertStringNotEmpty(msg,"消息内容");
        wmCustomerCommonService.sendCustomerAsyncNotify(msg);
        return new BooleanResult(true);
    }

    @Override
    public BooleanResult saveCustomerContract(WmCustomerContractSaveParam saveParam) throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(saveParam);
        AssertUtil.assertIntegerMoreThan0(saveParam.getCustomerId(),"客户ID");
        AssertUtil.assertCollectionNotEmpty(saveParam.getContractTypeList(),"待创建合同");
        return wmCustomerCommonService.saveCustomerContract(saveParam);
    }

    @Override
    public BooleanResult sendCMSToCustomerKP(SendCmsToKpParam param) throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(param);
        AssertUtil.assertIntegerMoreThan0(param.getCustomerId(),"客户ID");
        AssertUtil.assertStringNotEmpty(param.getSmsTemplateId(),"短信模版");
        AssertUtil.assertMapNotEmpty(param.getPair(),"短信动态参数");
        return wmCustomerCommonService.sendCMSToCustomerKP(param);
    }

    @Override
    public BooleanResult sendCMSToWmPoiContact(SendCmsToWmPoiContactParam param) throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(param);
        AssertUtil.assertLongMoreThan0(param.getWmPoiId(),"门店ID");
        AssertUtil.assertStringNotEmpty(param.getSmsTemplateId(),"短信模版");
        AssertUtil.assertMapNotEmpty(param.getPair(),"短信动态参数");
        return wmCustomerCommonService.sendCMSToWmPoiContact(param);
    }
}
