package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: scm
 * @description: 公共属性
 * @author: jianghuimin02
 * @date: 2020-04-24 11:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WmScCommonDB {
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户ID
     */
    private int userId;
    /**
     * 更新时间
     */
    private int utime;
    /**
     * 创建时间
     */
    private int ctime;
    /**
     * 额外字段json-map，使用方式:
     * key新增属性：value新增属性对应的值
     */
    private String ext;
}
