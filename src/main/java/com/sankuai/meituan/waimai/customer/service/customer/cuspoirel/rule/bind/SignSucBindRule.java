package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.bind;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.CustomerPoiRelStrategyBean;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IBindCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.poirel.flow.CustomerPoiRelFlowEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240109
 * @desc 签约通知成功进行绑定规则-非切换的正常流程
 */
@Service
@Slf4j
@Rule
public class SignSucBindRule {

    /**
     * 条件判断回调结果为成功
     *
     * @param context
     * @return
     */
    @Condition
    public boolean when(@Fact("context") CustomerPoiBindFlowContext context) {
        return context.getBindSignNoticeDTO().getSignResult().equals(EcontractTaskStateEnum.SUCCESS.getIntValue());
    }

    /**
     * 根据策略执行
     *
     * @return
     */
    @Action
    public void execute(@Fact("context") CustomerPoiBindFlowContext context) throws WmCustomerException {
        //定义各层级策略使用bean对象
        CustomerPoiRelStrategyBean customerPoiRelStrategyBean = CustomerPoiRelStrategyBean.builder()
                .checkBeanName("bindSignNoticeCheckStrategy")
                .preCoreBeanName("bindSignNoticePreCoreStrategy")
                .coreBeanName("directBindCoreStrategy")
                .afterBeanName("directBindAfterStrategy")
                .build();
        //根据bean名称计算各层级的实际策略信息
        BindStrategy strategy = BindStrategy.buiLdStrategyWithContext(customerPoiRelStrategyBean);
        //构建流程策略
        BindFlowStrategy bindFlowStrategy = BindFlowStrategy.builder()
                .flowEnum(CustomerPoiRelFlowEnum.DIRECT_BIND)
                .wmPoiIdSet(context.getWmPoiIdSet())
                .bindStrategy(strategy)
                .build();
        context.setBindFlowStrategyList(Lists.newArrayList(bindFlowStrategy));
    }


    @Priority
    public int getPriority() {
        return 1;
    }


}
