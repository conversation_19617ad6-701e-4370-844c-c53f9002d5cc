package com.sankuai.meituan.waimai.customer.util.common;

import com.sankuai.meituan.waimai.e.graycenter.sdk.GrayConfigClient;
import com.sankuai.meituan.waimai.e.graycenter.sdk.domain.GrayParams;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.InvalidParamGrayException;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.KeyNotExistGrayException;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.UnexpectedGrayException;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.mortbay.util.ajax.JSON;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 商家端灰度平台工具类
 * <AUTHOR>
 * @date 2024/06/13
 * @email <EMAIL>
 */
@Slf4j
@Service
public class GrayConfigHelper {

    private static final String WD_DAO_CAN_TAB_GRAY = "WD_DAO_CAN_TAB_GRAY";

    public boolean isWdDcTabGray(String misId) {
        if (Strings.isEmpty(misId)) {
            return false;
        }
        GrayParams params = new GrayParams();
        Map<String, String> others = new HashMap<>();
        others.put("misId", misId);
        params.setOthers(others);
        try {
            return getBoolean(WD_DAO_CAN_TAB_GRAY, params);
        } catch (Exception e) {
            log.error("GrayConfigHelper#isWdDcTabGray, 查询到餐合同Tab页面灰度配置异常, param: {}", JSON.toString(params), e);
            return false;
        }
    }

    public boolean isQkBusinessCustomerPeriodGray(Integer customerId){
        if (customerId == null) {
            return false;
        }
        GrayParams params = new GrayParams();
        Map<String, String> others = new HashMap<>();
        others.put("customerId", String.valueOf(customerId));
        params.setOthers(others);
        try {
            return getBoolean("QK_BUSINESS_CUSTOMER_PERIOD", params);
        } catch (Exception e) {
            log.error("获取企客公司合同有效期灰度配置异常, param: {}", JSON.toString(params), e);
            throw new RuntimeException(e);
        }
    }

    private static boolean getBoolean(String key, GrayParams params) throws WmCustomerException {
        String stringValue = getValue(key, params);
        return Boolean.parseBoolean(stringValue);
    }

    private static String getString(String key, GrayParams params) throws WmCustomerException {
        return getValue(key, params);
    }

    private static int getInt(String key, GrayParams params) throws WmCustomerException {
        String stringValue = getValue(key, params);
        return Integer.parseInt(stringValue);
    }

    //region 工具方法
    private static String getValue(String grayKey, GrayParams params) throws WmCustomerException {
        String grayConfig;
        try {
            //GrayConfigClient提供批量查询方法，有需要可选择调用
            grayConfig = GrayConfigClient.getGrayConfig(grayKey, params);
        } catch (InvalidParamGrayException | KeyNotExistGrayException | UnexpectedGrayException e) {
            log.error("灰度平台查询异常, key: {}, 参数 param: {}, e: ", grayKey, JSON.toString(params), e);
            throw new WmCustomerException(500,e.getMessage());
        }
        if (StringUtils.isBlank(grayConfig)) {
            log.error("关键灰度配置缺失, key: {}, 参数 param: {}", grayKey, JSON.toString(params));
            throw new WmCustomerException(500,"系统灰度配置缺失");
        }
        return grayConfig;
    }
}
