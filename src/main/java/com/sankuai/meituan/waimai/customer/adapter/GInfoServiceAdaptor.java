package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.xm.ginfo.thrift.GinfoOpenServiceI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 大象基础通信服务
 * 接入文档
 * https://km.sankuai.com/collabpage/62462076#id-1.21%20%E5%8F%91%E9%80%81%E7%BE%A4%E9%80%9A%E7%9F%A5%EF%BC%88sendTextNoticeToMembers%EF%BC%89
 */
@Service
@Slf4j
public class GInfoServiceAdaptor {

    private static final Integer APP_ID = 1;

    @Autowired
    private GinfoOpenServiceI.Iface ginfoOpenService;

    /**
     * 创建跨企业群
     * 
     * @param moderatorId 群主
     * @param participantIds 群成员
     * @param name 群名称
     * @throws WmCustomerException
     */
    public Long createOverCompanyRoom(Long moderatorId, Set<Long> participantIds, String name)
            throws WmCustomerException {
        try {
            Long groupID = ginfoOpenService.createOverCompanyRoom(moderatorId, Sets.newHashSet(moderatorId),
                    participantIds, APP_ID, name, true);
            log.info("GInfoServiceAdaptor.createOverCompanyRoom,创建跨企业群完成,moderatorId={},name={},groupId={}",
                    moderatorId, name, groupID);
            return groupID;
        } catch (Exception e) {
            log.error("GInfoServiceAdaptor.createOverCompanyRoom,创建跨企业群发生异常,moderatorId={},name={}", moderatorId, name,
                    e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "创建跨企业群发生异常");
        }
    }

    /**
     * 发送群通知消息
     * 
     * @param roomId
     * @param msg
     * @param uids
     * @throws WmCustomerException
     */
    public void sendRoomNoticeMsg(Long roomId, String msg, List<Long> uids) throws WmCustomerException {
        try {
            log.info("GInfoServiceAdaptor.sendRoomNoticeMsg,发送群通知消息,roomId={},msg={},uids={}", roomId, msg,
                    JSON.toJSONString(uids));
            ginfoOpenService.sendTextNoticeToMembers(0, roomId, uids, msg, APP_ID);
        } catch (Exception e) {
            log.error("GInfoServiceAdaptor.sendRoomNoticeMsg,发送群通知消息发生异常,roomId={},msg={}", roomId, msg, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "发送群通知消息异常");
        }

    }

}
