package com.sankuai.meituan.waimai.customer.dao.sc.delivery;

import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryFollowUpDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校交付跟进信息Mapper
 * <AUTHOR>
 * @date 2024/02/10
 * @email <EMAIL>
 **/
@Component
public interface WmSchoolDeliveryFollowUpMapper {

    /**
     * 根据主键ID查询学校交付跟进信息
     * @param id 主键ID
     * @return 学校交付跟进信息
     */
    WmSchoolDeliveryFollowUpDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据学校主键ID查询学校交付跟进信息
     * @param schoolPrimaryId 学校主键ID
     * @return 学校交付跟进信息
     */
    List<WmSchoolDeliveryFollowUpDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据交付ID查询学校交付跟进信息
     * @param deliveryId 交付ID
     * @return 学校交付跟进信息
     */
    WmSchoolDeliveryFollowUpDO selectByDeliveryId(@Param("deliveryId") Integer deliveryId);

    /**
     * 新增学校交付跟进信息
     * @param wmSchoolDeliveryFollowUpDO 交付跟进信息
     * @return 更新行数
     */
    int insertSelective(WmSchoolDeliveryFollowUpDO wmSchoolDeliveryFollowUpDO);

    /**
     * 根据主键ID更新学校交付跟进生效信息
     * @param wmSchoolDeliveryFollowUpDO 学校交付跟进生效信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmSchoolDeliveryFollowUpDO wmSchoolDeliveryFollowUpDO);

    /**
     * 通过主键ID进行软删除
     * @param id 主键ID
     */
    void deleteByPrimaryId(@Param("id") Integer id);

}
