package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpPoiService;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-02 14:15
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class OpmanagerRelPoiVerify extends KpPreverify {

    @Autowired
    private WmCustomerKpPoiService wmCustomerKpPoiService;

    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        // 新增校验
        if (insertKp != null) {
            // 新增空实现，后续可扩展
        }

        // 更新校验
        if (updateKp != null) {
            // 新增空实现，后续可扩展
        }

        // 删除操作时，kp关联的生效门店数不等于0时，不可操作kp删除
        if (deleteKp != null) {
            // 生效、生效变更待授权状态下，若关联门店数非0不可操作删除
            if (deleteKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
                Integer relNum = wmCustomerKpPoiService.getKpBindPoiNum(deleteKp.getId());
                if (relNum != 0) {
                    ThrowUtil.throwClientError("当前KP有关联的门店，不可删除");
                }
            } else if (deleteKp.getState() == KpSignerStateMachine.CHANGE_NO_AUTHORIZE.getState()) {
                Integer tempNum = wmCustomerKpPoiService.getTempKpBindPoiNum(deleteKp.getId());
                if (tempNum != 0) {
                    ThrowUtil.throwClientError("当前KP有关联的门店，不可删除");
                }
            }
        }
        return new Object();// 暂且返回object，方便后续扩展
    }

//    private void relPoiVerify(WmCustomerKp kp) throws WmCustomerException {
//        // 草稿表和正式表中只要有该门店关联过kp的记录，校验驳回
//        boolean relNum = wmCustomerKpPoiService.getPoiRelInfo(kp);
//        if (!relNum) {
//            ThrowUtil.throwClientError("门店列表中存在已被关联门店");
//        }
//    }
}
