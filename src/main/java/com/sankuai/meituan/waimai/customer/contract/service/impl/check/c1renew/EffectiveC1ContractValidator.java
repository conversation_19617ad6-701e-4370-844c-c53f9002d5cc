package com.sankuai.meituan.waimai.customer.contract.service.impl.check.c1renew;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-09-06 19:56
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class EffectiveC1ContractValidator implements IContractValidator {

    @Autowired
    private WmContractService wmContractService;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        log.info("EffectiveC1ContractValidator#valid，customerId:{}", contractBo.getBasicBo().getParentId());
        try {
            Long parentId = new Long(contractBo.getBasicBo().getParentId());
            List<Integer> types = Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(), WmTempletContractTypeEnum.C1_PAPER.getCode());
            Integer status = CustomerContractStatus.EFFECT.getCode();
            List<WmTempletContractBasicBo> wmTempletContractBasicBoList = wmContractService.selectByParentIdAndTypesAndStatus(parentId, types, status);
            return CollectionUtils.isEmpty(wmTempletContractBasicBoList);
        } catch(Exception e){
            log.error("EffectiveC1ContractValidator#valid Exception, customerId:{}", contractBo.getBasicBo().getParentId(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "系统繁忙，请稍后重试");
        }
    }
}
