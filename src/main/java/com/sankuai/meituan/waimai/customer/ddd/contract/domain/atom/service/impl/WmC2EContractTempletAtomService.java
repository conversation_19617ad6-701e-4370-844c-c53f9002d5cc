package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;

import java.util.UUID;

import com.sankuai.meituan.waimai.customer.domain.WmContractAgentInfo;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.agent.domain.WmCommercialAgentInfo;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractC2ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;

@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.C2_E})
public class WmC2EContractTempletAtomService extends AbstractWmEContractTempletAtomService {

    private static Logger logger = LoggerFactory.getLogger(WmC2EContractTempletAtomService.class);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String c2ENum = ContractNumberUtil.genC2ENum(insertId);
        contractBo.getBasicBo().setContractNum(c2ENum);
        logger.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, c2ENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), c2ENum);
        return insertId;
    }

    @Override
    protected EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB)
            throws WmCustomerException {
        WmContractSignAggre wmContractSignAggre = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList());
        WmTempletContractSignBo partyASigner = wmContractSignAggre.getPartyASignerBo();
        WmTempletContractSignBo partyBSigner = wmContractSignAggre.getPartyBSignerBo();

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C2CONTRACT);

        EcontractC2ContractInfoBo contractInfoBo = new EcontractC2ContractInfoBo.Builder().build();
        contractInfoBo.setContractNum(contractBo.getBasicBo().getContractNum());
        contractInfoBo.setValidate(DateUtil.secondsToString(contractBo.getBasicBo().getDueDate()));

        contractInfoBo.setPartAName(partyASigner.getSignName());
        contractInfoBo.setPartASignTime(partyASigner.getSignTime());

        WmCustomerKp kpOfSigner = wmCustomerKpService.getCustomerKpOfEffectiveSigner(contractBo.getBasicBo().getParentId());
        if (kpOfSigner != null) {
            contractInfoBo.setPartAContact(kpOfSigner.getCompellation());
            contractInfoBo.setPartAContactPhone(kpOfSigner.getPhoneNum());
        }

        contractInfoBo.setPartBName(partyBSigner.getSignName());
        contractInfoBo.setPartBContact(partyBSigner.getSignPeople());
        contractInfoBo.setPartBContactPhone(partyBSigner.getSignPhone());
        contractInfoBo.setPartBSignTime(partyBSigner.getSignTime());
        contractInfoBo.setAgentId(partyBSigner.getSignId());
        contractInfoBo.setAgentName(partyBSigner.getSignName());

        WmContractAgentInfo agentInfo = wmContractAgentService.queryAgentInfoById(partyBSigner.getSignId());
        contractInfoBo.setPartBName(agentInfo.getBusinessLicenseName());
        contractInfoBo.setAgentName(agentInfo.getBusinessLicenseName());
        contractInfoBo.setLegalPersonPhone(agentInfo.getLegalPersonPhone());
        contractInfoBo.setLegalPersonEmail(agentInfo.getLegalPersonEmail());
        contractInfoBo.setQuaNum(agentInfo.getBusinessLicenseCode());
        contractInfoBo.setAgentQuaType(CustomerType.CUSTOMER_TYPE_BUSINESS);
        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
        return applyBo;
    }

    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        if (!canInvalidC2Contract(contractId, opUid)) {
            return false;
        }
        WmContractVersionDB contractVersionDB = wmContractVersionService.getByIdAndTypeMaster(contractId,
                CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
        int contractVersionStatus = CustomerContractStatus.STAGE.getCode();
        if (contractVersionDB != null) {
            contractVersionStatus = contractVersionDB.getStatus();
        }

        boolean invalidRes = super.invalid(contractId, opUid, opUname);

        if (invalidRes && contractVersionDB != null && NumberUtils.isDigits(contractVersionDB.getTransaction_id())
                && contractVersionStatus == CustomerContractStatus.SIGNING.getCode()) {
            wmEcontractSignBzService.cancelSign(Long.valueOf(contractVersionDB.getTransaction_id()));
        }
        return invalidRes;
    }

}
