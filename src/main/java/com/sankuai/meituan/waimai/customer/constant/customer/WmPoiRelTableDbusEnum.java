package com.sankuai.meituan.waimai.customer.constant.customer;

import org.apache.commons.lang.StringUtils;

/**
 * 门店库DTS监听表变更-客户门店列表
 */
public enum WmPoiRelTableDbusEnum {

    TABLE_WM_POI_CONTROL("waimai.wm_poi_control", "门店控制信息", "wm_poi_control"),

    TABLE_WM_POI_BASE("waimai.wm_poi_base", "门店基本信息", "wm_poi_base"),

    TABLE_WM_POI_BRAND_REL("waimai.wm_poi_brand_rel", "门店与品牌关系", "wm_poi_brand_rel");

    private String code;

    private String desc;

    private String alias;


    WmPoiRelTableDbusEnum(String code, String desc, String alias) {
        this.code = code;
        this.desc = desc;
        this.alias = alias;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getAlias() {
        return alias;
    }

    public static WmPoiRelTableDbusEnum of(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WmPoiRelTableDbusEnum tableDbusEnum : values()) {
            if (tableDbusEnum.getCode().equals(code)) {
                return tableDbusEnum;
            }
        }
        return null;
    }

    public static WmPoiRelTableDbusEnum ofForAlas(String alias) {
        if (StringUtils.isBlank(alias)) {
            return null;
        }
        for (WmPoiRelTableDbusEnum tableDbusEnum : values()) {
            if (tableDbusEnum.getAlias().equals(alias)) {
                return tableDbusEnum;
            }
        }
        return null;
    }
}
