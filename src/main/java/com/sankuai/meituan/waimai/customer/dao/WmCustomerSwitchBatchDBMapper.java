package com.sankuai.meituan.waimai.customer.dao;


import org.springframework.stereotype.Component;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerSwitchBatchDB;

/**
 * 客户切换不下线-切换任务&签约任务关系
 *
 * Created by lix<PERSON>peng on 2021/7/19
 */
@Component
public interface WmCustomerSwitchBatchDBMapper {

    int insert(WmCustomerSwitchBatchDB switchBatchDB);

    int updateStatusById(WmCustomerSwitchBatchDB switchBatchDB);

    WmCustomerSwitchBatchDB selectBySwitchId(long switchId);

    WmCustomerSwitchBatchDB selectByManualBatchId(long manualBatchId);
}
