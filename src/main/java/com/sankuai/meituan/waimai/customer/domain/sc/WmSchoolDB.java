package com.sankuai.meituan.waimai.customer.domain.sc;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolDiningCabinetEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 学校信息DO
 *
 * @program: scm
 * @description: 学校数据库管理类
 * @author: jianghuimin02
 * @date: 2020-04-23 15:43
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmSchoolDB extends WmScCommonDB {
    /**
     * 物理主键
     */
    private int id;
    /**
     * 学校ID
     */
    private int schoolId;
    /**
     * 学校标志码
     */
    private long schoolCode;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 学校类型：211：1、985：2、211&985：3、普通公立：4、省重点：5、私立：6、专科：8、其他：9
     */
    private int schoolType;
    /**
     * 学校类型描述
     */
    private String typeDesc;
    /**
     * 供给分级：SKR直营：1、KR直营：2、食堂直营：3
     */
    private int grade;
    /**
     * 分级描述
     */
    private String gradeDesc;
    /**
     * 学校KP
     */
    private String schoolKp;
    /**
     * kp的手机号
     */
    private String schoolKpNum;
    /**
     * 蜂窝类型
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolAorTypeEnum}
     */
    private Integer aorType;
    /**
     * 蜂窝ID
     */
    private int aorId;
    /**
     * 蜂窝名称
     */
    private String aorName;
    /**
     * 区域
     */
    private String area;
    /**
     * 城市团队
     */
    private String cityTeam;
    /**
     * 物理城市ID
     */
    private int cityId;
    /**
     * 物理城市名称
     */
    private String cityName;
    /**
     * 食堂数量
     */
    private int canteenNum;
    /**
     * 状态（预留字段，默认状态生效：1,失效：0）
     */
    private int schoolStatus;
    /**
     * 状态描述
     */
    private String statusDesc;
    /**
     * 学校生效状态，1：生效、0：逻辑删除
     */
    private int valid;
    /**
     * 学校责任人
     */
    private String responsiblePerson;
    /**
     * 学校责任人UID
     */
    private Integer responsibleUid;
    /**
     * 物理主键集合
     */
    private List<Integer> idList;
    /**
     * 学校ID集合
     */
    private List<Integer> schoolIdList;
    /**
     * 合同编号(已废弃)
     */
    private String contractNum;
    /**
     * 线索ID
     */
    private Long wdcClueId;
    /**
     * 合作状态 0-未合作，1-已合作
     */
    private Integer wmCoStatus;
    /**
     * 查询条件-物理主键开始ID
     */
    private Integer beginId;
    /**
     * 查询条件-物理主键结束ID
     */
    private Integer endId;
    /**
     * 查询条件—门店Id
     */
    private Long poiId;
    /**
     * 学校地址
     */
    private String schoolAddress;
    /**
     * 在校师生人数
     */
    private Integer teaStuNum;
    /**
     * 校外是否可配送进校 1-是，2-否(已废弃)
     */
    private Integer outDeliveryIn;
    /**
     * 配送状况 1-第三方配送，2-自配送，3-聚合配送，4-其他(已废弃)
     */
    private Integer deliveryStatus;
    /**
     * 其他配送状况(已废弃)
     */
    private String deliveryStatusOther;
    /**
     * 配送状况为聚合配送时录入的站点ID(已废弃)
     */
    private Integer siteId;
    /**
     * 学校KP加密
     */
    private String schoolKpNumEncryption;
    /**
     * 学校KP加密token
     */
    private String schoolKpNumToken;
    /**
     * 原schoolKpNum是否写，只做判断使用不存储
     * 0-表示写 1-表示不写
     */
    private int notSaveSchoolKpNum;
    /**
     * 物理城市ID列表
     */
    private List<Integer> cityIdList;
    /**
     * 查询条件-食堂数量左值
     */
    private Integer canteenNumStart;
    /**
     * 查询条件-食堂数量右值
     */
    private Integer canteenNumEnd;
    /**
     * 查询条件-学校标签列表
     */
    private List<String> schoolLabelQueryList;
    /**
     * 更新条件-学校标签列表
     */
    private String schoolLabels;
    /**
     * 查询条件-学校负责人列表
     */
    private List<String> responsiblePersonList;
    /**
     * 查询条件-DSL语句
     */
    private String dslQuery;
    /**
     * 查询条件-学校蜂窝ID列表
     */
    private List<Integer> aorIdList;
    /**
     * 在校学生人数
     */
    private Integer studentNum;
    /**
     * 蜂窝内校外学生人数
     */
    private Integer outsideStudentNum;
    /**
     * 学校开发方式
     */
    private Integer schoolDevType;
    /**
     * 学校合作方式
     */
    private Integer schoolCooperateType;
    /**
     * 合同 / 授权编号
     */
    private String agreementCode;
    /**
     * 合同 / 授权编号类型
     */
    private Integer agreementType;
    /**
     * 合同 / 授权编号开始时间
     */
    private Integer agreementTimeStart;
    /**
     * 合同 / 授权编号结束时间
     */
    private Integer agreementTimeEnd;
    /**
     * 学校分级
     */
    private Integer schoolLevel;
    /**
     * 学校生命周期
     */
    private Integer schoolLifecycle;
    /**
     * 校方是否允许配送进校
     */
    private Integer schoolAllowDelivery;
    /**
     * 学校熄灯信息-JSON格式
     */
    private String schoolLightOffInfo;
    /**
     * 是否有取餐柜
     * {@link SchoolDiningCabinetEnum}
     */
    private Integer diningCabinet;
    /**
     * 学校聚合站点ID
     */
    private String aggreSiteId;
    /**
     * 聚合订单是否允许配送进校
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolAggreOrderAllowDeliveryEnum}
     */
    private Integer aggreOrderAllowDelivery;
    /**
     * 当前交付状态
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryCurrentStatusEnum}
     */
    private Integer currentDeliveryStatus;

    /**
     * 食堂品类
     */
    private List<Integer> canteenCategory;

    /**
     * 食堂品类数量
     */
    private Integer canteenCategoryNum;

    /**
     * 学校分类
     */
    private Integer schoolCategory;

    /**
     * 学校分类描述
     */
    private Integer schoolCategoryDesc;

}
