package com.sankuai.meituan.waimai.customer.contract.service.impl.check.config;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.econtrct.client.constants.config.FrameContractConfigStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: 配置化合同发起签约校验类
 * @author: liuyunjie05
 * @create: 2024/5/21 15:34
 */
@Slf4j
@Service
public class ConfigContractStartSignValidator implements IContractValidator {

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        if (ContractSourceEnum.isCodeSource(contractBo.getBasicBo().getContractSource())) {
            return true;
        }
        // 启用状态发起待打包 停用后流程正常流转
        if (contractBo.getManualBatchId() > 0) {
            return true;
        }
        ContractConfigInfo configContractInfo = contractBo.getBasicBo().getConfigContractInfo();
        boolean isEnable = configContractInfo.getStatus() == FrameContractConfigStatusEnum.ENABLE.getCode();
        if (!isEnable) {
            log.warn("ConfigContractStartSignValidator#valid, configContractInfo: {}", JSON.toJSONString(configContractInfo));
            throw new WmCustomerException(-1, "该合同未启用, 不允许发起保存");
        }
        return true;
    }
}
