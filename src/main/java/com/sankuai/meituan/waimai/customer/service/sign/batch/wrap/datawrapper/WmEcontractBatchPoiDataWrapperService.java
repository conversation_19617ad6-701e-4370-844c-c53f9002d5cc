package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfMakerConstant.DELIVERY_AGGREGATION_VERSION_V2;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import java.util.Set;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 客户批量门店数据
 */
@Service
public class WmEcontractBatchPoiDataWrapperService {

    private static final Logger LOG = LoggerFactory.getLogger(WmEcontractBatchPoiDataWrapperService.class);

    private static final String TEMPLET_NAME = "poilist_base_info";
    private static final String POILIST_BASE_INFO_V2 = "poilist_base_info_v2";
//    private static final String POILIST_DELIVERY_INFO_V2 = "poilist_delivery_info_v2";

    public static final String SUPPORT_MARK = "hasSupport";
    private static final String MODUEL_SETTLE = "SETTLE";
    private static final String MODUEL_DELIVERY = "DELIVERY";

    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo, Map<String, String> subjectEstampMap)
            throws IllegalAccessException, WmCustomerException {
        EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo =
                JSON.parseObject(taskBo.getApplyContext(), EcontractBatchPoiInfoExtBo.class);

        if(MccConfig.batchPoiDataWrapperDeliveryUseNewData()){
            List<PdfContentInfoBo> result = Lists.newArrayList();
            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            pdfInfoBo.setPdfTemplateName(POILIST_BASE_INFO_V2);
            pdfInfoBo.setPdfBizContent(generatePdfBizeObjectByModule(econtractBatchPoiInfoExtBo,Sets.newHashSet(MODUEL_SETTLE,MODUEL_DELIVERY)));
            pdfInfoBo.setPdfMetaContent(generatePdfMetaeObject(contextBo, econtractBatchPoiInfoExtBo, subjectEstampMap));
            result.add(pdfInfoBo);

//            pdfInfoBo = new PdfContentInfoBo();
//            pdfInfoBo.setPdfTemplateName(POILIST_DELIVERY_INFO_V2);
//            pdfInfoBo.setPdfBizContent(generatePdfBizeObjectByModule(econtractBatchPoiInfoExtBo,Sets.newHashSet(MODUEL_DELIVERY)));
//            pdfInfoBo.setPdfMetaContent(generatePdfMetaeObject(contextBo, econtractBatchPoiInfoExtBo));
//            result.add(pdfInfoBo);
            return result;
        }else{
            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            pdfInfoBo.setPdfTemplateName(TEMPLET_NAME);
            pdfInfoBo.setPdfBizContent(generatePdfBizeObject(econtractBatchPoiInfoExtBo));
            pdfInfoBo.setPdfMetaContent(generatePdfMetaeObject(contextBo, econtractBatchPoiInfoExtBo, subjectEstampMap));
            return Lists.newArrayList(pdfInfoBo);
        }
    }

    /**
     * 生成pdf内容
     */
    private List<Map<String, String>> generatePdfBizeObject(EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo)
            throws IllegalAccessException {
        return generatePdfBizeObjectByModule(econtractBatchPoiInfoExtBo, Sets.newHashSet(MODUEL_SETTLE,MODUEL_DELIVERY));
    }

    private List<Map<String, String>> generatePdfBizeObjectByModule(EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo,Set<String> module)
            throws IllegalAccessException {

        LOG.info("生成门店列表数据.....");

        List<Map<String, String>> listMap = Lists.newArrayList();
        if (econtractBatchPoiInfoExtBo != null) {
            List<EcontractPoiInfoExtBo> econtractPoiInfoExtBoList = econtractBatchPoiInfoExtBo.getEcontractPoiInfoExtBoList();
            if (CollectionUtils.isNotEmpty(econtractPoiInfoExtBoList)) {
                for (EcontractPoiInfoExtBo econtractPoiInfoExtBo : econtractPoiInfoExtBoList) {
                    Map<String, String> pdfMap = Maps.newHashMap();
                    //门店基本数据
                    pdfMap.put("address", StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getAddress(), StringUtils.EMPTY));
                    pdfMap.put("poiName", StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getPoiName(), StringUtils.EMPTY));
                    //结算模块数据
                    if(module.contains(MODUEL_SETTLE)){
                        assembleSettleInfo(econtractPoiInfoExtBo, pdfMap);
                    }
                    EcontractDeliveryInfoBo econtractDeliveryInfoBo = econtractPoiInfoExtBo.getEcontractDeliveryInfoBo();
                    EcontractDeliveryAggregationInfoBo econtractDeliveryAggregationInfoBo = null;
                    // 配送模块数据
                    if (module.contains(MODUEL_DELIVERY)) {
                        if (econtractDeliveryInfoBo != null) {
                            if (MccConfig.batchPoiDataWrapperDeliveryUseNewData()) {
                                pdfMap.putAll(MapUtil.Object2Map(econtractDeliveryInfoBo));
                                econtractDeliveryAggregationInfoBo = econtractDeliveryInfoBo.getEcontractDeliveryAggregationInfoBo();
                            } else {
                                assembleDelivery(pdfMap, econtractDeliveryInfoBo);
                            }
                        }
                    }
                    listMap.add(pdfMap);
                    // 单独一行组装聚合配送信息
                    Map<String, String> deliveryAggregationInfoMap = assembleDeliveryAggregationInfo(econtractPoiInfoExtBo, econtractDeliveryAggregationInfoBo);
                    if (MapUtils.isNotEmpty(deliveryAggregationInfoMap)) {
                        listMap.add(deliveryAggregationInfoMap);
                    }
                    // 组装闪购2.2重量加价
                    if(econtractDeliveryInfoBo!= null && econtractDeliveryInfoBo.getEcontractDeliverySG2_2InfoBo()!=null){
                        pdfMap.put("weightRule",StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getEcontractDeliverySG2_2InfoBo().getWeightRule(), StringUtils.EMPTY));
                        pdfMap.put("distanceRuleMinPrice",StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getEcontractDeliverySG2_2InfoBo().getDistanceRuleMinPrice(), StringUtils.EMPTY));
                    }

                }
            }
        }
        return listMap;
    }

    private Map<String, String> assembleDeliveryAggregationInfo(
            EcontractPoiInfoExtBo econtractPoiInfoExtBo,
            EcontractDeliveryAggregationInfoBo econtractDeliveryAggregationInfoBo)
            throws IllegalAccessException {
        Map<String, String> result = Maps.newHashMap();
        if(econtractPoiInfoExtBo != null && econtractDeliveryAggregationInfoBo != null && DELIVERY_AGGREGATION_VERSION_V2.equals(econtractDeliveryAggregationInfoBo.getDeliveryAggregationVersion())){
            result.put("onlyShowDeliveryAggregationInfo","support");
            //门店基本数据
            result.put("address",
                    StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getAddress(), StringUtils.EMPTY));
            result.put("poiName",
                    StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getPoiName(), StringUtils.EMPTY));
            result.put("deliveryType","聚合配送");
            result.putAll(MapUtil.Object2Map(econtractDeliveryAggregationInfoBo));
        }
        return result;
    }

    private void assembleSettleInfo(EcontractPoiInfoExtBo econtractPoiInfoExtBo,
            Map<String, String> pdfMap) {
        pdfMap.put("accountName",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getAccountName(), StringUtils.EMPTY));
        pdfMap.put("settleTypeName",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getSettleTypeName(), StringUtils.EMPTY));
        pdfMap.put("financialContacts", StringUtils
                .defaultIfEmpty(econtractPoiInfoExtBo.getFinancialContacts(), StringUtils.EMPTY));
        pdfMap.put("branchName",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getBranchName(), StringUtils.EMPTY));
        pdfMap.put("bankName",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getBankName(), StringUtils.EMPTY));
        pdfMap.put("minPayAmount",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getMinPayAmount(), StringUtils.EMPTY));
        pdfMap.put("accountNumber",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getAccountNumber(), StringUtils.EMPTY));
        pdfMap.put("cityName",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getCityName(), StringUtils.EMPTY));
        pdfMap.put("payPeriod",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getPayPeriod(), StringUtils.EMPTY));
        pdfMap.put("financePhone",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getFinancePhone(), StringUtils.EMPTY));
        pdfMap.put("provinceName",
                StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getProvinceName(), StringUtils.EMPTY));
    }

    private void assembleDelivery(Map<String, String> pdfMap,
            EcontractDeliveryInfoBo econtractDeliveryInfoBo) {
        pdfMap.put("supportSLA",
                StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getSupportSLA(), StringUtils.EMPTY));
        pdfMap.put("chargingDesc", StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getChargingDesc(),
                StringUtils.EMPTY));
        pdfMap.put("deliveryType", StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getDeliveryType(),
                StringUtils.EMPTY));
        pdfMap.put("servicePackageValueAddedFee", StringUtils.defaultIfEmpty(
                econtractDeliveryInfoBo.getServicePackageValueAddedFee(), StringUtils.EMPTY));
        pdfMap.put("servicePackageMinAmount", StringUtils.defaultIfEmpty(
                econtractDeliveryInfoBo.getServicePackageMinAmount(), StringUtils.EMPTY));
        pdfMap.put("promiseFinishRate", StringUtils
                .defaultIfEmpty(econtractDeliveryInfoBo.getPromiseFinishRate(), StringUtils.EMPTY));
        pdfMap.put("servicePackageFee", StringUtils
                .defaultIfEmpty(econtractDeliveryInfoBo.getServicePackageFee(), StringUtils.EMPTY));
        pdfMap.put("incrementalCost", StringUtils
                .defaultIfEmpty(econtractDeliveryInfoBo.getIncrementalCost(), StringUtils.EMPTY));
        pdfMap.put("feeInfo",
                StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getFeeInfo(), StringUtils.EMPTY));
        pdfMap.put("servicePackageName",
                StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getServicePackageName(), StringUtils.EMPTY));
    }

    /**
     * 生成签章内容
     */
    private Map<String, String> generatePdfMetaeObject(EcontractBatchContextBo contextBo,
            EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo, Map<String, String> subjectEstampMap) {

        Map<String, String> pdfMap = Maps.newHashMap();
        if (econtractBatchPoiInfoExtBo != null) {
            EcontractContractInfoBo contractInfoBo = econtractBatchPoiInfoExtBo.getEcontractContractInfoBo();
            String signTime = DateUtil.secondsToString(DateUtil.unixTime());

            pdfMap.put("hasSupport", econtractBatchPoiInfoExtBo.getHasSupport() ? SUPPORT_MARK : "");
            pdfMap.put("hasSLASupport", econtractBatchPoiInfoExtBo.getHasSLASupport() ? SUPPORT_MARK : "");
            pdfMap.put("sortKeys", econtractBatchPoiInfoExtBo.getSortkeys());
            pdfMap.put("contractNumber",
                    StringUtils.defaultIfEmpty(contractInfoBo.getContractNum(), StringUtils.EMPTY));
            pdfMap.put("partA",
                    StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
            pdfMap.put("partAStampName",
                    StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
            pdfMap.put("partBStampName",
                    StringUtils.defaultIfEmpty(contractInfoBo.getPartBName(), WmEcontractContextUtil.PARTB_STAMP_NAME));
            pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
            pdfMap.put("partBSignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
            pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
            pdfMap.put("partBEstamp",
                    subjectEstampMap.getOrDefault(contractInfoBo.getPartBName(), PdfConstant.MT_SIGNKEY));
            pdfMap.put("hasCommisionTypeRemark",
                    econtractBatchPoiInfoExtBo.getHasCommisionTypeRemark() ? "hasCommisionTypeRemark" : "");

        }

        return pdfMap;
    }
}
