package com.sankuai.meituan.waimai.customer.settle.bo;

public class WmSettleVo {

    private String wmCustomerId = "";
    private String partyAFinancePeople = ""; //财务联系人
    private String partyAFinancePhone = ""; //财务联系电话
    private String accName = "";//开户名
    private String accCardNo = "";//账号
    private String province = "";//省
    private String city = "";//市
    private String bank = "";//银行
    private String branchname = ""; //支行
    private Integer settleType = 0; //结算方式 1.周期结算 2.商家自提
    private Integer payPeriod = 0; //结算周期

    private String  payPeriodStr = "";//结算周期完整文案

    private String minPayAmount = ""; //最低结算金额
    private Integer minPayAmountType = 0; //最低结算金额状体 1:100元 2:300 3:500 4其他
    private Byte accType = (byte)0;  //账户类型 1=对公，2=对私
    private String legalCertNum; // required  营业执照
    private String legalPerson; // required  法人姓名
    private String legalIdCard; // required  法人身份证号
    private String certNum; // required  身份证号
    private String reservePhone; // required  银行预留手机号

    public String getPayPeriodStr() {
        return payPeriodStr;
    }

    public void setPayPeriodStr(String payPeriodStr) {
        this.payPeriodStr = payPeriodStr;
    }

    public String getWmCustomerId() {
        return wmCustomerId;
    }

    public void setWmCustomerId(String wmCustomerId) {
        this.wmCustomerId = wmCustomerId;
    }

    public String getPartyAFinancePeople() {
        return partyAFinancePeople;
    }

    public void setPartyAFinancePeople(String partyAFinancePeople) {
        this.partyAFinancePeople = partyAFinancePeople;
    }

    public String getPartyAFinancePhone() {
        return partyAFinancePhone;
    }

    public void setPartyAFinancePhone(String partyAFinancePhone) {
        this.partyAFinancePhone = partyAFinancePhone;
    }

    public String getAccName() {
        return accName;
    }

    public void setAccName(String accName) {
        this.accName = accName;
    }

    public String getAccCardNo() {
        return accCardNo;
    }

    public void setAccCardNo(String accCardNo) {
        this.accCardNo = accCardNo;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBranchname() {
        return branchname;
    }

    public void setBranchname(String branchname) {
        this.branchname = branchname;
    }

    public Integer getSettleType() {
        return settleType;
    }

    public void setSettleType(Integer settleType) {
        this.settleType = settleType;
    }

    public Integer getPayPeriod() {
        return payPeriod;
    }

    public void setPayPeriod(Integer payPeriod) {
        this.payPeriod = payPeriod;
    }

    public String getMinPayAmount() {
        return minPayAmount;
    }

    public void setMinPayAmount(String minPayAmount) {
        this.minPayAmount = minPayAmount;
    }

    public Integer getMinPayAmountType() {
        return minPayAmountType;
    }

    public void setMinPayAmountType(Integer minPayAmountType) {
        this.minPayAmountType = minPayAmountType;
    }

    public Byte getAccType() {
        return accType;
    }

    public void setAccType(Byte accType) {
        this.accType = accType;
    }

    public String getLegalCertNum() {
        return legalCertNum;
    }

    public void setLegalCertNum(String legalCertNum) {
        this.legalCertNum = legalCertNum;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getLegalIdCard() {
        return legalIdCard;
    }

    public void setLegalIdCard(String legalIdCard) {
        this.legalIdCard = legalIdCard;
    }

    public String getCertNum() {
        return certNum;
    }

    public void setCertNum(String certNum) {
        this.certNum = certNum;
    }

    public String getReservePhone() {
        return reservePhone;
    }

    public void setReservePhone(String reservePhone) {
        this.reservePhone = reservePhone;
    }
}
