package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule;

import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum.DEFAULT_TYPE;

@Slf4j
@Service
public class CusPoiRelBaseStrategy {

    @Autowired
    private List<IBindCheckStrategy> bindCheckStrategyList;

    /**
     * 获取绑定策略
     *
     * @param customerRealType
     * @return
     */
    public IBindCheckStrategy getBindCheckStrategy(Integer customerRealType) throws WmCustomerException {
        try {
            //客户类型有有效校验
            CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(customerRealType);
            if (customerRealTypeEnum == null || customerRealTypeEnum == DEFAULT_TYPE) {
                log.warn("CusPoiRelBaseStrategy.getBindCheckStrategy,客户类型非有效定义,customerRealType={}", customerRealType);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户类型非有效定义，请检查客户类型");
            }

            //根据客户类型获取对应校验规则
            for (IBindCheckStrategy bindCheckStrategy : bindCheckStrategyList) {
                if (bindCheckStrategy.hitCheck(customerRealType)) {
                    return bindCheckStrategy;
                }
            }
        } catch (Exception e) {
            log.error("getBindCheckStrategy,根据客户类型获取校验策略类发生异常,customerRealType={}", customerRealType, e);
        }
        log.warn("CusPoiRelBaseStrategy.getBindCheckStrategy,未获取到绑定校验规则,customerRealType={}", customerRealType);
        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未匹配到绑定校验");
    }

}
