package com.sankuai.meituan.waimai.customer.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 存储加密-加解密流程切点
 * Created by lixuepeng on 2021/8/17
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface Encryption {
    int recordType();

    String[] fields();

    boolean isJSON();
}
