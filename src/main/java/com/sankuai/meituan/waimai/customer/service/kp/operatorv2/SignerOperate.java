package com.sankuai.meituan.waimai.customer.service.kp.operatorv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.KPSource;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-01 18:09
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class SignerOperate extends WmCustomerKpOperate {

    @Override
    public void insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        // 数据校验
        List<KpPreverify> verifyList = Arrays.asList(kpDataVerify, kpBlackListVerify, kpTypeNumVerify, signerCertTypeVerify, agentAuthPreVerify);
        for (KpPreverify kpPreverify : verifyList) {
            kpPreverify.verify(wmCustomer, oldCustomerKpList, operateKp, null, null, uid, uname);
        }
        // DB操作
        signerDBOperator.insert(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
    }

    @Override
    public void update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        // 数据校验
        List<KpPreverify> verifyList;
        if (operateKp.getKpSource() == KPSource.WAIMAI_ZRZ || operateKp.getKpSource() == KPSource.SHANGOU_ZRZ) {
            verifyList = Arrays.asList(signerAuditStatusVerify, signerTypeChangeVerify, kpDataVerify, kpBlackListVerify, kpTypeNumVerify, signerCertTypeVerify, signerKpOperateVerify, agentAuthPreVerify);
        } else {
            verifyList = Arrays.asList(kpDataVerify, kpBlackListVerify, kpTypeNumVerify, signerCertTypeVerify, signerKpOperateVerify, agentAuthPreVerify);
        }
        
        for (KpPreverify kpPreverify : verifyList) {
            kpPreverify.verify(wmCustomer, oldCustomerKpList, null, operateKp, null, uid, uname);
        }
        // DB操作
        signerDBOperator.update(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
    }

    @Override
    public void delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        // 数据校验
        List<KpPreverify> verifyList = Arrays.asList(kpTypeNumVerify);
        for (KpPreverify kpPreverify : verifyList) {
            kpPreverify.verify(wmCustomer, oldCustomerKpList, null, operateKp, null, uid, uname);
        }
        // DB操作
        signerDBOperator.delete(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
    }

}
