package com.sankuai.meituan.waimai.customer.service.sign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.adapter.BizUserServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmCustomerAdaptor;
import com.sankuai.meituan.waimai.customer.bo.QueryAccountPoiBO;
import com.sankuai.meituan.waimai.customer.bo.sign.BatchApplyManualPackContext;
import com.sankuai.meituan.waimai.customer.bo.sign.QueryAccountCustomerBO;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.customer.constant.SignPackStatusConstant;
import com.sankuai.meituan.waimai.customer.constant.agreement.QueryTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerSign;
import com.sankuai.meituan.waimai.customer.constant.common.CommonConst;
import com.sankuai.meituan.waimai.customer.constant.common.StatusCodeEnum;
import com.sankuai.meituan.waimai.customer.constant.sign.BatchTaskConstant;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.customer.contract.bo.ContractTypeBO;
import com.sankuai.meituan.waimai.customer.contract.bo.QuerySignedContractBO;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractTaskApplyService;
import com.sankuai.meituan.waimai.customer.service.sign.base.WmEcontractBaseApplyService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.external.check.BatchApplyCheckFilter;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.ManualPackConditionService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.customer.service.sign.version.ContractVersionService;
import com.sankuai.meituan.waimai.customer.settle.service.adapter.PaymentAgentAdapter;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.WmEcontractSignBatchDBUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.base.BaseResponseUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.UpstreamStatusEnum;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiValidEnum;
import com.sankuai.meituan.waimai.thrift.config.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.OpCustomerResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.ContractVersionPageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.PageInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.SignTaskCustomerDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.SignTaskInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.TaskCustomerPageDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.request.QuerySignTaskReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.request.SignTaskCustomerReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.request.ToSignCustomerTaskReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.AccountSignTaskResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.ToSignTaskQueryResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.QueryDcMtCustomerIdParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.contracttask.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.DaoCanContractContext;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WmEcontractSignBzService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractSignBzService.class);
    private static final ScheduledExecutorService scheduledthreadpool = Executors.newScheduledThreadPool(2);

    private static final ExecutorService executorService = TraceExecutors
        .getTraceExecutorService(new ThreadPoolExecutor(10, 50, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(100), new ThreadPoolExecutor.AbortPolicy()));

    private static final ExecutorService cancelSignExecutorService = TraceExecutors
        .getTraceExecutorService(new ThreadPoolExecutor(10, 100, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(200), new ThreadPoolExecutor.CallerRunsPolicy()));

    private final static Set<EcontractTaskApplyTypeEnum> CAN_MANUAL_SIGN_WITHOUT_CHECK = ImmutableSet.of(
            EcontractTaskApplyTypeEnum.QUA_REAL_LETTER,
            EcontractTaskApplyTypeEnum.POI_PROMOTION_SERVICE,
            EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT,
            EcontractTaskApplyTypeEnum.GROUP_MEAL,
            EcontractTaskApplyTypeEnum.FOODCITY_STATEMENT,
            EcontractTaskApplyTypeEnum.MEDIC_ORDER_SPLIT,
            EcontractTaskApplyTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT,
            EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT,
            EcontractTaskApplyTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT,
            EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_PURCHASE,
            EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT,
            EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT
    );

    @Resource
    private WmEcontractTaskApplyService wmEcontractTaskApplyService;

    @Resource
    private WmEcontractBaseApplyService wmEcontractBaseApplyService;

    @Autowired
    private WmEcontractBatchBizService wmEcontractBatchBizService;

    @Autowired
    private WmEcontractTaskBizService wmEcontractTaskBizService;

    @Autowired
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    @Autowired
    private ManualPackConditionService manualPackConditionService;

    @Autowired
    private WmEcontractManualTaskApplyService wmEcontractManualTaskApplyService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private EcontractBizService econtractBizService;

    @Autowired
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;

    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmEcontractTaskBizService wmEcontractTaskService;

    @Autowired
    private PaymentAgentAdapter paymentAgentAdapter;

    @Resource
    private BizUserServiceAdapter bizUserServiceAdapter;

    @Resource
    private WmCustomerAdaptor wmCustomerAdaptor;

    @Resource
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Resource
    private WmEcontractToSignService wmEcontractToSignService;

    @Resource
    private WmCustomerKpService wmCustomerKpService;

    @Resource
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private ContractVersionService contractVersionService;

    @Resource
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    @Autowired
    private WmContractService wmContractService;

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    public LongResult applyTask(EcontractTaskApplyBo applyBo)
        throws TException, WmCustomerException, IllegalAccessException {
        return wmEcontractTaskApplyService.applyTask(applyBo);
    }

    public LongResult easyApplyTask(EcontractTaskApplyBo applyBo)
            throws TException, WmCustomerException, IllegalAccessException {
        return wmEcontractTaskApplyService.easyApplyTask(applyBo);
    }

    public LongResult triggerApplyTask(EcontractTaskApplyBo applyBo)
            throws TException, WmCustomerException, IllegalAccessException {
        return wmEcontractTaskApplyService.triggerApplyTask(applyBo);
    }

    public LongResult applyBase(EcontractBaseApplyBo applyBo)
        throws TException, WmCustomerException, IllegalAccessException {
        return wmEcontractBaseApplyService.applyBase(applyBo);
    }

    public RetrySmsResponse resendMsg(Long taskId) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.resendMsg(taskId);
    }

    /**
     * 底层只校验task和batch状态
     * @param taskId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public RetrySmsResponse easyResendMsg(Long taskId) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.easyResendMsg(taskId);
    }

    public RetrySmsResponse resendMsgPack(Long signBatchId) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.resendMsgPack(signBatchId);
    }

    public BooleanResult cancelSign(Long taskId) throws TException, WmCustomerException {
        return cancelSign(taskId, Boolean.TRUE);
    }

    public BooleanResult cancelSign(Long taskId, Boolean callBack) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.cancelSign(taskId, null, callBack);
    }

    public BooleanResult forceCancelSignByTaskId(EcontractForceCancelSignByTaskIdBo forceCancelSignByTaskIdBo) throws TException, WmCustomerException {
        LOGGER.info("cancelSign forceCancelSignByTaskIdBo={}", JSON.toJSONString(forceCancelSignByTaskIdBo));
        // 查询taskId对应的task
        EcontractTaskBo taskBo = wmEcontractTaskService.getById(forceCancelSignByTaskIdBo.getTaskId());
        LOGGER.info("cancelSign taskBo = {}", JSON.toJSONString(taskBo));
        // 按batchId取消签约
        if(taskBo != null && taskBo.getBatchId() != null && taskBo.getBatchId() != 0L){
            return cancelSignByBatchIdWithSource(taskBo.getBatchId(),  WmEcontractBatchConstant.TASK_MANAGE_BATCH_LIST, forceCancelSignByTaskIdBo.getCallBack());
        }
        throw new WmCustomerException(500,"未找到taskId对应的batchId");
    }

    public BooleanResult cancelSign(Long taskId, String actionSource) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.cancelSign(taskId, actionSource);
    }

    public BooleanResult cancelSign(Long taskId, String actionSource, Boolean callBack) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.cancelSign(taskId, actionSource, callBack);
    }

    public BooleanResult cancelSignWithReason(Long taskId, String actionSource, String reason, Boolean callBack) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.cancelSignWithReason(taskId, actionSource, reason, callBack);
    }

    public BooleanResult forceUnbind(Long taskId) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.cancelSign(taskId, WmEcontractBatchConstant.FORCE_UNBIND);
    }

    public BooleanResult cancelBase(Integer customerId) throws TException, WmCustomerException {
        return wmEcontractBaseApplyService.cancelBase(customerId);
    }

    public BooleanResult unbindPoi(Integer customerId, Long wmPoiId) throws TException, WmCustomerException {
        return wmEcontractBaseApplyService.unbingPoi(customerId, wmPoiId);
    }

    public BooleanResult bindPoi(Integer customerId, List<Long> wmPoiIdList)
        throws TException, WmCustomerException, IllegalAccessException {
        return wmEcontractBaseApplyService.bindPoi(customerId, wmPoiIdList);
    }

    public BooleanResult checkCustomerBatchMatch(int customerId, long batchId) {
        WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
        return new BooleanResult(wmEcontractSignBatchDB != null && wmEcontractSignBatchDB.getCustomerId() == customerId);
    }

    public BooleanResult checkQuaRealLetterBatchMatch(long batchId) {
        WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
        if (wmEcontractSignBatchDB == null) {
            return new BooleanResult(false);
        }

        EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(wmEcontractSignBatchDB.getBatchContext(), EcontractBatchContextBo.class);
        return new BooleanResult(econtractBatchContextBo != null && EcontractBatchTypeEnum.QUA_REAL_LETTER.equals(econtractBatchContextBo.getBatchTypeEnum()));
    }

    public BooleanResult checkCustomerTaskMatch(int customerId, long taskId) {
        EcontractTaskBo taskBo = wmEcontractTaskBizService.getById(taskId);
        return new BooleanResult(taskBo != null && taskBo.getCustomerId() == customerId);
    }

    public BooleanResult checkQuaRealLetterTaskMatch(long taskId) {
        EcontractTaskBo taskBo = wmEcontractTaskBizService.getById(taskId);
        return new BooleanResult(taskBo != null && EcontractTaskApplyTypeEnum.QUA_REAL_LETTER.equals(taskBo.getApplyType()));
    }

    public RetrySmsResponse resendMsgByBatchId(long batchId) throws TException, WmCustomerException {
        if (isPackSign(batchId)) {
            WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
            return resendMsgByPackId(wmEcontractSignBatchDB.getPackId());
        } else {
            WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
            if (wmEcontractSignBatchDB != null) {
                EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(wmEcontractSignBatchDB.getBatchContext(), EcontractBatchContextBo.class);
                // 新结算签约任务同步来源于金融侧,特殊处理此类型任务
                if (econtractBatchContextBo != null && econtractBatchContextBo.getBatchTypeEnum() == EcontractBatchTypeEnum.SETTLE_NEW) {
                    boolean resendResult = paymentAgentAdapter.resendMsgByBatchId(batchId, econtractBatchContextBo.getBizId());
                    RetrySmsResponse result = new RetrySmsResponse();
                    result.setOk(resendResult);
                    EcontractCustomerKPBo kpBo = econtractBatchContextBo.getKpBo();
                    result.setMobiles(kpBo == null ? "" : kpBo.getSignerPhoneNum());
                    return result;
                }
                // 代理签约人类型重发短信不考虑客户、KP是否生效
                if (econtractBatchContextBo != null && econtractBatchContextBo.getBatchTypeEnum() == EcontractBatchTypeEnum.AGENT_SIGNER_AUTH) {
                    List<EcontractTaskBo> taskInfo = wmEcontractTaskBizService.getByBatchId(batchId);
                    return easyResendMsg(taskInfo.get(0).getId());
                }
            }
            List<EcontractTaskBo> taskInfo = wmEcontractTaskBizService.getByBatchId(batchId);
            if (CollectionUtils.isEmpty(taskInfo)) {
                return new RetrySmsResponse(false);
            }
            return resendMsg(taskInfo.get(0).getId());
        }
    }

    public RetrySmsResponse resendMsgByPackId(long packId) throws TException, WmCustomerException {
        LOGGER.info("resendMsgByPackId#packId:{}", packId);
        WmEcontractSignPackDB signPackDB = wmEcontractSignPackService.querySignPackById(packId);
        if (signPackDB.getStatus() == SignPackStatusConstant.INIT
            || signPackDB.getStatus() == SignPackStatusConstant.UN_APPLY
            || signPackDB.getStatus() == SignPackStatusConstant.PART_APPLY) {
            LOGGER.info("打包任务未就绪，packId:{}，status:{}", packId, signPackDB.getStatus());
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态无法重发短信");
        }
        if (signPackDB.getStatus() == SignPackStatusConstant.SUCCESS
            || signPackDB.getStatus() == SignPackStatusConstant.FAIL
            || signPackDB.getStatus() == SignPackStatusConstant.CANCEL) {
            LOGGER.info("打包任务到达终态，packId:{}，status:{}", packId, signPackDB.getStatus());
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态无法重发短信");
        }
        List<WmEcontractSignBatchDB> signBatchList = wmEcontractBigBatchParseService.querySignBatchListByPackIdAndStatus(packId, EcontractBatchStateEnum.IN_PROCESSING.getName());
        if (CollectionUtils.isEmpty(signBatchList)) {
            return new RetrySmsResponse(false);
        }

        // 是否需要按照优先级排序
        if (WmEcontractSignBatchDBUtil.isNeedSortByPriority(signBatchList)) {
            List<WmEcontractSignBatchDB> signBatchDBListSortByPriority =  WmEcontractSignBatchDBUtil.sortByPriority(signBatchList);
            return resendMsgPack(signBatchDBListSortByPriority.get(0).getId());
        }

        return resendMsgPack(signBatchList.get(0).getId());
    }

    public BooleanResult cancelSignByBatchIdWithSource(long batchId, String source) throws TException, WmCustomerException {
        return cancelSignByBatchIdWithSource(batchId, source, true);
    }

    public BooleanResult cancelSignByBatchIdWithSource(long batchId, String source, Boolean callBack) throws TException, WmCustomerException {
        if (isPackSign(batchId)) {
            WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
            // 若获取到的batchId为空 or 上下文为空,返回false
            if (wmEcontractSignBatchDB == null || StringUtils.isEmpty(wmEcontractSignBatchDB.getBatchContext())) {
                return new BooleanResult(false);
            }
            // 若对应batch已更新为取消状态,则直接返回true
            if (wmEcontractSignBatchDB.getBatchState().equals(EcontractBatchStateEnum.CANCEL.getName())) {
                return new BooleanResult(true);
            }
            return cancelSignByPackId(wmEcontractSignBatchDB.getPackId(), "", callBack);
        } else {
            return cancelSignByBatchIdWithSourceCommon(batchId, source, callBack);
        }
    }

    public BooleanResult cancelSignByPackId(Long packId) throws TException, WmCustomerException {
        return cancelSignByPackId(packId, "", true);
    }

    public BooleanResult cancelSignByPackIdWithReason(Long packId, String reason) throws TException, WmCustomerException {
        return cancelSignByPackId(packId, reason, true);
    }

    public BooleanResult cancelSignByPackId(Long packId, String reason, Boolean callBack) throws TException, WmCustomerException {
        LOGGER.info("cancelSignByPackId#packId:{}", packId);
        if(packId <= 0){
            LOGGER.error("cancelbypack but pack id zero，dangerous！！！");
            Cat.logMetricForCount("cancelbypack_but_packiszero_error");
            return new BooleanResult(false);
        }
        //获取batchid，取消
        List<WmEcontractSignBatchDB> batchDBList = wmEcontractBigBatchParseService.queryBatchListByPackId(packId);
        //取消完成后更新pack状态
        if (CollectionUtils.isNotEmpty(batchDBList)) {
            Map<Long, String> errorBatchMap = new HashMap<>();
            for (WmEcontractSignBatchDB signBatch : batchDBList) {
                cancelSignExecutorService.execute(() -> {
                    try {
                        cancelSignByPackIdWithSource(signBatch.getId(), WmEcontractBatchConstant.TASK_MANAGE_BATCH_LIST, reason, callBack);
                    } catch (Exception e) {
                        LOGGER.error("cancelSignByPackId 取消任务失败 batchId:{}", signBatch.getId(), e);
                        errorBatchMap.put(signBatch.getId(), e.getMessage());
                    }
                });
            }
            //存在失败场景
            if (MapUtils.isNotEmpty(errorBatchMap)) {
                LOGGER.info("cancelSignByPackId 取消任务失败详情 errorBatchMap:{}", JSON.toJSONString(errorBatchMap));
                return new BooleanResult(false);
            }
            //更新pack状态
            wmEcontractSignPackService.updateSignPackStatusById(packId, SignPackStatusConstant.CANCEL);
        }
        return new BooleanResult(true);
    }

    private BooleanResult cancelSignByBatchIdWithSourceCommon(long batchId, String source) throws TException, WmCustomerException {
        return cancelSignByBatchIdWithSourceCommon(batchId, source, true);
    }

    private BooleanResult cancelSignByBatchIdWithSourceCommon(long batchId, String source, Boolean callBack) throws TException, WmCustomerException {
        WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
        if (wmEcontractSignBatchDB == null || StringUtils.isEmpty(wmEcontractSignBatchDB.getBatchContext())) {
            return new BooleanResult(false);
        }
        EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(wmEcontractSignBatchDB.getBatchContext(), EcontractBatchContextBo.class);
        Map<Long, EcontractTaskBo> taskIdAndTaskMap = econtractBatchContextBo.getTaskIdAndTaskMap();
        if (MapUtils.isEmpty(taskIdAndTaskMap)) {
            return new BooleanResult(false);
        }
        //同步取消第一个任务+异步取消其他任务(延迟1s)
        Iterator<Entry<Long, EcontractTaskBo>> iterator = taskIdAndTaskMap.entrySet().iterator();
        Entry<Long, EcontractTaskBo> entry = iterator.next();
        cancelSign(entry.getKey(), source, callBack);//WmEcontractBatchConstant.TASK_MANAGE_BATCH_LIST
        int delayTime = 1;
        while (iterator.hasNext()) {
            entry = iterator.next();
            addScheduledthreadpool(entry.getKey(), delayTime++, source, callBack);
        }
        // in final cancel the base batch, 更新batch状态
        wmEcontractBatchBaseService.updateState(batchId, EcontractTaskStateEnum.CANCEL.getName());
        return new BooleanResult(true);
    }

    private BooleanResult cancelSignByPackIdWithSource(long batchId, String source) throws TException, WmCustomerException {
        return cancelSignByPackIdWithSource(batchId, source, "", true);
    }

    private BooleanResult cancelSignByPackIdWithSource(long batchId, String source, String reason, Boolean callBack) throws TException, WmCustomerException {
        WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
        if (wmEcontractSignBatchDB == null || StringUtils.isEmpty(wmEcontractSignBatchDB.getBatchContext())) {
            return new BooleanResult(false);
        }
        EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(wmEcontractSignBatchDB.getBatchContext(), EcontractBatchContextBo.class);
        Map<Long, EcontractTaskBo> taskIdAndTaskMap = econtractBatchContextBo.getTaskIdAndTaskMap();
        if (MapUtils.isEmpty(taskIdAndTaskMap)) {
            return new BooleanResult(false);
        }

        // 同步取消所有任务
        for (Map.Entry<Long, EcontractTaskBo> entry : taskIdAndTaskMap.entrySet()) {
            return cancelSign(entry.getKey(), source, callBack);
        }

        // 取消原因不为空则更新取消原因
        if (StringUtils.isNotEmpty(reason)) {
            wmEcontractBatchBizService.updateBatchFailMsgByBatchId(batchId, reason, source);
        }

        return new BooleanResult(true);
    }

    public List<EcontractTaskBo> getTaskInfoByBatchId(long batchId) {
        return wmEcontractTaskBizService.getByBatchId(batchId);
    }

    public LongResult getUnBindPoiTaskId(long batchId) throws TException, WmCustomerException {
        List<EcontractTaskBo> taskBos = getTaskInfoByBatchId(batchId);
        if (taskBos.size() == 1 && taskBos.get(0).getApplyType().equals(
            EcontractTaskApplyTypeEnum.CUSTOMER.getName())) {
            return new LongResult(taskBos.get(0).getId());
        } else {
            return null;
        }
    }

    private void addScheduledthreadpool(final long taskId, int time, final String source) {
        addScheduledthreadpool(taskId, time, source, true);
    }

    private void addScheduledthreadpool(final long taskId, int time, final String source, Boolean callBack) {
        scheduledthreadpool.schedule(new Runnable() {
            @Override
            public void run() {
                try {
                    cancelSign(taskId, source, callBack);//WmEcontractBatchConstant.TASK_MANAGE_BATCH_LIST
                } catch (WmCustomerException e) {
                    LOGGER.warn("取消任务异常，任务Id={}", taskId, e);
                } catch (TException e) {
                    LOGGER.error("取消任务异常，任务Id={}", taskId, e);
                }
            }
        }, time, TimeUnit.SECONDS);
    }

    /**
     * 判断是否需要手动打包校验的弹窗
     */
    public BooleanResult canApplyManualTask(ManualTaskApplyBo manualTaskApplyBo) throws WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(manualTaskApplyBo.getCustomerId(), "客户ID");
        AssertUtil.assertIntegerMoreThan0(manualTaskApplyBo.getCommitUid(), "提交人");
        LOGGER.info("WmEcontractSignBzService#canApplyManualTask, manualTaskApplyBo: {}", JSONObject.toJSONString(manualTaskApplyBo));
        //灰度用户操作 && （是多店客户 || 有模块已生效）
        if (!wmEcontractSignGrayService.canUseManualPack(manualTaskApplyBo.getCommitUid())) {
            return new BooleanResult(false);
        }
        // 无需校验可直接发起的合同类型
        if (CAN_MANUAL_SIGN_WITHOUT_CHECK.contains(manualTaskApplyBo.getApplyTypeEnum())) {
            return new BooleanResult(true);
        }
        // 多门店客户
        if (manualPackConditionService.hasMultiPoi(manualTaskApplyBo.getCustomerId())) {
            return new BooleanResult(true);
        }
        // 兼容C2类型的合同
        if (manualTaskApplyBo.getApplyTypeEnum() == EcontractTaskApplyTypeEnum.C2CONTRACT) {
            // 单门店客户且直营门店，可以发起C2待打包
            boolean isAgentWmPoiId = wmCustomerPoiService.isSinglePoiAgentBoolean(manualTaskApplyBo.getCustomerId());
            if (!isAgentWmPoiId) {
                return new BooleanResult(true);
            }
            // 如果已存在holding状态的C2任务，可以发起C2待打包
            List<EcontractTaskBo> taskBoList = wmEcontractTaskService.getByCustomerIdAndStateMaster(
                manualTaskApplyBo.getCustomerId(), EcontractTaskStateEnum.HOLDING.getName());
            List<String> taskTypeList = taskBoList.stream()
                .map(EcontractTaskBo::getApplyType)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(taskTypeList)
                && taskTypeList.contains(EcontractTaskApplyTypeEnum.C2CONTRACT.getName())) {
                return new BooleanResult(true);
            }
        }
        // 存在有效的C1/C2/结算/配送数据
        if (manualPackConditionService.hasEffectModule(manualTaskApplyBo.getCustomerId())) {
            return new BooleanResult(true);
        }
        // 存在有效的主体变更协议
        List<WmTempletContractBasicBo> contractList = wmContractService.selectByParentIdAndTypes(new Long(manualTaskApplyBo.getCustomerId()),
                Lists.newArrayList(WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode()));
        if(manualTaskApplyBo.getApplyTypeEnum() == EcontractTaskApplyTypeEnum.SUBJECT_CHANGE_SUPPLEMENT && CollectionUtils.isNotEmpty(contractList)){
            return new BooleanResult(true);
        }

        //包含待绑定门店,允许手动打包
        if (manualPackConditionService.hasSwitchingWmPoi(manualTaskApplyBo.getCustomerId())) {
            LOGGER.info("hasSwitchingWmPoi#true,wmCustomerId={}", manualTaskApplyBo.getCustomerId());
            return new BooleanResult(true);
        }
        return new BooleanResult(false);
    }

    public LongResult applyManualTask(ManualTaskApplyBo manualTaskApplyBo) throws WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(manualTaskApplyBo.getCustomerId(), "客户ID");
        return wmEcontractManualTaskApplyService.applyManualTask(manualTaskApplyBo);
    }

    public LongResult applyTaskForAutoRenewal(ManualTaskApplyBo manualTaskApplyBo) throws WmCustomerException, TException {
        AssertUtil.assertIntegerMoreThan0(manualTaskApplyBo.getCustomerId(), "客户ID");
        AssertUtil.assertLongMoreThan0(manualTaskApplyBo.getManualTaskId(), "打包任务id");
        return wmEcontractManualTaskApplyService.applyTaskForAutoRenewal(manualTaskApplyBo);
    }

    public BooleanResult cancelManualTask(int wmCustomerId, EcontractTaskApplyTypeEnum applyType)
        throws WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户ID");
        return wmEcontractManualTaskApplyService.cancelManualTask(wmCustomerId, applyType);
    }

    public BooleanResult cancelManualTaskWithBizId(int wmCustomerId, long bizId, EcontractTaskApplyTypeEnum applyType)
        throws WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户ID");
        AssertUtil.assertLongMoreThan0(bizId, "业务ID");
        return wmEcontractManualTaskApplyService.cancelManualTaskWithBizId(wmCustomerId, bizId, applyType);
    }

    public BooleanResult cancelManualTaskWithBizId4Config(int wmCustomerId, long bizId, String module) throws WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户ID");
        AssertUtil.assertLongMoreThan0(bizId, "业务ID");
        return wmEcontractManualTaskApplyService.cancelManualTaskWithBizId4Config(wmCustomerId, bizId, module);
    }

    public BooleanResult cancelManualTask(long manualTaskId, int opUid, String source)
        throws WmCustomerException, TException {
        return wmEcontractManualTaskApplyService.cancelManualTask(manualTaskId, opUid, source);
    }

    public LongResult applyManualPack(List<Long> manualTaskIds, int commitUid, String source)
        throws WmCustomerException, TException {
        LOGGER.info("#applyManualPack,manualTaskIds={},commitUid={},source={}", manualTaskIds, commitUid, source);
        Map<String, List<WmEcontractSignManualTaskDB>> taskMap = manualPackConditionService.canApplyManualPack(manualTaskIds, commitUid);
        return wmEcontractManualTaskApplyService.applyManualPackWithGroup(taskMap, commitUid, manualTaskIds, source);
    }

    public BooleanResult cancelManualTaskByCustomerIdAndModule(int wmCustomerId, String module) {
        return wmEcontractManualTaskApplyService
            .cancelManualTaskByCustomerIdAndModule(wmCustomerId, module);
    }

    public List<LongResult> getManualTaskIdByCustomerId(int wmCustomerId) {
        return wmEcontractManualTaskApplyService.getManualTaskIdByCustomerId(wmCustomerId);
    }

    public List<Long> getManualTaskIdByCustomerId(ManualTaskIdQueryParam manualTaskIdQueryParam) {
        log.info("getManualTaskIdByCustomerId,manualTaskIdQueryParam={}", JSON.toJSON(manualTaskIdQueryParam));
        Integer customerId = manualTaskIdQueryParam.getCustomerId();
        Integer businessGroupLine = manualTaskIdQueryParam.getBusinessGroupLine();
        List<WmEcontractSignManualTaskBo> manualTaskInfoList = getManualTaskInfoByCustomerId(customerId);
        log.info("getManualTaskIdByCustomerId manualTaskInfoList={}", JSON.toJSON(manualTaskInfoList));
        if (CollectionUtils.isEmpty(manualTaskInfoList)) {
            return Collections.emptyList();
        }

       return manualTaskInfoList.stream()
                .filter(e -> filterMaunalTaskByBusinessGroupLine(e, businessGroupLine))
                .map(WmEcontractSignManualTaskBo::getId)
                .collect(Collectors.toList());
    }

    /**
     * 过滤手动打包任务：
     * 1. 业务线非到餐过滤到餐任务
     * 2. 业务线为到餐仅保留到餐任务
     * @param manualTaskInfo
     * @param businessGroupLine
     * @return
     */
    private boolean filterMaunalTaskByBusinessGroupLine(WmEcontractSignManualTaskBo manualTaskInfo, Integer businessGroupLine) {
        Set<EcontractTaskApplyTypeEnum> taskTypeSet = WmEcontractBatchConstant.TASK_TYPE_MAP.get(WmEcontractBatchConstant.ALL_TASK_TYPE_DC);
        if (Objects.nonNull(businessGroupLine) && businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())) {
            // 到餐业务线
            return taskTypeSet.contains(EcontractTaskApplyTypeEnum.getByName(manualTaskInfo.getModule()));
        }
        // 非到餐业务线
        return !taskTypeSet.contains(EcontractTaskApplyTypeEnum.getByName(manualTaskInfo.getModule()));
    }

    public List<LongResult> getManualTaskIdList(int wmCustomerId, int startTime) {
        return wmEcontractManualTaskApplyService.getManualTaskIdList(wmCustomerId, startTime);
    }

    public List<WmEcontractSignManualTaskBo> getManualTaskInfoByCustomerId(int wmCustomerId) {
        return wmEcontractManualTaskApplyService.getManualTaskInfoByCustomerId(wmCustomerId);
    }

    public BooleanResult cancelWmCustomerSwitch(Integer targetCustomerId, List<Long> wmPoiIdList, int opUid,
                                                String opUname)
        throws WmCustomerException, TException {
        return wmEcontractBaseApplyService.cancelWmCustomerSwitch(targetCustomerId, wmPoiIdList, opUid, opUname);
    }

    public BooleanResult cancelWmCustomerSwitchForWmPoi(CancelSignForSwitchParam param)
        throws WmCustomerException, TException {
        return wmEcontractBaseApplyService.cancelWmCustomerSwitchForWmPoi(param);
    }

    public String getVoiceNoticeMsg(long taskId) throws WmCustomerException, TException {
        return wmEcontractTaskApplyService.getVoiceNoticeMsg(taskId);
    }

    public SignVersionBo getSignVersionInfo(long taskId) {
        return wmEcontractTaskApplyService.getSignVersionInfo(taskId);
    }

    public Map<Long, List<Long>> getManualTaskIdByCustomerIdList(List<Long> wmCustomerIdList) {
        return wmEcontractManualTaskApplyService.getManualTaskIdByCustomerIdList(wmCustomerIdList);
    }

    public List<BatchApplyManualPackResultOutPutDTO> batchApplyManualPack(List<Long> customerIdList, int commitUid) throws TException, WmCustomerException {
        int commitManualPackTaskMaxSizeWaimai = MccSignConfig.getApplyManualPackBatchCustomerNumWaimai();
        if (customerIdList.size() > commitManualPackTaskMaxSizeWaimai) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                String.format("最多提交%个客户进行批量签约", commitManualPackTaskMaxSizeWaimai));
        }

        LOGGER.info(
            "wmEcontractSignBzService.batchApplyManualPack, before customerIdList = {}, size = {}, commitUid = {}",
            JSON.toJSONString(customerIdList),
            customerIdList.size(),
            commitUid);
        List<BatchApplyManualPackResultOutPutDTO> batchApplyManualPackResultOutPutDTOList = Lists.newArrayList();
        List<WmCustomerBasicBo> wmCustomerBasicBoList = Lists.newArrayList();
        try {
            wmCustomerBasicBoList = wmCustomerService.getCustomerListByIdOrMtCustomerId(Sets.newHashSet(customerIdList));
        } catch (WmCustomerException e) {
            LOGGER.warn("查询客户异常", e);
            for (Long customerId : customerIdList) {
                BatchApplyManualPackResultOutPutDTO batchApplyManualPackResultOutPutDTO = new
                    BatchApplyManualPackResultOutPutDTO();
                batchApplyManualPackResultOutPutDTO.setWmCustomerId(customerId);
                batchApplyManualPackResultOutPutDTO.setManualBatchId(0);
                batchApplyManualPackResultOutPutDTO.setCode(WmContractErrorCodeConstant.SYSTEM_ERROR);
                batchApplyManualPackResultOutPutDTO.setResultReason("系统异常");
                batchApplyManualPackResultOutPutDTOList.add(batchApplyManualPackResultOutPutDTO);
            }
            return batchApplyManualPackResultOutPutDTOList;
        }
        if (CollectionUtils.isEmpty(wmCustomerBasicBoList)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "客户不存在");
        }

        List<Long> existCustomerIdList = Lists.newArrayList();
        for (WmCustomerBasicBo wmCustomerBasicBo : wmCustomerBasicBoList) {
            existCustomerIdList.add(Long.valueOf(wmCustomerBasicBo.getId()));
        }

        Set<Long> notExistCustomerIdSet = Sets.difference(Sets.newHashSet(customerIdList),
            Sets.newHashSet(existCustomerIdList));
        for (Long customerId : notExistCustomerIdSet) {
            BatchApplyManualPackResultOutPutDTO batchApplyManualPackResultOutPutDTO = new BatchApplyManualPackResultOutPutDTO();
            batchApplyManualPackResultOutPutDTO.setWmCustomerId(customerId);
            batchApplyManualPackResultOutPutDTO.setManualBatchId(0);
            batchApplyManualPackResultOutPutDTO.setCode(WmContractErrorCodeConstant.BUSINESS_ERROR);
            batchApplyManualPackResultOutPutDTO.setResultReason("客户不存在");
            batchApplyManualPackResultOutPutDTOList.add(batchApplyManualPackResultOutPutDTO);
        }
        LOGGER.info("wmEcontractSignBzService.batchApplyExistCustomer after: customerId = {}, notExistCustomerId = {}",
            JSON.toJSONString
                (customerIdList),
            JSON.toJSONString(notExistCustomerIdSet));

        // 查询客户打包任务
        Map<Long, List<Long>> customerTaskMap = getManualTaskIdByCustomerIdList(existCustomerIdList);
        // 批量触发手动打包
        batchApplyManualPackResultOutPutDTOList.addAll(doBatchApply(existCustomerIdList, customerTaskMap, commitUid));
        LOGGER.info("#batchApplyManualPack, batchApplyManualPackResultOutPutDTOList = {}",
            JSON.toJSONString(batchApplyManualPackResultOutPutDTOList));

        return batchApplyManualPackResultOutPutDTOList;
    }

    public OpCustomerResultBo batchApplyManualPack(List<Long> customerIdList, int commitUid, OpCustomerResultBo opCustomerResultBo) throws TException, WmCustomerException {
        LOGGER.info("#batchApplyManualPack, customerIdList = {}, size = {}, commitUid = {}", JSON.toJSONString(customerIdList), customerIdList.size(), commitUid);
        BatchApplyManualPackContext context = new BatchApplyManualPackContext();
        initContext(context, customerIdList, commitUid, opCustomerResultBo);
        BatchApplyCheckFilter.getBatchApplyManualPackCheckFilter().filter(context);
        if (context.isNeedProcess()) {
            // 查询客户打包任务
            Map<Long, List<Long>> customerTaskMap = getManualTaskIdByCustomerIdList(context.getWmCustomerIdList());
            fillMtCustomerTaskMap(context.getWmCustomerBasicBoList(), customerTaskMap);
            // 批量触发手动打包
            doBatchApply(context, customerTaskMap);
        }
        LOGGER.info("#batchApplyManualPack, opCustomerResultBo = {}", JSON.toJSONString(opCustomerResultBo));
        return opCustomerResultBo;
    }

    private void initContext(BatchApplyManualPackContext context, List<Long> customerIdList, int commitUid, OpCustomerResultBo opCustomerResultBo) {
        customerIdList = customerIdList.stream().distinct().collect(Collectors.toList());
        context.setCustomerIdList(customerIdList);
        context.setCommitUid(commitUid);
        context.setResultMap(opCustomerResultBo.getResultMap());
    }

    private void fillMtCustomerTaskMap(List<WmCustomerBasicBo> wmCustomerBasicBoList, Map<Long, List<Long>> customerTaskMap) {
        Map<Long, Long> customerMtCustomerMap = wmCustomerBasicBoList.stream().collect(Collectors.toMap(basicBo -> Long.valueOf(basicBo.getId()), basicBo -> basicBo.getMtCustomerId()));
        Map<Long, List<Long>> mtCustomerTaskMap = Maps.newHashMap();
        for (Long customerId : customerTaskMap.keySet()) {
            Long mtCustomerId = customerMtCustomerMap.get(customerId);
            if (mtCustomerId != null) {
                List<Long> taskIds = customerTaskMap.get(customerId);
                mtCustomerTaskMap.put(mtCustomerId, taskIds);
            }
        }
        customerTaskMap.putAll(mtCustomerTaskMap);
    }


    private List<BatchApplyManualPackResultOutPutDTO> doBatchApply(List<Long> existCustomerIdList, Map<Long, List<Long>>
        customerTaskMap, int commitUid) {
        List<BatchApplyManualPackResultOutPutDTO> batchApplyManualPackResultOutPutDTOList = Lists.newArrayList();
        LOGGER.info("doBatchApply: customerIdList = {}", JSON.toJSONString(existCustomerIdList));
        int commitTaskMaxSize = MccSignConfig.getCommitManualPackTaskMaxSizeWaimai();
        String errMsg = String.format("待发起签约任务数量过多,本次仅提交成功%d个任务", commitTaskMaxSize);

        Set<Long> existTaskSet = customerTaskMap.keySet();
        List<Long> notExistTaskList = Lists.newArrayList(existCustomerIdList);
        notExistTaskList.removeAll(existTaskSet);
        for (Long customerId : notExistTaskList) {
            BatchApplyManualPackResultOutPutDTO batchApplyManualPackResultOutPutDTO = new
                BatchApplyManualPackResultOutPutDTO();
            batchApplyManualPackResultOutPutDTO.setWmCustomerId(customerId);
            batchApplyManualPackResultOutPutDTO.setManualBatchId(0);
            batchApplyManualPackResultOutPutDTO.setCode(CustomerErrorCodeConstants.BUSINESS_ERROR);
            batchApplyManualPackResultOutPutDTO.setResultReason("不存在待发起签约任务");

            batchApplyManualPackResultOutPutDTOList.add(batchApplyManualPackResultOutPutDTO);
        }

        List<Long> applyPackCustomerIdList = Lists.newArrayList(existTaskSet);
        LOGGER.info("doBatchApply: applyPackCustomerIdList = {}", JSON.toJSONString(applyPackCustomerIdList));

        for (Long customerId : applyPackCustomerIdList) {
            List<Long> taskIds = customerTaskMap.get(customerId);
            BatchApplyManualPackResultOutPutDTO batchApplyManualPackResultOutPutDTO = new
                BatchApplyManualPackResultOutPutDTO();
            // 客户打包任务数量校验
            List<Long> realProcessTaskIds = Lists.newArrayList();

            if (commitTaskMaxSize < taskIds.size()) {
                realProcessTaskIds.addAll(taskIds.stream().limit(commitTaskMaxSize).collect(Collectors.toList()));
                batchApplyManualPackResultOutPutDTO.setCode(CustomerErrorCodeConstants.MANUAL_PACK_TASK_ERROR);
                batchApplyManualPackResultOutPutDTO.setResultReason(errMsg);
            } else {
                realProcessTaskIds.addAll(taskIds);
            }

            try {
                batchApplyManualPackResultOutPutDTO.setWmCustomerId(customerId);
                LongResult batchIdResult = applyManualPack(realProcessTaskIds, commitUid, WmSignConstant.OTHER);
                batchApplyManualPackResultOutPutDTO.setManualBatchId(batchIdResult.getValue());

            } catch (WmCustomerException e) {
                LOGGER.error("doBatchApply applyManualPack WmCustomerException异常", e);
                batchApplyManualPackResultOutPutDTO.setCode(CustomerErrorCodeConstants.BUSINESS_ERROR);
                batchApplyManualPackResultOutPutDTO.setResultReason(e.getMsg());
            } catch (TException e) {
                LOGGER.error("doBatchApply applyManualPack TException异常", e);
                batchApplyManualPackResultOutPutDTO.setCode(CustomerErrorCodeConstants.SYSTEM_ERROR);
                batchApplyManualPackResultOutPutDTO.setResultReason("系统异常");
            }
            batchApplyManualPackResultOutPutDTOList.add(batchApplyManualPackResultOutPutDTO);
        }
        return batchApplyManualPackResultOutPutDTOList;

    }


    private void doBatchApply(BatchApplyManualPackContext context, Map<Long, List<Long>> customerTaskMap) {
        int commitUid = context.getCommitUid();
        List<Long> customerIdList = context.getCustomerIdList();
        Map resultMap = context.getResultMap();

        LOGGER.info("doBatchApply: customerIdList = {}", JSON.toJSONString(customerIdList));
        int commitTaskMaxSize = MccSignConfig.getCommitManualPackTaskMaxSize();
        String errMsg = String.format("待发起签约任务数量过多,本次仅提交成功%d个任务", commitTaskMaxSize);

        Set<Long> existTaskSet = customerTaskMap.keySet();
        List<Long> notExistTaskList = Lists.newArrayList(customerIdList);
        notExistTaskList.removeAll(existTaskSet);
        for (Long customerId : notExistTaskList) {
            resultMap.put(customerId, "不存在待发起签约任务");
        }

        customerIdList.retainAll(existTaskSet);
        LOGGER.info("doBatchApply: customerIdList = {}, existTaskCustomerId = {}, notExistTaskCustomerId = {}",
                JSON.toJSONString(customerIdList), JSON.toJSONString(existTaskSet), JSON.toJSONString(notExistTaskList));

        final CountDownLatch countDownLatch = new CountDownLatch(customerIdList.size());
        for (Long customerId : customerIdList) {
            List<Long> taskIds = customerTaskMap.get(customerId);

            // 客户打包任务数量校验
            List<Long> realProcessTaskIds = Lists.newArrayList();
            if (commitTaskMaxSize < taskIds.size()) {
                realProcessTaskIds.addAll(taskIds.stream().limit(commitTaskMaxSize).collect(Collectors.toList()));
                resultMap.put(customerId, errMsg);
            } else {
                realProcessTaskIds.addAll(taskIds);
            }

            // 批量触发打包签约
            executorService.execute(() -> {
                try {
                    applyManualPack(realProcessTaskIds, commitUid, WmSignConstant.OTHER);
                } catch (WmCustomerException e) {
                    resultMap.put(customerId, e.getMsg());
                } catch (TException e) {
                    resultMap.put(customerId, "系统异常");
                } catch (Exception e) {
                    resultMap.put(customerId, "系统异常");
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.warn("多线程等待结果超时异常", e);
        }
    }

    /**
     * 批量确认签约（需在完成用户验证后才可调用）
     *
     * @param batchSet
     */
    public void confirmSign(Set<Long> batchSet) throws WmCustomerException {
        LOGGER.info("#confirmSign, batchSet={}", batchSet);

        //将batid按照pack分组
        List<WmEcontractSignBatchDB> batchDBList = wmEcontractBigBatchParseService.batchQueryByBatchId(Lists.newArrayList(batchSet));
        doConfirmSign(batchDBList);
    }

    private void doConfirmSign(List<WmEcontractSignBatchDB> batchDBList) throws WmCustomerException {
        LOGGER.info("#doConfirmSign, batchDBList={}", batchDBList);

        Map<Long, List<WmEcontractSignBatchDB>> batchGroupMap = batchDBList.stream().collect(Collectors.groupingBy(WmEcontractSignBatchDB::getPackId));
        for (Map.Entry<Long, List<WmEcontractSignBatchDB>> entry : batchGroupMap.entrySet()) {
            if (entry.getKey() != 0) {
                //有packid打包签约
                Long packId = entry.getKey();
                List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.queryBatchListByPackId(packId);
                List<Long> packBatchIdList = signBatchDBList.stream().map(WmEcontractSignBatchDB::getId).collect(Collectors.toList());
                confirmSignByBatch(packBatchIdList);
            } else {
                //无packid，普通签约
                List<Long> batchIdList = entry.getValue().stream().map(WmEcontractSignBatchDB::getId).collect(Collectors.toList());
                confirmSignByBatch(batchIdList);
            }
        }
    }

    /**
     * 批量确认签约（需在完成用户验证后才可调用）
     *
     * @param confirmSignParams
     */
    public void confirmSignPermissionCheck(List<ConfirmSignParam> confirmSignParams) throws WmCustomerException {
        LOGGER.info("confirmSignPermissionCheck, confirmSignParams={}", JSON.toJSONString(confirmSignParams));
        if (CollectionUtils.isEmpty(confirmSignParams)) {
            LOGGER.info("confirmSignPermissionCheck confirmSignParams入参为空");
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "confirmSignParams入参为空");
        }

        // 提取batchId
        List<Long> batchIdList = confirmSignParams.stream().map(ConfirmSignParam::getBatchId).collect(Collectors.toList());
        List<Long> wmPoiIdList = confirmSignParams.stream().map(ConfirmSignParam::getWmPoiId).collect(Collectors.toList());

        // 批量查询
        List<WmEcontractSignBatchDB> batchDBList = wmEcontractBigBatchParseService.batchQueryByBatchId(batchIdList);
        List<WmCustomerPoiDB> poiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByWmPoiIdList(wmPoiIdList);

        // 进行校验
        if (!checkAuthForConfirmSign(confirmSignParams, batchDBList, poiDBList)) {
            if (ConfigUtilAdapter.getBoolean("interface_permission_isolation_switch", false)) {
                LOGGER.info("confirmSignPermissionCheck 权限校验失败,流程终止");
                throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "权限校验失败");
            } else {
                LOGGER.error("confirmSignPermissionCheck 权限校验失败,流程继续");
            }
        } else {
            LOGGER.info("confirmSignPermissionCheck 权限校验通过");
        }

        // 校验完成后，执行确认签约
        doConfirmSign(batchDBList);
    }

    private boolean checkAuthForConfirmSign(List<ConfirmSignParam> confirmSignParams, List<WmEcontractSignBatchDB> batchDBList, List<WmCustomerPoiDB> poiDBList) {
        // 提取batchId->ConfirmSignParam
        Map<Long, ConfirmSignParam> paramMap = confirmSignParams.stream().collect(Collectors.toMap(ConfirmSignParam::getBatchId, Function.identity(), (key1, key2) -> key2));

        // 校验customerID和batchID
        for (WmEcontractSignBatchDB batchDB : batchDBList) {
            // 表里的customerId和入参的customerId不一致
            if (paramMap.get(batchDB.getId()) == null) {
                LOGGER.info("checkAuthForConfirmSign 客户id与合同id不一致, batchid = {}为空", batchDB.getId());
                return false;
            }

            if (paramMap.get(batchDB.getId()).getCustomerId() == null || !paramMap.get(batchDB.getId()).getCustomerId().equals(batchDB.getCustomerId())) {
                LOGGER.info("checkAuthForConfirmSign 客户id与合同id不一致, (map)customerid = {}, (batchDB)customerid = {}",
                    paramMap.get(batchDB.getId()).getCustomerId(), batchDB.getCustomerId());
                return false;
            }
        }

        // 提取customerId->wmPoiIdList
        Map<Integer, List<Long>> wmPoiIdMap = Maps.newHashMap();
        for (ConfirmSignParam param : confirmSignParams) {
            List<Long> wmPoiIDList = wmPoiIdMap.getOrDefault(param.getCustomerId(), Lists.newArrayList());
            wmPoiIDList.add(param.getWmPoiId());
            wmPoiIdMap.put(param.getCustomerId(), wmPoiIDList);
        }

        // 校验customerId和门店Id
        for (WmCustomerPoiDB poiDB : poiDBList) {
            if (wmPoiIdMap.get(poiDB.getCustomerId()) == null) {
                LOGGER.info("checkAuthForConfirmSign 客户id与门店id不一致, customerid = {}为空", poiDB.getCustomerId());
                return false;
            }

            if (!wmPoiIdMap.get(poiDB.getCustomerId()).contains(poiDB.getWmPoiId())) {
                LOGGER.info("checkAuthForConfirmSign 客户id与门店id不一致, customerid = {}, wmpoiid = {}", poiDB.getCustomerId(), poiDB.getWmPoiId());
                return false;
            }
        }

        return true;
    }


    public void confirmSignByBatch(List<Long> batchList) throws WmCustomerException {
        LOGGER.info("#confirmSignByBatch,batchSet={}", batchList);
        boolean hasException = false;
        for (Long batchId : batchList) {
            String recordKey = wmEcontractBatchBizService.queryRecordKeyByBatchId(batchId);
            if (StringUtils.isEmpty(recordKey)) {
                continue;
            }
            try {
                econtractBizService.confirmSignEContractV2(recordKey);
            } catch (EcontractException e) {
                LOGGER.warn("confirmSignEContractV2 exception,recordKey={}", recordKey, e);
                hasException = true;
            } catch (TException e) {
                LOGGER.warn("confirmSignEContractV2 exception,recordKey={}", recordKey, e);
            }
        }
        if (hasException) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "系统异常,请稍后重试!");
        }
    }

    public String queryRecordIdByBatchId(long batchId) {
        return wmEcontractBatchBizService.queryRecordKeyByBatchId(batchId);
    }

    public Map<Long, String> getSingleTypeAndTaskId(long batchId) {
        Map<Long, String> result = Maps.newHashMap();
        List<EcontractTaskBo> taskBos = getTaskInfoByBatchId(batchId);
        if (taskBos.size() == 1) {
            result.put(taskBos.get(0).getId(), taskBos.get(0).getApplyType());
        }
        return result;
    }

    public BooleanResult syncUpstreamStatus(Long taskId, Integer upstreamStatus) {
        LOGGER.info("syncUpstreamStatus taskId={}, upstreamStatus:{}", taskId, upstreamStatus);
        // 更新对应task任务的上游状态字段
        wmEcontractTaskBizService.updateUpstreamStatusByTaskId(taskId, upstreamStatus);
        // 获取对应batchId
        Long batchId = wmEcontractTaskBizService.getBatchIdById(taskId);
        // 触发batch信息更新
        wmEcontractBatchBizService.checkAndSyncToEcontract(batchId);
        return new BooleanResult(true);
    }

    public Integer queryAllTaskUpstreamStatus(String recordKey) {
        LOGGER.info("queryAllTaskUpstreamStatus recordKey={}", recordKey);
        WmEcontractSignBatchDB batchDB = wmEcontractBatchBizService.queryByRecordKey(recordKey);
        UpstreamStatusEnum statusEnum = wmEcontractBatchBizService.queryAllTaskUpstreamStatusByBtachInfo(batchDB);
        return statusEnum.getCode();
    }

    public boolean isSignSuccessPushByMQ(Long taskId) {
        return taskId % 10000 < MccConfig.signSuccessPushByMQTaskGrayPercent();
    }

    public boolean isPackSign(Long batchId) {
        WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
        if (wmEcontractSignBatchDB != null && wmEcontractSignBatchDB.getPackId() > 0) {
            return true;
        }
        return false;
    }

    public Map<Long, Boolean> hasInProcessingCustomerUnBindWmPoiTask(CustomerUnBindWmPoiTaskSearchParam param) throws WmCustomerException {
        AssertUtil.assertObjectNotNull(param);
        AssertUtil.assertIntegerMoreThan0(param.getWmCustomerId());
        AssertUtil.assertCollectionNotEmpty(param.getWmPoiId());
        List<WmEcontractSignTaskDB> wmEcontractSignTaskDBS = wmEcontractTaskBizService.querySignTaskListForCustomerUnbindWmPoi(param.getWmCustomerId());
        Set<Long> taskWmPoiIdSet = Sets.newHashSet();
        for(WmEcontractSignTaskDB temp : wmEcontractSignTaskDBS){
            String applyContext = temp.getApplyContext();
            EcontractCancelAuthInfoBo econtractCancelAuthInfoBo = JSONObject.parseObject(applyContext, EcontractCancelAuthInfoBo.class);
            if(econtractCancelAuthInfoBo == null){
                continue;
            }
            taskWmPoiIdSet.addAll(econtractCancelAuthInfoBo.getWmPoiIdList());
        }
        Map<Long, Boolean> result = Maps.newHashMap();
        for (Long wmPoiId : param.getWmPoiId()) {
            result.put(wmPoiId, taskWmPoiIdSet.contains(wmPoiId));
        }
        return result;
    }

    /**
     * 账号任务信息
     * @param querySignTaskReq 请求
     * @return 返回
     */
    public AccountSignTaskResp queryAccountSignTaskInfo(QuerySignTaskReq querySignTaskReq) {
        // check the params
        if (isValidAccount(querySignTaskReq)
            && CollectionUtils.isEmpty(querySignTaskReq.getCustomerIdList())) {
            return BaseResponseUtil.fail(StatusCodeEnum.REQUEST_PARAM_ERROR, AccountSignTaskResp.class);
        }
        SignTaskInfoDTO signTaskInfoDTO = SignTaskInfoDTO.builder().hasSignTask(false).build();
        // 转换为customerId 信息, 同时过滤品牌id
        // if brand id is not empty try to filter the customer id with that, query customerId via wmPoiId List
        Set<Integer> taskProcessingCustomerIdSet = this.getCustomerIdSet(querySignTaskReq);
        // 计算灰度
        if (!isHitGray(taskProcessingCustomerIdSet)) {
            log.info("resultSignTaskInfoDTO = {}", JSON.toJSONString(signTaskInfoDTO));
            return BaseResponseUtil.success(signTaskInfoDTO, AccountSignTaskResp.class);
        }

        MetricHelper.build()
            .name(MultiCustomerSign.MULTI_CUSTOMER_SIGN_PREFIX)
            .tag(MultiCustomerSign.CUSTOMER_COUNT, "账户客户数")
            .count(taskProcessingCustomerIdSet.size());
        // filter not valid account

        try {
            List<WmCustomerDB> wmCustomerList = wmCustomerService.selectCustomerByIds(taskProcessingCustomerIdSet);
            log.info("当前用户信息:{}", JSON.toJSONString(wmCustomerList));
            if (CollectionUtils.isNotEmpty(wmCustomerList)) {
                // 对未生效客户，不做露出
                taskProcessingCustomerIdSet = wmCustomerList.stream()
                    .filter(Objects::nonNull)
                    .filter(m -> m.getEffective() == CustomerConstants.EFFECT)
                    .map(WmCustomerDB::getId)
                    .collect(Collectors.toSet());
            }
        } catch (WmCustomerException e) {
            log.error("查询客户信息异常:", e);
        }

        log.info("taskProcessingCustomerIdSet = 「{}」", JSON.toJSONString(taskProcessingCustomerIdSet));
        if (CollectionUtils.isEmpty(taskProcessingCustomerIdSet)) {
            return BaseResponseUtil.success(signTaskInfoDTO, AccountSignTaskResp.class);
        }
        Set<String> applyContractTypeList = MccConfig.getMultiCustomerSignContractType();
        log.info("筛选的合同类别：{}", JSON.toJSONString(applyContractTypeList));

        // 获取in_progressing状态
        Map<Integer, WmCustomerKp> customerKpMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(taskProcessingCustomerIdSet)) {
            customerKpMap =
                org.apache.commons.collections4.CollectionUtils.emptyIfNull(wmCustomerKpService.batchGetCustomerKpOfEffectiveSigner(
                        Lists.newArrayList(taskProcessingCustomerIdSet)))
                    .stream()
                    .collect(Collectors.toMap(WmCustomerKp::getCustomerId, Function.identity(), (k1, k2) -> k1));
        }
        for (Integer customerId : taskProcessingCustomerIdSet) {
            WmCustomerKp wmCustomerKp = customerKpMap.get(customerId);
            if (Objects.isNull(wmCustomerKp) || StringUtils.isBlank(wmCustomerKp.getPhoneNum())) {
                log.warn("当前客户id的KP信息无效：customerId={},wmCustomerKp ={}", customerId, JSON.toJSONString(wmCustomerKp));
                continue;
            }

            // 签约中或待发起签约
            if (hasProcessingTask(customerId, wmCustomerKp) || hasToSignManualTaskDraft(customerId)) {
                signTaskInfoDTO.setHasSignTask(Boolean.TRUE);
                return BaseResponseUtil.success(signTaskInfoDTO, AccountSignTaskResp.class);
            }
        }
        log.info("resultSignTaskInfoDTO = {}", JSON.toJSONString(signTaskInfoDTO));
        return BaseResponseUtil.success(signTaskInfoDTO, AccountSignTaskResp.class);
    }

    /**
     * * 计算灰度
     *
     * @param taskProcessingCustomerIdSet 客户id
     * @return 返回
     */

    private boolean isHitGray(Set<Integer> taskProcessingCustomerIdSet) {
        //是否总开关
        boolean isSwitch = MccConfig.isQueryAccountSignTaskOpen();
        //是否灰度客户
        boolean isGrayCustomerId = false;
        for (Integer customerId : taskProcessingCustomerIdSet) {
            if (MccConfig.customerGrayWhiteList().contains(customerId)) {
                isGrayCustomerId = true;
                break;
            }
            isGrayCustomerId = (customerId % 100 < MccConfig.isQueryAccountSignTaskOpenPercent());
            if (isGrayCustomerId) {
                break;
            }
        }
        return isSwitch && isGrayCustomerId;
    }

    /***
     *  待发起签约任务
     * @param customerId 客户id
     * @return 返回是否
     */
    private boolean hasToSignManualTaskDraft(Integer customerId) {

        List<WmEcontractSignManualTaskBo> wmEcontractSignManualTaskBoList = wmEcontractSignBzService.getManualTaskInfoByCustomerId(
            customerId);
        log.info("待发起签约任务信息:{}", JSON.toJSONString(wmEcontractSignManualTaskBoList));
        // check the poi status: online or ready, otherwise just ignore
        if (CollectionUtils.isNotEmpty(wmEcontractSignManualTaskBoList)) {
            List<WmPoiAggre> resultPoiIdList = filterCustomerByPoiStatus(wmEcontractSignManualTaskBoList);
            // 待发起签约，结果返回
            return CollectionUtils.isNotEmpty(resultPoiIdList);
        }
        return false;
    }

    private boolean hasProcessingTask(Integer customerId, WmCustomerKp wmCustomerKp) {

        ToSignCustomerTaskReq toSignCustomerTaskReq = ToSignCustomerTaskReq.builder()
            .customerId(customerId)
            .taskType(0)
            .kpPhoneNum(wmCustomerKp.getPhoneNum())
            .build();
        ToSignTaskQueryResp resp = wmEcontractToSignService.queryToSignTaskRecordKeysViaCustomer(toSignCustomerTaskReq);
        log.info("queryAccountSignTaskInfo： 客户id：customerId={},resp ={}", customerId, JSON.toJSONString(resp));
        // 签约中，结果返回
        return CollectionUtils.isNotEmpty(resp.getData());
    }


    private boolean isValidAccount(QuerySignTaskReq querySignTaskReq) {
        return Objects.isNull(querySignTaskReq.getAccountId()) || querySignTaskReq.getAccountId() == 0;
    }


    /**
     * 转换为customerId 信息, 同时过滤品牌id
     *
     * @param querySignTaskReq 请求参数。 如有customerList参数优先于accountId  with brandId filtering
     * @return customerId sets
     */
    private Set<Integer> getCustomerIdSet(QuerySignTaskReq querySignTaskReq) {
        // customerId List优先于accountId
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(querySignTaskReq.getCustomerIdList())) {
            // customerList参数优先于accountId
            return Sets.newHashSet(querySignTaskReq.getCustomerIdList());
        } else {
            QueryAccountCustomerBO queryAccountCustomerBO = QueryAccountCustomerBO.builder()
                .accountId(querySignTaskReq.getAccountId())
                .brandId(querySignTaskReq.getBrandId())
                .build();
            return getCustomerListViaAccountId(queryAccountCustomerBO);
        }
    }

    /**
     * 转换为customerId 信息, 同时过滤品牌id
     *
     * @param queryAccountCustomerBO 账号id，品牌id,过滤门店状态
     * @return 返回客户set. 客户列表，闪购账号任务信息，均有用到
     */
    private Set<Integer> getCustomerListViaAccountId(QueryAccountCustomerBO queryAccountCustomerBO) {
        log.info("通过账号查客户:{}", JSON.toJSONString(queryAccountCustomerBO));
        QueryAccountPoiBO queryAccountPoiBO = QueryAccountPoiBO.builder()
            .acctId(queryAccountCustomerBO.getAccountId())
            .build();
        List<Long> wmPoiIdList = bizUserServiceAdapter.queryAccountCustomerList(queryAccountPoiBO);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Sets.newHashSet();
        }
        MetricHelper.build()
            .name(MultiCustomerSign.MULTI_CUSTOMER_SIGN_PREFIX)
            .tag(MultiCustomerSign.POI_COUNT, "账号门店数")
            .count(wmPoiIdList.size());
        List<WmPoiAggre> wmPoiAggList = new ArrayList<>();
        if (MccConfig.queryPoiQueryAgreeSwitch()) {
            wmPoiAggList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList,
                CommonConst.WM_POI_FIELDS_AGG_BASE);
        } else {
            wmPoiAggList = wmPoiQueryAdapter.queryPoiAggre(wmPoiIdList,
                CommonConst.WM_POI_FIELDS_AGG_BASE);
        }
        if (CollectionUtils.isEmpty(wmPoiAggList)) {
            return Sets.newHashSet();
        }
        // ref : WM_POI_FIELD_VALID : // 门店流程状态(上单状态): 0、下线；1、上线；2、上单中；3、审核通过可上线)
        if (Objects.nonNull(queryAccountCustomerBO.getBrandId()) && queryAccountCustomerBO.getBrandId() > 0) {
            wmPoiAggList = wmPoiAggList.stream()
                .filter(m -> m.getBrand_id() == queryAccountCustomerBO.getBrandId())
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(wmPoiAggList)) {
            return Sets.newHashSet();
        }
        return wmPoiAggList.stream()
            .map(WmPoiAggre::getCustomer_id)
            .filter(customerId -> customerId != 0)
            .map(Long::intValue)
            .collect(Collectors.toSet());
    }

    public TaskCustomerPageDTO taskCustomerList(SignTaskCustomerReq signTaskCustomerReq)
        throws TException, WmCustomerException {

        // check params
        if (Objects.isNull(signTaskCustomerReq.getAccountId()) || signTaskCustomerReq.getAccountId() == 0) {
            return BaseResponseUtil.fail(StatusCodeEnum.REQUEST_PARAM_ERROR, TaskCustomerPageDTO.class);
        }

        formatPageInfo(signTaskCustomerReq);

        QueryAccountCustomerBO queryProcessingTaskCustomerBO = QueryAccountCustomerBO.builder()
            .accountId(signTaskCustomerReq.getAccountId())
            .brandId(signTaskCustomerReq.getBrandId())
            .build();
        Set<Integer> processingTaskCustomerSet = getCustomerListViaAccountId(queryProcessingTaskCustomerBO);
        log.info("processingTaskCustomerSet = {}", JSON.toJSONString(processingTaskCustomerSet));

        // sign TAB or signed TAB and paging
        Set<Integer> finalCustomerSet = Sets.newHashSet();
        if (signTaskCustomerReq.getQueryTabType() == QueryTypeEnum.QUERY_TO_SIGN.getCode()) {
            finalCustomerSet = buildProcessingTaskCustomerList(processingTaskCustomerSet);
        } else if (signTaskCustomerReq.getQueryTabType() == QueryTypeEnum.QUERY_SIGNED.getCode()) {
            finalCustomerSet = buildSignedContractCustomerList(processingTaskCustomerSet);
        }

        List<SignTaskCustomerDTO> signTaskCustomerDTOList = Lists.newArrayList();
        TaskCustomerPageDTO taskCustomerPageDTO = new TaskCustomerPageDTO();
        if (CollectionUtils.isEmpty(finalCustomerSet)) {
            PageInfoDTO pageInfoDTO = PageInfoDTO.builder()
                .totalCount(0L)
                .pageNo(signTaskCustomerReq.getPageNo())
                .pageSize(signTaskCustomerReq.getPageSize())
                .build();
            taskCustomerPageDTO = TaskCustomerPageDTO.builder()
                .signTaskCustomerDTOList(signTaskCustomerDTOList)
                .pageInfoDTO(pageInfoDTO)
                .build();

            return taskCustomerPageDTO;
        }
        taskCustomerPageDTO = pagingAndFillData(signTaskCustomerReq, finalCustomerSet);
        log.info("signTaskCustomerDTOList = {}", JSON.toJSONString(taskCustomerPageDTO));
        return taskCustomerPageDTO;
    }

    private Set<Integer> buildSignedContractCustomerList(Set<Integer> processingTaskCustomerSet) {
        Set<Integer> finalCustomerSet;
        finalCustomerSet = processingTaskCustomerSet.stream().filter(customerId -> customerId != 0).filter(customerId ->
            {
                boolean tagCustomer = false;
                for (ContractTypeBO m : MccConfig.getSignedContractTypeList()) {
                    QuerySignedContractBO querySignedContractBO = QuerySignedContractBO.builder()
                        .customerId(customerId)
                        .type(m.getType())
                        .pageNo(NumberUtils.INTEGER_ONE)
                        .pageSize(NumberUtils.INTEGER_ONE)
                        .opUid(NumberUtils.INTEGER_ZERO)
                        .opName(StringUtils.EMPTY)
                        .build();
                    try {
                        ContractVersionPageData contractVersionPageData = contractVersionService.getEffectiveContractVersions(
                            querySignedContractBO);
                        log.info("contractVersionPageData = {}", JSON.toJSONString(contractVersionPageData));
                        if (Objects.nonNull(contractVersionPageData)
                            && Objects.nonNull(contractVersionPageData.getTotal())
                            && contractVersionPageData.getTotal() > 0) {
                            tagCustomer = true;
                            break;
                        }
                    } catch (WmCustomerException e) {
                        log.error("客户已签约数据异常", e);
                    } catch (TException e) {
                        log.error("客户已签约数据异常", e);
                    }
                }
                return tagCustomer;
            }
        ).collect(Collectors.toSet());
        return finalCustomerSet;
    }

    private Set<Integer> buildProcessingTaskCustomerList(Set<Integer> processingTaskCustomerSet) {
        Set<Integer> finalCustomerSet = Sets.newHashSet();
        List<String> allConfigContractCode = wmFrameContractConfigService.allConfigFrameContract()
                .stream()
                .filter(configInfo -> configInfo.getSourceAuthInfo().getCanDisplayInSingleView())
                .map(ContractConfigInfo::getContractCode)
                .collect(Collectors.toList());
        for (Integer customerId : processingTaskCustomerSet) {
            WmCustomerKp wmCustomerKp = null;
            try {
                wmCustomerKp = wmCustomerKpService.getEffectSignerKp(customerId);
            } catch (WmCustomerException e) {
                log.warn("当前用户异常:", e);
            }
            if (Objects.isNull(wmCustomerKp) || StringUtils.isBlank(wmCustomerKp.getPhoneNum())) {
                log.warn("客户id的KP信息无效：customerId={},wmCustomerKp ={}", customerId, JSON.toJSONString(wmCustomerKp));
                continue;
            }
            ToSignCustomerTaskReq toSignCustomerTaskReq = ToSignCustomerTaskReq.builder()
                .customerId(customerId)
                .taskType(NumberUtils.INTEGER_ZERO)
                .kpPhoneNum(wmCustomerKp.getPhoneNum())
                .build();
            log.info("客户id：customerId={},签约中任务req ={}", customerId, JSON.toJSONString(toSignCustomerTaskReq));
            Integer batchTaskCount = wmEcontractToSignService.queryToSignTaskCountViaCustomer(toSignCustomerTaskReq, allConfigContractCode);
            log.info("客户id：customerId={},签约中任务batchTaskCount ={}", customerId, batchTaskCount);
            if (Objects.nonNull(batchTaskCount) && batchTaskCount > 0) {
                finalCustomerSet.add(customerId);
                continue;
            }

            // 待发起签约
            List<WmEcontractSignManualTaskBo> wmEcontractSignManualTaskBoList =
                wmEcontractSignBzService.getManualTaskInfoByCustomerId(
                    customerId);
            log.info("客户id：customerId={}，待发起签约任务信息:{}", customerId, JSON.toJSONString(wmEcontractSignManualTaskBoList));
            // check the poi status: online or ready, otherwise just ignore
            if (CollectionUtils.isNotEmpty(wmEcontractSignManualTaskBoList)) {
                List<WmPoiAggre> resultPoiIdList = filterCustomerByPoiStatus(wmEcontractSignManualTaskBoList);
                // 待发起签约，结果返回
                if (CollectionUtils.isNotEmpty(resultPoiIdList)) {
                    finalCustomerSet.add(customerId);
                }
            }
        }
        return finalCustomerSet;
    }

    private void formatPageInfo(SignTaskCustomerReq signTaskCustomerReq) {
        if (Objects.isNull(signTaskCustomerReq.getPageNo()) || signTaskCustomerReq.getPageNo() == 0) {
            signTaskCustomerReq.setPageNo(NumberUtils.INTEGER_ONE);
        }
        if (Objects.isNull(signTaskCustomerReq.getPageSize()) || signTaskCustomerReq.getPageSize() == 0) {
            signTaskCustomerReq.setPageNo(CommonConst.PAGE_SIZE);
        }
    }

    /**
     * 过滤门店状态： 上单中，上单审批通过
     *
     * @param wmEcontractSignManualTaskBoList 当前任务
     * @return 返回
     */
    private List<WmPoiAggre> filterCustomerByPoiStatus(
        List<WmEcontractSignManualTaskBo> wmEcontractSignManualTaskBoList) {
        List<Long> poiIdList = wmEcontractSignManualTaskBoList.stream()
            .map(WmEcontractSignManualTaskBo::getWmPoiId)
            .collect(Collectors.toList());
        return CollectionUtils.emptyIfNull(
            wmPoiQueryAdapter.getWmPoiAggre(poiIdList, CommonConst.WM_POI_FIELDS_AGG_BASE)).stream().filter(m ->
            Lists.newArrayList(WmPoiValidEnum.CREATING.getValue(),
                WmPoiValidEnum.READY.getValue()).contains(m.getValid())).collect(Collectors.toList());

    }

    /**
     * 分页并对返回数据进行补充
     *
     * @param signTaskCustomerReq 请求
     * @param finalCustomerSet    用户信息set
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private TaskCustomerPageDTO pagingAndFillData(SignTaskCustomerReq signTaskCustomerReq,
            Set<Integer> finalCustomerSet) throws TException, WmCustomerException {
        List<SignTaskCustomerDTO> signTaskCustomerDTOList = new ArrayList<>();

        // build the result with extended , name/poiId list ect.
        List<List<Integer>> partitionedCustomerList = Lists.partition(Lists.newArrayList(finalCustomerSet),
                CommonConst.QUERY_PAGE_SIZE);
        Map<Integer, List<Long>> customerPoiListMap = new HashMap<>();
        Map<Integer, String> customerNameMap = new HashMap<>();
        partitionedCustomerList.forEach(resultCustomerList -> {
            try {
                customerPoiListMap.putAll(WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerIdList(resultCustomerList));

                customerNameMap.putAll(CollectionUtils
                        .emptyIfNull(wmCustomerService.selectCustomerByIds(finalCustomerSet)).stream()
                        .collect(Collectors.toMap(WmCustomerDB::getId, WmCustomerDB::getCustomerName, (k1, k2) -> k1)));
            } catch (Exception ex) {
                log.error("补充用户信息异常:", ex);
            }
        });

        signTaskCustomerDTOList = finalCustomerSet.stream()
                .map(m -> SignTaskCustomerDTO.builder().customerId(m)
                        .customerName(customerNameMap.getOrDefault(m, StringUtils.EMPTY))
                        .wmPoiIdList(customerPoiListMap.getOrDefault(m, Collections.emptyList())).build())
                .filter(n -> {
                    if (StringUtils.isBlank(signTaskCustomerReq.getKeyword())) {
                        return true;
                    }
                    return n.getCustomerName().contains(signTaskCustomerReq.getKeyword());

                }).collect(Collectors.toList());

        try {
            // poi count sorted in reversed sorting
            signTaskCustomerDTOList
                    .sort((o1, o2) -> Integer.compare(o2.getWmPoiIdList().size(), o1.getWmPoiIdList().size()));
        } catch (Exception ex) {
            log.error("客户门店数排序异常", ex);
        }

        // 执行分页
        List<SignTaskCustomerDTO> resultCustomerList = PageUtil.paging(signTaskCustomerDTOList,
                signTaskCustomerReq.getPageNo(), signTaskCustomerReq.getPageSize());
        // erase poi id for not used ： 裁剪返回数据，优化前端加载时间
        resultCustomerList.forEach(m -> {
            MetricHelper.build().name(MultiCustomerSign.MULTI_CUSTOMER_SIGN_PREFIX)
                    .tag(MultiCustomerSign.CUSTOMER_POI_COUNT, "客户门店数" + m.getCustomerId())
                    .count(m.getWmPoiIdList().size());
            m.setWmPoiIdList(Lists.newArrayList());
        });

        PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount((long) finalCustomerSet.size())
                .pageNo(signTaskCustomerReq.getPageNo()).pageSize(signTaskCustomerReq.getPageSize()).build();
        TaskCustomerPageDTO taskCustomerPageDTO = TaskCustomerPageDTO.builder()
                .signTaskCustomerDTOList(resultCustomerList).pageInfoDTO(pageInfoDTO).build();
        log.info("taskCustomerPageDTO ={}", JSON.toJSONString(taskCustomerPageDTO));
        return taskCustomerPageDTO;
    }

    public List<Long> queryWmPoiIdListByTaskId(Long taskId) throws TException, WmCustomerException {
        return wmEcontractTaskApplyService.queryWmPoiIdListByTaskId(taskId);
    }

    public String getSmsShortLink(SmsShortLinkQueryParam queryParam) throws WmCustomerException {
        log.info("WmEcontractSignBzService#getSmsShortLink, queryParam: {}", JSON.toJSONString(queryParam));
        try {
            String smsShortLink = wmEcontractTaskApplyService.getSmsShortLink(queryParam);

            log.info("WmEcontractSignBzService#getSmsShortLink, smsShortLink: {}", smsShortLink);
            return smsShortLink;
        } catch (Exception e) {
            log.error("WmEcontractSignBzService#getSmsShortLink, 异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"获取签约链接异常");
        }
    }

    public BooleanResult checkCanCancelSign(CancelSignParam cancelSignParam) {
        log.info("WmEcontractSignBzService#checkCanCancelSign, cancelSignParam: {}", JSON.toJSONString(cancelSignParam));
        BooleanResult result;
        try {
            result = wmEcontractTaskApplyService.checkCanCancelSign(cancelSignParam);
        } catch (Exception e) {
            log.warn("WmEcontractSignBzService#checkCanCancelSign", e);
            result =  new BooleanResult(true);
        }
        log.info("WmEcontractSignBzService#checkCanCancelSign, result: {}", JSON.toJSONString(result));
        return result;
    }

    public Boolean isDcSignTaskByBatchId(Integer batchId) {
        if (batchId == null || batchId <= 0) {
            return false;
        }
        List<String> applyType = wmEcontractBatchBaseService.getApplyTypeListByBatchId(Long.valueOf(batchId));
        log.info("isDcSignTaskByBatchId applyType: {}", JSON.toJSON(applyType));
        return isContainsDcTaskByApplyType(applyType);
    }

    /**
     * 判断是否为到餐签约任务
     * @param param
     * @return
     */
    public Boolean isDcSignTask(IsDcSignTaskQueryParam param) {
        String idType = param.getIdType();
        Integer id = param.getId();
        if (BatchTaskConstant.BATCH_TYPE.equals(idType)) {
            return isDcSignTaskByBatchId(id);
        } else if (BatchTaskConstant.TASK_TYPE.equals(idType)) {
            return isDcSignTaskByTaskId(Long.valueOf(id));
        } else if (BatchTaskConstant.PACK_TYPE.equals(idType)) {
            return isDcSignTaskByPackId(id);
        } else if (BatchTaskConstant.MANUAL_TASK_TYPE.equals(idType)) {
            return isDcSignTaskByManualTaskId(id);
        }

        log.info("isDcSignTask 未知的签约任务类型, idType:{}", idType);
        return false;
    }

    private Boolean isDcSignTaskByManualTaskId(Integer manualTaskId) {
        if (manualTaskId == null || manualTaskId <= 0) {
            return false;
        }

        String moudle = wmEcontractManualTaskApplyService.getMoudleById(manualTaskId);
        if (!StringUtils.isBlank(moudle)) {

            return isDcTaskByApplyType(moudle);
        }
        return false;
    }


    private Boolean isDcSignTaskByPackId(Integer packId) {
        if (packId == null || packId <= 0) {
            return false;
        }

        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = wmEcontractBigBatchParseService.querySignBatchListByPackId(Long.valueOf(packId));
        if (CollectionUtils.isNotEmpty(wmEcontractSignBatchDBList)) {
            WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractSignBatchDBList.get(0);
            Long batchId = wmEcontractSignBatchDB.getId();

            return isDcSignTaskByBatchId(batchId.intValue());
        }

        return false;
    }

    private boolean isContainsDcTaskByApplyType(List<String> applyType) {
        if (CollectionUtils.isEmpty(applyType)) {
            return false;
        }

        for (String type : applyType) {
            if (isDcTaskByApplyType(type)) {
                return true;
            }
        }

        return false;
    }

    public boolean isDcSignTaskByTaskId(Long taskId) {
        EcontractTaskBo econtractTaskBo = wmEcontractTaskService.getById(taskId);
        String applyType = econtractTaskBo.getApplyType();
        if (Objects.nonNull(applyType)) {

            return isDcTaskByApplyType(applyType);
        }
        return false;
    }

    private boolean isDcTaskByApplyType(String applyType) {
        return EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getName().equals(applyType)
                || EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getName().equals(applyType);
    }

    public Long queryDcMtCustomerId(QueryDcMtCustomerIdParam param) throws WmCustomerException {
        String idType = param.getIdType();
        Integer id = param.getId();

        if (BatchTaskConstant.BATCH_TYPE.equals(idType)) {
            return getDcMtCustomerIdByBatchId(id);
        } else if (BatchTaskConstant.TASK_TYPE.equals(idType)) {
            return getDcMtCustomerIdByTaskId(id);
        } else if (BatchTaskConstant.PACK_TYPE.equals(idType)) {
            return getDcMtCustomerIdByPackId(id);
        } else if (BatchTaskConstant.MANUAL_TASK_TYPE.equals(idType)) {
            return getDcMtCustomerIdByManualTaskId(id);
        }

        log.info("queryDcMtCustomerId 未知的签约任务类型, idType:{}", idType);
        throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "未知的签约任务类型");
    }

    /**
     * 通过manualTaskId获取到餐的平台客户id
     * @param manualTaskId
     * @return
     */
    private Long getDcMtCustomerIdByManualTaskId(Integer manualTaskId) {
        if (manualTaskId == null || manualTaskId <= 0) {
            return null;
        }

        try {
            WmEcontractSignManualTaskDB taskDB = wmEcontractManualTaskApplyService.getManualTaskByManualTaskId(Long.valueOf(manualTaskId));
            if (Objects.isNull(taskDB)){
                return null;
            }

            String applyContext = taskDB.getApplyContext();
            ManualTaskSettleContextBo manualTaskSettleContextBo = JSON.parseObject(applyContext, ManualTaskSettleContextBo.class);
            DaoCanContractContext daoCanContractContext = manualTaskSettleContextBo.getDaoCanContractContext();

            log.info("getDcMtCustomerIdByManualTaskId daoCanContractContext:{}",JSON.toJSON(daoCanContractContext));
            return daoCanContractContext.getMtCustomerId();
        } catch (Exception e) {
            log.error("getDcMtCustomerIdByManualTaskId error, manualTaskId:{}", manualTaskId);
        }

        return null;
    }

    /**
     * 通过packId获取到餐的平台客户id
     * @param packId
     * @return
     */
    private Long getDcMtCustomerIdByPackId(Integer packId) {
        if (packId == null || packId <= 0) {
            return null;
        }

        try {
            List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = wmEcontractBigBatchParseService.querySignBatchListByPackId(Long.valueOf(packId));
            if (CollectionUtils.isNotEmpty(wmEcontractSignBatchDBList)) {
                WmEcontractSignBatchDB batchDB = wmEcontractSignBatchDBList.get(0);
                EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
                DaoCanContractContext daoCanContractContext = econtractBatchContextBo.getDaoCanContractContext();

                log.info("getDcMtCustomerIdByBatchId daoCanContractContext:{}", JSON.toJSON(daoCanContractContext));
                return daoCanContractContext.getMtCustomerId();
            }
        } catch (Exception e) {
            log.error("getDcMtCustomerIdByBatchId 解析batchContext异常, packId:{}", packId, e);
        }

        return null;
    }

    /**
     * 通过taskId获取到餐的平台客户id
     * @param taskId
     * @return
     */
    private Long getDcMtCustomerIdByTaskId(Integer taskId) {
        EcontractTaskBo taskBo = wmEcontractTaskService.getById(Long.valueOf(taskId));
        if (Objects.isNull(taskBo)) {
            return null;
        }

        Long batchId = taskBo.getBatchId();
        log.info("getDcMtCustomerIdByTaskId batchId:{}",batchId);
        return getDcMtCustomerIdByBatchId(batchId.intValue());
    }

    /**
     * 通过batchId获取到餐的平台客户id
     * @param batchId
     * @return
     */
    private Long getDcMtCustomerIdByBatchId(Integer batchId) {
        if (batchId == null || batchId <= 0) {
            return null;
        }

        WmEcontractSignBatchDB batchDB = wmEcontractBatchBaseService.queryByBatchId(Long.valueOf(batchId));
        if (Objects.isNull(batchDB)) {
            return null;
        }

        try {
            EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
            DaoCanContractContext daoCanContractContext = econtractBatchContextBo.getDaoCanContractContext();

            log.info("getDcMtCustomerIdByBatchId daoCanContractContext:{}", JSON.toJSON(daoCanContractContext));
            return daoCanContractContext.getMtCustomerId();
        } catch (Exception e) {
            log.error("getDcMtCustomerIdByBatchId 解析batchContext异常, batchId:{}", batchId, e);
            return null;
        }
    }

    /**
     * 查询pdfUrl信息
     * @param requestDTO
     * @return
     */
    public BatchPdfUrlContentBo queryPdfUrlInfo(PdfUrlRequestDTO requestDTO) throws WmCustomerException {
        Long confirmId = requestDTO.getConfirmId();
        if (Objects.isNull(confirmId)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "confirmId不能为空");
        }

        try {
            // 考虑主从延迟
            EcontractTaskBo taskBo = wmEcontractTaskService.queryByIdIfNullFromMaster(confirmId);
            log.info("WmEcontractSignBzService#queryPdfUrlInfo taskBo:{}", JSONObject.toJSONString(taskBo));
            Long batchId = taskBo.getBatchId();
            return wmEcontractBatchBaseService.getDownloadUrlByBatchId(batchId);
        } catch (Exception e) {
            log.error("queryPdfUrlInfo 查询任务信息异常, confirmId:{}", confirmId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "查询任务信息异常");
        }
    }
}
