package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsGatewayThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualBatchSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualSignItem;
import com.sankuai.meituan.waimai.poilogistics.thrift.exception.WmPoiLogisticsException;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 无人机配送回调任务
 */
@Slf4j
@Service
public class DroneDeliveryNoticeTask implements NoticeTask {

    @Autowired
    private WmContractService wmContractService;
    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;
    @Autowired
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;

    @Autowired
    private WmLogisticsGatewayThriftServiceAdapter wmLogisticsGatewayThriftServiceAdapter;

    @Override
    public void notice(String module, List<Long> bizIdList, ManualPackNoticeContext context, List<Long> taskIds) throws WmCustomerException, TException {
        log.info("notice#module:{}, context:{}", module, JSON.toJSONString(context));
        if (MapUtils.isEmpty(context.getTaskInfo()) || CollectionUtils.isEmpty(taskIds)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "参数不合法");
        }
        try {
            // 无人机任务列表
            Map<Long, Long> droneTaskWmPoiIdMap = context.getDroneTaskWmPoiIdMap();
            if (MapUtils.isEmpty(droneTaskWmPoiIdMap)) {
                log.error("droneTaskWmPoiIdMap为空");
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "任务数据组装异常");
            }
            List<HeronContractManualSignItem> manualSignItemList = droneTaskWmPoiIdMap.entrySet().stream()
                    .map(x -> {
                        HeronContractManualSignItem signItem = new HeronContractManualSignItem();
                        signItem.setWmPoiId(x.getValue());
                        signItem.setManualConfirmId(x.getKey());
                        return signItem;
                    }).collect(Collectors.toList());
            HeronContractOperator operator = HeronContractOperator.builder()
                    .opId((long) context.getCommitUid())
                    .build();

            HeronContractManualBatchSignParam manualBatchSignParam = HeronContractManualBatchSignParam.builder()
                    .batchManualConfirmId(context.getManualBatchId())
                    .signItemList(manualSignItemList)
                    .applyType(module)
                    .operator(operator).build();
            wmLogisticsGatewayThriftServiceAdapter.deliveryBatchApplySignUseNewIface(manualBatchSignParam);
        } catch (Exception e) {
            log.error("DroneDeliveryNoticeTask异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMessage());
        }
    }
}
