package com.sankuai.meituan.waimai.customer.settle.message.wallet;

import com.alibaba.fastjson.JSON;
import com.meituan.pay.mwallet.proxy.thrift.MwalletBizEventNotifyService;
import com.meituan.pay.mwallet.proxy.thrift.entity.bizeventnotify.*;
import com.meituan.pay.mwallet.util.SignAndEncUtil;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.mobile.mtthrift.server.MTIface;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.daoadapter.WmSettleAuditedDBMapperAdapter;
import com.sankuai.meituan.waimai.customer.settle.daoadapter.WmSettleDBMapperAdapter;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleLogService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmSettleConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class WmSettleWalletChangeNotifyHandleService extends MTIface implements MwalletBizEventNotifyService.Iface  {

    private static Logger log = LoggerFactory.getLogger(WmSettleWalletChangeNotifyHandleService.class);

    @Autowired
    private WmSettleAuditedDBMapper wmSettleAuditedDBMapper;

    @Autowired
    private WmSettleAuditedDBMapperAdapter wmSettleAuditedDBMapperAdapter;

    @Autowired
    private WmSettleDBMapper wmSettleDBMapper;

    @Autowired
    private WmSettleDBMapperAdapter wmSettleDBMapperAdapter;

    @Autowired
    private WmSettleLogService wmSettleLogService;

    private static final String UTF8_CHARSET = "UTF-8";

    public static final String WALLET_APP_KEY = "com.sankuai.waimai.e.customer";

    public static final String WALLET_PRIVATE_KEY = "mwallet.mcertify.rsa.private.key";

    @Override
    public BizEventNotifyResTo bizEventNotify(BizEventNotifyReqTo bizEventNotifyReqTo) throws TException {
        log.info("支付端变更推送开始 bizEventNotifyReqTo = {}", JSON.toJSONString(bizEventNotifyReqTo));

        if (BizEventType.MOBILE_CHANGE.getValue() == bizEventNotifyReqTo.getBizEventType().getValue()) {
            return handleMobileChangeEvent(bizEventNotifyReqTo);
        } else if (BizEventType.DEFAULT_WITHDRAW_CARD.getValue() == bizEventNotifyReqTo.getBizEventType().getValue()) {
            return handleDefaultWithdrawCardEvent(bizEventNotifyReqTo);
        }

        return new BizEventNotifyResTo("success");
    }

    private BizEventNotifyResTo handleDefaultWithdrawCardEvent(BizEventNotifyReqTo bizEventNotifyReqTo) {
        DefaultWithdrawCardEvent defaultWithdrawCardEvent = bizEventNotifyReqTo.getDefaultWithdrawCardEvent();
        BankcardInfo newBankcardInfo = defaultWithdrawCardEvent.getNewBankcardInfo();

        try {
            List<WmSettleAuditedDB> wmSettleAuditedDBList = wmSettleAuditedDBMapper.getByWalletId(defaultWithdrawCardEvent.getMwalletId());
            if (CollectionUtils.isEmpty(wmSettleAuditedDBList) || wmSettleAuditedDBList.size() != 1) {
                log.warn("一个钱包ID不存在结算或对应多个结算 walletId = ", defaultWithdrawCardEvent.getMwalletId());
                return new BizEventNotifyResTo("success");
            }

            WmSettleAuditedDB wmSettleAuditedDB = wmSettleAuditedDBList.get(0);
            WmSettleDB wmSettleDB = wmSettleDBMapper.getById(wmSettleAuditedDB.getWm_settle_id());
            log.info("银行卡信息变更前 wmSettleAuditedDB = {}, wmSettleDB = {}", JSON.toJSONString(wmSettleAuditedDB), JSON.toJSONString(wmSettleDB));
            // if (wmSettleDB == null) {
            //     log.warn("结算信息不一致。线上结算存在,线下结算不存在! wmSettleId = {}", wmSettleAuditedDB.getWm_settle_id());
            //     return new BizEventNotifyResTo("success");
            // }
            String logContent = wmSettleDB == null ? assembleWmSettleAuditedData(newBankcardInfo, wmSettleAuditedDB) : assembleWmSettleData(newBankcardInfo, wmSettleAuditedDB, wmSettleDB);
            log.info("进入银行卡信息变更 logContent isEmpty = {}", StringUtils.isEmpty(logContent));

            if (StringUtils.isNotEmpty(logContent)) {
                wmSettleAuditedDBMapperAdapter.updateByWmSettleId(wmSettleAuditedDB);
                wmSettleLogService.insertWmSettleLog(wmSettleAuditedDB.getWmCustomerId(), WmCustomerOplogBo.OpType.UPDATE, 0,
                        defaultWithdrawCardEvent.getOperator(), "支付侧的结算信息(开户行/银行卡号)变更:" + logContent);

                if (wmSettleDB != null && WmSettleConstant.SETTLE_STATUS_TO_CONFIRM != wmSettleDB.getStatus()
                        && WmSettleConstant.SETTLE_STATUS_TO_AUDIT != wmSettleDB.getStatus()
                        && WmSettleConstant.SETTLE_STATUS_WAIT_SIGN != wmSettleDB.getStatus()) {
                    wmSettleDBMapperAdapter.updateByPrimaryKeySelective(wmSettleDB);
                }
            }
            log.info("银行卡信息变更后 wmSettleAuditedDB = {}", JSON.toJSONString(wmSettleAuditedDB));
        } catch (Exception e) {
            log.error("支付侧的结算信息(开户行/银行卡号)变更,推送变更表失败", e);
            return new BizEventNotifyResTo("fail");
        }
        return new BizEventNotifyResTo("success");
    }

    private BizEventNotifyResTo handleMobileChangeEvent(BizEventNotifyReqTo bizEventNotifyReqTo) {
        MobileChangeEvent mobileChangeEvent = bizEventNotifyReqTo.getMobileChangeEvent();

        try {
            List<WmSettleAuditedDB> wmSettleAuditedDBList = wmSettleAuditedDBMapper.getByWalletId(mobileChangeEvent.getMwalletId());
            if (CollectionUtils.isEmpty(wmSettleAuditedDBList) || wmSettleAuditedDBList.size() != 1) {
                log.warn("一个钱包ID不存在结算或对应多个结算 walletId = ", mobileChangeEvent.getMwalletId());
                return new BizEventNotifyResTo("success");
            }
            String newMobile = decryptWalletEncryptContent(mobileChangeEvent.getNewMobile());

            WmSettleAuditedDB wmSettleAuditedDB = wmSettleAuditedDBList.get(0);
            log.info("手机号变更前 wmSettleAuditedDB = {}, newMobile = {}", JSON.toJSONString(wmSettleAuditedDB), newMobile);
            Byte acctype = wmSettleAuditedDB.getAcctype();
            //对公
            if(Byte.valueOf("1").equals(acctype)){
                if (wmSettleAuditedDB != null && !wmSettleAuditedDB.getParty_a_finance_phone().equals(newMobile)) {
                    log.info("进入手机号变更 oldMobile = {}", wmSettleAuditedDB.getParty_a_finance_phone());
                    StringBuilder changeLog = new StringBuilder("支付侧的结算信息(财务联系人手机号)变更");
                    changeLog.append("\n[字段变更] 手机号 : ").append(wmSettleAuditedDB.getParty_a_finance_phone()).append(" => ").append(newMobile);

                    wmSettleAuditedDB.setParty_a_finance_phone(newMobile);
                    wmSettleAuditedDBMapperAdapter.updateByWmSettleId(wmSettleAuditedDB);
                    wmSettleLogService.insertWmSettleLog(wmSettleAuditedDB.getWmCustomerId(), WmCustomerOplogBo.OpType.UPDATE, 0, mobileChangeEvent.getOperator(), changeLog.toString());

                    WmSettleDB wmSettleDB = wmSettleDBMapper.getById(wmSettleAuditedDB.getWm_settle_id());
                    if (wmSettleDB == null) {
                        log.warn("结算信息不一致。线上结算存在,线下结算不存在! wmSettleId = {}", wmSettleAuditedDB.getWm_settle_id());
                        return new BizEventNotifyResTo("success");
                    }
                    if (WmSettleConstant.SETTLE_STATUS_TO_CONFIRM != wmSettleDB.getStatus()
                            && WmSettleConstant.SETTLE_STATUS_TO_AUDIT != wmSettleDB.getStatus()
                            && WmSettleConstant.SETTLE_STATUS_WAIT_SIGN != wmSettleDB.getStatus()) {
                        wmSettleDB.setParty_a_finance_phone(newMobile);
                        wmSettleDBMapperAdapter.updateByPrimaryKeySelective(wmSettleDB);
                    }
                }
            }
            //对私
            else if(Byte.valueOf("2").equals(acctype)){
                if (wmSettleAuditedDB != null && !wmSettleAuditedDB.getReserve_phone().equals(newMobile)) {
                    log.info("进入手机号变更 oldMobile = {}", wmSettleAuditedDB.getReserve_phone());
                    StringBuilder changeLog = new StringBuilder("支付侧的结算信息(银行预留手机号)变更");
                    changeLog.append("\n[字段变更] 手机号 : ").append(wmSettleAuditedDB.getReserve_phone()).append(" => ").append(newMobile);

                    wmSettleAuditedDB.setReserve_phone(newMobile);
                    wmSettleAuditedDBMapperAdapter.updateByWmSettleId(wmSettleAuditedDB);
                    wmSettleLogService.insertWmSettleLog(wmSettleAuditedDB.getWmCustomerId(), WmCustomerOplogBo.OpType.UPDATE, 0, mobileChangeEvent.getOperator(), changeLog.toString());

                    WmSettleDB wmSettleDB = wmSettleDBMapper.getById(wmSettleAuditedDB.getWm_settle_id());
                    if (wmSettleDB == null) {
                        log.warn("结算信息不一致。线上结算存在,线下结算不存在! wmSettleId = {}", wmSettleAuditedDB.getWm_settle_id());
                        return new BizEventNotifyResTo("success");
                    }
                    if (WmSettleConstant.SETTLE_STATUS_TO_CONFIRM != wmSettleDB.getStatus()
                            && WmSettleConstant.SETTLE_STATUS_TO_AUDIT != wmSettleDB.getStatus()
                            && WmSettleConstant.SETTLE_STATUS_WAIT_SIGN != wmSettleDB.getStatus()) {
                        wmSettleDB.setReserve_phone(newMobile);
                        wmSettleDBMapperAdapter.updateByPrimaryKeySelective(wmSettleDB);
                    }
                }
            }
            log.info("手机号变更后 wmSettleAuditedDB = {}", JSON.toJSONString(wmSettleAuditedDB));
        } catch (Exception e) {
            log.error("支付侧的结算信息(银行预留手机号)变更,推送变更表失败", e);
            return new BizEventNotifyResTo("fail");
        }
        return new BizEventNotifyResTo("success");
    }

    private String assembleWmSettleData(BankcardInfo newBankcardInfo, WmSettleAuditedDB wmSettleAuditedDB, WmSettleDB wmSettleDB) throws Exception {
        if (!newBankcardInfo.isSetBankcardNumber()) {
            return StringUtils.EMPTY;
        }
        String bankCardNum = decryptWalletEncryptContent(newBankcardInfo.getBankcardNumber());
        if (bankCardNum.trim().equals(wmSettleAuditedDB.getAcc_cardno().trim())) {
            return StringUtils.EMPTY;
        }

        StringBuilder changeLog = new StringBuilder();
        changeLog.append("\n[字段变更] 银行卡号 : ").append(wmSettleAuditedDB.getAcc_cardno()).append(" => ").append(bankCardNum);
        wmSettleAuditedDB.setAcc_cardno(bankCardNum);
        wmSettleDB.setAcc_cardno(bankCardNum);

        if (newBankcardInfo.isSetBankId()) {
            changeLog.append("\n[字段变更] 银行ID : ").append(wmSettleAuditedDB.getBankid()).append(" => ").append(newBankcardInfo.getBankId());
            // 只要换了银行,省市对不上,不展示
            wmSettleAuditedDB.setBankid(Short.valueOf(newBankcardInfo.getBankId()));
            wmSettleAuditedDB.setProvince(-1);
            wmSettleAuditedDB.setCity(-1);
            wmSettleDB.setBankid(Short.valueOf(newBankcardInfo.getBankId()));
            wmSettleDB.setProvince(-1);
            wmSettleDB.setCity(-1);
        }

        if (newBankcardInfo.isSetBranchId()) {
            changeLog.append("\n[字段变更] 支行ID : ").append(wmSettleAuditedDB.getBranchid()).append(" => ").append(newBankcardInfo.getBranchId());
            wmSettleAuditedDB.setBranchid(Integer.valueOf(newBankcardInfo.getBranchId()));
            wmSettleDB.setBranchid(Integer.valueOf(newBankcardInfo.getBranchId()));
        }

        if (newBankcardInfo.isSetBranchName()) {
            changeLog.append("\n[字段变更] 支行名称 : ").append(wmSettleAuditedDB.getBranchname()).append(" => ").append(newBankcardInfo.getBranchName());
            wmSettleAuditedDB.setBranchname(newBankcardInfo.getBranchName());
            wmSettleDB.setBranchname(newBankcardInfo.getBranchName());
        }

        return changeLog.toString();
    }

    private String assembleWmSettleAuditedData(BankcardInfo newBankcardInfo, WmSettleAuditedDB wmSettleAuditedDB) throws Exception {
        if (!newBankcardInfo.isSetBankcardNumber()) {
            return StringUtils.EMPTY;
        }
        String bankCardNum = decryptWalletEncryptContent(newBankcardInfo.getBankcardNumber());
        if (bankCardNum.trim().equals(wmSettleAuditedDB.getAcc_cardno().trim())) {
            return StringUtils.EMPTY;
        }

        StringBuilder changeLog = new StringBuilder();
        changeLog.append("\n[字段变更] 银行卡号 : ").append(wmSettleAuditedDB.getAcc_cardno()).append(" => ").append(bankCardNum);
        wmSettleAuditedDB.setAcc_cardno(bankCardNum);

        if (newBankcardInfo.isSetBankId()) {
            changeLog.append("\n[字段变更] 银行ID : ").append(wmSettleAuditedDB.getBankid()).append(" => ").append(newBankcardInfo.getBankId());
            // 只要换了银行,省市对不上,不展示
            wmSettleAuditedDB.setBankid(Short.valueOf(newBankcardInfo.getBankId()));
            wmSettleAuditedDB.setProvince(-1);
            wmSettleAuditedDB.setCity(-1);
        }

        if (newBankcardInfo.isSetBranchId()) {
            changeLog.append("\n[字段变更] 支行ID : ").append(wmSettleAuditedDB.getBranchid()).append(" => ").append(newBankcardInfo.getBranchId());
            wmSettleAuditedDB.setBranchid(Integer.valueOf(newBankcardInfo.getBranchId()));
        }

        if (newBankcardInfo.isSetBranchName()) {
            changeLog.append("\n[字段变更] 支行名称 : ").append(wmSettleAuditedDB.getBranchname()).append(" => ").append(newBankcardInfo.getBranchName());
            wmSettleAuditedDB.setBranchname(newBankcardInfo.getBranchName());
        }

        return changeLog.toString();
    }


    private String decryptWalletEncryptContent(String encryptContent) throws Exception {
        log.info("加密内容 encryptContent = {}", encryptContent);
        if (StringUtils.isEmpty(encryptContent)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "钱包信息加密内容为空");
        }

        try {
            String privateKey = Kms.getByName(WALLET_APP_KEY, WALLET_PRIVATE_KEY);
            if (StringUtils.isEmpty(privateKey)) {
                log.error("获取密钥失败, 使用默认密钥。");
                privateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKo3fcrgiqUnLdJZ42Zdc0A4i3NfiSuP3iSTMBeeUtiMh9pT6TbjFQmrCbuSPHqoBvvJQQgnQ4U0U3DjBHYOA0m9CZTQC60ooJdWGbrbOtRsbpASRkiOH61f2rJX0lbDtBg7hDrwat8nVKlE/UYMftQRgZmU09KY5NJrXIl3MPU5AgMBAAECgYBIhU/w6e3oI2MgZe++oz4BNJYlV6THjIompxcsWOYPIQCYgnuwSNujvN1urwVZdNFU4Q/1+1SHIh4S0IYMDjhTtgGKooAnzTFPdsi0ieKCHhhPkLK4xdkubgDhs41tEoDS+I4xmLhqCVJujRkTNQ17VrjhzOo8oo73sqOV61t9AQJBAOBh1wMFTmWDwBtxACd9siwl5J1wOXGc+UW21Gpde7OiElDH9GeFzDjw3pHUKA0BAh5T2brO5e+Qm+TwrI3tABECQQDCM7yp4mP+fs7nQwV0r/MaB4WxEeWXdY2xqdHlB5AMLxd9QQ0FrTaP4K9Ty1u8oRQD+SLoq4Sz7medxUlPR0qpAkA2O2+ca0vnfVCRUV6YLOlDgmUfKGC59RbKosX0b2PWpvWGUM3ht5UErjMdTAniGrxIWDvnytoIbQtA6mrKYt4BAkA8okavjB3IYfNbxVM4wY7Xe63EQWA7z9oztF/ycFALXdCprNvR/+jfNznGoeG8nVZQf0Lk/lhqjW8QlX50IEAxAkBEbRFFGCQZFzPvajOti0gI5AzzTk/SunJdpqSldLi70cq37pBDqGqq6uG+xctlajx6i8ZrrJwKMyc4BZoL2c7K";
            }

            String decryptContent = SignAndEncUtil.decrypt(encryptContent, privateKey, UTF8_CHARSET);
            return decryptContent;
        } catch (Exception e) {
            log.error("加密内容({})解密异常:", encryptContent, e);
            throw e;
        }
    }
}
