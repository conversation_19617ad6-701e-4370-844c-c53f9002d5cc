package com.sankuai.meituan.waimai.customer.util;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.S3Object;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-07-16 16:29
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class S3Helper {

    /**
     * 用户secret Access ID
     */
    private static final String ACCESS_KEY_NAME = "s3.access.key";

    /**
     * 用户secret ID
     */
    private static final String SECRET_KEY_NAME = "s3.secret.key";

    // 支持内网+外网访问：s3plus.sankuai.com；仅支持内网访问：s3plus.vip.sankuai.com
    private static final String HOSTNAME = "s3plus.vip.sankuai.com";

    private static final AmazonS3 s3client = createAmazonS3Conn(HOSTNAME);

    /**
     * 生成AmazonS3对象
     *
     * @param hostname：MSS的endpoint服务地址
     * @return
     */
    public static AmazonS3 createAmazonS3Conn(String hostname) {
        String accessKey = KmsUtil.getString(ACCESS_KEY_NAME);
        String secretKey = KmsUtil.getString(SECRET_KEY_NAME);
        log.info("S3Helper fetch kms value accessKey:{}, secretKey:{}", accessKey, secretKey);
        AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
        ClientConfiguration configuration = new ClientConfiguration();
        // 默认协议为HTTPS。将这个值设置为Protocol.HTTP，则使用的是HTTP协议
        configuration.setProtocol(Protocol.HTTPS);

        //生成云存储api client
        AmazonS3 s3client = new AmazonS3Client(credentials, configuration);

        //配置云存储服务地址
        //注意Endpoint只填写域名，不包含协议( http:// 或 https:// )
        s3client.setEndpoint(hostname);

        //设置客户端生成的http请求hos格式，目前只支持path type的格式，不支持bucket域名的格式
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        s3ClientOptions.setPathStyleAccess(true);
        s3client.setS3ClientOptions(s3ClientOptions);
        return s3client;
    }

    /**
     * 创建bucket
     *
     * @param bucketName：bucket名称
     */
    public void createBucketIfNotExistExample(String bucketName) {
        try {
            //判断待创建的bucket是否存在，如果存在不用重复创建，重复创建同名bucket服务器端会返回错误
            if (s3client.doesBucketExist(bucketName) == false) {
                s3client.createBucket(bucketName);
            }
        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            log.warn("S3Util#createBucketIfNotExistExample Caught an ServiceException，Error Message：{}，HTTP Status Code：{}，Error Code：{}，Error Type：{}，Request ID：{}",
                    ase.getMessage(), ase.getStatusCode(), ase.getErrorCode(), ase.getErrorType(), ase.getRequestId());
        } catch (AmazonClientException ace) {
            //客户端处理异常
            log.warn("S3Util#createBucketIfNotExistExample Caught an ClientException，Error Message:{}", ace.getMessage());
        }
    }

    /**
     *
     * @param bucketName：指定上传文件所在的桶名
     * @param objectName：指定上传的文件名
     * @param content：指定上传的文件内容
     */
    public void putObject(String bucketName, String objectName, String content) {
        try {
            s3client.putObject(bucketName, objectName, new ByteArrayInputStream(content.getBytes()), null);
        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            log.warn("S3Util#putObject Caught an ServiceException，bucketName：{}，objectName：{}，content：{}", bucketName, objectName, content, ase);
        } catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("Caught an ClientException.");
            System.out.println("Error Message: " + ace.getMessage());
        }
    }

    /**
     *
     * @param bucketName：指定下载文件所在的桶名
     * @param objectName：指定下载的文件名
     */
    public byte[] getObject(String bucketName, String objectName) {
        S3Object s3object = null;
        try {
            s3object = s3client.getObject(bucketName, objectName);
        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            log.warn("S3Util#getObject Caught an ServiceException，bucketName：{}，objectName：{}", bucketName, objectName, ase);
        } catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("Caught an ClientException.");
            System.out.println("Error Message: " + ace.getMessage());
        }
        byte[] objectContent = null;
        try{
            if(s3object != null){
                InputStream inputStream = s3object.getObjectContent();
                objectContent = readInputStream(inputStream);
            }
        }catch(IOException e){
            log.warn("转换content异常，bucketName：{}，objectName：{}", bucketName, objectName, e);
        }
        return objectContent;
    }

    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }
}
