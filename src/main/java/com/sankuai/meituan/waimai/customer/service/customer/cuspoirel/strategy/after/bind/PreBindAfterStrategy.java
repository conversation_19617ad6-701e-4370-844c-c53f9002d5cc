package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.bind;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IBindAfterStrategy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240119
 * @desc 预绑定后置策略
 */
@Service
@Slf4j
public class PreBindAfterStrategy implements IBindAfterStrategy {

    @Autowired
    private WmCustomerService wmCustomerService;

    /**
     * 直接绑定后置操作
     *
     * @param context
     * @throws WmCustomerException
     */
    @Override
    public void execute(CustomerPoiBindFlowContext context) throws WmCustomerException {
        log.info("PreBindAfterStrategy.execute,客户预绑定发起成功，开始执行后置行为");
        preBindAfterActions(context);

    }

    /**
     * 预绑定发起成功的后置操作
     *
     * @param context
     */
    public void preBindAfterActions(CustomerPoiBindFlowContext context) {
        Integer customerId = context.getCustomerId();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        Integer opUid = context.getOpUid();
        String opName = context.getOpName();
        try {
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                    String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_APPLY_PRE_BIND, StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), "");

            // 发送MQ
            wmCustomerService.sendCustomerStatusNoticeMQ(customerId, CustomerMQEventEnum.CUSTOMER_PRE_BIND_POI_ING,
                    wmPoiIdSet, opUid, opName);
        } catch (Exception e) {
            log.error("PreBindAfterStrategy.preBindAfterActions,客户门店发起预绑定完成，记录日志并发送消息处理异常,customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet), e);
        }
    }

}
