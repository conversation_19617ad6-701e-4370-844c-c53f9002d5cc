package com.sankuai.meituan.waimai.customer.service.agreement.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.healthm.health.operation.contract.thrift.enums.Code;
import com.sankuai.healthm.health.operation.contract.thrift.result.PrescriptionInfoResult;
import com.sankuai.healthm.health.operation.contract.thrift.service.THealthContractQueryService;
import com.sankuai.meituan.health.merchant.center.thrift.client.SettledContractThriftService;
import com.sankuai.meituan.health.merchant.center.thrift.response.contract.ShowSecurityDepositContractResponse;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.thrift.WmCustomerContractVersionThriftServiceImpl;
import com.sankuai.meituan.waimai.customer.domain.ContractSignType;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementFormDB;
import com.sankuai.meituan.waimai.customer.service.agreement.WmAgreementCacheService;
import com.sankuai.meituan.waimai.customer.service.agreement.WmAgreementFilterService;
import com.sankuai.meituan.waimai.customer.service.agreement.WmAgreementModelDicService;
import com.sankuai.meituan.waimai.customer.service.agreement.WmAgreementService;
import com.sankuai.meituan.waimai.customer.service.agreement.WmAgreementSignLogService;
import com.sankuai.meituan.waimai.customer.service.agreement.WmAgreementSyncService;
import com.sankuai.meituan.waimai.customer.service.agreement.WmSignInfoForBService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.util.MasterSlaveHelper;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmPoiLogisticsAgreementInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsPoiAgreementThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmCustomerLinkageConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.AgreementTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.ContractSignTypeOutputDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.DeliveryAreaSignInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.SupplyChainSignInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAggrementAccountSignRequestParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAggrementAccountSignResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementFormBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementListPageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementModel;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementModelDicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementMsgParamBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementPoiSign;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementSignLogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.ContractVersionPageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractVersionBo;
import com.sankuai.meituan.waimai.thrift.customer.service.agreement.WmAgreementThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.tsp.phf.contract.dto.contract.QueryContractsByHistoryParamDTO;
import com.sankuai.tsp.phf.contract.dto.contract.QueryContractsByHistoryResDTO;
import com.sankuai.tsp.phf.contract.thrift.TspPhfPoiContractThriftService;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import joptsimple.internal.Strings;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 协议接口
 *
 * <AUTHOR>
 */
@Service
public class WmAgreementThriftServiceImpl implements WmAgreementThriftService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmAgreementThriftServiceImpl.class);

    @Autowired
    private WmAgreementService wmAgreementService;

    @Autowired
    private WmAgreementModelDicService wmAgreementModelDicService;

    @Autowired
    private WmAgreementSyncService wmAgreementSyncService;

    @Autowired
    private WmAgreementSignLogService wmAgreementSignLogService;

    @Autowired
    private WmAgreementFilterService wmAgreementFilterService;

    @Autowired
    private WmAgreementCacheService wmAgreementCacheService;

    @Autowired
    private WmPoiLogisticsPoiAgreementThriftService.Iface wmPoiLogisticsPoiAgreementThriftService;

    @Autowired
    private WmCustomerContractVersionThriftServiceImpl wmCustomerContractVersionThriftServiceImpl;

    @Autowired
    private WmSignInfoForBService wmSignInfoForBService;

    @Autowired
    private TspPhfPoiContractThriftService tspPhfPoiContractThriftService;

    @Autowired
    private SettledContractThriftService settledContractThriftService;

    @Autowired
    private THealthContractQueryService tHealthContractQueryService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;


    @Override
    public WmAgreementModel getAgreementInfoById(int agreementId) throws TException, WmCustomerException {
        return wmAgreementService.getAgreementById(agreementId);
    }

    @Override
    public List<WmAgreementModel> getAgreementsByIdList(List<Integer> agreementIds) throws TException, WmCustomerException {
        return wmAgreementService.getAgreementsByIdList(new HashSet<>(agreementIds));
    }

    @Override
    public WmAgreementModel getAgreementInfoByType(int agreementType) throws TException, WmCustomerException {
        return wmAgreementService.getAgreementByType(agreementType);
    }

    @Override
    public List<WmAgreementPoiSign> getPoiSignByWmPoiId(long wmPoiId, int type) throws TException, WmCustomerException {
        return wmAgreementService.getPoiSignByWmpoiId(wmPoiId, type);
    }

    @Override
    public void batchSignAgreement(int agreementId, Long accId, List<Long> wmPoiIds, int type,
                                   WmAgreementMsgParamBo msgParamBo)
        throws TException, WmCustomerException {
        wmAgreementService.batchSignAgreement(agreementId, accId, wmPoiIds, type, msgParamBo);
    }

    @Override
    public void accountSignAgreement(WmAggrementAccountSignRequestParam param) throws TException, WmCustomerException {
        LOGGER.info("accountSignAgreement#param={}", JSON.toJSONString(param));
        wmAgreementService.accountSignAgreement(param);
    }

    @Override
    public WmAgreementListPageData getWmAgreementList(WmAgreementFormBo formBo) throws TException, WmCustomerException {
        return wmAgreementService.getAgreementList(formBo);
    }

    @Override
    public BooleanResult addWmAgreementModelDicRecord(WmAgreementModelDicBo wmAgreementModelDicBo) throws TException, WmCustomerException {
        return wmAgreementModelDicService.addWmAgreementModelDicRecord(wmAgreementModelDicBo);
    }

    @Override
    public BooleanResult updateWmAgreementModelDicRecord(WmAgreementModelDicBo wmAgreementModelDicBo) throws TException, WmCustomerException {
        return wmAgreementModelDicService.updateWmAgreementModelDicRecord(wmAgreementModelDicBo);
    }

    @Override
    public BooleanResult addWmAgreementModel(WmAgreementModel wmAgreementModel) throws TException, WmCustomerException {
        return wmAgreementService.addWmAgreementModel(wmAgreementModel);
    }

    @Override
    public String getConfiguredVersion() throws TException, WmCustomerException {
        return JSONObject.toJSONString(wmAgreementCacheService.getConfiguredVersionFromLocalCache());
    }

    @Override
    public BooleanResult syncOldDataToNewData(List<Long> wmPoiIdList) throws TException, WmCustomerException {
        return wmAgreementSyncService.syncOldDataToNewData(wmPoiIdList, false, true);
    }

    @Override
    public BooleanResult compareAndFix(List<Long> wmPoiIdList, boolean needFix) throws TException, WmCustomerException {
        return wmAgreementSyncService.compare(wmPoiIdList, needFix);
    }

    @Override
    public void batchAddSignLog(List<WmAgreementSignLogBo> signLogBoList) throws TException, WmCustomerException {
        wmAgreementSignLogService.batchRecordOnPoiSignStatusChange(signLogBoList);
    }

    @Override
    public void batchAddAccountSignLog(List<WmAgreementSignLogBo> signLogBoList) throws TException, WmCustomerException {
        if(CollectionUtils.isEmpty(signLogBoList)){
            return;
        }
        LOGGER.info("账号维度签约记录：signLogBoList={}", JSON.toJSONString(signLogBoList));
        List<Integer> aggrementIds = signLogBoList.stream().map(signLog -> signLog.getWmAgreeModelId()).collect(Collectors.toList());
        Map<Integer,WmAgreementModel> aggrementModelMap = wmAgreementService.getAggrementModelMaps(aggrementIds);
        LOGGER.debug("账号维度签约记录：aggrementModelMap={}", JSON.toJSONString(aggrementModelMap));
        signLogBoList.stream().forEach(signLog -> {
            signLog.setWmAgreeModelType(aggrementModelMap.get(signLog.getWmAgreeModelId()) == null ?
                0 : aggrementModelMap.get(signLog.getWmAgreeModelId()).getAgreeModelType());
        });
        wmAgreementSignLogService.batchRecordOnPoiSignStatusChange(signLogBoList);
    }

    @Override
    public WmAggrementAccountSignResp getAccountSignResultByAccountId(Long accountId, String agreeTypes) throws TException, WmCustomerException {
        return wmAgreementService.getAccountSignResultByAccountId(accountId, agreeTypes);
    }

    @Override
    public List<WmAgreementModel> queryAllAgreementModel() {
        return wmAgreementService.queryAllAgreementModel();
    }

    @Override
    public void processDeliveryTypeCloseEvent(long wmPoiId, long utime, int deliveryType) throws TException, WmCustomerException {
        wmAgreementSyncService.processDeliveryTypeCloseEvent(wmPoiId, utime, deliveryType);
    }

    @Override
    public Map<Integer, List<Long>> getUnsignProtocolWmPoiIds(Long wmPoiId, String accountId) throws TException, WmCustomerException {
        return wmAgreementFilterService.getUnsignProtocolWmPoiIds(wmPoiId, accountId);
    }

    @Override
    public WmAgreementPoiSign getUnSignProtocolByWmPoiIdAndType(Long wmPoiId, Integer type) throws TException, WmCustomerException {
        LOGGER.info("根据门店和协议类型获取签约记录参数：wmPoiId={},type={}", wmPoiId, type);
        List<WmAgreementPoiSign> wmAgreementPoiSigns = wmAgreementService.getPoiSignByWmpoiId(wmPoiId, type);
        if (CollectionUtils.isEmpty(wmAgreementPoiSigns)) {
            return null;
        }
        return wmAgreementPoiSigns.get(0);
    }

    @Override
    public WmAgreementPoiSign getSignSuccessProtocolByWmPoiIdAndType(long wmPoiId, int type, boolean fromMaster)
        throws TException, WmCustomerException {
        LOGGER.info("#getSignSuccessProtocolByWmPoiIdAndType：wmPoiId={},type={},fromMaster={}",
            wmPoiId,
            type,
            fromMaster);
        List<WmAgreementPoiSign> wmAgreementPoiSigns = null;
        if (fromMaster) {
            wmAgreementPoiSigns = MasterSlaveHelper.doInMaster(() -> {
                return wmAgreementService.getPoiSignByWmpoiId(wmPoiId, type);
            });
        } else {
            wmAgreementPoiSigns = wmAgreementService.getPoiSignByWmpoiId(wmPoiId, type);
        }
        if (CollectionUtils.isEmpty(wmAgreementPoiSigns)) {
            return null;
        }
        return wmAgreementPoiSigns.get(0);
    }

    /**
     * 获取合同列表枚举
     *
     * @param wmPoiId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public List<ContractSignTypeOutputDTO> getContractTypeEnum(Long wmPoiId) throws TException, WmCustomerException {

        String contractSignTypeStr = MccConfig.getContractSignType();
        List<ContractSignTypeOutputDTO> contractSignTypeOutputDTOList = Lists.newArrayList();

        try {

            // 配送相关合同
            List<String> poiAgreementTypeList = wmPoiLogisticsPoiAgreementThriftService.getPoiAgreementType(wmPoiId);
            LOGGER.info("poiAgreementTypeList:[{}]", JSON.toJSONString(poiAgreementTypeList));

            Set<String> setPoiAgreementSet = Sets.newHashSet(CollectionUtils.isEmpty(poiAgreementTypeList) ? Lists.newArrayList() : poiAgreementTypeList);

            List<ContractSignType> contractSignTypeList = JSON.parseArray(contractSignTypeStr, ContractSignType.class);
            Map<Integer, ContractSignType> contractSignTypeMap = Maps.newHashMap();
            for (ContractSignType contractSignType : contractSignTypeList) {
                contractSignTypeMap.put(contractSignType.getType(), contractSignType);
                if (setPoiAgreementSet.contains(contractSignType.getKey())) {
                    contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignType));
                }
            }

            // 美团与客户合同
            ContractVersionPageData c1ContractVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId, 
                    WmCustomerLinkageConstant.WmCustomerSpecies.C1.getCode(), 1, 1, -1, Strings.EMPTY);
            if (c1ContractVersionPageData != null && !CollectionUtils.isEmpty(c1ContractVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.C1.getCode())));
            }

            // 合作商与客户合同
            ContractVersionPageData c2ContractVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId, 
                    WmCustomerLinkageConstant.WmCustomerSpecies.C2.getCode(), 1, 1, -1, Strings.EMPTY);
            if (c2ContractVersionPageData != null && !CollectionUtils.isEmpty(c2ContractVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.C2.getCode())));
            }

            // 企客配送服务合同
            ContractVersionPageData businessCustomerContractVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId,
                            WmCustomerLinkageConstant.WmCustomerSpecies.BUSINESS_CUSTOMER.getCode(), 1, 1, -1, Strings.EMPTY);
            if (businessCustomerContractVersionPageData != null && !CollectionUtils.isEmpty(
                businessCustomerContractVersionPageData.getList())) {
                contractSignTypeOutputDTOList
                    .add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.BUSINESS_CUSTOMER.getCode())));
            }

            // 打包(袋)服务合作协议
            ContractVersionPageData bagServiceContractVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId, 
                    WmCustomerLinkageConstant.WmCustomerSpecies.BAG_SERVICE.getCode(), 1, 1, -1, Strings.EMPTY);
            if (bagServiceContractVersionPageData != null && !CollectionUtils.isEmpty(bagServiceContractVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.BAG_SERVICE.getCode())));
            }

            // 美食城承诺书
            ContractVersionPageData foodcityStatementContractVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId,
                            WmCustomerLinkageConstant.WmCustomerSpecies.FOODCITY_STATEMENT.getCode(), 1, 1, -1, Strings.EMPTY);
            if (foodcityStatementContractVersionPageData != null && !CollectionUtils.isEmpty(
                foodcityStatementContractVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.FOODCITY_STATEMENT.getCode())));
            }

            // 配送产品临时调整补充协议
            ContractVersionPageData interimSelfContractVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId, 
                    WmCustomerLinkageConstant.WmCustomerSpecies.INTERIM_SELF.getCode(), 1, 1, -1, Strings.EMPTY);
            if (interimSelfContractVersionPageData != null && !CollectionUtils.isEmpty(interimSelfContractVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.INTERIM_SELF.getCode())));
            }

            // 主体变更补充协议
            ContractVersionPageData subjectChangeSupplementVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId,
                            WmCustomerLinkageConstant.WmCustomerSpecies.SUBJECT_CHANGE_SUPPLEMENT.getCode(), 1, 1, -1, Strings.EMPTY);
            if (subjectChangeSupplementVersionPageData != null && !CollectionUtils.isEmpty(subjectChangeSupplementVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.SUBJECT_CHANGE_SUPPLEMENT.getCode())));
            }

            // 四轮履约补充协议
            ContractVersionPageData fourWheelPerfSupplementPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId,
                    WmCustomerLinkageConstant.WmCustomerSpecies.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT.getCode(), 1, 1, -1, Strings.EMPTY);
            if (fourWheelPerfSupplementPageData != null && !CollectionUtils.isEmpty(fourWheelPerfSupplementPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT.getCode())));
            }

            // 极速达模式补充协议
            ContractVersionPageData speedyDeliveryCooperationPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId,
                    WmCustomerLinkageConstant.WmCustomerSpecies.SPEEDY_DELIVERY_COOPERATION_AGREEMENT.getCode(), 1, 1, -1, Strings.EMPTY);
            if (speedyDeliveryCooperationPageData != null && !CollectionUtils.isEmpty(speedyDeliveryCooperationPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.SPEEDY_DELIVERY_COOPERATION_AGREEMENT.getCode())));
            }

            // 采销合作协议
            ContractVersionPageData nationalSubsidyPurchasePageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId,
                    WmCustomerLinkageConstant.WmCustomerSpecies.NATIONAL_SUBSIDY_PURCHASE.getCode(), 1, 1, -1, Strings.EMPTY);
            if (nationalSubsidyPurchasePageData != null && !CollectionUtils.isEmpty(nationalSubsidyPurchasePageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.NATIONAL_SUBSIDY_PURCHASE.getCode())));
            }

            // 拼好饭协议
            QueryContractsByHistoryParamDTO paramDTO = new QueryContractsByHistoryParamDTO();
            paramDTO.setWmPoiId(wmPoiId);
            QueryContractsByHistoryResDTO resDTO = tspPhfPoiContractThriftService.queryContractsByHistory(paramDTO);
            if (resDTO != null && resDTO.getCode() == 0 && !CollectionUtils.isEmpty(resDTO.getContractResDTOs())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.PHF_CONTRACT.getCode())));
            }

            // 医药保证金协议
            ShowSecurityDepositContractResponse depositContractResponse = settledContractThriftService.queryShowSecurityDepositContract(wmPoiId);
            if (depositContractResponse != null && depositContractResponse.getCode() == 0 && !CollectionUtils.isEmpty(depositContractResponse.getShowSecurityDepositContractDTOList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.MED_DEPOSIT.getCode())));
            }

            // 服务质控商家协议
            PrescriptionInfoResult prescriptionInfoResult = tHealthContractQueryService.queryPrescriptionContractInfoByPoiId(wmPoiId);
            if (prescriptionInfoResult != null && prescriptionInfoResult.getCode() == Code.SUCCESS && prescriptionInfoResult.getData() != null) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.MED_OTC_QUALITY.getCode())));
            }
            // 医药分单补充协议
            ContractVersionPageData medicOrderSplitVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId,
                            WmCustomerLinkageConstant.WmCustomerSpecies.MEDIC_ORDER_SPLIT.getCode(), 1, 1, -1, Strings.EMPTY);
            if (Objects.nonNull(medicOrderSplitVersionPageData) && !CollectionUtils.isEmpty(
                medicOrderSplitVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.MEDIC_ORDER_SPLIT.getCode())));
            }

            // 资质属实商家承诺函
            ContractVersionPageData quaRealLetterVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId,
                            WmCustomerLinkageConstant.WmCustomerSpecies.QUA_REAL_LETTER.getCode(), 1, 1, -1, Strings.EMPTY);
            if (Objects.nonNull(quaRealLetterVersionPageData) && !CollectionUtils.isEmpty(
                quaRealLetterVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.QUA_REAL_LETTER.getCode())));
            }
            
            // 运营经理授权
            ContractVersionPageData poiPromotionServiceVersionPageData = wmCustomerContractVersionThriftServiceImpl.getEffectiveVersions(wmPoiId, 
                    WmCustomerLinkageConstant.WmCustomerSpecies.POI_PROMOTION_SERVICE.getCode(), 1, 1, -1, Strings.EMPTY);
            if (Objects.nonNull(poiPromotionServiceVersionPageData) && !CollectionUtils.isEmpty(
                poiPromotionServiceVersionPageData.getList())) {
                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignTypeMap.get(WmCustomerLinkageConstant.WmCustomerSpecies.POI_PROMOTION_SERVICE.getCode())));
            }

            // 配置化框架合同
            List<ContractConfigInfo> contractConfigInfoList = wmFrameContractConfigService.allConfigFrameContract();
            for (ContractConfigInfo configInfo : contractConfigInfoList) {
                if (!configInfo.getSourceAuthInfo().getCanDisplayInSingleView()) {
                    continue;
                }
                ContractVersionPageData configContractVersionPage = wmCustomerContractVersionThriftServiceImpl.getConfigContractEffectiveVersions(wmPoiId, configInfo.getContractId(), 1, 1, -1, Strings.EMPTY);
                if (Objects.nonNull(configContractVersionPage) && !CollectionUtils.isEmpty(configContractVersionPage.getList())) {
                    contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTOFromFrameContract(configInfo));
                }
            }

            LOGGER.info("contractSignTypeOutputDTOList:[{}]", JSON.toJSONString(contractSignTypeOutputDTOList));
        } catch (Exception e) {
            LOGGER.error("解析合同列表枚举失败 wmPoiId:[{}],contractSignTypeStr:[{}]", wmPoiId, contractSignTypeStr, e);
        }

        Collections.sort(contractSignTypeOutputDTOList, (o1, o2) -> o1.getSort() - o2.getSort());
        return contractSignTypeOutputDTOList;
    }

    private ContractSignTypeOutputDTO getContractSignTypeOutputDTOFromFrameContract(ContractConfigInfo configInfo) {
        ContractSignTypeOutputDTO contractSignTypeOutputDTO = new ContractSignTypeOutputDTO();
        contractSignTypeOutputDTO.setType(configInfo.getContractId());
        contractSignTypeOutputDTO.setTypeName(configInfo.getContractName());
        contractSignTypeOutputDTO.setKey(String.valueOf(configInfo.getContractId()));
        contractSignTypeOutputDTO.setSort(configInfo.getContractId());
        return contractSignTypeOutputDTO;
    }

    private ContractSignTypeOutputDTO getContractSignTypeOutputDTO(ContractSignType contractSignType) {
        ContractSignTypeOutputDTO contractSignTypeOutputDTO = new ContractSignTypeOutputDTO();
        if (contractSignType == null) {
            return contractSignTypeOutputDTO;
        }

        contractSignTypeOutputDTO.setType(contractSignType.getType());
        contractSignTypeOutputDTO.setTypeName(contractSignType.getName());
        contractSignTypeOutputDTO.setKey(contractSignType.getKey());
        contractSignTypeOutputDTO.setSort(contractSignType.getSort());
        return contractSignTypeOutputDTO;
    }

    /**
     * 获取签约协议枚举列表
     *
     * @param wmPoiId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public List<ContractSignTypeOutputDTO> getAgreementTypeEnum(Long wmPoiId) throws TException, WmCustomerException {

        List<ContractSignTypeOutputDTO> contractSignTypeOutputDTOList = Lists.newArrayList();

        WmAgreementFormDB wmAgreementFormDB = new WmAgreementFormDB();
        wmAgreementFormDB.setWmPoiId(wmPoiId);
        List<WmAgreementPoiSign> wmAgreementPoiSignList = wmAgreementService.selectAgreementList(wmAgreementFormDB);

        Set<Integer> agreementTypeSet = Sets.newHashSet();
        for (WmAgreementPoiSign wmAgreementPoiSign : wmAgreementPoiSignList) {
            agreementTypeSet.add(wmAgreementPoiSign.getAgreementType());
        }

        agreementTypeSet.stream().sorted();
        LOGGER.info("getAgreementTypeEnum agreementTypeSet:[{}]", JSON.toJSONString(agreementTypeSet));
        for (Integer agreementTyp : agreementTypeSet) {
            AgreementTypeEnum agreementTypeEnum = AgreementTypeEnum.getByType(agreementTyp);
            if (agreementTypeEnum != null) {
                ContractSignType contractSignType = new ContractSignType();
                contractSignType.setType(agreementTyp);
                contractSignType.setName(agreementTypeEnum.getName());

                contractSignTypeOutputDTOList.add(getContractSignTypeOutputDTO(contractSignType));
            }
        }

        return contractSignTypeOutputDTOList;
    }

    /**
     * 获取签约列表-针对非本地合同情况
     *
     * @param wmPoiId
     * @param type
     * @param pageNo
     * @param pageSize
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public ContractVersionPageData getContractSignList(long wmPoiId, int type, int pageNo, int pageSize, int opUid, String opName) {
        LOGGER.info("getContractSignList wmPoiId:[{}],type:[{}],pageNo:[{}],pageSize:[{}],opUid:[{}],opName:[{}]", wmPoiId, type, pageNo, pageSize, opUid, opName);
        ContractVersionPageData contractVersionPageData = new ContractVersionPageData();
        try {

            String contractSignTypeStr = MccConfig.getContractSignType();
            List<ContractSignType> contractSignTypeList = JSON.parseArray(contractSignTypeStr, ContractSignType.class);
            Map<Integer, ContractSignType> contractSignTypeMap = Maps.newHashMap();
            for (ContractSignType contractSignType : contractSignTypeList) {
                contractSignTypeMap.put(contractSignType.getType(), contractSignType);
            }
            LOGGER.info("contractSignTypeMap:[{}]", JSON.toJSONString(contractSignTypeMap));
            ContractSignType contractSignType = contractSignTypeMap.get(type);
            if (contractSignType == null) {
                return contractVersionPageData;
            }

            List<WmCustomerContractVersionBo> wmCustomerContractVersionBoList = Lists.newArrayList();
            if (type == WmCustomerLinkageConstant.WmCustomerSpecies.PHF_CONTRACT.getCode()) { //拼好饭协议
                QueryContractsByHistoryParamDTO paramDTO = new QueryContractsByHistoryParamDTO();
                paramDTO.setWmPoiId(wmPoiId);
                QueryContractsByHistoryResDTO resDTO = tspPhfPoiContractThriftService.queryContractsByHistory(paramDTO);
                LOGGER.info("resDTO:[{}]", JSON.toJSONString(resDTO));
                if (resDTO != null && resDTO.getCode() == 0 && !CollectionUtils.isEmpty(resDTO.getContractResDTOs())) {
                    wmAgreementService.assemblyContractVersionBoList(wmCustomerContractVersionBoList, resDTO.getContractResDTOs(), contractSignType);

                    Collections.sort(wmCustomerContractVersionBoList, new Comparator<WmCustomerContractVersionBo>() {
                        @Override
                        public int compare(WmCustomerContractVersionBo o1, WmCustomerContractVersionBo o2) {
                            return o2.getStatus() - o1.getStatus();
                        }
                    });

                    LOGGER.info("wmCustomerContractVersionBoList:[{}]", JSON.toJSONString(wmCustomerContractVersionBoList));
                }
            } else if(type == WmCustomerLinkageConstant.WmCustomerSpecies.MED_DEPOSIT.getCode()) { //医药保证金协议
                ShowSecurityDepositContractResponse depositContractResponse = settledContractThriftService.queryShowSecurityDepositContract(wmPoiId);
                LOGGER.info("depositContractResponse:[{}]", JSON.toJSONString(depositContractResponse));
                if (depositContractResponse != null && depositContractResponse.getCode() == 0 && !CollectionUtils.isEmpty(depositContractResponse.getShowSecurityDepositContractDTOList())) {
                    wmAgreementService.assemblyContractVersionBoList(wmCustomerContractVersionBoList, depositContractResponse.getShowSecurityDepositContractDTOList(), contractSignType);
                    LOGGER.info("wmCustomerContractVersionBoList:[{}]", JSON.toJSONString(wmCustomerContractVersionBoList));
                }
            } else if(type == WmCustomerLinkageConstant.WmCustomerSpecies.MED_OTC_QUALITY.getCode()) { //服务质控商家协议
                PrescriptionInfoResult prescriptionInfoResult = tHealthContractQueryService.queryPrescriptionContractInfoByPoiId(wmPoiId);
                LOGGER.info("prescriptionInfoResult:[{}]", JSON.toJSONString(prescriptionInfoResult));
                if (prescriptionInfoResult != null && prescriptionInfoResult.getCode() == Code.SUCCESS && prescriptionInfoResult.getData() != null) {
                    wmAgreementService.assemblyContractVersionBoList(wmCustomerContractVersionBoList, Arrays.asList(prescriptionInfoResult.getData()), contractSignType);
                    LOGGER.info("wmCustomerContractVersionBoList:[{}]", JSON.toJSONString(wmCustomerContractVersionBoList));
                }
            } else { //配送相关
                List<WmPoiLogisticsAgreementInfo> wmPoiLogisticsAgreementInfoList = wmPoiLogisticsPoiAgreementThriftService.getPoiAgreementList(wmPoiId, contractSignType.getKey());
                LOGGER.info("wmPoiLogisticsAgreementInfoList:[{}]", JSON.toJSONString(wmPoiLogisticsAgreementInfoList));
                if (CollectionUtils.isEmpty(wmPoiLogisticsAgreementInfoList)) {
                    return contractVersionPageData;
                }
                wmAgreementService.assemblyContractVersionBoList(wmCustomerContractVersionBoList, wmPoiLogisticsAgreementInfoList, contractSignType);
            }

            // 分页
            int count = wmCustomerContractVersionBoList.size();
            contractVersionPageData.setPageNo(pageNo);
            contractVersionPageData.setPages((count - 1) / pageSize + 1);
            contractVersionPageData.setTotal(count);
            contractVersionPageData.setList(PageUtil.paging(wmCustomerContractVersionBoList, pageNo, pageSize));

        } catch (Exception e) {
            LOGGER.error("getContractSignList wmPoiId:[{}],type:[{}],pageNo:[{}],pageSize:[{}],opUid:[{}],opName:[{}]",
                wmPoiId, type, pageNo, pageSize, opUid, opName, e);
        }
        return contractVersionPageData;
    }

    @Override
    public void closeAgreementByType(long wmPoiId, long utime, int type) throws TException, WmCustomerException {
        LOGGER.info("#closeAgreementByType,wmPoiId={},utime={},type={}", wmPoiId, utime, type);
        wmAgreementSyncService.closeAgreementByType(wmPoiId, utime, type);
    }

    @Override
    public SupplyChainSignInfoBo querySupplyChainSignInfoByWmPoiId(long wmPoiId)
        throws TException, WmCustomerException {
        AssertUtil.assertLongMoreThan0(wmPoiId, "门店ID");
        return wmSignInfoForBService.querySupplyChainSignInfoByWmPoiId(wmPoiId);
    }

    @Override
    public DeliveryAreaSignInfoBo queryDeliveryAreaSignInfoByWmCustomerIdAndBatchStatus(Integer wmCustomerId,
                                                                                        List<Long> batchIdList,
                                                                                        Integer batchStatus)
        throws TException, WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户ID");
        AssertUtil.assertCollectionNotEmpty(batchIdList, "batchId列表");
        return wmSignInfoForBService.queryDeliveryAreaSignInfoByWmCustomerIdAndBatchStatus(wmCustomerId,
            batchIdList,
            batchStatus);
    }

    /**
     * 获取签约列表-针对非本地合同情况
     * Throws: ref 406
     * WmCustomerException –
     * TException
     *
     * @param customerId
     * @param type
     * @param pageNo
     * @param pageSize
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public ContractVersionPageData getContractSignedList(long customerId, int type, int pageNo, int pageSize, int opUid,
                                                         String opName) throws WmCustomerException, TException {

        return null;
    }
}
