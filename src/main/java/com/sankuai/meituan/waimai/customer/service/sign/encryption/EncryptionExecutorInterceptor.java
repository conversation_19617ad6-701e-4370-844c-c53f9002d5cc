package com.sankuai.meituan.waimai.customer.service.sign.encryption;

import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.commons.collections.CollectionUtils;
import com.sankuai.meituan.waimai.customer.annotation.Encryption;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractEncryptionRecordDB;
import com.sankuai.meituan.waimai.customer.constant.EncryptionTypeConstant;

/**
 * Created by lixuepeng on 2021/8/18
 */
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),})
public class EncryptionExecutorInterceptor implements Interceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(EncryptionExecutorInterceptor.class);

    private EncryptionService encryptionService;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (!MccConfig.isEncryptionSwitch()) {//加密总开关
            return invocation.proceed();
        }

        long beginTime = System.currentTimeMillis();
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];

        //获取入参
        Object parameterObject = invocation.getArgs()[1];
        if (parameterObject == null) {//SQL入参不为POJO对象
            return invocation.proceed();
        }

        //获取目标SQL
        BoundSql boundSql = ms.getBoundSql(parameterObject);
        if (!isTargetTableName(boundSql)) {//不是目标表名
            return invocation.proceed();
        }

        //获取注解
        Encryption encryption = parameterObject.getClass().getAnnotation(Encryption.class);
        if (encryption == null) {//对象无注解
            return invocation.proceed();
        }

        //执行结果
        Object result;
        //获取操作类型
        SqlCommandType sqlCommandType = ms.getSqlCommandType();
        //获取入参对象各字段信息
        MetaObject metaParameterObject = MetaObject.forObject(parameterObject);
        //获取该对象需要加密的字段
        String[] fields = encryption.fields();
        //获取该对象类型
        int recordType = fitRecordType(encryption, boundSql);
        //根据注解信息抽取加密字段并进行加密处理
        List<WmEcontractEncryptionRecordDB> encryptionRecordDBList = new ArrayList<>();
        //旧值Map,用于处理完成后回填旧值
        Map<String, String> parameterIndexOldValueMap = new HashMap<>();

        if(encryptionService == null) {
            encryptionService = (EncryptionService) SpringContext.getBean("EncryptionService");
        }

        for (String toEncryptField : fields) {
            Object toEncryptObject = metaParameterObject.getValue(toEncryptField);
            if (toEncryptObject != null && toEncryptObject instanceof String) {
                String toEncryptValue = (String) toEncryptObject;

                //对于更新类型的操作or指定id操作的操作，先将加密表的对应信息逻辑删除
                long recordId = metaParameterObject.getValue("id") == null ? 0 : (long) metaParameterObject.getValue("id");
                if (sqlCommandType == SqlCommandType.UPDATE || recordId > 0) {
                    encryptionService.deleteEncryptionRecord(recordType, recordId, toEncryptField);
                }

                //对文本进行加密，得到加密后文本列表
                List<WmEcontractEncryptionRecordDB> subEncryptionRecordDBList = encryptionService.queryEncrypttionRecordList(toEncryptField, toEncryptValue);

                //MCC-停写明文 && 加密后数据不为空（空场景说明原字段为空或者加密失败）
                if (MccConfig.isFilterOriginValue() && CollectionUtils.isNotEmpty(subEncryptionRecordDBList)) {
                    parameterIndexOldValueMap.put(toEncryptField, toEncryptValue);
                    metaParameterObject.setValue(toEncryptField, "");
                    if (boundSql.hasAdditionalParameter(toEncryptField)) {
                        boundSql.setAdditionalParameter(toEncryptField, "");
                    }
                }

                //待插入加密表记录
                encryptionRecordDBList.addAll(subEncryptionRecordDBList);
            }
        }

        //执行SQL获取主键id
        result = invocation.proceed();
        long recordId =  (long) metaParameterObject.getValue("id");
        //将加密信息保存至加密表
        encryptionService.doSaveEncryptionRecords(recordType, recordId, encryptionRecordDBList);

        long endTime = System.currentTimeMillis();
        LOGGER.debug("EncryptionExecutorInterceptor#intercept cost:{} ms", endTime - beginTime);

        //处理完成后回填原值
        for (String toEncryptField : fields) {
            if (parameterIndexOldValueMap.containsKey(toEncryptField)) {
                String oldValue = parameterIndexOldValueMap.get(toEncryptField);
                metaParameterObject.setValue(toEncryptField, oldValue);
                if (boundSql.hasAdditionalParameter(toEncryptField)) {
                    boundSql.setAdditionalParameter(toEncryptField, oldValue);
                }
            }
        }

        return result;
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        } else {
            return target;
        }
    }

    @Override
    public void setProperties(Properties properties) {

    }

    private int fitRecordType(Encryption encryption, BoundSql boundSql) {
        //针对线上表，进行记录类型适配
        if (encryption.recordType() == EncryptionTypeConstant.TEMP_CONTRACT_SIGN_RECORD
                && boundSql.getSql().contains("wm_templet_contract_sign_audited")) {
            return EncryptionTypeConstant.TEMP_CONTRACT_SIGN_AUDITED_RECORD;
        }
        return encryption.recordType();
    }

    private boolean isTargetTableName(BoundSql boundSql) {
        try {
            List<String> tableNameList = Arrays.asList(MccConfig.encryptionTableNameList().split(","));
            if (CollectionUtils.isNotEmpty(tableNameList)) {
                for (String tableName : tableNameList) {
                    if (boundSql.getSql().contains(tableName)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("EncryptionExecutorInterceptor#isTargetTableName 判断是否目标表名异常 boundSql:{}", boundSql, e);
        }
        return false;
    }
}
