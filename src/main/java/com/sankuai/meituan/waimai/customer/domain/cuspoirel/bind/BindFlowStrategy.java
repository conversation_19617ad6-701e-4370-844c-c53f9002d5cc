package com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind;

import com.sankuai.meituan.waimai.customer.domain.cuspoirel.CustomerPoiRelStrategyBean;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.poirel.flow.CustomerPoiRelFlowEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240118
 * @desc 绑定流程策略定义
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BindFlowStrategy {

    /**
     * 门店ID列表
     */
    private Set<Long> wmPoiIdSet;

    /**
     * 绑定流程枚举
     */
    private CustomerPoiRelFlowEnum flowEnum;

    /**
     * 流程策略上下文
     */
    private BindStrategy bindStrategy;

    /**
     * 策略bean名称定义
     */
    private CustomerPoiRelStrategyBean customerPoiRelStrategyBean;
}
