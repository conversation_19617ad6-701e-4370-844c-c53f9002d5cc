package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.IntResult;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketSyncDto;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyRecord;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyDao;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyRecordDao;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerRecordOpTypeEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerStepEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyAddDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WmCustomerOwnerApplyService {

    @Autowired
    private WmCustomerOwnerApplyDao wmCustomerOwnerApplyDao;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmEmployeeService wmEmployeeService;

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    @Autowired
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    @Autowired
    private WmCustomerOwnerApplyRecordDao wmCustomerOwnerApplyRecordDao;

    /**
     * 任务驳回对应状态-任务系统
     */
    private static final Integer TICKET_SUC_CODE = 3;

    /**
     * 直接流转到审核通过
     * 
     * @param applyAddDTO
     * @param wmCustomerDB
     * @param ownerInWbMerchantOrgFlag
     */
    public WmCustomerOwnerApply createCustomerOwnerApply(CustomerOwnerApplyAddDTO applyAddDTO,
            WmCustomerDB wmCustomerDB, Boolean ownerInWbMerchantOrgFlag) throws WmCustomerException {

        log.info("createCustomerOwnerApply applyAddDTO:{},wmCustomerDB:{}", applyAddDTO, wmCustomerDB);
        // 创建申请单
        WmCustomerOwnerApply apply = wmCustomerOwnerApplyDao.insert(applyAddDTO, wmCustomerDB,
                ownerInWbMerchantOrgFlag);
        // 新增操作记录
        addCreateOwnerApplyRecord(apply, applyAddDTO.getUid());

        // 客户责任人为空 或 客户责任人在外包商管理组及其以下组织节点 则直接审批通过
        if (wmCustomerDB.getOwnerUid() == null || wmCustomerDB.getOwnerUid() == 0 || ownerInWbMerchantOrgFlag) {
            // 更新申请记录状态为完成
            wmCustomerOwnerApplyDao.updateStatusById(apply.getId(), CustomerOwnerApplyStatusEnum.COMPLETE.getCode(),
                    apply.getStatus());
            WmEmploy opWmEmploy = wmEmployeeService.getWmEmployById(apply.getApplyUid());
            // 修改客户责任人为申请人
            wmCustomerService.changeCustomerOwnerByApply(apply, apply.getApplyUid(),
                    opWmEmploy == null ? "" : opWmEmploy.getName());
            // 添加客户责任人申请变更的操作日志
            addAuditSucRecord(apply, 0);

            return apply;
        }
        apply.setStepId(CustomerOwnerStepEnum.CREATE_TO_AUDIT.getCode());
        // 创建本地审核表
        WmCustomerOwnerApplyAudit applyAudit = wmCustomerOwnerApplyAuditDao.insert(apply);
        // 发起任务审批任务
        Integer ticketID = createCustomerOwnerApplyTask(apply, wmCustomerDB);
        // 更新任务ID到审核记录
        wmCustomerOwnerApplyAuditDao.updateTicketIdById(ticketID, applyAudit.getId());
        return apply;
    }

    /**
     * 更新客户类型
     * 
     * @param customerId
     * @param newCustomerRealType
     */
    public void updateCustomerRealTypeInChange(Integer customerId, Integer newCustomerRealType) {
        // 根据客户ID查询是否存在申请记录
        List<WmCustomerOwnerApply> applyList = wmCustomerOwnerApplyDao.listByCustomerId(customerId);
        if (CollectionUtils.isEmpty(applyList)) {
            return;
        }
        List<Integer> applyIdList = applyList.stream().map(WmCustomerOwnerApply::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyIdList)) {
            return;
        }
        log.info("updateCustomerRealTypeInChange,客户类型变更，开始更新责任人申请的客户类型,applyIdList={},newCustomerRealType={}",
                JSON.toJSONString(applyIdList), newCustomerRealType);
        wmCustomerOwnerApplyDao.updateCustomerRealTypeByIds(applyIdList, newCustomerRealType);
        log.info("updateCustomerRealTypeInChange,客户类型变更，责任人申请的客户类型更新完成,applyIdList={},newCustomerRealType={}",
                JSON.toJSONString(applyIdList), newCustomerRealType);

    }

    /**
     * 客户责任人变更时驳回申请
     * 
     * @param customerId
     */
    public void rejectOwnerApplyWhenOwnerChange(Integer customerId) throws WmCustomerException {
        // 查询客户当前是否有申请记录
        WmCustomerOwnerApply apply = wmCustomerOwnerApplyDao.getDoingApplyByCustomerId(customerId);
        if (apply == null) {
            return;
        }
        log.info("rejectOwnerApplyWhenOwnerChange,客户责任人变更开始驳回客户申请,customerId={}", customerId);
        // 同步任务系统-终止任务
        syncApply2CancelOrReject(apply, apply.getApplyUid(), CustomerOwnerApplyStatusEnum.REJECT.getCode());
    }

    /**
     * 撤回申请
     * 1 通知任务系统撤销申请
     * 2 修改申请记录为已取消/驳回
     * 3 将审核记录修改为无效
     * 
     * @param apply
     * @param opUid
     * @param newStatus
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncApply2CancelOrReject(WmCustomerOwnerApply apply, Integer opUid, Integer newStatus)
            throws WmCustomerException {
        try {
            // 查询审批记录
            WmCustomerOwnerApplyAudit audit = wmCustomerOwnerApplyAuditDao.getByApplyId(apply.getId());
            if (audit == null) {
                log.info("syncApply2Cancel,未查询到审核记录不再执行撤回操作,applyId={}", apply.getId());
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "撤回申请异常");
            }
            // 将审核记录修改为无效
            wmCustomerOwnerApplyAuditDao.update2UnValidById(audit.getId());
            // 将申请记录修改为已取消
            wmCustomerOwnerApplyDao.updateStatusById(apply.getId(), newStatus, apply.getStatus());
            // 通知任务系统撤销申请
            IntResult result = syncTicketStatus(opUid, audit);
            if (result == null || result.getValue() <= 0) {
                log.error("syncApply2CancelOrReject,操作撤销客户责任人申请发生异常,applyId={}", apply.getId());
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "任务系统撤回申请异常");
            }
            log.info("syncApply2Cancel,撤回申请成功,applyId={}", apply.getId());
            // 记录操作日志
            if (newStatus == CustomerOwnerApplyStatusEnum.CANCEL.getCode()) {
                addCancelApplyRecord(apply, opUid);
            } else if (newStatus == CustomerOwnerApplyStatusEnum.REJECT.getCode()) {
                addOwnerChangeAutoRejectRecord(apply);
            }

        } catch (Exception e) {
            log.error("syncApply2CancelOrReject,取消或驳回客户责任人申请发生异常,applyId={}", apply.getId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "撤回申请异常");
        }

    }

    /**
     * 创建客户责任人申请任务
     *
     * @param apply
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private Integer createCustomerOwnerApplyTask(WmCustomerOwnerApply apply, WmCustomerDB wmCustomerDB)
            throws WmCustomerException {

        try {
            WmTicketDto ticketDto = getWmTicketDto(apply, wmCustomerDB);
            log.info("createCustomerOwnerApplyTask,创建客户责任人申请任务,ticketDto={}", JSON.toJSONString(ticketDto));

            Integer ticketId = wmCrmTicketThriftServiceAdapter.createTicket(ticketDto);
            return ticketId;
        } catch (Exception e) {
            log.error("createCustomerOwnerApplyTask,创建客户责任人申请任务发生异常,customerId={},applyId={}", wmCustomerDB.getId(),
                    apply.getId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "创建客户责任人申请任务发生异常");
        }
    }

    /**
     * 通知任务系统撤销申请
     * 
     * @param opUid
     * @param audit
     * @return
     * @throws WmSchCantException
     */
    private IntResult syncTicketStatus(Integer opUid, WmCustomerOwnerApplyAudit audit) throws WmSchCantException {
        WmTicketSyncDto syncDto = new WmTicketSyncDto();
        syncDto.setTicketId(audit.getTaskId());
        syncDto.setStatus(TICKET_SUC_CODE);
        syncDto.setStage(CustomerOwnerApplyStatusEnum.REJECT.getCode());
        syncDto.setOpUid(opUid);
        WmEmploy wmEmploy = wmEmployeeService.getWmEmployById(opUid);
        syncDto.setOpName(wmEmploy == null ? "" : wmEmploy.getName());
        syncDto.setWmTicketOperationId(0);
        syncDto.setWmTicketOperationName("撤回申请");
        log.info("syncApply2Cancel,开始执行撤回操作,syncDto={}", JSON.toJSONString(syncDto));
        IntResult result = wmCrmTicketThriftServiceAdapter.syncStatus(syncDto);
        return result;
    }

    /**
     * 构建任务信息
     *
     * @param apply
     * @param wmCustomerDB
     * @return
     */
    private WmTicketDto getWmTicketDto(WmCustomerOwnerApply apply, WmCustomerDB wmCustomerDB) {
        WmTicketDto ticketDto = new WmTicketDto();
        // 任务类型ID(任务系统审批流ID)
        ticketDto.setType(MccCustomerConfig.getCustomerOwnerApplyTicketFlowID());
        // 任务标题
        ticketDto.setTitle("客户责任人申请审核");
        // 任务审批人UID
        ticketDto.setOpUid(wmCustomerDB.getOwnerUid());
        WmEmploy wmEmploy = wmEmployeeService.getWmEmployById(wmCustomerDB.getOwnerUid());
        ticketDto.setOpUname(wmEmploy == null ? "" : wmEmploy.getName());
        // 任务一级状态码 1:待处理 2:进行中 3:已完成 4:已失效 5:已终止
        ticketDto.setStatus(1);
        // 任务二级状态码(业务方自定义)
        ticketDto.setStage(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        // 创建人UID（0表示系统）
        ticketDto.setCreateUid(apply.getApplyUid());
        // 任务创建时间
        ticketDto.setUnixCtime(TimeUtil.unixtime());
        // 任务更新时间
        ticketDto.setUnixUtime(TimeUtil.unixtime());
        // 设置审批流key自定义审批人
        Map<String, String> processVariablesMap = new HashMap<>();
        // 流程发起人uid
        processVariablesMap.put("uid", String.valueOf(apply.getApplyUid()));
        // 任务系统分配,审批流ID
        processVariablesMap.put("processRoutingKey", MccCustomerConfig.getCustomerOwnerApplyTicketFlowID().toString());

        Map<Integer, String> targetUidMap = new HashMap<>();
        targetUidMap.put(MccCustomerConfig.getCustomerOwnerApplyTicketNodeID(), wmCustomerDB.getOwnerUid().toString());
        processVariablesMap.put("targetUid", JSONObject.toJSONString(targetUidMap));
        ticketDto.setProcessVariables(processVariablesMap);
        // 标识业务唯一标识key
        ticketDto.setBusinessKey(apply.getId().longValue());
        // 任务优先级 1:普通 2:重要 3:无
        ticketDto.setTicketPriority(0);
        Map<String,Integer> extParamsMap = new HashMap<>();
        extParamsMap.put("applyId",apply.getId());
        ticketDto.setParams(JSON.toJSONString(extParamsMap));

        return ticketDto;
    }

    /**
     * 创建客户责任人申请-添加操作记录
     * 
     * @param apply
     * @param opUid
     */
    private void addCreateOwnerApplyRecord(WmCustomerOwnerApply apply, Integer opUid) {

        try {
            WmCustomerOwnerApplyRecord record = new WmCustomerOwnerApplyRecord();
            record.setApplyId(apply.getId().longValue());
            record.setCustomerId(apply.getCustomerId().longValue());
            record.setOpUid(opUid);
            record.setRemark("");
            record.setOpType(CustomerOwnerRecordOpTypeEnum.CREATE.getCode());
            record.setModuleId((int)WmCustomerOplogBo.OpModuleType.CUSTOMER.getType());

            String curOwnerName = "";
            if (apply.getCustomerOwnerUid() == null || apply.getCustomerOwnerUid() == 0) {
                curOwnerName = "无";
            } else {
                WmEmploy wmEmploy = wmEmployeeService.getWmEmployById(apply.getCustomerOwnerUid());
                curOwnerName = String.format("%s（%s）", wmEmploy == null ? "" : wmEmploy.getName(),
                        wmEmploy == null ? "" : wmEmploy.getMisId());
            }
            record.setContent(String.format("提交客户责任人申请\n申请编号：%s\n申请客户id:%s\n申请进度：申请中\n当前客户责任人：%s", apply.getId(),
                    apply.getMtCustomerId(), curOwnerName));
            wmCustomerOwnerApplyRecordDao.insertOwnerApplyRecord(record);
        } catch (Exception e) {
            log.error("addCreateOwnerApplyRecord,添加客户责任人申请记录发生异常,applyId={},opUid={}", apply.getId(), opUid, e);
        }
    }

    /**
     * 创建客户责任人申请-添加操作记录
     *
     * @param apply
     * @param opUid
     */
    private void addAuditSucRecord(WmCustomerOwnerApply apply, Integer opUid) {
        try {
            WmCustomerOwnerApplyRecord record = new WmCustomerOwnerApplyRecord();
            record.setApplyId(apply.getId().longValue());
            record.setCustomerId(apply.getCustomerId().longValue());
            record.setOpUid(opUid);
            record.setRemark("");
            record.setOpType(CustomerOwnerRecordOpTypeEnum.EDIT.getCode());
            record.setModuleId((int)WmCustomerOplogBo.OpModuleType.CUSTOMER.getType());
            String newOwnerUName = "";
            if (apply.getApplyUid() == null || apply.getApplyUid() == 0) {
                newOwnerUName = "无";
            } else {
                WmEmploy wmEmploy = wmEmployeeService.getWmEmployById(apply.getApplyUid());
                newOwnerUName = String.format("%s（%s）", wmEmploy == null ? "" : wmEmploy.getName(),
                        wmEmploy == null ? "" : wmEmploy.getMisId());
            }
            record.setContent(String.format("通过客户责任人申请 \n申请编号：%s\n申请客户id: %s\n申请进度：申请通过\n新客户责任人人：%s", apply.getId(),
                    apply.getMtCustomerId(), newOwnerUName));
            wmCustomerOwnerApplyRecordDao.insertOwnerApplyRecord(record);
        } catch (Exception e) {
            log.error("addAuditSucRecord,添加客户责任人申请记录发生异常,applyId={},opUid={}", apply.getId(), opUid, e);
        }
    }

    /**
     * 创建客户责任人申请-添加操作记录
     *
     * @param apply
     * @param opUid
     */
    private void addCancelApplyRecord(WmCustomerOwnerApply apply, Integer opUid) {
        try {
            WmCustomerOwnerApplyRecord record = new WmCustomerOwnerApplyRecord();
            record.setApplyId(apply.getId().longValue());
            record.setCustomerId(apply.getCustomerId().longValue());
            record.setOpUid(opUid);
            record.setRemark("");
            record.setOpType(CustomerOwnerRecordOpTypeEnum.EDIT.getCode());
            record.setModuleId((int)WmCustomerOplogBo.OpModuleType.CUSTOMER.getType());
            record.setContent(String.format("客户责任人申请任务撤回 \n申请编号：%s\n申请客户id: %s\n申请进度：已取消", apply.getId(),
                    apply.getMtCustomerId()));
            wmCustomerOwnerApplyRecordDao.insertOwnerApplyRecord(record);
        } catch (Exception e) {
            log.error("addCancelApplyRecord,添加客户责任人申请记录发生异常,applyId={},opUid={}", apply.getId(), opUid, e);
        }
    }


    /**
     * 客户责任人变更自动驳回申请操作日志
     * 
     * @param apply
     */
    private void addOwnerChangeAutoRejectRecord(WmCustomerOwnerApply apply) {
        try {
            WmCustomerOwnerApplyRecord record = new WmCustomerOwnerApplyRecord();
            record.setApplyId(apply.getId().longValue());
            record.setCustomerId(apply.getCustomerId().longValue());
            record.setOpUid(0);
            record.setRemark("");
            record.setOpType(CustomerOwnerRecordOpTypeEnum.EDIT.getCode());
            record.setModuleId((int)WmCustomerOplogBo.OpModuleType.CUSTOMER.getType());
            record.setContent(String.format("责任人变更自动驳回客户责任人申请 \n申请编号：%s\n申请客户id: %s\n申请进度：申请驳回", apply.getId(),
                    apply.getMtCustomerId()));
            wmCustomerOwnerApplyRecordDao.insertOwnerApplyRecord(record);
        } catch (Exception e) {
            log.error("addCancelApplyRecord,添加客户责任人申请记录发生异常,applyId={}", apply.getId(), e);
        }
    }

    public void autoRejectOwnerApply(Integer customerId) throws WmCustomerException {
        log.info("审批超时自动驳回-autoRejectOwnerApply:{}",customerId);
        // double check 当前申请单是否为申请中
        WmCustomerOwnerApply doingApply = wmCustomerOwnerApplyDao.getDoingApplyByCustomerId(customerId);
        if(doingApply == null || doingApply.getStatus() != CustomerOwnerApplyStatusEnum.AUDITING.getCode()){
            return;
        }
        // 修改审核记录为无效
        wmCustomerOwnerApplyAuditDao.update2UnValidByApplyId(doingApply.getId());
        // 修改申请记录为 申请驳回
        wmCustomerOwnerApplyDao.updateStatusById(doingApply.getId(), CustomerOwnerApplyStatusEnum.REJECT.getCode(),
                doingApply.getStatus());
        // 同步任务系统-终止任务
        syncApply4AutoReject(doingApply);
        // 记录操作日志
        addAutoRejectApplyRecord(doingApply);
    }

    public void syncApply4AutoReject(WmCustomerOwnerApply apply) throws WmCustomerException {
        // 查询审批记录
        WmCustomerOwnerApplyAudit audit = wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(apply.getId());
        if (audit == null) {
            log.info("syncApply4AutoReject,未查询到审核记录不再执行同步任务系统操作,applyId={}", apply.getId());
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "撤回申请异常");
        }
        // 通知任务系统撤销申请
        IntResult result = syncStatus4AutoReject(audit);
        if (result == null || result.getValue() <= 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "任务系统撤回申请异常");
        }
        log.info("syncApply4AutoReject,撤回申请成功,applyId={}", apply.getId());
    }

    /**
     * 超时自动驳回客户责任人申请操作记录
     * @param apply
     * @param opUid
     */
    private void addAutoRejectApplyRecord(WmCustomerOwnerApply apply){
        try {
            WmCustomerOwnerApplyRecord record = new WmCustomerOwnerApplyRecord();
            record.setApplyId(apply.getId().longValue());
            record.setCustomerId(apply.getCustomerId().longValue());
            record.setOpUid(0);
            record.setRemark("超时系统自动驳回");
            record.setOpType(CustomerOwnerRecordOpTypeEnum.EDIT.getCode());
            record.setModuleId((int)WmCustomerOplogBo.OpModuleType.CUSTOMER.getType());
            record.setContent(String.format("超时自动驳回客户责任人申请 \n申请编号：%s\n申请客户id: %s\n申请进度：申请驳回", apply.getId(),
                    apply.getMtCustomerId()));
            wmCustomerOwnerApplyRecordDao.insertOwnerApplyRecord(record);
        } catch (Exception e) {
            log.error("addCancelApplyRecord,添加客户责任人申请记录发生异常,applyId={},opUid={}", apply.getId(), e);
        }
    }


    private IntResult syncStatus4AutoReject(WmCustomerOwnerApplyAudit audit) throws WmCustomerException {
        WmTicketSyncDto syncDto = new WmTicketSyncDto();
        syncDto.setTicketId(audit.getTaskId());
        syncDto.setStatus(TICKET_SUC_CODE);
        syncDto.setStage(CustomerOwnerApplyStatusEnum.REJECT.getCode());
        syncDto.setOpUid(0);
        syncDto.setOpName("system");
        syncDto.setWmTicketOperationId(0);
        syncDto.setWmTicketOperationName("超时自动驳回客户责任人申请");
        try{
            log.info("超时自动驳回客户责任人申请-syncStatus4AutoReject,syncDto={}", JSON.toJSONString(syncDto));
            return wmCrmTicketThriftServiceAdapter.syncStatus(syncDto);
        }catch (WmSchCantException e){
            log.error("超时自动驳回客户责任人申请异常-syncStatus4AutoReject,syncDto={}",JSON.toJSONString(syncDto),e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "同步驳回状态到任务系统异常");
        }

    }

}
