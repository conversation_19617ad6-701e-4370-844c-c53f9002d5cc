package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.CustomerKpBusinessService;
import com.sankuai.meituan.waimai.customer.service.kp.DifferentCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.customer.service.sc.annotate.AuditField;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20240417
 * @desc 信息暂存
 */
@Service
@Slf4j
public class KpSignerTempStoreAction extends KpSignerAbstractAction {

    @Autowired
    private CustomerKpBusinessService customerKpBusinessService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;

    /**
     * 信息暂存
     *
     * @param fromState      原状态
     * @param toState        新状态
     * @param eventEnum      事件
     * @param context        上下文
     * @param kpSignerBaseSM 状态机
     */
    @Override
    public void execute(KpSignerStateMachine fromState, KpSignerStateMachine toState,
                        KpSignerEventEnum eventEnum, KpSignerStatusMachineContext context,
                        KpSignerBaseSM kpSignerBaseSM) {
        WmCustomerKp wmCustomerKp = context.getWmCustomerKp();
        log.info("KpSignerInitCreateAction.execute,开始执行初始化创建KP签约人事件,customerId={},wmCustomerKp={}", wmCustomerKp.getCustomerId(), JSON.toJSONString(wmCustomerKp));
        try {
            //初始化创建KP
            customerKpBusinessService.updateKp2TempStore(wmCustomerKp);
            List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(context.getOldCustomerKp(), context.getWmCustomerKp(), differentCustomerKpService.getKpDiffFieldsMap());
            //新增KP签约人添加操作记录
            wmCustomerKpLogService.updateKpLog(context.getWmCustomerKp(), diffCellBos, context.getOpUid(), context.getOpUName());
        } catch (Exception e) {
            log.error("KpSignerInitCreateAction.insertCustomerKp,KP签约人暂存异常,wmCustomerKp={}", JSON.toJSONString(wmCustomerKp), e);
            throw new StatusMachineException("KP签约人暂存异常");
        }

    }
}
