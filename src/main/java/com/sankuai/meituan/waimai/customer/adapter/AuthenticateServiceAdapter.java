package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.authenticate.client.service.AuthenticateService;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateAssertBatchOperationRequest;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateAssertRequest;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateQueryRequest;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateAssertBatchOperationResponse;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateAssertResponse;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据权限系统服务接口适配器
 * <AUTHOR>
 * @date 2023/08/30
 * @email <EMAIL>
 */
@Slf4j
@Service
public class AuthenticateServiceAdapter {

    @Autowired
    private AuthenticateService authenticateService;

    /**
     * 数据范围查询-DSL语句查询
     * @param queryRequest 请求入参
     * @return AuthenticateQueryResponse
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public AuthenticateQueryResponse getAuthQueryResult(AuthenticateQueryRequest queryRequest) throws WmSchCantException {
        try {
            log.info("[AuthenticateServiceAdapter.getAuthQueryResult] queryRequest = {}", JSONObject.toJSONString(queryRequest));
            AuthenticateQueryResponse response = authenticateService.authQuery(queryRequest);
            log.info("[AuthenticateServiceAdapter.getAuthQueryResult] response = {}", JSONObject.toJSONString(response));

            return response;
        } catch (Exception e) {
            log.error("[AuthenticateServiceAdapter.getAuthQueryResult] Exception. queryRequest = {}", JSONObject.toJSONString(queryRequest), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
    }

    /**
     * 批量查询操作鉴权结果
     * @param assertRequest 请求入参
     * @return AuthenticateAssertBatchOperationResponse
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public AuthenticateAssertBatchOperationResponse getAuthAssertResultByBatch(AuthenticateAssertBatchOperationRequest assertRequest) throws WmSchCantException {
        try {
            log.info("[AuthenticateServiceAdapter.getAuthAssertResultByBatch] assertRequest = {}", JSONObject.toJSONString(assertRequest));
            AuthenticateAssertBatchOperationResponse response = authenticateService.authAssertBatchOperation(assertRequest);
            log.info("[AuthenticateServiceAdapter.getAuthAssertResultByBatch] response = {}", JSONObject.toJSONString(response));

            return response;
        } catch (Exception e) {
            log.error("[AuthenticateServiceAdapter.getAuthAssertResultByBatch] Exception. assertRequest = {}", JSONObject.toJSONString(assertRequest), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
    }

    /**
     * 单个查询操作鉴权结果
     * @param assertRequest 请求入参
     * @return AuthenticateAssertBatchOperationResponse
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public AuthenticateAssertResponse getAuthAssertResult(AuthenticateAssertRequest assertRequest) throws WmSchCantException {
        try {
            log.info("[AuthenticateServiceAdapter.getAuthAssertResult] assertRequest = {}", JSONObject.toJSONString(assertRequest));
            AuthenticateAssertResponse response = authenticateService.authAssert(assertRequest);
            log.info("[AuthenticateServiceAdapter.getAuthAssertResult] response = {}", JSONObject.toJSONString(response));

            return response;
        } catch (Exception e) {
            log.error("[AuthenticateServiceAdapter.getAuthAssertResult] Exception. assertRequest = {}", JSONObject.toJSONString(assertRequest), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
    }

}
