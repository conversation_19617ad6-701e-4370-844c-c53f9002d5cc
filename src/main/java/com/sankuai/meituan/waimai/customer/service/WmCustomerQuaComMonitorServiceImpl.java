package com.sankuai.meituan.waimai.customer.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.service.customer.monitor.QuaComMonitorService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerAuditStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.CustomerQuaComCheckDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.PoiLabelChangeDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.PoiQuaComCheckDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.monitor.CustomerQuaComMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20231031
 * @desc 客户资质共用场景标签监控
 */
@Slf4j
@Service
public class WmCustomerQuaComMonitorServiceImpl implements CustomerQuaComMonitorService {

    @Autowired
    private QuaComMonitorService quaComMonitorService;

    /**
     * 客户门店关系变更：绑定/解绑 监控门店的资质共用标签
     *
     * @param poiQuaComCheckDTO
     * @return
     */
    @Override
    public String monitorPoiQuaComTagByRelChange(PoiQuaComCheckDTO poiQuaComCheckDTO) {
        log.info("monitorPoiQuaComTagByRelChange,poiQuaComCheckDTO={}", JSON.toJSONString(poiQuaComCheckDTO));
        if (poiQuaComCheckDTO == null) {
            return null;
        }
        if (poiQuaComCheckDTO.getCustomerId() == null || poiQuaComCheckDTO.getWmPoiId() == null
                || poiQuaComCheckDTO.getValid() == null
                || poiQuaComCheckDTO.getStatus() == null) {
            return null;
        }
        return quaComMonitorService.checkBindOrUnBindPoiQuaComTag(poiQuaComCheckDTO);
    }

    /**
     * 客户类型由美食城修改为非美食城校验是否有资质共用标签
     *
     * @param customerQuaComCheckDTO
     * @return
     */
    @Override
    public String checkCustomerQuaComTagOnTypeChange(CustomerQuaComCheckDTO customerQuaComCheckDTO) {
        log.info("checkCustomerQuaComTagOnTypeChange,customerQuaComCheckDTO={}", JSON.toJSONString(customerQuaComCheckDTO));
        if (customerQuaComCheckDTO == null) {
            return null;
        }
        Integer oldCustomerRealType = customerQuaComCheckDTO.getCustomerRealTypeOld();
        Integer newCustomerRealType = customerQuaComCheckDTO.getCustomerRealTypeNew();
        Integer auditStatus = customerQuaComCheckDTO.getAuditStatus();
        if (oldCustomerRealType == newCustomerRealType
                || oldCustomerRealType != CustomerRealTypeEnum.MEISHICHENG.getValue()
                || auditStatus != CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode()) {
            return null;
        }
        return quaComMonitorService.checkCustomerQuaComTag(customerQuaComCheckDTO.getCustomerId());
    }

    /**
     * 门店标签变更对是否子门店字段监控
     *
     * @param poiLabelChangeDTO
     * @return
     */
    @Override
    public String checkChildPoiFlag(PoiLabelChangeDTO poiLabelChangeDTO) {
        log.info("checkChildPoiFlag,poiLabelChangeDTO={}", JSON.toJSONString(poiLabelChangeDTO));
        if (poiLabelChangeDTO == null) {
            return null;
        }
        Long wmPoiId = poiLabelChangeDTO.getWmPoiId();
        Integer labelId = poiLabelChangeDTO.getLabelId();
        if (wmPoiId == null || labelId == null || wmPoiId <= 0) {
            return null;
        }
        //变更标签不是子门店标签则不关心
        if (!MccCustomerConfig.getSubPoiTagId().contains(labelId)) {
            return null;
        }
        return quaComMonitorService.checkChildPoiFlagWithEs(wmPoiId, labelId);
    }

}
