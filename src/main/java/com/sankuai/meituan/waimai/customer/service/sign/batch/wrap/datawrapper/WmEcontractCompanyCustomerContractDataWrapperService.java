package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.CommonCellBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.companycustomer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBusinessCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

/**
 * 企客合同数据打包
 */
@Service
@Slf4j
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.BUSINESS_CUSTOMER_E_CONTRACT)
public class WmEcontractCompanyCustomerContractDataWrapperService implements IWmEcontractDataWrapperService {

    private static final String SUPPORT_INSURED = "supportInsured";
    private static final String TEMPLET_NAME_MAIN = "business_customer_contract_main";
    private static final String TEMPLET_NAME_ATTACHMENT_1 = "business_customer_contract_attachment1";
    private static final String TEMPLET_NAME_ATTACHMENT_2 = "business_customer_contract_attachment2";
    private static final String TEMPLET_NAME_ATTACHMENT_3 = "business_customer_contract_attachment3";
    private static final String MONTH_SETTLE_CODE = "2";

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        boolean generatePdf = contextBo.isGeneratePdf();
        if(generatePdf){
            return warpNoGeneratePdf(contextBo);
        }
        return warpGeneratePdf(contextBo);

    }

    private List<PdfContentInfoBo> warpGeneratePdf(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT);
        Map<String, String> fullInfoMap = generatePdfObject(contextBo, taskBo);
        //是否使用新的PDF模板
        if (isNewPdfPattern(taskBo)) {
            PdfContentInfoBo newPdfInfoBo = new PdfContentInfoBo();
            // 指定模版
            newPdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("BUSINESS_CUSTOMER_TEMPLATE_ID", 21));
            // 传0的话则默认为当前已发布的版本
            newPdfInfoBo.setPdfTemplateVersion(isLongDistancePdfPattern(taskBo) ? 0
                    : ConfigUtilAdapter.getInt("BUSINESS_CUSTOMER_TEMPLATE_VERSION", 12));
            newPdfInfoBo.setPdfMetaContent(fullInfoMap);
            newPdfInfoBo.setPdfBizContent(Lists.newArrayList());
            return Arrays.asList(newPdfInfoBo);
        }

        List<PdfContentInfoBo> result = Lists.newArrayList();

        //主合同
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(TEMPLET_NAME_MAIN);
        pdfInfoBo.setPdfMetaContent(fullInfoMap);
        pdfInfoBo.setPdfBizContent(Lists.newArrayList());
        result.add(pdfInfoBo);

        //附件需要用到的数据
        String wmCompanyCustomerCityLevelInfoBos = MoreObjects.firstNonNull(fullInfoMap.remove("wmCompanyCustomerCityLevelInfoBos"),"");
        String insuredRange = MoreObjects.firstNonNull(fullInfoMap.remove("insuredRange"),"");
        String insuredMaxRange = MoreObjects.firstNonNull(fullInfoMap.remove("insuredMaxRange"),"");
        String insuredMax = MoreObjects.firstNonNull(fullInfoMap.remove("insuredMax"),"");
        String insuredGuaranteedAmount = MoreObjects.firstNonNull(fullInfoMap.remove("insuredGuaranteedAmount"),"");
        String insuredRate = MoreObjects.firstNonNull(fullInfoMap.remove("insuredRate"),"");
        String firstResponsible = MoreObjects.firstNonNull(fullInfoMap.remove("firstResponsible"),"");
        String secondResponsible = MoreObjects.firstNonNull(fullInfoMap.remove("secondResponsible"),"");
        String partA = MoreObjects.firstNonNull(fullInfoMap.get("partA"),"");
        String partASignTime = MoreObjects.firstNonNull(fullInfoMap.get("partASignTime"),"");
        String partBSignTime = MoreObjects.firstNonNull(fullInfoMap.get("partBSignTime"),"");
        String partAEstamp = MoreObjects.firstNonNull(fullInfoMap.get("partAEstamp"),"");
        String partBEstamp = MoreObjects.firstNonNull(fullInfoMap.get("partBEstamp"),"");


        //附件1
        pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(TEMPLET_NAME_ATTACHMENT_1);
        pdfInfoBo.setPdfMetaContent(Maps.newHashMap());
        pdfInfoBo.setPdfBizContent(Lists.newArrayList());
        result.add(pdfInfoBo);

        //附件2
        pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(TEMPLET_NAME_ATTACHMENT_2);
        Map<String, String> attachment2Map = Maps.newHashMap();
        attachment2Map.put("wmCompanyCustomerCityLevelInfoBos",wmCompanyCustomerCityLevelInfoBos);
        pdfInfoBo.setPdfMetaContent(attachment2Map);
        pdfInfoBo.setPdfBizContent(Lists.newArrayList());
        result.add(pdfInfoBo);

        //附件3
        if(WmEcontractContextUtil.SUPPORT_MARK.equals(fullInfoMap.get(SUPPORT_INSURED))){
            pdfInfoBo = new PdfContentInfoBo();
            pdfInfoBo.setPdfTemplateName(TEMPLET_NAME_ATTACHMENT_3);
            Map<String, String> attachment3Map = Maps.newHashMap();
            attachment3Map.put("insuredRange",insuredRange);
            attachment3Map.put("insuredMaxRange",insuredMaxRange);
            attachment3Map.put("insuredMax",insuredMax);
            attachment3Map.put("insuredGuaranteedAmount",insuredGuaranteedAmount);
            attachment3Map.put("insuredRate",insuredRate);
            attachment3Map.put("firstResponsible",firstResponsible);
            attachment3Map.put("secondResponsible",secondResponsible);
            attachment3Map.put("partA",partA);
            attachment3Map.put("partAStampName",partA);
            attachment3Map.put("partASignTime",partASignTime);
            attachment3Map.put("partBSignTime",partBSignTime);
            attachment3Map.put("partAEstamp",partAEstamp);
            attachment3Map.put("partBEstamp",partBEstamp);
            pdfInfoBo.setPdfMetaContent(attachment3Map);
            pdfInfoBo.setPdfBizContent(Lists.newArrayList());
            result.add(pdfInfoBo);
        }
        log.info("#WmEcontractCompanyCustomerContractDataWrapperService,wrap result={}",JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue));
        return result;
    }

    private List<PdfContentInfoBo> warpNoGeneratePdf(EcontractBatchContextBo contextBo) throws WmCustomerException {
        //获取pdfurl
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT);
        EcontractBusinessCustomerInfoBo businessCustomerInfoBo = JSONObject.parseObject(taskBo.getApplyContext(), EcontractBusinessCustomerInfoBo.class);
        WmCompanyCustomerContractWithoutPdfBo wmCompanyCustomerContractWithoutPdfBo = businessCustomerInfoBo.getWmCompanyCustomerContractWithoutPdfBo();
        String pdfUrl = wmCompanyCustomerContractWithoutPdfBo.getQkPdfInfoBo().getPsPdfUrl();
        //封装参数
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfUrl(pdfUrl);
        return Lists.newArrayList(pdfInfoBo);
    }

    /**
     * 生成企客合同信息数据
     */
    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) throws IllegalAccessException {
        EcontractBusinessCustomerInfoBo econtractBusinessCustomerInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBusinessCustomerInfoBo.class);
        log.info("#WmEcontractCompanyCustomerContractDataWrapperService,generatePdfObject={}", JSONObject.toJSONString(econtractBusinessCustomerInfoBo));
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        Map<String, String> pdfMap = Maps.newHashMap();

        WmCompanyCustomerContractBo wmCompanyCustomerContractBo = econtractBusinessCustomerInfoBo.getWmCompanyCustomerContractBo();

        List<WmCompanyCustomerKPIInfoBo> kpiAgingInfoBos = wmCompanyCustomerContractBo.getKPIAgingInfoBos();
        if (CollectionUtils.isNotEmpty(kpiAgingInfoBos)) {
            int kpiAgingMaxRow = 0;
            for (WmCompanyCustomerKPIInfoBo temp : kpiAgingInfoBos) {
                if (CollectionUtils.isNotEmpty(temp.getDetail())) {
                    temp.setRows(temp.getDetail().size() + "");
                    kpiAgingMaxRow += temp.getDetail().size();
                }
            }
            pdfMap.put("KPIAgingInfoBos_rows", kpiAgingMaxRow + "");
            pdfMap.put("KPIAgingInfoBos_rows", kpiAgingMaxRow + "");
        }
        List<CommonCellBo> complaintKPI = wmCompanyCustomerContractBo.getComplaintKPI();
        if (CollectionUtils.isNotEmpty(complaintKPI)) {
            pdfMap.put("complaintKPI_rows", complaintKPI.size() + "");
        }

        String settleType = wmCompanyCustomerContractBo.getSettleType();
        if (Strings.isNotEmpty(settleType) && MONTH_SETTLE_CODE.equals(settleType)){
            //月度结算
            pdfMap.put("show_month_settle","true");
        } else {
            //非月度结算
            pdfMap.put("show_no_month_settle","true");
        }


        List<WmCompanyCustomerCityLevelInfoBo> wmCompanyCustomerCityLevelInfoBos = wmCompanyCustomerContractBo.getWmCompanyCustomerCityLevelInfoBos();
        if (CollectionUtils.isNotEmpty(wmCompanyCustomerCityLevelInfoBos)) {
            for (WmCompanyCustomerCityLevelInfoBo temp : wmCompanyCustomerCityLevelInfoBos) {
                int city_rows = 0;
                if (temp.getDetail() != null) {
                    city_rows = temp.getDetail().size();
                }
                temp.setCity_rows(city_rows + "");
            }
        }

        assembleChosenServices(wmCompanyCustomerContractBo);

        Map<String, String> econtractBusinessCustomerInfoBoSimpleMap = ObjectUtil.ObjectStringField2Map(wmCompanyCustomerContractBo);
        Map<String, String> econtractBusinessCustomerInfoBoComplexMap = ObjectUtil.ObjectCollectionField2Map(wmCompanyCustomerContractBo);

        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAContact", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerName(), StringUtils.EMPTY));
        pdfMap.put("partAPhone", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerPhoneNum(), StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.MT_SH_SIGNKEY);
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partBSignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));

        pdfMap.putAll(econtractBusinessCustomerInfoBoSimpleMap);
        pdfMap.putAll(econtractBusinessCustomerInfoBoComplexMap);

        pdfMap.put("overdueDays", StringUtils.defaultIfEmpty(wmCompanyCustomerContractBo.getOverdueDays(), "/"));
        pdfMap.put("overdueRation", StringUtils.isEmpty(wmCompanyCustomerContractBo.getOverdueRation()) ? "/" : wmCompanyCustomerContractBo.getOverdueRation() + "%");


        //附件4隐藏域判断
        if(WmEcontractContextUtil.SUPPORT_MARK.equals(pdfMap.get(SUPPORT_INSURED))){
            pdfMap.put("is_has_supportInsured", "true");
        }

        //附件5隐藏域判断
        if (CollectionUtils.isNotEmpty(kpiAgingInfoBos) || CollectionUtils.isNotEmpty(complaintKPI)) {
            pdfMap.put("is_has_kpiInfo", "true");
        }

        //远距离信息
        if (wmCompanyCustomerContractBo.getLongDistanceInfoBo() != null) {
            pdfMap.put("longDistanceInfoBo", JSON.toJSONString(Arrays.asList(wmCompanyCustomerContractBo.getLongDistanceInfoBo())));
        } else {
            pdfMap.put("longDistanceInfoBo", JSON.toJSONString(new ArrayList<>()));
        }

        log.info("#WmEcontractCompanyCustomerContractDataWrapperService,pdfMap={}", JSONObject.toJSONString(pdfMap));
        return pdfMap;
    }

    private void assembleChosenServices(WmCompanyCustomerContractBo wmCompanyCustomerContractBo) {
        List<WmCompanyCustomerServiceInfoBo> serviceSeriesAndProducts = wmCompanyCustomerContractBo.getServiceSeriesAndProducts();
        WmCompanyCustomerLongDistanceInfoBo longDistanceInfoBo = wmCompanyCustomerContractBo.getLongDistanceInfoBo();
        List<WmCompanyCustomerChosenServicesBo> chosenServices = Lists.newArrayList();
        WmCompanyCustomerChosenServicesBo wmCompanyCustomerChosenServicesBo = null;
        if (CollectionUtils.isNotEmpty(serviceSeriesAndProducts)) {
            Map<String, List<String>> chosenServicesMap = Maps.newHashMap();
            List<String> serviceSeriesList = null;
            for (WmCompanyCustomerServiceInfoBo temp : serviceSeriesAndProducts) {
                serviceSeriesList = chosenServicesMap.get(temp.getServiceSeries());
                if (CollectionUtils.isEmpty(serviceSeriesList)) {
                    chosenServicesMap.put(temp.getServiceSeries(), Lists.newArrayList(temp.getServiceProduct()));
                } else {
                    serviceSeriesList.add(temp.getServiceProduct());
                }
            }
            for (Entry<String, List<String>> entry : chosenServicesMap.entrySet()) {
                wmCompanyCustomerChosenServicesBo = new WmCompanyCustomerChosenServicesBo();
                wmCompanyCustomerChosenServicesBo.setServiceSeries(entry.getKey());
                wmCompanyCustomerChosenServicesBo.setServiceProducts(entry.getValue());
                chosenServices.add(wmCompanyCustomerChosenServicesBo);
            }
        }
        if (longDistanceInfoBo != null) {
            wmCompanyCustomerChosenServicesBo = new WmCompanyCustomerChosenServicesBo();
            wmCompanyCustomerChosenServicesBo.setServiceSeries(longDistanceInfoBo.getServiceSeries());
            wmCompanyCustomerChosenServicesBo.setServiceProducts(Arrays.asList(longDistanceInfoBo.getServiceProduct()));
            chosenServices.add(wmCompanyCustomerChosenServicesBo);
        }
        wmCompanyCustomerContractBo.setChosenServices(chosenServices);
    }

    private boolean isNewPdfPattern(EcontractTaskBo taskBo) {
        try {
            EcontractBusinessCustomerInfoBo econtractBusinessCustomerInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBusinessCustomerInfoBo.class);
            WmCompanyCustomerContractBo bo = econtractBusinessCustomerInfoBo.getWmCompanyCustomerContractBo();
            return bo.getIsNewPdfPattern() != null && bo.getIsNewPdfPattern();
        } catch (Exception e) {
            log.error("#WmEcontractCompanyCustomerContractDataWrapperService#isNewPdfPattern taskId:{}", taskBo.getId(), e);
        }
        return false;
    }

    private boolean isLongDistancePdfPattern(EcontractTaskBo taskBo) {
        try {
            EcontractBusinessCustomerInfoBo econtractBusinessCustomerInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBusinessCustomerInfoBo.class);
            WmCompanyCustomerContractBo bo = econtractBusinessCustomerInfoBo.getWmCompanyCustomerContractBo();
            return bo.getIsLongDistancePdfPattern() != null && bo.getIsLongDistancePdfPattern();
        } catch (Exception e) {
            log.error("#WmEcontractCompanyCustomerContractDataWrapperService#isLongDistancePdfPattern taskId:{}", taskBo.getId(), e);
        }
        return false;
    }
}
