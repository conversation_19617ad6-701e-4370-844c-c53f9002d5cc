package com.sankuai.meituan.waimai.customer.util;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meituan.waimai.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KmsUtil {

    private static Logger logger = LoggerFactory.getLogger(KmsUtil.class);

    private static String APPKEY = "com.sankuai.waimai.e.customer";


    public static boolean getBoolean(String name) {
        String value = "";
        try {
            value = Kms.getByName(APPKEY, name);
        } catch (Exception e) {
            logger.error("getBoolean Kms.getByName  error ", e);
        }
        if (StringUtil.isNotBlank(value)) {
            if (value.trim().equalsIgnoreCase("true")) {
                return true;
            } else if (value.trim().equalsIgnoreCase("false")) {
                return false;
            } else if (value.trim().equalsIgnoreCase("1")) {
                return true;
            } else if (value.trim().equalsIgnoreCase("0")) {
                return false;
            }
        }
        String errorMsg = String.format("APPKEY=%s key=%s value=%s value is not boolean format!", APPKEY, name, value);
        logger.error(errorMsg);

        throw new RuntimeException(errorMsg);
    }

    public static boolean getBoolean(String name, boolean defaultValue) {
        String value = "";
        try {
            value = Kms.getByName(APPKEY, name);
        } catch (Exception e) {
            logger.error("getBoolean Kms.getByName  error ", e);
        }
        if (StringUtil.isNotBlank(value)) {
            if (value.trim().equalsIgnoreCase("true")) {
                return true;
            } else if (value.trim().equalsIgnoreCase("false")) {
                return false;
            } else if (value.trim().equalsIgnoreCase("1")) {
                return true;
            } else if (value.trim().equalsIgnoreCase("0")) {
                return false;
            }
            String errorMsg = String.format("APPKEY=%s key=%s value=%s value is not boolean format", APPKEY, name, value);
            logger.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }

        return defaultValue;
    }

    public static int getInt(String name) {
        String value = "";
        try {
            value = Kms.getByName(APPKEY, name);
        } catch (Exception e) {
            logger.error("getInt Kms.getByName  error ", e);
        }
        if (StringUtil.isNotBlank(value)) {
            long longValue = Long.parseLong(value);
            if (longValue > Integer.MAX_VALUE) {
                String errorMsg = String.format("APPKEY=%s key=%s value=%s value beyond integer scope", APPKEY, name, value);
                logger.error(errorMsg);
                throw new RuntimeException(errorMsg);
            } else {
                return (int) longValue;
            }
        } else {
            String errorMsg = String.format("APPKEY=%s key=%s value=%s value is blank", APPKEY, name, value);
            logger.error(errorMsg);

            throw new RuntimeException(errorMsg);
        }
    }

    public static int getInt(String name, int defaultValue) {
        String value = "";
        try {
            value = Kms.getByName(APPKEY, name);
        } catch (Exception e) {
            logger.error("getInt Kms.getByName  error ", e);
        }
        if (StringUtil.isNotBlank(value)) {
            long longValue = Long.parseLong(value);
            if (longValue > Integer.MAX_VALUE) {
                String errorMsg = String.format("APPKEY=%s key=%s value=%s value beyond integer scope", APPKEY, name, value);
                logger.error(errorMsg);
                throw new RuntimeException(errorMsg);
            } else {
                return (int) longValue;
            }
        } else {
            return defaultValue;
        }
    }

    public static long getLong(String name) {
        String value = "";
        try {
            value = Kms.getByName(APPKEY, name);
        } catch (Exception e) {
            logger.error("getLong Kms.getByName  error ", e);
        }
        if (StringUtil.isNotBlank(value)) {
            return Long.parseLong(value);
        } else {
            String errorMsg = String.format("APPKEY=%s key=%s value=%s value is blank", APPKEY, name, value);
            logger.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    public static long getLong(String name, long defaultValue) {
        String value = "";
        try {
            value = Kms.getByName(APPKEY, name);
        } catch (Exception e) {
            logger.error("getLong Kms.getByName  error ", e);
        }
        return null == value ? defaultValue : Long.parseLong(value);
    }

    public static String getString(String name) {
        String value = "";
        try {
            value = Kms.getByName(APPKEY, name);
        } catch (Exception e) {
            logger.error("getLong Kms.getByName  error ", e);
        }
        return StringUtil.isBlank(value) ? "" : value;
    }

    public static String getString(String name, String defaultValue) {
        String value = "";
        try {
            value = Kms.getByName(APPKEY, name);
        } catch (Exception e) {
            logger.error("getString Kms.getByName error ", e);
        }
        return StringUtil.isBlank(value) ? defaultValue : value;
    }

    public static String getKmsValue(String name) {
        try {
            String value = Kms.getByName(APPKEY, name);
            return value;
        } catch (KmsResultNullException e) {
            logger.error("KmsUtil.getKmsValue name={}", name, e);
            return "";
        }
    }
}