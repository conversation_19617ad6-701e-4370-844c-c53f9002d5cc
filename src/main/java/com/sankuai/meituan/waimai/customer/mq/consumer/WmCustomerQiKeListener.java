package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.service.companycustomer.CompanyCustomerSyncService;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WmCustomerQiKeListener implements IMessageListener {
    private static final String CUSTOMER_SWITCH = "customer_switch";

    private static final String EVENT_SOURCE = "event_source";

    @Autowired
    private CompanyCustomerSyncService companyCustomerSyncService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        try {
            String msg = message.getBody().toString();
            log.info("WmCustomerQiKeListener recvMessage = {}", msg);
            CustomerMQBody customerMQBody = JSON.parseObject(msg, CustomerMQBody.class);
            int eventType = customerMQBody.getEventType();
            ConsumeStatus status = ConsumeStatus.CONSUME_SUCCESS;
            if (CustomerMQEventEnum.CUSTOMER_BIND_POI.getCode() != eventType
                    && CustomerMQEventEnum.CUSTOMER_UNBIND_POI.getCode() != eventType) {
                return status;
            }
            int grayPercent = MccCustomerConfig.getQiKeSubscribeCustomerPoiPercentNew();
            if ((customerMQBody.getCustomerId() % 100) < grayPercent) {
                if (CustomerMQEventEnum.CUSTOMER_BIND_POI.getCode() == eventType) {
                    processBindPoi(customerMQBody);
                } else if (CustomerMQEventEnum.CUSTOMER_UNBIND_POI.getCode() == eventType) {
                    processUnBindPoi(customerMQBody);
                }
            }
            return status;
        } catch (Exception e) {
            log.error("客户门店关系变更，企客门店处理失败, message={} ", JSONObject.toJSONString(message), e);
            return ConsumeStatus.RECONSUME_LATER;
        }

    }

    private boolean eventSourceIsCustomerSwitch(CustomerMQBody customerMQBody) {
        JSONObject jo = JSON.parseObject(customerMQBody.getExtraData());
        return CUSTOMER_SWITCH.equals(jo.get(EVENT_SOURCE));
    }

    private void processBindPoi(CustomerMQBody customerMQBody) {
        //客户切换的门店绑定消息不再处理
        if (eventSourceIsCustomerSwitch(customerMQBody)) {
            log.info("客户切换绑定门店事件,customerMQBody={}", JSONObject.toJSONString(customerMQBody));
            return;
        }

        List<Long> wmPoiIdList = getModifyWmPoiIdList(customerMQBody);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return;
        }
        try {
            log.info("processBindPoi customerId={},wmPoiIdList={}", customerMQBody.getCustomerId(), JSONObject.toJSONString(wmPoiIdList));
            companyCustomerSyncService.handlePoiBindAndUnBindEvent(customerMQBody.getCustomerId(), wmPoiIdList, "bind");
        } catch (WmCustomerException | TException e) {
            log.error("handlePoiBindAndUnBindEvent error,customerId={},wmPoiIdList={}", customerMQBody.getCustomerId(), wmPoiIdList, e);
        }
    }

    private void processUnBindPoi(CustomerMQBody customerMQBody) {
        List<Long> wmPoiIdList = getModifyWmPoiIdList(customerMQBody);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return;
        }
        try {
            log.info("processUnBindPoi customerId={},wmPoiIdList={}", customerMQBody.getCustomerId(), JSONObject.toJSONString(wmPoiIdList));
            companyCustomerSyncService.handlePoiBindAndUnBindEvent(customerMQBody.getCustomerId(), wmPoiIdList, "unbind");
        } catch (WmCustomerException | TException e) {
            log.error("handlePoiBindAndUnBindEvent error,customerId={},wmPoiIdList={}", customerMQBody.getCustomerId(), wmPoiIdList, e);
        }
    }

    private List<Long> getModifyWmPoiIdList(CustomerMQBody customerMQBody) {
        JSONObject jo = JSON.parseObject(customerMQBody.getExtraData());
        String wmPoiIds = String.valueOf(jo.get("wmPoiIds"));
        String[] strs = StringUtils.split(wmPoiIds, ",");
        List<Long> wmPoiIdList = Lists.newArrayList();
        for (String str : strs) {
            wmPoiIdList.add(Long.valueOf(str));
        }
        return wmPoiIdList;
    }
}
