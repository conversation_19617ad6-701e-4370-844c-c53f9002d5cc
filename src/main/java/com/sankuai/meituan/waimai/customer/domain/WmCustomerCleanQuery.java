package com.sankuai.meituan.waimai.customer.domain;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20230118
 * @desc 清洗数据使用的查询对象
 */
public class WmCustomerCleanQuery {

    /**
     * 最小客户id-主键ID
     */
    private Integer minId;

    /**
     * 最大客户id-主键ID
     */
    private Integer maxId;

    /**
     * 客户ID列表
     */
    private List<Integer> ids;

    /**
     * 查询页数
     */
    private Integer pageSize;

    /**
     * 客户类型
     */
    private Integer customerRealType;

    public Integer getMinId() {
        return minId;
    }

    public void setMinId(Integer minId) {
        this.minId = minId;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getCustomerRealType() {
        return customerRealType;
    }

    public void setCustomerRealType(Integer customerRealType) {
        this.customerRealType = customerRealType;
    }

    public Integer getMaxId() {
        return maxId;
    }

    public void setMaxId(Integer maxId) {
        this.maxId = maxId;
    }
}
