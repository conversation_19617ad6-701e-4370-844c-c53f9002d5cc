package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.ModuleEnum;
import com.sankuai.meituan.waimai.channel.beacon.thrift.BeaconCommandThriftService;
import com.sankuai.meituan.waimai.channel.beacon.thrift.exception.BeaconServerException;
import com.sankuai.meituan.waimai.channel.beacon.thrift.vo.ModuleStateRecordVo;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiProduceOplogThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceSection;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceSource;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceStatus;
import com.sankuai.meituan.waimai.poiaudit.thrift.domain.WmPoiProduceOplog;
import com.sankuai.meituan.waimai.poiaudit.thrift.exception.WmPoiAuditException;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.*;

@Service
public class WmContractPoiProduceAtomService {

    private static Logger logger = LoggerFactory.getLogger(WmContractPoiProduceAtomService.class);

    @Autowired
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmPoiProduceOplogThriftServiceAdapter wmPoiProduceOplogThriftService;

    @Autowired
    private BeaconCommandThriftService beaconCommandThriftService;


    ExecutorService executorService = new ThreadPoolExecutor(10, 200,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue(1024), new ThreadPoolExecutor.AbortPolicy());

    public Integer getContractPoiProduceStatus(int customerId) {
        List<WmTempletContractDB> c1ContracList = wmTempletContractDBMapper.selectByParentIdAndTypesMaster((long) customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode())
        );
        if (CollectionUtils.isEmpty(c1ContracList)) {
            return null;
        }
        WmTempletContractDB c1ContractDb = c1ContracList.get(0);
        if (c1ContractDb.getStatus() == CustomerContractStatus.AUDITING.getCode()
                || c1ContractDb.getStatus() == CustomerContractStatus.SIGNING.getCode()
                || c1ContractDb.getStatus() == CustomerContractStatus.WAITING_SIGN.getCode()) {
            return WmPoiProduceStatus.AUDIT_COMMIT;
        }
        if (c1ContractDb.getStatus() == CustomerContractStatus.EFFECT.getCode()) {
            return WmPoiProduceStatus.AUDIT_APPROVED;
        }
        if (c1ContractDb.getStatus() == CustomerContractStatus.REJECT.getCode()
                || c1ContractDb.getStatus() == CustomerContractStatus.SIGN_FAIL.getCode()) {
            return WmPoiProduceStatus.AUDIT_REJECTED;
        }
        return null;
    }

    public void logPoiProduce(int customerId, final int opUid, final String opUname) {
        final List<Long> poiIds = wmCustomerPoiService.selectWmPoiIdsByCustomerId(customerId);
        if (CollectionUtils.isEmpty(poiIds)) {
            logger.info("customerId:{} 尚未绑定门店，不发送合同变更", customerId);
            return;
        }
        final Integer produceStatus = getContractPoiProduceStatus(customerId);
        if (produceStatus == null) {
            logger.info("customerId:{} 尚未有合同，不发送合同变更", customerId);
            return;
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                for (Long poiId : poiIds) {
                    recordProduceOpog(opUid, opUname, produceStatus, poiId);
                }
            }
        });

    }

    public void logPoiProduce(int customerId, final List<Long> poiIds, final int opUid, final String opUname) {
        if (CollectionUtils.isEmpty(poiIds)) {
            logger.info("customerId:{} 尚未绑定门店，不发送合同变更", customerId);
            return;
        }
        final Integer produceStatus = getContractPoiProduceStatus(customerId);
        if (produceStatus == null) {
            logger.info("customerId:{} 尚未有合同，不发送合同变更", customerId);
            return;
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                for (Long poiId : poiIds) {
                    recordProduceOpog(opUid, opUname, produceStatus, poiId);
                }
            }
        });

    }

    public void logPoiProduceForContractDEL(int customerId, final List<Long> poiIds, final int opUid, final String opUname) {
        if (CollectionUtils.isEmpty(poiIds)) {
            logger.info("customerId:{} 尚未绑定门店，不发送合同变更", customerId);
            return;
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                for (Long poiId : poiIds) {
                    recordProduceOpog(opUid, opUname, WmPoiProduceStatus.DELETED, poiId);
                }
            }
        });
    }

    public void logPoiProduceForPoiDEL(int customerId, final List<Long> poiIds, final int opUid, final String opUname) {
        if (CollectionUtils.isEmpty(poiIds)) {
            logger.info("customerId:{} 尚未绑定门店，不发送合同变更", customerId);
            return;
        }
        final Integer produceStatus = getContractPoiProduceStatus(customerId);
        if (produceStatus == null) {
            logger.info("customerId:{} 尚未有合同，不发送合同变更", customerId);
            return;
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                for (Long poiId : poiIds) {
                    recordProduceOpog(opUid, opUname, WmPoiProduceStatus.DELETED, poiId);
                }
            }
        });

    }

    private void recordProduceOpog(int opUid, String opUname, Integer produceStatus, Long poiId) {
        logger.info("通知商家门店状态  poiId:{}  status:{}  ", poiId, produceStatus);
        try {
            //写入状态中心
            ModuleStateRecordVo stateRecordVo = new ModuleStateRecordVo.Builder()
                    .wmPoiId(poiId.intValue())
                    .state(produceStatus)
                    .moduleCode(ModuleEnum.CONTRACT.getCode())
                    .remark(StringUtils.EMPTY)
                    .source((byte) WmPoiProduceSource.SCM)
                    .opUid(opUid)
                    .opUname(opUname)
                    .build();
            logger.info("contract request syncModuleState stateRecordVo = {}", JSON.toJSONString(stateRecordVo));
            beaconCommandThriftService.syncModuleState(stateRecordVo);
        } catch (BeaconServerException e) {
            logger.warn("contract调用状态机处理失败 wmPoiId = " + poiId + ",目标状态: " + produceStatus, e);
        } catch (TException e) {
            logger.error("contract调用状态机处理失败 wmPoiId = " + poiId + ",目标状态: " + produceStatus, e);
        }
    }
}
