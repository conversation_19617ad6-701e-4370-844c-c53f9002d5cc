package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.settle;

import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.SETTLE_MOON_SINGLE_INFO_V3)
@Service
@Slf4j
public class WmEcontractSettleMoonSingleInfoPdfMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext,
            EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractSettleMoonSingleInfoPdfMaker");
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.SETTLE_MOON_SINGLE_INFO_V3;

        EcontractTaskBo taskBo = WmEcontractContextUtil
                .selectByApplyType(originContext, EcontractTaskApplyTypeEnum.SETTLE);

        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());

        //抽取一个开钱包的对象-业务规则上允许部分开钱包
        EcontractSettleInfoBo infoBo = null;
        for(EcontractSettleInfoBo temp : settleInfoBoList){
            if(temp.isSupportWallet()){
                infoBo = temp;
                break;
            }
        }
        EcontractSettleInfoBo econtractSettleInfoBo = infoBo;
        econtractSettleInfoBo.setDiffInfo("");
        Map<String, String> pdfMap = MapUtil.Object2Map(econtractSettleInfoBo);
        pdfMap.put("signTime", org.apache.commons.lang.StringUtils
                .defaultIfEmpty(signTime, org.apache.commons.lang.StringUtils.EMPTY));
        pdfMap.put("partAStampName", org.apache.commons.lang.StringUtils
                .defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), org.apache.commons.lang.StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfMetaContent(pdfMap);
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        return pdfInfoBo;
    }



}
