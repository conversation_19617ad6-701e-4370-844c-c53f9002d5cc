package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList;

import com.dianping.frog.sdk.data.DmlType;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiListVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmColumnInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WmCustomerKpPoiCheck implements InfoUpdateCheck {

    private static final String KP_ID = "kp_id";
    private static final String VALID = "valid";


    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Override
    public String check(String tableName, DmlType operateType, Map<String, WmColumnInfo> columnInfoMap) {
        if (!tableName.equals(WmCustomerRelTableDbusEnum.TABLE_WM_CUSTOMER_KP_POI_REL.getAlias())) {
            return null;
        }
        switch (operateType) {
            case INSERT:
                return checkInsert(columnInfoMap);
            case UPDATE:
                return checkUpdate(columnInfoMap);
            case DELETE:
                return delete(columnInfoMap);
            default:
                return null;
        }
    }

    private String checkInsert(Map<String, WmColumnInfo> columnInfoMap) {
        String wmPoiId = columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getNewValue();
        String kpId = columnInfoMap.get(KP_ID).getNewValue();
        if (StringUtils.isBlank(wmPoiId) || StringUtils.isBlank(kpId)) {
            return null;
        }
        List<WmCustomerPoiListInfoDTO> list = getData(kpId, wmPoiId);
        if (CollectionUtils.isEmpty(list)) {
            return String.format("运营经理门店新增失败%s:%s,kpId:%s;", WmCustomerPoiListESFields.WM_POI_ID.getField(), wmPoiId, kpId);
        }
        return null;
    }

    private String checkUpdate(Map<String, WmColumnInfo> columnInfoMap) {
        String kpId = columnInfoMap.get(KP_ID).getNewValue();
        String valid = columnInfoMap.get(VALID).getNewValue();
        if (StringUtils.isBlank(kpId) || StringUtils.isBlank(valid)) {
            return null;
        }
        if (valid.equals(String.valueOf(ValidEnum.VALID_YES.getValue()))) {
            String newWmPoiId = columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getNewValue();
            List<WmCustomerPoiListInfoDTO> list = getData(kpId, newWmPoiId);
            if (CollectionUtils.isEmpty(list)) {
                return String.format("运营经理门店关联失败%s:%s,kpId:%s;", WmCustomerPoiListESFields.WM_POI_ID.getField(), newWmPoiId, kpId);
            }

            String oldWmPoiId = columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getOldValue();
            list = getData(kpId, oldWmPoiId);
            if (CollectionUtils.isNotEmpty(list)) {
                return String.format("运营经理门店取消失败%s:%s,kpId:%s;", WmCustomerPoiListESFields.WM_POI_ID.getField(), oldWmPoiId, kpId);
            }

        } else {
            String wmPoiId = columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getNewValue();
            List<WmCustomerPoiListInfoDTO> list = getData(kpId, wmPoiId);
            if (CollectionUtils.isNotEmpty(list)) {
                return String.format("运营经理门店取消失败%s:%s,kpId:%s;", WmCustomerPoiListESFields.WM_POI_ID.getField(), wmPoiId, kpId);
            }
        }

        return null;
    }

    private String delete(Map<String, WmColumnInfo> columnInfoMap) {
        String wmPoiId = columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getOldValue();
        String kpId = columnInfoMap.get(KP_ID).getOldValue();
        if (StringUtils.isBlank(wmPoiId) || StringUtils.isBlank(kpId)) {
            return null;
        }
        List<WmCustomerPoiListInfoDTO> list = getData(kpId, wmPoiId);
        if (CollectionUtils.isNotEmpty(list)) {
            return String.format("运营经理门店删除失败%s:%s,kpId:%s;", WmCustomerPoiListESFields.WM_POI_ID.getField(), wmPoiId, kpId);
        }
        return null;
    }


    private List<WmCustomerPoiListInfoDTO> getData(String kpId, String wmPoiId) {
        WmCustomerPoiListVo condition = new WmCustomerPoiListVo();
        condition.setWmPoiId(Integer.valueOf(wmPoiId));
        condition.setKpId(Integer.valueOf(kpId));
        condition.setPageNo(1);
        condition.setPageSize(30);
        return wmCustomerPoiListEsService.queryData(condition);
    }


}
