package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EstampInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractC2ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.EcontractDaoCanC2ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class WmEcontractStampAgentWrapperService implements IWmEcontractStampWrapperService {

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo, List<String> flowList)
            throws WmCustomerException {

        CertifyInfoBo certifyInfoBo = assembleCertifyInfoBo(contextBo);
        //合作商签章信息
        Map<String, String> estampParamMap = Maps.newHashMap();
        estampParamMap.put(TaskConstant.PDF_ESTAMP_SIGN_KEY, PdfConstant.AGENT_SIGNKEY);
        EstampInfoBo estampInfoBo = new EstampInfoBo.Builder()
                .setEstampMap(estampParamMap)
                .build();
        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.STAMP_AGENT)
                .certifyInfoBo(certifyInfoBo)
                .estampInfoBo(estampInfoBo)
                .metaFlowList(flowList)
                .build();
    }

    private CertifyInfoBo assembleCertifyInfoBo(EcontractBatchContextBo contextBo) throws WmCustomerException {
        if (EcontractBatchTypeEnum.DAOCAN_SERVICE_C2_CONTRACT == contextBo.getBatchTypeEnum()) {
            return assembleDcC2ContractCertifyInfoBo(contextBo);
        }
        EcontractTaskBo c2TaskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.C2CONTRACT);

        //合作商CA认证信息
        EcontractC2ContractInfoBo c2ContractInfoBo = JSON.parseObject(c2TaskBo.getApplyContext(), EcontractC2ContractInfoBo.class);
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
                .setCustomerName(c2ContractInfoBo.getAgentName())
//            .setCustomerId(String.valueOf(c2ContractInfoBo.getAgentId()))
                .setMobile(c2ContractInfoBo.getLegalPersonPhone())
                .setEmail(c2ContractInfoBo.getLegalPersonEmail())
                .setCaType(CustomerType.CUSTOMER_TYPE_BUSINESS.equals(c2ContractInfoBo.getAgentQuaType()) ? CAType.COMPANY : CAType.PERSON)
                .setQuaNum(c2ContractInfoBo.getQuaNum())
                .build();
        return certifyInfoBo;
    }

    private CertifyInfoBo assembleDcC2ContractCertifyInfoBo(EcontractBatchContextBo contextBo) throws WmCustomerException {
        EcontractTaskBo econtractTaskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT);
        EcontractDaoCanC2ContractInfoBo contractInfoBo = JSON.parseObject(econtractTaskBo.getApplyContext(), EcontractDaoCanC2ContractInfoBo.class);
        return new CertifyInfoBo.Builder()
                .setCustomerName(contractInfoBo.getAgentName())
                .setMobile(contractInfoBo.getLegalPersonPhone())
                .setEmail(contractInfoBo.getLegalPersonEmail())
                .setCaType(CustomerType.CUSTOMER_TYPE_BUSINESS.equals(contractInfoBo.getAgentQuaType()) ? CAType.COMPANY : CAType.PERSON)
                .setQuaNum(contractInfoBo.getQuaNum())
                .build();
    }

}
