package com.sankuai.meituan.waimai.customer.domain.sc;

/**
 * 学校范围DO
 * <AUTHOR>
 * @date 2021/06/09
 */
public class WmScSchoolAreaDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 学校AOI ID
     */
    private Long aoiId;
    /**
     * 学校AOI名称
     */
    private String aoiName;
    /**
     * 即时配是否允许配送进校 -1-未知 0-骑行 1-步行 2-禁止 4-可通行但骑步行未知
     */
    private Integer aoiMode;
    /**
     * 是否自动同步数据 1-是 0-否
     */
    private Integer autoSync;
    /**
     * 创建渠道来源 1-校园食堂，2-向日葵
     */
    private Integer crSource;
    /**
     * 修改渠道来源 1-校园食堂，2-向日葵
     */
    private Integer upSource;
    /**
     * 是否有效（是否已删除） 0-无效，1-有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
    /**
     * 学校范围
     */
    private String area;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSchoolPrimaryId() {
        return schoolPrimaryId;
    }

    public void setSchoolPrimaryId(Integer schoolPrimaryId) {
        this.schoolPrimaryId = schoolPrimaryId;
    }

    public Long getAoiId() {
        return aoiId;
    }

    public void setAoiId(Long aoiId) {
        this.aoiId = aoiId;
    }

    public String getAoiName() {
        return aoiName;
    }

    public void setAoiName(String aoiName) {
        this.aoiName = aoiName;
    }

    public Integer getAoiMode() {
        return aoiMode;
    }

    public void setAoiMode(Integer aoiMode) {
        this.aoiMode = aoiMode;
    }

    public Integer getAutoSync() {
        return autoSync;
    }

    public void setAutoSync(Integer autoSync) {
        this.autoSync = autoSync;
    }

    public Integer getCrSource() {
        return crSource;
    }

    public void setCrSource(Integer crSource) {
        this.crSource = crSource;
    }

    public Integer getUpSource() {
        return upSource;
    }

    public void setUpSource(Integer upSource) {
        this.upSource = upSource;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Long getCuid() {
        return cuid;
    }

    public void setCuid(Long cuid) {
        this.cuid = cuid;
    }

    public Long getMuid() {
        return muid;
    }

    public void setMuid(Long muid) {
        this.muid = muid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    @Override
    public String toString() {
        return "WmScSchoolAreaDO{" +
                "id=" + id +
                ", schoolPrimaryId=" + schoolPrimaryId +
                ", aoiId=" + aoiId +
                ", aoiName='" + aoiName + '\'' +
                ", aoiMode=" + aoiMode +
                ", autoSync=" + autoSync +
                ", crSource=" + crSource +
                ", upSource=" + upSource +
                ", valid=" + valid +
                ", cuid=" + cuid +
                ", muid=" + muid +
                ", ctime=" + ctime +
                ", utime=" + utime +
                ", area='" + area + '\'' +
                '}';
    }
}