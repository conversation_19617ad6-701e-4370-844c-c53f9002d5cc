package com.sankuai.meituan.waimai.customer.domain.sc;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiAuditStatusV2Enum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 食堂绑定门店日志组装BO
 * <AUTHOR>
 * @date 2023/07/19
 * @email <EMAIL>
 */
@Data
public class WmScCanteenPoiLogComposeBO {
    /**
     * 提交前任务状态
     */
    private CanteenPoiAuditStatusV2Enum preAuditStatus;
    /**
     * 提交后任务状态
     */
    private CanteenPoiAuditStatusV2Enum aftAuditStatus;
    /**
     * 日志值
     */
    private Map<Byte, String> values;
    /**
     * 生效门店列表
     */
    private List<WmPoiAggre> effectPoiAggreList;
    /**
     * 是否需展示门店信息
     */
    private boolean appendPoiInfo;
    /**
     * 回车
     */
    private String enter;


    public static final class Builder {

        private CanteenPoiAuditStatusV2Enum preAuditStatus;

        private CanteenPoiAuditStatusV2Enum aftAuditStatus;

        private Map<Byte, String> values;

        private List<WmPoiAggre> effectPoiAggreList;

        private boolean appendPoiInfo;

        private String enter;

        public Builder() {
        }

        public WmScCanteenPoiLogComposeBO.Builder preAuditStatus(CanteenPoiAuditStatusV2Enum val) {
            this.preAuditStatus = val;
            return this;
        }

        public WmScCanteenPoiLogComposeBO.Builder aftAuditStatus(CanteenPoiAuditStatusV2Enum val) {
            this.aftAuditStatus = val;
            return this;
        }

        public WmScCanteenPoiLogComposeBO.Builder values(Map<Byte, String> val) {
            this.values = val;
            return this;
        }

        public WmScCanteenPoiLogComposeBO.Builder effectPoiAggreList(List<WmPoiAggre> val) {
            this.effectPoiAggreList = val;
            return this;
        }

        public WmScCanteenPoiLogComposeBO.Builder appendPoiInfo(boolean val) {
            this.appendPoiInfo = val;
            return this;
        }

        public WmScCanteenPoiLogComposeBO.Builder enter(String val) {
            this.enter = val;
            return this;
        }

        public WmScCanteenPoiLogComposeBO build() {
            WmScCanteenPoiLogComposeBO wmScCanteenPoiLogComposeBO = new WmScCanteenPoiLogComposeBO();
            wmScCanteenPoiLogComposeBO.setAppendPoiInfo(this.appendPoiInfo);
            wmScCanteenPoiLogComposeBO.setEnter(this.enter);
            wmScCanteenPoiLogComposeBO.setValues(this.values);
            wmScCanteenPoiLogComposeBO.setEffectPoiAggreList(this.effectPoiAggreList);
            wmScCanteenPoiLogComposeBO.setAftAuditStatus(this.aftAuditStatus);
            wmScCanteenPoiLogComposeBO.setPreAuditStatus(this.preAuditStatus);
            return wmScCanteenPoiLogComposeBO;
        }
    }

    @Override
    public String toString() {
        return "WmScCanteenPoiLogComposeBO{" +
                "preAuditStatus=" + preAuditStatus +
                ", aftAuditStatus=" + aftAuditStatus +
                ", values=" + values +
                ", effectPoiAggreList=" + effectPoiAggreList +
                ", appendPoiInfo=" + appendPoiInfo +
                ", enter='" + enter + '\'' +
                '}';
    }
}
