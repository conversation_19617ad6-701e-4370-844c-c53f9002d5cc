package com.sankuai.meituan.waimai.customer.domain;

import lombok.Data;

import java.util.List;

@Data
public class WmCustomerPoiQueryCondtionVo {
    private Integer customerId;
    private Long wmPoiId;
    private Integer bindStatus;
    private Integer switchCustomerType;
    private List<Long> wmPoiIds;

    private Integer minId;

    private Integer maxId;

    /**
     * 是否查询所有数据，默认为否，只查询有效的及预绑定
     */
    private Integer allData;
}
