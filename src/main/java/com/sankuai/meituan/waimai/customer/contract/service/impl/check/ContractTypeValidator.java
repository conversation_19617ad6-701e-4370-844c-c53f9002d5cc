package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ContractTypeValidator implements IContractValidator {

    @Autowired
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {
        if (contractBo.getBasicBo().getTempletContractId() <= 0) {
            return true;
        }
        int type = wmTempletContractDBMapper
                .selectTypeByPrimaryKey(contractBo.getBasicBo().getTempletContractId());

        WmTempletContractTypeBo typeInDb = new WmTempletContractTypeBo(type);

        WmTempletContractTypeBo typeNew = new WmTempletContractTypeBo(contractBo.getBasicBo().getType());

        if (typeNew.getType() != typeInDb.getType()
                || typeNew.getCooperateMode() != typeInDb.getCooperateMode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同类型不可更改");
        }
        if (typeNew.getSignType() != typeInDb.getSignType()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约类型不可更改");
        }
        return true;
    }

}
