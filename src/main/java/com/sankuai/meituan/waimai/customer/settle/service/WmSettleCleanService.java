package com.sankuai.meituan.waimai.customer.settle.service;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleWalletCleanLogDBMapper;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleWalletCleanLogDB;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;

import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;

import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleWalletCleanLog;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-09 下午4:15
 */
@Service
public class WmSettleCleanService {


    @Autowired
    private WmSettleWalletCleanLogDBMapper wmSettleWalletCleanLogDBMapper;

    private static final ImmutableSet<String> WM_POI_FIELD = ImmutableSet
            .of(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_SCM_VERSION, WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_UID, WmPoiFieldQueryConstant.WM_POI_FIELD_VALID);

    private static Logger logger = LoggerFactory.getLogger(WmSettleCleanService.class);

    public List<WmSettleWalletCleanLog> getWmSettleWalletCleanLogByWmPoiIdLIst(List<Long> wmPoiIdList) {
        List<WmSettleWalletCleanLogDB> wmSettleWalletCleanLogDBList = wmSettleWalletCleanLogDBMapper.getWmSettleWalletCleanLogByWmPoiIdList(wmPoiIdList);
        List<WmSettleWalletCleanLog> wmSettleWalletCleanLogList = Lists.newArrayList();
        for (WmSettleWalletCleanLogDB wmSettleWalletCleanLogDB : wmSettleWalletCleanLogDBList) {
            WmSettleWalletCleanLog wmSettleWalletCleanLog = new WmSettleWalletCleanLog();
            BeanUtils.copyProperties(wmSettleWalletCleanLogDB, wmSettleWalletCleanLog);
            wmSettleWalletCleanLogList.add(wmSettleWalletCleanLog);
        }
        return wmSettleWalletCleanLogList;
    }

    public BooleanResult insertWmSettleWalletCleanLog(WmSettleWalletCleanLog wmSettleWalletCleanLog) {
        WmSettleWalletCleanLogDB wmSettleWalletCleanLogDB = new WmSettleWalletCleanLogDB();
        BeanUtils.copyProperties(wmSettleWalletCleanLog, wmSettleWalletCleanLogDB);
        wmSettleWalletCleanLogDBMapper.delete(wmSettleWalletCleanLog.getWmPoiId());
        wmSettleWalletCleanLogDBMapper.insert(wmSettleWalletCleanLogDB);
        return new BooleanResult(wmSettleWalletCleanLogDB.getId() > 0);
    }

    public BooleanResult updateWmSettleWalletCleanLog(WmSettleWalletCleanLog wmSettleWalletCleanLog) {
        WmSettleWalletCleanLogDB wmSettleWalletCleanLogDB = new WmSettleWalletCleanLogDB();
        BeanUtils.copyProperties(wmSettleWalletCleanLog, wmSettleWalletCleanLogDB);
        wmSettleWalletCleanLogDBMapper.update(wmSettleWalletCleanLogDB);
        return new BooleanResult(true);
    }
}