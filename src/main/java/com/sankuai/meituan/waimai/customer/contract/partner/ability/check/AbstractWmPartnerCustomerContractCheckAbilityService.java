package com.sankuai.meituan.waimai.customer.contract.partner.ability.check;

import com.sankuai.meituan.waimai.customer.contract.dao.service.TemplateContractVersionService;
import com.sankuai.meituan.waimai.customer.contract.domain.TemplateContractVersionPo;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.C2SignStatusCheckRequestDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/9 11:55
 */
public abstract class AbstractWmPartnerCustomerContractCheckAbilityService implements WmPartnerCustomerContractCheckAbilityService {

    @Resource
    private TemplateContractVersionService templateContractVersionService;

    protected abstract void checkRequestParam(C2SignStatusCheckRequestDTO requestDTO) throws WmCustomerException;

    protected List<TemplateContractVersionPo> queryContractVersionPoList(Long mtCustomerId, List<Integer> agentIdList) {
        int contractType = WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode();
        long currentTimeMillis = System.currentTimeMillis();
        return templateContractVersionService.batchQueryByMtCustomerIdAndAgentId(Collections.singletonList(mtCustomerId), agentIdList, contractType, currentTimeMillis);
    }
}
