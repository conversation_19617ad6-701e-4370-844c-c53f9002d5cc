package com.sankuai.meituan.waimai.customer.constant.contract;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.constant.sc.WdcClueTypeEnum;
import java.util.Map;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2022/12/4 11:01 AM
 */
public enum ManualTaskSubmitStatusEnum {

    /**
     * 手动待发起任务，处理中
     */
    SUBMIT_TASK_START_INIT(1, "开始处理"),
    SUBMIT_TASK_PROCESSING(2, "处理中"),
    SUBMIT_TASK_SUBMIT_FINISH(3, "已提交"),
    SUBMIT_TASK_START_COMPLETED(4, "失败"),
    SUBMIT_TASK_FAILED(5, "成功");


    private static Map<Integer, WdcClueTypeEnum> enumPreMap = Maps.newHashMap();

    static {
        for (WdcClueTypeEnum status : WdcClueTypeEnum.values()) {
            enumPreMap.put(status.getCode(), status);
        }
    }

    private Integer code;
    private String name;

    ManualTaskSubmitStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    private void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    private void setName(String name) {
        this.name = name;
    }

}
