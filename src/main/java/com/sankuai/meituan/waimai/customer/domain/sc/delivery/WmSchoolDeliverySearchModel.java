package com.sankuai.meituan.waimai.customer.domain.sc.delivery;

import com.sankuai.meituan.waimai.rice.eagle.model.*;
import lombok.Data;

import java.util.List;

/**
 * 外卖校园交付计划搜索模型
 */
@Data
@BoolQuery(queryType = BoolQueryType.MUST)
public class WmSchoolDeliverySearchModel extends SearchModel {

    /**
     * 学校ID
     */
    @Condition(field = "schoolId", expression = Expression.EQUALS)
    private Long schoolId;

    /**
     * 学校分类,高禀赋、高单量、其他
     */
    @Condition(field = "schoolCategory", expression = Expression.IN)
    private List<Integer> schoolType;

    /**
     * 配送方式：聚合配送、自配送
     */
    @Condition(field = "deliveryType", expression = Expression.IN)
    private List<Integer> deliveryType;

    /**
     * 交付状态: 交付中、交付完成、交付终止
     */
    @Condition(field = "deliveryStatus", expression = Expression.IN)
    private List<Integer> deliverStatus;

    /**
     * 交付发起时间from
     */
    @Condition(field = "createTimestamp", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long createTimestampBegin;

    /**
     * 交付发起时间to
     */
    @Condition(field = "createTimestamp", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long createTimestampEnd;

    /**
     * 合同结束时间from
     */
    @Condition(field = "contractAuthEndTime", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long contractAuthEndTimeBegin;

    /**
     * 合同结束时间to
     */
    @Condition(field = "contractAuthEndTime", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long contractAuthEndTimeEnd;

    /**
     * 合同开始时间from
     */
    @Condition(field = "contractAuthStartTime", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long contractAuthStartTimeBegin;

    /**
     * 合同开始时间to
     */
    @Condition(field = "contractAuthStartTime", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long contractAuthStartTimeEnd;

    /**
     * 交付完成时间from
     */
    @Condition(field = "finishTimestamp", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long finishTimestampBegin;

    /**
     * 交付完成时间to
     */
    @Condition(field = "finishTimestamp", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long finishTimestampEnd;

    /**
     * 交付完成天数from
     */
    @Condition(field = "finishDaysCount", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Integer finishDaysCountBegin;

    /**
     * 交付完成天数to
     */
    @Condition(field = "finishDaysCount", expression = Expression.LESS_THAN_OR_EQUALS)
    private Integer finishDaysCountEnd;

    /**
     * 交付终止时间from
     */
    @Condition(field = "terminateTimestamp", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long terminateTimestampBegin;

    /**
     * 交付终止时间to
     */
    @Condition(field = "terminateTimestamp", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long terminateTimestampEnd;

    /**
     * 交付终止天数from
     */
    @Condition(field = "terminateDaysCount", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Integer terminateDaysCountBegin;

    /**
     * 交付终止天数to
     */
    @Condition(field = "terminateDaysCount", expression = Expression.LESS_THAN_OR_EQUALS)
    private Integer terminateDaysCountEnd;

    /**
     * 深度交付完成时间from
     */
    @Condition(field = "deepDeliveryFinishTimestamp", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long deepDeliveryFinishTimestampBegin;

    /**
     * 深度交付完成时间to
     */
    @Condition(field = "deepDeliveryFinishTimestamp", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long deepDeliveryFinishTimestampEnd;

    /**
     * 深度交付完成时间from
     */
    @Condition(field = "deepDeliveryFinishDaysCount", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long deepDeliveryFinishDaysCountBegin;

    /**
     * 深度交付完成时间to
     */
    @Condition(field = "deepDeliveryFinishDaysCount", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long deepDeliveryFinishDaysCountEnd;

    /**
     * 订单转化率 >= 60% 时间from
     */
    @Condition(field = "order60TargetCompletionTimestamp", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long order60TargetCompletionTimestampBegin;

    /**
     * 订单转化率 >= 60% 时间to
     */
    @Condition(field = "order60TargetCompletionTimestamp", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long order60TargetCompletionTimestampEnd;

    /**
     * 订单转化率 >= 60% 天数from
     */
    @Condition(field = "order60TargetCompletionDaysCount", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long order60TargetCompletionDaysCountBegin;

    /**
     * 订单转化率 >= 60% 天数to
     */
    @Condition(field = "order60TargetCompletionDaysCount", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long order60TargetCompletionDaysCountEnd;

    /**
     * 订单转化率 >= 90% 时间from
     */
    @Condition(field = "order90TargetCompletionTimestamp", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long order90TargetCompletionTimestampBegin;

    /**
     * 订单转化率 >= 90% 时间to
     */
    @Condition(field = "order90TargetCompletionTimestamp", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long order90TargetCompletionTimestampEnd;

    /**
     * 订单转化率 >= 90% 天数from
     */
    @Condition(field = "order90TargetCompletionDaysCount", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long order90TargetCompletionDaysCountBegin;

    /**
     * 订单转化率 >= 90% 天数to
     */
    @Condition(field = "order90TargetCompletionDaysCount", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long order90TargetCompletionDaysCountEnd;

    /**
     * 营业档口渗透率from
     */
    @Condition(field = "onlineStallPercent", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Double onlineStallPercentBegin;

    /**
     * 营业档口渗透率to
     */
    @Condition(field = "onlineStallPercent", expression = Expression.LESS_THAN_OR_EQUALS)
    private Double onlineStallPercentEnd;

    /**
     * 创建人
     */
    @Condition(field = "creatorUid", expression = Expression.IN)
    private List<Integer> creatorUid;

    /**
     * 首批档口交付时间from
     */
    @Condition(field = "firstBatchDeliveryTimestamp", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long firstBatchDeliveryTimestampBegin;

    /**
     * 首批档口交付时间to
     */
    @Condition(field = "firstBatchDeliveryTimestamp", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long firstBatchDeliveryTimestampEnd;

    /**
     * 第二批档口交付时间from
     */
    @Condition(field = "secondBatchDeliveryTimestamp", expression = Expression.GREATER_THAN_OR_EQUALS)
    private Long secondBatchDeliveryTimestampBegin;

    /**
     * 第二批档口交付时间to
     */
    @Condition(field = "secondBatchDeliveryTimestamp", expression = Expression.LESS_THAN_OR_EQUALS)
    private Long secondBatchDeliveryTimestampEnd;

    /**
     * 校企所属团队
     */
    @Condition(field = "campusKaOrgId", expression = Expression.IN)
    private List<Integer> campusKaOrgIds;

    /**
     * 校企经理
     */
    @Condition(field = "campusKaUid", expression = Expression.IN)
    private List<Integer> campusKaUid;

    /**
     * 校园送所属团队
     */
    @Condition(field = "channelOrgId", expression = Expression.IN)
    private List<Integer> channelOrgIds;

    /**
     * 渠道经理
     */
    @Condition(field = "channelUid", expression = Expression.IN)
    private List<Integer> channelManagerUid;

    /**
     * 城市所属团队
     */
    @Condition(field = "cityOrgId", expression = Expression.IN)
    private List<Integer> cityOrgIds;

    /**
     * 蜂窝负责人
     */
    @Condition(field = "aorOwnerUid", expression = Expression.IN)
    private List<Integer> aorOwnerUid;

    @Condition(field = "schoolName", expression = Expression.MATCH)
    private String schoolName;

    @Condition(field = "valid", expression = Expression.EQUALS)
    private Integer valid = 1;
}
