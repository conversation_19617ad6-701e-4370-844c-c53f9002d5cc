package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.sankuai.meituan.waimai.econtrct.client.domain.api.ExtendDataBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

public interface IWmEcontractSpInteractionWrapperService {

    ExtendDataBo wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException;

}
