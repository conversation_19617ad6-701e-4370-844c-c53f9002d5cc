package com.sankuai.meituan.waimai.customer.service.sign.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.adapter.DeliveryContractAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDeliveryTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.EcontractDeliveryBaseInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.EcontractDeliveryFeeInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.batch.EcontractBatchDeliveryBaseAndFeeInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.single.EcontractSingleDeliveryBaseAndFeeInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 班次送技术服务费获取
 *
 * Created by wangyongfang on 2024/5/22
 */
@Service
@Slf4j
public class FruitTogetherLogisticsFeeDataQueryHandler implements EcontractDataQueryHandler<EcontractDeliveryInfoBo> {
    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;

    @Autowired
    private DeliveryContractAdapter deliveryContractAdapter;

    public static final String SUPPORT_MARK = "support";
    @Override
    public EcontractDataSourceEnum sorceEnum() {
        return EcontractDataSourceEnum.FRUIT_TOGETHER_LOGISTICS_FEE;
    }
    @Override
    public Map<Long, EcontractDeliveryInfoBo> queryData(Map<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> map, long manualBatchId) throws WmCustomerException {
        // 校验对应来源的门店数据是否正确
        if (CollectionUtils.isEmpty(map.get(EcontractDataSourceEnum.FRUIT_TOGETHER_LOGISTICS_FEE))) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "技术服务费数据来源无对应门店数据");
        }
        // 获取门店ID列表
        List<EcontractDataPoiBizBo> econtractDataPoiBizBoList = map.get(EcontractDataSourceEnum.FRUIT_TOGETHER_LOGISTICS_FEE);
        // 根据门店列表获取签约数据
        String data = wmLogisticsContractThriftServiceAdapter.getTechFeeSignDataMultiPoiWithRetry(econtractDataPoiBizBoList, EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY);
        // 判断签约数据是否合法
        EcontractBatchDeliveryBaseAndFeeInfoBo batchBaseAndFeeInfoBo =  JSON.parseObject(data, EcontractBatchDeliveryBaseAndFeeInfoBo.class);
        if (batchBaseAndFeeInfoBo == null || MapUtils.isEmpty(batchBaseAndFeeInfoBo.getBatchBaseAndFeeInfoMap())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "获取到的技术服务费签约数据不合法");
        }
        // 组装返回数据
        Map<Long, EcontractDeliveryInfoBo> resultMap = new HashMap<>();
        Map<Long, EcontractSingleDeliveryBaseAndFeeInfoBo> batchBaseAndFeeInfoMap = batchBaseAndFeeInfoBo.getBatchBaseAndFeeInfoMap();
        // 履约服务费数据来源信息
        Map<String, String> perPoiAndBizMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(map.get(EcontractDataSourceEnum.FRUIT_TOGETHER_LOGISTICS_PERFORMANCE))) {
            perPoiAndBizMap = map.get(EcontractDataSourceEnum.FRUIT_TOGETHER_LOGISTICS_PERFORMANCE).stream()
                    .collect(Collectors.toMap(x -> String.valueOf(x.getWmPoiId()), x -> String.valueOf(x.getBizId()), (x, y) -> x));
        }

        for (Map.Entry<Long, EcontractSingleDeliveryBaseAndFeeInfoBo> entry : batchBaseAndFeeInfoMap.entrySet()) {
            EcontractDeliveryInfoBo deliveryInfoBo = assemblyDeliveryInfoBo(entry.getValue(), EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY);
            log.info("#queryData deliveryInfoBo:{}", JSONObject.toJSONString(deliveryInfoBo));
            // 如果门店有外部履约数据来源，对应字段赋值
            if (MapUtils.isNotEmpty(perPoiAndBizMap) && null != perPoiAndBizMap.get(deliveryInfoBo.getWmPoiId())) {
                deliveryInfoBo.setHasExternalPerInfo("true");
                deliveryInfoBo.setExternalPerInfoBizId(perPoiAndBizMap.get(deliveryInfoBo.getWmPoiId()));
            }
            log.info("queryData deliveryInfoBo:{}",JSON.toJSONString(deliveryInfoBo));
            resultMap.put(entry.getKey(), deliveryInfoBo);
        }
        log.info("#FruitTogetherLogisticsFeeDataQueryHandler queryData resultMap:{}",JSON.toJSONString(resultMap));
        return resultMap;
    }
    // 将技术服务费信息和基本信息
    private EcontractDeliveryInfoBo assemblyDeliveryInfoBo(EcontractSingleDeliveryBaseAndFeeInfoBo baseAndFeeInfoBo, EcontractTaskApplyTypeEnum applyTypeEnum) {
        EcontractDeliveryBaseInfoBo baseInfo = baseAndFeeInfoBo.getBaseInfo();
        Map<Integer, EcontractDeliveryFeeInfoBo> feeInfoMap = baseAndFeeInfoBo.getFeeInfoMap();
        return buildCommon(baseInfo, feeInfoMap, applyTypeEnum);
    }

    private EcontractDeliveryInfoBo buildCommon(EcontractDeliveryBaseInfoBo baseInfo, Map<Integer, EcontractDeliveryFeeInfoBo> feeInfoMap, EcontractTaskApplyTypeEnum applyTypeEnum) {
        EcontractDeliveryInfoBo econtractDeliveryInfoBo = new EcontractDeliveryInfoBo();
        // 构造主配字段信息
        BeanUtils.copyProperties(baseInfo, econtractDeliveryInfoBo);
        // 如果是无人机请求，则会返回无人机费率
        BeanUtils.copyProperties(feeInfoMap.get(EcontractDeliveryTypeEnum.FRUIT_TOGETHER.getType()), econtractDeliveryInfoBo);
        return econtractDeliveryInfoBo;
    }
}