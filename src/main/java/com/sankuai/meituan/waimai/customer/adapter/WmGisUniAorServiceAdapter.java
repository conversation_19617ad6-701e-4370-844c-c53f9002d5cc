package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.gis.client.thrift.constants.WmUniAorType;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.AorDto;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.LongitudeAndLatitudeDto;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.UniAor;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.option.AorAreaOption;
import com.sankuai.meituan.waimai.gis.client.thrift.service.WmGisUniAorService;
import com.sankuai.meituan.waimai.gis.client.thrift.service.request.AorBaseQueryRequest;
import com.sankuai.meituan.waimai.gis.client.thrift.service.response.AorBaseQueryResponse;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 外卖蜂窝平台化适配器
 * <AUTHOR>
 * @date 2024/06/02
 * @email <EMAIL>
 */
@Service
@Slf4j
public class WmGisUniAorServiceAdapter {

    @Autowired
    private WmGisUniAorService wmGisUniAorService;

    /**
     * 根据经纬度查询蜂窝信息
     * @param latitude 纬度
     * @param longitude 经度
     * @param uniAorType 蜂窝大类，1:直营 2:代理商。 -1:优先直营查不到查代理 枚举：WmUniAorType
     * @return AorDto
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public AorDto getAorByLatLngAndAorType(Integer uniAorType, Long latitude, Long longitude) throws WmSchCantException {
        try {
            AorBaseQueryRequest request = new AorBaseQueryRequest();
            request.setUniAorType(uniAorType);

            LongitudeAndLatitudeDto longitudeAndLatitudeDto = new LongitudeAndLatitudeDto();
            longitudeAndLatitudeDto.setLatitude(latitude);
            longitudeAndLatitudeDto.setLongitude(longitude);
            request.setLongitudeAndLatitudeDto(longitudeAndLatitudeDto);

            log.info("[WmGisUniAorServiceAdapter.getAorByLatLngAndAorType] request = {}", JSONObject.toJSONString(request));
            AorBaseQueryResponse response = wmGisUniAorService.queryAorBase(request);
            log.info("[WmGisUniAorServiceAdapter.getAorByLatLngAndAorType] response = {}", JSONObject.toJSONString(response));

            if (CollectionUtils.isEmpty(response.getAorDtoList())) {
                return null;
            }

            if (response.getAorDtoList().size() > 1) {
                log.error("[WmGisUniAorServiceAdapter.getAorByLatLngAndAorType] response.getAorDtoList() error. list = {}",
                        JSONObject.toJSONString(response.getAorDtoList()));
                throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "根据经纬度查询蜂窝异常");
            }

            return response.getAorDtoList().get(0);
        } catch (TException e) {
            log.error("[WmGisUniAorServiceAdapter.searchAorByLatLng] TException. latitude = {}, longitude = {}", latitude, longitude, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "根据经纬度查询蜂窝异常");
        }
    }

}
