package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.ddd.contract.DDDGrayUtil;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete.DeliveryPdfDelete;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit.DeliveryPdfSplit;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.*;
import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

@Service
@Slf4j
public class WmEcontractPdfDataSplitCollector implements IWmEcontractDataCollector {
    @Autowired
    private List<DeliveryPdfSplit> spliters;

    @Autowired
    private List<DeliveryPdfDelete> deleters;

    static Map<DeliveryPdfDataTypeEnum, DeliveryPdfSplit> pdfSplitMap = Maps.newHashMap();
    static Map<DeliveryPdfDataTypeEnum, DeliveryPdfDelete> pdfDeleteMap = Maps.newHashMap();

    public static final String SUPPORT_MARK = "support";

    @Autowired
    private WmEcontractSignGrayService wmEcontractSignGrayService;


    @PostConstruct
    public void init() {
        for (DeliveryPdfSplit spliter : spliters) {
            PdfSpliter annotation = AopUtils.getTargetClass(spliter).getAnnotation(PdfSpliter.class);
            if (annotation == null || annotation.deliveryPdfDataType() == null) {
                continue;
            }
            pdfSplitMap.put(annotation.deliveryPdfDataType(), spliter);
        }
        log.info("WmEcontractPdfDataSplitCollector#init, pdfSplitMap:{}", JSON.toJSONString(pdfSplitMap));
        for (DeliveryPdfDelete deleter : deleters) {
            PdfDeleter annotation = AopUtils.getTargetClass(deleter).getAnnotation(PdfDeleter.class);
            if (annotation == null || annotation.deliveryPdfDataType() == null) {
                continue;
            }
            pdfDeleteMap.put(annotation.deliveryPdfDataType(), deleter);
        }
        log.info("WmEcontractPdfDataSplitCollector#init, pdfDeleteMap:{}", JSON.toJSONString(pdfDeleteMap));
    }

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext) throws WmCustomerException, IllegalAccessException, TException {
        if (wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(originContext.getCustomerId())) {
            if(!hasPoifeeOrBatchPoiFeeTask(originContext)){
                log.info("WmEcontractPdfDataSplitCollector#collect，客户:{}无配送签约任务，跳过WmEcontractPdfDataSplitCollector", originContext.getCustomerId());
                return;
            }
            log.info("WmEcontractPdfDataSplitCollector#collect，客户:{}命中配送模板迁移灰度", originContext.getCustomerId());
            if (middleContext.getSignDataFactor().isDeliveryMultiWmPoi()) {
                batchPoifeeSplit(originContext, middleContext, targetContext);
            } else {
                poifeeSplit(originContext, middleContext, targetContext);
            }
            deleteNodataPdfTemplet(middleContext);
        }
    }

    private boolean hasPoifeeOrBatchPoiFeeTask(EcontractBatchContextBo originContext) throws WmCustomerException {
        AssertUtil.assertMapNotEmpty(originContext.getTaskIdAndTaskMap(), "任务信息不能为空");
        for (Map.Entry<Long, EcontractTaskBo> entry : originContext.getTaskIdAndTaskMap().entrySet()) {
            if (entry.getValue() != null && (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(entry.getValue().getApplyType())
                    || EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(entry.getValue().getApplyType())
                    || EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName().equals(entry.getValue().getApplyType())
                    || EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName().equals(entry.getValue().getApplyType())
            )){
                return true;
            }
        }
        return false;
    }

    private void poifeeSplit(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext) throws WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(originContext, middleContext.getSignDataFactor());
        EcontractDeliveryInfoBo tempDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        deliveryTypeRouter(tempDeliveryInfoBo, middleContext);
        EcontractDeliveryInfoBo contextDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        contextDeliveryInfoBo.setDeliveryTypeUUID(tempDeliveryInfoBo.getDeliveryTypeUUID());
        taskBo.setApplyContext(JSON.toJSONString(contextDeliveryInfoBo));
    }

    private void batchPoifeeSplit(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext) throws WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(originContext, middleContext.getSignDataFactor());
        EcontractBatchDeliveryInfoBo tempBatchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        for (EcontractDeliveryInfoBo deliveryInfoBo : tempBatchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            deliveryTypeRouter(deliveryInfoBo, middleContext);
        }
        taskBo.setApplyContext(JSON.toJSONString(tempBatchDeliveryInfoBo));
    }

    private void deliveryTypeRouter(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        //门店信息唯一标识
        String deliveryInfoUUID = UUID.randomUUID().toString().substring(0, 16);
        deliveryInfoBo.setDeliveryTypeUUID(deliveryInfoUUID);
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        log.info("deliveryTypeRouter begin，tabPdfMap:{}，pdfDataMap:{}", JSON.toJSONString(tabPdfMap), JSON.toJSONString(pdfDataMap));
        for (Map.Entry<DeliveryPdfDataTypeEnum, DeliveryPdfSplit> entry : pdfSplitMap.entrySet()) {
            entry.getValue().split(deliveryInfoBo, middleContext);
        }
        log.info("deliveryTypeRouter finish，tabPdfMap:{}，pdfDataMap:{}", JSON.toJSONString(tabPdfMap), JSON.toJSONString(pdfDataMap));
    }

    private void deleteNodataPdfTemplet(EcontractBatchMiddleBo middleContext) {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        log.info("deleteNodataPdfTemplet begin，tabPdfMap:{}，pdfDataMap:{}", JSON.toJSONString(tabPdfMap), JSON.toJSONString(pdfDataMap));
        for (Map.Entry<DeliveryPdfDataTypeEnum, DeliveryPdfDelete> entry : pdfDeleteMap.entrySet()) {
            entry.getValue().delete(tabPdfMap, pdfDataMap);
        }
        log.info("deleteNodataPdfTemplet finish，tabPdfMap:{}，pdfDataMap:{}", JSON.toJSONString(tabPdfMap), JSON.toJSONString(pdfDataMap));
    }


}
