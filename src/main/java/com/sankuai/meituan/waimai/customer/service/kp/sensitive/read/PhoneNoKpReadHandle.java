package com.sankuai.meituan.waimai.customer.service.kp.sensitive.read;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.constant.RegexConstant;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.decrypt.KeyDecrypt;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.decrypt.KeyDecryptHandleService;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.EncryptResult;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncrypt;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncryptHandleService;
import com.sankuai.meituan.waimai.customer.util.RegexUtil;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 读手机号
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
@Slf4j
public class PhoneNoKpReadHandle implements IKpReadHandle {


    @Autowired
    private KeyDecryptHandleService keyDecryptHandleService;

    @Autowired
    private KeyEncryptHandleService keyEncryptHandleService;


    @Override
    public KmsKeyNameEnum handleType() {
        return KmsKeyNameEnum.PHONE_NO;
    }

    @Override
    public void doReadChoiceEncryptToDecrypt(KpRead kpRead) {
        if (kpRead == null) {
            return;
        }
        if (kpRead.getKp() != null) {
            kpRead.getKp().setPhoneNum(getReadPhoneNo(kpRead.getKp().getPhoneNum(), kpRead.getKp().getPhoneNumEncryption()));
        }
        if (kpRead.getKpTemp() != null) {
            kpRead.getKpTemp().setPhoneNum(getReadPhoneNo(kpRead.getKpTemp().getPhoneNum(), kpRead.getKpTemp().getPhoneNumEncryption()));
        }
    }

    @Override
    public String doReadEncryptToDecrypt(KpRead kpRead) {
        log.debug("execute::doReadEncryptToDecrypt = {}", JSON.toJSONString(kpRead));
        //读新字段，新的加密字段解密赋值给原字段
        if (StringUtils.isBlank(kpRead.getEncryptionValue()) || kpRead == null) {
            return "";
        }
        KeyDecrypt keyDecrypt = new KeyDecrypt();
        keyDecrypt.setKeyName(kpRead.getKeyName());
        keyDecrypt.setValueForDecrypt(kpRead.getEncryptionValue());
        return keyDecryptHandleService.execute(keyDecrypt);
    }


    private String getReadPhoneNo(String source, String targetEncryption) {
        if (MccCustomerConfig.encryptionCustomerKpPhoneNumReadSwitch()) {
            //读原字段
            return source;
        } else {
            if (StringUtils.isBlank(targetEncryption)) {
                //如果新字段没有值，看是否老字段不合法Token失败，如果老字段Token无法加密则返回老字段
                if (!tryEncryption(KmsKeyNameEnum.PHONE_NO, source)) {
                    //如果不符合Token加密条件则读原字段
                    log.debug("getReadPhoneNo-NotRegexMatch 客户手机号不合法Token加密条件，使用原手机号字段::source = {},", source);
                    return source;
                } else {
                    return targetEncryption;
                }
            }
            //读新字段，新的加密字段解密赋值给原字段
            KeyDecrypt keyDecrypt = new KeyDecrypt();
            keyDecrypt.setKeyName(KmsKeyNameEnum.PHONE_NO);
            keyDecrypt.setValueForDecrypt(targetEncryption);
            return keyDecryptHandleService.execute(keyDecrypt);
        }
    }

    /**
     * 对字段尝试加密
     *
     * @param key
     * @param value
     * @return
     */
    private boolean tryEncryption(KmsKeyNameEnum key, String value) {
        try {
            KeyEncrypt keyEncrypt = new KeyEncrypt();
            keyEncrypt.setKeyName(key);
            keyEncrypt.setValueForEncrypt(value);
            EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
            if (result == null) {
                return false;
            }
        } catch (WmCustomerException e) {
            log.info("tryEncryption::key = {}, value = {},code={},msg={}", key, value, e.getCode(), e.getMsg());
            return false;
        }
        return true;
    }

}
