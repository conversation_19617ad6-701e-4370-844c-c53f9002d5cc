package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.bind;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240115
 * @desc 单店客户类型绑定校验
 */
@Slf4j
@Service
public class AggDistributeBindCheckStrategy implements IBindCheckStrategy {

    @Override
    public void checkBindByParams(CustomerPoiBindFlowContext context) throws WmCustomerException {
        try {

            //聚合配送商不支持绑定客户
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "'聚合配送商'客户不允许绑定门店");
        } catch (WmCustomerException e) {
            log.error("AggDistributeBindCheckStrategy,校验发生业务异常,context={}", JSON.toJSONString(context), e);
            throw e;
        } catch (Exception e) {
            log.error("AggDistributeBindCheckStrategy,校验发生异常,context={}", JSON.toJSONString(context), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
    }

    @Override
    public boolean hitCheck(Integer customerRealType) {
        return CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue() == customerRealType;
    }
}
