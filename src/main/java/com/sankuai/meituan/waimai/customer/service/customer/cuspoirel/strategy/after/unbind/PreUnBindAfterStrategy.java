package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.unbind;

import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IUnBindAfterStrategy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240123
 * @desc 预解绑后置策略
 */
@Service
@Slf4j
public class PreUnBindAfterStrategy implements IUnBindAfterStrategy {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Override
    public void execute(CustomerPoiUnBindFlowContext context) throws WmCustomerException {
        preUnbindAfterAction(context);
    }

    /**
     * 预解绑后置行为（签约解绑的后置操作行为）
     *
     * @param context
     * @throws WmCustomerException
     */
    private void preUnbindAfterAction(CustomerPoiUnBindFlowContext context) throws WmCustomerException {

        Integer customerId = context.getCustomerId();
        Integer opUid = context.getOpUid();
        String opName = context.getOpName();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        //新增客户维度的操作记录
        wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_TO_CONFIRM,
                        StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)),
                context.getRemark());

    }
}
