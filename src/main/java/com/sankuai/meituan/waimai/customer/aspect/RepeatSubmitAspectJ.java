package com.sankuai.meituan.waimai.customer.aspect;

import com.cip.crane.netty.utils.SleepUtils;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.meituan.waimai.customer.util.TairLocker;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import javassist.NotFoundException;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Aspect
@Component
public class RepeatSubmitAspectJ {
    private static final Logger logger = LoggerFactory.getLogger(RepeatSubmitAspectJ.class);

    @Autowired
    private TairLocker tairLocker;

    @Pointcut("@annotation(com.sankuai.meituan.waimai.customer.aspect.RepeatSubmission)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        MethodSignature method = ((MethodSignature) pjp.getSignature());

        RepeatSubmission singleSubmit = AopUtils.getMostSpecificMethod(method.getMethod(), pjp.getTarget().getClass())
                .getAnnotation(RepeatSubmission.class);
        if (singleSubmit == null) {
            return pjp.proceed();
        }
        Map<String, Object> argMap = getArgMap(pjp, method);
        if (!shouldIntercept(argMap, singleSubmit)) {
            logger.info("不需要进行防重复提交拦截 whenExp == false");
            return pjp.proceed();
        }
        if (singleSubmit.expire() <= 0) {
            logger.info("不需要进行防重复提交拦截 expire <= 0");
            return pjp.proceed();
        }
        String key = createKey(argMap, pjp, singleSubmit);

        lock(singleSubmit, key);

        return proceedAndUnlock(pjp, key);
    }

    private void lock(RepeatSubmission singleSubmit, String key) throws WmCustomerException {
        logger.info("尝试获取全局锁, key: {}", key);
        boolean isSucc = tairLocker.tryLock(key, singleSubmit.expire());
        if (!isSucc) {
            if (!singleSubmit.needWait()) {
                repeatCommitException(key);
                return;
            }
            wait(singleSubmit.expire(), key);
        }
    }

    private void repeatCommitException(String key) throws WmCustomerException {
        logger.info("任务处理中， 请不要重复提交. key:{}", key);
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "任务处理中， 请不要重复提交. " + key);
    }

    private Object proceedAndUnlock(ProceedingJoinPoint pjp, String key) throws Throwable {
        try {
            Object obj = pjp.proceed();
            return obj;
        } finally {
            try {
                tairLocker.unLock(key);
            } catch (Exception e) {
                logger.warn(e.getMessage(), e);
            }
            logger.info("退出全局锁, key: {}", key);
        }
    }

    private void wait(int expire, String key) throws WmCustomerException {
        long waitTime = 0;
        boolean isSucc = false;
        while (waitTime < expire) {
            SleepUtils.sleep(1 * 1000);
            waitTime += 1;
            logger.info("再次尝试获取全局锁, key: {}, 等待时间: {}/{} s", key, waitTime, expire);
            isSucc = tairLocker.tryLock(key, expire);
            if (isSucc) {
                break;
            }
        }
        if (!isSucc) {
            repeatCommitException(key);
        }
    }

    private boolean shouldIntercept(Map<String, Object> argMap, RepeatSubmission submission) {
        if (submission != null && StringUtils.isNotBlank(submission.whenExp())) {
            Expression expression = AviatorEvaluator.compile(submission.whenExp(), true);
            return (boolean) expression.execute(argMap);
        }
        return true;
    }

    private String createKey(Map<String, Object> argMap, ProceedingJoinPoint pjp, RepeatSubmission singleSubmit) {
        if (StringUtils.isNotEmpty(singleSubmit.seedPrefix())) {
            Expression expression = AviatorEvaluator.compile(singleSubmit.seedExp(), true);
            String seedVal = String.valueOf(expression.execute(argMap));
            return singleSubmit.seedPrefix() + "_" + seedVal;
        }

        MethodSignature method = (MethodSignature) pjp.getSignature();
        String className = pjp.getTarget().getClass().getName();
        String methodName = method.getMethod().getName();

        Expression expression = AviatorEvaluator.compile(singleSubmit.seedExp(), true);
        String seedVal = String.valueOf(expression.execute(argMap));

        return className + "$$" + methodName + "#par#" + seedVal;
    }

    private Map<String, Object> getArgMap(ProceedingJoinPoint pjp, MethodSignature method) throws NotFoundException {
        String[] parameterNames;
        if (AopUtils.isJdkDynamicProxy(pjp.getThis())) {
            parameterNames = ReflectionUtil.getParameterNames(pjp.getTarget().getClass(), method.getMethod().getName());
        } else {
            parameterNames = method.getParameterNames();
        }
        Object[] pjpArgs = pjp.getArgs();

        Map<String, Object> argMap = Maps.newHashMap();

        if (parameterNames != null) {
            for (int i = 0; i < parameterNames.length; i++) {
                argMap.put(parameterNames[i], pjpArgs[i]);
            }
        }
        return argMap;
    }

}
