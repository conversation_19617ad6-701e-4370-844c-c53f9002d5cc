package com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
@Data
public class KeyEncrypt {

    /**
     * 加密的字段key
     */
    private KmsKeyNameEnum keyName;

    /**
     * 加密的字段值
     */
    private String valueForEncrypt;


    /**
     * 证件类型（证件号加密时使用）
     */
    private int certType;


}
