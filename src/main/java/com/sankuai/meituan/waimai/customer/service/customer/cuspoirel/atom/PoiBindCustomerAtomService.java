package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.atom;

import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiRelService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;

/**
 * 门店绑定客户原子能力服务
 */
@Service
public class PoiBindCustomerAtomService {

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("CUSTOMER_POI_REL_POOL_%d").build();

    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(10, 30, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(200), THREAD_FACTORY,
                    new RejectedExecutionHandler() {
                        @Override
                        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                            if (!executorService.isShutdown()) {
                                try {
                                    executor.getQueue().put(r);
                                } catch (InterruptedException e) {
                                    throw new RejectedExecutionException("Reject from " + executor.toString());
                                }
                            } else {
                                throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                            }
                        }
                    }));

    /**
     * 门店直接绑定客户
     * 状态更新为：已绑定
     *
     * @param customerId
     * @param wmPoiIdSet
     */
    public void poiDirectBindCustomer(Integer customerId, Set<Long> wmPoiIdSet, Map<Long, Integer> poiAndTaskMaps) {
        if (CollectionUtils.isEmpty(poiAndTaskMaps)) {
            wmCustomerPoiRelService.batchInsertCustomerPoi(customerId, wmPoiIdSet);
        } else {
            wmCustomerPoiRelService.batchInsertCustomerPoiWithBizTaskId(customerId, wmPoiIdSet, poiAndTaskMaps);
        }
    }

    /**
     * 签约预绑定客户
     * 状态更新为：待发起确认
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param poiAndTaskMaps
     */
    public void poiToPreBindCustomer(Integer customerId, Set<Long> wmPoiIdSet, Map<Long, Integer> poiAndTaskMaps) {
        if (CollectionUtils.isEmpty(poiAndTaskMaps)) {
            wmCustomerPoiRelService.batchInsertCustomerPoiForPreBind(customerId, wmPoiIdSet, 0L);
        } else {
            wmCustomerPoiRelService.batchInsertCustomerPoiForPreBindWithBizTaskId(customerId, wmPoiIdSet, 0L, poiAndTaskMaps);
        }
    }

    /**
     * 门店预绑定确认中
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param poiTaskIds
     */
    public void poiPreBindConfirmDoing(Integer customerId, Set<Long> wmPoiIdSet, Map<Long, Integer> poiTaskIds) {

        //客户门店关系更新绑定确认中
        wmCustomerPoiRelService.applyPreBind(wmPoiIdSet, 0L, customerId, poiTaskIds);
    }

    /**
     * 取消门店预绑定客户:客户门店关系状态为-确认绑定失败
     *
     * @param customerId
     * @param wmPoiIdSet
     */
    public void cancelPoiPreBindCustomer(Integer customerId, Set<Long> wmPoiIdSet) {
        wmCustomerPoiRelService.cancelPreBind(wmPoiIdSet, 0L, customerId);
    }

}
