package com.sankuai.meituan.waimai.customer.domain.sc.delivery;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.util.DateUtils;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contract.ContractApplyInfoDto;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverInstanceDto;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverTeamInfoDto;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 校园交付计划聚合
 */
@Getter
public class WmSchoolDeliveryPlanAggr {

    /**
     * 交付计划id
     */
    private Long deliverId;

    /**
     * 外卖校园交付计划，从交付系统获取
     */
    private DeliverInstanceDto deliverInstance;

    /**
     * 外卖校园合同信息，从校园CRM获取
     */
    private ContractApplyInfoDto contractApplyDto;

    /**
     * 外卖校园信息，从校园CRM获取
     */
    private WmSchoolDB wmSchoolDB;

    /**
     * 外卖校园交付历史信息，从本地数据库获取
     */
    private WmSchoolDeliveryRecord wmSchoolDeliveryRecord;

    /**
     * 外卖校园交付现状信息，数据侧提供相关信息
     */
    private WmSchoolDeliveryCurrentSituation wmSchoolDeliveryCurrentSituation;

    @Builder
    public WmSchoolDeliveryPlanAggr(DeliverInstanceDto deliverInstance, ContractApplyInfoDto contractApplyDto, WmSchoolDB wmSchoolDB, WmSchoolDeliveryRecord wmSchoolDeliveryRecord, WmSchoolDeliveryCurrentSituation wmSchoolDeliveryCurrentSituation) {

        Preconditions.checkNotNull(deliverInstance);
        Preconditions.checkNotNull(contractApplyDto);
        Preconditions.checkNotNull(wmSchoolDB);

        this.deliverId = deliverInstance.getDeliverId();
        this.deliverInstance = deliverInstance;
        this.contractApplyDto = contractApplyDto;
        this.wmSchoolDB = wmSchoolDB;
        this.wmSchoolDeliveryRecord = Optional.ofNullable(wmSchoolDeliveryRecord).orElse(new WmSchoolDeliveryRecord(deliverId));
        this.wmSchoolDeliveryCurrentSituation = Optional.ofNullable(wmSchoolDeliveryCurrentSituation).orElse(new WmSchoolDeliveryCurrentSituation(deliverId));
    }

    /**
     * 计算交付完成天数
     * @return
     */
    public Integer getFinishDaysCount() {
        if (Objects.isNull(deliverInstance.getFinishTimestamp())) {
            return null;
        }
        return getDaysCount(deliverInstance.getCreateTimestamp(), deliverInstance.getFinishTimestamp());
    }

    /**
     * 计算深度交付完成天数
     * @return
     */
    public Integer getDeepFinishDaysCount() {
        if (Objects.isNull(wmSchoolDeliveryRecord.getDeepDeliveryFinishTimestamp())) {
            return null;
        }
        return getDaysCount(deliverInstance.getCreateTimestamp(), wmSchoolDeliveryRecord.getDeepDeliveryFinishTimestamp());
    }

    /**
     * 计算交付终止天数
     * @return
     */
    public Integer getTerminateDaysCount() {
        if (Objects.isNull(deliverInstance.getEndTimestamp())) {
            return null;
        }
        return getDaysCount(deliverInstance.getCreateTimestamp(), deliverInstance.getEndTimestamp());
    }

    /**
     * 订单转化率 >= 60% 天数
     */
    public Integer getOrder60TargetCompletionDaysCount() {
        if (Objects.isNull(wmSchoolDeliveryRecord.getOrder60TargetCompletionTimestamp())) {
            return null;
        }
        return getDaysCount(deliverInstance.getCreateTimestamp(), wmSchoolDeliveryRecord.getOrder60TargetCompletionTimestamp());
    }

    /**
     * 订单转化率 >= 90% 天数
     */
    public Integer getOrder90TargetCompletionDaysCount() {
        if (Objects.isNull(wmSchoolDeliveryRecord.getOrder90TargetCompletionTimestamp())) {
            return null;
        }
        return getDaysCount(deliverInstance.getCreateTimestamp(), wmSchoolDeliveryRecord.getOrder90TargetCompletionTimestamp());
    }

    /**
     * 获取校园校企经理uid
     * @return
     */
    public Integer getCampusKaUid() {
        return getTeamOwner(MccCustomerConfig.getCampusKaTeamCode());
    }

    /**
     * 获取渠道经理uid
     * @return
     */
    public Integer getChannelUid() {
        return getTeamOwner(MccCustomerConfig.getChannelTeamCode());
    }

    /**
     * 获取城市经理uid
     */
    public Integer getCityUid() {
        return getTeamOwner(MccCustomerConfig.getCityTeamCode());
    }

    /**
     * 更新品牌深度交付完成时间
     * @param timestamp
     */
    public void updateDeepDeliveryFinishTimestamp(Long timestamp) {
        this.getWmSchoolDeliveryRecord().setDeepDeliveryFinishTimestamp(timestamp);
    }

    /**
     * 更新订单转化率超过60%完成时间
     * @param timestamp
     */
    public void updateOrder60TargetCompletionTimestamp(Long timestamp) {
        this.getWmSchoolDeliveryRecord().setOrder60TargetCompletionTimestamp(timestamp);
    }

    /**
     * 更新订单转化率超过00%完成时间
     * @param timestamp
     */
    public void updateOrder90TargetCompletionTimestamp(Long timestamp) {
        this.getWmSchoolDeliveryRecord().setOrder90TargetCompletionTimestamp(timestamp);
    }

    /**
     * 根据teamCode获取对应负责人信息
     * @param teamCode
     * @return
     */
    private Integer getTeamOwner(String teamCode) {
        if (StringUtils.isBlank(teamCode)
                || CollectionUtils.isEmpty(deliverInstance.getDeliverTeamInfoDtoList())) {
            return null;
        }

        for (DeliverTeamInfoDto teamInfoDto : deliverInstance.getDeliverTeamInfoDtoList()) {
            if (teamCode.equals(teamInfoDto.getTeamCode())
                    && CollectionUtils.isNotEmpty(teamInfoDto.getTeamStaffDtoList())) {
                return teamInfoDto.getTeamStaffDtoList().get(0).getUid();
            }
        }
        return null;
    }

    /**
     * 计算交付持续日期
     * @param startTime
     * @param endTime
     * @return
     */
    private Integer getDaysCount(Long startTime, Long endTime) {
        Preconditions.checkNotNull(startTime);
        Preconditions.checkNotNull(endTime);

        return DateUtils.calculateBetweenDaysWithIgnore(startTime, endTime);
    }
}
