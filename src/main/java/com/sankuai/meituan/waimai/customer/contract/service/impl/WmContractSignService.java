package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignWithOutSignPhoneDB;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class WmContractSignService {

    @Autowired
    WmTempletContractSignAuditedDBMapper wmTempletContractSignAuditedDBMapper;

    @Autowired
    WmTempletContractSignDBMapper wmTempletContractSignDBMapper;


    /**
     * 注意区分是否需要使用加密字段：signPhone.
     * 不需要使用signPhone字段的话，请使用下面方法链接，避免大量解密操作以降低资源利用率
     * @see WmContractSignService#getAuditedPartyBSignerWithOutSignPhone(long)
     */
    public WmTempletContractSignBo getAuditedPartyBSigner(long templetId) {
//        log.info("getAuditedPartyBSigner#contractId:{}", templetId);
        List<WmTempletContractSignDB> templetContractSignDBList = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(templetId);
        return getPartyBSigner(WmTempletContractTransUtil.templetSignDbToBoList(templetContractSignDBList));
    }

    //使用非加解密字段
    public WmTempletContractSignBo getAuditedPartyBSignerWithOutSignPhone(long templetId) {
        List<WmTempletContractSignWithOutSignPhoneDB> templetContractSignDBList = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractIdWithOutSignPhone(templetId);
        return getPartyBSigner(WmTempletContractTransUtil.templetSignDbWithOutSignPhoneToBoList(templetContractSignDBList));
    }

    /**
     * 注意区分是否需要使用加密字段：signPhone.
     * 不需要使用signPhone字段的话，请使用下面方法链接，避免大量解密操作以降低资源利用率
     * @see WmContractSignService#getPartyBSignerWithOutSignPhone(long)
     */

    public WmTempletContractSignBo getPartyBSigner(long templetId) {
        List<WmTempletContractSignDB> templetContractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractId(templetId);
        return getPartyBSigner(WmTempletContractTransUtil.templetSignDbToBoList(templetContractSignDBList));
    }

    //使用非加解密字段
    public WmTempletContractSignBo getPartyBSignerWithOutSignPhone(long templetId) {
        List<WmTempletContractSignWithOutSignPhoneDB> templetContractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractIdWithOutSignPhone(templetId);
        return getPartyBSigner(WmTempletContractTransUtil.templetSignDbWithOutSignPhoneToBoList(templetContractSignDBList));
    }

    public WmTempletContractSignBo getPartyASigner(List<WmTempletContractSignBo> singList) {
        if (singList == null) {
            return null;
        }
        for (WmTempletContractSignBo signBo : singList) {
            if ("A".equals(signBo.getSignType())) {
                return signBo;
            }
        }
        return null;
    }

    public WmTempletContractSignDB getPartyASignerDb(List<WmTempletContractSignDB> singList) {
        if (singList == null) {
            return null;
        }
        for (WmTempletContractSignDB signBo : singList) {
            if ("A".equals(signBo.getSignType())) {
                return signBo;
            }
        }
        return null;
    }

    public WmTempletContractSignDB getPartyBSignerDb(List<WmTempletContractSignDB> singList) {
        if (singList == null) {
            return null;
        }
        for (WmTempletContractSignDB signBo : singList) {
            if ("B".equals(signBo.getSignType())) {
                return signBo;
            }
        }
        return null;
    }


    public WmTempletContractSignBo getPartyBSigner(List<WmTempletContractSignBo> singList) {
        if (singList == null) {
            return null;
        }
        for (WmTempletContractSignBo signBo : singList) {
            if ("B".equals(signBo.getSignType())) {
                return signBo;
            }
        }
        return null;
    }
}
