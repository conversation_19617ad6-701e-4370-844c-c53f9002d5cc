package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList;

import com.dianping.frog.sdk.data.DmlType;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.channel.beacon.thrift.vo.ModuleStateResVo;
import com.sankuai.meituan.waimai.customer.adapter.BeaconQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiListVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerPoiListInfoTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmColumnInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WmPoiStatusCheck implements InfoUpdateCheck {

    private static final String TABLE_NAME = "wm_poi_status";

    @Autowired
    private BeaconQueryThriftServiceAdapter beaconQueryThriftServiceAdapter;


    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Override
    public String check(String tableName, DmlType operateType, Map<String, WmColumnInfo> columnInfoMap) {
        if (!tableName.equals(TABLE_NAME) || operateType != DmlType.UPDATE) {
            return null;
        }
        int wmPoiId = MoreObjects.firstNonNull(Integer.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getNewValue()), 0);

        WmCustomerPoiListVo condition = new WmCustomerPoiListVo();
        condition.setWmPoiId(wmPoiId);
        condition.setPageNo(1);
        condition.setPageSize(10);
        List<WmCustomerPoiListInfoDTO> list = wmCustomerPoiListEsService.queryData(condition);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        Map<String, Object> map = getPoiModuleStatus((long) wmPoiId);
        if (map.isEmpty()) {
            return null;
        }

        StringBuffer errMsg = new StringBuffer();
        for (WmCustomerPoiListInfoDTO dto : list) {
            Integer baseStatus = (Integer) map.get(WmCustomerPoiListESFields.BASE_STATUS.getField());
            if (!dto.getBaseStatus().equals(baseStatus)) {
                errMsg.append(String.format("门店状态不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.BASE_STATUS.getField(), baseStatus, dto.getBaseStatus()));
            }
            Integer quaStatus = (Integer) map.get(WmCustomerPoiListESFields.QUA_STATUS.getField());
            if (!dto.getQuaStatus().equals(quaStatus)) {
                errMsg.append(String.format("门店状态不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.QUA_STATUS.getField(), quaStatus, dto.getQuaStatus()));
            }
            Integer deliveryStatus = (Integer) map.get(WmCustomerPoiListESFields.DELIVERY_STATUS.getField());
            if (!dto.getDeliveryStatus().equals(deliveryStatus)) {
                errMsg.append(String.format("门店状态不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.DELIVERY_STATUS.getField(), deliveryStatus, dto.getDeliveryStatus()));
            }
            Integer settleStatus = (Integer) map.get(WmCustomerPoiListESFields.SETTLE_STATUS.getField());
            if (!dto.getSettleStatus().equals(settleStatus)) {
                errMsg.append(String.format("门店状态不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.SETTLE_STATUS.getField(), settleStatus, dto.getSettleStatus()));
            }
            Integer serviceStatus = (Integer) map.get(WmCustomerPoiListESFields.SERVICE_STATUS.getField());
            if (!dto.getServiceStatus().equals(serviceStatus)) {
                errMsg.append(String.format("门店状态不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.SERVICE_STATUS.getField(), serviceStatus, dto.getServiceStatus()));
            }
            Integer productStatus = (Integer) map.get(WmCustomerPoiListESFields.PRODUCT_STATUS.getField());
            if (!dto.getProductStatus().equals(productStatus)) {
                errMsg.append(String.format("门店状态不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.PRODUCT_STATUS.getField(), productStatus, dto.getProductStatus()));
            }
        }
        return errMsg.toString();
    }

    private Map<String, Object> getPoiModuleStatus(Long wmPoiId) {
        ModuleStateResVo vo = beaconQueryThriftServiceAdapter.queryPoiModuleState(wmPoiId);
        if (vo == null || CollectionUtils.isEmpty(vo.getModules())) {
            return null;
        }
        Map<String, Object> map = Maps.newHashMap();
        WmCustomerPoiListInfoTransUtil.buildModuleStatus(vo, map);
        return map;
    }
}
