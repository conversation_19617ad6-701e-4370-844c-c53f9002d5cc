package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.unbind;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.CustomerPoiRelStrategyBean;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindSignNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IUnBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IUnBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IUnBindCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IUnBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.poirel.flow.CustomerPoiRelFlowEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240125
 * @desc 签约通知成功直接进行解绑规则-非切换的正常流程
 */
@Service
@Slf4j
@Rule
public class SignSucDirectUnBindRule {

    /**
     * 条件判断回调结果:成功&不存在门店为上线状态&门店均绑定在解绑客户下
     *
     * @param context
     * @return
     */
    @Condition
    public boolean when(@Fact("context") CustomerPoiUnBindFlowContext context) {
        UnBindSignNoticeDTO unBindSignNoticeDTO = context.getUnBindSignNoticeDTO();
        Integer callBackResult = unBindSignNoticeDTO.getSignResult();
        return callBackResult.equals(EcontractTaskStateEnum.SUCCESS.getIntValue())
                && !context.isExistOnlineUnBindWmPoiIdFlag()
                && CollectionUtils.isEmpty(context.getNotBindCustomerWmPoiIList());
    }

    /**
     * 根据策略执行
     *
     * @return
     */
    @Action
    public void execute(@Fact("context") CustomerPoiUnBindFlowContext context) throws WmCustomerException {
        log.info("SignSucDirectUnBindRule.execute,命中预解绑签约通知成功执行解绑规则,customerId={},wmPoiIdSet={}", context.getCustomerId(), JSON.toJSONString(context.getWmPoiIdSet()));
        context.setCustomerPoiUnBindTypeEnum(CustomerPoiUnBindTypeEnum.CONFIRM_UNBIND);

        //定义解绑各层级使用策略bean
        CustomerPoiRelStrategyBean customerPoiRelStrategyBean = CustomerPoiRelStrategyBean.builder()
                .checkBeanName("unBindSignNoticeCheckStrategy")
                .preCoreBeanName("unBindSignNoticePreCoreStrategy")
                .coreBeanName("directUnBindCoreStrategy")
                .afterBeanName("directUnBindAfterStrategy")
                .build();
        //根据策略bean构建解绑策略
        UnBindStrategy strategy = UnBindStrategy.buildUnBindStrategyByBean(customerPoiRelStrategyBean);
        //根据解绑策略构建解绑流程策略
        UnBindFlowStrategy unBindFlowStrategy = UnBindFlowStrategy.builder()
                .flowEnum(CustomerPoiRelFlowEnum.SIGN_FAIL_CANCEL_UNBIND)
                .wmPoiIdSet(context.getWmPoiIdSet())
                .unBindStrategy(strategy)
                .build();
        context.setUnBindFlowStrategyList(Lists.newArrayList(unBindFlowStrategy));
    }


    @Priority
    public int getPriority() {
        return 4;
    }


}
