package com.sankuai.meituan.waimai.customer.contract.service.impl.check.partner;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.mtcoop.thrift.dto.TNeedResignCoopRequest;
import com.sankuai.meituan.waimai.customer.adapter.agent.CustomerIdMappingServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.daocan.CoopQueryServiceAdapter;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.WmAndDcCustomerRelService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigTaskParseService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.DcC1ExchangeInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskSettleContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.EcontractDaoCanC1ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.Arrays.stream;

/**
 * @description: 保存到餐C2合同时, 判断到餐的C1合同是否需要换签
 * @author: liuyunjie05
 * @create: 2024/8/5 09:52
 */
@Slf4j
@Service
public class DaoCanC1ContractRenewSignValidator implements IContractValidator {

    @Resource
    private CustomerIdMappingServiceAdapter customerIdMappingServiceAdapter;

    @Resource
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;

    @Resource
    private CoopQueryServiceAdapter coopQueryServiceAdapter;

    @Resource
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;


    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        if (WmTempletContractTypeEnum.getByCode(contractBo.getBasicBo().getType()) != WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT) {
            return true;
        }

        boolean needCheckC1Renew = contractBo.getBasicBo().getDaoCanContractInfo().isNeedCheckC1Renew();
        if (!needCheckC1Renew) {
            return true;
        }

        Long dcCustomerId = customerIdMappingServiceAdapter.getOriginCustomerIdByMtCustomerId(contractBo.getBasicBo().getDaoCanContractInfo().getMtCustomerId());
        List<DcC1ExchangeInfo> exChangeInfoList = coopQueryServiceAdapter.getNeedResignContractInfo(buildTNeedResignCoopRequest(dcCustomerId));
        if (CollectionUtils.isEmpty(exChangeInfoList)) {
            return true;
        }

        int wmCustomerId = contractBo.getBasicBo().getParentId();
        String module = EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getName();

        List<String> inProcessingProof = getInProcessingProofs(wmCustomerId, module);

        List<DcC1ExchangeInfo> finalReNewContractList = exChangeInfoList.stream()
                .filter(exChangeInfo -> !inProcessingProof.contains(exChangeInfo.getContractProof()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(finalReNewContractList)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_FRAME_CONTRACT_DC_C1_EXCHANGE, JSON.toJSONString(finalReNewContractList));
        }
        return true;
    }

    private List<String> getInProcessingProofs(int wmCustomerId, String module) {
        List<String> inProcessingProof = new ArrayList<>();
        inProcessingProof.addAll(getManualTaskProofs(wmCustomerId, module));
        inProcessingProof.addAll(getInProcessingSignTaskProofs(wmCustomerId, module));
        return inProcessingProof;
    }

    private List<String> getManualTaskProofs(int wmCustomerId, String module) {
        List<WmEcontractSignManualTaskDB> manualTaskList = wmEcontractManualTaskBizService.getManualTaskByCustomerIdAndModule(wmCustomerId, module);
        if (CollectionUtils.isEmpty(manualTaskList)) {
            return Collections.emptyList();
        }
        return manualTaskList.stream()
                .map(task -> JSON.parseObject(task.getApplyContext(), ManualTaskSettleContextBo.class))
                .map(context -> context.getDaoCanContractContext().getContractProof())
                .collect(Collectors.toList());
    }

    private List<String> getInProcessingSignTaskProofs(int wmCustomerId, String module) {
        List<WmEcontractSignTaskDB> signTaskDBList = wmEcontractBigTaskParseService.getInProcessingTaskByCustomerIdAndType(wmCustomerId, module);
        if (CollectionUtils.isEmpty(signTaskDBList)) {
            return Collections.emptyList();
        }
        return signTaskDBList.stream()
                .map(task -> JSON.parseObject(task.getApplyContext(), EcontractDaoCanC1ContractInfoBo.class))
                .map(EcontractDaoCanC1ContractInfoBo::getContractProof)
                .collect(Collectors.toList());
    }

    private TNeedResignCoopRequest buildTNeedResignCoopRequest(Long dcCustomerId) {
        TNeedResignCoopRequest request = new TNeedResignCoopRequest();
        request.setPartnerId(Math.toIntExact(dcCustomerId));
        return request;
    }

}
