package com.sankuai.meituan.waimai.customer.contract.frame;

import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.request.ContractSignSubjectInfoQueryRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.response.ContractSignSubjectInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/2 17:01
 */
public interface WmCustomerFrameContractService {

    List<BusinessGroupInfo> getAuthBusinessGroupLine(AuthGroupLineQueryRequestDTO authGroupLineQueryRequestDTO) throws WmCustomerException, TException;

    List<CooperateModeQueryRespDTO> getAllCooperateMode(CooperateModeQueryRequestDTO cooperateModeQueryRequestDTO) throws WmCustomerException, TException;

    List<TaskTypeQueryRespDTO> getAllTaskType(TaskTypeQueryRequestDTO taskTypeQueryRequestDTO) throws WmCustomerException, TException;

    List<AgentDTO> queryAgent(AgentQueryRequestDTO agentQueryRequestDTO) throws WmCustomerException, TException;

    DcCustomerInfoPageData getDcContractCustomerInfo(DcCustomerInfoPageQueryRequestDTO requestDTO) throws WmCustomerException, TException;

    String getContractRouteRef(ContractRouteRefRequestDTO contractRouteRefRequestDTO) throws WmCustomerException;

    DcCustomerIdQueryRespDTO queryDcCustomerId(DcCustomerIdQueryDTO requestDTO) throws WmCustomerException, TException;

    String queryDcC1PdfUrl(DcC1PdfUrlRequestDTO requestDTO) throws WmCustomerException;

    DcContractCustomerVo queryDcCustomerInfo(DcCustomerInfoRequestDTO requestDTO) throws WmCustomerException;

    boolean hasCreateDcC1ContractAuth(DcContractAuthRequestDTO requestDTO) throws WmCustomerException;

    List<ContractSignSubjectInfo> queryContractSignSubjectInfo(Integer customerId) throws WmCustomerException;
}
