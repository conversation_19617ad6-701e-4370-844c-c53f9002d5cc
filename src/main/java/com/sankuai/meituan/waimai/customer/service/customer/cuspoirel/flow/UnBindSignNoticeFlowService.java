package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.flow;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiRelConstants;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindSignNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindStrategy;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnbindConfirmBo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiRelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.unbind.SignFailUnBindRule;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.unbind.SignSucDirectUnBindRule;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.unbind.SignSucExistNotBindPoiCancelUnBindRule;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.unbind.SignSucExistOnlinePoiCancelUnBindRule;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.jeasy.rules.api.RulesEngineParameters;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240124
 * @desc 预解绑签约回调通知流程服务
 */
@Service
@Slf4j
public class UnBindSignNoticeFlowService {
    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private CustomerPoiBaseValidator customerPoiBaseValidator;

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Autowired
    private CustomerPoiUnBindService customerPoiUnBindService;

    /**
     * 解绑签约回调使用的rule引擎
     */
    private static volatile Rules unBindSignNoticeRules = null;

    private static final Object lockObject = new Object();

    /**
     * 根据流程以及规则的绑定流程
     *
     * @param smsRecordDB
     * @param callBackResult
     */
    public boolean signUnBindNoticeFlow(WmCustomerPoiSmsRecordDB smsRecordDB, Integer callBackResult) throws WmCustomerException, TException {
        log.info("signUnBindNoticeFlow,预绑定签约回调通知根据规则判断执行流程,smsRecordDB={},callBackResult={}", JSON.toJSONString(smsRecordDB), callBackResult);
        //步骤1：构建上下文参数
        CustomerPoiUnBindFlowContext context = buildUnBindSignCallContext(smsRecordDB, callBackResult);
        //步骤2：初始化构建绑定规则
        initPreUnBindNoticeRule();
        //步骤3：执行规则设置处理策略
        executeRule(context);
        //步骤4：获取规则返回的流程策略并执行处理
        List<UnBindFlowStrategy> unBindFlowStrategyList = context.getUnBindFlowStrategyList();
        //步骤4.1 校验是否匹配到有效的处理策略，有效策略则执行处理
        if (checkMatchSignUnBindCallStrategy(unBindFlowStrategyList)) {
            log.info("signUnBindNoticeFlow,匹配到有效签约回调处理策略，开始处理解绑签约回调流程,unBindFlowStrategyList={}", JSON.toJSONString(unBindFlowStrategyList));
            //遍历解绑流程策略列表，执行解绑签约回调规则策略。
            for (UnBindFlowStrategy unBindFlowStrategy : unBindFlowStrategyList) {
                UnBindStrategy unBindStrategy = unBindFlowStrategy.getUnBindStrategy();
                unBindStrategy.execute(context);
            }
            log.info("signUnBindNoticeFlow,匹配到有效签约回调处理策略，解绑签约回调流程处理完成,unBindFlowStrategyList={}", JSON.toJSONString(unBindFlowStrategyList));
            return true;
        }

        //步骤4.2 未获取到有效规则需要执行原流程并告警
        log.info("signUnBindNoticeFlow,根据规则未匹配到有效签约回调处理策略,执行原解绑签约回调流程,customerId={},wmPoiIdSet={}",
                context.getCustomerId(), JSON.toJSONString(context.getWmPoiIdSet()));
        //添加告警
        Cat.logEvent(CustomerPoiRelConstants.UNBIND_RULE_STRATEGY_EVENT, CustomerPoiRelConstants.UNBIND_SIGN_CALL_BACK_NO_MATCH_STRATEGY);
        //签约回调原处理逻辑
        executeOldUnBindSignCallBack(smsRecordDB, callBackResult, context.getWmPoiIdSet());
        return true;
    }


    /**
     * 校验是否匹配到有效的处理策略
     *
     * @param unBindFlowStrategyList
     * @return
     */
    private boolean checkMatchSignUnBindCallStrategy(List<UnBindFlowStrategy> unBindFlowStrategyList) {
        if (CollectionUtils.isEmpty(unBindFlowStrategyList)) {
            return false;
        }
        //遍历解绑流程策略列表，执行解绑签约回调规则策略。
        for (UnBindFlowStrategy unBindFlowStrategy : unBindFlowStrategyList) {
            UnBindStrategy unBindStrategy = unBindFlowStrategy.getUnBindStrategy();
            if (unBindStrategy == null || unBindStrategy.getUnBindCheckStrategy() == null) {
                return false;
            }
        }
        return true;
    }

    /**
     * 构建解绑签约回调上下文
     *
     * @param wmCustomerPoiSmsRecordDB
     * @param callBackResult
     * @return
     */
    private CustomerPoiUnBindFlowContext buildUnBindSignCallContext(WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB,
                                                                    Integer callBackResult) throws WmCustomerException {
        Integer customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        //解绑签约处理的门店ID列表
        Set<Long> wmPoiIdSet = convertWmPoiIds(wmCustomerPoiSmsRecordDB.getWmPoiIds());

        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID不存在");
        }

        //构建解绑签约回调处理的上下文
        CustomerPoiUnBindFlowContext unBindCustomerFlowContext = CustomerPoiUnBindFlowContext
                .builder()
                .customerId(customerId)
                .wmPoiIdSet(wmPoiIdSet)
                .opUid(0)
                .opName(CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT)
                .wmCustomerDB(wmCustomerDB)
                .unBindSignNoticeDTO(UnBindSignNoticeDTO
                        .builder()
                        .wmCustomerPoiSmsRecordDB(wmCustomerPoiSmsRecordDB)
                        .signResult(callBackResult)
                        .signUnBindRelTaskId(wmCustomerPoiSmsRecordDB.getTaskId())
                        .build())
                .notBindCustomerWmPoiIList(wmCustomerPoiRelService.selectNotCustomerIdByWmPoiIdsRT(wmPoiIdSet, customerId))
                .existOnlineUnBindWmPoiIdFlag(customerPoiBaseValidator.checkExistsOnlineWmPoiId(wmPoiIdSet))
                .needUnBindWmPoiIdSet(wmCustomerPoiRelService.selectExistPoiByWmPoiId(wmPoiIdSet))
                .build();
        return unBindCustomerFlowContext;

    }

    /**
     * 字符串形式的wmPoiIds转成Set
     *
     * @param wmPoiIds
     * @return
     * @throws WmCustomerException
     */
    private Set<Long> convertWmPoiIds(String wmPoiIds) throws WmCustomerException {
        if (StringUtils.isEmpty(wmPoiIds)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "当前任务ID的门店为空");
        }
        String[] wmPoiIdArray = wmPoiIds.split(CustomerConstants.SPLIT_SYMBOL);
        Set<Long> wmPoiIdSet = Sets.newHashSet();
        for (String wmPoiId : wmPoiIdArray) {
            wmPoiIdSet.add(Long.parseLong(wmPoiId));
        }
        return wmPoiIdSet;
    }


    /**
     * 初始化绑定规则
     */
    private void initPreUnBindNoticeRule() {
        //规则引擎非空则直接返回
        if (unBindSignNoticeRules != null) {
            return;
        }
        //初始化注册规则-注册规则
        synchronized (lockObject) {
            if (unBindSignNoticeRules != null) {
                return;
            }
            //注册规则
            unBindSignNoticeRules = new Rules();
            //预绑定回调通知失败规则
            unBindSignNoticeRules.register(new SignFailUnBindRule());
            //通知成功&门店不存在或存在上线状态门店-取消解绑规则
            unBindSignNoticeRules.register(new SignSucExistOnlinePoiCancelUnBindRule());
            //通知成功&存在门店未绑定在客户上-取消解绑规则
            unBindSignNoticeRules.register(new SignSucExistNotBindPoiCancelUnBindRule());
            //通知成功&不存在上线门店&门店均绑定在解绑客户下-直接解绑规则
            unBindSignNoticeRules.register(new SignSucDirectUnBindRule());
        }
    }


    /**
     * 执行规则
     *
     * @param context
     */
    private void executeRule(CustomerPoiUnBindFlowContext context) {
        RulesEngineParameters parameters = new RulesEngineParameters().skipOnFirstAppliedRule(true);
        RulesEngine rulesEngine = new DefaultRulesEngine(parameters);
        //执行规则
        Facts facts = new Facts();
        facts.put("context", context);
        rulesEngine.fire(unBindSignNoticeRules, facts);
    }

    /**
     * 执行原解绑签约回调处理流程
     */
    private void executeOldUnBindSignCallBack(WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB,
                                              Integer callBackResult, Set<Long> wmPoiIdSet) throws WmCustomerException {
        Integer customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(callBackResult,
                wmCustomerPoiSmsRecordDB.getTaskId());
        if (callBackResult == EcontractTaskStateEnum.SUCCESS.getType()) {
            customerPoiUnBindService.confirmUnBind(new WmCustomerPoiUnbindConfirmBo(customerId, wmPoiIdSet,
                    0, CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT, CustomerPoiUnBindTypeEnum.CONFIRM_UNBIND,
                    WmCustomerPoiOplogSourceTypeEnum.CONFIRM_UNBIND, wmCustomerPoiSmsRecordDB.getId()));
        } else if (callBackResult == EcontractTaskStateEnum.FAIL.getType()) {
            customerPoiUnBindService.cancelUnBind(new WmCustomerPoiUnbindConfirmBo(customerId, wmPoiIdSet,
                    0, CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT, CustomerPoiUnBindTypeEnum.CONFITM_CANCEL_UNBIND,
                    null, wmCustomerPoiSmsRecordDB.getId()));
        }
    }
}
