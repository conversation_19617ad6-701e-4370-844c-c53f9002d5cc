package com.sankuai.meituan.waimai.customer.contract.service.impl.check.config;

import com.sankuai.meituan.waimai.customer.adapter.config.FoodMachinePoiAuthorizeAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 校验是否可以发起食光机一口价框架合同
 *
 * @Author: wangyongfang
 * @Date: 2024-12-26
 */

@Slf4j
@Service
public class FoodMachineCustomerInfoValidator implements IContractValidator {

    @Resource
    private FoodMachinePoiAuthorizeAdapter foodMachinePoiAuthorizeAdapter;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {

        if (ContractSourceEnum.isCodeSource(contractBo.getBasicBo().getContractSource())) {
            return true;
        }
        int contractType = contractBo.getBasicBo().getType();
        // 判断当前合同是否是食光机一口价合同
        if (MccConfig.getFoodMachineContractTemplateId() != contractType) {
            return true;
        }
        if (contractBo.getBasicBo().getParentId() <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户ID信息异常");
        }
        boolean canSign = foodMachinePoiAuthorizeAdapter.isCustomerFoodMachinePoiAuthorized((long) contractBo.getBasicBo().getParentId(), contractType);

        if (!canSign) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该客户无法签约食光机一口价合同，请联系食光机运营同学");
        }
        return true;
    }
}
