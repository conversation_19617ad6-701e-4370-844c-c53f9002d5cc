package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 国补履约协议废除校验器
 * @author: liuyunjie05
 * @create: 2025/5/24 09:48
 */
@Service
@Slf4j
public class NationalSubsidyPurchaseInvalidValidator implements IContractValidator {

    @Resource
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        Long contractId = contractBo.getBasicBo().getTempletContractId();
        WmTempletContractDB wmTemplateContractPo = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        log.info("NationalSubsidyPurchaseInvalidValidator#valid, wmTemplateContractPo: {}", JSON.toJSONString(wmTemplateContractPo));
        if (wmTemplateContractPo == null) {
            log.warn("NationalSubsidyPurchaseInvalidValidator#valid, 废除合同校验器，contractId: {}, 不存在合同", contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在合同，不可废除");
        }
        if (wmTemplateContractPo.getStatus() == CustomerContractStatus.EFFECT.getCode()) {
            return true;
        }
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态不允许操作废除");
    }
}
