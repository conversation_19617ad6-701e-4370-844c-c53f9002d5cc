package com.sankuai.meituan.waimai.customer.domain.sc;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditResultEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditSystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WmScCanteenAuditNodeDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 审批任务ID，即审核信息表主键ID
     */
    private Integer auditTaskId;
    /**
     * 审批系统类型
     * {@link CanteenInfoAuditSystemTypeEnum}
     */
    private Integer auditSystemType;
    /**
     * 审批系统ID
     */
    private String auditSystemId;
    /**
     * 审批节点
     * {@link CanteenInfoAuditNodeTypeEnum}
     */
    private Integer auditNode;
    /**
     * 审批人UID
     */
    private Integer auditorUid;
    /**
     * 审批人MIS
     */
    private String auditorMis;
    /**
     * 审批人姓名
     */
    private String auditorName;
    /**
     * 审批结果
     * {@link CanteenInfoAuditResultEnum}
     */
    private Integer auditResult;
    /**
     * 审批原因
     */
    private String auditRemark;
    /**
     * 审批时间
     */
    private Integer auditTime;
    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer valid;
    /**
     * 创建人UID
     */
    private Long cuid;
    /**
     * 修改人UID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;

}
