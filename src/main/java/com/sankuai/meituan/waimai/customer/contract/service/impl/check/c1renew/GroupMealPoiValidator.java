package com.sankuai.meituan.waimai.customer.contract.service.impl.check.c1renew;

import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Service
@Slf4j
public class GroupMealPoiValidator implements IContractValidator {

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        WmPoiAggre wmPoiAggre = C1RenewPoiThreadLocalUtil.get();
        log.info("GroupMealPoiValidator#valid，customerId:{}，poiId:{}，wmPoiAggre:{}",
                contractBo.getBasicBo().getParentId(),
                wmPoiAggre == null ? "null" : wmPoiAggre.getWm_poi_id(),
                JSON.toJSONString(wmPoiAggre));
        if (wmPoiAggre != null) {
            boolean validateResult = false;
            List<Long> filteredPoiTagIds = MccConfig.getFilteredPoiTagIds();
            for (Long tagId : filteredPoiTagIds) {
                if (tagId == null) {
                    continue;
                }
                WmPoiLabelRel poiLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(
                        wmPoiAggre.getWm_poi_id(), tagId, LabelSubjectTypeEnum.POI.getCode());
                if (poiLabel != null && poiLabel.getId() > 0) {
                    validateResult = true;
                    break;
                }
            }
            log.info("GroupMealPoiValidator#valid, filteredPoiTagIds: {}, validateResult: {}, wmPoiId: {}", filteredPoiTagIds, validateResult, wmPoiAggre.getWm_poi_id());
            return validateResult;
        }
        return true;
    }
}