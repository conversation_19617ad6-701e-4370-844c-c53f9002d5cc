package com.sankuai.meituan.waimai.customer.service.sign.mafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.adapter.EcontractManagerServiceAdapter;
import com.sankuai.meituan.waimai.customer.constant.EcontractUserToken;
import com.sankuai.meituan.waimai.customer.constant.SignPackStatusConstant;
import com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.WmContractForSwitchService;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignPackDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.WmEcontractCallbackService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-04-09 15:25
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class MafkaSignPackStatusUpdateConsumer implements IMessageListener {

    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Autowired
    private WmEcontractSignPackDBMapper wmEcontractSignPackDBMapper;

    @Autowired
    private EcontractAPIService econtractAPIService;

    @Autowired
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;

    @Autowired
    private WmContractForSwitchService wmContractForSwitchService;

    @Resource
    private WmEcontractCallbackService wmEcontractCallbackService;

    @Autowired
    private WmEcontractBatchBizService wmEcontractBatchBizService;

    @Resource
    private EcontractManagerServiceAdapter econtractManagerServiceAdapter;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        log.info("MafkaSignPackStatusUpdateConsumer消费开始:{}, threadName:{}", message, Thread.currentThread().getName());
        if (message != null) {
            String messageBody = message.getBody().toString();
            log.info("MafkaSignPackStatusUpdateConsumer#messageBody:{}", messageBody);
            try {
                JSONObject jsonObject = JSONObject.parseObject(messageBody);
                String tryStatus = jsonObject.getString("tryStatus");
                if (tryStatus.equals("allapply")) {
                    handlerTryToAllApply(jsonObject);
                } else if (tryStatus.equals("end")) {
                    handlerTryToEnd(jsonObject);
                } else {
                    log.error("MafkaSignPackStatusUpdateConsumer未知状态类型，message:{}", message);
                }
            } catch (Exception e) {
                log.error("MafkaSignPackStatusUpdateConsumer消费失败，message:{}", message, e);
                Cat.logMetricForCount("pack_status_update_error");
                return ConsumeStatus.RECONSUME_LATER;
            }
        }
        log.info("MafkaSignPackStatusUpdateConsumer消费结束:{},threadName:{}", message,Thread.currentThread().getName());
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void handlerTryToAllApply(JSONObject jsonObject) throws WmCustomerException, TException {
        Long signPackId = jsonObject.getLong("signPackId");
        List<Long> batchIdList = JSONObject.parseArray(jsonObject.getString("batchIdList"), Long.class);
        boolean isAllApply = false;
        for (Long batchId : batchIdList) {
            isAllApply = wmEcontractSignPackService.trySignPackStatusToAllApply(signPackId, batchId);
        }
        if (isAllApply) {
            //发送批次完成请求
            Long recordBatchId = wmEcontractSignPackDBMapper.selectByPrimaryKey(signPackId).getRecordBatchId();
            econtractAPIService.completeApplyBatchRecord(EcontractUserToken.WAIMAI_CONTRACT, recordBatchId);
            econtractManagerServiceAdapter.sendBatchSignTaskSms(recordBatchId);
            log.info("打包任务提交完成，触发电子合同更新complete，packId:{},econtractRecordId:{}", signPackId, recordBatchId);
        }
    }

    private void handlerTryToEnd(JSONObject jsonObject) throws WmCustomerException {
        Long batchId = jsonObject.getLong("batchId");
        WmEcontractSignBatchDB batchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
        EcontractBatchContextBo batchContextBo = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
        EcontractNotifyBo notifyBo = JSONObject.parseObject(jsonObject.getString("notifyBo"), EcontractNotifyBo.class);
        Long signPackId = batchDB.getPackId();
        try {
            List<WmEcontractSignBatchDB> batchDBList = wmEcontractBigBatchParseService.queryBatchListByPackIdMaster(batchDB.getPackId());
            Map<Long, String> batchStatusMap = batchDBList.stream().collect(Collectors.toMap(WmEcontractSignBatchDB::getId, WmEcontractSignBatchDB::getBatchState));
            log.info("batchId:{}试图更新pack状态时，当前pack下所有batch的任务状态为:{}", batchDB.getId(), JSON.toJSONString(batchStatusMap));
            if (wmEcontractSignPackService.isAllBatchSuccess(batchDBList)) {
                wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, SignPackStatusConstant.SUCCESS);
                log.info("pack状态更新为success，packId:{}", signPackId);
            } else if (wmEcontractSignPackService.hasBatchFail(batchDBList)) {
                WmEcontractSignPackDB wmEcontractSignPackDB = wmEcontractSignPackDBMapper.selectByPrimaryKey(signPackId);
                if(wmEcontractSignPackDB.getStatus().equals(SignPackStatusConstant.FAIL)){
                    log.info("pack状态更新已更新为fail，不再处理，packId:{}，batchId:{}", signPackId, batchDB.getId());
                    return;
                }
                wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, SignPackStatusConstant.FAIL);
                log.info("pack状态更新为fail，packId:{}", signPackId);
            } else if (wmEcontractSignPackService.hasBatchCancel(batchDBList)) {
                WmEcontractSignPackDB wmEcontractSignPackDB = wmEcontractSignPackDBMapper.selectByPrimaryKey(signPackId);
                if(wmEcontractSignPackDB.getStatus().equals(SignPackStatusConstant.CANCEL)){
                    log.info("pack状态更新已更新为fail，不再处理，packId:{}，batchId:{}", signPackId, batchDB.getId());
                    return;
                }
                wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, SignPackStatusConstant.CANCEL);
                log.info("pack状态更新为cancel，packId:{}", signPackId);
            }
        } catch (Exception e) {
            log.error("updateSignPackToEndStatus更新终态失败，signPackId:{}，batchId:{}", signPackId, batchDB.getId(), e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约状态更新异常");
        }
        try{
            BooleanResult result = laterHandler(batchContextBo, notifyBo);
            if(!result.isRes()){
                log.error("updateSignPackToEndStatus后续流程处理失败，signPackId:{}，batchId:{}", signPackId, batchDB.getId());
            }
        }catch(Exception e){
            log.error("updateSignPackToEndStatus后续流程处理异常，signPackId:{}，batchId:{}", signPackId, batchDB.getId(), e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "发起打包结果处理异常");
        }
    }


//    private void handlerTryToEndDeploying(JSONObject jsonObject) throws WmCustomerException {
//        WmEcontractSignBatchDB batchDB = null;
//        EcontractBatchContextBo batchContextBo = null;
//        EcontractNotifyBo notifyBo = null;
//        Long signPackId = 0L;
//        try {
//            batchDB = JSONObject.parseObject(jsonObject.getString("batchDB"), WmEcontractSignBatchDB.class);
//            batchContextBo = JSONObject.parseObject(jsonObject.getString("batchContextBo"), EcontractBatchContextBo.class);
//            notifyBo = JSONObject.parseObject(jsonObject.getString("notifyBo"), EcontractNotifyBo.class);
//            signPackId = batchDB.getPackId();
//            List<WmEcontractSignBatchDB> batchDBList = wmEcontractBigBatchParseService.queryBatchListByPackIdMaster(batchDB.getPackId());
//            Map<Long, String> batchStatusMap = batchDBList.stream().collect(Collectors.toMap(WmEcontractSignBatchDB::getId, WmEcontractSignBatchDB::getBatchState));
//            log.info("batchId:{}试图更新pack状态时，当前pack下所有batch的任务状态为:{}", batchDB.getId(), JSON.toJSONString(batchStatusMap));
//            if (wmEcontractSignPackService.isAllBatchSuccess(batchDBList)) {
//                wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, SignPackStatusConstant.SUCCESS);
//                log.info("pack状态更新为success，packId:{}", signPackId);
//            } else if (wmEcontractSignPackService.hasBatchFail(batchDBList)) {
//                WmEcontractSignPackDB wmEcontractSignPackDB = wmEcontractSignPackDBMapper.selectByPrimaryKey(signPackId);
//                if(wmEcontractSignPackDB.getStatus().equals(SignPackStatusConstant.FAIL)){
//                    log.info("pack状态更新已更新为fail，不再处理，packId:{}，batchId:{}", signPackId, batchDB.getId());
//                    return;
//                }
//                wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, SignPackStatusConstant.FAIL);
//                log.info("pack状态更新为fail，packId:{}", signPackId);
//            } else if (wmEcontractSignPackService.hasBatchCancel(batchDBList)) {
//                WmEcontractSignPackDB wmEcontractSignPackDB = wmEcontractSignPackDBMapper.selectByPrimaryKey(signPackId);
//                if(wmEcontractSignPackDB.getStatus().equals(SignPackStatusConstant.CANCEL)){
//                    log.info("pack状态更新已更新为fail，不再处理，packId:{}，batchId:{}", signPackId, batchDB.getId());
//                    return;
//                }
//                wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, SignPackStatusConstant.CANCEL);
//                log.info("pack状态更新为cancel，packId:{}", signPackId);
//            }
//        } catch (Exception e) {
//            log.error("updateSignPackToEndStatus更新终态失败，signPackId:{}，batchId:{}", signPackId, batchDB.getId(), e);
//            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约状态更新异常");
//        }
//        try{
//            BooleanResult result = laterHandler(batchContextBo, notifyBo);
//            if(!result.isRes()){
//                log.error("updateSignPackToEndStatus后续流程处理失败，signPackId:{}，batchId:{}", signPackId, batchDB.getId());
//            }
//        }catch(Exception e){
//            log.error("updateSignPackToEndStatus后续流程处理异常，signPackId:{}，batchId:{}", signPackId, batchDB.getId(), e);
//            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "发起打包结果处理异常");
//        }
//    }

    private BooleanResult laterHandler(EcontractBatchContextBo batchContextBo, EcontractNotifyBo notifyBo) throws WmCustomerException, TException{
        //兼容客户切换不下线
        wmContractForSwitchService.handleEContractSigned(batchContextBo, notifyBo);
        //回调业务系统
        return new BooleanResult(wmEcontractCallbackService.handleCallback(batchContextBo, notifyBo));
    }

}
