package com.sankuai.meituan.waimai.customer.util.trans;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfMakerConstant.DELIVERY_AGGREGATION_VERSION_V1;

import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryAggregationInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import java.util.Map;
import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR>
 */
public class PdfContentInfoBoMakerTransUtil {

    public static void extractDeliveryAggregationVersion(
            EcontractDeliveryInfoBo econtractDeliveryInfoBo, Map<String, String> result) {
        if (MapUtils.isEmpty(result)) {
            return;
        }
        if (econtractDeliveryInfoBo != null
                && econtractDeliveryInfoBo.getEcontractDeliveryAggregationInfoBo() != null) {
            result.put("deliveryAggregationVersion", MoreObjects.firstNonNull(
                    econtractDeliveryInfoBo.getEcontractDeliveryAggregationInfoBo()
                            .getDeliveryAggregationVersion(), DELIVERY_AGGREGATION_VERSION_V1));
        }
    }

    public static void extractAggregationExclusiveFeeDiscountFactor2(
            EcontractDeliveryInfoBo econtractDeliveryInfoBo, Map<String, String> result){
        if (MapUtils.isEmpty(result)) {
            return;
        }
        if(econtractDeliveryInfoBo == null){
            return;
        }
        EcontractDeliveryAggregationInfoBo econtractDeliveryAggregationInfoBo = econtractDeliveryInfoBo.getEcontractDeliveryAggregationInfoBo();
        if(econtractDeliveryAggregationInfoBo != null){
            result.put("aggregationExclusiveFeeDiscountFactor2", MoreObjects.firstNonNull(
                    econtractDeliveryAggregationInfoBo.getAggregationExclusiveFeeDiscountFactor2(), ""));
        }
    }
}
