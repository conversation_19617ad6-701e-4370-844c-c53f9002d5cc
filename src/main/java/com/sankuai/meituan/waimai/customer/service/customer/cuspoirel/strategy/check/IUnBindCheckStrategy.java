package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check;

import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

public interface IUnBindCheckStrategy {


    /**
     * 绑定校验策略
     *
     * @param context
     * @throws WmCustomerException
     */
    void checkUnBindByParams(CustomerPoiUnBindFlowContext context) throws WmCustomerException, TException;
}
