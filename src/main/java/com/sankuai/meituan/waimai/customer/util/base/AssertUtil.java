package com.sankuai.meituan.waimai.customer.util.base;

import cn.hutool.core.util.ReflectUtil;
import com.sankuai.meituan.waimai.customer.constant.common.CommonConst;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

public class AssertUtil {

    public static <T extends Comparable<? super T>> void assertEqual(T o1, T o2, String desc) throws WmCustomerException {
        if (ObjectUtils.compare(o1, o2) != 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc);
        }
    }

    public static void assertLongMoreThan0(Long in, String desc) throws WmCustomerException {
        if (in == null || in <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能为空");
        }
    }

    public static void assertIntegerMoreThan0(Integer in) throws WmCustomerException {
        assertIntegerMoreThan0(in, "输入");
    }

    public static void assertIntegerLessThanOrEqualVal(Long in, Long val, String desc) throws WmCustomerException {
        if (in == null || in > val) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不允许大于" + val);
        }
    }

    public static void assertIntegerLessThanOrEqualVal(Integer in, Integer val, String desc) throws WmCustomerException {
        if (in == null || in > val) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不允许大于" + val);
        }
    }

    public static void assertIntegerMoreThanOrEqual0(Integer in, String desc) throws WmCustomerException {
        if (in == null || in < 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能为空");
        }
    }

    public static void assertIntegerMoreThan0(Integer in, String desc) throws WmCustomerException {
        if (in == null || in <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能为空");
        }
    }

    public static void assertByteMoreThan0(Byte in, String desc) throws WmCustomerException {
        if (in == null || in <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能为空");
        }
    }

    public static <T> void assertObjectNotNull(T object) throws WmCustomerException {
        if (object == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "输入不能为空");
        }
    }

    public static <T> void assertObjectNotNull(T object, String desc) throws WmCustomerException {
        if (object == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能为空");
        }
    }

    public static <T> void assertCollectionNotEmpty(Collection<T> collection) throws WmCustomerException {
        if (CollectionUtils.isEmpty(collection)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "输入不能为空");
        }
    }

    public static <T> void assertCollectionNotEmpty(Collection<T> collection, String desc) throws WmCustomerException {
        if (CollectionUtils.isEmpty(collection)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能为空");
        }
    }

    public static <T> void assertCollectionLessThanOrEqualVal(Collection<T> collection, int val, String desc) throws WmCustomerException {
        if (collection == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能为空");
        }
        if (CollectionUtils.size(collection) > val) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能大于" + val);
        }
    }

    public static <K, V> void assertMapNotEmpty(Map<K, V> map, String desc) throws WmCustomerException {
        if (MapUtils.isEmpty(map)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc);
        }
    }

    public static void assertStringNotEmpty(String str, String desc) throws WmCustomerException {
        if (StringUtils.isEmpty(str)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, desc + "不能为空");
        }
    }



    /**
     * 验证响应code是否为成功
     *
     * @param response
     * @param <T>
     * @return
     */
    public static <T> boolean gatewayResponseSuccess(T response) {

        return Objects.nonNull(response) && Objects.equals(ReflectUtil.getFieldValue(response, CommonConst.CODE_MARK),
            CommonConst.SUCCESS);
    }
}
