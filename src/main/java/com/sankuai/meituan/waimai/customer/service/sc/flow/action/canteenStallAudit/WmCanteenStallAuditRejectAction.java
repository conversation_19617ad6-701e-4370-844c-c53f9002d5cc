package com.sankuai.meituan.waimai.customer.service.sc.flow.action.canteenStallAudit;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmCanteenStallAuditStatusEnum;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmCanteenStallAuditStatusMachineEvent;
import com.sankuai.meituan.waimai.customer.service.sc.flow.entity.WmCanteenStallAuditStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.sc.flow.exception.WmScStatusMachineException;
import com.sankuai.meituan.waimai.customer.service.sc.flow.machine.WmCanteenStallAuditStatusMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.squirrelframework.foundation.fsm.AnonymousAction;

/**
 * 食堂档口线索跟进状态-审批驳回动作
 * <AUTHOR>
 * @date 2024/05/25
 * @email <EMAIL>
 */
@Component
@Slf4j
public class WmCanteenStallAuditRejectAction extends AnonymousAction<WmCanteenStallAuditStatusMachine, WmCanteenStallAuditStatusEnum, WmCanteenStallAuditStatusMachineEvent, WmCanteenStallAuditStatusMachineContext> {

    @Autowired
    private WmCanteenStallAuditTaskService wmCanteenStallAuditTaskService;

    /**
     * 食堂档口线索跟进状态-审批驳回动作
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    public void execute(WmCanteenStallAuditStatusEnum fromState,
                        WmCanteenStallAuditStatusEnum toState,
                        WmCanteenStallAuditStatusMachineEvent event,
                        WmCanteenStallAuditStatusMachineContext context,
                        WmCanteenStallAuditStatusMachine stateMachine) {
        log.info("[WmCanteenStallAuditRejectAction.execute] input param: fromState = {}, toState = {}, event = {}, context = {}, stateMachine = {}",
                fromState, toState, event, context, stateMachine);
        WmCanteenStallAuditTaskBO auditTaskBO = context.getAuditTaskBO();
        try {
            wmCanteenStallAuditTaskService.rejectAuditTask(auditTaskBO);
        } catch (Exception e) {
            log.error("[WmCanteenStallAuditRejectAction.execute] WmSchCantException. auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO), e);
            throw new WmScStatusMachineException("食堂档口线索跟进状态审批驳回异常");
        }
    }

}
