package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList;

import com.dianping.frog.sdk.data.DmlType;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmPoiRelTableDbusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmColumnInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListUpdateInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WmCustomerPoiListMonitor {

    private static final String TABLE_NAME = "wm_poi_status";

    @Autowired
    private List<InfoUpdateCheck> checkList;

    public String check(WmCustomerPoiListUpdateInfo info) {
        String tableName = info.getTableName();
        DmlType operateType = DmlType.getTypeByCode(info.getOperateType());
        Map<String, WmColumnInfo> columnInfoMap = info.getColumnInfoMap();

        if (WmPoiRelTableDbusEnum.ofForAlas(tableName) == null && WmCustomerRelTableDbusEnum.ofForAlas(tableName) == null && !TABLE_NAME.equals(tableName)) {
            return "";
        }
        StringBuffer checkInfo = new StringBuffer();
        for (InfoUpdateCheck infoUpdateCheck : checkList) {
            String result = infoUpdateCheck.check(tableName, operateType, columnInfoMap);
            if (StringUtils.isNotBlank(result)) {
                checkInfo.append(result);
            }
        }

        if (checkInfo.length() > 0) {
            return checkInfo.toString();
        }

        return "";
    }
}
