/**
 *
 */
package com.sankuai.meituan.waimai.customer.contract.domain;

import lombok.Data;

import java.util.List;

/**
 * 新合同（框架合同）
 * Created by lishijie on 2018年4月9日.
 */
@Data
public class WmFrameContractData {

    public int contractId; // 合同id

    /**
     * 合同类型
     *
     * @see com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum
     */
    private int type;

    public String contractNo; // 合同编号
    public int contractExpireDate; // 合同有效期

    public String firstParty; // 甲方
    public String firstPartyPeople; // 甲方联系人
    public String firstPartyPhone; // 甲方联系人电话
    public int firstPartySignDate; // 甲方签约日期

    public String secondParty; // 乙方
    public String secondPartyPeople; // 乙方联系人
    public String secondPartyPhone; // 乙方联系人电话
    public int secondPartySignDate; // 乙方签约日期

    public String contractUrl; // 合同扫描件URL
    public List<String> otherContractScans; // 合同其他附件

}
