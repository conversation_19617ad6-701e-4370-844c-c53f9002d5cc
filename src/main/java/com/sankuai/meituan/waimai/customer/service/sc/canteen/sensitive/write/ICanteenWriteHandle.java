package com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.write;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
public interface ICanteenWriteHandle {

    /**
     * 执行策略类型
     *
     * @return
     */
    KmsKeyNameEnum handleType();

    /**
     * 策略具体执行方法
     *
     * @param canteenEntryWrite
     * @return
     */
    void doWriteWhenInsertOrUpdate(CanteenEntryWrite canteenEntryWrite) throws WmSchCantException;

    void writeSourceWhenUpdate(CanteenEntryWrite canteenEntryWrite);
}
