package com.sankuai.meituan.waimai.customer.constant.agreement;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2022/11/27 10:09 AM
 */
public enum QueryTypeEnum {
    /**
     * 待签约/已签约合同，查询
     */
    QUERY_TO_SIGN(1, "待签约"),
    QUERY_SIGNED(2, "已签约合同");

    private final int code;

    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    QueryTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
