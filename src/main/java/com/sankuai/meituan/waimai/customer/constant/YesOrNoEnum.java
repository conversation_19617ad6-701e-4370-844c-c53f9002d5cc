package com.sankuai.meituan.waimai.customer.constant;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 是否公共枚举
 */
public enum YesOrNoEnum {


    NO(0, "否"),
    YES(1, "是");

    private static Map<Integer, YesOrNoEnum> enumPreMap = Maps.newHashMap();

    static {
        for (YesOrNoEnum status : YesOrNoEnum.values()) {
            enumPreMap.put(status.getCode(), status);
        }
    }


    private Integer code;
    private String name;


    YesOrNoEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static Map<Integer, YesOrNoEnum> getEnumPreMap() {
        return enumPreMap;
    }
}
