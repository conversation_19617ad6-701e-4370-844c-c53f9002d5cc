package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.multi;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryWholeCityInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import lombok.extern.slf4j.Slf4j;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.DELIVERY_MULTI_WHOLE_CITY_INFO)
@Slf4j
@Service
public class WmEcontractMultiWholeCityInfoPdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker
        implements IWmEcontractPdfContentInfoBoMaker {

    @Autowired
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext,
            EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException, TException {
        log.info("#WmEcontractMultiWholeCityInfoPdfMaker");
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.DELIVERY_MULTI_WHOLE_CITY_INFO;

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.BATCHPOIFEE);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<EcontractDeliveryInfoBo> deliveryInfoList;
        if(wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(originContext.getCustomerId())){
            deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.WHOLECITY_INFO_DEFAULT.getName());
        } else {
            deliveryInfoList = batchDeliveryInfoBo.getEcontractDeliveryInfoBoList();
        }


        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        EcontractDeliveryWholeCityInfoBo wholeCityInfoBo = null;
        Map<String, String> mapTemp = null;
        String tableSupportNewModel = "";
        String tableSupportOldModel = "";
        for (EcontractDeliveryInfoBo temp : deliveryInfoList) {
            wholeCityInfoBo = temp.getEcontractDeliveryWholeCityInfoBo();
            if (wholeCityInfoBo == null) {
                continue;
            }
            wholeCityInfoBo.setDeliveryArea(null);
            mapTemp = MapUtil.Object2Map(wholeCityInfoBo);
            if (StringUtils.isNotEmpty(temp.getSupportNewModle())) {
                mapTemp.put("supportNewModle", temp.getSupportNewModle());
            }
            if (WmEcontractContextUtil.SUPPORT_MARK.equals(temp.getSupportNewModle())) {
                tableSupportNewModel = WmEcontractContextUtil.SUPPORT_MARK;
            } else {
                tableSupportOldModel = WmEcontractContextUtil.SUPPORT_MARK;
            }
            pdfBizContent.add(mapTemp);
        }

        Map<String, String> pdfMetaContent = Maps.newHashMap();

        // 全城送表格是否支持新老模式
        pdfMetaContent.put("tableSupportNewModel", tableSupportNewModel);
        pdfMetaContent.put("tableSupportOldModel", tableSupportOldModel);

        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("partA", StringUtils.defaultIfEmpty(originContext.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMetaContent.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        log.info("#WmEcontractMultiWholeCityInfoPdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }

}
