package com.sankuai.meituan.waimai.customer.service.global;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.global.IWmCustomerGlobalEcontractService;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.*;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerGlobalEcontractThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/10 15:01
 */
@Service
@Slf4j
public class WmCustomerGlobalEcontractThriftServiceImpl implements WmCustomerGlobalEcontractThriftService {


    @Resource
    private IWmCustomerGlobalEcontractService wmCustomerGlobalEcontractService;

    @Override
    public BaseResponse<EContractRoleInfoResponse> queryContractRole(PageInfoSearchParam pageInfoSearchParam) throws TException, WmCustomerException {
        log.info("WmCustomerGlobalEcontractThriftService#queryContractRole");
        BaseResponse<EContractRoleInfoResponse> contractRoleInfos = wmCustomerGlobalEcontractService.queryContractRole(pageInfoSearchParam);
        log.info("WmCustomerGlobalEcontractThriftService#queryContractRole");
        return contractRoleInfos;
    }

    @Override
    public BaseResponse<List<ContractTypeInfo>> queryContractType() throws TException, WmCustomerException {
        log.info("WmCustomerGlobalEcontractThriftService#queryContractType");
        BaseResponse<List<ContractTypeInfo>> contractTypeResponse = wmCustomerGlobalEcontractService.queryContractType();
        log.info("WmCustomerGlobalEcontractThriftService#queryContractType, contractTypeResponse: {}", JSON.toJSON(contractTypeResponse));
        return contractTypeResponse;
    }

    @Override
    public BaseResponse<BooleanResult> saveContractRole(ContractRoleSaveParam contractRoleSaveParam) throws TException, WmCustomerException {
        log.info("WmCustomerGlobalEcontractThriftService#saveContractRole");
        BaseResponse<BooleanResult> result = wmCustomerGlobalEcontractService.saveContractRole(contractRoleSaveParam);
        log.info("WmCustomerGlobalEcontractThriftService#saveContractRole");
        return result;
    }

    @Override
    public BaseResponse<UacRoleListResponse> queryUacAllRole(UacRuleQueryParam uacRuleQueryParam) throws TException, WmCustomerException {
        log.info("WmCustomerGlobalEcontractThriftService#queryUacAllRole");
        BaseResponse<UacRoleListResponse> result = wmCustomerGlobalEcontractService.queryUacAllRole(uacRuleQueryParam);
        log.info("WmCustomerGlobalEcontractThriftService#queryUacAllRole");
        return result;
    }

    @Override
    public BaseResponse<List<ContractStatusInfo>> queryContractStatus() throws TException, WmCustomerException {
        log.info("WmCustomerGlobalEcontractThriftService#queryContractStatus");
        BaseResponse<List<ContractStatusInfo>> contractStatusResponse = wmCustomerGlobalEcontractService.queryContractStatus();
        log.info("WmCustomerGlobalEcontractThriftService#queryContractType, contractStatusResponse: {}", JSON.toJSONString(contractStatusResponse));
        return contractStatusResponse;
    }

    @Override
    public BaseResponse<List<ContractCategoryInfo>> queryContractCategory() throws TException, WmCustomerException {
        log.info("WmCustomerGlobalEcontractThriftService#queryContractCategory");
        BaseResponse<List<ContractCategoryInfo>> contractCategoryResp = wmCustomerGlobalEcontractService.queryContractCategory();
        log.info("WmCustomerGlobalEcontractThriftService#queryContractCategory, contractCategoryResp: {}", JSON.toJSONString(contractCategoryResp));
        return contractCategoryResp;
    }

    @Override
    public BaseResponse<GlobalContractResponse> queryContractInfo(GlobalContractSearchParam globalContractSearchParam) throws TException, WmCustomerException {
        log.info("WmCustomerGlobalEcontractThriftService#queryContractInfo, searchParam: {}", JSON.toJSONString(globalContractSearchParam));
        BaseResponse<GlobalContractResponse> globalContractResponse = wmCustomerGlobalEcontractService.queryContractInfo(globalContractSearchParam);
        log.info("WmCustomerGlobalEcontractThriftService#queryContractInfo, globalContractResponse: {}", JSON.toJSONString(globalContractResponse));
        return globalContractResponse;
    }

    @Override
    public BaseResponse<Boolean> exportContractInfo(GlobalContractBaseParam exportParam) throws TException, WmCustomerException {
        log.info("WmCustomerGlobalEcontractThriftService#exportContractInfo, exportParam: {}", JSON.toJSONString(exportParam));
        BaseResponse<Boolean> exportResult = wmCustomerGlobalEcontractService.exportContractInfo(exportParam);
        log.info("WmCustomerGlobalEcontractThriftService#exportContractInfo, exportResult: {}", JSON.toJSONString(exportResult));
        return exportResult;
    }
}
