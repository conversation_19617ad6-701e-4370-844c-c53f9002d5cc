package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore;

import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * <AUTHOR>
 * @date 20240122
 * @desc 解绑前置核心操作策略
 */
public interface IUnBindPreCoreStrategy {

    /**
     * 执行方法
     *
     * @param context
     * @throws WmCustomerException
     */
    void execute(CustomerPoiUnBindFlowContext context) throws WmCustomerException;

}
