package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service;

import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleAuditContext;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditMsg;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditSettleCommitData;

/**
 * 结算录入-原子化服务接口-审核相关
 */
public interface WmSettleInputAuditAtomService {

    /**
     * 组装提审数据
     */
    WmAuditSettleCommitData buildWmAuditSettleCommitData(WmSettleAuditContext context)
            throws WmCustomerException;

    /**
     * 纸质签约备份提审数据
     * @param context
     * @throws WmCustomerException
     */
    void saveWmSettlePaperSignAuditDB(WmSettleAuditContext context) throws WmCustomerException;

    /**
     * 提审动作
     */
    WmAuditMsg commitAudit(WmSettleAuditContext context) throws WmCustomerException;


    /**
     * 纸质审核通过
     */
    BooleanResult paperContractSettleApprove(WmSettleAuditContext context)
            throws WmCustomerException;

    /**
     * 纸质审核驳回
     */
    BooleanResult paperContractSettleReject(WmSettleAuditContext context)
            throws WmCustomerException;

}
