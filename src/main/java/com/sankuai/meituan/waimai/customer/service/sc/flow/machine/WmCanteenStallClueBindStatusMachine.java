package com.sankuai.meituan.waimai.customer.service.sc.flow.machine;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmCanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmCanteenStallClueBindStatusMachineEvent;
import com.sankuai.meituan.waimai.customer.service.sc.flow.entity.WmCanteenStallClueBindStatusMachineContext;
import lombok.extern.slf4j.Slf4j;
import org.squirrelframework.foundation.fsm.impl.AbstractStateMachine;

/**
 * 食堂档口线索绑定状态机
 * <AUTHOR>
 * @date 2024/05/28
 * @email <EMAIL>
 */
@Slf4j
public class WmCanteenStallClueBindStatusMachine extends AbstractStateMachine<WmCanteenStallClueBindStatusMachine, WmCanteenStallClueBindStatusEnum, WmCanteenStallClueBindStatusMachineEvent, WmCanteenStallClueBindStatusMachineContext> {

    /**
     * 异常处理
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void afterTransitionCausedException(WmCanteenStallClueBindStatusEnum fromState,
                                                  WmCanteenStallClueBindStatusEnum toState,
                                                  WmCanteenStallClueBindStatusMachineEvent event,
                                                  WmCanteenStallClueBindStatusMachineContext context) {
        log.info("[WmCanteenStallClueBindStatusMachine.afterTransitionCausedException] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        super.afterTransitionCausedException(fromState, toState, event, context);
    }

    /**
     * 每次流转完成时更新表中状态
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void afterTransitionCompleted(WmCanteenStallClueBindStatusEnum fromState,
                                            WmCanteenStallClueBindStatusEnum toState,
                                            WmCanteenStallClueBindStatusMachineEvent event,
                                            WmCanteenStallClueBindStatusMachineContext context) {
        log.info("[WmCanteenStallClueBindStatusMachine.afterTransitionCompleted] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        super.afterTransitionCompleted(fromState, toState, event, context);
    }

    /**
     * 流程实例初始节点开始前
     * @param fromState 流转前状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void beforeTransitionBegin(WmCanteenStallClueBindStatusEnum fromState,
                                         WmCanteenStallClueBindStatusMachineEvent event,
                                         WmCanteenStallClueBindStatusMachineContext context) {
        log.info("[WmCanteenStallClueBindStatusMachine.beforeTransitionBegin] fromState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 流程实例初始节点流转后
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void afterTransitionEnd(WmCanteenStallClueBindStatusEnum fromState,
                                      WmCanteenStallClueBindStatusEnum toState,
                                      WmCanteenStallClueBindStatusMachineEvent event,
                                      WmCanteenStallClueBindStatusMachineContext context) {
        log.info("[WmCanteenStallClueBindStatusMachine.afterTransitionEnd] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 每一个action执行前
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void beforeActionInvoked(WmCanteenStallClueBindStatusEnum fromState,
                                       WmCanteenStallClueBindStatusEnum toState,
                                       WmCanteenStallClueBindStatusMachineEvent event,
                                       WmCanteenStallClueBindStatusMachineContext context) {
        log.info("[WmCanteenStallClueBindStatusMachine.beforeActionInvoked] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 每一个action执行后
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    protected void afterActionInvoked(WmCanteenStallClueBindStatusEnum fromState,
                                      WmCanteenStallClueBindStatusEnum toState,
                                      WmCanteenStallClueBindStatusMachineEvent event,
                                      WmCanteenStallClueBindStatusMachineContext context) {
        log.info("[WmCanteenStallClueBindStatusMachine.afterActionInvoked] fromState = {}, toState = {}, event = {}, context = {}",
                JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    @Override
    public WmCanteenStallClueBindStatusEnum getCurrentState() {
        return super.getCurrentState();
    }

    @Override
    public void start() {
        super.start();
    }

}
