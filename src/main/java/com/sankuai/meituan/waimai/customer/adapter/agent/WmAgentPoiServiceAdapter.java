package com.sankuai.meituan.waimai.customer.adapter.agent;

import com.meituan.waimai.agent.otter.enterprise.service.agentpoi.WmAgentPoiService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 查询外卖门店和代理商的关系
 * @author: liuyunjie05
 * @create: 2025/2/26 11:31
 */
@Service
@Slf4j
public class WmAgentPoiServiceAdapter {

    @Resource
    private WmAgentPoiService wmAgentPoiService;

    public int getAgentIdByWmPoiId(long poiId) throws WmCustomerException {
        int agentId = getAgentIdByPoiIdFromSlave(poiId);
        if (agentId > 0) {
            return agentId;
        }
        return getAgentIdByPoiIdFromMaster(poiId);
    }

    private int getAgentIdByPoiIdFromSlave(long poiId) {
        try {
            log.info("AgentPoiServiceAdapter#getAgentIdByPoiIdFromSlave, poiId: {}", poiId);
            int agentId = wmAgentPoiService.getAgentIdByPoiId(poiId);
            log.info("AgentPoiServiceAdapter#getAgentIdByPoiIdFromSlave, agentId: {}", agentId);
            return agentId;
        } catch (Exception e) {
            log.error("AgentPoiServiceAdapter#getAgentIdByPoiIdFromSlave, error", e);
            return 0;
        }
    }

    private int getAgentIdByPoiIdFromMaster(long poiId) throws WmCustomerException {
        try {
            log.info("AgentPoiServiceAdapter#getAgentIdByPoiIdFromMaster, poiId: {}", poiId);
            int agentId = wmAgentPoiService.getAgentIdByPoiIdFromMaster(poiId);
            log.info("AgentPoiServiceAdapter#getAgentIdByPoiIdFromMaster, agentId: {}", agentId);
            return agentId;
        } catch (Exception e) {
            log.error("AgentPoiServiceAdapter#getAgentIdByPoiIdFromMaster, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "查询门店代理商异常");
        }
    }

}
