package com.sankuai.meituan.waimai.customer.settle.grey;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.heron.settle.exception.WmHeronSettleException;
import com.sankuai.meituan.waimai.heron.settle.service.WmSettleMigrationThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WmSettleNvWaGrayService {

    @Autowired
    private WmSettleMigrationThriftService wmSettleMigrationThriftService;

    public boolean queryGreyResultByWmPoiId(long wmPoiId){
        try {
            return wmSettleMigrationThriftService.queryGreyResultByWmPoiId(wmPoiId).isRes();
        } catch (WmHeronSettleException | TException e) {
            log.error("queryGreyResultByWmPoiId exception",e);
        }
        return false;
    }

    public long getMerchantId(){
        return ConfigUtilAdapter.getLong("nvwa.merchant.no", 11000001316859L);
    }

    public long getKuaJingMerchantId(){
        return ConfigUtilAdapter.getLong("kuajing.merchant.no", 11000049100512L);
    }

    public String getPrivateKey(){
        //业务线私钥,签名和解密用
        String privateKey = null;
        try {
            privateKey = Kms.getByName("com.sankuai.waimai.e.customer", "nvwa_biz_private_key");
        } catch (KmsResultNullException e) {
            log.error("获取密钥失败, 使用默认密钥。");
        }
        if(StringUtils.isEmpty(privateKey)){
            privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAOWx77lDEDDzBepz8j6EnOqRdRJbopTcGMadxAUp+7gdOzf5EEmaOmFm5obPgK2DIVhdu1cktB5c/hXN0OxrjFb5EkBg42vZnkdhBVDRAnlt4lIQXaqIiRDALXKx1uB0AxVqsY7aWWLezOwKOxZBr0OG5sPPjyRVfMP1TDd28zQDAgMBAAECgYEAqpNqgD1CX+eKBCOYyT64PSZKN5jfcwbES3NL573W1lK28IlAxDgJlN3S4lhkqfZQpAvvnPZ/4HCUrGKM/ZWcwG22aueEAmySrkEWHOrLapUTksKgxsI9r64HDlQmbNoCecp5p+EA/wk3PTziWhnuDv+J5PP0G0JsFCn7bmZdS+kCQQD3xEQzOyAvs3/e3pdywUIL1R8qVTLGqHCUZQXsF4A2GdzX6Wj5+xrBII0kFxVqVUB3tyVV8KKYOYJCDuxU34V9AkEA7VPvqOvUTN3kp8joBRG/d7uIV1+KSHVA1arPIOZXmOsOyxPWemtQ2giDbIac7PV88P/S6xUxW66px6aDUQTXfwJBALQh5i+U+faUOxAups8QgFMX9FuTq6HQzmCk/eRCxb0PmocDNIYiMLZeUx79BV3Uc1VWOve7RosDrJcYQGwIQgUCQEp4vUqffgYE1xyDYbM9h3x132mzdDwadxbVO5vSTAiTMhgtiG7vv/Y7F6p1b0a4HdWuISxdgRiI7YPVz30b7NcCQFKVClppsoZ5ORnI1248lXHDK9WxxYSVP+c6RcVvn199vrjJJxOPAYjbk/wVdx2q4+OR8lUMy3QJtnCdb14Y2YQ=";
        }
        return privateKey;
    }

    public String getKuaJingPrivateKey(){
        //业务线私钥,签名和解密用
        String privateKey = null;
        try {
            privateKey = Kms.getByName("com.sankuai.waimai.e.customer", "kuajing_biz_private_key");
        } catch (KmsResultNullException e) {
            log.error("获取密钥失败, 使用默认密钥。");
        }
        return privateKey;
    }


}
