package com.sankuai.meituan.waimai.customer.service.sign.toSign;

import java.util.List;

import com.google.common.util.concurrent.ListeningExecutorService;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpFailTaskDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpRequest;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpResponse;

/**
 * @description: 待签约页顶部提示合同类型
 * @author: lixuepeng
 * @create: 2021-12-29
 **/
public interface WmEcontractToSignTipsHandler {

    /**
     * 合同名称
     */
    String getName();

    /**
     * 该门店是否需要签约该合同
     */
    Boolean isNeedSign(int customerId, long wmPoiId);

    /**
     * 该门店是否已签约中or生效过
     */
    Boolean hasEffectiveRecord(int customerId, long wmPoiId);
}
