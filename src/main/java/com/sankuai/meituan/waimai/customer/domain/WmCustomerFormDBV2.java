package com.sankuai.meituan.waimai.customer.domain;

import java.util.List;

public class WmCustomerFormDBV2 {
    private long customerId;//客户ID
    private String customerName;//客户名称
    private long wmPoiId;//门店ID
    private int ownerUid;//责任人
    private String keyword;//关键字(蜜蜂)
    private int pageSize;//页面大小
    private int pageNo;//页码
    private List<Long> customerIdList;

    public long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public long getWmPoiId() {
        return wmPoiId;
    }

    public void setWmPoiId(long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    public int getOwnerUid() {
        return ownerUid;
    }

    public void setOwnerUid(int ownerUid) {
        this.ownerUid = ownerUid;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public List<Long> getCustomerIdList() {
        return customerIdList;
    }

    public void setCustomerIdList(List<Long> customerIdList) {
        this.customerIdList = customerIdList;
    }
}
