//package com.sankuai.meituan.waimai.customer.ddd.domain.kp.atom.service;
//
//import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//
//import java.util.List;
//
//public interface IWmCustomerKpAtomService {
//
//    /**
//     * 获取客户生效的签约人KP，后期可能会改成一个客户有多个签约人，所以返回值用List
//     * @param customerId
//     *    客户ID
//     * @return
//     *    返回生效的签约人KP信息，没有则返回null
//     */
//    public WmCustomerKp getCustomerKpOfEffectiveSigner(int customerId);
//
//
//    /**
//     * 获取客户KP列表
//     * @param customerId
//     *    客户ID
//     * @return
//     *    客户KP列表对象
//     */
//    public List<WmCustomerKp> getCustomerKpList(int customerId);
//
//
//    /**
//     * 批量添加或修改客户KP信息，来自PC
//     * @param wmCustomerKpList
//     */
//    public void batchSaveOrUpdateCustomerKp(int customerId, List<WmCustomerKp> wmCustomerKpList, int uid, String uname) throws WmCustomerException, TException;
//}

