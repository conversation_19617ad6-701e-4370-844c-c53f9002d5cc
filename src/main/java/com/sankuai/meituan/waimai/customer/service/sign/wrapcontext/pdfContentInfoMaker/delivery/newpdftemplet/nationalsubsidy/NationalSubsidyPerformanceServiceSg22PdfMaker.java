package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet.nationalsubsidy;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

/**
 * @description: 国补-闪购2.2-非新快送-履约合同
 * @author: liuyunjie05
 * @create: 2025/5/27 19:07
 */
@Slf4j
@Service
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_FEEMODE)
public class NationalSubsidyPerformanceServiceSg22PdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException, TException {
        List<EcontractDeliveryInfoBo> deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName());
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();

        for (EcontractDeliveryInfoBo temp : deliveryInfoList) {
            fillSupportData(temp, pdfMetaContent, pdfBizContent);
        }

        //是否展示特批日期
        boolean hasSpecialExpireTime = deliveryInfoList.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getSpcialFeeExpirationTime()));
        pdfMetaContent.put("hasSpecialExpireTime", Boolean.toString(hasSpecialExpireTime));
        boolean hasSpecialFeeInfo = deliveryInfoList.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getSpcialFeeInfo()));
        pdfMetaContent.put("hasSpecialFeeInfo", Boolean.toString(hasSpecialFeeInfo));

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("signTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        pdfMetaContent.put("partBStampName", ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc());
        pdfMetaContent.put("partBEstamp", PdfConstant.MT_SH_SIGNKEY);

        //基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_22_TEMPLATE_ID", 2373));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_22_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        log.info("NationalSubsidyPerformanceServiceSg22PdfMaker#makePdfContentInfoBo, pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }

    private void fillSupportData(EcontractDeliveryInfoBo temp, Map<String, String> pdfMetaContent, List<Map<String, String>> pdfBizContent) throws IllegalAccessException {
        temp.setDeliveryArea(null);
        temp.setEcontractDeliveryWholeCityInfoBo(null);
        temp.setEcontractDeliveryAggregationInfoBo(null);

        boolean hasMtDelivery = SUPPORT_MARK.equals(temp.getSupportMTDelivery());
        boolean hasSGV2_2Support = SUPPORT_MARK.equals(temp.getSupportSGV2_2Delivery());
        boolean isNewKs = temp.getEcontractDeliverySG2_2InfoBo() != null && SUPPORT_MARK.equals(temp.getEcontractDeliverySG2_2InfoBo().getSupportNewKS());

        // 闪购2.2区分非新快速模板
        if (hasSGV2_2Support && hasMtDelivery && !isNewKs) {
            // 内层数组-用于对应模板字段渲染
            Map<String, String> pdfMetaContentMap = MapUtil.Object2Map(temp);
            if (temp.getEcontractDeliverySG2_2InfoBo() != null) {
                pdfMetaContentMap.putAll(MapUtil.Object2Map(temp.getEcontractDeliverySG2_2InfoBo()));
            }
            pdfBizContent.add(pdfMetaContentMap);
        }
    }
}