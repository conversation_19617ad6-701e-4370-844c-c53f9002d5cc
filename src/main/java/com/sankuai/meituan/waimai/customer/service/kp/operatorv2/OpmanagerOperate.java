package com.sankuai.meituan.waimai.customer.service.kp.operatorv2;

import com.google.common.base.Function;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.KpDBOperator;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-01 18:12
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class OpmanagerOperate extends WmCustomerKpOperate {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpmanagerOperate.class);

    @Override
    public void insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        // 数据校验
        List<KpPreverify> verifyList = Arrays.asList(kpDataVerify, kpRealNameVerify);
        for (KpPreverify kpPreverify : verifyList) {
            kpPreverify.verify(wmCustomer, oldCustomerKpList, operateKp, null, null, uid, uname);
        }
        // DB操作
        opmanagerDBOperator.insert(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
        sendEffectiveMq(wmCustomer);
    }

    @Override
    public void update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        if (CollectionUtils.isEmpty(oldCustomerKpList)) {
            return;
        }
        Map<Integer, WmCustomerKp> oldKpMap = Maps.uniqueIndex(oldCustomerKpList, new Function<WmCustomerKp, Integer>() {
            @Override
            public Integer apply(WmCustomerKp wmCustomerKp) {
                //关联关系为mtCustomerId
                return wmCustomerKp.getId();
            }
        });
        if (null == oldKpMap.get(operateKp.getId())) {
            LOGGER.info("update#kpId:{},查无对应KP记录", operateKp.getId());
            return;
        }
        // 无字段变更
        if (isDiff(oldKpMap.get(operateKp.getId()), operateKp)) {
            LOGGER.info("update#kpId:{},无数据变更，不操作更新", operateKp.getId());
            return;
        }

        LOGGER.info("update#customerId:{},kpId:{}", wmCustomer.getId(), operateKp.getId());

        // 数据校验
        boolean isDiff = true;
        if (operateKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
            isDiff = (boolean) opmanagerEffectiveUpdateVerify.verify(wmCustomer, oldCustomerKpList, null, operateKp, null, uid, uname);
        } else {
            List<KpPreverify> verifyList = Arrays.asList(kpDataVerify, kpRealNameVerify);
            for (KpPreverify kpPreverify : verifyList) {
                kpPreverify.verify(wmCustomer, oldCustomerKpList, null, operateKp, null, uid, uname);
            }
        }
        // DB操作
        if (isDiff) {
            opmanagerDBOperator.update(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
        } else {
            opmanagerDBOperator.update(oldCustomerKpList, kpTransList(operateKp), uid, uname);
        }
        sendEffectiveMq(wmCustomer);
    }

    @Override
    public void delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        // 数据校验
        List<KpPreverify> verifyList = Arrays.asList(opmanagerStateVerify, opmanagerRelPoiVerify);
        for (KpPreverify kpPreverify : verifyList) {
            kpPreverify.verify(wmCustomer, oldCustomerKpList, null, null, operateKp, uid, uname);
        }
        // DB操作
        opmanagerDBOperator.delete(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
        sendEffectiveMq(wmCustomer);
    }
}
