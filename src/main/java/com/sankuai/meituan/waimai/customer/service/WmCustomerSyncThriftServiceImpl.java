package com.sankuai.meituan.waimai.customer.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractSyncService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.common.aggre.WmCustomerOplogAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.companycustomer.CompanyCustomerSyncService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.PoiGrayRollbackCheckResBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractScanBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerSyncThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ObjectUtil;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

@Service
public class WmCustomerSyncThriftServiceImpl implements WmCustomerSyncThriftService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmCustomerSyncThriftServiceImpl.class);

    @Autowired
    private WmContractSyncService wmContractSyncService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private CompanyCustomerSyncService companyCustomerSyncService;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    /**
     * 2.0同步3.0使用-已无调用量-已废弃
     * @param wmCustomerBasicBo
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    @Deprecated
    public void saveCustomer(WmCustomerBasicBo wmCustomerBasicBo, int opUid, String opName) throws TException, WmCustomerException {
        LOGGER.error("saveCustomer接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    public WmCustomerBasicBo getCustomerById(int customerId) throws TException, WmCustomerException {
        WmCustomerDB wmCustomerDB ;
        wmCustomerDB = wmCustomerService.selectCustomerByIdRT(customerId);
        LOGGER.info("同步接口获取客户,wmCustomerDB={}", JSONObject.toJSONString(wmCustomerDB));
        return WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
    }

    @Override
    public void customerUnBindPoi(int customerId, Set<Long> wmPoiIdSet, int opUid, String opName) throws TException, WmCustomerException {
        LOGGER.info("同步接口解绑客户,customerId={},wmPoiIdSet={},opUid={},opName={}", customerId, JSONObject.toJSONString(wmPoiIdSet), opUid, opName);
        wmCustomerPoiService.customerUnBindPoiForSync(customerId, wmPoiIdSet, opUid, opName, WmCustomerPoiOplogSourceTypeEnum.AGENT_AND_CONTRACT_START);
    }

    @Override
    public void deleteCustomer(int customerId, int opUid, String opName) throws TException, WmCustomerException{
        LOGGER.info("同步接口删除客户,customerId={},opUid={},opName={}",customerId,opUid,opName);
        wmCustomerService.deleteCustomer(customerId,opUid,opName);
    }

    @Override
    public void saveCustomerKp(WmCustomerKp wmCustomerKp) throws TException, WmCustomerException {
        LOGGER.info("saveCustomerKp wmCustomerKp = {}", JSON.toJSONString(wmCustomerKp));
        if (wmCustomerKp == null || wmCustomerKp.getCustomerId() == 0) {
            throw new WmCustomerException(400, "数据不合法");
        }
        WmCustomerKp kp = getSignerKp(wmCustomerKp.getCustomerId());
        if (kp == null) {
            ObjectUtil.defaultValue(wmCustomerKp);
            wmCustomerKp.setState(KpSignerStateMachine.EFFECT.getState());
            wmCustomerKp.setEffective(KpConstants.EFFECTIVE);
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(wmCustomerKp);
            wmCustomerKpDBMapper.insert(wmCustomerKp);
        } else {
            kp.setKpType(wmCustomerKp.getKpType());
            kp.setSignerType(wmCustomerKp.getSignerType());
            kp.setCompellation(wmCustomerKp.getCompellation());
            kp.setPhoneNum(wmCustomerKp.getPhoneNum());
            kp.setCertType(wmCustomerKp.getCertType());
            kp.setBankName(wmCustomerKp.getBankName());
            kp.setCreditCard(wmCustomerKp.getCreditCard());
            kp.setCertNumber(wmCustomerKp.getCertNumber());
            kp.setAgentAuth(wmCustomerKp.getAgentAuth());
            kp.setEmail(wmCustomerKp.getEmail());
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kp);
            wmCustomerKpDBMapper.updateByPrimaryKey(kp);
        }

    }

    @Override
    public WmCustomerKp getSignerKp(int customerId) throws TException, WmCustomerException {
        List<WmCustomerKp> wmCustomerKps = wmCustomerKpDBMapper.selectByCustomerId(customerId, KpTypeEnum.SIGNER.getType());
        if (CollectionUtils.isEmpty(wmCustomerKps)) {
            return null;
        }
        for (WmCustomerKp wmCustomerKp : wmCustomerKps) {
            if (KpSignerStateMachine.EFFECT.getState() == wmCustomerKp.getState()) {
                wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
                return wmCustomerKp;
            }
        }
        return null;
    }

    @Override
    public Integer saveContract(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        return wmContractSyncService.saveContractForSync(contractBo,opUid,opName);
    }

    @Override
    public List<WmCustomerContractBo> getEffectiveContractByCusId(int customerId, int opUid, String opName) throws WmCustomerException, TException {
        return wmContractSyncService.getEffectiveContractByCusIdForSync(customerId,opUid,opName);
    }

    @Override
    public PoiGrayRollbackCheckResBo checkPoiCanBeGrayRollback(long wmPoiId, int opUid, String opName) throws WmCustomerException, TException {
        return wmContractSyncService.checkPoiCanBeGrayRollback(wmPoiId, opUid, opName);
    }

    @Deprecated
    @Override
    public Integer syncCustomerToEs(int cusIdStart, int cusIdEnd, int opUid, String opName) throws WmCustomerException, TException {
        LOGGER.error("syncCustomerToEs接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    @Deprecated
    public Integer syncUpdateCustomerToEsV2(int cusIdStart, int cusIdEnd, int opUid, String opName) throws WmCustomerException, TException {
        LOGGER.error("syncUpdateCustomerToEsV2接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    @Deprecated
    public void syncPoiCountToCustomerListEsGray(List<Integer> customerIdList) throws WmCustomerException, TException {
        LOGGER.error("syncPoiCountToCustomerListEsGray接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    @Deprecated
    public void syncPoiCountToCustomerListEsAll(int idStart, int idEnd) throws WmCustomerException, TException {
        LOGGER.error("syncPoiCountToCustomerListEsAll接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    @Deprecated
    public Integer syncUpdateCustomerToEs(int cusIdStart, int cusIdEnd, int opUid, String opName) throws WmCustomerException, TException {
        LOGGER.error("syncPoiCountToCustomerListEsAll接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    public void handleCompanyLabelEvent(long mtCustomerId, String action)
            throws WmCustomerException, TException {
        companyCustomerSyncService.handleCompanyLabelEvent(mtCustomerId,action);
    }

    @Override
    public void handlePoiBindAndUnBindEvent(long wmCustomerId, List<Long> wmPoiIdList,
            String action) throws WmCustomerException, TException {
        companyCustomerSyncService.handlePoiBindAndUnBindEvent(wmCustomerId,wmPoiIdList,action);
    }

    @Override
    public void syncContractScan(WmCustomerContractScanBo contractScanBo, int opUid, String opName) throws WmCustomerException, TException {
        wmContractSyncService.syncContractScan(contractScanBo, opUid, opName);
    }

    @Override
    public void fixEmptyAuditedSign(List<Long> contractIdList, int opUid, String opName) throws WmCustomerException, TException {
        wmContractSyncService.fixEmptyAuditedSign(contractIdList, opUid, opName);
    }

    @Override
    public String fixC2AgentId(Long customerID, int opUid, String opName) throws WmCustomerException, TException {
        return wmContractSyncService.fixC2AgentId(customerID, opUid, opName);
    }

    @Override
    @Deprecated
    public void syncCustomerOplogToES() throws WmCustomerException, TException {
        LOGGER.error("syncCustomerOplogToES接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    @Deprecated
    public void syncCustomerOplogToESByIdList(List<Long> logIdList) throws WmCustomerException, TException {
        LOGGER.error("syncCustomerOplogToESByIdList接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

}
