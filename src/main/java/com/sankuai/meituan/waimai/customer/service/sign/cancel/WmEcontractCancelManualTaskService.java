package com.sankuai.meituan.waimai.customer.service.sign.cancel;

import com.sankuai.meituan.waimai.customer.adapter.WmPoiLogisticsClient;
import java.util.List;
import java.util.concurrent.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignManualTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
import com.sankuai.meituan.waimai.poilogistics.thrift.constant.OperateSource;
import com.sankuai.meituan.waimai.poilogistics.thrift.exception.WmPoiLogisticsException;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmLogisticsFeeThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskSettleContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
public class WmEcontractCancelManualTaskService {

    private static final Logger               LOGGER        = LoggerFactory.getLogger(WmEcontractCancelManualTaskService.class);

    private final static ExecutorService handleService = new ThreadPoolExecutor(5, 200,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue(1024), new ThreadPoolExecutor.AbortPolicy());

    @Autowired
    private WmEcontractSignManualTaskDBMapper wmEcontractSignManualTaskDBMapper;

    @Autowired
    private WmPoiLogisticsClient wmPoiLogisticsClient;

    @Autowired
    private WmSettleManagerService            wmSettleManagerService;

    public void cancelByUnbindingPoi(Integer customerId, Long wmPoiId) {
        //配送待发起任务数据
        List<WmEcontractSignManualTaskDB> poiFeeTaskInfo = wmEcontractSignManualTaskDBMapper.getManualTaskByCustomerIdAndModule(customerId,
                EcontractTaskApplyTypeEnum.POIFEE.getName());
        //结算待发起任务数据
        List<WmEcontractSignManualTaskDB> settleTaskInfo = wmEcontractSignManualTaskDBMapper.getManualTaskByCustomerIdAndModule(customerId,
                EcontractTaskApplyTypeEnum.SETTLE.getName());
        try {
            //取消配送-待发起任务
            if (CollectionUtils.isNotEmpty(poiFeeTaskInfo)) {
                cancelManualTaskForWmPoiFee(wmPoiId, poiFeeTaskInfo);
            }
            //取消结算-待发起任务
            if (CollectionUtils.isNotEmpty(settleTaskInfo)) {
                cancelManualTaskForWmSettle(wmPoiId, settleTaskInfo);
            }
        } catch (Exception e) {
            LOGGER.warn("cancelByUnbindingPoi异常", e);
        }
    }

    private void cancelManualTaskForWmSettle(Long wmPoiId, List<WmEcontractSignManualTaskDB> settleTaskInfo) throws WmCustomerException {
        LOGGER.info("#cancelManualTaskForWmSettle,wmPoiId={},settleTaskInfo={}", wmPoiId, JSONObject.toJSONString(settleTaskInfo));
        ManualTaskSettleContextBo manualTaskSettleContextBo = null;
        for (WmEcontractSignManualTaskDB temp : settleTaskInfo) {
            manualTaskSettleContextBo = JSONObject.parseObject(temp.getApplyContext(), ManualTaskSettleContextBo.class);
            if (manualTaskSettleContextBo == null || manualTaskSettleContextBo.getWmPoiList() == null) {
                continue;
            }
            if (manualTaskSettleContextBo.getWmPoiList().contains(wmPoiId)) {
                wmSettleManagerService.cancelConfirm(temp.getCustomerId());
                wmEcontractSignManualTaskDBMapper.deleteByPrimaryKey(temp.getId());
                break;
            }
        }
    }

    private void cancelManualTaskForWmPoiFee(Long wmPoiId, List<WmEcontractSignManualTaskDB> poiFeeTaskInfo)
            throws WmPoiLogisticsException, TException {
        LOGGER.info("#cancelManualTaskForWmPoiFee,poiFeeTaskInfo={}", JSONObject.toJSONString(poiFeeTaskInfo));
        for (WmEcontractSignManualTaskDB temp : poiFeeTaskInfo) {
            if (wmPoiId.equals(temp.getWmPoiId())) {
                wmEcontractSignManualTaskDBMapper.deleteByPrimaryKey(temp.getId());
                break;
            }
        }
    }

    public void batchCancelWmCustomerSwitch(Integer targetCustomerId, List<Long> wmPoiIdList) {
        //配送待发起任务数据
        List<WmEcontractSignManualTaskDB> poiFeeTaskInfo = wmEcontractSignManualTaskDBMapper.getManualTaskByCustomerIdAndModule(targetCustomerId,
                EcontractTaskApplyTypeEnum.POIFEE.getName());
        //结算待发起任务数据
        List<WmEcontractSignManualTaskDB> settleTaskInfo = wmEcontractSignManualTaskDBMapper.getManualTaskByCustomerIdAndModule(targetCustomerId,
                EcontractTaskApplyTypeEnum.SETTLE.getName());

        try {
            //取消配送-待发起任务
            if (CollectionUtils.isNotEmpty(poiFeeTaskInfo)) {
                batchCancelManualTaskForWmPoiFee(wmPoiIdList, poiFeeTaskInfo);
            }
            //取消结算-待发起任务
            if (CollectionUtils.isNotEmpty(settleTaskInfo)) {
                batchCancelManualTaskForWmSettle(wmPoiIdList, settleTaskInfo);
            }
        } catch (Exception e) {
            LOGGER.warn("cancelByUnbindingPoi异常", e);
        }
    }

    public void batchCancelWmCustomerSwitchForDelivery(Integer targetCustomerId, List<Long> wmPoiIdList){
        //配送待发起任务数据
        List<WmEcontractSignManualTaskDB> poiFeeTaskInfo = wmEcontractSignManualTaskDBMapper.getManualTaskByCustomerIdAndModule(targetCustomerId,
                EcontractTaskApplyTypeEnum.POIFEE.getName());
        try {
            //取消配送-待发起任务
            if (CollectionUtils.isNotEmpty(poiFeeTaskInfo)) {
                batchCancelManualTaskForWmPoiFee(wmPoiIdList, poiFeeTaskInfo);
            }
        } catch (Exception e) {
            LOGGER.error("batchCancelManualTaskForWmPoiFee异常", e);
        }
    }

    private void batchCancelManualTaskForWmSettle(List<Long> wmPoiIdList, List<WmEcontractSignManualTaskDB> settleTaskInfo)
            throws WmCustomerException {
        LOGGER.info("#batchCancelManualTaskForWmSettle,wmPoiIdList={},settleTaskInfo={}", wmPoiIdList, JSONObject.toJSONString(settleTaskInfo));
        ManualTaskSettleContextBo manualTaskSettleContextBo = null;
        for (WmEcontractSignManualTaskDB temp : settleTaskInfo) {
            manualTaskSettleContextBo = JSONObject.parseObject(temp.getApplyContext(), ManualTaskSettleContextBo.class);
            if (manualTaskSettleContextBo == null || manualTaskSettleContextBo.getWmPoiList() == null) {
                continue;
            }
            if (CollectionUtils.containsAny(manualTaskSettleContextBo.getWmPoiList(), wmPoiIdList)) {
                wmSettleManagerService.cancelConfirm(temp.getCustomerId());
                wmEcontractSignManualTaskDBMapper.deleteByPrimaryKey(temp.getId());
                break;
            }
        }
    }

    private void batchCancelManualTaskForWmPoiFee(List<Long> wmPoiIdList, List<WmEcontractSignManualTaskDB> poiFeeTaskInfo) {
        LOGGER.info("#cancelManualTaskForWmPoiFee,poiFeeTaskInfo={}", JSONObject.toJSONString(poiFeeTaskInfo));
        List<Long> toDeleteManualTaskId = Lists.newArrayList();
        for (WmEcontractSignManualTaskDB temp : poiFeeTaskInfo) {
            if (wmPoiIdList.contains(temp.getWmPoiId())) {
                handleService.submit(() -> {
                    try {
                        wmPoiLogisticsClient.cancelWmPoiAllFeeManualConfirmInfo(temp.getId(), temp.getWmPoiId(), "客户解绑门店", 0, OperateSource.CUSTOMER.getCode());
                    } catch (WmPoiLogisticsException | TException |WmCustomerException e) {
                        LOGGER.warn("cancelWmPoiAllFeeManualConfirmInfo异常,wmBizId={}", temp.getId(), e);
                    }
                });
                toDeleteManualTaskId.add(temp.getId());
            }
        }
        LOGGER.info("删除待手动打包任务：toDeleteManualTaskId=「{}」", toDeleteManualTaskId);
        if (CollectionUtils.isNotEmpty(toDeleteManualTaskId)) {
            wmEcontractSignManualTaskDBMapper.batchDeleteByPrimaryKey(toDeleteManualTaskId);
        }
    }
}
