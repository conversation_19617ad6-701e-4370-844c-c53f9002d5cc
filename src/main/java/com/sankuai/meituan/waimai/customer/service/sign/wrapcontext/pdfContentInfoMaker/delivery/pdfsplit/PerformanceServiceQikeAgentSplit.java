package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_QIKEAGENT_FEEMODE)
public class PerformanceServiceQikeAgentSplit implements DeliveryPdfSplit {

    public static final String SUPPORT_MARK = "support";
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));

        if (feeMode == LogisticsFeeModeEnum.AGENT_QIKE && SUPPORT_MARK.equals(deliveryInfoBo.getSupportAgentCompanyCustomer())) {
            List<String> performanceQikeAgentList = pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_QIKEAGENT_FEEMODE.getName());
            if (CollectionUtils.isEmpty(performanceQikeAgentList)) {
                performanceQikeAgentList = Lists.newArrayList();
                performanceQikeAgentList.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                performanceQikeAgentList.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_QIKEAGENT_FEEMODE.getName(), performanceQikeAgentList);
            log.info("ADD TO PERFORMANCE_SERVICE_QIKEAGENT_FEEMODE，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}
