/*
 * Copyright (c) 2022 meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.waimai.customer.service.kp;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealAuthService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerFalseStoreGrayService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.PreAuthResultTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 客户KP实名认证
 */
@Service
@Slf4j
public class WmCustomerKpRealAuthService {

    @Autowired
    private WmCustomerFalseStoreGrayService wmCustomerFalseStoreGrayService;

    @Autowired
    private WmCustomerRealAuthService wmCustomerRealAuthService;

    @Autowired
    protected WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    private static final List<Byte> AUTH_CERT_TYPE = Lists.newArrayList(CertTypeEnum.ID_CARD.getType(),
            CertTypeEnum.ID_CARD_TEMP.getType(),
            CertTypeEnum.ID_CARD_COPY.getType());

    /**
     * 是否需要企业四要素认证
     *
     * @param wmCustomerKp
     * @param isGray
     * @return
     */
    public boolean isNeedEnterpriseFourElePreAuth(WmCustomerKp wmCustomerKp, boolean isGray) {
        WmCustomerDB wmCustomer = getCustomer(wmCustomerKp.getCustomerId());
        return isNeedEnterpriseFourElePreAuth(wmCustomer, wmCustomerKp, isGray);
    }


    /**
     * 是否需要企业四要素认证
     *
     * @param wmCustomerKp
     * @param isGray
     * @return
     */
    public boolean isNeedEnterpriseFourElePreAuth(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, boolean isGray) {
        if (wmCustomerKp == null) {
            return false;
        }
        // 非【签约人KP】或【法人KP】不做企业四要素校验
        if (wmCustomerKp.getKpType() != KpTypeEnum.SIGNER.getType() && wmCustomerKp.getKpType() != KpTypeEnum.LEGAL.getType()) {
            return false;
        }
        // 签约人KP && 签约类型非法人代表 不做企业四要素校验
        if (wmCustomerKp.getKpType() == KpTypeEnum.SIGNER.getType() && wmCustomerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()) {
            return false;
        }
        // 证件类型非身份证、临时身份证、身份证复印件不做企业四要素校验
        if (!AUTH_CERT_TYPE.contains(wmCustomerKp.getCertType())) {
            return false;
        }
        return wmCustomerFalseStoreGrayService.isNeedAuthGray(wmCustomerDB.getOwnerUid(), wmCustomerDB,
                CertificateAuthTypeEnum.EXACT_ENTERPRISE_FOUR_ELEMENT_AUTH,
                isGray);
    }

    /**
     * 企业四要素认证(调用此接口前先判断是否需要企业四要素认证)
     *
     * @param wmCustomerKp KP对象
     * @param uid          操作用户ID
     * @return
     */
    public PreAuthResultBO enterpriseFourElePreAuthAndErrorMsg(WmCustomerKp wmCustomerKp, int uid) {
        try {
            if (wmCustomerKp == null) {
                return null;
            }
            WmCustomerDB wmCustomer = getCustomer(wmCustomerKp.getCustomerId());
            if (wmCustomer == null) {
                return null;
            }
            return wmCustomerRealAuthService.enterpriseFourElePreAuth(wmCustomer.getCustomerName(), wmCustomer.getCustomerNumber(), wmCustomer.getLegalPerson(), wmCustomerKp.getCertNumber());
        } catch (WmCustomerException e) {
            log.error("enterpriseFourElePreAuthAndErrorMsg uid={},kpId={}", uid, wmCustomerKp.getId(), e);
        } catch (Exception e) {
            log.error("enterpriseFourElePreAuthAndErrorMsg uid={},kpId={}", uid, wmCustomerKp.getId(), e);
        }
        return null;
    }

    /**
     * 企业四要素认证(调用此接口前先判断是否需要企业四要素认证)
     *
     * @param wmCustomerKp KP对象
     * @param uid          操作用户ID
     * @return
     */
    public PreAuthResultBO enterpriseFourElePreAuthAndErrorMsgForClean(WmCustomerKp wmCustomerKp, int uid) {
        try {
            if (wmCustomerKp == null) {
                return null;
            }
            WmCustomerDB wmCustomer = getCustomer(wmCustomerKp.getCustomerId());
            if (wmCustomer == null) {
                return null;
            }
            return wmCustomerRealAuthService.enterpriseFourElePreAuthForClean(wmCustomer.getCustomerName(), wmCustomer.getCustomerNumber(), wmCustomer.getLegalPerson(), wmCustomerKp.getCertNumber());
        } catch (WmCustomerException e) {
            log.error("enterpriseFourElePreAuthAndErrorMsg uid={},kpId={}", uid, wmCustomerKp.getId(), e);
        } catch (Exception e) {
            log.error("enterpriseFourElePreAuthAndErrorMsg uid={},kpId={}", uid, wmCustomerKp.getId(), e);
        }
        return null;
    }


    /**
     * 查询客户信息
     *
     * @param customerId
     * @return
     */
    private WmCustomerDB getCustomer(int customerId) {
        log.info("getCustomer customerId={}", customerId);
        try {
            if (customerId <= 0) {
                return null;
            }
            WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
            if (wmCustomer == null) {
                log.error("不存在的客户,customerId={}", customerId);
                return null;
            }
            return wmCustomer;
        } catch (WmCustomerException e) {
            log.error("getCustomer customerId={}", customerId, e);
        } catch (Exception e) {
            log.error("getCustomer customerId={}", customerId, e);
        }
        return null;
    }

    /**
     * 四要素认证后对客户四要素标签处理
     *
     * @param wmCustomerDB 客户对象
     * @param result       四要素认证结果
     * @param uid          操作人ID
     * @param uname        操作人名字
     */
    public void signerFourAuthTagDeal(WmCustomerDB wmCustomerDB, PreAuthResultBO result, int uid, String uname) {
        if (wmCustomerDB == null) {
            return;
        }
        //统一社会信用编码处于三四要素白名单中则删除四要素认证成功和失败的标签
        if (wmCustomerFalseStoreGrayService.isWhiteList(wmCustomerDB.getCustomerNumber())) {
            wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleSucForCustomerTag(), wmCustomerDB.getMtCustomerId(), uid, uname);
            wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleFailForCustomerTag(), wmCustomerDB.getMtCustomerId(), uid, uname);
            return;
        }
        if (result == null ) {
            return;
        }
        PreAuthResultTypeEnum resultType = PreAuthResultTypeEnum.getByType(result.getResult());
        //非企业四要素认证
        if (!CertificateAuthTypeEnum.EXACT_ENTERPRISE_FOUR_ELEMENT_AUTH.getType().equals(result.getAuthType())) {
            //认证通过则掉企业四要素标签
            if (PreAuthResultTypeEnum.SUCCESS.equals(resultType)) {
                wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleSucForCustomerTag(), wmCustomerDB.getMtCustomerId(), uid, uname);
                wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleFailForCustomerTag(), wmCustomerDB.getMtCustomerId(), uid, uname);
            }
            return;
        }
        //如果进行了四要素认证则进行四要素认证标签处理
        switch (resultType) {
            case SUCCESS:
                //认证成功打"企业四要素认证通过"标，掉"企业四要素认证失败标签"
                wmPoiFlowlineLabelThriftServiceAdapter.addCustomerLabelRel(MccCustomerConfig.getFourEleSucForCustomerTag(), wmCustomerDB.getMtCustomerId(), uid, uname);
                wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleFailForCustomerTag(), wmCustomerDB.getMtCustomerId(), uid, uname);
                return;
            case NO_RESULT:
                //认证无结果掉"企业四要素认证通过"标，掉"企业四要素认证失败标签"
                wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleSucForCustomerTag(), wmCustomerDB.getMtCustomerId(), uid, uname);
                wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleFailForCustomerTag(), wmCustomerDB.getMtCustomerId(), uid, uname);
                return;
            default:
                return;
        }
    }

    /**
     * 审核通过且生效状态的时候处理四要素认证标签
     *
     * @param oldWmCustomer 变更之前的客户对象信息
     * @param newWmCustomer 变更之后的客户对象信息
     */
    public void customerEffectAuditPassFourEleTag(WmCustomerDB oldWmCustomer, WmCustomerDB newWmCustomer) {
        if (newWmCustomer == null) {
            return;
        }
        boolean flag = !Objects.equals(oldWmCustomer.getCustomerName(), newWmCustomer.getCustomerName())
                || !Objects.equals(oldWmCustomer.getCustomerNumber(), newWmCustomer.getCustomerNumber())
                || !Objects.equals(oldWmCustomer.getLegalPerson(), newWmCustomer.getLegalPerson());
        if (flag) {
            log.info("customerEffectAuditPassFourEleTag 客户审核通过后处于有效状态，且客户名字或者法人或者统一社会信用编码发生变化，所以掉四要素认证的标签");
            deleteFourEleTag(oldWmCustomer.getMtCustomerId());
        }
    }

    /**
     * 删除四要素标签
     *
     * @param customerId
     */
    public void deleteFourEleTag(int customerId) {
        long mtCustomerId = wmCustomerPlatformDataParseService.getMtCustomerIdByWmCustomerId(customerId);
        deleteFourEleTag(mtCustomerId);
    }

    /**
     * 删除四要素标签
     *
     * @param mtCustomerId
     */
    public void deleteFourEleTag(long mtCustomerId) {
        if (mtCustomerId <= 0) {
            return;
        }
        wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleSucForCustomerTag(), mtCustomerId, 0, "系统");
        wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getFourEleFailForCustomerTag(), mtCustomerId, 0, "系统");
    }


    private boolean isFromSignerToAgent(byte oldSignerType, byte newKpSignerType) {
        return oldSignerType == KpSignerTypeEnum.SIGNER.getType()
                && newKpSignerType == KpSignerTypeEnum.AGENT.getType();
    }
}
