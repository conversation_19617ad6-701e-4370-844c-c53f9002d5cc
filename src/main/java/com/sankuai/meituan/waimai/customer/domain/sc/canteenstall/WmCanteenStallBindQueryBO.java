package com.sankuai.meituan.waimai.customer.domain.sc.canteenstall;

import lombok.Data;

import java.util.List;

/**
 * 食堂档口绑定任务查询BO
 * <AUTHOR>
 * @date 2024/05/24
 * @email <EMAIL>
 */
@Data
public class WmCanteenStallBindQueryBO {
    /**
     * 主键ID
     */
    private Integer bindId;
    /**
     * 主键ID列表
     */
    private List<Integer> bindIdList;
    /**
     * 食堂主键ID
     */
    private Integer canteenPrimaryId;
    /**
     * 外卖门店ID
     */
    private Long wmPoiId;
    /**
     * 线索ID
     */
    private Long wdcClueId;
    /**
     * 线索生成状态
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueGenerateStatusEnum}
     */
    private Integer clueGenerateStatus;
    /**
     * 线索绑定状态
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum}
     */
    private Integer clueBindStatus;
    /**
     * 外卖门店绑定状态
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum}
     */
    private Integer wmPoiBindStatus;
    /**
     * 线索跟进状态
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueFollowUpStatusEnum}
     */
    private Integer clueFollowUpStatus;
    /**
     * DSL查询语句
     */
    private String dslQuery;
    /**
     * 页面数量
     */
    private Integer pageSize;
    /**
     * 页数
     */
    private Integer pageFrom;
    /**
     * 结果是否为空
     */
    private Boolean resultEmpty = false;

}