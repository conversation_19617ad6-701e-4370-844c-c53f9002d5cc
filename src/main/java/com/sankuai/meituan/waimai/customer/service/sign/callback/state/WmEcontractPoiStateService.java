package com.sankuai.meituan.waimai.customer.service.sign.callback.state;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractPoiSignStateDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractPoiStateBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskPoiService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignStateBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class WmEcontractPoiStateService extends AbstractWmEcontractStateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractPoiStateService.class);

    @Resource
    private WmEcontractPoiStateBizService wmEcontractPoiSignStateService;

    @Resource
    private WmEcontractTaskPoiService wmEcontractTaskPoiService;

    private static List<String> poiStateList = Lists.newArrayList();

    static {
        poiStateList.add(EcontractTaskApplyTypeEnum.C1CONTRACT.getName());
        poiStateList.add(EcontractTaskApplyTypeEnum.C2CONTRACT.getName());
        poiStateList.add(EcontractTaskApplyTypeEnum.SETTLE.getName());
        poiStateList.add(EcontractTaskApplyTypeEnum.POIFEE.getName());
    }

    private static Map<String, List<String>> stateMap = Maps.newHashMap();

    static {
        stateMap.put(EcontractTaskStateEnum.TO_COMMIT.getName(), Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName(),
                                                                                    EcontractTaskStateEnum.IN_PROCESSING.getName()));

        stateMap.put(EcontractTaskStateEnum.HOLDING.getName(), Lists.newArrayList(EcontractTaskStateEnum.IN_PROCESSING.getName()));

        stateMap.put(EcontractTaskStateEnum.IN_PROCESSING.getName(), Lists.newArrayList(EcontractTaskStateEnum.SUCCESS.getName(),
                                                                                        EcontractTaskStateEnum.FAIL.getName(),
                                                                                        EcontractTaskStateEnum.CANCEL.getName(),
                                                                                        EcontractTaskStateEnum.HOLDING.getName()));

        stateMap.put(EcontractTaskStateEnum.FAIL.getName(), Lists.newArrayList(EcontractTaskStateEnum.TO_COMMIT.getName(),
                                                                               EcontractTaskStateEnum.HOLDING.getName(),
                                                                               EcontractTaskStateEnum.IN_PROCESSING.getName()));

        stateMap.put(EcontractTaskStateEnum.CANCEL.getName(), Lists.newArrayList(EcontractTaskStateEnum.TO_COMMIT.getName(),
                                                                                 EcontractTaskStateEnum.HOLDING.getName(),
                                                                                 EcontractTaskStateEnum.IN_PROCESSING.getName()));
    }

    public Boolean changeState(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo) throws WmCustomerException {
        //计算所有需要更新poistate的任务
        Map<Long, EcontractTaskBo> taskBoMap = getChangeMap(batchContextDB);
        if (MapUtils.isEmpty(taskBoMap)) {
            return Boolean.TRUE;
        }
        
        //重新计算目标状态
        String toState = calToState(notifyBo);
        //主动取消的情况暂不处理
        if (EcontractTaskStateEnum.CANCEL.getName().equals(toState)) {
            return Boolean.TRUE;
        }
        LOGGER.info("changeState toState = {}", toState);
        //重新计算signState
        List<WmEcontractPoiSignStateDB> reCalState = reCalState(batchContextDB, taskBoMap, toState);

        if(CollectionUtils.isNotEmpty(reCalState) && reCalState.size() == 1){
            LOGGER.info("changeState toState = {}", reCalState);
        }
        //变更状态
        wmEcontractPoiSignStateService.batchUpdateState(reCalState);
        return Boolean.TRUE;
    }

    /**
     * 查询当前所有任务中需要更新poistate的任务
     * @param batchContextDB
     * @return
     */
    private Map<Long, EcontractTaskBo> getChangeMap(EcontractBatchContextBo batchContextDB) {
        Map<Long, EcontractTaskBo> taskBoMap = Maps.newHashMap();
        for (Map.Entry<Long, EcontractTaskBo> entry : batchContextDB.getTaskIdAndTaskMap().entrySet()) {
            if (!poiStateList.contains(entry.getValue().getApplyType())) {
                continue;
            }

            taskBoMap.put(entry.getKey(), entry.getValue());
        }
        return taskBoMap;
    }

    /**
     * batch维度重新计算poiState
     * @param batchContextBo
     * @param taskBoMap
     * @return
     */
    private List<WmEcontractPoiSignStateDB> reCalState(EcontractBatchContextBo batchContextBo, Map<Long, EcontractTaskBo> taskBoMap, String toState)
        throws WmCustomerException {
        if (MapUtils.isEmpty(taskBoMap)) {
            LOGGER.info("stateDBList = {}", JSON.toJSONString(taskBoMap));
            return Lists.newArrayList();
        }
        List<WmEcontractPoiSignStateDB> stateDBList = wmEcontractPoiSignStateService.getByCustomerIdAndWmPoiIdList(batchContextBo.getCustomerId(), batchContextBo.getWmPoiIdList());
        for (Map.Entry<Long, EcontractTaskBo> entry:batchContextBo.getTaskIdAndTaskMap().entrySet()) {
            stateDBList = calStateByTask(stateDBList, entry.getValue(), toState);
        }
        return stateDBList;
    }

    /**
     * 单任务重新计算PoiState
     */
    private List<WmEcontractPoiSignStateDB> calStateByTask(List<WmEcontractPoiSignStateDB> storeList, EcontractTaskBo taskBo, String toState) {
        List<Long> wmPoiIdList = wmEcontractTaskPoiService.relateWmPoiIdList(taskBo);

        for (WmEcontractPoiSignStateDB store : storeList) {
            if (!wmPoiIdList.contains(store.getWmPoiId())) {
                continue;
            }

            EcontractSignStateBo stateBo = EcontractSignStateBo.parseObject(store.getSignState());
            if (EcontractTaskApplyTypeEnum.C1CONTRACT.getName().equals(taskBo.getApplyType())
                && canToNextState(stateBo.getContractState(), toState)) {
                LOGGER.info("in c1contract");
                stateBo.setContractState(toState);
                stateBo.setContractTaskId(taskBo.getId());
            } else if (EcontractTaskApplyTypeEnum.SETTLE.getName().equals(taskBo.getApplyType())
                && canToNextState(stateBo.getSettleState(), toState)) {
                LOGGER.info("in settle");
                stateBo.setSettleState(toState);
                stateBo.setSettleTaskId(taskBo.getId());
            } else if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(taskBo.getApplyType())
                && canToNextState(stateBo.getPoifeeState(), toState)) {
                LOGGER.info("in delivery");
                stateBo.setPoifeeState(toState);
                stateBo.setPoifeeTaskId(taskBo.getId());
            }else if (EcontractTaskApplyTypeEnum.C2CONTRACT.getName().equals(taskBo.getApplyType())
                    && canToNextState(stateBo.getContractC2State(), toState)) {
                LOGGER.info("in c2contract");
                stateBo.setContractC2State(toState);
                stateBo.setContractC2TaskId(taskBo.getId());
            }
            store.setSignState(JSON.toJSONString(stateBo));
        }
//        LOGGER.info("storeList = {}", JSON.toJSONString(storeList));
        return storeList;
    }

    public boolean canToNextState(String from, String to) {
        return stateMap.get(from) != null && stateMap.get(from).contains(to);
    }

}
