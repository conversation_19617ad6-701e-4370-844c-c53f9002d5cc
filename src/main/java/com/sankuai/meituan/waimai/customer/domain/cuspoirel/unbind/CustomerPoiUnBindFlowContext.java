package com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind;

import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240122
 * @desc 门店解绑客户流程上下文定义
 */
@Data
@Builder
public class CustomerPoiUnBindFlowContext {

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 客户信息
     */
    private WmCustomerDB wmCustomerDB;

    /**
     * 解绑门店set集合
     */
    private Set<Long> wmPoiIdSet;

    /**
     * 操作来源
     */
    private Integer opSource;

    /**
     * 详细操作来源
     */
    private String opSourceDetail;

    /**
     * 操作系统名称
     */
    private String opSysName;

    /**
     * 客户签约形式 1纸质 2电子
     * com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode
     */
    private Integer signMode;

    /**
     * 客户生效状态
     */
    private Integer customerEffectStatus;

    /**
     * 生效的签约人KP
     */
    private WmCustomerKp wmCustomerKp;

    /**
     * 解绑任务使用参数
     */
    private CustomerOperateBO customerOperateBO;

    /**
     * 操作人ID
     */
    private Integer opUid;

    /**
     * 操作人名称
     */
    private String opName;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 解绑生成任务id map集合
     */
    private Map<Long, Integer> poiAndTaskMaps;

    /**
     * 解绑流程策略
     */
    private List<UnBindFlowStrategy> unBindFlowStrategyList;

    /**
     * 解绑操作类型
     */
    private CustomerPoiUnBindTypeEnum customerPoiUnBindTypeEnum;

    /**
     * 重新建店场景的解绑标识
     */
    private boolean rebuildUnBindFlag;

    /**
     * 确认失败解绑标识
     */
    private boolean confirmFailedToUnBindFlag;

    /**
     * 有纸面客户解绑权限
     */
    private boolean hasPaperCustomerUnBindAuth;

    /**
     * 解绑接口返回提示文案
     */
    private String unbindTips;

    /**
     * 预解绑签约回调通知参数
     */
    private UnBindSignNoticeDTO unBindSignNoticeDTO;

    /**
     * 需要解绑的门店ID列表
     */
    private Set<Long> needUnBindWmPoiIdSet;

    /**
     * 未绑定在当前客户下的门店列表 true 存在 false 不存在
     */
    private List<WmCustomerPoiDB> notBindCustomerWmPoiIList;

    /**
     * 解绑中当前为上线状态的门店列表 true 存在上线门店 false 不存在
     */
    private boolean existOnlineUnBindWmPoiIdFlag;

    /**
     * 有生效或结算分成的门店ID列表
     */
    private Set<Long> existEffSettleWmPoiIdSet;
}
