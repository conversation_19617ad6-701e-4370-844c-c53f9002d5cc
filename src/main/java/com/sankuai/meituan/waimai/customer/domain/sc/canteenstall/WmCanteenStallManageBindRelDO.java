package com.sankuai.meituan.waimai.customer.domain.sc.canteenstall;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 食堂档口管理绑定关联DO
 * <AUTHOR>
 * @date 2024/05285
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmCanteenStallManageBindRelDO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 档口管理任务ID
     */
    private Integer manageId;
    /**
     * 档口绑定任务ID
     */
    private Integer bindId;
    /**
     * 是否生效
     */
    private Integer valid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
}
