package com.sankuai.meituan.waimai.customer.domain.sc.delivery;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 校园校企交付执行历史，记录关键时刻时间信息
 */
@Data
@NoArgsConstructor
public class WmSchoolDeliveryRecord {

    /**
     * 交付实例id
     */
    private Long deliverId;

    /**
     * 深度交付完成时间
     */
    private Long deepDeliveryFinishTimestamp;

    /**
     * 订单转化率 > 60% 达标完成时间
     */
    private Long order60TargetCompletionTimestamp;

    /**
     * 订单转化率 > 90% 达标完成时间
     */
    private Long order90TargetCompletionTimestamp;

    public WmSchoolDeliveryRecord(Long deliverId) {
        this.deliverId = deliverId;
    }

    public WmSchoolDeliveryRecord(Long deliverId, Long deepDeliveryFinishTimestamp, Long order60TargetCompletionTimestamp, Long order90TargetCompletionTimestamp) {
        this.deliverId = deliverId;
        this.deepDeliveryFinishTimestamp = deepDeliveryFinishTimestamp;
        this.order60TargetCompletionTimestamp = order60TargetCompletionTimestamp;
        this.order90TargetCompletionTimestamp = order90TargetCompletionTimestamp;
    }
}
