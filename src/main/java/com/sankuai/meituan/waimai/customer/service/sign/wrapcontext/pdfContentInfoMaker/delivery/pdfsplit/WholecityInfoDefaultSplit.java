package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryWholeCityInfoBo;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY_PERFORMANCE_SERVICE;
import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY_WHOLE_CITY;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.WHOLECITY_INFO_DEFAULT)
public class WholecityInfoDefaultSplit implements DeliveryPdfSplit {
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));

        if (!(feeMode == LogisticsFeeModeEnum.WAIMAI_V3
                || feeMode == LogisticsFeeModeEnum.WAIMAI_QIKE
                || feeMode == LogisticsFeeModeEnum.WAIMAI_QIKE_V2)
                && deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo() != null
                && CollectionUtils.isNotEmpty(tabPdfMap.get(TAB_DELIVERY_WHOLE_CITY))) {
            List<String> wholecityDefaultList = pdfDataMap.get(DeliveryPdfDataTypeEnum.WHOLECITY_INFO_DEFAULT.getName());
            if (CollectionUtils.isEmpty(wholecityDefaultList)) {
                wholecityDefaultList = Lists.newArrayList();
                wholecityDefaultList.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                wholecityDefaultList.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.WHOLECITY_INFO_DEFAULT.getName(), wholecityDefaultList);
            log.info("ADD TO WHOLECITY_INFO_DEFAULT，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}
