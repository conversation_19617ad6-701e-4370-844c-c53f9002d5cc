package com.sankuai.meituan.waimai.customer.service;


import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.OpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerEsThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Set;

@Service
public class WmCustomerEsThriftServiceImpl implements WmCustomerEsThriftService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerEsThriftServiceImpl.class);

    @Autowired
    private WmCustomerESService wmCustomerESService;


    @Override
    @Deprecated
    public void syncDataToEs(List<WmCustomerBasicBo> syncData, OpTypeEnum opType, int opUid, String opName) throws WmCustomerException, TException {
        logger.error("syncDataToEs功能已下线，如有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "syncDataToEs功能已下线");
    }

    @Override
    @Deprecated
    public List<WmCustomerBasicBo> queryCustomerListByUtimeSectionFromEs(int startTime, int endTime, int opUid, String opName) throws WmCustomerException, TException {

            logger.error("queryCustomerListByUtimeSectionFromEs功能已下线，如有使用请联系liuzhihao09");
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "queryCustomerListByUtimeSectionFromEs功能已下线");

    }

    @Override
    public List<Integer> syncWmCustomerToES(List<Integer> customerIdList, Set<String> field) throws WmCustomerException, TException {
        return wmCustomerESService.syncWmCustomerToES(customerIdList,field);
    }

    @Override
    public List<Integer> deleteWmCustomerEs(List<Integer> customerIdList) throws WmCustomerException, TException {
        return wmCustomerESService.deleteWmCustomerEs(customerIdList);
    }
}
