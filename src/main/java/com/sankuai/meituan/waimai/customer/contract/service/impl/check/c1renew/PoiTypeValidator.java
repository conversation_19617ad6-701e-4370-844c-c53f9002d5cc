package com.sankuai.meituan.waimai.customer.contract.service.impl.check.c1renew;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiRelationConstant;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_VALID;

import com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-09-07 11:20
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class PoiTypeValidator implements IContractValidator {

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    /**
     * WM_POI_FIELD_WM_POI_ID：门店id
     * WM_POI_FIELD_LABEL_IDS：标签id
     * WM_POI_FIELD_BIZ_ORG_CODE：所属业务线
     * WM_POI_FIELD_AGENT_ID：代理商id
     * WM_POI_FIELD_VALID：门店状态
     * LOGISTICS_FEE_MODE：门店费率模式
     * WM_POI_FIELD_OWNER_TYPE：门店所属品牌类型
     */
    private static final ImmutableSet<String> WM_C1_CONTRACTOR_POI_FIELD_SET = ImmutableSet.of(
            WM_POI_FIELD_WM_POI_ID,
            WM_POI_FIELD_LABEL_IDS,
            WM_POI_FIELD_BIZ_ORG_CODE,
            WM_POI_FIELD_AGENT_ID,
            WM_POI_FIELD_VALID,
            LOGISTICS_FEE_MODE,
            WM_POI_FIELD_OWNER_TYPE
    );

    private ExecutorService traceExecutorService = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(10, 300, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy()));


    private final String errorMsg = "请将以下门店切换到新费率模式并完成签约，再修改C1合同有效期。\n门店id：";


    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        log.info("PoiTypeValidator#valid，customerId:{}", contractBo.getBasicBo().getParentId());
        try {
            int customerId = contractBo.getBasicBo().getParentId();
            List<Long> poiIdList = wmCustomerPoiService.selectWmPoIIdByCustomerIdAndRelationStatus(customerId, WmCustomerPoiRelationConstant.BIND);
            List<WmPoiAggre> poiAggreList = wmPoiQueryAdapter.getWmPoiAggre(poiIdList, WM_C1_CONTRACTOR_POI_FIELD_SET);
            //客户下无门店时放行
            if (CollectionUtils.isNotEmpty(poiIdList) && CollectionUtils.isNotEmpty(poiAggreList)) {
                List<WmPoiAggre> filterResultPoiList = poiAggreList.stream().
                        filter(poiAggre -> poiAggre.getLabel_ids().contains(String.valueOf(ConfigUtilAdapter.getInt("school_canteen_labieid", 87)))
                                || poiAggre.getBiz_org_code() == PoiOrgEnum.SHAN_GOU.getCode()
                                || poiAggre.getBiz_org_code() == PoiOrgEnum.MEDICINE.getCode()).
                        collect(Collectors.toList());

                //有校园食堂、闪购、医药门店，放行
                if (CollectionUtils.isNotEmpty(filterResultPoiList)) {
                    return true;
                }

                //客户维度校验结束，进入门店维度校验
                StringBuffer sb = errorMsgInit();

                // 分批执行，避免大客户打满被拒绝
                List<List<WmPoiAggre>> partitionList = com.google.common.collect.Lists.partition(poiAggreList, 50);
                for (List<WmPoiAggre> parPoiList : partitionList) {
                    List<Future> futureList = new ArrayList<>();
                    for (WmPoiAggre poiAggre : parPoiList) {
                        PoiValidatorDomain poiValidatorDomain = new PoiValidatorDomain();
                        poiValidatorDomain.setContractBo(contractBo);
                        poiValidatorDomain.setOpUid(opUid);
                        poiValidatorDomain.setOpUname(opName);
                        poiValidatorDomain.setWmPoiAggre(poiAggre);
                        Future future = traceExecutorService.submit(new PoiValidatorHandler(poiValidatorDomain));
                        futureList.add(future);
                    }
                    // 组装结果
                    if (CollectionUtils.isNotEmpty(futureList)) {
                        for (Future future : futureList) {
                            Object invalidPoiId = future.get();
                            if (null != invalidPoiId) {
                                String errorWmPoiId = invalidPoiId.toString();
                                sb.append(errorWmPoiId);
                                sb.append("、");
                            }
                        }
                    }
                }
                if (!sb.toString().equals(errorMsg)) {
                    String resultMsg = sb.toString().substring(0, sb.toString().length() - 1);
                    log.info("C1保存旧费率卡控返回文案:{}", resultMsg);
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_C1_RENEW, resultMsg);
                }
            }
        } catch (WmCustomerException e) {
            throw e;
        } catch (InterruptedException | ExecutionException e) {
            log.error("customer PoiTypeValidator error or poiValidator error,customerId:{}", contractBo.getBasicBo().getParentId(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "系统繁忙，请稍后重试");
        }
        return true;
    }

    private StringBuffer errorMsgInit() {
        StringBuffer sb = new StringBuffer();
        sb.append(errorMsg);
        return sb;
    }


    class PoiValidatorHandler implements Callable {

        private WmCustomerContractBo contractBo;
        private int opUid;
        private String opUname;
        private WmPoiAggre wmPoiAggre;


        public PoiValidatorHandler(PoiValidatorDomain poiValidatorDomain) {
            this.contractBo = poiValidatorDomain.getContractBo();
            this.opUid = poiValidatorDomain.getOpUid();
            this.opUname = poiValidatorDomain.getOpUname();
            this.wmPoiAggre = poiValidatorDomain.getWmPoiAggre();
        }

        @Override
        public Object call() throws Exception {
            try {
                C1RenewPoiThreadLocalUtil.set(wmPoiAggre);
                ContractCheckFilter.c1RenewOldFeemodePoiFilter().whiteListFilter(contractBo, opUid, opUname);
                log.info("客户:{}，门店:{}，门店规则校验放行", contractBo.getBasicBo().getParentId(),
                        C1RenewPoiThreadLocalUtil.get().getWm_poi_id());
            } catch (WmCustomerException e) {
                log.warn("客户:{}，门店:{},执行C1续签命中门店校验规则", contractBo.getBasicBo().getParentId(),
                        C1RenewPoiThreadLocalUtil.get().getWm_poi_id(), e);
                //返回异常门店拼接文案
                return C1RenewPoiThreadLocalUtil.get().getWm_poi_id();
            } catch (Exception e) {
                log.error("客户:{}执行C1续签门店校验规则时系统异常", contractBo.getBasicBo().getParentId(), e);
            } finally {
                C1RenewPoiThreadLocalUtil.remove();
            }
            return null;
        }
    }

    @Data
    class PoiValidatorDomain {
        private WmCustomerContractBo contractBo;
        private int opUid;
        private String opUname;
        private WmPoiAggre wmPoiAggre;
    }
}
