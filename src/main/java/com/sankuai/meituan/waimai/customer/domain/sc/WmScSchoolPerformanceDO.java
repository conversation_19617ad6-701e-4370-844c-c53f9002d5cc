package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;

import java.util.List;

/**
 * 学校履约管控信息DO V2
 * <AUTHOR>
 * @date 2024/06/06
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmScSchoolPerformanceDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 是否有效 0-无效，1-有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
    /**
     * 学校履约管控信息单元列表
     */
    private List<WmScSchoolPerformanceUnitDO> wmScSchoolPerformanceUnitDOList;

    /**
     * 校方是否允许配送进校
     * {@link SchoolAllowDeliveryV2Enum}
     */
    private Integer schoolAllowDelivery;
    /**
     * 校方是否允许配送进校其他信息
     */
    private String schoolAllowDeliveryInfo;

}
