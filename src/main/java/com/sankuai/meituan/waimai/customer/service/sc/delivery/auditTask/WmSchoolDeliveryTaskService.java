package com.sankuai.meituan.waimai.customer.service.sc.delivery.auditTask;

import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliverySaveParamDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.apache.thrift.TException;

public interface WmSchoolDeliveryTaskService {

    /**
     * 根据交付编号ID获取审批任务类型
     * @param deliveryId 交付编号ID
     * @return 审批任务类型
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditTaskTypeEnum}
     */
    Integer getAuditTaskTypeByDeliveryId(Integer deliveryId);

    /**
     * 获取交付审批任务类型
     * @return 交付审批任务类型
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryStreamNodeEnum}
     */
    Integer getTaskTypeByDeliveryNodeType();

    /**
     * 根据交付编号ID查询提审前审批状态
     * @param deliveryId 交付编号ID
     * @return 提审前审批状态
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditStatusEnum}
     */
    Integer getLastAuditStatusByDeliveryId(Integer deliveryId);

    /**
     * 根据交付编号ID查询提审前生效数据版本号
     * @param deliveryId 交付编号ID
     * @return 提审前生效数据版本号
     */
    Integer getEffectiveDataVersionByDeliveryId(Integer deliveryId);

    /**
     * 提交审批
     * @param saveParamDTO saveParamDTO
     * @param deliveryId 交付编号ID
     */
    void submitAuditTask(WmSchoolDeliverySaveParamDTO saveParamDTO, Integer deliveryId) throws WmSchCantException, TException;

    /**
     * 撤回审批
     * @param auditTaskBO auditTaskBO
     */
    void cancelAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException;

    /**
     * 审批驳回
     * @param auditTaskBO auditTaskBO
     */
    void rejectAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException;

    /**
     * 审批终止
     * @param auditTaskBO auditTaskBO
     */
    void stopAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException;

    /**
     * 审批通过
     * @return 下一个审批节点
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditNodeTypeEnum}
     * @param auditTaskBO auditTaskBO
     */
    Integer passAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException;

    /**
     * 审批生效
     * @param auditTaskBO auditTaskBO
     */
    void effectAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException;
}
