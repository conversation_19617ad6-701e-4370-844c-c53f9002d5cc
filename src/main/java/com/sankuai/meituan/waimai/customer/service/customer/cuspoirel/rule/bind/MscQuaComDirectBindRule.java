package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.bind;


import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.CustomerPoiRelStrategyBean;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IBindCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.poirel.flow.CustomerPoiRelFlowEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240117
 * @desc 美食城资质共用客户直接绑定规则
 */
@Service
@Slf4j
@Rule
public class MscQuaComDirectBindRule {

    /**
     * 条件判断是否为美食城客户&客户有资质共用标
     *
     * @param context
     * @return
     */
    @Condition
    public boolean when(@Fact("context") CustomerPoiBindFlowContext context) {
        //客户类型
        Integer customerRealType = context.getWmCustomerDB().getCustomerRealType();
        return customerRealType == CustomerRealTypeEnum.MEISHICHENG.getValue() && context.getHasCustomerQuaComTag();
    }

    /**
     * 根据策略执行
     *
     * @return
     */
    @Action
    public void execute(@Fact("context") CustomerPoiBindFlowContext context) throws WmCustomerException {
        //定义各层级策略使用bean对象
        CustomerPoiRelStrategyBean customerPoiRelStrategyBean = CustomerPoiRelStrategyBean.builder()
                .checkBeanName("mscBindCheckStrategy")
                .preCoreBeanName("directBindPreCoreStrategy")
                .coreBeanName("directBindCoreStrategy")
                .afterBeanName("directBindAfterStrategy")
                .build();
        //根据bean名称计算各层级的实际策略信息
        BindStrategy strategy = BindStrategy.buiLdStrategyWithContext(customerPoiRelStrategyBean);
        //构建流程策略
        BindFlowStrategy bindFlowStrategy = BindFlowStrategy.builder()
                .flowEnum(CustomerPoiRelFlowEnum.DIRECT_BIND)
                .wmPoiIdSet(context.getWmPoiIdSet())
                .bindStrategy(strategy)
                .build();
        context.setBindFlowStrategyList(Lists.newArrayList(bindFlowStrategy));
    }


    @Priority
    public int getPriority() {
        return 1;
    }
}
