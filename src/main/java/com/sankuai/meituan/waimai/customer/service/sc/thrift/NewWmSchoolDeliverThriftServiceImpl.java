package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import com.google.common.collect.Lists;
import com.meituan.gecko.boot.domain.PageVo;
import com.meituan.gecko.boot.util.JacksonUtils;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.waimai.customer.adapter.DeliverThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmScDisplayFieldsDO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryPlanAggr;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliverySearchModel;
import com.sankuai.meituan.waimai.customer.service.sc.auth.WmDeliverAuthService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.WmSchoolDeliveryNewService;
import com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.WmScSchoolSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.customer.util.ScBuildUtil;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;
import com.sankuai.meituan.waimai.infra.service.WmVirtualOrgService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.DeliverStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CommonResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmScDeliveryPlanHandlerDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.NewWmSchoolDeliverThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import com.sankuai.waimai.crm.authenticate.client.service.AuthenticateRoleService;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateUserRoleAssertResponse;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverInstanceDto;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverTeamInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 学校交付系统 Thrift 服务实现类
 *
 * <p>主要功能:
 * <ul>
 *   <li>学校交付列表的搜索、导出、详情查询</li>
 *   <li>交付流程节点管理</li>
 *   <li>用户自定义展示字段的保存与查询</li>
 *   <li>组织节点和学校名称的模糊查询</li>
 *   <li>交付权限的查询与管理</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NewWmSchoolDeliverThriftServiceImpl implements NewWmSchoolDeliverThriftService {

    @Autowired
    private WmSchoolDeliveryNewService wmSchoolDeliveryNewService;

    @Autowired
    private AuthenticateRoleService authenticateRoleService;

    @Autowired
    private WmSchoolDeliverAssemble wmSchoolDeliverAssemble;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmScSchoolSensitiveWordsService wmScSchoolSensitiveWordsService;

    @Autowired
    private WmVirtualOrgService.Iface wmVirtualOrgService;

    @Autowired
    private WmEmployClient wmEmployClient;

    @Resource
    private DeliverThriftServiceAdapter deliverThriftServiceAdapter;

    @Resource
    private WmDeliverAuthService wmDeliverAuthService;

    // 租户id
    private static final int tenantId = 1000008;

    /**
     * 学校交付系统的客户信息管理交付列表查询
     *
     * @param searchVO 交付列表查询参数对象，包含分页信息、筛选条件等
     * @return WmScDeliverPlanPageResp 分页查询结果，包含交付列表数据及权限信息
     * @throws RuntimeException 当参数错误、分页参数缺失时抛出
     *
     * 主要功能：
     * 1. 根据查询条件搜索学校交付列表
     * 2. 转换查询结果为前端展示对象
     * 3. 校验并添加用户操作权限
     * 4. 设置导出按钮权限
     */
    @Override
    public WmScDeliverPlanPageResp WmSchoolDeliverySearch(WmSchoolDeliverySearchVO searchVO) {
        return ThriftUtils.exec(() -> {

            log.info("搜索入参:{}", JacksonUtils.toJson(searchVO));

            WmSchoolDeliverySearchModel searchModel = new WmSchoolDeliverySearchModel();
            wmSchoolDeliverAssemble.SearchVOToModel(searchVO, searchModel);

            SSOUtil.SsoUser user = SSOUtil.safeGetUser();
            Long uid = Objects.nonNull(user) ? user.getId() : -1L;

            if (searchModel == null){
                throw new RuntimeException("参数错误");
            }
            if (searchVO.getPageNum() == null || searchVO.getPageSize() == null) {
                throw new RuntimeException("分页参数错误,缺少pageNum或pageSize");
            }

            PageVo<WmSchoolDeliveryPlanAggr> search = wmSchoolDeliveryNewService.search(searchModel);

            List<WmScDeliveryPlanHandlerDTO> resultList = search.getList().stream()
                    .map(obj -> wmSchoolDeliverAssemble.convert(obj))
                    .collect(Collectors.toList());

            //查询权限
            if (uid > 0) {
                Map<Long, Map<String, Boolean>> authResult = wmDeliverAuthService.batchGetDeliverAuth(
                        resultList.stream().map(WmScDeliveryPlanHandlerDTO::getDeliverId).collect(Collectors.toList()),
                        uid.intValue()
                );
                withAuth(resultList, authResult);
            }

            WmScDeliverPlanPageDTO pageDTO = new WmScDeliverPlanPageDTO();
            pageDTO.setPageNo(search.getPageNum());
            pageDTO.setPageSize(search.getPageSize());
            pageDTO.setTotal((long) search.getTotal());
            pageDTO.setData(resultList);
            pageDTO.setExportDeliverListButtonAuth(0);
            pageDTO.setExportDeliverItemListButtonAuth(0);
            return pageDTO;
        }, new WmScDeliverPlanPageResp());
    }

    private void withAuth(List<WmScDeliveryPlanHandlerDTO> resultList, Map<Long, Map<String, Boolean>> authResult) {
        for (WmScDeliveryPlanHandlerDTO result : resultList) {
            Long deliverId = result.getDeliverId();
            DeliverButtonAuthDTO buttonAuthDTO = result.getDeliverButtonAuthDTO();
            Map<String, Boolean> authMap = authResult.get(deliverId);
            Boolean detailAuth = authMap.get(WmDeliverAuthService.DETAIL);
            Boolean editStaffAuth = authMap.get(WmDeliverAuthService.EDIT_TEAM_STAFF);
            Boolean endAuth = authMap.get(WmDeliverAuthService.END);
            Boolean invalidAuth = authMap.get(WmDeliverAuthService.INVALID);
            Boolean opLogAuth = authMap.get(WmDeliverAuthService.OP_LOG);
            Boolean restartAuth = authMap.get(WmDeliverAuthService.RESTART);
            if (!Boolean.TRUE.equals(detailAuth)) {
                buttonAuthDTO.setDetailButtonAuth(2);
            }
            if (!Boolean.TRUE.equals(editStaffAuth)) {
                buttonAuthDTO.setEditOwnerButtonAuth(2);
            }
            if (!Boolean.TRUE.equals(invalidAuth)) {
                buttonAuthDTO.setInvalidButtonAuth(2);
            }
            if (!Boolean.TRUE.equals(opLogAuth)) {
                buttonAuthDTO.setOpLogButtonAuth(2);
            }
            if (!Boolean.TRUE.equals(endAuth)) {
                buttonAuthDTO.setEndButtonAuth(2);
                buttonAuthDTO.setEndInApprovalButtonAuth(2);
            }
            if (!Boolean.TRUE.equals(restartAuth)) {
                buttonAuthDTO.setRestartButtonAuth(2);
                buttonAuthDTO.setRestartInApprovalButtonAuth(2);
            }
        }
    }

    /**
     * 学校交付系统的客户信息管理交付列表详情
     *
     * @param deliverId 交付ID
     * @return WmScDeliverDetailResp 交付详情响应对象，包含交付基本信息、流程节点信息等
     * @throws RuntimeException 当deliverId为空时抛出
     *
     * 主要功能：
     * 1. 参数校验，确保deliverId不为空
     * 2. 根据deliverId查询交付计划聚合信息
     * 3. 转换查询结果为前端展示对象
     */
    @Override
    public WmScDeliverDetailResp WmSchoolDeliveryGetById(Long deliverId) {
        return ThriftUtils.exec(() -> {
            if (deliverId == null) {
                throw new RuntimeException("获取请求参数为空");
            }
            log.info("学校交付系统的客户信息管理交付列表详情, deliverId = {}", JacksonUtils.toJson(deliverId));
            WmSchoolDeliveryPlanAggr buildPlaneAggrById= wmSchoolDeliveryNewService.buildPlaneAggrById(deliverId);
            return wmSchoolDeliverAssemble.convert(buildPlaneAggrById);
        }, new WmScDeliverDetailResp());
    }

    /**
     * 导出学校交付列表数据
     *
     * @param searchVO 交付列表查询参数对象，包含筛选条件和选中的交付ID列表
     * @return CommonResponse 通用响应对象
     *         成功返回提示信息："交付列表数据将异步导出, 导出完成后大象通知（附下载链接）"
     *         失败抛出RuntimeException,错误信息包含具体原因
     * @throws RuntimeException 当选中交付数超过1000个时抛出异常
     *
     * 主要功能：
     * 1. 校验导出数据量上限(最多1000条)
     * 2. 异步执行导出任务
     * 3. 生成Excel文件并上传获取下载链接
     * 4. 通过大象消息推送下载链接给用户
     *
     * 导出流程：
     * 1. 启动异步线程处理导出任务
     * 2. 调用导出服务生成Excel文件
     * 3. 上传文件获取下载链接
     * 4. 发送大象消息通知用户下载
     */
    @Override
    public CommonResponse exportDeliveryList(WmSchoolDeliverySearchVO searchVO) {
        return ThriftUtils.exec(() -> {
            // 1. 参数校验
            log.info("学校交付系统的客户信息管理交付列表导出, searchVO = {}", JacksonUtils.toJson(searchVO));
            if(searchVO.getDeliveryIds() != null && searchVO.getDeliveryIds().size() > 1000){
                throw new RuntimeException("单次最多勾选1000个交付");
            }

            // 2. 获取当前用户
            SSOUtil.SsoUser user = Optional.ofNullable(SSOUtil.getUser())
                    .orElseThrow(() -> new RuntimeException("获取用户信息失败"));

            new Thread(new TraceRunnable(new Runnable() {
                @Override
                public void run() {
                    String msg = "";
                    try {
                        // 1. 执行导出
                        String url = wmSchoolDeliverAssemble.exportDeliveryList(searchVO, user);
                        log.info("[exportDeliveryList.searchVO] url = {}", url);

                        // 2. 构建下载消息
                        msg = "交付列表数据已导出完成, 请点此下载: " + "[全部交付列表 |" + url + " ]";
                    } catch (Exception e) {
                        log.error("[exportDeliveryList][Exception]", e);
                        msg = "导出交付列表异常";
                    } finally {
                        // 3. 发送大象通知
                        String misById = wmEmployClient.getMisById((int) user.getId());
                        DaxiangUtilV2.push(msg, misById);
                    }
                }
            })).start();
            return "交付列表数据将异步导出, 导出完成后大象通知（附下载链接）";
        }, new CommonResponse());

    }

    /**
     * 导出学校交付系统的流程节点数据
     *
     * @param searchVO 交付列表查询参数对象，包含需要导出的交付ID列表
     * @return CommonResponse 通用响应对象
     *         成功返回异步导出提示信息
     *         失败抛出WmSchCantException，错误信息包含具体原因
     * @throws WmSchCantException 当参数校验失败、数据异常或导出失败时抛出
     * @throws TException Thrift调用异常
     *
     * 主要功能：
     * 1. 校验导出请求参数的合法性（交付ID列表不为空且数量不超过1000）
     * 2. 校验所选学校的交付状态（必须是已完成状态）
     * 3. 异步导出流程节点数据到Excel文件
     * 4. 通过大象消息推送导出结果和下载链接
     *
     * 业务限制：
     * - 单次最多可导出1000个交付的流程节点
     * - 仅支持导出交付完成状态的学校流程节点
     * - 导出任务超时时间为5分钟
     */
    @Override
    public CommonResponse exportDeliveryItemList(WmSchoolDeliverySearchVO searchVO) throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {
            log.info("学校交付系统的客户信息管理交付列表导出, searchVO = {}", JacksonUtils.toJson(searchVO));
            if(searchVO == null){
                throw new RuntimeException("获取请求参数为空");
            }

            //校验复选框是否为空，若为空，则toast提示：请先选择学校
            if(searchVO.getDeliveryIds() == null || searchVO.getDeliveryIds().size() == 0){
                throw new RuntimeException("请先选择学校");
            }

            //最大勾选复选框数为1000
            if(searchVO.getDeliveryIds() != null && searchVO.getDeliveryIds().size() > 1000){
                throw new RuntimeException("单次最多勾选1000个交付");
            }
            ArrayList<WmSchoolDeliveryPlanAggr> resultList = new ArrayList<>();
            List<Long> deliveryIds = searchVO.getDeliveryIds();

            SSOUtil.SsoUser user = SSOUtil.getUser();
            if (user == null) {
                throw new RuntimeException("获取用户信息失败");
            }

            //校验学校交付状态，若学校交付状态≠交付完成，则toast提示：学校XXXXX、XXXXX未交付完成，仅可导出交付完成学校的流程节点
            for (Long deliveryId : deliveryIds) {
                WmSchoolDeliveryPlanAggr aggr = wmSchoolDeliveryNewService.buildPlaneAggrById(deliveryId);
                if (aggr == null || aggr.getDeliverInstance() == null || aggr.getDeliverInstance().getDeliverStatus() == null){
                    throw new RuntimeException("数据异常");
                }
                resultList.add(aggr);
                if (aggr.getDeliverInstance().getDeliverStatus() != DeliverStatusEnum.COMPLETED.getType()){
                    throw new RuntimeException("学校" + aggr.getWmSchoolDB().getSchoolName() + "未交付完成，仅可导出交付完成学校的流程节点");
                }
            }

            new Thread(new TraceRunnable(new Runnable() {
                @Override
                public void run() {
                    SSOUtil.SsoUser user = SSOUtil.getUser();
                    if (user == null) {
                        throw new RuntimeException("获取用户信息失败");
                    }

                    ExecutorService executor = Executors.newSingleThreadExecutor();
                    Future<String> future = executor.submit(new Callable<String>() {
                        @Override
                        public String call() throws Exception {
                            //调用通知
                            String msg = wmSchoolDeliveryNewService.exportItemExcel(resultList, user);
                            log.info("学校交付系统的客户信息管理交付列表导出结果, result = {}", msg);
                            return msg;
                        }
                    });
                    String msg = "";
                    try {
                        String url = future.get(5, TimeUnit.MINUTES);
                        log.info("[exportDeliveryItemList] url = {}", url);
                        // 大象消息的超链接格式：[描述 | url]
                        msg = "流程节点数据已导出完成, 请点此下载: " + "[全部流程节点 |" + url + " ]";
                    } catch (Exception e) {
                        log.error("[exportExcelDeliveryV5][Exception]", e);
                        msg = "导出流程节点异常";
                    } finally {
                        executor.shutdown();
                        String misById = wmEmployClient.getMisById((int) user.getId());
                        DaxiangUtilV2.push(msg, misById);
                    }
                }
            })).start();
            return "流程节点数据将异步导出, 导出完成后大象通知（附下载链接）";
        }, new CommonResponse());
    }

    /**
     * 保存交付列表用户展示字段
     * @param fields 用户自定义展示字段,字符串格式
     * @return CommonResponse 通用响应对象
     *         成功返回"保存成功"
     *         失败抛出RuntimeException,错误信息包含具体原因
     */
    @Override
    public CommonResponse saveUserField(String fields) {
        return ThriftUtils.exec(() -> {
            // 1. 参数校验
            if (fields == null) {
                throw new RuntimeException("参数不能为空");
            }

            // 2. 获取当前用户
            SSOUtil.SsoUser user = Optional.ofNullable(SSOUtil.getUser())
                    .orElseThrow(() -> new RuntimeException("获取用户信息失败"));

            try {
                // 3. 构建保存对象
                WmScDisplayFieldsDO displayFieldsDO = new WmScDisplayFieldsDO();
                displayFieldsDO.setDisplayField(fields);
                displayFieldsDO.setUserId(user.getId());

                // 4. 保存展示字段
                log.info("开始保存用户展示字段, userId={}, fields={}", user.getId(), fields);
                wmSchoolDeliverAssemble.saveUserField(displayFieldsDO);
                log.info("保存用户展示字段成功, userId={}", user.getId());

                return "保存成功";
            } catch (Exception e) {
                log.error("保存用户展示字段失败, userId={}, fields={}", user.getId(), fields, e);
                throw new RuntimeException("保存展示字段失败:" + e.getMessage());
            }
        }, new CommonResponse());
    }

    /**
     * 查询交付列表用户展示字段
     *
     * @return CommonResponse 通用响应对象
     *         成功返回用户自定义展示字段字符串
     *         失败抛出RuntimeException,错误信息包含具体原因
     * @throws RuntimeException 当获取用户信息失败或查询展示字段失败时抛出
     *
     * 主要功能：
     * 1. 获取当前登录用户信息
     * 2. 根据用户ID查询其自定义展示字段配置
     * 3. 返回用户的展示字段配置字符串
     */
    @Override
    public CommonResponse queryUserField(){
        return ThriftUtils.exec(() -> {
            // 1. 获取当前用户
            SSOUtil.SsoUser user = Optional.ofNullable(SSOUtil.getUser())
                    .orElseThrow(() -> new RuntimeException("获取用户信息失败"));

            // 2. 查询用户展示字段
            WmScDisplayFieldsDO displayFields = wmSchoolDeliverAssemble.queryUserField(user.getId());

            // 3. 返回展示字段配置,如果为null则返回空字符串
            return Optional.ofNullable(displayFields.getDisplayField())
                    .orElse("");

        }, new CommonResponse());
    }

    /**
     * 根据组织节点名称进行模糊查询
     *
     * @param orgSearchDTO 组织查询参数对象，包含:
     *                    - name: 组织节点名称关键字
     *                    - teamType: 团队类型 (1:校园KA团队 2:校园PS团队 3:校园城市团队)
     * @return WmVirtualListResp 组织节点列表，每个节点包含:
     *                          - orgId: 组织ID
     *                          - orgName: 组织名称
     * @throws WmSchCantException 业务异常
     * @throws TException Thrift通信异常
     * @throws RuntimeException 当teamType不支持时抛出"不支持的团队类型"
     *                         当查询组织结构失败时抛出"查询组织结构失败"
     */
    @Override
    public WmVirtualListResp getOrgListByName(OrgSearchDTO orgSearchDTO) throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {
            log.info("查询组织节点请求 req:{}", JacksonUtils.toJson(orgSearchDTO));
            List<WmVirtualOrg> wmVirtualOrgs = Collections.emptyList();
            try {
                wmVirtualOrgs = wmVirtualOrgService.getLikeName(orgSearchDTO.getName());
            } catch (Exception e) {
                log.error("查询组织结构失败", e);
                throw new RuntimeException("查询组织结构失败");
            }

            Integer teamType = orgSearchDTO.getTeamType();
            Integer parentOrgId = null;
            switch (teamType) {
                case 1:
                    parentOrgId = MccScConfig.getCampusKaParentOrg();
                    break;
                case 2:
                    parentOrgId = MccScConfig.getCampusPsParentOrg();
                    break;
                case 3:
                    parentOrgId = MccScConfig.getCampusCityParentOrg();
                    break;
                default:
                    throw new RuntimeException("不支持的团队类型");
            }

            List<WmVirtualOrg> sonOrgs = Collections.emptyList();
            try {
                sonOrgs = wmVirtualOrgService.getOrgsByOrgId(parentOrgId, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN.getType());
            } catch (Exception e) {
                log.error("根据parentOrgId:{}查询子节点失败", parentOrgId);
                throw new RuntimeException("查询组织结构失败");
            }
            Set<Integer> sonOrgIds = sonOrgs.stream().map(WmVirtualOrg::getId).collect(Collectors.toSet());

            return wmVirtualOrgs.stream()
                    .filter(org -> sonOrgIds.contains(org.getId()))
                    .map(org -> {
                        WmVirtualOrgDTO orgDTO = new WmVirtualOrgDTO();
                        orgDTO.setOrgId(org.getId());
                        orgDTO.setOrgName(org.getName());
                        return orgDTO;
                    })
                    .collect(Collectors.toList());

        }, new WmVirtualListResp());
    }

    /**
     * 根据学校名称进行模糊查询
     *
     * @param name 学校名称关键字
     * @return SchoolBoListResp 学校信息列表响应对象，包含匹配的学校基本信息
     * @throws WmSchCantException 业务异常
     * @throws TException Thrift通信异常
     *
     * 主要功能：
     * 1. 根据学校名称模糊匹配查询学校信息
     * 2. 处理敏感词过滤
     * 3. 支持通过学校ID精确查询
     * 4. 转换查询结果为前端展示对象
     *
     * 查询逻辑：
     * 1. 先根据名称进行模糊查询
     * 2. 如果输入的是数字且在合法范围内,则尝试按ID精确查询并合并结果
     * 3. 对查询结果进行敏感词处理
     * 4. 转换为前端展示对象并设置描述信息
     */
    @Override
    public SchoolBoListResp getSchoolByName(String name) throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {
            log.info("查询学校请求:{} ", name);
            WmSchoolDB wmSchoolDB = WmSchoolDB.builder().schoolName(name).build();
            List<WmSchoolDB> wmSchoolDBS = wmSchoolMapper.selectSchoolList(wmSchoolDB);
            wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDBS);
            if (Pattern.compile("^[-\\+]?[\\d]*$").matcher(name).matches()
                    && new BigDecimal(name).compareTo(new BigDecimal(Integer.MAX_VALUE)) == -1) {
                WmSchoolDB schoolDB = wmSchoolMapper.selectSchoolBySchoolId(Integer.parseInt(name));
                wmScSchoolSensitiveWordsService.readWhenSelect(schoolDB);
                if (schoolDB != null && !wmSchoolDBS.contains(schoolDB)) {
                    wmSchoolDBS.add(schoolDB);
                }
            }
            if (CollectionUtils.isEmpty(wmSchoolDBS)) {
                return Lists.newArrayList();
            }
            List<SchoolBo> schoolBos = WmScTransUtil.schoolTransDbToBo(wmSchoolDBS);
            ScBuildUtil.setListDesc(schoolBos);
            return schoolBos;
        }, new SchoolBoListResp());
    }

    /**
     * 批量查询学校交付权限信息
     *
     * @param list 交付ID列表
     * @return List<SchoolDeliverAuthDTO> 交付权限信息列表，每个对象包含:
     *         - id: 交付ID
     *         - campusCityManager: 城市团队负责人UID
     *         - campusKaManager: KA团队负责人UID
     *         - campusPsManager: PS团队负责人UID
     * @throws WmSchCantException 业务异常
     * @throws TException Thrift通信异常
     *
     * 主要功能：
     * 1. 根据交付ID列表批量查询交付实例信息
     * 2. 从交付实例中提取各个团队的负责人UID
     * 3. 组装返回交付权限对象列表
     *
     * 异常处理：
     * - 查询过程发生异常时返回空列表
     * - 记录错误日志但不中断处理
     */
    @Override
    public List<SchoolDeliverAuthDTO> queryByIdList(List<Long> list) throws WmSchCantException, TException {
        try {
            return list.stream()
                    .map(deliverId -> {
                        SchoolDeliverAuthDTO schoolDeliverAuthDTO = new SchoolDeliverAuthDTO();
                        DeliverInstanceDto deliverInstanceDto = deliverThriftServiceAdapter.findDeliverInstanceById(deliverId);
                        Integer kaTeamManagerUid = getTeamOwner(deliverInstanceDto, MccCustomerConfig.getCampusKaTeamCode());
                        Integer channelUid = getTeamOwner(deliverInstanceDto, MccCustomerConfig.getChannelTeamCode());
                        Integer cityUid = getTeamOwner(deliverInstanceDto, MccCustomerConfig.getCityTeamCode());

                        schoolDeliverAuthDTO.setId(deliverId);
                        schoolDeliverAuthDTO.setCampusCityManager(cityUid);
                        schoolDeliverAuthDTO.setCampusKaManager(kaTeamManagerUid);
                        schoolDeliverAuthDTO.setCampusPsManager(channelUid);
                        return schoolDeliverAuthDTO;
                    }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询交付权限对象异常, idList:{}", list);
            return Collections.emptyList();
        }
    }

    @Override
    public CommonResponse testIndex(Long deliverId) throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {
            wmSchoolDeliveryNewService.indexDeliveryPlanById(deliverId);
            return null;
        }, new CommonResponse());
    }

    /**
     * 从交付实例中获取指定团队的负责人UID
     */
    private Integer getTeamOwner(DeliverInstanceDto deliverInstance, String teamCode) {
        if (StringUtils.isBlank(teamCode)
                || CollectionUtils.isEmpty(deliverInstance.getDeliverTeamInfoDtoList())) {
            return null;
        }

        for (DeliverTeamInfoDto teamInfoDto : deliverInstance.getDeliverTeamInfoDtoList()) {
            if (teamCode.equals(teamInfoDto.getTeamCode())
                    && CollectionUtils.isNotEmpty(teamInfoDto.getTeamStaffDtoList())) {
                return teamInfoDto.getTeamStaffDtoList().get(0).getUid();
            }
        }
        return null;
    }

    /**
     * 查询当前用户是否是超管
     */
    @Override
    public CommonResponse checkCurrentRole() {
        return ThriftUtils.exec(() -> {
            SSOUtil.SsoUser user = Optional.ofNullable(SSOUtil.getUser())
                    .orElseThrow(() -> new RuntimeException("获取用户信息失败"));
            // 当前用户为超管,更新食堂编辑信息，生成新建操作记录
            AuthenticateUserRoleAssertResponse authenticateUserRoleAssertResponse = authenticateRoleService.userRoleAssert(tenantId, (int) user.getId(), "school_canteen_hq");
            Boolean assertResult = authenticateUserRoleAssertResponse.getAssertResult();

            Map<String, Boolean> result = new HashMap<>();
            result.put("isSuperAdmin", assertResult);
            return JacksonUtils.toJson(result);
        }, new CommonResponse());
    }
}
