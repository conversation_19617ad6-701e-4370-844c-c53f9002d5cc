package com.sankuai.meituan.waimai.customer.contract.domain;

/**
 * @Description:
 * @Author: liuzhihao09
 * @Date: 2023/10/16 15:28
 */

public class WmTempletContractSignWithOutSignPhoneDB {
    private Long id;

    /**
     * 模板合同id
     */
    private Integer wmTempletContractId;

    /**
     * 类型 A:甲方,B:乙方
     */
    private String signType;

    /**
     * 签约方id,如签约方为合作商，则为合作商ID
     */
    private Integer signId;

    /**
     * 签约方
     */
    private String signName;

    /**
     * 签约人
     */
    private String signPeople;

    /**
     * 签约时间
     */
    private String signTime;

    private Byte valid;

    private Integer ctime;

    private Integer utime;

    private Integer opuid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWmTempletContractId() {
        return wmTempletContractId;
    }

    public void setWmTempletContractId(Integer wmTempletContractId) {
        this.wmTempletContractId = wmTempletContractId;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType == null ? null : signType.trim();
    }

    public Integer getSignId() {
        return signId;
    }

    public void setSignId(Integer signId) {
        this.signId = signId;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName == null ? null : signName.trim();
    }

    public String getSignPeople() {
        return signPeople;
    }

    public void setSignPeople(String signPeople) {
        this.signPeople = signPeople == null ? null : signPeople.trim();
    }


    public String getSignTime() {
        return signTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime == null ? null : signTime.trim();
    }

    public Byte getValid() {
        return valid;
    }

    public void setValid(Byte valid) {
        this.valid = valid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public Integer getOpuid() {
        return opuid;
    }

    public void setOpuid(Integer opuid) {
        this.opuid = opuid;
    }
}
