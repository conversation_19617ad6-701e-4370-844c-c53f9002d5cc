package com.sankuai.meituan.waimai.customer.service.sign.callback.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigTaskParseService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractCallbackUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCallbackBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class WmEcontractCallbackFailHandler implements WmEcontractCallbackHandler {

    private static Logger logger = LoggerFactory.getLogger(WmEcontractCallbackFailHandler.class);

    private static Map<String, String> failMsgMap = Maps.newHashMap();

    static {

    }

    @Resource
    private WmPoiLogisticsClient wmPoiLogisticsClient;

    @Resource
    private WmContractService wmContractService;

    @Resource
    private WmSettleManagerService wmSettleManagerService;

    @Resource
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Resource
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmEcontractBatchBizService wmEcontractBatchBizService;

    @Autowired
    private ShanGouRebateServiceClient shanGouRebateServiceClient;

    @Autowired
    private WmPoiBaseClient wmPoiBaseClient;

    @Autowired
    private WmPoiHealthClient wmPoiHealthClient;

    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;

    @Resource
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;

    @Override
    public Boolean handleCallback(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
            throws TException, WmCustomerException {
        return handleFail(batchContextDB, notifyBo);
    }

    private Boolean handleFail(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
            throws TException, WmCustomerException {
        if (StringUtils.isNotEmpty(notifyBo.getExecuteName())) {
            return handleExecuteTaskFail(batchContextDB, notifyBo);
        } else {
            return handleStageTaskFail(batchContextDB, notifyBo);
        }
    }

    private Boolean handleExecuteTaskFail(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
            throws TException, WmCustomerException {
        return callback(notifyBo.getStageName(), batchContextDB, Lists.newArrayList(notifyBo.getExecuteName()), notifyBo.getCode(), notifyBo.getMsg());
    }

    private Boolean handleStageTaskFail(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
            throws TException, WmCustomerException {
        Set<String> set = Sets.newHashSet();
        for (Map.Entry<Long, EcontractTaskBo> entry : batchContextDB.getTaskIdAndTaskMap().entrySet()) {
            set.add(entry.getValue().getApplyType());
        }

        return callback(notifyBo.getStageName(), batchContextDB, Lists.newArrayList(set), notifyBo.getCode(), notifyBo.getMsg());
    }

    /**
     * 单stageName回调
     */
    private Boolean callback(String stageName, EcontractBatchContextBo batchContextBo, List<String> executiveList, Integer failCode, String failMsg)
            throws TException, WmCustomerException {
        for (Map.Entry<Long, EcontractTaskBo> entry : batchContextBo.getTaskIdAndTaskMap().entrySet()) {
            if (executiveList.contains(entry.getValue().getApplyType())) {
                try {
                    callbackFail(entry.getKey(), entry.getValue(), stageName, failCode, failMsg);
                } catch (Exception e) {
                    logger.error("处理失败回调失败, taskKey = " + entry.getKey(), e);
                }
            }
        }
        return Boolean.TRUE;
    }

    public Boolean callbackFail(Long taskId, EcontractTaskBo taskBo, String stageName, Integer failCode, String failMsg) throws TException, WmCustomerException {
        return callbackFail(taskId, taskBo, stageName, failCode, failMsg, null, Boolean.TRUE);
    }

    /**
     * 单stage且单execute回调
     */
    public Boolean callbackFail(Long taskId, EcontractTaskBo taskBo, String stageName, Integer failCode, String failMsg, String actionSource) throws TException, WmCustomerException {
        return callbackFail(taskId, taskBo, stageName, failCode, failMsg, actionSource, Boolean.TRUE);
    }

    public Boolean callbackFail(Long taskId, EcontractTaskBo taskBo, String stageName, Integer failCode, String failMsg, String actionSource, Boolean callBack) throws TException, WmCustomerException {
        String executeName = taskBo.getApplyType();
        //构建callback对象
        String callBackFailMsg = WmEcontractCallbackUtil.handleFailMsg(failCode, failMsg);
        EcontractCallbackBo callbackBo = new EcontractCallbackBo.Builder()
                .taskId(taskId)
                .state(WmEcontractCallbackUtil.handleTaskState(failCode))
                .failMsg(callBackFailMsg)
                .stageName(stageName)
                .applyContractType(executeName)
                .build();
        logger.info("callbackFail taskId = {}, executeName = {}, stageName = {}, faileCode = {}, callBack = {}", taskId, executeName, stageName, failCode, callBack);

        //更新batch,failReason字段
        wmEcontractBatchBizService.updateBatchFailMsg(taskId, callBackFailMsg, actionSource);

        //如果不希望回调，则直接返回
        if (!callBack) {
            return Boolean.TRUE;
        }
        switch (EcontractTaskApplyTypeEnum.getByName(executeName)) {
            case POIFEE:
            case FRUIT_TOGETHER_DELIVERY:
            case VIP_CARD_CONTRACT_AGREEMENT:
            case DRONE_DELIVERY:
            case BATCHPOIFEE:
            case NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY:
            case NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY:
                return wmPoiLogisticsClient.cancel(callbackBo, actionSource);
            case PHF_DELIVERY:
                return wmPoiLogisticsClient.cancel(callbackBo, actionSource);
            case AGENT_SQS_STANDARD:
                wmLogisticsContractThriftServiceAdapter.cancelSignByChannel(callbackBo, taskBo);
                return Boolean.TRUE;
            case SETTLE:
                wmSettleManagerService.wmSettleConfirmFlowCallback(callbackBo, null);
                return Boolean.TRUE;
            case C1CONTRACT:
            case C2CONTRACT:
            case QUA_REAL_LETTER:
            case POI_PROMOTION_SERVICE:
            case DELIVERY_SERVICE_CONTRACT:
            case DELIVERY_SITE_CONTRACT:
            case AD_ANNUAL_FRAMEWORK_CONTRACT:
            case BRAND_AD_CONTRACT:
            case AD_ORDER:
            case PHF_CHARGE:
            case BUSINESS_CUSTOMER_E_CONTRACT:
            case GROUP_MEAL:
            case BAG_SERVICE:
            case FOODCITY_STATEMENT:
            case MEDIC_ORDER_SPLIT:
            case INTERIM_SELF:
            case SUBJECT_CHANGE_SUPPLEMENT:
            case FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT:
            case SPEEDY_DELIVERY_COOPERATION_AGREEMENT:
            case COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT:
            case DAOCAN_SERVICE_C1_CONTRACT:
            case DAOCAN_SERVICE_C2_CONTRACT:
            case NATIONAL_SUBSIDY_PURCHASE:
                return wmContractService.econtractCallback(callbackBo);
            case CUSTOMER:
                wmCustomerPoiService.customerPoiUnbindCallBack(callbackBo);
                return Boolean.TRUE;
            case KP:
            case AGENT_SIGNER_AUTH:
                wmCustomerKpAuditService.signerKpAuthCallback(taskId.intValue(), EcontractTaskStateEnum.FAIL);
                return Boolean.TRUE;
            case OPERATION_MANAGER_KP_CONFIRM:
                wmCustomerKpAuditService.operationManagerKpAuthCallback(callbackBo);
                return Boolean.TRUE;
            case BATCH_POI_GENERATE_PDF:
                return wmCustomerPoiService.customerPoiPdfCallBack(callbackBo);
            case ADDEDSERVICEDISCOUNT:
                wmPoiLogisticsClient.handleCallBackForVasFeeActivity(callbackBo, taskBo);
                return Boolean.TRUE;
            case SHANGOU_REBATE:
                shanGouRebateServiceClient.handleCallBackForShanGouRebate(callbackBo);
                return Boolean.TRUE;
            case WM_POI_BASE_TAG_SIGN:
                wmPoiBaseClient.handleCallBack(callbackBo,null,failCode);
                return Boolean.TRUE;
            case MED_DEPOSIT:
                return wmPoiHealthClient.handleCallBack(callbackBo);
            case FOODCITY_POI_TABLE:
                wmCustomerPoiService.customerPoiPreBindCallBack(callbackBo);
                return Boolean.TRUE;
            default:
                //不处理
        }
        return Boolean.FALSE;
    }
}
