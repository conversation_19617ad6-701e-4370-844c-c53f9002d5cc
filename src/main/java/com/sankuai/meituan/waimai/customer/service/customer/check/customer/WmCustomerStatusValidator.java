package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.check.BaseWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerAuditStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;

/**
 * 客户状态校验
 */
@Service
public class WmCustomerStatusValidator extends BaseWmCustomerValidator implements IWmCustomerValidator {

    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        if(wmCustomerBasicBo.getId() <= 0){
            return checkPass(validateResultBo);
        }

        WmCustomerDB wmCustomerDB = getExistWmCustomer(wmCustomerBasicBo.getId());
        //审核系统品控修改资质发起校验，不校验审核状态，因为发起校验时客户审核状态为审核中
        if (isAudit) {
            return checkPass(validateResultBo);
        }

        if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode()) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "正在审核,无法修改");
        }

        //校验客户状态为特批中，不允许修改
        if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDITING.getCode()) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "特批中,无法修改");
        }
        //校验客户状态为待发起特批，不允许修改
        if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_TO_APPLY_SPECIAL_AUDIT.getCode()) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "待发起特批,无法修改");
        }

        return checkPass(validateResultBo);
    }
}
