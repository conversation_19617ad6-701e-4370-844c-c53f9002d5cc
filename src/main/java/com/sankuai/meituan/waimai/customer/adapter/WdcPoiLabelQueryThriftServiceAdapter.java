package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.wdcn.api.dto.WdcPoiLabelDTO;
import com.sankuai.meituan.wdcn.api.request.BaseRequest;
import com.sankuai.meituan.wdcn.api.request.label.WdcPoiLabelQueryRequest;
import com.sankuai.meituan.wdcn.api.response.WdcPoiLabelQueryResponse;
import com.sankuai.meituan.wdcn.api.service.WdcPoiLabelQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公海标签查询服务
 * <AUTHOR>
 * @date 2024/05/30
 * @email <EMAIL>
 */
@Service
@Slf4j
public class WdcPoiLabelQueryThriftServiceAdapter {

    @Autowired
    private WdcPoiLabelQueryThriftService wdcPoiLabelQueryThriftService;

    public final String SYSTEM_APPKEY = "com.sankuai.waimai.e.customer";

    /**
     * 新增标签
     */
    public final int ADD_LABEL = 1;
    /**
     * 删除标签
     */
    public final int DELETE_LABEL = 0;


    /**
     * 根据线索ID和标签ID查询线索标签
     * @param wdcClueIdList 线索ID列表
     * @param labelId 标签ID
     * @return WdcPoiLabelDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<WdcPoiLabelDTO> getWdcClueLabelList(List<Long> wdcClueIdList, Integer labelId) throws WmSchCantException {
        try {
            WdcPoiLabelQueryRequest queryRequest = new WdcPoiLabelQueryRequest();
            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setAppKey(SYSTEM_APPKEY);

            WdcPoiLabelQueryRequest request = new WdcPoiLabelQueryRequest();
            // 标签ID
            request.setLabelId(labelId);
            // 线索ID
            request.setWdcIdList(wdcClueIdList);
            request.setBaseRequest(baseRequest);

            log.info("[WmWdcPoiLabelQueryThriftServiceAdapter.getWdcClueLabel] request = {}", JSONObject.toJSONString(request));
            WdcPoiLabelQueryResponse response = wdcPoiLabelQueryThriftService.queryWdcPoiLabel(request);
            log.info("[WmWdcPoiLabelQueryThriftServiceAdapter.getWdcClueLabel] response = {}", JSONObject.toJSONString(response));

            if (response == null) {
                log.error("[WmWdcPoiLabelQueryThriftServiceAdapter.getWdcClueLabel] response is null. request = {}", JSONObject.toJSONString(request));
                return new ArrayList<>();
            }

            // 只返回有线索标签绑定关系的记录, 否则返回null
            return response.getWdcPoiLabelList().stream()
                    .filter(poiLabel -> poiLabel.getLabelValue().equals(ADD_LABEL))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[WmWdcPoiLabelQueryThriftServiceAdapter.getWdcClueLabel] Exception. wdcClueIdList = {}, labelId = {}",
                    JSONObject.toJSONString(wdcClueIdList), labelId);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询公海线索标签失败");
        }
    }


}
