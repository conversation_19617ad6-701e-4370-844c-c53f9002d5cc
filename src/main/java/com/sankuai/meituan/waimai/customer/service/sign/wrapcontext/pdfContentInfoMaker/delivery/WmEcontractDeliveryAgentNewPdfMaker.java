package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-08-30 15:04
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.DELIVERY_AGENT_NEW)
@Slf4j
@Service
public class WmEcontractDeliveryAgentNewPdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker
        implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        EcontractDeliveryAgentNewInfoBo econtractDeliveryAgentNewInfoBo = deliveryInfoBo.getEcontractDeliveryAgentNewInfoBo();
        if (econtractDeliveryAgentNewInfoBo == null) {
            return null;
        }

        //pdf中的业务动态字段，单店list.size=1
        List<Map<String, String>> pdfBizContentList = Lists.newArrayList();
        Map<String, String> pdfBizContentMap = MapUtil.Object2Map(econtractDeliveryAgentNewInfoBo);
        pdfBizContentList.add(pdfBizContentMap);

        //pdf中的非业务动态字段
        Map<String, String> pdfMetaContentMap = Maps.newHashMap();
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContentMap.put("partA", StringUtils.defaultIfEmpty(originContext.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMetaContentMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContentMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMetaContentMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("NEWAGENT_TEMPLATE_ID", 64));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("NEWAGENT_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfBizContent(pdfBizContentList);
        pdfInfoBo.setPdfMetaContent(pdfMetaContentMap);
        log.info("#WmEcontractDeliveryAgentNewPdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }
}
