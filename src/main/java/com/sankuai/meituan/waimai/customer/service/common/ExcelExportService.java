package com.sankuai.meituan.waimai.customer.service.common;

import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.util.ExcelExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.Collection;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/11 15:19
 */
@Slf4j
@Service
public class ExcelExportService {

    @Resource
    private WmUploadEcontractService wmUploadEcontractService;

    public String exportExcel(Collection<?> data, Class<?> excelUnit, ByteArrayOutputStream os, SSOUtil.SsoUser user) {
        try {
            ExcelExportUtil.excelExport(data, excelUnit, os);
        } catch (Exception e) {
            log.warn("ExcelExportService#exportExcel, error ", e);
            return null;
        }
        return wmUploadEcontractService.uploadExcel(os, user);
    }

}
