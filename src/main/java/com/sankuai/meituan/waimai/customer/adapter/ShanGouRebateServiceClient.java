package com.sankuai.meituan.waimai.customer.adapter;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCallbackBo;
import com.sankuai.shangou.merchant.charging.thrift.client.RebateConfigClient;
import com.sankuai.shangou.merchant.charging.thrift.dto.common.OperatorInfoDTO;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ShanGouRebateServiceClient {

    public static final Long   OP_UID   = 0L;

    public static final String OP_UNAME = "商家处理";

    @Resource
    private RebateConfigClient rebateConfigClient;

    public boolean handleCallBackForShanGouRebate(EcontractCallbackBo callbackBo) {
        log.info("#handleCallBackForShanGouRebate,callbackBo={}", JSONObject.toJSONString(callbackBo));
        EcontractTaskStateEnum state = callbackBo.getState();
        switch (state) {
            case IN_PROCESSING:
                updateProtocolUrl(callbackBo);
                break;
            case SUCCESS:
                confirmRebateConfigSign(callbackBo);
                break;
            case FAIL:
                cancelRebateConfigSign(callbackBo);
                break;
            default:
                return false;
        }
        return true;
    }

    private void cancelRebateConfigSign(EcontractCallbackBo callbackBo) {
        Long taskId = callbackBo.getTaskId();
        OperatorInfoDTO operatorInfoDTO = new OperatorInfoDTO();
        operatorInfoDTO.setOpId(OP_UID);
        operatorInfoDTO.setOpName(OP_UNAME);
        try {
            rebateConfigClient.cancelRebateConfigSign(taskId, operatorInfoDTO);
        } catch (TException e) {
            log.error("cancelRebateConfigSign异常", e);
        }
    }

    private void confirmRebateConfigSign(EcontractCallbackBo callbackBo) {
        Long taskId = callbackBo.getTaskId();
        OperatorInfoDTO operatorInfoDTO = new OperatorInfoDTO();
        operatorInfoDTO.setOpId(OP_UID);
        operatorInfoDTO.setOpName(OP_UNAME);
        try {
            rebateConfigClient.confirmRebateConfigSign(taskId, operatorInfoDTO);
        } catch (TException e) {
            log.error("confirmRebateConfigSign异常", e);
        }
    }

    private void updateProtocolUrl(EcontractCallbackBo callbackBo) {
        Long taskId = callbackBo.getTaskId();
        String pdfUrl = MoreObjects.firstNonNull(callbackBo.getPdfUrl(), "");
        OperatorInfoDTO operatorInfoDTO = new OperatorInfoDTO();
        operatorInfoDTO.setOpId(OP_UID);
        operatorInfoDTO.setOpName(OP_UNAME);
        try {
            rebateConfigClient.updateProtocolUrl(taskId, pdfUrl, operatorInfoDTO);
        } catch (TException e) {
            log.error("updateProtocolUrl异常", e);
        }
    }
}
