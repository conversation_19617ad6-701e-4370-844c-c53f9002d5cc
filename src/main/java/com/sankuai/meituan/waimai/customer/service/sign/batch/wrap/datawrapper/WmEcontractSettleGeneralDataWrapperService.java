package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import java.util.Collection;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 电子合同结算信息pdf拼装
 * 普通版结算设置
 */
@Service
public class WmEcontractSettleGeneralDataWrapperService {

    private static final String TEMPLET_NAME = "settle_info_v3.ftl";

    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo)
            throws IllegalAccessException, WmCustomerException {
        List<EcontractSettleInfoBo> settleInfoBoList = JSON
                .parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);
        PdfContentInfoBo pdfInfoBo = genPdfContentInfoBo(settleInfoBoList);
        return Lists.newArrayList(pdfInfoBo);
    }

    //支持部分开钱包
    public void wrap(EcontractBatchContextBo contextBo,
            EcontractTaskBo taskBo, List<PdfContentInfoBo> pdfInfoBoList)
            throws IllegalAccessException, WmCustomerException {
        List<EcontractSettleInfoBo> settleInfoBoList = JSON
                .parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);

        List<EcontractSettleInfoBo> generalSettleInfoBoList = Lists.newArrayList();
        for (EcontractSettleInfoBo temp : settleInfoBoList) {
            if (!temp.isSupportWallet()) {
                generalSettleInfoBoList.add(temp);
            }
        }
        if (CollectionUtils.isEmpty(generalSettleInfoBoList)) {
            return;
        }
        pdfInfoBoList.add(genPdfContentInfoBo(generalSettleInfoBoList));
    }

    private PdfContentInfoBo genPdfContentInfoBo(List<EcontractSettleInfoBo> settleInfoBoList)
            throws IllegalAccessException {
        List<Map<String, String>> pdfMap = Lists.newArrayList();
        for (EcontractSettleInfoBo settleInfoBo:settleInfoBoList) {
            settleInfoBo.setDiffInfo("");
            Map<String, String> map = MapUtil.Object2Map(settleInfoBo);
            pdfMap.add(map);
        }

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfBizContent(pdfMap);
        pdfInfoBo.setPdfTemplateName(ConfigUtilAdapter.getString("PDF_SETTLE_INFO_V3", TEMPLET_NAME));
        return pdfInfoBo;
    }

    private String parseNames(List<EcontractPoiInfoBo> poiInfoBoList) {
        if (CollectionUtils.isEmpty(poiInfoBoList)) {
            return StringUtils.EMPTY;
        }

        StringBuilder sb = new StringBuilder();
        for (EcontractPoiInfoBo poiInfoBo:poiInfoBoList) {
            sb.append(poiInfoBo.getName());
        }
        return sb.toString();
    }

}
