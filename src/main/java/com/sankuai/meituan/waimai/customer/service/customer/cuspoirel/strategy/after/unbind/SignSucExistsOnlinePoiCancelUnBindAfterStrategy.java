package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.unbind;

import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IUnBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240124
 * @desc 签约通知成功但是存在门店是上线状态-取消解绑后置策略
 */
@Service
@Slf4j
public class SignSucExistsOnlinePoiCancelUnBindAfterStrategy implements IUnBindAfterStrategy {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private CustomerTaskService customerTaskService;

    @Override
    public void execute(CustomerPoiUnBindFlowContext context) throws WmCustomerException {
        preUnbindAfterAction(context);
    }

    /**
     * 预解绑后置行为（签约解绑的后置操作行为）
     *
     * @param context
     * @throws WmCustomerException
     */
    private void preUnbindAfterAction(CustomerPoiUnBindFlowContext context) throws WmCustomerException {

        Integer customerId = context.getCustomerId();
        Integer opUid = context.getOpUid();
        String opName = context.getOpName();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        String logMsg = String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_POI_ONLINE_CANCEL, StringUtils.join(wmPoiIdSet, "、"));

        //添加客户维度取消解绑操作记录
        wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS, logMsg);

        //更新客户门店解绑任务-取消状态
        customerTaskService.updateStatusByCustomerIdAndPoiIdAndType(customerId, context.getNeedUnBindWmPoiIdSet(), null,
                CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode(), CustomerTaskStatusEnum.CANCEL.getCode());
    }
}
