package com.sankuai.meituan.waimai.customer.constant.contract;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;

import java.util.List;

/**
 * @description: 常用的任务常量
 * @author: liuyunjie05
 * @create: 2024/3/8 19:20
 */
public class CommonTaskConstant {

    public static final String BATCH_TYPE = "batch";
    public static final String TASK_TYPE = "task";
    public static final String PACK_TYPE = "pack";
    public static final String MANUAL_TYPE = "manualTask";

    /**
     * 属于批量门店的类型
     */
    public static final List<Integer> BATCH_APPLY_TYPE_LIST = Lists.newArrayList(
            EcontractTaskApplyTypeEnum.BATCHPOIFEE.getType(),
            EcontractTaskApplyTypeEnum.DRONE_DELIVERY.getType(),
            EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY.getType(),
            EcontractTaskApplyTypeEnum.VIP_CARD_CONTRACT_AGREEMENT.getType()
            );

    /**
     * 属于批量门店的名称
     */
    public static final List<String> BATCH_APPLY_NAME_LIST = Lists.newArrayList(
            EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName(),
            EcontractTaskApplyTypeEnum.DRONE_DELIVERY.getName(),
            EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY.getName(),
            EcontractTaskApplyTypeEnum.VIP_CARD_CONTRACT_AGREEMENT.getName(),
            EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName(),
            EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName()
    );


}
