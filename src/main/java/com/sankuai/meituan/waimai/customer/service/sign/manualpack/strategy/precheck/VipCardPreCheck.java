package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck;

import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/3/10 18:17
 */
@Slf4j
@Service
public class VipCardPreCheck implements PreCheck {

    @Resource
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;

    @Override
    public void preCheck(String module, List<WmEcontractSignManualTaskDB> taskInfos, int commitUid) throws WmCustomerException, TException {
        wmLogisticsContractThriftServiceAdapter.trySignManualPackageInfoByChannel(taskInfos, module, commitUid);
    }
}
