package com.sankuai.meituan.waimai.customer.util;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

public class PageUtil {

    /**
     * 默认首页
     */
    private static final int DEFAULT_STARTPAGE = 1;

    /**
     * 默认pageSize
     */
    private static final int DEFAULT_PAGESIZE = 20;

    public static <T> PageData<T> page(List<T> queryResult) {
        return page(queryResult, queryResult);
    }

    public static <T> PageData<T> page(List queryResult, List<T> showResult) {
        if (CollectionUtils.isEmpty(queryResult)) {
            return getEmptyPage();
        }

        PageData pageData = new PageData();
        Page page = (Page) queryResult;
        pageData.setTotal(page.getTotal());
        pageData.setPages(page.getPages());
        pageData.setPageNo(page.getPageNum());
        pageData.setList(showResult);
        return pageData;
    }

    /**
     * 对特定list进行分页
     * @param list list
     * @param pageNum 最小为1
     * @param pageSize pageSize
     * @return list
     */
    public static <T> List<T> paging(List<T> list, int pageNum, int pageSize) {
        //数据校验
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        //数据预处理
        pageNum = pageNum <= 0 ? DEFAULT_STARTPAGE : pageNum;
        pageSize = pageSize <= 0 ? DEFAULT_PAGESIZE : pageSize;

        int fromIndex = (pageNum-1) * pageSize;//根据pageSize和pageNum计算的fromIndex
        int toIndex = pageNum * pageSize;//根据pageSize和pageNum计算的toIndex
        int maxToIndex = list.size();//能够承受的最大toIndex
        toIndex = toIndex > maxToIndex ? maxToIndex : toIndex;//若toIndex大于能够承受最大的toIndex，则重新复制toIndex

        if (fromIndex >= toIndex) {//若fromIndex大于toIndex则返回空list
            return Lists.newArrayList();
        }
        return list.subList(fromIndex, toIndex);
    }

    public static <T> PageData<T> getEmptyPage() {
        PageData pageData = new PageData();
        pageData.setTotal(0);
        pageData.setPages(0);
        pageData.setPageNo(0);
        pageData.setList(Collections.emptyList());
        return pageData;
    }

    public static <T> Page<T> listToPage(List<T> list, int pageNum, int pageSize) {
        Page<T> page = new Page<>(pageNum, pageSize);
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, list.size());
        if (startIndex <= endIndex) {
            page.addAll(list.subList(startIndex, endIndex));
        }
        page.setTotal(list.size());
        return page;
    }
}
