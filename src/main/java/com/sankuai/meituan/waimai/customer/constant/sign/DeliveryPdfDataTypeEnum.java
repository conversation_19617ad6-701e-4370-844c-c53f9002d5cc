package com.sankuai.meituan.waimai.customer.constant.sign;

public enum DeliveryPdfDataTypeEnum {

    TECHNICAL_SERVICE_DEFAULT("TECHNICAL_SERVICE_DEFAULT", "佣金"),
    TECHNICAL_SERVICE_FEE_AGENT_ACTUAL_PAYMENT("TECHNICAL_SERVICE_FEE_AGENT_ACTUAL_PAYMENT", "代理实付+保底佣金"),
    TECHNICAL_SERVICE_WAIMAIV3_FEEMODE("TECHNICAL_SERVICE_WAIMAIV3_FEEMODE", "佣金外卖新费率2.0"),
    TECHNICAL_SERVICE_DRONE("TECHNICAL_SERVICE_DRONE", "无人机佣金"),
    TECHNICAL_SERVICE_FRUIT_TOGETHER("TECHNICAL_SERVICE_FRUIT_TOGETHER", "班次送佣金"),
    TECHNICAL_SERVICE_WAIMAIQIKE_FEEMODE("TECHNICAL_SERVICE_WAIMAIQIKE_FEEMODE", "佣金外卖企客旧费率"),
    TECHNICAL_SERVICE_WAIMAIQIKEV2_FEEMODE("TECHNICAL_SERVICE_WAIMAIQIKEV2_FEEMODE", "佣金外卖企客新费率"),
    TECHNICAL_SERVICE_QIKEAGENT_FEEMODE("TECHNICAL_SERVICE_QIKEAGENT_FEEMODE", "佣金企客代理费率"),
    TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE("TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE", "佣金闪购企客新费率"),
    TECHNICAL_SERVICE_YIYAO_QIKE_FEEMODE("TECHNICAL_SERVICE_YIYAO_QIKE_FEEMODE", "佣金医药企客费率"),
    PERFORMANCE_SERVICE_DEFAULT("PERFORMANCE_SERVICE_DEFAULT", "配送服务费"),
    PERFORMANCE_SERVICE_DRONE("PERFORMANCE_SERVICE_DRONE", "无人机配送服务费"),
    PERFORMANCE_SERVICE_FRUIT_TOGETHER("PERFORMANCE_SERVICE_FRUIT_TOGETHER", "班次送配送服务费"),
    PERFORMANCE_SERVICE_WAIMAIV3_FEEMODE("PERFORMANCE_SERVICE_WAIMAIV3_FEEMODE", "配送服务费外卖新费率2.0"),
    PERFORMANCE_SERVICE_WAIMAIQIKE_FEEMODE("PERFORMANCE_SERVICE_WAIMAIQIKE_FEEMODE", "配送服务费外卖企客旧费率"),
    PERFORMANCE_SERVICE_WAIMAIQIKEV2_FEEMODE("PERFORMANCE_SERVICE_WAIMAIQIKE_FEEMODE", "配送服务费外卖企客新费率"),
    PERFORMANCE_SERVICE_QIKEAGENT_FEEMODE("PERFORMANCE_SERVICE_QIKEAGENT_FEEMODE", "配送服务费企客代理费率"),
    PERFORMANCE_SERVICE_SHANGOUV2_2NEC_FEEMODE("PERFORMANCE_SERVICE_SHANGOUV2_2NEC_FEEMODE", "配送服务费闪购企客新费率"),
    PERFORMANCE_SERVICE_YIYAO_QIKE_FEEMODE("PERFORMANCE_SERVICE_YIYAO_QIKE_FEEMODE", "配送服务费医药企客费率"),
    WHOLECITY_INFO_DEFAULT("WHOLECITY_INFO_DEFAULT", "全城送协议"),
    WHOLECITY_INFO_WAIMAIV3_FEEMODE("WHOLECITY_INFO_WAIMAIV3_FEEMODE", "全城送协议外卖新费率2.0"),
    COMPANYCUSTOMER_LONGDISTANCE_DEFAULT("COMPANYCUSTOMER_LONGDISTANCE_DEFAULT", "企客远距离协议"),
    COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKE_FEEMODE("COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKE_FEEMODE", "企客远距离协议外卖企客旧费率"),
    COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKEV2_FEEMODE("COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKEV2_FEEMODE", "企客远距离协议外卖企客新费率"),
    ENTERPRISE_CUSTOMER_LONG_DISTANCE_YIYAO_QIKE_FEEMODE("ENTERPRISE_CUSTOMER_LONG_DISTANCE_YIYAO_QIKE_FEEMODE", "企客远距离协议医药企客费率"),
    ENTERPRISE_CUSTOMER_LONG_DISTANCE_SHANGOUV2_2NEC_FEEMODE("ENTERPRISE_CUSTOMER_LONG_DISTANCE_SHANGOUV2_2NEC_FEEMODE", "企客远距离协议闪购企客新费率"),
    COLD_CHAIN_INFO_QIKE("COLD_CHAIN_QIKE", "冷链即时达配送服务收费标准确认函(医药企客/闪购企客)"),
    AGGREGATION_DELIVERY_SHANGOUV2_0("AGGREGATION_DELIVERY_SHANGOUV2_0", "闪购2.0聚合配协议"),
    AGGREGATION_DELIVERY_SHANGOUV2_2("AGGREGATION_DELIVERY_SHANGOUV2_2", "闪购2.2聚合配协议"),
    AGGREGATION_DELIVERY_DEFAULT("AGGREGATION_DELIVERY_DEFAULT", "旧聚合配"),
    AGGREGATION_DELIVERY_DEFAULT_V2("AGGREGATION_DELIVERY_DEFAULT_V2", "聚合配新版"),


    NATIONAL_SUBSIDY_SG22_PERFORMANCE("NATIONAL_SUBSIDY_SG22_PERFORMANCE", "国补-闪购2.2-专快混-履约合同"),
    NATIONAL_SUBSIDY_SG22_NKS_PERFORMANCE("NATIONAL_SUBSIDY_SG22_NKS_PERFORMANCE", "国补-闪购2.2-新快送-履约合同"),
    NATIONAL_SUBSIDY_SG22_WHOLE_CITY_PERFORMANCE("NATIONAL_SUBSIDY_SG22_WHOLE_CITY_PERFORMANCE", "国补-闪购2.2-全城送-履约合同"),
    NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE("NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE", "国补-闪购企客新费率-主配-履约合同"),
    NATIONAL_SUBSIDY_SG_NEW_QIKE_LONG_DISTANCE_PERFORMANCE("NATIONAL_SUBSIDY_SG_NEW_QIKE_LONG_DISTANCE_PERFORMANCE", "国补-闪购企客新费率-远距离-履约合同"),

    PERFORMANCE_SERVICE_SG_22_NKS("PERFORMANCE_SERVICE_SG_22_NKS", "闪购费率2.2-履约合同-新快送"),
    PERFORMANCE_SERVICE_SG_22("PERFORMANCE_SERVICE_SG_22", "闪购费率2.2-履约合同"),
    TECHNICAL_SERVICE_SG_20("TECHNICAL_SERVICE_SG_20", "闪购费率2.0-配送协议"),
    TECHNICAL_SERVICE_SG_22("TECHNICAL_SERVICE_SG_22", "闪购费率2.2-佣金合同")
    ;

    private String name;
    private String desc;

    DeliveryPdfDataTypeEnum(String name, String desc){
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }
}
