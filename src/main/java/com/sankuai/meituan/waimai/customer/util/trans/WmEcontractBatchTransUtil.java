package com.sankuai.meituan.waimai.customer.util.trans;

import com.alibaba.fastjson.JSONObject;
import com.dianping.frog.sdk.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.WmEcontractSignBatchBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Set;

@Slf4j
public class WmEcontractBatchTransUtil {
    /**
     * 指定类型的任务可以获取签约batch对象中的全部字段
     */
    private static final Set<EcontractBatchTypeEnum> CONTAINS_ALL_DATA_TYPE = Sets.newHashSet();
    static{
        CONTAINS_ALL_DATA_TYPE.add(EcontractBatchTypeEnum.OPERATION_MANAGER_KP_CONFIRM);
    }

    public static List<Long> transToIdList(List<WmEcontractSignBatchDB> batchDBList) {
        if (CollectionUtils.isEmpty(batchDBList)) {
            return Lists.newArrayList();
        }

        List<Long> batchIdList = Lists.newArrayList();
        for (WmEcontractSignBatchDB batchDB:batchDBList) {
            batchIdList.add(batchDB.getId());
        }
        return batchIdList;
    }

    public static List<WmEcontractSignBatchBo> transBatchDBList2BatchBoList(List<WmEcontractSignBatchDB> intput){
        if(CollectionUtils.isEmpty(intput)){
            return Lists.newArrayList();
        }
        List<WmEcontractSignBatchBo> result = Lists.newArrayList();
        for(WmEcontractSignBatchDB temp : intput){
            result.add(transBatchDB2BatchBo(temp));
        }
        return result;
    }

    private static WmEcontractSignBatchBo transBatchDB2BatchBo(WmEcontractSignBatchDB temp) {
        if(temp == null){
            return null;
        }
        WmEcontractSignBatchBo result = new WmEcontractSignBatchBo();
        result.setBatchId(temp.getId().intValue());
        result.setCustomerId(temp.getCustomerId());
        result.setBatchState(temp.getBatchState());
        result.setCommitUid(temp.getCommitUid());
        result.setCtime(temp.getCtime());
        result.setUtime(temp.getUtime());
        result.setPackId(temp.getPackId());
        result.setBatchContext(temp.getBatchContext());
        return result;
    }

    public static List<WmEcontractSignBatchBo> transBatchDBList2SimpleBatchBoList(List<WmEcontractSignBatchDB> intput) {
        if (CollectionUtils.isEmpty(intput)) {
            return Lists.newArrayList();
        }
        List<WmEcontractSignBatchBo> result = Lists.newArrayList();
        for (WmEcontractSignBatchDB temp : intput) {
            result.add(transBatchDB2SimpleBatchBo(temp));
        }
        log.info("#transBatchDBList2SimpleBatchBoList, result:{}", JsonUtils.toJson(result));
        return result;
    }

    private static WmEcontractSignBatchBo transBatchDB2SimpleBatchBo(WmEcontractSignBatchDB temp) {
        if (temp == null) {
            return null;
        }
        WmEcontractSignBatchBo result = new WmEcontractSignBatchBo();
        result.setBatchId(temp.getId().intValue());
        result.setCustomerId(temp.getCustomerId());
        result.setBatchState(temp.getBatchState());
        result.setCommitUid(temp.getCommitUid());
        result.setPackId(temp.getPackId());
        result.setCtime(temp.getCtime());
        result.setUtime(temp.getUtime());

        if (StringUtils.isNotBlank(temp.getBatchContext())) {
            EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(temp.getBatchContext(), EcontractBatchContextBo.class);
            EcontractBatchTypeEnum batchTypeEnum = econtractBatchContextBo.getBatchTypeEnum();
            if (!CONTAINS_ALL_DATA_TYPE.contains(batchTypeEnum)) {
                econtractBatchContextBo.setTaskIdAndTaskMap(Maps.newHashMap());
            }
            result.setBatchContext(JSONObject.toJSONString(econtractBatchContextBo));
        } else {
            result.setBatchContext(temp.getBatchContext());
        }
        return result;
    }
}
