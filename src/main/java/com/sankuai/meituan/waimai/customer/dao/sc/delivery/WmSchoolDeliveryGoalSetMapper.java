package com.sankuai.meituan.waimai.customer.dao.sc.delivery;

import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryGoalSetDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校交付目标制定生效信息Mapper
 * <AUTHOR>
 * @date 2024/02/08
 * @email <EMAIL>
 **/
@Component
public interface WmSchoolDeliveryGoalSetMapper {

    /**
     * 根据主键ID查询学校交付目标制定生效信息
     * @param id 主键ID
     * @return 学校交付目标制定生效信息
     */
    WmSchoolDeliveryGoalSetDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据学校主键ID查询学校交付目标制定生效信息
     * @param schoolPrimaryId 学校主键ID
     * @return 学校交付目标制定生效信息
     */
    List<WmSchoolDeliveryGoalSetDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据交付ID查询学校交付目标制定生效信息
     * @param deliveryId 交付ID
     * @return 学校交付目标制定生效信息
     */
    WmSchoolDeliveryGoalSetDO selectByDeliveryId(@Param("deliveryId") Integer deliveryId);

    /**
     * 新增学校交付目标制定生效信息
     * @param wmSchoolDeliveryGoalSetDO 交付目标制定生效信息
     * @return 更新行数
     */
    int insertSelective(WmSchoolDeliveryGoalSetDO wmSchoolDeliveryGoalSetDO);

    /**
     * 根据主键ID更新学校交付目标制定生效信息
     * @param wmSchoolDeliveryGoalSetDO 学校交付目标制定生效信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmSchoolDeliveryGoalSetDO wmSchoolDeliveryGoalSetDO);

}
