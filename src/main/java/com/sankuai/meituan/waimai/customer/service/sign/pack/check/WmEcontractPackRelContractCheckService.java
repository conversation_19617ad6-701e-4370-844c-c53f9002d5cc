package com.sankuai.meituan.waimai.customer.service.sign.pack.check;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 电子合同打包(反查关联合同校验)
 */
@Service
@Slf4j
public class WmEcontractPackRelContractCheckService {

    @Resource
    private WmContractService wmContractService;

    @Resource
    private WmCustomerPoiService wmCustomerPoiService;

    /**
     * 是否关联合同
     * @param customerId 客户ID
     * @return 是否关联合同
     */
    public boolean hasEffectRelContractByCustomerId(Integer customerId) {
        List<WmTempletContractDB> contractList =  wmContractService.getEffectContractByCustomerIdAndType(customerId,
                                                                                         Lists.newArrayList(
                                                                                             WmTempletContractTypeEnum.C1_E.getCode(),
                                                                                             WmTempletContractTypeEnum.C1_PAPER.getCode()));
        return contractList.size() > 0;
    }

    /**
     * 是否关联合同c2
     * @param customerId 客户ID
     * @return 是否关联合同
     */
    public boolean hasEffectRelContractC2ByCustomerId(Integer customerId) {
        boolean c2PackAllGrayBoolean= MccSignConfig.getC2PackAllGray();
        log.info("hasEffectRelContractC2ByCustomerId C2PackAllGray:[{}]",c2PackAllGrayBoolean);
        if(c2PackAllGrayBoolean){
            return false;
        }

        List<WmTempletContractDB> contractList =  wmContractService.getEffectContractByCustomerIdAndType(customerId,
                                                                                         Lists.newArrayList(
                                                                                             WmTempletContractTypeEnum.C2_E.getCode(),
                                                                                             WmTempletContractTypeEnum.C2_PAPER.getCode()));
        return contractList.size() > 0;
    }

    /**
     * 获取已录入c2合同
     * @param customerId
     * @return
     */
    public boolean hasRelContractC2ByCustomerId(Integer customerId) {
        List<WmTempletContractDB> contractList =  wmContractService.getContractByCustomerIdAndTypes(customerId,
                Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode()));
        return contractList.size() > 0;
    }

}
