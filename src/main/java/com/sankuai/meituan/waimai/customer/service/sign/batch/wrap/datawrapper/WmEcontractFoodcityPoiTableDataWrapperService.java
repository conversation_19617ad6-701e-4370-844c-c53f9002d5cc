package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-05-16 19:21
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.FOODCITY_POI_TABLE)
public class WmEcontractFoodcityPoiTableDataWrapperService implements IWmEcontractDataWrapperService {

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.FOODCITY_POI_TABLE);
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        // 新模式
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("FOODCITYPOITABLE_TEMPLATE_ID", 48)); // 指定模版
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("FOODCITYPOITABLE_TEMPLATE_VERSION")); // (可选)指定版本，不指定或传null、传0的话则默认为当前已发布的版本
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo, taskBo));
        pdfInfoBo.setPdfBizContent(Lists.<Map<String, String>>newArrayList());
        return Lists.newArrayList(pdfInfoBo);
    }

    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        log.info("taskBo = {}", JSON.toJSONString(taskBo));
        JSONObject jsonObject = JSON.parseObject(taskBo.getApplyContext());
        Map<String, String> pdfMap = Maps.newHashMap();
        Map<String, Long> poiTableNameMap = JSONObject.parseObject(jsonObject.getString("poiTableName"), Map.class);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Long> entry : poiTableNameMap.entrySet()) {
            sb.append(entry.getKey());
            sb.append(" ID ");
            sb.append(entry.getValue());
            sb.append("；\n");
        }
        String poiTableNameStr = sb.substring(0, sb.length() - 1);
        pdfMap.put("partAName", jsonObject.getString("partAName"));
        pdfMap.put("poiTableName", poiTableNameStr);
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.MT_SIGNKEY);
        log.info("pdfMap = {}", JSON.toJSONString(pdfMap));
        return pdfMap;
    }
}
