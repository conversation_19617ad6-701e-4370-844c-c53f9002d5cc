package com.sankuai.meituan.waimai.customer.config;

import com.sankuai.meituan.util.ConfigUtilAdapter;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description: 相关灰度时候的开关，以及灰度放量设置等等。
 * * 全量后，需移除开关，避免以后的代码婚活
 * -@date: 2023/4/28 4:04 PM
 */
public class MccGrayConfig {

    /**
     * 临时开关类别：
     * 医药多店消息是否推送，true-是，false-否，默认否
     * 全量后，需移除开关
     */
    public static boolean pushMedicAccountMsg() {
        return ConfigUtilAdapter.getBoolean("push_medic_account_msg", false);
    }

    /**
     * 多店消息百分比
     *
     * @return 灰度
     */
    public static int getMultiCustomerMSgPercent() {
        return ConfigUtilAdapter.getInt("multi_customer_msg_percent", 10000);
    }

    /**
     * 上线检查点合同侧不查询加解密字段
     *
     * @return 灰度百分比
     */
    public static Integer contractOnlineCheckWithoutSignPhoneGray() {
        return ConfigUtilAdapter.getInt("contract_online_check_without_signphone_gray", 0);
    }
}
