package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.factory;

import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.settle.constant.WmSettlePushDaXiangConstant;
import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.service.PushDXContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleInputContext;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleSwitchService;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleModifyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WmSettleInputContextFactory {

    @Autowired
    private WmSettleDBMapper wmSettleDBMapper;
    @Autowired
    private WmCustomerService wmCustomerService;
    @Autowired
    private WmSettleService wmSettleService;
    @Autowired
    private WmPoiSettleDBMapper wmPoiSettleDBMapper;
    @Autowired
    private WmSettleAuditedDBMapper wmSettleAuditedDBMapper;
    @Autowired
    private WmSettleSwitchService wmSettleSwitchService;

    public WmSettleInputContext makeContext(WmSettleModifyBo wmSettleModifyBo,
                                            int opUid, String opUname)
            throws WmCustomerException {
        WmSettleInputContext inputContext = new WmSettleInputContext();
        wmSettleModifyBo.setPackWay(MoreObjects.firstNonNull(wmSettleModifyBo.getPackWay(), SignPackWay.DO_SIGN.getCode()));

        inputContext.setWmSettleModifyBo(wmSettleModifyBo);
        inputContext.setWmCustomerId(wmSettleModifyBo.getWmCustomerId());
        inputContext.setOpUid(opUid);
        inputContext.setOpUname(opUname);

        int wmCustomerId = wmSettleModifyBo.getWmCustomerId();
        List<WmSettleDB> wmSettleDBList = wmSettleDBMapper.getWmSettleByCustomerId(wmCustomerId);
        inputContext.setWmSettleDBList(wmSettleDBList);

        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerId);
        inputContext.setWmCustomerDB(wmCustomerDB);

        WmSettle wmSettle = wmSettleModifyBo.getWmSettle();

        List<Integer> wmPoiCheckList = wmSettleService.genWmSettlePoiCheckList(wmSettleModifyBo.isEffective(), wmCustomerId, wmSettle.getId());

        PushDXContext pushDXContext = new PushDXContext();
        pushDXContext.setWmCustomerId(wmCustomerId);
        pushDXContext.setWmPoiCheckList(wmPoiCheckList);
        pushDXContext.setWmPoiIdList(wmSettle.getWmPoiIdList());
        pushDXContext.setErrorType(WmSettlePushDaXiangConstant.errorType.BATCH_ADD);
        pushDXContext.setErrorModule(WmSettlePushDaXiangConstant.errorModule.SETTLE_POI);
        pushDXContext.setErrorReason(WmSettlePushDaXiangConstant.errorReason.OFFLINE_SETTLE_REL);
        pushDXContext.setErrorMsg("存在门店已经关联其他结算,详细信息请看大象通知");
        pushDXContext.setOpUid(opUid);
        inputContext.setPushDXContext(pushDXContext);

        wmSettle.setWm_contract_id(wmCustomerId);
        wmSettle.setAcc_cardno(wmSettle.getAcc_cardno().replace(" ", ""));

        if (wmSettle.getId() > 0) {
            WmSettleDB wmSettleDBOld = wmSettleDBMapper.getById(wmSettle.getId());
            List<Integer> wmPoiIdListOld = wmPoiSettleDBMapper.getWmPoiIdListBySettleId(wmSettle.getId());

            wmPoiIdListOld.addAll(ObjectUtil.longList2IntList(wmSettleSwitchService.getOffineWmPoiIdByWmSettleId(wmCustomerId, wmSettle.getId())));

            inputContext.setWmSettleDBOld(wmSettleDBOld);
            inputContext.setWmPoiIdListOld(wmPoiIdListOld);

            WmSettleAuditedDB wmSettleAuditedDBOld = wmSettleAuditedDBMapper.getSettleAuditedBySettleId(wmSettle.getId());
            inputContext.setWmSettleAuditedDBOld(wmSettleAuditedDBOld);

            WmSettleAudited wmSettleAuditedByWmSettleId = wmSettleService.getWmSettleAuditedByWmSettleId(wmSettle.getId());
            if (wmSettleAuditedByWmSettleId != null) {
                inputContext.setWmSettleAuditedList(Lists.newArrayList(wmSettleAuditedByWmSettleId));
            }
        }

        if (!ConfigUtilAdapter.getBoolean("close_settle_name_check",true) && StringUtils.isNotEmpty(wmSettle.getName())) {
            List<Integer> wmSettleIdList = wmSettleDBMapper.getWmSettleIdListBySettleName(wmSettle.getName());
            inputContext.setWmSettleIdListBySettleName(wmSettleIdList);
            List<Integer> wmSettleIdListBySettleNameAudited = wmSettleAuditedDBMapper.getWmSettleIdListBySettleName(wmSettle.getName());
            inputContext.setWmSettleIdListBySettleNameAudited(wmSettleIdListBySettleNameAudited);
        }

        if (wmCustomerId > 0) {
            List<Long> switchingWmPoiIdList = wmSettleSwitchService.getSwitchingWmPoiIdList(wmCustomerId, true);
            inputContext.setSwitchingWmPoiIdSet(Sets.newHashSet(switchingWmPoiIdList));
            List<Long> switchingWmPoiIdListFromOldWmCustomerId = wmSettleSwitchService.getSwitchingWmPoiIdListFromOldWmCustomerId(wmCustomerId);
            inputContext.setSwitchingWmPoiIdListFromSourceCustomer(switchingWmPoiIdListFromOldWmCustomerId);
            List<SwitchPoiInfo> settleSwitchPoiInfoList = wmSettleSwitchService.getSettleSwitchPoiInfoList(wmCustomerId);
            inputContext.setSettleSwitchPoiInfoList(settleSwitchPoiInfoList);
        }

        inputContext.setWmSettleList(Lists.newArrayList(wmSettle));

        log.debug("#WmSettleInputContext#makeContext={}", JSONObject.toJSONString(inputContext));
        return inputContext;
    }

    public WmSettleInputContext makeContext(int wmCustomerId, String supplementalUrl, String qdbUrl,
            int opUid,
            String opName) {
        WmSettleInputContext inputContext = new WmSettleInputContext();
        WmSettleModifyBo modifyBo = WmSettleModifyBo.buildDefaultWmSettleModifyBo();
        modifyBo.setWmCustomerId(wmCustomerId);
        modifyBo.setSupplementalUrl(supplementalUrl);
        modifyBo.setQdbUrl(qdbUrl);
        inputContext.setWmSettleModifyBo(modifyBo);

        inputContext.setOpUid(opUid);
        inputContext.setOpUname(opName);

        log.debug("#WmSettleInputContext#makeContext={}",JSONObject.toJSONString(inputContext));
        return inputContext;
    }

    public WmSettleInputContext makeContext(int wmCustomerId, String wmCustomerName, int opUid,
            String opUname,
            String supplementalUrl, String qdbUrl) throws WmCustomerException{
        WmSettleInputContext inputContext = new WmSettleInputContext();
        inputContext.setWmCustomerId(wmCustomerId);
        inputContext.setOpUid(opUid);
        inputContext.setOpUname(opUname);
        WmSettleModifyBo modifyBo = WmSettleModifyBo.buildDefaultWmSettleModifyBo();
        modifyBo.setWmCustomerId(wmCustomerId);
        modifyBo.setQdbUrl(qdbUrl);
        modifyBo.setSupplementalUrl(supplementalUrl);
        modifyBo.setCommit(true);
        inputContext.setWmSettleModifyBo(modifyBo);

        List<WmSettleDB> wmSettleDBList = wmSettleDBMapper.getWmSettleByCustomerId(wmCustomerId);
        inputContext.setWmSettleDBList(wmSettleDBList);

        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerId);
        inputContext.setWmCustomerDB(wmCustomerDB);

        List<WmSettle> wmSettleList = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId, true);
        List<WmSettleAudited> wmSettleAuditedList = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId, true);
        inputContext.setWmSettleList(wmSettleList);
        inputContext.setWmSettleAuditedList(wmSettleAuditedList);

        List<Long> switchingWmPoiIdListFromOldWmCustomerId = wmSettleSwitchService.getSwitchingWmPoiIdListFromOldWmCustomerId(wmCustomerId);
        inputContext.setSwitchingWmPoiIdListFromSourceCustomer(switchingWmPoiIdListFromOldWmCustomerId);

        log.debug("#WmSettleInputContext#makeContext={}",JSONObject.toJSONString(inputContext));
        return inputContext;
    }

    public WmSettleInputContext makeContext(int wmCustomerId, int signPackWay, int opUid,
            String opUname) throws WmCustomerException{
        WmSettleInputContext inputContext = new WmSettleInputContext();
        inputContext.setWmCustomerId(wmCustomerId);

        List<WmSettle> wmSettleList = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId, true);
        List<WmSettleAudited> wmSettleAuditedList = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId, true);
        inputContext.setWmSettleList(wmSettleList);
        inputContext.setWmSettleAuditedList(wmSettleAuditedList);

        List<Long> switchingWmPoiIdListFromOldWmCustomerId = wmSettleSwitchService.getSwitchingWmPoiIdListFromOldWmCustomerId(wmCustomerId);
        inputContext.setSwitchingWmPoiIdListFromSourceCustomer(switchingWmPoiIdListFromOldWmCustomerId);

        log.debug("#WmSettleInputContext#makeContext={}",JSONObject.toJSONString(inputContext));
        return inputContext;
    }

    public WmSettleInputContext makeContext(int wmCustomerId) {
        WmSettleInputContext inputContext = new WmSettleInputContext();
        inputContext.setWmCustomerId(wmCustomerId);
        log.info("#WmSettleInputContext#makeContext={}",JSONObject.toJSONString(inputContext));
        return inputContext;
    }

    public WmSettleInputContext makeContextForAsyCommit(int wmCustomerId) throws WmCustomerException{
        WmSettleInputContext inputContext = new WmSettleInputContext();
        inputContext.setWmCustomerId(wmCustomerId);
        List<WmSettle> wmSettleList = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId, true);
        inputContext.setWmSettleList(wmSettleList);
        return inputContext;
    }
}
