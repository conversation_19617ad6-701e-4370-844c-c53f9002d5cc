package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WmScCanteenPoiAuditDB extends WmScCommonDB {
    private Long id;

    private Integer canteenId;

    private Long taskId;

    private String electronicId;

    private Integer auditStatus;

    private Integer valid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCanteenId() {
        return canteenId;
    }

    public void setCanteenId(Integer canteenId) {
        this.canteenId = canteenId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getElectronicId() {
        return electronicId;
    }

    public void setElectronicId(String electronicId) {
        this.electronicId = electronicId == null ? null : electronicId.trim();
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }
}