package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoResponseDTO;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SendBatchSignSmsRequestDTO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/23 16:13
 */
@Service
@Slf4j
public class EcontractManagerServiceAdapter {

    @Resource
    private EcontractManagerService econtractManagerService;

    public void sendBatchSignTaskSms(Long recordBatchId) {
        SendBatchSignSmsRequestDTO requestDTO = new SendBatchSignSmsRequestDTO();
        requestDTO.setBatchId(recordBatchId.intValue());

        log.info("EcontractManagerServiceAdapter#sendBatchSignTaskSms, requestDTO: {}", JSON.toJSONString(requestDTO));
        econtractManagerService.sendBatchSignTaskSms(requestDTO);
    }

    /**
     * 批量创建PDF(单模版)
     * @param pdfBos
     * @return
     */
    public List<String> batchOnlyCreatePdf(List<PdfContentInfoBo> pdfBos) throws WmCustomerException {
        log.info("EcontractManagerServiceAdapter#batchOnlyCreatePdfWithSingleTemplate pdfBos:{}", JSONObject.toJSONString(pdfBos));
        final int MAX_RETRIES = 3;

        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                List<String> pdfUrls = econtractManagerService.batchOnlyCreatePdf(pdfBos);
                log.info("EcontractManagerServiceAdapter#batchOnlyCreatePdfWithSingleTemplate pdfUrls:{}", JSONObject.toJSONString(pdfUrls));
                return pdfUrls;
            } catch (Exception e) {
                log.warn("EcontractManagerServiceAdapter#batchOnlyCreatePdfWithSingleTemplate 第{}次尝试失败", attempt, e);
                if (attempt == MAX_RETRIES) {
                    log.error("EcontractManagerServiceAdapter#batchOnlyCreatePdfWithSingleTemplate 重试{}次后仍然失败", MAX_RETRIES, e);
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "批量创建电子合同失败");
                }
            }
        }

        throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "批量创建电子合同失败");
    }

    /**
     * 查询签约任务信息
     * @param token
     * @param recordKey
     * @return
     */
    public StageBatchInfoResponseDTO queryStageBatchInfo(String token, String recordKey) throws WmCustomerException{
        log.info("EcontractManagerServiceAdapter#queryStageBatchInfo recordKey:{}, token:{}", recordKey, token);
        try {
            StageBatchInfoResponseDTO responseDTO = econtractManagerService.queryStageBatchInfoBoListByRecordKey(token, recordKey);
            log.info("EcontractManagerServiceAdapter#queryStageBatchInfo StageBatchInfoResponseDTO:{}", JSONObject.toJSONString(responseDTO));
            return responseDTO;
        } catch (Exception e) {
            log.error("EcontractManagerServiceAdapter#queryStageBatchInfo 查询签约任务信息失败", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询签约任务信息失败");
        }
    }
}
