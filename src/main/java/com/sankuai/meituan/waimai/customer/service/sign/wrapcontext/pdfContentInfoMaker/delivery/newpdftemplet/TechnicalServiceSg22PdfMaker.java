package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

@Slf4j
@Service
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.TECHNICAL_SERVICE_SG_22)
public class TechnicalServiceSg22PdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException, TException {
        log.info("#TechnicalServiceSg22PdfMaker, customerId:{}, batchId:{}", originContext.getCustomerId(), originContext.getBatchId());
        List<EcontractDeliveryInfoBo> deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName());
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();

        List<EcontractDeliveryInfoBo> supportSGV2_2InfoBoList = Lists.newArrayList();

        for (EcontractDeliveryInfoBo infoBo : deliveryInfoList) {
            if (infoBo.getEcontractDeliverySG2_2InfoBo() != null) {
                pdfMetaContent.putAll(MapUtil.Object2Map(infoBo.getEcontractDeliverySG2_2InfoBo()));
                infoBo.setPoiFeeUrl(infoBo.getEcontractDeliverySG2_2InfoBo().getPoiFeeUrl());
            }
            supportSGV2_2InfoBoList.add(infoBo);
        }
        assembleData(supportSGV2_2InfoBoList, pdfBizContent, pdfMetaContent, "SGV2_2");


        //是否展示特批日期
        boolean hasSpecialExpireTime = deliveryInfoList.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getSpcialFeeExpirationTime()));
        pdfMetaContent.put("hasSpecialExpireTime", Boolean.toString(hasSpecialExpireTime));
        boolean hasSpecialFeeInfo = deliveryInfoList.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getSpcialFeeInfo()));
        pdfMetaContent.put("hasSpecialFeeInfo", Boolean.toString(hasSpecialFeeInfo));

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("signTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        //基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("TECHNICAL_SERVICE_SG_22_TEMPLATE_ID", 602));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("TECHNICAL_SERVICE_SG_22_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        log.info("#TechnicalServiceSg22PdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }


    /**
     * @param infoBoList 业务数据
     * @param pdfBizContent pdf可遍历数据
     * @param pdfMetaContent pdf非遍历数据
     * @param template 所属模板
     */
    private void assembleData(List<EcontractDeliveryInfoBo> infoBoList, List<Map<String, String>> pdfBizContent, Map<String, String> pdfMetaContent, String template) throws IllegalAccessException {
        if (CollectionUtils.isEmpty(infoBoList)) {
            return;
        }

        Multimap<Integer, Map<String, String>> multimap = ArrayListMultimap.create();
        boolean hasSupport = false;
        boolean hasSLASupport = false;
        for (EcontractDeliveryInfoBo temp : infoBoList) {
            temp.setDeliveryArea(null);
            temp.setEcontractDeliveryWholeCityInfoBo(null);
            temp.setEcontractDeliveryAggregationInfoBo(null);
            multimap.put(Integer.valueOf(temp.getChargingDesc()), MapUtil.Object2Map(temp));
            if (SUPPORT_MARK.equals(temp.getSupportExclusive())) {
                hasSupport = true;
            }
            if (SUPPORT_MARK.equals(temp.getSupportSLA())) {
                hasSLASupport = true;
            }
        }
        List<Integer> sortKeys = Lists.newArrayList(multimap.keySet());
        Collections.sort(sortKeys);
        int count = 1;
        List<Map<String, String>> toModifyList = Lists.newArrayList();
        for (Integer temp : sortKeys) {
            toModifyList = (List<Map<String, String>>) multimap.get(temp);
            for (Map<String, String> mapTemp : toModifyList) {
                mapTemp.put("chargingDesc", count + "");
            }
            pdfBizContent.addAll(toModifyList);
            count++;
        }
        if (hasSupport) {
            pdfMetaContent.put(template + "_hasSupport", "hasSupport");
        }
        if (hasSLASupport) {
            pdfMetaContent.put(template + "_hasSLASupport", "hasSupport");
        }
        pdfMetaContent.put(template + "_sortKeys", Joiner.on(",").join(sortKeys));
    }
}