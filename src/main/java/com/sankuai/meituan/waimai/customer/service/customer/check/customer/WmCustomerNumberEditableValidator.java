package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.service.customer.UpmAuthCheckService;
import com.sankuai.meituan.waimai.customer.service.customer.check.BaseWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;


import java.util.Objects;

/**
 * 客户资质编号可编辑校验
 */
@Service
public class WmCustomerNumberEditableValidator extends BaseWmCustomerValidator implements IWmCustomerValidator {

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    /**
     * 客户资质编辑角色编码
     */
    private static final String CUSTOMER_CERT_EDIT_ROLE = "customerCertEditRole";
    
    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        if (wmCustomerBasicBo.getId() <= 0) {
            return checkPass(validateResultBo);
        }

        WmCustomerDB wmCustomerDB = getExistWmCustomer(wmCustomerBasicBo.getId());
        
        // 渠道为空或渠道 为外卖BD上单则需要执行资质编号修改校验
        if (wmCustomerBasicBo.getCustomerSource() == null
                || wmCustomerBasicBo.getCustomerSource() == CustomerSource.WAIMAI_BD) {
            return checkQuaEditAuthOnBD(validateResultBo, wmCustomerDB, wmCustomerBasicBo, opUid);
        }
        
        //已生效数据不能修改主体证件编号
        if (wmCustomerDB.getEffective() == CustomerConstants.EFFECT) {
            if (!Objects.equals(wmCustomerDB.getCustomerType(), wmCustomerBasicBo.getCustomerType())) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "客户资质主体、注册号、证件编号不可修改");
            }
            if (!Objects.equals(wmCustomerDB.getCustomerNumber(), wmCustomerBasicBo.getCustomerNumber())) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "客户资质主体、注册号、证件编号不可修改");
            }
        }
        return checkPass(validateResultBo);
    }

    /**
     * BD上单渠道或渠道参数为空则执行此校验
     * 
     * @param wmCustomerDB
     * @param wmCustomerBasicBo
     * @param opUid
     * @return
     */
    public ValidateResultBo checkQuaEditAuthOnBD(ValidateResultBo validateResultBo, WmCustomerDB wmCustomerDB,
            WmCustomerBasicBo wmCustomerBasicBo, Integer opUid) {
        if (!MccCustomerConfig.getCheckCustomerQuaEditAuthOnBDSource()) {
            return checkPass(validateResultBo);
        }
        
        // 客户未生效不校验资质修改
        if (wmCustomerDB.getEffective() != CustomerConstants.EFFECT) {
            return checkPass(validateResultBo);
        }
        //资质编号未修改不校验
        if(Objects.equals(wmCustomerDB.getCustomerNumber(), wmCustomerBasicBo.getCustomerNumber())){
            return checkPass(validateResultBo);
        }
        //外卖单店客户资质无论是否有权限均不允许修改
        if(wmCustomerDB.getCustomerRealType()!= null 
                && wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()){
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "客户资质主体、注册号、证件编号不可修改");
        }
        
        // 非外卖单店客户类型，无修改客户资质编号不允许修改
        if (!upmAuthCheckService.checkUpmRoleById(opUid,
                        MccConfig.getCustomerAuthRoleCode(CUSTOMER_CERT_EDIT_ROLE))) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "客户资质主体、注册号、证件编号不可修改");
        }

        return checkPass(validateResultBo);
    }
}
