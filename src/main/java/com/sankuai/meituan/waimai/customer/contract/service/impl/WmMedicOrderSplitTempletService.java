package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.adapter.BmContractServiceAdapter;
import com.sankuai.meituan.waimai.customer.bo.sign.BmDeliveryContractNoticeBO;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerRelDB;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EstampSubjectInfoEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.contractdocparam.EcontractMedicOrderSplitBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

/**
 * -@author: huangjianlou
 * -@description: 医药分单补充协议，方法实现
 * -@date: 2022/12/22 9:36 PM
 */
@Slf4j
@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.MEDIC_ORDER_SPLIT_E})
public class WmMedicOrderSplitTempletService extends AbstractWmEContractTempletService {

    @Resource
    private BmContractServiceAdapter bmContractServiceAdapter;

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB contractVersion)
            throws WmCustomerException {
        log.info("#WmMedicOrderSplitTempletService#buildEcontractTaskApplyBo,contractBo={},contractVersion={}",
                JSON.toJSONString(contractBo), JSON.toJSONString(contractVersion));
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(contractVersion.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.MEDIC_ORDER_SPLIT);

        //获取动态字段数据源&封装pdf动态字段
        try {
            EcontractMedicOrderSplitBo econtractMedicOrderSplitBo = new EcontractMedicOrderSplitBo();
            int wmCustomerId = contractBo.getBasicBo().getParentId();
            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerById(wmCustomerId);
            if (wmCustomerBasicBo == null) {
                log.info("医药分单补充协议封装pdf动态字段失败，无有效客户:{}", JSON.toJSONString(contractBo));
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待合同签约异常");
            }
            econtractMedicOrderSplitBo.setContractNum(contractBo.getBasicBo().getContractNum());
            String signDate = DateUtil.secondsToString(DateUtil.unixTime());
            econtractMedicOrderSplitBo.setSignDate(signDate);
            econtractMedicOrderSplitBo.setPartAName(wmCustomerBasicBo.getCustomerName());
            econtractMedicOrderSplitBo.setPartBName(EstampSubjectInfoEnum.SZBSK.getCustomerName());
            econtractMedicOrderSplitBo.setPartAOfficialSeal(wmCustomerBasicBo.getCustomerName());
            econtractMedicOrderSplitBo.setPartBOfficialSeal(EstampSubjectInfoEnum.SZBSK.getCustomerName());

            applyBo.setConfigBo(new EcontractTaskConfigBo());
            applyBo.setApplyInfoBo(JSON.toJSONString(econtractMedicOrderSplitBo));
            log.info("#WmMedicOrderSplitTempletService#applyBo,applyBo={}", JSON.toJSONString(applyBo));
            return applyBo;
        } catch (Exception e) {
            log.error("医药分单补充协议封装pdf动态字段失败:{}", JSON.toJSONString(contractBo), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
        }
    }

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName)
        throws WmCustomerException, TException {
        log.info("开始签署医药分单合同:contractBo={},opUid={},opName={}", JSON.toJSONString(contractBo), opUid, opName);
        // 如果是发起待打包
        if (SignPackWay.WAIT_HAND_PACK.getCode() == contractBo.getPackWay()) {
            try {
                // 前置校验
                ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
                // 获取当前数据
                WmCustomerContractBo oldBo = wmContractService
                    .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
                // 更新or插入
                Integer contractId = insertOrUpdate(contractBo, opUid, opName);
                // 记录变更状态
                int status = MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus();
                if (status != contractBo.getBasicBo().getStatus()) {
                    contractLogService.logStatusChange(
                        MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                        contractBo.getBasicBo(), opUid, opName);
                }
                contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
                // 正式发起待签约任务
                toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
                contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
                contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(),
                        new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
                ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(),
                    contractId,
                    opUid);
                wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
                return contractId;
            } catch (WmCustomerException ec) {
                log.error("【框架合同】发起待打包合同任务失败 contractBo:{} msg:{}", JSON.toJSONString(contractBo), ec.getMsg());
                throw ec;
            } catch (Exception e) {
                log.error("【框架合同】发起待打包合同任务失败 contractBo:{}", JSON.toJSONString(contractBo), e);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
            }
        } else {
            return super.startSign(contractBo, opUid, opName);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException,
            TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String medicOrderSplitEnum = ContractNumberUtil.genMedicOrderSplitStatementENum(insertId);
        contractBo.getBasicBo().setContractNum(medicOrderSplitEnum);
        log.info("医药分单生成合同编号  contractId:{}  contractNumber:{}", insertId, medicOrderSplitEnum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), medicOrderSplitEnum);
        return insertId;
    }


    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        try {
            log.info("医药分单生效:contractId={},opUid={},opUname={}", contractId, opUid, opUname);
            WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
            wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
            wmCustomerContractBo.getBasicBo().setTempletContractId(contractId);
            // 合同废除校验
            ContractCheckFilter.medicOrderSplitAbolishFilter().filter(wmCustomerContractBo, opUid, opUname);
            WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
            if (Objects.nonNull(offlineContract)) {
                WmCustomerRelDB wmCustomerRelDB =
                        wmCustomerRelMapper.selectByWmCustomerIdAndBizType(offlineContract.getParentId(),
                                CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
                if (Objects.nonNull(wmCustomerRelDB)) {
                    BmDeliveryContractNoticeBO bmDeliveryContractNoticeBO = BmDeliveryContractNoticeBO.builder()
                            .psCustomerId(wmCustomerRelDB.getCustomer_biz_id()).build();
                    bmContractServiceAdapter.notifyMedicOrderSplitSignAbolish(bmDeliveryContractNoticeBO);
                } else {
                    log.warn("当前企客尚未签署:customerId={}", offlineContract.getParentId());
                }
            }
        } catch (Exception ex) {
            log.error("废除合同回调配送异常", ex);
        }
        return super.invalid(contractId, opUid, opUname);
    }

    /**
     * 直接生效的场景
     *
     * @param templetContractId 合同id
     * @param opUid             操作人id
     * @param opUname           操作名称
     * @return 返回
     * @throws WmCustomerException 异常
     * @throws TException          异常
     */
    @Override
    public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
        // 单独具体合同中实现业务单独逻辑
        try {
            log.info("#effect#医药分单直接生效:templetContractId={},opUid={},opUname={}", templetContractId, opUid, opUname);
            WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
            wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
            wmCustomerContractBo.getBasicBo().setTempletContractId(templetContractId);
            WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey(templetContractId);
            if (Objects.nonNull(offlineContract)) {
                WmCustomerRelDB wmCustomerRel =
                        wmCustomerRelMapper.selectByWmCustomerIdAndBizType(offlineContract.getParentId(),
                                CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
                if (Objects.nonNull(wmCustomerRel)) {
                    BmDeliveryContractNoticeBO bmDeliveryContractNoticeBO = BmDeliveryContractNoticeBO.builder()
                            .psCustomerId(wmCustomerRel.getCustomer_biz_id()).signSuccessTime(TimeUtil.unixtime())
                            .build();
                    bmContractServiceAdapter.notifyMedicOrderSplitSignSuccess(bmDeliveryContractNoticeBO);
                } else {
                    log.warn("当前企客尚未签署合同:customerId={}", offlineContract.getParentId());
                }
            }
        } catch (Exception ex) {
            log.error("医药分单直接生效合同回调配送异常", ex);
        }
        return super.effect(templetContractId, opUid, opUname);
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, long contractId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(wmCustomerId)
                .applyTypeEnum(EcontractTaskApplyTypeEnum.MEDIC_ORDER_SPLIT)
                .bizId(contractId)
                .build();
    }
}
