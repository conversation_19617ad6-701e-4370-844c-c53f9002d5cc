package com.sankuai.meituan.waimai.customer.util.trans;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementModelDic;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementSignLogDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementModelDicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementSignLogBo;
import java.util.List;

public class WmAgreementTransUtil {

    public static WmAgreementModelDic transToWmAgreementModelDic(
            WmAgreementModelDicBo wmAgreementModelDicBo) {
        if(wmAgreementModelDicBo == null){
            return null;
        }
        WmAgreementModelDic dic = new WmAgreementModelDic();
        dic.setId(wmAgreementModelDicBo.getId() == 0 ? null : wmAgreementModelDicBo.getId());
        dic.setAgreeModelName(wmAgreementModelDicBo.getAgreeModelName());
        dic.setCurrentVersion(wmAgreementModelDicBo.getCurrentVersion());
        return dic;
    }

    public static List<WmAgreementSignLogDB> transToWmAgreementSignLogDBList(List<WmAgreementSignLogBo> signLogBoList){
        List<WmAgreementSignLogDB> result = Lists.newArrayList();
        for(WmAgreementSignLogBo temp : signLogBoList){
            result.add(transToWmAgreementSignLogDB(temp));
        }
        return result;
    }

    public static WmAgreementSignLogDB transToWmAgreementSignLogDB(WmAgreementSignLogBo logBo){
        if(logBo == null){
            return null;
        }
        WmAgreementSignLogDB logDB = new WmAgreementSignLogDB();
        logDB.setWmPoiId(logBo.getWmPoiId());
        logDB.setWmAgreeModelId(logBo.getWmAgreeModelId());
        logDB.setWmAgreeModelType(logBo.getWmAgreeModelType());
        logDB.setSignTime(logBo.getSignTime());
        logDB.setSignResult(logBo.getSignResult());
        logDB.setSignAccount(logBo.getSignAccount());
        logDB.setSignIp(logBo.getSignIp());
        logDB.setSignDevice(logBo.getSignDevice());
        return logDB;
    }
}
