package com.sankuai.meituan.waimai.customer.service.sign.applytask.context;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class ManualPackNoticeContext {
    private Map<String,List<Long>> taskInfo;
    private List<Long> c1ContractTaskInfo;
    private List<Long> settleTaskInfo;
    private List<Long> deliveryTaskInfo;
    private List<Long> droneTaskInfo;
    private Map<Long,Long> deliveryTaskWmPoiIdMap;
    private Map<Long,Long> droneTaskWmPoiIdMap;
    private Map<Long,Long> fruitTogetherTaskWmPoiIdMap;
    private Map<Long,Long> vipCardTaskWmPoiIdMap;
    private Map<Long, Long> nationalSubsidyDistributorWmPoiIdMap;
    private Map<Long, Long> nationalSubsidyHeadquartersWmPoiIdMap;
    private Map<Long,Long> depositTaskWmPoiIdMap;
    private List<Long> allTaskInfo;
    private Integer customerId;
    private int customerRelWmPoiIdCount;
    private int commitUid;
    private long manualBatchId;
    //是否仅存在配送任务
    private boolean onlyHaveDeliveryTask;
    //一次打包签约中，所有的子任务模块信息，值同EcontractTaskApplyTypeEnum.name
    private String taskMoudleInfo;
    //签约打包任务id
    private long signPackId;
    //打包任务来源
    private String source;
    /**
     * 是否到餐合同
     */
    private List<DcContractContext> dcContractContextList;
}
