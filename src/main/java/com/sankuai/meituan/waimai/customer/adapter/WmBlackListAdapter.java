package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.sdk.rule.SceneRuleClient;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.domain.rule.SceneRuleResultDto;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/29
 * @description 黑名单系统防腐层
 */

@Service
@Slf4j
public class WmBlackListAdapter {

    @Autowired
    private SceneRuleClient sceneRuleClient;

    /**
     * 根据不同对象的资质进行黑名单校验
     *
     * @param sceneCode, context
     * @return SceneRuleResultDto
     * @description 调用方请对返回值null情况进行兜底
     */
    public SceneRuleResultDto checkBlackList(String sceneCode, Map<String, Object> context) throws WmCustomerException {
        log.info("黑名单资质校验类型{}, 校验数据{}", sceneCode, JSONObject.toJSONString(context));
        SceneRuleResultDto sceneRuleResultDto;
        try {
            sceneRuleResultDto = sceneRuleClient.execute(sceneCode, context);
            log.info("黑名单系统资质校验结果{}", JSONObject.toJSONString(sceneRuleResultDto));
        } catch (Exception e) {
            log.warn("黑名单系统资质校验异常:类型{},上下文{}", sceneCode, JSONObject.toJSONString(context), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "黑名单系统校验异常, 请重试！" + e.getMessage());
        }
        if (sceneRuleResultDto == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "黑名单系统校验返回值异常，请重试！");
        }
        return sceneRuleResultDto;
    }
}
