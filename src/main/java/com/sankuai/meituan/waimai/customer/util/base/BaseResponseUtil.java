package com.sankuai.meituan.waimai.customer.util.base;

import com.sankuai.meituan.waimai.customer.constant.common.StatusCodeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;

/**
 * -@author: h<PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2022/11/18 4:38 PM
 */
public class BaseResponseUtil {

    private BaseResponseUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static <T extends BaseResponse<D>, D> T success(D data, Class<T> destinationClass) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("code", StatusCodeEnum.SUCCESS.getCode());
        map.put("data", data);
        map.put("msg", StatusCodeEnum.SUCCESS.getErrorMsg());
        return  transfer(map, destinationClass);
    }

    public static <T> T fail(int errCode, String errMsg, Class<T> destinationClass) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("code", errCode);
        map.put("msg", errMsg);
        return transfer(map, destinationClass);
    }

    public static <T> T fail(StatusCodeEnum statusCodeEnum, Class<T> destinationClass) {
        return fail(statusCodeEnum.getCode(), statusCodeEnum.getErrorMsg(), destinationClass);
    }

    private static <T> T transfer(Map<String, Object> srcMap, Class<T> destinationClass) {
        try {
            T newInstance = destinationClass.newInstance();
            BeanUtils.populate(newInstance, srcMap);
            return newInstance;
        } catch (Exception var3) {
            throw new RuntimeException("内部错误: 构造返回结果异常:" + destinationClass, var3);
        }
    }

}
