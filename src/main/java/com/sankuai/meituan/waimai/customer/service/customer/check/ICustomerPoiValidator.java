package com.sankuai.meituan.waimai.customer.service.customer.check;

import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindDecideResultEnum;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiCheckBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

import java.util.List;
import java.util.Map;

public interface ICustomerPoiValidator {

   boolean canCheck(int customerRealType);


   Map<CustomerPoiBindDecideResultEnum,List<Long>> validBind(WmCustomerPoiCheckBo wmCustomerPoiCheckBo) throws WmCustomerException, TException;
}
