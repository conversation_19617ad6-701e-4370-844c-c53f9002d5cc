package com.sankuai.meituan.waimai.customer.service.companycustomer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerQikeOperateTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmBusinessCustomerTempletService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiRelExtensionMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerRelMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiRelExtension;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerRelDB;
import com.sankuai.meituan.waimai.customer.mq.domain.WmCustomerQiKeDelayBo;
import com.sankuai.meituan.waimai.customer.mq.domain.WmCustomerQikePoiBo;
import com.sankuai.meituan.waimai.customer.mq.service.CustomerQikeSendService;
import com.sankuai.meituan.waimai.customer.util.ExecutorUtil;
import com.sankuai.meituan.waimai.customer.util.TairLocker;
import com.sankuai.meituan.waimai.poibizflow.constant.datatransfer.WmDataSubModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.base.LongResult;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.WmSupplyChainDataQueryThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.WmSupplyChainDataSyncThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.bo.customer.WmCustomerDataBo;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.bo.poi.WmPoiBaseBo;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.bo.poi.WmPoiDataBo;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.company.CompanyCustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class CompanyCustomerPoiSyncService {

    private static final LongResult NOT_NEED_PROCESS_RESULT = new LongResult(-1L);

    @Resource(name = "customerTairLocker")
    private TairLocker tairLocker;

    private static final String poiSycLock = "CompanyCustomerSync_{wmPoiId}_{biztype}";

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmCustomerRelMapper wmCustomerRelMapper;

    @Autowired
    private WmCustomerPoiRelExtensionMapper wmCustomerPoiRelExtensionMapper;

    @Autowired
    private CustomerQikeSendService customerQikeSendService;


    @Autowired
    private WmSupplyChainDataSyncThriftService wmSupplyChainDataSyncThriftService;

    @Autowired
    private WmSupplyChainDataQueryThriftService wmSupplyChainDataQueryThriftService;

    @Autowired
    private WmBusinessCustomerTempletService wmBusinessCustomerTempletService;


    public boolean isGray(long wmCustomerId) {
        int grayPercent = MccCustomerConfig.getQiKeCreateCompanyCustomerPercent();
        boolean isGray = (wmCustomerId % 100) <= grayPercent;
        return isGray;
    }

    /**
     * 发送门店入驻企客通知
     *
     * @param wmCustomerId
     * @param bmCompanyCustomerId
     * @param wmPoiIdList
     */
    public void sendCustomerPoiCreate(long wmCustomerId, long bmCompanyCustomerId, List<Long> wmPoiIdList) {
        log.info("sendCustomerPoiCreate wmCustomerId={},bmCompanyCustomerId={},wmPoiIdList={}", wmCustomerId, bmCompanyCustomerId, JSONObject.toJSONString(wmPoiIdList));
        for (Long wmPoiId : wmPoiIdList) {
            customerQikeSendService.sendQikePoiCreate(new WmCustomerQikePoiBo(wmCustomerId, bmCompanyCustomerId, wmPoiId));
        }
    }

    /**
     * 企客门店入驻及绑定
     *
     * @param poiBo
     */
    public void customerPoiCreate(WmCustomerQikePoiBo poiBo) {
        if (poiBo == null || poiBo.getBmCompanyCustomerId() <= 0L || poiBo.getWmCustomerId() <= 0L || poiBo.getWmPoiId() == null || poiBo.getWmPoiId().longValue() <= 0L) {
            log.error("customerPoiCreate error 参数异常 poiBo={}", JSONObject.toJSONString(poiBo));
            return;
        }
        Long wmPoiId = poiBo.getWmPoiId();
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectCustomerPoiRelRT(poiBo.getWmCustomerId(), poiBo.getWmPoiId());
        if (CollectionUtils.isEmpty(wmCustomerPoiDBList)) {
            log.error("customerPoiCreate error 门店客户关系未找到 poiBo={}", JSONObject.toJSONString(poiBo));
            return;
        }
        log.info("customerPoiCreate poiBo={}", JSONObject.toJSONString(poiBo));
        List<Long> existRelWmPoiIdList = wmCustomerPoiRelExtensionMapper.getExistRelWmPoiIdListMaster(Lists.newArrayList(wmPoiId), CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
        if (CollectionUtils.isEmpty(existRelWmPoiIdList)) {
            syncCreatePoiCustomer(poiBo.getWmCustomerId(), poiBo.getBmCompanyCustomerId(), wmPoiId, false);
        } else {
            //已入驻过企客的门店,触发重新绑定,配送侧保持幂等
            syncBindPoiCustomer(poiBo.getWmCustomerId(), wmPoiId);
        }
    }

    /**
     * 企客门店入驻
     *
     * @param wmCustomerId
     * @param bmCompanyCustomerId
     * @param wmPoiId
     * @param isDelay
     */
    private void syncCreatePoiCustomer(long wmCustomerId, Long bmCompanyCustomerId, Long wmPoiId, boolean isDelay) {
        log.info("syncCreatePoiCustomer wmCustomerId={},bmCompanyCustomerId={},wmPoiId={},isDelay={}", wmCustomerId, bmCompanyCustomerId, wmPoiId, isDelay);
        if (bmCompanyCustomerId == null || bmCompanyCustomerId.longValue() == 0 || wmPoiId == null || wmPoiId.longValue() <= 0L) {
            return;
        }
        LongResult poiCustomer = ExecutorUtil.execWithRetry(new ExecutorUtil.Executor<LongResult>() {
            @Override
            public LongResult exec() throws Exception {
                try {
                    WmPoiDataBo wmPoiDataBo = genWmPoiDataBo(bmCompanyCustomerId, wmPoiId);
                    if (wmPoiDataBo == null) {
                        return null;
                    }
                    WmPoiBaseBo wmPoiBaseBo = wmPoiDataBo.getWmPoiBaseBo();
                    //没有wdcId的门店无需同步
                    if (wmPoiBaseBo != null && wmPoiBaseBo.getWdcId() <= 0L) {
                        log.info("syncCreatePoiCustomer,no wdcId!wmPoiId={}", wmPoiId);
                        return NOT_NEED_PROCESS_RESULT;
                    }
                    if (isDelay) {
                        return wmSupplyChainDataSyncThriftService.createPoiCustomerDelay(wmPoiDataBo);
                    } else {
                        return wmSupplyChainDataSyncThriftService.createPoiCustomer(wmPoiDataBo);
                    }
                } catch (WmPoiBizException e) {
                    log.warn("createPoiCustomer error,bmCompanyCustomerId={},wmPoiId={}", bmCompanyCustomerId, wmPoiId, e);
                }
                return null;
            }

            @Override
            public boolean shouldRetry(LongResult result) throws Exception {
                return result == null;
            }

            @Override
            public void execComplete(LongResult result) {
                log.warn("createPoiCustomer retry error,bmCompanyCustomerId={},wmPoiId={}", bmCompanyCustomerId, wmPoiId);
            }
        }, 3, ConfigUtilAdapter.getInt("qike_create_poi_customer_retry_interval", 5));
        if (poiCustomer != null && poiCustomer.getValue() != NOT_NEED_PROCESS_RESULT.getValue()) {
            WmCustomerPoiRelExtension record = new WmCustomerPoiRelExtension();
            record.setWmPoiId(wmPoiId);
            record.setBizId(poiCustomer.getValue());
            record.setBizType(CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
            record.setValid((byte) 1);
            String lockKey = poiSycLock.replace("{wmPoiId}", wmPoiId + "").replace("{biztype}",
                    CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE + "");
            if (tairLocker.tryLockWhenExceptionReturnTrue(lockKey, 30)) {
                wmCustomerPoiRelExtensionMapper.insertSelective(record);
            }
        } else if (poiCustomer == null) {
            // 已经延迟处理了还不成功，发送告警
            if (isDelay) {
                log.error("createPoiCustomer retry error,bmCompanyCustomerId={},wmPoiId={},isDelay={}", bmCompanyCustomerId, wmPoiId, isDelay);
                HostEnv env = ProcessInfoUtil.getHostEnv();
                DaxiangUtilV2.push(String.format("[%s]企客门店入驻失败:wmPoiId:%s,companyCustomerId:%s",
                        env.name(), wmPoiId, bmCompanyCustomerId), MccCustomerConfig.getQikeAlarmList());
            } else {
                customerQikeSendService.sendQikeDelayMq(new WmCustomerQiKeDelayBo(wmCustomerId, wmPoiId, WmCustomerQikeOperateTypeEnum.BIND_POI));
            }
        }
    }

    /**
     * 企客门店绑定
     *
     * @param wmCustomerId
     * @param wmPoiId
     */
    private void syncBindPoiCustomer(long wmCustomerId, Long wmPoiId) {
        if (wmCustomerId <= 0L || wmPoiId == null || wmPoiId.longValue() <= 0L) {
            log.error("syncBindPoiCustomer 参数异常 wmCustomerId={},wmPoiId={}", wmCustomerId, wmPoiId);
            return;
        }
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmCustomerId, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
        if (wmCustomerRelDB == null) {
            return;
        }
        log.info("syncBindPoiCustomer wmCustomerId={},wmPoiId={}", wmCustomerId, wmPoiId);
        Long customerBizId = wmCustomerRelDB.getCustomer_biz_id();
        WmCustomerPoiRelExtension temp = wmCustomerPoiRelExtensionMapper.selectByWmPoiIdAndBizType(wmPoiId, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
        if (temp == null) {
            log.error("syncBindPoiCustomer error 未找到企客门店关系 wmCustomerId={},wmPoiId={}", wmCustomerId, wmPoiId);
        }
        ExecutorUtil.execWithRetry(new ExecutorUtil.Executor<Boolean>() {
            @Override
            public Boolean exec() throws Exception {
                try {
                    wmSupplyChainDataSyncThriftService.bindCustomer(customerBizId, temp.getBizId());
                    //门店绑定时，进行门店维度消息推送
                    wmSupplyChainDataSyncThriftService.forceSendChangeDelayMsg(temp.getWmPoiId().toString(), WmDataSubModuleEnum.WM_POI_BASE.getSubModuleName());
                    wmSupplyChainDataSyncThriftService.forceSendChangeDelayMsg(temp.getWmPoiId().toString(), WmDataSubModuleEnum.WM_POI_QUALIFICATION.getSubModuleName());
                    wmSupplyChainDataSyncThriftService.forceSendChangeDelayMsg(temp.getWmPoiId().toString(), WmDataSubModuleEnum.WM_POI_BRAND.getSubModuleName());
                    wmSupplyChainDataSyncThriftService.forceSendChangeDelayMsg(temp.getWmPoiId().toString(), WmDataSubModuleEnum.WM_POI_STATUS.getSubModuleName());
                } catch (WmPoiBizException | TException e) {
                    log.error("syncBindPoiCustomer error,companyCustomerId={},poiCustomerId={}", customerBizId, temp.getBizId(), e);
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            }

            @Override
            public boolean shouldRetry(Boolean result) throws Exception {
                return !Boolean.TRUE.equals(result);
            }

            @Override
            public void execComplete(Boolean result) {
                log.error("bindCustomer retry error,companyCustomerId={},poiCustomerId={}", wmCustomerRelDB.getCustomer_biz_id(), temp.getBizId());
                HostEnv env = ProcessInfoUtil.getHostEnv();
                DaxiangUtilV2.push(String.format("[%s]企客门店绑定失败:wmPoiId:%s,companyCustomerId:%s,poiCustomerId:%s",
                        env.name(), wmPoiId, wmCustomerRelDB.getCustomer_biz_id(), temp.getBizId()), MccCustomerConfig.getQikeAlarmList());
            }
        }, 3);
    }

    /**
     * 企客客户创建
     *
     * @param wmCustomerId
     * @param isDelay
     * @return
     */
    public Long syncCreateCompanyCustomer(long wmCustomerId, boolean isDelay) {
        if (wmCustomerId <= 0L) {
            return null;
        }
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmCustomerId, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
        if (wmCustomerRelDB != null) {
            return wmCustomerRelDB.getCustomer_biz_id();
        }
        log.info("syncCreateCompanyCustomer wmCustomerId={},isDelay={}", wmCustomerId, isDelay);
        LongResult result = ExecutorUtil.execWithRetry(new ExecutorUtil.Executor<LongResult>() {
            @Override
            public LongResult exec() throws Exception {
                try {
                    LongResult companyCustomer = null;
                    if (isDelay) {
                        companyCustomer = wmSupplyChainDataSyncThriftService.createCompanyCustomerDelay(genWmCustomerDataBo(wmCustomerId));
                    } else {
                        companyCustomer = wmSupplyChainDataSyncThriftService.createCompanyCustomer(genWmCustomerDataBo(wmCustomerId));
                    }
                    WmCustomerRelDB insertWmCustomerRelDB = new WmCustomerRelDB();
                    insertWmCustomerRelDB.setWm_customer_id(wmCustomerId);
                    insertWmCustomerRelDB.setCustomer_biz_id(companyCustomer.getValue());
                    insertWmCustomerRelDB.setBiz_type(CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
                    wmCustomerRelMapper.insertSelective(insertWmCustomerRelDB);
                    return companyCustomer;
                } catch (WmPoiBizException | TException e) {
                    log.warn("createCompanyCustomer error,wmCustomerId={}", wmCustomerId, e);
                }
                return null;
            }

            @Override
            public boolean shouldRetry(LongResult result) throws Exception {
                return result == null;
            }

            @Override
            public void execComplete(LongResult result) {
                log.warn("createCompanyCustomer retry error,wmCustomerId={}", wmCustomerId);
            }
        }, 3);
        if (result != null && result.getValue() != NOT_NEED_PROCESS_RESULT.getValue()) {
            return result.getValue();
        } else {
            // 已经延迟处理了还不成功，发送告警
            if (isDelay) {
                log.error("createCompanyCustomer retry error,wmCustomerId={} isDelay={}", wmCustomerId, isDelay);
                HostEnv env = ProcessInfoUtil.getHostEnv();
                DaxiangUtilV2.push(String.format("[%s]企客客户入驻失败:wmCustomerId:%s", env.name(), wmCustomerId), MccCustomerConfig.getQikeAlarmList());
                return null;
            } else {
                customerQikeSendService.sendQikeDelayMq(new WmCustomerQiKeDelayBo(wmCustomerId, null, WmCustomerQikeOperateTypeEnum.CREATE_CUSTOMER));
                return null;
            }
        }
    }

    /**
     * 延迟进行企客客户入驻
     *
     * @param wmCustomerId
     */
    public void syncCreateCompanyCustomerDelay(long wmCustomerId) {
        log.info("syncCreateCompanyCustomerDelay wmCustomerId={}", wmCustomerId);
        Long mtCustomerId = wmCustomerDBMapper.selectMtCustomerIdByWmCustomerId(wmCustomerId);
        if (mtCustomerId == null || mtCustomerId.longValue() <= 0L) {
            log.warn("通过wmCustomerId未获取到mtCustomerId,wmCustomerId={}", mtCustomerId);
            return;
        }
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmCustomerId, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
        if (wmCustomerRelDB != null) {
            return;
        }
        //未同步企客公司客户-进行公司客户同步
        Long bmCompanyCustomerId = syncCreateCompanyCustomer(wmCustomerId, true);
        if (bmCompanyCustomerId == null) {
            return;
        }
        List<Long> wmPoiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId((int) wmCustomerId);
        if (CollectionUtils.isEmpty(wmPoiIds)) {
            return;
        }
        // 企客门店客户同步
        sendCustomerPoiCreate(wmCustomerId, bmCompanyCustomerId, wmPoiIds);
    }

    /**
     * 延迟进行企客门店入驻
     *
     * @param wmCustomerId
     * @param wmPoiId
     */
    public void syncBindPoiCustomerDelay(long wmCustomerId, long wmPoiId) {
        log.info("syncBindPoiCustomerDelay wmCustomerId={},wmPoiId={}", wmCustomerId, wmPoiId);
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmCustomerId, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
        if (wmCustomerRelDB == null) {
            return;
        }
        Integer customerId = wmCustomerPoiDBMapper.selectCustomerIdByOneWmPoiId(wmPoiId);
        if (customerId == null || customerId != wmCustomerId) {
            return;
        }
        WmCustomerPoiRelExtension wmCustomerPoiRelExtension = wmCustomerPoiRelExtensionMapper.selectByWmPoiIdAndBizType(wmPoiId, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
        if (wmCustomerPoiRelExtension == null) {
            // 企客门店入驻
            syncCreatePoiCustomer(wmCustomerId, wmCustomerRelDB.getCustomer_biz_id(), wmPoiId, true);
        } else {
            // 通知企客门店绑定
            syncBindPoiCustomer(wmCustomerId, wmPoiId);
        }
    }


    private WmPoiDataBo genWmPoiDataBo(long bmCompanyCustomerId, long wmPoiId) {
        WmPoiDataBo wmPoiDataBo = ExecutorUtil.execWithRetry(new ExecutorUtil.Executor<WmPoiDataBo>() {
            @Override
            public WmPoiDataBo exec() throws Exception {
                try {
                    WmPoiDataBo wmPoiDataBo = wmSupplyChainDataQueryThriftService
                            .queryWmPoiDataBySubModule(wmPoiId, Sets.newHashSet(
                                    WmDataSubModuleEnum.WM_POI_BASE.getSubModuleName(),
                                    WmDataSubModuleEnum.WM_POI_QUALIFICATION.getSubModuleName(),
                                    WmDataSubModuleEnum.WM_POI_BRAND.getSubModuleName()
                            ));
                    if (wmPoiDataBo != null) {
                        wmPoiDataBo.setBmCompanyCustomerId(bmCompanyCustomerId);
                    }
                    return wmPoiDataBo;
                } catch (WmPoiBizException | TException e) {
                    log.error("queryWmPoiDataBySubModule error,wmPoiId={}", wmPoiId);
                }
                return null;
            }

            @Override
            public boolean shouldRetry(WmPoiDataBo wmPoiDataBo) throws Exception {
                return wmPoiDataBo == null;
            }
        }, 3);
        return wmPoiDataBo;
    }


    private WmCustomerDataBo genWmCustomerDataBo(long wmCustomerId) throws TException {
        try {
            return wmBusinessCustomerTempletService.genWmCustomerDataBoByWmCustomerId((int) wmCustomerId);
        } catch (WmCustomerException e) {
            log.error("genWmCustomerDataBoByWmCustomerId error,wmCustomerId={}", wmCustomerId);
        }
        return null;
    }
}
