package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck.PreCheck;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-14 21:13
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class NoticeTaskTypeFactory {

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    private static final Map<String, NoticeTask> noticeTaskMap = Maps.newHashMap();

    static {
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.C1CONTRACT.getName(), (NoticeTask) SpringBeanUtil.getBean("c1NoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.SETTLE.getName(), (NoticeTask) SpringBeanUtil.getBean("settleNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.POIFEE.getName(), (NoticeTask) SpringBeanUtil.getBean("poifeeNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.C2CONTRACT.getName(), (NoticeTask) SpringBeanUtil.getBean("c2NoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.QUA_REAL_LETTER.getName(), (NoticeTask) SpringBeanUtil.getBean("quaRealLetterNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.POI_PROMOTION_SERVICE.getName(), (NoticeTask) SpringBeanUtil.getBean("poiPromotionServiceNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT.getName(), (NoticeTask) SpringBeanUtil.getBean("businessCustomerNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.GROUP_MEAL.getName(), (NoticeTask) SpringBeanUtil.getBean("groupMealNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.MED_DEPOSIT.getName(), (NoticeTask) SpringBeanUtil.getBean("medDepositNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.FOODCITY_STATEMENT.getName(), (NoticeTask) SpringBeanUtil.getBean("foodcityStatementNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.MEDIC_ORDER_SPLIT.getName(), (NoticeTask) SpringBeanUtil.getBean("medicOrderSplitNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.SUBJECT_CHANGE_SUPPLEMENT.getName(), (NoticeTask) SpringBeanUtil.getBean("subjectChangeSupplementNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.AGENT_SQS_STANDARD.getName(), (NoticeTask) SpringBeanUtil.getBean("agentSqsStandardNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT.getName(), (NoticeTask) SpringBeanUtil.getBean("fourWheelPerfSupplementNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT.getName(), (NoticeTask) SpringBeanUtil.getBean("speedyDeliveryCooperationNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.DRONE_DELIVERY.getName(), (NoticeTask) SpringBeanUtil.getBean("droneDeliveryNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY.getName(), (NoticeTask) SpringBeanUtil.getBean("fruitTogetherDeliveryNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.VIP_CARD_CONTRACT_AGREEMENT.getName(), (NoticeTask) SpringBeanUtil.getBean("vipCardNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName(), (NoticeTask) SpringBeanUtil.getBean("commonConfigContractNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getName(), (NoticeTask) SpringBeanUtil.getBean("daoCanServiceC1ContractNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getName(), (NoticeTask) SpringBeanUtil.getBean("daoCanServiceC2ContractNoticeTask"));

        noticeTaskMap.put(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_PURCHASE.getName(), (NoticeTask) SpringBeanUtil.getBean("nationalSubsidyPurchaseNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName(), (NoticeTask) SpringBeanUtil.getBean("nationalSubsidyDistributorDeliveryNoticeTask"));
        noticeTaskMap.put(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName(), (NoticeTask) SpringBeanUtil.getBean("nationalSubsidyHeadquartersDeliveryNoticeTask"));
    }

    public NoticeTask getNoticeTaskByMoudle(String moudle) throws WmCustomerException {
        log.info("NoticeTaskTypeFactory#getNoticeTaskByMoudle, moudle: {}", moudle);
        NoticeTask noticeTask = noticeTaskMap.get(moudle);
        if (null == noticeTask) {
            ContractConfigInfo configInfo = wmFrameContractConfigService.queryContractConfigInfo(moudle);
            if (configInfo != null) {
                return (NoticeTask) SpringBeanUtil.getBean("commonConfigContractNoticeTask");
            }
            log.error("打包签约无对应模块的任务触发器，moudle:{}", moudle);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约任务触发异常!");
        }
        return noticeTask;
    }
}
