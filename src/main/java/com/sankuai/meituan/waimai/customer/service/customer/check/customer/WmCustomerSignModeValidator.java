package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;

/**
 * 签约形式校验
 */
@Service
public class WmCustomerSignModeValidator implements IWmCustomerValidator {

    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        if (CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue() == wmCustomerBasicBo.getCustomerRealType()
                && CustomerSignMode.PAPER.getCode() == wmCustomerBasicBo.getSignMode()) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "客户类型为'聚合配送商'时, 不可选择纸质签约");
        }
        if (CustomerRealTypeEnum.MEISHICHENG.getValue() == wmCustomerBasicBo.getCustomerRealType()
                && wmCustomerBasicBo.getSignMode() != null
                && CustomerSignMode.PAPER.getCode() == wmCustomerBasicBo.getSignMode() && MccCustomerConfig.openMscCustomerSignModeAndCustomerTypeValid()) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "客户类型为'美食城'时, 不可选择纸质签约");
        }
        return checkPass(validateResultBo);
    }
}
