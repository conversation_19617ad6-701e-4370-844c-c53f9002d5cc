package com.sankuai.meituan.waimai.customer.util;

import com.sankuai.meituan.waimai.customer.config.MccConfig;
import lombok.extern.slf4j.Slf4j;

import java.security.SecureRandom;
import java.util.Random;

@Slf4j
public class GrayUtil {

    /**
     * 生成随机数
     *
     * @return
     */
    public static int genRandomInt() {
        int ramInt = 1;
        try {
            SecureRandom random = new SecureRandom();
            //计算的结果为1-100的整数
            ramInt = (Math.abs(random.nextInt()) % 100) + 1;
        } catch (Exception e) {
            log.error("生成随机数异常，设置默认值为0", e);
        }
        return ramInt;
    }

    /**
     * 是否将C2合同设置为长期有效
     *
     * @return Boolean
     */
    public static boolean isC2LongEffect() {
        int random = new Random().nextInt(1000);
        return random < MccConfig.getGrayToSetC2DueDateLongEffect();
    }

}
