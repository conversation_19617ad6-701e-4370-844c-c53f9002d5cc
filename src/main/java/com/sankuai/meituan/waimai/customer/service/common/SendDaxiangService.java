package com.sankuai.meituan.waimai.customer.service.common;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 发送大象先富助手消息服务
 */
@Slf4j
@Service
public class SendDaxiangService {

    @Autowired
    private WmEmployClient wmEmployClient;

    /**
     * 客户法人/姓名变更，自动删除签约人信息通知
     *
     * @param legalPerson
     * @param after
     */
    public void kpSingerDeleteForCustomerUpdate(String legalPerson, WmCustomerDB after) {
        Integer ownerUid = after.getOwnerUid();
        if (ownerUid == null || ownerUid.intValue() <= 0) {
            log.warn("kpSingerDeleteForCustomerUpdate失败 客户责任人为空 legalPerson={},after={}", legalPerson, JSONObject.toJSONString(after));
            return;
        }
        WmEmploy wmEmploy = wmEmployClient.getEmployById(ownerUid);
        if (wmEmploy == null) {
            log.warn("kpSingerDeleteForCustomerUpdate失败 客户责任人为空 legalPerson={},after={}", legalPerson, JSONObject.toJSONString(after));
            return;
        }
        String reciver = String.format("%<EMAIL>", wmEmploy.getMisId());
        String msg = String.format("您负责的客户（%s+%s）客户信息审核已经通过，其中法定代表人由%s变更为%s，系统已自动删除%s的签约人信息，请及时补录签约人信息。", after.getCustomerName(),
                after.getMtCustomerId(), legalPerson, after.getLegalPerson(), legalPerson);
        sendDaxiang(msg, reciver);
    }



    /**
     * 客户状态变更为生效,签约人信息不符合要求删除信息通知
     * @param after
     */
    public void kpSingerDeleteForSignerCorrect(WmCustomerDB after) {
        Integer ownerUid = after.getOwnerUid();
        if (ownerUid == null || ownerUid.intValue() <= 0) {
            log.warn("kpSingerDeleteForSignerCorrect 客户责任人为空 after={}", JSONObject.toJSONString(after));
            return;
        }
        WmEmploy wmEmploy = wmEmployClient.getEmployById(ownerUid);
        if (wmEmploy == null) {
            log.warn("kpSingerDeleteForSignerCorrect 客户责任人为空 after={}", JSONObject.toJSONString(after));
            return;
        }
        String reciver = String.format("%<EMAIL>", wmEmploy.getMisId());
        String msg = String.format("您负责的客户（%s+%s），签约人信息不符合要求，需要重新录入，请及时处理", after.getMtCustomerId(), after.getCustomerName());
        sendDaxiang(msg, reciver);
    }


    private void sendDaxiang(String msg, String recivers) {
        try {
            JSONObject push = DaxiangUtilV2.push(msg, recivers);
            log.info("push result = {}", push.toString());
        } catch (Exception e) {
            log.error("sendDaxiang失败 recivers={},msg={}", recivers, msg, e);
        }
    }
}
