package com.sankuai.meituan.waimai.customer.service.sign.data;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.service.sign.context.FeeDataQueryRequest;
import com.sankuai.meituan.waimai.customer.service.sign.data.util.LogisticsFeeDataQueryUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.customer.constant.contract.CommonTaskConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import org.springframework.beans.factory.annotation.Autowired;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;

/**
 * Created by lixuepeng on 2023/5/17
 */
@Service
@Slf4j
public class EcontractDataQueryInvoker {

    @Autowired
    private List<EcontractDataQueryHandler> econtractDataQueryHandlers;

    private ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(10, 300, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(100),
                    Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy()));

    private Map<EcontractDataSourceEnum, EcontractDataQueryHandler> econtractDataQueryHandlerMap = new HashMap<>();

    @PostConstruct
    private void init() {
        log.info("init EcontractDataQueryInvoker start");
        for (EcontractDataQueryHandler handler : econtractDataQueryHandlers) {
            econtractDataQueryHandlerMap.put(handler.sorceEnum(), handler);
        }
        log.info("init EcontractDataQueryInvoker end");
    }

    // 获取各来源数据（只关注外卖侧维护的数据）-> 组装签约入参对象
    public EcontractTaskApplyBo assemblyApplyBo(EcontractTaskApplyBo originApplyBo) throws WmCustomerException {
        EcontractTaskApplyBo finalApplyBo = new EcontractTaskApplyBo();
        finalApplyBo.setBizId(originApplyBo.getBizId());
        finalApplyBo.setBizTypeEnum(originApplyBo.getBizTypeEnum());
        finalApplyBo.setApplyTypeEnum(originApplyBo.getApplyTypeEnum());
        finalApplyBo.setApplyInfoBo(initApplyInfoBo(originApplyBo.getApplyTypeEnum(), originApplyBo.getDataSourceBoList(), originApplyBo.getManualBatchId()));
        finalApplyBo.setConfigBo(new EcontractTaskConfigBo());
        finalApplyBo.setCommitUid(originApplyBo.getCommitUid());
        finalApplyBo.setManualBatchId(originApplyBo.getManualBatchId());
        finalApplyBo.setWmCustomerId(originApplyBo.getWmCustomerId());
        finalApplyBo.setTag(originApplyBo.getTag());
        finalApplyBo.setPhfTransferContextList(originApplyBo.getPhfTransferContextList());
        finalApplyBo.setDataSourceBoList(originApplyBo.getDataSourceBoList());

        return finalApplyBo;
    }

    private String initApplyInfoBo(EcontractTaskApplyTypeEnum applyTypeEnum, List<EcontractDataSourceBo> dataSourceBoList, long manualBatchId) throws WmCustomerException {
        // 配送单店签约
        if (EcontractTaskApplyTypeEnum.POIFEE == applyTypeEnum) {
            Map<Long, EcontractDeliveryInfoBo> resultMap = new HashMap<>();
            Map<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> dataSourceEnumMap = dataSourceBoList.stream()
                    .collect(Collectors.toMap(EcontractDataSourceBo::getSorceEnum, EcontractDataSourceBo::getWmPoiIdAndBizIdList));
            for (EcontractDataSourceBo dataSourceBo : dataSourceBoList) {
                // 先判空，因为不是所有的数据来源都需要在customer里处理
                if (econtractDataQueryHandlerMap.get(dataSourceBo.getSorceEnum()) != null) {
                    resultMap.putAll(econtractDataQueryHandlerMap.get(dataSourceBo.getSorceEnum()).queryData(dataSourceEnumMap, manualBatchId));
                }
            }
            EcontractDeliveryInfoBo deliveryInfoBo = resultMap.values().stream().findFirst().get();
            return JSON.toJSONString(deliveryInfoBo);
        }
        // 配送多店签约
        if (CommonTaskConstant.BATCH_APPLY_TYPE_LIST.contains(applyTypeEnum.getType())) {
            Map<Long, EcontractDeliveryInfoBo> resultMap = new HashMap<>();
            Map<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> dataSourceEnumMap = dataSourceBoList.stream()
                    .collect(Collectors.toMap(EcontractDataSourceBo::getSorceEnum, EcontractDataSourceBo::getWmPoiIdAndBizIdList));
            for (EcontractDataSourceBo dataSourceBo : dataSourceBoList) {
                // 先判空，因为不是所有的数据来源都需要在customer里处理
                if (econtractDataQueryHandlerMap.get(dataSourceBo.getSorceEnum()) != null) {
                    resultMap.putAll(econtractDataQueryHandlerMap.get(dataSourceBo.getSorceEnum()).queryData(dataSourceEnumMap, manualBatchId));
                }
            }

            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
            batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(new ArrayList<>(resultMap.values()));
            batchDeliveryInfoBo.setWmPoiIdList(new ArrayList<>(resultMap.keySet()));
            return JSON.toJSONString(batchDeliveryInfoBo);
        }

        // 国补相关配送任务
        if (EcontractTaskApplyTypeEnum.isNationalSubsidyDeliveryTaskType(applyTypeEnum)) {
            Map<Long, EcontractDeliveryInfoBo> resultMap = new HashMap<>();
            Map<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> dataSourceEnumMap = dataSourceBoList.stream()
                    .collect(Collectors.toMap(EcontractDataSourceBo::getSorceEnum, EcontractDataSourceBo::getWmPoiIdAndBizIdList));
            FeeDataQueryRequest request = FeeDataQueryRequest.builder()
                    .applyTypeEnum(applyTypeEnum)
                    .map(dataSourceEnumMap)
                    .manualBatchId(manualBatchId)
                    .build();
            for (EcontractDataSourceBo dataSourceBo : dataSourceBoList) {
                // 先判空，因为不是所有的数据来源都需要在customer里处理
                if (econtractDataQueryHandlerMap.get(dataSourceBo.getSorceEnum()) != null) {
                    resultMap.putAll(econtractDataQueryHandlerMap.get(dataSourceBo.getSorceEnum()).queryData(request));
                }
            }

            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
            batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(new ArrayList<>(resultMap.values()));
            batchDeliveryInfoBo.setWmPoiIdList(new ArrayList<>(resultMap.keySet()));
            return JSON.toJSONString(batchDeliveryInfoBo);
        }

        if (EcontractTaskApplyTypeEnum.PHF_DELIVERY == applyTypeEnum) {
            Map<Long, EcontractDeliveryInfoBo> resultMap = new HashMap<>();
            Map<EcontractDataSourceEnum, EcontractDataSourceBo> dataSourceEnumMap = null;
            try {
                dataSourceEnumMap = dataSourceBoList.stream()
                        .collect(Collectors.toMap(EcontractDataSourceBo::getSorceEnum, Function.identity()));

            } catch (IllegalStateException e) {
                log.error("包含重复的DataSourceEnum, dataSourceList:{}", JSONObject.toJSONString(dataSourceBoList));
                throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "包含重复的DataSourceEnum");
            }

            // datasource的顺序，可能会存在字段覆盖的问题
            for (EcontractDataSourceBo dataSourceBo : dataSourceBoList) {
                if (econtractDataQueryHandlerMap.get(dataSourceBo.getSorceEnum()) != null) {
                    econtractDataQueryHandlerMap.get(dataSourceBo.getSorceEnum()).queryAndAssembleData(dataSourceEnumMap, manualBatchId, resultMap);
                }
            }
            Map<Integer, List<List<Long>>> wmPoiIdGroupMap = LogisticsFeeDataQueryUtil.extractGroupMap(dataSourceBoList);

            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
            batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(new ArrayList<>(resultMap.values()));
            batchDeliveryInfoBo.setWmPoiIdList(new ArrayList<>(resultMap.keySet()));
            batchDeliveryInfoBo.setWmPoiIdGroupMap(wmPoiIdGroupMap);
            return JSON.toJSONString(batchDeliveryInfoBo);
        }

        return null;
    }
}
