package com.sankuai.meituan.waimai.customer.service.common;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.domain.CustomerBlackWhiteListDBParam;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBlackWhiteListService;
import com.sankuai.meituan.waimai.customer.tair.WmCustomerSettleKvLocalService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class WmCustomerTairService {


  private static final Logger LOGGER=LoggerFactory.getLogger(WmCustomerTairService.class);

  @Autowired
  private WmCustomerSettleKvLocalService wmCustomerSettleKvLocalService;

  @Autowired
  private WmCustomerBlackWhiteListService wmCustomerBlackWhiteListService;

  @Autowired
  private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

  @Autowired
  private  WmPoiClient wmPoiClient;

  public void checkSettleWhiteList(List<Integer> wmPoiIdList) throws WmCustomerException {
//    String whiteListStr = wmCustomerSettleKvService.getSettleWhiteList();
//    if(StringUtils.isEmpty(whiteListStr)){
//      return;
//    }
//    String[] whiteArray = whiteListStr.split(",");
//    Set<String> whiteSet = Sets.newHashSet(whiteArray);
    for(Integer temp : wmPoiIdList){
      if(!wmCustomerSettleKvLocalService.isWmPoiIdInSettleWhiteList(temp.toString())){
        throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "提交失败。门店id:"+temp+"，不可选择结算周期1天，请先申请特批");
      }
    }
  }

  public void checkSettleWhiteList(WmSettle wmSettle) throws WmCustomerException {
//    String whiteListStr = wmCustomerSettleKvService.getSettleWhiteList();
//    if(StringUtils.isEmpty(whiteListStr)){
//      return;
//    }
//    String[] whiteArray = whiteListStr.split(",");
//    Set<String> whiteSet = Sets.newHashSet(whiteArray);

    List<Integer> wmPoiIdList=wmSettle.getWmPoiIdList();

    Long customerId= Long.valueOf(wmSettle.getWmCustomerId());

   //校验客户
    CustomerBlackWhiteParam customerBlackWhiteParam = new CustomerBlackWhiteParam();
    customerBlackWhiteParam.setBizId(customerId)
            .setBizType(CustomerConstants.WHITE_LIST_BIZTYPE_SETTLE_CUSTOMER)
            .setType(CustomerConstants.TYPE_WHITE);
    BooleanResult booleanCustomerResult =wmCustomerBlackWhiteListService.queryInCustomerBlackWhiteList
            (customerBlackWhiteParam);
    LOGGER.info("checkSettleWhiteList 客户id={},结果={}",customerId,JSON.toJSONString(booleanCustomerResult));
    if(booleanCustomerResult.isRes()){
      return;
    }


    List<CustomerBlackWhiteListDBParam> brandBlackWhiteListDBParamList =wmCustomerBlackWhiteListService
            .queryCustomerBlackWhiteListDB(CustomerConstants.WHITE_LIST_BIZTYPE_SETTLE_BRAND,CustomerConstants.TYPE_WHITE);

    Set<Long> brandIdSet=Sets.newHashSet();
    for(CustomerBlackWhiteListDBParam customerBlackWhiteListDBParam:brandBlackWhiteListDBParamList){
      brandIdSet.add(customerBlackWhiteListDBParam.getBizId());
    }

    Set<Long> longWmPoiIdSet=Sets.newHashSet();
    for(Integer wmPoiId:wmPoiIdList){
      longWmPoiIdSet.add(Long.valueOf(wmPoiId));
    }

    List<WmPoiDomain> wmPoiDomainList=wmPoiClient.pageGetWmPoiByWmPoiIdList(Lists.newArrayList(longWmPoiIdSet),Sets
            .newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_BRAND_ID));

    Map<Integer,Long> wmPoiAggreeMap= Maps.newHashMap();
    for(WmPoiDomain wmPoiDomain:wmPoiDomainList){
      wmPoiAggreeMap.put(wmPoiDomain.getWmPoiId(),wmPoiDomain.getBrandId());
    }

    //校验门店和品牌
    for(Integer temp : wmPoiIdList){
      //校验门店
      if(wmCustomerSettleKvLocalService.isWmPoiIdInSettleWhiteList(temp.toString())){
        continue;
      }

      //校验品牌
     Long brandId=wmPoiAggreeMap.get(temp);
      if(brandId!=null){
        LOGGER.info("checkSettleWhiteList 品牌id={} ",brandId);
        if(brandIdSet.contains(brandId)){
          continue;
        }
      }

      throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "提交失败。门店id:"+temp+"，不可选择结算周期1天，请先申请特批");
    }

  }


  /**
   * 门店信息
   *
   * @param wmPoiId
   * @return
   * @throws WmServerException
   * @throws TException
   */
  private WmPoiAggre getWmPoiAggre(Long wmPoiId)  {
    LOGGER.info("getWmPoiAggre wmPoiId={}",wmPoiId);
    WmPoiAggre  wmPoiAggre = null;
    try {

      wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(
              wmPoiId, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,WmPoiFieldQueryConstant
                      .WM_POI_FIELD_BRAND_ID));

      LOGGER.info("getWmPoiAggre 结果 {}",JSON.toJSONString(wmPoiAggre));
    } catch (Exception e) {
      LOGGER.error("getWmPoiAggre出错了",e);
    }

   return wmPoiAggre;
  }
}
