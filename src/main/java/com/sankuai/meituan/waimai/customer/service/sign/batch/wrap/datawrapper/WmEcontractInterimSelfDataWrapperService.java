package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;
import java.util.Map;

import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractInterimSelfInfoBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

/**
 * 配送产品临时调整补充协议数据打包
 */
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.INTERIM_SELF)
public class WmEcontractInterimSelfDataWrapperService implements IWmEcontractDataWrapperService {

    private static final Logger LOG = LoggerFactory.getLogger(WmEcontractInterimSelfDataWrapperService.class);

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.INTERIM_SELF);
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        // 新模式
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("INTERIMSELF_TEMPLATE_ID", 83)); // 指定模版
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("INTERIMSELF_TEMPLATE_VERSION")); // (可选)指定版本，不指定或传null、传0的话则默认为当前已发布的版本
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo, taskBo));
        pdfInfoBo.setPdfBizContent(Lists.<Map<String, String>>newArrayList());
        return Lists.newArrayList(pdfInfoBo);
    }

    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        LOG.info("taskBo = {}", JSON.toJSONString(taskBo));

        EcontractInterimSelfInfoBo infoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractInterimSelfInfoBo.class);
        Map<String, String> pdfMap = Maps.newHashMap();

        pdfMap.put("partAName", infoBo.getPartAName());
        pdfMap.put("partBName", ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc());
        pdfMap.put("signTime", infoBo.getSignTime());
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.MT_SH_SIGNKEY);

        LOG.info("pdfMap = {}", JSON.toJSONString(pdfMap));
        return pdfMap;
    }
}
