package com.sankuai.meituan.waimai.customer.service.sign.cancel;

import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.handler.WmEcontractCallbackFailHandler;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 电子合同取消等待中任务
 */
@Service
public class WmEcontractCancelHoldingTaskService extends WmEcontractCancelBzService {

    public static final String FINISH = "econtract_finish";

    @Resource
    private WmEcontractTaskBizService wmEcontractTaskService;

    @Resource
    private WmEcontractCallbackFailHandler wmEcontractCallbackFailHandler;

    /**
     * 取消任务
     * @param taskBo
     * @return
     * @throws WmCustomerException
     */
    public Boolean cancel(EcontractTaskBo taskBo) throws WmCustomerException, TException {
        //更新poistate
        cancelPoiState(taskBo);
        //更新task信息
        wmEcontractTaskService.deleteTask(taskBo.getId());
        return Boolean.TRUE;
    }

}
