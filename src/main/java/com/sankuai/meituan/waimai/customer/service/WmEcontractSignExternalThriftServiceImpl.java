package com.sankuai.meituan.waimai.customer.service;

import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzExternalService;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.OpCustomerResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchApplyManualPackResultOutPutDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.external.WmEcontractSignExternalThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WmEcontractSignExternalThriftServiceImpl implements WmEcontractSignExternalThriftService {

    @Autowired
    private WmEcontractSignBzExternalService wmEcontractSignBzExternalService;

    @Override
    public OpCustomerResultBo batchApplyManualPackForShanGou(List<Long> customerIdList, int commitUid) throws TException, WmCustomerException {
        AssertUtil.assertCollectionNotEmpty(customerIdList, "手动打包任务客户ID列表");
        return wmEcontractSignBzExternalService.batchApplyManualPackForShanGou(customerIdList, commitUid);
    }

    /**
     * 批量触发手动打包任务
     *
     * @param customerIdList
     * @param commitUid
     * @return
     */
    @Override
    public List<BatchApplyManualPackResultOutPutDTO> batchApplyManualPack(List<Long> customerIdList, int commitUid)
            throws TException, WmCustomerException {
        AssertUtil.assertCollectionNotEmpty(customerIdList, "手动打包任务客户ID列表");
        return wmEcontractSignBzExternalService.batchApplyManualPack(customerIdList, commitUid);
    }
}
