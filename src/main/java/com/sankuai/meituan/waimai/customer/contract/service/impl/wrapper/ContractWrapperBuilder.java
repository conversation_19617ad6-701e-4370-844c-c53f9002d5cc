package com.sankuai.meituan.waimai.customer.contract.service.impl.wrapper;

import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

public class ContractWrapperBuilder {

    protected Long customerId;

    protected Integer contractType;

    protected Long contractId;

    protected WmCustomerContractBo contractBo;

    public ContractWrapperBuilder setCustomerId(Long customerId) {
        this.customerId = customerId;
        return this;
    }

    public ContractWrapperBuilder setContractType(Integer contractType) {
        this.contractType = contractType;
        return this;
    }

    public ContractWrapperBuilder setContractId(Long contractId) {
        this.contractId = contractId;
        return this;
    }

    public ContractWrapperBuilder setContractBo(WmCustomerContractBo contractBo) {
        this.contractBo = contractBo;
        return this;
    }

    public static ContractWrapperBuilder builder() {
        return new ContractWrapperBuilder();
    }

    public AbstractContractWrapper build()throws WmCustomerException {
        AssertUtil.assertObjectNotNull(contractType);
        WmTempletContractTypeEnum contractTypeEnum = WmTempletContractTypeEnum.getByCode(contractType);
        if (contractTypeEnum == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "未识别的合同类型：" + contractType);
        }

        switch (contractTypeEnum) {
            case DELIVERY_SERVICE_CONTRACT_E:
                return new DeliveryServiceContractWrapper(customerId, contractType, contractId, contractBo);
            case DELIVERY_SITE_CONTRACT_E:
                return new DeliverySiteContractWrapper(customerId, contractType, contractId, contractBo);
            case AD_ANNUAL_FRAMEWORK_CONTRACT_E:
                return new AdAnnualFrameworkContractWrapper(customerId, contractType, contractId, contractBo);
            case BRAND_AD_CONTRACT_E:
                return new BrandAdContractWrapper(customerId, contractType, contractId, contractBo);
            case AD_ORDER_E:
                return new AdOrderWrapper(customerId, contractType, contractId, contractBo);
            case PHF_CHARGE_E:
                return new PHFChargeContractWrapper(customerId, contractType, contractId, contractBo);
            default:
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "未识别的合同类型：" + contractType);
        }
    }
}
