package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.crm.ticket.thrift.constant.WmTicketStatusEnumV1;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmTicketNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAuditBO;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyRecord;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyDao;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyRecordDao;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerRecordOpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户责任人申请审核服务
 */
@Service
@Slf4j
public class CustomerOwnerApplyAuditService {

    @Autowired
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    @Autowired
    private WmCustomerOwnerApplyDao wmCustomerOwnerApplyDao;

    @Autowired
    private WmCustomerOwnerApplyRecordDao wmCustomerOwnerApplyRecordDao;

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmEmployeeService wmEmployeeService;

    /**
     * 二级状态驳回
     */
    private static final int TICKET_STAGE_REJECT = 2;

    /**
     * 处理任务审核记录
     * 
     * @param noticeDTO
     * @param audit
     */
    public void dealApplyAuditResult(WmTicketNoticeDTO noticeDTO, WmCustomerOwnerApplyAudit audit)
            throws WmCustomerException {
        log.info("CustomerOwnerApplyAuditService.dealApplyAuditResult,开始处理客户责任人申请审核结果,noticeDTO:{}, audit:{}",
                noticeDTO, audit);

        try {
            Integer auditStatus = getNewStatus(noticeDTO);
            // 未获取到有效状态则不需要处理
            if (auditStatus == null) {
                log.error("CustomerOwnerApplyAuditService.dealApplyAuditResult, 审核状态异常, noticeDTO:{},auditId={}",
                        JSON.toJSONString(noticeDTO), audit.getId());
                return;
            }
            // 查询任务信息
            WmTicketDto ticketDto = wmCrmTicketThriftServiceAdapter.getTicketById(audit.getTaskId());
            if (ticketDto == null) {
                log.error("CustomerOwnerApplyAuditService.dealApplyAuditResult,未查询到任务记录,noticeDTO:{},auditId={}",
                        JSON.toJSONString(noticeDTO), audit.getId());
                return;
            }
            // 查询子任务
            WmTicketDto ticketSubDto = wmCrmTicketThriftServiceAdapter.getSubTicketByParentTicketId(audit.getTaskId());
            if (ticketSubDto == null) {
                log.error("CustomerOwnerApplyAuditService.dealApplyAuditResult，未查询到子任务信息,noticeDTO:{},auditId={}",
                        JSON.toJSONString(noticeDTO), audit.getId());
                return;
            }
            // 查询客户责任人申请任务
            WmCustomerOwnerApply apply = wmCustomerOwnerApplyDao.getWmCustomerOwnerApplyById(audit.getApplyId());
            if (apply == null || apply.getStatus() != CustomerOwnerApplyStatusEnum.AUDITING.getCode()) {
                log.error("CustomerOwnerApplyAuditService.dealApplyAuditResult,未查询到处理中客户责任人记录,noticeDTO:{},auditId={}",
                        JSON.toJSONString(noticeDTO), audit.getId());
                return;
            }
            
            // 查主库，取消场景存在再次通知为驳回情况，这里需要查询主库以免再次处理
            WmCustomerOwnerApplyAudit applyAudit = wmCustomerOwnerApplyAuditDao.getByApplyIdRT(apply.getId());
            if (applyAudit == null) {
                log.error(
                        "CustomerOwnerApplyAuditService.dealApplyAuditResult,未查询到处理中客户责任人申请审核处理中记录,noticeDTO:{},auditId={}",
                        JSON.toJSONString(noticeDTO), audit.getId());
                return;
            }

            String opLog = "";
            Integer auditOpUid = ticketDto.getOpUid();
            WmCustomerOwnerApplyAuditBO auditBO = new WmCustomerOwnerApplyAuditBO();
            auditBO.setApplyId(audit.getApplyId());
            auditBO.setStatus(auditStatus);
            auditBO.setAuditUid(auditOpUid);
            auditBO.setAuditTime(ticketDto.getUnixUtime());
            String remark = ticketSubDto.getRemark();
            if (StringUtils.isNotBlank(remark) && remark.length() > 1024) {
                remark = remark.substring(0, 1024);
            }
            auditBO.setAuditResult(remark);
            // 更新审核记录
            wmCustomerOwnerApplyAuditDao.updateAuditStatusByApplyId(auditBO);
            // 更新申请记录为通过或驳回
            wmCustomerOwnerApplyDao.updateStatusById(audit.getApplyId(), auditStatus, apply.getStatus());
            if (auditStatus == CustomerOwnerApplyStatusEnum.COMPLETE.getCode()) {
                WmEmploy wmEmploy = wmEmployeeService.getWmEmployById(apply.getApplyUid());
                boolean updateOwnerFlag = (wmEmploy != null && wmEmploy.getValid() != 0);
                // 获取操作日志内容
                opLog = getCompleteOpLogContent(apply, updateOwnerFlag, apply.getApplyUid());
                // 判断新客户责任人存在且在职
                if (updateOwnerFlag) {
                    WmEmploy opWmEmploy = wmEmployeeService.getWmEmployById(auditOpUid);
                    // 修改客户责任人为申请人
                    wmCustomerService.changeCustomerOwnerByApply(apply, auditOpUid,
                            opWmEmploy == null ? "" : opWmEmploy.getName());
                }
            } else {
                opLog = getRejectOpLogContent(apply, noticeDTO.getTicketStatus());
            }
            // 添加操作
            addOpLog(apply, auditOpUid, opLog, ticketDto.getUnixUtime());

        } catch (Exception e) {
            log.error("CustomerOwnerApplyAuditService.dealApplyAuditResult根据通知结果处理审核信息发生异常, noticeDTO={},audit={}",
                    JSON.toJSONString(noticeDTO), JSON.toJSONString(audit), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }

    }

    /**
     * 根据任务状态获取审核状态
     * 
     * @param noticeDTO
     * @return
     */
    private static Integer getNewStatus(WmTicketNoticeDTO noticeDTO) {
        // 已终止状态
        if (noticeDTO.getTicketStatus() == WmTicketStatusEnumV1.TICKET_STATUS_STOPPED.getStatus()) {
            return CustomerOwnerApplyStatusEnum.STOP.getCode();
        }
        // 审核驳回
        if (noticeDTO.getTicketStatus() == WmTicketStatusEnumV1.TICKET_STATUS_CLOSED.getStatus()
                && noticeDTO.getTicketStage() == CustomerOwnerApplyStatusEnum.REJECT.getCode()) {
            return CustomerOwnerApplyStatusEnum.REJECT.getCode();
        }
        // 审核通过
        if (noticeDTO.getTicketStatus() == WmTicketStatusEnumV1.TICKET_STATUS_CLOSED.getStatus()
                && noticeDTO.getTicketStage() == CustomerOwnerApplyStatusEnum.COMPLETE.getCode()) {
            return CustomerOwnerApplyStatusEnum.COMPLETE.getCode();
        }
        return null;
    }

    /**
     * 添加操作日志
     * 
     * @param apply
     * @param auditOpUid
     * @param opLog
     */
    private void addOpLog(WmCustomerOwnerApply apply, Integer auditOpUid, String opLog, Integer auditTime) {
        try {
            WmCustomerOwnerApplyRecord record = new WmCustomerOwnerApplyRecord();
            record.setApplyId(apply.getId().longValue());
            record.setCustomerId(apply.getCustomerId().longValue());
            record.setOpUid(auditOpUid);
            record.setRemark("");
            record.setOpType(CustomerOwnerRecordOpTypeEnum.EDIT.getCode());
            record.setModuleId((int)WmCustomerOplogBo.OpModuleType.CUSTOMER.getType());
            record.setContent(opLog);
            record.setCtime(auditTime);
            wmCustomerOwnerApplyRecordDao.insertOwnerApplyRecord(record);
        } catch (Exception e) {
            log.error("addOpLog,添加操作记录发生异常,applyId={},opLog={}", apply.getId(), opLog, e);
        }
    }

    /**
     * 驳回添加操作日志记录
     * 
     * @param apply
     * @param ticketStatus
     * @return
     */
    private String getRejectOpLogContent(WmCustomerOwnerApply apply, Integer ticketStatus) {
        String logPrefix = "驳回客户责任人申请";
        // 任务终止操作记录标题
        if (ticketStatus == WmTicketStatusEnumV1.TICKET_STATUS_STOPPED.getStatus()) {
            logPrefix = "审批任务终止驳回客户责任人申请";
        }
        logPrefix = logPrefix + "\n申请编号：" + apply.getId() + "\n申请客户id: " + apply.getMtCustomerId() + "\n申请进度：申请驳回";
        return logPrefix;
    }

    /**
     * 申请通过添加操作日志记录
     * 
     * @param apply
     * @param updateOwnerFlag
     * @param newOwnerUid
     * @return
     */
    private String getCompleteOpLogContent(WmCustomerOwnerApply apply, boolean updateOwnerFlag, Integer newOwnerUid) {
        String logPrefix = "通过客户责任人申请\n申请编号：" + apply.getId() + "\n申请客户id: " + apply.getMtCustomerId() + "\n申请进度：申请通过";
        if (updateOwnerFlag) {
            logPrefix = logPrefix + "\n新客户责任人：" + wmEmployeeService.getUserAndId(newOwnerUid);
        } else {
            logPrefix = logPrefix + "\n申请人已离职，客户责任人不变更";
        }
        return logPrefix;
    }

}
