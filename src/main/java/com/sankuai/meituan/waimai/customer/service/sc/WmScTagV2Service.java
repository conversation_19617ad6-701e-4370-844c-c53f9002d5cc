package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScGradeLabelConstant;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScLableEnum;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiLabelMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiLabelDao;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenGradeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 打标服务
 * 迭代内容：门店全量打标修改为增量式打标
 * wm_sc_canteen_poi_task表 以及wm_sc_canteen_poi_task_audit_minutia表逻辑
 */
@Slf4j
@Service
public class WmScTagV2Service {

    @Autowired
    private WmScCanteenPoiLabelDao wmScCanteenPoiLabelDao;

    @Autowired
    private WmScCanteenPoiLabelMapper wmScCanteenPoiLabelMapper;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    /**
     * 终审通过后给门店打标时调用的方法
     * 食堂门店打标
     * 门店信息保存批量打标A、B标，批量去标
     *
     * @param canteen 食堂ID
     */
    public void saveCanteenPoiTag(WmCanteenDB canteen, Integer userId, String userName, List<Long> wmPoiIds) throws WmSchCantException {
        try {
            /**
             * A类：标签名称（校园食堂商家）；标签ID（87）
             * B类：标签与食堂供给分级对应关系
             */
            if (canteen.getGrade() == CanteenGradeEnum.EMPTY_CONTRACT.getType()) {
                // 如果食堂供给分级为空，则不打B标
                batchAddPoiLabel(canteen.getId(), userId, userName, wmPoiIds, WmScGradeLabelConstant.getLabelA());
            } else {
                batchAddPoiLabel(canteen.getId(), userId, userName, wmPoiIds, WmScGradeLabelConstant.getLabelA(), WmScGradeLabelConstant.getLabelIdByGrade(canteen.getGrade()));
            }
        } catch (WmServerException | TException e) {
            log.error("[食堂管理]调用标签系统v2打标异常,Method:SavePoiTag", e);
            throw new WmSchCantException(WmScCodeConstants.TAG_EXCEPTION, "调用标签系统v2打标失败");
        }
    }

    /**
     * 批量打标
     *
     * @param canteenId 食堂ID
     * @param labelIds  标签
     * @param userId    uid
     * @param userName  uname
     * @throws TException        TException
     * @throws WmServerException WmServerException
     */
    private void batchAddPoiLabel(Integer canteenId, int userId, String userName, List<Long> wmPoiIds, Long... labelIds) throws TException, WmServerException {
        log.info("[食堂管理]批量打标v2batchAddPoiLabel:labelIds={}", JSONUtil.toJSONString(labelIds));
        List<WmScCanteenPoiLabelDB> result = Lists.newArrayList();
        for (Long labelId : labelIds) {
            log.info("[食堂管理]批量打标v2参数:标签ID={},门店ID={}", labelId, wmPoiIds);
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelToPois(labelId, wmPoiIds, userId, userName);
            result.addAll(WmScTransUtil.toPoiLabelList(canteenId, wmPoiIds, labelId));
        }
        wmScCanteenPoiLabelDao.batchSave(result);
    }

    /**
     * 批量换标
     * @param userId   uid
     * @param userName uname
     * @throws TException        TException
     * @throws WmSchCantException WmSchCantException
     */
    public void batchChangeScCanPoiTag(int canteenId, List<Long> wmPoiIdList, int newGrade, int userId, String userName) throws TException, WmSchCantException {
        log.info("[WmScTagV2Service.batchChangeScCanPoiTag] input param: canteenId = {}, wmPoiIdList = {}, newGrade = {}, userId = {}, userName = {}",
                canteenId, JSONObject.toJSONString(wmPoiIdList), newGrade, userId, userName);
        try {
            List<WmScCanteenPoiLabelDB> delLabelList = wmScCanteenPoiLabelMapper.getLabelsByPoiIdList(wmPoiIdList, WmScLableEnum.LabelTypeEnum.B.getLabelType());
            // 批量去B标
            batchDeletePoiLabelsByCanteenId(delLabelList, userId, userName);

            // 如果供给分级不为空，则批量打B标
            if (newGrade != CanteenGradeEnum.EMPTY_CONTRACT.getType()) {
                batchAddPoiLabelWhenGradeChange(canteenId, wmPoiIdList, userId, userName, WmScGradeLabelConstant.getLabelIdByGrade(newGrade));
            }
        } catch (WmServerException e) {
            log.error("[WmScTagV2Service.batchChangeScCanPoiTag] WmServerException. canteenId = {}, wmPoiIdList = {}, newGrade = {}",
                    canteenId, JSONObject.toJSONString(wmPoiIdList), newGrade, e);
            throw new WmSchCantException(WmScCodeConstants.TAG_EXCEPTION, "调用标签系统v2打标异常");
        }
    }

    /**
     * 批量去食堂门店B类标签
     * @param delLabelList delLabelList
     * @param userId 用户ID
     * @param userName 用户名称
     * @throws TException org.apache.thrift.TException
     * @throws WmServerException com.sankuai.meituan.waimai.thrift.exception.WmServerException
     */
    private void batchDeletePoiLabelsByCanteenId(List<WmScCanteenPoiLabelDB> delLabelList, int userId, String userName) throws TException, WmServerException {
        log.info("[WmScTagV2Service.batchDeletePoiLabelsByCanteenId] input param: delLabelList = {}, userId = {}, userName = {}",
                JSONObject.toJSONString(delLabelList), userId, userName);
        // 批量去标只能去相同的标
        Map<Long, List<Long>> tmp = Maps.newHashMap();//labelId -> poiIdList
        for (WmScCanteenPoiLabelDB labelDB : delLabelList) {
            List<Long> poiIdList = tmp.get(labelDB.getLabelId());
            if (poiIdList == null) {
                poiIdList = Lists.newArrayList();
            }
            poiIdList.add(labelDB.getWmPoiId());
            tmp.put(labelDB.getLabelId(), poiIdList);
        }

        for (Map.Entry<Long, List<Long>> entry : tmp.entrySet()) {
            // 去标
            wmPoiFlowlineLabelThriftServiceAdapter.batchDeleteLabelToPois(entry.getKey(), entry.getValue(), userId, userName);
        }
        // 去记录
        wmScCanteenPoiLabelDao.batchDeleteLable(delLabelList);
    }

    /**
     * 批量打标
     * @param canteenId 食堂ID
     * @param poiIdList 需要被打标的门店列表
     * @param labelIds  标签
     * @param userId    uid
     * @param userName  uname
     * @throws TException        TException
     * @throws WmServerException WmServerException
     */
    public void batchAddPoiLabelWhenGradeChange(int canteenId, List<Long> poiIdList, int userId, String userName, Long... labelIds) throws TException, WmServerException {
        log.info("[WmScTagV2Service.batchAddPoiLabelWhenGradeChange] canteenId = {}, poiIdList = {}, userId = {}, userName = {}, labelIds = {}",
                canteenId, JSONUtil.toJSONString(labelIds), userId, userName, labelIds);
        List<WmScCanteenPoiLabelDB> result = Lists.newArrayList();
        for (Long labelId : labelIds) {
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelToPois(labelId, poiIdList, userId, userName);
            result.addAll(WmScTransUtil.toPoiLabelList(canteenId, poiIdList, labelId));
        }
        wmScCanteenPoiLabelDao.batchSave(result);
    }
}
