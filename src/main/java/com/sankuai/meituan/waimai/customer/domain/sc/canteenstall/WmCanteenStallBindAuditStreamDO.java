package com.sankuai.meituan.waimai.customer.domain.sc.canteenstall;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 食堂档口绑定审批状态流DO
 * <AUTHOR>
 * @date 2024/05/28
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmCanteenStallBindAuditStreamDO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 审批任务ID
     */
    private Integer auditTaskId;
    /**
     * 档口绑定任务ID
     */
    private Integer bindId;
    /**
     * 审批状态
     * {@link CanteenStallAuditStatusEnum}
     */
    private Integer auditStatus;
    /**
     * 是否有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Integer cuid;
    /**
     * 修改人ID
     */
    private Integer muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
}
