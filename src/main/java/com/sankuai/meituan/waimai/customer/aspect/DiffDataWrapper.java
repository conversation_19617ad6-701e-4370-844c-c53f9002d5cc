package com.sankuai.meituan.waimai.customer.aspect;

import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Inherited
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface DiffDataWrapper {

    EcontractDataWrapperEnum wrapperEnum();

}
