package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.aspect.AreaDataWrapper;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-07-13 16:14
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
@AreaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.DELIVERY_WHOLE_CITY)
public class WmEcontractDeliveryWholeCityAreaDataWrapperService implements IWmEcontractAreaDataWrapperService {

    public static final String SUPPORT_MARK = "support";

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        List<EcontractContentBo> result = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDeliveryInfoBo deliveryInfoBo = null;
        try {
            deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        } catch (Exception e) {
            log.warn("数据解析异常", e);
            return result;
        }

        if (deliveryInfoBo == null || deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo() == null) {
            return result;
        }

        EcontractDeliveryWholeCityInfoBo econtractDeliveryWholeCityInfoBo = deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo();

        if (!SUPPORT_MARK.equals(econtractDeliveryWholeCityInfoBo.getSupportSLA())
                || StringUtils.isEmpty(econtractDeliveryWholeCityInfoBo.getDeliveryArea())) {
            return result;
        }
        EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBo = JSONObject.parseObject(econtractDeliveryWholeCityInfoBo.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);

        econtractWmPoiSpAreaBo.setSlaText(WmEcontractConstant.DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT);
        EcontractContentBo econtractContentBo = WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(econtractWmPoiSpAreaBo);
        return Lists.newArrayList(econtractContentBo);
    }
}
