package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.EcontractDaoCanC2ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: 到餐C2合同PDF拼装类
 * @author: liuyunjie05
 * @create: 2024/8/5 14:57
 */
@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.DAOCAN_SERVICE_C2_CONTRACT)
public class DaoCanC2ContractDataWrapperService implements IWmEcontractDataWrapperService {

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT);
        EcontractDaoCanC2ContractInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDaoCanC2ContractInfoBo.class);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(MccConfig.getDaoCanC2ContractTemplateId());
        pdfInfoBo.setPdfTemplateVersion(MccConfig.getDaoCanC2ContractTemplateVersion());
        pdfInfoBo.setContractNumber(contractInfoBo.getContractNum());
        pdfInfoBo.setContractName(EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getDesc());
        pdfInfoBo.setContractDesc(EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getDesc());
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo, contractInfoBo));
        log.info("DaoCanC2ContractDataWrapperService#wrap, pdfInfoBo: {}", JSON.toJSONString(pdfInfoBo));
        return Lists.newArrayList(pdfInfoBo);
    }

    /**
     * 生成到餐C2合同信息数据
     */
    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractDaoCanC2ContractInfoBo contractInfoBo) {
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());

        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("contractNumber", StringUtils.defaultIfEmpty(contractInfoBo.getContractNum(), StringUtils.EMPTY));
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getDcCustomerInfo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(contextBo.getDcCustomerInfo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAContact", StringUtils.defaultIfEmpty(contextBo.getDcCustomerKp().getCompellation(), StringUtils.EMPTY));
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partB", StringUtils.defaultIfEmpty(contractInfoBo.getPartBName(), StringUtils.EMPTY));
        pdfMap.put("partBContact", StringUtils.defaultIfEmpty(contractInfoBo.getPartBContact(), StringUtils.EMPTY));
        pdfMap.put("partBSignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.AGENT_SIGNKEY);
        pdfMap.put("agentShowName", StringUtils.defaultIfEmpty(String.valueOf(contractInfoBo.getAgentShowName()), StringUtils.EMPTY));
        pdfMap.put("agentShowId", StringUtils.defaultIfEmpty(String.valueOf(contractInfoBo.getAgentId()), StringUtils.EMPTY));
        return pdfMap;
    }

}
