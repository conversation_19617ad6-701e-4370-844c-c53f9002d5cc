package com.sankuai.meituan.waimai.customer.service.kp;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.StringUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmAuditApiAdaptor;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpLegalStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.util.AppContext;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertNumberModifyEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.FromUnagentToAgentEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditCommitObj;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditAgentAuthCommitData;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditSpecialAuthCommitData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CustomerKpBusinessService {

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    public DifferentCustomerKpService differentCustomerKpService;


    @Autowired
    public MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private WmAuditApiAdaptor wmAuditApiAdaptor;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    /**
     * 新增KP操作
     *
     * @param wmCustomerKp
     * @return
     */
    public WmCustomerKp insertCustomerKp(WmCustomerKp wmCustomerKp) throws WmCustomerException {
        //1-设置敏感信息字段
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(wmCustomerKp);
        //2-新增客户KP
        wmCustomerKpDBMapper.insertSelective(wmCustomerKp);
        log.info("CustomerKpBusinessService.insertCustomerKp,初始化新增KP完成,customerId={},wmCustomerKp={}", wmCustomerKp.getCustomerId(), JSON.toJSONString(wmCustomerKp));
        return wmCustomerKp;
    }

    /**
     * KP更新到实名失败
     *
     * @param updateKp
     * @return
     */
    public void updateKp2RealNameFailOnUnEffectKp(WmCustomerKp updateKp) throws WmCustomerException {
        //设置敏感信息字段
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(updateKp);

        // 操作完成后更新db中的数据
        wmCustomerKpDBMapper.updateByPrimaryKeySelective(updateKp);
        log.info("CustomerKpBusinessService.updateKp2RealNameFail,KP流转到实名认证失败,kpId={}", updateKp.getId());
    }

    /**
     * 已生效KP实名认证失败处理
     *
     * @param context
     */
    public void updateKp2RealNameFailOnEffectKp(KpSignerStatusMachineContext context) throws WmCustomerException {

        WmCustomerKp updateKp = context.getWmCustomerKp();
        updateKp.setState(KpSignerStateMachine.CHANGE_PREAUTH_FAIL.getState());

        WmCustomerKp oldCustomerKp = context.getOldCustomerKp();
        //旧的签约人正在生效中，且修改了证件类型和证件编号，则需要更新线下表
        WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpIdMaster(oldCustomerKp.getId());
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpTempDB);
        log.info("签约人正在生效中，新增/更新线下表，customerId:{},kpId={}, state={}, kpTempDB={}", context.getCustomerId(), updateKp.getId(), updateKp.getState(), JSON.toJSONString(kpTempDB));

        List<WmCustomerDiffCellBo> kpUpdateFields = differentCustomerKpService.getSignerKpUpdateFields(oldCustomerKp, updateKp);

        //根据当前是否存在中间表信息获取最新流程中KP数据
        kpTempDB = getKpTempDB(kpTempDB, updateKp);
        wmCustomerKpLogService.updateSignerKpLog(updateKp, kpTempDB, kpUpdateFields, context.getOpUid(), context.getOpUName());

        List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(oldCustomerKp, updateKp, differentCustomerKpService.getKpDiffFieldsMap());
        kpPropertiesSet(oldCustomerKp, updateKp);
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(oldCustomerKp);
        wmCustomerKpTempDBMapper.updateByPrimaryKeySelective(kpTempDB);
        wmCustomerKpLogService.updateKpLog(oldCustomerKp, diffCellBos, context.getOpUid(), context.getOpUName());
    }

    /**
     * 生效签约人KP实名后直接生效
     *
     * @param context
     */
    public void drivenEffectKpSignerRealNameSuc2Effect(KpSignerStatusMachineContext context) throws WmCustomerException {

        WmCustomerKp oldCustomerKp = context.getOldCustomerKp();
        WmCustomerKp updateKp = context.getWmCustomerKp();
        Integer customerId = context.getCustomerId();
        //更新状态为生效
        context.getWmCustomerKp().setState(KpSignerStateMachine.EFFECT.getState());

        List<WmCustomerDiffCellBo> kpUpdateFields = differentCustomerKpService.getSignerKpUpdateFields(oldCustomerKp, updateKp);

        //旧的签约人正在生效中，且修改了证件类型和证件编号，则需要更新线下表
        WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpIdMaster(oldCustomerKp.getId());
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpTempDB);
        log.info("签约人正在生效中，新增/更新线下表，customerId:{},kpId={}, state={}, kpTempDB={}", customerId, updateKp.getId(), updateKp.getState(), JSON.toJSONString(kpTempDB));

        //根据当前是否存在中间表信息获取最新流程中KP数据
        kpTempDB = getKpTempDB(kpTempDB, updateKp);
        //直接生效，设置相同的属性
        oldCustomerKp.setOperateSource(updateKp.getOperateSource());
        //发送Push通知
        wmCustomerKpBuryingPointService.afterSingKp(null, context.getOpUid(), updateKp, oldCustomerKp);
        //不需原签约人授权，直接将数据更新为生效表数据，并删除该条临时记录
        wmCustomerKpService.tempKpEffect(kpTempDB, oldCustomerKp);
        kpTempDB.setState(KpSignerStateMachine.EFFECT.getState());
        kpTempDB.setValid(KpConstants.UN_VALID);
        log.info("不需原签约人授权，直接生效，kpTempDB={}，oldCustomerKp={}", JSON.toJSONString(kpTempDB), JSON.toJSONString(oldCustomerKp));
        //更新为逻辑删除
        wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(kpTempDB);
        wmCustomerKpTempDBMapper.updateByPrimaryKeySelective(kpTempDB);
        wmCustomerKpLogService.updateSignerKpLog(oldCustomerKp, kpTempDB, kpUpdateFields, context.getOpUid(), context.getOpUName());

        //如果是从非代理人更新成代理的则更新字段
        boolean fromUnAgentToAgent = wmCustomerKpAuditService.isFromUnAgentToAgent(oldCustomerKp.getSignerType(), kpTempDB.getSignerType());
        updateFromUnAgent2Agent(fromUnAgentToAgent, oldCustomerKp.getId());
        Map<String, WmCustomerDiffCellBo> kpUpdateFieldsMap = Maps.uniqueIndex(kpUpdateFields == null ? Lists.<WmCustomerDiffCellBo>newArrayList() : kpUpdateFields, new Function<WmCustomerDiffCellBo, String>() {
            @Override
            public String apply(@Nullable WmCustomerDiffCellBo input) {
                return input == null ? "" : input.getField();
            }
        });
        //如果证件号变更则更新字段
        updateCertifyNumberModify(kpUpdateFieldsMap, oldCustomerKp.getId());

        // 发送授权消息
        sendEffectiveMq(true, context.getCustomerId());
        //更新客户门店属性
        wmCustomerPoiAttributeService.updateForKpUpdateAsy(oldCustomerKp, false);
    }

    /**
     * KP信息更新为已录入状态-信息暂存
     *
     * @param wmCustomerKp
     * @throws WmCustomerException
     */
    public void updateKp2TempStore(WmCustomerKp wmCustomerKp) throws WmCustomerException {
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(wmCustomerKp);
        wmCustomerKp.setState(KpSignerStateMachine.RECORDED.getState());
        wmCustomerKp.setVersion(KpVersionEnum.V3.getCode());
        wmCustomerKpDBMapper.updateByPrimaryKeySelective(wmCustomerKp);
        log.info("CustomerKpBusinessService.updateKp2TempStore,KP流转到暂存状态,wmCustomerKp={}", JSON.toJSONString(wmCustomerKp));
    }

    /**
     * 已生效KP流转到实名认证失败
     *
     * @param context
     */
    public void updateEffectKp2RealNameFail(KpLegalStatusMachineContext context) throws WmCustomerException {

        //获取修改前后变更信息
        List<WmCustomerDiffCellBo> kpUpdateFields = differentCustomerKpService.getKpLegalUpdateFields(context.getOldCustomerKp(), context.getWmCustomerKp());
        //获取更新的KP信息
        WmCustomerKp updateKp = context.getWmCustomerKp();
        //查询是否存在临时KP记录
        WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpIdMaster(updateKp.getId());
        Integer kpTempPrimaryId = kpTempDB == null ? null : kpTempDB.getId();
        //构建临时表KP数据
        kpTempDB = buildKpTempByUpdateKp(updateKp, kpTempPrimaryId, KpLegalStateMachine.PREAUTH_FAIL.getState());
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTempDB);
        if (kpTempPrimaryId != null) {
            wmCustomerKpTempDBMapper.updateByPrimaryKeySelective(kpTempDB);
        } else {
            wmCustomerKpTempDBMapper.insertSelective(kpTempDB);
        }

        log.info("更新法人kp线下记录kpTempDB id={} info={}", kpTempDB.getId(), JSON.toJSONString(kpTempDB));
        wmCustomerKpLogService.updateSignerKpLog(updateKp, kpTempDB, kpUpdateFields, context.getOpUid(), context.getOpUName());
    }

    /**
     * 已生效KP流转到生效
     *
     * @param context
     */
    public void updateEffectKp2Effective(KpLegalStatusMachineContext context) throws WmCustomerException {
        //获取修改前后变更信息
        List<WmCustomerDiffCellBo> kpUpdateFields = differentCustomerKpService.getKpLegalUpdateFields(context.getOldCustomerKp(), context.getWmCustomerKp());
        //获取更新的KP信息
        WmCustomerKp updateKp = context.getWmCustomerKp();
        WmCustomerKp oldCustomerKp = context.getOldCustomerKp();
        //查询临时表数据
        WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpIdMaster(updateKp.getId());
        Integer kpPrimaryId = kpTempDB == null ? null : kpTempDB.getId();
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpTempDB);

        //构建临时表KP数据
        kpTempDB = buildKpTempByUpdateKp(updateKp, kpPrimaryId, KpLegalStateMachine.EFFECT.getState());
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTempDB);
        if (kpPrimaryId != null) {
            wmCustomerKpTempDBMapper.updateByPrimaryKeySelective(kpTempDB);
        } else {
            wmCustomerKpTempDBMapper.insertSelective(kpTempDB);
        }
        log.info("updateEffectKp2Effective,更新法人kp线下记录kpTempDB id={} info={}", kpTempDB.getId(), JSON.toJSONString(kpTempDB));
        wmCustomerKpLogService.updateSignerKpLog(updateKp, kpTempDB, kpUpdateFields, context.getOpUid(), context.getOpUName());

        //将temp表设置为无效
        kpTempDB.setValid(KpConstants.UN_VALID);
        wmCustomerKpTempDBMapper.updateByPrimaryKeySelective(kpTempDB);
        log.info("updateEffectKp2Effective,将临时KP表数据更新为无效,tempKp={}", JSON.toJSONString(kpTempDB));

        //更新KP主表数据
        kpPropertiesSet(oldCustomerKp, updateKp);
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(oldCustomerKp);
        wmCustomerKpDBMapper.updateByPrimaryKeySelective(oldCustomerKp);
    }

    /**
     * 提交代理人审核
     *
     * @param context
     */
    public void commitKpAudit(KpSignerStatusMachineContext context, byte kpAuditType) throws WmCustomerException {
        WmCustomerKp wmCustomerKp = context.getWmCustomerKp();
        WmCustomerDB wmCustomer = context.getWmCustomerDB();
        //1-创建提审记录
        WmCustomerKpAudit audit = wmCustomerKpAuditService.insertKpAudit(wmCustomerKp, kpAuditType, context.getOpUid(), context.getOpUName());
        //2-构建提审参数
        int bizType;
        String dataJson;
        if (context.getAuditType() == KpAuditConstants.TYPE_AGENT) {
            bizType = WmAuditTaskBizTypeConstant.AGENT_AUTH;
            WmAuditAgentAuthCommitData data = buildAgentAuditCommitData(wmCustomerKp, wmCustomer);
            dataJson = JSON.toJSONString(data);
        } else if (context.getAuditType() == KpAuditConstants.TYPE_SPECIAL) {
            bizType = WmAuditTaskBizTypeConstant.SPECIAL_AUTH;
            WmAuditSpecialAuthCommitData data = buildSpecialAuditCommitData(wmCustomerKp, wmCustomer);
            dataJson = JSON.toJSONString(data);
        } else {
            throw new WmCustomerException(500, "不合法的审核类型：" + context.getAuditType());
        }
        log.info("commitAudit,开始提审,customerKp={},auditId={}", JSON.toJSONString(wmCustomerKp), audit.getId());
        //构建提审对象
        WmAuditCommitObj commitObj = new WmAuditCommitObj()
                .setBiz_type(bizType)
                .setBiz_id(audit.getId())
                .setWm_poi_id(0)
                .setCustomer_id(wmCustomerKp.getCustomerId())
                .setSubmit_uid(context.getOpUid())
                .setData(dataJson);
        //3-提交审核
        wmAuditApiAdaptor.commitAudit(commitObj);
    }

    /**
     * 已生效KP提审
     *
     * @param context
     * @param kpAuditType
     * @throws WmCustomerException
     */
    public void effectKpCommitAudit(KpSignerStatusMachineContext context, byte kpAuditType) throws WmCustomerException {

        WmCustomerKp updateKp = context.getWmCustomerKp();
        WmCustomerKp oldCustomerKp = context.getOldCustomerKp();
        //提审
        commitKpAudit(context, kpAuditType);

        List<WmCustomerDiffCellBo> kpUpdateFields = differentCustomerKpService.getSignerKpUpdateFields(oldCustomerKp, updateKp);
        Map<String, WmCustomerDiffCellBo> kpUpdateFieldsMap = Maps.uniqueIndex(kpUpdateFields == null ? Lists.<WmCustomerDiffCellBo>newArrayList() : kpUpdateFields, new Function<WmCustomerDiffCellBo, String>() {
            @Override
            public String apply(@Nullable WmCustomerDiffCellBo input) {
                return input == null ? "" : input.getField();
            }
        });

        //旧的签约人正在生效中，且修改了证件类型和证件编号，则需要更新线下表
        WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpIdMaster(oldCustomerKp.getId());
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpTempDB);
        log.info("effectKpCommitAudit,签约人正在生效中，新增/更新线下表，customerId:{},kpId={}, state={}, kpTempDB={}", context.getCustomerId(), updateKp.getId(), updateKp.getState(), JSON.toJSONString(kpTempDB));

        //根据当前是否存在中间表信息获取最新流程中KP数据
        kpTempDB = getKpTempDB(kpTempDB, updateKp);

        wmCustomerKpLogService.updateSignerKpLog(updateKp, kpTempDB, kpUpdateFields, context.getOpUid(), context.getOpUName());

        boolean fromUnAgentToAgent = wmCustomerKpAuditService.isFromUnAgentToAgent(oldCustomerKp.getSignerType(), kpTempDB.getSignerType());

        //如果是从非代理人更新成代理的则更新字段
        updateFromUnAgent2Agent(fromUnAgentToAgent, oldCustomerKp.getId());
        //如果证件号变更则更新字段
        updateCertifyNumberModify(kpUpdateFieldsMap, oldCustomerKp.getId());
    }

    /**
     * 发送签约人KP生效消息
     *
     * @param sendEffectiveMq
     * @param customerId
     */
    public void sendSignerEffectiveMq(boolean sendEffectiveMq, int customerId) {
        if (!sendEffectiveMq) {
            return;
        }
        // 签约人KP生效发送MQ通知
        if (AppContext.isLazyProcess()) {
            AppContext.offerLazyTask(new AppContext.LazyTask() {
                @Override
                public void lazyProcess() {
                    mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
                }

                @Override
                public String taskDesc() {
                    return "自入驻绑定门店至3.0，签约人KP生效发送消息";
                }
            });
        } else {
            mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
        }
    }

    /**
     * 构建tempKp数据
     *
     * @param updateKp
     * @param kpTempPrimaryId
     * @return
     */
    private WmCustomerKpTemp buildKpTempByUpdateKp(WmCustomerKp updateKp, Integer kpTempPrimaryId, byte newKpState) {

        WmCustomerKpTemp kpTempDB = new WmCustomerKpTemp();
        if (kpTempPrimaryId != null) {
            kpTempDB.setId(kpTempPrimaryId);
        }
        kpTempDB.setKpId(updateKp.getId());
        kpTempDB.setKpType(updateKp.getKpType());
        kpTempDB.setCertType(updateKp.getCertType());
        kpTempDB.setCompellation(updateKp.getCompellation());
        kpTempDB.setCertNumber(updateKp.getCertNumber());
        kpTempDB.setCertNumberEncryption(updateKp.getCertNumberEncryption());
        kpTempDB.setCertNumberToken(updateKp.getCertNumberToken());
        kpTempDB.setBankId(updateKp.getBankId());
        kpTempDB.setBankName(updateKp.getBankName());
        kpTempDB.setCreditCard(updateKp.getCreditCard());
        kpTempDB.setCreditCardEncryption(updateKp.getCreditCardEncryption());
        kpTempDB.setCreditCardToken(updateKp.getCreditCardToken());
        kpTempDB.setPhoneNum(updateKp.getPhoneNum());
        kpTempDB.setPhoneNumEncryption(updateKp.getPhoneNumEncryption());
        kpTempDB.setPhoneNumToken(updateKp.getPhoneNumToken());
        //状态转换
        kpTempDB.setState(newKpState);
        kpTempDB.setValid(KpConstants.VALID);

        return kpTempDB;

    }

    /**
     * KP更新字段设置
     *
     * @param oldCustomerKp 数据库中现有的KP信息
     * @param wmCustomerKp  要更新的KP信息
     */
    public void kpPropertiesSet(WmCustomerKp oldCustomerKp, WmCustomerKp wmCustomerKp) {
        log.info("kpPropertiesSet oldCustomerKp = {}, wmCustomerKp = {}", JSON.toJSONString(oldCustomerKp), JSON.toJSONString(wmCustomerKp));
        oldCustomerKp.setKpType(wmCustomerKp.getKpType());
        oldCustomerKp.setSignerType(wmCustomerKp.getSignerType());
        oldCustomerKp.setCertType(wmCustomerKp.getCertType());
        oldCustomerKp.setCertNumber(wmCustomerKp.getCertNumber());
        oldCustomerKp.setCompellation(wmCustomerKp.getCompellation());
        oldCustomerKp.setPhoneNum(wmCustomerKp.getPhoneNum());
        oldCustomerKp.setCreditCard(wmCustomerKp.getCreditCard());
        oldCustomerKp.setEmail(wmCustomerKp.getEmail());
        oldCustomerKp.setAgentAuth(wmCustomerKp.getAgentAuth());
        oldCustomerKp.setAgentFrontIdcard(wmCustomerKp.getAgentFrontIdcard());
        oldCustomerKp.setAgentBackIdcard(wmCustomerKp.getAgentBackIdcard());
        oldCustomerKp.setFailReason(wmCustomerKp.getFailReason());
        oldCustomerKp.setSpecialAttachment(wmCustomerKp.getSpecialAttachment());
        oldCustomerKp.setState(wmCustomerKp.getState());
        oldCustomerKp.setBankName(wmCustomerKp.getBankName());
        oldCustomerKp.setBankId(wmCustomerKp.getBankId());
        oldCustomerKp.setBrandIds(wmCustomerKp.getBrandIds());
        oldCustomerKp.setVisitKPPro(wmCustomerKp.getVisitKPPro());
        oldCustomerKp.setWdcClueId(wmCustomerKp.getWdcClueId());
        oldCustomerKp.setLegalIdcardCopy(wmCustomerKp.getLegalIdcardCopy());
        oldCustomerKp.setHaveAgentAuth(wmCustomerKp.getHaveAgentAuth());
        oldCustomerKp.setVersion(wmCustomerKp.getVersion());
        oldCustomerKp.setEffective(wmCustomerKp.getEffective());
        //更新来源属性
        oldCustomerKp.setOperateSource(wmCustomerKp.getOperateSource());
        oldCustomerKp.setLegalAuthType(wmCustomerKp.getLegalAuthType());
    }

    /**
     * 构建特批提审数据对象
     *
     * @param wmCustomerKp
     * @return
     */
    public WmAuditSpecialAuthCommitData buildSpecialAuditCommitData(WmCustomerKp wmCustomerKp, WmCustomerDB wmCustomer) {
        //构造特批提审数据
        WmAuditSpecialAuthCommitData data = new WmAuditSpecialAuthCommitData();
        data.setName(wmCustomerKp.getCompellation());
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(wmCustomerKp.getCertType());
        data.setCardType(Integer.valueOf(wmCustomerKp.getCertType()));
        data.setCardTypeStr(certTypeEnum != null ? certTypeEnum.getName() : "未知证件类型");
        data.setCardNo(wmCustomerKp.getCertNumber());
        data.setPhoneNum(wmCustomerKp.getPhoneNum());
        data.setBankNum(wmCustomerKp.getCreditCard());
        data.setSpecialAuthUrl(Arrays.asList(wmCustomerKp.getSpecialAttachment().split(";")));
        if (wmCustomer.getBizOrgCode() != null && wmCustomer.getBizOrgCode().intValue() > 0) {
            data.setCustomerBusinessLine(wmCustomer.getBizOrgCode());
        }
        CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomer.getCustomerRealType());
        data.setCustomerType(customerRealTypeEnum.getName());
        data.setCustomerTypeCode(customerRealTypeEnum.getValue());
        return data;
    }

    /**
     * 构建代理人提审数据对象
     *
     * @param wmCustomerKp
     * @return
     */
    public WmAuditAgentAuthCommitData buildAgentAuditCommitData(WmCustomerKp wmCustomerKp, WmCustomerDB wmCustomer) {
        //构造代理人提审数据
        WmAuditAgentAuthCommitData data = new WmAuditAgentAuthCommitData();
        data.setName(wmCustomerKp.getCompellation());
        data.setCardType(Integer.valueOf(wmCustomerKp.getCertType()));
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(wmCustomerKp.getCertType());
        data.setCardTypeStr(certTypeEnum == null ? "未知证件类型" : certTypeEnum.getName());
        data.setCardNo(wmCustomerKp.getCertNumber());
        data.setPhoneNum(wmCustomerKp.getPhoneNum());
        data.setBankNum(wmCustomerKp.getCreditCard());
        data.setAgentAuthUrl(wmCustomerKp.getAgentAuth());
        data.setAgentCardUrl(wmCustomerKp.getAgentFrontIdcard());
        data.setAgentCardBackUrl(wmCustomerKp.getAgentBackIdcard());
        //代理人提审添加授权方式字段
        Integer legalAuthType = wmCustomerKp.getLegalAuthType();
        if (legalAuthType != null && legalAuthType == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            data.setLegalAuthType(LegalAuthTypeEnum.MESSAGE_AUTH.getCode());
            data.setLegalAuthTypeStr(LegalAuthTypeEnum.MESSAGE_AUTH.getDesc());
        } else {
            data.setLegalAuthType(LegalAuthTypeEnum.PAPER_AUTH.getCode());
            data.setLegalAuthTypeStr(LegalAuthTypeEnum.PAPER_AUTH.getDesc());
        }
        if (!StringUtil.isBlank(wmCustomer.getLegalPerson())) {
            data.setLegalPerson(wmCustomer.getLegalPerson());
        }
        if (wmCustomer.getBizOrgCode() != null && wmCustomer.getBizOrgCode().intValue() > 0) {
            data.setCustomerBusinessLine(wmCustomer.getBizOrgCode());
        }
        CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomer.getCustomerRealType());
        data.setCustomerType(customerRealTypeEnum.getName());
        data.setCustomerTypeCode(customerRealTypeEnum.getValue());

        data.setLegalPersonIdCardCopy(wmCustomerKp.getLegalIdcardCopy());
        return data;
    }

    private WmCustomerKpTemp getKpTempDB(WmCustomerKpTemp kpTempDB, WmCustomerKp updateKp) throws WmCustomerException {
        if (kpTempDB != null) {
            //说明是变更签约人的更新
            int id = kpTempDB.getId();
            BeanUtils.copyProperties(updateKp, kpTempDB);
            kpTempDB.setId(id);
            //状态转换
            kpTempDB.setState(updateKp.getState());
            kpTempDB.setValid(KpConstants.VALID);
            log.info("更新签约人kp线下记录kpTempDB={}", JSON.toJSONString(kpTempDB));
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTempDB);
            wmCustomerKpTempDBMapper.updateByPrimaryKeySelective(kpTempDB);
        } else {
            kpTempDB = new WmCustomerKpTemp();
            BeanUtils.copyProperties(updateKp, kpTempDB);
            kpTempDB.setKpId(updateKp.getId());
            //状态转换
            kpTempDB.setState(updateKp.getState());
            kpTempDB.setValid(KpConstants.VALID);
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTempDB);
            wmCustomerKpTempDBMapper.insertSelective(kpTempDB);
            log.info("更新签约人kp线下记录kpTempDB id={} info={}", kpTempDB.getId(), JSON.toJSONString(kpTempDB));
        }
        return kpTempDB;
    }

    private void sendEffectiveMq(boolean sendEffectiveMq, int customerId) {
        if (sendEffectiveMq) {
            // 签约人KP生效发送MQ通知
            if (AppContext.isLazyProcess()) {
                AppContext.offerLazyTask(new AppContext.LazyTask() {
                    @Override
                    public void lazyProcess() {
                        mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
                    }

                    @Override
                    public String taskDesc() {
                        return "自入驻绑定门店至3.0，签约人KP生效发送消息";
                    }
                });
            } else {
                mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
            }
        }
    }

    /**
     * 处理非代理人到代理人字段直接更新
     *
     * @param fromUnAgentToAgent
     */
    private void updateFromUnAgent2Agent(Boolean fromUnAgentToAgent, Integer kpId) throws WmCustomerException {
        if (fromUnAgentToAgent) {
            wmCustomerKpDBMapper.updateFromUnagentAgent(FromUnagentToAgentEnum.YES.getCode(), kpId);
        } else {
            wmCustomerKpDBMapper.updateFromUnagentAgent(FromUnagentToAgentEnum.NO.getCode(), kpId);
        }
    }

    private void updateCertifyNumberModify(Map<String, WmCustomerDiffCellBo> kpUpdateFieldsMap, Integer kpId) {
        if (kpUpdateFieldsMap.containsKey(KpConstants.KpFields.CERT_NUMBER)) {
            wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), kpId);
        } else {
            wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.NO.getCode(), kpId);
        }
    }
}
