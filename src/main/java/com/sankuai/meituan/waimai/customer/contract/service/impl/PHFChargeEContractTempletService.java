package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBrandAdContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPHFChargeInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.PHF_CHARGE_E})
public class PHFChargeEContractTempletService extends AbstractWmEContractTempletService {

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.PHF_CHARGE);
        applyBo.setConfigBo(new EcontractTaskConfigBo());

        EcontractPHFChargeInfoBo contractInfoBo = JSON.parseObject(contractBo.getExtraData(), EcontractPHFChargeInfoBo.class);
        contractInfoBo.setContractNumber(contractBo.getBasicBo().getContractNum());
        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
        return applyBo;
    }

    @Override
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String pHFChargeContractENum = ContractNumberUtil.genPHFChargeContractENum(insertId);
        contractBo.getBasicBo().setContractNum(pHFChargeContractENum);
        log.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, pHFChargeContractENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), pHFChargeContractENum);
        return insertId;
    }
}
