package com.sankuai.meituan.waimai.customer.util;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.util.common.HumanKey;
import com.sankuai.meituan.waimai.customer.util.common.HumanMessage;
import com.sankuai.meituan.waimai.customer.util.common.LocaleSpecificMessageInterpolator;
import com.sankuai.meituan.waimai.customer.util.common.ValidResult;
import com.sankuai.meituan.waimai.util.DateUtil;
import java.lang.reflect.Field;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.regex.Pattern;
import javax.annotation.Nullable;
import javax.validation.ConstraintViolation;
import javax.validation.MessageInterpolator;
import javax.validation.Path.Node;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.joda.time.DateTime;

@Slf4j
public class ValidateUtil {

    public static final Pattern PATTERN_IDENTITY_NUMBER = Pattern.compile("(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)");
    private static Validator validator;

    static {
        ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
        MessageInterpolator interpolator = new LocaleSpecificMessageInterpolator(
            validatorFactory.getMessageInterpolator(),
            Locale.SIMPLIFIED_CHINESE);

        validator = validatorFactory.usingContext().messageInterpolator(interpolator).getValidator();
    }


    private static boolean matchPattern(Pattern regexPattern, CharSequence input) {
        if (regexPattern == null) {
            throw new IllegalArgumentException("regexPattern is null");
        }
        if (input == null) {
            return false;
        }
        return regexPattern.matcher(input).matches();
    }

    /**
     * 是不是身份证号码
     *
     * @param input
     * @return
     */
    public static boolean isIDCardNumber(String input) {
        return matchPattern(PATTERN_IDENTITY_NUMBER, input);
    }

    /**
     * 未成年人校验
     * @param number
     * @return
     */
    public static boolean isAdult(String number) {
        //18位取6 ~ 14位，15位取
        String birthday = (number.length() == 18) ? number.substring(6, 14) : "19" + number.substring(6, 12);
        //是否满足18岁
        DateTime birthdayDate = new DateTime(DateUtil.string2Date4Null(birthday, "yyyyMMdd").getTime());
        if (birthdayDate.plusYears(18).isAfterNow()) {
            return false;
        }
        return true;
    }


    private static final Function<ImmutablePair<String, String>, String> validateResultPairTransform = new Function<ImmutablePair<String, String>, String>() {
        @Nullable
        @Override
        public String apply(@Nullable ImmutablePair<String, String> input) {
            if (null == input) {
                log.error("[不应该到这]_验证异常_input_isnull");
                return "";
            }
            return input.getLeft() + input.getRight();
        }
    };

    private static final Function<ConstraintViolation<?>, ImmutablePair<String, String>> constraintViolationTransform = new Function<ConstraintViolation<?>, ImmutablePair<String, String>>() {
        @Nullable
        @Override
        public ImmutablePair<String, String> apply(@Nullable ConstraintViolation<?> constraintViolation) {
            if (null == constraintViolation) {
                log.error("[验证异常]_不可能到这_constraintViolation_isnull");
                return ImmutablePair.of("", "");
            }
            Class clazz = constraintViolation.getLeafBean().getClass();
            Iterator<Node> it = constraintViolation.getPropertyPath().iterator();
            String fieldName = null;
            while (it.hasNext()) {
                fieldName = it.next().getName();
            }
            if (fieldName != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);

                    HumanMessage humanMessage = field.getAnnotation(HumanMessage.class);
                    String message = constraintViolation.getMessage();
                    if (humanMessage != null && (humanMessage.override() || message.length() > 0)) {
                        message = humanMessage.value();
                    }

                    HumanKey key = field.getAnnotation(HumanKey.class);
                    String keyValue = (key != null && key.value().length() != 0) ?
                        key.value() :
                        constraintViolation.getPropertyPath().toString();

                    return ImmutablePair.of(keyValue, message);
                } catch (NoSuchFieldException | SecurityException e) {
                    log.warn("Cannot find field " + fieldName, e);
                }
            }
            return ImmutablePair.of(constraintViolation.getPropertyPath().toString(), constraintViolation.getMessage());
        }
    };


    public static <T> ValidResult validate(T t) {

        Set<ConstraintViolation<T>> constraintViolations = validator.validate(t);

        if (constraintViolations.isEmpty()) {
            return ValidResult.EMPTY_RESULT;
        }
        List<ImmutablePair<String, String>> immutablePairList = Lists.transform(Lists.newArrayList(constraintViolations),
            constraintViolationTransform);
        return new ValidResult(Lists.transform(immutablePairList, validateResultPairTransform));

    }
}
