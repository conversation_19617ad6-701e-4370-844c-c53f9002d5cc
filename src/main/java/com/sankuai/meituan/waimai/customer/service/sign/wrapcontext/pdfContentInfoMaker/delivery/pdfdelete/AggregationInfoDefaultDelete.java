package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY_AGGREGATION;
import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum.DELIVERY_AGGREGATION_INFO;

@Slf4j
@Service
@PdfDeleter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_DEFAULT)
public class AggregationInfoDefaultDelete implements DeliveryPdfDelete {
    @Override
    public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
        if (CollectionUtils.isEmpty(pdfDataMap.get(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_DEFAULT.getName()))) {
            Collection<SignTemplateEnum> tabList = tabPdfMap.get(TAB_DELIVERY_AGGREGATION);
            if (CollectionUtils.isNotEmpty(tabList)) {
                tabList.removeIf(value -> value.equals(DELIVERY_AGGREGATION_INFO)
                        || value.equals(SignTemplateEnum.DELIVERY_MULTI_AGGREGATION_INFO));
            }
        }
    }
}
