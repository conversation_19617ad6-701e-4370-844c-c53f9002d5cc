package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

/**
 * 拼好饭结算合同短信模板
 */
@Service
public class WmEcontractPHFChargeSmsWrapperService extends AbstractWmEcontractSmsWrapperService {

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo) throws TException, WmCustomerException {
        return super.wrap(contextBo);
    }

    @Override
    protected void assembleSmsParamMap(Map<String, String> smsParamMap, EcontractBatchContextBo contextBo, List<Long> wmPoiIdList) throws TException, WmCustomerException {
        smsParamMap.put("platform", analysisPlatform(contextBo));
        smsParamMap.put("module", analysisModule(contextBo));
        smsParamMap.put("detail", analysisDetail(contextBo));
        smsParamMap.put("other", analysisOther(contextBo));

        JSONObject applyContext = JSON.parseObject(contextBo.getTaskIdAndTaskMap().entrySet().iterator().next().getValue().getApplyContext());
        smsParamMap.put("name", applyContext.getString("smsTemplateContact"));
        smsParamMap.put("phone", applyContext.getString("smsTemplateContactPhone"));
    }
}
