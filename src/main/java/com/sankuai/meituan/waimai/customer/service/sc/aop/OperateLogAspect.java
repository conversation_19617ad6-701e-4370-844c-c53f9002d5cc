package com.sankuai.meituan.waimai.customer.service.sc.aop;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.service.sc.WmScLogService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.OptTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;

/**
 * @program: scm
 * @description: 切面处理
 * @author: jianghuimin02
 * @create: 2020-04-27 12:04
 **/
@Aspect
@Component
@Slf4j
public class OperateLogAspect {

    @Autowired
    private WmScLogService wmScLogService;

    @Pointcut("@annotation(com.sankuai.meituan.waimai.customer.service.sc.aop.OperateLog)")
    public void pointcut() {
    }

    @AfterReturning(returning="result", pointcut="pointcut()")
    private void around(JoinPoint joinPoint, Object result) throws Throwable {
        try {
            Signature signature = joinPoint.getSignature();
            Method currentMethod = getCurMethod(joinPoint, signature);
            MethodSignature methodSignature = (MethodSignature)signature;

            OperateLog operateLog = currentMethod.getAnnotation(OperateLog.class);
            OptTypeEnum optType = operateLog.optType();
            int moduleType = operateLog.module();
            // 参数解析
            Object[] paramValues = joinPoint.getArgs();
            String[] paramNames = methodSignature.getParameterNames();
            log.info("校园食堂项目:插入日志中间参数:optType:{}:modeType:{}:paramValues:{}:paramNames:{}",
                    optType, moduleType, paramValues, paramNames);
            if (ArrayUtils.isEmpty(paramNames) || ArrayUtils.isEmpty(paramValues)) {
                return ;
            }
            String userName = null;
            Integer userId = null;
            CanteenBo canteenBo = new CanteenBo();
            SchoolBo schoolBo = new SchoolBo();
            for (int i = 0; i < paramNames.length; i++) {
                String paramName = paramNames[i];
                Object paramValue = paramValues[i];

                switch (paramName) {
                    case "userId":
                        userId = paramValue == null ? 0 : Integer.parseInt(paramValue.toString());
                        break;
                    case "userName":
                        userName = paramValue == null ? "" : paramValue.toString();
                        break;
                    case "schoolBo":
                        if(paramValue != null){
                            schoolBo = (SchoolBo)paramValue;
                        }
                        break;
                    case "canteenBO":
                        if(paramValue != null){
                            canteenBo = (CanteenBo)paramValue;
                        }
                        break;

                }
            }
            int moduleId  = postHandle(result);
            wmScLogService.handleLog((byte)optType.getType(), moduleType, moduleId, userId, userName , canteenBo, schoolBo);
            log.info("校园食堂项目:插入日志执行参数:optType:{}:moduleType:{}:moduleId:{}:userId:{}:userName:{}:canteenBo:{}:schoolBo:{}",
                    (byte)optType.getType(), moduleType, moduleId, userId, userName, JSONObject.toJSONString(canteenBo), JSONObject.toJSONString(schoolBo));
        } catch (Exception e) {
            log.error("校园食堂项目:插入操作日志失败", e);
        }
    }

    public Method getCurMethod(JoinPoint joinPoint,  Signature signature) throws Exception{
        MethodSignature msig = (MethodSignature) signature;
        Object target = joinPoint.getTarget();
        Method currentMethod = target.getClass().getMethod(msig.getName(), msig.getParameterTypes());
        return currentMethod;
    }

    /**
     * 返回数据
     * @param retVal
     * @return
     */
    private Integer postHandle(Object retVal) {
        if(null == retVal){
            return 0;
        }
        return Integer.parseInt(retVal.toString());
    }



}
