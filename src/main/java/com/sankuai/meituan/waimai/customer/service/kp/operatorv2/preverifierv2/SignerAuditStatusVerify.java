package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-09-09 19:53
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class SignerAuditStatusVerify extends KpPreverify {

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        // 新增校验
        if (insertKp != null) {
            // 新增空实现，后续可扩展
        }

        // 更新校验
        if (updateKp != null) {
            // 如果KP在审核中，校验不通过
            WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(updateKp.getId());
            if (null != wmCustomerKpTemp) {
                ThrowUtil.throwClientError("KP信息审核中不可修改");
            }
        }

        // 删除校验
        if (deleteKp != null) {
            // 删除空实现，后续可扩展

        }
        return new Object();
    }
}
