package com.sankuai.meituan.waimai.customer.service;

import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBrandService;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.IntResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBrandBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBrandListPageData;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerBrandThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WmCustomerBrandThriftServiceImpl implements WmCustomerBrandThriftService {

    @Autowired
    private WmCustomerBrandService wmCustomerBrandService;

    /**
     * 批量插入客户品牌关联
     * @param customerId
     * @param brandIds
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public void batchSaveCustomerBrandRel(int customerId, List<Integer> brandIds, int opUid, String opName) throws TException, WmCustomerException {
        wmCustomerBrandService.batchSaveCustomerBrandRel(customerId, brandIds, opUid, opName);
    }

    /**
     * 删除客户品牌关联
     * @param customerId
     * @param brandIds
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public void deleteCustomerBrandRel(int customerId, List<Integer> brandIds, int opUid, String opName) throws TException, WmCustomerException {
        wmCustomerBrandService.deleteCustomerBrandRel(customerId, brandIds, opUid, opName);
    }

    /**
     * 获取客户品牌列表
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public List<Integer> selectBrandIdListByCustomerId(int customerId) throws TException, WmCustomerException {
        return wmCustomerBrandService.selectBrandIdListByCustomerId(customerId);
    }

    /**
     * 查询客户绑定品牌信息列表
     * @param customerId
     * @param content
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public WmCustomerBrandListPageData getCustomerBrandList(Integer customerId, String content, Integer pageNo, Integer pageSize) throws TException, WmCustomerException {
        return wmCustomerBrandService.getCustomerBrandList(customerId, content, pageNo, pageSize);
    }

    /**
     * 查询客户下门店绑定的品牌
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public List<WmCustomerBrandBo> getCusPoiBrandByCusId(Integer customerId) throws TException, WmCustomerException {
        return wmCustomerBrandService.getCusPoiBrandByCusId(customerId);
    }

}
