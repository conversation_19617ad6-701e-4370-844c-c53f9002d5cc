package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.settle;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiInfoBo;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.SETTLE_MOON_ADVICE_INFO_V3)
@Service
@Slf4j
public class WmEcontractSettleMoonAdviceInfoPdfMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext,
            EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractSettleMoonAdviceInfoPdfMaker");
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.SETTLE_MOON_ADVICE_INFO_V3;

        EcontractTaskBo taskBo = WmEcontractContextUtil
                .selectByApplyType(originContext, EcontractTaskApplyTypeEnum.SETTLE);

        Map<String, String> pdfMap = generatePdfObject(originContext, taskBo);
        List<Map<String, String>> pdfList = generateAdvicePdfList(originContext, taskBo);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfMetaContent(pdfMap);
        pdfInfoBo.setPdfBizContent(pdfList);
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        return pdfInfoBo;
    }

    /**
     * 生成商家信息结算单-多店列表
     */
    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);

        //抽取一个开钱包的对象-业务规则上允许部分开钱包
        EcontractSettleInfoBo infoBo = null;
        for(EcontractSettleInfoBo temp : settleInfoBoList){
            if(temp.isSupportWallet()){
                infoBo = temp;
                break;
            }
        }

        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("qdbNumber", StringUtils.defaultIfEmpty(infoBo.getQdbNumber(), StringUtils.EMPTY));
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        return pdfMap;
    }

    /**
     * 生成商家信息结算单-多店列表
     */
    private List<Map<String, String>> generateAdvicePdfList(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);
        List<Map<String, String>> pdfList = Lists.newArrayList();

        List<EcontractSettleInfoBo> moonSettleInfoBoList = Lists.newArrayList();
        for(EcontractSettleInfoBo temp : settleInfoBoList){
            if(temp.isSupportWallet()){
                moonSettleInfoBoList.add(temp);
            }
        }

        for (EcontractSettleInfoBo settleInfoBo : moonSettleInfoBoList) {
            for (EcontractPoiInfoBo poiInfoBo : settleInfoBo.getPoiInfoBoList()) {
                CollectionUtils.addIgnoreNull(pdfList, parse(settleInfoBo, poiInfoBo));
            }
        }
        return pdfList;
    }

    private Map<String, String> parse(EcontractSettleInfoBo settleInfoBo, EcontractPoiInfoBo poiInfoBo) {
        Map<String, String> map = Maps.newHashMap();
        map.put("poiName", StringUtils.defaultIfEmpty(poiInfoBo.getName(), StringUtils.EMPTY));
        map.put("poiAddress", StringUtils.defaultIfEmpty(poiInfoBo.getAddress(), StringUtils.EMPTY));
        map.put("accountName", StringUtils.defaultIfEmpty(settleInfoBo.getAccountName(), StringUtils.EMPTY));
        map.put("accountCardNum", StringUtils.defaultIfEmpty(settleInfoBo.getAccountCardNum(), StringUtils.EMPTY));
        map.put("province", StringUtils.defaultIfEmpty(settleInfoBo.getProvince(), StringUtils.EMPTY));
        map.put("city", StringUtils.defaultIfEmpty(settleInfoBo.getCity(), StringUtils.EMPTY));
        map.put("bank", StringUtils.defaultIfEmpty(settleInfoBo.getBank(), StringUtils.EMPTY));
        map.put("branch", StringUtils.defaultIfEmpty(settleInfoBo.getBranch(), StringUtils.EMPTY));
        map.put("financialContact", StringUtils.defaultIfEmpty(settleInfoBo.getFinancialContact(), StringUtils.EMPTY));
        map.put("financialContactPhone", StringUtils.defaultIfEmpty(settleInfoBo.getFinancialContactPhone(), StringUtils.EMPTY));
        return map;
    }



}
