package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.upm.pigeon.invoker.api.UPM;
import com.sankuai.meituan.upm.pigeon.model.RemoteRoleBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * UPM权限适配器
 */
@Slf4j
@Service
public class UpmAuthAdaptorService {

    @Autowired
    private UPM upm;

    public static final String UPM_CLIENT_ID = "xianfu_waimai";


    /**
     * 查询用户拥有的所有upm角色
     *
     * @param userId
     * @return
     */
    public List<RemoteRoleBean> getAllRoles(Integer userId) {
        try {
            List<RemoteRoleBean> roles = upm.retrieveService.getUserRole(UPM_CLIENT_ID, userId);
            log.info("getAllRoles:: userId:{},拥有角色列表:{}", userId, JSON.toJSONString(roles));
            return Lists.newArrayList(roles);
        } catch (Exception e) {
            log.error("调用upm获取角色接口失败,userId:{}", userId, e);
        }
        return Collections.emptyList();
    }


}
