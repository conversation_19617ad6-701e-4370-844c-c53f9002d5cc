package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmPoiAttributesDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmPoiAttributesMapper {
    WmPoiAttributesDO selectByPrimaryKey(@Param("wmPoiId") Long wmPoiId);

    int deleteByPrimaryKey(@Param("wmPoiId") Long wmPoiId);

    int insertSelective(WmPoiAttributesDO record);

    int updateByPrimaryKeySelective(WmPoiAttributesDO record);

    List<WmPoiAttributesDO> selectByWmPoiIds(List<Long> wmPoiIds);

    /**
     * 更新解绑和换绑的次数，如果没有记录则插入一条记录，有则更新解绑和换绑的次数
     *
     * */
    int increaseRebindCountOrInsert(List<Long> wmPoiIds);
}

