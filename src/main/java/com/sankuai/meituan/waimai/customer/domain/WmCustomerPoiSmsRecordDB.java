package com.sankuai.meituan.waimai.customer.domain;

public class WmCustomerPoiSmsRecordDB {
    private Integer id;//主键
    private Integer customerId;//客户ID
    private Long taskId;//任务ID
    private String wmPoiIds;//门店ID(已逗号分隔)
    private Integer taskStatus;//任务状态 1待提交 2等待 3处理中 4处理成功 5处理失败 6 无需签约
    private Integer valid;//删除标识 0删除，1有效
    private Integer ctime;
    private Integer utime;
    /**
     * 短信记录类型 0解绑门店，1预绑定门店
     */
    private byte type;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getWmPoiIds() {
        return wmPoiIds;
    }

    public void setWmPoiIds(String wmPoiIds) {
        this.wmPoiIds = wmPoiIds;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }
}