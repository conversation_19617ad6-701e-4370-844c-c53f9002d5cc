package com.sankuai.meituan.waimai.customer.settle.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.meituan.waimai.customer.service.AutoWireBase;
import com.sankuai.meituan.waimai.customer.util.diff.BeanDiffUtil;
import com.sankuai.meituan.waimai.customer.util.SetUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil;
import com.sankuai.meituan.waimai.settle.WmSettleMsgBo;
import com.sankuai.meituan.waimai.thrift.config.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.constant.WmContractConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.domain.WmContractTairMsg;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class WmSettleMsgSender extends AutoWireBase {
    private static Logger log = LoggerFactory.getLogger(WmSettleMsgSender.class);

    @Resource(name="customerSettleMafkaProducer")
    private MafkaProducer customerSettleMafkaProducer;

    @Resource(name="customerSettlePoiMafkaProducer")
    private MafkaProducer customerSettlePoiMafkaProducer;

    private List<WmSettleMsgBo> settleSendList = new ArrayList<>();

    private List<WmSettleMsgBo> poiSendList = new ArrayList<>();

    private List<WmSettleMsgBo> contractPoiList = new ArrayList<>();

//    private static final String CUSTOMERSETTLE_TO_BLUMBLEBEE_KEY = "waimai.customer.settle.change";

//    private static final String CUSTOMERSETTLEPOI_TO_BLUMBLEBEE_KEY = "waimai.customer.settlepoi.change";

    /**
     * 上面三类消息发给结算,每个消息对应一个队列
     *
     *  增加合同缓存后,对上述三类消息做抽象封装,发给新的队列,由合同订阅,更新缓存
     */
//    private static final String CONTRACTCACHE_TO_BLUMBLEBEE_KEY = "waimai.contracttair";

    public static final String CONTRACT_TARI_MSG_KEY  = "waimai.contracttair.msg.version";

    public static final String SEND_SETTLECHANGEMSG_RABBITMQ_OPEN = "send_settleChangeMsg_rabbitmq_open";

    public static final String SEND_SETTLECHANGEMSG_MAFKA_OPEN = "send_settleChangeMsg_mafka_open";

    /**
     * 发送结算变更消息
     * @param settleMsgBo
     */
    private void sendSettleMsg(WmSettleMsgBo settleMsgBo) {
        log.info("mq 同步结算数据,同步结算:" + JSON.toJSONString(settleMsgBo));

        JSONObject jo = new JSONObject();
        jo.put("wmContractId", settleMsgBo.getWmContractId());
        jo.put("wmCustomerId", settleMsgBo.getWmCustomerId());
        jo.put("wmSettleId", settleMsgBo.getWmSettleId());
        jo.put("type", settleMsgBo.getType().getValue());

//        if(ConfigUtilAdapter.getBoolean(SEND_SETTLECHANGEMSG_RABBITMQ_OPEN,true)){
//            producer.send(CUSTOMERSETTLE_TO_BLUMBLEBEE_KEY, jo);
//        }

        if(ConfigUtilAdapter.getBoolean(SEND_SETTLECHANGEMSG_MAFKA_OPEN,true)){
            try {
                customerSettleMafkaProducer.sendMessage(jo.toJSONString());
            } catch (Exception e) {
                log.error("customerSettleMafkaProducer sendMsg exception");
            }
        }

        log.info("mq 同步结算数据,实际数据:{}",jo.toJSONString());

//        try {
//            WmContractTairMsg wmContractTairMsg = new WmContractTairMsg(
//                    WmContractConstant.WmContractTable.WM_SETTLE_AUDITED,
//                    settleMsgBo.getType().getValue(),
//                    JSONObject.toJSONString(jo),ConfigUtilAdapter.getInt(CONTRACT_TARI_MSG_KEY, 1));
//            log.info("mq 更新结算数据缓存:" + wmContractTairMsg);
//            producer.send(CONTRACTCACHE_TO_BLUMBLEBEE_KEY, JSONObject.toJSON(wmContractTairMsg));
//            log.info("mq 更新结算数据缓存完成:" + wmContractTairMsg);
//        }catch (Exception e) {
//            log.error("mq 更新结算数据缓存失败 {}", settleMsgBo,e);
//        }
    }

    /**
     * 发送poi变更消息
     * @param settleMsgBo
     */
    private void sendPoiMsg(WmSettleMsgBo settleMsgBo) {
        log.info("mq 同步结算数据,同步商家:" + JSON.toJSONString(settleMsgBo));

        JSONObject jo = new JSONObject();
        jo.put("wmContractId", settleMsgBo.getWmContractId());
        jo.put("wmCustomerId", settleMsgBo.getWmCustomerId());
        jo.put("wmSettleId", settleMsgBo.getWmSettleId());
        jo.put("wmPoiId", settleMsgBo.getWmPoiId());
        jo.put("type", settleMsgBo.getType().getValue());

//        if(ConfigUtilAdapter.getBoolean(SEND_SETTLECHANGEMSG_RABBITMQ_OPEN,true)){
//            producer.send(CUSTOMERSETTLEPOI_TO_BLUMBLEBEE_KEY, jo);
//        }

        if(ConfigUtilAdapter.getBoolean(SEND_SETTLECHANGEMSG_MAFKA_OPEN,true)){
            try {
                customerSettlePoiMafkaProducer.sendMessage(jo.toJSONString());
            } catch (Exception e) {
                log.error("customerSettlePoiMafkaProducer sendMsg exception");
            }
        }

        log.info("mq 同步结算商家数据,实际数据:{}",jo.toJSONString());

//        /********************更新合同缓存*************************/
//        try {
//            WmContractTairMsg wmContractTairMsg = new WmContractTairMsg(
//                    WmContractConstant.WmContractTable.WM_POI_SETTLE_AUDITED,
//                    settleMsgBo.getType().getValue(),
//                    JSONObject.toJSONString(jo),ConfigUtilAdapter.getInt(CONTRACT_TARI_MSG_KEY, 1));
//
//            log.info("mq 更新商家数据缓存:" + wmContractTairMsg);
//            producer.send(CONTRACTCACHE_TO_BLUMBLEBEE_KEY, JSONObject.toJSON(wmContractTairMsg));
//            log.info("mq 更新结算数据缓存完成:" + wmContractTairMsg);
//        }catch (Exception e) {
//            log.error("mq 更新商家数据缓存失败 {}", settleMsgBo,e);
//        }
    }

    public void addSettleMsg(WmSettleMsgBo wmSettleMsgBo) {
        settleSendList.add(wmSettleMsgBo);
    }


    /**
     * 发送poi变更消息（settle维度变更消息）
     * @param settleMsgBo
     * @param auditedList
     * @param unauditedList
     */
    public void addPoiSettleMsg(WmSettleMsgBo settleMsgBo, List<Integer> auditedList, List<Integer> unauditedList) {
        List<Integer> addList = BeanDiffUtil.genAddList(unauditedList, auditedList);//当前结算新增商家
        List<Integer> deleteList = BeanDiffUtil.genDeleteList(unauditedList, auditedList);//当前结算删除商家
        for (Integer wmPoiId:addList) {
            WmSettleMsgBo wmSettleMsgBo = new WmSettleMsgBo();
            wmSettleMsgBo.setWmContractId(settleMsgBo.getWmContractId());
            wmSettleMsgBo.setWmCustomerId(settleMsgBo.getWmCustomerId());
            wmSettleMsgBo.setWmSettleId(settleMsgBo.getWmSettleId());
            wmSettleMsgBo.setType(WmSettleMsgBo.OpType.CREATE);
            wmSettleMsgBo.setWmPoiId(wmPoiId);
            poiSendList.add(wmSettleMsgBo);
        }
        for (Integer wmPoiId:deleteList) {
            WmSettleMsgBo wmSettleMsgBo = new WmSettleMsgBo();
            wmSettleMsgBo.setWmContractId(settleMsgBo.getWmContractId());
            wmSettleMsgBo.setWmCustomerId(settleMsgBo.getWmCustomerId());
            wmSettleMsgBo.setWmSettleId(settleMsgBo.getWmSettleId());
            wmSettleMsgBo.setType(WmSettleMsgBo.OpType.DELETE);
            wmSettleMsgBo.setWmPoiId(wmPoiId);
            poiSendList.add(wmSettleMsgBo);
        }
    }


    public void addWmSettleMsg(Integer wmCustomerId, List<WmSettle> wmSettleList, List<WmSettleAudited> wmSettleAuditedList,List<Integer> switchWmPoiRelWmSettleIdList) {
        Map<Integer, WmSettle> wmSettleMap = WmSettleTransUtil.transWmSettleList2Map(wmSettleList);
        Map<Integer, WmSettleAudited> wmSettleAuditedMap = WmSettleTransUtil.transWmSettleAuditedList2Map(wmSettleAuditedList);

        Set<Integer> wmSettleIdList = wmSettleMap.keySet();
        Set<Integer> wmSettleAuditedIdList = wmSettleAuditedMap.keySet();

        Set<Integer> addSet = SetUtil.genAddSet(wmSettleIdList, wmSettleAuditedIdList);
        Set<Integer> commonSet = SetUtil.genCommonSet(wmSettleIdList, wmSettleAuditedIdList);
        Set<Integer> deleteSet = SetUtil.gendeleteSet(wmSettleIdList, wmSettleAuditedIdList);

        for (Integer wmSettleId:addSet) {
            WmSettleMsgBo wmSettleMsgBo = new WmSettleMsgBo();
            wmSettleMsgBo.setWmCustomerId(wmCustomerId);
            wmSettleMsgBo.setWmSettleId(wmSettleId);
            wmSettleMsgBo.setType(WmSettleMsgBo.OpType.CREATE);
            settleSendList.add(wmSettleMsgBo);
            addPoiSettleMsg(wmSettleMsgBo, new ArrayList<Integer>(), wmSettleMap.get(wmSettleId).getWmPoiIdList());
        }

        for (Integer wmSettleId:commonSet) {
            if(CollectionUtils.isNotEmpty(switchWmPoiRelWmSettleIdList) && !switchWmPoiRelWmSettleIdList.contains(wmSettleId)){
                continue;
            }
            WmSettleMsgBo wmSettleMsgBo = new WmSettleMsgBo();
            wmSettleMsgBo.setWmCustomerId(wmCustomerId);
            wmSettleMsgBo.setWmSettleId(wmSettleId);
            wmSettleMsgBo.setType(WmSettleMsgBo.OpType.CHANGE);
            settleSendList.add(wmSettleMsgBo);
            addPoiSettleMsg(wmSettleMsgBo, wmSettleAuditedMap.get(wmSettleId).getWmPoiIdList(), wmSettleMap.get(wmSettleId).getWmPoiIdList());
        }

        for (Integer wmSettleId:deleteSet) {
            WmSettleMsgBo wmSettleMsgBo = new WmSettleMsgBo();
            wmSettleMsgBo.setWmCustomerId(wmCustomerId);
            wmSettleMsgBo.setWmSettleId(wmSettleId);
            wmSettleMsgBo.setType(WmSettleMsgBo.OpType.DELETE);
            settleSendList.add(wmSettleMsgBo);
            addPoiSettleMsg(wmSettleMsgBo, wmSettleAuditedMap.get(wmSettleId).getWmPoiIdList(), new ArrayList<Integer>());
        }
    }

    public void send() {
        sendSettleMsg();
        sendPoiMsg();
    }

    private void sendSettleMsg() {
        if (CollectionUtils.isEmpty(settleSendList)) {
            return;
        }

        for (WmSettleMsgBo wmSettleMsgBo:settleSendList) {
            sendSettleMsg(wmSettleMsgBo);
        }
    }

    private void sendPoiMsg() {
        if (CollectionUtils.isEmpty(poiSendList)) {
            return;
        }

        for (WmSettleMsgBo wmSettleMsgBo:poiSendList) {
            sendPoiMsg(wmSettleMsgBo);
        }
    }

    public void sendDeleteWmSettle(List<WmSettleMsgBo> poiSendList) {
        if (CollectionUtils.isEmpty(poiSendList)) {
            return;
        }

        for (WmSettleMsgBo wmSettleMsgBo:poiSendList) {
            sendSettleMsg(wmSettleMsgBo);
        }
    }
}
