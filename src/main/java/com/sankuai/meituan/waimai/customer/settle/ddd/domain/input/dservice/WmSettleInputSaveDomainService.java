package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.dservice;

import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.IntResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleModifyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

public interface WmSettleInputSaveDomainService {

    IntResult saveWmSettleAndCommitWithModifyBo(WmSettleModifyBo wmSettleModifyBo, int opUid,
            String opUname) throws WmCustomerException;


    IntResult saveWmSettleAndCommit(int wmCustomerId, WmSettle wmSettle, int opUid,
            String opUname, boolean isBrandBD, boolean isEffective, boolean isCommit)
            throws WmCustomerException;


    BooleanResult saveOrUpdateWmSettleProtocol(int wmCustomerId, String supplementalUrl,
            String qdbUrl, int opUid, String opName) throws WmCustomerException;

}
