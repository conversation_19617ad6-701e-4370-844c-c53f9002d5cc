package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.impl;

import java.util.List;
import java.util.Set;

import javax.annotation.Nullable;

import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.base.Function;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.WmCustomerTairService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBlackWhiteListService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.service.PhoneCheckService;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.service.PushDXContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.service.PushDXService;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleInputContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.WmSettleInputCheckAtomService;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleCheckService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleDiffService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleSwitchService;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.thrift.constant.WmContractConstant;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.constant.SettleCardType;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmSettleConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleModifyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WmSettleInputCheckAtomServiceImpl implements WmSettleInputCheckAtomService {

    private static final int WM_POI_ID_SIZE = 5;
    @Autowired
    private WmCustomerTairService wmCustomerTairService;
    @Autowired
    private PhoneCheckService phoneCheckService;
    @Autowired
    private PushDXService pushDXService;
    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;
    @Autowired
    private WmSettleService wmSettleService;
    @Autowired
    private WmCustomerBlackWhiteListService wmCustomerBlackWhiteListService;
    @Autowired
    private WmSettleCheckService wmSettleCheckService;
    @Autowired
    private WmPoiClient wmPoiClient;
    @Autowired
    private WmSettleManagerService wmSettleManagerService;
    @Autowired
    private WmSettleSwitchService wmSettleSwitchService;
    @Autowired
    private WmSettleDiffService wmSettleDiffService;

    @Override
    //WmSettleModifyBo
    //OpUid
    //OpUname
    //WmCustomerDB
    public void checkBaseFields(WmSettleInputContext context) throws WmCustomerException {
        WmSettleModifyBo wmSettleModifyBo = context.getWmSettleModifyBo();
        AssertUtil.assertIntegerMoreThan0(wmSettleModifyBo.getWmCustomerId(), "客户id");
        AssertUtil.assertObjectNotNull(wmSettleModifyBo.getWmSettle(), "结算信息");
        AssertUtil.assertIntegerMoreThan0(context.getOpUid(), "操作人id");
        AssertUtil.assertStringNotEmpty(context.getOpUname(), "操作人");
        if(context.getWmCustomerDB() == null){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "客户不存在");
        }
    }

    @Override
    public void checkWallet(WmSettleInputContext context) throws WmCustomerException {

    }


    @Override
    //WmSettleDBList
    //WmSettleModifyBo#WmSettle#isEffective
    //WmSettleDBOld
    //WmSettleAuditedDBOld
    public void checkInputStatus(WmSettleInputContext context) throws WmCustomerException {
        if (CollectionUtils.isEmpty(context.getWmSettleDBList())) {
            return;
        }
        for (WmSettleDB wmSettleDB : context.getWmSettleDBList()) {
            if (!canSave(wmSettleDB.getStatus())) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "当前状态不允许保存");
            }
        }

        int wmSettleId = context.getWmSettleModifyBo().getWmSettle().getId();
        WmSettleDB wmSettleDBOld = context.getWmSettleDBOld();
        WmSettleAuditedDB wmSettleAuditedDBOld = context.getWmSettleAuditedDBOld();

        //校验状态
        if (wmSettleId > 0) {
            //无线下结算数据
            if (wmSettleDBOld == null) {
                //处理线下表数据被删除的场景
                if (wmSettleAuditedDBOld == null) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                            "历史结算信息不存在");
                }
            }
            //有线下结算数据-校验当前状态是否可保存
            else {
                if (!canSave(wmSettleDBOld.getStatus())) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                            "当前状态不允许保存");
                }
            }
        }

        if (context.getWmSettleModifyBo().isEffective()) {
            throw new WmCustomerException(WmContractErrorCodeConstant.AUTHORIZING, "暂不支持正在生效页数据保存");
        }

    }

    private boolean canSave(byte status) {
        return status != WmSettleConstant.SETTLE_STATUS_TO_CONFIRM
                && status != WmSettleConstant.SETTLE_STATUS_TO_AUDIT
                && status != WmSettleConstant.SETTLE_STATUS_WAIT_SIGN;
    }

    /**
     * 结算名重复校验+财务责任人手机号校验
     */
    @Override
    //WmSettleModifyBo
    //WmSettleIdListBySettleName
    //WmSettleIdListBySettleNameAudited
    //WmSettleModifyBo
    public void checkSpecialFields(WmSettleInputContext context) throws WmCustomerException {
        //结算名重复校验
        WmSettleModifyBo wmSettleModifyBo = context.getWmSettleModifyBo();
        WmSettle wmSettle = wmSettleModifyBo.getWmSettle();
        if (wmSettle == null || StringUtils.isEmpty(wmSettle.getName())) {
            //无需校验结算名
        }
        else{
            //20190807 结算名重复校验已废弃，wmSettleIdListBySettleName、wmSettleIdListBySettleNameAudited均为空List
            List<Integer> wmSettleIdListBySettleName = context.getWmSettleIdListBySettleName();
            List<Integer> wmSettleIdListBySettleNameAudited = context
                    .getWmSettleIdListBySettleNameAudited();

            int wmSettleId = wmSettle.getId();

            if (!CollectionUtils.isEmpty(wmSettleIdListBySettleName)) {
                if (wmSettleId == 0 || !wmSettleIdListBySettleName.contains(wmSettle.getId())) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "结算名称重复");
                }
            }

            if (!CollectionUtils.isEmpty(wmSettleIdListBySettleNameAudited)) {
                if (wmSettleId == 0 || !wmSettleIdListBySettleNameAudited.contains(wmSettle.getId())) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "结算名称重复");
                }
            }
        }

        //财务负责人手机号校验
        if (StringUtils.isNotEmpty(wmSettle.getParty_a_finance_phone())) {
            phoneCheckService
                    .checkPhoneNum(Lists.newArrayList(wmSettle.getParty_a_finance_phone()));
        }
    }

    @Override
    //wmSettleModifyBo.SupplementalUrl
    //wmSettleModifyBo.QdbUrl
    //wmSettleModifyBo.WmSettle
    //wmSettleModifyBo.Commit
    //wmSettleModifyBo.Effective
    //WmCustomerDB
    //WmSettleDBList
    public void checkPaperSettle(WmSettleInputContext context) throws WmCustomerException {
        WmSettleModifyBo wmSettleModifyBo = context.getWmSettleModifyBo();
        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        String supplementalUrl = wmSettleModifyBo.getSupplementalUrl();
        String qdbUrl = wmSettleModifyBo.getQdbUrl();
        WmSettle wmSettle = wmSettleModifyBo.getWmSettle();
        List<WmSettleDB> wmSettleDBList = context.getWmSettleDBList();

        //纸质签约客户校验 补充协议不能为空 若有开钱包的结算 钱袋宝协议也不能为空
        if (wmSettleModifyBo.isCommit() && wmCustomerDB.getSignMode() == CustomerSignMode.PAPER
                .getCode()) {
            if (StringUtils.isBlank(supplementalUrl)) {
                AssertUtil.assertStringNotEmpty(supplementalUrl, "补充协议");
            }
            boolean iswallet = false;
            if (wmSettle != null && wmSettle.getCard_type() == SettleCardType.WALLET.getCode()) {
                iswallet = true;
            }
            if (!iswallet) {
                for (WmSettleDB wmSettleDB : wmSettleDBList) {
                    if (wmSettleDB.getCard_type() == SettleCardType.WALLET.getCode()) {
                        iswallet = true;
                        break;
                    }
                }
            }
            if (iswallet && StringUtils.isBlank(qdbUrl)){
                AssertUtil.assertStringNotEmpty(qdbUrl, "钱袋宝协议");
            }
        }
    }

    @Override
    //WmSettleModifyBo.WmSettle
    //OpUid
    public boolean checkPaymentDays(WmSettleInputContext context) throws WmCustomerException {

        WmSettle wmSettleOrigin = context.getWmSettleModifyBo().getWmSettle();
        List<WmSettle> wmSettleList = Lists.newArrayList(wmSettleOrigin);
        int opUid = context.getOpUid();

        // 覆盖输入的isBrandBD
        boolean isBrandBD = WmEmployUtils
                .isUserInOrg(opUid, WmVirtualOrgSourceEnum.DAKEHU.getSource());

        for (WmSettle wmSettle : wmSettleList) {

            //结算类型数据未赋值强校验
            if(wmSettle.getSettle_type() == 0){
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "结算方式未选择");
            }

            if (!isBrandBD && (wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_DATE
                    || wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_LASTDAY)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "大客户BD才可选择 1.固定日期结算 2.固定月末结算");
            }
            if (wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_CIRCLE
                    && !checkSettleTypeCircle(context)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "周期结算必须选择结算周期");
            }
            if (wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_DATE && (
                    wmSettle.getPay_day_of_month() > 28 || wmSettle.getPay_day_of_month() < 1)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "固定日期结算只能选择1~28日");
            }
            if (wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_SELF
                    && wmSettle.getWmPoiIdList().size() > 1) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "多个门店不允许设置商家自提");
            }
        }
        return true;
    }

    private boolean checkSettleTypeCircle(WmSettleInputContext context) throws WmCustomerException {
        WmSettle wmSettle = context.getWmSettleModifyBo().getWmSettle();
        if (wmSettle == null) {
            return false;
        }

        if ((wmSettle.getPay_period_num() == 3 && wmSettle.getPay_period_unit() == 3)//3天
                || (wmSettle.getPay_period_num() == 1 && wmSettle.getPay_period_unit() == 1)//1周
                || (wmSettle.getPay_period_num() == 2 && wmSettle.getPay_period_unit() == 1)//2周
                || (wmSettle.getPay_period_num() == 1 && wmSettle.getPay_period_unit() == 2)) {//1月
            return true;
        }
        if (ConfigUtilAdapter.getBoolean("settle_check_white_list_open", true)
                && wmSettle.getPay_period_num() == 1 && wmSettle.getPay_period_unit() == 3) {
            wmCustomerTairService.checkSettleWhiteList(wmSettle);
            return true;
        }
        return false;
    }

    @Override
    //PushDXContext
    public void checkRelation(WmSettleInputContext context) throws WmCustomerException {
        PushDXContext pushDXContext = context.getPushDXContext();
        pushDXService.checkWmPoiIdListInSettleAndPushDX(pushDXContext);
    }

    @Override
    //customerId
    public void checkWmPoiBelong(WmSettleInputContext context) throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        Set<Long> wmCustomerPoiSet = Sets
                .newHashSet(wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerId));
        wmCustomerPoiSet.addAll(wmSettleSwitchService.getSwitchingWmPoiIdList(wmCustomerId,true));
        List<Long> wmPoiIdList = ObjectUtil.intList2LongList(
                wmSettleService.getOfflineWmPoiIdListByWmCustomerId(wmCustomerId));
        context.setWmCustomerPoiSet(wmCustomerPoiSet);
        context.setWmPoiIdList(wmPoiIdList);

        //校验结算门店附属于客户关联门店
        if (!wmCustomerPoiSet.containsAll(wmPoiIdList)) {
            List<Long> copyWmPoiIdList = Lists.newArrayList(wmPoiIdList);
            copyWmPoiIdList.removeAll(wmCustomerPoiSet);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                    "提交失败。门店id " + StringUtils.join(copyWmPoiIdList, ",") + "不属于该客户");
        }
    }

    @Override
    //WmCustomerPoiSet
    //WmCustomerId
    public void checkBankCardType(WmSettleInputContext context) throws WmCustomerException {
        Set<Long> wmCustomerPoiSet = context.getWmCustomerPoiSet();
        int wmCustomerId = context.getWmCustomerId();

        //校验单店-结算类型是否可选银行卡
        if (wmCustomerPoiSet.size() == 1) {
            int wmPoiId = wmCustomerPoiSet.iterator().next().intValue();
            if (!com.sankuai.meituan.util.ConfigUtilAdapter
                    .getBoolean("CanUseBankCardType_check_open", false)) {
                return;
            }
            WmSettle wmSettle = wmSettleService.getWmSettleBasicByWmCustomerIdAndWmPoiId(
                    wmCustomerId, wmPoiId);
            if (wmSettle != null && wmSettle.getCard_type() == WmContractConstant.CardType.BANK
                    .getIndex()) {
                CustomerBlackWhiteParam param = new CustomerBlackWhiteParam();
                param.setBizId(wmPoiId).setBizType(CustomerConstants.WHITE_LIST_BIZTYPE_SETTLE_CARD)
                        .setType(CustomerConstants.TYPE_WHITE);
                BooleanResult booleanResult = wmCustomerBlackWhiteListService
                        .queryInCustomerBlackWhiteList(param);
                if (!booleanResult.isRes()) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                            "请开通钱包");
                }
            }
        }
    }

    @Override
    //WmCustomerId
    public void checkSettleOpenWallerOrNot(WmSettleInputContext context)
            throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();

        //校验多结算钱包
        //该校验关闭，允许同一客户多个结算开钱包状态不一致
        if (!com.sankuai.meituan.util.ConfigUtilAdapter
                .getBoolean("close_checkSettleIsWalletOrNot", false) && !wmSettleCheckService
                .checkSettleIsWalletOrNot(wmSettleService.getWmSettleByWmCustomerId(wmCustomerId,true),
                        wmCustomerId)) {
            throw new WmCustomerException(
                    CustomerErrorCodeConstants.SETTLE_WALLET_CONSISTENCY_ERROR, "请确认客户内所有门店都开钱包");
        }
    }

    @Override
    public void checkCommit(WmSettleInputContext context) throws WmCustomerException {
        checkWmPoiBelong(context);
        checkBankCardType(context);
        checkSettleOpenWallerOrNot(context);
    }

    @Override
    public void checkCommitStatus(WmSettleInputContext context) throws WmCustomerException {
        byte status = wmSettleService.getSettleStatusByWmCustomerId(context.getWmCustomerId());
        if (!canCommit(status)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不能提交确认");
        }
    }

    private boolean canCommit(byte status) {
        return status != WmSettleConstant.SETTLE_STATUS_TO_CONFIRM
                && status != WmSettleConstant.SETTLE_STATUS_TO_AUDIT
                && status != WmSettleConstant.SETTLE_STATUS_WAIT_SIGN;
    }

    @Override
    //WmCustomerId
    public void checkWmPoiOnlineStatus(WmSettleInputContext context) throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();

        List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerId);
        List<WmPoiDomain> wmPoiDomainList = wmPoiClient.pageGetWmPoiByWmPoiIdList(wmPoiIdList);
        org.apache.commons.collections.CollectionUtils.filter(wmPoiDomainList, new Predicate() {
            @Override
            public boolean evaluate(Object object) {
                WmPoiDomain wmPoiDomain = (WmPoiDomain) object;
                return wmPoiDomain.getValid() == 1;
            }
        });
        List<Long> onlinePoiIdList = Lists
                .transform(wmPoiDomainList, new Function<WmPoiDomain, Long>() {
                    @Nullable
                    @Override
                    public Long apply(@Nullable WmPoiDomain input) {
                        return (long) input.getWmPoiId();
                    }
                });
        List<Long> existSettlePoiIdList = Lists
                .newArrayList(wmSettleManagerService.batchGetSettleWmPoiIdSet(onlinePoiIdList,wmCustomerId));
        onlinePoiIdList.removeAll(existSettlePoiIdList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(onlinePoiIdList)) {
            String errorMsg = String
                    .format("提交失败:门店id %s 未录入结算信息", StringUtils.join(onlinePoiIdList, "、"));
            if (onlinePoiIdList.size() > WM_POI_ID_SIZE) {
                List<Long> notExistSettlePoiIdList = Lists.newArrayList();
                for (int i = 0; i < WM_POI_ID_SIZE; i++) {
                    notExistSettlePoiIdList.add(onlinePoiIdList.get(i));
                }
                errorMsg = String.format("提交失败:门店id %s 等未录入结算信息",
                        StringUtils.join(notExistSettlePoiIdList, "、"));
            }
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, errorMsg);
        }
    }

    @Override
    //WmCustomerId
    public void checkCommitSettleNotNull(WmSettleInputContext context) throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        List<WmSettle> wmSettleList = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId,true);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(wmSettleList)){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "没有结算信息");
        }
    }

    @Override
    public void checkCommitSettleNotNullAndStatus(WmSettleInputContext context)
            throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        List<WmSettle> wmSettleList = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId,true);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(wmSettleList)){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "没有结算信息");
        }
        byte status = wmSettleList.get(0).getStatus();
        if (!canCommit(status)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不能提交确认");
        }
    }

    @Override
    //WmSettleList
    //WmSettleAuditedList
    //SwitchingWmPoiIdListFromSourceCustomer
    public void checkSettleCanEditInCustomerSwitch(WmSettleInputContext context) throws WmCustomerException {
        List<WmSettle> wmSettleList = context.getWmSettleList();
        List<WmSettleAudited> wmSettleAuditedList = context.getWmSettleAuditedList();
        List<Long> switchingWmPoiIdListFromSourceCustomer = context.getSwitchingWmPoiIdListFromSourceCustomer();
        List<Long> wmPoiIdList = wmSettleDiffService.judgeSpecialWmPoiIdListModify(wmSettleList, wmSettleAuditedList,
                switchingWmPoiIdListFromSourceCustomer);
        if (!CollectionUtils.isEmpty(wmPoiIdList)) {
            String errorMsg = "提交失败;门店id" + Joiner.on(",").join(wmPoiIdList) + "即将切换至其他客户，结算信息无法修改。如需修改，请先取消切换客户任务。";
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_SWITCH_CUSTOMER, errorMsg);
        }
    }
}
