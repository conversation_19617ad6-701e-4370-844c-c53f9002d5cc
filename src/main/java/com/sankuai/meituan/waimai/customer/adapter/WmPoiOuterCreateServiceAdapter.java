package com.sankuai.meituan.waimai.customer.adapter;


import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.wdc.poi.flow.action.common.vo.OuterPoiCreateVo;
import com.sankuai.meituan.wdc.poi.flow.action.common.vo.result.OuterPoiCreateResult;
import com.sankuai.meituan.wdc.poi.flow.action.service.PoiOuterCreateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 公海创建线索服务
 * <AUTHOR>
 * @date 2024/05/23
 * @email <EMAIL>
 */
@Service
@Slf4j
public class WmPoiOuterCreateServiceAdapter {

    @Autowired
    private PoiOuterCreateService poiOuterCreateService;

    /**
     * 系统来源-默认值520
     */
    public final int OUTER_POI_SYSTEM_SOURCE = 520;


    /**
     * 新建公海线索信息
     * @param createVo createVo
     * @return OuterPoiCreateResult
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public OuterPoiCreateResult createPoiAsync(OuterPoiCreateVo createVo) throws WmSchCantException {
        try {
            // 设置系统来源
            createVo.setSystemSource(OUTER_POI_SYSTEM_SOURCE);
            log.info("[WmPoiOuterCreateServiceAdapter.createPoiAsync] createVo = {}", JSONObject.toJSONString(createVo));
            OuterPoiCreateResult createResult = poiOuterCreateService.createPoiAsync(createVo);
            log.info("[WmPoiOuterCreateServiceAdapter.createPoiAsync] createResult = {}", JSONObject.toJSONString(createResult));

            return createResult;
        } catch (Exception e) {
            log.error("[WmPoiOuterCreateServiceAdapter.createPoiAsync] Exception. createVo = {}", JSONObject.toJSONString(createVo), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "创建公海线索失败");
        }
    }




}
