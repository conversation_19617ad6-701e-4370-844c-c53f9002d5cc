package com.sankuai.meituan.waimai.customer.service.common;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractBizInfoDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractBizInfoDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-11-11 16:26
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@Slf4j
@Service
public class RepeatObjectService {

    @Autowired
    private WmEcontractBizInfoDBMapper wmEcontractBizInfoDBMapper;

    @Autowired
    private MtriceService mtriceService;

    @Autowired
    private WmEcontractTaskDBMapper wmEcontractTaskDBMapper;

    public static final String ECONTRACT_SIGN_TASK = "ECONTRACT_SIGN_TASK";

    public int insert(WmEcontractBizInfoDB wmEcontractBizInfoDB) {
        return wmEcontractBizInfoDBMapper.insert(wmEcontractBizInfoDB);
    }

    public int insertSelective(WmEcontractBizInfoDB wmEcontractBizInfoDB) {
        return wmEcontractBizInfoDBMapper.insertSelective(wmEcontractBizInfoDB);
    }

    public WmEcontractBizInfoDB selectByBizIdAndBizTypeRT(Long bizId, String bizType) {
        return wmEcontractBizInfoDBMapper.selectByBizIdAndBizTypeRT(bizId, bizType);
    }

    public int batchDeleteByBizIdAndBizType(List<Long> bizIdList, String bizType){
        return wmEcontractBizInfoDBMapper.batchDeleteByBizIdAndBizType(bizIdList, bizType);
    }

    public WmEcontractBizInfoDB genBizInfo(Long bizId, String bizType) {
        WmEcontractBizInfoDB wmEcontractBizInfoDB = new WmEcontractBizInfoDB();
        wmEcontractBizInfoDB.setBizId(bizId);
        wmEcontractBizInfoDB.setBizType(bizType);
        wmEcontractBizInfoDB.setValid(1);//1有效，0无效
        return wmEcontractBizInfoDB;
    }

    public <T> void filterRepeatObject(List<T> bizBoList) {
        log.info("filterRepeatObject before filter:{}", JSON.toJSONString(bizBoList));
        if (CollectionUtils.isEmpty(bizBoList)) {
            return;
        }
        Iterator<T> i = bizBoList.iterator();
        while (i.hasNext()) {
            String bizType = StringUtils.EMPTY;
            Long bizId = 0L;
            T bizBo = i.next();
            if (bizBo instanceof EcontractTaskBo) {
                EcontractTaskBo taskBo = (EcontractTaskBo) bizBo;
                bizType = ECONTRACT_SIGN_TASK;
                bizId = taskBo.getId();
            }
            if (StringUtils.isEmpty(bizType) || bizId == 0L) {
                log.error("bizInfo异常，停止写入防重表,bizInfo:{}", JSON.toJSONString(i));
                continue;
            }
            WmEcontractBizInfoDB bizInfoDB = genBizInfo(bizId, bizType);
            try {
                WmEcontractBizInfoDB existInfo = selectByBizIdAndBizTypeRT(bizId, bizType);
                if (existInfo == null) {
                    insertSelective(bizInfoDB);
                } else {
                    i.remove();
                    log.info("任务bizType:{},bizId:{}已存在, 不写入防重表", bizType, bizId);
                    mtriceService.metricDuplicateSign(bizType);
                }
            } catch (DuplicateKeyException e) {
                i.remove();
                log.error("重复任务bizType:{},bizId:{}, 写入防重表失败", bizType, bizId);
                mtriceService.metricDuplicateSign(bizType);
            } catch (Exception e) {
                Cat.logMetricForCount("写入防重表异常");
                String taskType = wmEcontractTaskDBMapper.queryTaskTypeById(bizId);
                log.error("重复任务bizType:{},bizId:{},taskType:{}, 写入防重表系统异常，可能产生重复任务，请及时关注", bizType, bizId, taskType, e);
            }
        }
        log.info("filterRepeatObject after filter:{}", JSON.toJSONString(bizBoList));
    }

    public void deleteRepeatObject(List<Long> bizIdList, String bizType) {
        log.info("deleteRepeatObject bizIdList:{}, bizType:{}", JSON.toJSONString(bizIdList), bizType);
        int updateNum = batchDeleteByBizIdAndBizType(bizIdList,bizType);
        log.info("deleteRepeatObject updateNum:{}", updateNum);
    }

    public boolean repeatGray(Integer wmCustomerId) {
        if (MccConfig.repeatObjectSwitch()) {
            if (StringUtils.isNotEmpty(MccConfig.repeatGrayCustomerList())) {
                List<String> grayCustomerIdList = Lists.newArrayList(MccConfig.repeatGrayCustomerList().split(","));
                return grayCustomerIdList.contains(wmCustomerId.toString());
            }
            return wmCustomerId % 100 <= MccConfig.repeatGrayProportion();
        }
        return false;
    }

}
