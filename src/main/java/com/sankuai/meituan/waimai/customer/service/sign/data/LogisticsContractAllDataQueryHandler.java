package com.sankuai.meituan.waimai.customer.service.sign.data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;


/**
 * Created by lixuepeng on 2023/5/23
 */
@Service
@Slf4j
public class LogisticsContractAllDataQueryHandler implements EcontractDataQueryHandler<EcontractDeliveryInfoBo> {

    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;

    @Override
    public EcontractDataSourceEnum sorceEnum() {
        return EcontractDataSourceEnum.LOGISTICS_CONTRACT_ALL;
    }

    @Override
    public Map<Long, EcontractDeliveryInfoBo> queryData(Map<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> map, long manualBatchId) throws WmCustomerException {
        // 校验对应来源的门店数据是否正确
        if (CollectionUtils.isEmpty(map.get(EcontractDataSourceEnum.LOGISTICS_CONTRACT_ALL))) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "新结构数据来源无对应门店数据");
        }
        // 获取门店ID列表
        List<EcontractDataPoiBizBo> econtractDataPoiBizBoList = map.get(EcontractDataSourceEnum.LOGISTICS_CONTRACT_ALL);
        // 根据门店列表获取签约数据
        String data = wmLogisticsContractThriftServiceAdapter.getSignDataMultiPoiWithRetry(econtractDataPoiBizBoList, false);
        // 判断签约数据是否合法
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(data, EcontractBatchDeliveryInfoBo.class);
        if (CollectionUtils.isEmpty(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "获取到的新结构的签约数据不合法");
        }
        // 组装返回数据
        Map<Long, EcontractDeliveryInfoBo> resultMap = new HashMap<>();
        for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            resultMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), deliveryInfoBo);
        }
        return resultMap;
    }
}
