package com.sankuai.meituan.waimai.customer.service.customer;

import static com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil.PDF_MAPPING;


import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.TairLock;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.TairLockGrayPercentKeyEnum;
import com.sankuai.meituan.waimai.customer.constant.TairLockGroup;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiSwitchOperateTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractAgentService;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindSignNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindSignNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnbindConfirmBo;
import com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.flow.BindSignNoticeFlowService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.flow.UnBindSignNoticeFlowService;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService;
import com.sankuai.meituan.waimai.customer.service.gray.CustomerPoiRelFlowGrayService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleExtraInfo;
import com.sankuai.meituan.waimai.customer.settle.service.adapter.MerchantSettleQueryAdapter;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerSmsTypeEnum;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;

import com.sankuai.meituan.waimai.customer.domain.*;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpPoiService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.util.GrayUtil;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.WmPoiLogisticsInfoRequestDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.service.WmPoiLogisticsAbilityThriftService;

import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import com.sankuai.meituan.waimai.channel.beacon.lib.enums.CustomerModuleStateEnum;
import com.sankuai.meituan.waimai.customer.adapter.StateCenterAdapter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Function;
import com.google.common.base.Joiner;
import com.google.common.base.MoreObjects;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractVersionService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.pack.check.WmEcontractPackRelContractCheckService;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleBankService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.WmContractVersionUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiSwitchTaskTypeEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmPoiLogisticsInfoForPDF;
import com.sankuai.meituan.waimai.poilogistics.thrift.exception.WmPoiLogisticsException;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsInfoForPDFThriftService;
import com.sankuai.meituan.waimai.thrift.agent.service.WmCommercialAgentInfoThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractConstant;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;

@Service
public class WmCustomerPoiService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerPoiService.class);


    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    @Autowired
    private StateCenterAdapter stateCenterAdapter;

    @Autowired
    private WmCustomerKpPoiService wmCustomerKpPoiService;

    @Autowired
    private WmSettleManagerService wmSettleManagerService;

    @Autowired
    private MerchantSettleQueryAdapter merchantSettleQueryAdapter;

    @Autowired
    private WmEmployService.Iface wmEmployService;
    @Autowired
    private WmSettleService wmSettleService;
    @Autowired
    private WmSettleBankService wmSettleBankService;
    @Autowired
    private WmContractVersionService wmContractVersionService;
    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;
    @Autowired
    private WmContractService wmContractService;
    @Autowired
    private WmPoiLogisticsInfoForPDFThriftService.Iface wmPoiLogisticsInfoForPDFThriftService;
    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmPoiSwitchThriftService wmPoiSwitchThriftService;

    @Autowired
    private WmCustomerComplianceService wmCustomerComplianceService;

    @Autowired
    private WmEcontractPackRelContractCheckService wmEcontractPackRelContractCheckService;

    @Autowired
    WmContractAgentService wmContractAgentService;

    @Autowired
    private WmPoiLogisticsAbilityThriftService wmPoiLogisticsAbilityThriftService;

    @Autowired
    private IWmCustomerRealService wmLeafCustomerRealService;

    @Autowired
    private WmCustomerSwitchService wmCustomerSwitchService;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private CustomerPoiBindService customerPoiBindService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private CustomerPoiUnBindService customerPoiUnBindService;

    @Autowired
    private CustomerPoiRelFlowGrayService flowGrayService;

    @Autowired
    private BindSignNoticeFlowService bindSignNoticeFlowService;

    @Autowired
    private UnBindSignNoticeFlowService unBindSignNoticeFlowService;

    /**
     * 门店数量
     */
    private static final int POI_SIZE = 500;

    private static final int POI_BATCH_QUERY_SIZE = 300;
    /**
     * 配送分隔符
     */
    private static final String SEPARATOR_DELIVERY_REPLACE = "$$$";
    private static final String SEPARATOR_DELIVERY = "#";
    public static final String SUPPORT_MARK = "hasSupport";


    Splitter splitter = Splitter.on(CustomerConstants.SPLIT_SYMBOL).trimResults();


    /**
     * 根据客户ID获取绑定门店数量
     *
     * @param customerId
     * @return
     */
    public Integer countCustomerPoi(Integer customerId) {
        return wmCustomerPoiDBMapper.countCustomerPoi(customerId);
    }

    /**
     * 根据客户ID列表获取绑定门店数量
     *
     * @param
     * @return
     */
    public Map<Integer, WmCustomerListDB> countCustomerPoiList(List<Integer> customerIds) {
        List<WmCustomerListDB> wmCustomerListBoList = wmCustomerPoiDBMapper.countCustomerPoiList(customerIds);
        Map<Integer, WmCustomerListDB> customerListToMap =
                Maps.uniqueIndex(wmCustomerListBoList, new Function<WmCustomerListDB, Integer>() {
                    @Nullable
                    @Override
                    public Integer apply(@Nullable WmCustomerListDB input) {
                        return input.getId();
                    }
                });
        return customerListToMap;
    }

    /**
     * 同步解绑客户和门店关联
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     * @throws WmServerException
     */
    public void customerUnBindPoiForSync(Integer customerId, Set<Long> wmPoiIdSet, Integer opUid, String opName, WmCustomerPoiOplogSourceTypeEnum sourceTypeEnum)
            throws TException, WmCustomerException {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        // 过滤门店ID中没有绑定客户的门店
        List<Long> tmpWmPoiIdList = Lists.newArrayList(wmPoiIdSet);
        Map<Long, WmCustomerPoiDB> poiIdAndDbMap = getCusPoiMap(customerId);
        Set<Long> bindPoiIdSet = poiIdAndDbMap.keySet();
        if (!CollectionUtils.isEmpty(bindPoiIdSet)) {
            Iterator<Long> iterable = wmPoiIdSet.iterator();
            while (iterable.hasNext()) {
                Long wmPoiId = iterable.next();
                if (!bindPoiIdSet.contains(wmPoiId)) {
                    iterable.remove();
                }
            }
        } else {
            logger.info("customerId = {} 没有绑定门店", customerId);
            return;
        }
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            logger.info("customerId = {} 没有绑定门店", customerId);
            int grayPercent = MccCustomerConfig.getDoNotDealComplianceForCustomerUnbindPoiGrayPercent();
            if (GrayUtil.genRandomInt() > grayPercent) {
                logger.info("同步解绑客户和门店关联-处理支付合规 customerId = {}, tmpWmPoiIdList = {}", customerId, JSON.toJSONString(tmpWmPoiIdList));
                wmCustomerComplianceService.dealComplianceForCustomerUnbindPoi(customerId, tmpWmPoiIdList);
            }
            wmCustomerKpPoiService.deleteByCustomerIdAndWmPoiIdList(customerId, tmpWmPoiIdList);
            return;
        }
        Set<Long> cancelUnbindTaskSet = getCancelUnbindTaskSet(customerId, wmPoiIdSet, poiIdAndDbMap);
        for (Long taskId : cancelUnbindTaskSet) {
            try {
                cancelUnBind(customerId, taskId, opUid, opName);
            } catch (Exception e) {
                logger.error("取消签约任务失败 msg：" + e.getMessage(), e);
            }
        }
        logger.info("解绑门店wmPoiIdSet={}", JSONObject.toJSONString(wmPoiIdSet));

        boolean isGrayNew = wmCustomerGrayService.isGrayNewCustomerUnBindPoi(CustomerPoiUnBindTypeEnum.AGENT_SWITCH_CUSTOMER_UNBIND, customerId);
        if (isGrayNew) {
            customerPoiUnBindService.agentSwitchUnBind(customerId, wmPoiIdSet, opUid, opName);
        } else {
            WmCustomerPoiAggre.Factory.make().unbindCustomerPoi(wmPoiIdSet, customerId, opUid, opName, WmCustomerOplogBo.OpType.UPDATE,
                    CustomerConstants.CUSTOMER_LOG_TEMPLATE_POI_UNBIND, CustomerMQEventEnum.CUSTOMER_UNBIND_POI, sourceTypeEnum);
        }
    }

    private Set<Long> getCancelUnbindTaskSet(Integer customerId, Set<Long> wmPoiIdSet, Map<Long, WmCustomerPoiDB> poiIdAndDbMap) {
        Map<Long, Long> poiIdAndTaskIdMap = getPoiIdAndTaskIdMap(customerId);
        Set<Long> cancelUnbindTaskSet = Sets.newHashSet();
        for (Long poiId : wmPoiIdSet) {
            WmCustomerPoiDB poiDB = poiIdAndDbMap.get(poiId);
            if (poiDB != null && CustomerConstants.IS_UNBINDING_YES == poiDB.getIsUnbinding()) {
                cancelUnbindTaskSet.add(poiIdAndTaskIdMap.get(poiId));
            }
        }
        return cancelUnbindTaskSet;
    }

    private ImmutableMap<Long, WmCustomerPoiDB> getCusPoiMap(Integer customerId) {
        List<WmCustomerPoiDB> bindPoiList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(customerId);
        return Maps.uniqueIndex(MoreObjects.firstNonNull(bindPoiList, Lists.<WmCustomerPoiDB>newArrayList()), new Function<WmCustomerPoiDB, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable WmCustomerPoiDB input) {
                return input.getWmPoiId();
            }
        });
    }

    private Map<Long, Long> getPoiIdAndTaskIdMap(Integer customerId) {
        List<WmCustomerPoiSmsRecordDB> wmCustomerPoiSmsRecordDBS = wmCustomerPoiSmsRecordMapper.selectSmsRecordListByCustomerId(customerId);
        Map<Long, Long> poiIdAndTaskIdMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(wmCustomerPoiSmsRecordDBS)) {
            return poiIdAndTaskIdMap;
        }
        for (WmCustomerPoiSmsRecordDB recordDB : wmCustomerPoiSmsRecordDBS) {
            if (StringUtils.isEmpty(recordDB.getWmPoiIds())) {
                continue;
            }
            List<String> poiIds = splitter.splitToList(recordDB.getWmPoiIds());
            for (String poiId : poiIds) {
                poiIdAndTaskIdMap.put(Long.valueOf(poiId), Long.valueOf(recordDB.getTaskId()));
            }
        }
        return poiIdAndTaskIdMap;
    }

    /**
     * 确认解绑-短信签约回调
     *
     * @param taskId
     * @throws TException
     * @throws WmCustomerException
     */
    @Deprecated
    private void confirmUnbind(Long taskId) throws TException, WmCustomerException, WmServerException {
        logger.info("确认解绑开始,task={}", taskId);
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskId(taskId);
        if (wmCustomerPoiSmsRecordDB == null) {
            logger.error("未找到待确认解绑的任务 taskId={}", taskId);
            throw new WmCustomerException(500, "未找到待确认解绑的任务");
        }
        Integer customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        String wmPoiIds = wmCustomerPoiSmsRecordDB.getWmPoiIds();
        Set<Long> wmPoiIdSet = convertWmPoiIds(wmPoiIds);
        WmCustomerPoiAggre.Factory.make().unbindCustomerPoiForProcessCallBack(wmPoiIdSet, customerId, 0, CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT,
                WmCustomerOplogBo.OpType.CHANGESTATUS, CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_CONFIRM_UNBIND,
                CustomerMQEventEnum.CUSTOMER_UNBIND_POI, WmCustomerPoiOplogSourceTypeEnum.CONFIRM_UNBIND, wmCustomerPoiSmsRecordDB.getId());
        logger.info("确认解绑结束,task={}", taskId);
    }

    /**
     * 商家拒绝解绑
     *
     * @param taskId
     * @throws TException
     * @throws WmCustomerException
     */
    @Deprecated
    private void rejectUnbind(Long taskId) throws TException, WmCustomerException {
        logger.info("拒绝解绑开始,task={}", taskId);
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB =
                wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskId(taskId);
        Integer customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        String wmPoiIds = wmCustomerPoiSmsRecordDB.getWmPoiIds();
        Set<Long> wmPoiIdSet = convertWmPoiIds(wmPoiIds);
        wmCustomerPoiRelService.rejectUnBind(wmPoiIdSet, customerId, null);
        wmCustomerService.insertCustomerOpLog(customerId, 0, CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT,
                WmCustomerOplogBo.OpType.CHANGESTATUS,
                String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_REJECT_UNBIND,
                        StringUtils.join(wmPoiIdSet, ",")));
        logger.info("拒绝解绑结束,task={}", taskId);
    }

    /**
     * 取消解绑-客户任务列表取消回调
     *
     * @param customerId
     * @param taskId
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @TairLock(group = TairLockGroup.OLD_CUSTOMER_CONFIRM_OPERATE, seedExp = "taskId", grayKey = "taskId", grayPercentKey = TairLockGrayPercentKeyEnum.OLD_CUSTOMER_CONFIRM_OPERATE)
    public void cancelUnBind(Integer customerId, Long taskId, Integer opUid, String opName)
            throws TException, WmCustomerException {
        logger.info("客户取消解绑门店,customerId={},taskId={},opUid={},opName={}", customerId, taskId, opUid, opName);
        VersionCheckUtil.versionCheck(customerId, 0);
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskIdDoingRT(taskId);
        if (wmCustomerPoiSmsRecordDB == null || wmCustomerPoiSmsRecordDB.getType() != WmCustomerSmsTypeEnum.OLD_CUSTOMER_UNBIND.getCode()) {
            logger.error("客户取消解绑门店未找到对应的任务,customerId={},taskId={},opUid={},opName={}", customerId, taskId, opUid, opName);
            return;
        }
        String wmPoiIds = wmCustomerPoiSmsRecordMapper.selectWmPoiIdsByTaskId(taskId);
        Set<Long> wmPoiIdSet = convertWmPoiIds(wmPoiIds);
        wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.CANCEL.getType(), taskId);
        wmEcontractSignBzService.cancelSign(taskId, false);
        // 基于场景分析短信签约任务来源
        Long switchTaskId = wmCustomerSwitchService.checkFromSwitchUnBind(wmCustomerPoiSmsRecordDB);
        if (switchTaskId != null) {
            wmCustomerSwitchService.unBind(new WmCustomerPoiSwitchBO(switchTaskId, customerId, wmPoiIdSet, opUid, opName,
                    WmCustomerPoiSwitchOperateTypeEnum.UNBIND_TASK_CANCEL));
        } else {
            boolean isGrayNew = wmCustomerGrayService.isGrayNewCustomerUnBindPoi(CustomerPoiUnBindTypeEnum.BD_CANCEL_UNBIND, customerId);
            if (isGrayNew) {
                customerPoiUnBindService.cancelUnBind(new WmCustomerPoiUnbindConfirmBo(customerId, wmPoiIdSet,
                        opUid, opName, CustomerPoiUnBindTypeEnum.BD_CANCEL_UNBIND,
                        null, wmCustomerPoiSmsRecordDB.getId()));
            } else {
                wmCustomerPoiRelService.rejectUnBind(wmPoiIdSet, customerId, null);
                wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                        String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_CANCEL_UNBIND, wmPoiIds));
            }
        }
    }

    /**
     * 强制解绑-客户-任务列表触发
     *
     * @param customerId
     * @param taskId
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @TairLock(group = TairLockGroup.OLD_CUSTOMER_CONFIRM_OPERATE, seedExp = "taskId", grayKey = "taskId", grayPercentKey = TairLockGrayPercentKeyEnum.OLD_CUSTOMER_CONFIRM_OPERATE)
    public void forceUnbind(Integer customerId, Long taskId, Integer opUid, String opName)
            throws TException, WmCustomerException {
        logger.info("强制解绑,customerId={},taskId={},opUid={},opName={}", customerId, taskId, opName);
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskIdDoingRT(taskId);
        if (wmCustomerPoiSmsRecordDB == null || wmCustomerPoiSmsRecordDB.getType() != WmCustomerSmsTypeEnum.OLD_CUSTOMER_UNBIND.getCode()) {
            logger.error("未找到待确认解绑的任务 taskId={}", taskId);
            throw new WmCustomerException(500, "未找到待确认解绑的任务");
        }
        String wmPoiIds = wmCustomerPoiSmsRecordMapper.selectWmPoiIdsByTaskId(taskId);
        Set<Long> wmPoiIdSet = convertWmPoiIds(wmPoiIds);
        wmEcontractSignBzService.cancelSign(taskId, WmEcontractBatchConstant.FORCE_UNBIND, false);
        //取消签约失败抛异常导致流程中断，取消签约成功后更新客户门店短信记录表
        wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.SUCCESS.getType(), taskId);
        // 基于场景分析短信签约任务来源
        Long switchTaskId = wmCustomerSwitchService.checkFromSwitchUnBind(wmCustomerPoiSmsRecordDB);
        if (switchTaskId != null) {
            wmCustomerSwitchService.unBind(new WmCustomerPoiSwitchBO(switchTaskId, customerId, wmPoiIdSet, opUid, opName,
                    WmCustomerPoiSwitchOperateTypeEnum.UNBIND_TASK_FORCE));
        } else {
            customerPoiUnBindService.confirmUnBind(new WmCustomerPoiUnbindConfirmBo(customerId, wmPoiIdSet,
                    opUid, opName, CustomerPoiUnBindTypeEnum.BD_FORCE_UNBIND,
                    WmCustomerPoiOplogSourceTypeEnum.FORCE_UNBIND, wmCustomerPoiSmsRecordDB.getId()));
        }
    }

    /**
     * KP确认门店关系解绑短信回调
     *
     * @param econtractCallbackBo
     * @throws TException
     * @throws WmCustomerException
     */
    @TairLock(group = TairLockGroup.OLD_CUSTOMER_CONFIRM_OPERATE, seedExp = "econtractCallbackBo.taskId", grayKey = "econtractCallbackBo.taskId", grayPercentKey = TairLockGrayPercentKeyEnum.OLD_CUSTOMER_CONFIRM_OPERATE)
    public void customerPoiUnbindCallBack(EcontractCallbackBo econtractCallbackBo)
            throws TException, WmCustomerException {
        try {
            WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskIdDoingRT(econtractCallbackBo.getTaskId());
            if (wmCustomerPoiSmsRecordDB == null || wmCustomerPoiSmsRecordDB.getType() != WmCustomerSmsTypeEnum.OLD_CUSTOMER_UNBIND.getCode()) {
                logger.warn("KP确认门店关系解绑未找到对应的任务,econtractCallbackBo={}", JSONObject.toJSONString(econtractCallbackBo));
                return;
            }

            //回调通知结果非成功、失败则不关注
            if (econtractCallbackBo.getState() != EcontractTaskStateEnum.SUCCESS && econtractCallbackBo.getState() != EcontractTaskStateEnum.FAIL) {
                logger.info("customerPoiUnbindCallBack,解绑回调通知非成功或失败状态不处理,econtractCallbackBo={}", JSON.toJSONString(econtractCallbackBo));
                return;
            }
            logger.info("KP确认门店关系解绑短信回调,econtractCallbackBo={}", JSONObject.toJSONString(econtractCallbackBo));
            // 基于场景分析短信签约任务来源
            Long switchTaskId = wmCustomerSwitchService.checkFromSwitchUnBind(wmCustomerPoiSmsRecordDB);
            String wmPoiIds = wmCustomerPoiSmsRecordDB.getWmPoiIds();
            Set<Long> wmPoiIdSet = convertWmPoiIds(wmPoiIds);
            if (switchTaskId != null) {
                wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(econtractCallbackBo.getState().getType(),
                        econtractCallbackBo.getTaskId());
                if (econtractCallbackBo.getState() == EcontractTaskStateEnum.SUCCESS) {
                    wmCustomerSwitchService.unBind(new WmCustomerPoiSwitchBO(switchTaskId, wmCustomerPoiSmsRecordDB.getCustomerId(),
                            wmPoiIdSet, 0, "商家处理", WmCustomerPoiSwitchOperateTypeEnum.UNBIND_SMS_CONFIRM));
                } else if (econtractCallbackBo.getState() == EcontractTaskStateEnum.FAIL) {
                    wmCustomerSwitchService.unBind(new WmCustomerPoiSwitchBO(switchTaskId, wmCustomerPoiSmsRecordDB.getCustomerId(),
                            wmPoiIdSet, 0, "商家处理", WmCustomerPoiSwitchOperateTypeEnum.UNBIND_SMS_REJECT));
                }
            } else {
                int customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
                //命中灰度
                if (flowGrayService.hitPoiUnBindCustomerFlowGray(wmCustomerPoiSmsRecordDB.getCustomerId())) {
                    //走新流程解绑签约回调
                    unBindSignNoticeFlowService.signUnBindNoticeFlow(wmCustomerPoiSmsRecordDB, econtractCallbackBo.getState().getType());
                } else {
                    wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(econtractCallbackBo.getState().getType(),
                            econtractCallbackBo.getTaskId());
                    if (econtractCallbackBo.getState() == EcontractTaskStateEnum.SUCCESS) {
                        customerPoiUnBindService.confirmUnBind(new WmCustomerPoiUnbindConfirmBo(customerId, wmPoiIdSet,
                                0, CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT, CustomerPoiUnBindTypeEnum.CONFIRM_UNBIND,
                                WmCustomerPoiOplogSourceTypeEnum.CONFIRM_UNBIND, wmCustomerPoiSmsRecordDB.getId()));
                    } else if (econtractCallbackBo.getState() == EcontractTaskStateEnum.FAIL) {
                        customerPoiUnBindService.cancelUnBind(new WmCustomerPoiUnbindConfirmBo(customerId, wmPoiIdSet,
                                0, CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT, CustomerPoiUnBindTypeEnum.CONFITM_CANCEL_UNBIND,
                                null, wmCustomerPoiSmsRecordDB.getId()));
                    }
                }
            }
        } catch (Exception e) {
            logger.error("KP确认门店关系解绑短信回调失败 econtractCallbackBo={}", JSONObject.toJSONString(econtractCallbackBo), e);
        }
    }


    /**
     * 门店与客户预绑定,签约确认回调处理
     *
     * @param econtractCallbackBo
     * @throws TException
     * @throws WmCustomerException
     */
    @TairLock(group = TairLockGroup.NEW_CUSTOMER_CONFIRM_OPERATE, seedExp = "econtractCallbackBo.taskId", grayKey = "econtractCallbackBo.taskId", grayPercentKey = TairLockGrayPercentKeyEnum.NEW_CUSTOMER_CONFIRM_OPERATE)
    public void customerPoiPreBindCallBack(EcontractCallbackBo econtractCallbackBo) throws TException, WmCustomerException {
        logger.info("customerPoiPreBindCallBack,预绑定回调通知结果,econtractCallbackBo={}", JSON.toJSONString(econtractCallbackBo));
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskIdDoingRT(econtractCallbackBo.getTaskId());
        if (wmCustomerPoiSmsRecordDB == null || wmCustomerPoiSmsRecordDB.getType() != WmCustomerSmsTypeEnum.NEW_CUSTOMER_BIND.getCode()) {
            logger.error("KP确认门店预绑定未找到对应的任务,econtractCallbackBo={}", JSONObject.toJSONString(econtractCallbackBo));
            return;
        }

        try {
            //回调通知的状态非成功、失败或取消 则不需要关注
            if (econtractCallbackBo.getState() != EcontractTaskStateEnum.SUCCESS
                    && econtractCallbackBo.getState() != EcontractTaskStateEnum.FAIL
                    && econtractCallbackBo.getState() != EcontractTaskStateEnum.CANCEL) {
                logger.info("customerPoiPreBindCallBack,回调通知预绑定结果，通知状态非成功、失败或取消则不需要关注,econtractCallbackBo={}", JSON.toJSONString(econtractCallbackBo));
                return;
            }
            Integer customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
            // 基于场景分析短信签约任务来源
            Long switchTaskId = wmCustomerSwitchService.checkFromSwitchBind(wmCustomerPoiSmsRecordDB);

            //非切换任务 && 客户命中绑定规则流程的灰度
            if (switchTaskId == null && flowGrayService.hitPoiBindCustomerFlowGray(customerId)) {
                //非切换任务走新流程
                bindSignNoticeFlowService.signBindNoticeFlow(wmCustomerPoiSmsRecordDB, econtractCallbackBo.getState().getType());
            } else {
                if (econtractCallbackBo.getState() == EcontractTaskStateEnum.SUCCESS) {
                    wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.SUCCESS.getType(), econtractCallbackBo.getTaskId());
                    confirmPreBind(econtractCallbackBo.getTaskId());
                } else if (econtractCallbackBo.getState() == EcontractTaskStateEnum.FAIL) {
                    wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.FAIL.getType(), econtractCallbackBo.getTaskId());
                    cancelPreBind(econtractCallbackBo.getTaskId(), CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT, CustomerTaskStatusEnum.FAIL.getCode());
                } else if (econtractCallbackBo.getState() == EcontractTaskStateEnum.CANCEL) {
                    wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.CANCEL.getType(), econtractCallbackBo.getTaskId());
                    cancelPreBind(econtractCallbackBo.getTaskId(), CustomerConstants.CUSTOMER_LOG_OP_NAME_BD, CustomerTaskStatusEnum.CANCEL.getCode());
                }
            }
        } catch (Exception e) {
            logger.error("KP确认门店预绑定回调失败 econtractCallbackBo={}", JSONObject.toJSONString(econtractCallbackBo), e);
        }
    }

    /**
     * 短信签约-预绑定通知回调失败、取消
     *
     * @param taskId
     * @param source
     * @throws WmCustomerException
     */
    public void cancelPreBind(Long taskId, String source, Integer taskStatus) throws WmCustomerException {
        logger.info("#cancelPreBind,task={},source={}", taskId, source);
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskId(taskId);
        Integer customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        String wmPoiIds = wmCustomerPoiSmsRecordDB.getWmPoiIds();
        Set<Long> wmPoiIdSet = convertWmPoiIds(wmPoiIds);

        // 基于场景分析短信签约任务来源
        Long switchTaskId = wmCustomerSwitchService.checkFromSwitchBind(wmCustomerPoiSmsRecordDB);
        if (switchTaskId != null) {
            WmCustomerPoiSwitchOperateTypeEnum typeEnum = WmCustomerPoiSwitchOperateTypeEnum.BIND_SMS_REJECT;
            if (taskStatus == CustomerTaskStatusEnum.CANCEL.getCode()) {
                typeEnum = WmCustomerPoiSwitchOperateTypeEnum.BIND_TASK_CANCEL;
            }
            wmCustomerSwitchService.bind(new WmCustomerPoiSwitchBO(switchTaskId, customerId, wmPoiIdSet, 0, source,
                    typeEnum));
        } else {
            customerPoiBindService.cancelBind(customerId, wmPoiIdSet, CustomerTaskStatusEnum.of(taskStatus));
        }
    }

    public void confirmPreBind(Long taskId) throws WmCustomerException, TException {
        logger.info("#confirmPreBind,taskId={}", taskId);
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB =
                wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskId(taskId);

        Integer customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        String wmPoiIds = wmCustomerPoiSmsRecordDB.getWmPoiIds();
        Set<Long> wmPoiIdSet = convertWmPoiIds(wmPoiIds);
        // 基于场景分析短信签约任务来源
        Long switchTaskId = wmCustomerSwitchService.checkFromSwitchBind(wmCustomerPoiSmsRecordDB);
        if (switchTaskId != null) {
            wmCustomerSwitchService.bind(new WmCustomerPoiSwitchBO(switchTaskId, customerId, wmPoiIdSet, 0, "商家处理",
                    WmCustomerPoiSwitchOperateTypeEnum.BIND_SMS_CONFIRM));
        } else {
            customerPoiBindService.confirmBind(customerId, wmPoiIdSet, wmCustomerPoiSmsRecordDB.getId());
        }
    }

    /**
     * 当前客户是否存在多个门店
     */
    public boolean hasMultiPoi(Integer customerId) {
        List<Long> wmPoiIdList = selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(customerId);
        return wmPoiIdList.size() > 1;
    }

    /**
     * 单店是否是合作商门店
     *
     * @param customerId
     * @return
     * @throws WmCustomerException
     */
    public boolean isSinglePoiAgentBoolean(Integer customerId) throws WmCustomerException {
        boolean c2PackAllGrayBoolean = MccSignConfig.getC2PackAllGray();
        logger.info("isSinglePoiAgentBoolean灰度开关 C2PackAllGray:[{}]", c2PackAllGrayBoolean);
        if (c2PackAllGrayBoolean) {
            return false;
        }

        logger.info("isSinglePoiAgentBoolean customerId:[{}]", customerId);
        boolean isAgentWmPoi = false;
        List<Long> wmPoiIdList = selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(customerId);
        if (!CollectionUtils.isEmpty(wmPoiIdList) && wmPoiIdList.size() == 1) {
            //延时处理
            if (!CollectionUtils.isEmpty(getAgentIdsByPoiId(wmPoiIdList.get(0)))) {
                isAgentWmPoi = true;
            }
        }
        logger.info("isSinglePoiAgentBoolean isAgentWmPoi:[{}]", isAgentWmPoi);
        return isAgentWmPoi;
    }


    private List<Integer> getAgentIdsByPoiId(long wmPoiId) {
        List<Integer> agentIds = null;
        try {
            agentIds = wmContractAgentService.getAgentIdByWmPoiId(wmPoiId);
            logger.info("getAgentIdsByPoiId:[{}]", JSON.toJSONString(agentIds));
        } catch (Exception e) {
            logger.error("WmCustomerContractThriftServiceImpl#getAgentIdsByPoiId, error", e);
        }

        return agentIds;
    }


    public boolean isSinglePoiAgentAndHasC2Boolean(Integer customerId) throws WmCustomerException {
        boolean c2PackAllGrayBoolean = MccSignConfig.getC2PackAllGray();
        logger.info("isSinglePoiAgentAndHasC2Boolean灰度开关 C2PackAllGray:[{}]", c2PackAllGrayBoolean);
        if (c2PackAllGrayBoolean) {
            return false;
        }

        if (isSinglePoiAgentBoolean(customerId)) {
            //首次上单，一般一条数据
            boolean hasC2Boolean = wmEcontractPackRelContractCheckService.hasRelContractC2ByCustomerId(customerId);
            logger.info("customerId:[{}] hasC2Boolean:[{}]", customerId, hasC2Boolean);
            return hasC2Boolean;

        }
        return false;
    }


    /**
     * 根据客户ID获取门店Ids
     *
     * @param customerId
     * @return
     */
    @Deprecated
    public List<Long> selectWmPoiIdsByCustomerId(Integer customerId) {
        return wmCustomerPoiRelService.selectWmPoiIdsByCustomerId(customerId);
    }

    public List<Long> selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(Integer customerId) {
        List<Long> wmPoiIdList = wmCustomerPoiRelService.selectWmPoiIdsByCustomerId(customerId);
        List<SwitchPoiInfo> switchPoiInfoList = Lists.newArrayList();
        List<Long> collect = Lists.newArrayList();
        try {
            switchPoiInfoList = wmPoiSwitchThriftService.getToBizWmPoiListInConfirmingProc(customerId + "", WmPoiSwitchTaskTypeEnum.CUS_SWITCH, 0, "系统");
            collect = switchPoiInfoList.stream().map(info -> info.getWmPoiId()).collect(Collectors.toList());
        } catch (WmPoiBizException | TException e) {
            logger.error("getToBizWmPoiListInConfirmingProc异常,customerId={}", customerId, e);
        }
        wmPoiIdList.addAll(collect);
        return wmPoiIdList;
    }

    /**
     * 根据客户IDs获取门店Ids
     *
     * @param customerIds
     * @return
     */
    public List<Long> selectWmPoiIdsByCustomerIds(List<Integer> customerIds) {
        return wmCustomerPoiDBMapper.selectWmPoiIdsByCustomerIds(customerIds);
    }


    /**
     * 客户维度通知上单状态机
     */
    public void insertPoiStateCenterByCustomerId(Integer customerId, CustomerModuleStateEnum stateEnum, Integer opUid, String opName) {
        List<Long> wmPoiIds = wmCustomerPoiRelService.selectWmPoiIdsByCustomerId(customerId);
        if (wmPoiIds.isEmpty()) {
            logger.info("当前客户尚未绑定门店，无需通知上单状态机");
            return;
        }
        logger.info("门店ID:{}通知上单状态机,code={}", wmPoiIds, stateEnum.getCode());
        stateCenterAdapter.batchSyncCustomerState(wmPoiIds, stateEnum, opUid, opName);
    }

    /**
     * 字符串形式的wmPoiIds转成Set
     *
     * @param wmPoiIds
     * @return
     * @throws WmCustomerException
     */
    private Set<Long> convertWmPoiIds(String wmPoiIds) throws WmCustomerException {
        if (StringUtils.isEmpty(wmPoiIds)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "当前任务ID的门店为空");
        }
        String[] wmPoiIdArray = wmPoiIds.split(CustomerConstants.SPLIT_SYMBOL);
        Set<Long> wmPoiIdSet = Sets.newHashSet();
        for (String wmPoiId : wmPoiIdArray) {
            wmPoiIdSet.add(Long.parseLong(wmPoiId));
        }
        return wmPoiIdSet;
    }

    /**
     * 通过门店ID查询客户ID
     *
     * @param wmPoiIdSet
     * @return
     */
    public Set<Integer> selectCustomerIdByPoiId(Set<Long> wmPoiIdSet) {
        return wmCustomerPoiDBMapper.selectCustomerIdByPoiId(wmPoiIdSet);
    }


    /**
     * 客户门店生成pdf
     *
     * @param wmCustomerGeneratePdfPoiBo
     * @param opUid
     * @throws TException
     * @throws WmCustomerException
     */
    public LongResult generatePdfPoi(WmCustomerGeneratePdfPoiBo wmCustomerGeneratePdfPoiBo, int opUid, String opName)
            throws TException, WmCustomerException {
        logger.info("客户门店生成pdf wmCustomerGeneratePdfPoiBo={},opUid={},opName={}",
                JSON.toJSONString(wmCustomerGeneratePdfPoiBo), opUid, opName);

        if (wmCustomerGeneratePdfPoiBo == null || wmCustomerGeneratePdfPoiBo.getCustomerId() == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }

        // 补充门店其他信息参数校验
        List<Short> poiOtherInfoList = wmCustomerGeneratePdfPoiBo.getPoiOtherInfo();
        // 补充门店信息
        boolean commisionTypeRemark = false;
        // 优惠申请书
        boolean preferentialPolicyRemark = false;
        if (!CollectionUtils.isEmpty(poiOtherInfoList)) {
            for (Short poiOtherInfo : poiOtherInfoList) {
                if (!CPoiOtherInfo.contains(poiOtherInfo)) {
                    logger.error("补充门店其他信息参数有误 poiOtherInfo={}", poiOtherInfo);
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "补充门店其他信息参数有误");

                }

                if (CPoiOtherInfo.getValue(poiOtherInfo).getValue() == CPoiOtherInfo.COMMISION_TYPE_REMARK.getValue()) {
                    commisionTypeRemark = true;
                }

                if (CPoiOtherInfo.getValue(poiOtherInfo).getValue() == CPoiOtherInfo.PREFERENTIAL_POLICY_REMARK
                        .getValue()) {
                    preferentialPolicyRemark = true;
                }
            }

        }

        // 客户校验
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerGeneratePdfPoiBo.getCustomerId());
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }

        // 电子签约
        if (CustomerSignMode.ELECTTRONIC.getCode() != wmCustomerDB.getSignMode().intValue()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店生成只支持电子签约商家");
        }

        // 合同信息
        List<WmTempletContractBasicBo> wmTempletContractBasicBoList = wmContractService.selectByParentIdAndTypes(
                Long.valueOf(wmCustomerGeneratePdfPoiBo.getCustomerId()), Lists.newArrayList(
                        WmTempletContractTypeEnum.C1_E.getCode(),
                        WmTempletContractTypeEnum.C1_PAPER.getCode()
                ));
        if (CollectionUtils.isEmpty(wmTempletContractBasicBoList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "提交失败；该客户的合同未签约生效，无法生成门店信息列表");
        }

        WmTempletContractBasicBo wmTempletContractBasicBo = wmTempletContractBasicBoList.get(0);

        // 门店列表
        List<Long> poiIdList = wmCustomerGeneratePdfPoiBo.getPoiIdList();
        // 确定查询门店id集合
        List<Long> queryPoiIdList = Lists.newArrayList();
        List<Long> customerPoiIdList =
                wmCustomerPoiDBMapper.selectWmPoiIdsExcludeUnbinding(wmCustomerGeneratePdfPoiBo.getCustomerId());
        if (!CollectionUtils.isEmpty(poiIdList)) {
            // 门店数量校验
            if (poiIdList.size() > POI_SIZE) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("提交失败，一次最多提交%s个门店", POI_SIZE));
            }

            // 校验门店从属关系
            List<Long> copyPoiIdList = Lists.newArrayList(poiIdList);
            copyPoiIdList.removeAll(customerPoiIdList);
            if (!CollectionUtils.isEmpty(copyPoiIdList)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("门店id %s 不属于该客户，无法提交", JSON.toJSONString(copyPoiIdList)));
            }

            queryPoiIdList = Lists.newArrayList(Sets.<Long>newHashSet(poiIdList));
        } else {
            // 门店数量校验
            if (customerPoiIdList.size() > POI_SIZE) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("提交失败，一次最多提交%s个门店", POI_SIZE));
            }

            queryPoiIdList = Lists.newArrayList(customerPoiIdList);
        }

        if (CollectionUtils.isEmpty(queryPoiIdList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "该客户下无门店,不可生成pdf");
        }
        try {

            logger.info("生成pdf门店id {}", JSON.toJSONString(queryPoiIdList));
            // 门店id->结算集合
            List<WmPoiSettleAuditedDB> wmPoiSettleAuditedDBList =
                    wmSettleManagerService.batchQueryPoiSettleAudited(queryPoiIdList);
            //门店结算集合
            Map<Integer, WmPoiSettleAuditedDB> wmPoiSettleAuditedDBHashMap = Maps.newHashMap();

            if (!CollectionUtils.isEmpty(wmPoiSettleAuditedDBList)) {
                for (WmPoiSettleAuditedDB wmPoiSettleAuditedDB : wmPoiSettleAuditedDBList) {
                    wmPoiSettleAuditedDBHashMap.put(wmPoiSettleAuditedDB.getWm_poi_id(), wmPoiSettleAuditedDB);
                }
            }

            // 结算id集合
            List<Integer> settleIdList = Lists.newArrayList(
                    Lists.transform(wmPoiSettleAuditedDBList, new Function<WmPoiSettleAuditedDB, Integer>() {

                        @Nullable
                        @Override
                        public Integer apply(@Nullable WmPoiSettleAuditedDB wmPoiSettleAuditedDB) {
                            return wmPoiSettleAuditedDB.getWm_settle_id();
                        }
                    }));
            // 去重
            List querySettleIdList = Lists.newArrayList(Sets.newHashSet(settleIdList));

            // 门店信息集合
            Map<Long, WmPoiAggre> wmPoiAggreMap = getWmPoiAggreMap(queryPoiIdList);

            // 结算集合
            Map<Integer, WmSettleAudited> wmSettleAuditedHashMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(querySettleIdList)) {
                List<WmSettleAudited> wmSettleAuditedList = wmSettleService.getWmSettleAuditedBasicListByWmSettleIdList
                        (querySettleIdList);
                for (WmSettleAudited wmSettleAudited : wmSettleAuditedList) {
                    wmSettleAuditedHashMap.put(wmSettleAudited.getWm_settle_id(), wmSettleAudited);
                }
            }

            // 配送信息
            List<WmPoiLogisticsInfoForPDF> wmPoiLogisticsInfoForPDFList = wmPoiLogisticsInfoForPDFThriftService
                    .getPoiLogisticsInfoForPDF(queryPoiIdList, preferentialPolicyRemark);
            logger.info("配送信息{}", JSON.toJSONString(wmPoiLogisticsInfoForPDFList));

            // 优惠
            EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBoSupport =
                    getEcontractBatchPoiInfoExtBoSupport(preferentialPolicyRemark, wmPoiLogisticsInfoForPDFList);
            Multimap<Integer, WmPoiLogisticsInfoForPDF> multimap =
                    getMultimapWmPoiLogisticsInfoForPDF(wmPoiLogisticsInfoForPDFList);
            List<Integer> sortKeys = Lists.newArrayList(multimap.keySet());
            Collections.sort(sortKeys);
            // 配送信息集合
            Map<Long, EcontractDeliveryInfoBo> econtractDeliveryInfoBoMap = getDeliveryInfoMap(sortKeys, multimap);

            // 组装数据
            EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo = new EcontractBatchPoiInfoExtBo();
            // 门店详情集合
            List<EcontractPoiInfoExtBo> econtractPoiInfoExtBoList = Lists.newArrayList();
            // 循环处理数据
            for (Long wmPoiId : queryPoiIdList) {

                Integer wmPoiIdInteger = wmPoiId.intValue();

                WmPoiSettleAuditedDB wmPoiSettleAuditedDB = wmPoiSettleAuditedDBHashMap.get(wmPoiIdInteger);
                logger.info("结算信息 wmPoiSettleAuditedDB {}", JSON.toJSONString(wmPoiSettleAuditedDB));

                // 配送
                EcontractDeliveryInfoBo econtractDeliveryInfoBo = econtractDeliveryInfoBoMap.get(wmPoiId);
                logger.info("配送信息 econtractDeliveryInfoBo {}", JSON.toJSONString(econtractDeliveryInfoBo));

                if (wmPoiSettleAuditedDB == null && econtractDeliveryInfoBo == null) {
                    continue;
                }

                // 门店详情
                EcontractPoiInfoExtBo econtractPoiInfoExtBo = new EcontractPoiInfoExtBo();
                econtractPoiInfoExtBo.setWmPoiId(wmPoiIdInteger);
                WmPoiAggre wmPoiAggre = wmPoiAggreMap.get(wmPoiId);
                if (wmPoiAggre != null) {
                    econtractPoiInfoExtBo.setPoiName(wmPoiAggre.getName());
                    econtractPoiInfoExtBo.setAddress(wmPoiAggre.getAddress());
                }

                // 结算信息
                if (wmPoiSettleAuditedDB != null) {
                    WmSettleAudited wmSettleAudited = wmSettleAuditedHashMap.get(wmPoiSettleAuditedDB.getWm_settle_id());

                    if (wmSettleAudited != null) {
                        econtractPoiInfoExtBo.setAccountName(wmSettleAudited.getAcc_name());
                        econtractPoiInfoExtBo.setAccountNumber(wmSettleAudited.getAcc_cardno());
                        econtractPoiInfoExtBo
                                .setProvinceName(wmSettleBankService.getProvinceNameByProvinceId(wmSettleAudited.getProvince()));
                        econtractPoiInfoExtBo.setCityName(wmSettleBankService.getCityNameByLocationId(wmSettleAudited.getCity()));
                        econtractPoiInfoExtBo.setBankName(wmSettleBankService.getBankByBankId(wmSettleAudited.getBankid()));
                        econtractPoiInfoExtBo.setBranchName(wmSettleAudited.getBranchname());
                        econtractPoiInfoExtBo.setSettleTypeName(PDF_MAPPING.get("settleType" + wmSettleAudited.getSettle_type()));
                        econtractPoiInfoExtBo.setFinancialContacts(wmSettleAudited.getParty_a_finance_people());
                        econtractPoiInfoExtBo.setFinancePhone(wmSettleAudited.getParty_a_finance_phone());
                        econtractPoiInfoExtBo.setPayPeriod(getPayPeriod(wmSettleAudited));
                        econtractPoiInfoExtBo.setMinPayAmount(getMinPayAmount(wmSettleAudited));
                    }
                }

                //配送
                econtractPoiInfoExtBo.setEcontractDeliveryInfoBo(econtractDeliveryInfoBo);

                econtractPoiInfoExtBoList.add(econtractPoiInfoExtBo);
            }

            // c1电子合同信息
            EcontractContractInfoBo econtractContractInfoBo = new EcontractContractInfoBo();
            econtractContractInfoBo.setContractNum(wmTempletContractBasicBo.getContractNum());

            econtractBatchPoiInfoExtBo.setSortkeys(Joiner.on(",").join(sortKeys));
            econtractBatchPoiInfoExtBo.setEcontractPoiInfoExtBoList(econtractPoiInfoExtBoList);
            econtractBatchPoiInfoExtBo.setEcontractContractInfoBo(econtractContractInfoBo);
            econtractBatchPoiInfoExtBo.setHasSupport(econtractBatchPoiInfoExtBoSupport.getHasSupport());
            econtractBatchPoiInfoExtBo.setHasSLASupport(econtractBatchPoiInfoExtBoSupport.getHasSLASupport());
            econtractBatchPoiInfoExtBo.setHasCommisionTypeRemark(commisionTypeRemark);
            econtractBatchPoiInfoExtBo.setSignature(wmCustomerGeneratePdfPoiBo.getSignature());

            // 电子合同申请
            EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
            econtractTaskApplyBo.setBizId(wmCustomerGeneratePdfPoiBo.getCustomerId().toString());
            econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
            econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.BATCH_POI_GENERATE_PDF);
            econtractTaskApplyBo.setApplyInfoBo(JSONObject.toJSONString(econtractBatchPoiInfoExtBo));
            econtractTaskApplyBo.setCommitUid(opUid);
            LongResult result = wmEcontractSignBzService.applyTask(econtractTaskApplyBo);

            // 插入合同版本列表
            WmContractVersionDB versionDB = new WmContractVersionDB();
            versionDB.setCustomer_id(String.valueOf(wmCustomerGeneratePdfPoiBo.getCustomerId()));
            versionDB.setWm_contract_id(wmCustomerGeneratePdfPoiBo.getCustomerId());
            versionDB.setType(CustomerContractConstant.POI_GENERATE_TYPE);
            versionDB.setOp_uid(opUid);
            versionDB.setVersion_number(
                    WmContractVersionUtil.genVersionNumForSettle(wmCustomerGeneratePdfPoiBo.getCustomerId()));
            if (wmCustomerGeneratePdfPoiBo.getSignature()) {
                versionDB.setStatus((byte) CCustomerPoiSignature.SIGNATUREING.getValue());

            } else {
                versionDB.setStatus((byte) CCustomerPoiSignature.NO_CHOICE_SIGNATURE.getValue());
            }
            versionDB.setValid((byte) 1);
            versionDB.setTransaction_id(result.getValue() + "");

            wmContractVersionService.insert(versionDB);

            return new LongResult(versionDB.getId());

        } catch (Exception e) {
            logger.error("客户门店列表pdf生成失败", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "操作失败,请再试试");
        }

    }

    private WmSettleAudited getWmSettleAudited(WmPoiSettleAuditedDB wmPoiSettleAuditedDB,
                                               Map<Integer, WmSettleAudited> wmSettleAuditedHashMap,
                                               Map<String, WmSettleAudited> wmSettleAuditedHashNewMap) {
        if (MccConfig.getSettleQuerySwitch()) {
            return wmSettleAuditedHashNewMap.get(((WmPoiSettleExtraInfo) wmPoiSettleAuditedDB).getBizSettleId());
        } else {
            return wmSettleAuditedHashMap.get(wmPoiSettleAuditedDB.getWm_settle_id());
        }
    }

    private void assembleSettleInfoMap(List<Long> queryPoiIdList,
                                       Map<Integer, WmPoiSettleAuditedDB> wmPoiSettleAuditedDBHashMap,
                                       Map<Integer, WmSettleAudited> wmSettleAuditedHashMap,
                                       Map<String, WmSettleAudited> wmSettleAuditedHashNewMap) {
        if (MccConfig.getSettleQuerySwitch()) {
            // 迁移金融侧的新接口
            assembleSettleInfoMapNew(queryPoiIdList, wmPoiSettleAuditedDBHashMap, wmSettleAuditedHashNewMap);
        } else {
            // 旧接口
            assembleSettleInfoMapOld(queryPoiIdList, wmPoiSettleAuditedDBHashMap, wmSettleAuditedHashMap);
        }
    }

    /**
     * 根据门店ID列表，获取门店结算信息详情
     *
     * @param queryPoiIdList
     * @param wmPoiSettleAuditedDBHashMap
     * @param wmSettleAuditedHashMap
     */
    private void assembleSettleInfoMapOld(List<Long> queryPoiIdList,
                                       Map<Integer, WmPoiSettleAuditedDB> wmPoiSettleAuditedDBHashMap,
                                       Map<Integer, WmSettleAudited> wmSettleAuditedHashMap) {
        // 门店id->结算集合
        List<WmPoiSettleAuditedDB> wmPoiSettleAuditedDBList =
                wmSettleManagerService.batchQueryPoiSettleAudited(queryPoiIdList);

        if (!CollectionUtils.isEmpty(wmPoiSettleAuditedDBList)) {
            for (WmPoiSettleAuditedDB wmPoiSettleAuditedDB : wmPoiSettleAuditedDBList) {
                wmPoiSettleAuditedDBHashMap.put(wmPoiSettleAuditedDB.getWm_poi_id(), wmPoiSettleAuditedDB);
            }
        }
        // 结算id集合
        List<Integer> settleIdList = Lists.newArrayList(
                Lists.transform(wmPoiSettleAuditedDBList, new Function<WmPoiSettleAuditedDB, Integer>() {

                    @Nullable
                    @Override
                    public Integer apply(@Nullable WmPoiSettleAuditedDB wmPoiSettleAuditedDB) {
                        return wmPoiSettleAuditedDB.getWm_settle_id();
                    }
                }));
        // 去重
        List querySettleIdList = Lists.newArrayList(Sets.newHashSet(settleIdList));
        if (!CollectionUtils.isEmpty(querySettleIdList)) {
            List<WmSettleAudited> wmSettleAuditedList = wmSettleService.getWmSettleAuditedBasicListByWmSettleIdList
                    (querySettleIdList);
            for (WmSettleAudited wmSettleAudited : wmSettleAuditedList) {
                wmSettleAuditedHashMap.put(wmSettleAudited.getWm_settle_id(), wmSettleAudited);
            }
        }
    }

    /**
     * 根据门店ID列表，获取门店结算信息详情
     *
     * @param queryPoiIdList
     * @param wmPoiSettleAuditedDBHashMap
     * @param wmSettleAuditedHashMap
     */
    private void assembleSettleInfoMapNew(List<Long> queryPoiIdList,
                                          Map<Integer, WmPoiSettleAuditedDB> wmPoiSettleAuditedDBHashMap,
                                          Map<String, WmSettleAudited> wmSettleAuditedHashMap) {
        // 门店id->结算集合
        List<WmPoiSettleAuditedDB> wmPoiSettleAuditedDBList =
                merchantSettleQueryAdapter.batchQueryPoiSettleAudited(queryPoiIdList);
        if (CollectionUtils.isEmpty(wmPoiSettleAuditedDBList)) {
            return;
        }
        for (WmPoiSettleAuditedDB wmPoiSettleAuditedDB : wmPoiSettleAuditedDBList) {
            wmPoiSettleAuditedDBHashMap.put(wmPoiSettleAuditedDB.getWm_poi_id(), wmPoiSettleAuditedDB);
        }
        // 结算id集合
        List<String> settleIdList = wmPoiSettleAuditedDBList.stream()
                .map(e -> e instanceof WmPoiSettleExtraInfo ? ((WmPoiSettleExtraInfo) e).getBizSettleId() : null)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(settleIdList)) {
            Map<String, WmSettleAudited> wmSettleAuditedMap = merchantSettleQueryAdapter.getWmSettleListByWmSettleIdList(settleIdList);
            for (String settleId : wmSettleAuditedMap.keySet()) {
                wmSettleAuditedHashMap.put(settleId, wmSettleAuditedMap.get(settleId));
            }
        }
    }

    public LongResult generatePdfPoiNew(WmCustomerGeneratePdfPoiBo wmCustomerGeneratePdfPoiBo, int opUid, String opName)
            throws WmCustomerException {
        logger.info("#generatePdfPoiNew={},opUid={},opName={}", JSON.toJSONString(wmCustomerGeneratePdfPoiBo), opUid, opName);

        if (wmCustomerGeneratePdfPoiBo == null || wmCustomerGeneratePdfPoiBo.getCustomerId() == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }

        if (wmCustomerGeneratePdfPoiBo.getType() != null && wmCustomerGeneratePdfPoiBo.getType() == 1) {
            return generatePoiBasePdf(wmCustomerGeneratePdfPoiBo, opUid, opName);
        }

        List<Short> poiOtherInfoList = wmCustomerGeneratePdfPoiBo.getPoiOtherInfo();
        // 计费方式说明
        boolean commisionTypeRemark = false;
        // 优惠申请书
        boolean preferentialPolicyRemark = false;
        if (!CollectionUtils.isEmpty(poiOtherInfoList)) {
            for (Short poiOtherInfo : poiOtherInfoList) {
                if (!CPoiOtherInfo.contains(poiOtherInfo)) {
                    logger.error("补充门店其他信息参数有误 poiOtherInfo={}", poiOtherInfo);
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "补充门店其他信息参数有误");
                }

                if (CPoiOtherInfo.getValue(poiOtherInfo).getValue() == CPoiOtherInfo.COMMISION_TYPE_REMARK.getValue()) {
                    commisionTypeRemark = true;
                }

                if (CPoiOtherInfo.getValue(poiOtherInfo).getValue() == CPoiOtherInfo.PREFERENTIAL_POLICY_REMARK.getValue()) {
                    preferentialPolicyRemark = true;
                }
            }
        }

        // 确定查询门店id集合
        List<Long> queryPoiIdList = Lists.newArrayList();

        // 客户校验
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerGeneratePdfPoiBo.getCustomerId());
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }

        // 电子签约检验
        if (CustomerSignMode.ELECTTRONIC.getCode() != wmCustomerDB.getSignMode().intValue()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店生成只支持电子签约商家");
        }

        // 合同信息
        List<WmCustomerContractBo> wmCustomerContractBoList = wmContractService.getAuditedContractBoListByCusIdAndType(
                Long.valueOf(wmCustomerGeneratePdfPoiBo.getCustomerId()),
                Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(), WmTempletContractTypeEnum.C1_PAPER.getCode()),
                0,
                "客户门店生成PDF");
        if (CollectionUtils.isEmpty(wmCustomerContractBoList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "提交失败；该客户的合同未签约生效，无法生成门店信息列表");
        }
        WmCustomerContractBo wmCustomerContractBo = wmCustomerContractBoList.get(0);
        WmTempletContractBasicBo wmTempletContractBasicBo = wmCustomerContractBo.getBasicBo();
        Optional<WmTempletContractSignBo> contractSignBo = wmCustomerContractBo.getSignBoList().stream().filter(item -> "B".equals(item.getSignType())).findFirst();

        // 门店列表
        List<Long> poiIdList = wmCustomerGeneratePdfPoiBo.getPoiIdList();

        List<Long> customerPoiIdList =
                wmCustomerPoiDBMapper.selectWmPoiIdsExcludeUnbinding(wmCustomerGeneratePdfPoiBo.getCustomerId());
        if (!CollectionUtils.isEmpty(poiIdList)) {
            // 门店数量校验
            if (poiIdList.size() > POI_SIZE) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("提交失败，一次最多提交%s个门店", POI_SIZE));
            }

            // 校验门店从属关系
            List<Long> copyPoiIdList = Lists.newArrayList(poiIdList);
            copyPoiIdList.removeAll(customerPoiIdList);
            if (!CollectionUtils.isEmpty(copyPoiIdList)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("门店id %s 不属于该客户，无法提交", JSON.toJSONString(copyPoiIdList)));
            }

            queryPoiIdList = Lists.newArrayList(Sets.<Long>newHashSet(poiIdList));
        } else {
            // 门店数量校验
            if (customerPoiIdList.size() > POI_SIZE) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("提交失败，一次最多提交%s个门店", POI_SIZE));
            }

            queryPoiIdList = Lists.newArrayList(customerPoiIdList);
        }

        if (CollectionUtils.isEmpty(queryPoiIdList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "该客户下无门店,不可生成pdf");
        }
        try {

            logger.info("生成pdf门店id {}", JSON.toJSONString(queryPoiIdList));

            // 门店信息集合
            Map<Long, WmPoiAggre> wmPoiAggreMap = getWmPoiAggreMap(queryPoiIdList);

            //门店结算集合
            Map<Integer, WmPoiSettleAuditedDB> wmPoiSettleAuditedDBHashMap = Maps.newHashMap();

            // 结算集合
            Map<Integer, WmSettleAudited> wmSettleAuditedHashMap = Maps.newHashMap();

            // 新接口的结算集合
            Map<String, WmSettleAudited> wmSettleAuditedHashNewMap = Maps.newHashMap();

            // 获取结算信息
            assembleSettleInfoMap(queryPoiIdList, wmPoiSettleAuditedDBHashMap, wmSettleAuditedHashMap, wmSettleAuditedHashNewMap);

            // 配送信息
            WmPoiLogisticsInfoRequestDTO requestDTO = new WmPoiLogisticsInfoRequestDTO();
            requestDTO.setWmCustomerId(wmCustomerGeneratePdfPoiBo.getCustomerId().longValue());
            requestDTO.setWmPoiIdList(queryPoiIdList);
            String poiLogisticsInfoSign = wmPoiLogisticsAbilityThriftService
                    .getPoiLogisticsInfoSign(requestDTO);
            logger.info("#getPoiLogisticsInfoSign={}", poiLogisticsInfoSign);
            if (StringUtils.isEmpty(poiLogisticsInfoSign)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "配送模块数据异常");
            }

            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSONObject
                    .parseObject(poiLogisticsInfoSign, EcontractBatchDeliveryInfoBo.class);
            List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = batchDeliveryInfoBo
                    .getEcontractDeliveryInfoBoList();

            // 按照计费方式说明分组
            Multimap<Integer, EcontractDeliveryInfoBo> multimap = ArrayListMultimap.create();
            for (EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {
                if (Strings.isEmpty(temp.getChargingDesc())) {
                    logger.error("配送模块数据异常:{}", JSON.toJSONString(temp));
                    continue;
                }
                //减少不需要的数据
                temp.setDeliveryArea(null);
                if (temp.getEcontractDeliveryAggregationInfoBo() == null) {
                    temp.setEcontractDeliveryAggregationInfoBo(null);
                }
                temp.setEcontractDeliveryWholeCityInfoBo(null);
                multimap.put(Integer.valueOf(temp.getChargingDesc()), temp);
            }
            // 计费方式说明排序
            List<Integer> sortKeys = Lists.newArrayList(multimap.keySet());
            Collections.sort(sortKeys);

            // 优惠和SLA标记
            EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBoSupport =
                    getEcontractBatchPoiInfoExtBoSupportByDeliveryInfoBo(preferentialPolicyRemark, econtractDeliveryInfoBoList);

            // 配送信息集合
            Map<Long, EcontractDeliveryInfoBo> econtractDeliveryInfoBoMap = getDeliveryInfoMapByDeliveryInfoBo(sortKeys, multimap);

            // 组装数据
            EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo = new EcontractBatchPoiInfoExtBo();
            // 门店详情集合
            List<EcontractPoiInfoExtBo> econtractPoiInfoExtBoList = Lists.newArrayList();
            // 循环处理数据
            for (Long wmPoiId : queryPoiIdList) {

                Integer wmPoiIdInteger = wmPoiId.intValue();

                WmPoiSettleAuditedDB wmPoiSettleAuditedDB = wmPoiSettleAuditedDBHashMap.get(wmPoiIdInteger);
//                logger.info("结算信息 wmPoiSettleAuditedDB {}", JSON.toJSONString(wmPoiSettleAuditedDB));

                // 配送
                EcontractDeliveryInfoBo econtractDeliveryInfoBo = econtractDeliveryInfoBoMap.get(wmPoiId);
                logger.info("配送信息 econtractDeliveryInfoBo {}", JSON.toJSONString(econtractDeliveryInfoBo));

                if (wmPoiSettleAuditedDB == null && econtractDeliveryInfoBo == null) {
                    logger.warn("无结算信息和配送信息 wmPoiSettleAuditedDB={},econtractDeliveryInfoBo = {}", JSON.toJSONString
                            (wmPoiSettleAuditedDB), JSON.toJSONString(econtractDeliveryInfoBo));
                    continue;
                }

                // 门店详情
                EcontractPoiInfoExtBo econtractPoiInfoExtBo = new EcontractPoiInfoExtBo();
                econtractPoiInfoExtBo.setWmPoiId(wmPoiIdInteger);
                WmPoiAggre wmPoiAggre = wmPoiAggreMap.get(wmPoiId);
                if (wmPoiAggre != null) {
                    econtractPoiInfoExtBo.setPoiName(wmPoiAggre.getName());
                    econtractPoiInfoExtBo.setAddress(wmPoiAggre.getAddress());
                }

                // 结算信息
                if (wmPoiSettleAuditedDB != null) {
                    WmSettleAudited wmSettleAudited = getWmSettleAudited(wmPoiSettleAuditedDB, wmSettleAuditedHashMap, wmSettleAuditedHashNewMap);

                    if (wmSettleAudited != null) {
                        econtractPoiInfoExtBo.setAccountName(wmSettleAudited.getAcc_name());
                        econtractPoiInfoExtBo.setAccountNumber(wmSettleAudited.getAcc_cardno());
                        econtractPoiInfoExtBo
                                .setProvinceName(wmSettleBankService.getProvinceNameByProvinceId(wmSettleAudited.getProvince()));
                        econtractPoiInfoExtBo.setCityName(wmSettleBankService.getCityNameByLocationId(wmSettleAudited.getCity()));
                        econtractPoiInfoExtBo.setBankName(wmSettleBankService.getBankByBankId(wmSettleAudited.getBankid()));
                        econtractPoiInfoExtBo.setBranchName(wmSettleAudited.getBranchname());
                        econtractPoiInfoExtBo.setSettleTypeName(PDF_MAPPING.get("settleType" + wmSettleAudited.getSettle_type()));
                        econtractPoiInfoExtBo.setFinancialContacts(wmSettleAudited.getParty_a_finance_people());
                        econtractPoiInfoExtBo.setFinancePhone(wmSettleAudited.getParty_a_finance_phone());
                        econtractPoiInfoExtBo.setPayPeriod(getPayPeriod(wmSettleAudited));
                        econtractPoiInfoExtBo.setMinPayAmount(getMinPayAmount(wmSettleAudited));
                    }
                }

                //配送信息
                econtractPoiInfoExtBo.setEcontractDeliveryInfoBo(econtractDeliveryInfoBo);
                econtractPoiInfoExtBoList.add(econtractPoiInfoExtBo);
            }

            // c1电子合同信息
            EcontractContractInfoBo econtractContractInfoBo = new EcontractContractInfoBo();
            econtractContractInfoBo.setContractNum(wmTempletContractBasicBo.getContractNum());
            econtractContractInfoBo.setPartBName(contractSignBo.isPresent() ? contractSignBo.get().getSignName() : StringUtils.EMPTY);

            econtractBatchPoiInfoExtBo.setSortkeys(Joiner.on(",").join(sortKeys));
            econtractBatchPoiInfoExtBo.setEcontractPoiInfoExtBoList(econtractPoiInfoExtBoList);
            econtractBatchPoiInfoExtBo.setEcontractContractInfoBo(econtractContractInfoBo);
            econtractBatchPoiInfoExtBo.setHasSupport(econtractBatchPoiInfoExtBoSupport.getHasSupport());
            econtractBatchPoiInfoExtBo.setHasSLASupport(econtractBatchPoiInfoExtBoSupport.getHasSLASupport());
            econtractBatchPoiInfoExtBo.setHasCommisionTypeRemark(commisionTypeRemark);
            econtractBatchPoiInfoExtBo.setSignature(wmCustomerGeneratePdfPoiBo.getSignature());

            // 电子合同申请
            EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
            econtractTaskApplyBo.setBizId(wmCustomerGeneratePdfPoiBo.getCustomerId().toString());
            econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
            econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.BATCH_POI_GENERATE_PDF);
            econtractTaskApplyBo.setApplyInfoBo(JSONObject.toJSONString(econtractBatchPoiInfoExtBo));
            econtractTaskApplyBo.setCommitUid(opUid);
            econtractTaskApplyBo.setWmCustomerId(wmCustomerGeneratePdfPoiBo.getCustomerId());
            LongResult result = wmEcontractSignBzService.applyTask(econtractTaskApplyBo);

            // 插入合同版本列表
            WmContractVersionDB versionDB = new WmContractVersionDB();
            versionDB.setCustomer_id(String.valueOf(wmCustomerGeneratePdfPoiBo.getCustomerId()));
            versionDB.setWm_contract_id(wmCustomerGeneratePdfPoiBo.getCustomerId());
            versionDB.setType(CustomerContractConstant.POI_GENERATE_TYPE);
            versionDB.setOp_uid(opUid);
            versionDB.setVersion_number(
                    WmContractVersionUtil.genVersionNumForSettle(wmCustomerGeneratePdfPoiBo.getCustomerId()));
            if (wmCustomerGeneratePdfPoiBo.getSignature()) {
                versionDB.setStatus((byte) CCustomerPoiSignature.SIGNATUREING.getValue());

            } else {
                versionDB.setStatus((byte) CCustomerPoiSignature.NO_CHOICE_SIGNATURE.getValue());
            }
            versionDB.setValid((byte) 1);
            versionDB.setTransaction_id(result.getValue() + "");

            wmContractVersionService.insert(versionDB);

            return new LongResult(versionDB.getId());

        } catch (Exception e) {
            logger.error("客户门店列表pdf生成失败", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "操作失败,请再试试");
        }

    }

    public LongResult generatePoiBasePdf(WmCustomerGeneratePdfPoiBo wmCustomerGeneratePdfPoiBo, int opUid, String opName) throws WmCustomerException {
        logger.info("导出门店基本信息,customerid={},opUid={},opName={}", wmCustomerGeneratePdfPoiBo.getCustomerId(), opUid, opName);
        // 确定查询门店id集合
        List<Long> queryPoiIdList = Lists.newArrayList();

        // 客户校验
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerGeneratePdfPoiBo.getCustomerId());
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }

        // 电子签约检验
        if (CustomerSignMode.ELECTTRONIC.getCode() != wmCustomerDB.getSignMode().intValue()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店生成只支持电子签约商家");
        }

        // 合同信息
        List<WmCustomerContractBo> wmCustomerContractBoList = wmContractService.getAuditedContractBoListByCusIdAndType(
                Long.valueOf(wmCustomerGeneratePdfPoiBo.getCustomerId()),
                Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(), WmTempletContractTypeEnum.C1_PAPER.getCode()),
                0,
                "客户门店生成PDF");
        if (CollectionUtils.isEmpty(wmCustomerContractBoList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "提交失败；该客户的合同未签约生效，无法生成门店信息列表");
        }
        WmCustomerContractBo wmCustomerContractBo = wmCustomerContractBoList.get(0);
        WmTempletContractBasicBo wmTempletContractBasicBo = wmCustomerContractBo.getBasicBo();
        Optional<WmTempletContractSignBo> contractSignABo = wmCustomerContractBo.getSignBoList().stream().filter(item -> "A".equals(item.getSignType())).findFirst();
        Optional<WmTempletContractSignBo> contractSignBBo = wmCustomerContractBo.getSignBoList().stream().filter(item -> "B".equals(item.getSignType())).findFirst();

        // 门店列表
        List<Long> poiIdList = wmCustomerGeneratePdfPoiBo.getPoiIdList();

        List<Long> customerPoiIdList =
                wmCustomerPoiDBMapper.selectWmPoiIdsExcludeUnbinding(wmCustomerGeneratePdfPoiBo.getCustomerId());
        int poiLimitNum = ConfigUtilAdapter.getInt("batch_poibaseinfo_size", 2000);
        if (!CollectionUtils.isEmpty(poiIdList)) {
            // 门店数量校验
            if (poiIdList.size() > poiLimitNum) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("提交失败，一次最多提交%s个门店", poiLimitNum));
            }

            // 校验门店从属关系
            List<Long> copyPoiIdList = Lists.newArrayList(poiIdList);
            copyPoiIdList.removeAll(customerPoiIdList);
            if (!CollectionUtils.isEmpty(copyPoiIdList)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("门店id %s 不属于该客户，无法提交", JSON.toJSONString(copyPoiIdList)));
            }

            queryPoiIdList = Lists.newArrayList(Sets.<Long>newHashSet(poiIdList));
        } else {
            // 门店数量校验
            if (customerPoiIdList.size() > poiLimitNum) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("提交失败，一次最多提交%s个门店", poiLimitNum));
            }

            queryPoiIdList = Lists.newArrayList(customerPoiIdList);
        }

        if (CollectionUtils.isEmpty(queryPoiIdList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "该客户下无门店,不可生成pdf");
        }
        try {
            // 门店信息集合
            Map<Long, WmPoiAggre> wmPoiAggreMap = getWmPoiAggreMapPartition(queryPoiIdList);
            // 组装数据
            EcontractBatchPoiBaseInfoBo econtractBatchPoiBaseInfoBo = new EcontractBatchPoiBaseInfoBo();
            // 门店详情集合
            List<EcontractPoiBaseInfoBo> econtractPoiBaseInfoBoList = Lists.newArrayList();
            // 循环处理数据
            for (Long wmPoiId : queryPoiIdList) {
                Integer wmPoiIdInteger = wmPoiId.intValue();
                // 门店详情
                EcontractPoiBaseInfoBo econtractPoiBaseInfoBo = new EcontractPoiBaseInfoBo();
                econtractPoiBaseInfoBo.setWmPoiId(wmPoiIdInteger);
                WmPoiAggre wmPoiAggre = wmPoiAggreMap.get(wmPoiId);
                if (wmPoiAggre != null) {
                    econtractPoiBaseInfoBo.setName(wmPoiAggre.getName());
                    econtractPoiBaseInfoBo.setAddress(wmPoiAggre.getAddress());
                }
                econtractPoiBaseInfoBoList.add(econtractPoiBaseInfoBo);
            }

            // 合同基本信息
            EcontractContractInfoBo econtractContractInfoBo = new EcontractContractInfoBo();
            econtractContractInfoBo.setContractNum(wmTempletContractBasicBo.getContractNum());
            econtractContractInfoBo.setPartAName(contractSignABo.isPresent() ? contractSignABo.get().getSignName() : StringUtils.EMPTY);
            econtractContractInfoBo.setPartBName(contractSignBBo.isPresent() ? contractSignBBo.get().getSignName() : StringUtils.EMPTY);
            String signTime = DateUtil.secondsToString(DateUtil.unixTime());
            econtractContractInfoBo.setPartASignTime(signTime);
            econtractContractInfoBo.setPartBSignTime(signTime);


            econtractBatchPoiBaseInfoBo.setEcontractPoiBaseInfoBoList(econtractPoiBaseInfoBoList);
            econtractBatchPoiBaseInfoBo.setEcontractContractInfoBo(econtractContractInfoBo);

            // 电子合同申请
            EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
            econtractTaskApplyBo.setBizId(wmCustomerGeneratePdfPoiBo.getCustomerId().toString());
            econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
            econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.BATCH_POI_BASEINFO_GENERATE_PDF);
            econtractTaskApplyBo.setApplyInfoBo(JSONObject.toJSONString(econtractBatchPoiBaseInfoBo));
            econtractTaskApplyBo.setCommitUid(opUid);
            LongResult result = wmEcontractSignBzService.applyTask(econtractTaskApplyBo);

            // 插入合同版本列表
            WmContractVersionDB versionDB = new WmContractVersionDB();
            versionDB.setCustomer_id(String.valueOf(wmCustomerGeneratePdfPoiBo.getCustomerId()));
            versionDB.setWm_contract_id(wmCustomerGeneratePdfPoiBo.getCustomerId());
            versionDB.setType(CustomerContractConstant.POI_BASEINFO_GENERATE_TYPE);
            versionDB.setOp_uid(opUid);
            versionDB.setVersion_number(
                    WmContractVersionUtil.genVersionNumForSettle(wmCustomerGeneratePdfPoiBo.getCustomerId()));
            if (wmCustomerGeneratePdfPoiBo.getSignature()) {
                versionDB.setStatus((byte) CCustomerPoiSignature.SIGNATUREING.getValue());
            } else {
                versionDB.setStatus((byte) CCustomerPoiSignature.NO_CHOICE_SIGNATURE.getValue());
            }
            versionDB.setValid((byte) 1);
            versionDB.setTransaction_id(result.getValue() + "");
            wmContractVersionService.insert(versionDB);
            return new LongResult(versionDB.getId());
        } catch (Exception e) {
            logger.error("客户门店基本信息列表pdf生成失败", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "操作失败,请再试试");
        }
    }


    /**
     * 门店信息
     *
     * @param queryPoiIdList
     * @return
     * @throws WmServerException
     * @throws TException
     */
    private Map<Long, WmPoiAggre> getWmPoiAggreMap(List<Long> queryPoiIdList) throws WmServerException, TException {
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                queryPoiIdList, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
                        WmPoiFieldQueryConstant.WM_POI_FIELD_ADDRESS, WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID));

        Map<Long, WmPoiAggre> wmPoiAggreMap = Maps.newHashMap();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            wmPoiAggreMap.put(wmPoiAggre.getWm_poi_id(), wmPoiAggre);
        }
        return wmPoiAggreMap;
    }

    private Map<Long, WmPoiAggre> getWmPoiAggreMapPartition(List<Long> queryPoiIdList) throws WmServerException, TException {
        List<List<Long>> partitionList = Lists.partition(queryPoiIdList, POI_BATCH_QUERY_SIZE);
        List<WmPoiAggre> wmPoiAggreList = Lists.newArrayList();
        for (List<Long> part : partitionList) {
            List<WmPoiAggre> wmPoiAggreListPartition = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                    part, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
                            WmPoiFieldQueryConstant.WM_POI_FIELD_ADDRESS, WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID));
            wmPoiAggreList.addAll(wmPoiAggreListPartition);
        }

        Map<Long, WmPoiAggre> wmPoiAggreMap = Maps.newHashMap();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            wmPoiAggreMap.put(wmPoiAggre.getWm_poi_id(), wmPoiAggre);
        }
        return wmPoiAggreMap;
    }

    /**
     * 获取多维map
     *
     * @param wmPoiLogisticsInfoForPDFList
     * @return
     */
    private Multimap<Integer, WmPoiLogisticsInfoForPDF> getMultimapWmPoiLogisticsInfoForPDF(
            List<WmPoiLogisticsInfoForPDF> wmPoiLogisticsInfoForPDFList) {
        Multimap<Integer, WmPoiLogisticsInfoForPDF> multimap = ArrayListMultimap.create();
        if (CollectionUtils.isEmpty(wmPoiLogisticsInfoForPDFList)) {
            return multimap;

        }

        for (WmPoiLogisticsInfoForPDF wmPoiLogisticsInfoForPDF : wmPoiLogisticsInfoForPDFList) {
            if (Strings.isEmpty(wmPoiLogisticsInfoForPDF.getChargingDesc())) {
                logger.error("wmPoiLogisticsInfoForPDF 数据有误 {}", JSON.toJSONString(wmPoiLogisticsInfoForPDF));
                continue;
            }
            multimap.put(Integer.valueOf(wmPoiLogisticsInfoForPDF.getChargingDesc()), wmPoiLogisticsInfoForPDF);
        }

        return multimap;
    }

    /**
     * 获取是否有优惠
     *
     * @param preferentialPolicyRemark
     * @param econtractDeliveryInfoBoList
     * @return
     */
    private EcontractBatchPoiInfoExtBo getEcontractBatchPoiInfoExtBoSupportByDeliveryInfoBo(boolean preferentialPolicyRemark,
                                                                                            List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList) {
        EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo = new EcontractBatchPoiInfoExtBo();
        for (EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {

            // 有优惠申请书&门店有数据
            if (WmEcontractContextUtil.SUPPORT_MARK.equals(temp.getSupportExclusive()) && preferentialPolicyRemark) {
                econtractBatchPoiInfoExtBo.setHasSupport(true);
            }
            // 存在旧费率模式且支持SLA的门店
            if (WmEcontractContextUtil.SUPPORT_MARK.equals(temp.getSupportSLA()) && !WmEcontractContextUtil.SUPPORT_MARK.equals(temp.getSupportNewModle())) {
                econtractBatchPoiInfoExtBo.setHasSLASupport(true);
            }
        }
        return econtractBatchPoiInfoExtBo;
    }

    /**
     * 获取是否有优惠
     *
     * @param preferentialPolicyRemark
     * @param wmPoiLogisticsInfoForPDFList
     * @return
     */
    private EcontractBatchPoiInfoExtBo getEcontractBatchPoiInfoExtBoSupport(boolean preferentialPolicyRemark,
                                                                            List<WmPoiLogisticsInfoForPDF> wmPoiLogisticsInfoForPDFList) {

        EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo = new EcontractBatchPoiInfoExtBo();
        for (WmPoiLogisticsInfoForPDF wmPoiLogisticsInfoForPDF : wmPoiLogisticsInfoForPDFList) {

            // 有优惠申请书&门店有数据
            if (wmPoiLogisticsInfoForPDF.isSupportExclusive() && preferentialPolicyRemark) {
                econtractBatchPoiInfoExtBo.setHasSupport(wmPoiLogisticsInfoForPDF.isSupportExclusive());
            }
            if (wmPoiLogisticsInfoForPDF.isIsSLA()) {
                econtractBatchPoiInfoExtBo.setHasSLASupport(wmPoiLogisticsInfoForPDF.isIsSLA());
            }

        }

        return econtractBatchPoiInfoExtBo;

    }

    /**
     * 获取配送信息
     *
     * @param sortKeys
     * @param multimap
     * @return
     * @throws WmPoiLogisticsException
     * @throws TException
     */
    private Map<Long, EcontractDeliveryInfoBo> getDeliveryInfoMap(List<Integer> sortKeys,
                                                                  Multimap<Integer, WmPoiLogisticsInfoForPDF> multimap) {

        logger.info("getDeliveryInfoMap multimap={}", JSON.toJSONString(multimap));
        // 配送信息集合
        Map<Long, EcontractDeliveryInfoBo> econtractDeliveryInfoBoMap = Maps.newHashMap();

        // 处理计费方式说明
        int count = 1;
        for (Integer key : sortKeys) {
            List<WmPoiLogisticsInfoForPDF> wmPoiLogisticsInfoForPDFS =
                    (List<WmPoiLogisticsInfoForPDF>) multimap.get(key);
            for (WmPoiLogisticsInfoForPDF wmPoiLogisticsInfoForPDF : wmPoiLogisticsInfoForPDFS) {

                // 配送
                EcontractDeliveryInfoBo econtractDeliveryInfoBo = new EcontractDeliveryInfoBo();

                econtractDeliveryInfoBo.setDeliveryType(wmPoiLogisticsInfoForPDF.getDeliveryType());
                econtractDeliveryInfoBo.setFeeInfo(wmPoiLogisticsInfoForPDF.getFeeInfo());
                String feeInfo = wmPoiLogisticsInfoForPDF.getFeeInfo();
                if (StringUtils.isNotEmpty(feeInfo)) {
                    econtractDeliveryInfoBo.setFeeInfo(feeInfo.replace(SEPARATOR_DELIVERY, SEPARATOR_DELIVERY_REPLACE));
                }
                // 有优惠
                if (wmPoiLogisticsInfoForPDF.isSupportExclusive()) {
                    econtractDeliveryInfoBo.setSupportExclusive(SUPPORT_MARK);
                }

                econtractDeliveryInfoBo.setExclusiveFee(wmPoiLogisticsInfoForPDF.getExclusiveFee());
                econtractDeliveryInfoBo.setServerSupport(wmPoiLogisticsInfoForPDF.getServerSupport());
                econtractDeliveryInfoBo.setOther(wmPoiLogisticsInfoForPDF.getOther());
                econtractDeliveryInfoBo.setDeposit(wmPoiLogisticsInfoForPDF.getDeposit());
                econtractDeliveryInfoBo.setValidate(wmPoiLogisticsInfoForPDF.getValidate());
                econtractDeliveryInfoBo.setChargingDesc(String.valueOf(count));
                if (wmPoiLogisticsInfoForPDF.isIsSLA()) {
                    econtractDeliveryInfoBo.setSupportSLA(SUPPORT_MARK);
                }
                econtractDeliveryInfoBo.setServicePackageValueAddedFee(wmPoiLogisticsInfoForPDF.getTotalFee());
                econtractDeliveryInfoBo.setServicePackageMinAmount(wmPoiLogisticsInfoForPDF.getTotalAmount());
                econtractDeliveryInfoBo.setPromiseFinishRate(wmPoiLogisticsInfoForPDF.getCompleteRate());
                econtractDeliveryInfoBo.setServicePackageFee(wmPoiLogisticsInfoForPDF.getBaseFee());
                econtractDeliveryInfoBo.setServicePackageName(wmPoiLogisticsInfoForPDF.getSlaPackageType());
                String intervalPrice = wmPoiLogisticsInfoForPDF.getIntervalPrice();
                if (StringUtils.isNotEmpty(intervalPrice)) {
                    econtractDeliveryInfoBo.setIncrementalCost(intervalPrice.replace(SEPARATOR_DELIVERY, SEPARATOR_DELIVERY_REPLACE));
                }

                econtractDeliveryInfoBoMap.put(wmPoiLogisticsInfoForPDF.getWmPoiId(), econtractDeliveryInfoBo);
            }
            count++;
        }


        logger.info("getDeliveryInfoMap econtractDeliveryInfoBoMap={}", JSON.toJSONString(econtractDeliveryInfoBoMap));

        return econtractDeliveryInfoBoMap;
    }

    private Map<Long, EcontractDeliveryInfoBo> getDeliveryInfoMapByDeliveryInfoBo(List<Integer> sortKeys,
                                                                                  Multimap<Integer, EcontractDeliveryInfoBo> multimap) {
        logger.info("#getDeliveryInfoMapByDeliveryInfoBo,multimap={}", JSON.toJSONString(multimap));
        // 配送信息集合
        Map<Long, EcontractDeliveryInfoBo> econtractDeliveryInfoBoMap = Maps.newHashMap();
        // 处理计费方式说明
        int count = 1;
        List<EcontractDeliveryInfoBo> infoBoList = Lists.newArrayList();
        for (Integer key : sortKeys) {
            infoBoList = (List<EcontractDeliveryInfoBo>) multimap.get(key);
            for (EcontractDeliveryInfoBo temp : infoBoList) {
                temp.setChargingDesc(String.valueOf(count));
                econtractDeliveryInfoBoMap.put(Long.valueOf(temp.getWmPoiId()), temp);
            }
            count++;
        }
        logger.info("#getDeliveryInfoMapByDeliveryInfoBo,econtractDeliveryInfoBoMap={}", JSON.toJSONString(econtractDeliveryInfoBoMap));
        return econtractDeliveryInfoBoMap;
    }

    /**
     * 获取结算周期
     *
     * @param wmSettleAudited
     * @return
     */
    private String getPayPeriod(WmSettleAudited wmSettleAudited) {
        // 结算周期
        // 周期结算3天、7天、14天、30天
        if (wmSettleAudited.getSettle_type() == WmContractConstant.SETTLETYPE_CIRCLE) {
            return getPayPeriod(wmSettleAudited.getPay_period_num(), wmSettleAudited.getPay_period_unit());
            // 每月几日
        } else if (wmSettleAudited.getSettle_type() == WmContractConstant.SETTLETYPE_DATE) {
            return "每月" + wmSettleAudited.getPay_day_of_month() + "日";
            // 每月最后一天
        } else if (wmSettleAudited.getSettle_type() == WmContractConstant.SETTLETYPE_LASTDAY) {
            return "每月最后一天";
        } else if (wmSettleAudited.getSettle_type() == WmContractConstant.SETTLETYPE_SELF) {
            return Strings.EMPTY;
        } else {
            return "未选中结算类型";
        }
    }

    private String getPayPeriod(int payPeriodNum, int payPeriodUnit) {
        if (payPeriodNum == 1 && payPeriodUnit == 1) {
            return "7天";
        } else if (payPeriodNum == 2 && payPeriodUnit == 1) {
            return "14天";
        } else if (payPeriodNum == 3 && payPeriodUnit == 3) {
            return "3天";
        } else if (payPeriodNum == 1 && payPeriodUnit == 2) {
            return "30天";
        } else if (payPeriodNum == 1 && payPeriodUnit == 3) {
            return "1天";
        } else {
            return "其他";
        }
    }

    /**
     * 最低结算金额
     *
     * @param wmSettleAudited
     * @return
     */
    private String getMinPayAmount(WmSettleAudited wmSettleAudited) {
        if (wmSettleAudited.getSettle_type() == WmContractConstant.SETTLETYPE_SELF) {
            return Strings.EMPTY;
        }

        BigDecimal bg = BigDecimal.valueOf(wmSettleAudited.getMin_pay_amount()).setScale(2, RoundingMode.HALF_UP);
        return String.valueOf(bg.doubleValue()) + "元";

    }

    /**
     * pdf生成回调
     *
     * @param econtractCallbackBo
     * @throws TException
     * @throws WmCustomerException
     */
    public Boolean customerPoiPdfCallBack(EcontractCallbackBo econtractCallbackBo)
            throws TException, WmCustomerException {
        logger.info("客户门店pdf生成回调,econtractCallbackBo={}", JSONObject.toJSONString(econtractCallbackBo));

        long transactionId = econtractCallbackBo.getTaskId();
        AssertUtil.assertLongMoreThan0(transactionId, "transactionId");
        String pdfUrl = econtractCallbackBo.getPdfUrl();
        EcontractTaskStateEnum state = econtractCallbackBo.getState();
        WmContractVersionDB wmContractVersionDB = wmSettleManagerService.getWmCustomerIdByTransactionId(transactionId);

        switch (state) {
            case IN_PROCESSING:
                handleInProcessing(wmContractVersionDB, pdfUrl);
                break;
            case SUCCESS:
                handleSuccess(wmContractVersionDB, pdfUrl);
                break;
            case FAIL:
                handleFail(econtractCallbackBo.getFailMsg(), wmContractVersionDB, pdfUrl);
                break;
            default:
                break;
        }

        return Boolean.TRUE;
    }

    private void handleInProcessing(WmContractVersionDB wmContractVersionDB, String pdfUrl) {
        logger.info("handleInProcessing pdfUrl={},wmContractVersionDB={},pdfUrl={}", pdfUrl, JSON.toJSONString
                (wmContractVersionDB), pdfUrl);

        if (StringUtils.isNotEmpty(pdfUrl)) {
            wmContractVersionService.updatePdfUrl(wmContractVersionDB.getId(), pdfUrl, "");
            if (wmContractVersionDB.getStatus() != (byte) CCustomerPoiSignature.NO_CHOICE_SIGNATURE.getValue()) {
                wmContractVersionDB.setStatus((byte) CCustomerPoiSignature.SIGNATUREING.getValue());
                wmContractVersionService.updateStatus(wmContractVersionDB);
            }

        }
    }


    /**
     * 处理成功
     *
     * @param wmContractVersionDB
     * @param pdfUrl
     */
    private void handleSuccess(WmContractVersionDB wmContractVersionDB, String pdfUrl) {
        logger.info("handleSuccess pdfUrl={},wmContractVersionDB={},pdfUrl={}", pdfUrl, JSON.toJSONString
                (wmContractVersionDB), pdfUrl);

        if (StringUtils.isNotEmpty(pdfUrl)) {

            wmContractVersionService.updatePdfUrl(wmContractVersionDB.getId(), pdfUrl, "");
            if (wmContractVersionDB.getStatus() != (byte) CCustomerPoiSignature.NO_CHOICE_SIGNATURE.getValue()) {
                wmContractVersionDB.setStatus((byte) CCustomerPoiSignature.SIGNATURE_SUCCESS.getValue());
                wmContractVersionService.updateStatus(wmContractVersionDB);
            }
        }

    }

    /**
     * 处理失败
     *
     * @param failStr
     * @param wmContractVersionDB
     */
    private void handleFail(String failStr, WmContractVersionDB wmContractVersionDB, String pdfUrl) {
        logger.info("handleFail 客户门店pdf生成failStr={},wmContractVersionDB={},pdfUrl={}", failStr,
                JSON.toJSONString(wmContractVersionDB), pdfUrl);
        try {
            if (StringUtils.isNotEmpty(pdfUrl)) {
                wmContractVersionService.updatePdfUrl(wmContractVersionDB.getId(), pdfUrl, "");
            }

            wmContractVersionDB.setStatus((byte) CCustomerPoiSignature.SIGNATURE_FAIL.getValue());
            wmContractVersionService.updateStatus(wmContractVersionDB);

            Long customerId = Long.valueOf(wmContractVersionDB.getCustomer_id());
            WmCustomerOplogBo customerOplogBo = new WmCustomerOplogBo(customerId.intValue(),
                    WmCustomerOplogBo.OpModuleType.CUSTOMER_POI, wmContractVersionDB.getId());
            WmEmploy wmEmploy = getWmEmploy(wmContractVersionDB.getOp_uid());
            customerOplogBo.setOpType(WmCustomerOplogBo.OpType.CHANGESTATUS.type)
                    .setOpUid(wmContractVersionDB.getOp_uid()).setOpUname(wmEmploy == null ? String.valueOf(wmContractVersionDB
                            .getOp_uid()) : wmEmploy.getName()).setLog("客店门店列表生成pdf失败原因：" + failStr);

            wmCustomerOplogService.insert(customerOplogBo);
        } catch (Exception e) {
            logger.error("handleFail 客户门店pdf生成失败异常", e);
        }

    }

    /**
     * 查询客户门店生成pdf列表
     *
     * @param customerId
     * @param pageNo
     * @param pageSize
     * @param id
     * @param name
     * @return
     */
    public WmCustomerPoiPdfPage queryPdfPoiList(Integer customerId, Integer pageNo, Integer pageSize, int id,
                                                String name) {
        logger.info("查询客户门店生成pdf列表 customerId={},pageNo={},pageSize={},id={},name={}", customerId, pageNo, pageSize, id,
                name);

        // 初始化
        PageHelper.startPage(pageNo, pageSize);

        List<WmContractVersionDB> wmContractVersionDBList = wmContractVersionService
                .getByWmContractIdAndStatus(customerId, CustomerContractConstant.POI_GENERATE_TYPE, null);

        // 处理
        List<WmCustomerPoiPdf> wmCustomerPoiPdfList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(wmContractVersionDBList)) {
            for (WmContractVersionDB wmContractVersionDB : wmContractVersionDBList) {

                WmCustomerPoiPdf wmCustomerPoiPdf = new WmCustomerPoiPdf();

                // 赋值
                logger.info("WmContractVersionDB id={}", wmContractVersionDB.getId());
                wmCustomerPoiPdf.setContractId(wmContractVersionDB.getId());
                wmCustomerPoiPdf.setVersionNumber(wmContractVersionDB.getVersion_number());
                wmCustomerPoiPdf.setStatusCode(Short.valueOf(wmContractVersionDB.getStatus()));
                wmCustomerPoiPdf.setStatus(CCustomerPoiSignature
                        .getValue(Short.valueOf(wmContractVersionDB.getStatus())).getDescription());
                wmCustomerPoiPdf.setPdfUrl(wmContractVersionDB.getPdf_url());
                wmCustomerPoiPdf.setCtime(TimeUtil.format(TimeUtil.SECOND_FORMAT, wmContractVersionDB.getCtime()));
                if (Strings.isNotEmpty(wmContractVersionDB.getPdf_url())) {
                    String fileName =
                            wmContractVersionDB.getPdf_url().substring(wmContractVersionDB.getPdf_url().lastIndexOf("/") + 1);
                    wmCustomerPoiPdf.setPdfFileName(fileName);
                }


                WmEmploy wmEmploy = getWmEmploy(wmContractVersionDB.getOp_uid());
                wmCustomerPoiPdf.setMisName(wmEmploy == null ? String.valueOf(wmContractVersionDB.getOp_uid()) : wmEmploy.getName());
                wmCustomerPoiPdfList.add(wmCustomerPoiPdf);
            }

        }

        PageData<WmCustomerPoiPdf> page = PageUtil.page(wmContractVersionDBList, wmCustomerPoiPdfList);

        // 返回
        return new WmCustomerPoiPdfPage(page.getPageInfo(), page.getList());
    }


    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    public WmEmploy getWmEmploy(Integer userId) {
        try {
            return wmEmployService.getById(userId);

        } catch (Exception e) {
            logger.error("调用getWmEmploy异常", e);
        }
        return null;
    }

    public Map<Long, Integer> selectByWmPoiIdList(List<Long> wmPoiIdList) {
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByWmPoiIdList(wmPoiIdList);
        Map<Long, Integer> collect = wmCustomerPoiDBList.stream().collect(Collectors.toMap(item -> item.getWmPoiId(), item -> item.getCustomerId()));
        return collect;
    }


    public int getActivePoiSizeByCustomerId(long mtCustomerId) {
        logger.info("根据客户id查询门店数量：mtCustomerId={}", mtCustomerId);
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.getWmCustomerByMtCustomerId(mtCustomerId);
        if (wmCustomerDB == null) {
            logger.info("没有查询到客户信息：mtCustomerId={}", mtCustomerId);
            return 0;
        }
        List<Long> wmPoiIs = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(wmCustomerDB.getId());
        if (CollectionUtils.isEmpty(wmPoiIs)) {
            return 0;
        }
        return wmLeafCustomerRealService.getActivePoiList(wmPoiIs).size();
    }

    public List<Long> selectWmPoIIdByCustomerIdAndRelationStatus(Integer customerId, Integer relationStatus) {
        return wmCustomerPoiDBMapper.selectWmPoIIdByCustomerIdAndRelationStatus(customerId, relationStatus);
    }

    /**
     * 根据客户id获取解绑短信信息
     */
    public Map<Integer, List<Long>> getSmsRecordCtimeBycustomerId(Integer customerId, List<Long> wmPoiIds) {
        List<WmCustomerPoiSmsRecordDB> smsRecords = wmCustomerPoiSmsRecordMapper.selectUnbindSmsRecordListByCustomerId(customerId);
        Map<Integer, List<Long>> map = new HashMap<>();
        for (WmCustomerPoiSmsRecordDB smsRecord : smsRecords) {
            String[] list = smsRecord.getWmPoiIds().split(",");
            List<Long> smsRecordPoiIds = new ArrayList<>();
            for (String s : list) {
                smsRecordPoiIds.add(Long.parseLong(s));
            }
            smsRecordPoiIds.retainAll(wmPoiIds);
            if (smsRecordPoiIds.size() != 0) {
                if (!map.containsKey(smsRecord.getCtime())) {
                    map.put(smsRecord.getCtime(), smsRecordPoiIds);
                } else {
                    map.get(smsRecord.getCtime()).addAll(smsRecordPoiIds);
                }

            }
        }
        return map;
    }

    /**
     * 根据客户ID查询关联的所有门店关系:已绑定/绑定中
     *
     * @param customerId
     * @return
     */
    public List<WmCustomerPoiDB> listPoiRelByCustomerId(Integer customerId) {
        List<WmCustomerPoiDB> list = Lists.newArrayList();
        logger.info("listPoiRelByCustomerId customerId={}", customerId);
        int pageSize = MccCustomerConfig.getPageQueryCustomerPoiSize();
        Integer customerPoiCnt = wmCustomerPoiDBMapper.countBindingOrBindPoiByCustomerId(customerId);
        if (customerPoiCnt == null || customerPoiCnt <= 0) {
            return list;
        }
        //记录超过设置分页查询数
        if (customerPoiCnt > pageSize) {
            Integer queryCnt = customerPoiCnt % pageSize == 0 ? (customerPoiCnt / pageSize) : (customerPoiCnt / pageSize + 1);
            Integer minId = null;
            for (Integer count = 1; count <= queryCnt; count++) {
                List<WmCustomerPoiDB> tempList = wmCustomerPoiDBMapper.listBindingOrBindPoiByCustomerId(customerId, minId, pageSize);
                if (CollectionUtils.isEmpty(tempList)) {
                    break;
                }
                list.addAll(tempList);
                minId = tempList.get(tempList.size() - 1).getId();
            }
        } else {
            List<WmCustomerPoiDB> tempList = wmCustomerPoiDBMapper.listBindingOrBindPoiByCustomerId(customerId, null, pageSize);
            list.addAll(tempList);
        }
        return list;
    }

    /**
     * 根据客户ID查询关联的所有绑定门店ID
     * 
     * @param customerId
     * @return
     */
    public List<Long> listBindWmPoiIdsByCustomerId(Integer customerId) {
        return wmCustomerPoiDBMapper.listBindWmPoiIdsByCustomerId(customerId);
    }
}
