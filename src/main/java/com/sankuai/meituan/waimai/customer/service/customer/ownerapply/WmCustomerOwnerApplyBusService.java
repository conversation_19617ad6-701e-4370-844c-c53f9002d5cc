package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.customer.*;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.check.WmCustomerAuthService;
import com.sankuai.meituan.waimai.customer.service.customer.dto.OwnerApplyStepUpdateDTO;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyDao;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyRecordDao;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.*;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 客户责任人申请相关业务服务
 * @date 20241227
 * 
 */
@Service
@Slf4j
public class WmCustomerOwnerApplyBusService {

    /**
     * 根据资质查询客户
     */
    private static final Integer QUERY_CUSTOMER_BY_QUA_TYPE = 1;

    /**
     * 根据门店查询客户
     */
    private static final Integer QUERY_CUSTOMER_BY_WM_POI_TYPE = 2;
    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private CustomerOwnerApplyCheckService customerOwnerApplyCheckService;

    @Autowired
    private WmCustomerESService wmCustomerESService;

    @Autowired
    private WmCustomerOwnerApplyDao wmCustomerOwnerApplyDao;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmEmployeeService wmEmployeeService;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmOrgClient wmOrgClient;

    @Autowired
    private WmCustomerOwnerApplyService wmCustomerOwnerApplyService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private GInfoServiceAdaptor gInfoServiceAdaptor;

    @Autowired
    private WmCustomerAuthService wmCustomerAuthService;

    @Autowired
    private WmCustomerOwnerApplyRecordDao wmCustomerOwnerApplyRecordDao;

    @Autowired
    private UDbServiceAdaptor uDbServiceAdaptor;

    @Autowired
    private WmEnterpriseAgentElephantServiceAdaptor wmEnterpriseAgentElephantServiceAdaptor;

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    @Autowired
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    /**
     * 客户责任人申请列表查询
     * 
     * @param queryBO
     * @return
     */
    public BaseResponse listCustomerOwnerApply(CustomerOwnerApplyQueryBO queryBO) {
        CustomerOwnerApplyListDTO customerOwnerApplyListDTO = new CustomerOwnerApplyListDTO();
        customerOwnerApplyListDTO.setTotalCnt(0);
        customerOwnerApplyListDTO.setCustomerOwnerApplyDTOList(Lists.newArrayList());
        try {
            // 参数校验
            customerOwnerApplyCheckService.checkCustomerOwnerApplyQueryBo(queryBO);
            Integer customerId = null;

            // 根据平台客户ID或资质编号查询客户信息
            if ((queryBO.getMtCustomerId() != null && queryBO.getMtCustomerId() > 0)
                    || StringUtils.isNotBlank(queryBO.getCustomerNumber())) {
                List<WmCustomerDB> wmCustomerDBList = wmCustomerESService
                        .queryByCustomerIdOrCustomerNumber(queryBO.getMtCustomerId(), queryBO.getCustomerNumber());
                // 查询不到信息则直接返回
                if (CollectionUtils.isEmpty(wmCustomerDBList)) {
                    return BaseResponse.success(customerOwnerApplyListDTO);
                }
                customerId = wmCustomerDBList.get(0).getId();
                queryBO.setCustomerId(customerId);
            }
            // 门店ID非空则需要设置客户ID
            if (queryBO.getWmPoiId() != null && queryBO.getWmPoiId() > 0) {
                WmCustomerPoiDB wmCustomerPoiDB = wmCustomerPoiDBMapper
                        .selectCustomerPoiRelByWmPoiIdWithValid(queryBO.getWmPoiId());
                // 查询不到客户则直接返回
                if (wmCustomerPoiDB == null) {
                    return BaseResponse.success(customerOwnerApplyListDTO);
                }
                // 录入客户ID或资质编号查询客户 与 门店检索客户ID不一致
                if (customerId != null && !queryBO.getCustomerId().equals(wmCustomerPoiDB.getCustomerId())) {
                    return BaseResponse.success(customerOwnerApplyListDTO);
                }
                queryBO.setCustomerId(wmCustomerPoiDB.getCustomerId());
            }

            // 构建查询对象
            WmCustomerOwnerApplyQueryBO query = convert2CustomerOwnerApplyQueryBO(queryBO);
            // 查询总数为0则直接返回
            int total = wmCustomerOwnerApplyDao.countByQueryBo(query);
            if (total <= 0) {
                return BaseResponse.success(customerOwnerApplyListDTO);
            }
            customerOwnerApplyListDTO.setTotalCnt(total);

            // 分页查询客户责任申请记录
            int maxId = wmCustomerOwnerApplyDao.countMaxIdByQueryBo(query);
            query.setMaxId(maxId);

            List<WmCustomerOwnerApply> list = wmCustomerOwnerApplyDao.listCustomerOwnerApplyByQueryBo(query);
            customerOwnerApplyListDTO
                    .setCustomerOwnerApplyDTOList(convertCustomerOwnerApplyDTOList(list, queryBO.getUid()));

        } catch (WmCustomerException wmCustomerException) {
            log.error("listCustomerOwnerApply,查询客户责任人申请记录发生异常,queryBO={}", JSON.toJSONString(queryBO),
                    wmCustomerException);
            BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, wmCustomerException.getMsg());
        } catch (Exception e) {
            log.error("listCustomerOwnerApply,查询客户责任人申请记录发生异常,queryBO={}", JSON.toJSONString(queryBO), e);
            BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }

        return BaseResponse.success(customerOwnerApplyListDTO);
    }

    /**
     * 转换查询参数
     * 
     * @param queryBO
     * @return
     */
    private WmCustomerOwnerApplyQueryBO convert2CustomerOwnerApplyQueryBO(CustomerOwnerApplyQueryBO queryBO) {
        WmCustomerOwnerApplyQueryBO query = new WmCustomerOwnerApplyQueryBO();
        query.setCustomerId(queryBO.getCustomerId());
        Integer opUid = queryBO.getUid();

        // 获取下级节点ID
        List<Integer> queryUids = Lists.newArrayList();
        queryUids.add(opUid);
        List<Integer> downUids = wmVirtualOrgServiceAdaptor.getUidsByUid(opUid, WmOrgConstant.QUERY_ALL_SOURCE,
                WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType());
        if (!CollectionUtils.isEmpty(downUids)) {
            queryUids.addAll(downUids);
        }
        query.setApplyUidList(queryUids);
        // 根据权限获取是否有总部权限,如果有总部权限根据客户类型查询，无总部权限根据申请人查询
        List<Integer> customerRealTypes = wmCustomerAuthService.getAuthCustomerRealType(opUid,
                WmCustomerAuthTypeEnum.READ_ONLY.getCode());
        if (CollectionUtils.isNotEmpty(customerRealTypes)) {
            query.setCustomerRealTypeList(customerRealTypes);
        } 

        query.setApplyUid(queryBO.getApplyUid());
        if (queryBO.getStatus() == CustomerOwnerApplyStatusEnum.REJECT.getCode()) {
            query.setStatusList(Lists.newArrayList(CustomerOwnerApplyStatusEnum.REJECT.getCode(),
                    CustomerOwnerApplyStatusEnum.STOP.getCode()));
        } else {
            query.setStatus(queryBO.getStatus());
        }
        query.setOffset((queryBO.getPageNo() - 1) * queryBO.getPageSize());
        query.setPageSize(queryBO.getPageSize());
        return query;
    }

    /**
     * 客户责任人申请相关权限校验
     * 
     * @param userId
     * @return
     * @throws WmCustomerException
     */
    public CustomerListAuthInfoDTO checkHasCustomerOwnerApplyAuth(Integer userId) throws WmCustomerException {

        String ownerApplyAuthCode = MccCustomerConfig.getCustomerOwnerApplyAuthCode();
        Boolean hasCustomerOwnerApplyAuth = upmAuthCheckService.checkHasUpmAuthByCode(userId, ownerApplyAuthCode);

        CustomerListAuthInfoDTO customerListAuthInfoDTO = new CustomerListAuthInfoDTO();
        customerListAuthInfoDTO.setHasCustomerOwnerApplyAuth(hasCustomerOwnerApplyAuth);

        return customerListAuthInfoDTO;
    }

    /**
     * 撤回客户责任人申请记录
     * 
     * @param applyId
     * @param uid
     * @return
     * @throws WmCustomerException
     */
    public BaseResponse revokeApply(Integer applyId, Integer uid) throws WmCustomerException {
        // 查询申请记录
        WmCustomerOwnerApply apply = wmCustomerOwnerApplyDao.getWmCustomerOwnerApplyById(applyId);
        if (apply == null) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户责任人申请记录");
        }
        // 校验申请状态
        if (apply.getStatus() != CustomerOwnerApplyStatusEnum.AUDITING.getCode()) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "非申请中状态不允许操作");
        }
        // 撤回客户责任人申请记录
        wmCustomerOwnerApplyService.syncApply2CancelOrReject(apply, uid, CustomerOwnerApplyStatusEnum.CANCEL.getCode());

        return BaseResponse.successWithMsg(true, "成功");
    }

    /**
     * 一键建群
     * 
     * @param applyId
     * @return
     */
    public BaseResponse<Boolean> createRoomOnCustomerOwnerApply(Integer applyId) throws WmCustomerException {

        // 查询申请记录
        WmCustomerOwnerApply apply = wmCustomerOwnerApplyDao.getWmCustomerOwnerApplyById(applyId);
        if (apply == null) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户责任人申请记录");
        }
        // 校验申请状态
        if (apply.getStatus() != CustomerOwnerApplyStatusEnum.AUDITING.getCode()) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "非申请中状态不允许操作");
        }
        // 校验是否已建群
        if (apply.getGroupId() != null && apply.getGroupId() > 0) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "已创建群，群ID为" + apply.getGroupId());
        }

        // 查询客户是否存在
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService
                .selectCustomerFromPlatformById(apply.getCustomerId());
        if (wmCustomerDB == null) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户信息");
        }
        Integer applyUid = apply.getApplyUid();

        // 查询群主的大象uid
        Long moderatorId = getDxUidByEmpUid(applyUid);

        // 查询当前责任人的大象uid
        Long oldOwnerDxUid = getDxUidByEmpUid(apply.getCustomerOwnerUid());
        Set<Long> participantIds = Sets.newHashSet(moderatorId, oldOwnerDxUid);
        String roomName = String.format("客户%s(%s)责任人申请沟通", wmCustomerDB.getCustomerName(),
                wmCustomerDB.getMtCustomerId());

        Long groupId = gInfoServiceAdaptor.createOverCompanyRoom(moderatorId, participantIds, roomName);
        if (groupId != null && groupId > 0) {
            log.info("createRoomOnCustomerOwnerApply 创建群成功, applyId:{}, groupId:{},moderatorId={},participantIds={}",
                    applyId, groupId, moderatorId, JSON.toJSONString(participantIds));
            // 将群组ID更新到申请记录
            wmCustomerOwnerApplyDao.updateGroupIdById(applyId, groupId);
            String noticeMsg = String.format("您好，客户%s(%s)责任人申请请于本群内进行沟通", wmCustomerDB.getCustomerName(),
                    wmCustomerDB.getMtCustomerId());
            // 发送群通知
            gInfoServiceAdaptor.sendRoomNoticeMsg(groupId, noticeMsg, Lists.newArrayList(participantIds));
            return BaseResponse.successWithMsg(true, "成功");
        }
        
        return BaseResponse.error(1, "建群失败");
    }

    /**
     * 新增客户责任人申请记录
     * 
     * @param customerOwnerApplyAddDTO
     * @return
     */
    public BaseResponse<Boolean> addCustomerOwnerApply(CustomerOwnerApplyAddDTO customerOwnerApplyAddDTO)
            throws WmCustomerException {
        Integer customerId = customerOwnerApplyAddDTO.getCustomerId();

        // 查询客户是否存在
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        if (wmCustomerDB == null) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户信息");
        }
        // 判断客户类型是否允许申请
        if (!MccCustomerConfig.getAllowCustomerOwnerApplyCustomerRealTypes()
                .contains(wmCustomerDB.getCustomerRealType())) {
            String customerRealTypeStr = "";
            for (Integer customerRealType : MccCustomerConfig.getAllowCustomerOwnerApplyCustomerRealTypes()) {
                customerRealTypeStr = (StringUtils.isBlank(customerRealTypeStr) ? "" : customerRealTypeStr + "、")
                        + CustomerRealTypeEnum.getByValue(customerRealType).getName();
            }
            String errorMsg = String.format("仅可申请%s类型客户，当前客户类型为%s，不可申请", customerRealTypeStr,
                    CustomerRealTypeEnum.getByValue(wmCustomerDB.getCustomerRealType()).getName());
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, errorMsg);
        }
        Integer oldOwnerUid = wmCustomerDB.getOwnerUid();
        // 当前责任人是操作人
        if (oldOwnerUid != null && oldOwnerUid.intValue() == customerOwnerApplyAddDTO.getUid().intValue()) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "您已是该客户责任人，无需进行申请");
        }

        // 查询是否存在申请中任务
        WmCustomerOwnerApply wmCustomerOwnerApply = wmCustomerOwnerApplyDao.getDoingApplyByCustomerId(customerId);
        if (wmCustomerOwnerApply != null) {
            return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "该客户已有其他客户责任人申请任务，无法申请");
        }

        // 客户责任人在外卖管理组内
        Boolean ownerInWbMerchantOrgFlag = false;
        Integer oldOwnerUId = wmCustomerDB.getOwnerUid();
        List<Integer> orgIds = wmOrgClient.getSuperiorBySource(oldOwnerUId, WmVirtualOrgSourceEnum.WAIMAI.getSource());

        if (CollectionUtils.isNotEmpty(orgIds) && orgIds.contains(MccCustomerConfig.getWBMerchantManageOrgId())) {
            ownerInWbMerchantOrgFlag = true;
        }

        // 不在外包商管理组以及其下组织节点，则可以创建
        if (!ownerInWbMerchantOrgFlag) {
            // 直接创建申请单 无责任人则直接申请通过
            wmCustomerOwnerApplyService.createCustomerOwnerApply(customerOwnerApplyAddDTO, wmCustomerDB,
                    ownerInWbMerchantOrgFlag);
        } else {
            List<Long> wmPoiIds = wmCustomerPoiService.listBindWmPoiIdsByCustomerId(customerId);
            // 客户下有任意一个门店的责任人在 外包商管理组内则不允许创建
            if (CollectionUtils.isNotEmpty(wmPoiIds) && checkHavePoiOwnerInWbMerchantOrg(wmPoiIds)) {
                return BaseResponse.error(CustomerConstants.RESULT_CODE_ERROR, "该客户正由电销处理中，不可申请");
            }
            // 直接创建申请单 无责任人则直接申请通过
            wmCustomerOwnerApplyService.createCustomerOwnerApply(customerOwnerApplyAddDTO, wmCustomerDB,
                    ownerInWbMerchantOrgFlag);
        }
        return BaseResponse.successWithMsg(true, "成功");
    }

    /**
     * 判断门店责任人在外卖商管组内
     * 
     * @param wmPoiIds
     * @return
     * @throws WmCustomerException
     */
    private Boolean checkHavePoiOwnerInWbMerchantOrg(List<Long> wmPoiIds) throws WmCustomerException {
        // 查询所有门店的责任人
        List<Long> ownerUids = wmPoiQueryAdapter
                .getWmPoiAggre(wmPoiIds,
                        Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_UID,
                                WmPoiFieldQueryConstant.WM_POI_FIELD_SELLER_UID))
                .stream().map(WmPoiAggre::getOwner_uid).collect(Collectors.toList());
        //无门店责任人 则直接返回false
        if (CollectionUtils.isEmpty(ownerUids)) {
            return false;
        }
        for (Long ownerUid : ownerUids) {
            if (ownerUid != null && ownerUid > 0) {
                List<Integer> orgIds = wmOrgClient.getSuperiorBySource(ownerUid.intValue(),
                        WmVirtualOrgSourceEnum.WAIMAI.getSource());
                if (CollectionUtils.isNotEmpty(orgIds)
                        && orgIds.contains(MccCustomerConfig.getWBMerchantManageOrgId())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 根据misId获取大象uid
     * 
     * @param uid
     * @return
     * @throws WmCustomerException
     */
    private Long getDxUidByEmpUid(Integer uid) throws WmCustomerException {
        try {
            // 查询群主的大象uid
            WmEmploy wmEmploy = wmEmployeeService.getWmEmployById(uid);
            if (wmEmploy == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到员工信息");
            }

            Long dxUid = uDbServiceAdaptor.getUidByMisId(wmEmploy.getMisId());
            if (dxUid == null || dxUid <= 0) {
                dxUid = wmEnterpriseAgentElephantServiceAdaptor.getDxUidByOuterMis(wmEmploy.getMisId());
            }
            return dxUid;
        } catch (Exception e) {
            log.error("getDxUidByEmpUid,根据misId获取大象Uid发生异常,uid={}", uid, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
    }

    /**
     * 参数转换
     * 
     * @param list
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    private List<CustomerOwnerApplyDTO> convertCustomerOwnerApplyDTOList(List<WmCustomerOwnerApply> list, Integer opUid)
            throws WmCustomerException, TException {
        List<CustomerOwnerApplyDTO> customerOwnerApplyDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)) {
            return customerOwnerApplyDTOList;
        }

        for (WmCustomerOwnerApply wmCustomerOwnerApply : list) {
            CustomerOwnerApplyDTO customerOwnerApplyDTO = new CustomerOwnerApplyDTO();
            customerOwnerApplyDTO.setApplyId(wmCustomerOwnerApply.getId());
            customerOwnerApplyDTO.setCustomerId(wmCustomerOwnerApply.getCustomerId());
            customerOwnerApplyDTO.setStatus(wmCustomerOwnerApply.getStatus());
            // 组装状态名称
            CustomerOwnerApplyStatusEnum customerOwnerApplyStatusEnum = CustomerOwnerApplyStatusEnum
                    .of(wmCustomerOwnerApply.getStatus());
            String statusName = customerOwnerApplyStatusEnum == CustomerOwnerApplyStatusEnum.STOP
                    ? CustomerOwnerApplyStatusEnum.REJECT.getDesc() : customerOwnerApplyStatusEnum.getDesc();
            customerOwnerApplyDTO.setStatusName(statusName);

            // 组装客户信息
            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService
                    .getCustomerById(wmCustomerOwnerApply.getCustomerId());
            if (wmCustomerBasicBo == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户信息");
            }
            Integer customerType = wmCustomerBasicBo.getCustomerType();
            // 个人资质设置个人证件号，非个人资质设置资质编号
            if (customerType == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
                customerOwnerApplyDTO.setCustomerIdCardNumber(wmCustomerBasicBo.getCustomerNumber());
            } else {
                customerOwnerApplyDTO.setCustomerNumber(wmCustomerBasicBo.getCustomerNumber());
            }
            customerOwnerApplyDTO.setCustomerName(wmCustomerBasicBo.getCustomerName());
            customerOwnerApplyDTO.setCustomerType(customerType);
            customerOwnerApplyDTO.setCustomerTypeName(CustomerType.getByCode(customerType).getDesc());
            customerOwnerApplyDTO.setMtCustomerId(wmCustomerBasicBo.getMtCustomerId());
            customerOwnerApplyDTO.setCustomerRealType(wmCustomerBasicBo.getCustomerRealType());
            customerOwnerApplyDTO.setCustomerRealTypeName(
                    CustomerRealTypeEnum.getNameByValue(wmCustomerBasicBo.getCustomerRealType()));

            // 组装申请人以及原客户责任人信息
            Integer applyUid = wmCustomerOwnerApply.getApplyUid();
            Integer oldOwnerUid = wmCustomerOwnerApply.getCustomerOwnerUid();

            List<Integer> uidList = Lists.newArrayList(applyUid, oldOwnerUid);
            List<WmEmploy> employs = wmEmployeeService.mgetByUids(uidList);
            for (WmEmploy wmEmploy : employs) {
                // 申请人
                if (applyUid.equals(wmEmploy.getUid())) {
                    customerOwnerApplyDTO
                            .setApplyUserName(String.format("%s(%s)", wmEmploy.getName(), wmEmploy.getMisId()));
                } else if (oldOwnerUid.equals(wmEmploy.getUid())) {
                    // 原负责人
                    customerOwnerApplyDTO
                            .setOldOwnerName(String.format("%s(%s)", wmEmploy.getName(), wmEmploy.getMisId()));
                }
            }
            // 组装权限信息
            customerOwnerApplyDTO.setApplyAuthDTO(convertCustomerOwnerApplyAuthDTO(wmCustomerOwnerApply, opUid));

            customerOwnerApplyDTOList.add(customerOwnerApplyDTO);

        }
        return customerOwnerApplyDTOList;

    }

    /**
     *
     * @param applyQueryCustomerBO
     * @return
     */
    public List<OwnerApplyCustomerResult> getCustomerResult(OwnerApplyQueryCustomerBO applyQueryCustomerBO)
            throws WmCustomerException {
        List<OwnerApplyCustomerResult> customerResultList = Lists.newArrayList();
        // 资质编号或证件编号查询
        if (applyQueryCustomerBO.getType() == QUERY_CUSTOMER_BY_QUA_TYPE) {
            List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService
                    .getCustomerByCustomerNumber(applyQueryCustomerBO.getCustomerNumber());
            if (CollectionUtils.isEmpty(wmCustomerDBList)) {
                log.info("getCustomerResult,根据资质编号未查询到关联客户,customerNumber={}",
                        applyQueryCustomerBO.getCustomerNumber());
                return customerResultList;
            }
            customerResultList = convertOwnerApplyCustomerResult(wmCustomerDBList);
        } else if (applyQueryCustomerBO.getType() == QUERY_CUSTOMER_BY_WM_POI_TYPE) {
            // 门店ID查询
            Set<Integer> customerIds = wmCustomerPoiService
                    .selectCustomerIdByPoiId(Sets.newHashSet(applyQueryCustomerBO.getWmPoiId()));
            if (CollectionUtils.isEmpty(customerIds)) {
                log.info("getCustomerResult,根据门店ID未查询到关联客户,wmPoiId={}", applyQueryCustomerBO.getWmPoiId());
                return customerResultList;
            }
            // 查询客户是否存在
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService
                    .selectCustomerFromPlatformById(customerIds.iterator().next());
            if (wmCustomerDB == null) {
                log.info("getCustomerResult,根据客户ID未查询到关联客户,customerId={}", customerIds.iterator().next());
                return customerResultList;
            }
            customerResultList = convertOwnerApplyCustomerResult(Lists.newArrayList(wmCustomerDB));
        }
        return customerResultList;
    }

    /**
     * 组装客户信息
     *
     * @param wmCustomerDBList
     * @return
     */
    private List<OwnerApplyCustomerResult> convertOwnerApplyCustomerResult(List<WmCustomerDB> wmCustomerDBList) {
        List<OwnerApplyCustomerResult> customerResultList = Lists.newArrayList();
        for (WmCustomerDB wmCustomerDB : wmCustomerDBList) {
            OwnerApplyCustomerResult customerResult = new OwnerApplyCustomerResult();
            customerResult.setCustomerName(wmCustomerDB.getCustomerName());
            customerResult.setCustomerId(wmCustomerDB.getId());
            customerResult.setCustomerNumber(wmCustomerDB.getCustomerNumber());
            customerResult.setMtCustomerId(wmCustomerDB.getMtCustomerId());
            Integer ownerUid = wmCustomerDB.getOwnerUid();
            WmEmploy wmEmploy = wmEmployeeService.getWmEmployById(ownerUid);
            customerResult.setCurrentOwnerUid((ownerUid == null || ownerUid == 0) ? null : ownerUid);
            customerResult.setCurrentOwnerMisId(wmEmploy == null ? "" : wmEmploy.getMisId());
            customerResult.setCurrentOwnerUName(wmEmploy == null ? "" : wmEmploy.getName());
            customerResult.setCustomerRealType(wmCustomerDB.getCustomerRealType());
            customerResult
                    .setCustomerRealTypeDesc(CustomerRealTypeEnum.getNameByValue(wmCustomerDB.getCustomerRealType()));
            customerResultList.add(customerResult);
        }
        return customerResultList;
    }

    /**
     * 组装权限信息
     * 
     * @param wmCustomerOwnerApply
     * @param opUid
     * @return
     */
    private CustomerOwnerApplyAuthDTO convertCustomerOwnerApplyAuthDTO(WmCustomerOwnerApply wmCustomerOwnerApply,
            Integer opUid) {
        CustomerOwnerApplyAuthDTO customerOwnerApplyAuthDTO = new CustomerOwnerApplyAuthDTO();
        customerOwnerApplyAuthDTO.setReadApplyDetailAuth(true);
        customerOwnerApplyAuthDTO.setReadOperateRecordAuth(true);
        customerOwnerApplyAuthDTO.setOnClickGroupAuth(false);
        customerOwnerApplyAuthDTO.setRevokeApplyAuth(false);

        List<Integer> hqWriteCustomerRealTypes = wmCustomerAuthService.getAuthCustomerRealType(opUid,
                WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode());
        List<Integer> downUids = wmVirtualOrgServiceAdaptor.getUidsByUid(opUid, WmOrgConstant.QUERY_ALL_SOURCE,
                WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType());

        Integer applyUid = wmCustomerOwnerApply.getApplyUid();
        Boolean isWrite = hqWriteCustomerRealTypes.contains(wmCustomerOwnerApply.getCustomerRealType())
                || downUids.contains(applyUid) || applyUid.intValue() == opUid;
        // 有可读可写权限 && 申请中 则可以操作撤回
        if (isWrite && wmCustomerOwnerApply.getStatus() == CustomerOwnerApplyStatusEnum.AUDITING.getCode()) {
            customerOwnerApplyAuthDTO.setRevokeApplyAuth(true);
        }
        // 有可读可写权限 && 申请单未绑定群ID 则可以操作建群
        if (isWrite && (wmCustomerOwnerApply.getGroupId() == null || wmCustomerOwnerApply.getGroupId() == 0)) {
            customerOwnerApplyAuthDTO.setOnClickGroupAuth(true);
        }
        return customerOwnerApplyAuthDTO;
    }

    /**
     * 根据申请单ID查询客户责任人申请单详情
     *
     * @param applyId
     * @return
     * @throws WmCustomerException
     */
    public CustomerOwnerApplyDetailDTO getCustomerOwnerApplyDetail(Integer applyId) throws WmCustomerException {
        log.info("根据申请单ID查询客户责任人申请单详情#getCustomerOwnerApplyDetail applyId:{}", applyId);
        // 根据申请单ID查询客户责任人申请单
        WmCustomerOwnerApply wmCustomerOwnerApply = wmCustomerOwnerApplyDao.getWmCustomerOwnerApplyById(applyId);
        if (wmCustomerOwnerApply == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户责任人申请单");
        }
        return convertCustomerOwnerApplyDetailDTO(wmCustomerOwnerApply);
    }

    private CustomerOwnerApplyDetailDTO convertCustomerOwnerApplyDetailDTO(WmCustomerOwnerApply wmCustomerOwnerApply)
            throws WmCustomerException {
        CustomerOwnerApplyDetailDTO customerOwnerApplyDetailDTO = new CustomerOwnerApplyDetailDTO();
        customerOwnerApplyDetailDTO.setApplyId(wmCustomerOwnerApply.getId());
        customerOwnerApplyDetailDTO.setCustomerId(wmCustomerOwnerApply.getCustomerId());
        customerOwnerApplyDetailDTO.setMtCustomerId(wmCustomerOwnerApply.getMtCustomerId());
        customerOwnerApplyDetailDTO.setStatus(wmCustomerOwnerApply.getStatus());
        customerOwnerApplyDetailDTO
                .setStatusName(CustomerOwnerApplyStatusEnum.of(wmCustomerOwnerApply.getStatus()).getDesc());
        customerOwnerApplyDetailDTO.setApplyReason(wmCustomerOwnerApply.getApplyReason());
        List<Integer> uidList = Lists.newArrayList(wmCustomerOwnerApply.getCustomerOwnerUid(),
                wmCustomerOwnerApply.getApplyUid());
        List<WmEmploy> employs = wmEmployeeService.mgetByUids(uidList);
        employs.forEach(wmEmploy -> {
            if (wmCustomerOwnerApply.getCustomerOwnerUid().equals(wmEmploy.getUid())) {
                customerOwnerApplyDetailDTO
                        .setOldCustomerOwner(String.format("%s(%s)", wmEmploy.getName(), wmEmploy.getMisId()));
            }
            if (wmCustomerOwnerApply.getApplyUid().equals(wmEmploy.getUid())) {
                customerOwnerApplyDetailDTO
                        .setNewCustomerOwner(String.format("%s(%s)", wmEmploy.getName(), wmEmploy.getMisId()));
            }
        });
        // 查询客户信息，获取客户名称
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerOwnerApply.getCustomerId());
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户信息");
        }
        customerOwnerApplyDetailDTO.setCustomerName(wmCustomerDB.getCustomerName());
        fillApplyStepInfo(customerOwnerApplyDetailDTO, wmCustomerOwnerApply);

        return customerOwnerApplyDetailDTO;

    }

    private void fillApplyStepInfo(CustomerOwnerApplyDetailDTO customerOwnerApplyDetailDTO,
            WmCustomerOwnerApply wmCustomerOwnerApply) throws WmCustomerException {
        // 补充步骤信息
        CustomerOwnerStepEnum stepEnum = CustomerOwnerStepEnum.getByCode(wmCustomerOwnerApply.getStepId());
        if (stepEnum == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户申请单步骤信息");
        }
        List<CustomerOwnerNodeEnum> progressList = stepEnum.getProgress();
        List<CustomerOwnerApplyStep> applyStepList = progressList.stream().map(node -> {
            CustomerOwnerApplyStep step = new CustomerOwnerApplyStep();
            step.setStepId(node.getCode());
            step.setStepName(node.getDesc());// 初始化步骤名称
            return step;
        }).collect(Collectors.toList());
        // 补充初始节点信息
        // 创建任务节点，补充创建任务信息
        CustomerOwnerApplyStep createStep = applyStepList.get(0);
        Integer createUid = wmCustomerOwnerApply.getApplyUid();
        WmEmploy wmEmploy = getEmployeeById(createUid);
        createStep.setAuditTips(String.format("%s(%s)", wmEmploy.getName(), wmEmploy.getMisId()) + "创建");
        createStep.setStepTime(wmCustomerOwnerApply.getCtime());
        createStep.setStatusCode(CustomerOwnerApplyStepStatusEnum.CREATE.getCode());// 创建节点默认初始状态0
        createStep.setStepUserName(wmEmploy.getName());
        // 初始化审批完成节点
        CustomerOwnerApplyStep completeStep = applyStepList.get(applyStepList.size()-1);
        completeStep.setStatusCode(CustomerOwnerApplyStepStatusEnum.COMPLETE.getCode());

        // 三节点的情况下，根据申请单状态设置审批中节点展示信息
        CustomerOwnerApplyStatusEnum applyStatusEnum = CustomerOwnerApplyStatusEnum
                .of(wmCustomerOwnerApply.getStatus());
        if (applyStatusEnum == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户申请单状态信息");
        }
        switch (applyStatusEnum) {
            case AUDITING:
                processAuditingStatus(wmCustomerOwnerApply, applyStepList);
                break;
            case COMPLETE:
                processCompleteStatus(wmCustomerOwnerApply, applyStepList, stepEnum);
                break;
            case REJECT:
                processRejectStatus(wmCustomerOwnerApply, applyStepList);
                break;
            case STOP:
                processSimpleStatus(wmCustomerOwnerApply, applyStepList, CustomerOwnerApplyStepStatusEnum.STOP, "已终止","已终止");
                break;
            case CANCEL:
                processSimpleStatus(wmCustomerOwnerApply, applyStepList, CustomerOwnerApplyStepStatusEnum.CANCEL,"已撤回","已撤回");
                break;
        }
        customerOwnerApplyDetailDTO.setApplyStepList(applyStepList);

    }

    /**
     * 处理审批中状态
     * @param wmCustomerOwnerApply
     * @param applyStepList
     * @throws WmCustomerException
     */
    private void processAuditingStatus(WmCustomerOwnerApply wmCustomerOwnerApply,
            List<CustomerOwnerApplyStep> applyStepList) throws WmCustomerException {
        CustomerOwnerApplyStep auditIngStep = applyStepList.get(1);
        String nameAndMis = wmEmployeeService.getUserAndId(wmCustomerOwnerApply.getCustomerOwnerUid());
        updateStepInfo(auditIngStep, OwnerApplyStepUpdateDTO.builder()
                .onStep(true)
                .status(CustomerOwnerApplyStepStatusEnum.AUDITING)
                .auditTips(nameAndMis)
                .stepTime(wmCustomerOwnerApply.getUtime())
                .stepName("当前客户责任人审批")
                .build());
        String auditLinkUrl = getAuditLinkUrl(wmCustomerOwnerApply.getId());
        auditIngStep.setAuditUrl(auditLinkUrl);
    }

    /**
     * 处理完成状态
     * @param wmCustomerOwnerApply
     * @param applyStepList
     * @param stepEnum
     * @throws WmCustomerException
     */
    private void processCompleteStatus(WmCustomerOwnerApply wmCustomerOwnerApply,
            List<CustomerOwnerApplyStep> applyStepList, CustomerOwnerStepEnum stepEnum) throws WmCustomerException {
        int completeStepIndex = applyStepList.size() - 1;
        updateStepInfo(applyStepList.get(completeStepIndex), OwnerApplyStepUpdateDTO.builder()
                .onStep(true)
                .status(CustomerOwnerApplyStepStatusEnum.COMPLETE)
                .stepTime(wmCustomerOwnerApply.getUtime())
                .stepName(CustomerOwnerNodeEnum.COMPLETE.getDesc())
                .build());
        if (stepEnum.equals(CustomerOwnerStepEnum.CREATE_TO_AUDIT)) {
            String nameAndMis = wmEmployeeService.getUserAndId(wmCustomerOwnerApply.getCustomerOwnerUid());
            updateStepInfo(applyStepList.get(1), OwnerApplyStepUpdateDTO.builder()
                    .onStep(false)
                    .status(CustomerOwnerApplyStepStatusEnum.PASS)
                    .auditTips(nameAndMis+"已通过")
                    .stepTime(wmCustomerOwnerApply.getUtime())
                    .auditRemark(getAuditRemark(wmCustomerOwnerApply.getId()))
                    .stepName("审批通过")
                    .build());
        }
    }

    /**
     * 处理驳回状态
     * @param wmCustomerOwnerApply
     * @param applyStepList
     * @throws WmCustomerException
     */
    private void processRejectStatus(WmCustomerOwnerApply wmCustomerOwnerApply,
            List<CustomerOwnerApplyStep> applyStepList) throws WmCustomerException {
        // 查看审核表，确认是系统自动驳回还是操作人驳回
        WmCustomerOwnerApplyAudit applyAudit = Optional.ofNullable(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(wmCustomerOwnerApply.getId()))
                .orElseThrow(() -> new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户申请单审批信息"));
        if (applyAudit.getAuditUid() == 0) {
            updateStepInfo(applyStepList.get(1), OwnerApplyStepUpdateDTO.builder()
                    .onStep(true)
                    .status(CustomerOwnerApplyStepStatusEnum.REJECT)
                    .auditTips("系统驳回")
                    .auditRemark("超时系统自动驳回")
                    .stepTime(wmCustomerOwnerApply.getUtime())
                    .stepName("审批驳回")
                    .build());
            return;
        }
        String nameAndMis = wmEmployeeService.getUserAndId(wmCustomerOwnerApply.getCustomerOwnerUid());
        updateStepInfo(applyStepList.get(1), OwnerApplyStepUpdateDTO.builder()
                .onStep(true)
                .status(CustomerOwnerApplyStepStatusEnum.REJECT)
                .auditTips(nameAndMis+ "已驳回")
                .auditRemark(getAuditRemark(wmCustomerOwnerApply.getId()))
                .stepTime(wmCustomerOwnerApply.getUtime())
                .stepName("审批驳回")
                .build());
    }

    /**
     * 处理简单状态
     * 包括终止、撤回
     * @param wmCustomerOwnerApply
     * @param applyStepList
     * @param status
     * @param auditTips
     * @throws WmCustomerException
     */
    private void processSimpleStatus(WmCustomerOwnerApply wmCustomerOwnerApply,
            List<CustomerOwnerApplyStep> applyStepList, CustomerOwnerApplyStepStatusEnum status, String auditTips,String stepName)
            throws WmCustomerException {
        updateStepInfo(applyStepList.get(1), OwnerApplyStepUpdateDTO.builder()
                .onStep(true)
                .status(status)
                .auditTips(auditTips)
                .auditRemark(getAuditRemark(wmCustomerOwnerApply.getId()))
                .stepTime(wmCustomerOwnerApply.getUtime())
                .stepName(stepName)
                .build());
    }

    private void updateStepInfo(CustomerOwnerApplyStep step, OwnerApplyStepUpdateDTO updateDTO) {
        step.setStepTime(updateDTO.getStepTime());
        step.setStatusCode(updateDTO.getStatus().getCode());
        step.setAuditTips(updateDTO.getAuditTips());
        step.setOnStep(updateDTO.isOnStep());
        step.setRemark(updateDTO.getAuditRemark());
        step.setStepName(updateDTO.getStepName());
    }

    private String getAuditRemark(Integer applyId) throws WmCustomerException {
        WmCustomerOwnerApplyAudit applyAudit = Optional.ofNullable(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId))
                .orElseThrow(() -> new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户申请单审批信息"));
        return applyAudit.getAuditResult();
    }

    private WmEmploy getEmployeeById(Integer employeeId) throws WmCustomerException {
        return Optional.ofNullable(wmEmployeeService.getWmEmployById(employeeId))
                .orElseThrow(() -> new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户责任人信息"));
    }

    private String getAuditLinkUrl(Integer applyId) throws WmCustomerException {
        WmCustomerOwnerApplyAudit applyAudit = Optional.ofNullable(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId))
                .orElseThrow(() -> new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户申请单审批信息"));

        try {
            WmTicketDto wmTicketDto = wmCrmTicketThriftServiceAdapter
                    .getSubTicketByParentTicketId(applyAudit.getTaskId());
            return MccScConfig.getTicketSystemUrlPrefix() + wmTicketDto.getId()
                    + MccScConfig.getTicketSystemUrlSuffix();
        } catch (Exception e) {
            log.info("查询任务系统异常:{}", applyAudit.getTaskId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询任务系统异常");
        }
    }

    public OwnerApplyRecordListDTO getCustomerOwnerApplyRecordPage(OwnerApplyRecordQueryBO queryBO)
            throws WmCustomerException {
        log.info("分页查询客户责任人申请操作记录-getCustomerOwnerApplyRecordPage queryBO:{}", JSON.toJSONString(queryBO));
        WmCustomerOwnerApplyRecordQueryBo dbQueryBo = convertWmCustomerOwnerApplyRecordQueryBo(queryBO);
        List<WmCustomerOwnerApplyRecord> wmCustomerOwnerApplyRecords = wmCustomerOwnerApplyRecordDao
                .selectPageByCondition(dbQueryBo);
        int total = wmCustomerOwnerApplyRecordDao.countByCondition(dbQueryBo);
        log.info("分页查询客户责任人申请操作记录-getCustomerOwnerApplyRecordPage-db:{}",
                JSON.toJSONString(wmCustomerOwnerApplyRecords));
        OwnerApplyRecordListDTO result = new OwnerApplyRecordListDTO();
        result.setTotal(total);
        result.setList(convertOwnerApplyRecordDTO(wmCustomerOwnerApplyRecords));
        return result;

    }

    private WmCustomerOwnerApplyRecordQueryBo
            convertWmCustomerOwnerApplyRecordQueryBo(OwnerApplyRecordQueryBO queryBO) {
        WmCustomerOwnerApplyRecordQueryBo dbQueryBo = new WmCustomerOwnerApplyRecordQueryBo();
        dbQueryBo.setOffset((queryBO.getPageNo() - 1) * queryBO.getPageSize());
        dbQueryBo.setPageSize(queryBO.getPageSize());
        dbQueryBo.setModuleId(queryBO.getModuleId());
        dbQueryBo.setOpType(queryBO.getOpType());
        dbQueryBo.setOpUid(queryBO.getOpUser());
        dbQueryBo.setCreateTimeStart(queryBO.getOpTimeStart());
        dbQueryBo.setCreateTimeEnd(queryBO.getOpTimeEnd());
        dbQueryBo.setContent(queryBO.getContent());
        dbQueryBo.setApplyId(queryBO.getApplyId());
        return dbQueryBo;
    }

    private List<OwnerApplyRecordDTO> convertOwnerApplyRecordDTO(List<WmCustomerOwnerApplyRecord> records)
            throws WmCustomerException {
        List<OwnerApplyRecordDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        List<Integer> uidList = records.stream().map(WmCustomerOwnerApplyRecord::getOpUid).filter(opUid -> opUid >0).collect(Collectors.toList());
        List<WmEmploy> employs = wmEmployeeService.mgetByUids(uidList);
        Map<Integer, WmEmploy> uid2EmployMap = employs.stream()
                .collect(Collectors.toMap(WmEmploy::getUid, Function.identity()));
        for (WmCustomerOwnerApplyRecord record : records) {
            OwnerApplyRecordDTO dto = new OwnerApplyRecordDTO();
            dto.setContent(record.getContent());
            dto.setOpTime(record.getCtime());
            dto.setOpUid(record.getOpUid() <= 0 ? 0 : record.getOpUid());
            dto.setOpUname(record.getOpUid() <= 0 ? "系统" : uid2EmployMap.get(record.getOpUid()).getName());
            dto.setOpUserMisId(record.getOpUid() <=0 ? "" :uid2EmployMap.get(record.getOpUid()).getMisId());
            int moduleId = record.getModuleId();
            WmCustomerOplogBo.OpModuleType opModuleType = WmCustomerOplogBo.OpModuleType.getByCode((short)moduleId);
            dto.setModuleTypeName(opModuleType == null ? "" : opModuleType.desc);
            CustomerOwnerRecordOpTypeEnum opTypeEnum = CustomerOwnerRecordOpTypeEnum.getByCode(record.getOpType());
            dto.setOpTypeName(opTypeEnum == null ? "" : opTypeEnum.getDesc());
            result.add(dto);
        }
        return result;
    }

    /**
     * 根据创建时间范围查询具有申请中申请单的客户ID列表
     * 
     * @param queryBO
     * @return
     * @throws WmCustomerException
     */
    public List<Integer> getDoingCustomerListByTime(CustomerOwnerApplyListQueryBO queryBO) throws WmCustomerException {
        log.info("根据条件查询客户责任人申请单-getDoingCustomerListByTime-req:{}", JSON.toJSONString(queryBO));
        WmCustomerOwnerApplyListDbQuery dbQuery = new WmCustomerOwnerApplyListDbQuery();
        dbQuery.setCustomerId(queryBO.getCustomerId());
        dbQuery.setCtimeEnd(queryBO.getCreatedTimeEnd());
        dbQuery.setCtimeStart(queryBO.getCreatedTimeStart());
        List<WmCustomerOwnerApply> wmCustomerOwnerApplies = wmCustomerOwnerApplyDao
                .listDoingApplyByTimeCondition(dbQuery);
        log.info("根据条件查询客户责任人申请单-getDoingCustomerListByTime-req:{},size:{}", JSON.toJSONString(queryBO),
                wmCustomerOwnerApplies.size());
        List<Integer> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmCustomerOwnerApplies)) {
            return resultList;
        }
        return wmCustomerOwnerApplies.stream().map(WmCustomerOwnerApply::getCustomerId).collect(Collectors.toList());
    }

}
