package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.clueflow;


import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 食堂档口线索绑定任务Service
 * <AUTHOR>
 * @date 2024/05/29
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallClueTaskService {

    @Autowired
    private WmCanteenStallClueFlowCanteenService wmCanteenStallClueFlowCanteenService;

    @Autowired
    private WmCanteenStallClueFlowLabelService wmCanteenStallClueFlowLabelService;


    /**
     * 线索绑定成功流程
     * @param clueBindBO clueBindBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void bindClueSuccess(WmCanteenStallClueBindBO clueBindBO) throws WmSchCantException {
        log.info("[WmCanteenStallClueTaskService.bindClueSuccess] input param: clueBindBO = {}", JSONObject.toJSONString(clueBindBO));
        // 1-线索绑定状态更新为"绑定成功"(学校系统)
        wmCanteenStallClueFlowCanteenService.updateClueBindStatusSuccess(clueBindBO);

        // 2-线索打校园食堂标签(标签系统)
        wmCanteenStallClueFlowLabelService.addWdcClueLabel(clueBindBO);
    }


    /**
     * 线索绑定失败流程
     * @param clueBindBO clueBindBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void bindClueFail(WmCanteenStallClueBindBO clueBindBO) throws WmSchCantException {
        log.info("[WmCanteenStallClueTaskService.bindClueFail] input param: clueBindBO = {}", JSONObject.toJSONString(clueBindBO));
        // 1-线索绑定状态更新为"绑定失败"(学校系统)
        wmCanteenStallClueFlowCanteenService.updateClueBindStatusFail(clueBindBO);
    }


    /**
     * 解绑线索
     * @param clueBindBO clueBindBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void unbindClue(WmCanteenStallClueBindBO clueBindBO) throws WmSchCantException {
        log.info("[WmCanteenStallClueTaskService.unbindClue] input param: clueBindBO = {}", JSONObject.toJSONString(clueBindBO));
        // 1-线索绑定状态更新为"未绑定"(学校系统)
        wmCanteenStallClueFlowCanteenService.updateClueBindStatusUnbind(clueBindBO);

        // 2-线索掉校园食堂标签(标签系统)
        wmCanteenStallClueFlowLabelService.deleteWdcClueLabel(clueBindBO);
    }

}
