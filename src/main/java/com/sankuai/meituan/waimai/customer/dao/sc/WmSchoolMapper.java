package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolSearchCondition;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolUpdateResponsiblePersonBO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: scm
 * @description: 学校的处理类
 * @author: jianghuimin02
 * @create: 2020-04-23 15:42
 **/
@Component
public interface WmSchoolMapper {

    /**
     * 插入学校
     */
    int insertSchool(WmSchoolDB wmSchoolDB);

    /**
     * 更新学校字段
     */
    int updateSchool(WmSchoolDB wmSchoolDB);

    /**
     * 查询一个学校
     */
    WmSchoolDB selectSchoolById(int id);

    /**
     * 查询一个学校
     */
    WmSchoolDB selectSchoolBySchoolId(int schoolId);

    /**
     * 查询学校列表
     */
    List<WmSchoolDB> selectSchoolByIds(@Param("schoolPrimaryIds") List<Integer> schoolPrimaryIds);

    /**
     * 根据名字精确查找一个学校 (valid != 0)
     */
    List<WmSchoolDB> selectValidSchoolByName(String schoolName);

    /**
     * 根据学校名称前缀模糊搜索
     * @param schoolName 模糊搜索内容
     * @return 学校列表
     */
    List<WmSchoolDB> selectValidSchoolBySchoolName(@Param("schoolName") String schoolName);

    /**
     * 增加学校食堂的数量
     */
    int addCanteeNumById(int id);

    /**
     * 减少学校食堂的数量
     */
    int subCanteeNumById(int id);

    /**
     * 查询一个学校列表
     */
    List<WmSchoolDB> selectSchoolList(WmSchoolDB wmSchoolDB);

    /**
     * 根据contractorId查询学校列表
     */
    List<WmSchoolDB> selectSchoolsByContractorId(int contractorId);

    /**
     * 根据contractorId查询生效食堂关联的学校列表
     */
    List<WmSchoolDB> selectEffectSchoolsByContractorId(int contractorId);

    /**
     * 根据contractorId查询学校列表
     */
    List<WmSchoolDB> selectSchoolsByContractorIds(@Param("contractorIds") List<Integer> contractorIds);

    /**
     * 更新学校ID
     */
    int updateSchoolId(WmSchoolDB wmSchoolDB);

    /**
     * 根据id逻辑删除一个学校
     */
    void deleteSchoolById(int id);

    /**
     * 更新学校责任人
     */
    int bindResponsiblePerson(WmSchoolDB wmSchoolDB);

    /**
     * 查询学校列表
     */
    List<WmSchoolDB> selectByCondition(WmSchoolSearchCondition condition);

    /**
     * 查询学校列表
     */
    List<WmSchoolDB> selectByConditionMaster(WmSchoolSearchCondition condition);

    /**
     * 清洗学校蜂窝信息
     */
    int updateSchoolAorMsg(WmSchoolDB wmSchoolDB);

    /**
     * 根据学校责任人列表查询生效食堂关联的学校列表
     */
    List<WmSchoolDB> selectEffectSchoolsByResponsiblePersonList(@Param("responsiblePersonList") List<String> responsiblePersonList);

    /**
     * 根据用户UID查询作为学校负责人的学校列表
     */
    List<Integer> selectSchoolPrimaryIdListByResponsibleUidList(@Param("responsibleUidList") List<Integer> uidList);

    /**
     * 查询存在的学校ID列表
     */
    List<Integer> selectExistSchoolIdList(@Param("schoolIdList") List<Integer> schoolIdList);

    /**
     * 更新学校标签
     */
    int updateSchoolLabels(WmSchoolDB wmSchoolDb);

    /**
     * 更新学校当前交付状态
     */
    int updateSchoolCurrentDeliveryStatus(@Param("schoolPrimaryId") Integer schoolPrimaryId,
                                          @Param("currentDeliveryStatus") Integer currentDeliveryStatus);

    /**
     * 根据学校ID获取学校标签
     */
    String selectSchoolLabelsBySchoolId(Integer schoolId);

    /**
     * 根据学校名称模糊查询有数据权限的食堂列表
     * @param schoolName 学校名称
     * @param dslQuery DSL查询语句
     * @return List<WmSchoolDB>
     */
    List<WmSchoolDB> selectByLikeSchoolNameWithDSL(@Param("schoolName") String schoolName,
                                                   @Param("dslQuery") String dslQuery);

    /**
     * 批量更新学校责任人
     * @param wmSchoolUpdateResponsiblePersonBO wmSchoolUpdateResponsiblePersonBO
     */
    int updateSchoolResponsiblePersonByBatch(WmSchoolUpdateResponsiblePersonBO wmSchoolUpdateResponsiblePersonBO);

    /**
     * 获取最大学校ID
     */
    int getMaxSchoolId();
}
