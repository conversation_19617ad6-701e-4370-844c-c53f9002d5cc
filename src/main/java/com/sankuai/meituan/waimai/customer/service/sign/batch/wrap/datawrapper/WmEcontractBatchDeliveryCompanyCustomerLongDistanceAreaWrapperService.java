package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.aspect.AreaDataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@AreaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE)
public class WmEcontractBatchDeliveryCompanyCustomerLongDistanceAreaWrapperService implements IWmEcontractAreaDataWrapperService {

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws WmCustomerException {

        List<EcontractContentBo> result = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);

        if (econtractBatchDeliveryInfoBo == null) {
            return result;
        }

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = econtractBatchDeliveryInfoBo.getEcontractDeliveryInfoBoList();

        if (CollectionUtils.isEmpty(econtractDeliveryInfoBoList)) {
            return result;
        }

        EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBoTemp = null;
        for (EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {
            if (temp.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo() != null
                    && StringUtils.isNotEmpty(temp.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo().getDeliveryArea())) {
                econtractWmPoiSpAreaBoTemp = JSONArray.parseObject(temp.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo().getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
                if (econtractWmPoiSpAreaBoTemp == null) {
                    continue;
                }
                // 根据配送方式封装特别声明文案（企客远距离）
                econtractWmPoiSpAreaBoTemp.setSlaText(WmEcontractConstant.DEFAULT_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE_DOCUMENT);
                result.add(WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(econtractWmPoiSpAreaBoTemp));
            }
        }
        return result;
    }
}
