package com.sankuai.meituan.waimai.customer.service;

import com.dianping.cat.util.MetricHelper;
import com.google.common.base.Stopwatch;
import com.sankuai.meituan.waimai.customer.service.kp.clean.WmCusKpCleanService;
import com.sankuai.meituan.waimai.customer.util.Preconditions;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CleanLegalAuthTypeResult;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpCleanDataThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant.METRIC_SINGLE_KP_LEGAL_AUTH_TYPE_CLEAN_DATA_DURATION;
import static com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant.METRIC_SINGLE_LEGAL_KP_STATUS_CLEAN_DATA_DURATION;

/**
 * 客户KP数据清洗服务
 *
 * <AUTHOR>
 * @since 22 十一月 2022
 */
@Slf4j
@Service
public class WmCustomerKpCleanDataThriftServiceImpl implements WmCustomerKpCleanDataThriftService {

    @Resource
    private WmCusKpCleanService wmCusKpCleanService;

    @Override
    public CleanLegalAuthTypeResult cleanDataForLegalAuthType(long kpId) throws WmCustomerException, TException {
        Stopwatch st = Stopwatch.createStarted();
        try {
            Preconditions.checkArgument(kpId > 0, CustomerErrorCodeConstants.BIZ_ERROR, "KpId不合法");
            return wmCusKpCleanService.cleanDataForLegalAuthType(kpId);
        } catch (WmCustomerException var1) {
            log.warn("cleanDataForLegalAuthType fail. kpId:{}", kpId, var1);
            throw var1;
        } catch (TException var2) {
            log.error("cleanDataForLegalAuthType fail. kpId:{}", kpId, var2);
            throw var2;
        } finally {
            MetricHelper.build()
                    .name(METRIC_SINGLE_KP_LEGAL_AUTH_TYPE_CLEAN_DATA_DURATION)
                    .duration(st.elapsed(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public boolean cleanDataForLegalKpStatus(long kpId) throws WmCustomerException, TException {
        Stopwatch st = Stopwatch.createStarted();
        try {
            Preconditions.checkArgument(kpId > 0, CustomerErrorCodeConstants.BIZ_ERROR, "KpId不合法");
            return wmCusKpCleanService.cleanDataForLegalKpStatus(kpId);
        } catch (WmCustomerException var1) {
            log.warn("cleanDataForLegalKpStatus fail. kpId:{}", kpId, var1);
            throw var1;
        } catch (TException var2) {
            log.error("cleanDataForLegalKpStatus fail. kpId:{}", kpId, var2);
            throw var2;
        } finally {
            MetricHelper.build()
                    .name(METRIC_SINGLE_LEGAL_KP_STATUS_CLEAN_DATA_DURATION)
                    .duration(st.elapsed(TimeUnit.MILLISECONDS));
        }
    }
}
