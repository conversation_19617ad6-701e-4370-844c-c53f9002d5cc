package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.adapter.EcontractManagerServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.AbstractWmEcontractApplyAdapterService;
import com.sankuai.meituan.waimai.customer.service.sign.compare.phf.WmEcontractPhfCompareService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

/**
 * @description: 拼好饭子发起类型
 * @author: zhangyuanhao02
 * @create: 2024/12/13 11:39
 */
@Slf4j
@Service
public abstract class AbstractWmEcontractPhfSubApplyAdapterService extends AbstractWmEcontractApplyAdapterService {

    @Resource
    private EcontractManagerServiceAdapter econtractManagerServiceAdapter;

    @Resource
    private WmEcontractPhfCompareService wmEcontractPhfCompareService;

    // todo zyh 线程池参数
    private final static ExecutorService executorService = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(
            6,
            32,
            5L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactoryBuilder().setNameFormat("phf-pdf-create-pool-%d").build(),
                    new ThreadPoolExecutor.AbortPolicy()
    ));

    public abstract EcontractTaskApplySubTypeEnum getSubTypeEnum();

    /**
     * 封装PDF链接，仅在一个门店一个合同的场景下使用
     * @return
     */
    public BatchPdfUrlContentBo getPdfUrlContentBo(EcontractBatchContextBo batchContextBo, JSONObject downloadUrl) throws WmCustomerException {
        List<PdfUrlContentBo> techPdfInfos = new ArrayList<>();
        log.info("getPdfUrlContentBo downloadUrl:{}", JSONObject.toJSONString(downloadUrl));

        final String pdfUrlPrefix = MccConfig.getPdfUrlPrefix();
        for (String key : downloadUrl.keySet()) {
            String[] keyInfos = StringUtils.split(key, '_');
            int len = keyInfos.length;
            String pdfUrl = downloadUrl.getString(key);
            if (StringUtils.isNotEmpty(pdfUrl) && pdfUrl.startsWith(pdfUrlPrefix)) {
                pdfUrl = pdfUrl.replace(pdfUrlPrefix, "");
            }

            Long wmPoiId = Long.valueOf(keyInfos[len - 1]);
            techPdfInfos.add(new PdfUrlContentBo(Lists.newArrayList(wmPoiId),pdfUrl ));
        }

        BatchPdfUrlContentBo batchPdfUrlContentBo = new BatchPdfUrlContentBo();
        batchPdfUrlContentBo.setTechPdfInfos(techPdfInfos);
        return batchPdfUrlContentBo;
    }

    /**
     * 签约数据比对
     * @param econtractBatchBo
     * @param batchContextBo
     */
    public void compareSignData(EcontractBatchBo econtractBatchBo, EcontractBatchContextBo batchContextBo) {
        wmEcontractPhfCompareService.submitCompareTask(econtractBatchBo, batchContextBo, getSubTypeEnum());
    }

    /**
     * 一个门店一个合同的场景下，获取流程列表。
     * 返回结构: 发起类型 + "_" + 门店ID
     * @return
     */
    public List<String> getFlowList(EcontractBatchContextBo batchContextBo) throws WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.PHF_DELIVERY);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);

        if(CollectionUtils.isEmpty(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList())){
            log.warn("AbstractWmEcontractPhfSubApplyAdapterService deliveryInfoBo为空，流程中止，customerId:{}", batchContextBo.getCustomerId());
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "原始数据缺失，pdf封装失败");
        }

        List<String> flowList = new ArrayList<>();
        for (EcontractDeliveryInfoBo infoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            EcontractDeliveryPhfInfoBo phfInfoBo = infoBo.getEcontractDeliveryPhfInfoBo();
            String wmPoiId = phfInfoBo.getWmPoiId();

            String preKey = getSubTypeEnum().name() + "_" + wmPoiId;
            flowList.add(preKey);
        }

        log.info("AbstractWmEcontractPhfSubApplyAdapterService getFlowList: {}", JSONObject.toJSONString(flowList));
        return flowList;
    }

    /**
     * 根据合同参数生成PDF的URL链接
     * 同时会更新到batchContextBo中
     * @param stageBatchInfoBoList
     * @return
     */
    public void wrapCreatePdfStageWithUrl(List<StageBatchInfoBo> stageBatchInfoBoList, EcontractBatchContextBo batchContextBo) throws WmCustomerException {
        for (StageBatchInfoBo stageBatchInfoBo : stageBatchInfoBoList) {
            String stageName = stageBatchInfoBo.getStageName();
            if (!WmEcontractConstant.CREATE_PDF.equals(stageName)) {
                continue;
            }

            Transaction transaction = Cat.newTransaction("phf.sign", "batchOnlyCreatePdf");
            try {
                log.info("wrapCreatePdfStageWithUrl, 当前的子发起类型:{}, 开始创建PDF", getSubTypeEnum().getDesc());
                Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap = stageBatchInfoBo.getPdfContentInfoBoMap();

                // 这里默认一个pdfContentInfoBo对应一个pdf，如果出现多个pdfContentInfoBo对应一个，这里需要修改
                Set<Map.Entry<String, List<PdfContentInfoBo>>> entries = pdfContentInfoBoMap.entrySet();
                List<PdfContentInfoBo> allPdfContentInfoBoList = new ArrayList<>();
                for (Map.Entry<String, List<PdfContentInfoBo>> entry : entries) {
                    allPdfContentInfoBoList.addAll(entry.getValue());
                }
                log.info("wrapCreatePdfStageWithUrl, 需要创建的PDF数量：{}", allPdfContentInfoBoList.size());
                // 批量创建合同
                buildPdfContentInfoBoList(allPdfContentInfoBoList);
            } catch (Exception e) {
                transaction.setStatus(e);
                log.error("AbstractWmEcontractPhfSubApplyAdapterService#wrapCreatePdfStageWithUrl 异常", e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "装载PDF链接异常");
            } finally {
                transaction.complete();
            }

            log.info("wrapCreatePdfStageWithUrl, 创建PDF完成 stageBatchInfoBo: {}", JSONObject.toJSONString(stageBatchInfoBo));
            // 更新batchContext中的URL
            updateBatchContextUrl(batchContextBo, stageBatchInfoBo);
        }
    }

    /**
     * 更新batchContext中的URL
     * @param batchContextBo
     * @param stageBatchInfoBo
     */
    private void updateBatchContextUrl(EcontractBatchContextBo batchContextBo, StageBatchInfoBo stageBatchInfoBo) {
        if (MapUtils.isEmpty(stageBatchInfoBo.getPdfContentInfoBoMap())) {
            return;
        }
        String downLoadUrl = batchContextBo.getDownLoadUrl();
        log.info("AbstractWmEcontractPhfSubApplyAdapterService#updateBatchContextUrl 当前类型:{}, 更新前PDF链接:{}", getSubTypeEnum().getName(), downLoadUrl);
        JSONObject jo = StringUtils.isBlank(downLoadUrl) ? new JSONObject() : JSONObject.parseObject(downLoadUrl);
        stageBatchInfoBo.getPdfContentInfoBoMap().forEach((key, pdfContentInfoBoList) -> {
            if (CollectionUtils.isEmpty(pdfContentInfoBoList) || pdfContentInfoBoList.size() != 1) {
                log.warn("AbstractWmEcontractPhfSubApplyAdapterService#updateBatchContextUrl 异常, pdfContentInfoBoList = {}", JSON.toJSONString(pdfContentInfoBoList));
                return;
            }
            Optional<PdfContentInfoBo> optional = pdfContentInfoBoList.stream().findFirst();
            if (optional.isPresent()) {
                jo.put(key, optional.get().getPdfUrl());
            }
        });
        downLoadUrl = jo.toJSONString();
        batchContextBo.setDownLoadUrl(downLoadUrl);
        log.info("AbstractWmEcontractPhfSubApplyAdapterService#updateBatchContextUrl 当前类型:{}, 更新后PDF链接:{}", getSubTypeEnum().getName(), downLoadUrl);
    }

    private void buildPdfContentInfoBoList(List<PdfContentInfoBo> pdfContentInfoBos) throws WmCustomerException, ExecutionException, InterruptedException, TimeoutException {
        if (CollectionUtils.isEmpty(pdfContentInfoBos)) {
            return;
        }

        // 分批次调用
        List<List<PdfContentInfoBo>> partition = Lists.partition(pdfContentInfoBos, getBatchSize());

        // 多线程获取去创建PDF列表
        List<Future> futureList = Lists.newArrayList();
        for (List<PdfContentInfoBo> currentPdfContentInfos : partition) {
            futureList.add(executorService.submit(new WrapPdfUrlThread(currentPdfContentInfos)));
        }

        // 等待线程返回
        StringBuilder failMsg = new StringBuilder();
        for (Future future : futureList) {
            failMsg.append(future.get(MccConfig.getPhfBatchCreatePdfMaxDelayTime(), TimeUnit.SECONDS));
        }
        log.info("AbstractWmEcontractPhfSubApplyAdapterService#buildPdfContentInfoBoList final PdfContentInfoBo:{}", JSON.toJSONString(pdfContentInfoBos));
        if (StringUtils.isNotBlank(failMsg.toString())) {
            log.error("AbstractWmEcontractPhfSubApplyAdapterService#buildPdfContentInfoBoList 异常, failMsg = {}", failMsg);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, failMsg.toString());
        }
    }

    /**
     * 获取批次大小
     * @return
     */
    public int getBatchSize(){
        EcontractTaskApplySubTypeEnum subTypeEnum = getSubTypeEnum();
        if (subTypeEnum == EcontractTaskApplySubTypeEnum.PHF_FORMAL) {
            return MccConfig.getBathOnlyCreatePdfWithMultiPoiMaxSize();
        }

        return MccConfig.getBatchOnlyCreatePdfMaxSize();
    }

    /**
     * 更新PDF的元数据信息（删除掉创建PDF已使用的参数）
     * @param contentInfoBo
     */
    public void updatePdfMetaContent(PdfContentInfoBo contentInfoBo) {
        Map<String, String> oldPdfMetaContentMap = contentInfoBo.getPdfMetaContent();
        Map<String, String> newPdfMetaContentMap = new HashMap<>();

        // 合同签章信息
        if (oldPdfMetaContentMap.containsKey("partAName")) {
            newPdfMetaContentMap.put("partAName", oldPdfMetaContentMap.getOrDefault("partAName", ""));
        }

        if (oldPdfMetaContentMap.containsKey("partBName")) {
            newPdfMetaContentMap.put("partBName", oldPdfMetaContentMap.getOrDefault("partBName", ""));
        }

        if (oldPdfMetaContentMap.containsKey("partCName")) {
            newPdfMetaContentMap.put("partBName", oldPdfMetaContentMap.getOrDefault("partBName", ""));
        }

        // 全局合同使用wmPoiId、poiInfo
        if (oldPdfMetaContentMap.containsKey("wmPoiId")) {
            newPdfMetaContentMap.put("wmPoiId", oldPdfMetaContentMap.getOrDefault("wmPoiId", ""));
        }
        if (oldPdfMetaContentMap.containsKey("poiInfo")) {
            newPdfMetaContentMap.put("poiInfo", oldPdfMetaContentMap.getOrDefault("poiInfo", ""));
        }

        contentInfoBo.setPdfMetaContent(newPdfMetaContentMap);
    }

    /**
     * 批量创建合同并获取链接
     */
    class WrapPdfUrlThread implements Callable<String> {

        List<PdfContentInfoBo> pdfContentInfoBoList;

        public WrapPdfUrlThread(List<PdfContentInfoBo> pdfContentInfoBoList) {
            this.pdfContentInfoBoList = pdfContentInfoBoList;
        }

        @Override
        public String call(){
            try {
                if (CollectionUtils.isEmpty(pdfContentInfoBoList)) {
                    return Strings.EMPTY;
                }

                List<String> pdfUrlList = econtractManagerServiceAdapter.batchOnlyCreatePdf(pdfContentInfoBoList);
                log.info("WrapPdfUrlThread 生成的pdf链接列表：{}", JSONObject.toJSONString(pdfUrlList));
                if (pdfContentInfoBoList.size() != pdfUrlList.size()) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "生成PDF数量与创建PDF数量不一致");
                }
                for (int i = 0; i < pdfUrlList.size(); i++) {
                    String pdfUrl = pdfUrlList.get(i);
                    PdfContentInfoBo contentInfoBo = pdfContentInfoBoList.get(i);

                    // pdf链接
                    contentInfoBo.setPdfUrl(pdfUrl);
                    updatePdfMetaContent(contentInfoBo);
                    // 清除PdfBizContent
                    contentInfoBo.setPdfBizContent(null);
                }
                log.info("WrapPdfUrlThread 生成pdf链接完成 pdfContentInfoBoList:{}", JSONObject.toJSONString(pdfContentInfoBoList));
            } catch (Exception e) {
                log.error("WrapPdfUrlThread 生成pdf链接异常, pdfContentInfoBoList:{}", JSONObject.toJSONString(pdfContentInfoBoList), e);
                return "生成pdf链接异常";
            }
            return Strings.EMPTY;
        }
    }

}
