package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.DeleteSignerSceneEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.PoiEffectQuaInfoChangeMsgDTO;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmPoiQualificationInfoDTO;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpRealAuthService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 20240428
 * @desc 门店资质信息变更同步客户资质
 */
@Service
@Slf4j
public class PoiEffectQuaInfoChangeListener implements IMessageListener {


    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;


    @Autowired
    protected WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;


    @Autowired
    WmEmployClient wmEmployClient;

    /**
     * 门店资质营业执照type
     */
    private static final int CUSTOMER_BUSINESS_CERT_TYPE = 1;

    /**
     * 门店资质营业执照second_type
     */
    private static final int CUSTOMER_BUSINESS_CERT_SECOND_TYPE = 0;

    /**
     * 门店资质URL字段名称
     */
    private static final String POI_QUA_URL = "url";

    /**
     * 门店资质法人字段名称
     */
    private static final String POI_QUA_LEGAL_PERSON = "legal_person";

    /**
     * 门店资质经营范围字段名称
     */
    private static final String POI_QUA_BUSINESS_SCOPE = "business_scope";

    /**
     * 门店资质经营范围字段名称-明细字段（拼接资质使用）
     */
    private static final String POI_QUA_BUSINESS_SCOPE_DETAIL = "scope";

    /**
     * 门店资质有效期字段名称
     */
    private static final String POI_QUA_VALIDATE_DATE = "validate_date";

    /**
     * 门店资质地址字段名称
     */
    private static final String POI_QUA_ADDRESS = "address";

    /**
     * 门店资质经营人或法人字段名称
     */
    private static final String POI_QUA_OWNER = "owner";

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        String msg = message.getBody().toString();
        try {
            log.info("PoiEffectQuaInfoChangeListener,监听门店资质变更消息,msg={}", msg);
            //判空
            if (null == message.getBody() || StringUtils.isBlank(msg)) {
                log.info("PoiEffectQuaInfoChangeListener,监听门店资质变更消息对象为空，请及时关注,message={}", JSON.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            PoiEffectQuaInfoChangeMsgDTO poiEffectQuaInfoChangeMsgDTO = JSON.parseObject(msg, PoiEffectQuaInfoChangeMsgDTO.class);
            if (poiEffectQuaInfoChangeMsgDTO == null || poiEffectQuaInfoChangeMsgDTO.getDiffMapJson() == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            Map<String, Object> diffMap = JSON.parseObject(poiEffectQuaInfoChangeMsgDTO.getDiffMapJson(), Map.class);
            //变更diff为空则直接返回
            if (MapUtils.isEmpty(diffMap)) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,资质编号发生变更不需要关注,diffMap={}", JSONObject.toJSONString(diffMap));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //资质编号变更不需要关注
            if (diffMap.get("number") != null) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,资质编号发生变更不需要关注,diffMap={}", JSONObject.toJSONString(diffMap));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //校验关注字段是否发生变更
            if (diffMap.get(POI_QUA_URL) == null && diffMap.get(POI_QUA_LEGAL_PERSON) == null
                    && diffMap.get(POI_QUA_BUSINESS_SCOPE) == null && diffMap.get(POI_QUA_VALIDATE_DATE) == null
                    && diffMap.get(POI_QUA_ADDRESS) == null && diffMap.get(POI_QUA_OWNER) == null) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,关注的信息字段未发生变更,diffMap={}", JSONObject.toJSONString(diffMap));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            WmPoiQualificationInfoDTO wmPoiQualificationInfoDTO = poiEffectQuaInfoChangeMsgDTO.getData();
            log.info("PoiEffectQuaInfoChangeListener.recvMessage,wmPoiQualificationInfoDTO={}", JSON.toJSONString(wmPoiQualificationInfoDTO));

            //type=1 && getSecond_subtype=0 标识营业执照资质变更
            if (wmPoiQualificationInfoDTO.getType() != CUSTOMER_BUSINESS_CERT_TYPE || wmPoiQualificationInfoDTO.getSecond_subtype() != CUSTOMER_BUSINESS_CERT_SECOND_TYPE) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,非营业执照资质变更，不需要关注,diffMap={}", JSONObject.toJSONString(diffMap));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //根据资质编号查询客户信息
            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getNewCustomerByNumberAndType(wmPoiQualificationInfoDTO.getNumber(), 1);
            if (wmCustomerBasicBo == null) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,根据资质编号未查询到客户信息，不需要同步");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //未命中灰度则直接返回
            if (!wmCustomerGrayService.hitPoiQuaAutoSyncCustomerFlowGray(wmCustomerBasicBo.getId())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //非外卖单店客户类型，不需要同步
            if (wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue()) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,客户类型非外卖单店，不需要同步");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //客户未生效过，不需要同步
            if (wmCustomerBasicBo.getEffective() != CustomerConstants.EFFECT) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,客户未生效过，不需要同步");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //客户非纸质形式，不需要同步
            if (wmCustomerBasicBo.getCertificateType() != CertificateTypeEnum.PAPER.getType()) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,客户非纸质形式，不需要同步");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //查询客户下绑定或绑定的门店数量，如果数量不为1，则不需要同步。
            Integer cnt = wmCustomerPoiDBMapper.countBindingOrBindPoiByCustomerId(wmCustomerBasicBo.getId());
            if (cnt == null || cnt != 1) {
                log.info("PoiEffectQuaInfoChangeListener.recvMessage,客户下绑定门店数不是1个不需要同步");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //同步变更信息至客户平台
            syncPoiQuaInfo2WmCustomer(wmPoiQualificationInfoDTO, wmCustomerBasicBo, diffMap);
            log.info("PoiEffectQuaInfoChangeListener.recvMessage,同步变更门店资质信息到客户侧完成,customerId={},wmPoiQualificationInfoDTO={}",
                    wmCustomerBasicBo.getId(), JSON.toJSONString(wmPoiQualificationInfoDTO));

        } catch (Exception e) {
            log.error("PoiEffectQuaInfoChangeListener.recvMessage,同步门店变更生效资质到客户资质发生异常,msg={}", msg, e);
            return ConsumeStatus.RECONSUME_LATER;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 同步变更资质信息到客户侧：
     * 1-同步信息到客户平台 2-同步客户信息到客户侧 3-记录操作日志
     *
     * @param wmPoiQualificationInfoDTO
     * @param wmCustomerBasicBo
     * @return
     */
    private boolean syncPoiQuaInfo2WmCustomer(WmPoiQualificationInfoDTO wmPoiQualificationInfoDTO,
                                              WmCustomerBasicBo wmCustomerBasicBo, Map<String, Object> diffMap) throws WmCustomerException {

        try {
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(wmCustomerBasicBo.getId());
            //记录操作日志
            String opLog = "";

            if (wmCustomerDB == null) {
                log.info("syncPoiQuaInfo2WmCustomer,根据客户ID未查到客户信息，不进行处理,customerId={}", wmCustomerBasicBo.getId());
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "根据客户id查询客户信息失败");
            }
            //客户法人是否变更
            boolean legalPersonChange = false;
            String oldCustomerLegalPerson = null;

            //法定代表人/经营者
            if (diffMap.get(POI_QUA_LEGAL_PERSON) != null && !wmCustomerBasicBo.getLegalPerson().equals(wmPoiQualificationInfoDTO.getLegal_person())) {
                opLog = opLog + "法定代表人/经营者: " + wmCustomerDB.getLegalPerson() + " => " + wmPoiQualificationInfoDTO.getLegal_person() + ";\n";
                legalPersonChange = true;
                oldCustomerLegalPerson = wmCustomerDB.getLegalPerson();
                wmCustomerDB.setLegalPerson(wmPoiQualificationInfoDTO.getLegal_person());
            }
            //经营范围 现在资质侧存储形式有2种：json串存储含有scope标识经营范围/字符串形式存储经营范围
            if (diffMap.get(POI_QUA_BUSINESS_SCOPE) != null) {
                if (wmPoiQualificationInfoDTO.getBusiness_scope().contains(POI_QUA_BUSINESS_SCOPE_DETAIL)) {
                    Map<String, Object> bussinessScopeMap = JSON.parseObject(wmPoiQualificationInfoDTO.getBusiness_scope(), Map.class);
                    if (!bussinessScopeMap.get(POI_QUA_BUSINESS_SCOPE_DETAIL).equals(wmCustomerDB.getBusinessScope())) {
                        opLog = opLog + "经营范围: " + wmCustomerDB.getBusinessScope() + " => " + bussinessScopeMap.get(POI_QUA_BUSINESS_SCOPE_DETAIL) + ";\n";
                        wmCustomerDB.setBusinessScope(bussinessScopeMap.get(POI_QUA_BUSINESS_SCOPE_DETAIL).toString());
                    }
                } else {
                    if (!wmPoiQualificationInfoDTO.getBusiness_scope().equals(wmCustomerDB.getBusinessScope())) {
                        opLog = opLog + "经营范围: " + wmCustomerDB.getBusinessScope() + " => " + wmPoiQualificationInfoDTO.getBusiness_scope() + ";\n";
                        wmCustomerDB.setBusinessScope(wmPoiQualificationInfoDTO.getBusiness_scope());
                    }
                }
            }
            //经营场所
            if (diffMap.get(POI_QUA_ADDRESS) != null && !wmCustomerBasicBo.getAddress().equals(wmPoiQualificationInfoDTO.getAddress())) {
                opLog = opLog + "经营场所: " + wmCustomerDB.getAddress() + " => " + wmPoiQualificationInfoDTO.getAddress() + ";\n";
                wmCustomerDB.setAddress(wmPoiQualificationInfoDTO.getAddress());
            }
            //名称
            if (diffMap.get(POI_QUA_OWNER) != null && !wmCustomerDB.getCustomerName().equals(wmPoiQualificationInfoDTO.getOwner())) {
                opLog = opLog + "名称: " + wmCustomerDB.getCustomerName() + " => " + wmPoiQualificationInfoDTO.getOwner() + ";\n";
                wmCustomerDB.setCustomerName(wmPoiQualificationInfoDTO.getOwner());
            }
            // 营业执照有效期
            if (diffMap.get(POI_QUA_VALIDATE_DATE) != null && wmCustomerBasicBo.getValidateDate() != wmPoiQualificationInfoDTO.getValidate_date()) {
                opLog = opLog + "营业执照有效期: " + wmCustomerDB.getValidateDate() + " => " + wmPoiQualificationInfoDTO.getValidate_date() + ";\n";
                wmCustomerDB.setValidateDate(wmPoiQualificationInfoDTO.getValidate_date());
            }
            //营业执照图片
            if (diffMap.get("url") != null && !wmCustomerDB.getPicUrl().equals(wmPoiQualificationInfoDTO.getUrl())) {
                opLog = opLog + "营业执照图片: " + wmCustomerDB.getPicUrl() + " => " + wmPoiQualificationInfoDTO.getUrl() + ";\n";
                wmCustomerDB.setPicUrl(wmPoiQualificationInfoDTO.getUrl());
            }

            //同步变更信息到客户平台
            mtCustomerThriftServiceAdapter.updateMtCustomer(wmCustomerDB, false);
            //同步客户信息到客户侧
            wmCustomerService.updateCustomer(wmCustomerDB, 0);
            //添加操作记录
            insertCustomerOpLog(wmCustomerBasicBo.getId(), opLog, wmPoiQualificationInfoDTO.getWm_poi_id());
            //法人变更则需要处理KP
            if (legalPersonChange) {
                deleteKpOnLegalPersonChange(wmCustomerDB, oldCustomerLegalPerson);
            }


        } catch (Exception e) {
            log.error("syncPoiQuaInfo2WmCustomer,同步外卖单店变更资质信息到客户资质发生异常,customerId={},wmPoiQualificationInfoDTO={},diffMap={}",
                    wmCustomerBasicBo.getId(), JSON.toJSONString(wmPoiQualificationInfoDTO), JSON.toJSONString(diffMap), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
        return true;
    }


    /**
     * 插入客户操作日志
     */
    private void insertCustomerOpLog(Integer customerId, String logStr, Long wmPoiId) {
        try {
            if (StringUtils.isBlank(logStr)) {
                return;
            }
            WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId, WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
            wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.UPDATE.type);
            wmCustomerOplogBo.setLog(logStr);
            wmCustomerOplogBo.setOpUid(0);
            wmCustomerOplogBo.setOpUname(String.format("门店%s发起修改，审核通过自动同步客户", wmPoiId));
            wmCustomerOplogBo.setRemark(null);
            wmCustomerOplogService.insert(wmCustomerOplogBo);
        } catch (Exception e) {
            log.error("PoiEffectQuaInfoChangeListener,监听门店资质变更消息同步资质记录操作记录发生异常,customerId={},logStr={},wmPoiId={}",
                    customerId, logStr, wmPoiId, e);
        }

    }

    /**
     * 法人你变更需要删除KP操作
     *
     * @param wmCustomerDB
     */
    private void deleteKpOnLegalPersonChange(WmCustomerDB wmCustomerDB, String oldCustomerLegalPerson) {

        Integer customerId = wmCustomerDB.getId();
        Long mtCustomerId = wmCustomerDB.getMtCustomerId();
        try {
            //客户法人变更删除签约人(签约类型为法人)
            WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(customerId);
            if (wmCustomerKp != null && KpSignerTypeEnum.SIGNER.getType() == wmCustomerKp.getSignerType()) {
                wmCustomerKpService.deleteKpSignerForCustomerUpdate(DeleteSignerSceneEnum.POI_QUA_OWNER_CHANGE, wmCustomerKp,
                        CustomerType.CUSTOMER_TYPE_BUSINESS, 0, "客户系统");
                //签约人删除后掉客户企业四要素标签
                wmCustomerKpRealAuthService.deleteFourEleTag(mtCustomerId);
                sendDelSingerMsg(oldCustomerLegalPerson, wmCustomerDB, CustomerType.CUSTOMER_TYPE_BUSINESS);
                log.info("deleteKpOnLegalPersonChange,当前KP类型是签约人的数据不符合规则，已删除。customerId={}", customerId);
            }
            //处理KP法人
            wmCustomerKpService.customerEffectForLegalKpOperate(wmCustomerDB, oldCustomerLegalPerson, WmCustomerConstant.POI_QUA_LEGAL_PERSON_UPDATE);
            log.info("deleteKpOnLegalPersonChange,当前KP类型是法人的数据不符合规则，已删除。customerId={}", customerId);

        } catch (Exception e) {
            log.error("deleteKpOnLegalPersonChange,同步门店资质法人变更并删除KP操作发生异常,customerId={}", customerId, e);
        }
    }

    /**
     * 资质类型为个人证件时客户姓名变更，自动删除签约人信息通知
     *
     * @param nameBefore
     * @param after
     */
    private void sendDelSingerMsg(String nameBefore, WmCustomerDB after, CustomerType customerType) {
        if (after == null || customerType == null) {
            return;
        }
        Integer ownerUid = after.getOwnerUid();
        if (ownerUid == null || ownerUid.intValue() <= 0) {
            log.warn("sendDelSingerMsg 客户责任人为空 name={},after={}", nameBefore, JSONObject.toJSONString(after));
            return;
        }
        String msg = String.format("您负责的客户（%s%s）客户信息中法定代表人由%s变更为%s，系统已自动删除%s的签约人信息，请及时补录签约人信息。", after.getCustomerName(),
                after.getMtCustomerId(), nameBefore, after.getLegalPerson(), nameBefore);
        sendMsg(ownerUid, msg);
    }


    /**
     * 发送消息
     *
     * @param uid
     * @param msg
     */
    private void sendMsg(Integer uid, String msg) {
        try {
            if (uid == null) {
                return;
            }
            WmEmploy originalOwner = wmEmployClient.getEmployById(uid);
            if (originalOwner != null && StringUtils.isNotEmpty(msg)) {
                DaxiangUtilV2.push(msg, String.format("%<EMAIL>", originalOwner.getMisId()));
                log.info("sendMsg,给客户责任人发送客户资质变更消息 uid={},msg={}", uid, msg);
            }
        } catch (Exception e) {
            log.error("sendMsg::uid = {}, msg = {}", uid, msg, e);
        }
    }
}
