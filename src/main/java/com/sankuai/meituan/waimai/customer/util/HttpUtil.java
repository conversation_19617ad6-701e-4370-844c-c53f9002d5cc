package com.sankuai.meituan.waimai.customer.util;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

public class HttpUtil {

    public static byte[] getBytesFrommUrl(String url) {
        if (StringUtils.isBlank(url)) {
            throw new RuntimeException("getBytesFrommUrl::url为空");
        }
        HttpClient client = new HttpClient();
        GetMethod get = new GetMethod(url);
        int statusCode;
        byte[] bytes;
        try {
            statusCode = client.executeMethod(get);
            if (statusCode != org.apache.commons.httpclient.HttpStatus.SC_OK) {
                throw new RuntimeException("getBytesFrommUrl失败::url = " + url);
            }
            bytes = get.getResponseBody();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return bytes;
    }
}
