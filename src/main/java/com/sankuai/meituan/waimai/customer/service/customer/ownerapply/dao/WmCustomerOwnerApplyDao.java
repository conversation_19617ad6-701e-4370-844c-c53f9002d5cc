package com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao;

import com.sankuai.meituan.waimai.customer.dao.WmCustomerOwnerApplyMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyListDbQuery;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyQueryBO;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerStepEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyAddDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WmCustomerOwnerApplyDao {

    @Autowired
    private WmCustomerOwnerApplyMapper wmCustomerOwnerApplyMapper;

    /**
     * 新增客户责任人申请
     * 
     * @param applyAddDTO
     * @param wmCustomerDB
     * @return
     */
    public WmCustomerOwnerApply insert(CustomerOwnerApplyAddDTO applyAddDTO, WmCustomerDB wmCustomerDB,
            Boolean ownerInWbMerchantOrgFlag) {
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setCustomerId(applyAddDTO.getCustomerId());
        apply.setMtCustomerId(wmCustomerDB.getMtCustomerId());
        apply.setApplyUid(applyAddDTO.getUid());
        apply.setApplyReason(applyAddDTO.getApplyReason());
        apply.setCustomerOwnerUid(wmCustomerDB.getOwnerUid());
        apply.setCustomerNumber(wmCustomerDB.getCustomerNumber());
        apply.setGroupId(0L);
        if (wmCustomerDB.getOwnerUid() == null || wmCustomerDB.getOwnerUid() == 0 || ownerInWbMerchantOrgFlag) {
            apply.setStepId(CustomerOwnerStepEnum.CREATE_DIRECT_SUC.getCode());
        } else {
            apply.setStepId(CustomerOwnerStepEnum.CREATE_TO_AUDIT.getCode());
        }
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());

        apply.setCustomerRealType(wmCustomerDB.getCustomerRealType());

        wmCustomerOwnerApplyMapper.insert(apply);
        return apply;
    }

    /**
     * 根据参数查询总数
     * 
     * @param queryBO
     * @return
     */
    public int countByQueryBo(WmCustomerOwnerApplyQueryBO queryBO) {
        return wmCustomerOwnerApplyMapper.countByCustomerOwnerApplyQueryBo(queryBO);
    }

    /**
     * 查询当前页最大的ID
     * 
     * @param queryBO
     * @return
     */
    public Integer countMaxIdByQueryBo(WmCustomerOwnerApplyQueryBO queryBO) {
        return wmCustomerOwnerApplyMapper.countMaxIdByQueryBo(queryBO);
    }

    /**
     * 根据参数请求分页查询
     * 
     * @param queryBO
     * @return
     */
    public List<WmCustomerOwnerApply> listCustomerOwnerApplyByQueryBo(WmCustomerOwnerApplyQueryBO queryBO) {
        return wmCustomerOwnerApplyMapper.listCustomerOwnerApplyByQueryBo(queryBO);
    }

    /**
     * 根据客户ID查询处理中客户责任人申请记录
     * 
     * @param customerId
     * @return
     */
    public WmCustomerOwnerApply getDoingApplyByCustomerId(Integer customerId) {
        return wmCustomerOwnerApplyMapper.getDoingApplyByCustomerId(customerId);
    }

    public List<WmCustomerOwnerApply> listByCustomerId(Integer customerId) {
        return wmCustomerOwnerApplyMapper.listByCustomerId(customerId);
    }

    /**
     * 更新客户责任人申请记录状态
     * 
     * @param id
     * @param status
     */
    public void updateStatusById(Integer id, Integer status,Integer oldStatus) {
        wmCustomerOwnerApplyMapper.updateStatusById(id, status, oldStatus);
    }

    public WmCustomerOwnerApply getWmCustomerOwnerApplyById(Integer id) {
        return wmCustomerOwnerApplyMapper.getWmCustomerOwnerApplyById(id);
    }

    /**
     * 更新客户责任人申请记录groupId
     *
     * @param id
     * @param groupId
     */
    public void updateGroupIdById(Integer id, Long groupId) {
        wmCustomerOwnerApplyMapper.updateGroupIdById(id, groupId);
    }

    /**
     * 根据ID列表更新客户类型
     * 
     * @param ids
     * @param customerRealType
     */
    public void updateCustomerRealTypeByIds(List<Integer> ids, Integer customerRealType) {
        wmCustomerOwnerApplyMapper.updateCustomerRealTypesByIds(ids, customerRealType);
    }

    public List<WmCustomerOwnerApply> listDoingApplyByTimeCondition(WmCustomerOwnerApplyListDbQuery queryBO) {
        return wmCustomerOwnerApplyMapper.listDoingApplyByTimeCondition(queryBO);
    }
}
