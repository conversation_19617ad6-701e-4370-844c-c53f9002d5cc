package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant.FLOW_WITH_POI_MT_QDB_STAMP;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAQDBWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractSlaDataWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampQDBWrapperService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 结算信息与配送信息申请电子合同平台（钱袋宝版）
 * 生成pdf -> 钱袋宝CA认证 -> 发短信 -> 钱袋宝签章 -> 合同归档 -> 完成合同
 */
@Service
public class WmEcontractBatchSmDApplyService extends AbstractWmEcontractApplyAdapterService {
    private static final String FLOW_SETTLE = "settle";

    private static final String FLOW_DELIVEY = "delivery";

    private static List<String> flowList = Lists.newArrayList();

    private static List<String> qdbStampList = Lists.newArrayList();

    private static List<String> poiStampList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_SETTLE);
        flowList.add(FLOW_DELIVEY);

        qdbStampList.add(FLOW_SETTLE);

        poiStampList.add(FLOW_SETTLE);


        dataWrapperMap.put(FLOW_SETTLE, EcontractDataWrapperEnum.SETTLE);
        dataWrapperMap.put(FLOW_DELIVEY, EcontractDataWrapperEnum.DELIVERY);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAQDBWrapperService wmEcontractCAQDBWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampQDBWrapperService wmEcontractStampQDBWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Autowired
    private WmEcontractSlaDataWrapperService wmEcontractDeliverySlaDataWrapperService;

    @Autowired
    private WmEcontractCAMTWrapperService wmEcontractCAMTWrapperService;

    @Autowired
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;


    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo,EcontractSignDataFactor econtractSignDataFactor)
        throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        Map<String, EcontractDataWrapperEnum> dataWrapperMapActually = WmEcontractContextUtil
                .analysisDataWrapperMapForDelivery(batchContextBo,
                        Maps.newHashMap(dataWrapperMap));

        List<String> currentPoiStampList = Lists.newArrayList(poiStampList);
       if(WmEcontractContextUtil.isSupport(batchContextBo,dataWrapperMapActually)){
           currentPoiStampList.add(FLOW_DELIVEY);
       }

        List<String> currentFlowList = Lists.newArrayList(flowList);
        List<String> currentMtStampList = Lists.newArrayList();
        String currentEcontractType = TaskConstant.TYPE_WM_CUSTOMER_BATCH_SETTLEMOON_DELIVERY;

        if (econtractSignDataFactor.isDeliverySupportWholeCity()) {
            WmEcontractContextUtil.assembleFlowForDeliverySupportWholeCity(dataWrapperMapActually, currentFlowList);
        }

        if(econtractSignDataFactor.isDeliverySupportAggregation()){
            WmEcontractContextUtil.assembleFlowForDeliveryAggregation(dataWrapperMapActually, currentPoiStampList, currentMtStampList, currentFlowList);
            //增加美团CA和美团签章数据
            batchInfoBoList.add(wmEcontractCAMTWrapperService.wrap(batchContextBo));
            batchInfoBoList.add(wmEcontractStampMTWrapperService.wrap(batchContextBo, currentMtStampList));
            //签约流程为商家签章+美团签章+钱袋宝签章
            currentEcontractType = FLOW_WITH_POI_MT_QDB_STAMP;
        }

        if (econtractSignDataFactor.isDeliverySupportCompanyCustomerLongDistanceDelivery()) {
            WmEcontractContextUtil.assembleFlowForDeliveryCompanyCustomerLongDistance(dataWrapperMapActually, currentFlowList);
        }

        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMapActually));
        batchInfoBoList.add(wmEcontractCAQDBWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampQDBWrapperService.wrap(batchContextBo, qdbStampList));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, currentPoiStampList));
        wmEcontractDeliverySlaDataWrapperService.addContext(batchInfoBoList,batchContextBo,dataWrapperMapActually.get(FLOW_DELIVEY));
        wmEcontractDeliverySlaDataWrapperService.addContext(batchInfoBoList,batchContextBo,dataWrapperMapActually.get(EcontractTaskApplySubTypeEnum.DELIVERY_WHOLE_CITY.getName()));

        return new EcontractBatchBo.Builder()
            .token(getToken())
            .econtractBizId(getBizId(batchContextBo))
            .econtractType(currentEcontractType)
            .stageInfoBoList(batchInfoBoList)
            .flowList(currentFlowList)
            .callBackUrl(getCallbackUrl())
            .build();
    }
}
