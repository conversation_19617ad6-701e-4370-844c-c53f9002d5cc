package com.sankuai.meituan.waimai.customer.service.sign.biz;

import com.google.common.collect.Lists;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 一个任务的关联任务
 * 同一个batch下其他任务
 */
@Service
public class WmEcontractTaskRelService {

    @Resource
    private WmEcontractTaskBizService wmEcontractTaskService;

    /**
     * 找到一个task下关联的当前batch下的其他task
     */
    public List<EcontractTaskBo> getRelTask(EcontractTaskBo taskBo) {
        if (taskBo.getBatchId() == 0L) {
            return Lists.newArrayList();
        }

        List<EcontractTaskBo> taskBoList = wmEcontractTaskService.getByBatchId(taskBo.getBatchId());
        List<EcontractTaskBo> relBoList = Lists.newArrayList();
        for (EcontractTaskBo store:taskBoList) {
            if (store.getId().equals(taskBo.getId())) {
                continue;
            }

            relBoList.add(store);
        }
        return relBoList;
    }

}
