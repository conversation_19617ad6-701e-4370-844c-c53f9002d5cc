package com.sankuai.meituan.waimai.customer.settle.dao;

import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleDB;
import com.sankuai.meituan.waimai.datasource.multi.annotation.DataSource;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@DataSource("dbContractWrite")
public interface WmPoiSettleDBMapper {
    public final static String INSERT_KEYS = "wm_contract_id, wm_settle_id, wm_poi_id, ctime, utime, valid";
    public final static String SELECT_KEYS = "id, " + INSERT_KEYS;

    @Options(useGeneratedKeys = true)
    int insertSelective(WmPoiSettleDB record);

    int updateByContractAndPoiId(WmPoiSettleDB wmPoiSettleDB);

    @Update("update wm_poi_settle set valid=0 where wm_contract_id=#{contractId}")
    @DataSource("dbContractWrite")
    void deleteWmPoiSettleByContractId(@Param("contractId") int contractId);

    int deleteByWmSettleId(Integer wmSettleId);

    int deleteByWmSettleIdList(@Param("wmSettleIdList")List<Integer> wmSettleIdList);

    @Update("update wm_poi_settle set valid=0, utime=unix_timestamp() where wm_contract_id = #{wmContractId} and id in(${ids})")
    int deleteBatch(@Param("wmContractId") int wmContractId, @Param("ids") String ids);

    @Update("update wm_poi_settle set valid=0, utime=unix_timestamp() where wm_contract_id = #{wmContractId} and wm_settle_id in(${ids})")
    int deleteByWmContractIdAndWmSettleIdList(@Param("wmContractId") int wmContractId, @Param("ids") String ids);

    @Update("update wm_poi_settle set valid=0, utime=unix_timestamp() where wm_contract_id = #{wmContractId} and wm_poi_id = #{wmPoiId} and valid = 1")
    int deleteByWmContractIdAndWmPoiId(@Param("wmContractId") int wmContractId, @Param("wmPoiId") Integer wmPoiId);

    @Update("update wm_poi_settle set valid=0, utime=unix_timestamp() where wm_contract_id = #{wmContractId} and wm_poi_id in(${wmPoiIds})")
    int deleteByWmContractIdAndWmPoiIdList(@Param("wmContractId") int wmContractId, @Param("wmPoiIds") String wmPoiIds);

    int deleteByWmCustomerIdAndWmPoiIdList(@Param("wmCustomerId") int wmCustomerId, @Param("wmPoiIdList") List<Long> wmPoiIdList);

    @Select("/*master*/select " + SELECT_KEYS + " from wm_poi_settle where wm_settle_id = #{settleId} and valid = 1")
    @DataSource("dbContractWrite")
    List<WmPoiSettleDB> getByWmSettleIdMaster(@Param("settleId") Integer settleId);

    @DataSource("dbContractRead")
    List<WmPoiSettleDB> getByWmSettleId(Integer id);

    @Select("select wm_poi_id from wm_poi_settle where wm_settle_id = #{settleId} and valid = 1")
    @DataSource("dbContractRead")
    List<Integer> getWmPoiIdListBySettleId(@Param("settleId") Integer wmSettleId);

    @Select("select wm_poi_id from wm_poi_settle where wm_settle_id = #{settleId} and wm_contract_id = #{wmContractId} and valid = 1")
    List<Integer> getWmPoiIdListBySettleIdAndWmContractId(@Param("settleId") Integer wmSettleId,@Param("wmContractId") Integer wmContractId);

    @Select("/*master*/select wm_poi_id from wm_poi_settle where wm_settle_id = #{settleId} and valid = 1")
    List<Integer> getWmPoiIdListBySettleIdMaster(@Param("settleId") Integer wmSettleId);

    @Select("/*master*/select wm_poi_id from wm_poi_settle where wm_settle_id = #{settleId} and wm_contract_id = #{wmContractId} and valid = 1")
    List<Integer> getWmPoiIdListBySettleIdAndWmContractIdMaster(@Param("settleId") Integer wmSettleId,@Param("wmContractId") Integer wmContractId);

    @Select("select " + SELECT_KEYS + " from wm_poi_settle where wm_contract_id = #{wmContractId} and valid = 1")
    @DataSource("dbContractRead")
    List<WmPoiSettleDB> getByWmContractId(@Param("wmContractId") Integer wmContractId);

    @Select("/*master*/select " + SELECT_KEYS + " from wm_poi_settle where wm_contract_id = #{wmContractId} and valid = 1")
    @DataSource("dbContractWrite")
    List<WmPoiSettleDB> getWmPoiSettleByWmContractIdMaster(@Param("wmContractId") Integer wmContractId);

    @DataSource("dbContractRead")
    List<WmPoiSettleDB> getByWmContractIdAndWmPoiIdList(@Param("wmContractId") Integer wmContractId, @Param
            ("wmPoiIdList") List<Long> wmPoiIdList);

//    @DataSource("dbContractRead")
//    List<Integer> getWmSettleIdListByWmContractAndWmPoiId(@Param("wmContractId") int wmContractId,
//                                                          @Param("wmPoiIdList") List<Integer> wmPoiIdList);

    List<WmPoiSettleDB> getByWmSettleIdList(@Param("wmSettleIdList")List<Integer> wmSettleIdList);

    List<WmPoiSettleDB> getByWmSettleIdListAndWmContractId(@Param("wmSettleIdList")List<Integer> wmSettleIdList,@Param("wmContractId") Integer wmContractId);

    List<WmPoiSettleDB> getByWmSettleIdListMaster(@Param("wmSettleIdList")List<Integer> wmSettleIdList);

    List<WmPoiSettleDB> getByWmSettleIdListAndWmContractIdMaster(@Param("wmSettleIdList")List<Integer> wmSettleIdList,@Param("wmContractId") Integer wmContractId);

    int deleteByWmSettleIdAndWmPoiIdList(@Param("wmSettleId")int wmSettleId, @Param("wmPoiIdList")List<Integer> wmPoiIdList);

    List<Integer> getWmPoiIdListByWmSettleIdList(@Param("wmSettleIdList")List<Integer> wmSettleIdList);

    List<Integer> getWmSettleIdListBySettleAndPoi(@Param("wmSettleIdList")List<Integer> wmSettleIdList, @Param("wmPoiIdList")List<Integer> wmPoiIdList);

    List<Integer> getWmSettleIdListByWmPoiId(@Param("wmPoiId")int wmPoiId);

    List<Integer> getWmSettleIdListByWmPoiIdAndWmContractId(@Param("wmPoiId")int wmPoiId,@Param("wmContractId")int wmContractId);

    int deleteByWmSettleIdListAndWmPoiIdList(@Param("wmSettleIdList") List<Integer> wmSettleIdList,
        @Param("wmPoiIdList") List<Long> wmPoiIdList);

    List<Long> batchGetSettleWmPoiIdSet(@Param("wmPoiIdList")List<Long> wmPoiIdList,@Param("wmCustomerId")int wmCustomerId);

    int deleteUnnecessaryOfflineRelByWmCustomerIdAndWmPoiIdList(@Param("wmCustomerId") int wmCustomerId, @Param("wmPoiIdList") List<Long> wmPoiIdList);
}