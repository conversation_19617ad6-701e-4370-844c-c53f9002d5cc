package com.sankuai.meituan.waimai.customer.util.diff;

import com.sankuai.meituan.waimai.customer.util.diff.BeanDiffUtil.PropertyDesc;
import com.sankuai.meituan.waimai.templetcontract.constant.DiffOpType;
import java.util.Map;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 从object生成diffModule
 * @param <T>
 */
@Getter
public class DiffHelper<T> {

    private static Logger logger = LoggerFactory.getLogger(DiffHelper.class);

    private Object from;

    private Object to;

    private Map<String, PropertyDesc> fieldAndDescMap;

    private DiffOpType opType;

    private String module;

    private String desc;

    public DiffHelper(T from, T to, Map<String, BeanDiffUtil.PropertyDesc> fieldAndDescMap) {
        this.from = from;
        this.to = to;
        this.fieldAndDescMap = fieldAndDescMap;
        this.desc = "";
    }

//    public ClazzDiff toClazzDiff() throws WmServerException {
//        AssertUtil.assertStringNotEmpty(module, "模块");
//        AssertUtil.assertObjectNotNull(opType, "操作类型");
//
//        List<FieldDiff> unitList = BeanDiffUtil.diff(from, to, fieldAndDescMap);
//        logger.info("unitList = {}", JSON.toJSONString(unitList));
//        return new ClazzDiff(module, opType, desc, unitList);
//    }

    public DiffHelper setFrom(Object from) {
        this.from = from;
        return this;
    }

    public DiffHelper setTo(Object to) {
        this.to = to;
        return this;
    }

    public DiffHelper setFieldAndDescMap(
        Map<String, BeanDiffUtil.PropertyDesc> fieldAndDescMap) {
        this.fieldAndDescMap = fieldAndDescMap;
        return this;
    }

    public DiffHelper setOpType(DiffOpType opType) {
        this.opType = opType;
        return this;
    }

    public DiffHelper setModule(String module) {
        this.module = module;
        return this;
    }

    public DiffHelper setDesc(String desc) {
        this.desc = desc;
        return this;
    }
}
