package com.sankuai.meituan.waimai.customer.contract.partner;

import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.DaoCanContractContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/2 16:51
 */
public interface WmPartnerCustomerContractService {

    CustomerContractSaveResponseDTO saveCustomerContract(CustomerContractSaveRequestDTO customerContractSaveRequestDTO) throws WmCustomerException;

    C2SignStatusCheckResponseDTO checkC2ContractSignStatus(C2SignStatusCheckRequestDTO c2SignStatusCheckRequestDTO) throws WmCustomerException;

    CustomerContractCancelSignResponseDTO cancelCustomerContractSign(CustomerContractCancelSignRequestDTO requestDTO) throws WmCustomerException;

    List<CustomerContractQueryResponseDTO> queryCustomerContract(CustomerContractQueryRequestDTO customerContractQueryRequestDTO) throws WmCustomerException;

    List<C2ContractCheckResponseDTO> hasC2Contract(C2ContractCheckRequestDTO c2ContractCheckRequestDTO) throws WmCustomerException;

    List<DaoCanContractContext> queryDcContractInfo(List<String> recordKeyList);

    PushMsgSendResponseDTO sendBatchTaskPushMessage(PushMsgSendRequestDTO requestDTO);

    ContractSignTypeResponseDTO queryContractSignType(ContractSignTypeRequestDTO requestDTO) throws WmCustomerException;
}
