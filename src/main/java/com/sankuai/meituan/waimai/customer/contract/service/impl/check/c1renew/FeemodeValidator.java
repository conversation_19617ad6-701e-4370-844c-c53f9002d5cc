package com.sankuai.meituan.waimai.customer.contract.service.impl.check.c1renew;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-09-09 11:33
 * Email: <EMAIL>
 * Desc:
 */
@Service
@Slf4j
public class FeemodeValidator implements IContractValidator {

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        WmPoiAggre wmPoiAggre = C1RenewPoiThreadLocalUtil.get();
        log.info("FeemodeValidator#valid，customerId:{}，poiId:{}，wmPoiAggre:{}",
                contractBo.getBasicBo().getParentId(),
                wmPoiAggre == null ? "null" : wmPoiAggre.getWm_poi_id(),
                JSON.toJSONString(wmPoiAggre));
        if(wmPoiAggre != null){
            return wmPoiAggre.getLogistics_fee_mode() != LogisticsFeeModeEnum.WAIMAI.getCode();
        }
        return true;
    }
}
