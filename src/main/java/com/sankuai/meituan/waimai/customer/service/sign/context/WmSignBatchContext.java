package com.sankuai.meituan.waimai.customer.service.sign.context;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-02 11:47
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class WmSignBatchContext<T> implements WmSignContext<T> {

    @Override
    public T deleteUseless(T context) {
        String uselessBatchContext = (String) context;
        try {
            Map<Long, EcontractTaskBo> coldContextMap = JSONObject.parseObject(uselessBatchContext, Map.class);
            for (Map.Entry<Long, EcontractTaskBo> entry : coldContextMap.entrySet()) {
                EcontractTaskBo taskBo = JSONObject.parseObject(JSON.toJSONString(entry.getValue()), EcontractTaskBo.class);
                //只处理配送类型的task
                if (taskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.POIFEE.getName())) {
                    handlerPoiFeeTask(taskBo);
                    entry.setValue(taskBo);
                } else if (taskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName())) {
                    handlerBatchPoiFeeTask(taskBo);
                    entry.setValue(taskBo);
                } else {
                    continue;
                }
            }
            uselessBatchContext = JSON.toJSONString(coldContextMap);
            return (T) uselessBatchContext;
        } catch (Exception e) {
            log.error("删除无用上下文流程异常，context:{}", uselessBatchContext, e);
            return (T) uselessBatchContext;
        }
    }

    private void handlerPoiFeeTask(EcontractTaskBo taskBo) {
        EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        deliveryTypeRouter(deliveryInfoBo);
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
    }

    private void handlerBatchPoiFeeTask(EcontractTaskBo taskBo) {
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        batchDeliveryInfoBo.getEcontractDeliveryInfoBoList().stream().forEach(deliveryInfoBo -> deliveryTypeRouter(deliveryInfoBo));
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
    }


    private void deliveryTypeRouter(EcontractDeliveryInfoBo deliveryInfoBo) {
        // 主配配送范围
        if (StringUtils.isNotEmpty(deliveryInfoBo.getDeliveryArea())) {
            EcontractWmPoiSpAreaBo mainDeliveryArea = JSONObject.parseObject(deliveryInfoBo.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
            deliveryAreaContextDelete(mainDeliveryArea);
            deliveryInfoBo.setDeliveryArea(JSON.toJSONString(mainDeliveryArea));
        }
        //聚合配配送范围
        if (deliveryInfoBo.getEcontractDeliveryAggregationInfoBo() != null
                && StringUtils.isNotEmpty(deliveryInfoBo.getEcontractDeliveryAggregationInfoBo().getDeliveryArea())) {
            EcontractDeliveryAggregationInfoBo aggregationInfoBo = deliveryInfoBo.getEcontractDeliveryAggregationInfoBo();
            EcontractWmPoiSpAreaBo aggregationDeliveryArea = JSONObject.parseObject(aggregationInfoBo.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
            deliveryAreaContextDelete(aggregationDeliveryArea);
            aggregationInfoBo.setDeliveryArea(JSON.toJSONString(aggregationDeliveryArea));
        }
        //全城送配送范围
        if (deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo() != null
                && StringUtils.isNotEmpty(deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo().getDeliveryArea())) {
            EcontractDeliveryWholeCityInfoBo wholeCityInfoBo = deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo();
            EcontractWmPoiSpAreaBo wholeCityDeliveryArea = JSONObject.parseObject(wholeCityInfoBo.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
            deliveryAreaContextDelete(wholeCityDeliveryArea);
            wholeCityInfoBo.setDeliveryArea(JSON.toJSONString(wholeCityDeliveryArea));
        }
        // 企客远距离配送范围
        if (deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo() != null
                && StringUtils.isNotEmpty(deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo().getDeliveryArea())) {
            EcontractDeliveryCompanyCustomerLongDistanceInfoBo companyCustomerLongDistanceInfoBo = deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo();
            EcontractWmPoiSpAreaBo companyCustomerLongDistanceDeliveryArea = JSONObject.parseObject(companyCustomerLongDistanceInfoBo.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
            deliveryAreaContextDelete(companyCustomerLongDistanceDeliveryArea);
            companyCustomerLongDistanceInfoBo.setDeliveryArea(JSON.toJSONString(companyCustomerLongDistanceDeliveryArea));
        }
    }

    private void deliveryAreaContextDelete(EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBo) {
        List<EcontractWmPoiSelfDeliveryPlanBo> selfDeliveryPlanBoList = econtractWmPoiSpAreaBo.getSelfDeliveryPlanBoList();
        if(CollectionUtils.isNotEmpty(selfDeliveryPlanBoList)){
            for (EcontractWmPoiSelfDeliveryPlanBo deliveryPlanBo : selfDeliveryPlanBoList) {
                List<EcontractWmPoiAreaPlanBo> areaBoList = deliveryPlanBo.getAreaBoList();
                for (EcontractWmPoiAreaPlanBo areaPlanBo : areaBoList) {
                    areaPlanBo.setArea(Lists.newArrayList());
                    areaPlanBo.setOriginalArea(Lists.newArrayList());
                }
            }
        }
        List<EcontractWmPoiAreaSlaPlanBo> areaBoList = econtractWmPoiSpAreaBo.getAreaBoList();
        if(CollectionUtils.isNotEmpty(areaBoList)){
            for (EcontractWmPoiAreaSlaPlanBo areaSlaPlanBo : areaBoList) {
                areaSlaPlanBo.setArea(Lists.newArrayList());
                areaSlaPlanBo.setOriginalArea(Lists.newArrayList());
            }
        }
    }
}
