package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

/**
 * <AUTHOR>
 */
public interface IWmEcontractSignDataFactorCollector {

    void collect(EcontractBatchContextBo originContext, EcontractSignDataFactor econtractSignDataFactor)
            throws TException, WmCustomerException;

}
