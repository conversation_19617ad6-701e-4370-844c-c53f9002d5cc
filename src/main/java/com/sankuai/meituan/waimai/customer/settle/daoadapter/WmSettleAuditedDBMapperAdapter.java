package com.sankuai.meituan.waimai.customer.settle.daoadapter;

import com.google.common.base.MoreObjects;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.exception.SettleEncryptRuntimeException;
import com.sankuai.meituan.waimai.customer.settle.constant.EncryptConstants;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.service.adapter.TokenAccessThriftServiceAdapter;
import com.sankuai.meituan.waimai.heron.settle.exception.WmHeronSettleException;
import java.security.GeneralSecurityException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * wm_settle_audited 敏感数据加密使用
 * <AUTHOR>
 * @see <a href="https://km.sankuai.com/page/1084680704">https://km.sankuai.com/page/1084680704</a>
 */
@Slf4j
@Service
public class WmSettleAuditedDBMapperAdapter {
    @Autowired
    private WmSettleAuditedDBMapper wmSettleAuditedDBMapper;
    @Autowired
    private TokenAccessThriftServiceAdapter tokenAccessThriftServiceAdapter;

    private static final int DEFAULT_LIMIT_LENGTH = 256;

    public int insertSelective(WmSettleAuditedDB record){
        WmSettleAuditedDB recordCopy = new WmSettleAuditedDB();
        BeanUtils.copyProperties(record,recordCopy);
        try {
            encryptWmSettleAuditedDB(recordCopy,EncryptConstants.INSERT_ACTION);
        } catch (Exception e) {
            log.error("encryptWmSettleAuditedDB exception",e);
        }
        int result = wmSettleAuditedDBMapper.insertSelective(recordCopy);
        record.setId(recordCopy.getId());
        return result;
    }

    public int updateByWmSettleId(WmSettleAuditedDB wmSettleAuditedDB){
        WmSettleAuditedDB recordCopy = new WmSettleAuditedDB();
        BeanUtils.copyProperties(wmSettleAuditedDB,recordCopy);
        try {
            dataCompletion(recordCopy);
            encryptWmSettleAuditedDB(recordCopy,EncryptConstants.UPDATE_ACTION);
        } catch (Exception e) {
            log.error("encryptWmSettleAuditedDB exception",e);
        }
        return wmSettleAuditedDBMapper.updateByWmSettleId(recordCopy);
    }

    private void dataCompletion(WmSettleAuditedDB record) {
        if(record == null){
            return;
        }
        if(record.getWm_settle_id() == null || record.getWm_settle_id() <= 0){
            return;
        }
        if((record.getLegal_cert_num() != null || record.getCert_num() != null) && (record.getCert_type() == null || record.getCert_type() == 0)){
            record.setCert_type(wmSettleAuditedDBMapper.getCertTypeByWmSettleId(record.getWm_settle_id()));
        }
    }

    private void lengthCheck(String source){
        if(source.length() > DEFAULT_LIMIT_LENGTH){
            throw new SettleEncryptRuntimeException("加密后字段长度超过"+DEFAULT_LIMIT_LENGTH);
        }
    }

    private void encryptWmSettleAuditedDB(WmSettleAuditedDB record,String action)
            throws GeneralSecurityException, TException, WmHeronSettleException {
        if(!ConfigUtilAdapter.getBoolean("open_encryptWmSettleAuditedDB",false)){
            return;
        }

        if(record.getParty_a_finance_phone() != null){
            Pair<String, String> mobileTokenAndEncrypt = tokenAccessThriftServiceAdapter.getMobileTokenAndEncrypt(record.getParty_a_finance_phone());
            record.setParty_a_finance_phone_token(mobileTokenAndEncrypt.getLeft());
            record.setParty_a_finance_phone_encrypted(mobileTokenAndEncrypt.getRight());
            lengthCheck(record.getParty_a_finance_phone_encrypted());
            record.setParty_a_finance_phone(cleanPlaintextForPartyAFinanceInfo(record.getParty_a_finance_phone(),mobileTokenAndEncrypt.getRight(),action));
        }

        if(record.getAcc_cardno() != null){
            Pair<String, String> bankcardTokenAndEncrypt = tokenAccessThriftServiceAdapter.getBankcardTokenAndEncrypt(record.getAcc_cardno());
            record.setAcc_cardno_token(bankcardTokenAndEncrypt.getLeft());
            record.setAcc_cardno_encrypted(bankcardTokenAndEncrypt.getRight());
            lengthCheck(record.getAcc_cardno_encrypted());
            record.setAcc_cardno(cleanPlaintext(record.getAcc_cardno(),bankcardTokenAndEncrypt.getRight()));
        }

        if(record.getLegal_cert_num() != null){
            Pair<String, String> identifyTokenAndEncrypt = tokenAccessThriftServiceAdapter.getIdentifyTokenAndEncrypt(MoreObjects.firstNonNull(record.getCert_type(),0).intValue(), record.getLegal_cert_num());
            record.setLegal_cert_num_token(identifyTokenAndEncrypt.getLeft());
            record.setLegal_cert_num_encrypted(identifyTokenAndEncrypt.getRight());
            lengthCheck(record.getLegal_cert_num_encrypted());
            record.setLegal_cert_num(cleanPlaintext(record.getLegal_cert_num(),identifyTokenAndEncrypt.getRight()));
        }

        if(record.getLegal_id_card() != null){
            Pair<String, String> identifyTokenAndEncrypt = tokenAccessThriftServiceAdapter.getIdentifyTokenAndEncrypt(1001, record.getLegal_id_card());
            record.setLegal_id_card_token(identifyTokenAndEncrypt.getLeft());
            record.setLegal_id_card_encrypted(identifyTokenAndEncrypt.getRight());
            lengthCheck(record.getLegal_id_card_encrypted());
            record.setLegal_id_card(cleanPlaintext(record.getLegal_id_card(),identifyTokenAndEncrypt.getRight()));
        }

        if(record.getCert_num() != null){
            Pair<String, String> identifyTokenAndEncrypt = tokenAccessThriftServiceAdapter.getIdentifyTokenAndEncrypt(MoreObjects.firstNonNull(record.getCert_type(),0).intValue(), record.getCert_num());
            record.setCert_num_token(identifyTokenAndEncrypt.getLeft());
            record.setCert_num_encrypted(identifyTokenAndEncrypt.getRight());
            lengthCheck(record.getCert_num_encrypted());
            record.setCert_num(cleanPlaintext(record.getCert_num(),identifyTokenAndEncrypt.getRight()));
        }

        if(record.getReserve_phone() != null){
            Pair<String, String> mobileTokenAndEncrypt = tokenAccessThriftServiceAdapter.getMobileTokenAndEncrypt(record.getReserve_phone());
            record.setReserve_phone_token(mobileTokenAndEncrypt.getLeft());
            record.setReserve_phone_encrypted(mobileTokenAndEncrypt.getRight());
            lengthCheck(record.getReserve_phone_encrypted());
            record.setReserve_phone(cleanPlaintext(record.getReserve_phone(),mobileTokenAndEncrypt.getRight()));
        }
    }

    /**
     * 明文字段停写处理[停写开关开启&&密文字段存在有效数据]
     * @param plaintext
     * @param encryptText
     * @return
     */
    private String cleanPlaintext(String plaintext, String encryptText) {
        if(ConfigUtilAdapter.getBoolean("stop_wm_settle_audited_plaintext_update_open",false) && !StringUtils.isEmpty(encryptText)){
            return null;
        }
        return plaintext;
    }

    private String cleanPlaintextForPartyAFinanceInfo(String plaintext, String encryptText, String action) {
        if(ConfigUtilAdapter.getBoolean("stop_wm_settle_audited_plaintext_update_open",false) && !StringUtils.isEmpty(encryptText)){
            return EncryptConstants.INSERT_ACTION.equals(action) ? "" : null;
        }
        return plaintext;
    }

}
