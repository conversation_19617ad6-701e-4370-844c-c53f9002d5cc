/*
* Copyright (c) 2016 meituan.com. All Rights Reserved.
*/
package com.sankuai.meituan.waimai.customer.mq.dao;

import com.sankuai.meituan.waimai.customer.mq.domain.MqRecord;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Author:jinhu
 * Date:2017/3/16
 * Time:下午5:37
 */
@Component
public interface MqRecordMapper {

    int insert(MqRecord mqRecord);

    int updateSuccess(MqRecord mqRecord);

    int updateFailed(MqRecord mqRecord);

    List<Integer> batchSelectPendingIds(Integer currentTime);

    List<MqRecord> selectPendingRecordByIdList(@Param("idList") List<Integer> idList);
}