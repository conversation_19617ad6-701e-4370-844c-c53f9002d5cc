package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractApplySubDataEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractApplySubDataBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.shangou.merchant.logistics.thrift.constants.LogisticsFeeModeEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractRecordSourceEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 电子签约数据来源
 * @author: lixuepeng
 * @create: 2023-06-2
 **/
@Service
@Slf4j
public class WmEcontractDataApplySubDataCollector implements IWmEcontractDataCollector {

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext)
            throws WmCustomerException, IllegalAccessException, TException {
        log.info("WmEcontractDataApplySubDataCollector#batchId:{}, batchTypeEnum:{}", originContext.getBatchId(), originContext.getBatchTypeEnum());
        EcontractSignDataFactor signDataFactor = middleContext.getSignDataFactor();
        List<EcontractApplySubDataBo> applySubDataBoList = new ArrayList<>();
        if (EcontractBatchTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY == originContext.getBatchTypeEnum()) {
            collectHeadQuartesDelivery(originContext, applySubDataBoList, signDataFactor);
            return;
        } else if (EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY == originContext.getBatchTypeEnum()) {
            collectDistributorDelivery(originContext, applySubDataBoList, signDataFactor);
            return;
        }
        if (signDataFactor.isDeliverySingleWmPoi()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.POIFEE);
            EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
            if ("true".equals(deliveryInfoBo.getHasExternalPerInfo())) {
                EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
                Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
                if (isSupportSGOneChainAndSGFeeModeAndMTDelivery(deliveryInfoBo)) {
                    subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.SG_2_DELIVERY_SINGLE_PERFORMANCE_PDF);
                    wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                } else {
                    subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DELIVERY_SINGLE_PERFORMANCE_PDF);
                    wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                }
                subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
                applySubDataBoList.add(subDataBo);
            }
            if ("true".equals(deliveryInfoBo.getHasExternalAreaInfo())) {
                EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
                subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DELIVERY_SINGLE_AREA);
                Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
                wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
                subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
                applySubDataBoList.add(subDataBo);
            }
        } else if (signDataFactor.isDeliveryMultiWmPoi()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.BATCHPOIFEE);
            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
            EcontractApplySubDataBo multiPerSubDataBo = new EcontractApplySubDataBo();
            multiPerSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DELIVERY_MULTI_PERFORMANCE_PDF);
            multiPerSubDataBo.setWmPoiAndBizMap(new HashMap<>());
            EcontractApplySubDataBo mulAreaSubDataBo = new EcontractApplySubDataBo();
            mulAreaSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DELIVERY_MULTI_AREA);
            mulAreaSubDataBo.setWmPoiAndBizMap(new HashMap<>());

            EcontractApplySubDataBo sgMultiPerSubDataBo = new EcontractApplySubDataBo();
            sgMultiPerSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.SG_2_DELIVERY_MULTI_PERFORMANCE_PDF);
            sgMultiPerSubDataBo.setWmPoiAndBizMap(new HashMap<>());
            for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
                if ("true".equals(deliveryInfoBo.getHasExternalPerInfo())) {
                    if (isSupportSGOneChainAndSGFeeModeAndMTDelivery(deliveryInfoBo)) {
                        sgMultiPerSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                    } else {
                        multiPerSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                    }
                }
                if ("true".equals(deliveryInfoBo.getHasExternalAreaInfo())) {
                    mulAreaSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
                }
            }
            if (MapUtils.isNotEmpty(multiPerSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(multiPerSubDataBo);
            }
            if (MapUtils.isNotEmpty(mulAreaSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(mulAreaSubDataBo);
            }
            if (MapUtils.isNotEmpty(sgMultiPerSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(sgMultiPerSubDataBo);
            }
        }
        targetContext.setApplySubDataBoList(applySubDataBoList);
    }

    private void collectDistributorDelivery(EcontractBatchContextBo originContext,
                                            List<EcontractApplySubDataBo> applySubDataBoList, EcontractSignDataFactor signDataFactor) throws WmCustomerException {
        collectNationalSubsidyDelivery(originContext, applySubDataBoList, signDataFactor,
                EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY,
                new NationalSubsidyEnumConfig(
                        EcontractApplySubDataEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_SINGLE_DELIVERY_PERFORMANCE_SERVICE_FEE,
                        EcontractApplySubDataEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_SINGLE_AREA,
                        EcontractApplySubDataEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_MULTI_DELIVERY_PERFORMANCE_SERVICE_FEE,
                        EcontractApplySubDataEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_MULTI_AREA
                ));
    }

    private void collectHeadQuartesDelivery(EcontractBatchContextBo originContext,
                                            List<EcontractApplySubDataBo> applySubDataBoList,
                                            EcontractSignDataFactor signDataFactor) throws WmCustomerException {
        collectNationalSubsidyDelivery(originContext, applySubDataBoList, signDataFactor,
                EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY,
                new NationalSubsidyEnumConfig(
                        EcontractApplySubDataEnum.NATIONAL_SUBSIDY_HEADQUARTERS_SINGLE_DELIVERY_PERFORMANCE_SERVICE_FEE,
                        EcontractApplySubDataEnum.NATIONAL_SUBSIDY_HEADQUARTERS_SINGLE_AREA,
                        EcontractApplySubDataEnum.NATIONAL_SUBSIDY_HEADQUARTERS_MULTI_DELIVERY_PERFORMANCE_SERVICE_FEE,
                        EcontractApplySubDataEnum.NATIONAL_SUBSIDY_HEADQUARTERS_MULTI_AREA
                ));
    }

    /**
     * 通用的国补配送数据收集方法
     *
     * @param originContext 原始上下文
     * @param applySubDataBoList 申请子数据列表
     * @param signDataFactor 签约数据因子
     * @param taskApplyType 任务申请类型
     * @param enumConfig 枚举配置
     * @throws WmCustomerException 异常
     */
    private void collectNationalSubsidyDelivery(EcontractBatchContextBo originContext,
                                               List<EcontractApplySubDataBo> applySubDataBoList,
                                               EcontractSignDataFactor signDataFactor,
                                               EcontractTaskApplyTypeEnum taskApplyType,
                                               NationalSubsidyEnumConfig enumConfig) throws WmCustomerException {
        if (signDataFactor.isDeliverySingleWmPoi()) {
            collectSingleWmPoiDelivery(originContext, applySubDataBoList, taskApplyType, enumConfig);
        } else if (signDataFactor.isDeliveryMultiWmPoi()) {
            collectMultiWmPoiDelivery(originContext, applySubDataBoList, taskApplyType, enumConfig);
        }
    }

    /**
     * 收集单门店配送数据
     */
    private void collectSingleWmPoiDelivery(EcontractBatchContextBo originContext,
                                           List<EcontractApplySubDataBo> applySubDataBoList,
                                           EcontractTaskApplyTypeEnum taskApplyType,
                                           NationalSubsidyEnumConfig enumConfig) throws WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, taskApplyType);
        EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);

        if (hasExternalPerInfo(deliveryInfoBo)) {
            EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
            Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
            subDataBo.setApplySubDataEnum(enumConfig.getSinglePerformanceEnum());
            wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
            subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
            applySubDataBoList.add(subDataBo);
        }

        if (hasExternalAreaInfo(deliveryInfoBo)) {
            EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
            subDataBo.setApplySubDataEnum(enumConfig.getSingleAreaEnum());
            Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
            wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
            subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
            applySubDataBoList.add(subDataBo);
        }
    }

    /**
     * 收集多门店配送数据
     */
    private void collectMultiWmPoiDelivery(EcontractBatchContextBo originContext,
                                          List<EcontractApplySubDataBo> applySubDataBoList,
                                          EcontractTaskApplyTypeEnum taskApplyType,
                                          NationalSubsidyEnumConfig enumConfig) throws WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, taskApplyType);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);

        EcontractApplySubDataBo multiPerSubDataBo = new EcontractApplySubDataBo();
        multiPerSubDataBo.setApplySubDataEnum(enumConfig.getMultiPerformanceEnum());
        multiPerSubDataBo.setWmPoiAndBizMap(new HashMap<>());

        EcontractApplySubDataBo mulAreaSubDataBo = new EcontractApplySubDataBo();
        mulAreaSubDataBo.setApplySubDataEnum(enumConfig.getMultiAreaEnum());
        mulAreaSubDataBo.setWmPoiAndBizMap(new HashMap<>());

        for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            if (hasExternalPerInfo(deliveryInfoBo)) {
                multiPerSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
            }
            if (hasExternalAreaInfo(deliveryInfoBo)) {
                mulAreaSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
            }
        }

        if (MapUtils.isNotEmpty(multiPerSubDataBo.getWmPoiAndBizMap())) {
            applySubDataBoList.add(multiPerSubDataBo);
        }
        if (MapUtils.isNotEmpty(mulAreaSubDataBo.getWmPoiAndBizMap())) {
            applySubDataBoList.add(mulAreaSubDataBo);
        }
    }

    /**
     * 国补枚举配置类
     */
    private static class NationalSubsidyEnumConfig {
        private final EcontractApplySubDataEnum singlePerformanceEnum;
        private final EcontractApplySubDataEnum singleAreaEnum;
        private final EcontractApplySubDataEnum multiPerformanceEnum;
        private final EcontractApplySubDataEnum multiAreaEnum;

        public NationalSubsidyEnumConfig(EcontractApplySubDataEnum singlePerformanceEnum,
                                        EcontractApplySubDataEnum singleAreaEnum,
                                        EcontractApplySubDataEnum multiPerformanceEnum,
                                        EcontractApplySubDataEnum multiAreaEnum) {
            this.singlePerformanceEnum = singlePerformanceEnum;
            this.singleAreaEnum = singleAreaEnum;
            this.multiPerformanceEnum = multiPerformanceEnum;
            this.multiAreaEnum = multiAreaEnum;
        }

        public EcontractApplySubDataEnum getSinglePerformanceEnum() {
            return singlePerformanceEnum;
        }

        public EcontractApplySubDataEnum getSingleAreaEnum() {
            return singleAreaEnum;
        }

        public EcontractApplySubDataEnum getMultiPerformanceEnum() {
            return multiPerformanceEnum;
        }

        public EcontractApplySubDataEnum getMultiAreaEnum() {
            return multiAreaEnum;
        }
    }

    private boolean hasExternalPerInfo(EcontractDeliveryInfoBo deliveryInfoBo) {
        return "true".equals(deliveryInfoBo.getHasExternalPerInfo());
    }

    private boolean hasExternalAreaInfo(EcontractDeliveryInfoBo deliveryInfoBo) {
        return "true".equals(deliveryInfoBo.getHasExternalAreaInfo());
    }

    /**
     * 是否是一体化链路的闪购2.0费率门店,且为美团配送
     *
     * @param deliveryInfoBo
     * @return
     */
    private boolean isSupportSGOneChainAndSGFeeModeAndMTDelivery(EcontractDeliveryInfoBo deliveryInfoBo) {
        return String.valueOf(LogisticsFeeModeEnum.SHANGOU.getCode()).equals(deliveryInfoBo.getFeeMode());
    }
}
