package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.legal;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.CustomerKpBusinessService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpLegalAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpLegalEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpLegalStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpLegalStatusSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpLegalStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20230821
 * @desc KP法人实名认证失败事件
 */
@Service
@Slf4j
public class KpLegalRealNameFailAction extends KpLegalAbstractAction {

    @Autowired
    private CustomerKpBusinessService customerKpBusinessService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    /**
     * KP法人实名认证失败
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpLegalStatusSM
     */
    @Override
    public void execute(KpLegalStateMachine from, KpLegalStateMachine to, KpLegalEventEnum eventEnum,
                        KpLegalStatusMachineContext context, KpLegalStatusSM kpLegalStatusSM) {
        try {
            //重置上下文中KP状态
            context.getWmCustomerKp().setState(KpLegalStateMachine.PREAUTH_FAIL.getState());
            context.getWmCustomerKp().setEffective(KpConstants.UN_EFFECTIVE);
            context.getWmCustomerKp().setFailReason("实名认证失败");
            customerKpBusinessService.updateKp2RealNameFailOnUnEffectKp(context.getWmCustomerKp());
            //添加状态变更操作日志
            wmCustomerKpLogService.addKpStatusChangeLog(context.getWmCustomerKp(), context.getOpUid(), context.getOpUName(), context.getOldCustomerKp().getState());
        } catch (Exception e) {
            log.error("KpLegalInitCreateAction.updateKp2RealNameFail,KP法人流转到实名认证失败发生异常,context={}", JSON.toJSONString(context), e);
            throw new StatusMachineException("KP法人实名失败事件异常");
        }
    }
}
