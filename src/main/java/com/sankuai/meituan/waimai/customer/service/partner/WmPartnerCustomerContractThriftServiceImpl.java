package com.sankuai.meituan.waimai.customer.service.partner;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.partner.WmPartnerCustomerContractService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.DaoCanContractContext;
import com.sankuai.meituan.waimai.thrift.customer.service.contract.WmPartnerCustomerContractThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/2 16:49
 */
@Slf4j
@Service
public class WmPartnerCustomerContractThriftServiceImpl implements WmPartnerCustomerContractThriftService {

    @Resource
    private WmPartnerCustomerContractService wmPartnerCustomerContractService;

    @Override
    public CustomerContractSaveResponseDTO saveCustomerContract(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmPartnerCustomerContractThriftServiceImpl#saveCustomerContract, requestDTO: {}", JSON.toJSONString(requestDTO));
            CustomerContractSaveResponseDTO responseDTO = wmPartnerCustomerContractService.saveCustomerContract(requestDTO);
            log.info("WmPartnerCustomerContractThriftServiceImpl#saveCustomerContract, responseDTO: {}", JSON.toJSONString(responseDTO));
            return responseDTO;
        } catch (WmCustomerException exception) {
            log.warn("WmPartnerCustomerContractThriftServiceImpl#saveCustomerContract, warn", exception);
            throw exception;
        } catch (Exception e) {
            log.error("WmPartnerCustomerContractThriftServiceImpl#saveCustomerContract, error, e", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "保存失败");
        }
    }

    @Override
    public C2SignStatusCheckResponseDTO checkC2ContractSignStatus(C2SignStatusCheckRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmPartnerCustomerContractThriftServiceImpl#checkC2ContractSignStatus, requestDTO: {}", JSON.toJSONString(requestDTO));
            C2SignStatusCheckResponseDTO responseDTO = wmPartnerCustomerContractService.checkC2ContractSignStatus(requestDTO);
            log.info("WmPartnerCustomerContractThriftServiceImpl#checkC2ContractSignStatus, responseDTO: {}", JSON.toJSONString(responseDTO));
            return responseDTO;
        } catch (WmCustomerException exception) {
            log.warn("WmPartnerCustomerContractThriftServiceImpl#checkC2ContractSignStatus, warn", exception);
            throw exception;
        } catch (Exception e) {
            log.error("WmPartnerCustomerContractThriftServiceImpl#checkC2ContractSignStatus, error, e", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "校验失败");
        }
    }

    @Override
    public List<CustomerContractQueryResponseDTO> queryCustomerContract(CustomerContractQueryRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmPartnerCustomerContractThriftServiceImpl#queryCustomerContract, requestDTO: {}", JSON.toJSONString(requestDTO));
            List<CustomerContractQueryResponseDTO> contractList = wmPartnerCustomerContractService.queryCustomerContract(requestDTO);
            log.info("WmPartnerCustomerContractThriftServiceImpl#queryCustomerContract, contractList: {}", JSON.toJSONString(contractList));
            return contractList;
        } catch (WmCustomerException exception) {
            log.warn("WmPartnerCustomerContractThriftServiceImpl#checkC2ContractSignStatus, warn", exception);
            throw exception;
        } catch (Exception e) {
            log.error("WmPartnerCustomerContractThriftServiceImpl#checkC2ContractSignStatus, error, e", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询失败");
        }
    }

    /**
     * 这个方案不应该有调用
     */
    @Override
    public CustomerContractCancelSignResponseDTO cancelCustomerContractSign(CustomerContractCancelSignRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmPartnerCustomerContractThriftServiceImpl#cancelCustomerContractSign, requestDTO: {}", JSON.toJSONString(requestDTO));
            CustomerContractCancelSignResponseDTO responseDTO = wmPartnerCustomerContractService.cancelCustomerContractSign(requestDTO);
            log.info("WmPartnerCustomerContractThriftServiceImpl#cancelCustomerContractSign, responseDTO: {}", JSON.toJSONString(responseDTO));
            return responseDTO;
        } catch (WmCustomerException exception) {
            log.warn("WmPartnerCustomerContractThriftServiceImpl#cancelCustomerContractSign, warn", exception);
            throw exception;
        } catch (Exception e) {
            log.error("WmPartnerCustomerContractThriftServiceImpl#cancelCustomerContractSign, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "取消签约失败");
        }
    }

    @Override
    public List<C2ContractCheckResponseDTO> hasC2Contract(C2ContractCheckRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmPartnerCustomerContractThriftServiceImpl#hasC2Contract, requestDTO: {}", JSON.toJSONString(requestDTO));
            List<C2ContractCheckResponseDTO> checkResultList = wmPartnerCustomerContractService.hasC2Contract(requestDTO);
            log.info("WmPartnerCustomerContractThriftServiceImpl#hasC2Contract, responseDTO: {}", JSON.toJSONString(checkResultList));
            return checkResultList;
        } catch (WmCustomerException exception) {
            log.warn("WmPartnerCustomerContractThriftServiceImpl#hasC2Contract, warn", exception);
            throw exception;
        } catch (Exception e) {
            log.error("WmPartnerCustomerContractThriftServiceImpl#hasC2Contract, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询失败");
        }
    }

    @Override
    public List<DaoCanContractContext> queryDcContractInfo(List<String> recordKeyList) {
        try {
            log.info("WmPartnerCustomerContractThriftServiceImpl#queryDcContractInfo, recordKeyList: {}", JSON.toJSONString(recordKeyList));
            List<DaoCanContractContext> dcContractList = wmPartnerCustomerContractService.queryDcContractInfo(recordKeyList);
            log.info("WmPartnerCustomerContractThriftServiceImpl#queryDcContractInfo, dcContractList: {}", JSON.toJSONString(dcContractList));
            return dcContractList;
        } catch (Exception e) {
            log.info("WmPartnerCustomerContractThriftServiceImpl#queryDcContractInfo, error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public PushMsgSendResponseDTO sendBatchTaskPushMessage(PushMsgSendRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmPartnerCustomerContractThriftServiceImpl#sendBatchTaskPushMessage, requestDTO: {}", JSON.toJSONString(requestDTO));
            return wmPartnerCustomerContractService.sendBatchTaskPushMessage(requestDTO);
        } catch (Exception e) {
            log.error("WmPartnerCustomerContractThriftServiceImpl#sendBatchTaskPushMessage, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发送失败");
        }
    }

    @Override
    public ContractSignTypeResponseDTO queryContractSignType(ContractSignTypeRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmPartnerCustomerContractThriftServiceImpl#queryContractSignType, requestDTO: {}", JSON.toJSONString(requestDTO));
            ContractSignTypeResponseDTO responseDTO = wmPartnerCustomerContractService.queryContractSignType(requestDTO);
            log.info("WmPartnerCustomerContractThriftServiceImpl#queryContractSignType, responseDTO: {}", JSON.toJSONString(responseDTO));
            return responseDTO;
        } catch (Exception e) {
            log.error("WmPartnerCustomerContractThriftServiceImpl#queryContractSignType, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询合同签约类型失败");
        }
    }
}
