package com.sankuai.meituan.waimai.customer.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public interface WmCustomerCommonQuaDBMapper {
    void batchInsertCommonQua(@Param("customerId") Integer customerId, @Param("set") Set<String> urlSet);

    void deleteCommonQua(Integer customerId);

    Set<String> selectCommonQuaList(Integer customerId);

    void batchInsertCommonQuaOther(@Param("customerId") Integer customerId, @Param("list") List<String> urlList);

    void deleteCommonQuaOther(Integer customerId);

    List<String> selectCommonQuaOtherList(Integer customerId);

    Set<String> selectCommonQuaOtherListMaster(Integer customerId);
}
