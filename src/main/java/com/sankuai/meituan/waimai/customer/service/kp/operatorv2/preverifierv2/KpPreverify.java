package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-01 18:20
 * Email: <EMAIL>
 * Desc:
 */
@Service
public abstract class KpPreverify {

    // 预校验
    public abstract Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp,
                                  WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException;


    public Map<String, List<WmCustomerKp>> parameterTrans(WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp) {
        Map<String, List<WmCustomerKp>> resultMap = new HashMap<>();
        if (null != insertKp) {
            List<WmCustomerKp> insertKpList = Arrays.asList(insertKp);
            resultMap.put("insert", insertKpList);
        }
        if (null != updateKp) {
            List<WmCustomerKp> updateKpList = Arrays.asList(updateKp);
            resultMap.put("update", updateKpList);
        }
        if (null != deleteKp) {
            List<WmCustomerKp> deleteKpList = Arrays.asList(deleteKp);
            resultMap.put("delete", deleteKpList);
        }
        return resultMap;
    }
}
