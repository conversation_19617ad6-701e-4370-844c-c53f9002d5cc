package com.sankuai.meituan.waimai.customer.contract.service.impl.check.config;

import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigActivityService;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractSignService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/21 19:04
 */
@Service
public class ConfigContractDueDateAndSignTimeValidator implements IContractValidator {

    @Resource
    private WmContractSignService wmContractSignService;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        if (ContractSourceEnum.isCodeSource(contractBo.getBasicBo().getContractSource())) {
            return true;
        }
        ContractConfigInfo configInfo = contractBo.getBasicBo().getConfigContractInfo();
        if (configInfo == null || configInfo.getContractProperty() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同配置化信息异常");
        }
        ContractProperty contractProperty = configInfo.getContractProperty();
        ContractValidityInfo contractValidityProperty = contractProperty.getContractValidityProperty();
        ContractEffectiveTimeInfo contractEffectiveTimeProperty = contractProperty.getContractEffectiveTimeProperty();
        validateContractValidityProperty(contractValidityProperty, contractBo);
        validateContractEffectiveTimeProperty(contractEffectiveTimeProperty, contractBo);
        validateDueDateAndSignTime(contractBo, contractProperty);
        return true;
    }

    private void validateDueDateAndSignTime(WmCustomerContractBo contractBo, ContractProperty contractProperty) throws WmCustomerException {
        //合同有效期必须大于预计生效时间
        if (contractBo.getBasicBo().getDueDate() != 0
                && contractBo.getBasicBo().getExpectEffectiveDate() != 0
                && contractBo.getBasicBo().getExpectEffectiveDate() > (int) contractBo.getBasicBo().getDueDate()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "生效时间必须早于有效期");
        }
        List<ContractPartProperty> partPropertyList = contractProperty.getPartPropertyList();
        int todayTime = DateUtil.date2Unixtime(DateUtil.today());

        for (ContractPartProperty contractPartProperty : partPropertyList) {
            if (contractPartProperty.getOrder() == 0) {
                validatePartADueDateAndSignTime(todayTime, contractBo);
            } else if (contractPartProperty.getOrder() == 1) {
                validatePartBDueDateAndSignTime(todayTime, contractBo);
            } else {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同配置化信息异常");
            }
        }
    }

    private void validatePartBDueDateAndSignTime(int todayTime, WmCustomerContractBo contractBo) throws WmCustomerException {
        WmTempletContractSignBo partyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());
        int partyBSignerTime = DateUtil.date2Unixtime(DateUtil.string2DateDay(partyBSigner.getSignTime()));
        if (partyBSignerTime > todayTime) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "乙方签约时间不可晚于今日。");
        }
        if (contractBo.getBasicBo().getDueDate() != 0 && partyBSignerTime > contractBo.getBasicBo().getDueDate()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "乙方签约时间不可晚于合同有效期。");
        }
    }

    private void validatePartADueDateAndSignTime(int todayTime, WmCustomerContractBo contractBo) throws WmCustomerException {
        WmTempletContractSignBo partyASigner = wmContractSignService.getPartyASigner(contractBo.getSignBoList());
        int partyASignerTime = DateUtil.date2Unixtime(DateUtil.string2DateDay(partyASigner.getSignTime()));
        if (partyASignerTime > todayTime) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "甲方签约时间不可晚于今日。");
        }
        if (contractBo.getBasicBo().getDueDate() != 0 && partyASignerTime > contractBo.getBasicBo().getDueDate()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "甲方签约时间不可晚于合同有效期。");
        }
    }

    private void validateContractValidityProperty(ContractValidityInfo validityInfo, WmCustomerContractBo contractBo) throws WmCustomerException {
        long dueDate = contractBo.getBasicBo().getDueDate();
        // 验证是否支持长期有效
        if (dueDate == 0) {
            Boolean supportLongTermEffective = validityInfo.getSupportLongTermEffective();
            if (!supportLongTermEffective) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该合同不支持长期有效");
            }
        } else {
            Boolean supportValidity = validityInfo.getSupportValidity();
            if (!supportValidity) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该合同不支持设置有效期");
            }
            ContractTimeInfo leftInterval = validityInfo.getLeftInterval();
            ContractTimeInfo rightInterval = validityInfo.getRightInterval();
            checkLeftInterval(leftInterval, dueDate);
            checkRightInterval(rightInterval, dueDate);
        }
    }

    private void validateContractEffectiveTimeProperty(ContractEffectiveTimeInfo effectiveTimeInfo, WmCustomerContractBo contractBo) throws WmCustomerException {
        int expectEffectiveDate = contractBo.getBasicBo().getExpectEffectiveDate();
        // 校验是否支持立即生效
        if (expectEffectiveDate == 0) {
            Boolean supportEffectImmediately = effectiveTimeInfo.getSupportEffectImmediately();
            if (!supportEffectImmediately) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该合同不支持立即生效");
            }
        } else {
            Boolean supportEffectNotNow = effectiveTimeInfo.getSupportEffectNotNow();
            if (!supportEffectNotNow) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该合同不支持预约生效");
            }
            ContractTimeInfo leftInterval = effectiveTimeInfo.getLeftInterval();
            ContractTimeInfo rightInterval = effectiveTimeInfo.getRightInterval();
            checkLeftInterval(leftInterval, expectEffectiveDate);
            checkRightInterval(rightInterval, expectEffectiveDate);
        }
    }

    private void checkLeftInterval(ContractTimeInfo leftInterval, long dueDate) throws WmCustomerException {
        if (MccConfig.closeCheckLeftAndRightInterval()) {
            return;
        }
        String unit = leftInterval.getUnit();
        switch (unit) {
            case "天":
                if (isXXDaysBefore(dueDate, leftInterval.getCount())) {
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "超出配置化时间");
                }
                break;
            case "年":
                if (isXXYearsBefore(dueDate, leftInterval.getCount())) {
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "超出配置化时间");
                }
                break;
            default:
        }
    }

    private void checkRightInterval(ContractTimeInfo rightInterval, long dueDate) throws WmCustomerException {
        if (MccConfig.closeCheckLeftAndRightInterval()) {
            return;
        }
        String unit = rightInterval.getUnit();
        switch (unit) {
            case "天":
                if (isXXDaysLater(dueDate, rightInterval.getCount())) {
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "超出配置化时间");
                }
                break;
            case "年":
                if (isXXYearsLater(dueDate, rightInterval.getCount())) {
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "超出配置化时间");
                }
                break;
            default:
        }
    }

    private boolean isXXYearsBefore(Long dueDate, int xx) {
        DateTime xxYearsLater = DateTime.now().plusYears(xx).plusDays(1).withTimeAtStartOfDay();
        return dueDate < date2UnixTime(xxYearsLater.toDate());
    }

    private boolean isXXDaysBefore(Long dueDate, int xx) {
        DateTime xxYearsLater = DateTime.now().plusDays(xx + 1).withTimeAtStartOfDay();
        return dueDate < date2UnixTime(xxYearsLater.toDate());
    }

    private boolean isXXYearsLater(Long dueDate, int xx) {
        DateTime xxYearsLater = DateTime.now().plusYears(xx);
        return dueDate > date2UnixTime(xxYearsLater.toDate());
    }

    private boolean isXXDaysLater(Long dueDate, int xx) {
        DateTime xxYearsLater = DateTime.now().plusDays(xx);
        return dueDate > date2UnixTime(xxYearsLater.toDate());
    }

    private long date2UnixTime(Date date) {
        return date.getTime() / 1000L;
    }

}
