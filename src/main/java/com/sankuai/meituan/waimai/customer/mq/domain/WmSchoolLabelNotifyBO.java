package com.sankuai.meituan.waimai.customer.mq.domain;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
/**
 * 学校标签变更消息- 业务对象
 * -@author: houjikang
 * -@date: 2023/03/28
 * -@email: <EMAIL>
 */
@Data
public class WmSchoolLabelNotifyBO {
    /**
     * 发生变更表名
     */
    @JSONField(name = "tableName")
    String tableName;
    /**
     * 变更类型：insert-新增即打标，update-更新，delete-删除即掉标
     */
    @JSONField(name = "type")
    String type;
    /**
     * 详细数据
     */
    @JSONField(name = "data")
    WmSchoolLabelNotifyDetailBO data;
}
