package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check.delivery;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class DuplicateDeliveryContractAtomValidator implements IContractValidator {

    @Autowired
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {

        isExistTheSameTypeContract(contractBo);
        return true;
    }

    /**
     * 是否存在该客户下相同类型合同
     */
    private void isExistTheSameTypeContract(WmCustomerContractBo contractBo) throws WmCustomerException {
        WmTempletContractBasicBo basicBo = contractBo.getBasicBo();
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper
                .selectByParentIdAndType((long) basicBo.getParentId(), basicBo.getType());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return;
        }
        WmTempletContractTypeBo typeBo = new WmTempletContractTypeBo(basicBo.getType());
        String msg = String.format("提交失败，该客户已有【%s】合同，请勿重复添加", typeBo.getTypeEnum().getMsg());
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
    }


}
