package com.sankuai.meituan.waimai.customer.domain.sc.canteenstall;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.InvalidDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditTaskTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 食堂档口管理日志相关BO
 * <AUTHOR>
 * @date 2024/05/27
 * @email <EMAIL>
 */
@Data
@Builder
public class WmCanteenStallLogBO {
    /**
     * 食堂主键ID
     */
    private Integer canteenPrimaryId;
    /**
     * 审批任务ID
     */
    private Integer auditTaskId;
    /**
     * 档口绑定任务ID
     */
    private Integer bindId;
    /**
     * 档口绑定任务ID列表
     */
    private List<Integer> bindIdList;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 审批人MIS
     */
    private String auditorMis;
    /**
     * 审批人姓名
     */
    private String auditorName;
    /**
     * 审批任务类型
     * {@link CanteenStallAuditTaskTypeEnum}
     */
    private Integer auditTaskType;
    /**
     * 下一个审批节点
     */
    private Integer nextAuditNode;
    /**
     * 标记异常原因
     */
    private Integer abnormalReason;
    /**
     * 证明图片
     */
    private String proofPicture;
    /**
     * 档口绑定任务DO(变更前)
     */
    private WmCanteenStallBindDO bindDOBefore;
    /**
     * 档口绑定任务DO(变更后)
     */
    private WmCanteenStallBindDO bindDOAfter;
    /**
     * 线索DO变更前
     */
    private WmCanteenStallClueDO clueDO;
    /**
     * 档口管理任务ID
     */
    private Integer manageId;
    /**
     * 线索ID
     */
    private Long wdcClueId;
    /**
     * 操作来源
     */
    private String operationSource;
    /**e
     * 门店ID
     */
    private Long wmPoiId;
    /**
     * 线索绑定失败原因
     */
    private String clueBindFailReason;
    /**
     * 表示线索是否解除绑定。
     */
    private Boolean isClueBind;

    /**
     * 线索的原始状
     */
    private Integer clueStatus;

    /**
     * 线索的后续状态。
     *
     */
    private Integer afterClueStatus;

    /**
     *  错误数据的处理来源  InvalidDataSourceEnum
     *
     * */
    private Integer invalidDataSourceCode;


    /**
     * 待绑定食堂主键id
     */
    private Long canteenToID;

    /**
     * 原外卖门店绑定状态
     */
    private Integer beforeWmPoiBindStatus;
}
