package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.c1contract.c1medicine;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.c1contract.WmEcontractC1ContractContentGenerate;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-08-17 15:44
 * Email: <EMAIL>
 * Desc:
 */
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.C1CONTRACT_INFO_V5)
@Slf4j
@Service
public class WmEcontractC1ContractV5PdfMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext,
                                                 EcontractBatchMiddleBo middleContext)
            throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractC1ContractV5PdfMaker,customerId:{}",originContext.getCustomerId());
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.C1CONTRACT_INFO_V5;

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.C1CONTRACT);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        // 新模式使用id和version
        boolean isMedicineCustomer = middleContext.getSignDataFactor().isAMedicineBoolean();
        if(isMedicineCustomer){
            pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("C1_TEMPLATE_MEDICINE_ID", 13)); // 指定模版
            pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("C1_TEMPLATE_MEDICINE_VERSION", 0)); // (可选)指定版本，不指定或传null、传0的话则默认为当前已发布的版本
        }else{
            pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("C1_TEMPLATE_UN_MEDICINE_ID", 12));
            pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("C1_TEMPLATE_UN_MEDICINE_VERSION", 0));
        }
        pdfInfoBo.setPdfMetaContent(new WmEcontractC1ContractContentGenerate().generatePdfObject(originContext, taskBo));
        pdfInfoBo.setPdfBizContent(Lists.newArrayList());
        return pdfInfoBo;
    }
}