package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.bind;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.CustomerModuleStateEnum;
import com.sankuai.meituan.waimai.customer.adapter.StateCenterAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmContractServiceAdaptor;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractPoiProduceService;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiOplogService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.action.after.oplog.WmCusPoiRelOpLogAfterService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.action.after.oplog.WmPoiOpLogAfterService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.action.after.tag.AddPoiTagOnBindAfterService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectOpTagEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240119
 * @desc 直接绑定后置策略
 */
@Service
@Slf4j
public class DirectBindAfterStrategy implements IBindAfterStrategy {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private WmCustomerPoiOplogService wmCustomerPoiOplogService;

    @Autowired
    private WmContractPoiProduceService wmContractPoiProduceService;

    @Autowired
    private StateCenterAdapter stateCenterAdapter;

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Autowired
    private WmContractServiceAdaptor wmContractServiceAdaptor;

    @Autowired
    private WmCusPoiRelOpLogAfterService wmCusPoiRelOplogAfterService;

    @Autowired
    private WmPoiOpLogAfterService wmPoiOpLogAfterService;

    @Autowired
    private AddPoiTagOnBindAfterService addPoiTagOnBindAfterService;

    /**
     * 直接绑定后置操作
     *
     * @param context
     * @throws WmCustomerException
     */
    @Override
    public void execute(CustomerPoiBindFlowContext context) throws WmCustomerException {
        log.info("DirectBindAfterStrategy.execute,客户门店关系绑定完成，开始执行后置行为");
        directBindPostActions(context);
    }

    /**
     * 直接绑定后置操作执行
     */
    public void directBindPostActions(CustomerPoiBindFlowContext poiBindFlowContext) throws WmCustomerException {
        log.info("DirectBindAfterStrategy.directBindPostActions,poiBindFlowContext={}", JSONObject.toJSONString(poiBindFlowContext));
        WmCustomerDB wmCustomerDB = poiBindFlowContext.getWmCustomerDB();
        Set<Long> wmPoiIdSet = poiBindFlowContext.getWmPoiIdSet();
        Integer opUid = poiBindFlowContext.getOpUid();
        String opName = poiBindFlowContext.getOpName();
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";

        try {
            int customerId = wmCustomerDB.getId();

            // 批量添加客户门店属性
            wmCustomerPoiAttributeService.upsertCustomerPoiAttribute(customerId, wmPoiIdSet);

            // 变更门店主体
            wmContractServiceAdaptor.batchChangePoiSubject(customerId, wmPoiIdSet, ContractSignSubjectOpTagEnum.CUSTOMER_BIND.getCode());

            //给门店打标：新结算标签、美食城标签、资质共用标签
            addTagOnPoi(poiBindFlowContext);

            // 更新任务状态-已完成
            if (!CollectionUtils.isEmpty(poiBindFlowContext.getPoiAndTaskMaps())) {
                customerTaskService.updateTaskStatus(customerId, wmPoiIdSet, Lists.newArrayList(poiBindFlowContext.getPoiAndTaskMaps().values()));
            }

            // 客户门店列表操作：批量刷新es
            if (poiBindFlowContext.getOpSource() != null
                    && poiBindFlowContext.getOpSource().equals(CustomerTaskSourceEnum.BD_SETTLE.getCode())) {
                wmCustomerPoiListEsService.batchInsertEsSync(customerId, wmPoiIdSet);
            }

            //新增客户维度以及绑定关系维度的操作记录
            wmCusPoiRelOplogAfterService.bindAddCustomerOpLog(poiBindFlowContext);

            //门店维度-添加客户门店绑定操作日志
            wmPoiOpLogAfterService.poiBindCustomerAddOpLogOnPoi(wmCustomerDB, wmPoiIdSet, opUid, opName);

            // 插入上单状态机通知，门店维度日志
            wmContractPoiProduceService.logPoiProduce(customerId, Lists.newArrayList(wmPoiIdSet), opUid, opName);

            //同步灯塔状态机-客户
            stateCenterAdapter.batchSyncCustomerState(wmPoiIdSet, CustomerModuleStateEnum.findByCode(wmCustomerDB.getAuditStatus()), opUid, opName);

            // 发送MQ
            wmCustomerService.sendCustomerStatusNoticeMQ(customerId, CustomerMQEventEnum.CUSTOMER_BIND_POI,
                    wmPoiIdSet, opUid, opName);
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            Cat.logError(String.format("%s error errMsg=%s", CustomerMetricEnum.CUSTOMER_POI_BIND.getName(), e.getMsg()), e);
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            Cat.logError(String.format("%s error", CustomerMetricEnum.CUSTOMER_POI_BIND.getName()), e);
            throw e;
        } finally {
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_BIND.getName(), poiBindFlowContext.getPoiOplogSourceTypeEnum().getDesc(), status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_BIND.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_BIND.getTag(), poiBindFlowContext.getPoiOplogSourceTypeEnum().getDesc())
                    .tag(CustomerMetricEnum.CUSTOMER_POI_BIND.getStatus(), status).count(wmPoiIdSet.size());
        }
    }

    /**
     * 给门店打标：新结算标签、美食城标签、资质共用标签
     *
     * @param context
     */
    private void addTagOnPoi(CustomerPoiBindFlowContext context) throws WmCustomerException {
        // 新结算绑定，给门店打新结算标签
        addPoiTagOnBindAfterService.addNewSettleTagOnPoi(context);

        //门店标签
        addPoiTagOnBindAfterService.addMscTagOnPoi(context);

        //资质共用标签处理
        addPoiTagOnBindAfterService.addQuaComTagOnPoi(context);
    }

}
