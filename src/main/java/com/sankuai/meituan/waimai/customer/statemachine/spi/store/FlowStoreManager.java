package com.sankuai.meituan.waimai.customer.statemachine.spi.store;


import com.sankuai.meituan.waimai.customer.statemachine.api.FlowTrait;
import com.sankuai.meituan.waimai.customer.statemachine.api.OperationInfo;

/**
 * 流程对象信息更新功能.
 * <p>
 * 状态机使用者需实现此spi，使得框架最后能更新修改后的对象信息
 * <p>
 * Created by ji<PERSON><PERSON> on 16/8/9.
 */
public interface FlowStoreManager<F extends FlowTrait> {

    /**
     * 更新流程对象信息.
     * <p>
     * notice: 实现时注意检查对象version属性
     *
     * @param flow 更改后的流程对象
     */
    F updateFlow(F flow, OperationInfo operationInfo);

    /**
     * 获取最新流程数据
     * 当只传入flowId时,从此处获取FlowTrait
     *
     * @param flowId
     * @return
     */
    F queryFlow(String flowId);
}
