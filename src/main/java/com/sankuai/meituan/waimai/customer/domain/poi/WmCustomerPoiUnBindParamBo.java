package com.sankuai.meituan.waimai.customer.domain.poi;

import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 客户门店解绑调用入参对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WmCustomerPoiUnBindParamBo {
    /**
     * 待解绑客户id
     */
    private int customerId;
    /**
     * 待解绑门店
     */
    private Set<Long> wmPoiIdSet;
    /**
     * 客户操作日志备注
     */
    private String remark;
    /**
     * 操作人id
     */
    private Integer opUid;
    /**
     * 操作人姓名
     */
    private String opName;
    /**
     * 操作来源
     */
    private CustomerTaskSourceEnum sourceTypeEnum;
    /**
     * 操作来源对象
     */
    private CustomerOperateBO customerOperateBO;
    /**
     * 客户门店解绑操作请求类型
     */
    private CustomerPoiUnBindTypeEnum typeEnum;

}
