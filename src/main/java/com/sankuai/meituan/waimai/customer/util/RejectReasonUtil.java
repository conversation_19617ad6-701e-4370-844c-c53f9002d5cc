package com.sankuai.meituan.waimai.customer.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.zebra.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15-6-12.
 */
public class RejectReasonUtil {

    protected static final Map<String, String> TEMPLET_CONTRACT_C1_C2_DEF_MAP = new HashMap<>();

    static String lineSeperator = "\n";

    static {

        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("contractNo", "合同编号");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("contractExpireDate", "合同有效期");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("firstParty", "甲方");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("firstPartyPeople", "甲方联系人");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("firstPartyPhone", "甲方联系人电话");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("firstPartySignDate", "甲方签约日期");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("secondParty", "乙方");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("secondPartyPeople", "乙方联系人");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("secondPartyPhone", "乙方联系人电话");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("secondPartySignDate", "乙方签约日期");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("contractUrl", "合同扫描件");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("otherContractScans", "其他附件");
        TEMPLET_CONTRACT_C1_C2_DEF_MAP.put("distributionServiceSubject", "履约服务费主体");

    }

    public static String getContractRejectReason(String rejectReason, Map<String, String> map) {
        StringBuilder diff = new StringBuilder();

        if (StringUtils.isBlank(rejectReason)) {
            return diff.toString();
        }

        JSONObject rejectRes = JSON.parseObject(rejectReason).getJSONObject("rejectMap");
        if (rejectRes.containsKey("all")) {
            diff.append("直接驳回 : ").append(rejectRes.getString("all"));
        }
        for (Map.Entry<String, Object> entry : rejectRes.entrySet()) {
            if (!map.containsKey(key2Desc(entry.getKey()))) {
                continue;
            }
            diff.append(lineSeperator).append(map.get(key2Desc(entry.getKey())))
                    .append(" : ").append(entry.getValue()).append("；");
        }

        return diff.toString();
    }

    public static void main(String[] args) {
        String reason = "{\"rejectMap\":{\"all\":\"整体驳回了~~~\",\"contractUrl\":\"你也被驳回了\"}}";
        System.out.println(getTempletC1C2RejectReason(reason));
    }

    private static String key2Desc(String key) {
        String propName;

        Pattern p = Pattern.compile("_[0-9]+");
        Matcher m = p.matcher(key);
        if (m.find()) {
            propName = m.replaceAll("");
        } else {
            propName = key.toString();
        }

        return propName;
    }

    public static String getTempletC1C2RejectReason(String rejectReason) {
        return getContractRejectReason(rejectReason, TEMPLET_CONTRACT_C1_C2_DEF_MAP);
    }

}
