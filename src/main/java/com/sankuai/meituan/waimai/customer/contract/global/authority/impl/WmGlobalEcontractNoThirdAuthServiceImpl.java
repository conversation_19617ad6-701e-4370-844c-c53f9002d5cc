package com.sankuai.meituan.waimai.customer.contract.global.authority.impl;

import com.sankuai.meituan.uac.sdk.entity.UacRoleEntity;
import com.sankuai.meituan.waimai.customer.contract.dao.WmGlobalContractRoleAuthorityDBMapper;
import com.sankuai.meituan.waimai.customer.contract.global.authority.WmGlobalEcontractAuthorityService;
import com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractAuthorityCondition;
import com.sankuai.meituan.waimai.customer.domain.global.WmGlobalContractRoleAuthorityDB;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 非三方接入合同的配置权限
 * @author: liuyunjie05
 * @create: 2024/1/12 15:41
 */
@Slf4j
@Service
public class WmGlobalEcontractNoThirdAuthServiceImpl implements WmGlobalEcontractAuthorityService {

    @Resource
    private WmGlobalContractRoleAuthorityDBMapper wmGlobalContractRoleAuthorityDBMapper;


    @Override
    public void getAuthority(GlobalEcontractAuthorityCondition authorityCondition, String uid) {
        if (authorityCondition.isSuperAdmin()) {
            log.info("超级管理员拥有全部权限, 无需额外判断UAC权限");
            return;
        }
        List<Long> roleIdList = authorityCondition.getUserUacRoleList().stream().map(UacRoleEntity::getRoleId).collect(Collectors.toList());
        List<WmGlobalContractRoleAuthorityDB> wmGlobalContractRoleAuthorityDBS = wmGlobalContractRoleAuthorityDBMapper.selectByUacRoleIds(roleIdList);
        List<Integer> contractTypeCodeList = wmGlobalContractRoleAuthorityDBS.stream()
                .map(WmGlobalContractRoleAuthorityDB::getContractTypeCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        authorityCondition.setContractTypeCodeList(contractTypeCodeList);
    }

    @Override
    public int priority() {
        return 2;
    }
}
