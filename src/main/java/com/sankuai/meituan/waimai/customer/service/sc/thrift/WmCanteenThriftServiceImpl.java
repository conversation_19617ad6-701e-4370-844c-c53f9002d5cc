package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.waimai.customer.adapter.CityCommonServiceAdapter;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.service.sc.*;
import com.sankuai.meituan.waimai.customer.service.sc.auth.WmScCanteenAuthService;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallBindService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerContractorCanteenQueryListResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerContractorCanteenQueryParamBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_CANTEEN_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum;

/**
 * @program: scm
 * @description: 食堂的实现类
 * @author: jianghuimin02
 * @create: 2020-04-23 15:37
 **/
@Slf4j
@Service
public class WmCanteenThriftServiceImpl extends WmScCommonService implements WmCanteenThriftService {

    @Autowired
    private WmCanteenMapper canteenMapper;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmSchoolServerService wmSchoolServerService;

    @Autowired
    private WmScLogService wmScLogService;

    @Autowired
    private WmScCanteenServerService wmScCanteenServerService;

    @Autowired
    private WmScTagService wmScTagService;

    @Autowired
    private WmScCanteenService wmScCanteenService;

    @Autowired
    private WmCanteenService wmCanteenService;

    public final static int CANTEENID_BEGIN = 100000;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private CityCommonServiceAdapter cityCommonServiceAdapter;

    @Autowired
    private WmScCanteenAuthService wmScCanteenAuthService;

    @Autowired
    private WmCanteenStallBindService wmCanteenStallBindService;

    /**
     * 新建食堂提审
     * @param canteenBO canteenBO
     * @param userId 用户ID
     * @param userName 用户名称
     * @return 食堂主键ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public int saveCanteenTicket(CanteenBo canteenBO, int userId, String userName) throws TException, WmSchCantException {
        return wmCanteenService.saveCanteenTicket(canteenBO, userId, userName);
    }

    /**
     * 修改食堂信息保存审核
     * @param canteenBO canteenBO
     * @param userId 用户ID
     * @param userName 用户名称
     * @return 食堂主键ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public int editCanteenTicket(CanteenBo canteenBO, int userId, String userName) throws TException, WmSchCantException {
        return wmCanteenService.editCanteenTicket(canteenBO, userId, userName);
    }


    /**
     * 删除食堂
     * @methodDesc 删除食堂
     * @param id 食堂主键ID
     *            userId 用户ID
     *            userName 用户名称
     *            isRecordLog 是否记录日志
     */
    @Override
    public void deleteCanteen(int id, int userId, String userName, Boolean isRecordLog) throws TException, WmSchCantException {
        log.info("校园食堂项目:删除食堂:删除食堂入参:id:{}:userId:{}:userName:{}", id, userId, userName);
        WmCanteenDB wmCanteenDBIn = canteenMapper.selectCanteenById(id);
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDBIn);
        log.info("校园食堂项目:删除食堂::wmCanteenDBIn:{}", JSON.toJSONString(wmCanteenDBIn));
        if (wmCanteenDBIn == null) {
            throw new WmSchCantException(GET_CATEEN_FAIL, "获取食堂失败");
        }

        // 当食堂关联的档口绑定任务中，存在“线索绑定状态”、“外卖门店绑定状态”为：“绑定中”或“‘绑定成功”时，则不允许食堂被删除
        List<WmCanteenStallBindDO> bindDOListClue = wmCanteenStallBindService.getStallBindListByCanteenPrimaryIdWithClueBindingOrSuccess(id);
        List<Integer> bindStatusList = new ArrayList<>();
        bindStatusList.add((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());
        bindStatusList.add((int) CanteenStallWmPoiBindStatusEnum.REBINDING.getType());
        bindStatusList.add((int) CanteenStallWmPoiBindStatusEnum.UNBINDING.getType());
        List<WmCanteenStallBindDO> bindDOListWmPoi = wmCanteenStallBindService.getByWmPoiIdWithSpecificBindStatus(id,bindStatusList);
        if (bindDOListClue.size() > 0 || bindDOListWmPoi.size() > 0) {
            log.info("[WmCanteenThriftServiceImpl.deleteCanteen] bindDOListClue = {}, bindDOListWmPoi = {}",
                    JSONObject.toJSONString(bindDOListClue), JSONObject.toJSONString(bindDOListWmPoi));
            throw new WmSchCantException(CANTEEN_STORE_EXIST, "食堂有门店和线索关联或正在关联不可删除");
        }

        //当前用户超管,添加操作日志
        if (isRecordLog) {
            String logInfo = "操作：超管角色删除食堂通过\\n" + "食堂ID：" + id + "\\n" + "操作人：" + userName
                    + "(" + userId + ")\\n" + "操作时间：" + DateUtil.now();
            wmScLogService.insertScOptLog(OptTypeEnum.DELETE.getType(), SC_CANTEEN_LOG, id,
                    userId, userName, logInfo, "");
        }

        // 删除食堂+重新计算学校关联食堂数量
        wmScCanteenServerService.deleteCanteenById(id);
        wmSchoolServerService.subCanteeNumBySchoolId(wmCanteenDBIn.getSchoolId());

        // 删除完之后再计算食堂承包商的等级
        int contractorId = wmCanteenDBIn.getContractorId();
        if (wmCanteenDBIn.getCanteenAttribute() == CanteenAttributeEnum.CONTRACTOR.getType() && contractorId > 0) {
            computeFinalContractor(contractorId, new CanteenBo(), userId, userName);
        }
    }

    /**
     * 更新完库之后计算最终的等级
     */
    public void computeFinalContractor(int contractorId, CanteenBo nowCanteen, int userId, String userName) throws TException, WmSchCantException {
        log.info("校园食堂:更新完库之后计算最终的等级, computeFinalContractor:contractorId:{}, nowCanteen:{}, userId:{}, userName:{}", contractorId, JSONUtil.toJSONString(nowCanteen), userId, userName);
        int grade = wmScCanteenServerService.computeContractorGrade(contractorId);
        nowCanteen.setGrade(grade);
        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setGrade(grade);
        wmCanteenDB.setContractorId(contractorId);
        int result = canteenMapper.updateCanteenGrade(wmCanteenDB);

        //等级变更打标
        wmScTagService.updatePoiGradeByContractId(contractorId, grade, userId, userName);
        log.info("校园食堂:更新完库之后计算最终的等级:contractorId:{}:result:{}:canteen:{}", contractorId, result, JSONUtil.toJSONString(nowCanteen));
    }


    /**
     * 获取单个食堂信息
     * @param id 食堂主键ID
     * @return CanteenBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public CanteenBo getCanteen(int id) throws TException, WmSchCantException {
        return wmCanteenService.getCanteen(id);
    }

    /**
     * 食堂详情页面-获取食堂和提审修改的diff内容
     * @param id 食堂主键id
     * @return CanteenAuditDetailBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public CanteenAuditDetailBo getCanteenAndAuditDiffForPage(Integer id) throws TException, WmSchCantException {
        return wmCanteenService.getCanteenAndAuditDiffForPage(id);
    }

    /**
     * 分页获取食堂列表
     * @param canteenQueryBo 查询条件
     * @return WmCanteenListPageData
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public WmCanteenListPageData getCanteenList(CanteenQueryBo canteenQueryBo) throws TException, WmSchCantException {
        return wmCanteenService.getCanteenList(canteenQueryBo);
    }

    /**
     * 获取任务系统中的食堂审核信息
     * @param taskId 任务id
     * @return Canteen4AuditPageBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public Canteen4AuditPageBo getCanteen4AuditPage(Integer taskId) throws TException, WmSchCantException {
        return wmCanteenService.getCanteen4AuditPage(taskId);
    }

    /**
     * 查询食堂审批信息
     * @param auditSystemId 审批任务ID
     * @param auditSystemType 审批系统类型
     * @param userId 用户ID
     * @return Canteen4AuditPageBo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public Canteen4AuditPageBo getCanteenAuditInfo(String auditSystemId, Integer auditSystemType, Integer userId) throws WmSchCantException, TException {
        return wmCanteenService.getCanteenAuditInfo(auditSystemId, auditSystemType, userId);
    }

    /**
     * 查询承包商关联的城市和食堂的名称
     */
    @Override
    public CityAndCanteenBo getCityAndCanteen(int contractorId) throws TException, WmSchCantException {
        List<WmCanteenDB> wmCanteenDBS = canteenMapper.getCanteenByContractorId(contractorId);
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDBS);
        List<WmSchoolDB> wmSchoolDBS = wmSchoolMapper.selectEffectSchoolsByContractorId(contractorId);
        Set<String> cityNames = Sets.newHashSet();
        List<String> cityNameList = Lists.newArrayList();
        List<String> canteenNames = Lists.newArrayList();
        if (wmCanteenDBS != null) {
            for (WmCanteenDB canteenDB : wmCanteenDBS) {
                if (EffectiveStatusEnum.EFFECTIVE.getType() != canteenDB.getEffective()) {
                    continue;
                }
                canteenNames.add(canteenDB.getCanteenName());
            }
        }
        if (wmSchoolDBS != null) {
            for (WmSchoolDB wmSchoolDB : wmSchoolDBS) {
                if (wmSchoolDB.getCityId() > 0) {
                    cityNames.add(cityCommonServiceAdapter.getNameById(wmSchoolDB.getCityId()));
                }
            }
        }
        //计算承包商的供给分级
        cityNameList.addAll(cityNames);
        int grade = wmScCanteenServerService.computeContractorGrade(contractorId);
        String gradeDesc = ContractorGradeEnum.getByType(grade).getName();
        CityAndCanteenBo cityAndCanteenBo = new CityAndCanteenBo();
        cityAndCanteenBo.setCanteenNames(canteenNames);
        cityAndCanteenBo.setCityNames(cityNameList);
        cityAndCanteenBo.setGradeDecs(gradeDesc);
        return cityAndCanteenBo;
    }

    /**
     * 绑定食堂责任人
     * @param wmScCanteenOwnerBindDTO wmScCanteenOwnerBindDTO
     * @return true: 责任人发生变更
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public Boolean bindCanteenOwner(WmScCanteenOwnerBindDTO wmScCanteenOwnerBindDTO) throws TException, WmSchCantException {
        try {
            return wmScCanteenService.bindCanteenOwner(wmScCanteenOwnerBindDTO);
        } catch (Exception e) {
            log.error("[WmCanteenThriftServiceImpl.bindCanteenOwner] wmScCanteenOwnerBindDTO = {}", JSONObject.toJSONString(wmScCanteenOwnerBindDTO), e);
            throw new WmSchCantException(SERVER_ERROR, "修改食堂责任人异常");
        }
    }

    /**
     * 食堂取消合作
     * @param id 食堂主键ID
     * @param misId 操作人mis
     * @param userId 操作人uid
     * @param userName 操作人名称
     * @return true: 取消成功
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public boolean setCanteenStatusUnCooperateV2(Long id, String misId, Integer userId, String userName) throws TException, WmSchCantException {
        log.info("[WmCanteenThriftServiceImpl.setCanteenStatusUnCooperateV2] input param: id={}, misId={}, userId={}, userName={}", id, misId, userId, userName);
        if (id == null || id <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "参数ID不合法");
        }
        WmCanteenDB wmCanteenDB = canteenMapper.selectCanteenById(id.intValue());
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDB);
        // 校验食堂信息, 包括是否生效\是否合作\学校是否存在等
        wmScCanteenService.checkCanteenWhenUncooperate(wmCanteenDB);

        // 解绑该食堂所有绑定的线索与外卖门店
        wmCanteenStallBindService.cancelCooperation(id.intValue(), userId, userName, "食堂取消合作");
        return true;
    }

    @Override
    public Map<Integer, Integer> countCanteenByContractorIds(List<Integer> contractorIds) throws TException, WmSchCantException {
        log.info("校园食堂项目:批量根据承包商ID获取关联食堂数:contractorIds:{}", JSONUtil.toJSONString(contractorIds));
        Map<Integer, Integer> result = new HashMap<>();
        for (Integer contractorId : contractorIds) {
            int canteenCount = canteenMapper.countCanteenByContractorId(contractorId);
            result.put(contractorId, canteenCount);
        }
        log.info("校园食堂项目:批量根据承包商ID获取关联食堂数结果:contractorIds:{}, result:{}", JSONUtil.toJSONString(contractorIds), JSONUtil.toJSONString(result));
        return result;
    }

    @Override
    public Map<Integer, Integer> countCanteenPoiByContractorIds(List<Integer> contractorIds) throws TException, WmSchCantException {
        log.info("校园食堂项目:批量根据承包商ID获取关联门店数:contractorIds:{}", JSONUtil.toJSONString(contractorIds));
        Map<Integer, Integer> result = new HashMap<>();
        for (Integer contractorId : contractorIds) {
            Integer canteenPoiCount = canteenMapper.countCanteenPoiByContractorId(contractorId);
            if (canteenPoiCount != null) {
                result.put(contractorId, canteenPoiCount);
            }
        }
        log.info("校园食堂项目:批量根据承包商ID获取关联门店数结果:contractorIds:{}, result:{}", JSONUtil.toJSONString(contractorIds), JSONUtil.toJSONString(result));
        return result;
    }

    /**
     * 根据食堂Id或食堂name查询食堂信息
     */
    @Override
    public List<CanteenListQuaryBo> getCanteensByIdOrName(String canteenIdOrName) throws TException, WmSchCantException {
        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setCanteenName(canteenIdOrName);
        List<WmCanteenDB> canteenBos = wmCanteenMapper.selectCanteenList(wmCanteenDB);
        wmScCanteenSensitiveWordsService.readWhenSelect(canteenBos);
        if (Pattern.compile("[0-9]*").matcher(canteenIdOrName).matches()
                && new BigDecimal(canteenIdOrName).compareTo(new BigDecimal(Integer.MAX_VALUE)) == -1) {
            wmCanteenDB.setCanteenName("");
            wmCanteenDB.setCanteenId(Integer.parseInt(canteenIdOrName));
            List<WmCanteenDB> canteenDB = wmCanteenMapper.selectCanteenList(wmCanteenDB);
            if (canteenDB != null && canteenDB.size() > 0 && !canteenBos.stream().filter(w -> w.getId() == canteenDB.get(0).getId()).findAny().isPresent()) {
                canteenBos.add(canteenDB.get(0));
            }
        }

        List<CanteenListQuaryBo> result = new ArrayList<>();
        for (WmCanteenDB canteenDB : canteenBos) {
            CanteenListQuaryBo canteenListQuaryBo = new CanteenListQuaryBo();
            canteenListQuaryBo.setId(canteenDB.getId());
            canteenListQuaryBo.setCanteenId(canteenDB.getCanteenId());
            canteenListQuaryBo.setCanteenName(canteenDB.getCanteenName());
            result.add(canteenListQuaryBo);
        }
        return result;
    }

    /**
     * 根据食堂Id或食堂name查询食堂列表
     * @param canteenIdOrName 食堂Id或食堂name
     * @param canteenId 当前食堂ID
     * @return 食堂信息列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public List<CanteenListQuaryBo> getCanteensOfDesignatedSchoolByIdOrName(String canteenIdOrName, Integer canteenId) throws TException, WmSchCantException {
        log.info("[WmCanteenThriftServiceImpl.getCanteensOfDesignatedSchoolByIdOrName] input param: canteenIdOrName = {}, canteenId = {}",
                canteenIdOrName, canteenId);
        if (canteenId == null || canteenIdOrName == null) {
            return Lists.newLinkedList();
        }
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenId);
        if (wmCanteenDB == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂Id不合法");
        }
        Integer schoolId = wmCanteenDB.getSchoolId();
        List<WmCanteenDB> canteensOfDesignatedSchool = wmCanteenMapper.selectCanteensByScId(schoolId);
        if (CollectionUtils.isEmpty(canteensOfDesignatedSchool)) {
            return Lists.newLinkedList();
        }
        List<Integer> canteenIdsOfDesignatedSchool = canteensOfDesignatedSchool
                .stream()
                .map(WmCanteenDB::getId)
                .collect(Collectors.toList());
        List<CanteenListQuaryBo> canteenListQuaryBoList = getCanteensByIdOrName(canteenIdOrName);
        canteenListQuaryBoList = canteenListQuaryBoList.stream().filter((item) ->
                {
                    return canteenIdsOfDesignatedSchool.contains(item.getId());
                }
        ).collect(Collectors.toList());
        return canteenListQuaryBoList;
    }

    @Override
    public List<CanteenBo> getCanteensBySchoolId(int schoolId) throws TException, WmSchCantException {
        return wmCanteenService.getCanteensBySchoolId(schoolId);
    }

    /**
     * 刷新食堂门店标签
     * @param canteenId 食堂物理ID
     * @param userId    操作人ID
     * @param userName  操作人名字
     * @return 需要重新绑定标签的门店ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public List<Long> refreshCanteenPoiTag(Integer canteenId, int userId, String userName) throws TException, WmSchCantException {
        return wmScTagService.refreshCanteenPoiTag(canteenId, userId, userName);
    }

    @Override
    public WmCustomerContractorCanteenQueryListResultBO getContractorCanteenList(WmCustomerContractorCanteenQueryParamBO param) throws TException, WmSchCantException {
        return wmScCanteenService.getContractorCanteenList(param);
    }

    /**
     * 获取最大的食堂ID
     * @return 最大的食堂ID
     */
    @Override
    public Integer getMaxCanteenId() {
        return wmScCanteenService.getMaxCanteenId();
    }

    /**
     * 更新食堂合作状态-定时任务
     * @param canteenId 食堂ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public void refreshCanteenCooperationStatus(Integer canteenId) throws TException, WmSchCantException {
        wmScCanteenService.refreshCanteenCooperationStatus(canteenId);
    }

    /**
     * 获取用户对食堂某一操作的鉴权结果
     * @param uid 用户ID
     * @param operationCode 操作code
     * @param canteenPrimaryId 食堂主键ID
     * @return true: 有权限 / false: 无权限
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public Boolean getOperationAuthAssertResult(Integer uid, String operationCode, Integer canteenPrimaryId) throws WmSchCantException, TException {
        return wmScCanteenAuthService.getOperationAuthAssertResult(uid, operationCode, canteenPrimaryId);
    }

    /**
     * 查询单个食堂批量操作的鉴权结果
     * @param operationList 操作列表
     * @param canteenPrimaryId 食堂主键ID
     * @param uid 用户ID
     * @return 鉴权结果Map: key->operationCode, value->assert result
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public Map<String, Boolean> batchGetOperationAuthAssertResult(Set<String> operationList, Integer canteenPrimaryId, Integer uid)
            throws WmSchCantException, TException {
        return wmScCanteenAuthService.batchGetOperationAuthAssertResult(operationList, canteenPrimaryId, uid);
    }

    /**
     * 根据食堂ID精确搜索或根据食堂名称前缀模糊搜索食堂列表
     * @param content 模糊搜索内容
     * @return List<CanteenBo>
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public List<CanteenBo> getCanteenListByFuzzySearch(String content) throws TException, WmSchCantException {
        return wmScCanteenService.getCanteenListByFuzzySearch(content);
    }

    /**
     * 根据食堂ID精确搜索或根据食堂名称前缀模糊搜索食堂列表
     * @param content 模糊搜索内容
     * @return List<CanteenBo>
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public List<CanteenBo> getCanteenListBySchoolId(String content,Long shoolId) throws TException, WmSchCantException {
        return wmScCanteenService.getCanteenListBySchoolId(content,shoolId);
    }

    /**
     * 根据学校主键ID查询操作人拥有数据权限的食堂
     * @param schoolPrimaryId 学校主键ID
     * @param uid 用户ID
     * @return List<CanteenBo>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public List<CanteenBo> getCanteenListBySchoolPrimaryIdWithAuth(Integer schoolPrimaryId, Integer uid) throws WmSchCantException, TException {
        return wmScCanteenService.getCanteenListBySchoolPrimaryIdWithAuth(schoolPrimaryId, uid);
    }


    /**
     * 食堂信息审批通过
     * @param auditDTO auditDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public void passCanteenInfoAudit(WmCanteenAuditDTO auditDTO) throws WmSchCantException, TException {
        wmCanteenService.passCanteenInfoAuditByHummingBird(auditDTO);
    }

    /**
     * 食堂信息审批驳回
     * @param auditDTO auditDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public void rejectCanteenInfoAudit(WmCanteenAuditDTO auditDTO) throws WmSchCantException, TException {
        wmCanteenService.rejectCanteenInfoAuditByHummingBird(auditDTO);
    }

    /**
     * 食堂信息食堂品类批量上传
     * @param canteenBoList canteenBoList
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public Canteen4AuditPageBo canteenBoExcelUpLoad(List<CanteenBo> canteenBoList) throws WmSchCantException, TException {
        wmCanteenService.canteenBoExcelUpLoad(canteenBoList);
        return null;
    }
}
