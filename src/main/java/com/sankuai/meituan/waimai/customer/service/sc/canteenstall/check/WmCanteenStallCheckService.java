package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.check;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolAreaMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallClueMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueDO;
import com.sankuai.meituan.waimai.customer.service.sc.WmScCanteenService;
import com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusMachine;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.AorDto;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.infra.constants.WmUniAorType;
import com.sankuai.meituan.waimai.poicategory.bo.WmPoiCateDic;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueParseErrorEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallBindSubmitDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallCheckFailDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallClueExcelDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiHighSeasPoiInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.wdc.domain.service.common.vo.WdcPoiView;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 食堂档口管理校验相关服务
 * <AUTHOR>
 * @date 2024/05/30
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallCheckService {

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private CanteenStatusMachine canteenStatusMachine;

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Autowired
    private WmPoiCategoryCacheServiceAdapter wmPoiCategoryCacheServiceAdapter;

    @Autowired
    private WmOpenCityServiceAdapter wmOpenCityServiceAdapter;

    @Autowired
    private WmHighseasThriftServiceAdapter wmHighseasThriftServiceAdapter;

    @Autowired
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    @Autowired
    private WdcPoiQueryServiceAdapter wdcPoiQueryServiceAdapter;

    @Autowired
    private WmCanteenStallClueMapper wmCanteenStallClueMapper;

    @Autowired
    private WdcRelationServiceAdapter wdcRelationServiceAdapter;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmGisUniAorServiceAdapter wmGisUniAorServiceAdapter;

    @Autowired
    private WmScCanteenService wmScCanteenService;

    /**
     * 门店删除
     */
    private static final int WM_POI_ID_IS_DELETE = 1;

    public final String CANTEEN_STATUS_CHECK_FAIL_MSG = "食堂信息未生效或在审批中，不可保存";

    public final String CANTEEN_STATUS_CHECK_FOR_CLUE_BIND_FAIL_MSG = "食堂信息未生效或在审批中，不可绑定";

    public final String CLUE_BIND_OTHER_CANTEEN_CHECK_FAIL_MSG = "该线索有与其他食堂的绑定中或绑定成功的档口绑定任务";

    public final String CLUE_AOR_CHECK_FAIL_MSG = "该线索蜂窝与食堂所属学校的蜂窝不一致";

    public final String CLUE_CATE_CHECK_FAIL_MSG = "该线索品类不属于美食、甜点、饮品下的末级品类";

    public final String CLUE_COORDINATE_CHECK_FAIL_MSG = "该线索不在学校范围内";

    public final String CLUE_VALID_CHECK_FAIL_MSG = "该线索不存在或不是WDC收录为有效开门的主门店";

    public final String WM_POI_VALID_CHECK_FAIL_MSG = "该外卖门店ID不存在";

    public final String WM_POI_BIND_OTHER_CANTEEN_CHECK_FAIL_MSG = "该外卖门店有与其他食堂的绑定中或已绑定的档口绑定任务";

    public final String WM_POI_CATE_CHECK_FAIL_MSG = "该外卖门店品类不属于美食、甜点、饮品下的末级品类";

    public final String WM_POI_AOR_CHECK_FAIL_MSG = "该外卖门店蜂窝与食堂所属学校的蜂窝不一致";

    public final String WM_POI_COORDINATE_CHECK_FAIL_MSG = "该外卖门店不在学校范围内";

    public final String WM_POI_REBINDING_UNBIND = "该外卖门店正在绑定或解绑中的任务";



    /**
     * 批量创建线索-校验上传的Excel文件列表内容
     * @param canteenPrimaryId 食堂主键ID
     * @param excelDTOList 上传的Excel文件列表
     * @return List<WmCanteenStallClueExcelDTO>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public List<WmCanteenStallClueExcelDTO> checkClueExcelDTOList(Integer canteenPrimaryId, List<WmCanteenStallClueExcelDTO> excelDTOList)
            throws WmSchCantException, TException {
        log.info("[WmCanteenStallService.checkClueExcelDTOList] input param: canteenPrimaryId = {}, excelDTOList = {}",
                canteenPrimaryId, JSONObject.toJSONString(excelDTOList));
        if (canteenPrimaryId == null || canteenPrimaryId <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂ID信息为空");
        }

        WmCanteenDB canteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (canteenDB == null) {
            log.error("[WmCanteenStallService.checkClueExcelDTOList] canteenDB is null. canteenPrimaryId = {}", canteenPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂查询结果为空");
        }

        // 1-食堂信息未生效或审批中校验
        String canteenStatusCheckMsg = checkCanteenStatusAuditingOrInsertReject(canteenDB.getId());
        if (StringUtils.isNotBlank(canteenStatusCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, canteenStatusCheckMsg);
        }

        // 2-食堂档口数量校验
        String stallNumLimitCheckMsg = checkCanteenStallNumLimit(canteenDB.getId(), excelDTOList.size());
        if (StringUtils.isNotBlank(stallNumLimitCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, stallNumLimitCheckMsg);
        }

        // 3-校验Excel文件内容
        List<WmCanteenStallClueExcelDTO> clueCheckExcelDTOList = checkCanteenStallClueExcelDTOList(excelDTOList);
        log.info("[WmCanteenStallService.clueCheckExcelDTOList] clueCheckExcelDTOList = {}", JSONObject.toJSONString(clueCheckExcelDTOList));
        return clueCheckExcelDTOList;
    }

    /**
     * 创建线索信息校验
     * @param manageId 档口管理ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSubmitByBatchCreateClue(Integer manageId) throws WmSchCantException {
        // 1-根据档口管理任务ID查询线索信息列表
        List<WmCanteenStallClueDO> canteenStallClueDOList = wmCanteenStallClueMapper.selectByManageId(manageId);
        if (CollectionUtils.isEmpty(canteenStallClueDOList)) {
            log.error("[WmCanteenStallService.checkSubmitByBatchCreateClue] canteenStallClueDOList is empty. manageId = {}", manageId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂档口线索信息为空");
        }

        // 2-校验是否已经创建线索
        for (WmCanteenStallClueDO clueDO : canteenStallClueDOList) {
            if (clueDO.getBindId() > 0) {
                log.error("[WmCanteenStallService.checkSubmitByBatchCreateClue] bindId > 0. manageId = {}", manageId);
                throw new WmSchCantException(BIZ_PARA_ERROR, "已完成创建线索，请勿重复提交");
            }
        }

        // 3-根据食堂主键ID查询关联学校信息
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenStallClueDOList.get(0).getCanteenPrimaryId());
        if (wmCanteenDB == null) {
            log.error("[WmCanteenStallService.checkSubmitByBatchCreateClue] wmCanteenDB is null. canteenPrimaryId = {}", canteenStallClueDOList.get(0).getCanteenPrimaryId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂信息查询为空");
        }

        // 4-根据学校主键ID查询学校范围列表
        List<WmScSchoolAreaDO> schoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmCanteenDB.getSchoolId());
        List<String> schoolAreaList = schoolAreaDOList.stream()
                .map(WmScSchoolAreaDO::getArea)
                .collect(Collectors.toList());

        // 5-所有线索的扎点信息校验
        String coordinateCheckMsg = checkClueListCoordinate(canteenStallClueDOList, schoolAreaList);
        if (StringUtils.isNotBlank(coordinateCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, coordinateCheckMsg);
        }

        // 6-食堂信息未生效或审批中校验
        String canteenStatusCheckMsg = checkCanteenStatusAuditingOrInsertReject(wmCanteenDB.getId());
        if (StringUtils.isNotBlank(canteenStatusCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, canteenStatusCheckMsg);
        }

        // 7-食堂档口数量限制校验
        String stallNumLimitCheckMsg = checkCanteenStallNumLimit(wmCanteenDB.getId(), canteenStallClueDOList.size());
        if (StringUtils.isNotBlank(stallNumLimitCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, stallNumLimitCheckMsg);
        }
    }

    /**
     * 校验食堂状态是否未生效或在审批中(线索绑定校验)
     * @param canteenPrimaryId 食堂主键ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String checkCanteenStatusAuditingOrInsertRejectForClueBind(Integer canteenPrimaryId) throws WmSchCantException {
        WmCanteenDB canteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (canteenDB == null) {
            log.error("[WmCanteenStallCheckService.checkCanteenStatusAuditingOrInsertRejectForClueBind] canteenDB is null. canteenPrimaryId = {}", canteenPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂信息查询为空");
        }

        if (canteenStatusMachine.isAuditing(canteenDB) || canteenStatusMachine.isInsertReject(canteenDB)) {
            log.warn("[WmCanteenStallService.checkCanteenStatusAuditingOrInsertRejectForClueBind] canteen is auditing or insert reject. canteenDB = {}", JSONObject.toJSONString(canteenDB));
            return CANTEEN_STATUS_CHECK_FOR_CLUE_BIND_FAIL_MSG;
        }
        return Strings.EMPTY;
    }


    /**
     * 校验食堂状态是否未生效或在审批中
     * @param canteenPrimaryId 食堂主键ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String checkCanteenStatusAuditingOrInsertReject(Integer canteenPrimaryId) throws WmSchCantException {
        WmCanteenDB canteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (canteenDB == null) {
            log.error("[WmCanteenStallCheckService.checkCanteenStatusAuditingOrInsertReject] canteenDB is null. canteenPrimaryId = {}", canteenPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂信息查询为空");
        }

        if (canteenStatusMachine.isAuditing(canteenDB) || canteenStatusMachine.isInsertReject(canteenDB)) {
            log.warn("[WmCanteenStallService.checkCanteenStatusAuditingOrInsertReject] canteen is auditing or insert reject. canteenDB = {}", JSONObject.toJSONString(canteenDB));
            return CANTEEN_STATUS_CHECK_FAIL_MSG;
        }
        return Strings.EMPTY;
    }

    /**
     * 食堂档口数量校验
     * @param canteenPrimaryId 食堂主键ID
     * @param insertNum 新增档口数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String checkCanteenStallNumLimit(Integer canteenPrimaryId, Integer insertNum) throws WmSchCantException {
        WmCanteenDB canteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (canteenDB == null) {
            log.error("[WmCanteenStallCheckService.checkCanteenStallNumLimit] canteenDB is null. canteenPrimaryId = {}", canteenPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂信息查询为空");
        }

        // 已占用档口数量(食堂的线索绑定状态处于“绑定成功”/“绑定中”的档口绑定任务数量)
        List<WmCanteenStallBindDO> canteenStallBindDOList = wmCanteenStallBindMapper.selectByCanteenPrimaryIdWithClueBindingOrBindSuccess(canteenDB.getId());

        log.info("[WmCanteenStallCheckService.checkCanteenStallNumLimit] stallNum = {}, stallNumOccupied = {}, insertNum = {}",
                canteenDB.getStallNum(), canteenStallBindDOList.size(), insertNum);

        if (wmScCanteenService.isCanteenInCanteenStallAccuracyGrayList(canteenPrimaryId)) {
            if (insertNum + canteenStallBindDOList.size() > canteenDB.getOfflineBizStallNum()) {
                return "食堂线下营业档口数量" + canteenDB.getOfflineBizStallNum() + "，已占用" + canteenStallBindDOList.size() +
                        "，本次上传" + insertNum + "条数据，数量不足不可保存。请删减或解绑部分档口绑定任务，或修改食堂线下营业档口数量后再进行";
            }
        } else {
            if (insertNum + canteenStallBindDOList.size() > canteenDB.getStallNum()) {
                return "食堂档口数量" + canteenDB.getStallNum() + "，已占用" + canteenStallBindDOList.size() +
                        "，本次上传" + insertNum + "条数据，数量不足不可保存。请删减或解绑部分档口绑定任务，或修改食堂档口数量后再进行";
            }
        }

        return Strings.EMPTY;
    }

    /**
     * 食堂档口数量校验(公海绑定提交)
     * @param canteenPrimaryId 食堂主键ID
     * @param wdcClueId 线索ID
     * @return 校验信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String checkCanteenStallNumLimitByWdcClueId(Integer canteenPrimaryId, Long wdcClueId) throws WmSchCantException {
        // 1-通过线索ID+食堂ID搜索档口绑定任务
        WmCanteenStallBindDO bindDO = wmCanteenStallBindMapper.selectByWdcClueIdAndCanteenPrimaryId(wdcClueId, canteenPrimaryId);
        log.info("[WmCanteenStallCheckService.checkCanteenStallNumLimitByWdcClueId] bindDO = {}", JSONObject.toJSONString(bindDO));

        // 2-存在档口绑定任务且线索绑定状态为"绑定中"/"绑定成功"
        if (bindDO != null && (bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BINDING.getType())
                || bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType()))) {
            return Strings.EMPTY;
        }

        // 3-食堂关联的线索绑定状态为"绑定中"/"绑定成功"的档口绑定任务 < 食堂维护档口数量
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindMapper.selectByCanteenPrimaryIdWithClueBindingOrBindSuccess(canteenPrimaryId);
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);

        if (wmScCanteenService.isCanteenInCanteenStallAccuracyGrayList(canteenPrimaryId)) {
            if (bindDOList.size() >= wmCanteenDB.getOfflineBizStallNum()) {
                return "食堂线下营业档口数量" + wmCanteenDB.getOfflineBizStallNum() + "，已占用" + bindDOList.size() +
                        "，本次上传1条数据，数量不足不可保存。请删减或解绑部分档口绑定任务，或修改食堂线下营业档口数量后再进行";
            }
        } else {
            if (bindDOList.size() >= wmCanteenDB.getStallNum()) {
                return "食堂档口数量" + wmCanteenDB.getStallNum() + "，已占用" + bindDOList.size() +
                        "，本次上传1条数据，数量不足不可保存。请删减或解绑部分档口绑定任务，或修改食堂档口数量后再进行";
            }
        }


        return Strings.EMPTY;
    }

    /**
     * 食堂档口数量校验(外卖门店绑定提交)
     * @param canteenPrimaryId 食堂主键ID
     * @param wmPoiId 门店ID
     * @return 校验信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String checkCanteenStallNumLimitByWmPoiId(Integer canteenPrimaryId, Long wmPoiId) throws WmSchCantException {
        // 1-门店是否有关联的线索ID
        Long wdcClueId = wdcRelationServiceAdapter.getWdcClueIdByWmPoiId(wmPoiId);
        if (wdcClueId == null) {
            log.info("[WmCanteenStallCheckService.checkCanteenStallNumLimitByWmPoiId] wdcClueId is null. canteenPrimaryId = {}, wmPoiId = {}",
                    canteenPrimaryId, wmPoiId);
            return Strings.EMPTY;
        }

        // 2-类似公海档口校验
        return checkCanteenStallNumLimitByWdcClueId(canteenPrimaryId, wdcClueId);
    }

    /**
     * 校验所有线索的扎点信息
     * @param canteenStallClueDOList 食堂档口线索DO列表
     * @param schoolAreaList 学校范围列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String checkClueListCoordinate(List<WmCanteenStallClueDO> canteenStallClueDOList, List<String> schoolAreaList)
            throws WmSchCantException {
        // 1-是否所有线索都有扎点信息
        List<String> cluePoiNameListByCoordinateEmpty = new ArrayList<>();
        for (WmCanteenStallClueDO clueDO : canteenStallClueDOList) {
            if (StringUtils.isBlank(clueDO.getCluePoiCoordinate()) || "[]".equals(clueDO.getCluePoiCoordinate())) {
                cluePoiNameListByCoordinateEmpty.add(clueDO.getCluePoiName());
            }
        }

        if (CollectionUtils.isNotEmpty(cluePoiNameListByCoordinateEmpty)) {
            log.info("[WmCanteenStallCheckService.checkClueListCoordinate] cluePoiNameListByCoordinateEmpty = {}", JSONObject.toJSONString(cluePoiNameListByCoordinateEmpty));
            return StringUtils.join(cluePoiNameListByCoordinateEmpty, "、") + "扎点未完成，请完成后提交";
        }

        // 2-校验所有线索扎点是否在学校范围内
        List<String> cluePoiNameListByCoordinateInvalid = new ArrayList<>();
        for (WmCanteenStallClueDO clueDO : canteenStallClueDOList) {
            List<WmScPoint> wmScPoints = JSONObject.parseArray(clueDO.getCluePoiCoordinate(), WmScPoint.class);
            int clueLatitude = wmScPoints.get(0).getX();
            int clueLongitude = wmScPoints.get(0).getY();
            if (!WmRtreeUtil.withinAreaList(clueLatitude, clueLongitude, schoolAreaList)) {
                cluePoiNameListByCoordinateInvalid.add(clueDO.getCluePoiName());
            }
        }

        if (CollectionUtils.isNotEmpty(cluePoiNameListByCoordinateInvalid)) {
            log.info("[WmCanteenStallCheckService.checkClueListCoordinate] cluePoiNameListByCoordinateInvalid = {}", JSONObject.toJSONString(cluePoiNameListByCoordinateInvalid));
            return StringUtils.join(cluePoiNameListByCoordinateInvalid, "、") + "扎点不在学校范围内，请调整后提交";
        }
        return Strings.EMPTY;
    }

    /**
     * 批量创建线索-校验上传的Excel文件列表内容
     * @param excelDTOList 上传的Excel文件列表
     * @return List<WmCanteenStallClueExcelDTO>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<WmCanteenStallClueExcelDTO> checkCanteenStallClueExcelDTOList(List<WmCanteenStallClueExcelDTO> excelDTOList)
            throws WmSchCantException {
        if (CollectionUtils.isEmpty(excelDTOList)) {
            log.error("[WmCanteenStallCheckService.checkClueExcelDTOList] excelDTOList is empty. excelDTOList = {}", JSONObject.toJSONString(excelDTOList));
            throw new WmSchCantException(BIZ_PARA_ERROR, "文件上传内容为空");
        }

        // 1-查询二级城市Map
        List<String> secondCityNameList = excelDTOList.stream()
                .map(WmCanteenStallClueExcelDTO::getSecondCityName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Map<String, WmOpenCity> secondCityMap = wmOpenCityServiceAdapter.getSecondCityMapBySecondCityNameList(secondCityNameList);

        // 2-查询三级城市Map
        List<String> thirdCityNameList = excelDTOList.stream()
                .map(WmCanteenStallClueExcelDTO::getThirdCityName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Map<String, List<WmOpenCity>> thridCityMap = wmOpenCityServiceAdapter.getThirdCityMapByThirdCityNameList(thirdCityNameList);

        for (WmCanteenStallClueExcelDTO excelDTO : excelDTOList) {
            List<Integer> errorReasonList = new ArrayList<>();
            // 1-解析外卖城市(二级城市)
            if (!parseAndSetSecondCityInfo(excelDTO, secondCityMap)) {
                errorReasonList.add((int) CanteenStallClueParseErrorEnum.SECOND_CITY_PARSE_ERROR.getType());
            }

            // 2-解析外卖行政区(三级城市)
            if (!parseAndSetThirdCityInfo(excelDTO, thridCityMap)) {
                errorReasonList.add((int) CanteenStallClueParseErrorEnum.THIRD_CITY_PARSE_ERROR.getType());
            }

            // 3-解析门店品类
            if (!parseAndSetCluePoiCate(excelDTO)) {
                errorReasonList.add((int) CanteenStallClueParseErrorEnum.CATE_PARSE_ERROR.getType());
            } else {
                // 4-门店品类是否是末级品类
                if (!parseAndSetCluePoiCateTree(excelDTO)) {
                    errorReasonList.add((int) CanteenStallClueParseErrorEnum.CATE_NOT_BELONG_FOOD_DESSERT_DRINK.getType());
                }
            }

            // 5-门店地址是否填写
            if (!isClueAddressFill(excelDTO.getCluePoiAddress())) {
                excelDTO.setCluePoiAddress("-");
                errorReasonList.add((int) CanteenStallClueParseErrorEnum.POI_ADDRESS_IS_NULL.getType());
            }

            // 6-门店电话是否合规
            if (!isCluePhoneNumValid(excelDTO.getCluePoiPhoneNum())) {
                errorReasonList.add((int) CanteenStallClueParseErrorEnum.POI_PHONE_NOT_VALID.getType());
            }

            // 7-门店名称未录入
            if (!isClueNameFill(excelDTO.getCluePoiName())) {
                excelDTO.setCluePoiName("-");
                errorReasonList.add((int) CanteenStallClueParseErrorEnum.POI_NAME_NOT_ENTERED.getType());
            }

            // 8-门店名称过长
            if (!isClueNameLengthMatch(excelDTO.getCluePoiName())) {
                errorReasonList.add((int) CanteenStallClueParseErrorEnum.POI_NAME_TOO_LONG.getType());
            }

            // 9-门店地址过长
            if (!isClueAddressLengthMatch(excelDTO.getCluePoiAddress())) {
                errorReasonList.add((int) CanteenStallClueParseErrorEnum.POI_ADDRESS_TOO_LONG.getType());
            }

            excelDTO.setErrorReasonList(errorReasonList);
        }
        return excelDTOList;
    }

    /**
     * 校验线索手机号是否有效
     * @param phoneNum 手机号
     * @return true: 有效 / false: 无效
     */
    private Boolean isCluePhoneNumValid(String phoneNum) {
        if (StringUtils.isBlank(phoneNum))  {
            return false;
        }

        List<String> phoneRegexList = MccScConfig.getCanteenStallCluePhoneRegexList();
        for (String phoneNumRegex : phoneRegexList) {
            if (phoneNum.matches(phoneNumRegex)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验线索门店名称是否填写
     * @param clueName 门店名称
     * @return true:已填写 / false:未填写
     */
    private Boolean isClueNameFill(String clueName) {
        return StringUtils.isNotBlank(clueName);
    }

    /**
     * 校验线索门店名称是否符合长度要求
     * @param clueName 门店名称
     * @return true:符合 / false:不符合
     */
    private Boolean isClueNameLengthMatch(String clueName) {
        if (StringUtils.isBlank(clueName)) {
            return true;
        }

        return clueName.length() <= MccScConfig.getCanteenStallClueNameMaxLength();
    }

    /**
     * 校验线索门店地址是否符合长度要求
     * @param clueAddress 门店地址
     * @return true:符合 / false:不符合
     */
    private Boolean isClueAddressLengthMatch(String clueAddress) {
        if (StringUtils.isBlank(clueAddress)) {
            return true;
        }

        return clueAddress.length() <= MccScConfig.getCanteelStallClueAddressMaxLength();
    }

    /**
     * 校验线索门店地址是否填写
     * @param address 线索地址
     * @return true:已填写 / false:未填写
     */
    private Boolean isClueAddressFill(String address) {
        return StringUtils.isNotBlank(address);
    }


    /**
     * 校验二级城市是否解析成功
     * @param excelDTO excelDTO
     * @return true: 解析成功 / false: 解析失败
     */
    private Boolean parseAndSetSecondCityInfo(WmCanteenStallClueExcelDTO excelDTO, Map<String, WmOpenCity> secondCityMap) {
        if (StringUtils.isBlank(excelDTO.getSecondCityName())) {
            excelDTO.setSecondCityName("-");
            excelDTO.setSecondCityId(0);
            return false;
        }

        WmOpenCity wmOpenCity = secondCityMap.get(excelDTO.getSecondCityName());
        if (wmOpenCity == null || wmOpenCity.getCityId() == null || wmOpenCity.getCityId() <= 0) {
            excelDTO.setSecondCityName("-");
            excelDTO.setSecondCityId(0);
            return false;
        }

        excelDTO.setSecondCityId(wmOpenCity.getCityId());
        excelDTO.setSecondCityName(wmOpenCity.getCityName());
        return true;
    }

    /**
     * 校验三级城市是否解析成功
     * @param excelDTO excelDTO
     * @return true: 解析成功 / false: 解析失败
     */
    private Boolean parseAndSetThirdCityInfo(WmCanteenStallClueExcelDTO excelDTO, Map<String, List<WmOpenCity>> thridCityMap) {
        if (StringUtils.isBlank(excelDTO.getThirdCityName()) || excelDTO.getSecondCityId().equals(0)) {
            excelDTO.setThirdCityName("-");
            excelDTO.setThirdCityId(0);
            return false;
        }

        List<WmOpenCity> wmOpenCityList = thridCityMap.get(excelDTO.getThirdCityName());
        if (CollectionUtils.isEmpty(wmOpenCityList)) {
            excelDTO.setThirdCityName("-");
            excelDTO.setThirdCityId(0);
            return false;
        }

        for (WmOpenCity wmOpenCity : wmOpenCityList) {
            if (wmOpenCity.getParentCityId().equals(excelDTO.getSecondCityId())) {
                excelDTO.setThirdCityName(wmOpenCity.getCityName());
                excelDTO.setThirdCityId(wmOpenCity.getCityId());
                return true;
            }
        }

        excelDTO.setThirdCityName("-");
        excelDTO.setThirdCityId(0);
        return false;
    }

    /**
     * 校验门店品类是否解析成功
     * @param excelDTO excelDTO
     * @return true: 解析成功 / false: 解析失败
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private Boolean parseAndSetCluePoiCate(WmCanteenStallClueExcelDTO excelDTO) throws WmSchCantException {
        if (StringUtils.isBlank(excelDTO.getCluePoiCate())) {
            excelDTO.setCluePoiCate("-");
            return false;
        }

        // 根据品类名称查询品类ID
        WmPoiCateDic wmPoiCateDic = wmPoiCategoryCacheServiceAdapter.getWmPoiCateDicByCateName(excelDTO.getCluePoiCate());
        if (wmPoiCateDic == null || wmPoiCateDic.getId() <= 0) {
            excelDTO.setCluePoiCate("-");
            return false;
        }

        excelDTO.setClueLeafCateId(wmPoiCateDic.getId());
        return true;
    }

    /**
     * 校验门店品类是否属于美食/甜点/饮品
     * @param excelDTO excelDTO
     * @return true: 成功 / false: 失败
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private Boolean parseAndSetCluePoiCateTree(WmCanteenStallClueExcelDTO excelDTO) throws WmSchCantException {
        // 1-根据品类名称查询品类ID
        WmPoiCateDic wmPoiCateDic = wmPoiCategoryCacheServiceAdapter.getWmPoiCateDicByCateName(excelDTO.getCluePoiCate());
        if (wmPoiCateDic == null || wmPoiCateDic.getId() <= 0) {
            excelDTO.setCluePoiCate("-");
            return false;
        }

        // 2-根据品类ID查询品类链路
        List<WmPoiCateDic> wmPoiCateDicList = wmPoiCategoryCacheServiceAdapter.getWmPoiCateDicTreeByCateId(wmPoiCateDic.getId());
        excelDTO.setCluePoiCate(WmScTransUtil.transPoiCateDicListToCate(wmPoiCateDicList));

        // 3-校验品类是否为末级品类
        if (wmPoiCateDic.getIs_leaf() == 0) {
            return false;
        }

        return true;
    }


    /**
     * 线索与其他食堂在绑定中或绑定成功校验
     * @param bindDO 档口绑定DO
     * @return 校验String
     */
    public String checkClueBindWithOtherCanteen(WmCanteenStallBindDO bindDO) {
        WmCanteenStallBindDO bindDOByWdc = wmCanteenStallBindMapper.selectByWdcClueIdWithClueBindingOrBindSuccess(bindDO.getWdcClueId());
        if (bindDOByWdc != null && !bindDOByWdc.getCanteenPrimaryId().equals(bindDO.getCanteenPrimaryId())) {
            log.info("[WmCanteenStallCheckService.checkClueBindWithOtherCanteen] clue is bind with other canteen. bindDO = {}, bindDOByWdc = {}",
                    JSONObject.toJSONString(bindDO), JSONObject.toJSONString(bindDOByWdc));
            return CLUE_BIND_OTHER_CANTEEN_CHECK_FAIL_MSG;
        }

        return Strings.EMPTY;
    }

    /**
     * 校验线索所处的蜂窝是否与学校蜂窝一致
     * @param bindDO 档口绑定任务DO
     * @return 校验信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String checkClueAor(WmCanteenStallBindDO bindDO) throws WmSchCantException {
        // 1-查询线索蜂窝ID
        WmPoiHighSeasPoiInfo info = wmHighseasThriftServiceAdapter.getWdcClueInfoByWdcClueId(bindDO.getWdcClueId());
        if (info == null) {
            log.error("[WmCanteenStallCheckService.checkClueAor] WmPoiHighSeasPoiInfo is null. wdcClueId = {}", bindDO.getWdcClueId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索信息查询为空");
        }

        // 2-查询关联的食堂信息
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(bindDO.getCanteenPrimaryId());
        if (wmCanteenDB == null) {
            log.error("[WmCanteenStallCheckService.checkClueAor] wmCanteenDB is null. canteenPrimaryId = {}", bindDO.getCanteenPrimaryId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂信息查询为空");
        }

        // 3-查询关联的学校信息
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());
        if (wmSchoolDB == null) {
            log.error("[WmCanteenStallCheckService.checkClueAor] wmSchoolDB is null. schoolPrimaryId = {}", wmCanteenDB.getSchoolId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校信息查询为空");
        }

        if (wmSchoolDB.getAorId() != info.getAorId()) {
            log.info("[WmCanteenStallCheckService.checkClueAor] clue aor is not same with school. bindDO = {}", JSONObject.toJSONString(bindDO));
            return CLUE_AOR_CHECK_FAIL_MSG;
        }

        return Strings.EMPTY;
    }


    /**
     * 校验线索坐标是否在学校范围内
     * @param bindDO 线索绑定DO
     * @return 校验信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String checkClueCoordinate(WmCanteenStallBindDO bindDO) throws WmSchCantException {
        // 1-查询线索经纬度坐标
        WmPoiHighSeasPoiInfo info = wmHighseasThriftServiceAdapter.getWdcClueInfoByWdcClueId(bindDO.getWdcClueId());
        if (info == null) {
            log.error("[WmCanteenStallCheckService.checkClueCoordinate] WmPoiHighSeasPoiInfo is null. wdcClueId = {}", bindDO.getWdcClueId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索信息查询为空");
        }

        // 2-查询关联的食堂信息
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(bindDO.getCanteenPrimaryId());
        if (wmCanteenDB == null) {
            log.error("[WmCanteenStallCheckService.checkClueCoordinate] wmCanteenDB is null. canteenPrimaryId = {}", bindDO.getCanteenPrimaryId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂信息查询为空");
        }

        // 3-查询学校范围信息
        List<WmScSchoolAreaDO> schoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmCanteenDB.getSchoolId());
        List<String> schoolAreaList = schoolAreaDOList.stream()
                .map(WmScSchoolAreaDO::getArea)
                .collect(Collectors.toList());

        // 4-判断线索经纬度是否在学校范围内
        if (!WmRtreeUtil.withinAreaList((int) info.getLatitude(), (int) info.getLongitude(), schoolAreaList)) {
            log.info("[WmCanteenStallCheckService.checkClueCoordinate] clue coordinate is not within school area. bindDO = {}", JSONObject.toJSONString(bindDO));
            return "该线索扎点不在学校范围内";
        }
        return Strings.EMPTY;
    }


    /**
     * 公海绑定提交前置校验
     * @param submitDTO submitDTO
     * @return List<WmCanteenStallCheckFailDTO>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public List<WmCanteenStallCheckFailDTO> checkSubmitByWdcClueBind(WmCanteenStallBindSubmitDTO submitDTO) throws WmSchCantException, TException {
        log.info("[WmCanteenStallCheckService.checkSubmitByWdcClueBind] input param: submitDTO = {}", JSONObject.toJSONString(submitDTO));
        if (submitDTO == null || submitDTO.getCanteenPrimaryId() == null || CollectionUtils.isEmpty(submitDTO.getWdcClueIdList())) {
            log.error("[WmCanteenStallCheckService.checkSubmitByWdcClueBind] input invalid. submitDTO = {}", JSONObject.toJSONString(submitDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmCanteenDB canteenDB = wmCanteenMapper.selectCanteenById(submitDTO.getCanteenPrimaryId());
        // 1-食堂信息未生效或审批中校验
        String canteenStatusCheckMsg = checkCanteenStatusAuditingOrInsertReject(canteenDB.getId());
        if (StringUtils.isNotBlank(canteenStatusCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, canteenStatusCheckMsg);
        }

        // 2-校验食堂档口数量
        String canteenNumCheckMsg = submitDTO.getWdcClueIdList().size() > 1 ?
                checkCanteenStallNumLimit(submitDTO.getCanteenPrimaryId(), submitDTO.getWdcClueIdList().size()) :
                checkCanteenStallNumLimitByWdcClueId(submitDTO.getCanteenPrimaryId(), submitDTO.getWdcClueIdList().get(0));
        if (StringUtils.isNotBlank(canteenNumCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, canteenNumCheckMsg);
        }

        Map<Long, List<String>> failMap = new HashMap<>();
        List<Long> wdcClueIdList = submitDTO.getWdcClueIdList().stream().distinct().collect(Collectors.toList());
        // 3-提交的线索是否为WDC收录的有效开门的主门店
        List<Long> failIdList1 = checkWdcClueListValid(wdcClueIdList);
        for (Long wdcClueId : failIdList1) {
            failMap.computeIfAbsent(wdcClueId, key -> new ArrayList<>()).add(CLUE_VALID_CHECK_FAIL_MSG);
        }

        // 4-线索是否有与其他食堂的线索绑定状态为"绑定中"/"绑定成功"的档口绑定任务
        List<Long> failIdList2 = checkWdcClueListBindWithOtherCanteen(submitDTO.getCanteenPrimaryId(), wdcClueIdList);
        for (Long wdcClueId : failIdList2) {
            failMap.computeIfAbsent(wdcClueId, key -> new ArrayList<>()).add(CLUE_BIND_OTHER_CANTEEN_CHECK_FAIL_MSG);
        }

        // 5-线索与食堂所属学校的蜂窝是否一致
        List<Long> failIdList4 = checkWdcClueListAor(wdcClueIdList, submitDTO.getCanteenPrimaryId());
        for (Long wdcClueId : failIdList4) {
            failMap.computeIfAbsent(wdcClueId, key -> new ArrayList<>()).add(CLUE_AOR_CHECK_FAIL_MSG);
        }

        // 6-线索是否在学校范围内
        List<Long> failIdList5 = checkWdcClueListCoordinate(wdcClueIdList, submitDTO.getCanteenPrimaryId());
        for (Long wdcClueId : failIdList5) {
            failMap.computeIfAbsent(wdcClueId, key -> new ArrayList<>()).add(CLUE_COORDINATE_CHECK_FAIL_MSG);
        }

        List<WmCanteenStallCheckFailDTO> checkFailDTOList = getCheckFailDTOListByMap(failMap);
        log.info("[WmCanteenStallCheckService.checkSubmitByWdcClueBind] checkFailDTOList = {}", JSONObject.toJSONString(checkFailDTOList));
        return checkFailDTOList;
    }

    /**
     * 外卖门店绑定提交前置校验
     * @param submitDTO submitDTO
     * @return List<WmCanteenStallCheckFailDTO>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public List<WmCanteenStallCheckFailDTO> checkSubmitByWmPoiBind(WmCanteenStallBindSubmitDTO submitDTO) throws WmSchCantException, TException {
        log.info("[WmCanteenStallCheckService.checkSubmitByWmPoiBind] input param: submitDTO = {}", JSONObject.toJSONString(submitDTO));
        if (submitDTO == null || submitDTO.getCanteenPrimaryId() == null || CollectionUtils.isEmpty(submitDTO.getWmPoiIdList())) {
            log.error("[WmCanteenStallCheckService.checkSubmitByWdcClueBind] input invalid. submitDTO = {}", JSONObject.toJSONString(submitDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmCanteenDB canteenDB = wmCanteenMapper.selectCanteenById(submitDTO.getCanteenPrimaryId());
        // 1-食堂信息未生效或审批中校验
        String canteenStatusCheckMsg = checkCanteenStatusAuditingOrInsertReject(canteenDB.getId());
        if (StringUtils.isNotBlank(canteenStatusCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, canteenStatusCheckMsg);
        }

        // 2-校验食堂档口数量
        String canteenNumCheckMsg = submitDTO.getWmPoiIdList().size() > 1 ?
                checkCanteenStallNumLimit(submitDTO.getCanteenPrimaryId(), submitDTO.getWmPoiIdList().size()) :
                checkCanteenStallNumLimitByWmPoiId(submitDTO.getCanteenPrimaryId(), submitDTO.getWmPoiIdList().get(0));
        if (StringUtils.isNotBlank(canteenNumCheckMsg)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, canteenNumCheckMsg);
        }

        Map<Long, List<String>> failMap = new HashMap<>();
        List<Long> wmPoiIdList = submitDTO.getWmPoiIdList().stream().distinct().collect(Collectors.toList());
        // 3-外卖门店ID是否存在
        List<Long> failWmPoiIdList1 = checkWmPoiListValid(wmPoiIdList);
        for (Long wmPoiId : failWmPoiIdList1) {
            failMap.computeIfAbsent(wmPoiId, key -> new ArrayList<>()).add(WM_POI_VALID_CHECK_FAIL_MSG);
        }

        // 4-外卖门店是否有与其他食堂的绑定中或已绑定的档口绑定任务
        List<Long> failWmPoiIdList2 = checkWmPoiListBindWithOtherCanteen(submitDTO.getCanteenPrimaryId(), wmPoiIdList);
        for (Long wmPoiId : failWmPoiIdList2) {
            failMap.computeIfAbsent(wmPoiId, key -> new ArrayList<>()).add(WM_POI_BIND_OTHER_CANTEEN_CHECK_FAIL_MSG);
        }
        // 4-2 不能有正在绑定或者解绑中的任务
        List<Long> failWmPoiIdList3 = checkWmPoiListBind(wmPoiIdList);
        for (Long wmPoiId : failWmPoiIdList3) {
            failMap.computeIfAbsent(wmPoiId, key -> new ArrayList<>()).add(WM_POI_REBINDING_UNBIND);
        }

        // 5-外卖门店关联的线索是否有与其他食堂的绑定中或已绑定的档口绑定任务
        Map<Long, String> failWmPoiIdMap = checkWdcClueListBindWithOtherCanteenByWmPoiIdList(submitDTO.getCanteenPrimaryId(), wmPoiIdList);
        for (Map.Entry<Long, String> entry : failWmPoiIdMap.entrySet()) {
            failMap.computeIfAbsent(entry.getKey(), key -> new ArrayList<>()).add(entry.getValue());
        }

        // 6-外卖门店所处蜂窝是否与食堂关联学校蜂窝一致
        List<Long> failWmPoiIdList5 = checkWmPoiListAor(wmPoiIdList, submitDTO.getCanteenPrimaryId());
        for (Long wmPoiId : failWmPoiIdList5) {
            failMap.computeIfAbsent(wmPoiId, key -> new ArrayList<>()).add(WM_POI_AOR_CHECK_FAIL_MSG);
        }

        // 7-外卖门店坐标是否在学校范围内
        List<Long> failWmPoiIdList6 = checkWmPoiListCoordinate(wmPoiIdList, submitDTO.getCanteenPrimaryId());
        for (Long wmPoiId : failWmPoiIdList6) {
            failMap.computeIfAbsent(wmPoiId, key -> new ArrayList<>()).add(WM_POI_COORDINATE_CHECK_FAIL_MSG);
        }

        List<WmCanteenStallCheckFailDTO> checkFailDTOList = getCheckFailDTOListByMap(failMap);
        log.info("[WmCanteenStallCheckService.checkSubmitByWmPoiBind] checkFailDTOList = {}", JSONObject.toJSONString(checkFailDTOList));
        return checkFailDTOList;
    }


    public List<WmCanteenStallCheckFailDTO> getCheckFailDTOListByMap(Map<Long, List<String>> failMap) {
        List<WmCanteenStallCheckFailDTO> resList = new ArrayList<>();
        if (failMap == null || failMap.isEmpty()) {
            return resList;
        }

        for (Map.Entry<Long, List<String>> entry : failMap.entrySet()) {
            WmCanteenStallCheckFailDTO failDTO = new WmCanteenStallCheckFailDTO();
            failDTO.setId(String.valueOf(entry.getKey()));
            failDTO.setFailReason(entry.getValue());
            resList.add(failDTO);
        }
        return resList;
    }


    /**
     * 外卖门店坐标是否在学校范围内
     * @param wmPoiIdList 外卖门店ID列表
     * @param canteenPrimaryId 食堂主键ID
     * @return 检验不通过的门店ID列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> checkWmPoiListCoordinate(List<Long> wmPoiIdList, Integer canteenPrimaryId) throws WmSchCantException {
        List<Long> failWmPoiIdList = new ArrayList<>();
        // 1-查询门店经纬度信息
        Map<Long, WmPoiAggre> resMap = wmPoiQueryAdapter.getWmPoiAggreMap(wmPoiIdList,
                Sets.newHashSet(
                        WM_POI_FIELD_WM_POI_ID,
                        WM_POI_FIELD_LATITUDE,
                        WM_POI_FIELD_LONGITUDE
                )
        );
        if (resMap == null || resMap.isEmpty()) {
            return new ArrayList<>();
        }

        // 2-查询学校范围信息
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        List<WmScSchoolAreaDO> schoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmCanteenDB.getSchoolId());
        List<String> schoolAreaList = schoolAreaDOList.stream()
                .map(WmScSchoolAreaDO::getArea)
                .collect(Collectors.toList());

        // 3-判断线索是否在学校范围内
        for (Map.Entry<Long, WmPoiAggre> entry : resMap.entrySet()) {
            if (!WmRtreeUtil.withinAreaList((int) entry.getValue().getLatitude(), (int) entry.getValue().getLongitude(), schoolAreaList)) {
                failWmPoiIdList.add(entry.getKey());
            }
        }

        log.info("[WmCanteenStallCheckService.checkWmPoiListCoordinate] failWmPoiIdList = {}", JSONObject.toJSONString(failWmPoiIdList));
        return failWmPoiIdList;
    }


    /**
     * 外卖门店所处蜂窝是否与食堂关联学校蜂窝一致
     * @param wmPoiIdList 外卖门店ID
     * @param canteenPrimaryId 食堂主键ID
     * @return 检验不通过的门店ID列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> checkWmPoiListAor(List<Long> wmPoiIdList, Integer canteenPrimaryId) throws WmSchCantException {
        List<Long> failWmPoiIdList = new ArrayList<>();
        Map<Long, WmPoiAggre> resMap = wmPoiQueryAdapter.getWmPoiAggreMap(wmPoiIdList,
                Sets.newHashSet(
                        WM_POI_FIELD_WM_POI_ID,
                        WM_POI_FIELD_LATITUDE,
                        WM_POI_FIELD_LONGITUDE,
                        WM_POI_FIELD_AGENT_ID
                )
        );
        if (resMap == null || resMap.isEmpty()) {
            return new ArrayList<>();
        }

        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());

        for (Map.Entry<Long, WmPoiAggre> entry : resMap.entrySet()) {
            WmPoiAggre wmPoiAggre = entry.getValue();
            // 判断蜂窝类型为直营/代理商
            Integer uniAorType = wmPoiAggre.getAgent_id() > 0 ? WmUniAorType.DLS.code() : WmUniAorType.ZY.code();

            // 根据门店经纬度查询门店蜂窝ID
            AorDto aorDto = wmGisUniAorServiceAdapter.getAorByLatLngAndAorType(uniAorType, wmPoiAggre.getLatitude(), wmPoiAggre.getLongitude());
            if (aorDto == null || !aorDto.getAorId().equals(wmSchoolDB.getAorId())) {
                failWmPoiIdList.add(entry.getKey());
            }
        }

        log.info("[WmCanteenStallCheckService.checkWmPoiListAor] failWmPoiIdList = {}", JSONObject.toJSONString(failWmPoiIdList));
        return failWmPoiIdList;
    }

    /**
     * 校验门店ID是否存在
     * @param wmPoiIdList 门店ID列表
     * @return 校验不通过的门店ID列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> checkWmPoiListValid(List<Long> wmPoiIdList) throws WmSchCantException {
        List<Long> failWmPoiIdList = new ArrayList<>();
        Map<Long, WmPoiAggre> resMap = wmPoiQueryAdapter.getWmPoiAggreMap(wmPoiIdList,
                Sets.newHashSet(
                        WM_POI_FIELD_WM_POI_ID,
                        WM_POI_FIELD_NAME,
                        WM_POI_FIELD_VALID,
                        WM_POI_FIELD_IS_DELETE
                )
        );
        if (resMap == null || resMap.isEmpty()) {
            return wmPoiIdList;
        }

        for (Long wmPoiId : wmPoiIdList) {
            if (resMap.get(wmPoiId) == null || resMap.get(wmPoiId).getIs_delete() == WM_POI_ID_IS_DELETE) {
                failWmPoiIdList.add(wmPoiId);
            }
        }
        failWmPoiIdList = failWmPoiIdList.stream().distinct().collect(Collectors.toList());
        log.info("[WmCanteenStallCheckService.checkWmPoiListValid] failWmPoiIdList = {}", JSONObject.toJSONString(failWmPoiIdList));
        return failWmPoiIdList;
    }

    /**
     * 校验线索是否为WDC收录为有效开门的主门店
     * @param wdcClueIdList 线索ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> checkWdcClueListValid(List<Long> wdcClueIdList) throws WmSchCantException {
        Map<Long, WmPoiHighSeasPoiInfo> resultMap = wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList);
        log.info("[WmCanteenStallCheckService.checkWdcClueValid] resultMap = {}", JSONObject.toJSONString(resultMap));
        if (resultMap == null || resultMap.isEmpty()) {
            return wdcClueIdList;
        }

        List<Long> failClueIdList = new ArrayList<>();
        for (Long wdcClueId : wdcClueIdList) {
            if (resultMap.get(wdcClueId) == null) {
                failClueIdList.add(wdcClueId);
            }
        }

        failClueIdList = failClueIdList.stream().distinct().collect(Collectors.toList());
        log.info("[WmCanteenStallCheckService.checkWdcClueValid] failClueIdList = {}", JSONObject.toJSONString(failClueIdList));
        return failClueIdList;
    }


    /**
     * 线索与其他食堂的线索绑定状态为"绑定中"/"绑定成功"的档口绑定任务
     * @param canteenPrimaryId 食堂主键ID
     * @param wdcClueIdList 线索ID列表
     * @return 校验不通过的线索ID
     */
    public List<Long> checkWdcClueListBindWithOtherCanteen(Integer canteenPrimaryId, List<Long> wdcClueIdList) {
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindMapper.selectByWdcClueIdList(wdcClueIdList);
        log.info("[WmCanteenStallCheckService.checkWdcClueListBindWithOtherCanteen] bindDOList = {}", JSONObject.toJSONString(bindDOList));

        List<Long> failIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(bindDOList)) {
            return failIdList;
        }

        for (WmCanteenStallBindDO bindDO : bindDOList) {
            // 线索绑定状态为"绑定中"或"绑定成功"
            if (bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BINDING.getType())
                    || bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType())) {
                if (!bindDO.getCanteenPrimaryId().equals(canteenPrimaryId)) {
                    failIdList.add(bindDO.getWdcClueId());
                }
            }
        }

        failIdList = failIdList.stream().distinct().collect(Collectors.toList());
        log.info("[WmCanteenStallCheckService.checkWdcClueListBindWithOtherCanteen] failIdList = {}", JSONObject.toJSONString(failIdList));
        return failIdList;
    }

    /**
     * 外卖门店是否有与其他食堂的绑定中或已绑定的档口绑定任务
     * @param canteenPrimaryId 食堂主键ID
     * @param wmPoiIdList 外卖门店ID列表
     * @return 校验不通过的外卖门店ID
     */
    public List<Long> checkWmPoiListBindWithOtherCanteen(Integer canteenPrimaryId, List<Long> wmPoiIdList) {
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindMapper.selectByWmPoiIdList(wmPoiIdList);
        log.info("[WmCanteenStallCheckService.checkWmPoiListBindWithOtherCanteen] bindDOList = {}", JSONObject.toJSONString(bindDOList));
        if (CollectionUtils.isEmpty(bindDOList)) {
            return new ArrayList<>();
        }

        List<Long> failIdList = new ArrayList<>();
        for (WmCanteenStallBindDO bindDO : bindDOList) {
            // 线索绑定状态为"绑定中"或"绑定成功" || 外卖门店绑定状态为"绑定成功"
            if (bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BINDING.getType())
                    || bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType())
                    || bindDO.getWmPoiBindStatus().equals((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType())) {
                if (!bindDO.getCanteenPrimaryId().equals(canteenPrimaryId)) {
                    failIdList.add(bindDO.getWmPoiId());
                }
            }
        }

        failIdList = failIdList.stream().distinct().collect(Collectors.toList());
        log.info("[WmCanteenStallCheckService.checkWmPoiListBindWithOtherCanteen] failIdList = {}", JSONObject.toJSONString(failIdList));
        return failIdList;
    }

    /**
     * 检查外卖门店是否处于换绑中或者解绑中
     *
     * @param wmPoiIdList 外卖门店ID列表
     * @return 校验不通过的外卖门店ID列表
     */
    public List<Long> checkWmPoiListBind(List<Long> wmPoiIdList) {
        if(wmPoiIdList == null || wmPoiIdList.size() == 0){
            return new ArrayList<>();
        }
        // 查询给定ID列表中处于换绑或解绑状态的门店数据对象
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindMapper.selectByWmPoiIdWithRebind(wmPoiIdList);
        log.info("[WmCanteenStallCheckService.checkWmPoiListBind] bindDOList = {}", JSONObject.toJSONString(bindDOList));

        // 判断查询结果是否为空并返回相应结果
        if (CollectionUtils.isEmpty(bindDOList)) {
            return new ArrayList<>(); // 返回空列表，表示所有门店均通过校验
        } else {
            // 返回不通过校验的门店ID列表
            return bindDOList.stream()
                    .map(WmCanteenStallBindDO::getWmPoiId)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 外卖门店关联的线索是否有与其他食堂的绑定中或已绑定的档口绑定任务
     * @param canteenPrimaryId 食堂主键ID
     * @param wmPoiIdList 外卖门店ID列表
     * @return key->wmPoiId val->msg
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<Long, String> checkWdcClueListBindWithOtherCanteenByWmPoiIdList(Integer canteenPrimaryId, List<Long> wmPoiIdList)
            throws WmSchCantException {
        // key->wmPoiId val->wdcClueId
        Map<Long, Long> wdcClueMap = wdcRelationServiceAdapter.getWdcClueIdMapByWmPoiIdList(wmPoiIdList);
        if (wdcClueMap == null || wdcClueMap.isEmpty()) {
            return new HashMap<>();
        }

        List<Long> wdcClueIdList = new ArrayList<>();
        // key->wdcClueId val->wmPoiId
        Map<Long, Long> wmPoiMap = new HashMap<>();
        for (Map.Entry<Long, Long> entry : wdcClueMap.entrySet()) {
            wdcClueIdList.add(entry.getValue());
            wmPoiMap.put(entry.getValue(), entry.getKey());
        }

        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindMapper.selectByWdcClueIdList(wdcClueIdList);
        log.info("[WmCanteenStallCheckService.checkWdcClueListBindWithOtherCanteen] bindDOList = {}", JSONObject.toJSONString(bindDOList));

        Map<Long, String> resultMap = new HashMap<>();
        for (WmCanteenStallBindDO bindDO : bindDOList) {
            // 线索绑定状态为"绑定中"或"绑定成功" || 外卖门店绑定状态为"绑定成功"
            if (bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BINDING.getType())
                    || bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType())
                    || bindDO.getWmPoiBindStatus().equals((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType())) {
                if (!bindDO.getCanteenPrimaryId().equals(canteenPrimaryId)) {
                    resultMap.put(wmPoiMap.get(bindDO.getWdcClueId()), "该外卖门店关联的线索（ID：" + bindDO.getWdcClueId() + "）有与其他食堂的绑定中或已绑定的档口绑定任务");
                }
            }
        }

        log.info("[WmCanteenStallCheckService.checkWdcClueListByWmPoiIdBindWithOtherCanteen] resultMap = {}", JSONObject.toJSONString(resultMap));
        return resultMap;
    }

    /**
     * 线索与食堂所属学校的蜂窝是否一致
     * @param canteenPrimaryId 食堂主键ID
     * @param wdcClueIdList 线索ID列表
     * @return 校验不通过的线索ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> checkWdcClueListAor(List<Long> wdcClueIdList, Integer canteenPrimaryId) throws WmSchCantException {
        Map<Long, WmPoiHighSeasPoiInfo> resultMap = wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList);
        List<Long> failIdList = new ArrayList<>();
        if (resultMap == null || resultMap.isEmpty()) {
            return failIdList;
        }

        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());

        for (Map.Entry<Long, WmPoiHighSeasPoiInfo> entry : resultMap.entrySet()) {
            if (entry.getValue().getAorId() != wmSchoolDB.getAorId()) {
                failIdList.add(entry.getKey());
            }
        }

        log.info("[WmCanteenStallCheckService.checkWdcClueListAor] failIdList = {}", JSONObject.toJSONString(failIdList));
        return failIdList;
    }


    /**
     * 线索是否在学校范围内
     * @param canteenPrimaryId 食堂主键ID
     * @param wdcClueIdList 线索ID列表
     * @return 校验不通过的线索ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> checkWdcClueListCoordinate(List<Long> wdcClueIdList, Integer canteenPrimaryId) throws WmSchCantException {
        // 1-查询线索信息
        Map<Long, WmPoiHighSeasPoiInfo> resultMap = wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList);
        List<Long> failIdList = new ArrayList<>();
        if (resultMap == null || resultMap.isEmpty()) {
            return failIdList;
        }

        // 2-查询学校范围信息
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        List<WmScSchoolAreaDO> schoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmCanteenDB.getSchoolId());
        List<String> schoolAreaList = schoolAreaDOList.stream()
                .map(WmScSchoolAreaDO::getArea)
                .collect(Collectors.toList());

        // 3-判断线索是否在学校范围内
        for (Map.Entry<Long, WmPoiHighSeasPoiInfo> entry : resultMap.entrySet()) {
            if (!WmRtreeUtil.withinAreaList((int) entry.getValue().getLatitude(), (int) entry.getValue().getLongitude(), schoolAreaList)) {
                failIdList.add(entry.getKey());
            }
        }

        log.info("[WmCanteenStallCheckService.checkWdcClueListCoordinate] failIdList = {}", JSONObject.toJSONString(failIdList));
        return failIdList;
    }

}