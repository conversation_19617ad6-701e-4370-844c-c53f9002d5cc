package com.sankuai.meituan.waimai.customer.settle.constant;

import com.google.common.collect.Maps;
import com.sankuai.conch.certify.tokenaccess.enums.CertificateType;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SettleEncryptConstant {

    public static final String TYPE_PHONE = "phoneNo";
    public static final String TYPE_IDENTIFY = "identifyId";
    public static final String TYPE_BANKCARDNO = "bankCardNo";

    public static final Map<Integer, CertificateType> CERTIFICATE_TYPE_MAP = Maps.newHashMap();
    static{
        CERTIFICATE_TYPE_MAP.put(1001,CertificateType.IDENTITY_CARD);
        CERTIFICATE_TYPE_MAP.put(1002,CertificateType.RESIDENCE_BOOKLET);
        CERTIFICATE_TYPE_MAP.put(1003,CertificateType.CHINESE_PASSPORT);
        CERTIFICATE_TYPE_MAP.put(1004,null);
        CERTIFICATE_TYPE_MAP.put(1005,CertificateType.MAINLAND_TRAVEL_PERMIT_FOR_HONG_KONG_AND_MACAO_RESIDENTS);
        CERTIFICATE_TYPE_MAP.put(1006,CertificateType.MAINLAND_TRAVEL_PERMIT_FOR_TAIWAN_RESIDENTS);
        CERTIFICATE_TYPE_MAP.put(1007,CertificateType.PERMANENT_RESIDENCE_CARD_FOR_FOREIGNER);
        CERTIFICATE_TYPE_MAP.put(1008,CertificateType.FOREIGN_PASSPORT);
        CERTIFICATE_TYPE_MAP.put(1009,CertificateType.RESIDENCE_PERMIT_FOR_HONG_KONG_AND_MACAO_RESIDENTS);
        CERTIFICATE_TYPE_MAP.put(1010,CertificateType.RESIDENCE_PERMIT_FOR_TAIWAN_RESIDENTS);
        CERTIFICATE_TYPE_MAP.put(9999,null);
        CERTIFICATE_TYPE_MAP.put(2001,CertificateType.BUSINESS_LICENSE);
        CERTIFICATE_TYPE_MAP.put(2002,CertificateType.CERTIFICATE_OF_PUBLIC_INSTITUTION_WITH_LEGAL_PERSON_STATUS);
        CERTIFICATE_TYPE_MAP.put(2003,CertificateType.CERTIFICATE_OF_REGISTRATION_FOR_PRIVATE_NONENTERPRISE_ENTITIES);
        CERTIFICATE_TYPE_MAP.put(2004,CertificateType.SOCIAL_GROUP_CERTIFICATION);
        CERTIFICATE_TYPE_MAP.put(2005,CertificateType.MILITARY_UNIT_EXTERNAL_PAID_SERVICE_PERMIT);
        CERTIFICATE_TYPE_MAP.put(2006,CertificateType.ARMED_POLICE_BORDER_GUARDS_EXTERNAL_PAID_SERVICE_PERMIT);
        CERTIFICATE_TYPE_MAP.put(2007,CertificateType.OVERSEAS_BUSINESS_QUALIFICATION);
        CERTIFICATE_TYPE_MAP.put(2008,CertificateType.ARMY_CODE);
        CERTIFICATE_TYPE_MAP.put(2009,CertificateType.REGISTRATION_CERTIFICATE_FOR_THE_FOUNDATION_LEGAL_PERSON);
        CERTIFICATE_TYPE_MAP.put(2010,CertificateType.PRACTICE_LICENSE_OF_LAW_FIRM);
        CERTIFICATE_TYPE_MAP.put(2011,CertificateType.REGISTRATION_CERTIFICATE_FOR_FOREIGN_ENTERPRISES);
        CERTIFICATE_TYPE_MAP.put(2012,CertificateType.FORENSIC_LICENSE);
        CERTIFICATE_TYPE_MAP.put(2013,CertificateType.REGISTRATION_CERTIFICATE_FOR_FOREIGN_INSTITUTIONS);
        CERTIFICATE_TYPE_MAP.put(2014,CertificateType.REGISTRATION_CERTIFICATE_FOR_FOREIGN_CULTURE_CENTER);
        CERTIFICATE_TYPE_MAP.put(2015,CertificateType.REGISTRATION_CERTIFICATE_FOR_FOREIGN_GOVERMENT_TOURISM_DEPARTMENT);
        CERTIFICATE_TYPE_MAP.put(2016,CertificateType.REGISTRATION_CERTIFICATE_FOR_RELIGIOUS_ACTIVITIES);
        CERTIFICATE_TYPE_MAP.put(2017,CertificateType.FOOD_BUSINESS_LICENSE);
        CERTIFICATE_TYPE_MAP.put(2018,CertificateType.GOVERNMENT_AGENCY_UNIFIED_CREDIT_CODE);
        CERTIFICATE_TYPE_MAP.put(2019,CertificateType.REGISTRATION_CERTIFICATE_FOR_HONG_KONG_AND_MACAO_AND_TAIWAN_TOURISM_DEPARTMENT);
        CERTIFICATE_TYPE_MAP.put(2020,CertificateType.TEMPORARY_BUSINESS_LICENSE);
    }
}
