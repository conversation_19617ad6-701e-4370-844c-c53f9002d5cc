package com.sankuai.meituan.waimai.customer.service.sc.flow.constant;

/**
 * 食堂档口线索绑定状态机事件
 * <AUTHOR>
 * @date 2024/05/28
 * @email <EMAIL>
 */
public enum WmCanteenStallClueBindStatusMachineEvent {
    /**
     * 线索绑定成功
     */
    CLUE_BIND_SUCCESS(1, "线索绑定成功"),
    /**
     * 线索绑定失败
     */
    CLUE_BIND_FAIL(2, "线索绑定失败"),
    /**
     * 线索解绑
     */
    CLUE_UNBIND(3, "线索解绑");


    private final Integer code;
    private final String desc;

    WmCanteenStallClueBindStatusMachineEvent(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
