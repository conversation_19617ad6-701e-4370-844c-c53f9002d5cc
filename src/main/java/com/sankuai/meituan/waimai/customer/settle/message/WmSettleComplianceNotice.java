package com.sankuai.meituan.waimai.customer.settle.message;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiComplianceModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.compliance.WmComplianceThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;

@Service
public class WmSettleComplianceNotice {

    private static Logger logger = LoggerFactory.getLogger(WmSettleComplianceNotice.class);

    @Autowired
    WmComplianceThriftService wmComplianceThriftService;

    /**
     * 对非开通钱包的结算进行支付合规，开通钱包的结算需要等待开通钱包后再进行支付合规
     *
     * @param customerId
     * @param newWmSettleList
     * @param opUid
     * @param opUname
     */
    public void noticeBankSettleCompliance(int customerId, List<WmSettle> newWmSettleList, int opUid, String opUname) {
        logger.info("进行银行卡支付合规 customerId:{} opuId:{} opUname:{}", customerId, opUid, opUname);
        if (CollectionUtils.isEmpty(newWmSettleList)) {
            logger.info("无银行卡结算，不需要同步");
            return;
        }
        List<Long> wmSettlePoiIdList = Lists.newArrayList();
        for (WmSettle auditedSettle : newWmSettleList) {
            if (CollectionUtils.isEmpty(auditedSettle.getWmPoiIdList())) {
                continue;
            }
            wmSettlePoiIdList.addAll(Lists.transform(auditedSettle.getWmPoiIdList(), new Function<Integer, Long>() {
                @Nullable
                @Override
                public Long apply(@Nullable Integer input) {
                    return Long.valueOf(input);
                }
            }));
        }
        notice(customerId, opUid, opUname, wmSettlePoiIdList);
    }

    /**
     * 对非开通钱包的结算进行支付合规，开通钱包的结算需要等待开通钱包后再进行支付合规
     *
     * @param customerId
     * @param newWmSettleList
     * @param opUid
     * @param opUname
     */
    public void noticeWalletComplianceForNoWalletIdChange(int customerId, List<WmSettleAudited> newWmSettleList, int opUid, String opUname) {
        logger.info("进行钱包支付合规 customerId:{} opuId:{} opUname:{}", customerId, opUid, opUname);
        if (CollectionUtils.isEmpty(newWmSettleList)) {
            logger.info("无钱包结算，不需要同步");
            return;
        }
        List<Long> wmSettlePoiIdList = Lists.newArrayList();
        for (WmSettleAudited auditedSettle : newWmSettleList) {
            if (CollectionUtils.isEmpty(auditedSettle.getWmPoiIdList())) {
                continue;
            }
            wmSettlePoiIdList.addAll(Lists.transform(auditedSettle.getWmPoiIdList(), new Function<Integer, Long>() {
                @Nullable
                @Override
                public Long apply(@Nullable Integer input) {
                    return Long.valueOf(input);
                }
            }));
        }
        notice(customerId, opUid, opUname, wmSettlePoiIdList);
    }

    private void notice(int customerId, int opUid, String opUname, List<Long> wmSettlePoiIdList) {
        if (CollectionUtils.isEmpty(wmSettlePoiIdList)) {
            logger.info("结算无关联门店，不需要同步");
            return;
        }
        boolean success = false;
        int time = 0;
        while (time++ <= 3) {
            try {
                wmComplianceThriftService.noticePoiListModuleEffective(wmSettlePoiIdList, WmPoiComplianceModuleEnum.settle, opUid, opUname);
                success = true;
                break;
            } catch (WmPoiBizException e) {
                logger.warn("进行支付合规失败", e);
            } catch (Exception e) {
                logger.error("进行支付合规失败", e);
            }
        }
        if (!success) {
            logger.error("进行支付合规失败 customerId:{}", customerId);
        }
    }

}
