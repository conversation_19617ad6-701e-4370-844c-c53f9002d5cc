package com.sankuai.meituan.waimai.customer.dao.sc.canteenstall;

import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindQueryBO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 食堂档口绑定任务Mapper
 * <AUTHOR>
 * @date 2024/05/13
 * @email <EMAIL>
 **/
@Component
public interface WmCanteenStallBindMapper {

    /**
     * 根据主键ID查询食堂档口绑定任务信息
     * @param id 主键ID
     * @return 食堂档口绑定任务信息
     */
    WmCanteenStallBindDO selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 根据主键ID列表查询食堂档口绑定任务信息
     * @param bindIdList 主键ID列表
     * @return 食堂档口绑定任务信息
     */
    List<WmCanteenStallBindDO> selectByBindIdList(@Param("bindIdList") List<Integer> bindIdList);

    /**
     * 更新档口绑定任务审批状态
     * @param auditStatus 审批状态
     * @param bindIdList 主键ID列表
     */
    void updateAuditStatusByPrimaryIdList(@Param("auditStatus") Integer auditStatus,
                                          @Param("bindIdList") List<Integer> bindIdList);

    /**
     * 更新档口绑定任务线索跟进状态
     * @param clueFollowUpStatus 线索跟进状态
     * @param bindIdList 主键ID列表
     */
    void updateClueFollowUpStatusByPrimaryIdList(@Param("clueFollowUpStatus") Integer clueFollowUpStatus,
                                                 @Param("bindIdList") List<Integer> bindIdList);

    /**
     * 根据线索ID查询关联的档口管理任务ID列表
     * @param wdcClueId 线索ID
     * @return 档口绑定任务ID列表
     */
    List<WmCanteenStallBindDO> selectByWdcCludId(@Param("wdcClueId") Long wdcClueId);

    /**
     * 根据线索ID查询线索生成状态=生成中的档口绑定任务
     * @param wdcClueId 线索ID
     * @return 档口绑定任务
     */
    WmCanteenStallBindDO selectByWdcClueIdWithClueGenerating(@Param("wdcClueId") Long wdcClueId);

    /**
     * 根据查询条件查询食堂档口绑定任务总数
     * @param bindQueryBO WmCanteenStallBindQueryBO
     * @return 档口绑定任务总数
     */
    int selectCountBySearchBO(WmCanteenStallBindQueryBO bindQueryBO);

    /**
     * 根据查询条件查询食堂档口绑定任务列表
     * @param bindQueryBO WmCanteenStallBindQueryBO
     * @return 档口绑定任务列表
     */
    List<WmCanteenStallBindDO> selectBindListBySearchBO(WmCanteenStallBindQueryBO bindQueryBO);


    /**
     * 根据门店ID查询关联的档口管理任务ID列表
     * @param wmPoiId 门店ID
     * @return 档口绑定任务ID列表
     */
    List<WmCanteenStallBindDO> selectByWmPoiId(@Param("wmPoiId") Long wmPoiId);

    /**
     * 根据食堂主键ID查询食堂档口绑定任务信息列表
     * @param canteenPrimaryId 食堂主键ID
     * @return 食堂档口绑定任务信息列表
     */
    List<WmCanteenStallBindDO> selectByCanteenPrimaryId(@Param("canteenPrimaryId") Integer canteenPrimaryId);

    /**
     * 根据食堂主键ID查询线索绑定状态处于"绑定成功"或"绑定中"的档口绑定任务信息列表
     * @param canteenPrimaryId 食堂主键ID
     * @return 食堂档口绑定任务信息列表
     */
    List<WmCanteenStallBindDO> selectByCanteenPrimaryIdWithClueBindingOrBindSuccess(@Param("canteenPrimaryId") Integer canteenPrimaryId);

    /**
     * 根据食堂主键ID列表查询线索绑定状态处于"绑定成功"或"绑定中"的档口绑定任务信息列表
     * @param canteenPrimaryIdList 食堂主键ID列表
     * @return 食堂档口绑定任务信息列表
     */
    List<WmCanteenStallBindDO> selectByCanteenPrimaryIdListWithClueBindingOrBindSuccess(@Param("canteenPrimaryIdList") List<Integer> canteenPrimaryIdList);

    /**
     * 根据食堂主键ID查询外卖门店绑定状态处于"绑定成功"的档口绑定任务信息列表
     * @param canteenPrimaryId 食堂主键ID
     * @return 食堂档口绑定任务信息列表
     */
    List<WmCanteenStallBindDO> selectByCanteenPrimaryIdWithWmPoiBindSuccess(@Param("canteenPrimaryId") Integer canteenPrimaryId);

    /**
     * 根据食堂主键ID查询外卖门店绑定状态处于（换绑中、绑定成功、解绑中）的档口绑定任务信息列表
     * @param canteenPrimaryId 食堂主键ID
     * @return 食堂档口绑定任务信息列表
     */
    List<WmCanteenStallBindDO> selectByCanteenPrimaryIdWithWmPoiBindingStatus(@Param("canteenPrimaryId") Integer canteenPrimaryId);

    /**
     * 根据食堂主键ID列表查询外卖门店绑定状态处于"绑定成功"的档口绑定任务信息列表
     * @param canteenPrimaryIdList 食堂主键ID列表
     * @return 食堂档口绑定任务信息列表
     */
    List<WmCanteenStallBindDO> selectByCanteenPrimaryIdListWithWmPoiBindSuccess(@Param("canteenPrimaryIdList") List<Integer> canteenPrimaryIdList);

    /**
     * 根据线索ID查询线索绑定状态处于"绑定成功"或"绑定中"的档口绑定任务信息
     * @param wdcClueId 线索ID
     * @return 档口绑定任务信息
     */
    WmCanteenStallBindDO selectByWdcClueIdWithClueBindingOrBindSuccess(@Param("wdcClueId") Long wdcClueId);

    /**
     * 根据线索ID列表查询线索绑定状态处于"绑定成功"或"绑定中"的档口绑定任务信息
     * @param wdcClueIdList 线索ID列表
     * @return 档口绑定任务信息
     */
    List<WmCanteenStallBindDO> selectByWdcClueIdList(@Param("wdcClueIdList") List<Long> wdcClueIdList);

    /**
     * 根据外卖门店ID列表查询档口绑定任务信息
     * @param wmPoiIdList 外卖门店ID列表
     * @return 档口绑定任务信息
     */
    List<WmCanteenStallBindDO> selectByWmPoiIdList(@Param("wmPoiIdList") List<Long> wmPoiIdList);

    /**
     * 根据线索ID查询线索绑定状态处于"绑定成功"的档口绑定任务信息
     * @param wdcClueId 线索ID
     * @return 档口绑定任务信息
     */
    WmCanteenStallBindDO selectByWdcClueIdWithClueBindSuccess(@Param("wdcClueId") Long wdcClueId);


    /**
     * 根据线索ID和指定的线索绑定状态列表查询档口绑定任务信息
     * @param canteenPrimaryId 食堂主键ID
     * @param statusLis 状态
     * @return 档口绑定任务信息列表
     */
    List<WmCanteenStallBindDO> selectByCanteenPrimaryIdWithSpecificBindStatus(@Param("canteenPrimaryId") Integer canteenPrimaryId,
                                                                           @Param("statusList") List<Integer> statusLis);

    /**
     * 根据外卖门店id找出处于换绑或者解绑中的门店
     *
     * @param wmPoiIdList 外卖门店ID列表
     * @return 档口绑定任务信息
     */
    List<WmCanteenStallBindDO> selectByWmPoiIdWithRebind(@Param("wmPoiIdList") List<Long> wmPoiIdList);

    /**
     * 根据线索ID查询档口绑定任务列表
     * @param wdcClueId 线索ID
     * @return 档口绑定任务列表
     */
    List<WmCanteenStallBindDO> selectByWdcClueId(@Param("wdcClueId") Long wdcClueId);

    /**
     * 根据线索ID+食堂ID查询档口绑定任务列表
     * @param wdcClueId 线索ID
     * @param canteenPrimaryId 食堂主键ID
     * @return 档口绑定任务列表
     */
    WmCanteenStallBindDO selectByWdcClueIdAndCanteenPrimaryId(@Param("wdcClueId") Long wdcClueId,
                                                              @Param("canteenPrimaryId") Integer canteenPrimaryId);

    /**
     * 根据门店ID+食堂ID查询档口绑定任务列表
     * @param wmPoiId 线索ID
     * @param canteenPrimaryId 食堂主键ID
     * @return 档口绑定任务列表
     */
    WmCanteenStallBindDO selectByWmPoiIdAndCanteenPrimaryId(@Param("wmPoiId") Long wmPoiId,
                                                            @Param("canteenPrimaryId") Integer canteenPrimaryId);

    /**
     * 新增食堂档口绑定任务信息
     * @param wmCanteenStallBindTaskDO 食堂档口绑定任务信息
     * @return 更新行数
     */
    int insertSelective(WmCanteenStallBindDO wmCanteenStallBindTaskDO);

    /**
     * 根据主键ID更新食堂档口绑定任务信息
     * @param wmCanteenStallBindTaskDO 食堂档口绑定任务信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmCanteenStallBindDO wmCanteenStallBindTaskDO);

    /**
     * 批量更新主键id对应的档口绑定任务状态信息（门店绑定状态） （根据绑定id）
     * @param wmCanteenStallBindTaskDO 食堂档口绑定任务信息
     * @return 更新行数
     */
    int updateWmPoiBindStatusByBindIdList(WmCanteenStallBindDO wmCanteenStallBindTaskDO);

    /**
     * 批量更新主键id对应的档口绑定任务状态信息（门店绑定状态）（根据门店id）
     * @param wmCanteenStallBindTaskDO 食堂档口绑定任务信息
     * @return 更新行数
     */
    int updateWmPoiBindStatusByWmPoiIdList(WmCanteenStallBindDO wmCanteenStallBindTaskDO);


    /**
     * 获取目标食堂信息（门店绑定状态）（根据门店id）
     * @param wmAuditStatus 门店绑定状态
     * @return 更新行数
     */
    List<WmCanteenStallBindDO> selectByWmPoiIdsAndCanteenPriIdWitchStatus(@Param("wmAuditStatus") Integer wmAuditStatus,
                                                                          @Param("wmPoiIdList") List<Long> wmPoiIdList,
                                                                          @Param("canteenPrimaryId") Integer canteenPrimaryId);
}
