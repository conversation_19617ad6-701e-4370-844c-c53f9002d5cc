package com.sankuai.meituan.waimai.customer.service.kp.sensitive.read;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.decrypt.KeyDecrypt;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.decrypt.KeyDecryptHandleService;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.EncryptResult;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncrypt;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncryptHandleService;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.write.IdentifyIdKpEntryWriteHandle;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 读证件号
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
@Slf4j
public class IdentifyIdKpReadHandle implements IKpReadHandle {


    @Autowired
    private KeyDecryptHandleService keyDecryptHandleService;

    @Autowired
    private KeyEncryptHandleService keyEncryptHandleService;

    @Autowired
    private IdentifyIdKpEntryWriteHandle identifyIdKpEntryWriteHandle;


    @Override
    public KmsKeyNameEnum handleType() {
        return KmsKeyNameEnum.IDENTIFY_ID;
    }

    @Override
    public void doReadChoiceEncryptToDecrypt(KpRead kpRead) {
        if (kpRead == null) {
            return;
        }
        if (kpRead.getKp() != null) {
            kpRead.getKp().setCertNumber(getReadCertNumber(kpRead.getKp().getCertNumber(), kpRead.getKp().getCertNumberEncryption(), kpRead.getKp().getCertType()));
        }
        if (kpRead.getKpTemp() != null) {
            kpRead.getKpTemp().setCertNumber(getReadCertNumber(kpRead.getKpTemp().getCertNumber(), kpRead.getKpTemp().getCertNumberEncryption(), kpRead.getKpTemp().getCertType()));
        }
    }

    @Override
    public String doReadEncryptToDecrypt(KpRead kpRead) {
        log.info("execute::doReadEncryptToDecrypt = {}", JSON.toJSONString(kpRead));
        //读新字段，新的加密字段解密赋值给原字段
        if (StringUtils.isBlank(kpRead.getEncryptionValue()) || kpRead == null) {
            return "";
        }
        KeyDecrypt keyDecrypt = new KeyDecrypt();
        keyDecrypt.setKeyName(kpRead.getKeyName());
        keyDecrypt.setValueForDecrypt(kpRead.getEncryptionValue());
        return keyDecryptHandleService.execute(keyDecrypt);
    }


    private String getReadCertNumber(String source, String targetEncryption, int certType) {
        if (MccCustomerConfig.encryptionCustomerKpCertNumberReadSwitch()) {
            //读原字段
            return source;
        } else {
            if (StringUtils.isBlank(targetEncryption)) {
                if (!tryEncryption(KmsKeyNameEnum.IDENTIFY_ID, source, certType)) {
                    //如果不符合Token加密条件则读原字段
                    log.debug("getReadPhoneNo-NotRegexMatch 客户手机号不合法Token加密条件，使用原手机号字段::source = {},", source);
                    return source;
                } else {
                    return targetEncryption;
                }
            }
            //读新字段，新的加密字段解密赋值给原字段
            KeyDecrypt keyDecrypt = new KeyDecrypt();
            keyDecrypt.setKeyName(KmsKeyNameEnum.IDENTIFY_ID);
            keyDecrypt.setValueForDecrypt(targetEncryption);
            String num = keyDecryptHandleService.execute(keyDecrypt);
            if (StringUtils.isBlank(num)) {
                //如果无加密字段返回原字段
                return source;
            }
            return num;
        }
    }


    /**
     * 对字段尝试加密
     *
     * @param key
     * @param value
     * @return
     */
    private boolean tryEncryption(KmsKeyNameEnum key, String value, int certType) {
        try {
            KeyEncrypt keyEncrypt = new KeyEncrypt();
            keyEncrypt.setKeyName(key);
            keyEncrypt.setValueForEncrypt(value);
            keyEncrypt.setCertType(identifyIdKpEntryWriteHandle.getCertificateType(CertTypeEnum.getByType(certType), value));
            EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
            if (result == null) {
                return false;
            }
        } catch (WmCustomerException e) {
            log.info("tryEncryption::key = {}, value = {},code={},msg={}", key, value, e.getCode(), e.getMsg());
            return false;
        }
        return true;
    }


}
