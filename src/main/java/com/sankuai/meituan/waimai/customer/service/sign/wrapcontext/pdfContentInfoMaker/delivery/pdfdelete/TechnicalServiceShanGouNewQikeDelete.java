package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY;

/**
 * 判断佣金协议合同参数为空，移除该合同
 *
 * @Author: wangyongfang
 * @Date: 2023-12-08
 */
@Slf4j
@Service
@PdfDeleter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE)
public class TechnicalServiceShanGouNewQikeDelete implements DeliveryPdfDelete {
    @Override
    public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
        if (CollectionUtils.isEmpty(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE.getName()))) {
            Collection<SignTemplateEnum> tabList = tabPdfMap.get(TAB_DELIVERY);
            if (CollectionUtils.isNotEmpty(tabList)) {
                log.info("technicalServiceShanGouNewQikeDelete remove TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE");
                tabList.removeIf(value -> value.equals(SignTemplateEnum.TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE));
            }
        }
    }
}
