package com.sankuai.meituan.waimai.customer.service.kp.sensitive.write;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
public class KpEntryWriteHandleService {

    @Autowired
    private List<IKpWriteHandle> iKpWriteHandleList;

    private Map<KmsKeyNameEnum, IKpWriteHandle> iKpWriteHandleMap = new HashMap<>();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(iKpWriteHandleList)) {
            return;
        }
        for (IKpWriteHandle handel : iKpWriteHandleList) {
            iKpWriteHandleMap.put(handel.handleType(), handel);
        }
    }


    public void doWriteKpWhenInsertOrUpdate(KpEntryWrite kpEntryWrite) throws WmCustomerException {
        if (kpEntryWrite == null) {
            return;
        }
        iKpWriteHandleMap.get(kpEntryWrite.getKeyName()).doWriteKpWhenInsertOrUpdate(kpEntryWrite);
    }


    public void writeKpSourceWhenUpdate(KpEntryWrite kpEntryWrite) {
        if (kpEntryWrite == null) {
            return;
        }
        iKpWriteHandleMap.get(kpEntryWrite.getKeyName()).writeKpSourceWhenUpdate(kpEntryWrite);
    }
}
