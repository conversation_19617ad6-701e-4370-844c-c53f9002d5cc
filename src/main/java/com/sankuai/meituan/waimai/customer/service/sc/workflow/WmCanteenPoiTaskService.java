package com.sankuai.meituan.waimai.customer.service.sc.workflow;

import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenPoiTaskAuditMinutiaBO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenPoiTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskDO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiAuditStatusV2Enum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskSumBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.apache.thrift.TException;

public interface WmCanteenPoiTaskService {
    /**
     * 获取任务类型
     *
     * @return
     */
    CanteenPoiTaskTypeEnum getTaskType();

    /**
     * 食堂绑定/换绑门店任务校验
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    void checkTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException, TException;

    /**
     * 创建任务
     *
     * @param wmCanteenPoiTaskBO
     * @return
     * @throws WmSchCantException
     */
    long createTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException, TException;

    /**
     * 任务完成提交（通过/驳回）
     *
     * @param wmCanteenPoiTaskAuditMinutiaBO
     * @throws WmSchCantException
     */
    CanteenPoiAuditStatusV2Enum commitTask(WmCanteenPoiTaskAuditMinutiaBO wmCanteenPoiTaskAuditMinutiaBO) throws WmSchCantException;

    /**
     * 任务取消
     *
     * @param taskId
     * @param opUid
     * @param opUname
     * @throws WmSchCantException
     */
    void cancelTask(long taskId, int opUid, String opUname) throws WmSchCantException;

    /**
     * 任务生效：进行绑定或/换绑动作
     *
     * @throws WmSchCantException
     */
    void effectTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException, TException;

    /**
     * 获取审核任务详情
     * @param wmScCanteenPoiTaskDO
     * @param wmCanPoiTaskSumBo
     * @throws WmSchCantException
     * @throws TException
     */
    void getAuditInfo(WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO,WmCanPoiTaskSumBo wmCanPoiTaskSumBo) throws WmSchCantException, TException;
}
