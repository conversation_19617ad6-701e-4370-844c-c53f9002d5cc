package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * wm_customer表对应dao层<br>
 * 与客户平台融合，统一封装在WmCustomerPlatformDataParseService<br>
 * 请勿直接使用该类!
 */
@Component
public interface WmCustomerDBMapper {

    Integer deleteCustomer(Integer id);

    Integer insertCustomer(WmCustomerDB wmCustomerDB);

    List<WmCustomerDB> selectCustomerByIds(@Param("set")Set<Integer> set);

    List<WmCustomerDB> selectCustomerBySuperCustomerId(Integer superCustomerId);


    //tips：这个方法通过Mybatis拦截器+Mcc配置，最终实现走主库
    // com.sankuai.meituan.waimai.customer.util.MasterSlaveHelper
    WmCustomerDB selectCustomerById(Integer id);

    //主库治理
    WmCustomerDB selectCustomerByIdFromSlave(Integer id);

    Integer selectOnwerUidByWmCustomerId(Integer id);

    List<WmCustomerDB> selectCustomerBySuperId(@Param("superCustomerId")Long id);

    Integer selectWmCustomerIdByMtCustomerId(Long id);

    Long selectMtCustomerIdByWmCustomerId(@Param("wmCustomerId")Long wmCustomerId);

    Integer selectWmCustomerIdByMtCustomerIdMaster(Long id);

    Integer unBindSuperCustomer(Integer id);

    WmCustomerDB selectCustomerByIdRT(Integer id);

    WmCustomerDB selectCustomerByIdOrMtCustomerId(long customerId);

    /**
     * 从主库查询客户信息
     *
     * @param customerId
     * @return
     */
    WmCustomerDB selectCustomerByIdOrMtCustomerIdRT(long customerId);

    List<WmCustomerDB> selectCustomerListByIdOrMtCustomerId(@Param("idSet")Set<Long> idSet);

    WmCustomerDB selectCustomerByWmPoiId(Long wmPoiId);
    Integer updateCustomer(WmCustomerDB wmCustomerDB);

    Integer updateCustomerContractor(WmCustomerDB wmCustomerDB);

    @Deprecated
    Integer updateCustomerByAudit(WmCustomerDB wmCustomerDB);

    Integer updateCustomerAuditStatus(WmCustomerDB wmCustomerDB);

    Integer updateSignMode(@Param("cusId") Integer cusId, @Param("signMode") Integer signMode);

    @Select("select effective from wm_customer where id=#{id} and valid=1")
    Integer checkCustomerEffect(@Param("id")Integer id);

    List<WmCustomerDB> selectCustomerListByKeyword(@Param("keyword")String keyword,@Param("searchType")Integer searchType, @Param("isLeaf")Integer isLeaf);

    List<WmCustomerListDB> selectCustomerList(WmCustomerFormDB wmCustomerFormDB);

    void distributeCustomer(@Param("list")List<Integer> customerIdList, @Param("userId") int userId);

    List<WmCustomerDB> selectCustomerOwnUidList(List<Integer> customerIdList);

    List<Integer> selectCustomerIdsByOwnerUid(Integer ownerUid);

    Integer countCustomerIdsByOwner(@Param("ownerUid") Integer ownerUid);

    List<WmCustomerDB> selectCustomerByIdSection(@Param("idStart") Integer idStart, @Param("idEnd") Integer idEnd);

    List<WmCustomerDB> selectCustomerByUtimeSection(@Param("startTime") Integer startTime, @Param("endTime") Integer endTime);

    List<WmCustomerDB> selectCustomerDBList(@Param("list") List<Integer> customerIdList);

    List<WmCustomerDB> selectCustomerDBListByMtCustomerId(@Param("list") List<Long> customerIdList);

    Integer updateCustomerRealType(@Param("cusId") Integer cusId, @Param("customerRealType") Integer customerRealType);

    /**
     * 查询关联的下级客户数量
     * @param customerIds
     * @return
     */
    List<WmCustomerListDB> countsubCustomerList(@Param("customerIds") List<Integer> customerIds);

    /**
     * 根据资质编号查询客户信息</br>
     * 只查询状态为生效，并且为叶子节点的客户
     *
     * @param customerNumber
     * @return
     */
    List<WmCustomerDB> selectCustomerByCustomerNumber(@Param("customerNumber")String customerNumber);
    /**
     * 根据ID列表查询
     */
    List<WmCustomerDB> selectContractorByIds(@Param("list")List<Integer> list);

    /**
     * 食堂承包商客户执照名称判重用
     */
    int existByRealTypeAndCustomerNameAndAuditStatusAndCustomerType(@Param("customerRealType")int customerRealType, @Param("customerName")String customerName, @Param("auditStatus")int auditStatus, @Param("customerType")int customerType);

    List<WmCustomerDB> queryWmCustomerByCustomerRealTypeList(@Param("customerRealTypeList") List<Integer>
                                                                     customerRealTypeList,@Param("customerIdList") List<Integer>customerIdList,@Param("num") int num);

    /**
     *
     * @param customerIdList
     * @param customerRelType
     */
    void updateCustomerRealTypeByCustomerIds(@Param("customerIdList") List<Integer> customerIdList, @Param
            ("customerRelType") int customerRelType);

    List<WmCustomerDB> selectByCondition(WmCustomerSelectConditionFormDB condition);

    List<WmCustomerDB> selectByQueryVo(WmCustomerQueryVo vo);

    /**
     * 更新客户执照状态-刷数据使用
     * @param id
     * @param certificateStatus
     * @return
     */
    Integer updateCertificateStatus(@Param("id") Integer id, @Param("certificateStatus") Integer certificateStatus);

    /**
     * 更新客户过期标记-刷数据使用
     *
     * @param id
     * @param certificateOverdue
     * @return
     */
    Integer updateCertificateOverdue(@Param("id") Integer id, @Param("certificateOverdue") Integer certificateOverdue);

    List<WmCustomerDB> selectCustomerByMtCustomerIds(@Param("list")List<Long> list);

    void updateAuditStatus(@Param("id") Integer id, @Param("auditStatus") Integer auditStatus);

    /**
     * 根据uid列表分页查询指定类型客户
     */
    List<WmCustomerDB> selectCustomerListByUidListAndRealType(@Param("uidList") List<Integer> uidList, @Param("customerRealType") int customerRealType,@Param("pageFrom") Integer pageFrom,@Param("pageSize") Integer pageSize);

    int getMaxId();

    List<WmCustomerDB> listCustomerByCleanQueryParams(WmCustomerCleanQuery wmCustomerCleanQuery);

    List<WmCustomerDB> getNotLimitValidByCustomerIds(@Param("idList") List<Integer> idList);

    /**
     * 根据客户ID更新上级客户其他信息
     *
     * @param id
     * @param customerExtPro
     */
    void updateCustomerExtPro(@Param("id") Integer id, @Param("customerExtPro") String customerExtPro);

    /**
     * 根据用户UID列表查询作为负责人的食堂承包商客户列表
     */
    List<Integer> selectContractorIdListByOwnerUidList(@Param("ownerUidList") List<Integer> uidList);

    /**
     * 根据客户ID更新客户类型
     *
     * @param customerId
     * @param newCustomerRealType
     */
    void updateCustomerRealTypeById(@Param("customerId") Integer customerId, @Param("newCustomerRealType") Integer newCustomerRealType);

    void updateCustomerInfoById(WmCustomerDB wmCustomerDB);
}

