package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmBusinessCustomerTempletService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTSHWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCADynamicWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractEstampDynamicWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTSHWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 企客合同签约数据组装
 */
@Service
public class WmEcontractCompanyCustomerContractApplyService extends AbstractWmEcontractApplyAdapterService {

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Autowired
    private WmBusinessCustomerTempletService wmBusinessCustomerTempletService;

    @Autowired
    private WmEcontractCADynamicWrapperService wmEcontractCADynamicWrapperService;

    @Autowired
    private WmEcontractEstampDynamicWrapperService wmEcontractEstampDynamicWrapperService;

    @Autowired
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Autowired
    private WmEcontractStampMTSHWrapperService wmEcontractStampMTSHWrapperService;

    @Autowired
    private WmEcontractCAMTSHWrapperService wmEcontractCAMTSHWrapperService;

    private static final String TYPE_BUSINESS_CUSTOMER_CONTRACT = "type_business_customer_contract";

    private static final String FLOW_BUSINESS_CUSTOMER_CONTRACT = EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT.getName();

    private static List<String> flowList = Lists.newArrayList();

    private static List<String> poiStampList = Lists.newArrayList();

    private static List<String> mtStampList = Lists.newArrayList();

    private static List<String> dynamicStampList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_BUSINESS_CUSTOMER_CONTRACT);

        poiStampList.add(FLOW_BUSINESS_CUSTOMER_CONTRACT);

        mtStampList.add(FLOW_BUSINESS_CUSTOMER_CONTRACT);

        dynamicStampList.add(FLOW_BUSINESS_CUSTOMER_CONTRACT);

        dataWrapperMap.put(FLOW_BUSINESS_CUSTOMER_CONTRACT, EcontractDataWrapperEnum.BUSINESS_CUSTOMER_E_CONTRACT);
    }

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo) throws TException, IllegalAccessException, WmCustomerException {
        //生成pdf处理方式识别
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT);
        boolean generatePdf = wmBusinessCustomerTempletService.generatePdf(taskBo.getApplyContext(), batchContextBo.getCustomerId());
        batchContextBo.setGeneratePdf(generatePdf);

        //签约过程数据组装
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));

        if(generatePdf){
            String caSubjectName = wmBusinessCustomerTempletService.queryCaSubjectName(taskBo.getApplyContext(), batchContextBo.getCustomerId());
            Map<String,String> estampInfoMap = wmBusinessCustomerTempletService.queryEstampInfoMap(taskBo.getApplyContext(), batchContextBo.getCustomerId());
            batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
            batchInfoBoList.add(wmEcontractCADynamicWrapperService.wrap(batchContextBo, caSubjectName));
            if(MapUtils.isNotEmpty(estampInfoMap)){
                for(Map.Entry<String,String> map : estampInfoMap.entrySet()){
                    StageBatchInfoBo stageBatchInfoBo = wmEcontractEstampDynamicWrapperService.wrap(batchContextBo, dynamicStampList, map);
                    Map<String, String> estampParamMap = Maps.newHashMap();
                    estampParamMap.put(TaskConstant.PDF_ESTAMP_SIGN_KEY,map.getValue());
                    estampParamMap.put(TaskConstant.PDF_ESTAMP_SIGN_KEY_SUBJECT,map.getKey());
                    stageBatchInfoBo.getEstampInfoBo().setEstampMap(estampParamMap);
                    batchInfoBoList.add(stageBatchInfoBo);
                }
            }

        }else{
            batchInfoBoList.add(wmEcontractCAMTSHWrapperService.wrap(batchContextBo));
            batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
            batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, poiStampList));
            batchInfoBoList.add(wmEcontractStampMTSHWrapperService.wrap(batchContextBo, mtStampList));
        }

        generatePdfEcontractFlowModify(batchContextBo, batchInfoBoList);

        return new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(TYPE_BUSINESS_CUSTOMER_CONTRACT)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .callBackUrl(getCallbackUrl()).econtractBatchSource(getSource(batchContextBo))
                .build();
    }

    private void generatePdfEcontractFlowModify(EcontractBatchContextBo batchContextBo, List<StageBatchInfoBo> batchInfoBoList){
        if(!batchContextBo.isGeneratePdf()){
            return;
        }
        for(StageBatchInfoBo stageBatchInfoBo : batchInfoBoList){
            if(stageBatchInfoBo.getStageName().equals(WmEcontractConstant.CA_MT)){
                stageBatchInfoBo.setStageName(WmEcontractConstant.CA_MTSH);
            }else if(stageBatchInfoBo.getStageName().equals(WmEcontractConstant.STAMP_MT)){
                stageBatchInfoBo.setStageName(WmEcontractConstant.STAMP_MTSH);
            }
        }
    }

}
