package com.sankuai.meituan.waimai.customer.settle.service.adapter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.meituan.pay.mwallet.proxy.thrift.merchantentry.MerchantEntryProxyService;
import com.meituan.pay.mwallet.proxy.thrift.merchantentry.entity.BooleanResTo;
import com.meituan.pay.mwallet.proxy.thrift.merchantentry.entity.QueryMerchantEntryInfoReq;
import com.meituan.pay.mwallet.proxy.thrift.merchantentry.entity.QueryMerchantEntryInfoSignRes;
import com.meituan.pay.mwallet.proxy.thrift.merchantentry.entity.ResendConfirmSmsReqToForWmSignTask;
import com.meituan.pay.mwallet.proxy.util.BeanToMapUtil;
import com.meituan.pay.mwallet.util.SignAndEncUtil;
import com.sankuai.meituan.waimai.customer.settle.grey.WmSettleNvWaGrayService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.sankuai.meituan.waimai.poi.constants.PoiBizTypeEnum;

import java.util.Map;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PaymentAgentAdapter {

    @Autowired
    private WmSettleNvWaGrayService wmSettleNvWaGrayService;
    @Autowired
    private MerchantEntryProxyService.Iface merchantEntryProxyService;

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;


    public int getSettleStatus(int wmCustomerId,long wmPoiId) throws WmCustomerException, TException {
        QueryMerchantEntryInfoReq queryMerchantEntryInfoReq = new QueryMerchantEntryInfoReq();
        queryMerchantEntryInfoReq.setCustomerId(wmCustomerId);
        queryMerchantEntryInfoReq.setPoiId(wmPoiId);
        WmPoiAggre wmPoiAggre = null;
        try {
            wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(wmPoiId, Sets.newHashSet(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_POI_BIZ_TYPES));
            log.info("getSettleStatus wmPoiAggre:{}", JSONObject.toJSONString(wmPoiAggre));
        }catch (Exception ex){
            throw  new WmCustomerException(-1,"获取门店信息异常");
        }
        if(wmPoiAggre != null && String.valueOf(PoiBizTypeEnum.CROSS_BORDER.getType()).equals(wmPoiAggre.getPoi_biz_types())){
            queryMerchantEntryInfoReq.setIphPayMerchantNo(wmSettleNvWaGrayService.getKuaJingMerchantId());
        }else {
            queryMerchantEntryInfoReq.setIphPayMerchantNo(wmSettleNvWaGrayService.getMerchantId());
        }
        Map<String, String> convertMap = null;
        try {
            convertMap = BeanToMapUtil.convertBeanForSign(queryMerchantEntryInfoReq);
        } catch (Exception e) {
            log.error("convertBeanForSign exception",e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,"查询金融服务异常");
        }
        try {
            if(wmPoiAggre != null && String.valueOf(PoiBizTypeEnum.CROSS_BORDER.getType()).equals(wmPoiAggre.getPoi_biz_types())){
                String sign = SignAndEncUtil.sign(convertMap, wmSettleNvWaGrayService.getKuaJingPrivateKey(), "UTF-8");
                if(sign == null){
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,"获取跨境私钥失败");
                }
                queryMerchantEntryInfoReq.setSign(sign);
            }else {
                String sign = SignAndEncUtil.sign(convertMap, wmSettleNvWaGrayService.getPrivateKey(), "UTF-8");
                queryMerchantEntryInfoReq.setSign(sign);
            }
            queryMerchantEntryInfoReq.setSignType("RSA");
        } catch (Exception e) {
            log.error("sign exception",e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,"查询金融服务异常");
        }
        log.info("#queryMerchantEntryInfoReq={}", JSONObject.toJSONString(queryMerchantEntryInfoReq));
        QueryMerchantEntryInfoSignRes queryMerchantEntryInfoSignRes = merchantEntryProxyService.queryMerchantEntryInfo(queryMerchantEntryInfoReq);
        log.info("#queryMerchantEntryInfoSignRes={}", JSONObject.toJSONString(queryMerchantEntryInfoSignRes));
        //查询失败
        if(queryMerchantEntryInfoSignRes.getPlain().getStatus().equalsIgnoreCase("fail")){
            //商户进件信息不存在
            if(queryMerchantEntryInfoSignRes.getPlain().getError().getCode() == 160100){
                return 1;
            }
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,queryMerchantEntryInfoSignRes.getPlain().getError().getMessage());
        }
        return queryMerchantEntryInfoSignRes.getPlain().getData().getStatus();
    }

    public boolean resendMsgByBatchId(long batchId, String bizId) throws WmCustomerException, TException {
        if(StringUtils.isEmpty(bizId)){
            return false;
        }
        ResendConfirmSmsReqToForWmSignTask reqTo = new ResendConfirmSmsReqToForWmSignTask();
        reqTo.setIphPayMerchantNo(wmSettleNvWaGrayService.getMerchantId());
        reqTo.setEntryFlowId(bizId);
        reqTo.setWmSettleTaskId(batchId);
        Map<String, String> convertMap = null;
        try {
            convertMap = BeanToMapUtil.convertBeanForSign(reqTo);
        } catch (Exception e) {
            log.error("convertBeanForSign exception",e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,"重发短信异常");
        }
        try {
            String sign = SignAndEncUtil.sign(convertMap, wmSettleNvWaGrayService.getPrivateKey(), "UTF-8");
            reqTo.setSign(sign);
            reqTo.setSignType("RSA");
        } catch (Exception e) {
            log.error("sign exception",e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,"重发短信异常");
        }
        log.info("#resendConfirmSmsReqToForWmSignTask={}", JSONObject.toJSONString(reqTo));
        BooleanResTo booleanResTo = merchantEntryProxyService.resendConfirmSmsForWmSignTask(reqTo);
        log.info("#booleanResTo={}", JSONObject.toJSONString(booleanResTo));
        if("fail".equalsIgnoreCase(booleanResTo.getStatus())){
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,booleanResTo.getError().getMessage());
        }
        return true;
    }
}
