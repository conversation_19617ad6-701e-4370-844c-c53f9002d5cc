package com.sankuai.meituan.waimai.customer.dao.sc.delivery;

import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校交付审批任务主表Mapper
 * <AUTHOR>
 * @date 2024/02/15
 * @email <EMAIL>
 **/
@Component
public interface WmSchoolDeliveryAuditTaskMapper {

    /**
     * 根据主键ID查询学校交付审批任务
     * @param id 主键ID
     * @return 学校交付审批任务信息
     */
    WmSchoolDeliveryAuditTaskDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据学校主键ID查询学校交付审批任务
     * @param schoolPrimaryId 学校主键ID
     * @return 学校交付审批任务信息
     */
    List<WmSchoolDeliveryAuditTaskDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据学校主键ID查询[审批中]的学校交付审批任务
     * @param schoolPrimaryId 学校主键ID
     * @return [审批中]的学校交付审批任务信息
     */
    WmSchoolDeliveryAuditTaskDO selectAuditingTaskBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据交付编号ID查询[审批中]的学校交付审批任务
     * @param deliveryId 交付编号ID
     * @return [审批中]的学校交付审批任务信息
     */
    WmSchoolDeliveryAuditTaskDO selectAuditingTaskByDeliveryId(@Param("deliveryId") Integer deliveryId);

    /**
     * 根据交付ID查询学校交付审批任务
     * @param deliveryId 交付ID
     * @return 学校交付审批任务信息
     */
    List<WmSchoolDeliveryAuditTaskDO> selectByDeliveryId(@Param("deliveryId") Integer deliveryId);

    /**
     * 根据交付编号ID查询最近一次审批任务
     * @param deliveryId 交付ID
     * @return 学校交付审批任务信息
     */
    WmSchoolDeliveryAuditTaskDO selectLastestAuditTaskByDeliveryId(@Param("deliveryId") Integer deliveryId);

    /**
     * 根据学校主键ID查询最近一次交付跟进的审批任务
     * @param schoolPrimaryId 学校主键ID
     * @return 最近一次交付跟进的审批任务
     */
    WmSchoolDeliveryAuditTaskDO selectLastFollowUpAuditTaskBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 新增学校交付审批任务信息
     * @param wmSchoolDeliveryAuditTaskDO 学校交付审批任务
     * @return 更新行数
     */
    int insertSelective(WmSchoolDeliveryAuditTaskDO wmSchoolDeliveryAuditTaskDO);

    /**
     * 根据主键ID更新学校交付审批任务信息
     * @param wmSchoolDeliveryAuditTaskDO 学校交付审批任务信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmSchoolDeliveryAuditTaskDO wmSchoolDeliveryAuditTaskDO);

    /**
     * 根据交付节点ID查询审批中的审批任务列表
     * @param deliveryNodeType 交付节点ID
     * @return 审批中的审批任务列表
     */
    List<WmSchoolDeliveryAuditTaskDO> selectAuditingTaskByDeliveryNodeType(@Param("deliveryNodeType") Integer deliveryNodeType);

    /**
     * 根据交付编号ID和交付节点查询最近一次审批任务
     * @param deliveryId 交付编号ID
     * @param deliveryNodeType 交付流节点类型
     * @return 最近一次审批任务
     */
    WmSchoolDeliveryAuditTaskDO selectLatestAuditTaskByDeliveryIdAndDeliveryNodeType(@Param("deliveryId") Integer deliveryId,
                                                                                     @Param("deliveryNodeType") Integer deliveryNodeType);
}
