package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

/**
 * Author: 李学鹏
 * MIS: lixuepeng
 * Date: 2022-4-19
 * Email: <EMAIL>
 * Desc: 打包(袋)服务合作协议废除前置校验
 */
@Service
@Slf4j
public class BagServiceInvalidValidator implements IContractValidator {

    @Autowired
    WmTempletContractDBMapper wmTempletContractDBMapper;

    /**
     * @param contractBo
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     * 签约失败、待生效、已生效的情况才能废除合同
     */
    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        Long contractId = contractBo.getBasicBo().getTempletContractId();
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        if (wmTempletContractDB == null) {
            log.info("valid#废除合同校验器，contractId:{}，不存在合同", contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在合同，不可废除");
        }
        if (wmTempletContractDB.getStatus() == CustomerContractStatus.SIGN_FAIL.getCode()
                || wmTempletContractDB.getStatus() == CustomerContractStatus.TO_EFFECT.getCode()
                || wmTempletContractDB.getStatus() == CustomerContractStatus.EFFECT.getCode()) {
            return true;
        }
        log.info("valid#废除合同校验器，contractId:{}，合同状态为:{}，不允许操作废除", CustomerContractStatus.getByCode(wmTempletContractDB.getStatus()).getDesc());
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态不允许操作废除");
    }

}
