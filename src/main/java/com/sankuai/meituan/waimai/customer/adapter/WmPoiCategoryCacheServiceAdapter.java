package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.poicategory.bo.WmPoiCateDic;
import com.sankuai.meituan.waimai.poicategory.service.impl.WmPoiCategoryCacheService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 门店品类服务适配器
 * <AUTHOR>
 * @date 2024/05/10
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmPoiCategoryCacheServiceAdapter {

    @Autowired
    private WmPoiCategoryCacheService wmPoiCategoryCacheService;

    /**
     * 根据门店品类名称全匹配找到对应的品类节点
     * @param cateName 门店品类名称
     * @return 品类节点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmPoiCateDic getWmPoiCateDicByCateName(String cateName) throws WmSchCantException {
        try {
            log.info("[WmPoiCategoryCacheServiceAdapter.getWmPoiCateDicByCateName] cateName = {}", cateName);
            WmPoiCateDic wmPoiCateDic = wmPoiCategoryCacheService.getWmPoiCateDicByCateName(cateName);
            log.info("[WmPoiCategoryCacheServiceAdapter.getWmPoiCateDicByCateName] wmPoiCateDic = {}", JSONObject.toJSONString(wmPoiCateDic));

            return wmPoiCateDic;
        } catch (Exception e) {
            log.error("[WmPoiCategoryCacheServiceAdapter.getWmPoiCateDicByCateName] Exception. cateName = {}", cateName, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询门店品类信息失败");
        }
    }


    /**
     * 根据门店叶子节点品类ID 获取品类链，品类链中元素的顺序为：非叶子节点—>非叶子节点—>叶子节点
     * @param cateId 品类ID
     * @return 品类链
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<WmPoiCateDic> getWmPoiCateDicTreeByCateId(Long cateId) throws WmSchCantException {
        try {
            log.info("[WmPoiCategoryCacheServiceAdapter.getWmPoiCateDicTreeByCateId] cateId = {}", cateId);
            List<WmPoiCateDic> wmPoiCateDicList = wmPoiCategoryCacheService.getWmPoiCateDicTreeByCateId(cateId);
            log.info("[WmPoiCategoryCacheServiceAdapter.getWmPoiCateDicTreeByCateId] wmPoiCateDicList = {}", JSONObject.toJSONString(wmPoiCateDicList));

            return wmPoiCateDicList;
        } catch (Exception e) {
            log.error("[WmPoiCategoryCacheServiceAdapter.getWmPoiCateDicTreeByCateId] Exception. cateId = {}", cateId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询门店品类信息失败");
        }
    }


    /**
     * 根据门店品类ID批量查询品类链，品类链中元素的顺序为：非叶子节点—>非叶子节点—>叶子节点
     * @param cateIdList 品类ID列表
     * @return Map<Long, List<WmPoiCateDic>>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<Long, List<WmPoiCateDic>> mgetWmPoiCateDicTreeByCateIds(List<Long> cateIdList) throws WmSchCantException {
        try {
            log.info("[WmPoiCategoryCacheServiceAdapter.mgetWmPoiCateDicTreeByCateIds] cateIdList = {}", JSONObject.toJSONString(cateIdList));
            Map<Long, List<WmPoiCateDic>> resultMap = wmPoiCategoryCacheService.mgetWmPoiCateDicTreeByCateIds(cateIdList);
            log.info("[WmPoiCategoryCacheServiceAdapter.mgetWmPoiCateDicTreeByCateIds] resultMap = {}", JSONObject.toJSONString(resultMap));

            return resultMap;
        } catch (Exception e) {
            log.error("[WmPoiCategoryCacheServiceAdapter.mgetWmPoiCateDicTreeByCateIds] Exception. cateIdList = {}", JSONObject.toJSONString(cateIdList), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "批量查询门店品类信息失败");
        }
    }
}
