package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.SlaDataWrapper;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractContentTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
public class WmEcontractSlaDataWrapperService {

    private static Logger logger = LoggerFactory.getLogger(WmEcontractSlaDataWrapperService.class);

    @Resource
    private List<IWmEcontractSlaDataWrapperService> slaDataWrapperServiceList;

    static Map<EcontractDataWrapperEnum, IWmEcontractSlaDataWrapperService> enumAndWrapperMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        for (IWmEcontractSlaDataWrapperService wrapperService : slaDataWrapperServiceList) {
            SlaDataWrapper annotation = AopUtils.getTargetClass(wrapperService).getAnnotation(SlaDataWrapper.class);
            if (annotation == null || annotation.wrapperEnum() == null) {
                continue;
            }
            enumAndWrapperMap.put(annotation.wrapperEnum(), wrapperService);
        }
    }

    /**
     * 组装SLA-配送范围数据
     */
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo, EcontractDataWrapperEnum dataWrapperEnum)
            throws WmCustomerException, IllegalAccessException {
        List<EcontractContentBo> slaDataList = null;
        if(enumAndWrapperMap.get(dataWrapperEnum) !=null){
            slaDataList = enumAndWrapperMap.get(dataWrapperEnum).wrap(contextBo);
        }

        Map<String, String> viewMap = Maps.newHashMap();
        viewMap.put(EcontractContentTypeEnum.SP_AREA.getName(), JSONObject.toJSONString(slaDataList));

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.VIEW_CONTENT)
                .viewContentMap(viewMap)
                .build();
    }

    public void addContext(List<StageBatchInfoBo> batchInfoBoList, EcontractBatchContextBo contextBo,
            EcontractDataWrapperEnum dataWrapperEnum) throws WmCustomerException, IllegalAccessException {
        if (dataWrapperEnum == null) {
            return;
        }
        List<EcontractContentBo> slaDataList = null;
        if (enumAndWrapperMap.get(dataWrapperEnum) != null) {
            slaDataList = enumAndWrapperMap.get(dataWrapperEnum).wrap(contextBo);
        }
        if (CollectionUtils.isNotEmpty(slaDataList)) {
            StageBatchInfoBo viewContentInfoBo = null;
            for (StageBatchInfoBo infoBoTemp : batchInfoBoList) {
                if (WmEcontractConstant.VIEW_CONTENT.equals(infoBoTemp.getStageName())) {
                    viewContentInfoBo = infoBoTemp;
                }
            }
            Map<String, String> viewMap = Maps.newHashMap();
            Map<String, Map<String, String>> stageAndViewContentMap = Maps.newHashMap();
            viewMap.put(EcontractContentTypeEnum.SP_AREA.getName(), JSONObject.toJSONString(slaDataList));
            String stageNameForMap = "";
            if (dataWrapperEnum == EcontractDataWrapperEnum.DELIVERY
                    || dataWrapperEnum == EcontractDataWrapperEnum.BATCH_DELIVERY) {
                stageNameForMap = EcontractTaskApplyTypeEnum.POIFEE.getName();
            } else if (dataWrapperEnum == EcontractDataWrapperEnum.DELIVERY_WHOLE_CITY
                    || dataWrapperEnum == EcontractDataWrapperEnum.BATCH_DELIVERY_WHOLE_CITY) {
                stageNameForMap = EcontractTaskApplySubTypeEnum.DELIVERY_WHOLE_CITY.getName();
            }
            stageAndViewContentMap.put(stageNameForMap, viewMap);
            if (viewContentInfoBo == null) {
                batchInfoBoList.add(new StageBatchInfoBo.Builder().stageName(WmEcontractConstant.VIEW_CONTENT)
                        .stageAndViewContentMap(stageAndViewContentMap).build());
            } else {
                viewContentInfoBo.getStageAndViewContentMap().putAll(stageAndViewContentMap);
            }
        }
    }
}
