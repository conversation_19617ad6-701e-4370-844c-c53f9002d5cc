package com.sankuai.meituan.waimai.customer.service.sc.flow.action.canteenStallClueBind;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.clueflow.WmCanteenStallClueTaskService;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmCanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmCanteenStallClueBindStatusMachineEvent;
import com.sankuai.meituan.waimai.customer.service.sc.flow.entity.WmCanteenStallClueBindStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.sc.flow.exception.WmScStatusMachineException;
import com.sankuai.meituan.waimai.customer.service.sc.flow.machine.WmCanteenStallClueBindStatusMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.squirrelframework.foundation.fsm.AnonymousAction;

/**
 * 食堂档口线索绑定状态-绑定失败动作
 * <AUTHOR>
 * @date 2024/05/28
 * @email <EMAIL>
 */
@Component
@Slf4j
public class WmCanteenStallClueBindFailAction extends AnonymousAction<WmCanteenStallClueBindStatusMachine, WmCanteenStallClueBindStatusEnum, WmCanteenStallClueBindStatusMachineEvent, WmCanteenStallClueBindStatusMachineContext> {

    @Autowired
    private WmCanteenStallClueTaskService wmCanteenStallClueTaskService;


    /**
     * 食堂档口线索绑定状态-绑定失败动作
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    public void execute(WmCanteenStallClueBindStatusEnum fromState,
                        WmCanteenStallClueBindStatusEnum toState,
                        WmCanteenStallClueBindStatusMachineEvent event,
                        WmCanteenStallClueBindStatusMachineContext context,
                        WmCanteenStallClueBindStatusMachine stateMachine) {
        log.info("[WmCanteenStallClueBindFailAction.execute] input param: fromState = {}, toState = {}, event = {}, context = {}, stateMachine = {}",
                fromState, toState, event, context, stateMachine);
        WmCanteenStallClueBindBO clueBindBO = context.getClueBindBO();
        try {
            wmCanteenStallClueTaskService.bindClueFail(clueBindBO);
        } catch (Exception e) {
            log.error("[WmCanteenStallClueBindFailAction.execute] WmSchCantException. clueBindBO = {}", JSONObject.toJSONString(clueBindBO), e);
            throw new WmScStatusMachineException("食堂档口线索绑定失败异常");
        }
    }

}