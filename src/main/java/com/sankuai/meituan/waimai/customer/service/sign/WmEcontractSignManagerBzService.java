package com.sankuai.meituan.waimai.customer.service.sign;

import static com.sankuai.meituan.waimai.customer.util.trans.WmEcontractBatchTransUtil.transBatchDBList2SimpleBatchBoList;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.SignPackStatusConstant;
import com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.WmContractForSwitchService;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignPackDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam;
import com.sankuai.meituan.waimai.customer.domain.SignTaskQueryParam;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractDelayProcessDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.domain.contract.SignContractTaskParam;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.WmEcontractCallbackService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.state.WmEcontractBatchStateService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.state.WmEcontractPoiStateService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.state.WmEcontractTaskStateService;
import com.sankuai.meituan.waimai.customer.service.sign.delayProcess.WmEcontractDelayProcessService;
import com.sankuai.meituan.waimai.customer.service.sign.pack.check.WmEcontractPackTaskCheckService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.customer.util.WmEcontractSignBatchDBUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.business.WmCustomerUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmEcontractBatchTransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmEcontractDelayProcessTransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmEcontractManualTaskTransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmEcontractTaskTransUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class WmEcontractSignManagerBzService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractSignManagerBzService.class);

    private final static ExecutorService handleService = new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(10, 200,
            0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue(1024), new ThreadPoolExecutor.CallerRunsPolicy()));

    @Resource
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    @Resource
    private WmEcontractCallbackService wmEcontractCallbackService;

    @Resource
    private WmEcontractTaskStateService wmEcontractTaskStateService;

    @Resource
    private WmEcontractPoiStateService wmEcontractPoiStateService;

    @Resource
    private WmEcontractBatchStateService wmEcontractBatchStateService;

    @Resource
    private WmEcontractTaskBizService wmEcontractTaskBizService;

    @Autowired
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;

    @Autowired
    private WmEcontractDelayProcessService wmEcontractDelayProcessService;

    @Autowired
    private WmEcontractPackTaskCheckService wmEcontractPackTaskCheckService;

    @Autowired
    private WmContractForSwitchService wmContractForSwitchService;

    @Autowired
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;

    @Autowired
    private WmEcontractSignPackDBMapper wmEcontractSignPackDBMapper;

    @Resource
    private WmEcontractTaskDBMapper wmEcontractTaskDBMapper;

    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Autowired
    private WmEmployeeService wmEmployeeService;

    /**
     * 电子合同平台回调处理
     */
    public BooleanResult handleCallBack(EcontractNotifyBo notifyBo) throws TException, WmCustomerException {
        LOGGER.info("callback notifyBo = {}", JSON.toJSONString(notifyBo));
        // 判断是否主动取消
        String toState = wmEcontractPoiStateService.calToState(notifyBo);
        if (EcontractTaskStateEnum.CANCEL.getName().equals(toState)) {
            return new BooleanResult(Boolean.TRUE);//主动取消已处理
        }

        // 封装签约batch信息
        WmEcontractSignBatchDB batchDB = wmEcontractBatchBaseService.queryByRecordKey(notifyBo.getRecordKey());
        EcontractBatchContextBo batchContextBo = JSON.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
        batchContextBo.setBatchId(batchDB.getId());

        //新打包结构如果已经fail，则不处理
        if(isPackFail(batchDB)){
            return new BooleanResult(Boolean.TRUE);
        }

        //更新task信息
        wmEcontractTaskStateService.changeState(batchContextBo, notifyBo);
        //更新当前poistate信息
        wmEcontractPoiStateService.changeState(batchContextBo, notifyBo);
        //更新batch信息
        wmEcontractBatchStateService.changeState(batchContextBo, notifyBo);
        //batchContext中维护batch信息
        updateBatchPdfInfo(notifyBo,batchContextBo);
        //更新pack信息
        wmEcontractSignPackService.updateSignPackToEndStatus(batchDB, notifyBo);
        //新打包签约后续流程消息驱动
        if(batchDB.getPackId() > 0){
            //终态时，各模块状态会有延迟
            return new BooleanResult(true);
        }else{
            //兼容客户切换不下线
            wmContractForSwitchService.handleEContractSigned(batchContextBo, notifyBo);
            //回调业务系统
            return new BooleanResult(wmEcontractCallbackService.handleCallback(batchContextBo, notifyBo));
        }
    }

    private boolean isPackFail(WmEcontractSignBatchDB batchDB){
        boolean isPackFail = false;
        Long packId = batchDB.getPackId();
        if(packId > 0){
            WmEcontractSignPackDB wmEcontractSignPackDB = wmEcontractSignPackDBMapper.selectByPrimaryKey(packId);
            if(wmEcontractSignPackDB.getStatus().equals(SignPackStatusConstant.FAIL)){
                isPackFail = true;
                LOGGER.info("pack状态更新已更新为fail，不再处理同pack下其他batch的失败回调请求，packId:{}，batchId:{}", packId, batchDB.getId());
            }
        }
        return isPackFail;
    }

    private void updateBatchPdfInfo(EcontractNotifyBo notifyBo, EcontractBatchContextBo batchContextBo) {
        if (StringUtils.isNotEmpty(notifyBo.getDownLoadUrl()) && !notifyBo.getDownLoadUrl()
                .equals(batchContextBo.getDownLoadUrl())) {
            batchContextBo.setDownLoadUrl(notifyBo.getDownLoadUrl());
            wmEcontractBatchBaseService.updateBatchContext(batchContextBo.getBatchId(),
                    JSONObject.toJSONString(batchContextBo));
        }
    }


    /**
     * 条件查询batch列表信息
     * @param thriftParam
     * @return
     */
    public List<WmEcontractSignBatchBo> queryBatchBo(SignBatchQueryThriftParam thriftParam) throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(thriftParam);
        AssertUtil.assertIntegerMoreThan0(thriftParam.getWmCustomerId(), "客户ID");
        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = Lists.newArrayList();
        if (!ConfigUtilAdapter.getBoolean("signTask_queryBatchBo_withConfig", false)) {
            wmEcontractSignBatchDBList = wmEcontractBatchBaseService.queryWithParam(genQueryParam(thriftParam));
        } else {
            wmEcontractSignBatchDBList = wmEcontractBatchBaseService.queryWithParamWithConfigCatchException(genQueryParam(thriftParam));
        }
        return WmEcontractBatchTransUtil.transBatchDBList2BatchBoList(wmEcontractSignBatchDBList);
    }

    /**
     * 条件查询batch列表信息(不考虑pack的情况)
     * @param thriftParam
     * @return
     */
    public List<WmEcontractSignBatchBo> queryBatchBoWithoutPackByParam(SignBatchQueryThriftParam thriftParam) throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(thriftParam);
        AssertUtil.assertIntegerMoreThan0(thriftParam.getWmCustomerId(), "客户ID");
        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = Lists.newArrayList();
        if (!ConfigUtilAdapter.getBoolean("signTask_queryBatchBo_withConfig", false)) {
            wmEcontractSignBatchDBList = wmEcontractBatchBaseService.queryWithParamWithoutPack(genQueryParam(thriftParam));
        } else {
            wmEcontractSignBatchDBList = wmEcontractBatchBaseService.queryWithParamWithConfigCatchExceptionWithoutPack(genQueryParam(thriftParam));
        }
        return WmEcontractBatchTransUtil.transBatchDBList2BatchBoList(wmEcontractSignBatchDBList);
    }

    /**
     * 条件查询batch列表信息
     * @param thriftParam
     * @return
     */
    public List<WmEcontractSignBatchBo> queryBatchBoWithPackByParam(SignBatchQueryThriftParam thriftParam) throws WmCustomerException {
        try {
            LOGGER.info("queryBatchBoWithPackByParam thriftParam:{}", JSON.toJSONString(thriftParam));
            AssertUtil.assertObjectNotNull(thriftParam);
            AssertUtil.assertIntegerMoreThan0(thriftParam.getWmCustomerId(), "客户ID");
            List<WmEcontractSignBatchBo> signBatchBoList = new ArrayList<>();
            // 获取packId列表
            List<Long> originPackIdList = wmEcontractBatchBaseService.queryPackIdsWithParam(genQueryParam(thriftParam));
            if (CollectionUtils.isEmpty(originPackIdList)) {
                return signBatchBoList;
            }
            // 过滤未完成打包的packId
            List<Integer> packStatusList = new ArrayList<>(Arrays.asList(SignPackStatusConstant.ALL_APPLY,
                    SignPackStatusConstant.SUCCESS, SignPackStatusConstant.FAIL, SignPackStatusConstant.CANCEL));
            List<Long> packIdList = wmEcontractSignPackDBMapper.queryPackIdByStatusList(originPackIdList, packStatusList);

            LOGGER.info("queryBatchBoWithPackByParam packIdList:{}", packIdList);
            if (CollectionUtils.isEmpty(packIdList)) {
                return signBatchBoList;
            }
            // 多线程获取各个packId对应的batch列表
            List<Future> futureList = Lists.newArrayList();
            for (final Long packId : packIdList) {
                futureList.add(handleService.submit(new Callable<WmEcontractSignBatchBo>() {
                    @Override
                    public WmEcontractSignBatchBo call() throws Exception {
                        List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.querySignBatchListByPackId(packId);

                        if (WmEcontractSignBatchDBUtil.isNeedSortByPriority(signBatchDBList)) {
                            List<WmEcontractSignBatchDB>  signBatchDBListSortByPriority = WmEcontractSignBatchDBUtil.sortByPriority(signBatchDBList);
                            return assemblyBatchBo(signBatchDBListSortByPriority);
                        }
                        return assemblyBatchBo(signBatchDBList);
                    }
                }));
            }
            // 等待线程返回
            for (Future future : futureList) {
                signBatchBoList.add((WmEcontractSignBatchBo) future.get(5, TimeUnit.SECONDS));
            }
            LOGGER.info("queryBatchBoWithPackByParam signBatchBoList:{}", JSON.toJSONString(signBatchBoList));
            return signBatchBoList;
        } catch (Exception e) {
            LOGGER.error("queryBatchBoWithPackByParam thriftParam:{}", JSON.toJSONString(thriftParam), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "获取新打包流程下的任务异常，请重试");
        }
    }

    /**
     * 根据packId查询batch信息
     * @param packId
     * @return
     */
    public WmEcontractSignBatchBo queryBatchBoByPackId(Long packId) throws WmCustomerException, TException {
        LOGGER.info("WmEcontractSignManagerBzService#queryBatchBoByPackId packId:{}", packId);
        WmEcontractSignPackDB packDB = wmEcontractSignPackService.querySignPackById(packId);
        LOGGER.info("WmEcontractSignManagerBzService#queryBatchBoByPackId packDB:{}", JSON.toJSONString(packDB));
        List<Integer> packStatusList = new ArrayList<>(Arrays.asList(SignPackStatusConstant.ALL_APPLY, SignPackStatusConstant.SUCCESS, SignPackStatusConstant.FAIL, SignPackStatusConstant.CANCEL));
        if (!packStatusList.contains(packDB.getStatus())) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该打包任务的子任务未全部发起");
        }
        List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.querySignBatchListByPackId(packId);
        return assemblyBatchBo(signBatchDBList);
    }

    public List<WmEcontractSignTaskBo> queryTaskBo(SignBatchQueryThriftParam thriftParam)throws TException, WmCustomerException{
        AssertUtil.assertObjectNotNull(thriftParam);
        AssertUtil.assertIntegerMoreThan0(thriftParam.getWmCustomerId(), "客户ID");

        List<WmEcontractSignTaskDB> taskDBList = wmEcontractTaskBizService.queryWithParam(genQueryParam(thriftParam));
        return WmEcontractTaskTransUtil.transTaskDBList2TaskBoList(taskDBList);
    }

    private SignBatchQueryParam genQueryParam(SignBatchQueryThriftParam thriftParam){
        SignBatchQueryParam param = new SignBatchQueryParam();

        param.setCustomerId(thriftParam.getWmCustomerId());

        if(StringUtils.isNotEmpty(thriftParam.getBatchState())){
            param.setBatchState(thriftParam.getBatchState());
        }

        Integer commitUid = thriftParam.getCommitUid();
        if(commitUid!=null && commitUid>0){
            param.setCommitUid(commitUid);
        }

        if (thriftParam.getStartTime() > 0 || thriftParam.getEndTime() > 0) {
            param.setStartTime(thriftParam.getStartTime());
            param.setEndTime(
                thriftParam.getEndTime() == 0 ? Integer.MAX_VALUE : thriftParam.getEndTime());
        }
        return param;
    }

    public List<WmEcontractSignManualTaskBo> queryManualTaskBo(
            SignBatchQueryThriftParam thriftParam) throws TException, WmCustomerException{
        AssertUtil.assertObjectNotNull(thriftParam);
        AssertUtil.assertIntegerMoreThan0(thriftParam.getWmCustomerId(), "客户ID");
        List<WmEcontractSignManualTaskDB> manualTaskDBList = wmEcontractManualTaskBizService
                .queryWithParam(genQueryParam(thriftParam));
        return WmEcontractManualTaskTransUtil.transManualTaskDBList2ManualTaskBoList(manualTaskDBList);
    }

    public List<Long> queryBatchBoIdList(SignBatchQueryThriftParam thriftParam) throws TException, WmCustomerException{
        AssertUtil.assertObjectNotNull(thriftParam);
        AssertUtil.assertIntegerMoreThan0(thriftParam.getWmCustomerId(), "客户ID");
        return wmEcontractBatchBaseService.queryIdWithParam(genQueryParam(thriftParam));
    }

    public List<Long> queryBatchBoIdListWithoutPack(SignBatchQueryThriftParam thriftParam) throws TException, WmCustomerException{
        AssertUtil.assertObjectNotNull(thriftParam);
        AssertUtil.assertIntegerMoreThan0(thriftParam.getWmCustomerId(), "客户ID");
        return wmEcontractBatchBaseService.queryIdWithParamWithoutPack(genQueryParam(thriftParam));
    }

    public List<Long> queryPackIdsWithParam(SignBatchQueryThriftParam thriftParam) throws TException, WmCustomerException{
        AssertUtil.assertObjectNotNull(thriftParam);
        AssertUtil.assertIntegerMoreThan0(thriftParam.getWmCustomerId(), "客户ID");
        return wmEcontractBatchBaseService.queryPackIdsWithParam(genQueryParam(thriftParam));
    }

    public List<WmEcontractSignBatchBo> batchQueryByBatchId(List<Long> batchIdList){
        return WmEcontractBatchTransUtil.transBatchDBList2BatchBoList(wmEcontractBatchBaseService.batchQueryByBatchId(batchIdList));
    }

    public List<WmEcontractSignBatchBo> batchQuerySimpleBatchResultByBatchId(List<Long> batchIdList) {
        return transBatchDBList2SimpleBatchBoList(wmEcontractBatchBaseService.batchQueryByBatchId(batchIdList));
    }

    public List<WmEcontractDelayProcessBo> queryWmEcontractDelayProcessBo(
            EcontractDelayProcessThriftParam delayProcessThriftParam) throws WmCustomerException{
        AssertUtil.assertObjectNotNull(delayProcessThriftParam);
        AssertUtil.assertIntegerMoreThan0(delayProcessThriftParam.getExpireTime());
        AssertUtil.assertStringNotEmpty(delayProcessThriftParam.getBizType(),"业务类型");
        List<WmEcontractDelayProcessDB> wmEcontractDelayProcessDBList = wmEcontractDelayProcessService.queryWmEcontractDelayProcessBo(delayProcessThriftParam);
        return WmEcontractDelayProcessTransUtil.tansDelayProcessDBList2DelayProcessBoList(wmEcontractDelayProcessDBList);
    }

    public BooleanResult updateWmEcontractDelayProcessBo(WmEcontractDelayProcessBo delayProcessBo) throws WmCustomerException{
        AssertUtil.assertObjectNotNull(delayProcessBo);
        AssertUtil.assertLongMoreThan0(delayProcessBo.getId(),"延迟任务处理ID");
        return wmEcontractDelayProcessService.updateWmEcontractDelayProcessBo(delayProcessBo);
    }

    public BooleanResult queryZRZFirstOrder(SignBatchQueryThriftParam signBatchQueryParam) throws TException, WmCustomerException {
        LOGGER.info("queryZRZFirstOrder signBatchQueryParam:{}", JSON.toJSONString(signBatchQueryParam));

        // 参数校验
        AssertUtil.assertObjectNotNull(signBatchQueryParam);
        AssertUtil.assertIntegerMoreThan0(signBatchQueryParam.getWmCustomerId(), "客户ID");

        // 获取是否存在有效数据
        boolean hasZRZFirstEffectDataBoolean = wmEcontractPackTaskCheckService.hasZRZFirstEffectData(signBatchQueryParam.getWmCustomerId());
        if (hasZRZFirstEffectDataBoolean) {
            return new BooleanResult(false);
        }

        // 获取对应batch任务(不考虑新打包流程)
        List<WmEcontractSignBatchBo> wmEcontractSignBatchBoListWithoutPack = queryBatchBoWithoutPackByParam(signBatchQueryParam);
        if (!CollectionUtils.isEmpty(wmEcontractSignBatchBoListWithoutPack)) {
            for (WmEcontractSignBatchBo wmEcontractSignBatchBo : wmEcontractSignBatchBoListWithoutPack) {
                if (EcontractBatchStateEnum.IN_PROCESSING.getName().equalsIgnoreCase(wmEcontractSignBatchBo.getBatchState())) {
                    EcontractBatchContextBo econtractBatchContextBo = JSON.parseObject(wmEcontractSignBatchBo.getBatchContext(), EcontractBatchContextBo.class);
                    // 打包且包含C1和配送
                    if (EcontractBatchTypeEnum.BATCH_C1C2D.equals(econtractBatchContextBo.getBatchTypeEnum())
                            || EcontractBatchTypeEnum.BATCH_CD.equals(econtractBatchContextBo.getBatchTypeEnum())
                            || EcontractBatchTypeEnum.BATCH_CDH.equals(econtractBatchContextBo.getBatchTypeEnum())
                            || EcontractBatchTypeEnum.BATCH_C1C2DH.equals(econtractBatchContextBo.getBatchTypeEnum())) {
                        return new BooleanResult(true);
                    }
                }
            }
        }

        // 获取对应batch任务(新打包流程)
        List<WmEcontractSignBatchBo> wmEcontractSignBatchBoListWithPack = queryBatchBoWithPackByParam(signBatchQueryParam);
        if (!CollectionUtils.isEmpty(wmEcontractSignBatchBoListWithPack)) {
            for (WmEcontractSignBatchBo wmEcontractSignBatchBo : wmEcontractSignBatchBoListWithPack) {
                if (EcontractBatchStateEnum.IN_PROCESSING.getName().equalsIgnoreCase(wmEcontractSignBatchBo.getBatchState())) {
                    boolean isContainC1 = false;
                    boolean isContainDelivery = false;
                    EcontractBatchContextBo subContextBo = null;
                    for (Map.Entry<Long, String> entry : wmEcontractSignBatchBo.getSubBatchContextMap().entrySet()) {
                        subContextBo = JSON.parseObject(entry.getValue(), EcontractBatchContextBo.class);
                        if (EcontractBatchTypeEnum.C1_CONTRACT.equals(subContextBo.getBatchTypeEnum())) {
                            isContainC1 = true;
                        } else if (EcontractBatchTypeEnum.DELIVERY.equals(subContextBo.getBatchTypeEnum()) ||
                                EcontractBatchTypeEnum.BATCH_DELIVERY.equals(subContextBo.getBatchTypeEnum())) {
                            isContainDelivery = true;
                        }
                    }
                    // 打包且包含C1和配送
                    if (isContainC1 && isContainDelivery) {
                        return new BooleanResult(true);
                    }
                }
            }
        }
        return new BooleanResult(false);
    }

    public BooleanResult resetBatchState(long batchId, String state) throws WmCustomerException {
        AssertUtil.assertLongMoreThan0(batchId,"batchId");
        WmEcontractSignBatchDB signBatchDB = wmEcontractBatchBaseService.queryByBatchId(batchId);
        if (signBatchDB == null) {// 无对应记录
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "未查询到记录");
        }
        if (signBatchDB.getPackId() > 0) {// 新打包流程
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该任务和其他任务绑定，请到任务列表进行取消");
        }
        wmEcontractBatchBaseService.updateState(batchId,state);
        return new BooleanResult(true);
    }

    /**
     * 获取超时未签约的batch任务
     *
     * @param queryParam
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public List<WmEcontractSignBatchBo> queryOverTimeBatchBo(SignBatchOverTimeQueryThriftParam queryParam)
        throws TException, WmCustomerException {
        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = wmEcontractBatchBaseService.queryOverTimeBatch(
            queryParam);
        return WmEcontractBatchTransUtil.transBatchDBList2BatchBoList(wmEcontractSignBatchDBList);
    }

    /**
     * 获取超时未签约的batch任务，旧打包，走ctime索引
     *
     * @param queryParam 查询参数
     * @return 返回数据集
     * @throws TException          TException异常
     * @throws WmCustomerException WmCustomerException异常
     */
    public List<WmEcontractSignBatchBo> queryOverTimeBatchBoViaCtime(SignBatchOverTimeQueryThriftParam queryParam)
        throws TException, WmCustomerException {
        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = wmEcontractBatchBaseService.queryOverTimeBatchViaCtime(queryParam);
        return WmEcontractBatchTransUtil.transBatchDBList2BatchBoList(wmEcontractSignBatchDBList);
    }

    /**
     * 获取超时未签约的batch任务(不考虑新流程，即pack为0)
     *
     * @param queryParam
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public List<WmEcontractSignBatchBo> queryOverTimeBatchBoWithoutPack(SignBatchOverTimeQueryThriftParam queryParam)
        throws TException, WmCustomerException {
        LOGGER.info("queryOverTimeBatchBoWithoutPack queryParam:{}", JSON.toJSONString(queryParam));
        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = wmEcontractBatchBaseService.queryOverTimeBatchWithoutPack(
            queryParam);
        List<WmEcontractSignBatchBo> finalSignBatchBoList = WmEcontractBatchTransUtil.transBatchDBList2BatchBoList(
            wmEcontractSignBatchDBList);
        LOGGER.info("queryOverTimeBatchBoWithoutPack queryParam:{} result:{}",
            JSON.toJSONString(queryParam),
            JSON.toJSON(finalSignBatchBoList));
        return finalSignBatchBoList;
    }

    /**
     * 获取超时未签约的batch任务(新流程，即pack不为0)
     *
     * @param queryParam
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public List<WmEcontractSignBatchBo> queryOverTimeBatchBoWithPack(SignBatchOverTimeQueryThriftParam queryParam) throws TException, WmCustomerException {
        LOGGER.info("queryOverTimeBatchBoWithPack queryParam:{}", JSON.toJSONString(queryParam));
        List<WmEcontractSignBatchBo> finalSignBatchBoList = new ArrayList<>();
        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = wmEcontractBatchBaseService.queryOverTimeBatchWithPack(queryParam);
        if (CollectionUtils.isEmpty(wmEcontractSignBatchDBList)) {
            return finalSignBatchBoList;
        }
        Map<Long, List<WmEcontractSignBatchDB>> batchDBMap = wmEcontractSignBatchDBList.stream().collect(Collectors.groupingBy(e -> e.getPackId()));
        for (Map.Entry<Long, List<WmEcontractSignBatchDB>> entry : batchDBMap.entrySet()) {
            finalSignBatchBoList.add(assemblyBatchBo(entry.getValue()));
        }
        LOGGER.info("queryOverTimeBatchBoWithPack queryParam:{} result:{}", JSON.toJSONString(queryParam), JSON.toJSON(finalSignBatchBoList));
        return finalSignBatchBoList;
    }


    /**
     * 获取超时未签约的待签约任务
     *
     * @param queryParam
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public List<WmEcontractSignManualTaskBo> queryOverTimeManualTaskBo(SignBatchOverTimeQueryThriftParam queryParam) throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(queryParam);
        List<WmEcontractSignManualTaskDB> manualTaskDBList = wmEcontractManualTaskBizService.queryOverTimeTaskWithParam(queryParam);
        return WmEcontractManualTaskTransUtil.transManualTaskDBList2ManualTaskBoList(manualTaskDBList);
    }

    private WmEcontractSignBatchBo assemblyBatchBo(List<WmEcontractSignBatchDB> batchDBList) {
        WmEcontractSignBatchBo batchBo = new WmEcontractSignBatchBo();

        batchBo.setCustomerId(batchDBList.get(0).getCustomerId());
        batchBo.setBatchId(batchDBList.get(0).getId().intValue());
        batchBo.setBatchState(batchDBList.get(0).getBatchState());
        batchBo.setCommitUid(batchDBList.get(0).getCommitUid());
        batchBo.setPackId(batchDBList.get(0).getPackId());
        batchBo.setCtime(batchDBList.get(0).getCtime());
        batchBo.setUtime(batchDBList.get(0).getUtime());

        EcontractBatchContextBo finalContextBo = JSONObject.parseObject(batchDBList.get(0).getBatchContext(), EcontractBatchContextBo.class);
        finalContextBo.setBatchTypeEnum(finalContextBo.getBatchTypeEnum());
        batchBo.setBatchContext(JSON.toJSONString(finalContextBo));

        Map<Long, String> subBatchContextMap = new HashMap<>();
        for (WmEcontractSignBatchDB batchDB : batchDBList) {
            EcontractBatchContextBo subContextBo = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
            for (Map.Entry<Long, EcontractTaskBo> entry : subContextBo.getTaskIdAndTaskMap().entrySet()) {
                if (entry.getValue().getApplyType().equals(EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT.getName())) {
                    EcontractBusinessCustomerInfoBo businessCustomerInfoBo =  JSONObject.parseObject(entry.getValue().getApplyContext(), EcontractBusinessCustomerInfoBo.class);
                    businessCustomerInfoBo.setWmCompanyCustomerContractBo(null);
                    entry.getValue().setApplyContext(JSON.toJSONString(businessCustomerInfoBo));
                }
                if (entry.getValue().getApplyType().equals(EcontractTaskApplyTypeEnum.SETTLE.getName()) ||
                        entry.getValue().getApplyType().equals(EcontractTaskApplyTypeEnum.POIFEE.getName()) ||
                        entry.getValue().getApplyType().equals(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName())) {
                    entry.getValue().setApplyContext("");
                }
            }
            subBatchContextMap.put(batchDB.getId(), batchDB.getBatchContext());
        }
        batchBo.setSubBatchContextMap(subBatchContextMap);
        LOGGER.info("WmEcontractSignManagerBzService#assembly, batchBo: {}", JSON.toJSON(batchBo));
        return batchBo;
    }


    public List<WmEcontractSignTaskBo> querySignTaskBoByIds(List<Long> idList) {
        LOGGER.info("querySignTaskBoByIds#idList={}", JSON.toJSON(idList));
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        List<WmEcontractSignTaskDB> signTaskDBList = wmEcontractTaskDBMapper.getByIdList(idList);
        return WmEcontractTaskTransUtil.transTaskDBList2TaskBoList(signTaskDBList);
    }

    public List<WmEcontractSignTaskBo> querySignTaskByParam(SignTaskQueryThriftParam param) {
        LOGGER.info("querySignTaskByParam#param={}", JSON.toJSONString(param));
        if (param == null) {
            return Lists.newArrayList();
        }
        if (param.getStartTime() <= 0 || param.getEndTime() <= 0 || param.getStartTime() >= param.getEndTime()) {
            return Lists.newArrayList();
        }
        if (StringUtils.isEmpty(param.getApplyState())) {
            return Lists.newArrayList();
        }
        if (param.getPageSize() <= 0) {
            return Lists.newArrayList();
        }
        List<WmEcontractSignTaskDB> signTaskDBList = wmEcontractTaskDBMapper.getBySignTaskQueryThriftParam(param);
        return WmEcontractTaskTransUtil.transTaskDBList2TaskBoList(signTaskDBList);
    }

    public LongResult addSettleSignInfoForNvwaV2(SettleSignInfoParam param) {
        LOGGER.info("#addSettleSignInfoForNvwaV2,param={}",JSONObject.toJSONString(param));
        WmEcontractSignBatchDB batchDB = new WmEcontractSignBatchDB();
        //通过mis反查uid并补全数据
        if(StringUtils.isNotEmpty(param.getCommitMisId()) && param.getCommitUid() <= 0){
            int uid = wmEmployeeService.getUid(param.getCommitMisId());
            param.setCommitUid(uid);
        }
        batchDB.setCustomerId(param.getWmCustomerId());
        batchDB.setBatchState(EcontractBatchStateEnum.IN_PROCESSING.getName());
        batchDB.setCommitUid(param.getCommitUid());
        batchDB.setCtime(param.getCtime());
        batchDB.setUtime(param.getUtime());

        EcontractBatchContextBo.Builder batchContextBuilder = new EcontractBatchContextBo.Builder();
        batchContextBuilder.customerId(param.getWmCustomerId());
        batchContextBuilder.bizId(param.getSettleId());
        batchContextBuilder.wmPoiIdList(param.getWmPoiIdList());

        EcontractCustomerKPBo.Builder kpBuilder = new EcontractCustomerKPBo.Builder();
        kpBuilder.signerName(param.getSignerName());
        kpBuilder.signerPhoneNum(param.getSignerPhoneNum());
        batchContextBuilder.kpBo(kpBuilder.build());

        batchContextBuilder.batchTypeEnum(EcontractBatchTypeEnum.SETTLE_NEW);
        batchContextBuilder.commitUid(param.getCommitUid());
        batchDB.setBatchContext(JSONObject.toJSONString(batchContextBuilder.build()));
        batchDB.setRecordKey(StringUtils.EMPTY);

        LOGGER.warn("batchDB={}",JSONObject.toJSONString(batchDB));
        long batchId = wmEcontractBatchBaseService.insert(batchDB);
        LOGGER.info("batchId={}",batchId);
        return new LongResult(batchId);
    }

    public BooleanResult updateSettleSignInfoForNvwaV2(SettleSignInfoParam param) throws WmCustomerException{
        LOGGER.info("#updateSettleSignInfoForNvwaV2,param={}",JSONObject.toJSONString(param));
        //check记录为女娲结算签约任务
        WmEcontractSignBatchDB record = wmEcontractBatchBaseService.queryByBatchId(param.getBatchId());
        if(record == null){
            record = wmEcontractBatchBaseService.queryByBatchIdFromMaster(param.getBatchId());
        }
        if(record == null){
            return new BooleanResult(false);
        }
        if (StringUtils.isNotBlank(record.getBatchContext())) {
            EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(record.getBatchContext(),
                EcontractBatchContextBo.class);
            EcontractBatchTypeEnum batchTypeEnum = econtractBatchContextBo.getBatchTypeEnum();
            if (batchTypeEnum != EcontractBatchTypeEnum.SETTLE_NEW) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "不支持的任务类型");
            }
            if (EcontractBatchStateEnum.CANCEL.getName().equals(param.getBatchState()) ||
                EcontractBatchStateEnum.FAIL.getName().equals(param.getBatchState())) {
                econtractBatchContextBo.setFailReason(param.getFailReason());
                //updateStatusWithContext
                record.setBatchState(param.getBatchState());
                record.setUtime(param.getUtime());
                record.setBatchContext(JSONObject.toJSONString(econtractBatchContextBo));
                LOGGER.info("#updateStatusWithContext,record={}", JSONObject.toJSONString(record));
                return new BooleanResult(wmEcontractBatchBaseService.updateStatusWithContext(record) > 0);
            } else if (EcontractBatchStateEnum.SUCCESS.getName().equals(param.getBatchState())) {
                //updateStatus
                record.setBatchState(param.getBatchState());
                record.setUtime(param.getUtime());
                LOGGER.info("#updateStatusForSettleNew,record={}", JSONObject.toJSONString(record));
                return new BooleanResult(wmEcontractBatchBaseService.updateStatusForSettleNew(record) > 0);
            }
        } else {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "不支持的任务类型");
        }
        return new BooleanResult(false);
    }

    public List<WmEcontractSignTaskDB> listProcessingTask(SignTaskQueryParam signTaskQueryParam) {
        LOGGER.info("#listProcessingTask,signTaskQueryParam={}", JSONObject.toJSONString(signTaskQueryParam));
        List<WmEcontractSignTaskDB> wmEcontractSignTaskDBList =
            wmEcontractTaskDBMapper.pagedListTask(signTaskQueryParam);
        LOGGER.info("#listProcessingTask,wmEcontractSignTaskDBList={}",
            JSONObject.toJSONString(wmEcontractSignTaskDBList));
        return wmEcontractSignTaskDBList;
    }

    public List<WmEcontractSignBatchBo> querySignBatchTask(SignContractTaskParam signedContractTaskParam)
        throws ExecutionException, InterruptedException, TimeoutException {

        List<WmEcontractSignBatchDB> wmEcontractSignBatchList =
            wmEcontractBigBatchParseService.querySignBatchTask(signedContractTaskParam);
        // transfer PO to BO
        List<WmEcontractSignBatchDB> wmEcontractSimpleSignBatchList =
            wmEcontractSignBatchList.stream().filter(m -> m.getPackId() == 0).collect(Collectors.toList());
        List<WmEcontractSignBatchBo> wmEcontractSignBatchBoList =
            WmEcontractBatchTransUtil.transBatchDBList2SimpleBatchBoList(Optional.ofNullable(
                wmEcontractSimpleSignBatchList).orElse(Lists.newArrayList()));
        List<Long> originPackIdList =
            wmEcontractSignBatchList.stream().filter(m -> m.getPackId() > 0).map(WmEcontractSignBatchDB::getPackId
            ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(originPackIdList)) {
            return wmEcontractSignBatchBoList;
        }
        List<WmEcontractSignBatchBo> signBatchBoList = packBatchTaskQuery(originPackIdList);
        if (!CollectionUtils.isEmpty(signBatchBoList)) {
            wmEcontractSignBatchBoList.addAll(signBatchBoList);
        }
        return wmEcontractSignBatchBoList;
    }

    private List<WmEcontractSignBatchBo> packBatchTaskQuery(List<Long> originPackIdList) throws InterruptedException,
        ExecutionException, TimeoutException {
        // 过滤未完成打包的packId
        List<Integer> packStatusList = Lists.newArrayList(SignPackStatusConstant.SUCCESS);
        List<Long> packIdList = wmEcontractSignPackDBMapper.queryPackIdByStatusList(originPackIdList, packStatusList);
        LOGGER.info("queryPackIdByStatusList packIdList:{}", packIdList);
        // 多线程获取各个packId对应的batch列表
        List<Future> futureArrayList = Lists.newArrayList();
        for (final Long packId : packIdList) {
            futureArrayList.add(handleService.submit(new Callable<WmEcontractSignBatchBo>() {
                @Override
                public WmEcontractSignBatchBo call() throws Exception {
                    List<WmEcontractSignBatchDB> signBatchList = wmEcontractBigBatchParseService.querySignBatchListByPackId(
                        packId);
                    return assemblyBatchBo(signBatchList);
                }
            }));
        }
        List<WmEcontractSignBatchBo> signBatchBoList = new ArrayList<>();
        // 等待线程返回
        for (Future future : futureArrayList) {
            signBatchBoList.add((WmEcontractSignBatchBo) future.get(10, TimeUnit.SECONDS));
        }
        return signBatchBoList;
    }
}
