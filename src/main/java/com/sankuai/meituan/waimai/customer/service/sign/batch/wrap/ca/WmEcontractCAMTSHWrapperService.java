package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.CertifyConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import org.springframework.stereotype.Service;

@Service
public class WmEcontractCAMTSHWrapperService implements IWmEcontractCAWrapperService {

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo) {
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
                .setCustomerId(WmEcontractConstant.CERTIFY_CUSTOMERID_MTSH)
                .build();

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.CA_MTSH)
                .certifyInfoBo(certifyInfoBo)
                .build();
    }
}
