package com.sankuai.meituan.waimai.customer.service.common;


import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceSection;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceStatus;
import com.sankuai.meituan.waimai.poiaudit.thrift.domain.WmPoiProduceOplog;
import com.sankuai.meituan.waimai.poiaudit.thrift.exception.WmPoiAuditException;
import com.sankuai.meituan.waimai.poiaudit.thrift.service.WmPoiProduceOplogThriftService;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProduceNotifyService {

    private static Logger logger = LoggerFactory.getLogger(ProduceNotifyService.class);

    @Autowired
    private WmPoiProduceOplogThriftService.Iface wmPoiProduceOplogThriftService;

    public static final int SETTLEMENT = WmPoiProduceSection.SETTLEMENT;

    public static final int INSERT = 0;
    public static final int COMMIT_AUDIT = 1;
    public static final int PASS = 2;
    public static final int REJECT = 3;
    public static final int DELETE = 4;

    public static final List<String> MESSAGE_TO = Lists.newArrayList("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>");

    private static final int BASE_CODE = 10;


    public int getBaseCode(){
        return BASE_CODE;
    }

    public void notifyInsert(long wmPoiId, int type, int opUid, String opUname,byte source) {
        int opCode = type * getBaseCode() + INSERT;
        oplogNotify(wmPoiId, opCode, opUid, opUname,source);
    }
    
    public void notifyInsert(long wmPoiId, int type, int opUid, String opUname) {
        int opCode = type * getBaseCode() + INSERT;
        oplogNotify(wmPoiId, opCode, opUid, opUname);
    }

    public void notifyCommitAudit(long wmPoiId, int type, int opUid, String opUname) {
        int opCode = type * getBaseCode() + COMMIT_AUDIT;
        oplogNotify(wmPoiId, opCode, opUid, opUname);
    }
    
    public void notifyPass(long wmPoiId, int type, int opUid, String opUname) {
        int opCode = type * getBaseCode() + PASS;
        oplogNotify(wmPoiId, opCode, opUid, opUname);
    }

    public void notifyInsertWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.INPUT,opUid,opUname,remark);
    }

    public void notifyToConfirmingWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.CONFIRMING,opUid,opUname,remark);
    }

    public void notifyEffectWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.CONFIRM_OK,opUid,opUname,remark);
    }

    public void notifyCancelWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.CONFIRM_NO,opUid,opUname,remark);
    }

    public void notifyDeleteWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.DELETED,opUid,opUname,remark);
    }

    public void notifyReject(long wmPoiId, int type, int opUid, String opUname) {
        int opCode = type * getBaseCode() + REJECT;
        oplogNotify(wmPoiId, opCode, opUid, opUname);
    }

    private void oplogNotify(long wmPoiId, int opCode, int opUid, String opUname) {
        logger.info("发送流程数据 wmPoiId={}, opCode={}", wmPoiId, opCode);
        WmPoiProduceOplog wmPoiProduceOplog = new WmPoiProduceOplog();
        wmPoiProduceOplog.setWm_poi_id((int) wmPoiId);
        wmPoiProduceOplog.setOp_type(opCode);
        wmPoiProduceOplog.setOp_uid(opUid);
        wmPoiProduceOplog.setOp_uname(opUname);

        boolean success = false;
        for (int cnt = 0; cnt < 10; ++cnt) {
            if (success) {
                break;
            } else {
                try {
                    wmPoiProduceOplogThriftService.recordOplog(wmPoiProduceOplog);
                    success = true;
                } catch (WmPoiAuditException e) {
                    logger.warn("该异常为正常现象", e);
                    success = true;
                } catch (TException e) {
                    logger.warn("服务化链接异常", e);
                }
            }
        }

        if (!success) {
            String message = "wmPoiId = " + wmPoiId + ", opCode = " + opCode + " 写入produce异常";
            DaxiangUtil.push("<EMAIL>", message, MESSAGE_TO);
        }
    }
    
    private void oplogNotify(long wmPoiId, int opCode, int opUid, String opUname,byte source) {
        logger.info("发送流程数据 wmPoiId={}, opCode={}", wmPoiId, opCode);
        WmPoiProduceOplog wmPoiProduceOplog = new WmPoiProduceOplog();
        wmPoiProduceOplog.setWm_poi_id((int) wmPoiId);
        wmPoiProduceOplog.setOp_type(opCode);
        wmPoiProduceOplog.setOp_uid(opUid);
        wmPoiProduceOplog.setOp_uname(opUname);
        wmPoiProduceOplog.setSource(source);

        boolean success = false;
        for (int cnt = 0; cnt < 10; ++cnt) {
            if (success) {
                break;
            } else {
                try {
                    wmPoiProduceOplogThriftService.recordOplog(wmPoiProduceOplog);
                    success = true;
                } catch (WmPoiAuditException e) {
                    logger.warn("该异常为正常现象", e);
                    success = true;
                } catch (TException e) {
                    logger.warn("服务化链接异常", e);
                }
            }
        }

        if (!success) {
            String message = "wmPoiId = " + wmPoiId + ", opCode = " + opCode + " 写入produce异常";
            DaxiangUtil.push("<EMAIL>", message, MESSAGE_TO);
        }
    }

    private void oplogNotifyWithRemark(long wmPoiId, int opCode, int opUid, String opUname, String remark) {
        logger.info("发送流程数据 wmPoiId={}, opCode={}", wmPoiId, opCode);
        WmPoiProduceOplog wmPoiProduceOplog = new WmPoiProduceOplog();
        wmPoiProduceOplog.setWm_poi_id((int) wmPoiId);
        wmPoiProduceOplog.setOp_type(opCode);
        wmPoiProduceOplog.setOp_uid(opUid);
        wmPoiProduceOplog.setOp_uname(opUname);
        wmPoiProduceOplog.setRemark(remark);

        boolean success = false;
        for (int cnt = 0; cnt < 10; ++cnt) {
            if (success) {
                break;
            } else {
                try {
                    wmPoiProduceOplogThriftService.recordOplog(wmPoiProduceOplog);
                    success = true;
                } catch (WmPoiAuditException e) {
                    logger.warn("该异常为正常现象 code={}, message = {}", e.getCode(), e.getMsg());
                    logger.warn("", e);
                    success = true;
                } catch (TException e) {
                    logger.warn("服务化链接异常", e);
                }
            }
        }

        if (!success) {
            String message = "wmPoiId = " + wmPoiId + ", opCode = " + opCode + " 写入produce异常";
            DaxiangUtil.push("<EMAIL>", message, MESSAGE_TO);
        }
    }

    private void oplogNotifyWithRemark(long wmPoiId,int section,int opType,int opUid, String opUname, String remark) {
        logger.info("发送流程数据 wmPoiId={}, section={},opType={}", wmPoiId, section,opType);
        WmPoiProduceOplog wmPoiProduceOplog = new WmPoiProduceOplog();
        wmPoiProduceOplog.setWm_poi_id((int) wmPoiId);
        wmPoiProduceOplog.setSection(section);
        wmPoiProduceOplog.setOp_type(opType);
        wmPoiProduceOplog.setOp_uid(opUid);
        wmPoiProduceOplog.setOp_uname(opUname);
        wmPoiProduceOplog.setRemark(remark);

        boolean success = false;
        for (int cnt = 0; cnt < 10; ++cnt) {
            if (success) {
                break;
            } else {
                try {
                    wmPoiProduceOplogThriftService.recordOplog(wmPoiProduceOplog);
                    success = true;
                } catch (WmPoiAuditException e) {
                    logger.warn("该异常为正常现象 code={}, message = {}", e.getCode(), e.getMsg());
                    logger.warn("", e);
                    success = true;
                } catch (TException e) {
                    logger.warn("服务化链接异常", e);
                }
            }
        }
    }


}
