package com.sankuai.meituan.waimai.customer.util;

import com.sankuai.meituan.waimai.thrift.util.DateUtil;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-5-12.
 */
public class WmContractVersionUtil {

    public static String genVersionNumForSettle(int id){
        String number = String.format("%09d", id);
        String unixtimeStr = String.valueOf(DateUtil.unixTime());
        return "s"+number + unixtimeStr;
    }

    public static String genVersionNum(int id) {
        String number = String.format("%08d", id);
        String unixtimeStr = String.valueOf(DateUtil.unixTime());
        return number + unixtimeStr;
    }


    public static void main(String[] args) {
        System.out.println(genVersionNum(122223));
    }

    /**
     * 生成版本编号(合同编号 + unixtime)
     *
     * @param contractNum
     * @return
     */
    public static String genVersionNum(String contractNum) {
        String unixtimeStr = String.valueOf(DateUtil.unixTime());
        String[] strs = (contractNum + unixtimeStr).split("-");
        return strs[strs.length - 1];
    }


    /**
     * 产生流水号(版本编号 + 操作类型(2位) + unixtime)
     *
     * @param versionNum
     * @param type
     * @return
     */
    public static String genDealNum(String versionNum, int type) {
        String typeStr = String.format("%02d", type);
        String unixtimeStr = String.valueOf(DateUtil.unixTime());
        String[] strs = (versionNum + typeStr + unixtimeStr).split("-");
        return strs[strs.length - 1];
    }

}
