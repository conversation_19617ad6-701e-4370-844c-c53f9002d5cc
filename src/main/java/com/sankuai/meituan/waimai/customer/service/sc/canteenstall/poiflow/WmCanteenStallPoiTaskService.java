package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.poiflow;


import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallWmPoiBindBO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiOperateTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 食堂档口门店绑定任务相关服务
 * <AUTHOR>
 * @date 2024/05/29
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallPoiTaskService {

    @Autowired
    private WmCanteenStallPoiLabelService wmCanteenStallPoiLabelService;

    @Autowired
    private WmCanteenStallPoiCanteenSerivice wmCanteenStallPoiCanteenSerivice;


    /**
     * 门店解绑流程(掉标+删除绑定关系)
     * @param wmPoiBindBO 档口绑定BO
     */
    public void unbindCanteenPoi(WmCanteenStallWmPoiBindBO wmPoiBindBO) {
        log.info("[WmCanteenStallPoiTaskService.unbindCanteenPoi] input param: wmPoiBindBO = {}", JSONObject.toJSONString(wmPoiBindBO));
        WmCanteenStallBindDO bindDO = wmPoiBindBO.getBindDO();
        // 1-档口绑定任务外卖门店状态绑定状态更新为"未绑定"
        wmCanteenStallPoiCanteenSerivice.updateWmPoiBindStatusByUnBind(bindDO);

        // 2-查询门店下的所有A类和B类标签(食堂系统)
        List<WmScCanteenPoiLabelDB> poiLabelDBList = wmCanteenStallPoiCanteenSerivice.getLabelListByCanteenPrimaryIdAndWmPoiId(bindDO.getCanteenPrimaryId(), bindDO.getWmPoiId());

        // 3-删除门店上的A类和B类标签(标签系统)
        wmCanteenStallPoiLabelService.deleteCanteenPoiLabel(poiLabelDBList, wmPoiBindBO.getUserId(), wmPoiBindBO.getUserName());

        // 4-删除本地记录的门店标签关系(食堂系统)
        wmCanteenStallPoiCanteenSerivice.deleteCanteenPoiLabel(poiLabelDBList);

        // 5-删除食堂门店关联属性(食堂系统）
        wmCanteenStallPoiCanteenSerivice.deleteCanteenPoiAttribute(bindDO.getCanteenPrimaryId(), bindDO.getWmPoiId());

        // 6-更新食堂合作档口数量(食堂系统)
        wmCanteenStallPoiCanteenSerivice.updateCanteenStoreNum(bindDO.getCanteenPrimaryId());
    }

    /**
     * 门店换绑中对原来门店打标流程，包括打标和添加绑定关系。
     *
     * @param wmPoiBindBO 档口绑定业务对象，包含门店和档口的相关信息
     * @throws WmSchCantException 当操作失败时抛出此异常
     */
    public void transferBindCanteenPoi(WmCanteenStallWmPoiBindBO wmPoiBindBO) throws WmSchCantException {
        log.info("[WmCanteenStallPoiTaskService.transferBindCanteenPoi] input param: wmPoiBindBO = {}", JSONObject.toJSONString(wmPoiBindBO));

        WmCanteenStallBindDO bindDO = wmPoiBindBO.getBindDO();

        // 1. 将档口绑定任务的外卖门店状态更新为"绑定成功"，并同步更新食堂系统中的外卖门店信息
        wmCanteenStallPoiCanteenSerivice.updateWmPoiIdAndWmPoiBindStatusByBindSuccess(wmPoiBindBO);

        // 2. 为门店添加A类和B类标签(标签系统)
        List<Long> labelIdList = wmCanteenStallPoiLabelService.addCanteenPoiLabel(
                bindDO.getCanteenPrimaryId(),
                wmPoiBindBO.getWmPoiId(),
                wmPoiBindBO.getUserId(),
                wmPoiBindBO.getUserName()
        );

        // 3. 记录门店和其A、B类标签的本地关联关系(食堂系统)
        wmCanteenStallPoiCanteenSerivice.addCanteenPoiLabel(
                bindDO.getCanteenPrimaryId(),
                wmPoiBindBO.getWmPoiId(),
                labelIdList
        );

        // 4. 记录本次打标的历史记录到食堂系统
        wmCanteenStallPoiCanteenSerivice.insertCanteenPoiHistory(
                bindDO.getCanteenPrimaryId(),
                wmPoiBindBO.getWmPoiId(),
                wmPoiBindBO.getUserId(),
                wmPoiBindBO.getUserName(),
                CanteenPoiOperateTypeEnum.TRANSFER_BIND.getCode()
        );

        // 5. 添加或更新食堂门店的关联属性
        wmCanteenStallPoiCanteenSerivice.upsertCanteenPoiAttribute(
                bindDO.getCanteenPrimaryId(),
                bindDO.getWmPoiId()
        );

        // 6. 更新食堂合作档口的数量信息
        wmCanteenStallPoiCanteenSerivice.updateCanteenStoreNum(bindDO.getCanteenPrimaryId());
    }


    /**
     * 门店绑定流程(打标+添加绑定关系)
     * @param wmPoiBindBO 档口绑定BO
     */
    public void bindCanteenPoi(WmCanteenStallWmPoiBindBO wmPoiBindBO) throws WmSchCantException {
        log.info("[WmCanteenStallPoiTaskService.bindCanteenPoi] input param: wmPoiBindBO = {}", JSONObject.toJSONString(wmPoiBindBO));
        WmCanteenStallBindDO bindDO = wmPoiBindBO.getBindDO();
        // 1-档口绑定任务外卖门店状态绑定状态更新为"绑定成功"且更新外卖门店(食堂系统)
        wmCanteenStallPoiCanteenSerivice.updateWmPoiIdAndWmPoiBindStatusByBindSuccess(wmPoiBindBO);

        // 2-给门店打上A类和B类标签(标签系统)
        List<Long> labelIdList = wmCanteenStallPoiLabelService.addCanteenPoiLabel(bindDO.getCanteenPrimaryId(), wmPoiBindBO.getWmPoiId(), wmPoiBindBO.getUserId(), wmPoiBindBO.getUserName());

        // 3-本地记录门店A类和B类标签关系(食堂系统)
        wmCanteenStallPoiCanteenSerivice.addCanteenPoiLabel(bindDO.getCanteenPrimaryId(), wmPoiBindBO.getWmPoiId(), labelIdList);

        // 4-记录本次打标记录(食堂系统)
        wmCanteenStallPoiCanteenSerivice.insertCanteenPoiHistory(bindDO.getCanteenPrimaryId(), wmPoiBindBO.getWmPoiId(), wmPoiBindBO.getUserId(), wmPoiBindBO.getUserName(),CanteenPoiOperateTypeEnum.BIND.getCode());

        // 5-添加食堂门店关联属性(食堂系统）
        wmCanteenStallPoiCanteenSerivice.upsertCanteenPoiAttribute(bindDO.getCanteenPrimaryId(), bindDO.getWmPoiId());

        // 6-更新食堂合作档口数量(食堂系统)
        wmCanteenStallPoiCanteenSerivice.updateCanteenStoreNum(bindDO.getCanteenPrimaryId());
    }

}
