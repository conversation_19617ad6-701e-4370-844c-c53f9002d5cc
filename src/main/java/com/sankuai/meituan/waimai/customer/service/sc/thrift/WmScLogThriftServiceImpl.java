package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.sankuai.meituan.util.JsonUtil;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScLogMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScLogDB;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmScLogThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;


/**
 * @program: scm
 * @description: 学校食堂日志操作模块
 * @author: jianghuimin02
 * @create: 2020-05-18 19:25
 **/
@Slf4j
@Service
public class WmScLogThriftServiceImpl implements WmScLogThriftService {

    private static final int MAX_PAGE_SIZE = 200;

    @Autowired
    private WmScLogMapper wmScLogMapper;

    /**
     * 分页获日志列表
     */
    @Override
    public WmScLogListPageData getScLogList(WmScOplogBo wmScOplogBo) throws TException, WmSchCantException {
        log.info("[getScLogList] wmScOplogBo = {}", JSONObject.toJSONString(wmScOplogBo));
        if (wmScOplogBo.getPageSize() >= MAX_PAGE_SIZE) {
            log.error("校园食堂项目日志查询功能分页参数每页记录数不合理:日志功能查询参数:wmScOplogBo:{}", JsonUtil.toJsonString(wmScOplogBo));
            throw new WmSchCantException(BIZ_PARA_ERROR, "分页参数每页记录数不合理");
        }
        PageHelper.startPage(wmScOplogBo.getPageNo(), wmScOplogBo.getPageSize());
        List<WmScLogDB> wmScLogDBS = wmScLogMapper.queryScOpLog(wmScOplogBo);
        log.info("校园食堂项目:日志功能查询参数:wmScOplogBo:{}:wmScLogDBS:{}", JsonUtil.toJsonString(wmScOplogBo), JsonUtil.toJsonString(wmScLogDBS));
        List<WmScOplogBo> scOplogBos = WmScTransUtil.scLogTransDbToBo(wmScLogDBS);
        PageData<WmScOplogBo> pageData = PageUtil.page(wmScLogDBS, scOplogBos);
        return new WmScLogListPageData(pageData.getPageInfo(), pageData.getList());
    }

    /**
     * 查询学校交付管理日志列表
     * @param wmScOplogBo wmScOplogBo
     * @return WmScLogListPageData
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public WmScLogListPageData getSchoolDeliveryLogList(WmScOplogBo wmScOplogBo) throws TException, WmSchCantException {
        log.info("[WmScLogThriftServiceImpl.getSchoolDeliveryLogList] wmScOplogBo = {}", JSONObject.toJSONString(wmScOplogBo));
        if (wmScOplogBo.getPageSize() >= MAX_PAGE_SIZE) {
            log.error("[WmScLogThriftServiceImpl.getSchoolDeliveryLogList] getPageSize error. wmScOplogBo:{}", JsonUtil.toJsonString(wmScOplogBo));
            throw new WmSchCantException(BIZ_PARA_ERROR, "分页参数每页记录数不合理");
        }

        PageHelper.startPage(wmScOplogBo.getPageNo(), wmScOplogBo.getPageSize());
        List<WmScLogDB> wmScLogDBS = wmScLogMapper.selectSchoolDeliveryOpLog(wmScOplogBo);
        List<WmScOplogBo> scOplogBos = WmScTransUtil.scLogTransDbToBo(wmScLogDBS);
        PageData<WmScOplogBo> pageData = PageUtil.page(wmScLogDBS, scOplogBos);

        return new WmScLogListPageData(pageData.getPageInfo(), pageData.getList());
    }


    @Override
    public WmScLogListPageData getCanteenStallBindLogList(WmScOplogBo wmScOplogBo) throws TException, WmSchCantException {
        log.info("[WmScLogThriftServiceImpl.getCanteenStallBindLogList] wmScOplogBo = {}", JSONObject.toJSONString(wmScOplogBo));
        if (wmScOplogBo.getPageSize() >= MAX_PAGE_SIZE) {
            log.error("[WmScLogThriftServiceImpl.getCanteenStallBindLogList] getPageSize error. wmScOplogBo:{}", JsonUtil.toJsonString(wmScOplogBo));
            throw new WmSchCantException(BIZ_PARA_ERROR, "分页参数每页记录数不合理");
        }

        PageHelper.startPage(wmScOplogBo.getPageNo(), wmScOplogBo.getPageSize());
        List<WmScLogDB> wmScLogDBS = wmScLogMapper.selectCanteenStallBindOpLog(wmScOplogBo);
        List<WmScOplogBo> scOplogBos = WmScTransUtil.scLogTransDbToBo(wmScLogDBS);
        PageData<WmScOplogBo> pageData = PageUtil.page(wmScLogDBS, scOplogBos);

        return new WmScLogListPageData(pageData.getPageInfo(), pageData.getList());
    }

}
