package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.flow;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiRelConstants;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindStrategy;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.service.customer.UpmAuthCheckService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiRelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.unbind.*;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsFeeThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRoleTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskDetailSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskOpSystemEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.poirel.CustomerPoiUnBindDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.jeasy.rules.api.RulesEngineParameters;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240122
 * @desc 门店解绑客户新流程服务
 */
@Service
@Slf4j
public class PoiUnBindCustomerFlowService {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    @Autowired
    private CustomerPoiUnBindService customerPoiUnBindService;

    @Autowired
    private WmSettleManagerService wmSettleManagerService;

    @Autowired
    private WmPoiLogisticsFeeThriftService.Iface wmPoiLogisticsFeeThriftService;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    /**
     * 解绑使用的rule引擎
     */
    private static volatile Rules unBindRules = null;

    private static final Object lockObject = new Object();

    /**
     * 无纸质客户解绑权限提示文案
     */
    private static final String NO_PAPER_UNBIND_AUTH_TIPS = "纸质签约客户解绑门店需要权限才可操作";

    /**
     * 根据规则执行解绑新流程
     *
     * @param customerPoiUnBindDTO
     * @param customerOperateBO
     * @throws WmCustomerException
     */
    public String unBindByFlowAndRule(CustomerPoiUnBindDTO customerPoiUnBindDTO, CustomerOperateBO customerOperateBO) throws WmCustomerException, TException {
        Integer customerId = customerPoiUnBindDTO.getCustomerId();
        Set<Long> wmPoiIdSet = customerPoiUnBindDTO.getWmPoiIds();
        //步骤1：构建解绑流程上下文
        log.info("unBindByFlowAndRule,客户门店解绑根据规则判断执行流程,customerId={},wmPoiIds={}", customerId, JSON.toJSONString(wmPoiIdSet));
        CustomerPoiUnBindFlowContext context = buildPoiUnBindCustomerFlowContext(customerPoiUnBindDTO, customerOperateBO);
        //步骤2：注册解绑规则
        initUnBindFlowRule();
        //步骤3：执行解绑规则
        executeUnBindRules(context);
        //步骤4：获取规则返回的策略开始处理
        List<UnBindFlowStrategy> unBindFlowStrategyList = context.getUnBindFlowStrategyList();
        //步骤4.1 判断规则有效则根据策略执行流程
        if (checkMatchUnBindStrategy(unBindFlowStrategyList, context.getUnbindTips())) {
            log.info("unBindByFlowAndRule,解绑操作匹配到有效策略，根据策略开始执行解绑操作,unBindFlowStrategyList={}", JSON.toJSONString(unBindFlowStrategyList));
            for (UnBindFlowStrategy unBindFlowStrategy : unBindFlowStrategyList) {
                //上下文中的门店IDSet需要从策略中获取并重置
                context.setWmPoiIdSet(unBindFlowStrategy.getWmPoiIdSet());
                UnBindStrategy unBindStrategy = unBindFlowStrategy.getUnBindStrategy();
                unBindStrategy.execute(context);
            }
            log.info("unBindByFlowAndRule,解绑操作根据策略执行完成,customerId={},wmPoiIdSet={},unbindTips={}", customerId, JSON.toJSONString(wmPoiIdSet), context.getUnbindTips());
            return context.getUnbindTips();
        }

        //步骤4.2 规则无效，则需要执行原流程并告警
        log.error("unBindByFlowAndRule,解绑操作未匹配到有效策略，执行原解绑流程，customerPoiUnBindDTO={}", JSON.toJSONString(customerPoiUnBindDTO));
        //添加告警
        Cat.logEvent(CustomerPoiRelConstants.UNBIND_RULE_STRATEGY_EVENT, CustomerPoiRelConstants.UNBIND_RULE_NO_MATCH_STRATEGY);
        //直接原解绑流程
        return customerPoiUnBindService.unBind(new WmCustomerPoiUnBindParamBo(customerPoiUnBindDTO.getCustomerId(), customerPoiUnBindDTO.getWmPoiIds()
                , customerPoiUnBindDTO.getRemark(), customerPoiUnBindDTO.getOpUId(), customerPoiUnBindDTO.getOpUName(),
                CustomerTaskSourceEnum.of(customerPoiUnBindDTO.getOpSource()), customerOperateBO, CustomerPoiUnBindTypeEnum.DIRECT_UNBIND));

    }

    /**
     * 校验解绑策略是否有效可执行
     *
     * @param unBindFlowStrategyList
     * @param unBindTips
     * @return
     */
    private boolean checkMatchUnBindStrategy(List<UnBindFlowStrategy> unBindFlowStrategyList, String unBindTips) {
        if (CollectionUtils.isEmpty(unBindFlowStrategyList)
                && !unBindTips.equals(CustomerConstants.CUSTOMER_POI_UNBIND_SUCCESS)
                && !unBindTips.contains(NO_PAPER_UNBIND_AUTH_TIPS)) {
            return false;
        }
        //遍历所有策略确认是否有效
        for (UnBindFlowStrategy unBindFlowStrategy : unBindFlowStrategyList) {
            UnBindStrategy unBindStrategy = unBindFlowStrategy.getUnBindStrategy();
            if (unBindStrategy == null || unBindStrategy.getUnBindCheckStrategy() == null) {
                return false;
            }
        }
        return true;
    }

    /**
     * 构建解绑上下文参数
     *
     * @param customerPoiUnBindDTO
     * @param customerOperateBO
     * @return
     */
    private CustomerPoiUnBindFlowContext buildPoiUnBindCustomerFlowContext(CustomerPoiUnBindDTO customerPoiUnBindDTO,
                                                                           CustomerOperateBO customerOperateBO) throws WmCustomerException, TException {
        Integer customerId = customerPoiUnBindDTO.getCustomerId();
        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户不存在");
        }

        List<Long> wmPoiIdList = Lists.newArrayList(customerPoiUnBindDTO.getWmPoiIds());
        //查询门店是否有生效结算信息
        Set<Long> effectiveSettle = wmSettleManagerService.batchGetSettleEffectiveWmPoiIdSet(wmPoiIdList, customerId);
        Set<Long> effectiveLogisticFee = wmPoiLogisticsFeeThriftService.getValidLogisticsFeeByPoiIds(wmPoiIdList);
        // 取生效分成，结算的并集
        Set<Long> effectiveSellteWmPoiIdSet = Sets.union(effectiveLogisticFee, effectiveSettle);
        //存在绑定关系的门店ID列表
        Set<Long> needUnBindWmPoiIdSet = wmCustomerPoiRelService.selectExistPoiByWmPoiId(customerPoiUnBindDTO.getWmPoiIds());

        CustomerPoiUnBindFlowContext customerPoiUnBindFlowContext = CustomerPoiUnBindFlowContext.builder()
                .customerId(customerId)
                .wmPoiIdSet(customerPoiUnBindDTO.getWmPoiIds())
                .opSource(customerPoiUnBindDTO.getOpSource())
                .opSourceDetail(customerPoiUnBindDTO.getOpSourceDetail())
                .opSysName(customerPoiUnBindDTO.getSystemName())
                .opUid(customerPoiUnBindDTO.getOpUId())
                .opName(customerPoiUnBindDTO.getOpUName())
                .customerOperateBO(customerOperateBO)
                .wmCustomerDB(wmCustomerDB)
                .signMode(wmCustomerDB.getSignMode())
                .remark(customerPoiUnBindDTO.getRemark())
                .customerPoiUnBindTypeEnum(CustomerPoiUnBindTypeEnum.DIRECT_UNBIND)
                .rebuildUnBindFlag(checkReBuildUnBind(customerPoiUnBindDTO.getOpSource(), customerPoiUnBindDTO.getOpSourceDetail(), customerPoiUnBindDTO.getSystemName()))
                .confirmFailedToUnBindFlag(checkConfirmFailedToUnBind(customerId, customerPoiUnBindDTO.getWmPoiIds()))
                .unbindTips(CustomerConstants.CUSTOMER_POI_UNBIND_SUCCESS)
                .existEffSettleWmPoiIdSet(effectiveSellteWmPoiIdSet)
                .needUnBindWmPoiIdSet(needUnBindWmPoiIdSet)
                .build();

        //电子签约查询生效的签约人KP信息
        if (wmCustomerDB.getSignMode() == CustomerSignMode.ELECTTRONIC.getCode()) {
            WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(customerId);
            customerPoiUnBindFlowContext.setWmCustomerKp(wmCustomerKp);
        } else {
            //纸质签约则需要设置属性：是否有纸质客户解绑权限
            boolean hasPaperAuth = upmAuthCheckService.hasRolePermission(customerPoiUnBindDTO.getOpUId(), CustomerRoleTypeEnum.PAPER_SIGNMODE_UNBINDPOI_MANAGER.getCode());
            customerPoiUnBindFlowContext.setHasPaperCustomerUnBindAuth(hasPaperAuth);
        }

        return customerPoiUnBindFlowContext;
    }

    /**
     * 是否为重新建店场景解绑
     *
     * @param opSource
     * @param opSourceDetail
     * @param opSysName
     * @return
     */
    private boolean checkReBuildUnBind(Integer opSource, String opSourceDetail, String opSysName) {
        //来源为空或非重新建店强制解绑则直接返回 否
        if (opSource == null || opSource != CustomerTaskSourceEnum.REBUILD_FORCE_UNBIND.getCode()) {
            return false;
        }
        List<Integer> supportOpDetailSource = MccCustomerConfig.getRebuildForceUnbindSupportOpDetailSource();
        List<Integer> supportOpSystem = MccCustomerConfig.getRebuildForceUnbindSupportOpSystem();
        //如果来源或系统为空则直接过滤
        if (StringUtils.isBlank(opSourceDetail) || StringUtils.isBlank(opSysName)) {
            return false;
        }
        Integer opSourceDetailCode = CustomerTaskDetailSourceEnum.getByDesc(opSourceDetail).getCode();
        Integer opSysCode = CustomerTaskOpSystemEnum.getByDesc(opSysName).getCode();
        //判断详细来源以及系统是否配置的MCC中，如果是则为重新建店
        if (supportOpDetailSource.contains(opSourceDetailCode)
                && supportOpSystem.contains(opSysCode)) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否为预绑定确认失败解绑
     *
     * @param customerId
     * @param wmPoiIdSet
     * @return
     * @throws WmCustomerException
     */
    private boolean checkConfirmFailedToUnBind(Integer customerId, Set<Long> wmPoiIdSet) throws WmCustomerException {

        if (CollectionUtils.isNotEmpty(wmPoiIdSet) && wmPoiIdSet.size() == 1) {
            //门店关联的确认绑定失败记录只有一条且没关联切换任务
            List<WmCustomerPoiDB> wmCustomerPoiForPreBind = wmCustomerPoiDBMapper.getWmCustomerPoiForPreBind(customerId, Lists.newArrayList(wmPoiIdSet), Lists.newArrayList(CustomerRelationStatusEnum.CONFIRM_BINDING_FAIL.getCode(), CustomerRelationStatusEnum.TO_APPLY_BIND.getCode()));
            if (CollectionUtils.isNotEmpty(wmCustomerPoiForPreBind)
                    && wmCustomerPoiForPreBind.size() == 1
                    && wmCustomerPoiForPreBind.get(0).getSwitchTaskId() == 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 初始化绑定规则
     */
    private void initUnBindFlowRule() {
        if (unBindRules != null) {
            return;
        }
        //初始化注册规则-注册规则
        synchronized (lockObject) {
            if (unBindRules != null) {
                return;
            }
            //注册规则
            unBindRules = new Rules();
            //重新建店场景-客户解绑策略
            unBindRules.register(new ReBuildUnBindRule());
            //确认绑定失败解绑
            unBindRules.register(new ConfirmFailedUnBindRule());
            //客户未生效解绑
            unBindRules.register(new CustomerUnEffectiveUnBindRule());
            //客户生效&签约方式为纸质签约且无权限解绑
            unBindRules.register(new EffCusPaperCusHasAuthUnBindRule());
            //客户生效&签约方式为电子签约且无签约人
            unBindRules.register(new EffCusElectNoEffSignerUnBindRule());
            //客户生效&签约方式为电子签约且有签约人的解绑规则
            unBindRules.register(new EffCusElectHasEffSignerUnBindRule());
            //客户生效&纸质客户&无权限绑定规则
            unBindRules.register(new EffCusPaperNoAuthUnBindRule());
        }
    }

    /**
     * 执行解绑规则
     *
     * @param context
     */
    private void executeUnBindRules(CustomerPoiUnBindFlowContext context) {
        RulesEngineParameters parameters = new RulesEngineParameters().skipOnFirstAppliedRule(true);
        RulesEngine rulesEngine = new DefaultRulesEngine(parameters);

        //执行规则
        Facts facts = new Facts();
        facts.put("context", context);
        rulesEngine.fire(unBindRules, facts);

    }
}
