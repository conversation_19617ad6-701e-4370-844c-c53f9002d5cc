package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca;

import org.springframework.stereotype.Service;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;

@Service
public class WmEcontractCAShenzhenBaiShouWrapperService implements IWmEcontractCAWrapperService {

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo) {
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
                .setCustomerId(WmEcontractConstant.CERTIFY_CUSTOMER_SHENZHENBAISHOU)
                .build();

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.CA_PERFORMANCE_SERVICE_FEE)
                .certifyInfoBo(certifyInfoBo)
                .build();
    }
}
