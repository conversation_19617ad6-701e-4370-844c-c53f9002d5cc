package com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt;

import com.alibaba.fastjson.JSON;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileTokenAndEncryptDataTo;
import com.sankuai.meituan.waimai.customer.adapter.SensitiveWordsEncryptionServiceAdapter;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 加密手机号
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
@Slf4j
public class PhoneNoEncryptHandle implements IEncryptHandle {

    @Autowired
    private SensitiveWordsEncryptionServiceAdapter sensitiveWordsEncryptionServiceAdapter;


    @Override
    public KmsKeyNameEnum handleType() {
        return KmsKeyNameEnum.PHONE_NO;
    }


    @Override
    public EncryptResult execute(KeyEncrypt keyDecrypt) throws WmCustomerException {
        log.debug("execute::execute = {}", JSON.toJSONString(keyDecrypt));
        if (keyDecrypt == null || StringUtils.isBlank(keyDecrypt.getValueForEncrypt())) {
            return null;
        }
        EncryptResult result = new EncryptResult();
        GetMobileTokenAndEncryptDataTo getMobileTokenAndEncryptDataTo = sensitiveWordsEncryptionServiceAdapter.getMobileTokenAndEncrypt(keyDecrypt.getValueForEncrypt());
        if (getMobileTokenAndEncryptDataTo == null) {
            return null;
        }
        result.setEncryption(getMobileTokenAndEncryptDataTo.getMobileNoEncrypt());
        result.setToken(getMobileTokenAndEncryptDataTo.getMobileNoToken());
        return result;
    }


}
