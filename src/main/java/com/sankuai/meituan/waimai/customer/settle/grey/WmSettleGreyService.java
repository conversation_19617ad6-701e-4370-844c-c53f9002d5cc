package com.sankuai.meituan.waimai.customer.settle.grey;

import com.google.common.base.Function;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant.OrgType;
import com.sankuai.meituan.waimai.mtauth.manager.WmAdminDataAuthManager;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

import java.util.*;
import javax.annotation.Nullable;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class WmSettleGreyService {

    @Autowired
    private CommonGreyLogic commonGreyLogic;

    private static final Map<String, String> GREY_MAP = new HashMap<>();

    static {
        GREY_MAP.put("all", "settle_module_ddd_open");
        GREY_MAP.put("grey", "settle_module_ddd_grey_open");
        GREY_MAP.put("customerId","settle_module_ddd_grey_customerId");
        GREY_MAP.put("isHq","settle_module_ddd_grey_customerId");
        GREY_MAP.put("ordId","settle_module_ddd_grey_OrgId");
    }

    /**
     * DDD灰度策略-指定客户id/客户负责人所在外卖城市
     */
    public boolean routeDDDGrey(int wmCustomerId, int opUid) {
        //全量开启开关-true:全量开启
        if (ConfigUtilAdapter.getBoolean(GREY_MAP.get("all"), false)) {
            log.info("routeDDDGrey#true");
            return true;
        }

        //灰度开启开关-true:开启灰度
        if (!ConfigUtilAdapter.getBoolean(GREY_MAP.get("grey"), false)) {
            return false;
        }

        boolean isGrey = commonGreyLogic.isGrey(wmCustomerId, opUid, GREY_MAP);
        return isGrey;
    }

    public boolean routeAsyCommitGrey(int wmCustomerId,int wmPoiIdSize){
        if(ConfigUtilAdapter.getBoolean("routeAsyCommitGrey_open_all",false)){
            return asyCommitWmPoiSizeCondition(wmPoiIdSize);
        }

        String greyCustomerIdStr = ConfigUtilAdapter.getString("asy_commit_grey_customerId");
        if(StringUtils.isEmpty(greyCustomerIdStr)){
            return false;
        }

        List<String> greyCustomerIdList = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(greyCustomerIdStr);
        if(greyCustomerIdList.contains(String.valueOf(wmCustomerId))){
            return asyCommitWmPoiSizeCondition(wmPoiIdSize);
        }
        return false;
    }

    private boolean asyCommitWmPoiSizeCondition(int wmPoiIdSize){
        return wmPoiIdSize >= com.sankuai.meituan.util.ConfigUtilAdapter.getInt("asy_commit_wmpoi_size_condition",1000);
    }

}
