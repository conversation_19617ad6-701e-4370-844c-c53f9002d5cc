package com.sankuai.meituan.waimai.customer.util.diff;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.domain.CustomerRealTypeSpInfoBoDiff;
import com.sankuai.meituan.waimai.customer.domain.CustomerSceneInfoBODiff;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.CustomerPaperContractRemarkBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.MultiFileJsonBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.nibcus.inf.customer.client.enums.RegionEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.Map;

public class WmLogDiffConstant {

    public static Map<String, BeanDiffUtil.PropertyDesc> CUSTOMER_BUSINESS_MAP;
    public static Map<String, BeanDiffUtil.PropertyDesc> CUSTOMER_REAL_TYPE_SP_INFO_MAP;

    public static Map<String, BeanDiffUtil.PropertyDesc> CUSTOMER_IDCARD_MAP;

    public static Map<String, BeanDiffUtil.PropertyDesc> CUSTOMER_BUSINESS_ABROAD_MAP;


    public static Map<String, BeanDiffUtil.PropertyDesc> CONTRACT_SIGN_MAP;

    public static Map<String, BeanDiffUtil.PropertyDesc> CONTRACT_BASIC_MAP;

    public static Map<String, BeanDiffUtil.PropertyDesc> CONTRACT_REMARK_MAP;

    public static Map<String, BeanDiffUtil.PropertyDesc> SETTLE_DIFF_WITHOUTPOIS_MAP;

    public static Map<String, BeanDiffUtil.PropertyDesc> SETTLE_DIFF_WITHPOIS_MAP;

    public static Map<String, BeanDiffUtil.PropertyDesc> CUSTOMER_SCENE_INFO_MAP;

    static {

        CUSTOMER_REAL_TYPE_SP_INFO_MAP = BeanDiffUtil.PropertyDesc.clazz(CustomerRealTypeSpInfoBoDiff.class)
                .name("foodCityName").desc("美食城名称")
                .name("foodCityPic").desc("美食城图片")
                .name("foodCityAorBiz").desc("蜂窝类型").valueDesc(0,"未选择").valueDesc(1,"直营").valueDesc(2,"代理")
                .name("foodCityAorId").desc("蜂窝ID")
                .name("foodCityAorName").desc("蜂窝名称")
                .name("foodCityLocation").desc("物理城市")
                .name("foodCityVideo").desc("美食城视频")
                .name("foodCityPoiCount").desc("档口数量")
                .getChainMap();

        CUSTOMER_SCENE_INFO_MAP = BeanDiffUtil.PropertyDesc.clazz(CustomerSceneInfoBODiff.class)
                .name("sceneType").desc("场景")
                .valueDesc(1,"一照多址（一照多址是指经营主体在注册地址以外再设一个或者多个经营场所，通过备案即可获得合法从事经营活动资格的场景，即多家不同地址的门店共用同一个营业执照）")
                .valueDesc(2,"无常规营业执照（门店无常规营业执照，但具备可代替[营业执照]的相关证件，如临时营业执照或小证）")
                .valueDesc(3,"其他")
                .name("duplicateCustomerIds").desc("重复客户ID")
                .name("description").desc("场景说明")
                .name("sceneProveFiles").desc("证明材料")
                .getChainMap();

        CUSTOMER_BUSINESS_MAP = BeanDiffUtil.PropertyDesc.clazz(WmCustomerDB.class)
                .name("id").desc("客户id")
                .name("customerRealType").desc("客户类型")
                .valueDesc(CustomerRealTypeEnum.QINGXUANZE.getValue(), CustomerRealTypeEnum.QINGXUANZE.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN.getValue(), CustomerRealTypeEnum.DANDIAN.getName())
                .valueDesc(CustomerRealTypeEnum.MEISHICHENG.getValue(), CustomerRealTypeEnum.MEISHICHENG.getName())
                .valueDesc(CustomerRealTypeEnum.SHITANG.getValue(), CustomerRealTypeEnum.SHITANG.getName())
                .valueDesc(CustomerRealTypeEnum.DALIANSUO.getValue(), CustomerRealTypeEnum.DALIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.XIAOLIANSUO.getValue(), CustomerRealTypeEnum.XIAOLIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.LIULIANGLIANSUO.getValue(), CustomerRealTypeEnum.LIULIANGLIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_SHANGCHAO_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_SHANGCHAO_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_SHENGXIAN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_SHENGXIAN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_XIANHUA_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_XIANHUA_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_MUYING_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_MUYING_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_MEIZHUANG_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_MEIZHUANG_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_FUSHIXIEMAO_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_FUSHIXIEMAO_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_RIYONGPIN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_RIYONGPIN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.CONTRACTOR.getValue(), CustomerRealTypeEnum.CONTRACTOR.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue(), CustomerRealTypeEnum.DANDIAN_YAOPIN.getName())
                .valueDesc(CustomerRealTypeEnum.B2C_DRUG.getValue(), CustomerRealTypeEnum.B2C_DRUG.getName())
                .valueDesc(CustomerRealTypeEnum.SG_CKA.getValue(), CustomerRealTypeEnum.SG_CKA.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN_SG.getValue(), CustomerRealTypeEnum.DANDIAN_SG.getName())
                .name("customerType").desc("客户资质主体")
                .valueDesc(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode(), CustomerType.CUSTOMER_TYPE_BUSINESS.getDesc())
                .valueDesc(CustomerType.CUSTOMER_TYPE_IDCARD.getCode(), CustomerType.CUSTOMER_TYPE_IDCARD.getDesc())
                .name("customerName").desc("执照名称")
                .name("customerNumber").desc("注册号")
                .name("picUrl").desc("资质附件")
                .name("legalPerson").desc("法定代表人/经营者")
                .name("address").desc("地址")
                .name("validateDate").desc("有效期").valueFmt(new BeanDiffUtil.PropertyValueFormatter() {
                    @Override
                    public String format(Object origin) {
                        if (origin == null) {
                            return "";
                        }
                        if (((Long) origin).longValue() == 0L) {
                            return "长期";
                        }
                        return DateUtil.seconds2TimeFormat((long) origin, "yyyy-MM-dd");
                    }
                })
                .name("businessScope").desc("经营范围")
                .name("signMode")
                .valueDesc(CustomerSignMode.ELECTTRONIC.getCode(), CustomerSignMode.ELECTTRONIC.getDesc())
                .valueDesc(CustomerSignMode.PAPER.getCode(), CustomerSignMode.PAPER.getDesc()).desc("签约模式")
                .name("mtCustomerId").desc("mtCustomerId")
                .name("superCustomerId").desc("上级客户ID")
                .name("isLeaf").desc("是否末级客户")
                .valueDesc(CustomerConstants.CUSTOMER_IS_LEAF_YES, "是")
                .valueDesc(CustomerConstants.CUSTOMER_IS_LEAF_NO, "否")
                .name("customerExtPro").desc("上级客户属性")
                .name("aliveIdentify").desc("活体识别")
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_UNKNOWN.getCode(), CustomerAliveIdentifyEnum.ALIVE_UNKNOWN.getDesc())
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_PASS.getCode(), CustomerAliveIdentifyEnum.ALIVE_PASS.getDesc())
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_NOT_PASS.getCode(), CustomerAliveIdentifyEnum.ALIVE_NOT_PASS.getDesc())
                .name("contractNum").desc("合同编号")
                .name("hqSecondCityId").desc("总部物理城市")
                .name("hqDetailAddress").desc("总部详细地址")
                .name("wdcClueId").desc("线索ID")
                .name("wmCoStatus").desc("食堂承包商状态变更")
                .valueDesc(WmCoStatusEnum.NO.getCode(), WmCoStatusEnum.NO.getDesc())
                .valueDesc(WmCoStatusEnum.YES.getCode(), WmCoStatusEnum.YES.getDesc())
                .name("certificateOverdue").desc("是否过期")
                .valueDesc(CertificateOverdueEnum.EXPIRED.getType(), CertificateOverdueEnum.EXPIRED.getName())
                .valueDesc(CertificateOverdueEnum.NOEXPIRED.getType(), CertificateOverdueEnum.NOEXPIRED.getName())
                .name("certificateType").desc("营业执照形式")
                .valueDesc(CertificateTypeEnum.PAPER.getType(), CertificateTypeEnum.PAPER.getName())
                .valueDesc(CertificateTypeEnum.ELECTRONIC.getType(), CertificateTypeEnum.ELECTRONIC.getName())
                .name("certificateStatus").desc("营业执照状态")
                .valueDesc(CertificateStatusEnum.NONE.getType(), CertificateStatusEnum.NONE.getName())
                .valueDesc(CertificateStatusEnum.DURATION.getType(), CertificateStatusEnum.DURATION.getName())
                .valueDesc(CertificateStatusEnum.CANCELLATION.getType(), CertificateStatusEnum.CANCELLATION.getName())
                .valueDesc(CertificateStatusEnum.REVOKE.getType(), CertificateStatusEnum.REVOKE.getName())
                .name("legalPersonChange").desc("法人变更")
                .valueDesc(LegalPersonChangeEnum.CHANGE.getType(), LegalPersonChangeEnum.CHANGE.getName())
                .valueDesc(LegalPersonChangeEnum.NOCHANGE.getType(), LegalPersonChangeEnum.NOCHANGE.getName())
                .name("multiplex").desc("复用已入驻客户")
                .valueDesc(CustomerMultiplexEnum.YES.getType(), CustomerMultiplexEnum.YES.getName())
                .valueDesc(CustomerMultiplexEnum.NO.getType(), CustomerMultiplexEnum.NO.getName())
                .name("multiplexCustomerId").desc("复用池客户ID")
                .name("multiplexBusinessLineId").desc("复用客户业务线ID")
                .name("legalAuthType").desc("授权方式")
                .valueDesc(LegalAuthTypeEnum.UN_SETTING.getCode(),LegalAuthTypeEnum.UN_SETTING.getDesc())
                .valueDesc(LegalAuthTypeEnum.PAPER_AUTH.getCode(), LegalAuthTypeEnum.PAPER_AUTH.getDesc())
                .valueDesc(LegalAuthTypeEnum.MESSAGE_AUTH.getCode(), LegalAuthTypeEnum.MESSAGE_AUTH.getDesc())
                .name("bizOrgCode")
                .valueDesc(CustomerBizOrgEnum.WAI_MAI.getCode(), CustomerBizOrgEnum.WAI_MAI.getDesc())
                .valueDesc(CustomerBizOrgEnum.SHAN_GOU.getCode(), CustomerBizOrgEnum.SHAN_GOU.getDesc())
                .valueDesc(CustomerBizOrgEnum.MEDICINE.getCode(), CustomerBizOrgEnum.MEDICINE.getDesc())
                .desc("业务线")
                .getChainMap();

        CUSTOMER_IDCARD_MAP = BeanDiffUtil.PropertyDesc.clazz(WmCustomerDB.class)
                .name("id").desc("客户id")
                .name("customerRealType").desc("客户类型")
                .valueDesc(CustomerRealTypeEnum.QINGXUANZE.getValue(), CustomerRealTypeEnum.QINGXUANZE.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN.getValue(), CustomerRealTypeEnum.DANDIAN.getName())
                .valueDesc(CustomerRealTypeEnum.MEISHICHENG.getValue(), CustomerRealTypeEnum.MEISHICHENG.getName())
                .valueDesc(CustomerRealTypeEnum.SHITANG.getValue(), CustomerRealTypeEnum.SHITANG.getName())
                .valueDesc(CustomerRealTypeEnum.DALIANSUO.getValue(), CustomerRealTypeEnum.DALIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.XIAOLIANSUO.getValue(), CustomerRealTypeEnum.XIAOLIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.LIULIANGLIANSUO.getValue(), CustomerRealTypeEnum.LIULIANGLIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_SHANGCHAO_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_SHANGCHAO_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_SHENGXIAN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_SHENGXIAN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_XIANHUA_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_XIANHUA_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_MUYING_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_MUYING_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_MEIZHUANG_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_MEIZHUANG_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_FUSHIXIEMAO_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_FUSHIXIEMAO_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_RIYONGPIN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_RIYONGPIN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.CONTRACTOR.getValue(), CustomerRealTypeEnum.CONTRACTOR.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue(), CustomerRealTypeEnum.DANDIAN_YAOPIN.getName())
                .valueDesc(CustomerRealTypeEnum.B2C_DRUG.getValue(), CustomerRealTypeEnum.B2C_DRUG.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN_SG.getValue(), CustomerRealTypeEnum.DANDIAN_SG.getName())
                .valueDesc(CustomerRealTypeEnum.SG_CKA.getValue(), CustomerRealTypeEnum.SG_CKA.getName())
                .name("customerType").desc("客户资质主体")
                .valueDesc(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode(), CustomerType.CUSTOMER_TYPE_BUSINESS.getDesc())
                .valueDesc(CustomerType.CUSTOMER_TYPE_IDCARD.getCode(), CustomerType.CUSTOMER_TYPE_IDCARD.getDesc())
                .name("customerSecondType").desc("个人证件类型")
                .valueDesc(Integer.valueOf(CertTypeEnum.ID_CARD.getType()), CertTypeEnum.ID_CARD.getName())
                .valueDesc(Integer.valueOf(CertTypeEnum.ID_CARD_TEMP.getType()), CertTypeEnum.ID_CARD_TEMP.getName())
                .valueDesc(Integer.valueOf(CertTypeEnum.ID_CARD_COPY.getType()), CertTypeEnum.ID_CARD_COPY.getName())
                .valueDesc(Integer.valueOf(CertTypeEnum.DRIVING_LICENCE.getType()), CertTypeEnum.DRIVING_LICENCE.getName())
                .valueDesc(Integer.valueOf(CertTypeEnum.PASSPORT.getType()), CertTypeEnum.PASSPORT.getName())
                .valueDesc(Integer.valueOf(CertTypeEnum.HK_MACAO_REENTRY_PERMIT.getType()), CertTypeEnum.HK_MACAO_REENTRY_PERMIT.getName())
                .valueDesc(Integer.valueOf(CertTypeEnum.TAIWAN_REENTRY_PERMIT.getType()), CertTypeEnum.TAIWAN_REENTRY_PERMIT.getName())
                .name("customerName").desc("姓名")
                .name("customerNumber").desc("证件编号")
                .name("picUrl").desc("身份证正/反面")
                .name("signMode")
                .valueDesc(CustomerSignMode.ELECTTRONIC.getCode(), CustomerSignMode.ELECTTRONIC.getDesc())
                .valueDesc(CustomerSignMode.PAPER.getCode(), CustomerSignMode.PAPER.getDesc()).desc("签约模式")
                .name("mtCustomerId").desc("mtCustomerId")
                .name("superCustomerId").desc("上级客户ID")
                .name("isLeaf").desc("是否末级客户")
                .valueDesc(CustomerConstants.CUSTOMER_IS_LEAF_YES, "是")
                .valueDesc(CustomerConstants.CUSTOMER_IS_LEAF_NO, "否")
                .name("customerExtPro").desc("上级客户属性")
                .name("aliveIdentify").desc("活体识别")
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_UNKNOWN.getCode(), CustomerAliveIdentifyEnum.ALIVE_UNKNOWN.getDesc())
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_PASS.getCode(), CustomerAliveIdentifyEnum.ALIVE_PASS.getDesc())
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_NOT_PASS.getCode(), CustomerAliveIdentifyEnum.ALIVE_NOT_PASS.getDesc())
                .name("contractNum").desc("合同编号")
                .name("hqSecondCityId").desc("总部物理城市")
                .name("hqDetailAddress").desc("总部详细地址")
                .name("wdcClueId").desc("线索ID")
                .name("wmCoStatus").desc("食堂承包商状态变更")
                .valueDesc(WmCoStatusEnum.NO.getCode(), WmCoStatusEnum.NO.getDesc())
                .valueDesc(WmCoStatusEnum.YES.getCode(), WmCoStatusEnum.YES.getDesc())
                .name("multiplex").desc("复用已入驻客户")
                .valueDesc(CustomerMultiplexEnum.YES.getType(), CustomerMultiplexEnum.YES.getName())
                .valueDesc(CustomerMultiplexEnum.NO.getType(), CustomerMultiplexEnum.NO.getName())
                .name("multiplexCustomerId").desc("复用池客户ID")
                .name("multiplexBusinessLineId").desc("复用客户业务线ID")
                .name("bizOrgCode")
                .valueDesc(CustomerBizOrgEnum.WAI_MAI.getCode(), CustomerBizOrgEnum.WAI_MAI.getDesc())
                .valueDesc(CustomerBizOrgEnum.SHAN_GOU.getCode(), CustomerBizOrgEnum.SHAN_GOU.getDesc())
                .valueDesc(CustomerBizOrgEnum.MEDICINE.getCode(), CustomerBizOrgEnum.MEDICINE.getDesc())
                .desc("业务线")
                .getChainMap();

        CUSTOMER_BUSINESS_ABROAD_MAP = BeanDiffUtil.PropertyDesc.clazz(WmCustomerDB.class)
                .name("id").desc("客户id")
                .name("customerRealType").desc("客户类型")
                .valueDesc(CustomerRealTypeEnum.QINGXUANZE.getValue(), CustomerRealTypeEnum.QINGXUANZE.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN.getValue(), CustomerRealTypeEnum.DANDIAN.getName())
                .valueDesc(CustomerRealTypeEnum.MEISHICHENG.getValue(), CustomerRealTypeEnum.MEISHICHENG.getName())
                .valueDesc(CustomerRealTypeEnum.SHITANG.getValue(), CustomerRealTypeEnum.SHITANG.getName())
                .valueDesc(CustomerRealTypeEnum.DALIANSUO.getValue(), CustomerRealTypeEnum.DALIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.XIAOLIANSUO.getValue(), CustomerRealTypeEnum.XIAOLIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.LIULIANGLIANSUO.getValue(), CustomerRealTypeEnum.LIULIANGLIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_SHANGCHAO_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_SHANGCHAO_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_SHENGXIAN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_SHENGXIAN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_XIANHUA_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_XIANHUA_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_MUYING_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_MUYING_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_MEIZHUANG_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_MEIZHUANG_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_FUSHIXIEMAO_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_FUSHIXIEMAO_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.ZONGBU_RIYONGPIN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_RIYONGPIN_LIANSUO.getName())
                .valueDesc(CustomerRealTypeEnum.CONTRACTOR.getValue(), CustomerRealTypeEnum.CONTRACTOR.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue(), CustomerRealTypeEnum.DANDIAN_YAOPIN.getName())
                .valueDesc(CustomerRealTypeEnum.B2C_DRUG.getValue(), CustomerRealTypeEnum.B2C_DRUG.getName())
                .valueDesc(CustomerRealTypeEnum.SG_CKA.getValue(), CustomerRealTypeEnum.SG_CKA.getName())
                .valueDesc(CustomerRealTypeEnum.DANDIAN_SG.getValue(), CustomerRealTypeEnum.DANDIAN_SG.getName())
                .name("customerType").desc("客户资质主体")
                .valueDesc(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode(), CustomerType.CUSTOMER_TYPE_BUSINESS.getDesc())
                .valueDesc(CustomerType.CUSTOMER_TYPE_IDCARD.getCode(), CustomerType.CUSTOMER_TYPE_IDCARD.getDesc())
                .name("customerName").desc("执照名称")
                .name("customerNumber").desc("注册号")
                .name("picUrl").desc("资质附件")
                .name("legalPerson").desc("法定代表人/经营者")
                .name("address").desc("地址")
                .name("legalAuthType").desc("授权方式")
                .valueDesc(LegalAuthTypeEnum.UN_SETTING.getCode(),LegalAuthTypeEnum.UN_SETTING.getDesc())
                .valueDesc(LegalAuthTypeEnum.PAPER_AUTH.getCode(), LegalAuthTypeEnum.PAPER_AUTH.getDesc())
                .valueDesc(LegalAuthTypeEnum.MESSAGE_AUTH.getCode(), LegalAuthTypeEnum.MESSAGE_AUTH.getDesc())
                .name("validateDate").desc("有效期").valueFmt(new BeanDiffUtil.PropertyValueFormatter() {
                    @Override
                    public String format(Object origin) {
                        if (origin == null) {
                            return "";
                        }
                        if (((Long) origin).longValue() == 0L) {
                            return "长期";
                        }
                        return DateUtil.seconds2TimeFormat((long) origin, "yyyy-MM-dd");
                    }
                })
                .name("businessScope").desc("经营范围")
                .name("signMode")
                .valueDesc(CustomerSignMode.ELECTTRONIC.getCode(), CustomerSignMode.ELECTTRONIC.getDesc())
                .valueDesc(CustomerSignMode.PAPER.getCode(), CustomerSignMode.PAPER.getDesc()).desc("签约模式")
                .name("mtCustomerId").desc("mtCustomerId")
                .name("superCustomerId").desc("上级客户ID")
                .name("isLeaf").desc("是否末级客户")
                .valueDesc(CustomerConstants.CUSTOMER_IS_LEAF_YES, "是")
                .valueDesc(CustomerConstants.CUSTOMER_IS_LEAF_NO, "否")
                .name("customerExtPro").desc("上级客户属性")
                .name("aliveIdentify").desc("活体识别")
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_UNKNOWN.getCode(), CustomerAliveIdentifyEnum.ALIVE_UNKNOWN.getDesc())
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_PASS.getCode(), CustomerAliveIdentifyEnum.ALIVE_PASS.getDesc())
                .valueDesc(CustomerAliveIdentifyEnum.ALIVE_NOT_PASS.getCode(), CustomerAliveIdentifyEnum.ALIVE_NOT_PASS.getDesc())
                .name("contractNum").desc("合同编号")
                .name("hqSecondCityId").desc("总部物理城市")
                .name("hqDetailAddress").desc("总部详细地址")
                .name("wdcClueId").desc("线索ID")
                .name("wmCoStatus").desc("食堂承包商状态变更")
                .valueDesc(WmCoStatusEnum.NO.getCode(), WmCoStatusEnum.NO.getDesc())
                .valueDesc(WmCoStatusEnum.YES.getCode(), WmCoStatusEnum.YES.getDesc())
                .name("certificateOverdue").desc("是否过期")
                .valueDesc(CertificateOverdueEnum.EXPIRED.getType(), CertificateOverdueEnum.EXPIRED.getName())
                .valueDesc(CertificateOverdueEnum.NOEXPIRED.getType(), CertificateOverdueEnum.NOEXPIRED.getName())
                .name("certificateType").desc("营业执照形式")
                .valueDesc(CertificateTypeEnum.PAPER.getType(), CertificateTypeEnum.PAPER.getName())
                .valueDesc(CertificateTypeEnum.ELECTRONIC.getType(), CertificateTypeEnum.ELECTRONIC.getName())
                .name("certificateStatus").desc("营业执照状态")
                .valueDesc(CertificateStatusEnum.NONE.getType(), CertificateStatusEnum.NONE.getName())
                .valueDesc(CertificateStatusEnum.DURATION.getType(), CertificateStatusEnum.DURATION.getName())
                .valueDesc(CertificateStatusEnum.CANCELLATION.getType(), CertificateStatusEnum.CANCELLATION.getName())
                .valueDesc(CertificateStatusEnum.REVOKE.getType(), CertificateStatusEnum.REVOKE.getName())
                .name("legalPersonChange").desc("法人变更")
                .valueDesc(LegalPersonChangeEnum.CHANGE.getType(), LegalPersonChangeEnum.CHANGE.getName())
                .valueDesc(LegalPersonChangeEnum.NOCHANGE.getType(), LegalPersonChangeEnum.NOCHANGE.getName())
                .name("registryState").desc("注册国家/地区")
                .valueDesc(RegionEnum.TW.getCode(), RegionEnum.TW.getRegionName())
                .valueDesc(RegionEnum.HK.getCode(), RegionEnum.HK.getRegionName())
                .valueDesc(RegionEnum.MO.getCode(), RegionEnum.MO.getRegionName())
                .valueDesc(RegionEnum.SG.getCode(), RegionEnum.SG.getRegionName())
                .valueDesc(RegionEnum.JP.getCode(), RegionEnum.JP.getRegionName())
                .valueDesc(RegionEnum.KR.getCode(), RegionEnum.KR.getRegionName())
                .valueDesc(RegionEnum.US.getCode(), RegionEnum.US.getRegionName())
                .valueDesc(RegionEnum.FR.getCode(), RegionEnum.FR.getRegionName())
                .valueDesc(RegionEnum.GB.getCode(), RegionEnum.GB.getRegionName())
                .valueDesc(RegionEnum.CH.getCode(), RegionEnum.CH.getRegionName())
                .valueDesc(RegionEnum.TH.getCode(), RegionEnum.TH.getRegionName())
                .valueDesc(RegionEnum.CA.getCode(), RegionEnum.CA.getRegionName())
                .valueDesc(RegionEnum.AU.getCode(), RegionEnum.AU.getRegionName())
                .valueDesc(RegionEnum.MY.getCode(), RegionEnum.MY.getRegionName())
                .valueDesc(RegionEnum.NZ.getCode(), RegionEnum.NZ.getRegionName())
                .valueDesc(RegionEnum.NL.getCode(), RegionEnum.NL.getRegionName())
                .valueDesc(RegionEnum.RU.getCode(), RegionEnum.RU.getRegionName())
                .valueDesc(RegionEnum.IN.getCode(), RegionEnum.IN.getRegionName())
                .valueDesc(RegionEnum.DE.getCode(), RegionEnum.DE.getRegionName())
                .valueDesc(RegionEnum.AT.getCode(), RegionEnum.AT.getRegionName())
                .valueDesc(RegionEnum.ES.getCode(), RegionEnum.ES.getRegionName())
                .valueDesc(RegionEnum.IT.getCode(), RegionEnum.IT.getRegionName())
                .valueDesc(RegionEnum.SE.getCode(), RegionEnum.SE.getRegionName())
                .valueDesc(RegionEnum.NO.getCode(), RegionEnum.NO.getRegionName())
                .name("bizOrgCode")
                .valueDesc(CustomerBizOrgEnum.WAI_MAI.getCode(), CustomerBizOrgEnum.WAI_MAI.getDesc())
                .valueDesc(CustomerBizOrgEnum.SHAN_GOU.getCode(), CustomerBizOrgEnum.SHAN_GOU.getDesc())
                .valueDesc(CustomerBizOrgEnum.MEDICINE.getCode(), CustomerBizOrgEnum.MEDICINE.getDesc())
                .desc("业务线")
                .getChainMap();

        CONTRACT_BASIC_MAP = BeanDiffUtil.PropertyDesc.clazz(WmTempletContractBasicBo.class)
                .name("id").desc("合同id")
                .name("type").desc("合同类型")
                .name("contractNum").desc("合同编号")
                .valueDesc(WmTempletContractTypeEnum.C1_E.getCode(), "美团与客户电子合同")
                .valueDesc(WmTempletContractTypeEnum.C1_PAPER.getCode(), "美团与客户纸质合同")
                .valueDesc(WmTempletContractTypeEnum.C2_E.getCode(), "美团与合作商电子合同")
                .valueDesc(WmTempletContractTypeEnum.C2_PAPER.getCode(), "美团与合作商纸质合同")
                .name("status").desc("合同状态")
                .valueDesc(CustomerContractStatus.STAGE.getCode(), CustomerContractStatus.STAGE.getDesc())
                .valueDesc(CustomerContractStatus.AUDITING.getCode(), CustomerContractStatus.AUDITING.getDesc())
                .valueDesc(CustomerContractStatus.REJECT.getCode(), CustomerContractStatus.REJECT.getDesc())
                .valueDesc(CustomerContractStatus.SIGNING.getCode(), CustomerContractStatus.SIGNING.getDesc())
                .valueDesc(CustomerContractStatus.SIGN_FAIL.getCode(), CustomerContractStatus.SIGN_FAIL.getDesc())
                .valueDesc(CustomerContractStatus.EFFECT.getCode(), CustomerContractStatus.EFFECT.getDesc())
                .name("dueDate").desc("有效日期").valueFmt(new BeanDiffUtil.PropertyValueFormatter() {
                    @Override
                    public String format(Object origin) {
                        if (origin == null) {
                            return "";
                        }
                        return DateUtil.seconds2TimeFormat((long) origin, "yyyy-MM-dd");
                    }
                })
                .getChainMap();

        CONTRACT_REMARK_MAP = BeanDiffUtil.PropertyDesc.clazz(CustomerPaperContractRemarkBo.class)
                .name("contractScan").desc("合同扫描件").valueFmt(new BeanDiffUtil.PropertyValueFormatter() {
                    @Override
                    public String format(Object origin) {
                        MultiFileJsonBo fileJsonBo = (MultiFileJsonBo) origin;
                        return joinUrlListToString(fileJsonBo);
                    }
                })
                .name("otherContractScan").desc("合同其他附件").valueFmt(new BeanDiffUtil.PropertyValueFormatter() {
                    @Override
                    public String format(Object origin) {
                        MultiFileJsonBo fileJsonBo = (MultiFileJsonBo) origin;
                        return joinUrlListToString(fileJsonBo);
                    }
                })
                .getChainMap();

        CONTRACT_SIGN_MAP = BeanDiffUtil.PropertyDesc.clazz(WmTempletContractSignBo.class)
                .name("signId").desc("签约方ID")
                .name("signName").desc("签约方")
                .name("signPeople").desc("签约人")
                .name("signPhone").desc("签约人联系方式")
                .name("signTime").desc("签约时间")
                .getChainMap();

        SETTLE_DIFF_WITHOUTPOIS_MAP = BeanDiffUtil.PropertyDesc.clazz(WmSettle.class)
                .name("min_pay_amount").desc("最低结算金额")
                .name("branchname").desc("银行信息")
//                .name("pay_day_of_month").desc("结算周期")//3天、7天、14天、30天
                .name("acc_cardno").desc("银行卡号")
                .name("acc_name").desc("银行卡开户名")
                .name("acctype").desc("对公对私").valueDesc((byte) 1, "对公").valueDesc((byte) 2, "对私")
//                .name("settle_type").desc("结算类型").valueDesc((byte) 1, "周期结算").valueDesc((byte) 2, "商家自提")
//                .valueDesc(
//                        (byte) 3, "固定日期结算").valueDesc((byte) 4, "固定月末结算")
                .name("party_a_finance_people").desc("财务联系人")
                .name("party_a_finance_phone").desc("财务联系人电话")
                .name("name").desc("结算名")
                .name("card_type").desc("是否开钱包").valueDesc(1, "否").valueDesc(2, "是")
                .name("cert_type").desc("开户证件类型").valueDesc((short) 1001, "身份证")
                .valueDesc((short) 1002, "户口簿")
                .valueDesc((short) 1003, "中国护照").valueDesc((short) 1004, "军官证/士兵证/武警警官证")
                .valueDesc((short) 1005, "港澳居民往来内地通行证").valueDesc((short) 1006, "台湾居民往来大陆通行证")
                .valueDesc((short) 1007, "外国人永久居留证").valueDesc((short) 1008, "外国护照")
                .valueDesc((short) 2001, "营业执照/统一社会信用代码").valueDesc((short) 2002, "事业单位法人证书")
                .valueDesc((short) 2003, "民办非企业单位登记证书").valueDesc((short) 2004, "社会团体证明")
                .valueDesc((short) 2005, "军队单位对外有偿服务许可证").valueDesc((short) 2006, "武警边防部队对外有偿服务许可证")
                .valueDesc((short) 2007, "香港/澳门/海外企业资质").valueDesc((short) 0, "无").defaultValue("0")
                .name("cert_num").desc("证件编号").defaultValue("")
                .name("reserve_phone").desc("银行卡预留手机号").defaultValue("")
                .name("legal_cert_num").desc("公司证件编号").defaultValue("")
                .name("legal_person").desc("法人姓名").defaultValue("")
                .name("legal_id_card").desc("法人身份证编号").defaultValue("")
                .getChainMap();

        SETTLE_DIFF_WITHPOIS_MAP = Maps.newHashMap(SETTLE_DIFF_WITHOUTPOIS_MAP);
        SETTLE_DIFF_WITHPOIS_MAP.putAll(BeanDiffUtil.PropertyDesc.clazz(WmSettle.class).name("wm_poi_ids").desc("关联门店").getChainMap());
    }

    private static String joinUrlListToString(MultiFileJsonBo fileJsonBo) {
        if (fileJsonBo == null || CollectionUtils.isEmpty(fileJsonBo.getList())) {
            return "";
        }
        return StringUtils.join(Lists.transform(fileJsonBo.getList(), new Function<MultiFileJsonBo.CustomerFile, String>() {
            @Nullable
            @Override
            public String apply(@Nullable MultiFileJsonBo.CustomerFile input) {
                return input.getUrl();
            }
        }), ",");
    }
}
