package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.*;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerRelDB;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.customer.util.DateUtils;
import com.sankuai.meituan.waimai.customer.util.ExecutorUtil;
import com.sankuai.meituan.waimai.customer.util.ServiceEnvUtils;
import com.sankuai.meituan.waimai.customer.util.common.GrayConfigHelper;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.dto.bmcontract.BmCompanyContractChangeRequestDTO;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.exception.WmHeronLogisticsException;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.poibizflow.constant.datatransfer.WmDataSubModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.base.BooleanResult;
import com.sankuai.meituan.waimai.poibizflow.thrift.base.LongResult;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.bo.contract.DataSyncOperateTypeConstant;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.bo.contract.WmContractBo;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.bo.customer.WmCustomerDataBo;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.CustomerPaperContractRemarkBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.companycustomer.WmCompanyCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.companycustomer.WmCompanyCustomerContractWithoutPdfBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.companycustomer.request.QkTriggerSignRequestParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.request.CreateWmCustomerContractReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBusinessCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-10-28 20:33
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E})
public class WmBusinessCustomerTempletService extends AbstractWmEContractTempletService {
    private static final int INSERT = 0;
    private static final int UPDATE = 1;

    @Resource
    private GrayConfigHelper grayConfigHelper;

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        log.info("待写入企客合同:{},opUid:{},opName:{}", JSON.toJSONString(contractBo), opUid, opName);
        // 合同前置校验
        ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = wmContractService.getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
        // 原数据状态
        int rollBackStatus = oldBo.getBasicBo() == null ? CustomerContractStatus.STAGE.getCode() : oldBo.getBasicBo().getStatus();
        if (rollBackStatus == CustomerContractStatus.WAITING_SIGN.getCode()) {
            return doStartSign(contractBo, opUid, rollBackStatus);
        } else {
            // 操作前合同状态记录
            int oldStatus = contractBo.getBasicBo().getTempletContractId() <= 0 ? INSERT : UPDATE;
            // 判断是否需要发起手动打包
            CustomerPaperContractRemarkBo remarkBo = JSON.parseObject(contractBo.getBasicBo().getExtStr(), CustomerPaperContractRemarkBo.class);
            if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {
                remarkBo.setWaitHandPack(true);
            } else {
                remarkBo.setWaitHandPack(false);
            }
            contractBo.getBasicBo().setExtStr(JSON.toJSONString(remarkBo));
            // 新增or更新都需要判断
            Integer contractId = insertOrUpdate(contractBo, opUid, opName);
            // 记录合同变更日志
            contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
            int wmCustomerId = contractBo.getBasicBo().getParentId();
            if (isEffectiveCustomerAndKp(wmCustomerId)) {
                // 数据推送至配送侧
                log.info("合同:{}对应的客户KP已生效，执行数据推送至配送侧，推送失败回滚状态:{}", contractId, rollBackStatus);
                dataSyncToBanma(contractBo, contractId, rollBackStatus, opUid, opName, oldStatus);
            } else {
                log.info("企客配送服务合同对应的客户kp还未生效，暂不将合同同步至配送侧，contractId:{}", contractId);
            }
            return contractId;
        }
    }

    private Integer doStartSign(WmCustomerContractBo contractBo, int opUid, int oldStatus) throws WmCustomerException, TException {
        // 合同ID
        Integer contractId = Integer.valueOf(contractBo.getBasicBo().getTempletContractId() + "");
        // 查询配送侧补齐的企客合同的个性化信息
        WmTempletContractExtension wmTempletContractExtension = wmTempletContractExtensionMapper.selectByPrimaryKeyMaster(contractBo.getBasicBo().getTempletContractId());
        // 封装发起签约的对象
        contractBo.setExtraData(wmTempletContractExtension.getExtension_context());
        WmContractVersionDB versionDB = saveVersion(opUid, "美团配送系统人工提交", contractId, CustomerContractStatus.SIGNING.getCode());
        // 状态变更
        toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), "美团配送系统人工提交");
        // 变更合同状态准备发起签约
        contractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());
        // 合同状态操作日志记录：已提交->签约中
        contractLogService.logStatusChange(oldStatus, contractBo.getBasicBo(), 0, "美团配送系统人工提交");
        applySign(contractBo, versionDB, wmTempletContractExtension, opUid, "美团配送系统人工提交");
        return contractId;
    }

    private void dataSyncToBanma(WmCustomerContractBo contractBo, int contractId, int rollBackStatus, int opUid, String opName, int oldStatus) throws WmCustomerException, TException {
        if (!isPushBusinessContractInfoToBanma(contractId)) {
            log.error("企客合同变更已推送至配送侧，不再推送，wmContractId:{}", contractId);
            return;
        }
        //判断是否有已同步公司客户
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType((long) contractBo.getBasicBo().getParentId(), CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (oldStatus == INSERT || wmCustomerRelDB == null) {
            // 新增流程，先新增客户，再新增合同
            createCustomerDataSyncToBanma(contractBo);
            createContractDataSyncToBanma(contractBo);
            // 调用打标平台给客户打标
            wmLeafCustomerRealService.batchAddCustomerLabel((long) contractBo.getBasicBo().getParentId(), ConfigUtilAdapter.getLong("business_customer_label_id", 0), opUid, opName);
        } else {
            // 更新流程，只更新合同
            updateContractDataSyncToBanma(contractBo, rollBackStatus);
        }
        upsertContractPushLog(contractId, 1);
        log.info("企客合同已推送至配送侧");
    }

    public boolean isPushBusinessContractInfoToBanma(long contractId) {
        WmTempletContractExtension wmTempletContractExtension = wmTempletContractExtensionMapper.selectByPrimaryKeyMaster(contractId);
        if (null == wmTempletContractExtension) {
            log.info("合同:{}无发送记录，不进行推送", contractId);
            return false;
        }
        log.info("wmTempletContractExtension:{}", JSON.toJSONString(wmTempletContractExtension));
        return wmTempletContractExtension.getSyncStatus() == 0;
    }

    public WmCustomerDataBo genWmCustomerDataBoByWmCustomerId(int wmCustomerId) throws WmCustomerException, TException {
        Map<String, String> customerParamMap = new HashMap<>();
        // 客户数据封装
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerId);
        WmCustomerBasicBo wmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
        wmCustomerTransService.transWmCustomerBoUrl(wmCustomerBasicBo, true);
        customerParamMap.put(WmDataSubModuleEnum.WM_CUSTOMER_BASE.getSubModuleName(), JSONObject.toJSONString(wmCustomerBasicBo));
        // kp数据封装
        List<WmCustomerKp> wmCustomerKpList = wmCustomerKpService.getCustomerKpList(wmCustomerId);
        customerParamMap.put(WmDataSubModuleEnum.WM_CUSTOMER_KP.getSubModuleName(), JSONObject.toJSONString(wmCustomerKpList));
        // c1合同封装
        WmTempletContractDB wmTempletContractDB = wmContractService.getValidC1Contract((long) wmCustomerId);
        if (null != wmTempletContractDB) {
            WmTempletContractBasicBo wmTempletContractBasicBo = WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB);
            customerParamMap.put(WmDataSubModuleEnum.WM_CUSTOMER_C1.getSubModuleName(), JSONObject.toJSONString(wmTempletContractBasicBo));
        }
        WmCustomerDataBo wmCustomerDataBo;
        try {
            wmCustomerDataBo = wmSupplyChainDataQueryThriftService.transToWmCustomerDataBySubModule(wmCustomerId, customerParamMap);
        } catch (WmPoiBizException e) {
            log.error("客户信息封装异常");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户信息封装异常");
        }
        return wmCustomerDataBo;
    }

    public void createCustomerDataSyncToBanma(WmCustomerContractBo contractBo) throws WmCustomerException, TException {
        // 客户数据封装
        Integer wmCustomerId = contractBo.getBasicBo().getParentId();
        WmCustomerDataBo wmCustomerDataBo = genWmCustomerDataBoByWmCustomerId(wmCustomerId);
        try {
            wmCustomerDataBo.setPricingMode(contractBo.getBasicBo().getPricingMode());
            LongResult longResult = wmSupplyChainDataSyncThriftService.createCompanyCustomer(wmCustomerDataBo);
            WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType((long) wmCustomerId, CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
            if (null == wmCustomerRelDB) {
                WmCustomerRelDB insertWmCustomerRelDB = new WmCustomerRelDB();
                insertWmCustomerRelDB.setWm_customer_id((long) wmCustomerId);
                insertWmCustomerRelDB.setCustomer_biz_id(longResult.getValue());
                insertWmCustomerRelDB.setBiz_type(CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
                wmCustomerRelMapper.insertSelective(insertWmCustomerRelDB);
            } else {
                log.info("企客公司客户关联关系已存在，不再新增，wmCustomerId:{},customerBizId:{}", wmCustomerId, wmCustomerRelDB.getCustomer_biz_id());
            }
        } catch (WmPoiBizException e) {
            // 推配送侧失败，将企客合同置为暂存状态
            toNextContractStatus(new Long(contractBo.getBasicBo().getTempletContractId()).intValue(), CustomerContractStatus.STAGE.getCode(), "");
            log.error("企客客户信息同步至配送侧异常 customerId:{} 异常信息:{}", contractBo.getBasicBo().getParentId(), e.getMessage());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "企客客户同步异常");
        }
    }

    public void createContractDataSyncToBanma(WmCustomerContractBo contractBo) throws WmCustomerException, TException {
        Integer wmCustomerId = contractBo.getBasicBo().getParentId();
        WmContractBo wmContractBo = new WmContractBo();
        // 1、封装配送客户id
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizTypeFromMaster((long) wmCustomerId, CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        wmContractBo.setPsCustomerId(wmCustomerRelDB.getCustomer_biz_id());
        // 2、封装外卖客户id
        wmContractBo.setWmCustomerId((long) contractBo.getBasicBo().getParentId());
        // 3、封装外卖合同id
        wmContractBo.setWmContractId(contractBo.getBasicBo().getTempletContractId());
        // 4、封装操作类型
        wmContractBo.setOperateType(DataSyncOperateTypeConstant.INSERT);
        // 5.透传入驻参数
        wmContractBo.setLogisticsMap(contractBo.getBasicBo().getLogisticsMap());
        wmContractBo.setPricingMode(contractBo.getBasicBo().getPricingMode());
        wmContractBo.setCategorys(contractBo.getBasicBo().getCategorys());
        wmContractBo.setDueDate(contractBo.getBasicBo().getDueDate());
        try {
            wmSupplyChainDataSyncThriftService.createOrUpdateBusinessCustomerContract(wmContractBo);
        } catch (WmPoiBizException e) {
            // 推配送侧失败，将企客合同置为暂存状态
            toNextContractStatus(new Long(contractBo.getBasicBo().getTempletContractId()).intValue(), CustomerContractStatus.STAGE.getCode(), "");
            log.error("新增企客合同信息同步至配送侧异常 contractBo:{}", JSON.toJSONString(contractBo), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "企客合同同步异常");
        }
    }

    public void updateContractDataSyncToBanma(WmCustomerContractBo contractBo, int rollBackStatus) throws WmCustomerException, TException {
        long wmCustomerId = (long) contractBo.getBasicBo().getParentId();
        if (wmLeafCustomerRealService.customerLabelTypeJudge(wmCustomerId, ConfigUtilAdapter.getLong("business_customer_label_id", 0))) {
            Long wmContractId = contractBo.getBasicBo().getTempletContractId();
            WmContractBo wmContractBo = new WmContractBo();
            // 1、封装配送合同版本id
            WmTempletContractRel wmTempletContractRel = wmTempletContractRelMapper.selectByWmContractIdAndBizType(wmContractId, ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
            if (null != wmTempletContractRel) {
                wmContractBo.setContractVersionId(wmTempletContractRel.getContract_biz_id());
            }
            // 2、封装配送客户id
            WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmCustomerId, CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
            wmContractBo.setPsCustomerId(wmCustomerRelDB.getCustomer_biz_id());
            // 3、封装操作类型
            wmContractBo.setOperateType(DataSyncOperateTypeConstant.UPDATE);
            wmContractBo.setDueDate(contractBo.getBasicBo().getDueDate());
            try {
                wmSupplyChainDataSyncThriftService.createOrUpdateBusinessCustomerContract(wmContractBo);
            } catch (WmPoiBizException e) {
                // 推配送侧失败，将企客合同置为暂存状态
                toNextStatus(new Long(contractBo.getBasicBo().getTempletContractId()).intValue(), rollBackStatus, 0, "");
                log.error("更新企客合同信息同步至配送侧异常");
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "企客合同同步异常");
            }
        } else {
            log.info("客户ID:{}无企客标签，不推送信息至配送侧", wmCustomerId);
        }
    }

    /**
     * 企客合同签约信息封装
     *
     * @param contractBo
     * @param versionDB
     * @return
     * @throws WmCustomerException
     */
    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {
        // 甲方乙方信息
        WmTempletContractSignBo partyASigner = wmContractSignService.getPartyASigner(contractBo.getSignBoList());
        WmTempletContractSignBo partyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT);
        // EcontractBusinessCustomerInfoBo由两部分组成：1.上单框架合同信息，2.企客合同个性化信息
        // 1.上单框架合同信息
        EcontractBusinessCustomerInfoBo businessCustomerInfoBo = new EcontractBusinessCustomerInfoBo();
        businessCustomerInfoBo.setContractNum(contractBo.getBasicBo().getContractNum());
        businessCustomerInfoBo.setValidate(DateUtil.secondsToString(contractBo.getBasicBo().getDueDate()));
        businessCustomerInfoBo.setPartAName(partyASigner.getSignName());
        businessCustomerInfoBo.setPartASignTime(partyASigner.getSignTime());
        WmCustomerKp kpOfSigner = wmCustomerKpService.getCustomerKpOfEffectiveSigner(contractBo.getBasicBo().getParentId());
        if (kpOfSigner != null) {
            businessCustomerInfoBo.setPartAContact(kpOfSigner.getCompellation());
            businessCustomerInfoBo.setPartAContactPhone(kpOfSigner.getPhoneNum());
        }
        businessCustomerInfoBo.setPartBName(partyBSigner.getSignName());
        businessCustomerInfoBo.setPartBContact(partyBSigner.getSignPeople());
        businessCustomerInfoBo.setPartBContactPhone(partyBSigner.getSignPhone());
        businessCustomerInfoBo.setPartBSignTime(partyBSigner.getSignTime());
        businessCustomerInfoBo.setWmCompanyCustomerContractBo(new WmCompanyCustomerContractBo());
        businessCustomerInfoBo.setWmCompanyCustomerContractWithoutPdfBo(new WmCompanyCustomerContractWithoutPdfBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(businessCustomerInfoBo));
        return applyBo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        ContractCheckFilter.businessCustomerContractSaveValidFilter().filter(contractBo, opUid, opName);
        Integer insertId = super.save(contractBo, opUid, opName);
        String businessCustomerENum = ContractNumberUtil.getBusinessCustomerENum(insertId);
        contractBo.getBasicBo().setContractNum(businessCustomerENum);
        log.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, businessCustomerENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), businessCustomerENum);
        // 获取修改前状态
        int oldStatus = wmTempletContractDBMapper.selectByPrimaryKey((long) insertId).getStatus();
        // 更新为待发起签约状态，保证不会多次编辑
        toNextContractStatus(new Long(contractBo.getBasicBo().getTempletContractId()).intValue(), CustomerContractStatus.COMMIT.getCode(), opName);
        // 合同状态操作日志记录：暂存->已提交
        WmTempletContractDB newWmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey((long) insertId);
        WmTempletContractBasicBo newWmTempletContractBasicBo = WmTempletContractTransUtil.templetContractBasicDbToBo(newWmTempletContractDB);
        contractLogService.logStatusChange(oldStatus, newWmTempletContractBasicBo, opUid, opName);
        // 插入发送记录
        upsertContractPushLog(insertId, 0);
        log.info("企客合同新建成功，未推送至配送侧");
        return insertId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        Integer wmContractId = super.update(contractBo, opUid, opName);
        // 插入发送记录
        upsertContractPushLog(contractBo.getBasicBo().getTempletContractId(), 0);
        // 获取修改前状态
        int oldStatus = wmTempletContractDBMapper.selectByPrimaryKey(contractBo.getBasicBo().getTempletContractId()).getStatus();
        // 更新状态
        toNextContractStatus(new Long(contractBo.getBasicBo().getTempletContractId()).intValue(), CustomerContractStatus.COMMIT.getCode(), opName);
        // 合同状态操作日志记录：合同更新前状态->已提交
        WmTempletContractDB newWmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(contractBo.getBasicBo().getTempletContractId());
        WmTempletContractBasicBo newWmTempletContractBasicBo = WmTempletContractTransUtil.templetContractBasicDbToBo(newWmTempletContractDB);
        contractLogService.logStatusChange(oldStatus, newWmTempletContractBasicBo, opUid, opName);
        log.info("企客合同更新成功，未触发推送配送");
        return wmContractId;
    }

    /**
     * upsert企客合同发送记录
     *
     * @param wmContractId
     * @param pushStatus，0未发送，1已发送
     */
    public void upsertContractPushLog(long wmContractId, int pushStatus) {
        WmTempletContractExtension wmTempletContractExtension = wmTempletContractExtensionMapper.selectByPrimaryKey(wmContractId);
        if (null == wmTempletContractExtension) {
            // 新增
            WmTempletContractExtension insertWmTempletContractExtension = new WmTempletContractExtension();
            insertWmTempletContractExtension.setId(wmContractId);
            insertWmTempletContractExtension.setSyncStatus(pushStatus);
            insertWmTempletContractExtension.setCreateStatus(INSERT);
            wmTempletContractExtensionMapper.insertSelective(insertWmTempletContractExtension);
        } else {
            // 更新
            WmTempletContractExtension updateWmTempletContractExtension = new WmTempletContractExtension();
            updateWmTempletContractExtension.setId(wmTempletContractExtension.getId());
            updateWmTempletContractExtension.setSyncStatus(pushStatus);
            updateWmTempletContractExtension.setCreateStatus(UPDATE);
            wmTempletContractExtensionMapper.updateByPrimaryKeySelective(updateWmTempletContractExtension);
            if (Objects.nonNull(updateWmTempletContractExtension) && Objects.nonNull(updateWmTempletContractExtension.getExtension_context())) {
                Cat.logEvent(MetricConstant.METRIC_EXTENSION_UPDATE, "update", WmCustomerConstant.SUCCESS, "");
                MetricHelper.build().name(MetricConstant.METRIC_EXTENSION_UPDATE).count();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBusinessCustomerContractExtraData(Long wmCustomerId, String contractExtraContent, boolean internalPdf) throws TException, WmCustomerException {
        log.info("企客合同补全数据:{},wmCustomerId:{}", contractExtraContent, wmCustomerId);
        try {
            // 记录企客合同补充信息
            WmTempletContractExtension wmTempletContractExtension = new WmTempletContractExtension();
            // 根据外卖客户id获取外卖合同id
            List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectNoInvalidByParentIdAndType(wmCustomerId, WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode());
            if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
                log.info("saveBusinessCustomerContractExtraData#客户{}名下无有效合同", wmCustomerId);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户名下无有效合同");
            }
            // 一个客户下只能有一份企客合同，可直接取
            WmTempletContractDB businessCustomerTempletContract = wmTempletContractDBList.get(0);
            // 获取企客合同的生效时间并记录，针对流程是否需要生成pdf，配送传不同结构的信息
            Long effectiveDate;
            if (internalPdf) {
                WmCompanyCustomerContractBo wmCompanyCustomerContractBo = JSONObject.parseObject(contractExtraContent, WmCompanyCustomerContractBo.class);
                effectiveDate = wmCompanyCustomerContractBo.getEffectiveDate();
                // 记录企客合同乙方签约主体
                updatePartBSignNameInfo(wmCustomerId, SignSubjectEnum.SH_SANKUAI.getDesc());
            } else {
                QkTriggerSignRequestParam requsetParam = JSONObject.parseObject(contractExtraContent, QkTriggerSignRequestParam.class);
                effectiveDate = requsetParam.getEffectiveDate();
            }
            if (null == effectiveDate) {
                log.error("企客合同生效时间为空，签约流程中断");
                throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "企客合同生效日期缺失，无法触发签约流程");
            }
            wmTempletContractDBMapper.updateContractExpectEffectiveDate(businessCustomerTempletContract.getId(), effectiveDate.intValue());
            // 记录企客合同签约数据
            wmTempletContractExtension.setId(businessCustomerTempletContract.getId());
            wmTempletContractExtension.setExtension_context(contractExtraContent);
            wmTempletContractExtensionMapper.updateByPrimaryKeySelective(wmTempletContractExtension);
            if (Objects.nonNull(wmTempletContractExtension) && Objects.nonNull(wmTempletContractExtension.getExtension_context())) {
                Cat.logEvent(MetricConstant.METRIC_EXTENSION_UPDATE, "update", WmCustomerConstant.SUCCESS, "");
                MetricHelper.build().name(MetricConstant.METRIC_EXTENSION_UPDATE).count();
            }
        } catch (Exception e) {
            log.error("企客合同签约数据保存异常", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "企客合同签约数据保存异常");
        }
    }

    public void businessCustomerContractApplySign(Long wmCustomerId) throws TException, WmCustomerException {
        log.info("businessCustomerContractApplySign,wmCustomerId={}", wmCustomerId);
        List<WmTempletContractDB> wmTempletContractDBList = getBusinessCustomerContractByWmCustomerId(wmCustomerId, false);
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            log.error("businessCustomerContractApplySign#客户:{}名下无合法企客合同", wmCustomerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "企客合同发起签约异常");
        }
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBList.get(0);
        Long wmContractId = wmTempletContractDB.getId();
        int opUid = wmTempletContractDB.getOpuid();
        int oldStatus = wmTempletContractDB.getStatus();
        log.info("businessCustomerContractApplySign,wmContractId={}", wmContractId);
        try {
            // 查询外卖侧已经更新的框架合同的签约人信息
            List<WmTempletContractSignDB> wmTempletContractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractId(wmContractId);
            // 待配送侧回传补齐企客合同后，再发起签约流程
            if (wmTempletContractDB.getStatus() == CustomerContractStatus.COMMIT.getCode()) {
                CustomerPaperContractRemarkBo remarkBo = JSON.parseObject(wmTempletContractDB.getOtherItem(), CustomerPaperContractRemarkBo.class);
                // 查询配送侧补齐的企客合同的个性化信息
                WmTempletContractExtension wmTempletContractExtension = wmTempletContractExtensionMapper.selectByPrimaryKeyMaster(wmContractId);
                // 封装发起签约的对象
                WmCustomerContractBo wmCustomerContractBo = createBusinesCustomerSignData(wmTempletContractDB, wmTempletContractSignDBList, wmTempletContractExtension);
                int contractId = new Long(wmContractId).intValue();
                if (remarkBo.getWaitHandPack() != null && remarkBo.getWaitHandPack()) {// 需要手动打包
                    try {
                        // 状态变更
                        toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), "美团配送系统人工提交");
                        // 变更合同状态准备发起签约
                        wmCustomerContractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
                        // 合同状态操作日志记录：已提交->待发起签约
                        contractLogService.logStatusChange(oldStatus, wmCustomerContractBo.getBasicBo(), 0, "美团配送系统人工提交");
                        // 发起待打包
                        ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(wmCustomerId.intValue(), contractId, opUid);
                        wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
                    } catch (WmCustomerException ec) {
                        log.error("【框架合同】发起待打包合同任务失败 contractBo:{} msg:{}", JSON.toJSONString(wmCustomerContractBo), ec.getMsg());
                        throw ec;
                    } catch (Exception e) {
                        log.error("【框架合同】发起待打包合同任务失败 contractBo:{}", JSON.toJSONString(wmCustomerContractBo), e);
                        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
                    }
                } else {
                    WmContractVersionDB versionDB = saveVersion(wmTempletContractDB.getOpuid(), "美团配送系统人工提交", contractId, CustomerContractStatus.SIGNING.getCode());
                    // 状态变更
                    toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), "美团配送系统人工提交");
                    // 变更合同状态准备发起签约
                    wmCustomerContractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());
                    // 合同状态操作日志记录：已提交->签约中
                    contractLogService.logStatusChange(oldStatus, wmCustomerContractBo.getBasicBo(), 0, "美团配送系统人工提交");
                    applySign(wmCustomerContractBo, versionDB, wmTempletContractExtension, wmTempletContractDB.getOpuid(), "美团配送系统人工提交");
                }
            } else {
                log.error("businessCustomerContractApplySign#企客配送服务合同还未补全，暂不发起签约，contractId:{}", wmContractId);
            }
        } catch (Exception e) {
            log.error("#businessCustomerContractApplySign,exception", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "企客合同发起签约异常");
        }
    }

    private WmCustomerContractBo createBusinesCustomerSignData(WmTempletContractDB wmTempletContractDB, List<WmTempletContractSignDB> wmTempletContractSignDBList, WmTempletContractExtension wmTempletContractExtension) {
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo wmTempletContractBasicBo = WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB);
        wmCustomerContractBo.setBasicBo(wmTempletContractBasicBo);
        List<WmTempletContractSignBo> wmTempletContractSignBoList = WmTempletContractTransUtil.templetSignDbToBoList(wmTempletContractSignDBList);
        wmCustomerContractBo.setSignBoList(wmTempletContractSignBoList);
        wmCustomerContractBo.setExtraData(wmTempletContractExtension.getExtension_context());
        return wmCustomerContractBo;
    }

    @Override
    public Boolean signFail(long templetContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        Boolean effect = super.signFail(templetContractId, failReason, opUid, opUname);
        // 签约结果推送给到家配送侧
        try {
            notifySignResult(templetContractId, WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(), false, failReason);
        } catch (Exception e) {
            log.warn("签约失败结果推送配送侧异常，不阻塞流程");
        }
        return effect;
    }

    public void notifySignResult(long wmContractId, int contractType, boolean result, String resultMsg) throws WmCustomerException, TException {
        if (contractType != WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode()) {
            return;
        }
        // 配送客户id
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(wmContractId);
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmTempletContractDB.getParentId(), CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (null == wmCustomerRelDB) {
            log.info("notifySignResult#外卖合同:{}无对应配送客户", wmContractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同绑定关系异常");
        }
        log.info("notifySignResult#外卖合同:{}对应配送客户:{}签约结果通知配送", wmContractId, wmCustomerRelDB.getCustomer_biz_id());
        try {
            wmSupplyChainDataSyncThriftService.notifyContractSignResult(wmCustomerRelDB.getCustomer_biz_id(), result, resultMsg);
        } catch (WmPoiBizException e) {
            log.error("合同生效推送配送侧异常", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "合同生效变更推送异常");
        }
    }

    public void pushBusinessContractEffectResult(Long wmCustomerId, Long psContractVersionId) throws TException, WmCustomerException {
        List<WmTempletContractDB> wmTempletContractDBList = getBusinessCustomerContractByWmCustomerId(wmCustomerId, false);
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            log.error("pushBusinessContractEffectResult#客户:{}名下无合法企客合同", wmCustomerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "企客合同发起签约异常");
        }
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBList.get(0);
        Long wmContractId = wmTempletContractDB.getId();
        log.info("pushBusinessContractEffectResult#配送企客合同生效，配送合同版本id:{}，外卖客户id:{}", psContractVersionId, wmCustomerId);
        WmTempletContractRel upsertContractRel = new WmTempletContractRel();
        upsertContractRel.setWm_templet_contract_id(wmContractId);
        upsertContractRel.setContract_biz_id(psContractVersionId);
        upsertContractRel.setBiz_type(ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        WmTempletContractRel wmTempletContractRel = wmTempletContractRelMapper.selectByWmContractIdAndBizType(wmContractId, ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (null == wmTempletContractRel) {
            //新增记录
            wmTempletContractRelMapper.insertSelective(upsertContractRel);
        } else {
            wmTempletContractRelMapper.updateByWmContractId(upsertContractRel);
        }
        BmCompanyContractChangeRequestDTO bmCompanyContractChangeRequestDTO = new BmCompanyContractChangeRequestDTO();
        bmCompanyContractChangeRequestDTO.setCustomerId(wmCustomerId);
        bmCompanyContractChangeRequestDTO.setBmCompanyContractId(psContractVersionId);
        try {
            wmPoiLogisticsBmContractThriftService.notifyBmCompanyContractChange(bmCompanyContractChangeRequestDTO);
        } catch (Exception e) {
            log.error("企客电子合同生效信息推送至供应链配送异常，外卖合同id:{}", wmContractId, e);
        }
    }

    /**
     * 企客合同废除
     *
     * @param contractId
     * @param opUid
     * @param opUname
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
        wmCustomerContractBo.getBasicBo().setTempletContractId((long) contractId);
        // 合同废除校验
        ContractCheckFilter.contractAbolishFilter().filter(wmCustomerContractBo, opUid, opUname);
        // 废除有两种情况，1.外卖单方面废除；2.外卖配送双方共同废除
        if (isOnlyWmInvalidBusinessCustomerContract(contractId)) {
            super.invalid(contractId, opUid, opUname);
        } else {
            WmTempletContractDB oldWmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
            int oldStatus = oldWmTempletContractDB.getStatus();
            contractLogService.logInvalid(oldWmTempletContractDB.getParentId().intValue(), contractId, oldWmTempletContractDB.getNumber(), opUid, opUname);
            // 更新线下表为废除确认中
            toNextContractStatus(contractId, CustomerContractStatus.ABOLISH_ING.getCode(), opUname);
            WmTempletContractDB newWmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
            WmTempletContractBasicBo logWmTempletContractBasicBo = WmTempletContractTransUtil.templetContractBasicDbToBo(newWmTempletContractDB);
            contractLogService.logStatusChange(oldStatus, logWmTempletContractBasicBo, opUid, opUname);
            // 通知配送侧
            Pair<Long, Long> psParamPair = getPsCustomerIdAndPsContractIdByWmContractId((long) contractId);
            try {
                wmSupplyChainDataSyncThriftService.pushContractCancelNotify(psParamPair.getLeft());
            } catch (WmPoiBizException e) {
                //todo:dxm，调配送抛出异常是否需要回滚状态
                log.info("企客合同废除首次通知配送侧异常，外卖合同id:{}", contractId, e);
                return false;
            }
        }
        return true;
    }

    private boolean isOnlyWmInvalidBusinessCustomerContract(int contractId) throws WmCustomerException {
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
        if (null == wmTempletContractDB) {
            log.error("外卖合同:{}无有效数据，合同失效流程异常", contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "无有效合同，废除失败");
        }
        // 1.如果有合同关联关系，不能单方面废除；2.如果没有关联关系但是pushstatus为已推送1，也不能单方面废除
        WmTempletContractRel wmTempletContractRel = wmTempletContractRelMapper.selectByWmContractIdAndBizType((long) contractId, CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (wmTempletContractRel == null) {
            WmTempletContractExtension wmTempletContractExtension = wmTempletContractExtensionMapper.selectByPrimaryKey((long) contractId);
            if (wmTempletContractExtension.getSyncStatus() == 1) {
                log.info("外卖合同:{}未与配送合同建立关联关系，但已经推送至配送，不可单方面操作废除", contractId);
                return false;
            }
            log.info("外卖合同:{}未与配送合同建立关联关系，且未推送至配送，可单方面操作废除", contractId);
            return true;
        } else {
            return false;
        }
    }

    public void pushAbolishBusinessCustomerContractResult(Long wmCustomerId, Boolean abolishResult, String failMsg) throws TException, WmCustomerException {
        // TODO:dxm，状态机更新异常捕获
        List<WmTempletContractDB> wmTempletContractDBList = getBusinessCustomerContractByWmCustomerId(wmCustomerId, false);
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            log.info("外卖客户:{}名下无生效合同", wmCustomerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "外卖侧无有效企客合同");
        }
        WmTempletContractDB businessCustomerContract = wmTempletContractDBList.get(0);
        Long wmContractId = businessCustomerContract.getId();
        Integer oldStatus = businessCustomerContract.getStatus();
        // 获取操作人名称以便记录日志
        WmEmploy wmEmploy = null;
        try {
            wmEmploy = wmEmployService.getById(businessCustomerContract.getOpuid());
        } catch (Exception e) {
            log.warn("企客合同废除驳回记录操作日志获取操作人名称异常，操作人id:{}，不阻塞流程", businessCustomerContract.getOpuid());
        }
        String opName = wmEmploy == null ? "" : wmEmploy.getName();
        if (abolishResult) {
            // 配送侧废除成功
            try {
                abolishBusinessCustomerResultDBUpdate(businessCustomerContract, opName);
            } catch (Exception e) {
                log.info("企客合同废弃失败，外卖合同id:{}", wmContractId, e);
                // todo:dxm，状态机异常catch住不抛
                throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "外卖侧合同废除状态更新失败");
            }
            log.info("企客合同废弃成功，外卖合同id:{}", wmContractId);
        } else {
            // 配送侧废除失败,更新线下表状态
            log.info("企客合同配送侧废除失败，配送合同版本id:pushAbolishBusinessCustomerContractResult，外卖合同id:{}，失败原因:{}", wmContractId, failMsg);
            toNextStatus(wmContractId.intValue(), CustomerContractStatus.ABOLISH_FAIL.getCode(), 0, "");
            // 操作日志记录
            WmTempletContractDB newWmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(wmContractId);
            WmTempletContractBasicBo logWmTempletContractBasicBo = WmTempletContractTransUtil.templetContractBasicDbToBo(newWmTempletContractDB);
            contractLogService.logStatusChange(oldStatus, logWmTempletContractBasicBo, newWmTempletContractDB.getOpuid(), opName);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void abolishBusinessCustomerResultDBUpdate(WmTempletContractDB wmTempletContractDB, String opUname) throws TException, WmCustomerException {
        // 更新合同正式表
        super.invalid(new Long(wmTempletContractDB.getId()).intValue(), wmTempletContractDB.getOpuid(), opUname);
        // 更新合同关联关系表
        wmTempletContractRelMapper.updateValidByContractIdAndBizType(wmTempletContractDB.getId(), 0, ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
    }

    private Pair<Long, Long> getPsCustomerIdAndPsContractIdByWmContractId(Long wmContractId) {
        // 获取配送合同版本id
        WmTempletContractRel wmTempletContractRel = wmTempletContractRelMapper.selectByWmContractIdAndBizType(wmContractId, ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        Long psContractVersionId = wmTempletContractRel == null ? null : wmTempletContractRel.getContract_biz_id();
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(wmContractId);
        // 获取配送客户id
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmTempletContractDB.getParentId(), CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        Long psCustomersId = wmCustomerRelDB.getCustomer_biz_id();
        return Pair.of(psCustomersId, psContractVersionId);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long pushBusinessCustomerPaperContractInfo(Long wmCustomerId, Long psContractVersionId,Long dueDate) throws TException, WmCustomerException {
        // 新建or更新企客纸质合同
        Long wmContractId;
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndTypeAndStatus(wmCustomerId, WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode(), CustomerContractStatus.EFFECT.getCode());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            WmTempletContractDB wmTempletContractDB = new WmTempletContractDB();
            wmTempletContractDB.setParentId(wmCustomerId);
            wmTempletContractDB.setType(WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode());
            wmTempletContractDB.setStatus(CustomerContractStatus.EFFECT.getCode());
            wmTempletContractDB.setOtherItem("");// 适配字段无默认值且notnull
            if(dueDate != null && dueDate > 0){
                wmTempletContractDB.setDueDate(dueDate.intValue());
            }
            wmTempletContractDBMapper.insertSelective(wmTempletContractDB);
            wmContractId = wmTempletContractDB.getId();
        } else {
            wmContractId = wmTempletContractDBList.get(0).getId();
            if(dueDate != null && dueDate > 0){
                wmTempletContractDBMapper.updateDueDateById(wmContractId, dueDate.intValue());
            }
        }
        log.info("外卖合同id:{}，配送合同id:{}", wmContractId, psContractVersionId);
        // 建立外卖合同与配送合同的关系
        WmTempletContractRel rel = new WmTempletContractRel();
        rel.setWm_templet_contract_id(wmContractId);
        rel.setContract_biz_id(psContractVersionId);
        rel.setBiz_type(ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        WmTempletContractRel wmTempletContractRel = wmTempletContractRelMapper.selectByWmContractIdAndBizType(wmContractId, ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (null == wmTempletContractRel) {
            wmTempletContractRelMapper.insertSelective(rel);
        } else {
            rel.setId(wmTempletContractRel.getId());
            wmTempletContractRelMapper.updateByPrimaryKeySelective(rel);
        }
        // 通知供应链配送，纸质合同生效
        BmCompanyContractChangeRequestDTO bmCompanyContractChangeRequestDTO = new BmCompanyContractChangeRequestDTO();
        bmCompanyContractChangeRequestDTO.setCustomerId(wmCustomerId);
        bmCompanyContractChangeRequestDTO.setBmCompanyContractId(psContractVersionId);
        try {
            wmPoiLogisticsBmContractThriftService.notifyBmCompanyContractChange(bmCompanyContractChangeRequestDTO);
        } catch (Exception e) {
            log.error("企客纸质合同生效信息推送至供应链配送异常，外卖合同id:{}", wmContractId, e);
            // 异常重试
            int retryTime = 3;
            ExecutorUtil.execWithRetry(new ExecutorUtil.Executor<Boolean>() {
                @Override
                public Boolean exec() throws Exception {
                    try {
                        wmPoiLogisticsBmContractThriftService.notifyBmCompanyContractChange(bmCompanyContractChangeRequestDTO);
                    } catch (WmHeronLogisticsException e) {
                        log.error("企客纸质合同生效信息推送至供应链配送重试异常，外卖合同id:{}", wmContractId, e);
                        return null;
                    }
                    return Boolean.TRUE;
                }

                @Override
                public boolean shouldRetry(Boolean result) throws Exception {
                    return !Boolean.TRUE.equals(result);
                }

                @Override
                public void execComplete(Boolean o) {
                    log.error("企客纸质合同生效信息推送至供应链配送重试{}次后失败，外卖合同id:{}", retryTime, wmContractId);
                }
            }, retryTime);
        }
        return wmContractId;
    }

    public void pushC1ContractEffectInfo(Integer wmContractId) throws WmCustomerException, TException {
        log.info("待推送C1合同:{}", wmContractId);
        // 通过wmContractId查询C1合同生效日期
        WmTempletContractDB wmTempletContractDB = wmTempletContractAuditedDBMapper.selectByIdMaster((long) wmContractId);
        if (null == wmTempletContractDB) {
            log.info("无生效的C1合同:{}，不推送", wmContractId);
            return;
        }
        // 获取配送客户id
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmTempletContractDB.getParentId(), CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (null == wmCustomerRelDB) {
            log.info("C1合同:{}对应客户未与配送企客客户建立关联关系，不推送", wmContractId);
            return;
        }
        // 获取C1合同所属客户名下的企客合同对应的配送合同版本id
        Long wmCustomerId = wmTempletContractDB.getParentId();
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndTypes(wmCustomerId, Lists.newArrayList(WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(), WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode()));
        List<WmTempletContractDB> removeInvalidContractList = wmTempletContractDBList.stream().filter(contract -> contract.getStatus() != CustomerContractStatus.INVALID.getCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(removeInvalidContractList)) {
            log.info("外卖客户:{}名下无企客合同，不推送", wmCustomerId);
            return;
        }
        WmTempletContractDB businessCustomerContract = removeInvalidContractList.get(0);
        WmTempletContractRel wmTempletContractRel = wmTempletContractRelMapper.selectByWmContractIdAndBizType(businessCustomerContract.getId(), ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (null == wmTempletContractRel) {
            log.info("外卖客户:{},企客合同:{}未与配送企客合同建立关联关系，不推送", wmCustomerId, businessCustomerContract.getId());
            return;
        }
        log.info("C1合同:{},有效期变更推送，配送客户id:{}，配送合同版本id:{}", JSON.toJSONString(wmTempletContractDB), wmCustomerRelDB.getCustomer_biz_id(), wmTempletContractRel.getContract_biz_id());
        try {
            wmSupplyChainDataSyncThriftService.pushC1ContractDueDate(wmCustomerRelDB.getCustomer_biz_id(), wmTempletContractDB.getDueDate(), wmTempletContractRel.getContract_biz_id());
        } catch (WmPoiBizException e) {
            log.error("C1合同有效期变更推送失败，外卖合同id:{}", wmContractId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "C1合同有效期变更推送失败");
        }
    }

    public void cancelSign(Integer wmContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        notifySignResult(wmContractId, WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(), false, failReason);
    }

    private List<WmTempletContractDB> getBusinessCustomerContractByWmCustomerId(long wmCustomerId, boolean isIncludeInvalid) {
        List<WmTempletContractDB> wmTempletContractDBList;
        if (isIncludeInvalid) {
            wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndTypes(wmCustomerId, Lists.newArrayList(WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(), WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode()));
        } else {
            wmTempletContractDBList = wmTempletContractDBMapper.selectValidByParentIdAndTypes(wmCustomerId, Lists.newArrayList(WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(), WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode()));
        }
        return wmTempletContractDBList;
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, long contractId, int opUid) {
        return new ManualTaskApplyBo.Builder().commitUid(opUid).customerId(wmCustomerId).applyTypeEnum(EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT).bizId(contractId).build();
    }

    public void retractBussinessContract(Long contractId, Long customerId, Integer opUid, String opName) throws WmCustomerException, TException {
        //无生效企客合同走入驻撤销
        WmTempletContractDB businessContractAudited = wmTempletContractAuditedDBMapper.selectByPrimaryKey(contractId);
        if (businessContractAudited == null) {
            businessContractAudited = wmTempletContractAuditedDBMapper.selectByIdMaster(contractId);
        }
        log.info("retractBussinessContract#businessContractAudited:{}", JSON.toJSONString(businessContractAudited));
        if (businessContractAudited == null) {
            if(MccConfig.supportBusinessContractRetractSwitch()) {
                handleRetractSuccessForSettle(contractId, customerId, opUid, opName);
                log.info("retractBussinessContract#handleRetractSuccessForSettle:{}", JSON.toJSONString(businessContractAudited));
                return;
            } else {
                log.info("retractBussinessContract#handleRetractSuccessForSettle,客户无已生效的企客合同，不允许撤销修改:{}", JSON.toJSONString(businessContractAudited));
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户无已生效的企客合同，不允许撤销修改");
            }
        }
        //非已提交状态不允许撤销
        WmTempletContractDB businessContract = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        if (businessContract == null) {
            businessContract = wmTempletContractDBMapper.selectByPrimaryKeyMaster(contractId);
        }
        log.info("retractBussinessContract#businessContract:{}", JSON.toJSONString(businessContract));
        if (businessContract.getStatus() != CustomerContractStatus.COMMIT.getCode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前企客合同状态不为已提交，不允许撤销修改");
        }
        //请求配送确认是否可撤销
        try{
            WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(customerId, CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
            if(wmCustomerRelDB == null){
                log.warn("retractBussinessContract#外卖客户:{}，无关联企客客户", customerId);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "系统异常");
            }
            Long psCustomerId = wmCustomerRelDB.getCustomer_biz_id();
            BooleanResult retractResult = wmSupplyChainDataSyncThriftService.retractBussinessContract(psCustomerId, contractId);
            if(retractResult.isRes()){
                handleRetractSuccess(contractId, opUid, opName);
            }
            log.info("retractBussinessContract#企客合同撤销成功，客户:{}，合同:{}", customerId, contractId);
        }catch(WmPoiBizException e1){
            log.error("retractBussinessContract#企客合同撤销失败，客户:{}，合同:{}", customerId, contractId, e1);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e1.getMsg());
        }catch(Exception e2){
            log.error("retractBussinessContract#企客合同撤销系统异常，客户:{}，合同:{}", customerId, contractId, e2);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "系统异常");
        }
    }

    private void handleRetractSuccess(Long wmContractId, Integer opUid, String opName) throws WmCustomerException {
        // 获取修改前状态
        WmTempletContractDB contractDB = wmTempletContractDBMapper.selectByPrimaryKey(wmContractId);
        int oldStatus = contractDB.getStatus();
        // 更新状态
        toNextContractStatus(new Long(contractDB.getId()).intValue(), CustomerContractStatus.EFFECT.getCode(), opName);
        // 合同状态操作日志记录：合同更新前状态->已提交
        WmTempletContractDB newWmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(contractDB.getId());
        WmTempletContractBasicBo newWmTempletContractBasicBo = WmTempletContractTransUtil.templetContractBasicDbToBo(newWmTempletContractDB);
        contractLogService.logStatusChange(oldStatus, newWmTempletContractBasicBo, opUid, opName);
    }

    /**
     * 新签场景取消企客流程
     *
     * @param contractId
     * @param customerId
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    private void handleRetractSuccessForSettle(Long contractId, Long customerId, Integer opUid, String opName) throws WmCustomerException {
        log.info("handleRetractSuccessForSettle,contractId:{},customerId:{},opUid:{},opName:{}", contractId, customerId, opUid, opName);
        // 获取修改前状态
        WmTempletContractDB contractDB = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        if (contractDB == null) {
            log.info("handleRetractSuccessForSettle 无对应合同,contractId:{},customerId:{}", contractId, customerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户企客合同不存在，不允许撤销");
        }
        try {
            WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(customerId, CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
            if (wmCustomerRelDB == null) {
                log.warn("retractBussinessContract#外卖客户:{}，无关联企客客户", customerId);
                invalidForSettle(contractDB.getId().intValue(), opUid,opName);
                log.info("handleRetractSuccessForSettle#外卖侧企客合同撤销成功，客户:{}，合同:{}", customerId, contractId);
                return;
            }
            //请求配送确认是否可撤销
            Long psCustomerId = wmCustomerRelDB.getCustomer_biz_id();
            log.info("handleRetractSuccessForSettle#retractBussinessContract,psCustomerId:{},contractId:{}", psCustomerId, contractId);
            BooleanResult retractResult = wmSupplyChainDataSyncThriftService.retractBussinessContract(psCustomerId, contractId);
            if (retractResult.isRes()) {
                // 删除
                invalidForSettle(contractDB.getId().intValue(), opUid,opName);
                log.info("handleRetractSuccessForSettle#企客合同撤销成功，客户:{}，合同:{}", customerId, contractId);
            } else {
                log.warn("handleRetractSuccessForSettle#企客合同撤销失败，客户:{}，合同:{}", customerId, contractId);
                throw new WmPoiBizException(500,"企客公司合同撤销失败");
            }
        } catch (WmPoiBizException e1) {
            log.error("handleRetractSuccessForSettle#企客合同撤销失败，客户:{}，合同:{}", customerId, contractId, e1);
            DaxiangUtil.push("<EMAIL>",ServiceEnvUtils.getEnv()+",撤销企客斑马公司合同失败，客户:"+customerId+",contractId:"+contractId, MccConfig.getContractEffectNotifyMisidList());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e1.getMsg());
        } catch (Exception e2) {
            log.error("handleRetractSuccessForSettle#企客合同撤销系统异常，客户:{}，合同:{}", customerId, contractId, e2);
            DaxiangUtil.push("<EMAIL>",ServiceEnvUtils.getEnv()+",撤销企客斑马公司合同失败，客户:"+customerId+",contractId:"+contractId, MccConfig.getContractEffectNotifyMisidList());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "系统异常");
        }
    }

    /**
     * 新签场景失效企客公司合同
     * @param contractId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    private void invalidForSettle(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        log.info("invalidForSettle#废除合同  contractId:{} opUid:{} opUname:{}", contractId, opUid, opUname);
        WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
        if (offlineContract == null) {
            log.info("invalidForSettle#废除合同  contractId:{} 不存在该合同", contractId);
            return;
        }
        log.info("invalidForSettle#废除合同  contractId:{} 存在生效合同", contractId);
        wmTempletContractDBMapper.invalidContract((long) contractId, opUid);
        wmTempletContractSignDBMapper.invalid((long) contractId, opUid);
        contractLogService.logInvalid(offlineContract.getParentId().intValue(), contractId, offlineContract.getNumber(), opUid, opUname);
    }

    private void applySign(WmCustomerContractBo contractBo, WmContractVersionDB versionDB, WmTempletContractExtension contractExtension, int opUid, String opName) throws WmCustomerException, TException {
        //区分流程
        String businessExtContext = contractExtension.getExtension_context();
        if (StringUtils.isEmpty(businessExtContext)) {
            log.error("WmBusinessCustomerTempletService.applySign#企客合同数据缺失，发起签约失败，客户:{}，合同:{}", contractBo.getBasicBo().getParentId(), contractBo.getBasicBo().getTempletContractId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "公司合同数据缺失，发起签约失败");
        }
        WmCompanyCustomerContractWithoutPdfBo wmCompanyCustomerContractWithoutPdfBo = JSONObject.parseObject(businessExtContext, WmCompanyCustomerContractWithoutPdfBo.class);
        boolean generatePdf = false;
        if (wmCompanyCustomerContractWithoutPdfBo.getQkPdfInfoBo() != null && wmCompanyCustomerContractWithoutPdfBo.getQkPdfInfoBo().isGeneratePdf()) {
            generatePdf = true;
        }
        EcontractTaskApplyBo applyBo;
        //分流程发起签约
        if (generatePdf) {
            //无pdf创建流程
            applyBo = buildEcontractTaskApplyBoWithoutPdf(contractBo, versionDB, contractExtension);
        } else {
            //有pdf创建流程
            applyBo = buildEcontractTaskApplyBoWithPdf(contractBo, versionDB, contractExtension);
        }
        applyBo.setCommitUid(opUid);
        applyBo.setManualBatchId(contractBo.getManualBatchId());
        applyBo.setWmCustomerId(contractBo.getBasicBo().getParentId());
        log.info("企客合同开始签约 bo :{}", JSON.toJSONString(applyBo, SerializerFeature.WriteMapNullValue));
        String transactionId;
        try {
            transactionId = String.valueOf(wmEcontractTaskApplyService.applyTask(applyBo).getValue());
        } catch (WmCustomerException e) {
            log.info(e.getMsg(), e);
            signFail(contractBo.getBasicBo().getTempletContractId(), e.getMessage(), opUid, opName);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (WmServerException e) {
            log.info(e.getMsg(), e);
            signFail(contractBo.getBasicBo().getTempletContractId(), e.getMsg(), opUid, opName);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            signFail(contractBo.getBasicBo().getTempletContractId(), "系统异常", opUid, opName);
            throw new TException(e.getMessage());
        }
        log.info("合同签约成功  contractId：{}  transactionId：{}", contractBo.getBasicBo().getTempletContractId(), transactionId);
        contractVersionService.updateTransactionId(transactionId, versionDB.getVersion_number());
    }

    private EcontractTaskApplyBo buildEcontractTaskApplyBoWithPdf(WmCustomerContractBo contractBo, WmContractVersionDB versionDB, WmTempletContractExtension contractExtension) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = this.buildEcontractTaskApplyBo(contractBo, versionDB);
        EcontractBusinessCustomerInfoBo businessCustomerInfoBo = JSONObject.parseObject(applyBo.getApplyInfoBo(), EcontractBusinessCustomerInfoBo.class);
        log.info("企客框架合同签约基础数据:{}", JSON.toJSONString(businessCustomerInfoBo));
        WmCompanyCustomerContractBo wmCompanyCustomerContractDB = JSONObject.parseObject(contractExtension.getExtension_context(), WmCompanyCustomerContractBo.class);
        BeanUtils.copyProperties(wmCompanyCustomerContractDB, businessCustomerInfoBo.getWmCompanyCustomerContractBo());
        log.info("企客框架合同签约全部数据:{}", JSON.toJSONString(businessCustomerInfoBo));
        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(businessCustomerInfoBo));
        return applyBo;
    }

    private EcontractTaskApplyBo buildEcontractTaskApplyBoWithoutPdf(WmCustomerContractBo contractBo, WmContractVersionDB versionDB, WmTempletContractExtension contractExtension) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = this.buildEcontractTaskApplyBo(contractBo, versionDB);
        EcontractBusinessCustomerInfoBo businessCustomerInfoBo = JSONObject.parseObject(applyBo.getApplyInfoBo(), EcontractBusinessCustomerInfoBo.class);
        WmCompanyCustomerContractWithoutPdfBo wmCompanyCustomerContractWithoutPdfBo = JSONObject.parseObject(contractExtension.getExtension_context(), WmCompanyCustomerContractWithoutPdfBo.class);
        log.info("wmCompanyCustomerContractWithoutPdfBo:{}", JSON.toJSONString(wmCompanyCustomerContractWithoutPdfBo));
        BeanUtils.copyProperties(wmCompanyCustomerContractWithoutPdfBo, businessCustomerInfoBo.getWmCompanyCustomerContractWithoutPdfBo());
        log.info("businessCustomerInfoBo:{}", JSON.toJSONString(businessCustomerInfoBo));
        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(businessCustomerInfoBo));
        return applyBo;
    }

    public boolean generatePdf(String context, int customerId) {
        if (StringUtils.isEmpty(context)) {
            log.error("generatePdf#企客公司合同签约任务上下文为空，customerId:{}", customerId);
        }
        EcontractBusinessCustomerInfoBo businessCustomerInfoBo = JSONObject.parseObject(context, EcontractBusinessCustomerInfoBo.class);
        WmCompanyCustomerContractWithoutPdfBo wmCompanyCustomerContractWithoutPdfBo = businessCustomerInfoBo.getWmCompanyCustomerContractWithoutPdfBo();
        boolean generatePdf = false;
        if (wmCompanyCustomerContractWithoutPdfBo.getQkPdfInfoBo() == null ? false : wmCompanyCustomerContractWithoutPdfBo.getQkPdfInfoBo().isGeneratePdf()) {
            generatePdf = true;
        }
        log.info("企客公司合同是否已生成pdf:{}", generatePdf);
        return generatePdf;
    }

    public String queryCaSubjectName(String context, int customerId) {
        if (StringUtils.isEmpty(context)) {
            log.error("queryCaSubjectName#企客公司合同签约任务上下文为空，customerId:{}", customerId);
            return StringUtils.EMPTY;
        }
        EcontractBusinessCustomerInfoBo businessCustomerInfoBo = JSONObject.parseObject(context, EcontractBusinessCustomerInfoBo.class);
        WmCompanyCustomerContractWithoutPdfBo wmCompanyCustomerContractWithoutPdfBo = businessCustomerInfoBo.getWmCompanyCustomerContractWithoutPdfBo();
        return wmCompanyCustomerContractWithoutPdfBo.getQkCaInfoBo().getPartBSubjectName();
    }

    public Map<String, String> queryEstampInfoMap(String context, int customerId) {
        if (StringUtils.isEmpty(context)) {
            log.error("queryEstampInfoMap#企客公司合同签约任务上下文为空，customerId:{}", customerId);
            return null;
        }
        EcontractBusinessCustomerInfoBo businessCustomerInfoBo = JSONObject.parseObject(context, EcontractBusinessCustomerInfoBo.class);
        WmCompanyCustomerContractWithoutPdfBo wmCompanyCustomerContractWithoutPdfBo = businessCustomerInfoBo.getWmCompanyCustomerContractWithoutPdfBo();
        return wmCompanyCustomerContractWithoutPdfBo.getQkEstampInfoBo().getEstampInfoMap();
    }

    public void updatePartBSignNameInfo(Long wmCustomerId, String signName) throws WmCustomerException {
        //获取企客合同id
        List<WmTempletContractDB> contractDBList = wmTempletContractDBMapper.selectByParentIdAndTypeAndStatus(wmCustomerId, WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(), CustomerContractStatus.COMMIT.getCode());
        if (CollectionUtils.isEmpty(contractDBList)) {
            log.error("客户:{}，无对应企客合同", wmCustomerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "企客合同缺失");
        }
        wmTempletContractSignDBMapper.updateSignNameByTempletId(contractDBList.get(0).getId(), "B", signName);
        log.info("客户:{}，企客合同:{}，乙方主体:{}，更新成功", wmCustomerId, contractDBList.get(0).getId(), signName);
    }

    @Override
    public Integer createWmCustomerContract(CreateWmCustomerContractReq req) throws WmCustomerException, TException {
        log.info("#createWmCustomerContract 创建企客公司合同 req:{}", JSON.toJSONString(req));
        if (req.getOpUid() == null || req.getOpUid() <= 0) {
            log.warn("#createWmCustomerContract 操作人不能为空 req:{}", JSON.toJSONString(req));
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "操作人不能为空");
        }
        Integer customerId = req.getWmCustomerId();
        WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerByIdOrMtCustomerId(customerId);
        if (wmCustomerBasicBo == null) {
            log.warn("#createWmCustomerContract 客户不存在 req:{}", JSON.toJSONString(req));
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户不存在");
        }
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(customerId);
        if (wmCustomerKp == null) {
            log.warn("#createWmCustomerContract Kp不存在 req:{}", JSON.toJSONString(req));
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "Kp不存在");
        }
        if(!super.isEffectiveCustomerAndKp(customerId)){
            log.warn("#createWmCustomerContract 客户或KP未生效 req:{}", JSON.toJSONString(req));
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户或KP未生效，不能发起标准化企客框架合同的创建");
        }
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        //1.构建框架合同基础信息
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setContractNum("电子合同保存后自动生成编号");
        basicBo.setParentId(req.getWmCustomerId());
        basicBo.setType(req.getType());
        //命中灰度的客户，有效期设置为当前时间+1年
        if(grayConfigHelper.isQkBusinessCustomerPeriodGray(customerId)){
            basicBo.setDueDate(DateUtils.getNYearLastSecond(1));
        }
        CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
        basicBo.setExtStr(JSON.toJSONString(paperContractRemarkBo));
        contractBo.setBasicBo(basicBo);
        contractBo.setIgnoreExistAnotherSignTypeContract(true);
        contractBo.setPackWay(req.getPackWay());
        //2.构建签约人信息，签约甲乙方Bo
        List<WmTempletContractSignBo> signList = Lists.newArrayList();
        //2.1 甲方
        WmTempletContractSignBo partyA = new WmTempletContractSignBo();
        partyA.setSignId(customerId);
        partyA.setSignName(wmCustomerBasicBo.getCustomerName());
        partyA.setSignPeople(wmCustomerKp.getCompellation());
        partyA.setSignPhone(wmCustomerKp.getPhoneNum());
        partyA.setSignTime(DateUtils.getNDay(0));
        partyA.setSignType("A");
        signList.add(partyA);
        //2.2 乙方
        WmTempletContractSignBo partyB = new WmTempletContractSignBo();
        partyB.setSignId(0);
        partyB.setSignName("");
        partyB.setSignPeople(req.getOpName());
        partyB.setSignPhone(empServiceAdaptor.getPhone(req.getOpUid()));
        partyB.setSignTime(DateUtils.getNDay(0));
        partyB.setSignType("B");
        signList.add(partyB);
        contractBo.setSignBoList(signList);
        contractBo.getBasicBo().setCategorys(req.getCategorys());
        contractBo.getBasicBo().setLogisticsMap(req.getLogisticsMap());
        contractBo.getBasicBo().setPricingMode(req.getPricingMode());
        log.info("#createWmCustomerContract 创建企客公司合同 startSign:{}", JSON.toJSONString(contractBo));
        return startSign(contractBo, req.getOpUid(), req.getOpName());
    }
}
