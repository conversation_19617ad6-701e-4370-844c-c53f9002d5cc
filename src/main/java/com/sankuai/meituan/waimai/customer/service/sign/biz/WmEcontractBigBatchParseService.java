package com.sankuai.meituan.waimai.customer.service.sign.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchContextDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper;
import com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchContextDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.contract.SignContractTaskParam;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBigContextManageService;
import com.sankuai.meituan.waimai.customer.service.sign.context.WmSignBatchContext;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpRequest;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-07-22 22:13
 * Email: <EMAIL>
 * Desc:
 */
@Service
@Slf4j
public class WmEcontractBigBatchParseService {

    public static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractBigBatchParseService.class);

    @Autowired
    private WmEcontractBatchDBMapper wmEcontractBatchDBMapper;
    @Autowired
    private WmEcontractBatchContextDBMapper wmEcontractBatchContextDBMapper;
    @Autowired
    private WmEcontractSignBigContextManageService wmEcontractSignBigContextManageService;
    @Autowired
    private WmSignBatchContext wmSignBatchContext;

    // com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper.insert
    public long insert(WmEcontractSignBatchDB batchDB) {
        if (wmEcontractSignBigContextManageService.batchGarySwitch(batchDB)) {
            //大文本上传
            Map<String, String> bigTaskContextMap = wmEcontractSignBigContextManageService.batchBigContextRemove(batchDB);
            wmEcontractBatchDBMapper.insert(batchDB);
            String objectName = parseBatchObjectName(batchDB, bigTaskContextMap);
            wmEcontractSignBigContextManageService.uploadObject(objectName, bigTaskContextMap.get("taskIdAndTaskMap"));
            //记录S3文本信息
            wmEcontractBatchDBMapper.updateBigBatchContextById(objectName, batchDB.getId());
            //上传完成后将大文本回写
            wmEcontractSignBigContextManageService.batchBigContextFill(batchDB, bigTaskContextMap.get("taskIdAndTaskMap"));
        } else {
            //是否冷热数据分离-写流程
            if (isSeperateHotAndColdDataInsert(batchDB)) {
                //先本地保存一份全量数据
                String backupContext = batchDB.getBatchContext();
                EcontractBatchContextBo batchContextBo = JSONObject.parseObject(backupContext, EcontractBatchContextBo.class);
                //新增冷数据字段
                batchContextBo.setColdDataList(getColdDataFieldList());
                //备份数据新增冷数据字段
                backupContext = JSON.toJSONString(batchContextBo);

                //获取冷数据
                String coldContext = JSON.toJSONString(batchContextBo.getTaskIdAndTaskMap());
                if(ConfigUtilAdapter.getBoolean("delete_batch_coldcontext", true)){
                    log.info("customerId:{},剔除坐标点前coldContext长度:{}", batchDB.getCustomerId(), coldContext.length());
                    //剔除无用冷数据不做存储
                    coldContext = (String) wmSignBatchContext.deleteUseless(coldContext);
                    log.info("customerId:{},剔除坐标点后coldContext长度:{}", batchDB.getCustomerId(), coldContext.length());
                }
                //保存热数据
                batchContextBo.setTaskIdAndTaskMap(new HashMap<>());
                batchDB.setBatchContext(JSON.toJSONString(batchContextBo));
                log.debug("WmEcontractBigBatchParseService#insert:{}",JSON.toJSONString(batchDB));
                wmEcontractBatchDBMapper.insert(batchDB);

                //保存冷数据
                WmEcontractSignBatchContextDB batchContextDB = new WmEcontractSignBatchContextDB();
                batchContextDB.setBatchId(batchDB.getId());
                batchContextDB.setContext(coldContext);
                wmEcontractBatchContextDBMapper.insert(batchContextDB);

                //回填全量数据
                batchDB.setBatchContext(backupContext);
            } else {
                wmEcontractBatchDBMapper.insert(batchDB);
            }
        }
        return batchDB.getId();
    }

    public int update(WmEcontractSignBatchDB batchDB) {
        int count = 0;
        if (wmEcontractSignBigContextManageService.batchGarySwitch(batchDB)) {
            // 未弃用灰度，以下代码均为废弃代码
            WmEcontractSignBatchDB wmEcontractSignBatchDB = queryByBatchId(batchDB.getId());
            // 获取入参大文本字段
            EcontractBatchContextBo newEcontractBatchContextBo = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
            String newTaskMapStr = StringUtils.EMPTY;
            if (null != newEcontractBatchContextBo) {
                newTaskMapStr = JSON.toJSONString(newEcontractBatchContextBo.getTaskIdAndTaskMap());
            }
            // 获取现有大文本字段
            EcontractBatchContextBo originalEcontractBatchContextBo = JSONObject.parseObject(wmEcontractSignBatchDB.getBatchContext(), EcontractBatchContextBo.class);
            String originalTaskMapStr = StringUtils.EMPTY;
            if (null != originalEcontractBatchContextBo) {
                originalTaskMapStr = JSON.toJSONString(originalEcontractBatchContextBo.getTaskIdAndTaskMap());
            }
            // 大文本相同，无需更新S3
            if (newTaskMapStr.equals(originalTaskMapStr)) {
                count = wmEcontractBatchDBMapper.update(batchDB);
            } else {
                //大文本上传
                Map<String, String> bigTaskContextMap = wmEcontractSignBigContextManageService.batchBigContextRemove(batchDB);
                wmEcontractBatchDBMapper.update(batchDB);
                String objectName = parseBatchObjectName(batchDB, bigTaskContextMap);
                wmEcontractSignBigContextManageService.uploadObject(objectName,
                    bigTaskContextMap.get("taskIdAndTaskMap"));
                //记录S3文本信息
                wmEcontractBatchDBMapper.updateBigBatchContextById(objectName, batchDB.getId());
                //上传完成后将大文本回写
                wmEcontractSignBigContextManageService.batchBigContextFill(batchDB,
                    bigTaskContextMap.get("taskIdAndTaskMap"));
            }
        } else {
            //是否冷热数据分离-更新流程（需要考虑存量数据）
            if (isSeperateHotAndColdDataUpdate(batchDB)) {
                //先本地保存一份全量数据
                String backupContext = batchDB.getBatchContext();
                EcontractBatchContextBo batchContextBo = JSONObject.parseObject(backupContext,
                    EcontractBatchContextBo.class);

                //更新移除冷数据后的记录
                batchContextBo.setTaskIdAndTaskMap(new HashMap<>());
                batchDB.setBatchContext(JSON.toJSONString(batchContextBo));
                if (Objects.nonNull(batchDB) && Objects.nonNull(batchDB.getBatchContext())) {
                    Cat.logEvent(MetricConstant.METRIC_SIGN_BATCH_UPDATE,
                        "update",
                        WmCustomerConstant.SUCCESS, "");

                    MetricHelper.build()
                        .name(MetricConstant.METRIC_SIGN_BATCH_UPDATE)
                        .count();
                }
                wmEcontractBatchDBMapper.update(batchDB);

                //回填全量数据
                batchDB.setBatchContext(backupContext);
            } else {
                if (Objects.nonNull(batchDB) && Objects.nonNull(batchDB.getBatchContext())) {
                    Cat.logEvent(MetricConstant.METRIC_SIGN_BATCH_UPDATE,
                        "update",
                        WmCustomerConstant.SUCCESS, "");

                    MetricHelper.build()
                        .name(MetricConstant.METRIC_SIGN_BATCH_UPDATE)
                        .count();
                }
                count = wmEcontractBatchDBMapper.update(batchDB);
            }
        }
        return count;
    }

    // com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper.queryByRecordKey
    public WmEcontractSignBatchDB queryByRecordKey(String recordKey) {
        WmEcontractSignBatchDB batchInfo;
        if (readFromS3AndDB()) {
            batchInfo = wmEcontractBatchDBMapper.queryByRecordKey(recordKey);
            fillBatchInfo(batchInfo);
        } else {
            batchInfo = wmEcontractBatchDBMapper.queryByRecordKey(recordKey);
            //封装批量签约冷数据
            fillSingleBatchContext(batchInfo);
        }
        return batchInfo;
    }

    public List<WmEcontractSignBatchDB> queryByRecordKeys(List<String> recordKeys) {
        return wmEcontractBatchDBMapper.queryByRecordKeys(recordKeys);
    }

    // com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper.queryByRecordKeyMaster
    public WmEcontractSignBatchDB queryByRecordKeyMaster(String recordKey) {
        WmEcontractSignBatchDB batchInfo;
        if (readFromS3AndDB()) {
            batchInfo = wmEcontractBatchDBMapper.queryByRecordKeyMaster(recordKey);
            fillBatchInfo(batchInfo);
        } else {
            batchInfo = wmEcontractBatchDBMapper.queryByRecordKeyMaster(recordKey);
            //封装批量签约冷数据
            fillSingleBatchContext(batchInfo);
        }
        return batchInfo;
    }

    public List<WmEcontractSignBatchDB> queryByRecordKeysMaster(List<String> recordKeys) {
        return wmEcontractBatchDBMapper.queryByRecordKeysMaster(recordKeys);
    }

    // com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper.queryByBatchId
    public WmEcontractSignBatchDB queryByBatchId(Long batchId) {
        WmEcontractSignBatchDB batchInfo;
        if (readFromS3AndDB()) {
            batchInfo = wmEcontractBatchDBMapper.queryByBatchId(batchId);
            fillBatchInfo(batchInfo);
        } else {
            batchInfo = wmEcontractBatchDBMapper.queryByBatchId(batchId);
            //封装批量签约冷数据
            fillSingleBatchContext(batchInfo);
        }
        return batchInfo;
    }

    public WmEcontractSignBatchDB queryByBatchIdMaster(Long batchId) {
        WmEcontractSignBatchDB batchInfo;
        if (readFromS3AndDB()) {
            batchInfo = wmEcontractBatchDBMapper.queryByBatchIdMaster(batchId);
            fillBatchInfo(batchInfo);
        } else {
            batchInfo = wmEcontractBatchDBMapper.queryByBatchIdMaster(batchId);
            //封装批量签约冷数据
            fillSingleBatchContext(batchInfo);
        }
        return batchInfo;
    }

    // com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper.batchQueryByBatchId
    public List<WmEcontractSignBatchDB> batchQueryByBatchId(List<Long> batchIdList) {
        List<WmEcontractSignBatchDB> batchInfoList;
        if (readFromS3AndDB()) {
            batchInfoList = wmEcontractBatchDBMapper.batchQueryByBatchId(batchIdList);
            fillBatchInfoList(batchInfoList);
        } else {
            batchInfoList = wmEcontractBatchDBMapper.batchQueryByBatchId(batchIdList);
            fillMutilBatchContext(batchInfoList);
        }
        return batchInfoList;
    }

    // com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper.queryWithParam
    List<WmEcontractSignBatchDB> queryWithParam(SignBatchQueryParam param) {
        List<WmEcontractSignBatchDB> batchInfoList;
        if (readFromS3AndDB()) {
            batchInfoList = wmEcontractBatchDBMapper.queryWithParam(param);
            fillBatchInfoList(batchInfoList);
        } else {
            batchInfoList = wmEcontractBatchDBMapper.queryWithParam(param);
            fillMutilBatchContext(batchInfoList);
        }
        return batchInfoList;
    }

    /**
     * 根据查询条件查询
     * @param param
     * @return
     */
    List<WmEcontractSignBatchDB> queryWithParamWithoutPack(SignBatchQueryParam param) {
        List<WmEcontractSignBatchDB> batchInfoList;
        if (readFromS3AndDB()) {
            batchInfoList = wmEcontractBatchDBMapper.queryWithParamWithoutPack(param);
            fillBatchInfoList(batchInfoList);
        } else {
            batchInfoList = wmEcontractBatchDBMapper.queryWithParamWithoutPack(param);
            fillMutilBatchContext(batchInfoList);
        }
        return batchInfoList;
    }

    public List<WmEcontractSignBatchDB> queryBatchItemParamWithLabel(EcontractBatchOpRequest request, long lastId, int limit) {
        List<WmEcontractSignBatchDB> batchInfoList;
        if (readFromS3AndDB()) {
            batchInfoList = wmEcontractBatchDBMapper.queryBatchItemParamWithLabel(request, lastId, limit);
            fillBatchInfoList(batchInfoList);
        } else {
            batchInfoList = wmEcontractBatchDBMapper.queryBatchItemParamWithLabel(request, lastId, limit);
            fillMutilBatchContext(batchInfoList);
        }
        return batchInfoList;
    }

    public List<WmEcontractSignBatchDB> queryBatchItemWithoutPackParamWithLabel(EcontractBatchOpRequest request, long lastId, int limit) {
        List<WmEcontractSignBatchDB> batchInfoList;
        if (readFromS3AndDB()) {
            batchInfoList = wmEcontractBatchDBMapper.queryBatchItemWithoutPackParamWithLabel(request, lastId, limit);
            fillBatchInfoList(batchInfoList);
        } else {
            batchInfoList = wmEcontractBatchDBMapper.queryBatchItemWithoutPackParamWithLabel(request, lastId, limit);
            fillMutilBatchContext(batchInfoList);
        }
        return batchInfoList;
    }

    public List<WmEcontractSignBatchDB> queryBatchItemWithPackParamWithLabel(EcontractBatchOpRequest request, long lastId, int limit) {
        List<WmEcontractSignBatchDB> batchInfoList;
        if (readFromS3AndDB()) {
            batchInfoList = wmEcontractBatchDBMapper.queryBatchItemWithPackParamWithLabel(request, lastId, limit);
            fillBatchInfoList(batchInfoList);
        } else {
            batchInfoList = wmEcontractBatchDBMapper.queryBatchItemWithPackParamWithLabel(request, lastId, limit);
            fillMutilBatchContext(batchInfoList);
        }
        return batchInfoList;
    }

    //com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper.queryEntityListWithLabel4Encryption
    public List<WmEcontractSignBatchDB> queryEntityListWithLabel4Encryption(long lastId, int size) {
        List<WmEcontractSignBatchDB> batchInfoList;
        if (readFromS3AndDB()) {
            batchInfoList = wmEcontractBatchDBMapper.queryEntityListWithLabel4Encryption(lastId, size);
            fillBatchInfoList(batchInfoList);
        } else {
            batchInfoList = wmEcontractBatchDBMapper.queryEntityListWithLabel4Encryption(lastId, size);
            fillMutilBatchContext(batchInfoList);
        }
        return batchInfoList;
    }

    //com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper.updateBatchContextByBatchDB
    public int updateBatchContextByBatchDB(WmEcontractSignBatchDB batchDB) {
        int count = 0;
        //是否冷热数据分离-更新流程（需要考虑存量数据）
        if (isSeperateHotAndColdDataUpdate(batchDB)) {
            //先本地保存一份全量数据
            String backupContext = batchDB.getBatchContext();
            EcontractBatchContextBo batchContextBo = JSONObject.parseObject(backupContext, EcontractBatchContextBo.class);

            //更新移除冷数据后的记录
            batchContextBo.setTaskIdAndTaskMap(new HashMap<>());
            batchDB.setBatchContext(JSON.toJSONString(batchContextBo));
            wmEcontractBatchDBMapper.updateBatchContextByBatchDB(batchDB);

            //回填全量数据
            batchDB.setBatchContext(backupContext);
        } else {
            count = wmEcontractBatchDBMapper.updateBatchContextByBatchDB(batchDB);
        }
        return count;
    }

    public List<WmEcontractSignBatchDB> querySignBatchListByPackIdAndStatus(Long packId, String batchStatus){
        return wmEcontractBatchDBMapper.querySignBatchListByPackIdAndStatus(packId, batchStatus);
    }

    public List<WmEcontractSignBatchDB> querySignBatchListByPackIdAndStatusMaster(Long packId, String batchStatus){
        return wmEcontractBatchDBMapper.querySignBatchListByPackIdAndStatusMaster(packId, batchStatus);
    }

    public List<WmEcontractSignBatchDB> querySignBatchListByPackId(Long packId) {
        List<WmEcontractSignBatchDB> batchInfoList = wmEcontractBatchDBMapper.querySignBatchListByPackId(packId);
        fillMutilBatchContext(batchInfoList);
        return batchInfoList;
    }

    public List<WmEcontractSignBatchDB> queryBatchListByPackId(Long packId) {
        List<WmEcontractSignBatchDB> batchInfoList = wmEcontractBatchDBMapper.queryBatchListByPackId(packId);
        if (MccConfig.isQuerySignBatchListViaPackMaster() && ((CollectionUtils.isEmpty(batchInfoList)
            || batchInfoList.stream()
            .map(WmEcontractSignBatchDB::getNotifyInfo)
            .collect(Collectors.toSet())
            .contains(null)))) {
            Cat.logMetricForCount("sign_pack_status_update_MQ_sign_pack_retrieve_from_master");
            batchInfoList = wmEcontractBatchDBMapper.queryBatchListByPackIdMaster(packId);
            log.info("修复主从延时NPE， batchInfoList = {}", JSON.toJSONString(batchInfoList));
        }
        fillMutilBatchContext(batchInfoList);
        return batchInfoList;
    }

    public List<WmEcontractSignBatchDB> queryBatchListByPackIdMaster(Long packId){
        return wmEcontractBatchDBMapper.queryBatchListByPackIdMaster(packId);
    }

    private void fillBatchInfo(WmEcontractSignBatchDB wmEcontractSignBatchDB) {
        if (null == wmEcontractSignBatchDB) {
            return;
        }
        EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(wmEcontractSignBatchDB.getBatchContext(), EcontractBatchContextBo.class);
        Map<Long, EcontractTaskBo> taskBoMap = econtractBatchContextBo.getTaskIdAndTaskMap();
        // TaskIdAndTaskMap为空再从S3获取，不为空说明没有命中大文本逻辑
        if (MapUtils.isNotEmpty(taskBoMap)) {
            return;
        }
        byte[] contextBytes = wmEcontractSignBigContextManageService.getObject(wmEcontractSignBatchDB.getObjectName());
        String context = Base64.getEncoder().encodeToString(contextBytes);
        Map<Long, EcontractTaskBo> taskIdAndTaskMap = JSONObject.parseObject(context, Map.class);
        econtractBatchContextBo.setTaskIdAndTaskMap(taskIdAndTaskMap);
    }

    private void fillBatchInfoList(List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList) {
        if (CollectionUtils.isEmpty(wmEcontractSignBatchDBList)) {
            return;
        }
        wmEcontractSignBatchDBList.stream().forEach(batchInfo -> fillBatchInfo(batchInfo));
    }

    private boolean readFromS3AndDB() {
        return ConfigUtilAdapter.getBoolean("econtract_bigcontext_read_switch", false);//默认关闭
    }

    private String parseBatchObjectName(WmEcontractSignBatchDB batchDB, Map<String, String> bigTaskContextMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(bigTaskContextMap.get("batchType"));
        sb.append("_");
        sb.append(batchDB.getCustomerId());
        sb.append("_");
        sb.append(batchDB.getId());
        return sb.toString();
    }

    /**
     * 封装单个批量签约任务信息
     * @param batchDB
     */
    private void fillSingleBatchContext(WmEcontractSignBatchDB batchDB) {
        if (batchDB == null) {
            return;
        }
        //根据batchId获取对应批量签约冷数据
        WmEcontractSignBatchContextDB batchContextDB = wmEcontractBatchContextDBMapper.queryByBatchId(batchDB.getId());
        //如果为空，尝试从主库读
        if (batchContextDB == null) {
            batchContextDB = wmEcontractBatchContextDBMapper.queryByBatchIdMaster(batchDB.getId());
        }
        //如果不为空，则表示已经保存批量签约冷数据，进行数据封装
        if (batchContextDB != null) {
            // 如果batch记录上下文为空，则重新获取
            if (MccConfig.isRetryIfContextIsEmpty() && StringUtils.isEmpty(batchDB.getBatchContext())) {
                int retryTimes = ConfigUtilAdapter.getInt("query_batch_context_retry_times", 3);
                for (int times = 0; times < retryTimes; times++) {
                    try {
                        Thread.sleep(50);
                    } catch (Exception e) {
                        log.error("重试获取batch上下文 延迟异常 batchId:{}", batchDB.getId(), e);
                    }
                    WmEcontractSignBatchDB batchDBTemp = wmEcontractBatchDBMapper.queryByBatchId(batchDB.getId());
                    if (StringUtils.isNotEmpty(batchDBTemp.getBatchContext())) {// 查询到数据则跳出for循环
                        batchDB.setBatchContext(batchDBTemp.getBatchContext());
                        break;
                    }
                }
            }
            EcontractBatchContextBo batchContextBo = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
            batchContextBo.setTaskIdAndTaskMap(JSONObject.parseObject(batchContextDB.getContext(), HashMap.class));
            batchDB.setBatchContext(JSON.toJSONString(batchContextBo));
        }
    }

    /**
     * 封装多个批量签约任务信息
     * @param batchDBList
     */
    private void fillMutilBatchContext(List<WmEcontractSignBatchDB> batchDBList) {
        log.info("WmEcontractBigBatchParseService#fillMutilBatchContext, batchDBList: {}", JSON.toJSONString(batchDBList));
        if (CollectionUtils.isEmpty(batchDBList)) {
            return;
        }
        List<Long> batchIdList = batchDBList.stream().map(WmEcontractSignBatchDB::getId).collect(Collectors.toList());
        List<WmEcontractSignBatchContextDB> batchContextDBList = wmEcontractBatchContextDBMapper.queryByBatchIdListMaster(batchIdList);
        if (CollectionUtils.isEmpty(batchContextDBList)) {
            return;
        }
        Map<Long, WmEcontractSignBatchContextDB> batchContextDBMap = batchContextDBList.stream()
                .collect(Collectors.toMap(WmEcontractSignBatchContextDB::getBatchId,
                        WmEcontractSignBatchContextDB -> WmEcontractSignBatchContextDB));
        for (WmEcontractSignBatchDB batchDB : batchDBList) {
            // 如果冷数据表中无数据，则表示未进行冷热数据分离
            if (batchContextDBMap.get(batchDB.getId()) == null) {
                continue;
            }
            // 如果batch记录上下文为空，则重新获取
            if (MccConfig.isRetryIfContextIsEmpty() && StringUtils.isEmpty(batchDB.getBatchContext())) {
                int retryTimes = ConfigUtilAdapter.getInt("query_batch_context_retry_times", 3);
                for (int times = 0; times < retryTimes; times++) {
                    try {
                        Thread.sleep(50);
                    } catch (Exception e) {
                        log.error("重试获取batch上下文 延迟异常 batchId:{}", batchDB.getId(), e);
                    }
                    WmEcontractSignBatchDB batchDBTemp = wmEcontractBatchDBMapper.queryByBatchId(batchDB.getId());
                    if (StringUtils.isNotEmpty(batchDBTemp.getBatchContext())) {// 查询到数据则跳出for循环
                        batchDB.setBatchContext(batchDBTemp.getBatchContext());
                        break;
                    }
                }
            }
            EcontractBatchContextBo batchContextBo = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
            batchContextBo.setTaskIdAndTaskMap(JSONObject.parseObject(batchContextDBMap.get(batchDB.getId()).getContext(), HashMap.class));
            batchDB.setBatchContext(JSON.toJSONString(batchContextBo));
        }
    }

    /**
     * 是否批量签约冷热数据分离更新流程（考虑存量情况）
     * @param batchDB
     * @return
     */
    private boolean isSeperateHotAndColdDataUpdate(WmEcontractSignBatchDB batchDB) {
        //批量签约冷热数据开关
        boolean isSwitch = isSeperateHotAndColdDataInsert(batchDB);
        //是否insert时已完成冷热数据分离
        String backupContext = batchDB.getBatchContext();
        EcontractBatchContextBo batchContextBo = JSONObject.parseObject(backupContext, EcontractBatchContextBo.class);
        boolean isHasSeperate = CollectionUtils.isNotEmpty(batchContextBo.getColdDataList());
        return isSwitch && isHasSeperate;
    }

    private boolean isSeperateHotAndColdDataInsert(WmEcontractSignBatchDB batchDB) {
        //是否总开关
        boolean isSwitch = MccConfig.isSeperateHotAndColdDataSwitch();
        //是否灰度客户
        boolean isGrayCustomerId = batchDB.getCustomerId() % 100 < MccConfig.seperateHotAndColdDataCustomerPercent();
        return isSwitch && isGrayCustomerId;
    }

    /**
     * 获取冷数据字段名
     */
    private List<String> getColdDataFieldList() {
        return Lists.newArrayList("taskIdAndTaskMap");
    }

    /**
     * 查询批量任务带上下文
     */
    public List<WmEcontractSignBatchDB> querySignBatchTask(SignContractTaskParam signedContractTaskParam) {
        List<WmEcontractSignBatchDB> wmEcontractSignBatchList =
                wmEcontractBatchDBMapper.querySignBatchTask(signedContractTaskParam);
        fillMutilBatchContext(wmEcontractSignBatchList);
        return wmEcontractSignBatchList;
    }
}
