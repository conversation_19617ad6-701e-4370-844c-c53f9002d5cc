package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.deliverystaff.station.label.exception.BmStaffStationLabelException;
import com.sankuai.deliverystaff.station.label.iface.OpenStationLabelQueryIface;
import com.sankuai.deliverystaff.station.label.view.StationLabelExistView;
import com.sankuai.meituan.waimai.customer.util.DateUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 履约系统聚合站点标签服务
 * <AUTHOR>
 * @date 2023/09/20
 * @email <EMAIL>
 */
@Service
@Slf4j
public class BmStationLabelServiceAdapter {

    @Autowired
    private OpenStationLabelQueryIface openStationLabelQueryIface;
    /**
     * 配送到校标签code
     */
    public static final String XY_TO_SCHOOL_CODE = "xytoschool";

    /**
     * 批量查询学校聚合站点当前时刻是否具有"配送进校"标签
     * @param aggreSiteIdList 聚合站点列表
     * @return Map<Integer, Boolean> key->聚合站点ID val->true/false
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<StationLabelExistView> getSchoolAggreSiteExistNow(List<Integer> aggreSiteIdList) throws WmSchCantException {
        try {
            Map<Integer, Boolean> map = new HashMap<>();
            if (CollectionUtils.isEmpty(aggreSiteIdList)) {
                return null;
            }
            List<Long> stationIdList = aggreSiteIdList.stream()
                    .map(Integer::longValue)
                    .collect(Collectors.toList());

            return openStationLabelQueryIface.queryBatchLabelExistByDate(stationIdList, XY_TO_SCHOOL_CODE, (long) DateUtil.unixTime());
        } catch (BmStaffStationLabelException e) {
            log.error("[BmStationLabelServiceAdapter.getSchoolAggreSiteExistNow] BmStaffStationLabelException. aggreSiteIdList = {}",
                    JSONObject.toJSONString(aggreSiteIdList), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询学校聚合站点信息异常");
        } catch (Exception e) {
            log.error("[BmStationLabelServiceAdapter.getSchoolAggreSiteExistNow] Exception. aggreSiteIdList = {}",
                    JSONObject.toJSONString(aggreSiteIdList), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询学校聚合站点信息异常");
        }
    }

}
