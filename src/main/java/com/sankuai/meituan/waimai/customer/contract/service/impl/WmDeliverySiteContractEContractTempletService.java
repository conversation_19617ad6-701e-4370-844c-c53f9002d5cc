package com.sankuai.meituan.waimai.customer.contract.service.impl;

import java.util.List;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliverySiteContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.DELIVERY_SITE_CONTRACT_E})
public class WmDeliverySiteContractEContractTempletService extends AbstractWmEContractTempletService {

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.DELIVERY_SITE_CONTRACT);
        applyBo.setConfigBo(new EcontractTaskConfigBo());

        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectListByParentIdAndType(Long.valueOf(contractBo.getBasicBo().getParentId()), WmTempletContractTypeEnum.DELIVERY_SERVICE_CONTRACT_E.getCode());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在生效《配送服务合同》");
        }

        EcontractDeliverySiteContractInfoBo contractInfoBo = JSON.parseObject(contractBo.getExtraData(), EcontractDeliverySiteContractInfoBo.class);
        contractInfoBo.setContractNumber(wmTempletContractDBList.get(0).getNumber());
        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
        return applyBo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String deliverySiteContractENum = ContractNumberUtil.genDeliverySiteContractENum(insertId);
        contractBo.getBasicBo().setContractNum(deliverySiteContractENum);
        log.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, deliverySiteContractENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), deliverySiteContractENum);
        return insertId;
    }
}
