package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PhfTransferContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 拼好送基础版比对
 * @author: zhangyuanhao02
 * @create: 2025/1/10 17:18
 */
@Slf4j
@Service
public class WmEcontractPhfVirtualCompareService extends AbstractWmEcontractPhfCompareService{

    @Override
    void compare(EcontractBatchBo currentData, EcontractBatchContextBo batchContextBo) throws WmCustomerException {
        List<PhfTransferContext> phfTransferContextList = batchContextBo.getPhfTransferContextList();
        log.info("拼好送基础版-开始比对, 比对上下文: {}", JSONObject.toJSONString(phfTransferContextList));
        super.compareSinglePoiPdf(currentData, phfTransferContextList);
        log.info("拼好送基础版-比对结束");
    }

    @Override
    public String getPdfContentInfoBoMapKeyByWmPoiId(Long wmPoiId) throws WmCustomerException {
        return EcontractTaskApplySubTypeEnum.PHF_VIRTUAL.name() + "_" + wmPoiId;
    }
}
