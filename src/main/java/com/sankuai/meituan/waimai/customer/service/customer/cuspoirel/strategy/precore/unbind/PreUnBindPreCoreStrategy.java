package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.unbind;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IUnBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCancelAuthInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc 预解绑前置核心操作策略
 * @date 20240123
 */
@Slf4j
@Service
public class PreUnBindPreCoreStrategy implements IUnBindPreCoreStrategy {

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    private static final int MAX_POI_NAME_NUM = 3;

    /**
     * 策略执行逻辑
     *
     * @param context
     * @throws WmCustomerException
     */
    @Override
    public void execute(CustomerPoiUnBindFlowContext context) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        Integer customerId = context.getCustomerId();
        //步骤1：前置操作，创建客户门店解绑任务
        Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerBindTask(wmCustomerDB.getId(), context.getWmPoiIdSet(), context.getCustomerOperateBO());
        context.setPoiAndTaskMaps(poiAndTaskMaps);
        log.info("DirectUnBindPreCoreStrategy,直接解绑流程的核心前置操作完成,customerId={},wmPoiIds={}", wmCustomerDB.getId(), JSON.toJSONString(context.getWmPoiIdSet()));

        //步骤2：发起预绑定解绑签约
        LongResult result = sendPreUnBindSign(context);
        log.info("PreUnBindPreCoreStrategy,预解绑添加短信记录完成,result={},customerId={},wmPoiIdSet={}", result.getValue(), customerId, JSON.toJSONString(wmPoiIdSet));

        //步骤3：添加解绑短信记录
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = addSignUnBindSmsRecord(wmCustomerDB.getId(), result.getValue(), wmPoiIdSet);
        log.info("PreUnBindPreCoreStrategy,预解绑添加短信记录完成,smsRecordId={}", wmCustomerPoiSmsRecordDB.getId());

        //步骤4：更新短信记录ID到解绑任务记录
        customerTaskService.updateTaskSignTaskId(customerId, Lists.newArrayList(poiAndTaskMaps.values()), wmCustomerPoiSmsRecordDB.getId());
    }


    /**
     * 发起预绑定解绑签约&记录签约短信&更新短信记录ID到任务表
     *
     * @param context
     * @return
     * @throws WmCustomerException
     */
    private LongResult sendPreUnBindSign(CustomerPoiUnBindFlowContext context) throws WmCustomerException {

        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        Integer opUid = context.getOpUid();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        LongResult result = null;
        CustomerTaskSourceEnum customerTaskSourceEnum = CustomerTaskSourceEnum.of(context.getOpSource());
        try {
            //构建解绑签约对象
            EcontractTaskApplyBo econtractTaskApplyBo = buildEcontractTaskApplyBo(wmCustomerDB, wmPoiIdSet, opUid);
            log.info("sendPreUnBindSign,econtractTaskApplyBo={}", JSON.toJSONString(econtractTaskApplyBo));
            // 发送短信通知生效KP
            result = wmEcontractSignBzService.applyTask(econtractTaskApplyBo);
            log.info("sendPreUnBindSign,result={}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("sendPreUnBindSign,发送客户门店解绑短信异常 customerId={},wmPoiIdSet={}", wmCustomerDB.getId(), JSON.toJSONString(wmPoiIdSet), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "发送客户门店解绑短信异常");
        } finally {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_SEND_CUSTOMER_POI_UNBIND_SMS_COUNT)
                    .tag("source", customerTaskSourceEnum.name())
                    .tag("success", String.valueOf(result.getValue() > 0))
                    .count();
        }

        return result;
    }

    /**
     * 组装解绑签约请求对象
     *
     * @param wmCustomerDB
     * @param wmPoiIdSet
     * @param opUid
     * @return
     */
    private EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerDB wmCustomerDB, Set<Long> wmPoiIdSet, Integer opUid) {
        EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
        econtractTaskApplyBo.setBizId(String.valueOf(wmCustomerDB.getId()));
        econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.CUSTOMER);

        EcontractCancelAuthInfoBo econtractCancelAuthInfoBo = new EcontractCancelAuthInfoBo();
        econtractCancelAuthInfoBo.setCustomerName(wmCustomerDB.getCustomerName());
        econtractCancelAuthInfoBo.setCustomerNumber(wmCustomerDB.getCustomerNumber());
        // 批量获取门店名称
        List<String> wmPoiNames = Lists.newArrayList();
        List<Long> wmPoiIds = Lists.newArrayList(wmPoiIdSet);
        econtractCancelAuthInfoBo.setWmPoiIdList(wmPoiIds);
        // 如果超过批量查询限制,则查询最大限制即可，因为只会取头几个
        if (wmPoiIds.size() > CustomerConstants.POI_QUERY_BATCH_NUM) {
            wmPoiIds = Lists.partition(wmPoiIds, CustomerConstants.POI_QUERY_BATCH_NUM).get(0);
        }
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIds,
                Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_NAME));
        int count = 0;
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            if (count > MAX_POI_NAME_NUM) {
                break;
            }
            wmPoiNames.add(wmPoiAggre.getName());
            count++;
        }
        if (wmPoiAggreList != null && wmPoiAggreList.size() > MAX_POI_NAME_NUM) {
            econtractCancelAuthInfoBo.setWmPoiName(StringUtils.join(wmPoiNames, "、") + "等");
        } else {
            econtractCancelAuthInfoBo.setWmPoiName(StringUtils.join(wmPoiNames, "、"));
        }
        econtractTaskApplyBo.setApplyInfoBo(JSONObject.toJSONString(econtractCancelAuthInfoBo));
        econtractTaskApplyBo.setCommitUid(opUid);
        return econtractTaskApplyBo;
    }

    /**
     * 添加解绑短信记录
     *
     * @param customerId
     * @param taskId
     * @param wmPoiIdSet
     * @return
     */
    private WmCustomerPoiSmsRecordDB addSignUnBindSmsRecord(Integer customerId, Long taskId, Set<Long> wmPoiIdSet) {
        // 记录短信记录
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = new WmCustomerPoiSmsRecordDB();
        wmCustomerPoiSmsRecordDB.setCustomerId(customerId);
        wmCustomerPoiSmsRecordDB.setTaskId(taskId);
        wmCustomerPoiSmsRecordDB.setWmPoiIds(StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL));
        wmCustomerPoiSmsRecordDB.setTaskStatus(EcontractTaskStateEnum.TO_COMMIT.getType());
        wmCustomerPoiSmsRecordDB.setValid(CustomerConstants.VALID);
        wmCustomerPoiSmsRecordDB.setType((byte) 0);
        wmCustomerPoiSmsRecordMapper.insertSmsRecord(wmCustomerPoiSmsRecordDB);
        return wmCustomerPoiSmsRecordDB;

    }
}
