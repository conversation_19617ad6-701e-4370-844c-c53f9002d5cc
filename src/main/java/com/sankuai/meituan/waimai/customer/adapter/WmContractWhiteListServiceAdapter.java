package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmPoiSignSubjectBo;
import com.sankuai.meituan.waimai.thrift.domain.BatchLabelAndLabelClassificationResult;
import com.sankuai.meituan.waimai.thrift.domain.LabelAndLabelClassificationResult;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiFlowlineLabelThriftService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 标签系统获取客户签约主体白名单
 *
 * <AUTHOR>
 * @date 2020/8/14
 */
@Service
public class WmContractWhiteListServiceAdapter {

    private static Logger logger = LoggerFactory.getLogger(WmContractWhiteListServiceAdapter.class);

    @Autowired
    private WmPoiFlowlineLabelThriftService.Iface wmPoiFlowlineLabelThriftService;

    public Optional<WmPoiSignSubjectBo> getContractWhiteList(Long customerId) throws WmCustomerException {
        logger.info("查询签约主体白名单 customerId={}", customerId);
        if (customerId == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "查询客户主体白名单参数为空");
        }
        List<Long> customerIds = Lists.newArrayList(customerId);
        List<BatchLabelAndLabelClassificationResult> resultList = null;
        try {
            resultList = wmPoiFlowlineLabelThriftService.selectWmPoiLabelAndWmPoiLabelClassificationBySubjectId(customerIds, LabelSubjectTypeEnum.CUSTOMER.getCode());
            logger.info("查询签约主体白名单结果：resultList = {}", JSON.toJSONString(resultList));
        } catch (WmServerException e) {
            logger.warn("查询签约主体白名单异常:customerId = {},msg = {}", customerId, e.getMsg());
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "查询合同签约主体白名单异常！");
        } catch (Exception e) {
            logger.error("查询签约主体白名单异常:customerId = {}", customerId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "查询合同签约主体白名单异常！");
        }
        if (CollectionUtils.isEmpty(resultList)) {
            logger.info("查询合同签约主体白名单为空：customerId = {}", customerId);
            return Optional.absent();
        }
        return generatePoiSignSubjectBo(resultList);

    }

    private Optional<WmPoiSignSubjectBo> generatePoiSignSubjectBo(List<BatchLabelAndLabelClassificationResult> list) throws WmCustomerException {
        WmPoiSignSubjectBo subjectBo = new WmPoiSignSubjectBo();

        int idA = MccConfig.getLabelClassificationIdA();
        int idB = MccConfig.getLabelClassificationIdB();
        logger.info("查询签约主体白名单，标签分类：idA={},idB={}", idA, idB);

        if (idA == 0 || idB == 0) {
            logger.warn("标签系统未配置标签分类！！！");
            return Optional.absent();
        }

        if (!CollectionUtils.isEmpty(list)) {
            //单客户查询
            BatchLabelAndLabelClassificationResult result = list.get(0);
            subjectBo.setCustomerId((int) result.getSubjectId());

            List<LabelAndLabelClassificationResult> labelClassificationResults = result.getLabelAndLabelClassificationResultList();
            //标签分类和标签集合
            for (LabelAndLabelClassificationResult label : labelClassificationResults) {
                //目前一个分类只会存在一个主体
                if (label.getWmLabelClassification().getId() == (long) idA) {
                    //技术服务费主体
                    subjectBo.setPartBName(label.getWmPoiLabel().getName());
                }
                if (label.getWmLabelClassification().getId() == (long) idB) {
                    //履约服务费主体
                    subjectBo.setPartlogisticsName(label.getWmPoiLabel().getName());
                }
            }
            //有一个为空则返回null
            if (StringUtils.isEmpty(subjectBo.getPartBName()) || StringUtils.isEmpty(subjectBo.getPartlogisticsName())) {
                return Optional.absent();
            }
        }
        logger.info("查询合同签约主体白名单：subjectBo={} ", JSON.toJSONString(subjectBo));
        return Optional.fromNullable(subjectBo);
    }
}
