package com.sankuai.meituan.waimai.customer.service.sc.workflow;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.Json;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.crm.ticket.thrift.exception.WmTicketServerException;
import com.sankuai.meituan.waimai.crm.ticket.thrift.service.WmCrmTicketThriftService;
import com.sankuai.meituan.waimai.customer.adapter.WdcRelationServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.constant.sc.WmChangeBindPoiLogTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallWmPoiBindBO;
import com.sankuai.meituan.waimai.customer.service.sc.WmScLogService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTagV2Service;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTairService;
import com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil;
import com.sankuai.meituan.waimai.customer.service.sc.auth.WmScCanteenAuthService;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallBindService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallManageService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.check.WmCanteenStallCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallClueBindService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallPoiBindService;
import com.sankuai.meituan.waimai.customer.service.sc.check.WmCanteenPoiCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenInfoService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiTaskSimpleService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskBaseBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskSumBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScCanIllegalPoi;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallBindSubmitDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.util.ObjectMapperUtils;
import com.sankuai.nibmp.infra.amp.common.util.ObjectComparison;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.service.sc.workflow.WmCanteenPoiBindService.CANTEEN_POI_TASK_LOCKKEY_PREFIX;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_CANTEEN_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_CANTEEN_STALL_BIND_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

@Slf4j
@Service
public class WmUnBindSimpleService implements WmCanteenPoiTaskSimpleService{


    @Autowired
    private WmCanteenPoiCheckService wmCanteenPoiCheckService;

    @Autowired
    private WmScCanteenPoiTaskMapper wmScCanteenPoiTaskMapper;

    @Autowired
    private WmCanteenPoiBindService wmCanteenPoiBindService;

    @Autowired
    private WmCrmTicketThriftService.Iface wmCrmTicketThriftService;

    @Autowired
    private WmAuditPersonService wmAuditPersonService;

    @Autowired
    private WmScCanteenInfoService wmScCanteenInfoService;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    @Autowired
    private WmScCanteenPoiTaskDetailMapper wmScCanteenPoiTaskDetailMapper;

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    @Autowired
    private WmScCanteenPoiTaskSimpleService wmScCanteenPoiTaskSimpleService;

    @Autowired
    private WmCanteenStallBindService wmCanteenStallBindService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private WmScCanteenPoiService wmScCanteenPoiService;


    @Autowired
    private WmScCanteenPoiAuditDetailMapper wmScCanteenPoiAuditDetailMapper;

    @Autowired
    private WmScCanteenPoiAuditMapper wmScCanteenPoiAuditMapper;


    @Autowired
    private WmScCanteenPoiLabelMapper wmScCanteenPoiLabelMapper;

    @Autowired
    private WmScTagV2Service wmScTagV2Service;


    @Autowired
    private WmScCanteenPoiHistoryMapper wmScCanteenPoiHistoryMapper;

    @Autowired
    private WmPoiAttributesMapper wmPoiAttributesMapper;


    @Autowired
    private WmScLogService wmScLogService;

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Autowired
    private WmCanteenStallManageService wmCanteenStallManageService;

    @Autowired
    private WdcRelationServiceAdapter wdcRelationServiceAdapter;

    @Autowired
    private WmCanteenStallClueBindService wmCanteenStallClueBindService;

    @Autowired
    private WmCanteenStallPoiBindService wmCanteenStallPoiBindService;

    @Autowired
    private WmCanteenStallCheckService wmCanteenStallCheckService;

    @Autowired
    private WmScCanteenAuthService wmScCanteenAuthService;

    @Autowired
    private WmScTairService wmScTairService;


    private static final int IS_IN_GRAY=1;

    // 门店删除
    public static final int WM_POI_ID_IS_DELETE = 1;


    private static final Integer  MAX_UNBIND_LIMIT = 50;

    private static final Integer  MAX_UNBIND_PNG_LIMIT = 10;



    @Override
    public CanteenPoiTaskTypeEnum getTaskType() {
        return CanteenPoiTaskTypeEnum.UNBIND;
    }

    @Override
    public void checkTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("checkTask wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));

        Boolean haveAuth = wmScCanteenAuthService.checkStallUnbindOperationAuth(wmCanteenPoiTaskBO.getCanteenFrom().getId(), wmCanteenPoiTaskBO.getUserId());
        if (!haveAuth) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "无权限操作");
        }

        // 2、图片个数验证
        if(wmCanteenPoiTaskBO.getProofMaterialImage().size() > MAX_UNBIND_PNG_LIMIT){
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, String.format("单次限制上传%s个图片", MAX_UNBIND_PNG_LIMIT));
        }


        // 1、档次换绑个数
        if(wmCanteenPoiTaskBO.getWmPoiIdList().size() > MAX_UNBIND_LIMIT){
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, String.format("单次限制解绑%s个门店", MAX_UNBIND_LIMIT));
        }
        List<Long> wmPoiIdList = wmCanteenPoiTaskBO.getWmPoiIdList();
        List<Long> bindIdList = wmCanteenPoiTaskBO.getBindIdList();


        // 1. 剩余可换绑和解绑次数的校验
        List<Long> failWmPoiIdListover = wmCanteenPoiCheckService.validateRemainingTransferCount(wmPoiIdList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(failWmPoiIdListover)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "所选门店" +
                    failWmPoiIdListover.stream().map(String::valueOf).collect(Collectors.joining("、")) +
                    "的换绑次数超过限制");
        }

        List<Long> failWmPoiIdList1 = wmCanteenStallCheckService.checkWmPoiListValid(wmPoiIdList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(failWmPoiIdList1)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "所选门店" +
                    failWmPoiIdList1.stream().map(String::valueOf).collect(Collectors.joining("、")) +
                    "不存在");
        }


        // 3. (增强)验证是否绑定了当前食堂
        List<Long> failWmPoiIdadd1 = wmCanteenPoiCheckService.validateCanteenInSameCanteen(wmCanteenPoiTaskBO,wmPoiIdList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(failWmPoiIdadd1)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "所选门店" +
                    failWmPoiIdadd1.stream().map(String::valueOf).collect(Collectors.joining("、")) +
                    "没有绑定当前食堂");
        }

        List<Long> failWmPoiIdList3 = wmCanteenStallCheckService.checkWmPoiListBind(wmPoiIdList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(failWmPoiIdList3)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "所选门店" +
                    failWmPoiIdList3.stream().map(String::valueOf).collect(Collectors.joining("、")) +
                    "有正在换绑或者解绑的任务");
        }
    }

    /**
     * 食堂换绑门店创建食堂门店审核任务
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @return 任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public long createTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("[WmCanteenPoiTranferBindService.createTask] wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));


        /*// [尝试加锁] 若加锁失败代表当前有任务进行中
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);*/

        // 第一步: 在食堂门店任务主表(wm_sc_canteen_poi_task)中新增一条数据, 返回主键ID即任务ID
        long taskId = insertWmCanteenPoiTask(wmCanteenPoiTaskBO);
        wmCanteenPoiTaskBO.setId(taskId);

        // 第二步: 在食堂门店任务子表(wm_sc_canteen_poi_task_detail)中新增N条数据, N = 绑定门店数
        wmCanteenPoiBindService.insertUnbindWmCanteenPoiTaskDetail(wmCanteenPoiTaskBO);

        // 第三步: 获取任务节点信息
        Integer nextAuditNode = CanteenAuditProgressEnum.getNextNode(wmCanteenPoiTaskBO.getAuditNodeTypeEnum(), null);
        // 第四步：创建任务
        createCanteenTaskWorkflow(wmCanteenPoiTaskBO, nextAuditNode);

        //  第五步： 提交成功后更新状态 1、更新任务主表的任务审核进度状态
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);
        if (taskId > 0 && auditNodeEnum != null) {
            wmScCanteenPoiTaskMapper.updateAuditStatusByPrimaryKey(taskId, auditNodeEnum.getAuditStatus());
        } else {
            log.error("createTask 出现异常任务 wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        }

        // 第六步：更新档口绑定任务的外卖门店绑定状态
        wmCanteenStallBindService.updateWmBindIdListBindStatus(wmCanteenPoiTaskBO.getBindIdList(),CanteenStallWmPoiBindStatusEnum.UNBINDING.getType());


        // 第七步：记录日志
        transfePublisCanteenPoiLog(wmCanteenPoiTaskBO);
        return taskId;
    }
    /**
     * 在食堂门店审核任务主表(wm_sc_canteen_poi_task)中, 新增一条数据
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @return 主键ID, 即任务ID
     */
    public long insertWmCanteenPoiTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        log.info("[WmUnBindSimpleService.insertWmCanteenPoiTask] wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = new WmScCanteenPoiTaskDO();
        wmScCanteenPoiTaskDO.setCanteenIdFrom(wmCanteenPoiTaskBO.getCanteenIdFrom());
        wmScCanteenPoiTaskDO.setCanteenIdTo(wmCanteenPoiTaskBO.getCanteenIdTo());
        wmScCanteenPoiTaskDO.setAuditStatus(CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT.getCode());
        wmScCanteenPoiTaskDO.setAuditNodeType(wmCanteenPoiTaskBO.getAuditNodeTypeEnum().getCode());
        wmScCanteenPoiTaskDO.setValid(ValidEnum.VALID.getTypeInt());
        wmScCanteenPoiTaskDO.setTaskType(wmCanteenPoiTaskBO.getTaskType());
        wmScCanteenPoiTaskDO.setTaskReasonType(wmCanteenPoiTaskBO.getTaskReasonType());
        wmScCanteenPoiTaskDO.setTaskReason(wmCanteenPoiTaskBO.getTaskReason());
        wmScCanteenPoiTaskDO.setUserId(wmCanteenPoiTaskBO.getUserId());
        wmScCanteenPoiTaskDO.setUserName(wmCanteenPoiTaskBO.getUserName());
        wmScCanteenPoiTaskDO.setProofMaterialImage(wmCanteenPoiTaskBO.getProofMaterialImage().toString());
        wmScCanteenPoiTaskMapper.insertSelective(wmScCanteenPoiTaskDO);
        return wmScCanteenPoiTaskDO.getId();
    }


    /**
     * 创建食堂门店审核任务流程
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @param nextAuditNode 下一个任务节点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void createCanteenTaskWorkflow(WmCanteenPoiTaskBO wmCanteenPoiTaskBO, Integer nextAuditNode) throws WmSchCantException {
        log.info("[WmUnBindSimpleService.createCanteenTaskWorkflow] input param: wmCanteenPoiTaskBO = {}, nextAuditNode = {}",
                JSONObject.toJSONString(wmCanteenPoiTaskBO), nextAuditNode);
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);
        if (nextAuditNode == null || auditNodeEnum == CanteenAuditNodeEnum.UNKNOW) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "换绑食堂信息任务未找到流程节点");
        }
        // 构建相关信息
        WmAuditPersonCondition condition = new WmAuditPersonCondition();
        condition.setAuditNodeEnum(auditNodeEnum);
        condition.setUserId(wmCanteenPoiTaskBO.getUserId());
        condition.setWmCanteenDB(wmCanteenPoiTaskBO.getCanteenTo());
        condition.setWmSchoolDB(wmCanteenPoiTaskBO.getSchoolTo());
        wmCanteenPoiTaskBO.setAuditNode(CanteenAuditNodeEnum.of(nextAuditNode));
        //wmCanteenPoiTaskBO.setNextUser(wmAuditPersonBo);
        wmCanteenPoiTaskBO.setNextNode(auditNodeEnum);
        wmCanteenPoiTaskBO.setAuditStatus(auditNodeEnum.getAuditStatus());
        wmCanteenPoiTaskBO.setTaskTypeEnum(CanteenPoiTaskTypeEnum.UNBIND);

        //任务系统创建任务
        createTaskInTicketSystem(wmCanteenPoiTaskBO);
    }


    public void transfePublisCanteenPoiLog(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        new Thread(new TraceRunnable(() -> {
            try {
                transfePublisCanteenPoiLogStart(wmCanteenPoiTaskBO);
            } catch (Exception e) {
                log.error("[WmCanteenStallManageService.bindCanteenPoiByWmPoiBindAsync] Exception. wmCanteenPoiTaskBO = {}",
                        JSONObject.toJSONString(wmCanteenPoiTaskBO), e);
            }
        })).start();
    }
    public void transfePublisCanteenPoiLogStart(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        log.info("transfePublisCanteenPoiLogStart wmCanteenPoiTaskBO",JSONObject.toJSONString(wmCanteenPoiTaskBO));
        List<Long> wmPoiIdList = wmCanteenPoiTaskBO.getWmPoiIdList().stream().collect(Collectors.toList());
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindMapper.selectByWmPoiIdsAndCanteenPriIdWitchStatus((int) CanteenStallWmPoiBindStatusEnum.UNBINDING.getType(),wmPoiIdList,wmCanteenPoiTaskBO.getCanteenFrom().getId());
        if (bindDOList == null) {
            bindDOList = Collections.emptyList();
        }
        if (bindDOList == null) {
            bindDOList = Collections.emptyList();
        }
        for (WmCanteenStallBindDO bindDO : bindDOList) {
            // 判断wmPoiId是否存在,如果不存在统一放到一个列表里面
            if (bindDO.getWmPoiId() == null || bindDO.getWmPoiId() <= 0) {

            } else {
                String logInfo = "操作：提交门店解绑申请\\n" + "门店ID：" + bindDO.getWmPoiId() + "\\n" + "档口绑定任务ID：" + bindDO.getId() + "\\n"
                        + "[字段变更] 外卖门店绑定状态：" + CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getName() + "=>解绑中\\n";
                /* "[字段变更] 档口绑定任务：" + statusEnum.getName() + "=>绑定成功\\n" + */

                // 档口绑定任务操作日志
                wmScLogService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG,
                        bindDO.getId(), wmCanteenPoiTaskBO.getUserId(), wmCanteenPoiTaskBO.getUserName(), logInfo, "");

                // 食堂信息操作日志
                wmScLogService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, wmCanteenPoiTaskBO.getCanteenFrom().getId(),
                        wmCanteenPoiTaskBO.getUserId(), wmCanteenPoiTaskBO.getUserName(), logInfo, "");
            }
        }
    }


    /**
     * 在任务系统中创建任务
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @return 任务系统中的任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String createTaskInTicketSystem(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("WmUnBindSimpleService createTaskInTicketSystem wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        if (wmCanteenPoiTaskBO.getNextNode().getSystemType() != CanteenAuditSystemEnum.TASK_SYSTEM.getCode()) {
            return null;
        }
        try {
            WmTicketDto wmTicketDto = buildWmTicketDto(wmCanteenPoiTaskBO);
            log.info("createTaskInTicketSystem 调用任务系统发起任务参数:{}", JSONObject.toJSONString(wmTicketDto));
            int ticketId = wmCrmTicketThriftService.createTicket(wmTicketDto);
            log.info("createTaskInTicketSystem 调用任务系统发起任务结果:taskId={},ticketId={}", wmCanteenPoiTaskBO.getId(), ticketId);

            //关联任务和任务系统父任务的id
            WmScCanteenPoiTaskDO taskDO = new WmScCanteenPoiTaskDO();
            taskDO.setId(wmCanteenPoiTaskBO.getId());
            taskDO.setParentTicketId((long) ticketId);
            taskDO.setCurrentNodeCode(String.valueOf(wmCanteenPoiTaskBO.getNextNode().getCode()));
            wmScCanteenPoiTaskMapper.updateByPrimaryKeySelective(taskDO);

            wmCanteenPoiTaskBO.setNextNodeAuditSystemId(String.valueOf(ticketId));
            return String.valueOf(ticketId);
        } catch (WmTicketServerException e) {
            log.error("createTaskInTicketSystem 调用任务系统创建任务异常 wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO), e);
            throw new WmSchCantException(WmScCodeConstants.TICKET_EXCEPTION, "调用任务系统创建任务异常");
        } catch (Exception e) {
            log.error("createTaskInTicketSystem 调用任务系统创建任务异常 wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO), e);
            throw new WmSchCantException(WmScCodeConstants.TICKET_EXCEPTION, "调用任务系统创建任务异常");
        }
    }


    private WmTicketDto buildWmTicketDto(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        log.info("任务系统buildWmTicketDto wmCanteenPoiTaskBO={}",JSONObject.toJSONString(wmCanteenPoiTaskBO));
        CanteenPoiTaskTypeEnum taskTypeEnum = wmCanteenPoiTaskBO.getTaskTypeEnum();

        WmTicketDto dto = new WmTicketDto();
        //dto.setType(taskTypeEnum.getTaskType());// 任务类型id，参见注册的任务类型
        dto.setType(MccScConfig.getUnbindMainTaskAuditNodeId());// 任务类型id，参见注册的任务类型
        //dto.setSource(MccConfig.getCanteenPoiAuditSource());//任务来源, 按照约定配置
        //待处理一级状态
        dto.setStatus(CanteenPoiAuditStatusEnum.AUDITING.getCode());//审核中在任务系统表示待处理
        //待处理二级状态
        dto.setStage(1000);//任务二级状态码（业务方自定义）
        dto.setCreateUid(wmCanteenPoiTaskBO.getUserId());//创建人uid（0表示系统）
        dto.setOpUid(wmCanteenPoiTaskBO.getUserId());//任务经办人uid,外卖城市负责人
        dto.setOpUname(wmCanteenPoiTaskBO.getUserName());


        dto.setRemark(wmCanteenPoiTaskBO.getTaskReason());

        dto.setTitle("门店解绑食堂审批：");
        int time = TimeUtil.unixtime();
        dto.setUnixCtime(time);
        dto.setUnixUtime(time);
        long businessKey = Long.valueOf(String.format("%s%s", wmCanteenPoiTaskBO.getNextNode().getCode(), wmCanteenPoiTaskBO.getId()));
        //业务id
        dto.setBusinessKey(businessKey);
        dto.setIdempotencyKey(String.format("sc_canteen_poi_transfer_%s", businessKey));
        dto.setTicketPriority(1);
        Map<String, String> param = Maps.newHashMap();
        param.put("taskType", String.valueOf(wmCanteenPoiTaskBO.getTaskType()));
        param.put("taskId", String.valueOf(wmCanteenPoiTaskBO.getId()));
        param.put("isGray",String.valueOf(IS_IN_GRAY));


        List<WmEmploy> auditPersonList = wmCanteenPoiTaskBO.getAuditPersonList();
        List<Integer> uidList = auditPersonList.stream()
                .map(WmEmploy::getUid)
                .collect(Collectors.toList());
        List<String> auditPersonUidList = uidList.stream()
                .map(String::valueOf) // 将每个 Integer 转换为 String
                .collect(Collectors.toList());

        param.put("auditNodeCodeNum",String.valueOf(uidList.size()));
        String auditPersonUidListStr = JSONObject.toJSONString(auditPersonUidList).replace("\"", "'");
        param.put("auditPersonUidList", auditPersonUidListStr);
        dto.setParams(ObjectMapperUtils.parseString(param));//嵌入页面或跳转页面参数


        Map<Integer, String> targetUidMap = new LinkedHashMap<>();
        Integer[] keys = {MccScConfig.getUnbindFirstSubTaskAuditNodeId(), MccScConfig.getUnbindSecondSubTaskAuditNodeId()};
        for (int i = 0; i < Math.min(auditPersonUidList.size(), keys.length); i++) {
            targetUidMap.put(keys[i], auditPersonUidList.get(i));
        }

        Map<String,String> processVariables = new HashMap<>();
        processVariables.put("processRoutingKey", String.valueOf(MccScConfig.getUnbindMainTaskAuditNodeId()));
// 使用JSONObject.toJSONString时，确保结果字符串中的引号是单引号
        String targetUidStr = JSONObject.toJSONString(targetUidMap).replace("\"", "'");
        processVariables.put("targetUid", targetUidStr);
        dto.setProcessVariables(processVariables);
        return dto;
    }





    @Override
    public void getAuditInfo(WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO, WmCanPoiTaskSumBo wmCanPoiTaskSumBo) throws WmSchCantException, TException {
        wmCanPoiTaskSumBo.setTaskType(wmScCanteenPoiTaskDO.getTaskType());
        //通过食堂ID 获取换绑前的食堂 学校 蜂窝信息
        int canteenFrom = wmScCanteenPoiTaskDO.getCanteenIdFrom();
        WmCanPoiTaskBaseBo beforeTaskVo = wmScCanteenInfoService.buildPoiTaskBaseBo(canteenFrom, false);
        wmCanPoiTaskSumBo.setWmCanTaskBeforeVo(beforeTaskVo);

        //获取将要添加的门店信息
        List<WmScCanteenPoiTaskDetailDO> taskDetailDOList = wmScCanteenPoiTaskDetailMapper.selectByCanteenPoiTaskId(wmScCanteenPoiTaskDO.getId());
        List<Long> delWmPoiIdList = taskDetailDOList.stream().map((item) -> {
            return item.getWmPoiId();
        }).collect(Collectors.toList());
        List<WmCanPoiTaskPoiInfoBo> delPoiInfoList = wmScCanteenInfoService.buildPoiInfoBos(delWmPoiIdList);

        // 解换绑操作次数
        Map<Long,Integer> RemainingTransferCountMap = wmCanteenPoiCheckService.getRemainingTransferCount(delWmPoiIdList);
        for (WmCanPoiTaskPoiInfoBo wmCanPoiTaskPoiInfoBo : delPoiInfoList) {
            wmCanPoiTaskPoiInfoBo.setUnbindOperationCount(RemainingTransferCountMap.get(wmCanPoiTaskPoiInfoBo.getWmPoiId()));
            wmCanPoiTaskPoiInfoBo.setMaxUnbindOperationCount(MccConfig.getMaxUnbindRebind0perationsSuggest());
        }

        wmCanPoiTaskSumBo.setDelWmPoiVos(delPoiInfoList);

        String proofMaterialImage = wmScCanteenPoiTaskDO.getProofMaterialImage();
        if (StringUtils.isNotBlank(proofMaterialImage)) {
            // 去掉字符串开头和结尾的方括号
            proofMaterialImage = proofMaterialImage.substring(1, proofMaterialImage.length() - 1);
            // 按逗号分割，并去除每个元素的前后空格
            List<String> proofMaterialImageList = Arrays.stream(proofMaterialImage.split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
            wmCanPoiTaskSumBo.setProofMaterialImage(proofMaterialImageList);
        }
    }


    //生效任务
    @Override
    public void effectTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException, TException {
        log.info("[WmCanteenPoiTranferBindService.effectTask] input param: wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        // 获得合法和不合法的门店
        WmCanApprovedPoiResultBo validAndValidPoisBO = new WmCanApprovedPoiResultBo();
        List<Long> invalidPoiIdList = new ArrayList<>();
        try{
            validAndValidPoisBO = getValidAndInvalidPoiIdList(wmCanteenPoiTaskBO);
            validAndValidPoisBO.setValidPoiIdList(removeDuplicates(validAndValidPoisBO.getValidPoiIdList()));
            validAndValidPoisBO.setInvalidPoiIdList(removeDuplicates(validAndValidPoisBO.getInvalidPoiIdList()));
        }catch (Exception e){
            log.info("解绑审批后出现问题，进行兜底处理，门店绑定状态恢复原状");
            validAndValidPoisBO.setValidPoiIdList(null);
            validAndValidPoisBO.setInvalidPoiIdList(wmCanteenPoiTaskBO.getWmPoiIdList());
        }

        // 处理合法门店
        handleValidPois(wmCanteenPoiTaskBO, validAndValidPoisBO);

        // 处理非法门店
        handleInvalidPois(wmCanteenPoiTaskBO, validAndValidPoisBO.getInvalidPoiIdList(),InvalidDataSourceEnum.VALIDATION_FAILURE.getCode());
    }

    // 去重处理
    private List<Long> removeDuplicates(List<Long> poiIdList) {
        return new ArrayList<>(new HashSet<>(poiIdList));
    }




    /**
     * 外卖门店绑定流程(换绑——异步执行)
     * @param userId 用户ID
     * @param userName 用户名称
     * @param bindDOMap key->wmPoiId val->bindDO
     */
    private void transferBindCanteenPoiByWmPoiBindAsync(Map<Long, WmCanteenStallBindDO> bindDOMap, Integer userId, String userName) {
        WmCanteenStallWmPoiBindBO wmPoiBindBO = new WmCanteenStallWmPoiBindBO();
        wmPoiBindBO.setUserId(userId);
        wmPoiBindBO.setUserName(userName);
        wmPoiBindBO.setBindDOMap(bindDOMap);
        wmPoiBindBO.setOperation(CanteenStallManageTaskTypeEnum.TRANSFER_BIND.getName());

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                wmCanteenStallPoiBindService.transferBindByWmPoiBind(wmPoiBindBO);
            } catch (Exception e) {
                log.error("[WmCanteenStallManageService.bindCanteenPoiByWmPoiBindAsync] Exception. bindDOMap = {}",
                        JSONObject.toJSONString(bindDOMap), e);
            }
        })).start();
    }

    /**
     * 任务审核驳回
     */
    @Override
    public void rejectTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException{
        log.info("[WmCanteenPoiTranferBindService.rejectTask] input param: wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        handleInvalidPois(wmCanteenPoiTaskBO,wmCanteenPoiTaskBO.getWmPoiIdList(),InvalidDataSourceEnum.REVIEW_REJECTION.getCode());
    }
    @Override
    public void cancelTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException{
        log.info("[WmCanteenPoiTranferBindService.rejectTask] input param: wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        handleInvalidPois(wmCanteenPoiTaskBO,wmCanteenPoiTaskBO.getWmPoiIdList(),InvalidDataSourceEnum.APPLICATION_TERMINATION.getCode());
    }

    private void handleValidPois(WmCanteenPoiTaskBO taskDeteilBO, WmCanApprovedPoiResultBo validAndValidPoisBO)
            throws WmSchCantException, TException {
        if(taskDeteilBO == null || validAndValidPoisBO == null){
            return;
        }
        List<Long> validPoiIdList = validAndValidPoisBO.getValidPoiIdList();
        if(validPoiIdList == null || validPoiIdList.isEmpty()){
            return;
        }
        log.info("[WmTranferBindSimpleService.handleValidPois] wmCanteenPoiTaskBO = {}, validPoiIdList = {}",
                JSONObject.toJSONString(taskDeteilBO), JSONObject.toJSONString(validPoiIdList));

        // 1. 获取合法的门店列表
        // 1.1 如果合法门店列表为空，则不进行后续操作
        if (CollectionUtils.isEmpty(validPoiIdList)) {
            log.info("[WmTranferBindSimpleService.handleValidPois] validPoiIdList is empty.");
            return ;
        }

        List<WmCanteenStallBindDO> stallList = wmCanteenStallBindMapper.selectByWmPoiIdsAndCanteenPriIdWitchStatus((int) CanteenStallWmPoiBindStatusEnum.UNBINDING.getType(),validPoiIdList,taskDeteilBO.getCanteenFrom().getId());
        for(WmCanteenStallBindDO bindDO:stallList){
            wmCanteenStallBindService.unbindClueAndWmPoiAfterAudit(bindDO, taskDeteilBO.getUserId(), taskDeteilBO.getUserName(), "审核后解绑");
        }

        // 2. 增加解换绑次数
        wmPoiAttributesMapper.increaseRebindCountOrInsert(validPoiIdList);

        // 对于不在换绑中的流程处理
        List<Long> FailPoiIdList = validAndValidPoisBO.getInvalidPoiIdList();
        Set<Long> existingWmPoiIds = stallList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .collect(Collectors.toSet());
        // 筛选出 wmPoiIdList 中没有在 stallList 中的 wmPoiId
        List<Long> missingWmPoiIds = validPoiIdList.stream()
                .filter(wmPoiId -> !existingWmPoiIds.contains(wmPoiId))
                .collect(Collectors.toList());
        FailPoiIdList.addAll(missingWmPoiIds);
        validAndValidPoisBO.setInvalidPoiIdList(FailPoiIdList);
    }


    /**
     * 处理非法门店
     * @param taskDeteilBO       记录任务详情
     * @param invalidPoiIdList 非法门店
     */
    private void handleInvalidPois(WmCanteenPoiTaskBO taskDeteilBO, List<Long> invalidPoiIdList,Integer InvalidDataSourceCode) {
        // 1. 获取合法的门店列表
        // 1.1 如果合法门店列表为空，则不进行后续操作
        if (invalidPoiIdList == null ||  invalidPoiIdList.isEmpty()) {
            log.info("[WmTranferBindSimpleService.handleValidPois] validPoiIdList is empty. validAndValidPoisBO = {}",
                    JSONObject.toJSONString(taskDeteilBO));
            return;
        }

        log.info("handleInvalidPois  taskDeteilBO:{},invalidPoiIdList:{}",JSONObject.toJSONString(taskDeteilBO),JSONObject.toJSONString(invalidPoiIdList));
        // 只还原处于解绑中的门店
        List<Long> missingWmPoiIds = new ArrayList<>();
        for (Long wmPoiId : invalidPoiIdList) {
            WmCanteenStallBindDO bindDO = wmCanteenStallBindMapper.selectByWmPoiIdAndCanteenPrimaryId(wmPoiId, Math.toIntExact(taskDeteilBO.getCanteenFrom().getId()));
            if (bindDO == null || bindDO.getWmPoiBindStatus() == null) {
                missingWmPoiIds.add(wmPoiId);
            }
            if(bindDO.getWmPoiBindStatus() == CanteenStallWmPoiBindStatusEnum.UNBINDING.getType()){
                // 恢复以前状态
                List<Long> bindIdList = new ArrayList<>();
                bindIdList.add(Long.valueOf(bindDO.getId()));
                wmCanteenStallBindService.updateWmBindIdListBindStatus(bindIdList,CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());
                WmCanteenStallWmPoiBindBO wmPoiBindBO = new WmCanteenStallWmPoiBindBO();
                wmPoiBindBO.setUserId(taskDeteilBO.getUserId());
                wmPoiBindBO.setUserName(taskDeteilBO.getUserName());
                wmPoiBindBO.setWmPoiId(bindDO.getWmPoiId());
                wmPoiBindBO.setCanteenPrimaryId(bindDO.getCanteenPrimaryId());
                // 插入日志
                wmCanteenStallPoiBindService.unbindRecordBindCanteenPoiLogReject(wmPoiBindBO, Long.valueOf(bindDO.getId()),InvalidDataSourceCode);
            }
        }

        log.info("handleInvalidPois updateWmPoiListBindStatus 未还原的绑定门店id：{}",JSONObject.toJSONString(missingWmPoiIds));
    }





    @Override
    public CanteenPoiAuditStatusV2Enum commitTask(WmCanteenPoiTaskAuditMinutiaBO wmCanteenPoiTaskAuditMinutiaBO) throws WmSchCantException {

        //当前节点审批状态需要写入任务主表里面——wm_sc_canteen_poi_task  audit_status 这个审批状态对应CanteenPoiAuditStatusV2Enum枚举类型 可以通过getNextNode方法并通过节点个数来更新  需要判断是否有二级审批嘛
        log.info("[WmUnBindSimpleService.commitTask] input param: wmCanteenPoiTaskAuditMinutiaBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskAuditMinutiaBO));
        CanteenPoiAuditStatusV2Enum resultStatus = null;
        switch (ProcessSecondStatusEnum.of(wmCanteenPoiTaskAuditMinutiaBO.getAuditResult())) {
            case APPROVED:
                // 任务系统审批通过
                resultStatus = setNextNodeCodeApprove(wmCanteenPoiTaskAuditMinutiaBO);
                break;
            //case ScConstants.CRM_TICKET_REJECT_STAGE:
            case REJECTED:
                // 任务系统审批驳回
                resultStatus = setNextNodeCodeReject(wmCanteenPoiTaskAuditMinutiaBO,CanteenAuditNodeEnum.REJECT_AUDITING.getCode());
                break;
            case TERMINATED:
                // 任务系统审批终止
                resultStatus = setNextNodeCodeReject(wmCanteenPoiTaskAuditMinutiaBO,CanteenAuditNodeEnum.TASK_TERMINATED.getCode());
                break;
            default:
                throw new WmSchCantException(WmScCodeConstants.TICKET_EXCEPTION, "无法识别的任务系统回调状态");
        }
        return resultStatus;
    }

    public CanteenPoiAuditStatusV2Enum setNextNodeCodeApprove(WmCanteenPoiTaskAuditMinutiaBO minutiaBO) throws WmSchCantException {
        log.info("setNextNodeCode中minutiaBO:{}",JSONObject.toJSONString(minutiaBO));
        WmTicketDto sonTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(minutiaBO.getAuditSystemId()));
        if (sonTicketDto == null) {
            log.error("[WmTranferBindSimpleService.setNextNodeCodeApprove] ticketDto is null. auditSystemId = {}", minutiaBO.getAuditSystemId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }
        String tairLockKey = CANTEEN_POI_TASK_LOCKKEY_PREFIX + "_" + sonTicketDto.getParentTicketId();
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

        // 1.2 获取任务信息（本地）
        WmScCanteenPoiTaskDO taskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTicketId(sonTicketDto.getParentTicketId());
        if (taskDO == null) {
            log.error("[WmTranferBindSimpleService.setNextNodeCodeApprove] taskDO is null. auditSystemId = {}", sonTicketDto.getParentTicketId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }

        if (!wmScCanteenPoiTaskSimpleService.isAuditNodeToBeProcessed(minutiaBO.getTicketType(), taskDO.getAuditStatus())) {
            log.info("[食堂管理]任务系统消息通知未找到审批信息，不处理,minutiaBO={}", JSONObject.toJSONString(minutiaBO));
            return CanteenPoiAuditStatusV2Enum.NO_ACTION;
        }

        //获取节点类型
        Integer nextAuditNode = CanteenAuditProgressEnum.getNextNode(CanteenAuditProgressEnum.of(taskDO.getAuditNodeType()), Integer.valueOf(taskDO.getCurrentNodeCode()));
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);

        WmScCanteenPoiTaskDO updateTaskDO = new WmScCanteenPoiTaskDO();
        updateTaskDO.setParentTicketId(sonTicketDto.getParentTicketId());
        updateTaskDO.setId(taskDO.getId());
        updateTaskDO.setCurrentNodeCode(String.valueOf(nextAuditNode));
        updateTaskDO.setAuditStatus(auditNodeEnum.getAuditStatus());
        log.info("setNextNodeCodeApprove中updateTaskDO:{}",JSONObject.toJSONString(updateTaskDO));
        wmScCanteenPoiTaskMapper.updateByPrimaryKeySelective(updateTaskDO);

        wmScTairService.unLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);
        return null;
    }

    public CanteenPoiAuditStatusV2Enum setNextNodeCodeReject(WmCanteenPoiTaskAuditMinutiaBO minutiaBO,Integer canteenPoiAuditStatus) throws WmSchCantException {
        log.info("setNextNodeCode中minutiaBO:{}",JSONObject.toJSONString(minutiaBO));
        WmTicketDto sonTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(minutiaBO.getAuditSystemId()));
        if (sonTicketDto == null) {
            log.error("[WmTranferBindSimpleService.setNextNodeCodeReject] ticketDto is null. auditSystemId = {}", minutiaBO.getAuditSystemId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }

        String tairLockKey = CANTEEN_POI_TASK_LOCKKEY_PREFIX + "_" + sonTicketDto.getParentTicketId();
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

        // 1.2 获取任务信息（本地）
        WmScCanteenPoiTaskDO taskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTicketId(sonTicketDto.getParentTicketId());
        if (taskDO == null) {
            log.error("[WmTranferBindSimpleService.setNextNodeCodeReject] taskDO is null. auditSystemId = {}", sonTicketDto.getParentTicketId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }

        if (!wmScCanteenPoiTaskSimpleService.isAuditNodeToBeProcessed(minutiaBO.getTicketType(), taskDO.getAuditStatus())) {
            log.info("[食堂管理]任务系统消息通知未找到审批信息，不处理,minutiaBO={}", JSONObject.toJSONString(minutiaBO));
            return CanteenPoiAuditStatusV2Enum.NO_ACTION;
        }

        WmScCanteenPoiTaskDO updateTaskDO = new WmScCanteenPoiTaskDO();
        updateTaskDO.setParentTicketId(sonTicketDto.getParentTicketId());
        updateTaskDO.setId(taskDO.getId());
        //  下个节点不应该发生变化
        //updateTaskDO.setCurrentNodeCode(String.valueOf(CanteenAuditNodeEnum.REJECT_AUDITING.getCode()));
        updateTaskDO.setAuditStatus(canteenPoiAuditStatus);


        log.info("setNextNodeCode中updateTaskDO:{}",JSONObject.toJSONString(updateTaskDO));
        wmScCanteenPoiTaskMapper.updateByPrimaryKeySelective(updateTaskDO);
        wmScTairService.unLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

        return null;
    }


    public void setNextNodeCode(WmCanteenPoiTaskAuditMinutiaBO minutiaBO) throws WmSchCantException {
        log.info("setNextNodeCode中minutiaBO:{}",JSONObject.toJSONString(minutiaBO));
        WmTicketDto sonTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(minutiaBO.getAuditSystemId()));
        if (sonTicketDto == null) {
            log.error("[WmTranferBindSimpleService.getAuditStreamByAuditSystemId] ticketDto is null. auditSystemId = {}", minutiaBO.getAuditSystemId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }
        // 1.2 获取任务信息（本地）
        WmScCanteenPoiTaskDO taskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTicketId(sonTicketDto.getParentTicketId());
        if (taskDO == null) {
            log.error("[WmTranferBindSimpleService.getAuditStreamByAuditSystemId] taskDO is null. auditSystemId = {}", sonTicketDto.getParentTicketId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }

        //获取节点类型
        Integer nextAuditNode = CanteenAuditProgressEnum.getNextNode(CanteenAuditProgressEnum.of(taskDO.getAuditNodeType()), Integer.valueOf(taskDO.getCurrentNodeCode()));
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);



        WmScCanteenPoiTaskDO updateTaskDO = new WmScCanteenPoiTaskDO();
        updateTaskDO.setParentTicketId(sonTicketDto.getParentTicketId());
        updateTaskDO.setId(taskDO.getId());
        updateTaskDO.setCurrentNodeCode(String.valueOf(nextAuditNode));
        updateTaskDO.setAuditStatus(auditNodeEnum.getAuditStatus());
        log.info("setNextNodeCode中updateTaskDO:{}",JSONObject.toJSONString(updateTaskDO));
        wmScCanteenPoiTaskMapper.updateByPrimaryKeySelective(updateTaskDO);
    }


    /**
     * 获取合法和非法门店列表（下线状态，或者处于校外的门店是非法门店）
     * @param wmCanteenPoiTaskBO 记录任务详情
     * @throws WmSchCantException WmSchCantException
     */
    private WmCanApprovedPoiResultBo getValidAndInvalidPoiIdList(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("[WmCanteenPoiBindService.getValidAndInvalidPoiIdList] wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        List<Long> wmpoiIdList = wmCanteenPoiTaskBO.getWmPoiIdList();
        WmCanApprovedPoiResultBo wmCanApprovedPoiResultBo = new WmCanApprovedPoiResultBo();
        List<Long> invalidPoiIdList = new ArrayList<>();
        List<Long> validPoiIdList = new ArrayList<>();
        validPoiIdList.addAll(wmpoiIdList);

        // 获得合法门店和不合法门店（根据是否处于解绑的状态判断）
        List<WmCanteenStallBindDO> stallList = wmCanteenStallBindMapper.selectByWmPoiIdsAndCanteenPriIdWitchStatus((int) CanteenStallWmPoiBindStatusEnum.UNBINDING.getType(),validPoiIdList,wmCanteenPoiTaskBO.getCanteenFrom().getId());
        Set<Long> existingWmPoiIds = stallList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .collect(Collectors.toSet());
        // 筛选出 wmPoiIdList 中没有在 stallList 中的 wmPoiId
        List<Long> missingWmPoiIds = wmpoiIdList.stream()
                .filter(wmPoiId -> !existingWmPoiIds.contains(wmPoiId))
                .collect(Collectors.toList());
        validPoiIdList.removeAll(missingWmPoiIds);
        invalidPoiIdList.addAll(missingWmPoiIds);

        /***解绑不需要保证原来食堂存在*/
/*        WmCanteenDB canteenDB = wmCanteenMapper.selectCanteenById(wmCanteenPoiTaskBO.getCanteenTo().getId());
        if (canteenDB == null) {
            invalidPoiIdList.addAll(validPoiIdList);
            validPoiIdList.clear();
            wmCanApprovedPoiResultBo.setValidPoiIdList(validPoiIdList);
            wmCanApprovedPoiResultBo.setInvalidPoiIdList(invalidPoiIdList);
            return wmCanApprovedPoiResultBo;
        }
        String canteenStatusCheckMsg = wmCanteenStallCheckService.checkCanteenStatusAuditingOrInsertReject(canteenDB.getId());
        if (StringUtils.isNotBlank(canteenStatusCheckMsg)) {
            invalidPoiIdList.addAll(validPoiIdList);
            validPoiIdList.clear();
            wmCanApprovedPoiResultBo.setValidPoiIdList(validPoiIdList);
            wmCanApprovedPoiResultBo.setInvalidPoiIdList(invalidPoiIdList);
            return wmCanApprovedPoiResultBo;
        }*/

        // 2. 剩余可换绑和解绑次数的校验
        List<Long> invalidList  = wmCanteenPoiCheckService.validateRemainingTransferCount(validPoiIdList);
        invalidPoiIdList.addAll(invalidList);
        validPoiIdList.removeAll(invalidList);

        // 校验门店是否绑定当前食堂
        List<Long> failWmPoiIdadd1 = wmCanteenPoiCheckService.validateCanteenInSameCanteen(wmCanteenPoiTaskBO,validPoiIdList);
        invalidPoiIdList.addAll(failWmPoiIdadd1);
        validPoiIdList.removeAll(failWmPoiIdadd1);
        wmCanApprovedPoiResultBo.setValidPoiIdList(validPoiIdList);
        wmCanApprovedPoiResultBo.setInvalidPoiIdList(invalidPoiIdList);
        return wmCanApprovedPoiResultBo;
    }


    @Override
    public WmCanteenPoiTaskBO buildSimpleTaskBO(long taskId) throws WmSchCantException {
        // 实现食堂解绑门店任务详情查询流程
        // 1. 查询任务主表信息，获取基本信息
        // 2. 查询换绑后的食堂信息，包括食堂所属学校及学校区域信息
        // 3. 查询任务详情表，获取绑定的门店ID列表
        // 4. 根据门店ID列表查询门店聚合信息，获取门店详细信息


        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = new WmCanteenPoiTaskBO();
        // 1. 根据任务ID查询任务主表信息
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskMapper.selectByPrimaryKey(taskId);
        wmCanteenPoiTaskBO.setId(wmScCanteenPoiTaskDO.getId());
        wmCanteenPoiTaskBO.setUserId(wmScCanteenPoiTaskDO.getUserId());
        wmCanteenPoiTaskBO.setUserName(wmScCanteenPoiTaskDO.getUserName());
        wmCanteenPoiTaskBO.setTaskType(wmScCanteenPoiTaskDO.getTaskType());

        // 1.1 如果存在解绑前的食堂ID，查询换绑前的食堂信息
        if (wmScCanteenPoiTaskDO.getCanteenIdFrom() != null && wmScCanteenPoiTaskDO.getCanteenIdFrom() > 0) {
            WmCanteenDB canteenDBFrom = wmCanteenMapper.selectCanteenById(wmScCanteenPoiTaskDO.getCanteenIdFrom());
            wmCanteenPoiTaskBO.setCanteenFrom(canteenDBFrom);
        }

        // 3. 查询任务详情表，获取绑定的门店ID列表
        List<WmScCanteenPoiTaskDetailDO> detailDOList = wmScCanteenPoiTaskDetailMapper.selectByCanteenPoiTaskId(taskId);
        if (CollectionUtils.isEmpty(detailDOList)) {
            return wmCanteenPoiTaskBO;
        }

        // 4. 获得所有门店
        List<Long> wmPoiIdList = detailDOList.stream()
                .map(WmScCanteenPoiTaskDetailDO::getWmPoiId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return wmCanteenPoiTaskBO;
        }

        // 4. 根据门店ID列表查询门店聚合信息
        wmCanteenPoiTaskBO.setWmPoiIdList(wmPoiIdList);
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, Sets.newHashSet(
                WM_POI_FIELD_WM_POI_ID,
                WM_POI_FIELD_NAME,
                WM_POI_FIELD_NAME,
                WM_POI_FIELD_VALID,
                WM_POI_FIELD_IS_DELETE,
                WM_POI_FIELD_AOR_ID,
                WM_POI_FIELD_OWNER_UID,
                WM_POI_FIELD_FIRST_TAG,
                WM_POI_FIELD_LATITUDE,
                WM_POI_FIELD_LONGITUDE));
        if (CollectionUtils.isNotEmpty(wmPoiAggreList)) {
            wmCanteenPoiTaskBO.setWmPoiAggreList(wmPoiAggreList);
        }
        return wmCanteenPoiTaskBO;
    }

    /**
     * 获取不合法门店列表: 门店下线\门店食堂\门店坐标不在学校范围内
     * @param wmPoiIdList 需要校验的门店ID列表
     * @param schoolPrimaryId 门店所处学校主键id
     * @return List<WmScCanIllegalPoi>
     */
    public List<WmScCanIllegalPoi> getIllegalCanPoisV2(List<Long> wmPoiIdList, int schoolPrimaryId) {
        log.info("getIllegalCanPois wmPoiIdList={},schoolPrimaryId={}", JSONObject.toJSONString(wmPoiIdList), schoolPrimaryId);
        List<WmScCanIllegalPoi> wmScCanIllegalPoiList = Lists.newLinkedList();
        // 1. 获取门店信息
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList,
                Sets.newHashSet(
                        WM_POI_FIELD_WM_POI_ID,
                        WM_POI_FIELD_NAME,
                        WM_POI_FIELD_VALID,
                        WM_POI_FIELD_LATITUDE,
                        WM_POI_FIELD_LONGITUDE,
                        WM_POI_FIELD_IS_DELETE
                )
        );
        // 如果门店信息为空，则直接返回空列表
        if (com.dianping.lion.client.util.CollectionUtils.isEmpty(wmPoiAggreList)) {
            return wmScCanIllegalPoiList;
        }
        // 2. 校验门店状态
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            WmScCanIllegalPoi wmScCanIllegalPoi = new WmScCanIllegalPoi();
            wmScCanIllegalPoi.setWmPoiId(wmPoiAggre.getWm_poi_id());
            wmScCanIllegalPoi.setPoiName(wmPoiAggre.getName());
            wmScCanIllegalPoi.setSchoolPrimaryId(schoolPrimaryId);
            // 2.1 校验门店是否下线
            if (wmPoiAggre.getValid() == CanteenPoiStatusEnum.OFFLINE.getCode()) {
                wmScCanIllegalPoi.setIllegalPoiType(WmIllegalCanPoiTypeEnum.POI_OFFLINE.getCode());
                wmScCanIllegalPoiList.add(wmScCanIllegalPoi);
                continue;
            }
            // 2.2 校验门店是否已删除
            if (wmPoiAggre.getIs_delete() == WM_POI_ID_IS_DELETE) {
                wmScCanIllegalPoi.setIllegalPoiType(WmIllegalCanPoiTypeEnum.POI_DELETED.getCode());
                wmScCanIllegalPoiList.add(wmScCanIllegalPoi);
                continue;
            }
        }
        // 返回不合法的门店列表
        return wmScCanIllegalPoiList;
    }


    /**
     * 任务生效流程 ：兼容旧表逻辑部分
     * @param wmCanteenPoiTaskBO       记录任务详情
     * @param wmCanApprovedPoiResultBo 记录审批通过的合法门店和非法门店
     * @throws WmSchCantException WmSchCantException
     */
    public void effectTaskCompatibleWithOldTable(WmCanteenPoiTaskBO wmCanteenPoiTaskBO, WmCanApprovedPoiResultBo wmCanApprovedPoiResultBo)
            throws WmSchCantException {
        log.info("effectTaskCompatibleWithOldTable wmCanteenPoiTaskBO={},wmCanApprovedPoiResultBo={}", JSONObject.toJSONString(wmCanteenPoiTaskBO),
                JSONObject.toJSONString(wmCanApprovedPoiResultBo));

        // 1. 获取目标食堂ID和源食堂ID
        int canteenIdTo = wmCanteenPoiTaskBO.getCanteenTo().getId();
        int canteenIdFrom = wmCanteenPoiTaskBO.getCanteenFrom().getId();
        int time = (int) (System.currentTimeMillis() / 1000L);

        // 2. 获取合法的门店列表
        List<Long> validPoiIdList = wmCanApprovedPoiResultBo.getValidPoiIdList();

        // 3. 合法门店为空说明不需要改动任何数据
        if (CollectionUtils.isEmpty(validPoiIdList)) {
            return;
        }

        // 4. 查询原食堂生效任务 todo:删掉   todo：已经确定，这里抛出异常不会再做任何处理，也就是任务系统那里显示已经通过或者已经驳回也就是已经处理，所以这里不能抛出异常
        WmScCanteenPoiAuditDB auditFrom = wmScCanteenPoiAuditMapper.getLatestCanteenPoiEffect(canteenIdFrom);
        if (auditFrom == null) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "该食堂数据异常，换绑失败");
        }

        // 5. 获取原食堂下绑定门店
        WmScCanteenPoiAuditDBCondition condition = new WmScCanteenPoiAuditDBCondition();
        condition.setCanteenPoiAuditId(auditFrom.getId());
        condition.setCanteenId(canteenIdFrom);
        condition.setValid(ValidEnum.VALID.getTypeInt());
        List<WmScCanteenPoiAuditDetailDB> detailFromList = wmScCanteenPoiAuditDetailMapper.selectPoiByCondition(condition);
        if (CollectionUtils.isEmpty(detailFromList)) { //todo:这个地方如果发现原来的绑定关系有问题 直接抛出异常是否可能呢
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "该食堂数据异常，换绑失败");
        }

        // 8. 判断该任务下是否还有其他绑定的门店
        List<Long> bindPoiFromList = detailFromList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
        bindPoiFromList.removeAll(validPoiIdList);
        if (CollectionUtils.isEmpty(bindPoiFromList)) {
            // 如果门店只有当前门店，则将原食堂任务置为无效
            wmScCanteenPoiAuditMapper.updateAuditUnValid(auditFrom.getId());
        }
        wmScCanteenPoiAuditDetailMapper.updateBindUnValid(validPoiIdList, auditFrom.getId(), canteenIdFrom);

        // 9. 查询待绑定食堂是否已经有生效任务
        WmScCanteenPoiAuditDB auditTo = wmScCanteenPoiAuditMapper.getLatestCanteenPoiEffect(canteenIdTo);
        if (auditTo == null) {
            // 新增一条已生效的食堂任务数据
            auditTo  = WmScCanteenPoiAuditDB.builder()
                    .canteenId(canteenIdTo)
                    .auditStatus((int) CanteenPoiAuditStatusEnum.EFFECTED.getCode())
                    .valid(ValidEnum.VALID.getTypeInt())
                    .build();

            auditTo.setUserId(wmCanteenPoiTaskBO.getUserId());
            auditTo.setUserName(wmCanteenPoiTaskBO.getUserName());
            auditTo.setCtime(time);
            auditTo.setUtime(time);
            wmScCanteenPoiAuditMapper.insertSelective(auditTo);
        } else {
            auditTo.setUtime(time);
            wmScCanteenPoiAuditMapper.updateByPrimaryKey(auditTo);
        }

        // 10. 新增加生效数据
        List<WmPoiAggre> wmPoiAggreList = wmCanteenPoiTaskBO.getWmPoiAggreList();
        WmScCanteenPoiAuditDB finalAuditTo = auditTo;
        List<WmScCanteenPoiAuditDetailDB> poiDetailList = wmPoiAggreList.stream().filter(
                //如果是处于门店下线状态 或者门店坐标处于学校外面 那么需要剔除该学校
                item -> validPoiIdList.contains(item.getWm_poi_id())
        ).map((item) -> {
            return WmScCanteenPoiAuditDetailDB.builder()
                    .canteenPoiAuditId(finalAuditTo.getId())
                    .wmPoiId(item.getWm_poi_id())
                    .canteenId(canteenIdTo)
                    .valid(ValidEnum.VALID.getTypeInt())
                    .ctime(time)
                    .utime(time)
                    .build();
        }).collect(Collectors.toList());
        wmScCanteenPoiAuditDetailMapper.batchInsert(poiDetailList);
    }



    public Map<Long, WmCanteenStallBindDO> upsertCanteenStallBindListByWmPoiBind(WmCanteenPoiTaskBO taskDeteilBO,WmScCanteenPoiTaskDO auditTaskDO,Integer manageId)
            throws WmSchCantException {
        log.info("[WmCanteenStallBindService.upsertCanteenStallBindListByWmPoiBind] input param: auditTaskDO = {}, manageId = {}",
                JSONObject.toJSONString(auditTaskDO), manageId);
        // 1-根据外卖门店ID查询线索ID
        Map<Long, Long> wdcClueMap = wdcRelationServiceAdapter.getWdcClueIdMapByWmPoiIdList(taskDeteilBO.getWmPoiIdList());

        Map<Long, WmCanteenStallBindDO> bindDOMap = new HashMap<>();
        for (Long wmPoiId : taskDeteilBO.getWmPoiIdList()) {

            WmCanteenStallBindDO oldBindDO = new WmCanteenStallBindDO();
            Long wdcClueId = wdcClueMap.get(wmPoiId);
            List<WmCanteenStallBindDO> bindDOListByWmPoiId = wmCanteenStallBindMapper.selectByWmPoiId(wmPoiId);
            // 注意这个是原来的
            oldBindDO = wmCanteenStallBindMapper.selectByWmPoiIdAndCanteenPrimaryId(wmPoiId, taskDeteilBO.getCanteenIdFrom());
            WmCanteenStallBindDO insertBindDO = new WmCanteenStallBindDO();
            insertBindDO.setId(oldBindDO.getId());
            insertBindDO.setCanteenPrimaryId(taskDeteilBO.getCanteenIdTo());
            insertBindDO.setWmPoiId(wmPoiId);
            // 外卖门店绑定状态为"未绑定"
            insertBindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());
            insertBindDO.setValid(1);
            insertBindDO.setCuid(taskDeteilBO.getUserId());
            insertBindDO.setMuid(taskDeteilBO.getUserId());
            if(wdcClueId != null){ // 有线索
                //clueBindDO.setClueFollowUpStatus((int) CanteenStallClueFollowUpStatusEnum.FOLLOW_UP.getType())
                insertBindDO.setWdcClueId(wdcClueId);
                // 线索生成状态为“生成成功”
                insertBindDO.setClueGenerateStatus((int) CanteenStallClueGenerateStatusEnum.GENERATE_SUCCESS.getType());
                // 线索绑定状态初始化为“绑定中”
                insertBindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BINDING.getType());
                int result = wmCanteenStallBindMapper.insertSelective(insertBindDO);
                if (result <= 0 || insertBindDO.getId() == null) {
                    log.error("[WmCanteenStallBindService.createStallBindByBatchCreateClue] error");
                    throw new WmSchCantException(SERVER_ERROR, "创建档口绑定任务异常");
                }
                // 驱动线索绑定状态更新为"绑定成功"(异步)
                wmCanteenStallClueBindService.updateClueBindStatusSuccess(insertBindDO, taskDeteilBO.getUserId(), taskDeteilBO.getUserName(), CanteenStallManageTaskTypeEnum.WM_POI_BIND.getName());
            }else{
                wmCanteenStallBindMapper.updateByPrimaryKeySelective(insertBindDO);
            }
            // 4-创建管理档口关联关系
            wmCanteenStallManageService.createStallManageBindRel(oldBindDO.getId(), manageId);
            bindDOMap.put(wmPoiId, insertBindDO);
        }

        log.info("[WmCanteenStallBindService.upsertCanteenStallBindListByWmPoiBind] bindDOMap = {}", JSONObject.toJSONString(bindDOMap));
        return bindDOMap;
    }

}
