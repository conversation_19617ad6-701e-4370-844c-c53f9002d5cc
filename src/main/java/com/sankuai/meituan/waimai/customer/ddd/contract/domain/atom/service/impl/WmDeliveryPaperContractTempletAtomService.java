package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@ServiceTag(templetTypes = {
        WmTempletContractTypeEnum.DELIVERY_PAPER,
        WmTempletContractTypeEnum.EXCLUSIVE_AGREEMENT_PAPER
})
public class WmDeliveryPaperContractTempletAtomService extends AbstractWmContractTempletAtomService {

    private static Logger logger = LoggerFactory.getLogger(WmDeliveryPaperContractTempletAtomService.class);

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws
            WmCustomerException, TException {
        throw new UnsupportedOperationException("纸质合同不需要签约");
    }

    @Override
    public Boolean signFail(long templetContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        throw new UnsupportedOperationException("纸质合同无签约流程");
    }

    @Override
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("WmDeliveryPaperContractTempletAtomService#save, 合同录入保存开始  contractBo : {} opUid : {}  opUname : {}", JSON.toJSONString(contractBo), opUid, opName);
        VersionCheckUtil.versionCheck(contractBo.getBasicBo().getParentId(), 0);
        if (contractBo.getBasicBo().getTempletContractId() > 0) {
            return update(contractBo, opUid, opName);
        }
        ContractCheckFilter.contractSaveValidFilter(WmTempletContractTypeEnum.getByCode(contractBo.getBasicBo().getType()))
                .filter(contractBo, opUid, opName);
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString().replaceAll("-", ""));
        contractBo.getBasicBo().setStatus(CustomerContractStatus.STAGE.getCode());
        WmTempletContractDB wmTempletContractDB = WmTempletContractTransUtil.templetContractBasicBoToDb(contractBo.getBasicBo(), contractBo.getSignBoList());
        wmTempletContractDB.setOpuid(opUid);
        wmTempletContractDBMapper.insertSelective(wmTempletContractDB);
        contractBo.getBasicBo().setTempletContractId(wmTempletContractDB.getId());
        contractLogAggre.logUpdate(null, contractBo, opUid, opName);
        contractLogAggre.logStatusChange(0, contractBo.getBasicBo(), opUid, opName);
        logger.info("WmDeliveryPaperContractTempletAtomService#save, 合同录入保存结束  contractId : {} opUid : {}  opUname : {}", wmTempletContractDB.getId(), opUid, opName);
        return wmTempletContractDB.getId().intValue();
    }

    @Override
    public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("##合同修改保存开始 contractBo : {} opUid : {}  opUname : {}", JSON.toJSONString(contractBo), opUid, opName);
        VersionCheckUtil.versionCheck(contractBo.getBasicBo().getParentId(), 0);
        ContractCheckFilter.contractUpdateValidFilter(WmTempletContractTypeEnum.getByCode(contractBo.getBasicBo().getType()))
                .filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = WmContractAggre.Factory.make(contractBo.getBasicBo().getTempletContractId())
                .getWmCustomerContractBoById(false, opUid, opName);
        if (oldBo == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在合同，请刷新页面");
        }
        contractBo.getBasicBo().setStatus(CustomerContractStatus.STAGE.getCode());
        contractBo.getBasicBo().setContractNum(oldBo.getBasicBo().getContractNum());
        WmTempletContractDB wmTempletContractDB = WmTempletContractTransUtil.templetContractBasicBoToDb(contractBo.getBasicBo(), contractBo.getSignBoList());
        wmTempletContractDB.setOpuid(opUid);
        wmTempletContractDB.setVersion(oldBo.getBasicBo().getVersion());
        wmTempletContractDBMapper.updateByPrimaryKeySelective(wmTempletContractDB);
        contractLogAggre.logUpdate(oldBo, contractBo, opUid, opName);
        if (oldBo.getBasicBo().getStatus() != contractBo.getBasicBo().getStatus()) {
            contractLogAggre.logStatusChange(oldBo.getBasicBo().getStatus(), contractBo.getBasicBo(), opUid, opName);
        }
        logger.info("##合同修改保存结束 contractId : {} opUid : {}  opUname : {}", contractBo.getBasicBo().getTempletContractId(), opUid, opName);
        return (int) contractBo.getBasicBo().getTempletContractId();
    }

    @Override
    public Integer commitAudit(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        throw new UnsupportedOperationException("不支持提审");
    }

    @Override
    public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname)
            throws WmCustomerException, TException {
        throw new UnsupportedOperationException("不支持驳回");
    }

    @Override
    public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("合同生效 templetContractId:{}, opUid:{} opUname:{}", templetContractId, opUid, opUname);
        WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey(templetContractId);
        if (offlineContract == null) {
            logger.info("合同不存在或者已经失效 contractId:{},opUid:{} ,  opUname:{}",
                    templetContractId, opUid, opUname);
            return true;
        }
        int oldStatus = offlineContract.getStatus();
        if (oldStatus == CustomerContractStatus.EFFECT.getCode()) {
            logger.info("合同已经生效 contractId:{},opUid:{} ,  opUname:{}",
                    templetContractId, opUid, opUname);
            return true;
        }
        offlineContract.setStatus(CustomerContractStatus.EFFECT.getCode());
        toNextContractStatus((int) templetContractId, CustomerContractStatus.EFFECT.getCode(), opUname);
        copyContractFromOffToOnline(templetContractId, opUid, offlineContract);
        contractLogAggre.logStatusChange(oldStatus, WmTempletContractTransUtil.templetContractBasicDbToBo(offlineContract), opUid, opUname);
        return true;
    }

    private void copyContractFromOffToOnline(long templetContractId, int opUid, WmTempletContractDB offlineContract) {
        offlineContract.setOpuid(opUid);
        WmTempletContractDB onlineContract = wmTempletContractAuditedDBMapper.selectByPrimaryKey(templetContractId);
        if (onlineContract == null) {
            wmTempletContractAuditedDBMapper.insertSelective(offlineContract);
        } else {
            offlineContract.setVersion(onlineContract.getVersion());
            wmTempletContractAuditedDBMapper.updateByPrimaryKeySelective(offlineContract);
        }
    }

    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        throw new UnsupportedOperationException("非法操作，不支持废除操作。合同id:" + contractId + ", opUid:" + opUid + ", opUname:" + opUname);
    }
}
