package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.rice.common.exception.BusinessException;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.ResponseDto;
import com.sankuai.waimai.cd.crm.clue.center.sdk.constant.ResultCode;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
public final class ThriftUtils {

    private ThriftUtils() {
    }

    /**
     * 异常捕获代码
     *
     * @param function 业务逻辑
     * @param result   返回值
     * @param <D>      业务数据
     * @param <T>      ResponseDto 子类
     * @return 业务逻辑的返回值，异常时则返回默认对象
     */
    public static <D, T extends ResponseDto<? super D>> T exec(Supplier<D> function, T result) {

        try {
            D data = function.get();
            result.setCode(ResultCode.SUCCESS.getCode());
            result.setMsg(ResultCode.SUCCESS.getDesc());
            result.setData(data);
            return result;
        } catch (IllegalArgumentException e) {
            log.warn("invoke failed, invalid argument error", e);
            result.setCode(ResultCode.INVALID_PARAMETER.getCode());
            result.setMsg(e.getMessage());
            return result;
        }  catch (BusinessException e) {
            log.warn("invoke failed, business error", e);
            result.setCode(e.getCode().getCode());
            result.setMsg(e.getMessage());
            return result;
        } catch (Exception e) {
            log.error("invoke failed.", e);
            result.setCode(ResultCode.SYSTEM_ERROR.getCode());
            result.setMsg(e.getMessage() + ", traceId: " + Tracer.getServerTracer().getTraceId());
            return result;
        }
    }
}
