package com.sankuai.meituan.waimai.customer.constant.customer;


import com.google.common.collect.ImmutableSet;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;

import java.util.HashSet;
import java.util.Set;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID;

/**
 * 美食城已占用档口，查询门店主数据字段常量
 */
public class MscPoiAggreConstant {

    public static final ImmutableSet<String> MSC_POI_AGGRE_FIELDS = ImmutableSet.of(
            WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_TYPE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_UID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_ORIGIN_BRAND_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BRAND_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_SELLER_UID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_SUB_WM_POI_TYPE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE
    );

    public static final ImmutableSet<String> MSC_POI_AGGRE_FIELDS_QUA = ImmutableSet.of(
            WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_QUA_INFO_LIST,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE);

}
