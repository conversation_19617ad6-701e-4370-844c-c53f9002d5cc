package com.sankuai.meituan.waimai.customer.contract.service.impl;


import java.util.UUID;

import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;

@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.GROUP_MEAL_E})
public class WmGroupMealEContractTempletService extends AbstractWmEContractTempletService {


    @Autowired
    WmContractService wmContractService;

    private static Logger logger = LoggerFactory.getLogger(WmGroupMealEContractTempletService.class);

    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.GROUP_MEAL);

        EcontractGroupMealInfoBo groupMealInfoBo = new EcontractGroupMealInfoBo();
        WmTempletContractSignBo partyASignerBo = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyASignerBo();
        groupMealInfoBo.setPartAName(partyASignerBo.getSignName());
        groupMealInfoBo.setContractNum(contractBo.getBasicBo().getContractNum());
        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(groupMealInfoBo));
        return applyBo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String groupMealENum = ContractNumberUtil.genGroupMealENum(insertId);
        contractBo.getBasicBo().setContractNum(groupMealENum);
        logger.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, groupMealENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), groupMealENum);
        return insertId;
    }

    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
        wmCustomerContractBo.getBasicBo().setTempletContractId((long) contractId);
        // 合同废除校验
        ContractCheckFilter.groupMealInvalidValidFilter().filter(wmCustomerContractBo, opUid, opUname);
        return super.invalid(contractId, opUid, opUname);
    }

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        // 如果是发起待打包
        if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {
            try {
                // 前置校验
                ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
                // 获取当前数据
                WmCustomerContractBo oldBo = wmContractService
                        .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
                // 更新or插入
                Integer contractId = insertOrUpdate(contractBo, opUid, opName);
                // 记录变更状态
                int status = MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus();
                if (status != contractBo.getBasicBo().getStatus()) {
                    contractLogService.logStatusChange(
                            MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                            contractBo.getBasicBo(), opUid, opName);
                }
                contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
                // 正式发起待签约任务
                toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
                contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
                contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                        contractBo.getBasicBo(), opUid, opName);
                ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(), contractId, opUid);
                wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
                return contractId;
            } catch (WmCustomerException ec) {
                logger.error("【框架合同】发起待打包合同任务失败 contractBo:{} msg:{}", JSON.toJSONString(contractBo), ec.getMsg());
                throw ec;
            } catch (Exception e) {
                logger.error("【框架合同】发起待打包合同任务失败 contractBo:{}", JSON.toJSONString(contractBo), e);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
            }
        } else {
            return super.startSign(contractBo, opUid, opName);
        }
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, long contractId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(wmCustomerId)
                .applyTypeEnum(EcontractTaskApplyTypeEnum.GROUP_MEAL)
                .bizId(contractId)
                .build();
    }

}
