package com.sankuai.meituan.waimai.customer.contract.domain;

import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import lombok.Builder;
import lombok.Data;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-09-23 21:09
 * Email: <EMAIL>
 * Desc:
 */
@Data
@Builder
public class WmContractMointor {
    private long contractId;
    private WmTempletContractBasicBo offline;
    private WmTempletContractBasicBo online;
    private int errorType;//1.数量不一致；2.状态不一致
}
