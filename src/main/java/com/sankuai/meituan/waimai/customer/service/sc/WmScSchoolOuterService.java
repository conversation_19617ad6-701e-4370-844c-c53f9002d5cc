package com.sankuai.meituan.waimai.customer.service.sc;

import com.google.common.base.Predicate;
import com.google.common.collect.Iterators;
import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.WmScSchoolSensitiveWordsService;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;
import com.sankuai.meituan.waimai.infra.service.WmVirtualOrgService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.IdTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScOuterIdBatchParamDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScCityTeamResultDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: scm
 * @description: 处理对外暴露的方法
 * @author: jianghuimin02
 * @create: 2020-10-14 15:19
 **/
@Slf4j
@Service
public class WmScSchoolOuterService {


    @Autowired
    private WmVirtualOrgService.Iface wmVirtualOrgService;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private WmScSchoolSensitiveWordsService wmScSchoolSensitiveWordsService;

    /**
     * 获取城市团队信息
     *
     * @param dto
     * @return
     * @throws WmSchCantException
     */
    public List<WmScCityTeamResultDTO> getScSchoolCityTeamData(WmScOuterIdBatchParamDTO dto) throws WmSchCantException {
        List<Integer> idList = dto.getIdList();
        if (IdTypeEnum.ID_SCHOOL.equals(dto.getIdType())) {
            return getCityTeamBySchoolIdList(idList);
        } else if (IdTypeEnum.ID_CANTEEN.equals(dto.getIdType())) {
            return getCityTeamByCanteenIdList(idList);
        }
        return Lists.newArrayList();
    }


    /**
     * 根据食堂业务ID获取城市团队信息
     *
     * @return
     */
    private List<WmScCityTeamResultDTO> getCityTeamByCanteenIdList(List<Integer> idList) {
        List<WmScCityTeamResultDTO> result = new ArrayList<>();
        for (Integer id : idList) {
            WmCanteenDB condition = new WmCanteenDB();
            condition.setCanteenId(id);
            List<WmCanteenDB> canteenList = wmCanteenMapper.selectCanteenList(condition);
            wmScCanteenSensitiveWordsService.readWhenSelect(canteenList);
            if (CollectionUtils.isEmpty(canteenList)) {
                continue;
            }

            WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(canteenList.get(0).getSchoolId());
            wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDB);
            if (wmSchoolDB == null) {
                continue;
            }
            WmScCityTeamResultDTO cityTeam = getCityTeam(wmSchoolDB.getAorId(), id);
            if (cityTeam == null) {
                continue;
            }
            result.add(cityTeam);
        }
        return result;
    }

    /**
     * 根据学校业务ID获取城市团队信息
     *
     * @return
     */
    private List<WmScCityTeamResultDTO> getCityTeamBySchoolIdList(List<Integer> idList) {
        List<WmScCityTeamResultDTO> result = new ArrayList<>();
        for (Integer id : idList) {
            WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(id);
            wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDB);
            if (wmSchoolDB == null) {
                continue;
            }
            WmScCityTeamResultDTO cityTeam = getCityTeam(wmSchoolDB.getAorId(), id);
            if (cityTeam == null) {
                continue;
            }
            result.add(cityTeam);
        }
        return result;
    }


    /**
     * 查询城市团队信息
     *
     * @param aorId 蜂窝ID
     * @param id    学校或者食堂业务主键ID
     * @return
     */
    private WmScCityTeamResultDTO getCityTeam(int aorId, int id) {
        if (aorId == 0) {
            return null;
        }
        WmVirtualOrg cityVirtual = getCityVirtualByAorId(aorId);
        if (cityVirtual == null) {
            return null;
        }
        WmScCityTeamResultDTO school = new WmScCityTeamResultDTO();
        school.setId(id);
        school.setCityTeamId(cityVirtual.getId());
        school.setCityTeamName(cityVirtual.getName());
        return school;
    }


    /**
     * 根据蜂窝ID获取城市团队
     *
     * @param aorId 蜂窝ID
     * @return
     */
    private WmVirtualOrg getCityVirtualByAorId(int aorId) {
        log.info("getCityVirtualByAorId::aorId = {}", aorId);
        if (aorId <= 0) {
            return null;
        }
        try {
            WmVirtualOrg wmVirtualOrg = wmVirtualOrgService.getVirtualOrgByCode(WmOrgConstant.OrgType.WM_AOR, aorId);
            if (wmVirtualOrg == null) {
                log.info("校园食堂项目:补全蜂窝信息:获取虚拟节点为空:aorId{}", aorId);
                return null;
            }
            List<WmVirtualOrg> wmVirtualOrgs = wmVirtualOrgService.getOrgsByOrgId(wmVirtualOrg.getId(), WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP.getType());
            if (CollectionUtils.isEmpty(wmVirtualOrgs)) {
                log.info("校园食堂项目:补全蜂窝信息:获取虚拟节点列表为空:aorId{}", wmVirtualOrg.getId());
                return null;
            }
            WmVirtualOrg wmCityVirtualOrg = Iterators.find(wmVirtualOrgs.iterator(), new Predicate<WmVirtualOrg>() {
                @Override
                public boolean apply(@Nullable WmVirtualOrg input) {
                    return WmOrgConstant.OrgType.WM_ORG_CITY == input.getOrgType();
                }
            }, null);
            return wmCityVirtualOrg;
        } catch (WmServerException e) {
            log.warn("getCityVirtualByAorId::获取城市团队失败 aorId = {},msg={}", aorId, e.getMsg());
        } catch (TException e) {
            log.warn("getCityVirtualByAorId::获取城市团队失败 aorId = {}", aorId, e);
        }
        return null;
    }


}
