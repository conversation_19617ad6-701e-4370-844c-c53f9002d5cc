package com.sankuai.meituan.waimai.customer.contract.service.impl;

import java.util.UUID;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractAdAnnualFrameworkContractInfoBo;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.AD_ANNUAL_FRAMEWORK_CONTRACT_E})
public class AdAnnualFrameworkContractEContractTempletService extends AbstractWmEContractTempletService {

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.AD_ANNUAL_FRAMEWORK_CONTRACT);
        applyBo.setConfigBo(new EcontractTaskConfigBo());

        EcontractAdAnnualFrameworkContractInfoBo contractInfoBo = JSON.parseObject(contractBo.getExtraData(), EcontractAdAnnualFrameworkContractInfoBo.class);
        contractInfoBo.setContractNumber(contractBo.getBasicBo().getContractNum());
        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
        return applyBo;
    }

    @Override
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String adAnnualFrameworkContractENum = ContractNumberUtil.genAdAnnualFrameworkContractENum(insertId);
        contractBo.getBasicBo().setContractNum(adAnnualFrameworkContractENum);
        log.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, adAnnualFrameworkContractENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), adAnnualFrameworkContractENum);
        return insertId;
    }
}
