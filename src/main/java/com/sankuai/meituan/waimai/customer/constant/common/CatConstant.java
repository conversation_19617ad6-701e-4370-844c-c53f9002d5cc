package com.sankuai.meituan.waimai.customer.constant.common;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description: CAT 打点字典。 字典以静态类进行分组
 * -@date: 2022/12/8 8:00 PM
 */
public class CatConstant {


    private CatConstant() {
        // default construct
    }

    /**
     *
     */
    public static class MultiCustomerSign {

        public static final String MULTI_CUSTOMER_SIGN_PREFIX = "multi.customer.sign.";

        /**
         * POI_COUNT
         */
        public static final String POI_COUNT = MULTI_CUSTOMER_SIGN_PREFIX + "poi.count";
        /**
         * CUSTOMER_POI_COUNT
         */
        public static final String CUSTOMER_POI_COUNT = MULTI_CUSTOMER_SIGN_PREFIX + "customer.poi.count";

        /**
         * CUSTOMER_COUNT
         */
        public static final String CUSTOMER_COUNT = MULTI_CUSTOMER_SIGN_PREFIX + "customer.count";
    }

    public static class MultiCustomerPush {

        public static final String VAS_DISCOUNT_PUSH_MESSAGE_CHANNEL = "vas_discount_push_message_channel";

        public static final String TO_SIGN_PUSH_MESSAGE_CHANNEL = "to_sign_push_message_channel";

        public static final String PRE_PUSH_MESSAGE_CHANNEL = "pre_push_message_channel";

        public static final String TO_SIGN_PUSH_MESSAGE_MEDIC_CHANNEL = "to_sign_push_message_medic_channel";

        public static final String TO_SIGN_PUSH_MESSAGE_SG_CHANNEL = "to_sign_push_message_sg_channel";

        public static final String TO_SIGN_PUSH_MESSAGE_ME_CHANNEL = "to_sign_push_message_ME_channel";

        public static final String TO_SIGN_PUSH_MESSAGE_WAIMAI_CHANNEL = "to_sign_push_message_waimai_channel";

        public static final String TO_SIGN_PUSH_MESSAGE_SHANGO_POI_CHANNEL = "to_sign_push_message_shango_poi_channel";

        public static final String TO_SIGN_PUSH_MESSAGE_MEDIC_POI_CHANNEL = "to_sign_push_message_medic_poi_channel";

        public static final String TO_SIGN_PUSH_WAIMAI_POI_ME_CHANNEL = "to_sign_push_waimai_poi_ME_channel";


    }

}
