package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meituan.payment.bankinfo.thrift.idl.BankInfoService;
import com.sankuai.meituan.gis.remote.service.thrift.TAdminDivisionService;
import com.sankuai.meituan.gis.remote.vo.thrift.TAdAPIResponse;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.settle.constant.WmSettleAuditConstant;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.repository.WmSettlePaperSignRepository;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.aggre.WmSettleEffectContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.atom.service.impl.WmSettleEffectAtomServiceImpl;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleAuditContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.WmSettleInputAuditAtomService;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettlePaperSignAuditDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleLogService;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.SettleCardType;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditCommitObj;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditMsg;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmAuditApiService;
import com.sankuai.meituan.waimai.thrift.vo.PoiData;
import com.sankuai.meituan.waimai.thrift.vo.SettleData;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditSettleCommitData;
import com.sankuai.meituan.waimai.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class WmSettleInputAuditAtomServiceImpl implements WmSettleInputAuditAtomService {

    @Autowired
    private TAdminDivisionService.Iface tadminDivisionService;
    @Autowired
    private BankInfoService.Iface bankInfoService;
    @Autowired
    private WmPoiClient wmPoiClient;
    @Autowired
    private WmAuditApiService.Iface wmAuditApiService;
    @Autowired
    private WmSettlePaperSignRepository wmSettlePaperSignRepository;
    @Autowired
    private WmSettleLogService wmSettleLogService;

    @Autowired
    private WmSettleEffectAtomServiceImpl wmSettleEffectAtomService;


    @Override
    //WmCustomerId
    //WmCustomerName
    //WmSettleList
    //WmSettleProtocolDB
    //WmCustomerName
    //BizId
    //OpUid
    public WmAuditSettleCommitData buildWmAuditSettleCommitData(WmSettleAuditContext context)
            throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        String wmCustomerName = context.getWmCustomerName();
        List<WmSettle> wmSettleList = context.getWmSettleList();
        WmSettleProtocolDB wmSettleProtocolDB = wmSettlePaperSignRepository.selectByCustomerOfflineMaster(wmCustomerId);
        context.setWmSettleProtocolDB(wmSettleProtocolDB);

        WmAuditSettleCommitData wmAuditSettleCommitData = new WmAuditSettleCommitData();
        wmAuditSettleCommitData.setCustomerId(wmCustomerId);
        wmAuditSettleCommitData.setCustomerName(wmCustomerName);

        List<SettleData> settleDataList = Lists.newArrayList();
        if (wmSettleProtocolDB != null) {
            if (StringUtils.isNotBlank(wmSettleProtocolDB.getSupplementalUrl())) {
                wmAuditSettleCommitData.setSettleProtocolUrl(Lists.newArrayList(wmSettleProtocolDB.getSupplementalUrl().split(",")));
            }
            if (StringUtils.isNotBlank(wmSettleProtocolDB.getQdbUrl())) {
                wmAuditSettleCommitData.setQdbProtocolUrl(Lists.newArrayList(wmSettleProtocolDB.getQdbUrl().split(",")));
            }
        }
        for (WmSettle wmSettle : wmSettleList) {
            context.setWmSettleTemp(wmSettle);
            settleDataList.add(buildData(context));
        }
        wmAuditSettleCommitData.setSettleDatas(settleDataList);
        WmAuditCommitObj wmAuditCommitObj = new WmAuditCommitObj();
        wmAuditCommitObj.setBiz_id(context.getBizId());
        wmAuditCommitObj.setBiz_type(WmAuditTaskBizTypeConstant.SETTLE);
        wmAuditCommitObj.setWm_poi_id(0);
        wmAuditCommitObj.setCustomer_id(wmCustomerId);
        wmAuditCommitObj.setSubmit_uid(context.getOpUid());
        wmAuditCommitObj.setData(JSONObject.toJSONString(wmAuditSettleCommitData));
        context.setWmAuditCommitObj(wmAuditCommitObj);
        return wmAuditSettleCommitData;
    }


    @Override
    //WmSettlePaperSignAuditDB
    public void saveWmSettlePaperSignAuditDB(WmSettleAuditContext context)
            throws WmCustomerException {
        if (wmSettlePaperSignRepository.insert(context.getWmSettlePaperSignAuditDB()) == 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "提交审核异常，请稍后重试");
        }
        context.setBizId(context.getWmSettlePaperSignAuditDB().getId());
    }

    private SettleData buildData(WmSettleAuditContext context){
//        WmSettleAuditContext.Context serviceContext = (WmSettleAuditContext.Context);
        WmSettle wmSettle = context.getWmSettleTemp();
//        TAdminDivisionService.Iface tadminDivisionService = serviceContext.getTadminDivisionService();
//        BankInfoService.Iface bankInfoService = serviceContext.getBankInfoService();
//        WmPoiClient wmPoiClient = serviceContext.getWmPoiClient();

        SettleData settleData = new SettleData();
        settleData.setSettleId(wmSettle.getId());
        settleData.setAccCardNo(wmSettle.getAcc_cardno());
        settleData.setAccName(wmSettle.getAcc_name());
        settleData.setAccType(wmSettle.getAcctype());
        settleData.setBranchId((long)wmSettle.getBranchid());
        String provinceName = "";
        try {
            TAdAPIResponse province = tadminDivisionService.getById(wmSettle.getProvince());
            provinceName = province.getChineseFullName();
        } catch (TException e) {
            log.error("获取省份名称异常，provinceId={},wmSettleId={}", wmSettle.getProvince(), wmSettle.getId());
        }
        String cityName = "";
        try {
            TAdAPIResponse city = tadminDivisionService.getById(wmSettle.getCity());
            cityName = city.getChineseFullName();
        } catch (TException e) {
            log.error("获取城市名称异常，cityId={},wmSettleId={}", wmSettle.getCity(), wmSettle.getId());
        }
        String bankName = "";
        try {
            bankName = bankInfoService.getBankInfo(wmSettle.getBankid()).getData().get(0).getBankName();
        } catch (TException e) {
            log.error("获取银行名称异常，bankId={},wmSettleId={}", wmSettle.getBankid(), wmSettle.getId());
        }
        settleData.setBranchName(provinceName+"-"+cityName +"-"+ bankName +"-"+ wmSettle.getBranchname());
        if(wmSettle.getCard_type() == SettleCardType.WALLET.getCode()) {
            settleData.setIsWallet(1);
            if(wmSettle.getAcctype() == 1){
                settleData.setCertType((int)wmSettle.getCert_type());
                settleData.setCertNum(wmSettle.getLegal_cert_num());
                settleData.setLegalIdCard(wmSettle.getLegal_id_card());
                settleData.setLegalPerson(wmSettle.getLegal_person());
            }else if(wmSettle.getAcctype() == 2){
                settleData.setCertType((int)wmSettle.getCert_type());
                settleData.setCertNum(wmSettle.getCert_num());
                settleData.setReservePhone(wmSettle.getReserve_phone());
            }
        }else{
            settleData.setIsWallet(0);
        }
        settleData.setPartyAFinancePeople(wmSettle.getParty_a_finance_people());
        settleData.setPartyAFinancePhone(wmSettle.getParty_a_finance_phone());
        settleData.setSettleType((int)wmSettle.getSettle_type());
        if(wmSettle.getSettle_type() != 2){
            settleData.setPayPeriodNum(wmSettle.getPay_period_num());
            settleData.setPayPeriodUnit(wmSettle.getPay_period_unit());
            settleData.setPayDayOfMonth((int)wmSettle.getPay_day_of_month());
            settleData.setMinPayAmount(wmSettle.getMin_pay_amount());
        }
        List<Long> poiIds = Lists.newArrayList();
        for (Integer id : wmSettle.getWmPoiIdList()) {
            poiIds.add(id.longValue());
        }
        settleData.setPoiIds(poiIds);
        try {
            List<WmPoiDomain> wmPoiDomains = wmPoiClient.pageGetWmPoiByWmPoiIdList(poiIds);
            List<PoiData> poiDataList = Lists.newArrayList();
            for (WmPoiDomain wmPoiDomain : wmPoiDomains) {
                PoiData poiData = new PoiData();
                poiData.setWmPoiId(wmPoiDomain.getWmPoiId());
                poiData.setWmPoiName(wmPoiDomain.getName());
                poiDataList.add(poiData);
            }
            settleData.setPoiList(poiDataList);
        } catch (WmCustomerException e) {
            log.error("服务化获取门店名称异常", e);
        }
        return settleData;
    }

    @Override
    //WmCustomerId
    //OpUid
    //OpUname
    //WmAuditCommitObj
    public WmAuditMsg commitAudit(WmSettleAuditContext context) throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();
        WmAuditCommitObj wmAuditCommitObj = context.getWmAuditCommitObj();

        WmAuditMsg wmAuditMsg = null;
        try {
            wmAuditMsg = wmAuditApiService.commitAudit(wmAuditCommitObj);
            log.info("纸质结算提交审核,result={}, wmAuditCommitObj={}", JSONObject.toJSONString(wmAuditMsg),
                    JSONObject.toJSONString(wmAuditCommitObj));
            wmSettleLogService.insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "提交审核");
        } catch (WmServerException | TException e) {
            log.error("纸质结算提交审核异常,wmAuditCommitObj={},msg={}",
                    JSONObject.toJSONString(wmAuditCommitObj), e.getMessage());
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "提交审核异常，请稍后重试");
        }
        return wmAuditMsg;
    }

    @Override
    //WmCustomerId
    //OpUid
    //OpUname
    public BooleanResult paperContractSettleApprove(WmSettleAuditContext context)
            throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();

        log.info("paperContractSettleApprove, wmCustomerId={}", wmCustomerId);
        WmSettlePaperSignAuditDB wmSettlePaperSignAuditDB = new WmSettlePaperSignAuditDB();
        wmSettlePaperSignAuditDB.setWmCustomerId(wmCustomerId);
        wmSettlePaperSignAuditDB.setAuditStatus(WmSettleAuditConstant.AUDIT_PASS.getCode());
        wmSettlePaperSignAuditDB.setValid(0);
        wmSettlePaperSignRepository.updateWmSettlePaperSignAuditByCustomerId(wmSettlePaperSignAuditDB);
        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "审核通过");
        return new BooleanResult(true);
    }

    @Override
    //WmCustomerId
    //RejectReason
    //OpUid
    //OpUname
    public BooleanResult paperContractSettleReject(WmSettleAuditContext context)
            throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        String rejectReason = context.getRejectReason();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();

        log.info("paperContractSettleReject, wmCustomerId={}, rejectReason={}", wmCustomerId,
                rejectReason);
        String reason = "%s审核驳回：%s";
        WmSettlePaperSignAuditDB wmSettlePaperSignAuditDB = new WmSettlePaperSignAuditDB();
        wmSettlePaperSignAuditDB.setWmCustomerId(wmCustomerId);
        wmSettlePaperSignAuditDB.setAuditStatus(WmSettleAuditConstant.AUDIT_REJECT.getCode());
        wmSettlePaperSignAuditDB.setValid(0);
        wmSettlePaperSignAuditDB.setAuditResult(rejectReason);
        wmSettlePaperSignRepository
                .updateWmSettlePaperSignAuditByCustomerId(wmSettlePaperSignAuditDB);
        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname,
                String.format(reason, DateUtil.DateToString(new Date(), "yyyy-MM-dd"),
                        rejectReason));
        return new BooleanResult(true);
    }
}
