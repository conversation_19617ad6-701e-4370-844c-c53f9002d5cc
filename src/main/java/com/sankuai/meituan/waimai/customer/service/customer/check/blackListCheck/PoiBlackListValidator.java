package com.sankuai.meituan.waimai.customer.service.customer.check.blackListCheck;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.sankuai.meituan.waimai.customer.adapter.WmQuaAdapter;
import com.sankuai.meituan.waimai.customer.constant.customer.BlackListCheckEnum;
import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.constatnt.SceneRuleConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiQualificationInfoBo;
import com.sankuai.meituan.waimai.thrift.domain.blackwhitelist.WmBlackWhiteListInfo;
import com.sankuai.meituan.waimai.thrift.domain.rule.RuleResultDto;
import com.sankuai.meituan.waimai.thrift.domain.rule.SceneRuleResultDto;
import com.sankuai.meituan.waimai.thrift.domain.rule.WmRulePoiCertDto;
import com.sankuai.meituan.waimai.thrift.domain.rule.WmRulePoiQuaInfoDto;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.thrift.constant.QuaCategory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/29
 * @description 门店资质黑名单校验
 */
@Slf4j
@Service
public class PoiBlackListValidator extends BlackListValidator {

    @Autowired
    private WmQuaAdapter wmQuaAdapter;


    public void checkPoiBlackList(List<Long> poiIdList, int customerId) throws WmCustomerException {
        if (CollectionUtils.isEmpty(poiIdList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店黑名单校验异常，门店信息为空");
        }
        // 不命中灰度
        if (!isCustomerIdInGrayList(customerId)) {
            log.info("未命中灰度白名单，不执行黑名单校验");
            return;
        }
        // 查询门店资质
        Map<Long, List<WmPoiQualificationInfoBo>> resultMap = wmQuaAdapter.getWmPoiQualificationInfo(poiIdList);
        // 用于存储(一级类型&二级类型&资质)到门店id的映射[存在键值相同情况]
        Map<String, List<Long>> poiIdMap = new HashMap<>();
        //待校验营业执照集合
        Set<String> quaBusinessLicense = new HashSet<>();
        //待校验个人证件集合(二级类型,编号)
        Set<Pair<Integer,String> > quaIdCard = new HashSet<>();
        // 处理门店资质信息（分类、获取<资质-门店>映射关系）
        handleQuaInfo(resultMap, poiIdMap, quaBusinessLicense, quaIdCard);
        if (CollectionUtils.isEmpty(quaBusinessLicense) && CollectionUtils.isEmpty(quaIdCard)) {
            // 门店资质黑名单校验结束，待校验资质信息集合为空，记录日志并返回结果。(相当于校验通过)
            log.info("门店资质黑名单校验结束,待校验资质信息集合为空");
            return;
        }
        Set<Long> blackPoiIdSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(quaBusinessLicense)){
            // 生成黑名单校验参数，根据资质类型生成不同的参数集合
            Map<String, Object> contextBus = generateCheckParam(QuaCategory.QUA_SUBTYPE_SUB.getSubType(), quaBusinessLicense, null);
            blackPoiIdSet.addAll(checkAndGetPoiBlackList(contextBus, poiIdMap));
        }
        if (!CollectionUtils.isEmpty(quaIdCard)){
            // 生成黑名单校验参数，根据资质类型生成不同的参数集合
            Map<String, Object> contextCid = generateCheckParam(QuaCategory.QUA_SUBTYPE_CID.getSubType(), null, quaIdCard);
            blackPoiIdSet.addAll(checkAndGetPoiBlackList(contextCid, poiIdMap));
        }
        //  如果黑名单集合不为空，则门店资质命中黑名单，拼接门店信息，并抛出异常提示
        if (CollectionUtils.isNotEmpty(blackPoiIdSet)){
            String msg = blackPoiIdSet.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining("，", "", "门店信息命中黑名单，无法绑定客户"));
            log.warn("门店资质黑名单校验结束,命中黑名单的门店信息:{}, 异常信息为{}", JSONObject.toJSONString(blackPoiIdSet), msg);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, msg);
        }
        log.info("门店资质黑名单校验结束, 全部门店通过校验");
    }

    private void handleQuaInfo(Map<Long, List<WmPoiQualificationInfoBo>> resultMap, Map<String, List<Long>> poiIdMap, Set<String> quaBusinessLicense, Set<Pair<Integer,String> > quaIdCard) {
        log.info("开始处理门店资质信息,生成<一级类型&二级类型&资质，门店>以及待校验资质集合:");
        for (Map.Entry<Long, List<WmPoiQualificationInfoBo>> map : resultMap.entrySet()) {
            Long wmPoiId = map.getKey();
            for (WmPoiQualificationInfoBo wmPoiAuditObjectBo : map.getValue()) {
                // 营业执照
                if (wmPoiAuditObjectBo.getType() == QuaCategory.QUA_SUBTYPE_SUB.getSubType()) {
                    quaBusinessLicense.add(wmPoiAuditObjectBo.getNumber());
                    String quaKey = wmPoiAuditObjectBo.getType() + "&" + wmPoiAuditObjectBo.getSecondSubType() + "&" + wmPoiAuditObjectBo.getNumber();
                    List<Long> tempList = poiIdMap.computeIfAbsent(quaKey, k -> new ArrayList<>());
                    tempList.add(wmPoiId);
                }
                // 个人证件
                if (wmPoiAuditObjectBo.getType() == QuaCategory.QUA_SUBTYPE_CID.getSubType()) {
                    quaIdCard.add(new Pair<>(wmPoiAuditObjectBo.getSecondSubType(), wmPoiAuditObjectBo.getNumber()));
                    String quaKey = wmPoiAuditObjectBo.getType() + "&" + wmPoiAuditObjectBo.getSecondSubType() + "&" + wmPoiAuditObjectBo.getNumber();
                    List<Long> tempList = poiIdMap.computeIfAbsent(quaKey, k -> new ArrayList<>());
                    tempList.add(wmPoiId);
                }
            }
        }
        log.info("处理门店资质信息结束，poiIdMap:{}, quaBusinessLicense:{}, quaIdCard:{}", JSONObject.toJSONString(poiIdMap),JSONObject.toJSONString(quaBusinessLicense), JSONObject.toJSONString(quaIdCard));
    }


    private Map<String, Object> generateCheckParam(int subType, Set<String> quaBusinessLicense, Set<Pair<Integer,String> > quaIdCard) {
        log.info("开始生成黑名单校验参数,subType:{}", subType);
        Map<String, Object> context = new HashMap<>();
        WmRulePoiQuaInfoDto wmRulePoiQuaInfoDto = new WmRulePoiQuaInfoDto();
        List<WmRulePoiCertDto> certList = new ArrayList<>();
        if (subType == QuaCategory.QUA_SUBTYPE_SUB.getSubType()) {
            certList = quaBusinessLicense.stream()
                    .map(qua -> WmRulePoiCertDto.builder()
                            // 黑名单系统设置是否修改为true
                            .isModified(true)
                            .number(qua)
                            .subType(subType)
                            .secondSubType(QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSecondSubType())
                            .build())
                    .collect(Collectors.toList());
        }
        if (subType == QuaCategory.QUA_SUBTYPE_CID.getSubType()) {
            certList = quaIdCard.stream()
                    .map(pair -> WmRulePoiCertDto.builder()
                            .isModified(true)
                            .number(pair.getValue())
                            .subType(subType)
                            .secondSubType(pair.getKey())
                            .build())
                    .collect(Collectors.toList());
        }
        wmRulePoiQuaInfoDto.setCertList(certList);
        context.put(SceneRuleConstant.WM_POI_QUA_INFO_PARAM_KEY_NAME, wmRulePoiQuaInfoDto);
        return context;
    }

    private Set<Long> checkAndGetPoiBlackList(Map<String, Object> context, Map<String, List<Long>> poiIdMap) throws WmCustomerException {
        log.info("执行门店资质黑名单校验，返回命中黑名单的门店集合");
        Set<Long> blackPoiIdSet = new HashSet<>();
        SceneRuleResultDto sceneRuleResult = checkBlackList(BlackListCheckEnum.POI_CUSTOMER_BAND.getSceneCode(), context);
        if (sceneRuleResult.getPass()) {
            log.info("门店当前资质黑名单校验通过");
            return blackPoiIdSet;
        }
        // 根据校验结果获取命中黑名单门店id集合(门店校验场景下列表中只有一个元素)
        RuleResultDto resultDto = sceneRuleResult.getResult().get(0);
        List<WmBlackWhiteListInfo> infos = JSONObject.parseObject(resultDto.getData(), new TypeReference<List<WmBlackWhiteListInfo>>() {});
        for (WmBlackWhiteListInfo info : infos) {
            String poiKey = info.getTypeLevelOne() + "&" + info.getTypeLevelTwo() + "&" + info.getValue();
            blackPoiIdSet.addAll(poiIdMap.get(poiKey));
        }
        return blackPoiIdSet;
    }

}
