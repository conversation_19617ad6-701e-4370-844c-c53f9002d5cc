package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck;

import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.dservice.impl.WmSettleInputCommitDomainServiceImpl;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-11 11:46
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class SettlePreCheck implements PreCheck {

    @Autowired
    private WmSettleInputCommitDomainServiceImpl wmSettleInputCommitDomainService;

    @Override
    public void preCheck(String module, List<WmEcontractSignManualTaskDB> taskInfos, int commitUid) throws WmCustomerException, TException {
        wmSettleInputCommitDomainService.tryCommitManualTask(taskInfos.get(0).getCustomerId());
    }
}
