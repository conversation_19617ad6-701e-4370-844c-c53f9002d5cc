package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.unbind;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.CustomerPoiRelStrategyBean;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IUnBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IUnBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.unbind.PoiUnBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IUnBindCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IUnBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.poirel.flow.CustomerPoiRelFlowEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240122
 * @desc 生效客户&电子签约&无有效签约人 解绑策略
 */
@Service
@Slf4j
@Rule
public class EffCusElectNoEffSignerUnBindRule {

    /**
     * 生效&电子签约&无生效签约人KP
     *
     * @param context
     * @return
     */
    @Condition
    public boolean when(@Fact("context") CustomerPoiUnBindFlowContext context) {
        return context.getWmCustomerDB().getEffective() == 1 && context.getSignMode() == CustomerSignMode.ELECTTRONIC.getCode()
                && context.getWmCustomerKp() == null;
    }

    /**
     * 根据策略执行
     *
     * @return
     */
    @Action
    public void execute(@Fact("context") CustomerPoiUnBindFlowContext context) throws WmCustomerException {
        log.info("EffCusElectNoEffSignerUnBindRule.execute,命中生效电子签约客户无生效签约人解绑规则,customerId={},wmPoiIdSet={}", context.getCustomerId(), JSON.toJSONString(context.getWmPoiIdSet()));
        //解绑场景设置为直接解绑
        context.setCustomerPoiUnBindTypeEnum(CustomerPoiUnBindTypeEnum.DIRECT_UNBIND);

        //定义解绑各层级使用策略bean
        CustomerPoiRelStrategyBean customerPoiRelStrategyBean = CustomerPoiRelStrategyBean.builder()
                .checkBeanName("poiUnBindCheckStrategy")
                .preCoreBeanName("directUnBindPreCoreStrategy")
                .coreBeanName("directUnBindCoreStrategy")
                .afterBeanName("directUnBindAfterStrategy")
                .build();
        //根据策略bean构建解绑策略
        UnBindStrategy strategy = UnBindStrategy.buildUnBindStrategyByBean(customerPoiRelStrategyBean);
        //根据解绑策略构建解绑流程策略
        UnBindFlowStrategy unBindFlowStrategy = UnBindFlowStrategy.builder()
                .flowEnum(CustomerPoiRelFlowEnum.DIRECT_UNBIND)
                .wmPoiIdSet(context.getWmPoiIdSet())
                .unBindStrategy(strategy)
                .build();
        context.setUnBindFlowStrategyList(Lists.newArrayList(unBindFlowStrategy));
    }


    @Priority
    public int getPriority() {
        return 2;
    }


}
