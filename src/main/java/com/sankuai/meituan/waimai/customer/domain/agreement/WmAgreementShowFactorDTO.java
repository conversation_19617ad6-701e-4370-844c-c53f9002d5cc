package com.sankuai.meituan.waimai.customer.domain.agreement;

import com.google.common.collect.Multimap;
import java.util.Map;
import lombok.Data;

/**
 * 协议展示影响因子聚合
 * <AUTHOR>
 */
@Data
public class WmAgreementShowFactorDTO {
    /**
     * 按照配送方式聚合<配送方式,门店ID>
     */
    Multimap<String,Long> logisticClassifyMultimap;

    /**
     * <门店ID,影响因子>
     */
    Map<Long,WmAgreementShowFactor> showFactorMap;
}
