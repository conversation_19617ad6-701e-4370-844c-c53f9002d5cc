package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractC2ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * C2合同数据打包
 */
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.C2_CONTRACT)
public class WmEcontractC2ContractDataWrapperService implements IWmEcontractDataWrapperService {

    private static final String TEMPLET_NAME = "c2contract_info_v3.ftl";

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo)
        throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.C2CONTRACT);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("C2CONTRACT_TEMPLATE_ID", 30)); // 指定模版
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("C2CONTRACT_TEMPLATE_VERSION", 0)); // (可选)指定版本，不指定或传null、传0的话则默认为当前已发布的版本
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo, taskBo));
        return Lists.newArrayList(pdfInfoBo);
    }

    /**
     * 生成C2合同信息数据
     */
    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        EcontractC2ContractInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractC2ContractInfoBo.class);
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());

        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("contractNumber", StringUtils.defaultIfEmpty(contractInfoBo.getContractNum(), StringUtils.EMPTY));
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName",StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        pdfMap.put("partAContact", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerName(), StringUtils.EMPTY));
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partB", StringUtils.defaultIfEmpty(contractInfoBo.getPartBName(), StringUtils.EMPTY));
        pdfMap.put("partBContact", StringUtils.defaultIfEmpty(contractInfoBo.getPartBContact(), StringUtils.EMPTY));
        pdfMap.put("partBSignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.AGENT_SIGNKEY);
        pdfMap.put("agentShowName", StringUtils.defaultIfEmpty(String.valueOf(contractInfoBo.getAgentShowName()), StringUtils.EMPTY));
        pdfMap.put("agentShowId", StringUtils.defaultIfEmpty(String.valueOf(contractInfoBo.getAgentId()), StringUtils.EMPTY));
        return pdfMap;
    }
}
