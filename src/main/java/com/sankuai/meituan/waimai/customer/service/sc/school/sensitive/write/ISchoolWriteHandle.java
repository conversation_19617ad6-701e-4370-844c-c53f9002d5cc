package com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.write;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
public interface ISchoolWriteHandle {

    /**
     * 执行策略类型
     *
     * @return
     */
    KmsKeyNameEnum handleType();

    /**
     * 策略具体执行方法
     *
     * @param schoolEntryWrite
     * @return
     */
    void doWriteWhenInsertOrUpdate(SchoolEntryWrite schoolEntryWrite) throws WmSchCantException;

    void writeSourceWhenUpdate(SchoolEntryWrite schoolEntryWrite);

}
