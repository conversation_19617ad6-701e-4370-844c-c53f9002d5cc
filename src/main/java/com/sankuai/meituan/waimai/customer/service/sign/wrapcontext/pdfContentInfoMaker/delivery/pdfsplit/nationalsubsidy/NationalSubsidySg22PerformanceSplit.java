package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit.nationalsubsidy;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit.DeliveryPdfSplit;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliverySG2_2InfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

/**
 * @description: 国补闪购2.2费率模式专快混履约合同
 * @author: liuyunjie05
 * @create: 2025/5/26 19:12
 */
@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE)
public class NationalSubsidySg22PerformanceSplit implements DeliveryPdfSplit {
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.parseInt(deliveryInfoBo.getFeeMode()));

        boolean hasMtDelivery = SUPPORT_MARK.equals(deliveryInfoBo.getSupportMTDelivery());
        boolean hasSGV2_2Support = SUPPORT_MARK.equals(deliveryInfoBo.getSupportSGV2_2Delivery());

        if (feeMode == LogisticsFeeModeEnum.SHANGOU_2_2
                && (hasMtDelivery && hasSGV2_2Support)
                && CollectionUtils.isNotEmpty(tabPdfMap.get(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE))
                && deliveryInfoBo.getEcontractDeliverySG2_2InfoBo() != null) {

            // 判断是否支持新快送，此处为非新快送模版
            EcontractDeliverySG2_2InfoBo econtractDeliverySG22InfoBo = deliveryInfoBo.getEcontractDeliverySG2_2InfoBo();
            if (SUPPORT_MARK.equals(econtractDeliverySG22InfoBo.getSupportNewKS())) {
                log.info("NationalSubsidySg22PerformanceSplit, 跳过新快送门店, wmPoiId：{}", deliveryInfoBo.getWmPoiId());
                return;
            }

            List<String> performanceServiceSg22List = pdfDataMap.get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName());
            if (CollectionUtils.isEmpty(performanceServiceSg22List)) {
                performanceServiceSg22List = Lists.newArrayList();
                performanceServiceSg22List.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                performanceServiceSg22List.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName(), performanceServiceSg22List);
            log.info("ADD TO NATIONAL_SUBSIDY_SG22_PERFORMANCE, UUID: {}, wmPoiId: {},feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}
