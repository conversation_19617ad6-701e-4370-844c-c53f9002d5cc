package com.sankuai.meituan.waimai.customer.service.sc.status.mechine;

import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.service.sc.annotate.AuditField;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusSubMachine;
import com.sankuai.meituan.waimai.customer.util.diff.sc.ScDiffUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenAuditDiffBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.DiffField;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.DiffFieldBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 抽象状态机
 *
 * <AUTHOR>
 * @date 2020/11/26 20:16
 */
public abstract class AbstractAuditStatusSubMachine implements CanteenStatusSubMachine {

    @Autowired
    private WmScCanteenAuditMapper wmScCanteenAuditMapper;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmEmployService.Iface wmEmployThriftService;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Override
    public CanteenAuditDiffBo getCanteenAuditDiffBo(WmCanteenDB canteenDB) throws WmSchCantException {
        WmScCanteenAuditDO wmScCanteenAuditDO = wmScCanteenAuditMapper.selectByCanteenId(canteenDB.getId());
        wmScCanteenSensitiveWordsService.readWhenSelect(wmScCanteenAuditDO);
        CanteenAuditDiffBo canteenAuditDiffBo = new CanteenAuditDiffBo();
        ArrayList<DiffField> diffValues = ScDiffUtil.auditDiff(canteenDB, wmScCanteenAuditDO, AuditField.class);

        List<DiffFieldBo> diffFieldBoList = new ArrayList<>();
        ScDiffUtil.syncDiffFieldList(diffValues, diffFieldBoList);
        canteenAuditDiffBo.setDiffJson(diffFieldBoList);
        canteenAuditDiffBo.setUtime(wmScCanteenAuditDO.getUtime());
        canteenAuditDiffBo.setAuditResult(wmScCanteenAuditDO.getAuditResult());

        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmScCanteenAuditDO.getSchoolId());
        String responsiblePersonMisId = wmSchoolDB.getResponsiblePerson();
        canteenAuditDiffBo.setSchoolResponsiblePersonMisId(responsiblePersonMisId);

        try {
            WmEmploy wmEmploy = wmEmployThriftService.getByMisId(responsiblePersonMisId);
            if (wmEmploy != null) {
                canteenAuditDiffBo.setSchoolResponsiblePersonName(wmEmploy.getName());
            }
        } catch (Exception e) {
            throw new WmSchCantException(WmScCodeConstants.GET_EMPLOY_FAIL, "获取审核用户失败");
        }

        return canteenAuditDiffBo;
    }

    @Override
    public List<DiffField> getCanteenAuditDiffList(WmCanteenDB canteenDB) {
        WmScCanteenAuditDO wmScCanteenAuditDO = wmScCanteenAuditMapper.selectByCanteenId(canteenDB.getId());
        wmScCanteenSensitiveWordsService.readWhenSelect(wmScCanteenAuditDO);
        return ScDiffUtil.auditDiff(canteenDB, wmScCanteenAuditDO, AuditField.class);
    }

    @Override
    public boolean isAuditing(WmCanteenDB canteenDB) {
        return false;
    }

    @Override
    public boolean isReject(WmCanteenDB canteenDB) {
        return false;
    }

    @Override
    public boolean isAudited(WmCanteenDB canteenDB){
        return false;
    }

    @Override
    public boolean isInsertAuditing(WmCanteenDB canteenDB){
        return false;
    }

    @Override
    public boolean isInsertReject(WmCanteenDB canteenDB){
        return false;
    }

}
