package com.sankuai.meituan.waimai.customer.service.sign.callback.state;

import com.google.common.collect.Lists;

import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 回调模块更新task信息
 * task State变更模块
 */
@Service
public class WmEcontractTaskStateService extends AbstractWmEcontractStateService {

    @Resource
    private WmEcontractTaskBizService wmEcontractTaskService;

    /**
     * 任务变更更新task变更
     * @param batchContextDB
     * @param notifyBo
     * @return
     * @throws WmCustomerException
     */
    public Boolean changeState(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo) throws WmCustomerException {
        String toState = calToState(notifyBo);
        if (StringUtils.isEmpty(notifyBo.getExecuteName())) {
            return changeStageTaskState(batchContextDB, notifyBo, toState);
        } else {
            return changeExecuteTaskState(batchContextDB, notifyBo, toState);
        }
    }

    /**
     * 变更stage模式下状态
     * @param batchContextDB
     * @param notifyBo
     * @param toState
     * @return
     * @throws WmCustomerException
     */
    public Boolean changeStageTaskState(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo, String toState)
        throws WmCustomerException {
        List<Long> taskIdList = Lists.newArrayList(batchContextDB.getTaskIdAndTaskMap().keySet());
        wmEcontractTaskService.batchUpdateState(taskIdList, toState);
        return Boolean.TRUE;
    }

    /**
     * 变更execute模式下状态
     * @param batchContextDB
     * @param notifyBo
     * @param toState
     * @return
     */
    public Boolean changeExecuteTaskState(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo, String toState) throws WmCustomerException {
        for (Map.Entry<Long, EcontractTaskBo> entry : batchContextDB.getTaskIdAndTaskMap().entrySet()) {
            if (!notifyBo.getExecuteName().equals(entry.getValue().getApplyType())) {
                continue;
            }

            wmEcontractTaskService.batchUpdateState(Lists.newArrayList(entry.getKey()), toState);
        }
        return Boolean.TRUE;
    }
}
