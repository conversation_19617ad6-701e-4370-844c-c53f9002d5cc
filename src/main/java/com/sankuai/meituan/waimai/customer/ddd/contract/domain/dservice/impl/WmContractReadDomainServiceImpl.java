package com.sankuai.meituan.waimai.customer.ddd.contract.domain.dservice.impl;

import java.util.List;
import java.util.Map;

import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractOnlineAggre;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModuleStatus;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.dservice.IWmContractReadDomainService;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.ContractBoPageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Deprecated
@Service
public class WmContractReadDomainServiceImpl implements IWmContractReadDomainService {

    private static Logger logger = LoggerFactory.getLogger(WmContractReadDomainServiceImpl.class);

    @Override
    public WmTempletContractBasicBo getBasicById(long templetId, boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        return WmContractAggre.Factory.make(templetId).getBasicById(isEffective, opUid, opName);
    }

    @Override
    public WmCustomerContractBo getWmCustomerContractBoById(long templetId, boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        return WmContractAggre.Factory.make(templetId).getWmCustomerContractBoById(isEffective, opUid, opName);
    }

    @Override
    public List<WmCustomerContractBo> getWmCustomerContractBoListByIdList(List<Long> templetIdList, boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        return WmContractAggre.Factory.make().getWmCustomerContractBoListByIdList(templetIdList, isEffective, opUid, opName);
    }

    @Override
    public List<WmTempletContractBasicBo> getWmCustomerContractBasicListByPoiIdIdAndTypes(long wmPoiId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        return WmContractAggre.Factory.make().getWmCustomerContractBasicListByPoiIdIdAndTypes(wmPoiId, types, opUid, opName);
    }

    @Override
    public ContractBoPageData getWmCustomerContractBoListByCusId(int customerId, int pageNo, int pageSize, int opUid, String opName)
            throws WmCustomerException, TException {
        return WmContractAggre.Factory.makeWithCustomerId(customerId).getWmCustomerContractBoListByCusId(pageNo, pageSize, opUid, opName);
    }

    @Override
    public CustomerModuleStatus checkContractForPoiSetup(int customerId, long wmPoiId) throws WmCustomerException {
        return WmContractOnlineAggre.Factory.makeWithCustomerId(customerId).checkContractForPoiSetup(wmPoiId);
    }

    @Override
    public void startC1SignPreCheck(Integer customerId, int opUid, String opUname) throws WmCustomerException, TException {
        WmContractAggre.Factory.makeWithCustomerId(customerId).startC1SignPreCheck(opUid, opUname);
    }


    @Override
    public Boolean needC2ForPoiOnline(long wmPoiId) throws WmCustomerException, TException {
        return WmContractOnlineAggre.Factory.make().needC2ForPoiOnline(wmPoiId);
    }

    @Override
    public Map<Long, Boolean> needC2ForPoisOnline(List<Long> wmPoiIds) throws WmCustomerException, TException {
        return WmContractOnlineAggre.Factory.make().needC2ForPoisOnline(wmPoiIds);
    }

    @Override
    public List<WmCustomerContractBo> getAuditedContractBoListByCusIdAndTypeRT(long customerId, List<Integer> types, int opUid, String opName) throws WmCustomerException, TException {
        return WmContractAggre.Factory.makeWithCustomerId(customerId).getAuditedContractBoListByCusIdAndTypeRT(types, opUid, opName);
    }

    @Override
    public List<WmCustomerContractBo> getCusContractBoListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName) throws WmCustomerException, TException {
        return WmContractAggre.Factory.makeWithCustomerId(customerId).getCusContractBoListByCusIdAndType(types, opUid, opName);
    }

    @Override
    public Boolean hasAuditedC2ContractForAgentAndWmPoi(long wmPoiId, int agentId) throws WmCustomerException, TException {
        return WmContractAggre.Factory.make().hasAuditedC2ContractForAgentAndWmPoi(wmPoiId, agentId);
    }

    @Override
    public Map<Long, Boolean> hasAuditedC2ContractForAgentAndWmPoiBatch(Map<Long, Integer> wmPoiIdAndAgentIdMap) throws WmCustomerException, TException {
        return WmContractAggre.Factory.make().hasAuditedC2ContractForAgentAndWmPoiBatch(wmPoiIdAndAgentIdMap);
    }

    @Override
    public List<Integer> getC2PoiIdListByWmAgentId(int agentId) throws WmCustomerException, TException {
        return WmContractAggre.Factory.make().getC2PoiIdListByWmAgentId(agentId);
    }

    @Override
    public LongResult getC1ContractStartSignTime(long wmPoiId) throws WmCustomerException, TException {
        return WmContractAggre.Factory.make().getC1ContractStartSignTime(wmPoiId);
    }
}
