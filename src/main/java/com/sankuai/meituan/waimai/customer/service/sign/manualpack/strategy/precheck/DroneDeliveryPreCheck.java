package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck;

import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class DroneDeliveryPreCheck implements PreCheck {

    @Autowired
    private WmContractService wmContractService;
    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;

    @Override
    public void preCheck(String module, List<WmEcontractSignManualTaskDB> taskInfos, int commitUid) throws WmCustomerException, TException {
        wmLogisticsContractThriftServiceAdapter.trySignManualPackageInfoByChannel(taskInfos, module, commitUid);
    }
}
