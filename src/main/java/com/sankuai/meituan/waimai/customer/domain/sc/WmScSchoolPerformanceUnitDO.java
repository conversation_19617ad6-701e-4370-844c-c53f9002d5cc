package com.sankuai.meituan.waimai.customer.domain.sc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import lombok.Data;

/**
 * 学校履约管控信息单元VO
 * <AUTHOR>
 * @date 2024/06/06
 * @email <EMAIL>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WmScSchoolPerformanceUnitDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 履约主键ID
     */
    private Long performancePrimaryId;
    /**
     * 校外送校内的配送方式
     * {@link SchoolInDeliveryTypeEnum}
     */
    private Integer schoolInDeliveryType;
    /**
     * 具体使用的自配方式
     * {@link SchoolPoiSelfDeliveryTypeEnum}
     */
    private Integer poiSelfDeliveryType;
    /**
     * 商家自配送其他信息
     */
    private String poiSelfDeliveryInfo;
    /**
     * 该配送方式可将餐品送到的具体位置
     * {@link SchoolDeliverySpecificLocationEnum}
     */
    private Integer deliverySpecificLocation;
    /**
     * 不能送到校门口原因
     * {@link SchoolDeliveryNotGateReasonEnum}
     */
    private Integer deliveryNotGateReason;
    /**
     * 不能送到校门口其他原因
     */
    private String deliveryNotGateInfo;
    /**
     * 不能进校原因
     * {@link SchoolDeliveryNotEnterReasonEnum}
     */
    private Integer deliveryNotEnterReason;
    /**
     * 不能进校其他原因
     */
    private String deliveryNotEnterInfo;
    /**
     * 可以进校的原因
     * {@link SchoolDeliveryEnterReasonEnum}
     */
    private Integer deliveryEnterReason;
    /**
     * 可以进校的其他原因
     */
    private String deliveryEnterInfo;
    /**
     * 可送餐上楼原因
     * {@link SchoolDeliveryUpstairsReasonEnum}
     */
    private Integer deliveryUpstairsReason;
    /**
     * 可送餐上楼其他原因
     */
    private String deliveryUpstairsInfo;

    /**
     * 是否有效 0-无效，1-有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;

}
