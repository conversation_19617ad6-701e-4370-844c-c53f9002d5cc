package com.sankuai.meituan.waimai.customer.constant.customer;

/***
 * 客户门店绑定校验码决策结果枚举
 */
public enum CustomerPoiUnBindDecideResultEnum {
    CAN_UNBIND_DIRECT(1, "可以直接解绑"),
    CAN_PRE_BIND_FAIL_UNBIND(2, "可以预绑定失败解绑"),
    NEED_SIGN_TO_UNBIND(3, "需要签约完成才能解绑"),
    CAN_UNBIND_AND_CANCEL_SIGN(4, "可以解绑并取消签约任务"),
    CAN_UNBIND_FOR_RELEASE(5, "可以门店释放解绑"),
    PAGE_UNBIND_NO_AUTH(6, "纸质客户解绑无权限"),
    ;
    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    CustomerPoiUnBindDecideResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
