package com.sankuai.meituan.waimai.customer.util.trans;

import com.alibaba.fastjson.JSON;
import com.dianping.frog.sdk.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.WmEcontractSignTaskBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class WmEcontractTaskTransUtil {

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    private static WmFrameContractConfigService wmFrameContractConfigServiceStatic;

    @PostConstruct
    public void init() {
        wmFrameContractConfigServiceStatic = wmFrameContractConfigService;
    }

    public static List<Long> trans(List<EcontractTaskBo> taskBoList) {
        if (CollectionUtils.isEmpty(taskBoList)) {
            return Lists.newArrayList();
        }

        Set<Long> idList = Sets.newHashSet();
        for (EcontractTaskBo taskBo:taskBoList) {
            idList.add(taskBo.getBatchId());
        }
        return Lists.newArrayList(idList);
    }


    public static WmEcontractSignTaskBo transTaskDB2TaskBo(WmEcontractSignTaskDB taskDB){
        if (taskDB == null) {
            return null;
        }
        WmEcontractSignTaskBo result = new WmEcontractSignTaskBo();
        result.setTaskId(taskDB.getId());
        result.setApplyState(taskDB.getApplyState());
        result.setCustomerId(taskDB.getCustomerId());
        result.setApplyType(wmFrameContractConfigServiceStatic.handleSignTaskApplyType(taskDB));
        result.setApplyContext(taskDB.getApplyContext());
        result.setCtime(taskDB.getCtime());
        result.setCommitUid(taskDB.getCommitUid());
        result.setBizId(taskDB.getBizId());
        return result;
    }

    public static List<WmEcontractSignTaskBo> transTaskDBList2TaskBoList(List<WmEcontractSignTaskDB> taskDBList) {
        List<WmEcontractSignTaskBo> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(taskDBList)){
            return result;
        }
        for(WmEcontractSignTaskDB temp : taskDBList){
            result.add(transTaskDB2TaskBo(temp));
        }
        log.info("#transTaskDBList2TaskBoList, result:{}", JsonUtils.toJson(result));
        return result;
    }
}
