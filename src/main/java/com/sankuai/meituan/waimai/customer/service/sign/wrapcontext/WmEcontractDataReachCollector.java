package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import java.util.List;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

/**
* 确定数据触达方式组装
*/
@Service
@Slf4j
public class WmEcontractDataReachCollector implements IWmEcontractDataCollector {

    @Autowired
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext)
            throws TException, WmCustomerException {
        List<StageBatchInfoBo> stageInfoBoList = targetContext.getStageInfoBoList();
        StageBatchInfoBo smsStage = wmEcontractSmsWrapperService.wrap(originContext);
        log.info("#WmEcontractDataStampCollector,smsStage={}", JSONObject.toJSONString(smsStage));
        stageInfoBoList.add(smsStage);
    }

}
