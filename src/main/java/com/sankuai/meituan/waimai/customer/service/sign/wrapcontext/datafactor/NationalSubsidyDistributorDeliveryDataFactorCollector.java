package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.datafactor;

import com.alibaba.fastjson.JSON;
import com.google.common.annotations.VisibleForTesting;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.IWmEcontractSignDataFactorCollector;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfMakerConstant.DELIVERY_AGGREGATION_VERSION_V2;
import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/5/26 14:25
 */
@Service
public class NationalSubsidyDistributorDeliveryDataFactorCollector implements IWmEcontractSignDataFactorCollector {

    @Resource
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractSignDataFactor econtractSignDataFactor) throws TException, WmCustomerException {
        for (Map.Entry<Long, EcontractTaskBo> entry : originContext.getTaskIdAndTaskMap().entrySet()) {
            EcontractTaskBo taskBo = entry.getValue();
            if (taskBo != null) {
                EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
                List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = batchDeliveryInfoBo.getEcontractDeliveryInfoBoList();
                econtractSignDataFactor.setDeliveryMultiWmPoi(true);
                for (EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {
                    fillDeliveryFactor(econtractSignDataFactor, temp, originContext.getCustomerId());
                }
                // 是否增加打包费,城市灰度全量后打开
                if (MccConfig.getAddPackageFeeGrayAll()) {
                    econtractSignDataFactor.setAddPackageFee(true);
                }
            }
        }
    }

    private void fillDeliveryFactor(EcontractSignDataFactor econtractSignDataFactor, EcontractDeliveryInfoBo deliveryInfoBo, Integer customerId)
            throws TException, WmCustomerException {
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportExclusive())) {
            econtractSignDataFactor.setDeliverySupportExclusive(true);
        }
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportSLA())) {
            econtractSignDataFactor.setDeliverySupportSLA(true);
        }
        if (deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo() != null) {
            econtractSignDataFactor.setDeliverySupportWholeCity(true);
            if (SUPPORT_MARK.equals(deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo().getSupportSLA())) {
                econtractSignDataFactor.setDeliveryWholeCitySupportSLA(true);
            }
        }
        if (deliveryInfoBo.getEcontractDeliveryAggregationInfoBo() != null) {
            econtractSignDataFactor.setDeliverySupportAggregation(true);
            EcontractDeliveryAggregationInfoBo econtractDeliveryAggregationInfoBo = deliveryInfoBo
                    .getEcontractDeliveryAggregationInfoBo();
            if (DELIVERY_AGGREGATION_VERSION_V2.equals(econtractDeliveryAggregationInfoBo.getDeliveryAggregationVersion())) {
                econtractSignDataFactor.setDeliverySupportAggregationV2(true);
            } else {
                econtractSignDataFactor.setDeliverySupportAggregationV1(true);
            }
        }
        if (deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo() != null) {
            econtractSignDataFactor.setDeliverySupportCompanyCustomerLongDistanceDelivery(true);
        }
        if (deliveryInfoBo.getColdChainInfoBo() != null) {
            econtractSignDataFactor.setDeliverySupportColdChain(true);
        }
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportNewModle())) {
            econtractSignDataFactor.setDeliverySupportNewModle(true);
        }
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportMTDelivery())) {
            econtractSignDataFactor.setDeliverySupportMTDelivery(true);
        }
        if (!SUPPORT_MARK.equals(deliveryInfoBo.getSupportNewModle()) && SUPPORT_MARK.equals(deliveryInfoBo.getSupportSLA())) {
            econtractSignDataFactor.setDeliverySupporOldModleAndSupportSLA(true);
        }
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportSGV2_1Delivery())) {
            econtractSignDataFactor.setDeliverySupportSGV2_1Delivery(true);
        }
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportSGV2_2Delivery())) {
            econtractSignDataFactor.setDeliverySupportSGV2_2Delivery(true);
        }
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportSGV2_2Delivery())
                && deliveryInfoBo.getEcontractDeliverySG2_2InfoBo() != null) {
            EcontractDeliverySG2_2InfoBo econtractDeliverySG2_2InfoBo = deliveryInfoBo.getEcontractDeliverySG2_2InfoBo();
            if (SUPPORT_MARK.equals(econtractDeliverySG2_2InfoBo.getSupportNewKS())) {
                econtractSignDataFactor.setDeliverySupportSGV2_2DeliveryXKS(true);
            }
        }
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportSGMedicineV2_1Delivery())) {
            econtractSignDataFactor.setDeliverySupportSGMedicineV2_1Delivery(true);
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportCompanyCustomerDelivery())) {
            econtractSignDataFactor.setDeliverySupportCompanyCustomerDelivery(true);
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportSelfDelivery())) {
            econtractSignDataFactor.setDeliverySupportSelfDelivery(true);
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportMedicineV2_1NewVersion())) {
            econtractSignDataFactor.setMedicineV2_1NewVersion(true);
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportAgentNewDelivery())) {
            econtractSignDataFactor.setDeliverySupportAgentNew(true);
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportNewZS())) {
            econtractSignDataFactor.setDeliverySupportXZS(true);
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportPerDiscountInfo())) {
            econtractSignDataFactor.setSupportPerDiscountInfo(true);
        }

        if (String.valueOf(LogisticsFeeModeEnum.SHANGOU_QIKE.getCode()).equals(deliveryInfoBo.getFeeMode())) {
            econtractSignDataFactor.setDeliverySupportQK(true);
        }


        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportNewZS())) {
            econtractSignDataFactor.setDeliverySupportXZS(true);
        }
        if (String.valueOf(LogisticsFeeModeEnum.SHANGOU_QIKE.getCode()).equals(deliveryInfoBo.getFeeMode())) {
            econtractSignDataFactor.setDeliverySupportQK(true);
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportAgentCompanyCustomer())) {
            econtractSignDataFactor.setDeliverySupportQikeAgent(true);
        }

        //配送模板迁移灰度
        if (wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(customerId)) {
            if (String.valueOf(LogisticsFeeModeEnum.WAIMAI_V3.getCode()).equals(deliveryInfoBo.getFeeMode())) {
                econtractSignDataFactor.setDeliverySupportWaimaiV3Feemode(true);
            }

            if (String.valueOf(LogisticsFeeModeEnum.WAIMAI_QIKE.getCode()).equals(deliveryInfoBo.getFeeMode())) {
                econtractSignDataFactor.setDeliverySupportWaimaiQikeFeemode(true);
                econtractSignDataFactor.setDeliverySupportQK(true);
            }

            if (String.valueOf(LogisticsFeeModeEnum.WAIMAI_QIKE_V2.getCode()).equals(deliveryInfoBo.getFeeMode())) {
                econtractSignDataFactor.setDeliverySupportWaimaiQikeV2Feemode(true);
                econtractSignDataFactor.setDeliverySupportQK(true);
            }

            if (String.valueOf(LogisticsFeeModeEnum.AGENT_QIKE.getCode()).equals(deliveryInfoBo.getFeeMode())) {
                econtractSignDataFactor.setDeliverySupportQikeAgentFeemode(true);
            }
            if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportYiyaoQike())) {
                econtractSignDataFactor.setDeliverySupportYiyaoQike(true);
            }
            fillShanGouV2_2NewEnterpriseCustomerFactor(econtractSignDataFactor, deliveryInfoBo);
        }
        fillSupportSGDeliveryFactor(econtractSignDataFactor, deliveryInfoBo);
        fillAgentApFactor(econtractSignDataFactor, deliveryInfoBo);
        econtractSignDataFactor.setDeliveryFeeMode(deliveryInfoBo.getFeeMode());
    }

    /**
     * 标识是否存在代理实付+保底费的门店
     *
     * @param econtractSignDataFactor
     * @param deliveryInfoBo
     */
    private void fillAgentApFactor(EcontractSignDataFactor econtractSignDataFactor, EcontractDeliveryInfoBo deliveryInfoBo) {
        if (String.valueOf(LogisticsFeeModeEnum.AGENT_ACTUAL_PAYMENT.getCode()).equals(deliveryInfoBo.getFeeMode())) {
            econtractSignDataFactor.setDeliverySupportAgentAp(true);
        }
    }

    /**
     * 填充闪购企客新费率标识
     *
     * @param econtractSignDataFactor
     * @param deliveryInfoBo
     */
    private void fillShanGouV2_2NewEnterpriseCustomerFactor(EcontractSignDataFactor econtractSignDataFactor, EcontractDeliveryInfoBo deliveryInfoBo) {
        // 闪购企客新费率标识
        if (String.valueOf(LogisticsFeeModeEnum.SHANGOU_QIKE_V2.getCode()).equals(deliveryInfoBo.getFeeMode())) {
            econtractSignDataFactor.setDeliverySupportSGV2_2DeliveryXQK(true);
        }
    }

    /**
     * 填充闪购企客新费率标识
     *
     * @param econtractSignDataFactor
     * @param deliveryInfoBo
     */
    @VisibleForTesting
    protected void fillSupportSGDeliveryFactor(EcontractSignDataFactor econtractSignDataFactor, EcontractDeliveryInfoBo deliveryInfoBo) {
        if (String.valueOf(LogisticsFeeModeEnum.SHANGOU.getCode()).equals(deliveryInfoBo.getFeeMode())) {
            econtractSignDataFactor.setDeliverySupportSGV2_0Delivery(true);
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportAggregationDelivery())) {
            econtractSignDataFactor.setDeliverySupportAggregation(true);
        }

        if (String.valueOf(LogisticsFeeModeEnum.SHANGOU.getCode()).equals(deliveryInfoBo.getFeeMode()) && SUPPORT_MARK.equals(deliveryInfoBo.getSupportAggregationDelivery())) {
            econtractSignDataFactor.setDeliverySupportSGV2_0Aggregation(true);
        }

        if (String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()).equals(deliveryInfoBo.getFeeMode()) && SUPPORT_MARK.equals(deliveryInfoBo.getSupportAggregationDelivery())) {
            econtractSignDataFactor.setDeliverySupportSGV2_2Aggregation(true);
        }

        if (String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()).equals(deliveryInfoBo.getFeeMode()) && SUPPORT_MARK.equals(deliveryInfoBo.getSupportMTDelivery())) {
            econtractSignDataFactor.setDeliverySupportSGV2_2MTDelivery(true);
        }

    }

}
