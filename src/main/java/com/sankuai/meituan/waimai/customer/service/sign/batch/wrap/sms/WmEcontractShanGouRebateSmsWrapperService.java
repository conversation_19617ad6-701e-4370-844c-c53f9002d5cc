package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms;

import static com.sankuai.meituan.waimai.thrift.customer.constant.SMSConstant.TEMPLATE_SHANGOU_REBATE;

import java.util.List;
import java.util.Map;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.SMSConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
public class WmEcontractShanGouRebateSmsWrapperService extends AbstractWmEcontractSmsWrapperService{

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo) throws TException, WmCustomerException {
        List<Long> wmPoiIdList= wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(contextBo.getCustomerId());
        /*短信参数*/
        Map<String, String> smsParamMap = Maps.newHashMap();

        /*实名认证信息*/
        SignerInfoBo.Builder signerInfoBoBuilder = new SignerInfoBo.Builder()
                /*签约人信息*/
                .setName(contextBo.getKpBo().getSignerName())
                .setIdCardNo(contextBo.getKpBo().getSignerIDCardNum())
                .setPhone(contextBo.getKpBo().getSignerPhoneNum())
                .setBankName(contextBo.getKpBo().getSignerBankName())
                .setBankCardNo(contextBo.getKpBo().getSignerBankCardNum())
                /*短信平台配置*/
                .setClientId(SMSConstant.contractClientId)
                .setClientSecret(SMSConstant.contractClientSecret)
                .setSmsTemplateId(TEMPLATE_SHANGOU_REBATE)
                .setSmsTempletVersion(SMSConstant.smsTempletVersion)
                /*短信信息*/
                .setSmsParamMap(smsParamMap)
                .setMobileList(Lists.newArrayList(contextBo.getKpBo().getSignerPhoneNum()));

                //是否增加商家推送渠道
//                if(!CollectionUtils.isEmpty(wmPoiIdList)&&wmPoiIdList.size()==1){
//                    signerInfoBoBuilder.setChannelList(Lists.newArrayList(SMSConstant.SMS,SMSConstant.TEMPLATE_ECONTRACT_ADDED_SERVICE_DISCOUNT_CHANNEL));
//                }

                SignerInfoBo signerInfoBo=signerInfoBoBuilder.build();

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.REALNAME_AUTH_POI)
                .signerInfoBo(signerInfoBo)
                .build();
    }


}
