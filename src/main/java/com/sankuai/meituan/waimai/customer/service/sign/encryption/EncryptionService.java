package com.sankuai.meituan.waimai.customer.service.sign.encryption;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.sankuai.meituan.waimai.customer.bo.WmSignSubContextBo;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.bo.RecordTypeBO;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractEncryptionDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractEncryptionRecordDB;
import com.sankuai.meituan.waimai.customer.util.SubContextUtil;
import com.sankuai.meituan.waimai.customer.util.TairLocker;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by lixuepeng on 2021/8/17
 */

@Component("EncryptionService")
public class EncryptionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EncryptionService.class);

    private static String PREFIX = "encryption#doSave#";

    private static final ListeningExecutorService encryptionExecutorService;

    private static final ListeningExecutorService decryptionExecutorService;

    static {
        encryptionExecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.encryptionThreadPoolSize()));
        decryptionExecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.encryptionThreadPoolSize()));
    }

    @Resource(name = "customerTairLocker")
    private TairLocker                    tairLocker;
    @Autowired
    private WmEcontractEncryptionDBMapper wmEcontractEncryptionDBMapper;

    public void deleteEncryptionRecord(Integer recordType, Long recordId, String recordColumn) {
        wmEcontractEncryptionDBMapper.deleteByRecordTypeAndIdAndColumn(recordType, recordId, recordColumn);
    }

    public List<WmEcontractEncryptionRecordDB> queryEncrypttionRecordList(String toEncryptField, String toEncryptValue) {
        LOGGER.info("EncryptionService#queryEncryptedValueList toEncryptField:{}, toEncryptValue:{}", toEncryptField, toEncryptValue);
        try {
            String toEncryptValueTmp = toEncryptValue;
            //结果
            List<WmEcontractEncryptionRecordDB> encryptionRecordDBList = Collections.synchronizedList(new ArrayList<>());
            //原始待加密列表
            List<WmSignSubContextBo> subContextBoList = SubContextUtil.assemblySubContext(toEncryptValueTmp);
            //加密后列表
            List<WmSignSubContextBo> encryptedSubContextBoList = Collections.synchronizedList(new ArrayList<>());
            WmCustomerException wmCustomerException = new WmCustomerException();
            wmCustomerException.setCode(CustomerErrorCodeConstants.SUCCESS);
            if (CollectionUtils.isNotEmpty(subContextBoList)) {
                CountDownLatch latch = new CountDownLatch(subContextBoList.size());
                for (WmSignSubContextBo subContextBo : subContextBoList) {
                    ListenableFuture<WmSignSubContextBo> future = encryptionExecutorService.submit(new Callable() {
                        @Override
                        public WmSignSubContextBo call() throws Exception {
                            WmSignSubContextBo result = new WmSignSubContextBo();
                            result.setSort(subContextBo.getSort());
                            result.setSubContext(EncryptionUtil.doEncrypt(JSON.toJSONString(subContextBo)));
                            return result;
                        }
                    });
                    Futures.addCallback(future, new FutureCallback<WmSignSubContextBo>() {
                        @Override
                        public void onSuccess(@Nullable WmSignSubContextBo result) {
                            encryptedSubContextBoList.add(result);
                            latch.countDown();
                        }
                        @Override
                        public void onFailure(Throwable t) {
                            wmCustomerException.setCode(CustomerErrorCodeConstants.SYSTEM_ERROR);
                            wmCustomerException.setMsg("KMS加密失败 subContextBo=" + JSON.toJSONString(subContextBo));
                            latch.countDown();
                        }
                    });
                }
                latch.await(10, TimeUnit.SECONDS);
            }

            //如果存在加密异常
            if (wmCustomerException.getCode() != CustomerErrorCodeConstants.SUCCESS || CollectionUtils.isEmpty(encryptedSubContextBoList)) {
                throw wmCustomerException;
            }
            //进行排序
            Collections.sort(encryptedSubContextBoList, new Comparator<WmSignSubContextBo>() {
                public int compare(WmSignSubContextBo o1, WmSignSubContextBo o2) {
                    return o1.getSort() - o2.getSort();
                }
            });
            //添加结果集
            for (WmSignSubContextBo subContextBo : encryptedSubContextBoList) {
                WmEcontractEncryptionRecordDB encryptionRecordEntity = new WmEcontractEncryptionRecordDB();
                encryptionRecordEntity.setRecordColumn(toEncryptField);
                encryptionRecordEntity.setRecordEncryption(subContextBo.getSubContext());
                encryptionRecordEntity.setValid((byte) 1);
                encryptionRecordDBList.add(encryptionRecordEntity);
            }
            return encryptionRecordDBList;
        } catch (Exception e) {
            LOGGER.error("EncryptionService#queryEncryptedValueList toEncryptField:{}, toEncryptValue:{}", toEncryptField, toEncryptValue, e);
        }
        return new ArrayList<>();
    }
    
    public void doSaveEncryptionRecords(int recordType, long recordId, List<WmEcontractEncryptionRecordDB> encryptionRecordDBList) throws WmCustomerException {
        LOGGER.debug("EncryptionService#doSaveEncryptionRecords recordType:{}, recordId:{}", recordType, recordId);
        for (WmEcontractEncryptionRecordDB encryptionRecordDB : encryptionRecordDBList) {
            encryptionRecordDB.setRecordType(recordType);
            encryptionRecordDB.setRecordId(recordId);
        }
        // 获取锁，尝试3次还是加锁失败就直接跳过
        String lockKey = PREFIX + recordType + "#" + recordId;
        try {
            int retryTime = 0;
            // 获取锁三次失败,则跳过加锁过程
            while (!tairLocker.tryLock(lockKey) && retryTime <= 5) {
                retryTime++;
                Thread.sleep(MccConfig.encryptionThreadSleepMillis());
            }
            // 存在则删除
            Integer cnt = wmEcontractEncryptionDBMapper.selectCntByRecordTypeAndId(recordType, recordId);
            if (cnt != null && cnt > 0) {
                wmEcontractEncryptionDBMapper.deleteByRecordTypeAndId(recordType, recordId);
            }
            // 加密后的数据不为空说明重新进行了加密
            if (CollectionUtils.isNotEmpty(encryptionRecordDBList)) {
                wmEcontractEncryptionDBMapper.batchInsert(encryptionRecordDBList);
            }
        } catch (Exception e) {
            LOGGER.error("EncryptionService#doSaveEncryptionRecords recordType:{}, recordId:{}", recordType, recordId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "插入密文信息失败");
        } finally {
            tairLocker.unLock(lockKey);
        }
    }

    public String doHandleDecrypt(int recordType, long recordId, String recordColumn) {
        LOGGER.info("EncryptionService#doHandleDecrypt recordType:{}, recordId:{}, recordColumn:{}", recordType, recordId, recordColumn);
        try {
            List<WmEcontractEncryptionRecordDB> recordDBList = Lists.newLinkedList();
            Boolean isGrayRecordType =
                CollectionUtils.isNotEmpty(MccConfig.getGrayRecordTypeList()) && MccConfig.getGrayRecordTypeList()
                    .stream()
                    .filter(RecordTypeBO::getGrayTag)
                    .map(RecordTypeBO::getRecordTypeId)
                    .collect(Collectors.toSet())
                    .contains(recordType);
            // 命中灰度新逻辑不读主库，  在recordType(即表维度)，recordid上进行灰度。走哪个表打点， 走主、走从 ，打点。
            if (isGrayRecordType
                && recordId % 10000 < MccConfig.readSalveRecordIdGrayPercent()) {
                try {
                    recordDBList = wmEcontractEncryptionDBMapper.queryEncryptionRecord(recordType,
                        recordId,
                        recordColumn);
                    LOGGER.info("从库查询加密库数据:recordType:{}, recordId:{}, recordColumn:{}", recordType, recordId,
                        recordColumn);
                    MetricHelper.build()
                        .name(MetricConstant.METRIC_ENCRYPTION_SLAVE_READ_COUNT)
                        .tag("recordType", String.valueOf(recordType))
                        .tag("recordId", String.valueOf(recordId))
                        .count();
                    Cat.logEvent(MetricConstant.CAT_EVENT_MASTER_SALVE_READ,
                        "slave",
                        WmCustomerConstant.SUCCESS, "");

                } catch (Exception ex) {
                    LOGGER.error("加密表读从库异常", ex);
                }

                if (CollectionUtils.isEmpty(recordDBList)) {

                    recordDBList = wmEcontractEncryptionDBMapper.selectByRecordTypeAndIdAndColumn(recordType, recordId,
                        recordColumn);

                    LOGGER.info("从库未查询到加密库数据，再强制走主库:recordType:{}, recordId:{}, recordColumn:{},recordDBList：{}",
                        recordType,
                        recordId,
                        recordColumn, JSON.toJSONString(recordDBList));
                    MetricHelper.build()
                        .name(MetricConstant.METRIC_ENCRYPTION_SLAVE_TO_MASTER_READ_COUNT)
                        .tag("recordType", String.valueOf(recordType))
                        .tag("recordId", String.valueOf(recordId))
                        .count();
                }
            } else {
                recordDBList = wmEcontractEncryptionDBMapper.selectByRecordTypeAndIdAndColumn(recordType,
                    recordId,
                    recordColumn);
                Cat.logEvent(MetricConstant.CAT_EVENT_MASTER_SALVE_READ,
                    "master",
                    WmCustomerConstant.SUCCESS, "");
            }
            if (CollectionUtils.isEmpty(recordDBList)) {
                return "";
            }
            List<WmSignSubContextBo> subContextBoList = Collections.synchronizedList(new ArrayList<>());
            CountDownLatch latch = new CountDownLatch(recordDBList.size());
            WmCustomerException wmCustomerException = new WmCustomerException();
            wmCustomerException.setCode(CustomerErrorCodeConstants.SUCCESS);
            for (WmEcontractEncryptionRecordDB recordDB : recordDBList) {
                ListenableFuture<String> future = decryptionExecutorService.submit(new Callable() {
                    @Override
                    public String call() throws Exception {
                        return EncryptionUtil.doDecrypt(recordDB.getRecordEncryption());
                    }
                });
                Futures.addCallback(future, new FutureCallback<String>() {
                    @Override
                    public void onSuccess(@Nullable String result) {
                        subContextBoList.add(JSON.parseObject(result, WmSignSubContextBo.class));
                        latch.countDown();
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        wmCustomerException.setCode(CustomerErrorCodeConstants.SYSTEM_ERROR);
                        wmCustomerException.setMsg("KMS解密失败 subContextBo=" + recordDB.getRecordEncryption());
                        latch.countDown();
                    }
                });
            }
            latch.await(5, TimeUnit.SECONDS);

            //如果存在解密失败的场景
            if (wmCustomerException.getCode() != CustomerErrorCodeConstants.SUCCESS) {
                throw wmCustomerException;
            }

            if (subContextBoList.size() != recordDBList.size()) {
                LOGGER.error("EncryptionService#doHandleDecrypt add操作存在并发问题 recordDBList:{}, subContextBoList:{}",
                        JSON.toJSONString(recordDBList), JSON.toJSONString(subContextBoList));
            }

            //开始拼接解密后子文本
            Collections.sort(subContextBoList, new Comparator<WmSignSubContextBo>() {
                public int compare(WmSignSubContextBo o1, WmSignSubContextBo o2) {
                    return o1.getSort() - o2.getSort();
                }
            });

            if (MccConfig.isHandleConcurrentRecord() && isHasDuplicateSortRerod(subContextBoList)) {
                LOGGER.error("EncryptionService#doHandleDecrypt 存在重复的密文记录 subContextBoList:{}", JSON.toJSONString(subContextBoList));
                // 对于存在重复记录的数据进行修复
                List<WmSignSubContextBo> finalSubContextBoList = handleConcurrentRecord(subContextBoList);
                LOGGER.info("EncryptionService#doHandleDecrypt finalSubContextBoList:{}", JSON.toJSONString(finalSubContextBoList));
                StringBuilder resultBuilder = new StringBuilder();
                for (WmSignSubContextBo subContextBo : finalSubContextBoList) {
                    resultBuilder.append(subContextBo.getSubContext());
                }
                return resultBuilder.toString();
            } else {
                StringBuilder resultBuilder = new StringBuilder();
                for (WmSignSubContextBo subContextBo : subContextBoList) {
                    resultBuilder.append(subContextBo.getSubContext());
                }
                return resultBuilder.toString();
            }

        } catch (Exception e) {
            LOGGER.error("EncryptionService#doHandleDecrypt recordType:{}, recordId:{}, recordColumn:{}", recordType, recordId, recordColumn, e);
        }
        return "";
    }

    /**
     * 事务-分批插入加密数据
     * @param encryptionRecordDBList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsertByTransaction(List<WmEcontractEncryptionRecordDB> encryptionRecordDBList) {
        // 为空则直接返回
        if (CollectionUtils.isEmpty(encryptionRecordDBList)) {
            return true;
        }
        List<List<WmEcontractEncryptionRecordDB>> subEncryptionRecordDBList = Lists.partition(encryptionRecordDBList, 15);
        for (List<WmEcontractEncryptionRecordDB> sub : subEncryptionRecordDBList) {
            wmEcontractEncryptionDBMapper.batchInsert(sub);
        }
        return true;
    }
    
    public List<WmEcontractEncryptionRecordDB> queryEncryptionRecordsWithLabel(int recordType, long recordId, String recordColumn) {
        long lastId = 0;
        List<WmEcontractEncryptionRecordDB> finalRecordDBList = new ArrayList<>();
        List<WmEcontractEncryptionRecordDB> recordDBList = wmEcontractEncryptionDBMapper
                .selectByRecordTypeAndIdAndColumnWithLabelByMaster(lastId, recordType, recordId, recordColumn);
        while (CollectionUtils.isNotEmpty(recordDBList)) {
            finalRecordDBList.addAll(recordDBList);
            recordDBList = wmEcontractEncryptionDBMapper.selectByRecordTypeAndIdAndColumnWithLabelByMaster(
                    recordDBList.get(recordDBList.size() - 1).getId(), recordType, recordId, recordColumn);
        }
        return finalRecordDBList;
    }

    //是否存在重复的sort的记录
    private boolean isHasDuplicateSortRerod(List<WmSignSubContextBo> subContextBoList) {
        // 获取对象的sort列表
        List<Integer> sortList = subContextBoList.stream().map(WmSignSubContextBo::getSort).collect(Collectors.toList());
        // 获取distinct后的数量
        long count = sortList.stream().distinct().count();
        // 不相等则代表存在重复的sort记录
        return sortList.size() != count;
    }

    private List<WmSignSubContextBo> handleConcurrentRecord(List<WmSignSubContextBo> subContextBoList) {

        List<Integer> exitSortList = new ArrayList<>();
        List<WmSignSubContextBo> finalSubContextBoList = new ArrayList<>();

        for (WmSignSubContextBo subContextBo : subContextBoList) {
            if (!exitSortList.contains(subContextBo.getSort())) {
                exitSortList.add(subContextBo.getSort());
                finalSubContextBoList.add(subContextBo);
            }
        }
        
        Collections.sort(finalSubContextBoList, new Comparator<WmSignSubContextBo>() {
            public int compare(WmSignSubContextBo o1, WmSignSubContextBo o2) {
                return o1.getSort() - o2.getSort();
            }
        });
        LOGGER.info("EncryptionService#handleConcurrentRecord finalSubContextBoList:{}", JSON.toJSONString(finalSubContextBoList));
        return finalSubContextBoList;
    }
}
