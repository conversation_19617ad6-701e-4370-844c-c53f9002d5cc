package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.bind;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.atom.PoiBindCustomerAtomService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractFoodcityPoiTableBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc 预绑定前置核心操作策略
 * @date 20240116
 */
@Slf4j
@Service
public class PreBindPreCoreStrategy implements IBindPreCoreStrategy {

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private PoiBindCustomerAtomService poiBindCustomerAtomService;

    @Autowired
    private WmEcontractSignThriftService wmEcontractSignThriftService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Override
    public void execute(CustomerPoiBindFlowContext context) throws WmCustomerException {

        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        Integer customerId = context.getCustomerId();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();

        //步骤1：创建客户门店绑定任务
        Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerBindTask(wmCustomerDB.getId(), context.getWmPoiIdSet(), context.getCustomerOperateBO());
        context.setPoiAndTaskMaps(poiAndTaskMaps);
        log.info("PreBindPreCoreStrategy.execute,直接绑定流程的核心前置操作完成,customerId={},wmPoiIds={}", wmCustomerDB.getId(), JSON.toJSONString(context.getWmPoiIdSet()));

        //步骤2：门店预绑定待发起确认，客户门店关系更新为待发起确认
        poiBindCustomerAtomService.poiToPreBindCustomer(customerId, wmPoiIdSet, poiAndTaskMaps);
        log.info("PreBindPreCoreStrategy.preBindCustomer,客户门店关系更新为待发起绑定状态，customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));

        //步骤3：发起绑定签约&创建绑定短信记录
        Long smsTaskId = poiPreBindSign(context);
        log.info("PreBindPreCoreStrategy.preBindCustomer,发起签约预绑定，customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));

        //步骤4：记录本地短信记录
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = addBindSignSmsRecord(customerId, smsTaskId, wmPoiIdSet);
        Integer smsRecordDBId = wmCustomerPoiSmsRecordDB.getId();
        log.info("PreBindPreCoreStrategy.preBindCustomer,预绑定本地短信记录新增成功，customerId={},wmPoiIdSet={},smsRecordDBId", customerId, JSON.toJSONString(wmPoiIdSet), smsRecordDBId);

        //步骤5：更新签约任务ID到绑定任务记录
        if (smsRecordDBId != null && smsRecordDBId > 0 && !CollectionUtils.isEmpty(context.getPoiAndTaskMaps())) {
            List<Integer> taskIds = Lists.newArrayList(context.getPoiAndTaskMaps().values());
            customerTaskService.updateTaskSignTaskId(customerId, taskIds, smsRecordDBId);
        }
        log.info("PreBindPreCoreStrategy.preBindCustomer,发起签约预绑定更新短信记录为，customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));

    }

    /**
     * 发起预绑定签约
     *
     * @param context
     * @throws WmCustomerException
     */
    private Long poiPreBindSign(CustomerPoiBindFlowContext context) throws WmCustomerException {

        Integer customerId = context.getCustomerId();
        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {

            EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
            econtractTaskApplyBo.setBizId(customerId.toString());
            econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
            econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.FOODCITY_POI_TABLE);
            EcontractFoodcityPoiTableBo econtractFoodcityPoiTableBo = new EcontractFoodcityPoiTableBo();
            econtractFoodcityPoiTableBo.setPartAName(wmCustomerDB.getCustomerName());
            Map<String, Long> wmPoiInfo = Maps.newHashMap();
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(Lists.newArrayList(wmPoiIdSet),
                    Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_NAME));
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                wmPoiInfo.put(wmPoiAggre.getName(), wmPoiAggre.getWm_poi_id());
            }
            econtractFoodcityPoiTableBo.setPoiTableName(wmPoiInfo);
            econtractFoodcityPoiTableBo.setPartAOfficialSeal(wmCustomerDB.getCustomerName());
            econtractFoodcityPoiTableBo.setPartBOfficialSeal("北京三快在线科技有限公司");
            econtractFoodcityPoiTableBo.setWmPoiIdList(Lists.newArrayList(wmPoiIdSet));
            econtractTaskApplyBo.setApplyInfoBo(JSONObject.toJSONString(econtractFoodcityPoiTableBo));
            econtractTaskApplyBo.setCommitUid(context.getOpUid());
            // 发送短信
            LongResult result = wmEcontractSignThriftService.applyTask(econtractTaskApplyBo);

            return result.getValue();
        } catch (Exception e) {
            log.error("发送客户门店预绑定确认短信异常 context={}", JSONObject.toJSONString(context), e);
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            Cat.logError(String.format("%s error", CustomerMetricEnum.CUSTOMER_POI_BIND_START_SIGN.getName()), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "发送客户门店预绑定确认短信异常");
        } finally {
            String sourceDesc = context.getOpSourceDetail();
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_BIND_START_SIGN.getName(), sourceDesc, status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_BIND_START_SIGN.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_BIND.getTag(), sourceDesc)
                    .tag(CustomerMetricEnum.CUSTOMER_POI_BIND_START_SIGN.getStatus(), status).count();
        }
    }


    private WmCustomerPoiSmsRecordDB addBindSignSmsRecord(Integer customerId, Long signResultId, Set<Long> wmPoiIdSet) {
        // 记录短信记录
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = new WmCustomerPoiSmsRecordDB();
        wmCustomerPoiSmsRecordDB.setCustomerId(customerId);
        wmCustomerPoiSmsRecordDB.setTaskId(signResultId);
        wmCustomerPoiSmsRecordDB.setWmPoiIds(StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL));
        wmCustomerPoiSmsRecordDB.setTaskStatus(EcontractTaskStateEnum.TO_COMMIT.getType());
        wmCustomerPoiSmsRecordDB.setValid(CustomerConstants.VALID);
        wmCustomerPoiSmsRecordDB.setType((byte) 1);
        wmCustomerPoiSmsRecordMapper.insertSmsRecord(wmCustomerPoiSmsRecordDB);
        return wmCustomerPoiSmsRecordDB;
    }
}
