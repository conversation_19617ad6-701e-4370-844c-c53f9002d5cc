package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.settle;

import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.SETTLE_MOON_PROXY_INFO_V3)
@Service
@Slf4j
public class WmEcontractSettleMoonProxyInfoPdfMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext,
            EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractSettleMoonProxyInfoPdfMaker");
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.SETTLE_MOON_PROXY_INFO_V3;
        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("partA", StringUtils
                .defaultIfEmpty(originContext.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMap.put("partAstamp", PdfConstant.POI_SIGNKEY);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfMetaContent(pdfMap);
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        return pdfInfoBo;
    }

}
