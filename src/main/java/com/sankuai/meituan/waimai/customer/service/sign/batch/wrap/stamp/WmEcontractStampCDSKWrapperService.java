package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EstampInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/6/26 11:08
 */

@Service
public class WmEcontractStampCDSKWrapperService implements IWmEcontractStampWrapperService{
    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo, List<String> flowList) throws WmCustomerException {
        // 成都三块
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
                .setCustomerId(WmEcontractConstant.CERTIFY_CUSTOMER_CDSK)
                .build();
        // 成都三快签章
        Map<String, String> estampParamMap = Maps.newHashMap();
        estampParamMap.put(TaskConstant.PDF_ESTAMP_SIGN_KEY, PdfConstant.CDSKZXKJ_SIGN_KET);
        EstampInfoBo estampInfoBo = new EstampInfoBo.Builder()
                .setEstampMap(estampParamMap)
                .build();

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.STAMP_CDSK)
                .certifyInfoBo(certifyInfoBo)
                .estampInfoBo(estampInfoBo)
                .metaFlowList(flowList)
                .build();
    }
}
