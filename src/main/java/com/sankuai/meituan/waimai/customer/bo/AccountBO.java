package com.sankuai.meituan.waimai.customer.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2023/3/20 4:04 PM
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountBO {

    private Long accountId;

    /**
     * 账号类型： @see com.sankuai.meituan.waimai.bizuser.thrift.AcctSubType
     */
    private Integer accountSubType;

}
