package com.sankuai.meituan.waimai.customer.service.kp;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.customer.UpmAuthCheckService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertNumberModifyEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.EFFECTIVE;


/**
 * 法人KP服务
 *
 * <AUTHOR>
 * @since 05 十二月 2022
 */
@Slf4j
@Service
public class WmCustomerLegalKpService {

    /**
     * 证件类型为身份证
     */
    private static final List<Integer> ID_CERT_TYPE = Lists.newArrayList(Integer.valueOf(CertTypeEnum.ID_CARD.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_TEMP.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_COPY.getType()));

    @Resource
    private WmCustomerKpService wmCustomerKpService;
    @Resource
    private WmCustomerKpLogService wmCustomerKpLogService;
    @Resource
    private UpmAuthCheckService upmAuthCheckService;
    @Resource
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;
    @Resource
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;
    @Resource
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;
    @Resource
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;
    @Resource
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;
    @Resource
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;
    @Resource
    private MafkaMessageSendManager mafkaMessageSendManager;
    @Resource
    private WmEcontractSignBzService wmEcontractSignBzService;
    @Resource
    private WmCustomerService wmCustomerService;
    @Autowired
    WmEmployClient wmEmployClient;
    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;
    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;


    /**
     * 强制授权（针对签约代理人KP，法人授权场景）
     *
     * @param kpId
     * @param uid
     * @param uname
     * @throws WmCustomerException
     * @throws TException
     */
    @Transactional(rollbackFor = Exception.class)
    public void forceAuthForLegalKp(int kpId, int uid, String uname) throws WmCustomerException, TException {
        log.info("forceAuthForLegalKp kpId = {}, uid = {}, uname = {}", kpId, uid, uname);
        boolean hasAuth = upmAuthCheckService.hasRolePermission(uid, CustomerRoleTypeEnum.SIGNER_AGENT_KP_FORCE_MESSAGE_AUTH_ROLE.getCode());
        if (!hasAuth) {
            log.warn("当前用户没有操作【强制授权】的权限 uid:{}", uid);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前用户没有操作【强制授权】的权限");
        }
        // 法人KP授权成功-后续逻辑
        Integer commitId = authSucForLegalKp(kpId, uid, uname);
        if (commitId == null || commitId <= 0) {
            return;
        }
        // 取消电子签约任务
        try {
            log.info("[法人授权]wmEcontractSignBzService.cancelSign commitId={}", commitId);
            BooleanResult result = wmEcontractSignBzService.cancelSign(Long.valueOf(commitId), false);
            log.info("[法人授权]wmEcontractSignBzService.cancelSign result={}", result.isRes());
        } catch (Exception e) {
            log.error("[法人授权]取消短信授权异常, commitId={}", commitId, e);
        }
    }

    /**
     * 法人KP授权成功
     *
     * @param kpId 签约人KPID
     * @throws WmCustomerException
     */
    private Integer authSucForLegalKp(int kpId, int uid, String uname) throws WmCustomerException, TException {
        WmCustomerKp signerKp = wmCustomerKpService.getCustomerKpInfo(kpId);
        if (signerKp == null) {
            log.warn("签约人KP不存在 kpId:{}", kpId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP不存在");
        }
        WmCustomerDB customer = wmCustomerService.selectCustomerById(signerKp.getCustomerId());
        if (customer == null) {
            log.warn("无法查询到客户信息 kpId:{} customerId:{}", kpId, signerKp.getCustomerId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "无法查询到客户信息");
        }
        if (customer.isUnEffectived()) {
            log.warn("客户未生效 kpId:{} customerId:{}", kpId, signerKp.getCustomerId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户未生效");
        }
        // 判断是否存在生效的法人KP
        Boolean existEffectiveLegalKp = wmCustomerKpService.checkHaveEffectiveLegalKp(signerKp.getCustomerId());
        if (!existEffectiveLegalKp) {
            log.warn("客户不存在生效的法人KP customerId:{}", signerKp.getCustomerId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户不存在生效的法人KP");
        }

        WmCustomerKpAudit kpAudit = wmCustomerKpAuditMapper.selectByKpIdAndType(kpId, (int) KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        if (kpAudit == null) {
            log.warn("KP Audit记录不能为空 kpId:{} kpAuditType:{}", kpId, KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "KP Audit记录不能为空");
        }
        if (KpSignerStateMachine.EFFECT.getState() == signerKp.getState()) {
            // 签约人KP已生效过，按KP变更逻辑处理
            authSucForLegalKpForKpModify(signerKp, kpAudit, uid, uname);
        } else {
            // 签约人KP首次新建，按KP新建逻辑处理
            authSucForLegalKpForKpCreate(signerKp, kpAudit, uid, uname);
        }
        wmCustomerKpLogService.insertOplog(signerKp, "强制授权", WmCustomerOplogBo.OpType.INSERT, uid, uname);
        //更新门店客户属性
        wmCustomerPoiAttributeService.updateForKpUpdateAsy(signerKp, false);
        /**
         * 满足如下条件掉客户四要素标签
         *  1.授权通过
         *  2.生效
         *  3.签约类型为非签约人或者证件类型为非身份证
         */
        if (signerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()
                || !ID_CERT_TYPE.contains((int) signerKp.getCertType())) {
            wmCustomerKpRealAuthService.deleteFourEleTag(customer.getId());
        }
        mafkaMessageSendManager.send(new CustomerMQBody(customer.getId(), CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, null));
        return kpAudit.getCommitId();
    }

    /**
     * 法人KP授权成功-签约人KP变更场景
     *
     * @param signerKp
     * @param kpAudit
     */
    private void authSucForLegalKpForKpModify(WmCustomerKp signerKp, WmCustomerKpAudit kpAudit, int uid, String uname) throws WmCustomerException {
        WmCustomerKpTemp signerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(signerKp.getId());
        wmCustomerSensitiveWordsService.readKpWhenSelect(signerKpTemp);
        if (signerKpTemp == null) {
            log.warn("KP Temp记录不能为空 kpId:{}", signerKp.getId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP信息不存在");
        }
        if (signerKpTemp.getKpType() != KpTypeEnum.SIGNER.getType() && signerKpTemp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
            log.warn("变更KP不为签约人或签约类型不为代理人 kpId:{} kpType:{} kpSignerType:{}", signerKp.getId(), signerKpTemp.getKpType(), signerKpTemp.getSignerType());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更KP不为签约人或签约类型不为代理人");
        }
        if (signerKpTemp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            log.warn("变更签约人KP必须设置为短信授权 kpId:{} legalAuthType:{}", signerKpTemp.getId(), signerKpTemp.getLegalAuthType());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP必须设置为短信授权");
        }
        if (signerKpTemp.getState() != KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()) {
            log.warn("变更签约人KP必须处于授权中 kpId:{} state:{}", signerKpTemp.getId(), signerKpTemp.getState());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP必须处于授权中");
        }
        // 埋点&商家端推送消息
        List<WmCustomerDiffCellBo> diffMessCellBos = DiffUtil.compare(signerKp, signerKpTemp, differentCustomerKpService.getKpMessDiffFieldsMap());
        wmCustomerKpBuryingPointService.afterSingKp(diffMessCellBos, 0, transformWmCustomerKpTemp(signerKpTemp), signerKp);
        // 流程中KP生效
        wmCustomerKpService.tempKpEffect(signerKpTemp, signerKp);
        // 记录状态变更操作日志
        wmCustomerKpLogService.addKpStatusChangeLog(signerKp, uid, uname, KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING, KpSignerStateMachine.EFFECT);
        // 签约人KP证件号码变更
        wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), signerKp.getId());
        // 失效KP-Temp记录
        wmCustomerKpTempDBMapper.deleteByPrimaryKey(signerKpTemp.getId());
        // 失效KP-Audit记录
        wmCustomerKpAuditMapper.invalidAndSetResult(kpAudit.getId(), "强制授权成功");
    }

    private WmCustomerKp transformWmCustomerKpTemp(WmCustomerKpTemp kpTemp) {
        WmCustomerKp kp = new WmCustomerKp();
        BeanUtils.copyProperties(kpTemp, kp);
        kp.setId(kpTemp.getKpId());
        kp.setEffective(EFFECTIVE);
        return kp;
    }

    /**
     * 法人KP授权成功-签约人KP新建场景
     *
     * @param signerKp
     * @param kpAudit
     */
    private void authSucForLegalKpForKpCreate(WmCustomerKp signerKp, WmCustomerKpAudit kpAudit, int uid, String uname) throws WmCustomerException {
        if (signerKp.getKpType() != KpTypeEnum.SIGNER.getType() && signerKp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
            log.warn("当前KP不为签约人或签约类型不为代理人 kpId:{} kpType:{} kpSignerType:{}", signerKp.getId(), signerKp.getKpType(), signerKp.getSignerType());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前KP不为签约人或签约类型不为代理人");
        }
        if (signerKp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            log.warn("签约人KP必须设置为短信授权 kpId:{} legalAuthType:{}", signerKp, signerKp.getLegalAuthType());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP必须设置为短信授权");
        }
        if (signerKp.getState() != KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()) {
            log.warn("签约人KP必须处于授权中 kpId:{} state:{}", signerKp.getId(), signerKp.getState());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP必须处于授权中");
        }
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdateNoEx(signerKp);
        signerKp.setEffective(KpConstants.EFFECTIVE);
        signerKp.setState(KpSignerStateMachine.EFFECT.getState());
        wmCustomerKpDBMapper.updateByPrimaryKey(signerKp);
        // 记录状态变更操作日志
        wmCustomerKpLogService.addKpStatusChangeLog(signerKp, uid, uname, KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING, KpSignerStateMachine.EFFECT);
        // 失效KP-Audit记录
        wmCustomerKpAuditMapper.invalidAndSetResult(kpAudit.getId(), "强制授权成功");
    }


    /**
     * 取消授权（法人授权）
     *
     * @param kpId
     * @param uid
     * @param uname
     * @throws WmCustomerException
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelAuthForLegalKp(int kpId, int uid, String uname, int deviceType) throws WmCustomerException, TException {
        log.info("ForLegalKp kpId = {}, uid = {}, uname = {}", kpId, uid, uname);
        WmCustomerKp signerKp = wmCustomerKpService.getCustomerKpInfo(kpId);
        if (signerKp == null) {
            log.warn("签约人KP不存在 kpId:{}", kpId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP不存在");
        }
        if (deviceType == CustomerDeviceType.BUSINESS_PC.getCode() || deviceType == CustomerDeviceType.BUSINESS_APP.getCode()) {
            wmCustomerKpLogService.insertOplog(signerKp, "商家取消授权", WmCustomerOplogBo.OpType.INSERT, uid, uname);
        } else if (deviceType == CustomerDeviceType.PC.getCode() || deviceType == CustomerDeviceType.APP.getCode()) {
            wmCustomerKpLogService.insertOplog(signerKp, "BD取消授权", WmCustomerOplogBo.OpType.INSERT, uid, uname);
        } else {
            wmCustomerKpLogService.insertOplog(signerKp, "取消授权", WmCustomerOplogBo.OpType.INSERT, uid, uname);
        }
        Integer commitId = authFailForLegalKp(signerKp, uid, uname);
        //取消短信授权
        try {
            log.info("[法人授权]wmEcontractSignBzService.cancelSign commitId={}", commitId);
            BooleanResult result = wmEcontractSignBzService.cancelSign(Long.valueOf(commitId), false);
            log.info("[法人授权]wmEcontractSignBzService.cancelSign result={}", result.isRes());
        } catch (Exception e) {
            log.error("[法人授权]取消短信授权异常, commitId={}", commitId, e);
        }
    }

    /**
     * 法人KP授权失败
     *
     * @param signerKp 签约人KP
     * @throws WmCustomerException
     */
    private Integer authFailForLegalKp(WmCustomerKp signerKp, int uid, String uname) throws WmCustomerException, TException {
        WmCustomerKpAudit kpAudit = wmCustomerKpAuditMapper.selectByKpIdAndType(signerKp.getId(), (int) KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        if (kpAudit == null) {
            log.warn("KP Audit记录不能为空 kpId:{} kpAuditType:{}", signerKp.getId(), KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "KP流程记录不能为空");
        }
        if (KpSignerStateMachine.EFFECT.getState() == signerKp.getState()) {
            // 签约人KP已生效过，按KP变更逻辑处理
            authFailForLegalKpForKpModify(signerKp, kpAudit, uid, uname);
        } else {
            // 签约人KP首次新建，按KP新建逻辑处理
            authFailForLegalKpForKpCreate(signerKp, kpAudit, uid, uname);
        }
        wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), signerKp.getId());
        return kpAudit.getCommitId();
    }

    /**
     * 法人KP授权失败-签约人KP变更场景
     *
     * @param signerKp
     * @param kpAudit
     */
    private void authFailForLegalKpForKpModify(WmCustomerKp signerKp, WmCustomerKpAudit kpAudit, int uid, String uname) throws WmCustomerException {
        WmCustomerKpTemp signerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(signerKp.getId());
        wmCustomerSensitiveWordsService.readKpWhenSelect(signerKpTemp);
        if (signerKpTemp == null) {
            log.warn("KP Temp记录不能为空 kpId:{}", signerKp.getId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP信息不存在");
        }
        if (signerKpTemp.getKpType() != KpTypeEnum.SIGNER.getType() && signerKpTemp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
            log.warn("变更KP不为签约人或签约类型不为代理人 kpId:{} kpType:{} kpSignerType:{}", signerKp.getId(), signerKpTemp.getKpType(), signerKpTemp.getSignerType());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更KP不为签约人或签约类型不为代理人");
        }
        if (signerKpTemp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            log.warn("变更签约人KP必须设置为短信授权 kpId:{} legalAuthType:{}", signerKpTemp.getId(), signerKpTemp.getLegalAuthType());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP必须设置为短信授权");
        }
        if (signerKpTemp.getState() != KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()) {
            log.warn("变更签约人KP必须处于授权中 kpId:{} state:{}", signerKpTemp.getId(), signerKpTemp.getState());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP必须处于授权中");
        }
        signerKpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
        wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(signerKpTemp);
        wmCustomerKpTempDBMapper.updateByPrimaryKey(signerKpTemp);
        // 记录状态变更操作日志
        wmCustomerKpLogService.addKpStatusChangeLog(signerKp, uid, uname, KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING, KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL);
        // 失效KP-Audit记录
        wmCustomerKpAuditMapper.invalidAndSetResult(kpAudit.getId(), "取消授权");
    }

    /**
     * 法人KP授权失败-签约人KP新建场景
     *
     * @param signerKp
     * @param kpAudit
     */
    private void authFailForLegalKpForKpCreate(WmCustomerKp signerKp, WmCustomerKpAudit kpAudit, int uid, String uname) throws WmCustomerException {
        if (signerKp.getKpType() != KpTypeEnum.SIGNER.getType() && signerKp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
            log.warn("当前KP不为签约人或签约类型不为代理人 kpId:{} kpType:{} kpSignerType:{}", signerKp.getId(), signerKp.getKpType(), signerKp.getSignerType());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前KP不为签约人或签约类型不为代理人");
        }
        if (signerKp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            log.warn("签约人KP必须设置为短信授权 kpId:{} legalAuthType:{}", signerKp, signerKp.getLegalAuthType());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP必须设置为短信授权");
        }
        if (signerKp.getState() != KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()) {
            log.warn("签约人KP必须处于授权中 kpId:{} state:{}", signerKp.getId(), signerKp.getState());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP必须处于授权中");
        }
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdateNoEx(signerKp);
        signerKp.setEffective(KpConstants.UN_EFFECTIVE);
        signerKp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
        wmCustomerKpDBMapper.updateByPrimaryKey(signerKp);
        // 记录状态变更操作日志
        wmCustomerKpLogService.addKpStatusChangeLog(signerKp, uid, uname, KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING, KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL);
        // 失效KP-Audit记录
        wmCustomerKpAuditMapper.invalidAndSetResult(kpAudit.getId(), "取消授权");
    }

    /**
     * 重发短信（法人授权）
     *
     * @param kpId
     * @param uid
     * @param uname
     * @throws WmCustomerException
     */
    public void reSendAuthSmsForLegalKp(Integer kpId, int uid, String uname) throws WmCustomerException, TException {
        log.info("reSendAuthSmsForLegalKp kpId = {}, uid = {}, uname = {}", kpId, uid, uname);
        WmCustomerKp signerKp = wmCustomerKpService.getCustomerKpInfo(kpId);
        if (signerKp == null) {
            log.warn("签约人KP不存在 kpId:{}", kpId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP不存在");
        }
        if (KpSignerStateMachine.EFFECT.getState() == signerKp.getState()) {
            // 签约人KP已生效过，按KP变更逻辑处理
            WmCustomerKpTemp signerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(signerKp.getId());
            wmCustomerSensitiveWordsService.readKpWhenSelect(signerKpTemp);
            if (signerKpTemp == null) {
                log.warn("KP Temp记录不能为空 kpId:{}", signerKp.getId());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP信息不存在");
            }
            if (signerKpTemp.getKpType() != KpTypeEnum.SIGNER.getType() && signerKpTemp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
                log.warn("变更KP不为签约人或签约类型不为代理人 kpId:{} kpType:{} kpSignerType:{}", signerKp.getId(), signerKpTemp.getKpType(), signerKpTemp.getSignerType());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更KP不为签约人或签约类型不为代理人");
            }
            if (signerKpTemp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
                log.warn("变更签约人KP必须设置为短信授权 kpId:{} legalAuthType:{}", signerKpTemp.getId(), signerKpTemp.getLegalAuthType());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP必须设置为短信授权");
            }
            if (signerKpTemp.getState() != KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()) {
                log.warn("变更签约人KP必须处于授权中 kpId:{} state:{}", signerKpTemp.getId(), signerKpTemp.getState());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更签约人KP必须处于授权中");
            }
        } else {
            // 签约人KP首次新建，按KP新建逻辑处理
            if (signerKp.getKpType() != KpTypeEnum.SIGNER.getType() && signerKp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
                log.warn("当前KP不为签约人或签约类型不为代理人 kpId:{} kpType:{} kpSignerType:{}", kpId, signerKp.getKpType(), signerKp.getSignerType());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前KP不为签约人或签约类型不为代理人");
            }
            if (signerKp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
                log.warn("签约人KP必须设置为短信授权 kpId:{} legalAuthType:{}", kpId, signerKp.getLegalAuthType());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP必须设置为短信授权");
            }
            if (signerKp.getState() != KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()) {
                log.warn("签约人KP必须处于授权中 kpId:{} state:{}", signerKp.getId(), signerKp.getState());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约人KP必须处于授权中");
            }
        }
        WmCustomerDB customer = wmCustomerService.selectCustomerById(signerKp.getCustomerId());
        if (customer == null) {
            log.warn("无法查询到客户信息 kpId:{} customerId:{}", kpId, signerKp.getCustomerId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "无法查询到客户信息");
        }
        if (customer.isUnEffectived()) {
            log.warn("客户未生效 kpId:{} customerId:{}", kpId, signerKp.getCustomerId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户未生效");
        }
        // 判断是否存在生效的法人KP
        Boolean existEffectiveLegalKp = wmCustomerKpService.checkHaveEffectiveLegalKp(signerKp.getCustomerId());
        if (!existEffectiveLegalKp) {
            log.warn("客户不存在生效的法人KP customerId:{}", signerKp.getCustomerId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户不存在生效的法人KP");
        }
        WmCustomerKpAudit kpAudit = wmCustomerKpAuditMapper.selectByKpIdAndType(kpId, (int) KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        if (kpAudit == null) {
            log.warn("KP Audit记录不能为空 kpId:{} kpAuditType:{}", kpId, KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "KP Audit记录不能为空");
        }
        try {
            wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), signerKp.getId());
            log.info("[法人授权]wmEcontractSignBzService.resendMsg commitId={}", kpAudit.getCommitId());
            RetrySmsResponse result = wmEcontractSignBzService.easyResendMsg(Long.valueOf(kpAudit.getCommitId()));
            log.info("[法人授权]wmEcontractSignBzService.resendMsg result={}", result.isOk());
            if (!result.isOk()) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更授权重发短信失败，请重试");
            }
        } catch (WmCustomerException e) {
            log.warn("[法人授权]变更授权重发短信异常, kpId={}, commitId={}", kpId, kpAudit.getCommitId(), e);
            throw e;
        } catch (Exception e) {
            log.warn("[法人授权]变更授权重发短信异常, kpId={}, commitId={}", kpId, kpAudit.getCommitId(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "变更授权重发短信失败，请重试");
        }

    }

    /**
     * 客户生效-法人数据操作
     *
     * @param customer
     * @param legalKp
     */
    public void customerEffectForLegalKpOperate(WmCustomerDB customer, WmCustomerKp legalKp, String beforLegalPerson, int sourceType) {
        int kpCertType = legalKp.getCertType();
        if (kpCertType != CertTypeEnum.ID_CARD.getType()) {
            log.warn("[customerEffectForLegalKpOperate]当前KP类型是法人的数据证件类型非身份证，已删除。,kpId:{}", legalKp.getId());
            wmCustomerKpDBMapper.deleteByPrimaryKey(legalKp.getId());
            wmCustomerKpLogService.insertOplog(legalKp, "当前KP类型是法人的数据证件类型非身份证，已删除。", WmCustomerOplogBo.OpType.UPDATE, 0, "客户系统");
            return;
        }
        Integer customerType = customer.getCustomerType();
        if (customerType == null) {
            log.error("[customerEffectForLegalKpOperate] 客户资质类型不能为空,customer:{} legalKp:{}", JSONObject.toJSONString(customer), JSONObject.toJSONString(legalKp));
            return;
        }
        if (customerType == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            // 客户资质类型：个人证件
            log.warn("[customerEffectForLegalKpOperate]当前KP类型是法人的数据不符合规则，已删除。customerId:{} kpId:{}", customer.getId(), legalKp.getId());
            wmCustomerKpDBMapper.deleteByPrimaryKey(legalKp.getId());
            wmCustomerKpLogService.insertOplog(legalKp, "当前KP类型是法人的数据不符合规则，已删除。", WmCustomerOplogBo.OpType.UPDATE, 0, "客户系统");
            sendDeleteLegalKpMsgForCusEffect(customer, legalKp);
            return;
        }
        // KP姓名
        String kpName = legalKp.getCompellation();
        // 客户资质法人姓名
        String cusLegalPerson = customer.getLegalPerson();
        if (StringUtils.isBlank(kpName) || StringUtils.isBlank(cusLegalPerson) || !kpName.equals(cusLegalPerson)) {
            log.warn("[customerEffectForLegalKpOperate]当前KP类型是法人的数据不符合规则，已删除。,kpId:{}", legalKp.getId());
            wmCustomerKpDBMapper.deleteByPrimaryKey(legalKp.getId());
            wmCustomerKpLogService.insertOplog(legalKp, "当前KP类型是法人的数据不符合规则，已删除。", WmCustomerOplogBo.OpType.UPDATE, 0, "客户系统");
            if (sourceType == WmCustomerConstant.CUSTOMER_SYSTEM_UPDATE) {
                sendDeleteLegalKpMsgForCusEffect(customer, legalKp);
            } else if (sourceType == WmCustomerConstant.CUSTOMER_PLATFORM_UPDATE) {
                sendDeleteLegalKpMsgForCusQuaModify(customer, legalKp, beforLegalPerson, cusLegalPerson);
            } else if (sourceType == WmCustomerConstant.POI_QUA_LEGAL_PERSON_UPDATE) {
                sendDeleteLegalKpMsgForCusEffect(customer, legalKp);
            }
            return;
        }

    }

    /**
     * 删除法人KP-大象通知(客户生效)
     *
     * @param customer
     * @param legalKp
     */
    private void sendDeleteLegalKpMsgForCusEffect(WmCustomerDB customer, WmCustomerKp legalKp) {
        Integer ownerUid = customer.getOwnerUid();
        if (ownerUid == null || ownerUid.intValue() <= 0) {
            log.warn("sendDeleteLegalKpMsgForCusQuaModify,客户责任人为空 ownerUid={}, customerKp={}", ownerUid, JSONObject.toJSONString(legalKp));
            return;
        }
        String content = String.format("您负责的客户（%s+%s），法人信息不符合要求，需要重新录入，请及时处理。", customer.getMtCustomerId(), customer.getCustomerName());
        // 发送大象通知
        sendMsg(ownerUid, content);
    }

    /**
     * 删除法人KP-大象通知(客户资质变更)
     *
     * @param customer
     * @param legalKp
     */
    private void sendDeleteLegalKpMsgForCusQuaModify(WmCustomerDB customer, WmCustomerKp legalKp, String oldLegalRepresentative, String newLegalRepresentative) {
        Integer ownerUid = customer.getOwnerUid();
        if (ownerUid == null || ownerUid.intValue() <= 0) {
            log.warn("sendDeleteLegalKpMsgForCusQuaModify,客户责任人为空 ownerUid={}, customerKp={}", ownerUid, JSONObject.toJSONString(legalKp));
            return;
        }
        String content = String.format("您负责的客户（%s %s），法人信息不符合要求，需要重新录入，请及时处理。",
                customer.getCustomerName(), customer.getMtCustomerId(), oldLegalRepresentative, newLegalRepresentative, oldLegalRepresentative);
        wmCustomerKpLogService.insertOplog(legalKp, content, WmCustomerOplogBo.OpType.UPDATE, 0, "客户系统");
        // 发送大象通知
        sendMsg(ownerUid, content);
    }


    /**
     * 发送消息
     *
     * @param uid
     * @param msg
     */
    private void sendMsg(Integer uid, String msg) {
        try {
            if (uid == null) {
                return;
            }
            WmEmploy originalOwner = wmEmployClient.getEmployById(uid);
            if (originalOwner != null && org.apache.commons.lang3.StringUtils.isNotEmpty(msg)) {
                DaxiangUtilV2.push(msg, String.format("%<EMAIL>", originalOwner.getMisId()));
                log.info("给客户责任人发送客户资质变更消息 uid={},msg={}", uid, msg);
            }
        } catch (Exception e) {
            log.error("pushMsg::uid = {}, msg = {}", uid, msg, e);
        }
    }
}
