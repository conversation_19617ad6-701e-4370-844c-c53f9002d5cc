package com.sankuai.meituan.waimai.customer.service.sc.flow.constant;

/**
 * 学校交付管理状态机事件
 * <AUTHOR>
 * @date 2024/05/25
 * @email <EMAIL>
 */
public enum WmSchoolDeliveryStatusMachineEvent {
    /**
     * 提交审核
     */
    SUBMIT(1,"提交审批"),
    /**
     * BD任务系统审核通过
     */
    CRM_TICKET_PASS(2, "BD任务系统审批通过"),
    /**
     * BD任务系统审核驳回
     */
    CRM_TICKET_REJECT(3, "BD任务系统审核驳回"),
    /**
     * BD任务系统审核终止
     */
    CRM_TICKET_STOP(4, "BD任务系统审核终止"),
    /**
     * 撤回审批回退至待提审
     */
    BACK_TO_PENDING(5, "撤回审批回退至待提审"),
    /**
     * 撤回审批回退至审批通过
     */
    BACK_TO_PASS(6, "撤回审批回退至审批通过"),
    /**
     * 撤回审批回退至审批驳回
     */
    BACK_TO_REJECT(7, "撤回审批回退至审批驳回"),
    /**
     * 上游数据变更
     */
    UPSTREAM_DATE_CHANGE(8, "上游数据变更");

    private final Integer code;
    private final String desc;

    WmSchoolDeliveryStatusMachineEvent(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
