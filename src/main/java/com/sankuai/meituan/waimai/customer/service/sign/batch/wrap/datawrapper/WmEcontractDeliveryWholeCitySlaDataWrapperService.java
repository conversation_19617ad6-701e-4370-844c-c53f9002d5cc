package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryWholeCityInfoBo;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.aspect.SlaDataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractWmPoiSpAreaBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.util.StringUtils;

@Service
@SlaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.DELIVERY_WHOLE_CITY)
public class WmEcontractDeliveryWholeCitySlaDataWrapperService implements IWmEcontractSlaDataWrapperService{
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractDeliveryWholeCitySlaDataWrapperService.class);

    public static final String SUPPORT_MARK = "support";

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo)
            throws WmCustomerException {

        List<EcontractContentBo> result = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil
                .selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDeliveryInfoBo deliveryInfoBo = null;
        try {
            deliveryInfoBo = JSON
                    .parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        } catch (Exception e) {
            LOGGER.warn("数据解析异常", e);
            return result;
        }

        if(deliveryInfoBo == null || deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo() == null){
            return result;
        }

        EcontractDeliveryWholeCityInfoBo econtractDeliveryWholeCityInfoBo = deliveryInfoBo
                .getEcontractDeliveryWholeCityInfoBo();

        if(!SUPPORT_MARK.equals(econtractDeliveryWholeCityInfoBo.getSupportSLA()) || StringUtils
                .isEmpty(econtractDeliveryWholeCityInfoBo.getDeliveryArea())){
            return result;
        }
        EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBo = JSONObject
                .parseObject(econtractDeliveryWholeCityInfoBo.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
//        EcontractContentBo econtractContentBo = WmPoiSpAreaBoUtil
//                .transEcontractWmPoiSpAreaBo2EcontractContentBo(econtractWmPoiSpAreaBo,
//                        WmEcontractConstant.DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT);
        econtractWmPoiSpAreaBo.setSlaText(WmEcontractConstant.DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT);
        EcontractContentBo econtractContentBo = WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(econtractWmPoiSpAreaBo);
        return Lists.newArrayList(econtractContentBo);
    }
}
