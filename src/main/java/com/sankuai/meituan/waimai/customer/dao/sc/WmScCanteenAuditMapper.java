package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmScCanteenAuditMapper {

    int insertSelective(WmScCanteenAuditDO record);

    WmScCanteenAuditDO selectByPrimaryKey(Integer id);

    List<WmScCanteenAuditDO> selectBySchoolIdAndAuditStatus(@Param("schoolId") Integer schoolId,
                                                            @Param("auditStatus") Integer auditStatus);


    int updateByPrimaryKeySelective(WmScCanteenAuditDO record);


    /**
     * 通过食堂id 查询
     * @param canteenId 食堂id
     */
    WmScCanteenAuditDO selectByCanteenId(Integer canteenId);

    /**
     * 通过食堂主键ID查询最近一次审核记录
     * @param canteenPrimaryId 食堂主键ID
     * @return WmScCanteenAuditDO
     */
    WmScCanteenAuditDO selectLatestByCanteenPrimaryId(@Param("canteenPrimaryId") Integer canteenPrimaryId);


    WmScCanteenAuditDO selectByTaskId(Integer taskId);

    /**
     * 失效食堂的所有的审批记录
     * @param canteenId 食堂主键ID
     */
    void invalidByCanteenId(Integer canteenId);

}