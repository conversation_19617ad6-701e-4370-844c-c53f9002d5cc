package com.sankuai.meituan.waimai.customer.service.sign.toSign;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigTaskParseService;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @description: 待签约页顶部提示合同类型-C1
 * @author: lixuepeng
 * @create: 2021-12-29
 **/
@Service
public class WmEcontractToSignTipsC2Handler implements WmEcontractToSignTipsHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractToSignTipsC2Handler.class);

    @Autowired
    private WmPoiClient              wmPoiClient;
    @Autowired
    WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;
    @Autowired
    WmTempletContractDBMapper        wmTempletContractDBMapper;
    @Autowired
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;

    @Override
    public String getName() {
        return "《合作商与客户合同》";
    }

    @Override
    public Boolean isNeedSign(int customerId, long wmPoiId) {
        try {
            WmPoiDomain poiDomain = wmPoiClient.getWmPoiById(wmPoiId);
            if (poiDomain == null) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询门店信息为空");
            }
            // 非代理商门店
            if (poiDomain.getAgentId() <= 0) {
                return false;
            }
            // 是代理商但是大连锁
            boolean lianSuoPoi = wmPoiClient.isLianSuoPoi(poiDomain);
            if (lianSuoPoi) {
                return false;
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("判断是否需要签约C2合同异常 customerId:{}, wmPoiId:{}", customerId, wmPoiId, e);
        }
        return false;
    }

    @Override
    public Boolean hasEffectiveRecord(int customerId, long wmPoiId) {
        // 先查询线上表
        List<WmTempletContractDB> wmTempletContractAuditedDBList = wmTempletContractAuditedDBMapper
                .getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(),
                        WmTempletContractTypeEnum.C2_PAPER.getCode()));
        // 不为空则代表生效过
        if (CollectionUtils.isNotEmpty(wmTempletContractAuditedDBList)) {
            return true;
        }
        // 再查询线下表
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectValidByParentIdAndTypes(
                Long.valueOf(customerId), Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(),
                        WmTempletContractTypeEnum.C2_PAPER.getCode()));
        // 线下表存在签约中数据
        if (CollectionUtils.isNotEmpty(wmTempletContractDBList)) {
            for (WmTempletContractDB wmTempletContractDB : wmTempletContractDBList) {
                if (wmTempletContractDB.getStatus() == CustomerContractStatus.EFFECT.getCode()
                        || wmTempletContractDB.getStatus() == CustomerContractStatus.TO_EFFECT.getCode()) {
                    return true;
                }
            }
        }

        // 查询签约表
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndType(customerId, EcontractTaskApplyTypeEnum.C2CONTRACT.getName());
        if (CollectionUtils.isNotEmpty(taskDBList)) {
            for (WmEcontractSignTaskDB taskDB : taskDBList) {
                // 签约成功or签约中
                if (EcontractTaskStateEnum.SUCCESS.getName().equals(taskDB.getApplyState())
                        || EcontractTaskStateEnum.IN_PROCESSING.getName().equals(taskDB.getApplyState())) {
                    return true;
                }
            }
        }

        return false;
    }
}
