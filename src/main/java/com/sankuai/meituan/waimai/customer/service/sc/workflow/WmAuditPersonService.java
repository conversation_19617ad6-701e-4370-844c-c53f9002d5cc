package com.sankuai.meituan.waimai.customer.service.sc.workflow;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.StringUtils;
import com.sankuai.meituan.waimai.bizsso.thrift.Bool;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.domain.sc.WmAuditPersonBo;
import com.sankuai.meituan.waimai.customer.domain.sc.WmAuditPersonCondition;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScEmployeeService;
import com.sankuai.meituan.waimai.customer.service.sc.WmSchoolServerService;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.*;
import com.sankuai.meituan.waimai.infra.domain.builder.WmOrgSearchParamBuilder;
import com.sankuai.meituan.waimai.infra.service.WmVirtualOrgService;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WmAuditPersonService {

    @Autowired
    private WmScEmployeeService wmScEmployeeService;


    @Autowired
    private WmEmployClient wmEmployClient;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmOrgServiceAdaptor wmOrgServiceAdaptor;

    @Autowired
    private WmEmployeeService wmEmployeeService;

    @Autowired
    private WmVirtualOrgService.Iface wmVirtualOrgService;

    @Autowired
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Autowired
    private WmOrgClient wmOrgClient;

    @Autowired
    private WmSchoolServerService wmSchoolServerService;

    public WmAuditPersonBo getAuditPerson(WmAuditPersonCondition condition) throws WmSchCantException {
        switch (condition.getAuditNodeEnum()) {
            case CM_AUDIT:
                return getCM(condition.getUserId());
            case SCHOOL_OWNER_AUDIT:
                return getSchoolOwner(condition.getWmSchoolDB());
            case CANTEEN_MANAGER_CONDFIRM:
                return getCanteenManager(condition.getWmCanteenDB());
            case SAVE_SUCCESS:
                WmAuditPersonBo wmAuditPersonBo = new WmAuditPersonBo();
                wmAuditPersonBo.setUserId(condition.getUserId());
                return wmAuditPersonBo;
            case FIRST_LEVEL_APPROVAL:
                //return getFirstLevelApproval(condition.getUserId());
            case SECOND_LEVEL_APPROVAL:
                //return getFirstLevelApproval(condition.getUserId());

            default:
                return null;
        }
    }

    private WmAuditPersonBo getWmAorAudit(int userId) throws WmSchCantException {
        WmUserVirtualOrgRel wmAor = wmScEmployeeService.getWmAorAudit(userId);
        if (wmAor == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "上级外卖蜂窝负责人不存在，不可保存。");
        }
        WmAuditPersonBo personBo = new WmAuditPersonBo();
        personBo.setUserId(wmAor.getUid());
        personBo.setUserName(wmAor.getUserName());
        return personBo;
    }

    private WmAuditPersonBo getWmContactPointAudit(int userId) throws WmSchCantException {
        WmUserVirtualOrgRel wmContactPoint = wmScEmployeeService.getWmContactPointAudit(userId);
        if (wmContactPoint == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "上级外卖联络点负责人不存在，不可保存。");
        }
        WmAuditPersonBo personBo = new WmAuditPersonBo();
        personBo.setUserId(wmContactPoint.getUid());
        personBo.setUserName(wmContactPoint.getUserName());
        return personBo;
    }

    /**
     * 获取解绑/换绑场景下的审批人列表，包含兜底审批人机制
     *
     * @param userMisId 用户MIS ID
     * @return 审批人列表
     * @throws WmSchCantException 业务异常
     */
    public List<WmEmploy> getFallbackApproverForRebinding(int userMisId) throws WmSchCantException {
        log.info("WmAuditPersonService#getFallbackApproverForRebinding->userMisId={}",userMisId);
        List<WmEmploy> wmEmployList = new ArrayList<>();
        int retryCount = 0;
        int maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                wmEmployList = findCityManagerEmploys(userMisId);
                break;
            } catch (Exception e) {
                // 获取审核人出现错误就重试，没有就停止重试
                log.info("WmAuditPersonService#getFallbackApproverForRebinding->开始重试，e:", e);
            }
            retryCount++;
        }
        log.info("WmAuditPersonService#getFallbackApproverForRebinding->getWmEmployByMisId  wmEmployList={}",wmEmployList);
        if (wmEmployList == null) { // 在这里检查wmEmployList是否为null
            wmEmployList = new ArrayList<>(); // 如果是null，则初始化为新的ArrayList
        }
        if (CollectionUtils.isEmpty(wmEmployList) || wmEmployList.size() < 2) {
            // 获取兜底审批人
            log.info("WmAuditPersonService#getFallbackApproverForRebinding开始获取兜底审批人");
            WmEmploy operator = wmEmployeeService.getWmEmployByMisId(MccConfig.getFallbackApproverForRebinding());
            if (operator != null) {
                wmEmployList.add(operator);
            }
        }
        log.info("getFallbackApproverForRebinding userMisId={}, wmEmployList:{}", userMisId,JSONObject.toJSONString(wmEmployList));
        return wmEmployList;
    }

    public List<WmEmploy> findCityManagerEmploys(Integer currentUserId) throws Exception {
        if(currentUserId == null){
            return null;
        }
        log.info("findCityManagerEmploys currentUserId={}",currentUserId);
        // 获取城市负责人下的节点
        WmOrgListResult orgListResult = wmOrgServiceAdaptor.serach(new WmOrgSearchParamBuilder()
                .needOrgResults(true)
                .source(WmVirtualOrgSourceEnum.WAIMAI.getSource())
                .currentUid(currentUserId)
                .needPositionUser(WmOrgConstant.Position.WM_ORG_CITY_MANAGER)
                .recursive(WmVirtualOrgRecursiveTypeEnum.TOP_DOWN.getType())
                .build());

        // 将所有节点放到列表中
        List<Integer> organizationIds = Optional.ofNullable(orgListResult)
                .filter(result -> result.getTotal() > 0)
                .map(WmOrgListResult::getOrgResults)
                .orElseGet(Collections::emptyList)
                .stream()
                .map(orgResult -> orgResult.getVirtualOrg().getId())
                .collect(Collectors.toList());

        log.info("findCityManagerEmploys organizationIds={}",organizationIds);

        List<WmEmploy> employList = new ArrayList<>();
        // 获取申请人所在节点（筛选出最后一个节点）
        List<WmVirtualOrg> userOrgs = wmVirtualOrgServiceAdaptor.getOrgsByUidWithinOrgIds(currentUserId, WmVirtualOrgSourceEnum.WAIMAI.getSource(), organizationIds, WmVirtualOrgRecursiveTypeEnum.NONE.getType());

        log.info("userOrgs :", JSONObject.toJSONString(userOrgs));
        if (userOrgs == null || userOrgs.isEmpty()) {
            throw new Exception("findCityManagerEmploys No organizations found for the given UID");
        }

        // 初始设为父节点进行遍历
        Integer currentOrgId = userOrgs.get(0).getParentId();


        Boolean isNomarl = false;
        // 对当前状态做个判断，需要不能直接是城市负责人  找出不是城市负责人的节点来
        for(WmVirtualOrg wmVirtualOrg : userOrgs){
            // 当前节点可能有多个岗位，如果有个岗位是城市负责人就不能用
            List<WmVirtualOrgPositionInstance> positionInstances = wmVirtualOrgServiceAdaptor.getOrgPositionsByOrgId(wmVirtualOrg.getId());
            log.info("findCityManagerEmploys positionInstances ={}", JSONObject.toJSONString(positionInstances));
            if (positionInstances == null || positionInstances.isEmpty() || positionInstances.get(0) == null) {
                continue;
            }
            // 遍历当前所有岗位，判断是否有城市负责人
            Boolean isCityManager = positionInstances.stream()
                    .anyMatch(positionInstance -> positionInstance.getPositionId() == WmOrgConstant.Position.WM_ORG_CITY_MANAGER);
            // 如果当前节点存在城市负责人岗位，则跳过当前节点
            if(isCityManager){
                continue;
            }
            // 到这说明 当前节点没有城市负责人的岗位 只有在不为城市责任人时才会向上遍历
            currentOrgId = wmVirtualOrg.getParentId();
            isNomarl = true;
            log.info("当前节点没有城市负责人的岗位,currentOrgId={}",currentOrgId);
            break;
        }

        // 如果获取的所有节点都是城市负责人，那么上级肯定就会超过城市负责人，所以就直接返回
        if(!isNomarl){
            log.info("findCityManagerEmploys 当前节点已经是城市负责人，无法再向上获取");
            return null;
        }

        while (employList.size() < 2) {
            // 根据当前节点获取所有岗位
            List<WmVirtualOrgPositionInstance> positionInstances = wmVirtualOrgServiceAdaptor.getOrgPositionsByOrgId(currentOrgId);
            //  判断当前节点是否为空，如果为空则跳出循环。
            if (positionInstances == null || positionInstances.isEmpty() || positionInstances.get(0) == null) {
                break;
            }
            // 判断当前节点是否为城市负责人，如果是则跳出循环。
            Boolean isCityManager = positionInstances.stream()
                    .anyMatch(positionInstance -> positionInstance.getPositionId() == WmOrgConstant.Position.WM_ORG_CITY_MANAGER);
            WmVirtualOrgPositionInstance firstPositionInstance = positionInstances.get(0);
            // 说明已经到城市负责人了
            if (isCityManager) {
                log.info("findCityManagerEmploys WM_ORG_CITY_MANAGER处理，已经到城市负责人了,currentOrgId={}",currentOrgId);
                List<Integer> uidList = wmVirtualOrgService.getUidsByOrgIds(Collections.singletonList(firstPositionInstance.getOrgId()),WmVirtualOrgSourceEnum.WAIMAI.getSource(), (byte) WmVirtualOrgRecursiveTypeEnum.NONE.getType());
                WmEmploy wmEmploys = null;

                // 当前负责人存在多个的逻辑
                if(uidList == null || uidList.isEmpty()){
                    //  当前负责人不存在多个的情况，获取当前岗位的负责人
                    log.info("findCityManagerEmploys  getMajorOwnerByOrgId");
                    wmEmploys = wmVirtualOrgServiceAdaptor.getMajorOwnerByOrgId(firstPositionInstance.getOrgId());
                }else{
                    // 存在当前负责人
                    log.info("findCityManagerEmploys getWmEmployById");
                    //同时存在两人取ID小的一位
                    int minUid = uidList.stream().min(Integer::compareTo).get();
                    wmEmploys = wmEmployeeService.getWmEmployById(minUid);
                }
                //WmEmploy majorOwner = wmVirtualOrgServiceAdaptor.getMajorOwnerByOrgId(firstPositionInstance.getOrgId());
                if (wmEmploys != null && !currentUserId.equals(wmEmploys.getUid())) {
                    final WmEmploy finalWmEmploys = wmEmploys;
                    if (!employList.stream().anyMatch(employ -> employ.getUid() == (finalWmEmploys.getUid()))) {
                        employList.add(wmEmploys);
                    }
                }
                break;
            } else {
                log.info("findCityManagerEmploys 非WM_ORG_CITY_MANAGER处理  没有到城市负责人节点");
                currentOrgId = firstPositionInstance.getVirtualOrg().getParentId();
                List<Integer> uidList = wmVirtualOrgService.getUidsByOrgIds(Collections.singletonList(firstPositionInstance.getOrgId()),WmVirtualOrgSourceEnum.WAIMAI.getSource(), (byte) WmVirtualOrgRecursiveTypeEnum.NONE.getType());
                WmEmploy wmEmploys = null;
                if(uidList == null || uidList.isEmpty()){
                    log.info("findCityManagerEmploys getMajorOwnerByOrgId");
                     wmEmploys = wmVirtualOrgServiceAdaptor.getMajorOwnerByOrgId(firstPositionInstance.getOrgId());
                }else{
                    log.info("findCityManagerEmploys getWmEmployById");
                    //同时存在两人取ID小的一位
                    int minUid = uidList.stream().min(Integer::compareTo).get();
                     wmEmploys = wmEmployeeService.getWmEmployById(minUid);
                }

                //WmEmploy majorOwner = wmVirtualOrgServiceAdaptor.getMajorOwnerByOrgId(firstPositionInstance.getOrgId());
                if (wmEmploys != null && !currentUserId.equals(wmEmploys.getUid())) {
                    final WmEmploy finalWmEmploys = wmEmploys; // 创建一个临时的final变量
                    if (!employList.stream().anyMatch(employ -> employ.getUid() == (finalWmEmploys.getUid()))) {
                        employList.add(wmEmploys);
                    }
                }
            }
        }

        // 使用LinkedHashMap来去重，同时保持原有顺序
        Map<Integer, WmEmploy> uniqueMap = new LinkedHashMap<>();
        // 根据uid去重
        for (WmEmploy employ : employList) {
            uniqueMap.put(employ.getUid(), employ);
        }
        // 构建去重后的列表，保持原有顺序
        List<WmEmploy> uniqueList = new ArrayList<>(uniqueMap.values());

        return uniqueList;
    }
    private WmAuditPersonBo getCM(int userId) throws WmSchCantException {
        WmUserVirtualOrgRel wmCM = wmScEmployeeService.getCmUid(userId);
        if (wmCM == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "上级外卖城市负责人不存在，不可保存。");
        }
        WmAuditPersonBo personBo = new WmAuditPersonBo();
        personBo.setUserId(wmCM.getUid());
        personBo.setUserName(wmCM.getUserName());
        return personBo;
    }

    private WmAuditPersonBo getSchoolOwner(WmSchoolDB wmSchoolDB) throws WmSchCantException {
        if (wmSchoolDB == null || StringUtils.isBlank(wmSchoolDB.getResponsiblePerson())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校责任人不存在，不可保存");
        }
        String schoolResponsiblePerson = wmSchoolDB.getResponsiblePerson();
        WmEmploy wmEmploy = wmEmployClient.getByMisId(schoolResponsiblePerson);
        if (wmEmploy == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校责任人不存在，不可保存");
        }
        WmAuditPersonBo personBo = new WmAuditPersonBo();
        personBo.setUserId(wmEmploy.getUid());
        personBo.setUserName(wmEmploy.getName());
        personBo.setUserMisId(wmEmploy.getMisId());
        return personBo;
    }

    private WmAuditPersonBo getCanteenManager(WmCanteenDB wmCanteenDB) throws WmSchCantException {
        if (wmCanteenDB == null || StringUtils.isBlank(wmCanteenDB.getManagerPhone()) || StringUtils.isBlank(wmCanteenDB.getManager())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂经理不存在，不可保存");
        }
        WmAuditPersonBo personBo = new WmAuditPersonBo();
        personBo.setUserName(wmCanteenDB.getManager());
        personBo.setPhone(wmCanteenDB.getManagerPhone());
        return personBo;
    }


    /**
     * 获取校园食堂深处场景下的审批人列表
     *
     * @param wmSchoolDB 学校信息
     * @return 审批人列表
     * @throws WmSchCantException 业务异常
     */
    public List<WmEmploy> getDeleteCanteenAuditPersionList(WmSchoolDB wmSchoolDB) throws WmSchCantException {
        log.info("WmAuditPersonService#getDeleteCanteenAuditPersionList->wmSchoolDB={}",wmSchoolDB);
        List<WmEmploy> wmEmployList = new ArrayList<>();
        int retryCount = 0;
        int maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                wmEmployList = findDeleteCanteenAuditEmploys(wmSchoolDB);
                break;
            } catch (Exception e) {
                // 获取审核人出现错误就重试，没有就停止重试
                log.info("WmAuditPersonService#getFallbackApproverForRebinding->开始重试，e:", e);
            }
            retryCount++;
        }
        log.info("WmAuditPersonService#getDeleteCanteenAuditPersionList->getWmEmployByMisId  wmEmployList={}",wmEmployList);
        return wmEmployList;
    }

    public List<WmEmploy> findDeleteCanteenAuditEmploys(WmSchoolDB wmSchoolDB) throws Exception {
        if(wmSchoolDB == null){
            return null;
        }
        if (wmSchoolDB.getAorId() <= 0) {
            return null;
        }
        List<WmEmploy> uniqueList = new ArrayList<>();
        log.info("findCityManagerEmploys currentUserId={}", wmSchoolDB);
        // 这块可参考上面 findCityManagerEmploys 方法业务处理
        //1.获取学校责任人上级
        SchoolBo schoolBo = wmSchoolServerService.selectSchoolById(wmSchoolDB.getId());
        WmUserVirtualOrgRel virtualOrgRel = wmVirtualOrgServiceAdaptor.getUpUserByUidAndPositionId(
                schoolBo.getResponsibleUid(),
                MccScConfig.getSchoolKaAreaManagerPositionId(),
                WmVirtualOrgSourceEnum.WAIMAI.getSource());
        if(virtualOrgRel != null && virtualOrgRel.getUid() > 0){
            WmEmploy contactPointEmploy = wmEmployeeService.getWmEmployById(virtualOrgRel.getUid());
            if(contactPointEmploy != null){
                uniqueList.add(contactPointEmploy);
            }
        }
        //2.1根据学校信息蜂窝ID获取蜂窝负责人uid
        WmUniAor wmUniAor = wmAorServiceAdapter.getAorInfoById(wmSchoolDB.getAorId());
        if(wmUniAor == null || CollectionUtils.isEmpty(wmUniAor.getOwnerInfos()) || wmUniAor.getOwnerInfos().get(0) == null){
            return null;
        }
        WmUniAorOwnerInfo wmUniAorOwnerInfo = wmUniAor.getOwnerInfos().get(0);

        if (wmUniAorOwnerInfo == null || wmUniAorOwnerInfo.getUid() <= 0) {
            return null;
        }

        WmEmploy aorEmploy = wmEmployeeService.getWmEmployById(wmUniAorOwnerInfo.getUid());

        //2.2 根据蜂窝ID获取联络点负责人uid
        int belongOrgId = wmUniAor.getBelongOrgId();
        WmVirtualOrg orgById = wmOrgClient.getOrgById(belongOrgId);
        if(orgById == null){
            return null;
        }
        //联络点
        List<WmUserVirtualOrgRel> userVirtualOrgRelsByOrgId = wmVirtualOrgService.getUserVirtualOrgRelsByOrgId(orgById.getId());
        if(CollectionUtils.isEmpty(userVirtualOrgRelsByOrgId)){
            return null;
        }
        WmUserVirtualOrgRel wmUserVirtualOrgRel = userVirtualOrgRelsByOrgId.get(0);
        if(wmUserVirtualOrgRel == null){
            return null;
        }
        WmEmploy contactPointEmploy = wmEmployeeService.getWmEmployById(wmUserVirtualOrgRel.getUid());
        //2.3 添加联络点负责人到唯一列表中
        uniqueList.add(contactPointEmploy);

        return uniqueList;
    }
}
