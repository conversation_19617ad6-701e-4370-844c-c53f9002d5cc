package com.sankuai.meituan.waimai.customer.service.sign.cancel;

import static com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant.ECONTRACT_SERVER_CANCEL_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant.MULTI_MODULE_CANCEL_ACTION_SOURCE;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.meituan.waimai.customer.constant.SignPackStatusConstant;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.handler.WmEcontractCallbackFailHandler;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import javax.annotation.Resource;

import joptsimple.internal.Strings;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WmEcontractCancelService {

    public static final String FINISH = "econtract_finish";

    private static Logger LOGGER = LoggerFactory.getLogger(WmEcontractCancelService.class);

    @Resource
    private WmEcontractCancelEcontractTaskService wmEcontractCancelEcontractTaskService;

    @Resource
    private WmEcontractCancelHoldingTaskService wmEcontractCancelHoldingTaskService;

    @Resource
    private WmEcontractBatchBizService wmEcontractBatchBizService;

    @Resource
    private WmEcontractCallbackFailHandler wmEcontractCallbackFailHandler;

    @Resource
    private WmEcontractManualBatchBizService wmEcontractManualBatchBizService;

    @Resource
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Autowired
    private WmEcontractCancelDRTaskService wmEcontractCancelDRTaskService;

    public Boolean cancel(EcontractTaskBo taskBo, String failMsg, String actionSource) throws WmCustomerException, TException {
        return cancel(taskBo, failMsg, actionSource, Boolean.TRUE);
    }

    public Boolean cancel(EcontractTaskBo taskBo, String failMsg, String actionSource, Boolean callBack) throws WmCustomerException, TException {
        //非解绑门店及客户删除场景+非任务列表批量操作取消+手动打包任务+多模块打包+非配送特殊取消==>不允许业务取消
        //打包任务，一旦进入签约中，不允许在合同列表取消
        if (!WmEcontractBatchConstant.NOT_LIMIT_CANCEL_ACTION_SOURCE.contains(actionSource)
                && taskBo.getManualBatchId() != null
                && taskBo.getManualBatchId() != 0L) {
            WmEcontractSignManualBatchDB manualBatch = wmEcontractManualBatchBizService
                    .getManualBatchFromMaster(taskBo.getManualBatchId());
            if (manualBatch.getPackId() != null && manualBatch.getPackId() > 0) {
                WmEcontractSignPackDB signPackDB = wmEcontractSignPackService.querySignPackById(manualBatch.getPackId());
                if (signPackDB.getStatus() == SignPackStatusConstant.UN_APPLY
                        || signPackDB.getStatus() == SignPackStatusConstant.PART_APPLY
                        || signPackDB.getStatus() == SignPackStatusConstant.ALL_APPLY) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "该客户多个信息在打包确认中，请前往先富系统-客户信息-任务列表操作取消");
                }
            }
            if (judgeMultilPack(manualBatch)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "由于该客户下多个信息在打包确认中，请前往先富系统-客户信息-任务列表操作取消");
            }
        }
        LOGGER.info("WmEcontractCancelService#cancel, taskBo={}", JSON.toJSONString(taskBo));
        if (taskBo.getBatchId() == 0L) {
            wmEcontractCancelHoldingTaskService.cancel(taskBo);
        } else {
            //如果是允许手动打包签约的取消，则按照batchId维度全部取消
            if (ConfigUtilAdapter.getBoolean("sign_cancel_optimize_open", false)) {
                if (!MULTI_MODULE_CANCEL_ACTION_SOURCE.contains(actionSource)
                        && taskBo.getManualBatchId() != null && taskBo.getManualBatchId() != 0L) {
                    wmEcontractSignBzService.cancelSignByBatchIdWithSource(taskBo.getBatchId(),
                            WmEcontractBatchConstant.CANCEL_BATCH_TASK, callBack);
                    return Boolean.TRUE;
                }
            } else {
                if (!WmEcontractBatchConstant.CANCEL_BATCH_TASK.equals(actionSource)
                        && taskBo.getManualBatchId() != null && taskBo.getManualBatchId() != 0L) {
                    wmEcontractSignBzService.cancelSignByBatchIdWithSource(taskBo.getBatchId(),
                            WmEcontractBatchConstant.CANCEL_BATCH_TASK, callBack);
                    return Boolean.TRUE;
                }
            }

            //task维度任务取消
            WmEcontractSignBatchDB batchDB = wmEcontractBatchBizService.queryByBatchId(taskBo.getBatchId());
            if (batchDB != null && batchDB.getValid() == 1 && EcontractBatchStateEnum.IN_PROCESSING.getName().equals(batchDB.getBatchState())) {
                wmEcontractCancelEcontractTaskService.cancel(taskBo);
            } else {
                wmEcontractCancelHoldingTaskService.cancel(taskBo);
            }
        }

        //回调
        wmEcontractCallbackFailHandler.callbackFail(taskBo.getId(), taskBo, FINISH, ECONTRACT_SERVER_CANCEL_ERROR, failMsg, actionSource, callBack);
        return Boolean.TRUE;
    }

    public Boolean cancelWithReason(EcontractTaskBo taskBo, String failMsg, String actionSource, String reason, Boolean callBack) throws WmCustomerException, TException {
        //非解绑门店及客户删除场景+非任务列表批量操作取消+手动打包任务+多模块打包+非配送特殊取消==>不允许业务取消
        //打包任务，一旦进入签约中，不允许在合同列表取消
        if (!WmEcontractBatchConstant.NOT_LIMIT_CANCEL_ACTION_SOURCE.contains(actionSource)
                && taskBo.getManualBatchId() != null
                && taskBo.getManualBatchId() != 0L) {
            WmEcontractSignManualBatchDB manualBatch = wmEcontractManualBatchBizService
                    .getManualBatchFromMaster(taskBo.getManualBatchId());
            if (manualBatch.getPackId() != null && manualBatch.getPackId() > 0) {
                WmEcontractSignPackDB signPackDB = wmEcontractSignPackService.querySignPackById(manualBatch.getPackId());
                if (signPackDB.getStatus() == SignPackStatusConstant.UN_APPLY
                        || signPackDB.getStatus() == SignPackStatusConstant.PART_APPLY
                        || signPackDB.getStatus() == SignPackStatusConstant.ALL_APPLY) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                            "该客户多个信息在打包确认中，请前往先富系统-客户信息-任务列表操作取消");
                }
            }
            if (judgeMultilPack(manualBatch)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                        "由于该客户下多个信息在打包确认中，请前往先富系统-客户信息-任务列表操作取消");
            }
        }
        LOGGER.info("canceling taskBo={}", JSON.toJSONString(taskBo));
        if (taskBo.getBatchId() == 0L) {
            wmEcontractCancelHoldingTaskService.cancel(taskBo);
        } else {
            //如果是允许手动打包签约的取消，则按照batchId维度全部取消
            if (ConfigUtilAdapter.getBoolean("sign_cancel_optimize_open", false)) {
                if (!MULTI_MODULE_CANCEL_ACTION_SOURCE.contains(actionSource)
                        && taskBo.getManualBatchId() != null && taskBo.getManualBatchId() != 0L) {
                    wmEcontractSignBzService.cancelSignByBatchIdWithSource(taskBo.getBatchId(),
                            WmEcontractBatchConstant.CANCEL_BATCH_TASK, callBack);
                    return Boolean.TRUE;
                }
            } else {
                if (!WmEcontractBatchConstant.CANCEL_BATCH_TASK.equals(actionSource)
                        && taskBo.getManualBatchId() != null && taskBo.getManualBatchId() != 0L) {
                    wmEcontractSignBzService.cancelSignByBatchIdWithSource(taskBo.getBatchId(),
                            WmEcontractBatchConstant.CANCEL_BATCH_TASK, callBack);
                    return Boolean.TRUE;
                }
            }

            //task维度任务取消
            WmEcontractSignBatchDB batchDB = wmEcontractBatchBizService.queryByBatchId(taskBo.getBatchId());
            if (batchDB != null && batchDB.getValid() == 1 && EcontractBatchStateEnum.IN_PROCESSING.getName().equals(batchDB.getBatchState())) {
                wmEcontractCancelEcontractTaskService.cancel(taskBo);
            } else {
                wmEcontractCancelHoldingTaskService.cancel(taskBo);
            }
        }

        // 回调
        wmEcontractCallbackFailHandler.callbackFail(taskBo.getId(), taskBo, FINISH, ECONTRACT_SERVER_CANCEL_ERROR, failMsg, actionSource, callBack);
        // 更新原因
        wmEcontractBatchBizService.updateBatchFailMsgByBatchId(taskBo.getBatchId(), reason, actionSource);
        return Boolean.TRUE;
    }

    /**
     * 双写空跑阶段取消签约任务，仅取消sign_task / sign_batch记录（双写空跑阶段不会真正发起签约）
     * @param taskBo
     * @param actionSource
     * @param callBack
     * @return
     */
    public boolean cancelSignWithDRTag(EcontractTaskBo taskBo, String actionSource, Boolean callBack) throws TException, WmCustomerException {
        LOGGER.info("wmEcontractCancelService#cancelSignWithDRTag taskBo:{}, actionSource:{}", JSONObject.toJSONString(taskBo), actionSource);
        //task维度任务取消
        WmEcontractSignBatchDB batchDB = wmEcontractBatchBizService.queryByBatchId(taskBo.getBatchId());
        if (batchDB != null && batchDB.getValid() == 1 && EcontractBatchStateEnum.IN_PROCESSING.getName().equals(batchDB.getBatchState())) {
            wmEcontractCancelDRTaskService.cancel(taskBo);
        } else {
            wmEcontractCancelHoldingTaskService.cancel(taskBo);
        }

        // 如果是系统自动取消，不进行回调
        if (WmEcontractBatchConstant.PROCESS_DR_AUTO_CANCEL.equals(actionSource)) {
            LOGGER.info("wmEcontractCancelService#cancelSignWithDRTag 系统自动取消，不进行回调");
            return Boolean.TRUE;
        }

        // 回调
        wmEcontractCallbackFailHandler.callbackFail(taskBo.getId(), taskBo, FINISH, ECONTRACT_SERVER_CANCEL_ERROR, Strings.EMPTY, actionSource, callBack);
        return Boolean.TRUE;
    }

    public boolean judgeMultilPack(WmEcontractSignManualBatchDB manualBatch) {
        if (manualBatch == null) {
            return false;
        }
        int packCount = 0;
        if (!manualBatch.getC1contractStatus().equals(WmEcontractConstant.NOT_PACK)) {
            packCount++;
        }
        if (!manualBatch.getDeliveryStatus().equals(WmEcontractConstant.NOT_PACK)) {
            packCount++;
        }
        if (!manualBatch.getSettleStatus().equals(WmEcontractConstant.NOT_PACK)) {
            packCount++;
        }
        return packCount > 1;
    }

}
