package com.sankuai.meituan.waimai.customer.bo;

import com.sankuai.sgmerchant.msgmanager.thrift.dto.SubMessageDTO;
import com.sankuai.sgmerchant.msgmanager.thrift.dto.TailMessageDTO;
import com.sankuai.sgmerchant.msgmanager.thrift.enums.MessageAppTypeEnum;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * -@author: huang<PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2023/4/24 5:39 PM
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShanGouMsgBO {

    private List<Long> poiIds;
    private Map<String, String> param;
    private String title;
    private String profile;
    private Integer templateId;
    private List<MessageAppTypeEnum> appTypeEnumList;
    private Integer pushAccount;
    private Integer pushType;
    private List<Long> acctIds;
    private List<SubMessageDTO> subMessageList;
    private TailMessageDTO tailMessage;
    private String lowVersionInclude;
    private String highVersionExclude;

}
