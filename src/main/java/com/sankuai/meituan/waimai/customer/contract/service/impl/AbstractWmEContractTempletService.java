package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.util.WmContractVersionUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public abstract class AbstractWmEContractTempletService extends AbstractWmContractTempletService {

    private static Logger logger = LoggerFactory.getLogger(AbstractWmEContractTempletService.class);

    @Autowired
    protected WmCustomerKpService wmCustomerKpService;

    @Autowired
    protected EmpServiceAdaptor empServiceAdaptor;

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = wmContractService
                .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
        Integer contractId = insertOrUpdate(contractBo, opUid, opName);

        //新建并发起签约时，同步暂存状态
        if (isC2ContractWithWmPoiId(contractBo)) {
            contractLogService.logStatusChangeForC2(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName, contractBo.getWmPoiId());
        } else {
            contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
        }

        WmContractVersionDB versionDB = saveVersion(opUid, opName, contractId, CustomerContractStatus.SIGNING.getCode());
        contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
        toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), opName);
        contractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());

        if (isC2ContractWithWmPoiId(contractBo)) {
            contractLogService.logStatusChangeForC2(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName, contractBo.getWmPoiId());
        } else {
            contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
        }
        applySign(contractBo, versionDB, opUid, opName);
        return contractId;
    }

    public Integer startSignDaoCanContract(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        ContractCheckFilter.dcContractSignValidFilter().filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = wmContractService
                .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
        Integer contractId = insertOrUpdate(contractBo, opUid, opName);

        // 新建并发起签约时，同步暂存状态
        contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);

        WmContractVersionDB versionDB = saveVersion(opUid, opName, contractId, CustomerContractStatus.SIGNING.getCode());
        contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
        toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), opName);
        contractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());

        contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);
        return applySign(contractBo, versionDB, opUid, opName).intValue();
    }

    private boolean isC2ContractWithWmPoiId(WmCustomerContractBo contractBo) {
        return new WmTempletContractTypeBo(contractBo.getBasicBo().getType()).getType() == WmTempletContractTypeBo.TYPE_C2
                && contractBo.getWmPoiId() != null &&  contractBo.getWmPoiId() > 0;
    }

    @Override
    public Integer startSignForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        if (contractBo == null || contractBo.getBasicBo() == null || contractBo.getBasicBo().getTempletContractId() <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同不存在不能发起签约");
        }
        Integer contractId = (int) contractBo.getBasicBo().getTempletContractId();
        WmCustomerContractBo oldBo = wmContractService.getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);

        WmContractVersionDB versionDB = saveVersion(opUid, opName, contractId, CustomerContractStatus.SIGNING.getCode());
        contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
        toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), opName);
        contractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());
        contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);
        applySign(contractBo, versionDB, opUid, opName);
        return contractId;
    }

    protected Long applySign(WmCustomerContractBo contractBo, WmContractVersionDB versionDB, int opUid,  String opName)
            throws WmCustomerException, TException {
        EcontractTaskApplyBo applyBo = buildEcontractTaskApplyBo(contractBo, versionDB);
        applyBo.setCommitUid(opUid);
        applyBo.setManualBatchId(contractBo.getManualBatchId());
        logger.info("开始签约 bo :{}", JSON.toJSONString(applyBo, SerializerFeature.WriteMapNullValue));
        String transactionId;
        try {
            transactionId = String.valueOf(wmEcontractTaskApplyService.applyTask(applyBo).getValue());
        } catch (WmCustomerException e) {
            logger.warn("AbstractWmEContractTempletService#applySign, WmCustomerException", e);
            signFail(contractBo.getBasicBo().getTempletContractId(), e.getMessage(), opUid, opName);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (WmServerException e) {
            logger.warn("AbstractWmEContractTempletService#applySign, WmServerException", e);
            signFail(contractBo.getBasicBo().getTempletContractId(), e.getMsg(), opUid, opName);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (Exception e) {
            logger.error("AbstractWmEContractTempletService#applySign, error", e);
            signFail(contractBo.getBasicBo().getTempletContractId(), "系统异常", opUid, opName);
            throw new TException(e.getMessage());
        }
        logger.info("合同签约成功  contractId：{}  transactionId：{}", contractBo.getBasicBo().getTempletContractId(), transactionId);
        contractVersionService.updateTransactionId(transactionId, versionDB.getVersion_number());
        return Long.parseLong(transactionId);
    }

    abstract EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean signFail(long templetContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("合同签约失败 templetContractId:{} rejectReason:{} opUid:{} opUname:{}", templetContractId, failReason, opUid, opUname);
        WmTempletContractBasicBo basic = wmContractService.getBasicById(templetContractId, false, opUid, opUname);
        if (basic == null || CustomerContractStatus.isInvalid(basic.getStatus())) {
            logger.info("合同不存在或者已经失效 contractId:{} rejectReason:{} opUid:{} opUname:{}", templetContractId, failReason, opUid, opUname);
            return true;
        }
        int oldContractStatus = basic.getStatus();
        toNextStatus((int) templetContractId, CustomerContractStatus.SIGN_FAIL.getCode(), opUid, opUname);
        basic.setStatus(CustomerContractStatus.SIGN_FAIL.getCode());
        contractLogService.logStatusChangeForSignFail(oldContractStatus, basic, failReason, opUid, opUname);
        return true;
    }

    protected WmContractVersionDB saveVersion(int opUid, String opName, Integer contractId, int status) {
        WmContractVersionDB versionDB = new WmContractVersionDB();
        versionDB.setWm_contract_id(contractId);
        versionDB.setType(CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
        versionDB.setVersion_number(WmContractVersionUtil.genVersionNum(contractId));
        versionDB.setStatus((byte) status);
        versionDB.setOp_uid(opUid);
        versionDB.setCuid(String.valueOf(opUid));
        versionDB.setOp_uname(opName);
        Integer versionId = contractVersionService.insert(versionDB);
        versionDB.setId(versionId);
        return versionDB;
    }

    @Override
    public Integer commitAudit(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "非法操作，电子合同无审核流程，不能进行提交审核");
    }

    @Override
    public Integer commitAuditForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "非法操作，电子合同无审核流程，不能进行提交审核");
    }

    @Override
    public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname)
            throws WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "非法操作，电子合同无审核流程，不能进行审核驳回");
    }
}
