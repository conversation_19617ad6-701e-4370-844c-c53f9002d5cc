package com.sankuai.meituan.waimai.customer.util;

import com.alibaba.fastjson.JSON;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.waimai.customer.exception.ParamException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

@Slf4j
public class SSOUtil {

    public static SsoUser getUser() {
        String ssoJson = Tracer.getContext("sso.user");
        if (Strings.isBlank(ssoJson)) {
            log.warn("SSOUtil trace中无user信息");
            throw new ParamException("无法获取sso用户信息");
        }
        SsoUser ssoUser = JSON.parseObject(ssoJson, SsoUser.class);
        if (null == ssoUser) {
            log.warn("SSOUtil trace中user信息错误 ssoJson={}", ssoJson);
            throw new ParamException("获取sso信息异常");
        }
        log.info("SSOUtil#getUser, ssoUser: {}", JSON.toJSON(ssoUser));
        return ssoUser;
    }

    public static SsoUser safeGetUser() {
        String ssoJson = Tracer.getContext("sso.user");
        if (Strings.isBlank(ssoJson)) {
            log.warn("SSOUtil trace中无user信息");
            return null;
        }
        SsoUser ssoUser = JSON.parseObject(ssoJson, SsoUser.class);
        if (null == ssoUser) {
            log.warn("SSOUtil trace中无user信息");
            return null;
        }
        log.info("SSOUtil#getUser, ssoUser: {}", JSON.toJSON(ssoUser));
        return ssoUser;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SsoUser {
        private long id;
        private String login;
        private String name;
        private String code;
        private String email;
    }
}