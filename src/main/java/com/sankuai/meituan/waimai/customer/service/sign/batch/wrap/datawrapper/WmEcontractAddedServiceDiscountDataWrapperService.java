package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBusinessLineTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ValueAddedServiceDiscountInfoBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ValueAddedServiceDiscountInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 增值服务费优惠PDF数据组装
 */
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.ADDED_SERVICE_DISCOUNT)
public class WmEcontractAddedServiceDiscountDataWrapperService implements
        IWmEcontractDataWrapperService {

    private static final String TEMPLET_NAME = "wm_econtract_service_fee";

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo)
            throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil
                .selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.ADDEDSERVICEDISCOUNT);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(TEMPLET_NAME);
        pdfInfoBo.setPdfBizContent(generatePdfObject(contextBo, taskBo));
        pdfInfoBo.setPdfMetaContent(generatePdfMetaContent(contextBo, taskBo));
        return Lists.newArrayList(pdfInfoBo);
    }

    private Map<String, String> generatePdfMetaContent(EcontractBatchContextBo contextBo,
            EcontractTaskBo taskBo) {
        ValueAddedServiceDiscountInfoBatchBo batchBo = JSON
                .parseObject(taskBo.getApplyContext(), ValueAddedServiceDiscountInfoBatchBo.class);
        Map<String, String> result = Maps.newHashMap();
        if (batchBo == null || CollectionUtils
                .isEmpty(batchBo.getValueAddedServiceDiscountInfoBoList())) {
            return result;
        }
        if (StringUtils.isNotEmpty(batchBo.getPeriodOfValidity())) {
            result.put("periodOfValidity", batchBo.getPeriodOfValidity());
        }

        if(batchBo.getBizLineTypeEnum() == EcontractTaskApplyBusinessLineTypeEnum.SHANGOU){
            result.put("bizLineType",EcontractTaskApplyBusinessLineTypeEnum.SHANGOU.getName());
        }else{
            result.put("bizLineType",EcontractTaskApplyBusinessLineTypeEnum.WAIMAI.getName());
        }
        return result;
    }

    private List<Map<String, String>> generatePdfObject(EcontractBatchContextBo contextBo,
            EcontractTaskBo taskBo) {
        ValueAddedServiceDiscountInfoBatchBo batchBo = JSON
                .parseObject(taskBo.getApplyContext(), ValueAddedServiceDiscountInfoBatchBo.class);
        if (batchBo == null || CollectionUtils
                .isEmpty(batchBo.getValueAddedServiceDiscountInfoBoList())) {
            return Lists.newArrayList();
        }
        List<ValueAddedServiceDiscountInfoBo> infoBo = batchBo
                .getValueAddedServiceDiscountInfoBoList();
        List<Map<String, String>> result = Lists.newArrayList();
        Map<String, String> baseMap = null;
        for (ValueAddedServiceDiscountInfoBo temp : infoBo) {
            baseMap = Maps.newHashMap();
            baseMap.put("wmPoiId",
                    StringUtils.defaultIfEmpty(temp.getWmPoiId(), StringUtils.EMPTY));
            baseMap.put("wmPoiName",
                    StringUtils.defaultIfEmpty(temp.getWmPoiName(), StringUtils.EMPTY));
            baseMap.put("discountType",
                    StringUtils.defaultIfEmpty(temp.getDiscountType(), StringUtils.EMPTY));
            baseMap.put("standardCommissionReturn", StringUtils
                    .defaultIfEmpty(temp.getStandardCommissionReturn(), StringUtils.EMPTY));
            baseMap.put("returnPercentage",
                    StringUtils.defaultIfEmpty(temp.getReturnPercentage(), StringUtils.EMPTY));
            result.add(baseMap);
        }
        return result;
    }
}