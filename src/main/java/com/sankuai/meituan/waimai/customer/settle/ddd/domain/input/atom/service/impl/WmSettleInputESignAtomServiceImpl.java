package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.repository.WmContractVersionRepository;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleESignContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.WmSettleInputESignAtomService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleLogService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettlePdfCreateService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.customer.util.WmContractVersionUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmSettleConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskSettleContextBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WmSettleInputESignAtomServiceImpl implements WmSettleInputESignAtomService {

    @Autowired
    private WmSettlePdfCreateService wmSettlePdfCreateService;
    @Autowired
    private WmEcontractSignThriftService wmEcontractSignThriftService;
    @Autowired
    private WmContractVersionRepository wmContractVersionRepository;
    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;
    @Autowired
    private WmSettleLogService wmSettleLogService;
    @Autowired
    private WmSettleService wmSettleService;

    @Override
    //WmCustomerId
    //OpUid
    //OpUname
    //ManualBatchId
    //WmPoiIdList
    public BooleanResult applyWmSettleConfirmFlow(WmSettleESignContext context)
            throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();
        long manualBatchId = context.getManualBatchId();
        List<Long> wmPoiIdList = context.getWmPoiIdList();

        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "提交确认");

        Pair<Boolean, EcontractTaskApplyBo> applyBoPair = wmSettlePdfCreateService
                .genSettleTaskApplyBo(wmCustomerId);
        LongResult applyResult = null;
        try {
            log.info("#applyWmSettleConfirmFlow#applyTask,input={}",
                    JSONObject.toJSONString(applyBoPair.getRight()));
            applyBoPair.getRight().setCommitUid(opUid);
            applyBoPair.getRight().setManualBatchId(manualBatchId);
            applyResult = wmEcontractSignThriftService.applyTask(applyBoPair.getRight());
            WmContractVersionDB versionDB = new WmContractVersionDB();
            versionDB.setWm_contract_id(wmCustomerId);
            //supportWallet
            if (applyBoPair.getLeft()) {
                versionDB.setType(CustomerContractConstant.MOON_SETTLE_VERSION_TYPE);
            } else {
                versionDB.setType(CustomerContractConstant.NORMAL_SETTLE_VERSION_TYPE);
            }
            versionDB.setVersion_number(WmContractVersionUtil.genVersionNumForSettle(wmCustomerId));
            versionDB.setStatus(WmSettleConstant.SETTLE_STATUS_TO_COMPLETE);
            versionDB.setTransaction_id(applyResult.getValue() + "");
            wmContractVersionRepository.insertSelective(versionDB);
        } catch (Exception ex) {
            log.error("applyTask异常", ex);
            return new BooleanResult(false);
        }

        wmSettleService.insertConfirmingProduceLogAsy(ObjectUtil.longList2IntList(wmPoiIdList), opUid, opUname);

        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "发起确认流程");

        return new BooleanResult(true);
    }

    @Override
    //WmCustomerId
    //OpUid
    //OpUname
    //ManualTaskApplyBo
    //WmPoiIdList
    public void startSignForHandPack(WmSettleESignContext context) throws WmCustomerException {
        int wmCustomerId = context.getWmCustomerId();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();
        ManualTaskApplyBo manualTaskApplyBo = context.getManualTaskApplyBo();
        List<Long> wmPoiIdList = context.getWmPoiIdList();

        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "提交待发起签约流程");

        ManualTaskSettleContextBo manualTaskSettleContextBo = new ManualTaskSettleContextBo();
        manualTaskSettleContextBo.setWmPoiList(wmPoiIdList);
        manualTaskApplyBo.setApplyContext(JSONObject.toJSONString(manualTaskSettleContextBo));
        wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);

        wmSettleService.insertConfirmingProduceLogAsy(ObjectUtil.longList2IntList(wmPoiIdList), opUid, opUname);

        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "发起待发起签约流程");
    }

}
