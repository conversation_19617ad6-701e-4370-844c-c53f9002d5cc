package com.sankuai.meituan.waimai.customer.service.sign.biz;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.MetricHelper;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mchange.lang.IntegerUtils;
import com.sankuai.meituan.waimai.customer.bo.sign.TaskSearchForSwitchCancelWmPoiParam;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBigContextManageService;
import com.sankuai.meituan.waimai.poibizflow.constant.customerSwitch.PoiCustomerSwitchStatusEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService;
import com.sankuai.meituan.waimai.scm.bizsettle.commonlib.constant.TaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskUpstreamStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;

/**
 * 电子合同任务
 */
@Service
public class WmEcontractTaskBizService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractTaskBizService.class);

    private static final Set<String> deliveryTaskTypeSet = Sets.newHashSet();
    static{
        deliveryTaskTypeSet.add(EcontractTaskApplyTypeEnum.POIFEE.getName());
        deliveryTaskTypeSet.add(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName());
    }

    private static final List<String> processingApplyStateList = Lists.newArrayList();
    static{
        processingApplyStateList.add(EcontractTaskStateEnum.HOLDING.getName());
        processingApplyStateList.add(EcontractTaskStateEnum.IN_PROCESSING.getName());
    }

    private static final Splitter COMMA_SPLITTER = Splitter.on(',').trimResults().omitEmptyStrings();

    @Resource
    private WmEcontractTaskDBMapper wmEcontractTaskDBMapper;

    @Autowired
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;

    @Autowired
    private WmPoiSwitchThriftService wmPoiSwitchThriftService;

    @Autowired
    private WmEcontractManualBatchBizService wmEcontractManualBatchBizService;

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    /**
     * 新增任务信息
     * @param applyBo 电子合同任务
     * @return taskId
     */
    public long insert(EcontractTaskApplyBo applyBo, EcontractTaskContextBo contextBo) {
        WmEcontractSignTaskDB taskDB = trans(applyBo, contextBo);
        assemblyAreaSeperateSave(taskDB);
        return wmEcontractBigTaskParseService.insert(taskDB);

    }

    /**
     * 更新batchId
     * @param taskIdList taskid
     * @param batchId batchId
     * @return
     * @throws WmCustomerException
     */
    public int batchUpdateBatchId(List<Long> taskIdList, Long batchId) throws WmCustomerException {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return 0;
        }

        return wmEcontractTaskDBMapper.batchUpdateBatchId(taskIdList, batchId);
    }

    /**
     * 批量修改任务状态
     * @param taskIdList 任务id列表
     * @param state 状态
     */
    public int batchUpdateState(List<Long> taskIdList, String state) throws WmCustomerException {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return 0;
        }
        if (StringUtils.isEmpty(state)) {
            return 0;
        }
        return wmEcontractTaskDBMapper.batchUpdateState(taskIdList, state);
    }

    /**
     * 任务撤回时删除任务
     * @param taskId 任务id
     * @return
     */
    public int deleteTask(Long taskId) {
        return wmEcontractTaskDBMapper.deleteTask(taskId);
    }

    /**
     * 根据客户ID删除task信息
     * @param customerId 客户ID
     * @return
     */
    public int deleteTaskByCustomerId(Integer customerId, List<String> stateList) {
        return wmEcontractTaskDBMapper.deleteByCustomerId(customerId, stateList);
    }

    /**
     * 根据id查询电子合同任务信息
     * @param taskId 任务id
     * @return
     */
    public EcontractTaskBo getById(Long taskId) {
        WmEcontractSignTaskDB taskDB = wmEcontractBigTaskParseService.getById(taskId);
        return trans(taskDB);
    }

    /**
     * 根据id和类型查询电子合同任务信息
     * @param taskId 任务id
     * @return
     */
    public EcontractTaskBo getByIdAndType(Long taskId, String taskType) {
        WmEcontractSignTaskDB taskDB = wmEcontractBigTaskParseService.getByIdAndType(taskId, taskType);
        return trans(taskDB);
    }

    public EcontractTaskBo getByIdMaster(Long taskId) {
        WmEcontractSignTaskDB taskDB = wmEcontractBigTaskParseService.getByIdMaster(taskId);
        return trans(taskDB);
    }

    public List<EcontractTaskBo> getTaskByCustomerAndTypeAndState(Integer wmCustomerId, String type, String state){
        List<EcontractTaskBo> taskBoList = Lists.newArrayList();
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getTaskByCustomerAndTypeAndState(wmCustomerId, type, state);
        for (WmEcontractSignTaskDB taskDB:taskDBList) {
            taskBoList.add(trans(taskDB));
        }
        return taskBoList;
    }

    /**
     * 批量查询一批任务
     * @param taskIdList
     * @return
     */
    public List<EcontractTaskBo> getByIdList(List<Long> taskIdList) {
        List<WmEcontractSignTaskDB> DBList = wmEcontractBigTaskParseService.getByIdList(taskIdList);
        if(CollectionUtils.isEmpty(DBList)){
            LOGGER.info("getByIdList#结果主动延迟，查主库，taskIdList:{}", JSON.toJSONString(taskIdList));
            DBList = wmEcontractBigTaskParseService.getByIdListMaster(taskIdList);
        }
        return batchTrans(DBList);
    }

    public List<EcontractTaskBo> getByIdListMaster(List<Long> taskIdList) {
        List<WmEcontractSignTaskDB> DBList = wmEcontractBigTaskParseService.getByIdListMaster(taskIdList);
        return batchTrans(DBList);
    }

    /**
     * 根据客户ID和任务状态查询任务
     * @param customerId 客户ID
     * @param state 任务状态 EcontractTaskStateEnum
     * @return 任务信息
     */
    public List<EcontractTaskBo> getByCustomerIdAndState(Integer customerId, String state) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndState(customerId, Lists.newArrayList(state));
        return batchTrans(taskDBList);
    }

    /**
     * 根据客户ID和任务状态查询任务
     * @param customerId 客户ID
     * @param state 任务状态 EcontractTaskStateEnum
     * @return 任务信息
     */
    public List<EcontractTaskBo> getByCustomerIdAndStateMaster(Integer customerId, String state) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndStateMaster(customerId, Lists.newArrayList(state));
        return batchTrans(taskDBList);
    }

    public List<EcontractTaskBo> getByCustomerIdAndStateMasterExcludeDcTask(Integer customerId, String state) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getWmTaskByCustomerIdAndStateMaster(customerId, Lists.newArrayList(state));
        return batchTrans(taskDBList);
    }

    /**
     * 查询最后一个C1合同任务
     * @param customerId
     * @param type
     * @return
     */
    public EcontractTaskBo getLastByCustomerIdAndType(Integer customerId, String type) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndType(customerId, type);
        if (CollectionUtils.isEmpty(taskDBList)) {
            return null;
        }

        return trans(taskDBList.get(taskDBList.size()-1));
    }

    /**
     * 查询最后一个生效的C1合同任务
     */
    public EcontractTaskBo getLastEffectContractByCustomerIdAndType(Integer customerId) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndType(customerId, EcontractTaskApplyTypeEnum.C1CONTRACT.getName());
        if (CollectionUtils.isEmpty(taskDBList)) {
            return null;
        }

        WmEcontractSignTaskDB lastEffect = null;
        for (WmEcontractSignTaskDB taskDB:taskDBList) {
            if (EcontractTaskStateEnum.SUCCESS.getName().equals(taskDB.getApplyState())) {
                lastEffect = taskDB;
            }
        }
        return trans(lastEffect);
    }
    /**
     * 查询最后一个生效的C2合同任务
     */
    public EcontractTaskBo getLastEffectC2ContractByCustomerIdAndType(Integer customerId) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndType(customerId,
                EcontractTaskApplyTypeEnum.C2CONTRACT.getName());
        if (CollectionUtils.isEmpty(taskDBList)) {
            return null;
        }

        WmEcontractSignTaskDB lastEffect = null;
        for (WmEcontractSignTaskDB taskDB:taskDBList) {
            if (EcontractTaskStateEnum.SUCCESS.getName().equals(taskDB.getApplyState())) {
                lastEffect = taskDB;
            }
        }
        return trans(lastEffect);
    }

    /**
     * 根据客户ID查询对应的配送任务
     */
    public List<EcontractTaskBo> getByCustomerIdAndTypeList(Integer customerId, List<String> stateList) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndState(customerId, stateList);
        List<EcontractTaskBo> taskBoList = Lists.newArrayList();
        for (WmEcontractSignTaskDB taskDB:taskDBList) {
            taskBoList.add(trans(taskDB));
        }
        return taskBoList;
    }

    public List<Long> getToSignBatchIdList(Integer customerId, List<String> typeList) {
        return wmEcontractTaskDBMapper.getToSignBatchIdList(customerId, typeList);
    }

    /**
     * 根据客户ID和门店ID查询对应的配送任务
     */
    public Map<String, EcontractTaskBo> getByCustomerIdAndWmPoiId(Integer customerId, Long wmPoiId, List<String> stateList) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndState(customerId, stateList);
        Map<String, EcontractTaskBo> taskBoMap = Maps.newHashMap();
        for (WmEcontractSignTaskDB taskDB:taskDBList) {
            //申请类型:配送信息且未该门店
            if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(taskDB.getApplyType())
                && wmPoiId.equals(taskDB.getBizId().longValue())) {
                taskBoMap.put(taskDB.getApplyType(), trans(taskDB));
            }
            //申请类型:批量配送信息且包含该门店
            if(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(taskDB.getApplyType())){
                EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = JSON
                        .parseObject(taskDB.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
                if(econtractBatchDeliveryInfoBo.getWmPoiIdList().contains(wmPoiId)){
                    taskBoMap.put(taskDB.getApplyType(), trans(taskDB));
                }
            }
            if (EcontractTaskApplyTypeEnum.SETTLE.getName().equals(taskDB.getApplyType())) {
                //申请的结算信息
                List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskDB.getApplyContext(), EcontractSettleInfoBo.class);
                for (EcontractSettleInfoBo settleInfoBo:settleInfoBoList) {
                    //结算关联的门店信息
                    List<EcontractPoiInfoBo> poiInfoBoList = settleInfoBo.getPoiInfoBoList();
                    for (EcontractPoiInfoBo poiInfoBo:poiInfoBoList) {
                        //若包含该门店
                        if (poiInfoBo.getWmPoiId().equals(wmPoiId.intValue())) {
                            taskBoMap.put(taskDB.getApplyType(), trans(taskDB));
                        }
                    }
                }
            }
        }
        return taskBoMap;
    }

    /**
     * 根据batchId查询task信息
     * @return
     */
    public List<EcontractTaskBo> getByBatchId(Long batchId) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByBatchId(batchId);
        return batchTrans(taskDBList);
    }

    /**
     * 批量任务DB信息转为任务Bo信息
     * @param taskDBList 任务DB信息
     * @return 任务Bo信息
     */
    private List<EcontractTaskBo> batchTrans(List<WmEcontractSignTaskDB> taskDBList) {
        if (CollectionUtils.isEmpty(taskDBList)) {
            return Lists.newArrayList();
        }

        List<EcontractTaskBo> taskBoList = Lists.newArrayList();
        for (WmEcontractSignTaskDB taskDB:taskDBList) {
            CollectionUtils.addIgnoreNull(taskBoList, trans(taskDB));
        }
        return taskBoList;
    }

    /**
     * 任务DB信息转为任务Bo信息
     * @param taskDB 任务DB信息
     * @return 任务Bo信息
     */
    private EcontractTaskBo trans(WmEcontractSignTaskDB taskDB) {
        if (taskDB == null) {
            return null;
        }

        return new EcontractTaskBo.Builder()
            .id(taskDB.getId())
            .bizId(taskDB.getBizId())
            .batchId(taskDB.getBatchId())
            .applyState(taskDB.getApplyState())
            .customerId(taskDB.getCustomerId())
            .applyType(wmFrameContractConfigService.handleSignTaskApplyType(taskDB))
            .recordId(taskDB.getRecordId())
            .applyContext(taskDB.getApplyContext())
            .resultContext(taskDB.getResultContext())
            .commitUid(taskDB.getCommitUid())
            .manualBatchId(taskDB.getManualBatchId())
            .build();
    }

    /**
     * 任务Bo信息转为任务DB信息
     * @param taskBo 任务Bo信息
     * @return 任务DB信息
     */
    private WmEcontractSignTaskDB trans(EcontractTaskBo taskBo) {
        WmEcontractSignTaskDB taskDB = new WmEcontractSignTaskDB();
        taskDB.setId(taskBo.getId());
        taskDB.setBizId(taskBo.getBizId());
        taskDB.setApplyState(taskBo.getApplyState());
        taskDB.setCustomerId(taskBo.getCustomerId());
        taskDB.setApplyType(taskBo.getApplyType());
        taskDB.setRecordId(taskBo.getRecordId());
        taskDB.setApplyContext(taskBo.getApplyContext());
        taskDB.setResultContext(taskBo.getResultContext());
        taskDB.setBatchId(taskBo.getBatchId());
        return taskDB;
    }

    private WmEcontractSignTaskDB trans(EcontractTaskApplyBo applyBo, EcontractTaskContextBo contextBo) {
        WmEcontractSignTaskDB taskDB = new WmEcontractSignTaskDB();
        taskDB.setBizId(IntegerUtils.parseInt(applyBo.getBizId(), 0));
        taskDB.setApplyState(applyBo.getConfigBo().isCommitConfirm()?
                             EcontractTaskStateEnum.HOLDING.getName():
                             EcontractTaskStateEnum.NOT_SIGN.getName());
        taskDB.setCustomerId(contextBo.getCustomerId());
        taskDB.setApplyType(transApplyType(applyBo.getApplyTypeEnum().getName(), applyBo));
        taskDB.setApplyContext(applyBo.getApplyInfoBo());
        taskDB.setBatchId(0L);
        taskDB.setRecordId(applyBo.getRecordId());
        taskDB.setResultContext(StringUtils.EMPTY);
        taskDB.setCommitUid(applyBo.getCommitUid());
        taskDB.setManualBatchId(applyBo.getManualBatchId());
        taskDB.setUpstreamStatus(EcontractTaskUpstreamStatusEnum.INIT.getType());
        LOGGER.info("taskDB = {}", JSON.toJSONString(taskDB));
        return taskDB;
    }

    /**
     * 配置化合同存储时映射成真正的类型
     */
    private String transApplyType(String originalApplyType, EcontractTaskApplyBo applyBo) {
        if (EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName().equals(originalApplyType) &&
                applyBo.getConfigContractContext() != null) {
            return applyBo.getConfigContractContext().getContractCode();
        }
        return originalApplyType;
    }

    public List<WmEcontractSignTaskDB> queryWithParam(SignBatchQueryParam signBatchQueryParam) {
        return wmEcontractBigTaskParseService.queryWithParam(signBatchQueryParam);
    }

    public List<EcontractTaskBo> getTaskByManualBatchId(Long manualBatchId) {
        return batchTrans(wmEcontractBigTaskParseService.getTaskByManualBatchId(manualBatchId));
    }

    public List<EcontractTaskBo> getTaskByManualBatchIdFromMaster(Long manualBatchId) {
        return batchTrans(wmEcontractBigTaskParseService.getTaskByManualBatchIdFromMaster(manualBatchId));
    }

    public Map<String, EcontractTaskBo> getByCustomerIdAndWmPoiIdList(Integer customerId, List<Long> wmPoiIdList, List<String> stateList) {
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndState(customerId, stateList);
        Map<String, EcontractTaskBo> taskBoMap = Maps.newHashMap();
        for (WmEcontractSignTaskDB taskDB : taskDBList) {
            //申请类型:配送信息且为该门店
            if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(taskDB.getApplyType()) && wmPoiIdList.contains(taskDB.getBizId().longValue())) {
                taskBoMap.put(taskDB.getApplyType(), trans(taskDB));
            }
            //申请类型:批量配送信息且包含该门店
            if (EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(taskDB.getApplyType())) {
                EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = JSON.parseObject(taskDB.getApplyContext(),
                        EcontractBatchDeliveryInfoBo.class);
                if (CollectionUtils.containsAny(econtractBatchDeliveryInfoBo.getWmPoiIdList(), wmPoiIdList)) {
                    taskBoMap.put(taskDB.getApplyType(), trans(taskDB));
                }
            }
            if (EcontractTaskApplyTypeEnum.SETTLE.getName().equals(taskDB.getApplyType())) {
                //申请的结算信息
                List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskDB.getApplyContext(), EcontractSettleInfoBo.class);
                List<Long> wmPoiListTemp = null;
                for (EcontractSettleInfoBo settleInfoBo : settleInfoBoList) {
                    //结算关联的门店信息
                    List<EcontractPoiInfoBo> poiInfoBoList = settleInfoBo.getPoiInfoBoList();
                    wmPoiListTemp = Lists.newArrayList(Lists.transform(poiInfoBoList, new Function<EcontractPoiInfoBo, Long>() {
                        @Nullable
                        @Override
                        public Long apply(@Nullable EcontractPoiInfoBo input) {
                            return input.getWmPoiId().longValue();
                        }
                    }));
                    if (CollectionUtils.containsAny(wmPoiIdList, wmPoiListTemp)) {
                        taskBoMap.put(taskDB.getApplyType(), trans(taskDB));
                    }
                }
            }
        }
        return taskBoMap;
    }

    /**
     * 针对客户切换门店维度取消场景,获取可取消的配送任务
     * @param param
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public Map<String, EcontractTaskBo> getMatchedDeliveryTaskForSwitchCancelWmPoi(TaskSearchForSwitchCancelWmPoiParam param) throws TException, WmCustomerException {
        Long taskId = param.getTaskId();
        Integer customerId = param.getCustomerId();
        List<String> stateList = param.getStateList();
        List<Long> wmPoiIdList = param.getWmPoiIdList();
        //不需要配送范围信息
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.getByCustomerIdAndState(customerId, stateList,false);
        LOGGER.info("配送任务taskDBList={}",JSONObject.toJSONString(taskDBList));
        Map<String, EcontractTaskBo> taskBoMap = Maps.newHashMap();
        for (WmEcontractSignTaskDB taskDB : taskDBList) {
            //申请类型:配送信息且为该门店
            if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(taskDB.getApplyType()) && wmPoiIdList.contains(taskDB.getBizId().longValue())) {
                taskBoMap.put(taskDB.getApplyType(), trans(taskDB));
            }
            //申请类型:批量配送信息且包含该门店
            if (EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(taskDB.getApplyType())) {
                EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = JSON.parseObject(taskDB.getApplyContext(),
                        EcontractBatchDeliveryInfoBo.class);
                LOGGER.info("任务上下文econtractBatchDeliveryInfoBo={}",JSONObject.toJSONString(econtractBatchDeliveryInfoBo));
                List<Long> canceledWmPoiIdList = Lists.newArrayList();
                try {
                    List<Integer> poiCustomerSwitchStatusList = Lists.newArrayList(PoiCustomerSwitchStatusEnum.SWITCH_CANCELING.getCode(),
                        PoiCustomerSwitchStatusEnum.SWITCH_CANCELED.getCode());
                    LOGGER.info("#getWmPoiIdListByTaskIdAndStatus#taskId={},customerSwitchList={}",
                        taskId,
                        JSONObject.toJSONString(poiCustomerSwitchStatusList));
                    canceledWmPoiIdList = wmPoiSwitchThriftService.getWmPoiIdListByTaskIdAndStatus(taskId,
                        poiCustomerSwitchStatusList);
                    LOGGER.info("#getWmPoiIdListByTaskIdAndStatus#canceledWmPoiIdList={}", canceledWmPoiIdList);
                } catch (WmPoiBizException e) {
                    LOGGER.error("调用客户切换任务异常",e);
                    throw new WmCustomerException(e.getCode(), e.getMsg());
                }
                List<Long> batchWmPoiIdList = econtractBatchDeliveryInfoBo.getWmPoiIdList();
                if(CollectionUtils.isEmpty(batchWmPoiIdList)){
                    continue;
                }
                batchWmPoiIdList.removeAll(canceledWmPoiIdList);
                if (CollectionUtils.isEmpty(batchWmPoiIdList)) {
                    LOGGER.info("切换取消门店与打包门店相符:#getByCustomerIdAndWmPoiIdListForDelivery#put,canceledWmPoiIdList={}", canceledWmPoiIdList);
                    taskBoMap.put(taskDB.getApplyType(), trans(taskDB));
                }
            }
        }

        //新打包逻辑中，如果存在除配送childTask之外的任务,则从taskBoMap中移除
        if(MapUtils.isNotEmpty(taskBoMap)) {
            Iterator<Map.Entry<String, EcontractTaskBo>> iterator = taskBoMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, EcontractTaskBo> next = iterator.next();
                Long manualBatchId = next.getValue().getManualBatchId();
                if (manualBatchId != null && manualBatchId > 0L) {
                    WmEcontractSignManualBatchDB manualBatch = wmEcontractManualBatchBizService.getManualBatch(manualBatchId);
                    if (manualBatch != null && StringUtils.isNotEmpty(manualBatch.getChildTask())) {
                        List<String> childTaskList = COMMA_SPLITTER.splitToList(manualBatch.getChildTask());
                        if (!deliveryTaskTypeSet.containsAll(childTaskList)) {
                            LOGGER.info("remove task,taskId={}", next.getValue().getId());
                            iterator.remove();
                        }
                    }
                }
            }
        }
        return taskBoMap;
    }



    public void updateUpstreamStatusByTaskId(Long taskId, Integer upstreamStatus) {
        wmEcontractTaskDBMapper.updateUpstreamStatusById(taskId, upstreamStatus);
    }

    public Long getBatchIdById(Long taskId) {
        return wmEcontractTaskDBMapper.getBatchIdById(taskId);
    }

    private void assemblyAreaSeperateSave(WmEcontractSignTaskDB taskDB) {
        // 对象为空 or 非批量配送类型任务
        if (taskDB == null || !EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(taskDB.getApplyType())) {
            return;
        }
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskDB.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        // 符合灰度的客户ID以及门店数量的任务
        if (taskDB.getCustomerId() % 100 < MccConfig.areaSeperateSaveGrayCutomerIdPercent()
                && batchDeliveryInfoBo.getWmPoiIdList().size() > MccConfig.areaSeperateSaveGrayPoiNum()) {
            batchDeliveryInfoBo.setIsAreaSeperateSave(true);
            taskDB.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        }
    }

    public List<WmEcontractSignTaskDB> querySignTaskListForCustomerUnbindWmPoi(Integer wmCustomerId) {
        return wmEcontractTaskDBMapper.querySignTaskListForCustomerUnbindWmPoi(wmCustomerId,processingApplyStateList);
    }

    /**
     * 根据任务ID查询任务信息，如果查不到，则会查主库
     */
    public EcontractTaskBo queryByIdIfNullFromMaster(Long taskId) {
        WmEcontractSignTaskDB taskDB = wmEcontractBigTaskParseService.getById(taskId);
        // 需要通过batchId查sign_Batch表
        if (Objects.isNull(taskDB) || Objects.isNull(taskDB.getBatchId())) {
            taskDB = wmEcontractBigTaskParseService.getByIdMaster(taskId);
        }

        return trans(taskDB);
    }
}
