package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyQueryBO;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyRecord;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyRecordQueryBo;

import java.util.List;

/**
 * 客户责任人申请操作日志表
 */
public interface WmCustomerOwnerApplyRecordMapper {

    int insert(WmCustomerOwnerApplyRecord record);

    int insertSelective(WmCustomerOwnerApplyRecord record);


    WmCustomerOwnerApplyRecord selectById(long id);


    int update(WmCustomerOwnerApplyRecord record);


    int delete(long id);


    List<WmCustomerOwnerApplyRecord> selectAll();

    List<WmCustomerOwnerApplyRecord> selectByCondition(WmCustomerOwnerApplyRecordQueryBo query);

    int countByCondition(WmCustomerOwnerApplyRecordQueryBo query);

    int countByConditions(WmCustomerOwnerApplyRecordQueryBo query);

}
