package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiAttributeMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAttributeDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 食堂绑定门店过程详情表业务逻辑
 */
@Slf4j
@Service
public class WmScCanteenPoiAuditDetailService {


    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;

    /**
     * 根据食堂ID查询有效的关联门店ID集合
     *
     * @param canteenIdList
     * @return
     */
    public List<Long> getWmPoiIdsByCanteenId(List<Long> canteenIdList) {
        log.info("[WmScCanteenPoiAuditDetailService.getWmPoiIdsByCanteenId] input param: canteenIdList = {}", JSONObject.toJSONString(canteenIdList));
        List<Long> wmPoiIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(canteenIdList)) {
            return wmPoiIds;
        }

        List<Long> wmPoiIdList = wmScCanteenPoiAttributeMapper.selectWmPoiIdListByCanteenPrimaryIdList(canteenIdList);
        log.info("[WmScCanteenPoiAuditDetailService.getWmPoiIdsByCanteenId] wmPoiIdList = {}", JSONObject.toJSONString(wmPoiIdList));
        return wmPoiIdList;
    }

    /**
     * 在关系集合中根据门店ID查找食堂ID
     *
     * @param relationList
     * @param wmPoiId
     * @return
     */
    public Integer findCanteenFromListByWmPoiIdNew(List<WmScCanteenPoiAttributeDO> relationList, Long wmPoiId) {
        if (CollectionUtils.isEmpty(relationList) || wmPoiId == null) {
            return null;
        }
        Optional<WmScCanteenPoiAttributeDO> op = relationList.stream()
                .filter(WmScCanteenPoiAttributeDO -> WmScCanteenPoiAttributeDO.getWmPoiId().equals(wmPoiId))
                .findFirst();
        return op.map(WmScCanteenPoiAttributeDO::getCanteenPrimaryId).orElse(null);
    }



}
