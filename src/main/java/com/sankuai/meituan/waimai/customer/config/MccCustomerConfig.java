package com.sankuai.meituan.waimai.customer.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meituan.gecko.boot.util.JacksonUtils;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.domain.CustomerGrayTeamMapCityVo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerHQAuthVo;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgTypeEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客户Mcc控制
 */
public class MccCustomerConfig {

    /**
     * 客户KP手机号
     * 是否读客户KP原手机号字段
     * 灰度过程中读原字段，新字段都有值后关闭开关读新字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpPhoneNumReadSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_phone_num_read_switch", true);
    }

    /**
     * 客户KP手机号
     * 是否写原字段
     * 原字段下掉后关闭开关不再写原字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpPhoneNumberOldWriteSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_phone_num_old_write_switch", true);
    }

    /**
     * 客户KP手机号
     * 是否写新字段
     * 开关开启开始写新字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpPhoneNumNewWriteSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_phone_num_new_write_switch", true);
    }

    /**
     * 客户KP银行卡号
     * 是否读客户KP原字段
     * 灰度过程中读原字段，新字段都有值后关闭开关读新字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpCreditCardReadSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_credit_card_read_switch", true);
    }

    /**
     * 客户KP银行卡号
     * 是否写原字段
     * 原字段下掉后关闭开关不再写原字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpCreditCardOldWriteSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_credit_card_old_write_switch", true);
    }

    /**
     * 客户KP银行卡号
     * 是否写新字段
     * 开关开启开始写新字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpCreditCardNewWriteSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_credit_card_new_write_switch", true);
    }

    /**
     * 客户KP证件号
     * 是否读客户KP原字段
     * 灰度过程中读原字段，新字段都有值后关闭开关读新字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpCertNumberReadSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_cert_number_read_switch", true);
    }

    /**
     * 客户KP证件号
     * 是否写原字段
     * 原字段下掉后关闭开关不再写原字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpCertNumberOldWriteSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_cert_number_old_write_switch", true);
    }

    /**
     * 客户KP证件号
     * 是否写新字段
     * 开关开启开始写新字段
     *
     * @return
     */
    public static boolean encryptionCustomerKpCertNumberNewWriteSwitch() {
        return ConfigUtilAdapter.getBoolean("encryption_customer_kp_cert_number_new_write_switch", true);
    }

    /* * 蜜蜂端客户显示电子执照的灰度城市和时间
     *
     * @return
     */
    public static JSONObject getCustomerBrandBatchNum() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("customer_bus_lic_type_ele_gray", "{\"secondCityId\": \"110100,310100,130800,350300\",\"deadline\": \"2021-10-11\"}"));
    }

    /**
     * 客户——证件形式（营业执照） 1-纸质,2-电子
     *
     * @return
     */
    public static int getDefaultCertificateType() {
        return ConfigUtilAdapter.getInt("customer_certificate_type_defaultval", 1);
    }

    /**
     * 获取验证平台日志操作人名称
     *
     * @return
     */
    public static String getVerifyServerOpUname() {
        return ConfigUtilAdapter.getString("verify_server_opuname", "权威验证平台自动更新");
    }

    /**
     * 电子营业执照
     *
     * @return
     */
    public static String getElicenceSourceCode() {
        return ConfigUtilAdapter.getString("queenbee.elicence.sourceCode", "d2c9f152b54857d3");
    }

    /**
     * 查询es的每页大小
     *
     * @return
     */
    public static int getEsPageSize() {
        return ConfigUtilAdapter.getInt("query_es_page_size", 30);
    }

    /**
     * mafka消息发送延迟时间
     *
     * @return
     */
    public static Long sendMafkaMsgDelayTime() {
        return ConfigUtilAdapter.getLong("send_mafka_msg_delay_time", 1000l);
    }

    /**
     * 电子营业执照权威验真平台分配的接入方ID
     *
     * @return
     */
    public static long getBusinessCertificationPartnerId() {
        return ConfigUtilAdapter.getLong("BusinessCertification.partnerId", 11231000);
    }


    /**
     * 电子营业执照调用方appkey，权威验真平台鉴权使用，复用资质的appkey鉴权
     *
     * @return
     */
    public static String getBusinessCertificationDigest() {
        return ConfigUtilAdapter.getString("BusinessCertification.digest", "com.sankuai.waimai.m.qualification");
    }


    /**
     * 校园团队orgTypes
     *
     * @return
     */
    public static List<String> getSchoolTeamOrgTypes() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer_school_team_org_types", "[1,23]"), String.class);
    }

    /**
     * 客户生效驱动kp签约人延迟时间
     *
     * @return
     */
    public static int getCustomerEffectDrivenKpSignerDelayTime() {
        return ConfigUtilAdapter.getInt("customer_effect_driven_kp_signer_delay_time", 2000);
    }


    /**
     * 客户kp优化流程灰度城市
     *
     * @return
     */
    public static List<Integer> getCustomerKpGrayCityIds() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer_kp_gray_city_ids", "[]"), Integer.class);
    }

    /**
     * 女娲开关
     *
     * @return
     */
    public static boolean getGoddessCustomerSwitch() {
        return ConfigUtilAdapter.getBoolean("goddess_customer_switch", true);
    }

    /**
     * 女娲灰度uid
     *
     * @return
     */
    public static List<Integer> getGoddessCustomerGrayUid() {
        return JSON.parseArray(ConfigUtilAdapter.getString("goddess_customer_gray_uid", "[]"), Integer.class);
    }

    /**
     * 女娲灰度城市
     *
     * @return
     */
    public static List<CustomerGrayTeamMapCityVo> getGoddessCustomerGrayTeamCity() {
        return JSON.parseArray(ConfigUtilAdapter.getString("goddess_customer_gray_team_city", "[]"), CustomerGrayTeamMapCityVo.class);
    }

    /**
     * 获取新结算客户标签id
     *
     * @return
     */
    public static long getNewSettleForCustomerTag() {
        return ConfigUtilAdapter.getLong("new_settle_for_customer_tag", 329l);
    }

    /**
     * 获取新结算门店标签id
     *
     * @return
     */
    public static long getNewSettleForPoiTag() {
        return ConfigUtilAdapter.getLong("new_settle_for_poi_tag", 328l);
    }


    /**
     * 客户生效驱动kp签约人延迟时间
     *
     * @return
     */
    public static int getCustomerSyncEsRetryTime() {
        return ConfigUtilAdapter.getInt("customer_sync_es_kp_retry_time", 5);
    }

    /**
     * 客户生效驱动kp签约人延迟时间
     *
     * @return
     */
    public static int getCustomerSyncEsWaitTime() {
        return ConfigUtilAdapter.getInt("customer_sync_es_kp_wait_time", 500);
    }

    /**
     * 门店客户列表ES结算模块状态变更查询主库流量灰度
     *
     * @return
     */
    public static int getSettleQueryMasterGrayPercent() {
        return ConfigUtilAdapter.getInt("settle_query_master_gray_percent", 0);
    }

    /**
     * 保存直接提审的客户类型
     *
     * @return
     */
    public static List<Integer> getSaveCommitCustomerRealType() {
        return JSON.parseArray(ConfigUtilAdapter.getString("save_commit_customer_real_type", "[4,5,6,7,8,9,10,11,12,13,14,15,17,18]"), Integer.class);
    }

    /**
     * 承包商提审是否新逻辑
     *
     * @return
     */
    public static boolean customerContractorCommitNew() {
        return ConfigUtilAdapter.getBoolean("customer_contractor_commit_new", true);
    }

    /**
     * 客户门店属性与Agree一致性监控开关
     *
     * @return
     */
    public static boolean customerPoiAttributeAgreeMonitorSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_poi_attribute_agree_monitor_switch", false);
    }

    /**
     * 根据客户查询门店分页大小
     *
     * @return
     */
    public static int getPoiByCustomerIdPageSize() {
        return ConfigUtilAdapter.getInt("poi_by_customerId_pageSize", 5000);
    }

    /**
     * 根据客户查询门店使用分页流量灰度
     *
     * @return
     */
    public static int getPoiByCustomerIdPageGrayPercent() {
        return ConfigUtilAdapter.getInt("poi_by_customerId_page_gray_percent", 100);
    }


    /**
     * 获取客户维度总部写权限配置
     *
     * @return
     */
    public static List<WmCustomerHQAuthVo> getHQAuthForWrite() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("hq_auth_for_write", "[{\"roleCode\":\"wm_poi_kehu_waimai_hq_rw\",\"customerRealType\":[0,1,2,3,15,4,5,6,17]},{\"roleCode\":\"wm_poi_kehu_shangou_hq_rw\",\"customerRealType\":[0,1,5,7,8,10,11,12,13,14]},{\"roleCode\":\"wm_poi_kehu_yiyao_hq_rw\",\"customerRealType\":[0,16,9,18]}]"), WmCustomerHQAuthVo.class);
    }

    /**
     * 获取客户维度总部读权限配置
     *
     * @return
     */
    public static List<WmCustomerHQAuthVo> getHQAuthForRead() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("hq_auth_for_read", "[{\"roleCode\":\"wm_poi_kehu_waimai_hq_r\",\"customerRealType\":[0,1,2,3,15,4,5,6,17]}, {\"roleCode\":\"wm_poi_kehu_shangou_hq_r\",\"customerRealType\":[0,1,5,7,8,10,11,12,13,14]}, {\"roleCode\":\"wm_poi_kehu_yiyao_hq_r\",\"customerRealType\":[0,16,9,18]}]"), WmCustomerHQAuthVo.class);
    }

    /**
     * 获取所有客户类型
     *
     * @return
     */
    public static List<Integer> getAllCustomerRealType() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("all_customer_real_type", "[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18]"), Integer.class);
    }

    /**
     * 总部拆分权限开关
     */
    public static boolean openHQAuthSplitForCustomer() {
        return ConfigUtilAdapter.getBoolean("hq_auth_split_customer_open", true);
    }

    /**
     * 虚假门店项目营业执照三要素认证开关（营业执照名字、统一社会编码和法人名字）
     *
     * @return
     */
    public static boolean customerBusinessLicenseThreeAuthSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_business_license_three_auth_switch", false);
    }

    /**
     * 虚假门店项目营业执照三要素认证按照城市灰度开关（开关打开走配置的灰度城市，开关关闭所有的城市都生效都会走灰度逻辑）
     *
     * @return
     */
    public static boolean customerBusinessLicenseThreeAuthCitySwitch() {
        return ConfigUtilAdapter.getBoolean("customer_business_license_three_auth_city_switch", true);
    }


    /**
     * 虚假门店项目营业执照三要素认证灰度城市
     *
     * @return
     */
    public static List<Integer> customerBusinessLicenseThreeAuthCity() {
        return JSON.parseArray(ConfigUtilAdapter.getString("customer_business_license_three_auth_city", "[]"), Integer.class);
    }

    /**
     * 虚假门店项目营业执照四要素认证开关（营业执照名字、统一社会编码、法人名字和身份证号）
     *
     * @return
     */
    public static boolean customerBusinessLicenseFourAuthSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_business_license_four_auth_switch", false);
    }

    /**
     * 虚假门店项目营业执照四要素认证按照城市灰度开关
     *
     * @return
     */
    public static boolean customerBusinessLicenseFourAuthCitySwitch() {
        return ConfigUtilAdapter.getBoolean("customer_business_license_four_auth_city_switch", true);
    }


    /**
     * 虚假门店项目营业执照四要素认证灰度城市
     *
     * @return
     */
    public static List<Integer> customerBusinessLicenseFourAuthCity() {
        return JSON.parseArray(ConfigUtilAdapter.getString("customer_business_license_four_auth_city", "[]"), Integer.class);
    }

    /**
     * 虚假门店项目营业执照三/四要素认证白名单
     *
     * @return
     */
    public static List<String> customerBusinessLicenseNumberAuthWhiteList() {
        return JSON.parseArray(ConfigUtilAdapter.getString("customer_business_license_number_auth_wihite_list", "[]"), String.class);
    }

    /**
     * 金融实名认证商户ID(与个人二要素认证共用一个iph)
     *
     * @return
     */
    public static long getMerchantNoForRealAuth() {
        return ConfigUtilAdapter.getLong("merchant.no", 11000124917927L);
    }

    /**
     * 虚假门店项目营业执照三/四要素认证返回无结果的错误码
     *
     * @return
     */
    public static List<String> preAuthNoResultErrorCode() {
        return JSON.parseArray(ConfigUtilAdapter.getString("pre_auth_no_result_error_code", "[3002,160114]"), String.class);
    }


    /**
     * 客户"四要素认证成功"标签
     *
     * @return
     */
    public static long getFourEleSucForCustomerTag() {
        return ConfigUtilAdapter.getLong("four_ele_suc_for_customer_tag", 382l);
    }

    /**
     * 客户"四要素认证失败"标签
     *
     * @return
     */
    public static long getFourEleFailForCustomerTag() {
        return ConfigUtilAdapter.getLong("four_ele_fail_for_customer_tag", 383l);
    }

    /**
     * ES最大支持条件查询参数最大个数
     *
     * @return
     */
    public static int getESQueryMaxSupportParamCount() {
        return ConfigUtilAdapter.getInt("es_query_max_support_param_count", 10240);
    }

    /**
     * 是否发送客户门店释放消息
     *
     * @return
     */
    public static boolean sendCustomerPoiReleaseMsgSwitch() {
        return ConfigUtilAdapter.getBoolean("send_customer_poi_release_msg_switch", true);
    }

    /**
     * 获取跨境B2C注册国家/地区枚举
     *
     * @return
     */
    public static String getB2CRegistryState() {
        return ConfigUtilAdapter.getString("b2c_registry_state", "[228,85,129,170,104,233,197,68,225,41,187,38,13,137,229,143,164,95,51,12,62,100,169,144]");
    }

    /**
     * 同步客户平台信息修改是否使用新接口开关
     *
     * @return
     */
    public static boolean syncCustomerUpdateUseNewPortSwitch() {
        return ConfigUtilAdapter.getBoolean("sync_customer_update_user_new_port_switch", true);
    }


    /**
     * 客户注册号重复返回客户id来源
     */
    public static List<Integer> getRepeateCustomerNumberReturnIdSource() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("repeate_customer_number_return_id_source", "[4,5]"), Integer.class);
    }

    /**
     * 供应链内部客户资质图片前缀
     *
     * @return
     */
    public static List<String> getCustomerInnerPicUrlPrefixs() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer_inner_pic_url_prefixs",
                "[\"/download/mos\",\"/customer/download\",\"/qb/download/mos\"]"), String.class);
    }

    /**
     * 监听验真平台消息做字段对比的时候是否开启字符处理
     *
     * @return
     */
    public static boolean asciiDealWhenCompareSwitch() {
        return ConfigUtilAdapter.getBoolean("customer.asci.deal.when.compare.switch", true);
    }

    /**
     * 监听验真平台消息做字段对比的时候处理因为ascii问题而引起的字段
     *
     * @return
     */
    public static List<String> asciiDealFieldWhenCompare() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer.asci.deal.field.when.compare",
                "[\"legalPerson\",\"customerName\"]"), String.class);
    }

    /**
     * 医药门店绑定客户业务类型校验开关
     */
    public static boolean getDrugPoiBindCustomerBizTypeCheckSwitch() {
        return ConfigUtilAdapter.getBoolean("drug_poi_bind_customer_biz_type_check_switch", true);
    }


    /**
     * 金融实名认证商户ID，用于数据清洗
     *
     * @return
     */
    public static long getMerchantNoForCleanRealAuth() {
        return ConfigUtilAdapter.getLong("merchant.no.for.clean", 11000153386737L);
    }

    /**
     * 企客监听客户门店变更消息灰度百分比
     *
     * @return
     */
    public static int getQiKeSubscribeCustomerPoiPercentNew() {
        return ConfigUtilAdapter.getInt("qike_subcribe_customer_poi_percent_new", 0);
    }

    /**
     * 企客客户入驻灰度百分比
     *
     * @return
     */
    public static int getQiKeCreateCompanyCustomerPercent() {
        return ConfigUtilAdapter.getInt("qike_create_company_customer_percent", 0);
    }

    /**
     * 美食城客户类型签约模式&资质主体类型校验开关
     *
     * @return true 开启校验 false 关闭校验
     */
    public static boolean openMscCustomerSignModeAndCustomerTypeValid() {
        return ConfigUtilAdapter.getBoolean("open_msc_customer_signmode_customertype_valid", true);
    }

    /**
     * 门店绑定客户场景,美食城类型客户前置条件校验
     *
     * @return
     */
    public static boolean openValidateCustomerRealTypeForBindWmPoi() {
        return ConfigUtilAdapter.getBoolean("open_validateCustomerRealTypeForBindWmPoi", false);
    }

    /**
     * 是否开启预绑定流程
     *
     * @return
     */
    public static boolean openApplyPreBind() {
        return ConfigUtilAdapter.getBoolean("open_openApplyPreBind", false);
    }

    public static boolean closeDDSwitchToMSCQuaListCheck() {
        return ConfigUtilAdapter.getBoolean("closeDDSwitchToMSCQuaListCheck", false);
    }

    /**
     * 客户-"已审核美食城" 标签ID
     *
     * @return
     */
    public static int getAuditedMSCCustomerLabel() {
        return ConfigUtilAdapter.getInt("audit_msc_customer_label_id", 396);
    }

    /**
     * 美食城门店标签
     *
     * @return
     */
    public static int getMSCWmPoiLabel() {
        return ConfigUtilAdapter.getInt("msc_wmpoi_label_id", 397);
    }

    /**
     * 获取企客告警接受人列表
     *
     * @return
     */
    public static String[] getQikeAlarmList() {
        return com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter.getString("qike_customer_alarm_list", "<EMAIL>").split(",");
    }

    /**
     * 客户同步ES延迟时间
     *
     * @return
     */
    public static int getCustomerSyncESDelayTime() {
        return ConfigUtilAdapter.getInt("customer_sync_es_kp_delay_time", 1000);
    }


    /**
     * 删除客户是否同步客户平台
     *
     * @return
     */
    public static boolean deleteCustomerSynPlatform() {
        return ConfigUtilAdapter.getBoolean("delete_customer_syn_platform", false);
    }

    /**
     * 查询客户的时候资质类型使用firstType字段开关
     *
     * @return
     */
    public static boolean selectCustomerPlatformQuaFirstTypeSwitch() {
        return ConfigUtilAdapter.getBoolean("select_customer_platform_qua_first_type_switch", true);
    }

    /**
     * 客户列表是否允许按照签约人证件类型单独查询
     *
     * @return
     */
    public static boolean listCustomerBySignerCertTypeSwitch() {
        return ConfigUtilAdapter.getBoolean("list_customer_by_signer_cert_type_switch", false);
    }

    /**
     * 是否使用customerDeviceType属性
     *
     * @return
     */
    public static boolean useCustomerDeviceType() {
        return ConfigUtilAdapter.getBoolean("use_customerDeviceType", false);
    }

    /**
     * 是否使用客户复用校验器（待前端上线后再开启）
     *
     * @return
     */
    public static boolean useCustomerMultiplexChecker() {
        return ConfigUtilAdapter.getBoolean("use_customer_multiplex_checker", false);
    }

    /**
     * 客户权限校验公共方法门店维度开关
     *
     * @return
     */
    public static boolean checkAuthTypeCommonPoiSwitch() {
        return ConfigUtilAdapter.getBoolean("check_auth_type_common_poi_switch", true);
    }

    /**
     * 客户审核无结果数据清洗开关
     *
     * @return
     */
    public static boolean customerAuditNoResultSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_audit_no_result_switch", false);
    }

    /**
     * 客户KP审核无结果数据清洗开关
     *
     * @return
     */
    public static boolean customerKpAuditNoResultSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_kp_audit_no_result_switch", false);
    }

    /**
     * 客户审核无结果数据清洗跳出循环
     *
     * @return
     */
    public static boolean customerAuditNoResultStop() {
        return ConfigUtilAdapter.getBoolean("customer_audit_no_result_stop", false);
    }

    /**
     * 客户KP审核无结果数据清洗跳出循环
     *
     * @return
     */
    public static boolean customerKpAuditNoResultStop() {
        return ConfigUtilAdapter.getBoolean("customer_kp_audit_no_result_stop", false);
    }

    /**
     * 获取数据不一致告警接受人列表
     *
     * @return
     */
    public static String[] getAuditAlarmList() {
        return com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter.getString("audit_customer_alarm_list", "<EMAIL>").split(",");
    }

    /**
     * 门店客户列表模块状态变更异步化灰度
     *
     * @return
     */
    public static int getCustomerPoiListESAsyncGrayPercent() {
        return ConfigUtilAdapter.getInt("customer_poi_es_async_gray_percent", 0);
    }

    /**
     * 门店客户列表模块状态变更异步化延迟时间
     *
     * @return
     */
    public static int getCustomerPoiListESAsyncDelayTime() {
        return ConfigUtilAdapter.getInt("customer_poi_es_async_delay_time", 4000);
    }

    /**
     * 客户门店解绑（删除合同以及代理商切换调用，取消门店合规操作）灰度
     *
     * @return 放量数
     */
    public static int getDoNotDealComplianceForCustomerUnbindPoiGrayPercent() {
        return ConfigUtilAdapter.getInt("do_not_deal_compliance_customer_unbind_poi_gray_percent", 0);
    }

    /**
     * 门店解绑校验门店绑定的客户是否为传入的客户开关
     *
     * @return
     */
    public static boolean unbindCheckPoiBindCustomerSwitch() {
        return ConfigUtilAdapter.getBoolean("unbind_check_poi_bind_customer_switch", true);
    }


    /**
     * 门店解绑客户触发上线门店下线的渠道白名单去
     *
     * @return
     */
    public static List<Integer> offlinePoiSourceWhiteListForUnbindPoi() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("offline_poi_source_white_list_unbind_poi", "[]"), Integer.class);
    }


    /**
     * 门店下线新接口场景key
     *
     * @return
     */
    public static String offlinePoiScenKeyForUnbindPoi() {
        return ConfigUtilAdapter.getString("offline_poi_scene_key_unbind_poi", "wmpoi_status_number_85");
    }


    /**
     * 解绑删除触发门店下线原因
     *
     * @return
     */
    public static Integer getCustomerSetOfflineReasonNew() {
        return ConfigUtilAdapter.getInt("customer_set_offline_reason_new", 133);
    }

    /**
     * 校验KP证件号是否实名通过开关-默认开启校验
     *
     * @return
     */
    public static boolean getCheckKpLegalCertNumPreSwitch() {
        return ConfigUtilAdapter.getBoolean("check.kp.legal.cert.num.pre.switch", true);
    }


    /**
     * 客户平台通知客户法人变更，法人信息处理校验是否复用开关
     *
     * @return
     */
    public static boolean customerKpLegalCheckisMultiplexSwitch() {
        return ConfigUtilAdapter.getBoolean("customer.kp.legal.check.is.multiplex.switch", true);
    }

    /**
     * 客户切换完成通知客户切换系统结果指定partition发送开关
     *
     * @return
     */
    public static boolean customerSwitchCompleteNoticePartitionSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_switch_complete_notice_partition_switch", false);
    }

    /**
     * 校验规则开关
     *
     * @return
     */
    public static boolean getCheckRuleSwitch() {
        return ConfigUtilAdapter.getBoolean("check_rule_switch", false);
    }


    /**
     * 清洗客户类型任务临时终止开关
     *
     * @return
     */
    public static boolean getCleanCustomerRealTypeAndBizCodeStopSwitch() {
        return ConfigUtilAdapter.getBoolean("clean.customer.real.type.and.biz.code.job.stop.switch", false);
    }

    /**
     * 清洗客户类型字段开关
     *
     * @return
     */
    public static boolean getCleanCustomerRealTypeDealSwitch() {
        return ConfigUtilAdapter.getBoolean("clean.customer.real.type.deal.switch", true);
    }

    /**
     * 清洗客户类型任务添加延迟查询平台客户接口开关
     *
     * @return
     */
    public static boolean getCleanJobQueryMtCustomerSwitch() {
        return ConfigUtilAdapter.getBoolean("clean.job.query.mt.customer.switch", true);
    }

    /**
     * 清洗客户业务线字段延时查询客户信息时间设置-5s
     *
     * @return
     */
    public static int getUpdateBizOrdCode2MtCustomerDelayTime() {
        return ConfigUtilAdapter.getInt("update_biz_org_code_2_mt_customer_delay_time", 5000);
    }

    /**
     * 数据清洗任务配置限流规则
     *
     * @return
     */
    public static String getCustomerRealTypeLimitConfig() {
        return ConfigUtilAdapter.getString("customer_real_type_limit_config", "[{\"startTime\":0,\"endTime\":6,\"limitRate\":100},{\"startTime\":6,\"endTime\":22,\"limitRate\":30},{\"startTime\":22,\"endTime\":24,\"limitRate\":100}]");
    }

    /**
     * 多个资金账户客户ID列表
     *
     * @return
     */
    public static List<Integer> getMultiZjAccountCustomerListConfig() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("multi_zj_account_customer_id_list", "[418317,470784,934074,2008822,1717292,1983328,2989940,3116744,5046485,4670588,5568068,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********]"), Integer.class);
    }

    /**
     * 校验业务线展示不合规门店灰度比例
     *
     * @return
     */
    public static int getCheckBizOrgCodeShowWmPoiIdGrayPercent() {
        return ConfigUtilAdapter.getInt("check_bizOrgCode_show_wmPoiId_gray_percent", 0);
    }

    /**
     * 校验业务线展示不合规门店个数
     *
     * @return
     */
    public static int getCheckBizOrgCodeShowWmPoiIdSize() {
        return ConfigUtilAdapter.getInt("check_bizOrgCode_show_wmPoiId_gray_size", 100);

    }

    /**
     * 自入驻-客户门店关系绑定的appKey渠道配置
     * 调用接口：
     * com.sankuai.meituan.waimai.customer.service.WmCustomerCommonThriftServiceImpl#saveWmCustomerModulesInfo使用
     *
     * @return
     */
    public static Map<String, Object> getCustomerPoiAppKeySourceConfig() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("zrz_customer_poi_bind_appKey_config",
                "{\"com.sankuai.sgmerchant.ruzhu\":{\"opSource\":5,\"opDetailSource\":\"闪购单店自入驻\",\"opSystem\":\"闪购单店业务系统\"},\n" +
                        "\"com.sankuai.shangou.platform.admission\":{\"opSource\":15,\"opDetailSource\":\"闪购多店代运营\",\"opSystem\":\"闪购多店代运营\"},\n" +
                        "\"com.sankuai.health.ruzhu.dandian\":{\"opSource\":7,\"opDetailSource\":\"医药单店自入驻\",\"opSystem\":\"医药单店自入驻系统\"},\n" +
                        "\"com.sankuai.waimaiesales.hunter\":{\"opSource\":3,\"opDetailSource\":\"外卖单店自入驻\",\"opSystem\":\"电销系统\"}}"), Map.class);
    }

    /**
     * 团餐BD有查看、编辑 外卖单店类型客户 权限
     *
     * @return 返回结构id
     */
    public static int tuancanBDAuthOrgId() {
        return ConfigUtilAdapter.getInt("tuan_can_bd_org_id", 170690);
    }

    /**
     * 开启团餐BD新增外卖单店客户类型查看编辑 开关
     *
     * @return boolean
     */
    public static boolean enableTuancanbdAuth() {
        return ConfigUtilAdapter.getBoolean("enable_tuancanbd_auth", true);
    }

    /**
     * 上线检查点灰度控制
     *
     * @return
     */
    public static int newCustomerOnlineCheckPercentGray() {
        return ConfigUtilAdapter.getInt("new_customer_online_check_percent_gray", 0);
    }

    /**
     * 调用结算新接口灰度百分比
     *
     * @return
     */
    public static int getSwitchCustomerUpdateAttributeThrowExceptionGrayPercent() {
        return ConfigUtilAdapter.getInt("switch_customer_update_attribute_throw_exception_gray_percent", 0);
    }

    /**
     * 批量处理客户门店数量-处理客户门店关系的时候使用
     *
     * @return
     */
    public static Integer getBatchDealCustomerPoiCount() {
        return ConfigUtilAdapter.getInt("batch.deal.customer.poi.count", 100);
    }

    /**
     * 防重加锁灰度百分比
     *
     * @return
     */
    public static Map<String, Integer> getPreventDuplicationLockGrayPercent() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("prevent.duplication.lock.gray.percent", "{ \"old.customer.confirm.start\":0," +
                " \"old.customer.confirm.operate\":0," +
                " \"switch.customer\":0, " +
                "\"cancel.switch.customer\":0," +
                " \"rollback.switch.customer\":0," +
                " \"switch.force.unbind\":0}"), Map.class);
    }

    /**
     * 防重加锁失败是否中断业务操作开关
     *
     * @return boolean
     */
    public static boolean getPreventDuplicationLockErrorStopBusinessOperateSwitch() {
        return ConfigUtilAdapter.getBoolean("prevent.duplication.lock.error.stop.business.operate", false);
    }

    /**
     * 自入驻资质重复判断抛异常灰度百分比
     *
     * @return
     */
    public static Integer getBizsettleDuplicateNumberThrowExceptionGrayPercent() {
        return ConfigUtilAdapter.getInt("bizsettle.duplicate.number.throw.exception.gray.percent", 0);
    }

    /**
     * 客户列表关联门店数量展示是否来自ES
     */
    public static boolean getCustomerListPoiCountSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_list_poi_count_from_db_switch", false);
    }

    /**
     * 监听ES是否一致的标签列表
     *
     * @return
     */
    public static List<Long> getESDataLabelIdList() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("es_data_label_id_list", "[329,260,396]"), Long.class);
    }

    /**
     * 客户切换执行检查灰度
     *
     * @return
     */
    public static Integer customerSwitchCheckGrayPercent() {
        return ConfigUtilAdapter.getInt("customer.switch.check.gray.percent", 0);
    }

    /**
     * 客户切换原客户确认发起检查灰度
     *
     * @return
     */
    public static Integer oldCustomerStartCheckGrayPercent() {
        return ConfigUtilAdapter.getInt("old.customer.start.check.gray.percent", 0);
    }

    /**
     * 客户信息变更同步ES通过MQ灰度
     *
     * @return
     */
    public static int customerSyncEsByMQGrayPercent() {
        return ConfigUtilAdapter.getInt("customer.sync.es.mq.gray.percent", 0);
    }

    /**
     * 客户信息变更同步ES获取kp信息查询主库灰度
     *
     * @return
     */
    public static int kpSyncEsByMasterGrayPercent() {
        return ConfigUtilAdapter.getInt("kp.sync.es.master.gray.percent", 0);
    }


    /**
     * 客户门店属性与Agree一致性监控慢查询治理灰度百分比
     * 分段查询百分比
     *
     * @return
     */
    public static int bigCustomerQueryPercent() {
        return ConfigUtilAdapter.getInt("big_customer_query_percent", 0);
    }

    /**
     * 分段查询百分比
     *
     * @return
     */
    public static boolean enableBigCustomerQueryDiffCompare() {
        return ConfigUtilAdapter.getBoolean("enable_big_customer_query_diff_compare", true);
    }

    /**
     * 按照业务类型+组织节点进行客户类型配置
     *
     * @return
     */
    public static Map<String, Object> bizTypeAndOrgIdMapperCustomerRealType() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("bizType.orgId.limit.customerRealType",
                "{" +
                        "\"18\":[{\"sourceType\":18,\"orgIds\":[170690],\"customerRealTypes\":[1],\"grayMisIds\":[]}]," +
                        "\"17\":[{\"sourceType\":17,\"orgIds\":[173445],\"customerRealTypes\":[1],\"grayMisIds\":[\"-1\"]}]" +
                        "}"), Map.class);
    }

    /**
     * 使用配置获取特殊客户类型映射开关
     *
     * @return
     */
    public static boolean useConfigGetSpecialCustomerRealType() {
        return ConfigUtilAdapter.getBoolean("use.config.get.special.customerRealType", false);
    }

    /**
     * 客户门店解绑appKey配置任务来源
     *
     * @return
     */
    public static Map<String, Object> getCustomerUnbindPoiAppKeySourceConfig() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("customer_poi_unbind_appKey_config",
                "{\"com.sankuai.waimai.m.agent\":{\"opSource\":16,\"opDetailSource\":\"门店代理属性变更\",\"opSystem\":\"代理商服务\"},\n" +
                        "\"com.sankuai.waimai.m.contractprocessor\":{\"opSource\":11,\"opDetailSource\":\"C1合同废弃\",\"opSystem\":\"合同公共消息服务\"}}"), Map.class);
    }

    /**
     * 客户门店绑定解绑创建任务全量开关
     *
     * @return boolean
     */
    public static Boolean getCustomerUnBindAddTaskSwitch() {
        return ConfigUtilAdapter.getBoolean("customer.unBind.poi.add.task.switch", false);
    }

    /**
     * 客户门店绑定解绑创建任务灰度比例
     *
     * @return boolean
     */
    public static Integer getCustomerUnBindAddTaskGray() {
        return ConfigUtilAdapter.getInt("customer.unBind.poi.add.task.gray", 0);
    }

    /**
     * 新主子门店模式：主站门店标签id(默认线上环境值）
     */
    public static int getMainPoiTagId() {
        return ConfigUtilAdapter.getInt("main_poi_tag_id", 660);
    }

    /**
     * 新主子门店模式：子门店标签id（默认线上环境值）
     *
     * @return
     */
    public static List<Integer> getSubPoiTagId() {
        String subPoiTagId = ConfigUtilAdapter.getString("sub_poi_tag_id", "[216,291,399,394,380,378,621,666]");
        return JSONArray.parseArray(subPoiTagId, Integer.class);
    }

    /**
     * 美食城支持的主子门店标签
     *
     * @return
     */
    public static Map<String, Integer> getMscSupportPoiLabels() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("msc.support.poi.labels", "{\"394\":660}"), Map.class);
    }

    /**
     * 美食城门店绑定客户新校验灰度二级物理城市
     *
     * @return
     */
    public static List<Integer> getMscPoiBindCustomerNewGrayCity() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("msc.poi.bind.customer.new.gray.city", "[-1]"),
                Integer.class);
    }

    /**
     * 从主库获取业务线进行校验来源
     *
     * @return
     */
    public static List<Integer> getBizOrgCodeFromMasterSource() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("bizOrgCode.from.master.source", "[]"),
                Integer.class);
    }

    /**
     * 签约人KP添加建议审核中开关
     *
     * @return
     */
    public static Boolean getCustomerKpCheckAuditSwitch() {
        return ConfigUtilAdapter.getBoolean("customer.kp.check.audit.switch", false);
    }

    /**
     * KP操作流程中添加KP状态校验灰度比例
     *
     * @return
     */
    public static Integer getCustomerKpCheckAuditGray() {
        return ConfigUtilAdapter.getInt("customer.kp.check.audit.gray", 0);
    }

    /**
     * 签约人KP不可修改的状态集合
     *
     * @return
     */
    public static List<Integer> getSignerKpUnEditStateList() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("signer.kp.un.edit.state.list", "[30,50,120,102,104,106]"), Integer.class);
    }

    /**
     * 上级客户其他信息与资质信息一起修改提审是否直接更新开关控制
     *
     * @return
     */
    public static Boolean getSuperCustomerOtherUpdateAuditSwitch() {
        return ConfigUtilAdapter.getBoolean("super.customer.other.info.audit.switch", true);
    }

    /**
     * 门店释放取消进行中任务开关
     *
     * @return boolean
     */
    public static Boolean getPoiReleaseCancelTaskSwitch() {
        return ConfigUtilAdapter.getBoolean("poi.release.cancel.task.switch", true);
    }

    /**
     * 客户门店关系变更物理删除操作监控开关
     *
     * @return
     */
    public static Boolean getCheckCustomerPoiRelDeleteSwitch() {
        return ConfigUtilAdapter.getBoolean("check.poi.rel.delete.switch", true);
    }

    /**
     * 任务新增判断是否存在流程中数据开关
     *
     * @return
     */
    public static Boolean getCheckTaskInsertExistSwitch() {
        return ConfigUtilAdapter.getBoolean("check.task.insert.exist.switch", true);
    }

    /**
     * 客户门店关系新增判断是否存在流程中数据开关
     *
     * @return
     */
    public static Boolean getCheckPoiRelInsertExistTaskSwitch() {
        return ConfigUtilAdapter.getBoolean("check.task.insert.exist.switch", true);
    }

    /**
     * 删除无效任务
     *
     * @return
     */
    public static Boolean getDeleteUnValidTaskSwitch() {
        return ConfigUtilAdapter.getBoolean("delete.un.valid.task.switch", true);
    }

    /**
     * 支持强制解绑（重新建店）的入口来源
     *
     * @return
     */
    public static List<Integer> getRebuildForceUnbindSupportOpDetailSource() {
        return JSONArray.parseArray(ConfigUtilAdapter.getString("rebuild.force.unbind.support.opDetailSource", "[21]"), Integer.class);
    }

    /**
     * 支持强制解绑（重新建店）的系统来源
     *
     * @return
     */
    public static List<Integer> getRebuildForceUnbindSupportOpSystem() {
        return JSONArray.parseArray(ConfigUtilAdapter.getString("rebuild.force.unbind.support.opSystem", "[6]"), Integer.class);
    }

    /**
     * 门店解绑客户新逻辑灰度（按照场景）
     *
     * @return
     */
    public static Map<String, Integer> getPoiUnBindCustomerNewGrayPercent() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("poi.unbind.customer.new.gray.percent", "{" +
                "\"DIRECT_UNBIND\":0," +
                "\"CONFIRM_UNBIND\":0," +
                "\"BD_FORCE_UNBIND\":0," +
                "\"POI_RELEASE_UNBIND\":0," +
                "\"DELETE_CUSTOMER_UNBIND\":0," +
                "\"CONFITM_CANCEL_UNBIND\":0," +
                "\"BD_CANCEL_UNBIND\":0," +
                "\"AGENT_SWITCH_CUSTOMER_UNBIND\":0" +
                "}"), Map.class);
    }

    /**
     * 获取任务角色新逻辑灰度
     *
     * @return
     */
    public static int getUserRoleNewGrayPercent() {
        return ConfigUtilAdapter.getInt("user.role.new.gray.percent", 0);
    }

    /**
     * 监控客户ES一致性的过滤字段集合
     *
     * @return
     */
    public static List<String> monitorCustomerEsConsistenceFilterFields() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("monitor.customer.es.consistence.fields", "[\"ctime\",\"utime\"]"), String.class);
    }

    /**
     * 客户信息变更同步ES异步灰度
     *
     * @return
     */
    public static int customerSyncEsAsyncGrayPercent() {
        return ConfigUtilAdapter.getInt("customer.sync.es.async.gray.percent", 0);
    }

    /**
     * 开启BD绑定美食城校验
     *
     * @return
     */
    public static boolean enableBdBindMSCPOICheck() {
        return ConfigUtilAdapter.getBoolean("enable_bd_bind_msc_poi_check", false);
    }

    /**
     * 开启BD绑定美食城校验灰度组织节点
     *
     * @return
     */
    public static List<Integer> enableBdBindMSCPOICheckGrayOrgIds() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("enable_bd_bind_msc_poi_check_gray_org_ids", "[2135]"), Integer.class);
    }

    /**
     * 开启BD绑定美食城全量总开关
     *
     * @return
     */
    public static boolean enableBdBindMSCPOICheckTotalFlag() {
        return ConfigUtilAdapter.getBoolean("enable_bd_bind_msc_poi_check_total_flag", false);
    }

    /**
     * 开启BD绑定美食城校验组织节点
     *
     * @return
     */
    public static List<Integer> enableBdBindMSCPOIOrgIds() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("enable_bd_bind_msc_poi_orgIds", "[2133,2259,16423,54454]"), Integer.class);
    }

    /**
     * 记录日志新格式白名单
     *
     * @return
     */
    public static List<Integer> hitNewLogRecordFormatWitheList() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("hit_new_log_record_format_withe_list", "[20422880,20423977]"), Integer.class);
    }

    /**
     * 记录日志新格式灰度百分比
     *
     * @return
     */
    public static int hitNewLogRecordFormatGrayPercent() {
        return ConfigUtilAdapter.getInt("hit_new_log_record_format_gray_percent", 0);
    }

    /**
     * 调用ES接口超时时间
     */
    public static int getESThreadTimeOutWithSeconds() {
        return ConfigUtilAdapter.getInt("es_thread_timeout_with_seconds", 10);
    }

    /**
     * 客户-资质共用 标签ID
     *
     * @return
     */
    public static int getQuaCommonCustomerLabel() {
        return ConfigUtilAdapter.getInt("qua_common_customer_label_id", 717);
    }

    /**
     * 门店-资质共用 标签ID
     *
     * @return
     */
    public static int getQuaCommonPoiLabel() {
        return ConfigUtilAdapter.getInt("qua_common_poi_label_id", 718);
    }

    /**
     * 查询客户门店ES-占用档口数逻辑指定分页大小
     *
     * @return
     */
    public static int getPoiRelEsUsedPoiCntSize() {
        return ConfigUtilAdapter.getInt("poi_rel_es_used_poi_cnt_size", 10000);
    }

    /**
     * "是否子门店"字段到客户门店关系ES的每批次处理数
     *
     * @return
     */
    public static int getSyncChildPoi2PoiRelEsSize() {
        return ConfigUtilAdapter.getInt("sync_child_poi_2_poi_rel_es_size", 500);
    }

    /**
     * 美食城客户类型允许不填写档口数开关
     *
     * @return
     */
    public static Boolean getMscAllowNoPoiCntSwitch() {
        return ConfigUtilAdapter.getBoolean("msc_allow_no_poi_cnt", true);
    }

    /**
     * 从ES获取绑定占用档口数开关
     *
     * @return
     */
    public static boolean getBindUsedPoiCntFromEsSwitch() {
        return ConfigUtilAdapter.getBoolean("get_bind_or_used_poi_from_es_switch", false);
    }

    /**
     * 分页查询客户门店关系表页数设置
     *
     * @return
     */
    public static Integer getPageQueryCustomerPoiSize() {
        return ConfigUtilAdapter.getInt("get_page_query_customer_poi_size", 100);
    }

    /**
     * 客户门店解绑接口下线开关
     *
     * @return
     */
    public static boolean getCustomerPoiUnBindOfflineSwitch() {
        return ConfigUtilAdapter.getBoolean("customer.poi.rel.unbind.offline.switch", true);
    }


    /**
     * 客户门店绑定接口下线开关
     *
     * @return
     */
    public static boolean getCustomerPoiBindOfflineSwitch() {
        return ConfigUtilAdapter.getBoolean("customer.poi.rel.bind.offline.switch", true);
    }


    /**
     * 压测不允许绑定的客户类型
     *
     * @return
     */
    public static List<Integer> getStressTestLimitBindCustomerRealType() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("stress_test_limit_bind_customer_real_types", "[2,17]"), Integer.class);
    }

    /**
     * 压测不允许修改为的客户类型
     *
     * @return
     */
    public static List<Integer> getStressTestLimitChangeCustomerRealType() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("stress_test_limit_change_customer_real_types", "[2,3]"), Integer.class);
    }

    /**
     * 压测允许请求的APPKey,多个使用英文逗号分割
     *
     * @return
     */
    public static String getStressTestAllowRequestAppKeys() {
        return ConfigUtilAdapter.getString("stress_test_allow_request_app_keys", "com.sankuai.waimaipoi.continent.bdsettle");
    }

    /**
     * 客户流量尖刺治理三期开关
     */
    public static boolean getCustomerTrafficSpikeSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_traffic_spike_switch", true);
    }


    /**
     * 废弃使用isHQ灰度
     *
     * @return
     */
    public static int disableIsHQApiPercent() {
        return ConfigUtilAdapter.getInt("disable_is_hq_api_percent", -1);
    }

    /**
     * 判断客户是否有多个门店limit size
     *
     * @return
     */
    public static int checkCustomerHaveMoreThanOnePoiSize() {
        return ConfigUtilAdapter.getInt("check_customer_have_more_than_one_poi_size", 5000);
    }

    /**
     * 绑定新流程化全量开关
     *
     * @return
     */
    public static boolean getCustomerBindPoiFlowAllSwitch() {
        return ConfigUtilAdapter.getBoolean("customer.poi.bind.flow.all.switch", false);
    }

    /**
     * 绑定新流程化灰度配置开关
     *
     * @return
     */
    public static Map<String, Integer> getCustomerPoiBindFlowGrayConfig() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("customer.poi.bind.flow.gray.config",
                        "{\"1\":0,\"2\":0,\"3\":0,\"4\":0,\"5\":0,\"6\":0,\"7\":0,\"8\":0,\"9\":0,\"10\":0,\"11\":0,\"12\":0,\"13\":0,\"14\":0,\"15\":0,\"16\":0,\"17\":0,\"18\":0,\"19\":0,\"20\":0}")
                , Map.class);
    }

    /**
     * 绑定新流程化灰度客户ID配置
     *
     * @return
     */
    public static List<Integer> getBindPoiFlowGrayCustomerIds() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("bind.poi.flow.gray.customer.ids", "[]"), Integer.class);
    }

    /**
     * 解绑新流程化全量开关
     *
     * @return
     */
    public static boolean getPoiUnbindCustomerFlowAllSwitch() {
        return ConfigUtilAdapter.getBoolean("poi.unbind.customer.flow.all.switch", false);
    }

    /**
     * 解绑新流程化灰度配置开关
     *
     * @return
     */
    public static Map<String, Integer> getPoiUnbindCustomerFlowGrayConfig() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("poi.unbind.customer.flow.gray.config",
                        "{\"1\":0,\"2\":0,\"3\":0,\"4\":0,\"5\":0,\"6\":0,\"7\":0,\"8\":0,\"9\":0,\"10\":0,\"11\":0,\"12\":0,\"13\":0,\"14\":0,\"15\":0,\"16\":0,\"17\":0,\"18\":0,\"19\":0,\"20\":0}")
                , Map.class);
    }

    /**
     * 解绑新流程化灰度客户ID配置
     *
     * @return
     */
    public static List<Integer> getPoiUnbindCustomerFlowGrayCustomerIds() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("unbind.poi.flow.gray.customer.ids", "[]"), Integer.class);
    }

    /**
     * 门店基础信息绑定前校验是否允许绑定切换新逻辑灰度设置
     *
     * @return
     */
    public static Integer getBaseInfoCheckBind2NewFlowGray() {
        return ConfigUtilAdapter.getInt("base.info.check.bind.2.new.flow.gray", 0);
    }


    //默认线上值，线下配置业务识别码 到家固定值
    //线下： 11000001316859
    //线上： 11000005117120
    public static long getIphPayMerchantNo() {
        return ConfigUtilAdapter.getLong("iph_pay_merchant_no", 11000005117120l);
    }

    /**
     * 结算接口迁移灰度百分比
     *
     * @return
     */
    public static int settleDataTransferPercent() {
        return ConfigUtilAdapter.getInt("settle_data_transfer_percent", -1);
    }

    /**
     * 结算接口迁移流量对比开关
     *
     * @return
     */
    public static boolean settleDataTransferDiffFlag() {
        return ConfigUtilAdapter.getBoolean("settle_data_transfer_percent_diff_flag", true);
    }

    /**
     * DTS监听wm_poi_control表变更过滤拼好饭类型门店开关
     *
     * @return
     */
    public static boolean filterPHFPoiForDTSFlag() {
        return ConfigUtilAdapter.getBoolean("filter_phf_poi_for_dts_flag", true);
    }

    /**
     * 外卖单店个人资质场景全量开关
     *
     * @return
     */
    public static boolean getWmSinglePerCertifyAllSwitch() {
        return ConfigUtilAdapter.getBoolean("wm.single.person.certify.all.switch", false);
    }

    /**
     * 外卖单店个人资质灰度客户ID列表
     *
     * @return
     */
    public static List<Integer> getWmSinglePerCertifyGrayCustomerIds() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("wm.single.person.certify.gray.customer.ids", "[]"), Integer.class);
    }

    /**
     * 外卖单店个人资质灰度组织结构配置
     *
     * @return
     */
    public static String getWmSinglePerCertifyOrgGrayConfig() {
        return ConfigUtilAdapter.getString("wm.single.person.certify.org.config", "[{\"source\":1,\"orgIds\":[]},{\"source\":6,\"orgIds\":[]},{\"source\":19,\"orgIds\":[]}]");
    }

    /**
     * 个人资质管理员角色code
     *
     * @return
     */
    public static Integer getPersonCertifyAdminRoleId() {
        return ConfigUtilAdapter.getInt("person_certify_admin_auth_role_id", 10054473);
    }

    /**
     * 个人资质普通用户角色code
     *
     * @return
     */
    public static Integer getPersonCertifyUserRoleId() {
        return ConfigUtilAdapter.getInt("person_certify_user_auth_role_id", 10054474);
    }

    /**
     * 客户场景标签对应关系
     *
     * @return
     */
    public static Map<String, Integer> getCustomerSceneAndTagRel() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("customer.scene.tag.rel", "{\"1\":751,\"2\":752,\"3\":753}"), Map.class);
    }

    /**
     * 外卖单店场景特批任务类型ID
     *
     * @return
     */
    public static Integer getSingleCustomerSceneSpecialTaskType() {
        return ConfigUtilAdapter.getInt("single_customer_scene_special_task_type", 420);

    }

    /**
     * 允许查询主从的APPKey
     *
     * @return
     */
    public static String getAllowQueryMasterAppKeys() {
        return ConfigUtilAdapter.getString("allow.query.master.appKeys", "com.sankuai.waimai.audit");
    }

    /**
     * 门店资质变更字段同步客户资质灰度配置
     *
     * @return
     */
    public static Integer getPoiQuaAutoSyncCustomerGrayConfig() {
        return ConfigUtilAdapter.getInt("single.qua.auto.sync.gray.config", 0);

    }

    /**
     * KP签约代理人电销入口初次审核通过新流程开关
     *
     * @return
     */
    public static Integer getKpSignerAgentDxFirstAuditSucFlowGrayConfig() {
        return ConfigUtilAdapter.getInt("kp.signer.agent.dx.first.audit.suc.gray.config", 0);
    }

    /**
     * 计算美食城档口数批量查询门店个数
     *
     * @return
     */
    public static Integer getCalMscUsedCntBatchQueryPoiCnt() {
        return ConfigUtilAdapter.getInt("cal.msc.used.cnt.batch.query.poi.cnt", 100);
    }


    /**
     * 美食城客户一店多开灰度配置
     * true：命中灰度，走新逻辑
     * false：不命中灰度，走老逻辑
     * @return
     */
    public static boolean getMscCustomerUsedPoiGrayConfig(){
        return ConfigUtilAdapter.getBoolean("msc.customer.used.poi.gray.config", false);
    }

    public static Integer getDcCustomerPageSize(){
        return ConfigUtilAdapter.getInt("dc.customer.page.size", 500);
    }

    public static boolean getWdGray(){
        return ConfigUtilAdapter.getBoolean("wd.gray", false);
    }

    public static List<Long> getDCBdRoleIds(){
        return JSONObject.parseArray(ConfigUtilAdapter.getString("dc.role.id", "[]"),Long.class);
    }

    /**
     * 根据uid查询有权限的客户列表，由于需要系统做翻页，为了防止死循环，加一个兜底的最大阈值，超过此阈值就不在继续翻页了
     * @return
     */
    public static Integer getDcCustomerIdMaxSize(){
        return ConfigUtilAdapter.getInt("dc.customer.max.size", 5000);
    }

    /**
     * 根据资质查询重复门店时的分页大小
     * @return
     */
    public static Integer getNumberRepeatPoiPageSize(){
        return ConfigUtilAdapter.getInt("repeat.poi.page.size", 500);
    }

    /**
     * 根据资质查询重复门店时的最大数量
     * 防止死循环
     * @return
     */
    public static Integer getNumberRepeatPoiMaxSize(){
        return ConfigUtilAdapter.getInt("repeat.poi.max.size", 10000);
    }

    /**
     * 美食城客户一店多开灰度配置
     *
     * @return
     */
    public static String getMscPoiCntCheckNewOrgConfig() {
        return ConfigUtilAdapter.getString("wm.msc.poi.cnt.org.config", "[{\"source\":1,\"orgIds\":[]},{\"source\":6,\"orgIds\":[]},{\"source\":19,\"orgIds\":[]}]");
    }

    /**
     * 前端拆分灰度配置
     * @return
     */
    public static String getCustomerFrontendSplitOrgConfig(){
        return ConfigUtilAdapter.getString("customer.frontend.split.org.config", "[{\"source\":1,\"orgIds\":[17640]},{\"source\":12,\"orgIds\":[192504]},{\"source\":19,\"orgIds\":[128793]},{\"source\":23,\"orgIds\":[233399]}]");
    }

    /**
     * 美食城客户一店多开灰度配置-全量开关
     *
     * @return
     */
    public static boolean getMscPoiCntCheckNewSwitch() {
        return ConfigUtilAdapter.getBoolean("wm.msc.poi.cnt.switch",false);
    }

    public static boolean getCustomerFrontendSplitSwitch(){
        return ConfigUtilAdapter.getBoolean("customer.frontend.split.switch",false);
    }


    public static Map<Integer,Integer> getSpecialCityPoiCntLimit(){
        return JSONObject.parseObject(ConfigUtilAdapter.getString("wm.msc.poi.cnt.special.city.limit", "{110100:3}"), Map.class);
    }



    /**
     * 客户平台查询接口替换灰度百分比
     * @return
     */
    public static Integer platformCustomerQueryPercent() {
        return ConfigUtilAdapter.getInt("platform.query.switch.percent", 0);
    }

    /**
     * 美食城已占用档口数新接口开关
     * @return
     */
    public static boolean mscUsedPoiCntNewQuaInterfaceSwitch() {
        return ConfigUtilAdapter.getBoolean("msc.used.poi.cnt.new.qua.interface.switch",false);
    }

    /**
     * KP非身份证允许无银行流水开关
     *
     * @return
     */
    public static Boolean getKpNotCardAllowNoBankStatementSwitch() {
        return ConfigUtilAdapter.getBoolean("kp.not.idcard.allow.no.bank.statement", false);
    }

    /**
     * 允许无银行流水新增会修改KP的客户ID列表
     *
     * @return
     */
    public static List<Long> getAllowNoBankStatementCustomerIds() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("allow.no.bank.statement.customer.ids", "[]"), Long.class);
    }

    /**
     * 校验客户资质编辑权限在BD来源渠道
     * 
     * @return
     */
    public static Boolean getCheckCustomerQuaEditAuthOnBDSource() {
        return ConfigUtilAdapter.getBoolean("check.customer.qua.edit.auth.on.bd", false);
    }

    /**
     * 客户责任人申请权限的角色ID
     *
     * @return
     */
    public static Integer getCustomerOwnerApplyRoleId() {
        return ConfigUtilAdapter.getInt("customer.owner.apply.role.id", ********);
    }

    /**
     * 允许申请客户责任人的客户类型集合
     *
     * @return
     */
    public static List<Integer> getAllowCustomerOwnerApplyCustomerRealTypes() {
        return JSONObject.parseArray(
                ConfigUtilAdapter.getString("allow.customer.owner.apply.customer.real.types", "[1,20]"), Integer.class);
    }

    /**
     * 外包商管理组织ID
     *
     * @return
     */
    public static Integer getWBMerchantManageOrgId() {
        return ConfigUtilAdapter.getInt("wb.merchant.manage.org.id", 166883);
    }

    /**
     * 客户责任人申请任务系统审批流ID
     *
     * @return
     */
    public static Integer getCustomerOwnerApplyTicketFlowID() {
        return ConfigUtilAdapter.getInt("customer.owner.apply.ticket.flow.id", 3971);
    }

    /**
     * 客户责任人申请任务系统审批节点ID
     *
     * @return
     */
    public static Integer getCustomerOwnerApplyTicketNodeID() {
        return ConfigUtilAdapter.getInt("customer.owner.apply.ticket.node.id", 3972);
    }

    /**
     * 同步客户变更信息到客户责任人申请记录开关
     *
     * @return
     */
    public static Boolean getSyncCustomerChangeInfo2CustomerOwnerApplySwitch() {
        return ConfigUtilAdapter.getBoolean("sync.customer.change.info.2.customer.owner.apply.switch", false);
    }

    /**
     * 获取客户大象群组环境配置
     *
     * @return
     */
    public static boolean getCustomerDxGroupOnLine() {
        return ConfigUtilAdapter.getBoolean("customer.online", true);
    }

    public static Boolean getCheckUpdateCustomerNumberForDCSwitch() {
        return ConfigUtilAdapter.getBoolean("check.customer.update.number.switch", false);
    }

    /**
     * 客户责任人申请权限code
     * 
     * @return
     */
    public static String getCustomerOwnerApplyAuthCode() {
        return ConfigUtilAdapter.getString("customer.owner.apply.auth.code",
                "de36d75600_25c70c432edd51b68b196cfb175a6343");
    }

    /**
     * 获取校企经理团队code
     * @return
     */
    public static String getCampusKaTeamCode() {
        return ConfigUtilAdapter.getString("team.code.campus.ka", "campus");
    }

    /**
     * 获取校企区域orgType
     * @return
     */
    public static int getCampusKaTeamOrgType() {
        return ConfigUtilAdapter.getInt("org.type.campus.ka", WmVirtualOrgTypeEnum.WM_ORG_CITY.getOrgType());
    }

    /**
     * 获取校企经理团队code
     * @return
     */
    public static String getChannelTeamCode() {
        return ConfigUtilAdapter.getString("team.code.channel", "channel");
    }

    /**
     * 获取城市团队code
     * @return
     */
    public static String getCityTeamCode() {
        return ConfigUtilAdapter.getString("team.code.city", "city");
    }

    /**
     * 获取城市所属团队orgType
     * @return
     */
    public static int getCityTeamOrgType() {
        return ConfigUtilAdapter.getInt("org.type.city", WmVirtualOrgTypeEnum.WM_CONTACT_POINT.getOrgType());
    }

    /**
     * 获取蜂窝BD岗位id
     * @return
     */
    public static List<Integer> getBDPositionId() {
        String str = ConfigUtilAdapter.getString("positionId.school.aor.bd", "[]");
        return JacksonUtils.parseList(str, Integer.class);
    }

    public static Boolean getForceUpdateSwitchForReBuild() {
        return ConfigUtilAdapter.getBoolean("force.update.customer.rebuild.switch", false);
    }

    /**
     * 子门店识别逻辑切换开关
     */
    public static Boolean getSubPoiIdentifySwitch() {
        return ConfigUtilAdapter.getBoolean("sub.poi.identify.switch", false);
    }

}
