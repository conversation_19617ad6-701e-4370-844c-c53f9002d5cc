package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_WAIMAIQIKE_FEEMODE)
public class PerformanceServiceWaimaiQikeSplit implements DeliveryPdfSplit {

    public static final String SUPPORT_MARK = "support";

    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));

        if (feeMode == LogisticsFeeModeEnum.WAIMAI_QIKE && SUPPORT_MARK.equals(deliveryInfoBo.getSupportCompanyCustomerDelivery())) {
            List<String> performanceWaimaiQikeList = pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_WAIMAIQIKE_FEEMODE.getName());
            if (CollectionUtils.isEmpty(performanceWaimaiQikeList)) {
                performanceWaimaiQikeList = Lists.newArrayList();
                performanceWaimaiQikeList.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                performanceWaimaiQikeList.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_WAIMAIQIKE_FEEMODE.getName(), performanceWaimaiQikeList);
            log.info("ADD TO PERFORMANCE_SERVICE_WAIMAIQIKE_FEEMODE，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}
