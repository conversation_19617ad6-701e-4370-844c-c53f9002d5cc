package com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.aggre.factory;

import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiSwitchModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiSettleInfo;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleProtocolAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleProtocolDBMapper;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.aggre.WmSettleEffectContext;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleSwitchService;
import com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.diff.ChangedEvent;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.wallet.WmWalletConstant;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class WmSettleEffectContextFactory {
    @Autowired
    private WmSettleService                 wmSettleService;
    @Autowired
    private WmSettleDBMapper                wmSettleDBMapper;
    @Autowired
    private WmSettleProtocolDBMapper        wmSettleProtocolDBMapper;
    @Autowired
    private WmSettleProtocolAuditedDBMapper wmSettleProtocolAuditedDBMapper;
    @Autowired
    private WmCustomerPoiService            wmCustomerPoiService;
    @Autowired
    private WmSettleSwitchService           wmSettleSwitchService;

    public WmSettleEffectContext makeContext(int wmCustomerId, int opUid, String opUname) throws WmCustomerException {

        WmSettleEffectContext effectContext = new WmSettleEffectContext();
        effectContext.setWmCustomerId(wmCustomerId);
        effectContext.setOpUid(opUid);
        effectContext.setOpUname(opUname);

        List<WmSettle> newWmSettleList = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId, false);
        effectContext.setNewWmSettleList(newWmSettleList);

        List<WmSettle> newWmSettleListWithSwitchInfo = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId, true);
        effectContext.setNewWmSettleListWithSwitchInfo(newWmSettleListWithSwitchInfo);

        List<WmSettleAudited> newWmSettleAuditedList = WmSettleTransUtil.transWmSettleList2WmSettleAuditedList(newWmSettleList);
        effectContext.setNewWmSettleAuditedList(newWmSettleAuditedList);

        List<WmSettleAudited> oldWmSettleAuditedList = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId, false);
        effectContext.setOldWmSettleAuditedList(oldWmSettleAuditedList);

        List<WmSettleAudited> oldWmSettleListWithSwitchInfo = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId, true);
        effectContext.setOldWmSettleAuditedListWithSwitchInfo(oldWmSettleListWithSwitchInfo);

        List<WmSettleDB> wmSettleByCustomerIdMaster = wmSettleDBMapper.getWmSettleByCustomerIdMaster(wmCustomerId);
        effectContext.setWmSettleByCustomerIdMaster(wmSettleByCustomerIdMaster);

        List<ChangedEvent> poiChangedEventList = Lists.newArrayList();
        List<ChangedEvent> settleChangedEventList = Lists.newArrayList();
        effectContext.setPoiChangedEventList(poiChangedEventList);
        effectContext.setSettleChangedEventList(settleChangedEventList);

        Set<Integer> deletePoiSet = Sets.newHashSet();
        effectContext.setDeletePoiSet(deletePoiSet);

        //非开通钱包的结算需要触发支付合规，开通钱包的结算需要等待开通钱包之后进行支付合规
        List<WmSettle> bankSettleList = Lists.newArrayList();
        for (WmSettle wmSettle : MoreObjects.firstNonNull(newWmSettleList, Lists.<WmSettle> newArrayList())) {
            if (wmSettle.getCard_type() == WmWalletConstant.CardType.BANK.getIndex()) {
                bankSettleList.add(wmSettle);
            }
        }
        effectContext.setBankSettleList(bankSettleList);

        List<Long> customerRelPoiList = wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerId);
        customerRelPoiList.addAll(wmSettleSwitchService.getSwitchingWmPoiIdList(wmCustomerId,true));
        effectContext.setCustomerRelPoiList(customerRelPoiList);

        List<SwitchPoiInfo> settleSwitchPoiInfoList = wmSettleSwitchService.getSettleSwitchPoiInfoList(wmCustomerId);
        effectContext.setSettleSwitchPoiInfoList(settleSwitchPoiInfoList);

        log.info("#WmSettleEffectContext#makeContext={}", JSONObject.toJSONString(effectContext));
        return effectContext;
    }

    public WmSettleEffectContext makeContext(int wmCustomerId, Map<Long, String> wmSettleSwitchInfo, int opUid, String opUname)
            throws WmCustomerException {
        WmSettleEffectContext effectContext = new WmSettleEffectContext();

        effectContext.setWmCustomerId(wmCustomerId);
        effectContext.setOpUid(opUid);
        effectContext.setOpUname(opUname);
        effectContext.setSwitchWmPoiIdList(Lists.newArrayList(wmSettleSwitchInfo.keySet()));
        if(ConfigUtilAdapter.getBoolean("is_setSwitchWmPoiRelWmSettleIdList",true)){
            effectContext.setSwitchWmPoiRelWmSettleIdList(getSwitchWmPoiRelWmSettleIdList(wmSettleSwitchInfo));
        }
        List<ChangedEvent> poiChangedEventList = Lists.newArrayList();
        List<ChangedEvent> settleChangedEventList = Lists.newArrayList();
        effectContext.setPoiChangedEventList(poiChangedEventList);
        effectContext.setSettleChangedEventList(settleChangedEventList);

        List<WmSettleAudited> mergedWmSettleAuditedList = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId, wmSettleSwitchInfo);
        List<WmSettle> newWmSettleList = WmSettleTransUtil.transWmSettleAuditedList2WmSettleList(mergedWmSettleAuditedList);
        effectContext.setNewWmSettleList(newWmSettleList);
        effectContext.setNewWmSettleListWithSwitchInfo(newWmSettleList);

        List<WmSettleAudited> wmSettleAuditedList = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId, false);
        effectContext.setOldWmSettleAuditedList(wmSettleAuditedList);
        List<WmSettleAudited> newWmSettleAuditedList = WmSettleTransUtil.transWmSettleList2WmSettleAuditedList(newWmSettleList);
        effectContext.setNewWmSettleAuditedList(newWmSettleAuditedList);

        List<WmSettle> bankSettleList = Lists.newArrayList();
        for (WmSettle wmSettle : MoreObjects.firstNonNull(newWmSettleList, Lists.<WmSettle> newArrayList())) {
            if (wmSettle.getCard_type() == WmWalletConstant.CardType.BANK.getIndex()) {
                bankSettleList.add(wmSettle);
            }
        }
        effectContext.setBankSettleList(bankSettleList);

        List<WmSettle> wmSettleOfflineList = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId, false);
        List<WmSettle> wmSettleOfflineListWithSwitchInfo = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId, wmSettleSwitchInfo);
        effectContext.setWmSettleOfflineList(wmSettleOfflineList);
        effectContext.setWmSettleOfflineListWithSwitchInfo(wmSettleOfflineListWithSwitchInfo);

        log.info("#WmSettleEffectContext#makeContext={}", JSONObject.toJSONString(effectContext));
        return effectContext;
    }

    public WmSettleEffectContext makeEffectiveContext(int wmCustomerId) throws WmCustomerException{
        WmSettleEffectContext effectContext = new WmSettleEffectContext();
        List<WmSettleAudited> oldWmSettleAuditedList = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId, false);
        effectContext.setOldWmSettleAuditedList(oldWmSettleAuditedList);
        return effectContext;
    }

    private List<Integer> getSwitchWmPoiRelWmSettleIdList(Map<Long,String> wmSettleSwitchInfo) {
        if(MapUtils.isEmpty(wmSettleSwitchInfo)){
            return Lists.newArrayList();
        }
        String temp = null;
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        Set<Integer> wmSettleIdSet = Sets.newHashSet();
        for(Entry<Long,String> entry : wmSettleSwitchInfo.entrySet()){
            temp = entry.getValue();
            if(!StringUtils.isEmpty(temp)){
                switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp);
                if(switchPoiSettleInfo.getOnlineWmSettleId()!=null && switchPoiSettleInfo.getOnlineWmSettleId()>0){
                    wmSettleIdSet.add(switchPoiSettleInfo.getOnlineWmSettleId().intValue());
                }
            }
        }
        return Lists.newArrayList(wmSettleIdSet);
    }
}
