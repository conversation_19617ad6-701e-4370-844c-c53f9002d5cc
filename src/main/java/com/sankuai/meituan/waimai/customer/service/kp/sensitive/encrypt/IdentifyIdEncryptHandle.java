package com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt;

import com.alibaba.fastjson.JSON;
import com.sankuai.conch.certify.tokenaccess.thrift.GetIdentifyTokenAndEncryptDataTo;
import com.sankuai.meituan.waimai.customer.adapter.SensitiveWordsEncryptionServiceAdapter;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 加密证件号
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
@Slf4j
public class IdentifyIdEncryptHandle implements IEncryptHandle {

    @Autowired
    private SensitiveWordsEncryptionServiceAdapter sensitiveWordsEncryptionServiceAdapter;


    @Override
    public KmsKeyNameEnum handleType() {
        return KmsKeyNameEnum.IDENTIFY_ID;
    }


    @Override
    public EncryptResult execute(KeyEncrypt keyDecrypt) throws WmCustomerException {
        log.debug("execute::execute = {}", JSON.toJSONString(keyDecrypt));
        if (keyDecrypt == null || StringUtils.isBlank(keyDecrypt.getValueForEncrypt())) {
            return null;
        }
        EncryptResult result = new EncryptResult();
        GetIdentifyTokenAndEncryptDataTo data = sensitiveWordsEncryptionServiceAdapter.getIdentifyToken(keyDecrypt.getValueForEncrypt(), keyDecrypt.getCertType());
        if (data == null) {
            return null;
        }
        result.setEncryption(data.getIdentifyIdEncrypt());
        result.setToken(data.getIdentifyIdToken());
        return result;
    }


}
