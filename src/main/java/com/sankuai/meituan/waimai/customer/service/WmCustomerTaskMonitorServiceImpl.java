package com.sankuai.meituan.waimai.customer.service;

import com.sankuai.meituan.waimai.customer.service.customer.monitor.CustomerTaskMonitorService;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiRelDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerTaskDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.monitor.WmCustomerTaskMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20230808
 * @desc 客户任务数据监控服务
 */
@Service
public class WmCustomerTaskMonitorServiceImpl implements WmCustomerTaskMonitorService {

    @Autowired
    private CustomerTaskMonitorService customerTaskMonitorService;

    /**
     * 客户门店关系变更对客户任务数据进行监控
     *
     * @param monitorCustomerPoiRelDTO
     * @return
     */
    @Override
    public String monitorCustomerTaskByRelChange(MonitorCustomerPoiRelDTO monitorCustomerPoiRelDTO) {
        return customerTaskMonitorService.checkTaskOnCustomerRelChange(monitorCustomerPoiRelDTO);
    }

    /**
     * 客户任务记录变更发起客户任务数据正确性监控
     *
     * @param monitorCustomerTaskDTO
     * @return
     */
    @Override
    public String monitorCustomerTaskByTaskChange(MonitorCustomerTaskDTO monitorCustomerTaskDTO) {
        return customerTaskMonitorService.checkTaskOnCustomerTaskChange(monitorCustomerTaskDTO);
    }
}
