package com.sankuai.meituan.waimai.customer.service.sign.callback.state;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractCallbackUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;

import org.apache.commons.lang3.StringUtils;

public abstract class AbstractWmEcontractStateService {
    /**
     * 计算当前回调场景后的状态
     * @param notifyBo 回调信息
     */
    public String calToState(EcontractNotifyBo notifyBo) {
        //新申请电子合同平台
        if (WmEcontractConstant.APPLY_ECONTRACT.equals(notifyBo.getStageName())) {
            return EcontractTaskStateEnum.IN_PROCESSING.getName();
            //电子合同平台回调 - 处理失败
        } else if (TaskConstant.TASK_FAIL.equals(notifyBo.getState())) {
            return WmEcontractCallbackUtil.handleTaskState(notifyBo.getCode()).getName();
        } else if (TaskConstant.TASK_SUCCESS.equals(notifyBo.getState())) {
            if (WmEcontractConstant.EFFECT.equals(notifyBo.getStageName())) {
                return EcontractTaskStateEnum.SUCCESS.getName();
            } else {
                return EcontractTaskStateEnum.IN_PROCESSING.getName();
            }
        } else {
            return StringUtils.EMPTY;
        }
    }

}
