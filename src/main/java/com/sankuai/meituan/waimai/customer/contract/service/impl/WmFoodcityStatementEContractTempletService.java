package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-05-11 17:37
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.FOODCITY_STATEMENT_E})
public class WmFoodcityStatementEContractTempletService extends AbstractWmEContractTempletService {

    @Autowired
    private WmCustomerService wmCustomerService;

    private static final String DEFAULT_PARTB_NAME = "北京三快在线科技有限公司";

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.FOODCITY_STATEMENT);

        //获取动态字段数据源&封装pdf动态字段
        try{
            EcontractFoodcityStatementInfoBo foodcityStatementInfoBo = new EcontractFoodcityStatementInfoBo();
            Integer wmCustomerId = contractBo.getBasicBo().getParentId();
            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerById(wmCustomerId);
            if(wmCustomerBasicBo == null){
                log.info("美食城承诺书封装pdf动态字段失败，无有效客户:{}", JSON.toJSONString(contractBo));
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
            }
            foodcityStatementInfoBo.setPartAName(wmCustomerBasicBo.getCustomerName());
            foodcityStatementInfoBo.setPartANumber(wmCustomerBasicBo.getCustomerNumber());
            foodcityStatementInfoBo.setPartAOfficialSeal(wmCustomerBasicBo.getCustomerName());
            foodcityStatementInfoBo.setPartBOfficialSeal(DEFAULT_PARTB_NAME);

            applyBo.setConfigBo(new EcontractTaskConfigBo());
            applyBo.setApplyInfoBo(JSON.toJSONString(foodcityStatementInfoBo));
            return applyBo;
        }catch(Exception e){
            log.error("美食城承诺书封装pdf动态字段失败:{}", JSON.toJSONString(contractBo), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String groupMealENum = ContractNumberUtil.genFoodcityStatementENum(insertId);
        contractBo.getBasicBo().setContractNum(groupMealENum);
        log.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, groupMealENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), groupMealENum);
        return insertId;
    }

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        // 如果是发起待打包
        if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {
            try {
                // 前置校验
                ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
                // 获取当前数据
                WmCustomerContractBo oldBo = wmContractService
                        .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
                // 更新or插入
                Integer contractId = insertOrUpdate(contractBo, opUid, opName);
                // 记录变更状态
                int status = MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus();
                if (status != contractBo.getBasicBo().getStatus()) {
                    contractLogService.logStatusChange(
                            MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                            contractBo.getBasicBo(), opUid, opName);
                }
                contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
                // 正式发起待签约任务
                toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
                contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
                contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                        contractBo.getBasicBo(), opUid, opName);
                ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(), contractId, opUid);
                wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
                return contractId;
            } catch (WmCustomerException ec) {
                log.error("【框架合同】发起待打包合同任务失败 contractBo:{} msg:{}", JSON.toJSONString(contractBo), ec.getMsg());
                throw ec;
            } catch (Exception e) {
                log.error("【框架合同】发起待打包合同任务失败 contractBo:{}", JSON.toJSONString(contractBo), e);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
            }
        } else {
            return super.startSign(contractBo, opUid, opName);
        }
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, long contractId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(wmCustomerId)
                .applyTypeEnum(EcontractTaskApplyTypeEnum.FOODCITY_STATEMENT)
                .bizId(contractId)
                .build();
    }
}
