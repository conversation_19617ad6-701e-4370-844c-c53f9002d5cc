package com.sankuai.meituan.waimai.customer.util.trans;

import java.util.List;

import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractDelayProcessDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.WmEcontractDelayProcessBo;

public class WmEcontractDelayProcessTransUtil {

    public static List<WmEcontractDelayProcessBo> tansDelayProcessDBList2DelayProcessBoList(List<WmEcontractDelayProcessDB> wmEcontractDelayProcessDBList) {
        List<WmEcontractDelayProcessBo> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmEcontractDelayProcessDBList)) {
            return result;
        }
        for (WmEcontractDelayProcessDB temp : wmEcontractDelayProcessDBList) {
            result.add(tansDelayProcessDB2DelayProcessBo(temp));
        }
        return result;
    }

    public static WmEcontractDelayProcessBo tansDelayProcessDB2DelayProcessBo(WmEcontractDelayProcessDB temp) {
        if (temp == null) {
            return null;
        }
        WmEcontractDelayProcessBo result = new WmEcontractDelayProcessBo();
        result.setId(temp.getId());
        result.setBizId(temp.getBizId());
        result.setToProcessTime(temp.getToProcessTime());
        result.setCount(temp.getCount());
        return result;
    }
}
