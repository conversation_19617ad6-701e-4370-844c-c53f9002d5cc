package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerESFields;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.domain.SearchCustomerEsConditionDTO;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerEsQueryVo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerListDB;
import com.sankuai.meituan.waimai.customer.service.customer.proxy.RestHighLevelClientProxy;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerMultiplexEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.SignerKpDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.SignerKpQueryParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.SignerKpQueryResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpSearchCondition;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSearchType.SEARCHTYPE_INCLU;
import static com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSearchType.SEARCHTYPE_SC;
import static org.elasticsearch.index.query.QueryBuilders.*;

@Service
public class WmCustomerESService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerESService.class);

    @Autowired
    private RestHighLevelClientProxy restHighLevelClient;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    protected WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;


    // 客户门店列表索引
    private String getCustomerIndex() {
        return ConfigUtilAdapter.getString("eagle_customer_index_name", "waimai_e_customer");
    }


    private String CUSTOMER_INDEX_TYPE = "customer";

    private List<WmCustomerDB> handleResonseToDbList(SearchHit[] searchHits) {
        List<WmCustomerDB> result = Lists.newArrayList();
        for (SearchHit searchHit : searchHits) {
            WmCustomerDB wmCustomerDB = WmCustomerESFields
                    .CustomerBasicFields
                    .mapAsWmCustomerDb(searchHit.getSourceAsMap());
            result.add(wmCustomerDB);
        }
        return result;
    }

    private WmCustomerListDB handleResonseToListDbList(SearchHit[] searchHits) {
        for (SearchHit searchHit : searchHits) {
            WmCustomerListDB wmCustomerDB = WmCustomerESFields
                    .CustomerBasicFields
                    .mapAsWmCustomerListDB(searchHit.getSourceAsMap());
            wmCustomerDB.setSignerPhoneNum(wmCustomerSensitiveWordsService.getReadEncryption(wmCustomerDB.getSignerPhoneNumEncryption(), KmsKeyNameEnum.PHONE_NO));
            wmCustomerDB.setSignerCertNumber(wmCustomerSensitiveWordsService.getReadEncryption(wmCustomerDB.getSignerCertNumberEncryption(), KmsKeyNameEnum.IDENTIFY_ID));
            wmCustomerDB.setSignerTypeDesc(KpSignerTypeEnum.getDescByCode(wmCustomerDB.getSignerType()));
            wmCustomerDB.setSignerCertTypeDesc(CertTypeEnum.getDescByCode(wmCustomerDB.getSignerCertType()));
            if (wmCustomerDB.getCtime() != null) {
                wmCustomerDB.setCtimeStr(DateUtil.seconds2TimeFormat(wmCustomerDB.getCtime(), DateUtil.DefaultLongFormat));
            }
            return wmCustomerDB;
        }
        return null;
    }


    /**
     * 批量写入es数据
     *
     * @param data 待写入数据
     * @throws IOException
     */
    public void batchSyncToEs(List<WmCustomerDB> data) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueSeconds(10));
        for (WmCustomerDB wmCustomerDB : data) {
            XContentBuilder xContentBuilder = WmCustomerESFields.CustomerBasicFields.customerDbToXContentBuilder(wmCustomerDB);
            IndexRequest indexRequest = new IndexRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, String.valueOf(wmCustomerDB.getId()))
                    .source(xContentBuilder);
            bulkRequest.add(indexRequest);
        }

        BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest);
        logger.debug("bulkResponse：{}", JSON.toJSON(bulkResponse));
    }


    /**
     * 批量更新es数据
     *
     * @param data 待写入数据
     * @throws IOException
     */
    public void batchSynUpdateToEs(List<WmCustomerDB> data) throws IOException, TimeoutException {
        for (WmCustomerDB wmCustomerDB : data) {
            refreshToUpsertEs(wmCustomerDB);
        }
    }


    /**
     * 批量更新es数据
     *
     * @param data 待写入数据
     * @throws IOException
     */
    public void batchUpdateToEs(List<WmCustomerDB> data) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueSeconds(10));
        logger.info("校园食堂项目:写入ES列表data:{}", JSONObject.toJSONString(data));
        for (WmCustomerDB wmCustomerDB : data) {
            XContentBuilder xContentBuilder = WmCustomerESFields.CustomerBasicFields.customerDbToXContentBuilder(wmCustomerDB);
            logger.info("校园食堂项目:写入ES列表单个数据2:{}:另外一个:{}:在一个:{}", JSONObject.toJSONString(xContentBuilder), JSON.toJSON(xContentBuilder), xContentBuilder.prettyPrint());
            UpdateRequest updateRequest = new UpdateRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, String.valueOf(wmCustomerDB.getId()))
                    .doc(xContentBuilder);
            updateRequest.docAsUpsert(true);
            logger.info("校园食堂项目:写入ES列表单个数据:{}", JSONObject.toJSONString(updateRequest));
            bulkRequest.add(updateRequest);
        }
        logger.info("校园食堂项目:写入ES列表bulkRequest:{}", JSONObject.toJSONString(bulkRequest));
        BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest);
        logger.info("校园食堂项目:bulkResponse：{}", JSON.toJSON(bulkResponse));
    }

    /**
     * 单条写入es数据
     *
     * @param wmCustomerDB 待写入数据
     * @throws IOException
     */
    public void syncToUpsertEs(WmCustomerDB wmCustomerDB) throws IOException, TimeoutException {
        logger.info("#ES# 同步客户 wmCustomerDB：{}", JSON.toJSON(wmCustomerDB));
        Map<String, Object> customerMap = WmCustomerESFields.CustomerBasicFields.customerDbAsMap(wmCustomerDB);
        // 执行更新
        UpdateRequest updateRequest = new UpdateRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, wmCustomerDB.getId().toString());
        updateRequest.timeout(TimeValue.timeValueSeconds(1));
        updateRequest.doc(customerMap);

        // 如果更新数据不存在，则执行插入。
        updateRequest.upsert(JSON.toJSONString(customerMap), XContentType.JSON);

        logger.info("#ES# 同步客户 request：{}", JSON.toJSON(updateRequest));
        UpdateResponse response = restHighLevelClient.update(updateRequest);
        logger.info("response：{}", JSON.toJSON(response));
    }


    /**
     * 刷新客户ES信息（重新组织客户ES全量信息后再刷新到ES中）
     *
     * @param db
     * @throws IOException
     * @throws TimeoutException
     */
    public void refreshToUpsertEs(WmCustomerDB db) throws IOException, TimeoutException {
        try {
            if (db == null) {
                return;
            }
            Long mtCustomerId = db.getMtCustomerId();
            if (mtCustomerId != null && mtCustomerId != 0L) {
                WmCustomerKp wmCustomerKp = null;
                if (db.getId() % 100 < MccCustomerConfig.kpSyncEsByMasterGrayPercent()) {
                    wmCustomerKp = wmCustomerKpService.selectEffectSignerByCustomerIdRT(db.getId());
                } else {
                    wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(db.getId());
                }
                db = makeEsCustomerDb(db, wmCustomerKp);
            }
            syncToUpsertEs(db);
            logger.info("refreshToUpsertEs::同步更新客户es数据成功 customerId = {}", db.getId());
        } catch (WmCustomerException e) {
            logger.warn("refreshToUpsertEs::db = {}", JSON.toJSONString(db), e);
        }
    }

    private WmCustomerDB makeEsCustomerDb(WmCustomerDB db, WmCustomerKp wmCustomerSignerKp) throws WmCustomerException {
        if (db == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "更新ES时客户对象不合法");
        }
        Long mtCustomerId = db.getMtCustomerId();
        if (mtCustomerId != null && mtCustomerId != 0L) {
            WmCustomerDB customer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(db.getId());
            if (customer != null) {
                db.setCustomerName(customer.getCustomerName());
                db.setCustomerType(customer.getCustomerType());
                db.setEffective(customer.getEffective());
                db.setCustomerNumber(customer.getCustomerNumber());
                db.setCustomerSecondType(customer.getCustomerSecondType());
                db.setLegalPerson(customer.getLegalPerson());
                db.setValidateDate(customer.getValidateDate());
                db.setAddress(customer.getAddress());
                db.setBizOrgCode(customer.getBizOrgCode());
                // 客户关联门店数量
                db.setWmPoiCount(wmCustomerPoiService.countCustomerPoi(db.getId()));
            }
            if (wmCustomerSignerKp != null) {
                db.setSignerType((int) wmCustomerSignerKp.getSignerType());
                db.setSignerCertType((int) wmCustomerSignerKp.getCertType());
                db.setSignerCertNumberToken(wmCustomerSignerKp.getCertNumberToken());
                db.setSignerCompellation(wmCustomerSignerKp.getCompellation());
                db.setSignerPhoneNumToken(wmCustomerSignerKp.getPhoneNumToken());
                db.setSignerId(wmCustomerSignerKp.getId());
                db.setSignerPhoneNumEncryption(wmCustomerSignerKp.getPhoneNumEncryption());
                db.setSignerCertNumberEncryption(wmCustomerSignerKp.getCertNumberEncryption());
            } else {
                db.setSignerType(null);
                db.setSignerCertType(null);
                db.setSignerCertNumberToken(null);
                db.setSignerCompellation(null);
                db.setSignerPhoneNumToken(null);
                db.setSignerId(null);
                db.setSignerPhoneNumEncryption(null);
                db.setSignerCertNumberEncryption(null);
            }
        }
        return db;
    }

    /**
     * 更新客户关联门店数量
     *
     * @param wmCustomerId 客户ID
     * @throws IOException
     */
    public Boolean syncCustomerPoiCountToUpsertEs(Integer wmCustomerId) throws IOException {
        int count = wmCustomerPoiService.countCustomerPoi(wmCustomerId);
        logger.info("#ES#同步客户关联门店数量：customerId={},count={}", wmCustomerId, count);
        Map<String, Object> customerMap = WmCustomerESFields.CustomerBasicFields.customerPoiCountMap(wmCustomerId, count);
        // 执行更新
        UpdateRequest updateRequest = new UpdateRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, wmCustomerId.toString());
        updateRequest.timeout(TimeValue.timeValueSeconds(1));
        updateRequest.doc(customerMap);
        logger.info("#ES# 同步客户 request：{}", JSON.toJSON(updateRequest));
        UpdateResponse response = restHighLevelClient.update(updateRequest);
        logger.info("response：{}", JSON.toJSON(response));
        return true;
    }

    /**
     * 迁移客户平台更新customerName
     *
     * @param customerName 客户名称
     * @throws IOException
     */
    public void syncCustomerNameToUpsertEs(String customerName, Integer wmCustomerId) throws IOException {
        logger.info("#ES#同步客户名称：{}", customerName);
        Map<String, Object> customerMap = WmCustomerESFields.CustomerBasicFields.customerNameAsMap(wmCustomerId, customerName);
        // 执行更新
        UpdateRequest updateRequest = new UpdateRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, wmCustomerId.toString());
        updateRequest.timeout(TimeValue.timeValueSeconds(1));
        updateRequest.doc(customerMap);
        // 如果更新数据不存在，则执行插入。
        updateRequest.upsert(JSON.toJSONString(customerMap), XContentType.JSON);

        logger.info("#ES# 同步客户 request：{}", JSON.toJSON(updateRequest));
        UpdateResponse response = restHighLevelClient.update(updateRequest);
        logger.info("response：{}", JSON.toJSON(response));
    }
    /**
     * 更新客户ES的KP信息
     *
     * @param wmCustomerKp 客户签约人信息
     * @throws IOException
     */
    public void syncCustomerKpToUpsertEs(Integer wmCustomerId, WmCustomerKp wmCustomerKp) throws IOException {
        logger.info("#ES#同步客户KP：{}", wmCustomerKp);
        Map<String, Object> customerMap = WmCustomerESFields.CustomerBasicFields.customerKpAsMap(wmCustomerId, wmCustomerKp);
        // 执行更新
        UpdateRequest updateRequest = new UpdateRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, wmCustomerId.toString());
        updateRequest.timeout(TimeValue.timeValueSeconds(1));
        updateRequest.doc(customerMap);
        // 如果更新数据不存在，则执行插入。
        updateRequest.upsert(JSON.toJSONString(customerMap), XContentType.JSON);

        logger.info("#ES# 同步客户KP request：{}", JSON.toJSON(updateRequest));
        UpdateResponse response = restHighLevelClient.update(updateRequest);
        logger.info("response：{}", JSON.toJSON(response));
    }

    /**
     * 通过mtcustomerId获取客户名称-同步到es中
     *
     * @param mtCustomerId
     * @return
     * @throws WmCustomerException
     */
    public boolean updateCustomerNameToEs(Long mtCustomerId) throws WmCustomerException {
        Pair<Integer, String> customerPair = wmCustomerPlatformDataParseService
                .getCustomerNameFromMtCustomer(mtCustomerId);
        if (customerPair == null) {
            return false;
        }
        try {
            syncCustomerNameToUpsertEs(customerPair.getRight(), customerPair.getLeft());
        } catch (IOException e) {
            logger.error("syncCustomerNameToUpsertEs IOException", e);
            return false;
        }
        return true;
    }

    /**
     * 客户标签id更新
     *
     * @param wmCustomerId
     * @param customerLabelIds
     * @throws IOException
     */
    public void syncCustomerLabelToUpsertEs(Long wmCustomerId, String customerLabelIds) throws IOException {
        logger.info("syncCustomerLabelToUpsertEs#ES同步客户标签id：{}", customerLabelIds);
        Map<String, Object> customerLabelMap = WmCustomerESFields.CustomerBasicFields.customerLabelAsMap(new Long(wmCustomerId).intValue(), customerLabelIds);
        // 执行更新
        UpdateRequest updateRequest = new UpdateRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, wmCustomerId.toString());
        updateRequest.timeout(TimeValue.timeValueSeconds(1));
        updateRequest.doc(customerLabelMap);
        // 如果更新数据不存在，则执行插入。
        updateRequest.upsert(JSON.toJSONString(customerLabelMap), XContentType.JSON);
        logger.info("#ES# 同步客户 request：{}", JSON.toJSON(updateRequest));
        UpdateResponse response = restHighLevelClient.update(updateRequest);
        logger.info("response：{}", JSON.toJSON(response));
    }

    /**
     * 单条删除es数据
     *
     * @param cusId 待删除客户ID
     * @throws IOException
     */
    public void syncToDelEs(Integer cusId) throws IOException, TimeoutException {
        logger.info("#ES# 删除客户 id：{}", cusId);
        DeleteRequest deleteRequest = new DeleteRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, cusId.toString());
        deleteRequest.timeout(TimeValue.timeValueSeconds(1));
        DeleteResponse deleteResponse = restHighLevelClient.delete(deleteRequest);
        logger.info("response：{}", JSON.toJSON(deleteResponse));
    }

    /**
     * 根据传入字段更新客户ES
     * @param customerIdList
     * @param field
     * @return
     * @throws WmCustomerException
     */
    public List<Integer> syncWmCustomerToES(List<Integer> customerIdList, Set<String> field) throws WmCustomerException {
        logger.info("syncWmCustomerToES：customerIdList:{}  field:{}", JSON.toJSON(customerIdList), field);
        List<Integer> errIds = Lists.newArrayList();
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.getCustomerDBList(customerIdList);
        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            return errIds;
        }
        buildWmCustomerDBList(wmCustomerDBList, field);
        for (WmCustomerDB wmCustomer : wmCustomerDBList) {
            try {
                // 执行更新
                UpdateRequest updateRequest = new UpdateRequest(getCustomerIndex(), CUSTOMER_INDEX_TYPE, wmCustomer.getId().toString());
                updateRequest.timeout(TimeValue.timeValueSeconds(1));
                Map<String, Object> customerMap = WmCustomerESFields.CustomerBasicFields.customerDbAsMap(wmCustomer, field);
                updateRequest.doc(customerMap);
                // 如果更新数据不存在，则执行插入。
                updateRequest.upsert(JSON.toJSONString(customerMap), XContentType.JSON);
                logger.info("#ES# 同步客户 request：{}", JSON.toJSON(updateRequest));
                UpdateResponse response = restHighLevelClient.update(updateRequest);
                logger.info("response：{}", JSON.toJSON(response));

            } catch (Exception e) {
                logger.error("同步更新/新增es数据失败了 wmCustomer={},field={}",JSONObject.toJSONString(wmCustomer),JSONObject.toJSONString(field), e);
                errIds.add(wmCustomer.getId());
            }
        }
        return errIds;
    }

    private void buildWmCustomerDBList(List<WmCustomerDB> wmCustomerDBList, Set<String> field) {
        List<Integer> customerIdList = wmCustomerDBList.stream().map(x -> x.getId()).collect(Collectors.toList());
        boolean isUpdateKp = false;
        boolean isUpdateWmPoiCount = false;

        List<String> signerFields = Lists.newArrayList(
                WmCustomerESFields.CustomerBasicFields.signerId.getField(), WmCustomerESFields.CustomerBasicFields.signerType.getField(),
                WmCustomerESFields.CustomerBasicFields.signerCertType.getField(), WmCustomerESFields.CustomerBasicFields.signerCompellation.getField(),
                WmCustomerESFields.CustomerBasicFields.signerCertNumberToken.getField(), WmCustomerESFields.CustomerBasicFields.signerCertNumberEncryption.getField(),
                WmCustomerESFields.CustomerBasicFields.signerPhoneNumToken.getField(), WmCustomerESFields.CustomerBasicFields.signerPhoneNumEncryption.getField()
        );
        Map<Integer, WmCustomerKp> kpMap = Maps.newHashMap();
        Map<Integer, WmCustomerListDB> countCustomerPoiMap = Maps.newHashMap();
        if (!Collections.disjoint(field, signerFields)) {
            WmCustomerKpSearchCondition condition = new WmCustomerKpSearchCondition();
            condition.setCustomerIdList(customerIdList);
            condition.setKpType((int) KpTypeEnum.SIGNER.getType());
            condition.setEffective(KpConstants.EFFECTIVE);
            condition.setValid(CustomerConstants.VALID);
            List<WmCustomerKp> result = wmCustomerKpDBMapper.selectByCondition(condition);
            if (!CollectionUtils.isEmpty(result)) {
                kpMap = result.stream().collect(Collectors.toConcurrentMap(WmCustomerKp::getCustomerId, wmCustomerKp -> wmCustomerKp));
            }
            isUpdateKp = true;
        }
        if (field.contains(WmCustomerESFields.CustomerBasicFields.wmPoiCount.getField())) {
            countCustomerPoiMap = wmCustomerPoiService.countCustomerPoiList(customerIdList);
            isUpdateWmPoiCount = true;
        }

        if (!isUpdateKp && !isUpdateWmPoiCount) {
            return;
        }
        for (WmCustomerDB db : wmCustomerDBList) {
            Integer customerId = db.getId();
            if (isUpdateKp) {
                WmCustomerKp wmCustomerSignerKp = kpMap.get(customerId);
                if (wmCustomerSignerKp != null) {
                    db.setSignerType((int) wmCustomerSignerKp.getSignerType());
                    db.setSignerCertType((int) wmCustomerSignerKp.getCertType());
                    db.setSignerCertNumberToken(wmCustomerSignerKp.getCertNumberToken());
                    db.setSignerCompellation(wmCustomerSignerKp.getCompellation());
                    db.setSignerPhoneNumToken(wmCustomerSignerKp.getPhoneNumToken());
                    db.setSignerId(wmCustomerSignerKp.getId());
                    db.setSignerPhoneNumEncryption(wmCustomerSignerKp.getPhoneNumEncryption());
                    db.setSignerCertNumberEncryption(wmCustomerSignerKp.getCertNumberEncryption());
                } else {
                    db.setSignerType(null);
                    db.setSignerCertType(null);
                    db.setSignerCertNumberToken(null);
                    db.setSignerCompellation(null);
                    db.setSignerPhoneNumToken(null);
                    db.setSignerId(null);
                    db.setSignerPhoneNumEncryption(null);
                    db.setSignerCertNumberEncryption(null);
                }
            }
            if (isUpdateWmPoiCount) {
                WmCustomerListDB wmCustomerListDB = countCustomerPoiMap.get(customerId);
                if (wmCustomerListDB != null) {
                    db.setWmPoiCount(wmCustomerListDB.getWmPoiCount());
                } else {
                    db.setWmPoiCount(0);
                }
            }
        }
    }


    /**
     * 通过keyword模糊匹配客户（ID和客户名称）
     *
     * @param keyword
     * @param isLeaf  是否末级客户：1、是；2、否。默认为是
     * @return
     * @throws IOException
     */
    public List<WmCustomerDB> queryCustomerListByKeyword(int searchType, String keyword, int isLeaf) throws IOException {
        logger.info("queryCustomerListByKeyword , keyword={}", keyword);

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (isNormalInteger(keyword)) {
            queryBuilder.must(termQuery(WmCustomerESFields.
                    CustomerBasicFields.id.getField(), Integer.valueOf(keyword)));
        } else if (isMtCustomerId(keyword)) {
            logger.info("queryCustomerListByKeyword, keyword={}", keyword);
            queryBuilder.must(termQuery(WmCustomerESFields.
                    CustomerBasicFields.mtCustomerId.getField(), Long.valueOf(keyword)));
        } else {
            queryBuilder.must(matchPhraseQuery(WmCustomerESFields.
                    CustomerBasicFields.customerName.getField(), keyword));
        }
        if (isLeaf != CustomerConstants.CUSTOMER_IS_LEAF_ALL) {
            //是否上级客户
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.isLeaf.getField(), isLeaf));
        }

        if (searchType == SEARCHTYPE_SC.getCode()) {
            // 只查询承包商数据
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), CustomerRealTypeEnum.CONTRACTOR.getValue()));
        } else if (searchType == SEARCHTYPE_INCLU.getCode()) {
            // 查询的数据包含校园承包商，不增加条件
        } else {
            // 原接口不查询校园承包商
            queryBuilder.mustNot(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), CustomerRealTypeEnum.CONTRACTOR.getValue()));
            // 非客户列表页不查聚合配送商
            queryBuilder.mustNot(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue()));
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        searchSourceBuilder.from(0);
        //设置大小选项，确定要返回的搜索匹配数。 默认为10。
        searchSourceBuilder.size(30);
        searchSourceBuilder.sort(WmCustomerESFields.
                CustomerBasicFields.id.getField(), SortOrder.DESC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(600));
        SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("queryCustomerListByKeyword, searchHits={}", JSONObject.toJSONString(searchHits));
        logger.info("totalHits:{} maxScore:{}", searchResponse.getHits().getTotalHits(),
                searchResponse.getHits().getMaxScore());
        return handleResonseToDbList(searchHits);
    }

    public List<WmCustomerDB> queryCustomerListByCondition(SearchCustomerEsConditionDTO searchCustomerEsConditionDTO) throws IOException {
        logger.info("queryCustomerListByCondition , condition={}", JSON.toJSONString(searchCustomerEsConditionDTO));

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        String keyword = searchCustomerEsConditionDTO.getKeyWord();
        if (isNormalInteger(keyword)) {
            queryBuilder.must(termQuery(WmCustomerESFields.
                    CustomerBasicFields.id.getField(), Integer.valueOf(keyword)));
        } else if (isMtCustomerId(keyword)) {
            logger.info("queryCustomerListByCondition, keyword={}", keyword);
            queryBuilder.must(termQuery(WmCustomerESFields.
                    CustomerBasicFields.mtCustomerId.getField(), Long.valueOf(keyword)));
        } else {
            queryBuilder.must(matchPhraseQuery(WmCustomerESFields.
                    CustomerBasicFields.customerName.getField(), keyword));
        }
        if (searchCustomerEsConditionDTO.getCustomerRealType() != null){
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(),searchCustomerEsConditionDTO.getCustomerRealType()));
        }
        if (searchCustomerEsConditionDTO.getEffective() != null){
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.effectiveForQuery.getField(),searchCustomerEsConditionDTO.getEffective().intValue()));
        }
        // 原接口不查询校园承包商
        queryBuilder.mustNot(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), CustomerRealTypeEnum.CONTRACTOR.getValue()));
        // 非客户列表页不查聚合配送商
        queryBuilder.mustNot(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue()));

        // 默认查第一页，返回30条数据
        SearchSourceBuilder searchSourceBuilder = getSearchSourceBuilder(0,MccCustomerConfig.getEsPageSize(),queryBuilder);
        SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("queryCustomerListByCondition, searchHits={}", JSONObject.toJSONString(searchHits));
        logger.info("queryCustomerListByCondition,totalHits:{} maxScore:{}", searchResponse.getHits().getTotalHits(),
                searchResponse.getHits().getMaxScore());
        return handleResonseToDbList(searchHits);

    }

    private SearchSourceBuilder getSearchSourceBuilder(Integer pageNo,Integer pageSize,BoolQueryBuilder queryBuilder){
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.from(pageNo);
        searchSourceBuilder.size(pageSize);
        searchSourceBuilder.sort(WmCustomerESFields.
                CustomerBasicFields.id.getField(), SortOrder.DESC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(600));
        return searchSourceBuilder;
    }

    /**
     * 通过客户执照名称查询承包商 （customerType为客户执照）
     *
     * @param customerName 客户执照名称
     * @return List<WmCustomerDB>
     * @throws IOException
     */
    public List<WmCustomerDB> queryContractorByCustomerName(String customerName) throws IOException {
        logger.info("WmCustomerESService.queryContractorByCustomerName, customerName={}", customerName);

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), CustomerRealTypeEnum.CONTRACTOR.getValue()));
        queryBuilder.must(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.customerName.getField(), customerName));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        searchSourceBuilder.from(0);
        //设置大小选项，确定要返回的搜索匹配数。 默认为10。
        searchSourceBuilder.size(100);
        searchSourceBuilder.sort(WmCustomerESFields.
                CustomerBasicFields.id.getField(), SortOrder.DESC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(1000));
        SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("WmCustomerESService.queryContractorByCustomerName, searchHits={}", JSONObject.toJSONString(searchHits));
        logger.info("WmCustomerESService.queryContractorByCustomerName, totalHits:{} maxScore:{}", searchResponse.getHits().getTotalHits(),
                searchResponse.getHits().getMaxScore());
        return handleResonseToDbList(searchHits);
    }

    private boolean isNormalInteger(String keyword) {
        return NumberUtils.isDigits(keyword) && keyword.length() <= (String.valueOf(Integer.MAX_VALUE).length() - 1);
    }

    private boolean isMtCustomerId(String keyword) {
        //mtCustomerId 大于10位
        return NumberUtils.isDigits(keyword) && keyword.length() > (String.valueOf(Integer.MAX_VALUE).length() - 1) && keyword.length() <= (String.valueOf(Long.MAX_VALUE).length() - 1);
    }

    /**
     * 获取客户列表（带分页数据）
     *
     * @param wmCustomerEsQueryVo
     * @return
     * @throws IOException
     */
    public Page<WmCustomerListDB> queryCustomerPage(WmCustomerEsQueryVo wmCustomerEsQueryVo) throws IOException, WmCustomerException {
        logger.info("通过ES查询：查询参数:{}", JSONObject.toJSONString(wmCustomerEsQueryVo));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(genQueryBuilderForCustomerPage(wmCustomerEsQueryVo));
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        searchSourceBuilder.from((wmCustomerEsQueryVo.getPageNo() - 1) * wmCustomerEsQueryVo.getPageSize());
        //设置大小选项，确定要返回的搜索匹配数。 默认为10。
        searchSourceBuilder.size(wmCustomerEsQueryVo.getPageSize());
        searchSourceBuilder.sort(WmCustomerESFields.
                CustomerBasicFields.id.getField(), SortOrder.DESC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(600));
        SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
        logger.info("通过ES查询 searchRequest：{}", JSON.toJSON(searchRequest));
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("totalHits:{} maxScore:{}", searchResponse.getHits().getTotalHits(),
                searchResponse.getHits().getMaxScore());
        return handleResponseToPage(wmCustomerEsQueryVo, searchResponse, searchHits);
    }

    /**
     * 根据资质编号或平台客户ID查询客户
     * 
     * @param mtCustomerId
     * @param customerNumber
     * @return
     * @throws IOException
     * @throws WmCustomerException
     */
    public List<WmCustomerDB> queryByCustomerIdOrCustomerNumber(Long mtCustomerId, String customerNumber)
            throws IOException, WmCustomerException {
        WmCustomerEsQueryVo queryVo = new WmCustomerEsQueryVo();
        queryVo.setMtCustomerId(mtCustomerId);
        queryVo.setCustomerNumber(customerNumber);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(genQueryBuilderForCustomerPage(queryVo));
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(30);
        searchSourceBuilder.sort(WmCustomerESFields.CustomerBasicFields.id.getField(), SortOrder.DESC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(600));
        SearchRequest searchRequest = new SearchRequest(new String[] {getCustomerIndex()}, searchSourceBuilder);
        logger.info("queryByCustomerIdOrCustomerNumber,通过ES查询 searchRequest：{}", JSON.toJSON(searchRequest));
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("queryByCustomerIdOrCustomerNumber,totalHits:{} maxScore:{}",
                searchResponse.getHits().getTotalHits(), searchResponse.getHits().getMaxScore());
        return handleResonseToDbList(searchHits);
    }

    /**
     * 获取客户id列表
     *
     * @param wmCustomerEsQueryVo
     * @return
     * @throws IOException
     */
    public List<String> queryCustomerIdList(WmCustomerEsQueryVo wmCustomerEsQueryVo) throws WmCustomerException, IOException {
        logger.info("通过ES查询客户列表id：查询参数:{}", JSONObject.toJSONString(wmCustomerEsQueryVo));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(genQueryBuilderForCustomerPage(wmCustomerEsQueryVo));
        searchSourceBuilder.fetchSource(new String[]{"id"}, null);
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        searchSourceBuilder.from((wmCustomerEsQueryVo.getPageNo() - 1) * wmCustomerEsQueryVo.getPageSize());
        //设置大小选项，确定要返回的搜索匹配数。 默认为10。
        searchSourceBuilder.size(wmCustomerEsQueryVo.getPageSize());
        searchSourceBuilder.sort(WmCustomerESFields.
                CustomerBasicFields.mtCustomerId.getField(), SortOrder.ASC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(600));
        SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
        logger.info("通过ES查询 searchRequest：{}", JSON.toJSON(searchRequest));
        SearchResponse searchResponse = null;
        searchResponse = restHighLevelClient.search(searchRequest);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("totalHits:{} maxScore:{}", searchResponse.getHits().getTotalHits(),
                searchResponse.getHits().getMaxScore());
        List<String> idList = new ArrayList<>();
        for (SearchHit hit : searchHits) {
            String id = hit.getId();
            idList.add(id);
        }
        return idList;
    }

    /**
     * 根据客户ID查询ES
     *
     * @param customerId 客户ID
     * @return
     * @throws IOException
     */
    public WmCustomerListDB queryCustomerByCustomerId(Integer customerId) throws IOException {
        logger.info("通过ES查询：查询参数:customerId={}", customerId);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.id.getField(), customerId));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        searchSourceBuilder.from(0);
        //设置大小选项，确定要返回的搜索匹配数。
        searchSourceBuilder.size(1);
        searchSourceBuilder.sort(WmCustomerESFields.
                CustomerBasicFields.id.getField(), SortOrder.DESC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(600));
        SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
        logger.info("通过ES查询 searchRequest：{}", JSON.toJSON(searchRequest));
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("totalHits:{} maxScore:{}", searchResponse.getHits().getTotalHits(),
                searchResponse.getHits().getMaxScore());
        return handleResonseToListDbList(searchHits);
    }

    /**
     * 根据客户ID查询ES(waimai_e_customer)
     *
     * @param customerId 客户ID
     * @return WmCustomerDB 客户相关信息
     * @throws IOException 处理异常
     */
    public WmCustomerDB queryCustomerEsInfoByCustomerId(Integer customerId) throws IOException {
        logger.info("[queryCustomerInfoByCustomerId] 通过ES查询：查询参数:customerId={}", customerId);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.id.getField(), customerId));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        searchSourceBuilder.from(0);
        //设置大小选项，确定要返回的搜索匹配数。
        searchSourceBuilder.size(1);
        searchSourceBuilder.sort(WmCustomerESFields.
                CustomerBasicFields.id.getField(), SortOrder.DESC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(600));
        SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
        logger.info("通过ES查询 searchRequest：{}", JSON.toJSON(searchRequest));
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("totalHits:{} maxScore:{}", searchResponse.getHits().getTotalHits(),
                searchResponse.getHits().getMaxScore());
        List<WmCustomerDB> result = handleResonseToDbList(searchHits);
        if (result.size() != 1) {
            return null;
        }
        return result.get(0);
    }


    private Page<WmCustomerListDB> handleResponseToPage(WmCustomerEsQueryVo wmCustomerFormDB, SearchResponse searchResponse, SearchHit[] searchHits) {
        Page<WmCustomerListDB> result = new Page(wmCustomerFormDB.getPageNo(), wmCustomerFormDB.getPageSize(), true);
        for (SearchHit searchHit : searchHits) {
            WmCustomerListDB wmCustomerDB = WmCustomerESFields
                    .CustomerBasicFields
                    .mapAsWmCustomerListDB(searchHit.getSourceAsMap());
            wmCustomerDB.setSignerPhoneNum(wmCustomerSensitiveWordsService.getReadEncryption(wmCustomerDB.getSignerPhoneNumEncryption(), KmsKeyNameEnum.PHONE_NO));
            wmCustomerDB.setSignerCertNumber(wmCustomerSensitiveWordsService.getReadEncryption(wmCustomerDB.getSignerCertNumberEncryption(), KmsKeyNameEnum.IDENTIFY_ID));
            wmCustomerDB.setSignerTypeDesc(KpSignerTypeEnum.getDescByCode(wmCustomerDB.getSignerType()));
            wmCustomerDB.setSignerCertTypeDesc(CertTypeEnum.getDescByCode(wmCustomerDB.getSignerCertType()));
            if (wmCustomerDB.getCtime() != null) {
                wmCustomerDB.setCtimeStr(DateUtil.seconds2TimeFormat(wmCustomerDB.getCtime(), DateUtil.DefaultLongFormat));
            }
            wmCustomerDB.setMultiplexDesc(CustomerMultiplexEnum.getDescByCode(wmCustomerDB.getMultiplex()));
            wmCustomerDB.setCertificateStatusDesc(CertificateStatusEnum.getDescByCode(wmCustomerDB.getCertificateStatus()));
            result.add(wmCustomerDB);
        }
        result.setTotal(searchResponse.getHits().getTotalHits());
        return result;
    }

    private BoolQueryBuilder genQueryBuilderForCustomerPage(WmCustomerEsQueryVo queryVo) throws WmCustomerException {
        //是否查询的是营业执照
        boolean isQueryCustomerTypeBusiness = false;
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtil.isNotEmpty(queryVo.getKeyword())) {
            BoolQueryBuilder innerQueryBuilder = QueryBuilders.boolQuery();
            if (isNormalInteger(queryVo.getKeyword())) {
                innerQueryBuilder.should(termQuery(WmCustomerESFields.
                        CustomerBasicFields.id.getField(), Integer.valueOf(queryVo.getKeyword())));
            }
            if (isMtCustomerId(queryVo.getKeyword())) {
                innerQueryBuilder.should(termQuery(WmCustomerESFields.
                        CustomerBasicFields.mtCustomerId.getField(), Long.valueOf(queryVo.getKeyword())));
            }

            innerQueryBuilder.should(matchPhraseQuery(WmCustomerESFields.
                    CustomerBasicFields.customerName.getField(), queryVo.getKeyword()));

            innerQueryBuilder.should(matchPhraseQuery(WmCustomerESFields.
                    CustomerBasicFields.customerNumber.getField(), queryVo.getKeyword()));
            //签约人KP的手机号
            String phoneNumToken = wmCustomerSensitiveWordsService.encryption(queryVo.getKeyword(), KmsKeyNameEnum.PHONE_NO, 0);
            if (StringUtils.isNotBlank(phoneNumToken)) {
                innerQueryBuilder.should(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.signerPhoneNumToken.getField(), phoneNumToken));
            }
            // 签约人证件号（目前当作身份证处理）
            String certNumberToken = wmCustomerSensitiveWordsService.encryption(queryVo.getKeyword(), KmsKeyNameEnum.IDENTIFY_ID, CertTypeEnum.ID_CARD.getType());
            if (StringUtils.isNotBlank(certNumberToken)) {
                innerQueryBuilder.should(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.signerCertNumberToken.getField(), certNumberToken));
            }
            //签约人名字
            innerQueryBuilder.should(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.signerCompellation.getField(), queryVo.getKeyword()));
            queryBuilder.must(innerQueryBuilder);
        }

        //是否上级客户
        if (queryVo.getIsLeaf() != CustomerConstants.CUSTOMER_IS_LEAF_ALL) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.isLeaf.getField(), queryVo.getIsLeaf()));
        }

        // 客户标签
        if (StringUtil.isNotEmpty(queryVo.getLabelIds())) {
            List<String> labelIdList = customerLabelIdParse(queryVo.getLabelIds());
            for (String labelId : labelIdList) {
                queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.customerLabel.getField(), labelId));
            }
        }

        // 客户责任人
        if (queryVo.getOwnerUid() > 0) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.ownerUid.getField(), queryVo.getOwnerUid()));
        }

        // 客户类型
        if (queryVo.getCustomerRealType() != null && queryVo.getCustomerRealType() != -1) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), queryVo.getCustomerRealType()));
        }

        if (queryVo.isHqAuthLimit()) {
            BoolQueryBuilder innerQueryBuilder = QueryBuilders.boolQuery();
            // 客户责任人列表
            if (!CollectionUtils.isEmpty(queryVo.getOwnerUidList())) {
                innerQueryBuilder.should(termsQuery(WmCustomerESFields.CustomerBasicFields.ownerUid.getField(), queryVo.getOwnerUidList().toArray()));
            }
            // 客户类型列表
            if (!CollectionUtils.isEmpty(queryVo.getCustomerRealTypeList())) {
                innerQueryBuilder.should(termsQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), queryVo.getCustomerRealTypeList().toArray()));
            }
            queryBuilder.must(innerQueryBuilder);
        } else {
            // 客户责任人列表
            if (!CollectionUtils.isEmpty(queryVo.getOwnerUidList())) {
                queryBuilder.must(termsQuery(WmCustomerESFields.CustomerBasicFields.ownerUid.getField(), queryVo.getOwnerUidList().toArray()));
            }

            // 客户类型列表
            if (!CollectionUtils.isEmpty(queryVo.getCustomerRealTypeList())) {
                queryBuilder.must(termsQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), queryVo.getCustomerRealTypeList().toArray()));
            }
        }

        // 不允许查询的客户类型
        if (!CollectionUtils.isEmpty(queryVo.getNotCustomerRealTypeList())) {
            queryBuilder.mustNot(termsQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), queryVo.getNotCustomerRealTypeList().toArray()));
        }

        //新增查询条件——证件形式（营业执照） 1-纸质,2-电子
        if (queryVo.getCertificateType() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.certificateType.getField(), queryVo.getCertificateType()));
            isQueryCustomerTypeBusiness = true;
        }
        //新增查询条件——证件状态（营业执照） 0-无,1-存续,2-注销,3-吊销
        if (queryVo.getCertificateStatus() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.certificateStatus.getField(), queryVo.getCertificateStatus()));
            isQueryCustomerTypeBusiness = true;
        }
        //新增查询条件——证件是否过期 0-未过期，1-过期子
        if (queryVo.getCertificateOverdue() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.certificateOverdue.getField(), queryVo.getCertificateOverdue()));
            isQueryCustomerTypeBusiness = true;
        }
        //新增查询条件——法人变更 0-否，1-是
        if (queryVo.getLegalPersonChange() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.legalPersonChange.getField(), queryVo.getLegalPersonChange()));
            isQueryCustomerTypeBusiness = true;
        }
        if (isQueryCustomerTypeBusiness) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.customerType.getField(), CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()));
        }

        return genQueryBuilderForCustomerPageSplit(queryVo, queryBuilder);
    }

    private BoolQueryBuilder genQueryBuilderForCustomerPageSplit(WmCustomerEsQueryVo queryVo, BoolQueryBuilder queryBuilder) throws WmCustomerException {
        // 客户平台id
        if (queryVo.getMtCustomerId() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.mtCustomerId.getField(), queryVo.getMtCustomerId()));
        }

        // 客户id
        if (queryVo.getCustomerId() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.id.getField(), queryVo.getCustomerId()));
        }

        // 客户id列表
        if (!CollectionUtils.isEmpty(queryVo.getCustomerIdList())) {
            queryBuilder.must(termsQuery(WmCustomerESFields.CustomerBasicFields.id.getField(), queryVo.getCustomerIdList().toArray()));
        }
        // 客户平台ID列表
        // 此处不需要判空，如果列表为空，则认为没有权限，只需要判断不为null即可
        if (queryVo.getMtCustomerIdList() != null){
            queryBuilder.must(QueryBuilders.termsQuery(WmCustomerESFields.CustomerBasicFields.mtCustomerId.getField(), queryVo.getMtCustomerIdList()));
        }
        // 资质编号列表
        // 此处不需要判空，如果列表为空，则认为没有权限，只需要判断不为null即可
        if (queryVo.getCustomerNumberList() != null){
            queryBuilder.must(QueryBuilders.termsQuery(WmCustomerESFields.CustomerBasicFields.customerNumber.getField(), queryVo.getCustomerNumberList()));
        }

        // 客户名称
        if (StringUtil.isNotEmpty(queryVo.getCustomerName())) {
            queryBuilder.must(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.customerName.getField(), queryVo.getCustomerName()));
        }
        //校园食堂三期客户列表查询条件增加证件类型、生效状态、承销商合作状态
        if (queryVo.getCustomerType() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.customerType.getField(), queryVo.getCustomerType()));
        }
        if (queryVo.getEffective() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.effectiveForQuery.getField(), queryVo.getEffective()));
        }
        if (queryVo.getWmCoStatus() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.wmCoStatus.getField(), queryVo.getWmCoStatus()));
            //合作状态查询条件只对食堂承包商有效
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.customerRealType.getField(), CustomerRealTypeEnum.CONTRACTOR.getValue()));
        }
        if (queryVo.getSuperCustomerId() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.superCustomerId.getField(), queryVo.getSuperCustomerId()));
        }

        if (queryVo.getSignMode() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.signMode.getField(), queryVo.getSignMode()));
        }

        //新增查询条件customerNumber--
        if (StringUtils.isNotEmpty(queryVo.getCustomerNumber())) {
            queryBuilder.must(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.customerNumber.getField(), queryVo.getCustomerNumber()));
        }
        //查询条件——签约人类型
        if (queryVo.getSignerType() != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.signerType.getField(), queryVo.getSignerType()));
        }
        //查询条件——签约人的手机号
        if (StringUtils.isNotBlank(queryVo.getSignerPhoneNumToken())) {
            queryBuilder.must(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.signerPhoneNumToken.getField(), queryVo.getSignerPhoneNumToken()));
        }
        if (MccCustomerConfig.listCustomerBySignerCertTypeSwitch()) {
            if (queryVo.getSignerCertType() != null) {
                queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.signerCertType.getField(), queryVo.getSignerCertType()));
            }
        }
        //查询条件——签约人的证件类型&签约人KP的证件号(签约人证件类型和证件号两个查询条件是绑定关系，两个条件都录入的时候才生效，否则认定为不生效)
        if (queryVo.getSignerCertType() != null && StringUtils.isNotBlank(queryVo.getSignerCertNumberToken())) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.signerCertType.getField(), queryVo.getSignerCertType()));
            queryBuilder.must(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.signerCertNumberToken.getField(), queryVo.getSignerCertNumberToken()));
        }
        //查询条件——创建时间（开始时间和结束时间都录入才算做生效）
        if (queryVo.getCtimeBegin() != null && queryVo.getCtimeEnd() != null) {
            queryBuilder.must(rangeQuery(WmCustomerESFields.CustomerBasicFields.ctime.getField())
                    .from(queryVo.getCtimeBegin(), true).to(queryVo.getCtimeEnd(), true));
        }
        //查询条件——签约人KP的名字
        if (StringUtils.isNotBlank(queryVo.getSignerCompellation())) {
            queryBuilder.must(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.signerCompellation.getField(), queryVo.getSignerCompellation()));
        }
        //客户复用查询
        CustomerMultiplexEnum customerMultiplexEnum = CustomerMultiplexEnum.getByType(queryVo.getMultiplex());
        if (customerMultiplexEnum != null) {
            if (CustomerMultiplexEnum.YES.equals(customerMultiplexEnum)) {
                queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.multiplex.getField(), queryVo.getMultiplex()));
            } else {
                queryBuilder.mustNot(termQuery(WmCustomerESFields.CustomerBasicFields.multiplex.getField(), CustomerMultiplexEnum.YES.getType()));
            }
        }
        // 查询条件-门店关联门店数量
        if (queryVo.getPoiCountStart() != null && queryVo.getPoiCountEnd() != null) {
            queryBuilder.must(rangeQuery(WmCustomerESFields.CustomerBasicFields.wmPoiCount.getField()).from(queryVo.getPoiCountStart()).to(queryVo.getPoiCountEnd()));
        }

        // 业务线
        if (queryVo.getBizOrgCode() != null && queryVo.getBizOrgCode().intValue() > 0) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.bizOrgCode.getField(), queryVo.getBizOrgCode()));
        }
        return queryBuilder;
    }

    private List<String> customerLabelIdParse(String labelIds) {
        if (StringUtil.isEmpty(labelIds)) {
            return null;
        }
        List<String> labelIdList = Arrays.stream(labelIds.split(",")).collect(Collectors.toList());
        return labelIdList;
    }

    /**
     * 根据签约人KP手机号或证件类型+证件号查询ES得到KP列表
     *
     * @param signerKpQueryParam 请求入参
     * @return response
     * @throws IOException java.io.IOException
     * @throws WmCustomerException com.sankuai.meituan.waimai.thrift.exception.WmCustomerException
     */
    public SignerKpQueryResponse queryKpListByCustomerKpInfo(SignerKpQueryParam signerKpQueryParam) throws IOException, WmCustomerException {
        logger.info("根据签约人KP手机号或证件号查询ES，查询参数 signerKpQueryParam={}", JSONObject.toJSONString(signerKpQueryParam));

        if (signerKpQueryParam == null) {
            logger.info("queryKpListByCustomerKpInfo入参校验失败: 签约人KP入参signerKpQueryParam为空");
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "签约人KP入参为空");
        }
        // 证件类型和证件号要么同时传入，要么均为空
        if ((StringUtils.isNotBlank(signerKpQueryParam.getCertNumber()) && signerKpQueryParam.getCertType() == null) || (StringUtils.isBlank(signerKpQueryParam.getCertNumber()) && signerKpQueryParam.getCertType() != null)) {
            logger.info("queryKpListByCustomerKpInfo入参校验失败: 证件类型和证件号须同时传入");
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "证件类型和证件号须同时传入");
        }
        // 手机号和证件号至少应有其一
        if (StringUtils.isBlank(signerKpQueryParam.getCertNumber()) && StringUtils.isBlank(signerKpQueryParam.getPhoneNum())) {
            logger.info("queryKpListByCustomerKpInfo入参校验失败: 手机号和证件号至少应有其一");
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "手机号和证件号至少应有其一");
        }
        // pageNo与pageSize入参校验
        if (signerKpQueryParam.getPageNo() <= 0 || signerKpQueryParam.getPageSize() < 0 || signerKpQueryParam.getPageSize() > 10000) {
            logger.info("queryKpListByCustomerKpInfo入参校验失败: pageNo最小为1且pageSize范围是0-10000");
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "pageNo最小为1且pageSize范围是0-10000");
        }
        // pageSize建议值校验
        if (signerKpQueryParam.getPageSize() > ConfigUtilAdapter.getInt("queryKpListByCustomerKpInfo_pageSize", 200)) {
            logger.info("queryKpListByCustomerKpInfo入参校验失败: pageSize超过建议值{}", ConfigUtilAdapter.getInt("queryKpListByCustomerKpInfo_pageSize", 200));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "pageSize超过建议值");
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(genQueryBuilderForKpList(signerKpQueryParam));
        // 设置from选项，确定要开始搜索的结果索引。默认为0。
        searchSourceBuilder.from((signerKpQueryParam.getPageNo() - 1) * signerKpQueryParam.getPageSize());
        // 设置大小选项，确定要返回的搜索匹配数。 默认为10。
        searchSourceBuilder.size(signerKpQueryParam.getPageSize());
        searchSourceBuilder.sort(WmCustomerESFields.CustomerBasicFields.id.getField(), SortOrder.DESC);
        searchSourceBuilder.timeout(TimeValue.timeValueMillis(600));
        SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
        logger.info("queryKpListByCustomerKpInfo 通过ES查询 searchRequest：{}", JSON.toJSON(searchRequest));
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
        if(searchResponse == null || searchResponse.getHits() == null) {
            return new SignerKpQueryResponse();
        }
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        logger.info("queryKpListByCustomerKpInfo ES查询总数 totalHits:{} maxScore:{}", searchResponse.getHits().getTotalHits(),
                searchResponse.getHits().getMaxScore());
        // 数据处理与组装
        SignerKpQueryResponse response = new SignerKpQueryResponse();
        response.setTotal(searchResponse.getHits().getTotalHits());
        response.setList(handleResponseToKpList(searchHits));
        return response;
    }

    private BoolQueryBuilder genQueryBuilderForKpList(SignerKpQueryParam signerKpQueryParam) throws WmCustomerException {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        // 校验签约人手机号与签约人证件号是否能生成token
        checkPhoneNumTokenAndCertNumTokenIsEmpty(signerKpQueryParam.getPhoneNum(), signerKpQueryParam.getCertNumber(), signerKpQueryParam.getCertType());
        // 签约人KP手机号
        String phoneNumToken = new String();
        if (StringUtils.isNotBlank(signerKpQueryParam.getPhoneNum())) {
            phoneNumToken = wmCustomerSensitiveWordsService.encryption(signerKpQueryParam.getPhoneNum(), KmsKeyNameEnum.PHONE_NO, 0);
            if (StringUtils.isNotBlank(phoneNumToken)) {
                logger.info("genQueryBuilderForKpList: 手机号phoneNumber={}, phoneNumberToken={}有效，作为ES查询条件", signerKpQueryParam.getPhoneNum(), phoneNumToken);
                queryBuilder.must(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.signerPhoneNumToken.getField(), phoneNumToken));
            }
        }
        // 签约人KP证件号和证件类型(签约人证件类型和证件号两个查询条件是绑定关系，两个条件都录入的时候才生效，否则认定为不生效)
        String certNumberToken = new String();
        if (StringUtils.isNotBlank(signerKpQueryParam.getCertNumber())) {
            certNumberToken = wmCustomerSensitiveWordsService.encryption(signerKpQueryParam.getCertNumber(), KmsKeyNameEnum.IDENTIFY_ID, signerKpQueryParam.getCertType());
            if (signerKpQueryParam.getCertType() != null && StringUtils.isNotBlank(certNumberToken)) {
                logger.info("genQueryBuilderForKpList: 证件号与证件类型certType={}, certNumber={}, certNumberToken={}有效，作为ES查询条件", signerKpQueryParam.getCertType(), signerKpQueryParam.getCertNumber(), certNumberToken);
                queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.signerCertType.getField(), signerKpQueryParam.getCertType()));
                queryBuilder.must(matchPhraseQuery(WmCustomerESFields.CustomerBasicFields.signerCertNumberToken.getField(), certNumberToken));
            }
        }

        if (StringUtils.isBlank(phoneNumToken) && StringUtils.isBlank(certNumberToken)) {
            logger.info("genQueryBuilderForKpList: 证件号和手机号生成token均为空，查询失败");
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "证件号和手机号生成token均为空，查询失败");
        }
        return queryBuilder;
    }

    public List<SignerKpDTO> handleResponseToKpList(SearchHit[] searchHits) {
        List<SignerKpDTO> result = new ArrayList<>();
        for (SearchHit searchHit : searchHits) {
            WmCustomerListDB wmCustomerDB = WmCustomerESFields
                    .CustomerBasicFields
                    .mapAsWmCustomerListDB(searchHit.getSourceAsMap());
            SignerKpDTO signerKpDTO = new SignerKpDTO();
            signerKpDTO.setPhoneNum(wmCustomerSensitiveWordsService.getReadEncryption(wmCustomerDB.getSignerPhoneNumEncryption(), KmsKeyNameEnum.PHONE_NO));
            signerKpDTO.setPhoneNumToken(wmCustomerDB.getSignerPhoneNumToken());
            signerKpDTO.setPhoneNumEncryption(wmCustomerDB.getSignerPhoneNumEncryption());
            signerKpDTO.setCertNumber(wmCustomerSensitiveWordsService.getReadEncryption(wmCustomerDB.getSignerCertNumberEncryption(), KmsKeyNameEnum.IDENTIFY_ID));
            signerKpDTO.setCertNumberToken(wmCustomerDB.getSignerCertNumberToken());
            signerKpDTO.setCertNumberEncryption(wmCustomerDB.getSignerCertNumberEncryption());
            signerKpDTO.setId(wmCustomerDB.getSignerId());
            signerKpDTO.setCertType(wmCustomerDB.getSignerCertType());
            signerKpDTO.setMtCustomerId(wmCustomerDB.getMtCustomerId());
            signerKpDTO.setCompellation(wmCustomerDB.getSignerCompellation());
            signerKpDTO.setSignerType(wmCustomerDB.getSignerType());
            signerKpDTO.setCustomerId(wmCustomerDB.getId());
            result.add(signerKpDTO);
        }
        logger.info("queryKpListByCustomerKpInfo查询结果 result={}, total={}", JSON.toJSONString(result), searchHits.length);
        return result;
    }

    /**
     * 校验签约人手机号与签约人证件号是否能生成token
     *
     * @param phoneNumber 签约人手机号
     * @param certNumber 签约人证件号
     * @param certType 签约人证件类型
     * @throws WmCustomerException com.sankuai.meituan.waimai.thrift.exception.WmCustomerException
     */
    public void checkPhoneNumTokenAndCertNumTokenIsEmpty(String phoneNumber, String certNumber, Integer certType) throws WmCustomerException {
        // 校验签约人手机号能否生成token
        if (StringUtils.isNotBlank(phoneNumber)) {
            String phoneNumToken =  wmCustomerSensitiveWordsService.encryption(phoneNumber, KmsKeyNameEnum.PHONE_NO, 0);
            if (StringUtils.isBlank(phoneNumToken)) {
                logger.error("[checkPhoneNumTokenAndCertNumTokenIsEmpty] 手机号phoneNumber={}生成token为空", phoneNumber);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "签约人手机号无效");
            }
        }
        // 校验签约人证件号能否生成token
        if (StringUtils.isNotBlank(certNumber) && certType != null) {
            String certNumberToken = wmCustomerSensitiveWordsService.encryption(certNumber, KmsKeyNameEnum.IDENTIFY_ID, certType);
            if (certNumberToken == null) {
                logger.error("[checkPhoneNumTokenAndCertNumTokenIsEmpty] 证件号与证件类型certType={}, certNumber={}生成token为空",certNumber, certType);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "签约人证件号码无效");
            }
        }
    }

    /**
     * 根据客户ID物理删除ES
     * @param customerIdList
     * @return
     */
    public List<Integer> deleteWmCustomerEs(List<Integer> customerIdList) {
        List<Integer> errIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(customerIdList)) {
            return errIds;
        }
        Map<Integer, Integer> customerMap = Maps.newHashMap();
        List<List<Integer>> idList = Lists.partition(customerIdList, 100);
        for (List<Integer> ids : idList) {
            List<WmCustomerDB> wmCustomerDBList = wmCustomerDBMapper.getNotLimitValidByCustomerIds(customerIdList);
            if (!CollectionUtils.isEmpty(wmCustomerDBList)) {
                customerMap = wmCustomerDBList.stream().collect(Collectors.toConcurrentMap(WmCustomerDB::getId, wmCustomerDB -> wmCustomerDB.getValid()));
            }
            for (Integer customerId : customerIdList) {
                Integer valid = customerMap.get(customerId);
                if (valid != null && valid.intValue() == CustomerConstants.VALID) {
                    logger.error("deleteWmCustomerEs 客户信息有效不能删除 customerId={}", customerId);
                    continue;
                }
                try {
                    syncToDelEs(customerId);
                } catch (TimeoutException e) {
                    errIds.add(customerId);
                    logger.warn("同步删除客户es数据失败，超时了 customerId={}", customerId, e);
                } catch (Exception e) {
                    errIds.add(customerId);
                    logger.error("同步删除客户es数据失败 customerId={}", customerId, e);
                }
            }
        }
        return errIds;
    }

    /**
     * 根据用户ID和是否生效查询负责客户总数量
     *
     * @param ownerUId
     * @param effective
     * @return
     */
    public Long getCountByOwnerUIdAndEffective(Integer ownerUId, Integer effective) throws WmCustomerException{
        try {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(getBuilderByOwnerUIdAndEffective(ownerUId, effective));
            searchSourceBuilder.sort(WmCustomerESFields.
                    CustomerBasicFields.utime.getField(), SortOrder.DESC);
            searchSourceBuilder.size(20);
            SearchRequest searchRequest = new SearchRequest(new String[]{getCustomerIndex()}, searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
            logger.info("getCountByOwnerUIdAndEffective,totalHits:{}", searchResponse.getHits().getTotalHits());
            if (searchResponse == null || searchResponse.getHits() == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "ES查询结果异常");
            }
            return searchResponse.getHits().getTotalHits();
        } catch (Exception e) {
            logger.error("getCountByOwnerUIdAndEffective,查询ES发生异常,ownerUId={},effective={}", ownerUId, effective, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
    }

    /**
     * 根据参数组装查询Builer
     *
     * @param ownerUId  客户责任人UID
     * @param effective 客户生效状态
     * @return
     */
    private BoolQueryBuilder getBuilderByOwnerUIdAndEffective(Integer ownerUId, Integer effective) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.ownerUid.getField(),
                ownerUId.intValue()));
        if (effective != null) {
            queryBuilder.must(termQuery(WmCustomerESFields.CustomerBasicFields.effective.getField(),
                    effective.intValue()));
        }
        return queryBuilder;
    }
}
