package com.sankuai.meituan.waimai.customer.contract.dao;

import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignWithOutSignPhoneDB;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmTempletContractSignAuditedDBMapper {

    int insert(WmTempletContractSignDB record);

    int insertSelective(WmTempletContractSignDB record);

    WmTempletContractSignDB selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmTempletContractSignDB record);

    int updateByPrimaryKey(WmTempletContractSignDB record);

    List<WmTempletContractSignDB> selectByWmTempletContractId(@Param("wmTempletContractId") Long wmTempletContractId);

    List<WmTempletContractSignDB> selectByWmTempletContractIdRT(@Param("wmTempletContractId") Long wmTempletContractId);

    List<WmTempletContractSignWithOutSignPhoneDB> selectByWmTempletContractIdWithOutSignPhone(@Param("wmTempletContractId") Long wmTempletContractId);

    List<WmTempletContractSignDB> selectByWmTempletContractIdMaster(@Param("wmTempletContractId") Long wmTempletContractId);

    int batchInsertOrUpdate(@Param("signDbList") List<WmTempletContractSignDB> signDbList);

    int invalid(@Param("templetContractId") Long templetContractId, @Param("opuid") Integer opuid);

    int deleteByTempletId(@Param("templetContractId") Long templetContractId);

    List<WmTempletContractSignDB> selectByWmTempletContractIdList(@Param("templetIdList") List<Long> templetIdList);

    Integer queryLastIndex();

    List<WmTempletContractSignDB> queryEntityListWithLabel4Encryption(@Param("lastId") long lastId, @Param("size") int size);

    int updateOriginalRecordById(Long id);

    void batchUpdateOriginalRecordByIds(@Param("ids") List<Long> ids);
}