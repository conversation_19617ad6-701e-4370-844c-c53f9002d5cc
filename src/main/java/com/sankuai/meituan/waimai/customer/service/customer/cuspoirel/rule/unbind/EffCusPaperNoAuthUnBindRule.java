package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.unbind;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.CustomerPoiRelStrategyBean;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.UnBindStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IUnBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IUnBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.unbind.PoiUnBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IUnBindCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IUnBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.poirel.flow.CustomerPoiRelFlowEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240122
 * @desc 生效客户&纸质签约&无纸质客户解绑权限 解绑策略
 */
@Service
@Slf4j
@Rule
public class EffCusPaperNoAuthUnBindRule {

    /**
     * 生效客户&纸质签约&无纸质客户解绑权限
     *
     * @param context
     * @return
     */
    @Condition
    public boolean when(@Fact("context") CustomerPoiUnBindFlowContext context) {
        return context.getWmCustomerDB().getEffective() == 1
                && context.getSignMode() == CustomerSignMode.PAPER.getCode()
                && !context.isHasPaperCustomerUnBindAuth();
    }

    /**
     * 根据策略执行
     *
     * @return
     */
    @Action
    public void execute(@Fact("context") CustomerPoiUnBindFlowContext context) throws WmCustomerException, TException {
        log.info("EffCusPaperNoAuthUnBindRule.execute,命中生效纸质客户无纸质客户解绑权限的解绑规则,customerId={},wmPoiIdSet={}", context.getCustomerId(), JSON.toJSONString(context.getWmPoiIdSet()));

        List<UnBindFlowStrategy> unBindFlowStrategyList = Lists.newArrayList();
        //解绑场景设置为直接解绑
        context.setCustomerPoiUnBindTypeEnum(CustomerPoiUnBindTypeEnum.DIRECT_UNBIND);

        // 取生效分成，结算的并集
        Set<Long> effectiveSet = context.getExistEffSettleWmPoiIdSet();
        //纸质客户无纸质客户解绑签约，存在生效的结算或分成不能解绑，需要返回提示
        if (CollectionUtils.isNotEmpty(effectiveSet)) {
            context.setUnbindTips(String.format(CustomerConstants.CUSTOMER_POI_UNBIND_NEED_AUTH,
                    JSONObject.toJSONString(effectiveSet)));
        }
        // 门店ID排除生效分成，结算并集
        Set<Long> unEffectiveSet = Sets.difference(context.getWmPoiIdSet(), effectiveSet);

        //构建可以直接解绑的策略
        if (CollectionUtils.isNotEmpty(unEffectiveSet)) {
            unBindFlowStrategyList.add(getDirectUnBindStrategy(unEffectiveSet));
        }
        context.setUnBindFlowStrategyList(unBindFlowStrategyList);
    }


    @Priority
    public int getPriority() {
        return 4;
    }


    /**
     * 构建直接解绑策略
     *
     * @param wmPoiIdSet
     * @return
     */
    private UnBindFlowStrategy getDirectUnBindStrategy(Set<Long> wmPoiIdSet) {
        //定义解绑各层级使用策略bean
        CustomerPoiRelStrategyBean customerPoiRelStrategyBean = CustomerPoiRelStrategyBean.builder()
                .checkBeanName("poiUnBindCheckStrategy")
                .preCoreBeanName("directUnBindPreCoreStrategy")
                .coreBeanName("directUnBindCoreStrategy")
                .afterBeanName("directUnBindAfterStrategy")
                .build();
        //根据策略bean构建解绑策略
        UnBindStrategy strategy = UnBindStrategy.buildUnBindStrategyByBean(customerPoiRelStrategyBean);
        //根据解绑策略构建解绑流程策略
        UnBindFlowStrategy unBindFlowStrategy = UnBindFlowStrategy.builder()
                .flowEnum(CustomerPoiRelFlowEnum.DIRECT_UNBIND)
                .wmPoiIdSet(wmPoiIdSet)
                .unBindStrategy(strategy)
                .build();
        return unBindFlowStrategy;
    }

}
