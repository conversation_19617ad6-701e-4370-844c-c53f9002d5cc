package com.sankuai.meituan.waimai.customer.service.kp.preverifier;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.DifferentCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 签约人KP操作校验
 * <AUTHOR>
 */
@Component
public class SignerKpOperatePreVerify implements KpPreverifier {

    private static final Logger LOGGER = LoggerFactory.getLogger(SignerKpOperatePreVerify.class);

    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Override
    public void verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldWmCustomerKpList, List<WmCustomerKp> addKpList, List<WmCustomerKp> deleteKpList, List<WmCustomerKp> upgradeKpList) throws WmCustomerException {
        //更新的签约人KP
        WmCustomerKp signerKpByUpdate = differentCustomerKpService.getSignerKp(upgradeKpList);
        if (signerKpByUpdate != null) {
            WmCustomerKp signerKpOld = differentCustomerKpService.getSignerKp(oldWmCustomerKpList);
            if (signerKpOld == null) {
                LOGGER.error("出问题了，更新签约人KP，居然没有旧的签约人KP，kpId：" + signerKpByUpdate.getId());
                ThrowUtil.throwSeverError("出问题了，请联系RD排查！");
            }

            if (signerKpByUpdate.getId() != signerKpOld.getId()) {
                ThrowUtil.throwClientError("更新的签约人KP与旧的签约人KP不一致");
            }

            //签约人KP更新的字段
            List<WmCustomerDiffCellBo> kpUpdateFields = differentCustomerKpService.getSignerKpUpdateFields(signerKpOld, signerKpByUpdate);
            LOGGER.info("kpUpdateFields={}", JSON.toJSONString(kpUpdateFields));
            if (!CollectionUtils.isEmpty(kpUpdateFields)) {
                //签约人只读状态校验
                KpSignerStateMachine oldState = null;
                //如果KP是生效中状态，取临时表状态
                byte oldSignerState = signerKpOld.getState();
                if (oldSignerState == KpSignerStateMachine.EFFECT.getState()) {
                    WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpId(signerKpOld.getId());
                    wmCustomerSensitiveWordsService.readKpWhenSelect(kpTempDB);
                    if (kpTempDB != null) {
                        oldState = KpSignerStateMachine.getByState(kpTempDB.getState());
                    } else {
                        oldState = KpSignerStateMachine.getByState(oldSignerState);
                    }
                } else {
                    oldState = KpSignerStateMachine.getByState(oldSignerState);
                }

                if (oldState == null) {
                    LOGGER.error("KP签约人状态不合法，id:{}, state:{}", signerKpOld.getId(), oldSignerState);
                    ThrowUtil.throwSeverError("KP签约人状态不合法，id:" + signerKpOld.getId());
                }

                if (oldState.isReadOnly()) {
                    ThrowUtil.throwSeverError(String.format("签约人KP%s，不允许修改", oldState.getDes()));
                }

            }
        }

    }

    @Override
    public int order() {
        return 4;
    }

}
