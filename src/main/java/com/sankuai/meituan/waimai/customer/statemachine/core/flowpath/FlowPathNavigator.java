package com.sankuai.meituan.waimai.customer.statemachine.core.flowpath;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.statemachine.api.FlowTrait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: jinhu  Date: 16/8/11 Time: 下午4:30
 */
public class FlowPathNavigator {

    private static final Logger logger = LoggerFactory.getLogger(FlowPathNavigator.class);

    private final FlowDefinition flowDefinition;

    public FlowPathNavigator(FlowDefinition flowDefinition) {
        this.flowDefinition = flowDefinition;
    }

    public boolean hasPath(FlowTrait flow, String targetStatus) {
        logger.info(JSON.toJSONString(flow));
        FlowStateDefinition fromState = this.flowDefinition.getFlowStateDefinition(flow.currentState());
        FlowStateDefinition toState = this.flowDefinition.getFlowStateDefinition(targetStatus);
        if (toState == null) {
            return false;
        }

        FlowConnectionDefinition connectionDefinition = fromState.getFlowConnectionDefinition(toState);
        return connectionDefinition != null;

    }
}
