package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmEcontractQueryParam;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractTemplet;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractTempletWithBLOBs;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public interface WmEcontractTempletMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(WmEcontractTempletWithBLOBs record);

    int insertSelective(WmEcontractTempletWithBLOBs record);

    WmEcontractTempletWithBLOBs selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(WmEcontractTempletWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(WmEcontractTempletWithBLOBs record);

    int updateByPrimaryKey(WmEcontractTemplet record);

    List<WmEcontractTemplet> queryBasicByParam(WmEcontractQueryParam queryParam);

    List<WmEcontractTempletWithBLOBs> queryByParam(WmEcontractQueryParam queryParam);

    int updateStatusByParam(WmEcontractQueryParam queryParam);

    int updateByBizInfo(WmEcontractTempletWithBLOBs record);
}