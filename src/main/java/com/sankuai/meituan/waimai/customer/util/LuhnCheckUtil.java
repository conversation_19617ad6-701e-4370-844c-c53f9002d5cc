package com.sankuai.meituan.waimai.customer.util;

/**
 * @title LuhnCheckUtil
 * @description 支付卡校验位的算法 The Luhn Mod-10 Method
 * @version 1.0
 */
public class LuhnCheckUtil {
    public static boolean luhnCheck(String cardNo) {
        int sum = 0;
        int digit = 0;
        int addend = 0;
        boolean timesTwo = false;
        for (int i = cardNo.length() - 1; i >= 0; i--) {
            digit = Integer.parseInt(cardNo.substring(i, i + 1));
            if (timesTwo) {
                addend = digit * 2;
                if (addend > 9) {
                    addend -= 9;
                }
            } else {
                addend = digit;
            }
            sum += addend;
            timesTwo = !timesTwo;
        }
        int modulus = sum % 10;
        return modulus == 0;
    }

}
