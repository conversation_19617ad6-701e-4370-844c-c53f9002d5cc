package com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 绑定签约回调请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BindSignNoticeDTO {

    /**
     * 绑定关联的签约任务ID
     */
    private Long signBindRelTaskId;

    /**
     * 签约结果
     * com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum
     */
    private Integer signResult;

    /**
     * 签约绑定短信记录
     */
    private WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB;
}
