package com.sankuai.meituan.waimai.customer.contract.config.dto;

import lombok.Data;

/**
 * @description: 合同有效期相关属性
 * @author: liuyunjie05
 * @create: 2024/5/21 10:23
 */
@Data
public class ContractValidityPropertyDTO {

    // 是否支持长期有效
    private Boolean supportLongTermEffective;

    // 是否支持设置有效期
    private Boolean supportValidity;

    // 最早可选多久
    private TimePropertyDTO leftInterval;

    // 最晚可选多久
    private TimePropertyDTO rightInterval;

}
