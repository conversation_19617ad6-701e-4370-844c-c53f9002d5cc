package com.sankuai.meituan.waimai.customer.contract.dao;

import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface WmTempletContractAuditedDBMapper {

    int insert(WmTempletContractDB record);

    int insertSelective(WmTempletContractDB record);

    WmTempletContractDB selectByPrimaryKey(Long id);

    WmTempletContractDB selectByIdMaster(Long id);

    int updateByPrimaryKeySelective(WmTempletContractDB record);

    List<WmTempletContractDB> listWmTempletContract(@Param("startDueDate") Long startDueDate,@Param("endDueDate") Long endDueDate, @Param("type") List<Integer> type, @Param("startId") Long startId,@Param("size") Integer size);

    int updateStatus(@Param("parentId") Long parentId, @Param("type") Integer type, @Param("status") Integer status, @Param("opuid") Integer opuid);

    WmTempletContractDB selectByParentIdAndType(@Param("parentId") Long parentId, @Param("type") Integer type);

    List<WmTempletContractDB> selectListByParentIdAndType(@Param("parentId") Long parentId, @Param("type") Integer type);

    WmTempletContractDB selectByParentIdAndTypeFromMaster(@Param("parentId") Long parentId, @Param("type") Integer type);

    int selectTypeByPrimaryKey(Long id);

    int invalidContract(@Param("templetContractId") Long parentId, @Param("opuid") Integer opuid);

    List<WmTempletContractDB> getBasicListWhenDueDateBefore(@Param("unixTime") int unixTime, @Param("size") int size);

    List<WmTempletContractDB> getBasicListWhenDueDateEquals(@Param("unixTime") int unixTime, @Param("startPageNum") int startPageNum, @Param("pageSize") int pageSize);

    List<WmTempletContractDB> getBasicListByParentIdAndTypes(@Param("parentId") int parentId, @Param("type") List<Integer> type);

    List<WmTempletContractDB> getBasicListByParentIdAndTypesMaster(@Param("parentId") long parentId, @Param("type") List<Integer> type);

    List<Long> getTempletIdByUtime(@Param("startTime") Integer startTime, @Param("endTime") Integer endTime);

    List<WmTempletContractDB> selectByParentIdAndTypes(@Param("parentId") Long parentId, @Param("types") List<Integer> types);

    List<WmTempletContractDB> selectByParentIdAndTypesMaster(@Param("parentId") Long parentId, @Param("types") List<Integer> types);


    List<WmTempletContractDB> selectValidByParentIdAndTypes(@Param("parentId") Long parentId, @Param("types") List<Integer> types);

    List<WmTempletContractDB> selectByParentIdsAndTypes(@Param("parentIds") List<Long> parentIds, @Param("types") List<Integer> types);

    List<WmTempletContractDB> selectByParentIdAndTypesAndStatus(@Param("parentId") Long parentId, @Param("types") List<Integer> types, @Param("status") Integer status);

    List<WmTempletContractDB> selectC2ContractListByAgentId(@Param("agentId") Integer agentId);

    List<WmTempletContractDB> selectByPrimaryKeyList(@Param("ids") List<Long> ids);

    List<WmTempletContractDB> selectByPrimaryKeyListWithoutValid(@Param("ids") List<Long> ids);


    int getLatestC1ClosingDate(@Param("customerIdSet")Set<Long> customerIdSet);


    void expiredContract(@Param("contractId") long contractId, @Param("opUid") Integer opUid);
}