package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.PdfContentInfoBoMakerTransUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 优惠申请书
 */
@Service
public class WmEcontractPoiSupportDataWrapperService {

    private static final Logger LOG = LoggerFactory.getLogger(WmEcontractPoiSupportDataWrapperService.class);

    private static final String TEMPLET_NAME = "poilist_discount_info";
    private static final String POILIST_DISCOUNT_INFO_V2 = "poilist_discount_info_v2";

    public static final String SUPPORT_MARK = "hasSupport";
    public static final String ORIGIN_SUPPORT_MARK = "support";

    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo, Map<String, String> subjectEstampMap)
            throws IllegalAccessException, WmCustomerException {

        EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo =
                JSON.parseObject(taskBo.getApplyContext(), EcontractBatchPoiInfoExtBo.class);

        if(MccConfig.batchPoiDataWrapperDeliveryUseNewData()){
            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            pdfInfoBo.setPdfTemplateName(POILIST_DISCOUNT_INFO_V2);
            pdfInfoBo.setPdfBizContent(generatePdfBizeObjectNew(econtractBatchPoiInfoExtBo));
            pdfInfoBo.setPdfMetaContent(generatePdfMetaeObject(contextBo, econtractBatchPoiInfoExtBo, subjectEstampMap));
            return Lists.newArrayList(pdfInfoBo);
        }else{
            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            pdfInfoBo.setPdfTemplateName(TEMPLET_NAME);
            pdfInfoBo.setPdfBizContent(generatePdfBizeObject(econtractBatchPoiInfoExtBo));
            pdfInfoBo.setPdfMetaContent(generatePdfMetaeObject(contextBo, econtractBatchPoiInfoExtBo, subjectEstampMap));
            return Lists.newArrayList(pdfInfoBo);
        }
    }

    /**
     * 生成pdf内容
     */
    private List<Map<String, String>> generatePdfBizeObject(EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo) {

        List<Map<String, String>> listMap = Lists.newArrayList();
        if (econtractBatchPoiInfoExtBo != null) {
            List<EcontractPoiInfoExtBo> econtractPoiInfoExtBoList = econtractBatchPoiInfoExtBo.getEcontractPoiInfoExtBoList();
            if (CollectionUtils.isNotEmpty(econtractPoiInfoExtBoList)) {
                for (EcontractPoiInfoExtBo econtractPoiInfoExtBo : econtractPoiInfoExtBoList) {
                    EcontractDeliveryInfoBo econtractDeliveryInfoBo = econtractPoiInfoExtBo.getEcontractDeliveryInfoBo();
                    if (econtractDeliveryInfoBo == null) {
                        LOG.info("generatePdfBizeObject 无配送信息{}", JSON.toJSONString(econtractPoiInfoExtBo));
                        continue;
                    }
                    if (SUPPORT_MARK.equalsIgnoreCase(econtractDeliveryInfoBo.getSupportExclusive())) {
                        Map<String, String> pdfMap = Maps.newHashMap();
                        pdfMap.put("address", StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getAddress(), StringUtils.EMPTY));
                        pdfMap.put("poiName", StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getPoiName(), StringUtils.EMPTY));
                        pdfMap.put("validate", StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getValidate(), StringUtils.EMPTY));
                        pdfMap.put("other", StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getOther(), StringUtils.EMPTY));
                        pdfMap.put("supportExclusive", StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getSupportExclusive(), StringUtils.EMPTY));
                        pdfMap.put("exclusiveFee", StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getExclusiveFee(), StringUtils.EMPTY));
                        pdfMap.put("serverSupport", StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getServerSupport(), StringUtils.EMPTY));
                        pdfMap.put("deposit", StringUtils.defaultIfEmpty(econtractDeliveryInfoBo.getDeposit(), StringUtils.EMPTY));
                        listMap.add(pdfMap);
                    }
                }
            }
        }
        return listMap;
    }

    private List<Map<String, String>> generatePdfBizeObjectNew(EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo) throws IllegalAccessException {
        List<Map<String, String>> listMap = Lists.newArrayList();
        if (econtractBatchPoiInfoExtBo != null) {
            List<EcontractPoiInfoExtBo> econtractPoiInfoExtBoList = econtractBatchPoiInfoExtBo.getEcontractPoiInfoExtBoList();
            if (CollectionUtils.isNotEmpty(econtractPoiInfoExtBoList)) {
                for (EcontractPoiInfoExtBo econtractPoiInfoExtBo : econtractPoiInfoExtBoList) {
                    EcontractDeliveryInfoBo econtractDeliveryInfoBo = econtractPoiInfoExtBo.getEcontractDeliveryInfoBo();
                    if (econtractDeliveryInfoBo == null) {
                        LOG.info("generatePdfBizeObject 无配送信息{}", JSON.toJSONString(econtractPoiInfoExtBo));
                        continue;
                    }
                    if (ORIGIN_SUPPORT_MARK.equalsIgnoreCase(econtractDeliveryInfoBo.getSupportExclusive())) {
                        Map<String, String> pdfMap = Maps.newHashMap();
                        pdfMap.put("address", StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getAddress(), StringUtils.EMPTY));
                        pdfMap.put("poiName", StringUtils.defaultIfEmpty(econtractPoiInfoExtBo.getPoiName(), StringUtils.EMPTY));
                        pdfMap.putAll(MapUtil.Object2Map(econtractDeliveryInfoBo));
                        PdfContentInfoBoMakerTransUtil.extractDeliveryAggregationVersion(econtractDeliveryInfoBo, pdfMap);
                        PdfContentInfoBoMakerTransUtil.extractAggregationExclusiveFeeDiscountFactor2(econtractDeliveryInfoBo, pdfMap);
                        listMap.add(pdfMap);
                    }
                }
            }
        }
        return listMap;
    }

    /**
     * 生成签章内容
     */
    private Map<String, String> generatePdfMetaeObject(EcontractBatchContextBo contextBo,
            EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo, Map<String, String> subjectEstampMap) {
        Map<String, String> pdfMap = Maps.newHashMap();
        if (econtractBatchPoiInfoExtBo != null) {
            String signTime = DateUtil.secondsToString(DateUtil.unixTime());
            EcontractContractInfoBo contractInfoBo = econtractBatchPoiInfoExtBo.getEcontractContractInfoBo();
            pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
            pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
            pdfMap.put("partBStampName", StringUtils.defaultIfEmpty(contractInfoBo.getPartBName(), WmEcontractContextUtil.PARTB_STAMP_NAME));
            pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
            pdfMap.put("partBSignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
            pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
            pdfMap.put("partBEstamp", subjectEstampMap.getOrDefault(contractInfoBo.getPartBName(), PdfConstant.MT_SIGNKEY));
        }
        return pdfMap;
    }
}
