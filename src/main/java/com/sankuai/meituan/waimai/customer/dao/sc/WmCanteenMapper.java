package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenSearchCondition;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: scm
 * @description: 食堂的处理类
 * @author: jianghuimin02
 * @create: 2020-04-23 15:42
 **/
@Component
public interface WmCanteenMapper {

    /**
     * 新增食堂
     */
    int insertCanteen(WmCanteenDB wmCanteenDB);

    /**
     * 更新学校字段
     */
    int updateCanteen(WmCanteenDB wmCanteenDB);

    /**
     * 更新食堂等级：承包商的时候
     */
    int updateCanteenGrade(WmCanteenDB wmCanteenDB);

    /**
     * 查询一个食堂
     */
    WmCanteenDB selectCanteenById(int id);

    /**
     * 查询一个食堂忽略伪删除Valid字段
     */
    WmCanteenDB selectCanteenByIdIgnoreValid(int id);

    /**
     * 在从库查询一个食堂
     */
    WmCanteenDB selectCanteenByIdSlave(int id);

    /**
     * 查询一个食堂列表
     */
    List<WmCanteenDB> selectCanteenList(WmCanteenDB wmCanteenDB);

    /**
     * 根据食堂名称前缀模糊搜索
     * @param canteenName 食堂名称
     * @return List<WmCanteenDB>
     */
    List<WmCanteenDB> selectByLikeCanteenName(@Param("canteenName") String canteenName);

    /**
     * 根据学校主键ID和食堂名称模糊查询有数据权限的食堂列表
     * @param schoolId 学校主键ID
     * @param dslQuery DSL查询语句
     * @return List<WmCanteenDB>
     */
    List<WmCanteenDB> selectBySchoolIdWithDSL(@Param("schoolId") Integer schoolId,
                                              @Param("dslQuery") String dslQuery);

    /**
     * 根据食堂名称模糊查询有数据权限的食堂列表
     * @param canteenName 食堂名称
     * @param dslQuery DSL查询语句
     * @return List<WmCanteenDB>
     */
    List<WmCanteenDB> selectByLikeCanteenNameWithDSL(@Param("canteenName") String canteenName,
                                                     @Param("dslQuery") String dslQuery);

    /**
     * 通过DSL语句查询一个食堂列表
     * @param wmCanteenDB wmCanteenDB
     * @param dslQuery dslQuery查询语句
     * @return 食堂列表
     */
    List<WmCanteenDB> selectCanteenListByDSL(@Param("wmCanteenDB") WmCanteenDB wmCanteenDB,
                                             @Param("dslQuery") String dslQuery);

    /**
     * 根据承包商ID获取食堂列表
     */
    List<WmCanteenDB> getCanteenByContractorId(int contractorId);

    /**
     * 根据承包商ID批量获取食堂信息
     */
    List<WmCanteenDB> getCanteenByContractorIds(@Param("contractorIds")List<Integer> contractorIds);

    /**
     * 根据承包商ID获取食堂的数量
     */
    int countCanteenByContractorId(int contractorId);

    /**
     * 根据承包商ID获取门店的数量
     */
    Integer countCanteenPoiByContractorId(int contractorId);

    /**
     * 根据schoolId修改名称
     */
    void linkUpdateSchoolName(WmCanteenDB wmCanteenDB);

    int updateCanteenId(WmCanteenDB wmCanteenDB);

    /**
     * 绑定食堂责任人
     */
    int bindResponsiblePerson(WmCanteenDB wmCanteenDB);

    /**
     * 逻辑删除食堂
     */
    void deleteCanteenById(int id);

    /**
     * 查询食堂列表
     */
    List<WmCanteenDB> selectByCondition(WmCanteenSearchCondition condition);

    /**
     * 查询食堂数量
     */
    int selectCountByCondition(WmCanteenSearchCondition condition);

    /**
     * 根据食堂主键ID批量获取食堂信息
     */
    List<WmCanteenDB> selectCanteensByIds(@Param("ids")List<Integer> ids);

    /**
     * 根据用户MIS列表查询作为食堂负责人的食堂主键ID列表
     */
    List<Integer> selectCanteenPrimaryIdListByResponsibleMisIdList(@Param("misIdList") List<String> misIdList);

    /**
     * 根据承包商ID列表查询关联的食堂主键ID列表
     */
    List<Integer> selectCanteenPrimaryIdListByCustomerIdList(@Param("customerIdList") List<Integer> customerIdList);

    /**
     * 根据学校主键ID列表查询关联的食堂主键ID列表
     */
    List<Integer> selectCanteenPrimaryIdListBySchoolPrimaryIdList(@Param("schoolPrimaryIdList") List<Integer> schoolPrimaryIdList);

    /**
     * 根据学校主键ID查询关联的食堂主键ID列表
     */
    List<Integer> selectCanteenPrimaryIdListBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据学校schoolPrimaryId 获取食堂列表
     */
    List<WmCanteenDB> selectCanteensByScId(@Param("schoolId") Integer schoolId);

    /**
     * 根据学校ID获取档口总数
     * @param primarySchoolId 学校主键ID
     * @return 档口总数
     */
    Integer getStallNumTotalBySchoolId(@Param("primarySchoolId") Integer primarySchoolId);

    /**
     * 根据学校ID获取合作档口总数
     * @param schoolId 学校ID
     * @return 合作档口总数
     */
    Integer getStoreNumTotalBySchoolId(@Param("schoolId") Integer schoolId);

    /**
     * 根据学校ID获取档口总数和合作档口总数
     * @param schoolId 学校ID
     * @return 档口总数和合作档口总数
     */
    WmCanteenDB getStallAndStoreNumTotalBySchoolId(@Param("schoolId") Integer schoolId);

    /**
     * 更新食堂门店数量
     * @param id 食堂主键ID
     * @param storeNum 门店数量
     * @return
     */
    int updateStoreNum(@Param("id") int id, @Param("storeNum") int storeNum);

    /**
     * 根据食堂主键ID批量获取食堂信息
     */
    List<WmCanteenDB> selectCanteensByIdsALL(@Param("ids")List<Integer> ids);

    /**
     * 获取最大的食堂ID
     * @return 最大的食堂ID
     */
    int selectMaxCanteenId();

    Integer getCanteenCategoryNum(WmSchoolDB wmSchoolDB);
}
