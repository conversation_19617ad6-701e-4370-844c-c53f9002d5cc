package com.sankuai.meituan.waimai.customer.service.sign.callback.handler;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.thrift.TException;

public interface WmEcontractCallbackHandler {

    public Boolean handleCallback(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo)
        throws TException, WmCustomerException;

}
