package com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.read;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
public interface ISchoolReadHandle {

    /**
     * 执行策略类型
     *
     * @return
     */
    KmsKeyNameEnum handleType();

    /**
     * 选择读原字段还是新字段，如果读新字段则解密
     *
     * @param schoolRead
     * @return
     */
    void doReadChoiceEncryptToDecrypt(SchoolRead schoolRead);

    /**
     * 读新字段，解密后返回
     *
     * @param schoolRead
     * @return
     */
    String doReadEncryptToDecrypt(SchoolRead schoolRead);

}
