package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.MccUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT)
public class TechnicalServiceDefaultSplit implements DeliveryPdfSplit {
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));

        if (!(feeMode == LogisticsFeeModeEnum.WAIMAI_V3
                || feeMode == LogisticsFeeModeEnum.WAIMAI_QIKE
                || feeMode == LogisticsFeeModeEnum.WAIMAI_QIKE_V2
                || feeMode == LogisticsFeeModeEnum.AGENT_QIKE
                || feeMode == LogisticsFeeModeEnum.SHANGOU_QIKE_V2
                || feeMode == LogisticsFeeModeEnum.YIYAO_QIKE_V2
                || feeMode == LogisticsFeeModeEnum.AGENT_ACTUAL_PAYMENT
                || (MccUtil.isGrayTemplateEnum(SignTemplateEnum.TECHNICAL_SERVICE_SG_20) && feeMode == LogisticsFeeModeEnum.SHANGOU)
                || (MccUtil.isGrayTemplateEnum(SignTemplateEnum.TECHNICAL_SERVICE_SG_22) && feeMode == LogisticsFeeModeEnum.SHANGOU_2_2))
                && (CollectionUtils.isNotEmpty(tabPdfMap.get(TAB_DELIVERY))
                && (tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.TECHNICAL_SERVICE_FEE)
                || tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.MULTI_TECHNICAL_SERVICE_FEE)
                || tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.TECHNICAL_SERVICE_FEE_V2)
                || tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.MULTI_TECHNICAL_SERVICE_FEE_V2)
                || tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.TECHNICAL_SERVICE_FEE_V2_1)
                || tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.MULTI_TECHNICAL_SERVICE_FEE_V2_1)
                || tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.TECHNICAL_SERVICE_SG_22)))) {
            List<String> technicalDefault = pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT.getName());
            if (CollectionUtils.isEmpty(technicalDefault)) {
                technicalDefault = Lists.newArrayList();
                technicalDefault.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                technicalDefault.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT.getName(), technicalDefault);
            log.info("ADD TO TECHNICAL_SERVICE_DEFAULT，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}
