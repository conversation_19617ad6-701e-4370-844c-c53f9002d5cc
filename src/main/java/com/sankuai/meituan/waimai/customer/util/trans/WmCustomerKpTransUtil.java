package com.sankuai.meituan.waimai.customer.util.trans;

import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerMultiplexEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import joptsimple.internal.Strings;

public class WmCustomerKpTransUtil {

    /**
     * WmCustomerMultiplexSignerDTO to WmCustomerKp
     *
     * @param signerDto
     * @return
     */
    public static WmCustomerKp multiplexSignerDtoToCustomerKp(WmCustomerMultiplexSignerDTO signerDto) {
        if (signerDto == null) {
            return null;
        }
        WmCustomerKp kp = new WmCustomerKp();
        kp.setKpType(KpTypeEnum.SIGNER.getType());
        kp.setSignerType((byte) signerDto.getSignerType());
        kp.setCertType((byte) signerDto.getCertType());
        kp.setCompellation(signerDto.getName());
        kp.setPhoneNum(signerDto.getMobileNo());
        kp.setCreditCard(signerDto.getBankAccountNumber());
        kp.setCertNumber(signerDto.getIdentityCardNumber());
        kp.setMultiplex(CustomerMultiplexEnum.YES.getType());
        kp.setSpecialAttachment(Strings.EMPTY);
        kp.setBankName(Strings.EMPTY);
        return kp;
    }
}
