package com.sankuai.meituan.waimai.customer.service.agreement;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_AGENT_ID;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_AOR_ID;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_TYPE;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_WM_LOGISTICS_IDS;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import com.sankuai.meituan.waimai.bizuser.thrift.AcctWmPoiTService;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementShowFactor;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementShowFactorDTO;
import com.sankuai.meituan.waimai.customer.service.tair.WmAgreementTairService;
import com.sankuai.meituan.waimai.infra.domain.WmAor;
import com.sankuai.meituan.waimai.infra.service.WmAorService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.AgreementTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.WmLogisticsEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementPoiSign;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;

/**
 * 商家端协议过滤服务
 */
@Service
public class WmAgreementFilterService {
    private static Logger logger = LoggerFactory.getLogger(WmAgreementFilterService.class);

    private static final int WAIMAI_BIZ_ORG_CODE = 14010;

    private static final Set<Integer> OWNER_TYPE_SET = Sets.newHashSet(-1,0,2);

    private static final String SH_SANKUAI_ZHISONG = "上海三快智送科技有限公司";

    private static final Set<Integer> WORK_SPACE_AOR_TYPE_SET = Sets.newHashSet(0,2);

    private static final Set<Long> wmPoiIdSetLoadFromLocalFile = Sets.newHashSet();

    private static Splitter SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    static final ImmutableSet<String> WM_POI_FIELDS_AGGRE_WMPOI = ImmutableSet.of(
            WM_POI_FIELD_WM_POI_ID,
            WM_POI_FIELD_WM_LOGISTICS_IDS,
            WM_POI_FIELD_BIZ_ORG_CODE,
            WM_POI_FIELD_AGENT_ID,
            WM_POI_FIELD_OWNER_TYPE,
            WM_POI_FIELD_AOR_ID,
            WM_POI_FIELD_CITY_LOCATION_ID,
            WM_POI_FIELD_LABEL_IDS
    );

    @Resource
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;
    @Resource
    private AcctWmPoiTService.Iface acctWmPoiTService;
    @Autowired
    private WmAgreementService wmAgreementService;
    @Autowired
    private WmAgreementCacheService wmAgreementCacheService;
    @Autowired
    private WmAgreementTairService wmAgreementTairService;
    @Autowired
    private WmContractService wmContractService;
    @Autowired
    private WmAorService.Iface wmAorThriftService;

    private RateLimiter LIMITER = RateLimiter.create(MccConfig.getSupplierQueryQps());

    private static final String EMPTY_VALUE = "empty&value";

    private static final String DEFAULT_VALUE = "-1";

    @PostConstruct
    public void init() {
        if(ConfigUtilAdapter.getBoolean("wmPoiIdSetLoadFromLocalFile_close",false)){
            return;
        }
        try{
            File file = ResourceUtils.getFile("classpath:local-source/wm_poi_id.txt");
            FileReader fileReader = null;
            BufferedReader bufferFileReader = null;
            try {
                fileReader = new FileReader(file);
                bufferFileReader = new BufferedReader(fileReader);
                String line;
                while ((line = bufferFileReader.readLine()) != null) {
                    wmPoiIdSetLoadFromLocalFile.add(Long.valueOf(line));
                }
            } finally {
                if (bufferFileReader != null) {
                    bufferFileReader.close();
                }
                if (fileReader != null) {
                    fileReader.close();
                }
            }
            logger.info("wmPoiIdSetLoadFromLocalFile size = {}",wmPoiIdSetLoadFromLocalFile.size());
        }catch(Exception e){
            logger.error("WmAgreementFilterService init error!",e);
        }
    }

    /**
     * 获取未签协议门店关系
     *
     * @param wmPoiId
     * @param acctId
     * @return
     */
    public Map<Integer, List<Long>> getUnsignProtocolWmPoiIds(Long wmPoiId, String acctId) {

        //限制qps
        if (MccConfig.isOpenRateLimit()) {
            LIMITER.acquire();
        }

        //读取缓存
        boolean flag = wmPoiId != null && wmPoiId != -1;
        if (flag && MccConfig.isCacheOpen()) {
            String result = wmAgreementTairService.getPoiAgreement(wmPoiId);
            if (DEFAULT_VALUE.equals(result) || EMPTY_VALUE.equals(result)) {
                return Maps.newHashMap();
            }
        }

        //获取所有门店
        List<Long> wmPoiIdList = getWmPoiIdListByWmPoiIdAndAcctId(wmPoiId, acctId);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return null;
        }

        //组装map
        //e.g: {4:[8421]} 有美团跑腿配送方式(4)的门店集合(8421)
        WmAgreementShowFactorDTO wmAgreementShowFactorDTO = partitionWmPoiIds(wmPoiIdList);
        logger.info("wmAgreementShowFactorDTO={}", JSON.toJSONString(wmAgreementShowFactorDTO));

        //拼装协议门店map
        //e.g: WmLogisticsEnum.type->门店集合
        Map<Integer, List<Long>> poiIds = getProtocolWmPoiIdMap(wmAgreementShowFactorDTO);

        //设置缓存
        if (flag && MccConfig.isCacheOpen()) {
            if (poiIds == null || poiIds.isEmpty()) {
                wmAgreementTairService.setPoiAgreement(wmPoiId, DEFAULT_VALUE, EMPTY_VALUE);
            }
        }
        return poiIds;
    }

    private List<Long> getWmPoiIdListByWmPoiIdAndAcctId(long wmPoiId, String acctId) {
        List wmPoiIdList;
        if (wmPoiId == -1L) {
            wmPoiIdList = this.getWmPoiIdsByAccountId(acctId);
        } else {
            wmPoiIdList = Arrays.asList(wmPoiId);
        }
        return Collections.unmodifiableList(wmPoiIdList);
    }

    private List<Long> getWmPoiIdsByAccountId(String accountId) {
        List<Long> resultWmPoiIdList = new ArrayList();
        if (StringUtils.isEmpty(accountId)) {
            return resultWmPoiIdList;
        } else {
            try {
                //根据账号id获取门店
                long acctIdLong = Long.parseLong(accountId);
                resultWmPoiIdList = acctWmPoiTService.getWmPoiIdsByAcctId(acctIdLong, -1, -1);
            } catch (Exception e) {
                logger.error("getWmPoiIdsByAccountId Exception accountId:{}", accountId, e);
            }
            return resultWmPoiIdList;
        }
    }

    private WmAgreementShowFactorDTO partitionWmPoiIds(List<Long> wmPoiIdList) {
        WmAgreementShowFactorDTO result = new WmAgreementShowFactorDTO();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(wmPoiIdList)) {
            result.setLogisticClassifyMultimap(HashMultimap.create(1, 1));
        } else {
            List<List<Long>> idsList = Lists.partition(wmPoiIdList, 300);
            Iterator i$ = idsList.iterator();
            List<WmPoiAggre> resultWmPoiList = Lists.newArrayList();
            while (i$.hasNext()) {
                List<WmPoiAggre> wmPoiList = null;
                List<Long> ids = (List) i$.next();
                try {
                    wmPoiList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(ids, WM_POI_FIELDS_AGGRE_WMPOI);
                } catch (WmServerException | TException e) {
                    logger.error("查询门店信息异常", e);
                }
                if (!CollectionUtils.isEmpty(wmPoiList)) {
                    resultWmPoiList.addAll(wmPoiList);
                }
            }
            result.setLogisticClassifyMultimap(partirionWmPoiIdsByWmPoiAgges(resultWmPoiList));
            result.setShowFactorMap(resolveShowFactor(resultWmPoiList));
        }
        return result;
    }

    private Map<Long, WmAgreementShowFactor> resolveShowFactor(List<WmPoiAggre> resultWmPoiList) {
        Map<Long, WmAgreementShowFactor> result = Maps.newHashMap();
        if(CollectionUtils.isEmpty(resultWmPoiList)){
            return result;
        }
        WmAgreementShowFactor factor = null;
        for(WmPoiAggre temp : resultWmPoiList){
            factor = new WmAgreementShowFactor();
            factor.setBizOrgCode(temp.getBiz_org_code());
            factor.setAgentId(temp.getAgent_id());
            factor.setOwnerType(temp.getOwner_type());
            factor.setAorId(temp.getAor_id());
            factor.setCityLocationId(temp.getCity_location_id());
            factor.setLabelIds(temp.getLabel_ids());
            result.put(temp.getWm_poi_id(),factor);
        }
        return result;
    }

    private Multimap<String, Long> partirionWmPoiIdsByWmPoiAgges(List<WmPoiAggre> wmPoiList) {
        if (CollectionUtils.isEmpty(wmPoiList)) {
            return HashMultimap.create(1, 1);
        } else {
            Multimap<String, Long> multimap = HashMultimap.create();
            String wmLogistics;
            for (WmPoiAggre poiAggre : wmPoiList) {
                wmLogistics = poiAggre.getWm_logistics_ids();
                if (StringUtils.isNotEmpty(wmLogistics)) {
                    //有生效配送方式
                    String[] wmLogistic = wmLogistics.split(",");
                    List<String> wmLogisticList = Arrays.asList(wmLogistic);
                    for (String logistic : wmLogisticList) {
                        multimap.put(logistic, poiAggre.getWm_poi_id());
                    }
                }
            }
            return multimap;
        }
    }

    private Map<Integer, List<Long>> getProtocolWmPoiIdMap(WmAgreementShowFactorDTO wmAgreementShowFactorDTO) {
        Multimap<String, Long> multimap = wmAgreementShowFactorDTO.getLogisticClassifyMultimap();
        Map<Integer, List<Long>> poiIds = Maps.newHashMap();
        //无配送方式协议类型映射的门店集合
        List<Long> noAgreementTypeList = Lists.newArrayList();
        Set<Long> noAgreementTypeSet = Sets.newHashSet();
        for (String type : multimap.keySet()) {
            //无配送方式-协议类型映射的门店（例如星火配送服务主体变更协议）
            noAgreementTypeSet.addAll(multimap.get(type));
            //配送方式协议映射
            WmLogisticsEnum wmLogisticsEnum = WmLogisticsEnum.getById(NumberUtils.toInt(type));
            if (wmLogisticsEnum == null) {
                continue;
            }
            //有配送方式-协议类型映射的门店
            List<Long> crIds = getUnsignProtocolWmPoiIds(Lists.newArrayList(multimap.get(type)), wmLogisticsEnum.getType());
            if (!CollectionUtils.isEmpty(crIds)) {
                logger.info("getProtocolWmPoiIdMap 设置配送方式协议,poiIds:{},wmLogisticsEnum:{}",JSON.toJSONString(crIds),wmLogisticsEnum.getName());
                if(!MccConfig.closeLogisticsSubject()) {
                    logger.info("门店设置配送方式协议,poiIds:{},wmLogisticsEnum:{}",JSON.toJSONString(crIds),wmLogisticsEnum.getName());
                    poiIds.put(wmLogisticsEnum.getType(), crIds);
                }
            }
        }


        if (!CollectionUtils.isEmpty(noAgreementTypeSet)){
            noAgreementTypeList = Lists.newArrayList(noAgreementTypeSet);
            //添加门店主体变更协议
            List<Long> crIds = getUnsignSubjectChangeWmPoiIds(noAgreementTypeList,wmAgreementShowFactorDTO.getShowFactorMap());
            if (!CollectionUtils.isEmpty(crIds)) {
                poiIds.put(AgreementTypeEnum.DELIVERY_SUBJECT_CHANGE.getType(), crIds);
            }
            //数字货币授权协议
            Pair<List<Long>, List<Long>> dcAuthIdPair = getNeedSignDCAuthWmPoiIds(noAgreementTypeList,wmAgreementShowFactorDTO.getShowFactorMap());
            if(!CollectionUtils.isEmpty(dcAuthIdPair.getLeft())){
                poiIds.put(AgreementTypeEnum.DIGITAL_CURRENCY_AUTHORIZATION_OPENED.getType(), dcAuthIdPair.getLeft());
            }
            if(!CollectionUtils.isEmpty(dcAuthIdPair.getRight())){
                poiIds.put(AgreementTypeEnum.DIGITAL_CURRENCY_AUTHORIZATION_TO_OPEN.getType(), dcAuthIdPair.getRight());
            }
        }
        logger.info("WmAgreementFilterService#getProtocolWmPoiIdMap, poiIds: {}", JSON.toJSONString(poiIds));
        return poiIds;
    }

    private Pair<List<Long>, List<Long>> getNeedSignDCAuthWmPoiIds(List<Long> noAgreementTypeList,
            Map<Long, WmAgreementShowFactor> showFactorMap) {
        Pair<List<Long>, List<Long>> pair = Pair
                .of(Lists.newArrayList(), Lists.newArrayList());
        if(CollectionUtils.isEmpty(noAgreementTypeList)){
            return pair;
        }
        //仅针对指定标签的商家使用
        WmAgreementShowFactor factor = null;
        List<Long> left = Lists.newArrayList();
        List<Long> right = Lists.newArrayList();
        for(Long temp : noAgreementTypeList){
            factor = showFactorMap.get(temp);
            if(factor != null && hasSpecifiedLabel(factor.getLabelIds(),
                    ConfigUtilAdapter.getString("digital_currency_authorization_opened_label_id"))){
                if(CollectionUtils.isEmpty(wmAgreementService.getPoiSignByWmpoiId(temp, AgreementTypeEnum.DIGITAL_CURRENCY_AUTHORIZATION_OPENED.getType()))){
                    left.add(temp);
                }
            }

            if(factor != null && hasSpecifiedLabel(factor.getLabelIds(),
                    ConfigUtilAdapter.getString("digital_currency_authorization_to_open_label_id"))){
                if(CollectionUtils.isEmpty(wmAgreementService.getPoiSignByWmpoiId(temp, AgreementTypeEnum.DIGITAL_CURRENCY_AUTHORIZATION_TO_OPEN.getType()))){
                    right.add(temp);
                }
            }
        }
        return Pair.of(left,right);
    }

    private boolean hasSpecifiedLabel(String labelIds,String labelId) {
        if(StringUtils.isNotEmpty(labelIds) && StringUtils.isNotEmpty(labelId)){
            return SPLITTER.splitToList(labelIds).contains(labelId);
        }
        return false;
    }

    private boolean subjectChangeWmPoiIdExcludeFilterWithoutException(long wmPoiId, Map<Long, WmAgreementShowFactor> showFactorMap){
        try{
            return subjectChangeWmPoiIdExcludeFilter(wmPoiId,showFactorMap);
        }catch(Exception e){
            logger.error("subjectChangeWmPoiIdExcludeFilter error,wmPoiId={}",wmPoiId,e);
        }
        return true;
    }


    /**
     * @param wmPoiId
     * @param showFactorMap
     * @return true 不需要签署主题变更协议
     */
    private boolean subjectChangeWmPoiIdExcludeFilter(long wmPoiId,
            Map<Long, WmAgreementShowFactor> showFactorMap)
            throws WmCustomerException, TException, WmServerException {
        //指定白名单需要签署-本次废弃
//        if (MccConfig.getSubjectChangeWmPoiIdBoolean(wmPoiId)) {
//            return false;
//        }
        WmAgreementShowFactor wmAgreementShowFactor = showFactorMap.get(wmPoiId);
        if(wmAgreementShowFactor == null){
            return true;
        }

        //灰度范围内，同时满足以下条件需要签署协议
        //包括:
        //1.外卖+直营+白领城市
        //不包括:
        //1.C1合同已变更为"上海三快智送"
        //2.指定批量商家
        if(MccConfig.subjectChangeJudgeTotalOpen() || MccConfig.subjectChangeGrey(wmAgreementShowFactor.getCityLocationId())){
            //非外卖商家
            if(wmAgreementShowFactor.getBizOrgCode() != WAIMAI_BIZ_ORG_CODE){
                logger.info("mark1");
                return true;
            }
            //非直营商家
            if(wmAgreementShowFactor.getAgentId() > 0){
                logger.info("mark2");
                return true;
            }
            //非城市商家
            if(!OWNER_TYPE_SET.contains(wmAgreementShowFactor.getOwnerType())){
                logger.info("mark3");
                return true;
            }
            //指定数据集中的商家
            if(wmPoiIdSetLoadFromLocalFile.contains(wmPoiId)){
                logger.info("mark4");
                return true;
            }
            //非白领商家
            if(wmAgreementShowFactor.getAorId() > 0){
                WmAor wmAor = wmAorThriftService.getById((int) wmAgreementShowFactor.getAorId());
                if(wmAor != null && !WORK_SPACE_AOR_TYPE_SET.contains(wmAor.getType())){
                    logger.info("mark5");
                    return true;
                }
            }
            //C1合同附件4配送服务签约主体已经变更为“上海三快智送”
            List<WmTempletContractBasicBo> contractInfo = wmContractService
                    .getAuditedContractBasicListByPoiIdAndType(wmPoiId,
                            Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(),
                                    WmTempletContractTypeEnum.C1_PAPER.getCode()), 0, "系统");
            if(!CollectionUtils.isEmpty(contractInfo)){
                WmTempletContractBasicBo wmTempletContractBasicBo = contractInfo.get(0);
                if(SH_SANKUAI_ZHISONG.equals(wmTempletContractBasicBo.getLogisticsSubject())){
                    logger.info("mark6");
                    return true;
                }
            }

            return false;
        }
        return true;
    }

    //获取未签署门店主体变更协议
    private List<Long> getUnsignSubjectChangeWmPoiIds(List<Long> oriIdList,Map<Long,WmAgreementShowFactor> showFactorMap) {
        List<Long> oldIdList = Lists.newArrayList(oriIdList);
        List<Long> removeList = Lists.newArrayList();
        //获取已经签协议的门店
        for (Long wmPoiId : oldIdList) {
            List<WmAgreementPoiSign> poiSignList = null;
            try {
                poiSignList = wmAgreementService.getPoiSignByWmpoiId(wmPoiId, AgreementTypeEnum.DELIVERY_SUBJECT_CHANGE.getType());
            } catch (Exception e) {
                logger.error("查询门店是否签约异常", e);
            }
            //已签署过无需签署+不满足条件无需签署
            if (!CollectionUtils.isEmpty(poiSignList) || subjectChangeWmPoiIdExcludeFilterWithoutException(wmPoiId,showFactorMap)) {
                removeList.add(wmPoiId);
            }
        }

        if (!CollectionUtils.isEmpty(removeList)) {
            oldIdList.removeAll(removeList);
        }

        if (CollectionUtils.isEmpty(oldIdList)) {
            return null;
        }
        return oldIdList;
    }

    private List<Long> getUnsignProtocolWmPoiIds(List<Long> oriIdList, int protocol) {
        List<Long> oldIdList = Lists.newArrayList(oriIdList);
        List<Long> removeList = Lists.newArrayList();
        //获取已经签协议的门店
        for (Long wmPoiId : oldIdList) {
            List<WmAgreementPoiSign> poiSignList = null;
            try {
                poiSignList = wmAgreementService.getPoiSignByWmpoiId(wmPoiId, protocol);
            } catch (Exception e) {
                logger.error("查询门店是否签约异常", e);
            }
            if (!CollectionUtils.isEmpty(poiSignList)) {
                removeList.add(wmPoiId);
            }
        }

        if (!CollectionUtils.isEmpty(removeList)) {
            oldIdList.removeAll(removeList);
        }

        //过滤七天未签署
        filterTimeOutPoiId(protocol, oldIdList);

        if (CollectionUtils.isEmpty(oldIdList)) {
            return null;
        }
        return oldIdList;
    }

    private void filterTimeOutPoiId(int protocol, List<Long> oldIdList) {
        if (CollectionUtils.isEmpty(oldIdList)) {
            return;
        }
        for (Iterator<Long> iterator = oldIdList.iterator(); iterator.hasNext(); ) {
            long oldId = iterator.next();
            long firstReadTime = NumberUtils.toLong(getTairProtocol(protocol, oldId), System.currentTimeMillis()) / 1000;
            if (getTimeBeforeFewDays(7) > firstReadTime) {
                iterator.remove();
            }
        }
    }

    private String getTairProtocol(int protocol, long wmPoiId) {
        if (WmLogisticsEnum.MIX_DELIVERY.getType() == protocol) {
            return wmAgreementCacheService.getMixDeliveryFirstReadTime(wmPoiId);
        } else if (WmLogisticsEnum.WHOLE_CITY_DELIVERY.getType() == protocol) {
            return wmAgreementCacheService.getWholeCityFirstReadTime(wmPoiId);
        }
        return null;
    }

    public static long getTimeBeforeFewDays(int fewDays) {
        Calendar c = Calendar.getInstance();
        c.set(5, c.get(5) - fewDays);
        long time = c.getTimeInMillis();
        time /= 1000L;
        return time;
    }
}
