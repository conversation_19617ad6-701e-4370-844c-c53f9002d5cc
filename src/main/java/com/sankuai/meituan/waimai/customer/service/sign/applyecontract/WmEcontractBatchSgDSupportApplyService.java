package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant.FLOW_WITH_POI_MT_STAMP;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractSlaDataWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 普通结算、配送信息合并确认流程
 *
 * 生成pdf -> 发短信 -> 签章-> 完成合同
 */
@Service
public class WmEcontractBatchSgDSupportApplyService extends AbstractWmEcontractApplyAdapterService {

    private static List<String>                          flowList                = Lists.newArrayList();

    private static String                                TYPE_SETTLE_SIGN_POIFEE = "type_settle_sign_poifee";
    private static List<String>                          poiStampList            = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap          = Maps.newHashMap();

    static {
        flowList.add(EcontractTaskApplyTypeEnum.SETTLE.getName());
        flowList.add(EcontractTaskApplyTypeEnum.POIFEE.getName());

        poiStampList.add(EcontractTaskApplyTypeEnum.POIFEE.getName());

        dataWrapperMap.put(EcontractTaskApplyTypeEnum.SETTLE.getName(), EcontractDataWrapperEnum.SETTLE);
        dataWrapperMap.put(EcontractTaskApplyTypeEnum.POIFEE.getName(), EcontractDataWrapperEnum.DELIVERY);
    }

    @Resource
    private WmEcontractDateWrapperService     wmEcontractDateWrapperService;

    @Resource
    private WmEcontractSmsWrapperService      wmEcontractSmsWrapperService;

    @Autowired
    private WmEcontractSlaDataWrapperService  wmEcontractDeliverySlaDataWrapperService;
    @Resource
    private WmEcontractCAPoiWrapperService    wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Resource
    private WmEcontractCAMTWrapperService wmEcontractCAMTWrapperService;

    @Resource
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo,EcontractSignDataFactor econtractSignDataFactor) throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        Map<String, EcontractDataWrapperEnum> dataWrapperMapActually = WmEcontractContextUtil.analysisDataWrapperMapForDelivery(batchContextBo,
                Maps.newHashMap(dataWrapperMap));

        List<String> currentFlowList = Lists.newArrayList(flowList);
        List<String> currentPoiStampList = Lists.newArrayList();
        List<String> currentMtStampList = Lists.newArrayList();
        String currentEcontractType = TYPE_SETTLE_SIGN_POIFEE;

        if(econtractSignDataFactor.isDeliverySupportExclusive()){
            currentPoiStampList.addAll(poiStampList);
        }

        if (econtractSignDataFactor.isDeliverySupportWholeCity()) {
            WmEcontractContextUtil.assembleFlowForDeliverySupportWholeCity(dataWrapperMapActually, currentFlowList);
        }

        if(econtractSignDataFactor.isDeliverySupportAggregation()) {
            WmEcontractContextUtil.assembleFlowForDeliveryAggregation(dataWrapperMapActually, currentPoiStampList, currentMtStampList, currentFlowList);
            //增加美团CA和美团签章数据
            batchInfoBoList.add(wmEcontractCAMTWrapperService.wrap(batchContextBo));
            batchInfoBoList.add(wmEcontractStampMTWrapperService.wrap(batchContextBo, currentMtStampList));
            //签约流程为商家签章+美团签章
            currentEcontractType = FLOW_WITH_POI_MT_STAMP;
        }

        if (econtractSignDataFactor.isDeliverySupportCompanyCustomerLongDistanceDelivery()) {
            WmEcontractContextUtil.assembleFlowForDeliveryCompanyCustomerLongDistance(dataWrapperMapActually, currentFlowList);
        }

        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMapActually));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, currentPoiStampList));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        wmEcontractDeliverySlaDataWrapperService.addContext(batchInfoBoList, batchContextBo,
                dataWrapperMapActually.get(EcontractTaskApplyTypeEnum.POIFEE.getName()));
        wmEcontractDeliverySlaDataWrapperService.addContext(batchInfoBoList,batchContextBo,dataWrapperMapActually.get(EcontractTaskApplySubTypeEnum.DELIVERY_WHOLE_CITY.getName()));

        return new EcontractBatchBo.Builder().token(getToken()).econtractBizId(getBizId(batchContextBo)).econtractType(currentEcontractType)
                .stageInfoBoList(batchInfoBoList).flowList(currentFlowList).callBackUrl(getCallbackUrl()).build();

    }

}
