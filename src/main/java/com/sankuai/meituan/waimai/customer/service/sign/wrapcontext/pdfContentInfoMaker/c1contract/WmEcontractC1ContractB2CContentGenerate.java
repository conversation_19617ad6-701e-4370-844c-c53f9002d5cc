package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.c1contract;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettlePdfTransService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * B2C合同数据拼装
 * <AUTHOR>
 */
@Slf4j
public class WmEcontractC1ContractB2CContentGenerate {

    public Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        log.info("WmEcontractC1ContractB2CContentGenerate contextBo = {}", JSON.toJSONString(contextBo));
        EcontractContractInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractContractInfoBo.class);
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());

        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("contractNumber", StringUtils.defaultIfEmpty(contractInfoBo.getContractNum(), StringUtils.EMPTY));
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        pdfMap.put("partAAddress", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getAddress(), StringUtils.EMPTY));
        pdfMap.put("legalPerson",
                contextBo.getCustomerInfoBo().getQuaTypeEnum() == CustomerType.CUSTOMER_TYPE_BUSINESS
                        || contextBo.getCustomerInfoBo().getQuaTypeEnum() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD
                                ? contextBo.getCustomerInfoBo().getLegalPerson()
                                : contextBo.getCustomerInfoBo().getCustomerName());
        pdfMap.put("signerName", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerName(), StringUtils.EMPTY));
        pdfMap.put("signerIDCareNumber", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerIDCardNum(), StringUtils.EMPTY));
        pdfMap.put("signerPhone", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerPhoneNum(), StringUtils.EMPTY));
        pdfMap.put("acceptPhone", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerPhoneNum(), StringUtils.EMPTY));
        pdfMap.put("signerEmail", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerEmail(), StringUtils.EMPTY));
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));

        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.MED_B2C_SIGNKEY);
        log.info("WmEcontractC1ContractB2CContentGenerate pdfMap = {}", JSON.toJSONString(pdfMap));
        return pdfMap;
    }
}