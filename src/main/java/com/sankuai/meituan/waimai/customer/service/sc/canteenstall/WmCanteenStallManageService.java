package com.sankuai.meituan.waimai.customer.service.sc.canteenstall;

import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallManageBindRelMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallManageMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.*;
import com.sankuai.meituan.waimai.customer.service.sc.WmScCanteenService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService;
import com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallPoiBindService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallManageSubmitStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallManageTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanCancelBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScPageInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallBindSubmitDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallManageDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallManageListDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallManageQueryDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 食堂档口管理任务服务
 * <AUTHOR>
 * @date 2024/06/05
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallManageService {

    @Autowired
    private WmCanteenStallManageMapper wmCanteenStallManageMapper;

    @Autowired
    private WmScOuterService wmScOuterService;

    @Autowired
    private WmCanteenStallManageBindRelMapper wmCanteenStallManageBindRelMapper;

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmCanteenStallBindService wmCanteenStallBindService;

    @Autowired
    private WmCanteenStallPoiBindService wmCanteenStallPoiBindService;

    @Autowired
    private WmScCanteenService wmScCanteenService;

    @Autowired
    private WmScCanteenPoiService wmScCanteenPoiService;

    @Autowired
    private WmCanteenStallAuthService wmCanteenStallAuthService;


    /**
     * 查询档口管理任务列表
     * @param queryDTO 查询参数
     * @return WmCanteenStallManageListDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public WmCanteenStallManageListDTO getManageList(WmCanteenStallManageQueryDTO queryDTO) throws WmSchCantException, TException {
        log.info("[WmCanteenStallManageService.getManageList] input param: queryDTO = {}", JSONObject.toJSONString(queryDTO));
        if (queryDTO == null) {
            log.error("[WmCanteenStallManageService.getManageList] queryDTO is null, return.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmCanteenStallManageListDTO manageListDTO = new WmCanteenStallManageListDTO();
        WmScPageInfoDTO pageInfoDTO = new WmScPageInfoDTO();
        pageInfoDTO.setPageNum(queryDTO.getPageNum());
        pageInfoDTO.setPageSize(queryDTO.getPageSize());

        // 1-根据入参组装查询条件
        WmCanteenStallManageQueryBO manageSearchBO = getManageQueryBOByQueryDTO(queryDTO);
        if (manageSearchBO.getResultEmpty()) {
            pageInfoDTO.setTotal(0L);
            manageListDTO.setPageInfoDTO(pageInfoDTO);
            return manageListDTO;
        }

        // 2-按条件查询总数
        int count = wmCanteenStallManageMapper.selectCountBySearchBO(manageSearchBO);
        pageInfoDTO.setTotal((long) count);
        manageListDTO.setPageInfoDTO(pageInfoDTO);
        if (count <= 0) {
            return manageListDTO;
        }

        // 3-按条件分页查询
        manageSearchBO.setPageSize(queryDTO.getPageSize());
        manageSearchBO.setPageFrom((queryDTO.getPageNum() - 1) * queryDTO.getPageSize());
        List<WmCanteenStallManageDO> manageDOList = wmCanteenStallManageMapper.selectManageListBySearchBO(manageSearchBO);
        if (CollectionUtils.isEmpty(manageDOList)) {
            return manageListDTO;
        }

        // 4-数据组装并返回
        List<WmCanteenStallManageDTO> manageDTOList = WmScTransUtil.transCanteenStallManageDOsToDTOs(manageDOList);
        List<WmCanteenStallManageDTO> manageDTOListFinal = getManageDTOListWithScAndCreatorInfo(manageDTOList);

        // 5-设置档口管理列表按钮权限
        wmCanteenStallAuthService.setManageDTOListButtonAuth(manageDTOListFinal, queryDTO.getUserId());

        manageListDTO.setManageDTOList(manageDTOListFinal);
        return manageListDTO;
    }

    /**
     * 根据食堂档口管理查询DTO组装查询BO
     * @param queryDTO queryDTO
     * @return WmCanteenStallManageQueryBO
     */
    public WmCanteenStallManageQueryBO getManageQueryBOByQueryDTO(WmCanteenStallManageQueryDTO queryDTO) throws WmSchCantException {
        WmCanteenStallManageQueryBO manageSearchBO = new WmCanteenStallManageQueryBO();
        manageSearchBO.setResultEmpty(false);
        // 学校主键ID-查询学校关联的档口管理任务
        if (queryDTO.getSchoolPrimaryId() != null) {
            composeManageQueryBOWithSchoolPrimaryId(manageSearchBO, queryDTO.getSchoolPrimaryId());
            if (manageSearchBO.getResultEmpty()) {
                return manageSearchBO;
            }
        }
        // 线索ID-按照线索编号精确搜索包含此线索编号的档口管理任务
        if (queryDTO.getWdcClueId() != null) {
            composeManageQueryBOWithWdcClueId(manageSearchBO, queryDTO.getWdcClueId());
            if (manageSearchBO.getResultEmpty()) {
                return manageSearchBO;
            }
        }
        // 门店ID-按照门店ID精确搜索包含此门店ID的档口管理任务
        if (queryDTO.getWmPoiId() != null) {
            composeManageQueryBOWithWmPoiId(manageSearchBO, queryDTO.getWmPoiId());
            if (manageSearchBO.getResultEmpty()) {
                return manageSearchBO;
            }
        }
        // 食堂主键ID
        manageSearchBO.setCanteenPrimaryId(queryDTO.getCanteenPrimaryId());
        // 创建人
        manageSearchBO.setCuid(queryDTO.getCuid());
        // 创建时间
        manageSearchBO.setCtimeStart(queryDTO.getCtimeStart());
        manageSearchBO.setCtimeEnd(queryDTO.getCtimeEnd());
        // 档口管理任务ID
        manageSearchBO.setId(queryDTO.getManageId());
        // 任务类型
        manageSearchBO.setTaskType(queryDTO.getTaskType());
        // 提交状态
        manageSearchBO.setSubmitStatus(queryDTO.getSubmitStatus());
        // DSL查询条件
        manageSearchBO.setDslQuery(wmCanteenStallAuthService.getManageListQueryDSL(queryDTO.getUserId()));

        log.info("[WmCanteenStallManageService.getManageQueryBOByQueryDTO] manageSearchBO = {}", JSONObject.toJSONString(manageSearchBO));
        return manageSearchBO;
    }

    /**
     * 学校主键ID-查询学校关联的档口管理任务
     * @param manageSearchBO manageSearchBO
     * @param schoolPrimaryId 学校主键ID
     */
    private void composeManageQueryBOWithSchoolPrimaryId(WmCanteenStallManageQueryBO manageSearchBO, Integer schoolPrimaryId) {
        List<Integer> canteenPrimaryIdList = wmScOuterService.getCanteenPrimaryIdListBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(canteenPrimaryIdList)) {
            manageSearchBO.setResultEmpty(true);
            return;
        }
        manageSearchBO.setCanteenPrimaryIdList(canteenPrimaryIdList);
    }

    /**
     * 线索ID-按照线索编号精确搜索包含此线索编号的档口管理任务
     * @param manageSearchBO manageSearchBO
     * @param wdcClueId 线索ID
     */
    private void composeManageQueryBOWithWdcClueId(WmCanteenStallManageQueryBO manageSearchBO, Long wdcClueId) {
        // 1-根据线索ID查询档口管理任务
        List<WmCanteenStallBindDO> bindDOListByWdcClueId = wmCanteenStallBindMapper.selectByWdcCludId(wdcClueId);
        if (wdcClueId <= 0 || CollectionUtils.isEmpty(bindDOListByWdcClueId)) {
            manageSearchBO.setResultEmpty(true);
            return;
        }

        List<Integer> bindIdList = bindDOListByWdcClueId.stream()
                .map(WmCanteenStallBindDO::getId)
                .distinct()
                .collect(Collectors.toList());

        // 2-根据档口绑定ID查询档口管理ID
        List<WmCanteenStallManageBindRelDO> relDOList = wmCanteenStallManageBindRelMapper.selectByBindIdList(bindIdList);
        if (CollectionUtils.isEmpty(relDOList)) {
            manageSearchBO.setResultEmpty(true);
            return;
        }

        List<Integer> manageIdList = relDOList.stream()
                .map(WmCanteenStallManageBindRelDO::getManageId)
                .distinct()
                .collect(Collectors.toList());
        manageSearchBO.setIdList(manageIdList);
        log.info("[WmCanteenStallManageService.composeManageQueryBOWithWdcClueId] manageSearchBO = {}", JSONObject.toJSONString(manageSearchBO));
    }

    /**
     * 门店ID-按照门店ID精确搜索包含此门店ID的档口管理任务
     * @param manageSearchBO manageSearchBO
     * @param wmPoiId 门店ID
     */
    private void composeManageQueryBOWithWmPoiId(WmCanteenStallManageQueryBO manageSearchBO, Long wmPoiId) {
        // 1-根据门店ID查询档口绑定任务
        List<WmCanteenStallBindDO> bindDOListByWmPoiId = wmCanteenStallBindMapper.selectByWmPoiId(wmPoiId);
        if (wmPoiId <= 0 || CollectionUtils.isEmpty(bindDOListByWmPoiId)) {
            manageSearchBO.setResultEmpty(true);
            return;
        }

        List<Integer> bindIdList = bindDOListByWmPoiId.stream()
                .map(WmCanteenStallBindDO::getId)
                .distinct()
                .collect(Collectors.toList());

        // 2-根据档口绑定ID查询档口管理ID
        List<WmCanteenStallManageBindRelDO> relDOList = wmCanteenStallManageBindRelMapper.selectByBindIdList(bindIdList);
        if (CollectionUtils.isEmpty(relDOList)) {
            manageSearchBO.setResultEmpty(true);
            return;
        }

        List<Integer> manageIdList = relDOList.stream()
                .map(WmCanteenStallManageBindRelDO::getManageId)
                .distinct()
                .collect(Collectors.toList());

        // 3-求线索ID和门店ID关联档口管理任务ID的交集
        if (CollectionUtils.isNotEmpty(manageSearchBO.getIdList())) {
            List<Integer> resultIdList = manageSearchBO.getIdList().stream()
                    .filter(manageIdList::contains)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(resultIdList)) {
                manageSearchBO.setResultEmpty(true);
                return;
            }
            manageSearchBO.setIdList(resultIdList);
        } else {
            manageSearchBO.setIdList(manageIdList);
        }
        log.info("[WmCanteenStallManageService.composeManageQueryBOWithWmPoiId] manageSearchBO = {}", JSONObject.toJSONString(manageSearchBO));
    }

    /**
     * 组装食堂档口管理列表信息(学校名称、食堂名称、创建人姓名等)
     * @param manageDTOList manageDTOList
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private List<WmCanteenStallManageDTO> getManageDTOListWithScAndCreatorInfo(List<WmCanteenStallManageDTO> manageDTOList) throws WmSchCantException {
        if (CollectionUtils.isEmpty(manageDTOList)) {
            return manageDTOList;
        }
        // 1-组装食堂和学校名称
        List<WmCanteenStallManageDTO> manageDTOListWithScName = getManageDTOListWithSchoolAndCanteenName(manageDTOList);

        // 2-组装创建人信息
        return getManageDTOListWithCreatorInfo(manageDTOListWithScName);
    }

    /**
     * 组装创建人姓名和MIS信息
     * @param manageDTOList manageDTOList
     */
    private List<WmCanteenStallManageDTO> getManageDTOListWithCreatorInfo(List<WmCanteenStallManageDTO> manageDTOList) throws WmSchCantException {
        List<Integer> uidList = manageDTOList.stream()
                .map(WmCanteenStallManageDTO::getCuid)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, WmEmploy> employMap = wmScEmployAdaptor.getEmployMapByUids(uidList);

        for (WmCanteenStallManageDTO manageDTO : manageDTOList) {
            WmEmploy wmEmploy = employMap.get(manageDTO.getCuid());
            manageDTO.setCmis(wmEmploy == null ? "system" : wmEmploy.getMisId());
            manageDTO.setCname(wmEmploy == null ? "系统" : wmEmploy.getName());
        }

        return manageDTOList;
    }

    /**
     * 组装学校和食堂名称
     * @param manageDTOList manageDTOList
     */
    private List<WmCanteenStallManageDTO> getManageDTOListWithSchoolAndCanteenName(List<WmCanteenStallManageDTO> manageDTOList) {
        // 1-根据食堂主键ID查询食堂信息
        List<Integer> canteenPrimaryIdList = manageDTOList.stream()
                .map(WmCanteenStallManageDTO::getCanteenPrimaryId)
                .distinct()
                .collect(Collectors.toList());
        List<WmCanteenDB> wmCanteenDBList = wmCanteenMapper.selectCanteensByIds(canteenPrimaryIdList);
        Map<Integer, WmCanteenDB> canteenMap = wmCanteenDBList.stream()
                .collect(Collectors.toMap(WmCanteenDB::getId, wmCanteenDB -> wmCanteenDB));

        // 2-根据学校主键ID查询学校信息
        List<Integer> schoolPrimaryIdList = wmCanteenDBList.stream()
                .map(WmCanteenDB::getSchoolId)
                .distinct()
                .collect(Collectors.toList());
        List<WmSchoolDB> wmSchoolDBList = wmSchoolMapper.selectSchoolByIds(schoolPrimaryIdList);
        Map<Integer, WmSchoolDB> schoolMap = wmSchoolDBList.stream()
                .collect(Collectors.toMap(WmSchoolDB::getId, wmSchoolDB -> wmSchoolDB));

        for (WmCanteenStallManageDTO manageDTO : manageDTOList) {
            WmCanteenDB wmCanteenDB = canteenMap.get(manageDTO.getCanteenPrimaryId());
            manageDTO.setCanteenName(wmCanteenDB == null ? "-" : wmCanteenDB.getCanteenName());
            manageDTO.setSchoolPrimaryId(wmCanteenDB == null ? 0 : wmCanteenDB.getSchoolId());

            WmSchoolDB wmSchoolDB = schoolMap.get(manageDTO.getSchoolPrimaryId());
            manageDTO.setSchoolName(wmSchoolDB == null ? "-" : wmSchoolDB.getSchoolName());
            manageDTO.setSchoolClueId(wmSchoolDB == null ? 0L : wmSchoolDB.getWdcClueId());
        }

        return manageDTOList;
    }

    /**
     * 创建食堂档口管理任务(批量创建线索)
     * @param canteenPrimaryId 食堂主键ID
     * @param userId 操作用户ID
     * @return 档口管理任务主键ID
     */
    public Integer createCanteenStallManageByBatchCreateClue(Integer canteenPrimaryId, Integer userId) throws WmSchCantException {
        log.info("[WmCanteenStallManageService.createCanteenStallManageByBatchCreateClue] canteenPrimaryId = {}, userId = {}", canteenPrimaryId, userId);
        WmCanteenStallManageDO canteenStallManageDO = new WmCanteenStallManageDO();
        canteenStallManageDO.setCanteenPrimaryId(canteenPrimaryId);
        // 提交状态=未提交
        canteenStallManageDO.setSubmitStatus((int) CanteenStallManageSubmitStatusEnum.NOT_SUBMITTED.getType());
        // 任务类型=批量创建线索
        canteenStallManageDO.setTaskType((int) CanteenStallManageTaskTypeEnum.BATCH_CREATE_CLUE.getType());
        canteenStallManageDO.setCuid(userId);
        canteenStallManageDO.setMuid(userId);
        int result = wmCanteenStallManageMapper.insertSelective(canteenStallManageDO);
        if (result <= 0 || canteenStallManageDO.getId() == null) {
            log.error("[WmCanteenStallManageService.createCanteenStallManageByBatchCreateClue] insertSelective error. canteenStallManageDO = {}",
                    JSONObject.toJSONString(canteenStallManageDO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "创建档口管理任务异常");
        }

        return canteenStallManageDO.getId();
    }

    /**
     * 创建食堂档口管理任务(公海绑定)
     * @param canteenPrimaryId 食堂主键ID
     * @param userId 操作用户ID
     * @return 档口管理任务主键ID
     */
    public Integer createCanteenStallManageByWdcBind(Integer canteenPrimaryId, Integer userId) throws WmSchCantException {
        log.info("[WmCanteenStallManageService.createCanteenStallManageByWdcBind] canteenPrimaryId = {}, userId = {}", canteenPrimaryId, userId);
        WmCanteenStallManageDO canteenStallManageDO = new WmCanteenStallManageDO();
        canteenStallManageDO.setCanteenPrimaryId(canteenPrimaryId);
        // 提交状态=已提交
        canteenStallManageDO.setSubmitStatus((int) CanteenStallManageSubmitStatusEnum.SUBMITTED.getType());
        // 任务类型=公海绑定
        canteenStallManageDO.setTaskType((int) CanteenStallManageTaskTypeEnum.WDC_BIND.getType());
        canteenStallManageDO.setCuid(userId);
        canteenStallManageDO.setMuid(userId);
        int result = wmCanteenStallManageMapper.insertSelective(canteenStallManageDO);
        if (result <= 0 || canteenStallManageDO.getId() == null) {
            log.error("[WmCanteenStallManageService.createCanteenStallManageByWdcBind] insertSelective error. canteenStallManageDO = {}",
                    JSONObject.toJSONString(canteenStallManageDO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "创建档口管理任务异常");
        }

        return canteenStallManageDO.getId();
    }

    /**
     * 创建食堂档口管理任务(外卖门店绑定)
     * @param canteenPrimaryId 食堂主键ID
     * @param userId 操作用户ID
     * @return 档口管理任务主键ID
     */
    public Integer createCanteenStallManageByWmPoiBind(Integer canteenPrimaryId, Integer userId, Integer taskType) throws WmSchCantException {
        log.info("[WmCanteenStallManageService.createCanteenStallManageByWmPoiBind] canteenPrimaryId = {}, userId = {}", canteenPrimaryId, userId);
        WmCanteenStallManageDO canteenStallManageDO = new WmCanteenStallManageDO();
        canteenStallManageDO.setCanteenPrimaryId(canteenPrimaryId);
        // 提交状态=已提交
        canteenStallManageDO.setSubmitStatus((int) CanteenStallManageSubmitStatusEnum.SUBMITTED.getType());
        // 任务类型=外卖门店绑定
        canteenStallManageDO.setTaskType(taskType);
        canteenStallManageDO.setCuid(userId);
        canteenStallManageDO.setMuid(userId);
        int result = wmCanteenStallManageMapper.insertSelective(canteenStallManageDO);
        if (result <= 0 || canteenStallManageDO.getId() == null) {
            log.error("[WmCanteenStallManageService.createCanteenStallManageByWmPoiBind] insertSelective error. canteenStallManageDO = {}",
                    JSONObject.toJSONString(canteenStallManageDO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "创建档口管理任务异常");
        }

        log.info("[WmCanteenStallManageService.createCanteenStallManageByWmPoiBind] manageId = {}", canteenStallManageDO.getId());
        return canteenStallManageDO.getId();
    }

    /**
     * 更新档口管理任务提交状态为"已提交"
     * @param manageId 档口管理任务ID
     */
    public void updateStallManageSubmitStatusToSubmitted(Integer manageId) {
        WmCanteenStallManageDO canteenStallManageDO = new WmCanteenStallManageDO();
        canteenStallManageDO.setId(manageId);
        canteenStallManageDO.setSubmitStatus((int) CanteenStallManageSubmitStatusEnum.SUBMITTED.getType());
        wmCanteenStallManageMapper.updateByPrimaryKeySelective(canteenStallManageDO);
    }

    /**
     * 创建管理档口关联关系
     * @param bindId 档口绑定ID
     * @param manageId 档口管理ID
     */
    public void createStallManageBindRel(Integer bindId, Integer manageId) {
        log.info("[WmCanteenStallManageService.createStallManageBindRel] input param: bindId = {}, manageId = {}", bindId, manageId);
        WmCanteenStallManageBindRelDO bindRelDO = new WmCanteenStallManageBindRelDO();
        bindRelDO.setBindId(bindId);
        bindRelDO.setManageId(manageId);
        wmCanteenStallManageBindRelMapper.insertSelective(bindRelDO);
    }

    /**
     * 公海绑定提交
     * @param submitDTO 提交DTO
     * @return 档口管理ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Integer submitByWdcClueBind(WmCanteenStallBindSubmitDTO submitDTO) throws WmSchCantException, TException {
        log.info("[WmCanteenStallService.submitByWdcClueBind] input param: submitDTO = {}", JSONObject.toJSONString(submitDTO));
        if (submitDTO == null || submitDTO.getCanteenPrimaryId() == null || CollectionUtils.isEmpty(submitDTO.getWdcClueIdList())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        // 1-创建档口管理任务(提交状态为"已提交")
        Integer manageId = createCanteenStallManageByWdcBind(submitDTO.getCanteenPrimaryId(), submitDTO.getUserId());

        // 2-更新/创建档口绑定任务
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindService.upsertCanteenStallBindListByWdcBind(submitDTO, manageId);

        // 3-外卖门店绑定流程(异步)
        bindCanteenPoiByWdcBindAsync(bindDOList, submitDTO.getUserId(), submitDTO.getUserName());

        return manageId;
    }

    /**
     * 外卖门店绑定流程(异步)
     * @param bindDOList 档口绑定列表
     * @param userId 用户ID
     * @param userName 用户名称
     */
    private void bindCanteenPoiByWdcBindAsync(List<WmCanteenStallBindDO> bindDOList, Integer userId, String userName) {
        WmCanteenStallWmPoiBindBO wmPoiBindBO = new WmCanteenStallWmPoiBindBO();
        wmPoiBindBO.setUserId(userId);
        wmPoiBindBO.setUserName(userName);
        wmPoiBindBO.setBindDOList(bindDOList);
        wmPoiBindBO.setOperation(CanteenStallManageTaskTypeEnum.WDC_BIND.getName());

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                wmCanteenStallPoiBindService.bindCanteenPoiByWdcBind(wmPoiBindBO);
            } catch (Exception e) {
                log.error("[WmCanteenStallManageService.bindCanteenPoiByWdcBindAsync] Exception. bindDOList = {}, userId = {}, userName = {}",
                        JSONObject.toJSONString(bindDOList), userId, userName, e);
            }
        })).start();
    }

    /**
     * 外卖门店绑定提交
     * @param submitDTO 提交DTO
     * @return 档口管理ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Integer submitByWmPoiBind(WmCanteenStallBindSubmitDTO submitDTO) throws WmSchCantException, TException {
        log.info("[WmCanteenStallService.submitByWmPoiBind] input param: submitDTO = {}", JSONObject.toJSONString(submitDTO));
        if (submitDTO == null || submitDTO.getCanteenPrimaryId() == null || CollectionUtils.isEmpty(submitDTO.getWmPoiIdList())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        // 1-创建档口管理任务(提交状态为"已提交")
        Integer manageId = createCanteenStallManageByWmPoiBind(submitDTO.getCanteenPrimaryId(), submitDTO.getUserId(), (int) CanteenStallManageTaskTypeEnum.WM_POI_BIND.getType());

        // 2-更新/创建档口绑定任务(key->wmPoiId val->bindDO)
        Map<Long, WmCanteenStallBindDO> bindDOMap = wmCanteenStallBindService.upsertCanteenStallBindListByWmPoiBind(submitDTO, manageId);

        // 3-外卖门店绑定流程(异步执行)
        bindCanteenPoiByWmPoiBindAsync(bindDOMap, submitDTO.getUserId(), submitDTO.getUserName());

        return manageId;
    }

    /**
     * 外卖门店绑定流程(异步执行)
     * @param userId 用户ID
     * @param userName 用户名称
     * @param bindDOMap key->wmPoiId val->bindDO
     */
    private void bindCanteenPoiByWmPoiBindAsync(Map<Long, WmCanteenStallBindDO> bindDOMap, Integer userId, String userName) {
        WmCanteenStallWmPoiBindBO wmPoiBindBO = new WmCanteenStallWmPoiBindBO();
        wmPoiBindBO.setUserId(userId);
        wmPoiBindBO.setUserName(userName);
        wmPoiBindBO.setBindDOMap(bindDOMap);
        wmPoiBindBO.setOperation(CanteenStallManageTaskTypeEnum.WM_POI_BIND.getName());

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                wmCanteenStallPoiBindService.bindCanteenPoiByWmPoiBind(wmPoiBindBO);
            } catch (Exception e) {
                log.error("[WmCanteenStallManageService.bindCanteenPoiByWmPoiBindAsync] Exception. bindDOMap = {}",
                        JSONObject.toJSONString(bindDOMap), e);
            }
        })).start();
    }

    /**
     * 初始化食堂档口管理和绑定任务
     * @param canteenId 食堂ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void initiateCanteenStallManageAndBind(Integer canteenId) throws WmSchCantException, TException {
        log.info("[WmCanteenStallService.initiateCanteenStallManageAndBind] input param: canteenId = {}", canteenId);
        // 1-取消所有正在流程中的绑定门店流程、换绑门店流程
        Integer canteenPrimaryId = WmScTransUtil.getCanteenPrimaryIdByCanteenId(canteenId);
        if (wmScCanteenService.isCanteenBindingPoi(canteenPrimaryId)) {
            WmCanCancelBo wmCanCancelBo = new WmCanCancelBo();
            wmCanCancelBo.setId(canteenPrimaryId);
            wmCanCancelBo.setUserId(0);
            wmCanCancelBo.setUserName("系统初始化");
            wmScCanteenPoiService.cancelTask(wmCanCancelBo);
        }

        // 2-判断是否有绑定的门店
        List<Long> wmPoiIdList = wmScOuterService.getWmPoiIdsByCanteenId(canteenPrimaryId);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            log.info("[WmCanteenStallService.initiateCanteenStallManageAndBind] wmPoiIdList is empty. canteenPrimaryId = {}", canteenPrimaryId);
            return;
        }

        // 3-生成档口管理任务
        Integer manageId = createCanteenStallManageByWmPoiBind(canteenPrimaryId, 0, (int) CanteenStallManageTaskTypeEnum.WM_POI_BIND.getType());

        // 4-生成档口绑定任务 & 档口管理绑定关系
        wmCanteenStallBindService.createCanteenStallBindListByInit(canteenPrimaryId, wmPoiIdList, manageId);
    }


}
