package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealTypeService;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户类型校验
 */
@Service
@Slf4j
public class WmCustomerRealTypeValidator implements IWmCustomerValidator {

    @Autowired
    private WmCustomerRealTypeService wmCustomerRealTypeService;

    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        try {
            // 校验客户类型
            return wmCustomerRealTypeService.validateCustomerRealType(wmCustomerBasicBo, opUid);
//            return wmCustomerService.validateCustomerRealType(wmCustomerBasicBo, opUid);
        } catch (Exception e) {
            log.warn("校验客户类型异常 wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "校验客户类型异常");
        }
    }
}
