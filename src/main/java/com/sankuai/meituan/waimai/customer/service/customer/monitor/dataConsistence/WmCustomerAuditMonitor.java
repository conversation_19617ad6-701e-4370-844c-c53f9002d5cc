package com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerAuditDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerAuditDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerAuditStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WmCustomerAuditMonitor {

    private static final List<Integer> AUDIT_RESULT = Lists.newArrayList(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode(), CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode());

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmCustomerAuditDBMapper wmCustomerAuditDBMapper;

    public String check(int bizType, int customerId, int bizId) {
        log.info("WmCustomerAuditMonitor.check  bizType={},customerId={},bizId={}", bizType, customerId, bizId);
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            return String.format("未找到客户信息%s", customerId);
        }
        WmCustomerAuditDB wmCustomerAuditDB = wmCustomerAuditDBMapper.selectCustomerAuditById(bizId);
        if (wmCustomerAuditDB != null && wmCustomerAuditDB.getAuditStatus() != null && !AUDIT_RESULT.contains(wmCustomerAuditDB.getAuditStatus())) {
            return String.format("客户审核结果未同步:customerId:%s,bizId:%s", customerId, bizId);
        }
        return "";
    }
}
