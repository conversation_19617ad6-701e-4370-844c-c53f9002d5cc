package com.sankuai.meituan.waimai.customer.domain.sc.delivery;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryEffectiveStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryStreamNodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学校交付流程子表DO
 * <AUTHOR>
 * @date 2024/02/22
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmSchoolDeliveryStreamDetailDO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 交付编号ID
     */
    private Integer deliveryId;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 交付节点类型
     * {@link SchoolDeliveryStreamNodeEnum}
     */
    private Integer deliveryNodeType;
    /**
     * 是否生效
     * {@link SchoolDeliveryEffectiveStatusEnum}
     */
    private Integer effective;
    /**
     * 当前审批状态
     * {@link SchoolDeliveryAuditStatusEnum}
     */
    private Integer auditStatus;
    /**
     * 是否有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
}
