package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.ParamInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCancelAuthInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 电子合同解绑
 */
@Service
public class WmEcontractCancelAuthDataWrapperService implements IWmEcontractDataParamWrapperService{

    private static final String TEMPLET_NAME = "cancel_auth";

    @Override
    public ParamInfoBo wrap(EcontractBatchContextBo contextBo)
        throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.CUSTOMER);

        return new ParamInfoBo.Builder()
            .metaContent(generate(contextBo, taskBo))
            .templateName(TEMPLET_NAME)
            .build();
    }

    private Map<String, String> generate(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        EcontractCancelAuthInfoBo cancelInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractCancelAuthInfoBo.class);
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("wmPoiName", StringUtils.defaultIfEmpty(cancelInfoBo.getWmPoiName(), StringUtils.EMPTY));
        paramMap.put("customerName", StringUtils.defaultIfEmpty(cancelInfoBo.getCustomerName(), StringUtils.EMPTY));
        paramMap.put("customerNumber", StringUtils.defaultIfEmpty(cancelInfoBo.getCustomerNumber(), StringUtils.EMPTY));
        return paramMap;
    }


}
