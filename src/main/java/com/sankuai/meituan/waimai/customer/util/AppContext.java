package com.sankuai.meituan.waimai.customer.util;

import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class AppContext {

    private static final Logger logger = LoggerFactory.getLogger(AppContext.class);

    private static ThreadLocal<Boolean> shouldLazyProcess = new ThreadLocal<>();

    private static ThreadLocal<List<LazyTask>> lazyProcessQueue = new ThreadLocal<>();

    public static void lazyProcess(boolean lazy) {
        shouldLazyProcess.set(lazy);
        lazyProcessQueue.set(Lists.<LazyTask>newArrayList());
    }

    public static void clear() {
        shouldLazyProcess.remove();
        lazyProcessQueue.remove();
    }

    public static boolean isLazyProcess() {
        Boolean aBoolean = shouldLazyProcess.get();
        return aBoolean != null && aBoolean;
    }

    public static void executeLazyTask() {
        List<LazyTask> lazyTasks = lazyProcessQueue.get();
        if (CollectionUtils.isEmpty(lazyTasks)) {
            return;
        }
        logger.info("延迟处理任务总数有 {}", lazyTasks.size());
        for (int i = 0; i < lazyTasks.size(); i++) {
            try {
                LazyTask lazyTask = lazyTasks.get(i);
                logger.info("延迟处理任务{} lazyTask:{}", i + 1, lazyTask.taskDesc());
                lazyTask.lazyProcess();
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    public static void offerLazyTask(LazyTask lazyTask) {
        List<LazyTask> lazyTasks = lazyProcessQueue.get();
        if (lazyTasks == null) {
            return;
        }
        lazyTasks.add(lazyTask);
    }

    public interface LazyTask {
        void lazyProcess();

        String taskDesc();
    }

}
