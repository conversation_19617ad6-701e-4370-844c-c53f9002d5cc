package com.sankuai.meituan.waimai.customer.service.sc.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.ColumnInfo;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.lion.client.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * 门店绑定/解绑食堂的时候监控门店打/掉食堂标签是否正常
 *
 * <AUTHOR>
 * @date 2022年03月01日
 */
public class WmScCanteenPoiTagRuleRunner extends DefaultRuleRunner {

    @Override
    public String check(RawData triggerData, RawData... targetData) throws Exception {
        BinlogRawData binlogRawData = (BinlogRawData) triggerData;
        String tableName = binlogRawData.getRealTableName();
        Map<String, ColumnInfo> columnInfoMap = binlogRawData.getColumnInfoMap();

        RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenPoiThriftService",
                "com.sankuai.waimai.e.customer", 10000, null, "8458");
        Map<String, Object> params = Maps.newHashMap();

        if ("wm_sc_canteen_poi_attribute".equals(tableName)) {
            String validOld = columnInfoMap.get("valid").getOldValue() == null ? "" : columnInfoMap.get("valid").getOldValue().toString();
            String validNew = columnInfoMap.get("valid").getNewValue().toString();
            if (validOld.equals(validNew)) {
                return null;
            }
            params.put("wmPoiId", columnInfoMap.get("wm_poi_id").getNewValue());
            params.put("canteenPrimaryId", columnInfoMap.get("canteen_primary_id").getNewValue());
            params.put("valid", columnInfoMap.get("valid").getNewValue());
        }

        String result = rpcService.invoke("monitorScCanteenPoiTag",
                Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.sc.monitor.MonitorScCanteenPoiTagDTO"),
                Lists.newArrayList(JsonUtils.toJson(params)));
        if (!StringUtils.isBlank(result) && !result.equals("\"\"")) {
            return result;
        }
        return null;
    }

    /**
     * 默认情况下alarm方法会自动发送大象告警(不要覆盖该方法)，如需通过mafka或者泛化调用等方式请复写alarm方法
     *
     * @param checkResult 是check方法的返回结果，传入alarm方便进行告警
     * @param triggerData 触发数据：触发数据源的数据
     * @param targetData  目标数据：根据触发数据源的设置的key对应的其他源数据源的数据；单流校验为空，多流校验则为所有数据源数据除去触发数据
     */
    @Override
    public void alarm(String checkResult, RawData triggerData, RawData... targetData) throws Exception {
        DXUtil.sendAlarm(checkResult);
    }
}
