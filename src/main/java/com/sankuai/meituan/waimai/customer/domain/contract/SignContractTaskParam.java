package com.sankuai.meituan.waimai.customer.domain.contract;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2023/3/14 4:06 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class SignContractTaskParam {

    private Long customerId;

    private Long startTime;

    private Long endTime;

    private String batchStatus;

    private Integer maxLimit;

}
