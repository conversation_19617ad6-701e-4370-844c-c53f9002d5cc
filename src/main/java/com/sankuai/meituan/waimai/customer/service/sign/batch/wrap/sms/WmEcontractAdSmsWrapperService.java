package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.SMSConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 广告协议
 */
@Service
public class WmEcontractAdSmsWrapperService extends AbstractWmEcontractSmsWrapperService {

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Resource
    private EmpServiceAdaptor empServiceAdaptor;

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo) throws TException, WmCustomerException {
        List<Long> wmPoiIdList= wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(contextBo.getCustomerId());
        WmEmploy wmEmploy = getOwner(contextBo.getCustomerId());

        Map<String, String> smsParamMap = Maps.newHashMap();
        smsParamMap.put("reason", analysisReason(contextBo));
        smsParamMap.put("detail", analysisDetail(contextBo));
        smsParamMap.put("name", analysisName(wmEmploy));
        smsParamMap.put("phone", analysisPhone(wmEmploy));

        boolean isPackSign = false;
        Map<String, String> packSmsParamMap = Maps.newHashMap();
        if(contextBo.getSignPackId() > 0 && wmEcontractCustomerPackService.manualSignUpdateGray(contextBo.getCustomerId())){
            isPackSign = true;
            assembleSmsParamMap(packSmsParamMap, contextBo, wmPoiIdList);
        } else {
            // 非手动打包场景
            // 判断是否展示虚拟号
            boolean isShowSgVirtualNumber = isShowVirtualNumByContextBo(contextBo);
            if (isShowSgVirtualNumber) {
                // 设置展示优先级（手动打包任务中，客服号与虚拟号同时存在时，优先展示虚拟号）
                contextBo.setPriority(1);
                // 覆盖原有的phone
                smsParamMap.put("phone", MccConfig.getShanGouBdVirtualNumber());
            }
        }

        /*实名认证信息*/
        SignerInfoBo.Builder signerInfoBoBuilder = new SignerInfoBo.Builder()
                /*签约人信息*/
                .setName(contextBo.getKpBo().getSignerName())
                .setIdCardNo(contextBo.getKpBo().getSignerIDCardNum())
                .setPhone(contextBo.getKpBo().getSignerPhoneNum())
                .setBankName(contextBo.getKpBo().getSignerBankName())
                .setBankCardNo(contextBo.getKpBo().getSignerBankCardNum())
                /*短信平台配置*/
                .setClientId(SMSConstant.contractClientId)
                .setClientSecret(SMSConstant.contractClientSecret)
                .setSmsTemplateId(isPackSign ? SMSConstant.TEMPLATE_ECONTRACT : SMSConstant.TEMPLATE_AD)
                .setSmsTempletVersion(SMSConstant.smsTempletVersion)
                /*短信信息*/
                .setSmsParamMap(isPackSign ? packSmsParamMap : smsParamMap)
                .setMobileList(Lists.newArrayList(contextBo.getKpBo().getSignerPhoneNum()));

        if (!CollectionUtils.isEmpty(wmPoiIdList) && wmPoiIdList.size() == 1) {
            signerInfoBoBuilder.setChannelList(Lists.newArrayList(SMSConstant.SMS, SMSConstant.WM_MERCHANT_CHANNEL));
        }

        signerInfoBoBuilder.setCertifyH5InfoBo(assemblyCertifyInfo(contextBo));

        SignerInfoBo signerInfoBo=signerInfoBoBuilder.build();

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.REALNAME_AUTH_POI)
                .signerInfoBo(signerInfoBo)
                .build();
    }

    private String analysisReason(EcontractBatchContextBo contextBo) throws WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POI_PROMOTION_SERVICE);
        JSONObject jsonObject = JSON.parseObject(taskBo.getApplyContext());
        String partBName = jsonObject.getString("partBName");
        StringBuilder sb = new StringBuilder("由于美团外卖内部业务调整，门店推广的部分业务将由");
        sb.append(partBName);
        sb.append("承接");
        return sb.toString();
//        return ConfigUtilAdapter.getString("poi_promotion_service_sms_reason", "由于美团外卖内部业务调整，门店推广的部分业务将由上海三快智送科技有限公司承接");
    }

    @Override
    public String analysisDetail(EcontractBatchContextBo contextBo) {
        return contextBo.getCustomerInfoBo().getCustomerName();
    }

    private String analysisName(WmEmploy wmEmploy) {
        if (wmEmploy == null) {
            return "";
        }
        return wmEmploy.getName();
    }

    private String analysisPhone(WmEmploy wmEmploy) {

        if (wmEmploy == null) {
            return "";
        }
        return MoreObjects.firstNonNull(empServiceAdaptor.getPhone(wmEmploy.getUid()), "");
    }

}
