package com.sankuai.meituan.waimai.customer.statemachine.core.biz;

import com.sankuai.meituan.waimai.customer.statemachine.core.flowpath.parse.DefaultFlowDefinitionParser;
import com.sankuai.meituan.waimai.customer.statemachine.core.flowpath.parse.FlowDefinitionParser;
import com.sankuai.meituan.waimai.customer.statemachine.spi.store.FlowStoreManager;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * 流程状态机配置信息.
 *
 * Created by jinh<PERSON> on 16/8/9.
 */
public class FlowStateMachineConfig {

    /**
     * 状态机实列key.
     */
    private final String appKey;

    /**
     * 流程定义文件位置.
     */
    private final String definitionFile;

    /**
     * 流程对象存储管理
     */
    private final FlowStoreManager flowStoreManager;

    /**
     * 状态机配置文件解析器
     */
    private FlowDefinitionParser flowDefinitionParser;

    /**
     * 状态机使用的事务管理器.
     */
    private DataSourceTransactionManager transactionManager;

    public FlowStateMachineConfig(String appKey, String definitionFile, FlowStoreManager flowStoreManager) {
        this(appKey, definitionFile, flowStoreManager, null);
    }

    public FlowStateMachineConfig(String appKey, String definitionFile, FlowStoreManager flowStoreManager,
                                  DataSourceTransactionManager transactionManager) {
        this.appKey = appKey;
        this.definitionFile = definitionFile;
        this.flowStoreManager = flowStoreManager;
        this.flowDefinitionParser = new DefaultFlowDefinitionParser();
        this.transactionManager = transactionManager;
    }

    public String getAppKey() {
        return appKey;
    }

    public String getDefinitionFile() {
        return definitionFile;
    }

    public FlowStoreManager getFlowStoreManager() {
        return flowStoreManager;
    }

    public FlowDefinitionParser getFlowDefinitionParser() {
        return flowDefinitionParser;
    }

    public DataSourceTransactionManager getTransactionManager() {
        return transactionManager;
    }
}
