package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.multi;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.MULTI_PERFORMANCE_SERVICE_FEE_V2)
@Slf4j
@Service
public class WmEcontractMultiPerformanceServiceFeeV2PdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Autowired
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException, TException {
        log.info("#WmEcontractMultiPerformanceServiceFeeV2PdfMaker");
        SignTemplateEnum signTemplateEnum = extractSignTemplateEnum();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<EcontractDeliveryInfoBo> deliveryInfoList;
        if (wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(originContext.getCustomerId())) {
            deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT.getName());
        } else {
            deliveryInfoList = batchDeliveryInfoBo.getEcontractDeliveryInfoBoList();
        }

        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();
        // 模板兼容
        pdfMetaContent.put("hasMtDelivery", "notSupport");
        for (EcontractDeliveryInfoBo temp : deliveryInfoList) {
            fillSupportData(temp, pdfMetaContent, pdfBizContent);
        }

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        pdfInfoBo.setPdfBizContent(pdfBizContent);

        // 商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("signTime", org.apache.commons.lang.StringUtils.defaultIfEmpty(signTime, org.apache.commons.lang.StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", org.apache.commons.lang.StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), org.apache.commons.lang.StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        log.info("#WmEcontractMultiPerformanceServiceFeeV2PdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }

    private void fillSupportData(EcontractDeliveryInfoBo temp, Map<String, String> pdfMetaContent, List<Map<String, String>> pdfBizContent) throws IllegalAccessException {
        temp.setDeliveryArea(null);
        temp.setEcontractDeliveryWholeCityInfoBo(null);
        temp.setEcontractDeliveryAggregationInfoBo(null);

        boolean hasSLA = SUPPORT_MARK.equals(temp.getSupportSLA()) ? true : false;
        boolean hasNewModel = SUPPORT_MARK.equals(temp.getSupportNewModle()) ? true : false;
        boolean hasCompanyCustomerDelivery = SUPPORT_MARK.equals(temp.getSupportCompanyCustomerDelivery()) ? true : false;
        boolean hasMtDelivery = SUPPORT_MARK.equals(temp.getSupportMTDelivery()) ? true : false;
        boolean hasSGV2_1Support = SUPPORT_MARK.equals(temp.getSupportSGV2_1Delivery()) ? true : false;
        boolean hasSGV2_2Support = SUPPORT_MARK.equals(temp.getSupportSGV2_2Delivery()) ? true : false;
        boolean hasSGMedicineV2_1Support = SUPPORT_MARK.equals(temp.getSupportSGMedicineV2_1Delivery()) ? true : false;
        boolean hasAgentNewSupport = SUPPORT_MARK.equals(temp.getSupportAgentNewDelivery()) ? true : false;

        if (SUPPORT_MARK.equals(temp.getSupportExclusive())) {
            pdfMetaContent.put("hasSupport", "hasSupport");
        }
        if (hasSLA) {
            pdfMetaContent.put("hasSLASupport", "hasSupport");
        }
        if (hasSGV2_1Support && hasMtDelivery) {
            pdfMetaContent.put("hasSGV2_1Support", "hasSupport");
            pdfBizContent.add(MapUtil.Object2Map(temp));
        }
        if (hasSGMedicineV2_1Support && hasMtDelivery) {
            pdfMetaContent.put("hasSGMedicineV2_1Support", "hasSupport");
            pdfBizContent.add(MapUtil.Object2Map(temp));
        }
        // 闪购2.2区分新快速和非新快速模板
        if (hasSGV2_2Support && hasMtDelivery) {
            // 新快送模板
            if (temp.getEcontractDeliverySG2_2InfoBo() != null
                    && SUPPORT_MARK.equals(temp.getEcontractDeliverySG2_2InfoBo().getSupportNewKS())) {
                // 外层字段-用于判断是否渲染对应模板
                pdfMetaContent.put("hasSGV2_2XKSSupport", "hasSupport");
                // 内层数组-用于对应模板字段渲染
                Map<String, String> pdfMetaContentMap = MapUtil.Object2Map(temp);
                pdfMetaContentMap.put("supportSGV2_2XKS", SUPPORT_MARK);
                pdfMetaContentMap.putAll(MapUtil.Object2Map(temp.getEcontractDeliverySG2_2InfoBo()));
                pdfBizContent.add(pdfMetaContentMap);
            } else {
                // 外层字段-用于判断是否渲染对应模板
                pdfMetaContent.put("hasSGV2_2Support", "hasSupport");
                // 内层数组-用于对应模板字段渲染
                Map<String, String> pdfMetaContentMap = MapUtil.Object2Map(temp);
                pdfMetaContentMap.put("supportSGV2_2", SUPPORT_MARK);
                if(temp.getEcontractDeliverySG2_2InfoBo() != null){
                    pdfMetaContentMap.putAll(MapUtil.Object2Map(temp.getEcontractDeliverySG2_2InfoBo()));
                }
                pdfBizContent.add(pdfMetaContentMap);
            }
        }
        if (hasCompanyCustomerDelivery) {
            pdfMetaContent.put("hasCompanyCustomerDelivery", "hasSupport");
            pdfBizContent.add(MapUtil.Object2Map(temp));
        }
        if (hasMtDelivery && hasNewModel && hasSLA) {
            pdfMetaContent.put("hasMtDelivery", "hasSupport");
            pdfBizContent.add(MapUtil.Object2Map(temp));
        }
        if (hasMtDelivery && hasAgentNewSupport) {
            pdfMetaContent.put("hasAgentNewSupport", "hasSupport");
            pdfBizContent.add(MapUtil.Object2Map(temp));
        }
    }
}
