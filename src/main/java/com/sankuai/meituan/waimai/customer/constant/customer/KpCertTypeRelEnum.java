package com.sankuai.meituan.waimai.customer.constant.customer;

import com.sankuai.conch.certify.tokenaccess.enums.CertificateType;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.nibcus.inf.customer.client.enums.CertificateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum KpCertTypeRelEnum {

    IDENTITY_CARD(CertificateTypeEnum.IDENTITY_CARD,CertTypeEnum.ID_CARD, "身份证"),
    CHINESE_PASSPORT(CertificateTypeEnum.CHINESE_PASSPORT, CertTypeEnum.PASSPORT,"中国护照"),
    MAINLAND_TRAVEL_PERMIT_FOR_HONG_KONG_AND_MACAO_RESIDENTS(CertificateTypeEnum.MAINLAND_TRAVEL_PERMIT_FOR_HONG_KONG_AND_MACAO_RESIDENTS,CertTypeEnum.HK_MACAO_REENTRY_PERMIT, "港澳居民往来内地通行证"),
    HONG_KONG_AND_MACAO_RESIDENT_RESIDENCE_PERMIT(CertificateTypeEnum.HONG_KONG_AND_MACAO_RESIDENT_RESIDENCE_PERMIT, CertTypeEnum.REDIDENCE_PERMIT_OF_HK_MACAO_TAIWAI,"港澳居民居住证"),
    MAINLAND_TRAVEL_PERMIT_FOR_TAIWAN_RESIDENTS(CertificateTypeEnum.MAINLAND_TRAVEL_PERMIT_FOR_TAIWAN_RESIDENTS, CertTypeEnum.TAIWAN_REENTRY_PERMIT,"台湾居民往来大陆通行证"),
    TAIWAN_RESIDENT_RESIDENCE_PERMIT(CertificateTypeEnum.TAIWAN_RESIDENT_RESIDENCE_PERMIT,CertTypeEnum.REDIDENCE_PERMIT_OF_HK_MACAO_TAIWAI, "台湾居民居住证"),
    ;

    private  final CertificateTypeEnum dcCertType;

    private  final CertTypeEnum wmCertType;

    private final String desc;

    public static CertTypeEnum getWmCertTypeByDcCertType(CertificateTypeEnum dcCertType) {
        if (dcCertType != null){
            for (KpCertTypeRelEnum value : KpCertTypeRelEnum.values()) {
                if (value.getDcCertType().equals(dcCertType)) {
                    return value.getWmCertType();
                }
            }
        }
        return null;
    }
}
