package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.label.thrift.exception.WmLabelException;
import com.sankuai.meituan.waimai.label.thrift.service.WmLabelThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WmLabelAdaptor {

    @Autowired
    private WmLabelThriftService wmLabelThriftService;

    /**
     * @param subjectIds  打标对象ID，比如门店ID、客户ID、品牌ID等。注意这里门店ID、客户ID具体指什么，请先跟标签产品确认。
     * @param subjectType 对象类型，见{@link LabelSubjectTypeEnum}
     * @param labelId     标签ID。注意线上线下标签ID不一致，请与自己的产品确定好。
     * @return
     */
    public boolean hasTargetLable(List<Long> subjectIds, int subjectType, int labelId) throws WmCustomerException {
        log.info("查询目标标签，subjectIds:{}，subjectType:{}，labelId:{}", subjectIds, subjectType, labelId);
        try {
            Map<Long, Boolean> labelMap = wmLabelThriftService.haveLabelRel(subjectIds, subjectType, labelId);
            log.info("hasTargetLable resultMap:{}", JSON.toJSONString(labelMap));
            if(MapUtils.isNotEmpty(labelMap)){
                return labelMap.get(subjectIds.get(0));
            }
        } catch (WmLabelException e1) {
            log.error("查询目标标签业务异常，subjectIds:{}，subjectType:{}，labelId:{}", subjectIds, subjectType, labelId , e1);
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, e1.getMessage());
        } catch (Exception e2) {
            log.error("查询目标标签系统异常，subjectIds:{}，subjectType:{}，labelId:{}", subjectIds, subjectType, labelId , e2);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, e2.getMessage());
        }
        return false;
    }
}
