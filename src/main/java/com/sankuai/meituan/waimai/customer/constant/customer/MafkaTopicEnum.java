package com.sankuai.meituan.waimai.customer.constant.customer;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * mq主题
 */
public enum MafkaTopicEnum {

    /**
     * 从门店消息迁移，定义为客户变更的消息
     */
    MAFKA_WAIMAI_BRAND_CHANGE_COMMON_NOTICE("mafka.waimai.customer.change.notice", "客户变更消息");

    private String code;

    private String desc;


    MafkaTopicEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<String, MafkaTopicEnum> map = Maps.newHashMap();

    static {
        for (MafkaTopicEnum item : MafkaTopicEnum.values()) {
            map.put(item.code, item);
        }
    }

    public static Map<String, MafkaTopicEnum> getMap() {
        return map;
    }

    public static void setMap(Map<String, MafkaTopicEnum> map) {
        MafkaTopicEnum.map = map;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
