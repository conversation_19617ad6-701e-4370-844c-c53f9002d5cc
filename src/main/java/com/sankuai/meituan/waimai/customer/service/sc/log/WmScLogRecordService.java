package com.sankuai.meituan.waimai.customer.service.sc.log;

import com.sankuai.meituan.waimai.customer.dao.sc.WmScLogRecordMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScLogRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 校园食堂快照日志业务逻辑
 */
@Slf4j
@Service
public class WmScLogRecordService {

    @Autowired
    private WmScLogRecordMapper wmScLogRecordMapper;

    /**
     * 新增快照日志
     *
     * @param wmScLogRecordDO
     */
    public void insert(WmScLogRecordDO wmScLogRecordDO) {
        if (wmScLogRecordDO == null) {
            return;
        }
        wmScLogRecordMapper.insertSelective(wmScLogRecordDO);
    }
}
