package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.bind;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.CustomerPoiRelStrategyBean;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IBindCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.poirel.flow.CustomerPoiRelFlowEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.jeasy.rules.annotation.*;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240117
 * @desc 美食城 &无拼好饭移动参数子门店&无资质共用标签 绑定规则
 */
@Service
@Slf4j
@Rule
public class MscNoChildSignBindRule {

    /**
     * 条件判断是否:美食城 && 无拼好饭移动参数子门店 &客户无资质共用标签->预绑定
     *
     * @param context
     * @return
     */
    @Condition
    public boolean when(@Fact("context") CustomerPoiBindFlowContext context) {
        return (CustomerRealTypeEnum.MEISHICHENG.getValue() == context.getWmCustomerDB().getCustomerRealType()
                && !context.getHasCustomerQuaComTag() && MapUtils.isEmpty(context.getPhfChildPoiList()));
    }

    /**
     * 根据策略执行
     *
     * @return
     */
    @Action
    public void execute(@Fact("context") CustomerPoiBindFlowContext context) throws WmCustomerException {
        //定义各层级策略使用bean对象
        CustomerPoiRelStrategyBean customerPoiRelStrategyBean = CustomerPoiRelStrategyBean.builder()
                .checkBeanName("mscBindCheckStrategy")
                .preCoreBeanName("preBindPreCoreStrategy")
                .coreBeanName("preBindCoreStrategy")
                .afterBeanName("preBindAfterStrategy")
                .build();
        //根据bean名称计算各层级的实际策略信息
        BindStrategy strategy = BindStrategy.buiLdStrategyWithContext(customerPoiRelStrategyBean);
        //构建流程策略
        BindFlowStrategy bindFlowStrategy = BindFlowStrategy.builder()
                .flowEnum(CustomerPoiRelFlowEnum.PRE_BIND)
                .wmPoiIdSet(context.getWmPoiIdSet())
                .bindStrategy(strategy)
                .build();
        context.setBindFlowStrategyList(Lists.newArrayList(bindFlowStrategy));
    }


    @Priority
    public int getPriority() {
        return 2;
    }
}