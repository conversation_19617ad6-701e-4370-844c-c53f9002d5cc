package com.sankuai.meituan.waimai.customer.service.customer.dto;

import lombok.Data;

/**
 * 美食城客户门店信息
 */
@Data
public class MscPoiInfoDTO {

    /**
     * 门店id
     */
    private Long poiId;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 门店上单状态 0、下线；1、上线；2、上单中；3、审核通过可上线
     */
    private int poiValid;

    /**
     * 门店上单状态描述
     */
    private String poiValidDesc;

    /**
     * 商家类型
     */
    private String poiOwnerType;

    /**
     * 门店责任人
     */
    private Long poiOwnerUid;

    /**
     * 物理品牌ID
     */
    private Long poiOrigBrandId;

    /**
     * 物理品牌名称
     */
    private String poiOrigBrandName;

    /**
     * 业务品牌ID
     */
    private Long poiBrandId;

    /**
     * 业务品牌名称
     */
    private String poiBrandName;

    /**
     * 品牌责任人
     */
    private int poiSellerUid;

    /**
     * 品牌责任人：姓名
     */
    private String poiSellerName;

    /**
     * 品牌责任人：mis
     */
    private String poiSellerMis;

    /**
     * 门店责任人：姓名格式
     */
    private String poiOwnerName;

    /**
     * 门店责任人：mis
     */
    private String poiOwnerMis;

}
