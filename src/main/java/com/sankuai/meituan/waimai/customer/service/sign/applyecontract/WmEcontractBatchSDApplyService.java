package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.customer.service.sign.common.WmEcontractApplyTrans;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class WmEcontractBatchSDApplyService extends AbstractWmEcontractApplyAdapterService {

    @Resource
    private WmEcontractBatchSgDApplyService wmEcontractBatchSgDApplyService;

    @Resource
    private WmEcontractBatchSmDApplyService wmEcontractBatchSmDApplyService;
    @Resource
    private WmEcontractBatchSgDSupportApplyService wmEcontractBatchSgDSupportApplyService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo,EcontractSignDataFactor econtractSignDataFactor)
        throws TException, IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.SETTLE);
        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);

        econtractSignDataFactor = WmEcontractContextUtil
                .analysisEcontractSignDataFactor(batchContextBo);

        if (WmEcontractApplyTrans.isSupportWallet(settleInfoBoList)) {
            return wmEcontractBatchSmDApplyService.wrapEcontractBo(batchContextBo,econtractSignDataFactor);
        } else {
            Map<String, EcontractDataWrapperEnum> dataWrapperMapActually = WmEcontractContextUtil.analysisDataWrapperMapForDelivery(batchContextBo,
                    Maps.newHashMap());

            boolean hasSupport = WmEcontractContextUtil.isSupport(batchContextBo, dataWrapperMapActually);
            econtractSignDataFactor.setDeliverySupportExclusive(hasSupport);

            if (hasSupport || econtractSignDataFactor.isDeliverySupportAggregation()) {
                return wmEcontractBatchSgDSupportApplyService.wrapEcontractBo(batchContextBo,econtractSignDataFactor);
            } else {
                return wmEcontractBatchSgDApplyService.wrapEcontractBo(batchContextBo,econtractSignDataFactor);
            }
        }
    }
}
