package com.sankuai.meituan.waimai.customer.util;

import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.scm.cloud.service.MtCloudService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对客户系统图片地址转换的操作类
 */
@Service
public class CustomerPicUrlHelper {

    private final static String URL_SEPARATOR = ",";
    private static String URL_HTTP_CONSTANT = "http";
    private static String URL_PREFIX = "/";
    private static String URL_QUESTION_MARK = "?";

    @Resource(name = "mtCloudService")
    private MtCloudService mtCloudService;

    /**
     * 根据数据库中存储的图片地址，获取图片在美团云中的地址。</br>
     * 如果图片是多张，则用逗号分隔
     * @param picUrl
     * @return
     */
    public String getMtCloudUrls(String picUrl){
        String mtCloudUrl = "";
        List<String> list = getFileNames(picUrl);
        for(String fileName : list){
            mtCloudUrl += getMtCloudUrl(fileName) + URL_SEPARATOR;
        }

        if(mtCloudUrl.length() >0){
            mtCloudUrl = mtCloudUrl.substring(0,mtCloudUrl.length()-1);
        }
        return mtCloudUrl;
    }

    /**
     * 根据数据库中存储的图片地址，获取图片名称
     * @param picUrl
     * @return
     */
    public static List<String> getFileNames(String picUrl){

        List<String> listRet = Lists.newArrayList();
        if(StringUtils.isEmpty(picUrl)){
            return listRet;
        }

        String filePathArr [] = picUrl.split(URL_SEPARATOR);
        for(String filePath : filePathArr){
            listRet.add(getFileName(filePath));
        }

        return listRet;
    }


    private static String getFileName(String filePath){
        //只处理三种情况，其他情况无需处理

        String fileName = "";
        if(filePath.contains(URL_HTTP_CONSTANT)){
            //1、已http开头的数据
            fileName = filePath;
        }else if(filePath.contains(URL_QUESTION_MARK)){
            //2、?问号结尾的数据
            fileName = filePath.substring(filePath.lastIndexOf(URL_PREFIX)+1, filePath.lastIndexOf(URL_QUESTION_MARK));
        }else {
            fileName = filePath.substring(filePath.lastIndexOf(URL_PREFIX)+1);
        }

        return fileName;
    }

    /**
     * 根据fildId，获取在美团云的地址
     * @param fileName
     * @return
     */
    public String getMtCloudUrl(String fileName){
        return mtCloudService.getAbsoluteDownUrl(fileName, false, 0, 0, "客户系统");
    }

    public String getMtCloudInnerUrlWithConfigExpireTime(String fileName,int expireTime){
        return mtCloudService.getAbsoluteDownUrl(getFileName(fileName), true, expireTime,0, 0, "客户系统");
    }
}
