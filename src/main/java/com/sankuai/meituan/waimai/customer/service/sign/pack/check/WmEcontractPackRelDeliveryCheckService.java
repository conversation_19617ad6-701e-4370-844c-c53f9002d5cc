package com.sankuai.meituan.waimai.customer.service.sign.pack.check;

import com.sankuai.meituan.waimai.customer.adapter.WmPoiLogisticsClient;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmPoiLogisticsFee;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class WmEcontractPackRelDeliveryCheckService {

    @Resource
    private WmPoiLogisticsClient wmPoiLogisticsClient;

    @Resource
    private WmCustomerPoiService wmCustomerPoiService;

    /**
     * 根据客户id查询关联配送信息
     */
    public boolean hasEffectRelDeliveryByCustomerId(Integer customerId) throws TException, WmCustomerException {
        List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(customerId);
        return hasEffectRelDeliveryByWmPoiIdList(wmPoiIdList);
    }

    /**
     * 根据门店id查询关联配送信息
     */
    public boolean hasEffectRelDeliveryByWmPoiIdList(List<Long> wmPoiIdList) throws TException, WmCustomerException {
        List<WmPoiLogisticsFee> feeList = wmPoiLogisticsClient.getPoiLogisticsList(wmPoiIdList);
        if (CollectionUtils.isEmpty(feeList)) {
            return false;
        }

        for (WmPoiLogisticsFee fee:feeList) {
            if (CollectionUtils.isNotEmpty(fee.getWmPoiFeeList())) {
                return true;
            }
        }
        return false;
    }
}
