package com.sankuai.meituan.waimai.customer.util;

import com.google.common.collect.Lists;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 大象消息工具类
 *
 * @Author: wangyongfang
 * @Date: 2024-02-26
 */
@Slf4j
public class DXAlertUtils {
    public static final String DX_SENDER = "<EMAIL>";

    private static final String placeHolder = "\\{\\}";

    /**
     * 发送大象消息提醒
     * 通过“先富业务系统”公众号发送提醒消息
     *
     * @param message 消息体支持占位符，占位符格式：{}，发送消息时，占位符会被替换成参数值
     * @param misIds 接收人Mis
     * @param params 替换占位符的参数
     */
    public static void dxAlert(String message, List<String> misIds, Object...params) {
        String finalMsg = assembleSendMessage(message, params);
        log.info("DaXiangAlertUtils.dxAlert执行。。。misIds={} msg={}",misIds,finalMsg);
        List<String> receivers = Lists.newArrayList();
        for (String misId : misIds) {
            if (StringUtils.isNotBlank(misId)) {
                CollectionUtils.addIgnoreNull(receivers, misId);
            }
        }
        DaxiangUtil.push(DX_SENDER, finalMsg, receivers);
    }


    /**
     * 组装消息文本
     * 增加环境标识
     * 对{}占位符进行替换
     *
     * @param originMsg
     * @param args
     * @return
     */
    public static String assembleSendMessage(String originMsg, Object...args) {
        HostEnv env =  ProcessInfoUtil.getHostEnv();
        StringBuilder finalMsgBuilder = new StringBuilder();
        for (Object arg : args) {
            originMsg = originMsg.replaceFirst(placeHolder, String.valueOf(arg));
        }
        finalMsgBuilder.append("【")
                .append(env)
                .append("环境】")
                .append(originMsg.replaceAll(placeHolder, " "));
        return finalMsgBuilder.toString();
    }
}
