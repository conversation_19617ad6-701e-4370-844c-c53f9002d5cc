package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.blackListCheck.CustomerBlackListValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/1
 * @description 客户黑名单校验
 */
@Service
@Slf4j
public class WmCustomerBlackListValidator implements IWmCustomerValidator {

    @Autowired
    private CustomerBlackListValidator customerBlackListValidator;

    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        log.info("客户黑名单校验开始执行WmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));
        try {
            // 黑名单校验失败会直接抛出异常
            customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);
            return validateResultBo;
        } catch (Exception e) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
            validateResultBo.setMsg(e.getMessage());
        }
        return validateResultBo;
    }
}
