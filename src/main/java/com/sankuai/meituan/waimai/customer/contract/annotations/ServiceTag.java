package com.sankuai.meituan.waimai.customer.contract.annotations;

import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ServiceTag {

    WmTempletContractTypeEnum[] templetTypes();

}
