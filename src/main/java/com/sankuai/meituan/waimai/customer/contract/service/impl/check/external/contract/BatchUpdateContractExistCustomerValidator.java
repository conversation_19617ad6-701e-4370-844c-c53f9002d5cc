package com.sankuai.meituan.waimai.customer.contract.service.impl.check.external.contract;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.contract.bo.BatchContractUpdateContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractUpdateBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 校验 合同对应客户是否存在
 */
@Service
public class BatchUpdateContractExistCustomerValidator implements IBatchContractUpdateValidator {

    @Autowired
    private WmCustomerService wmCustomerService;

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchUpdateContractExistCustomerValidator.class);

    @Override
    public void valid(BatchContractUpdateContext context) throws WmCustomerException {
        LOGGER.info("BatchUpdateContractExistCustomerValidator wmCustomerBasicBoList.isEmpty = {}", CollectionUtils.isEmpty(context.getWmCustomerBasicBoList()));
        if (!context.isNeedProcess()) {
            return;
        }

        List<WmCustomerContractUpdateBo> wmCustomerContractUpdateBoList = context.getWmCustomerContractUpdateBoList();
        List<Long> customerIdList = wmCustomerContractUpdateBoList.stream().map(WmCustomerContractUpdateBo::getCustomerId).distinct().collect(Collectors.toList());

        List<WmCustomerBasicBo> wmCustomerBasicBoList = Lists.newArrayList();
        try {
            wmCustomerBasicBoList = wmCustomerService.getCustomerListByIdOrMtCustomerId(Sets.newHashSet(customerIdList));
        } catch (WmCustomerException e) {
            LOGGER.warn("查询闪购客户异常", e);
            handleErrorContract(context.getContractIdList(), context.getResultMap(), "系统异常");
        }
        context.setWmCustomerBasicBoList(wmCustomerBasicBoList);
        if (CollectionUtils.isEmpty(wmCustomerBasicBoList)) {
            handleErrorContract(context.getContractIdList(), context.getResultMap(), "关联客户信息不存在");
            context.setNeedProcess(false);
        }
    }

    private void handleErrorContract(List<Long> contractIdList, Map resultMap, String errMsg) {
        if (CollectionUtils.isEmpty(contractIdList)) {
            return;
        }
        for (Long contractId : contractIdList) {
            resultMap.put(contractId, errMsg);
        }
    }
}
