package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.BatchQueryPoiSignDataParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.OperatorParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.PoiSession;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractBatchDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractSingleDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.thrift.BmContractSettleSignThriftIface;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractErrorCode;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.batch.EcontractBatchDeliveryPerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.single.EcontractSingleDeliveryPerInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 履约服务费相关接口
 * Created by limingxuan on 2023/6/5
 */
@Slf4j
@Service
public class DeliveryContractAdapter {

    @Autowired
    private BmContractSettleSignThriftIface bmContractSettleSignThriftIface;

    public Map<Long, Boolean> queryPerformanceSignDataWithRetry(Map<Long, Long> wmPoiAndBizMap) {
        if (MapUtils.isEmpty(wmPoiAndBizMap)) {
            return null;
        }
        int retryTimes = MccConfig.queryEcontractDataRetryTimes();
        for (int i = 0; i < retryTimes; i++) {
            try {
                BatchQueryPoiSignDataParam param = new BatchQueryPoiSignDataParam();
                // 门店-session关系列表
                List<PoiSession> poiSessionList = new ArrayList<>();
                for (Map.Entry<Long, Long> entry : wmPoiAndBizMap.entrySet()) {
                    PoiSession poiSession = new PoiSession();
                    poiSession.setWmPoiId(entry.getKey());
                    poiSession.setSessionId(entry.getValue());
                    poiSessionList.add(poiSession);
                }
                // 操作人信息
                OperatorParam operatorParam = new OperatorParam();
                operatorParam.setOpId(-99L);
                operatorParam.setOpName("电子签约系统");
                operatorParam.setMisId("电子签约系统");

                param.setPoiSessionList(poiSessionList);
                param.setOperatorParam(operatorParam);

                BmContractPlatformProcessResp<ElectronicContractBatchDeliveryPerInfoBo> resp = bmContractSettleSignThriftIface.batchQueryPoiSignData(param);
                if (resp == null || !resp.isSuccess() || resp.getCode() != 0 || resp.getData() == null) {
                    log.info("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据失败 wmPoiAndBizMap:{}", wmPoiAndBizMap);
                    throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "调用配送系统获取履约服务费签约数据失败");
                }
                Map<Long, Boolean> result = assemblyBatchPerInfoBo(resp.getData());
                log.info("#queryPerformanceSignDataWithRetry result:{}", JSON.toJSONString(result));
                return result;
            } catch (TException ex) {
                // TException 为超时异常，进行重试
                log.error("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据超时异常 wmPoiAndBizMap:{}", wmPoiAndBizMap, ex);
            } catch (Exception e) {
                // 其他异常则直接catch并抛出业务异常
                log.error("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据异常 wmPoiAndBizMap:{}", wmPoiAndBizMap, e);
            }
        }
        return null;
    }

    private Map<Long, Boolean> assemblyBatchPerInfoBo(ElectronicContractBatchDeliveryPerInfoBo electronicContractBatchDeliveryPerInfoBo) {
        Map<Long, Boolean> result = new HashMap<>();
        EcontractBatchDeliveryPerInfoBo econtractBatchDeliveryPerInfoBo = new EcontractBatchDeliveryPerInfoBo();
        Map<Long, EcontractSingleDeliveryPerInfoBo> batchPerInfoMap = new HashMap<>();
        for (Map.Entry<Long, ElectronicContractSingleDeliveryPerInfoBo> entry : electronicContractBatchDeliveryPerInfoBo.getBatchPerInfoMap().entrySet()) {
            if (entry.getValue().getPerDiscountInfo() == null) {
                result.put(entry.getKey(), false);
            } else {
                result.put(entry.getKey(), true);
            }
        }
        econtractBatchDeliveryPerInfoBo.setBatchPerInfoMap(batchPerInfoMap);
        return result;
    }

    /**
     * 查询履约合同签约数据
     * @return
     */
    public ElectronicContractBatchDeliveryPerInfoBo queryPerFeeWithRetry(Map<Long, Long> wmPoiAndBizMap) {
        if (MapUtils.isEmpty(wmPoiAndBizMap)) {
            return null;
        }
        int retryTimes = MccConfig.queryEcontractDataRetryTimes();
        for (int i = 0; i < retryTimes; i++) {
            try {
                BatchQueryPoiSignDataParam param = new BatchQueryPoiSignDataParam();
                // 门店-session关系列表
                List<PoiSession> poiSessionList = new ArrayList<>();
                for (Map.Entry<Long, Long> entry : wmPoiAndBizMap.entrySet()) {
                    PoiSession poiSession = new PoiSession();
                    poiSession.setWmPoiId(entry.getKey());
                    poiSession.setSessionId(entry.getValue());
                    poiSessionList.add(poiSession);
                }
                // 操作人信息
                OperatorParam operatorParam = new OperatorParam();
                operatorParam.setOpId(-99L);
                operatorParam.setOpName("电子签约系统");
                operatorParam.setMisId("电子签约系统");

                param.setPoiSessionList(poiSessionList);
                param.setOperatorParam(operatorParam);

                BmContractPlatformProcessResp<ElectronicContractBatchDeliveryPerInfoBo> resp = bmContractSettleSignThriftIface.batchQueryPoiSignData(param);
                if (resp == null || !resp.isSuccess() || resp.getCode() != 0 || resp.getData() == null) {
                    log.info("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据失败 wmPoiAndBizMap:{}", wmPoiAndBizMap);
                    throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "调用配送系统获取履约服务费签约数据失败");
                }

                log.info("#queryPerformanceSignDataWithRetry result:{}", JSON.toJSONString(resp.getData()));
                return resp.getData();
            } catch (TException ex) {
                // TException 为超时异常，进行重试
                log.error("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据超时异常 wmPoiAndBizMap:{}", wmPoiAndBizMap, ex);
            } catch (Exception e) {
                // 其他异常则直接catch并抛出业务异常
                log.error("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据异常 wmPoiAndBizMap:{}", wmPoiAndBizMap, e);
            }
        }
        return null;
    }
}
