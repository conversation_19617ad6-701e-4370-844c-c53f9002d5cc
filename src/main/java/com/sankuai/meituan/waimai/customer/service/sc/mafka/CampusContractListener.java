package com.sankuai.meituan.waimai.customer.service.sc.mafka;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.adapter.DeliverThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.DeliveryContractAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmCampusContactServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.WmSchoolDeliveryNewService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contract.ContractApplyInfoDto;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverInstanceDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
public class CampusContractListener implements IMessageListener {

    @Autowired
    private WmCampusContactServiceAdapter wmCampusContactServiceAdapter;

    @Autowired
    private WmSchoolDeliveryNewService wmSchoolDeliveryNewService;

    @Autowired
    private DeliverThriftServiceAdapter deliverThriftServiceAdapter;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext messagetContext) {
        try {
            log.info("[CampusContractListener.recvMessage] input param: message={}, partition={}", message.getBody(), message.getParttion());

            if (!MccScConfig.getCampusContractConsumerSwitch()) {
                log.info("[CampusContractListener.recvMessage] switch down");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            if (null == message.getBody() || StringUtils.isBlank(message.getBody().toString())) {
                log.error("[CampusContractListener.recvMessage] message body is null, return. message = {}", JSONObject.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            JSONObject jsonObject = JSONObject.parseObject(message.getBody().toString());
            JSONObject pipelineEvent = jsonObject.getJSONObject("pipelineEvent");
            Integer tenantId = pipelineEvent.getInteger("tenantId");
            String apiName = pipelineEvent.getString("apiName");
            Integer operationType = pipelineEvent.getInteger("operationType");
            log.info("operationType:{}, tenantId:{}, apiName:{}", operationType, tenantId, apiName);
            if (!Objects.equals(tenantId, 1000008) || !Objects.equals(apiName, "ContractApply") || !Objects.equals(operationType, 102)) {
                log.info("[CampusContractListener.recvMessage] not campus contract update msg, dont process");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            String contractId = pipelineEvent.getString("objectId");
            ContractApplyInfoDto contract = wmCampusContactServiceAdapter.getSchoolContractById(Long.parseLong(contractId));
            if (Objects.isNull(contract)) {
                throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "根据id查询合同申请失败");
            }

            //过滤合同签约方式及业务类型
            if (!Objects.equals(contract.getRecordType(), 0)) {
                log.info("[CampusContractListener.recvMessage]非学校类型合同，不处理");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            if (!Objects.equals(contract.getSignType(), 0) && !Objects.equals(contract.getSignType(), 3)) {
                log.info("[CampusContractListener.recvMessage]非新签类型合同，不处理");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //判断合同审批通过时间
            if (Objects.isNull(contract.getApprovalTime()) || contract.getApprovalTime().getTime() < MccScConfig.getCampusContractApprovalProcessTime()) {
                log.info("[CampusContractListener.recvMessage]合同审批通过时间在规定前，不处理");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //判断合同生命状态，已生效时，判断是否存在交付，不存在则新发起交付
            Long deliverId = null;
            if (contract.getLifeStatus() == 2) {
                DeliverInstanceDto deliverInstanceDto = deliverThriftServiceAdapter.findDeliverInstanceOpportunityId(contract.getOpportunityId());
                if (Objects.isNull(deliverInstanceDto)) { //当前不存在交付，需要发起交付
                    deliverId = deliverThriftServiceAdapter.create(
                            contract.getOpportunityId(),
                            "School",
                            String.valueOf(contract.getSchoolId()),
                            contract.getOwner().intValue(),
                            Objects.equals(contract.getDeliveryMethod(), 0) ? "aggregateDelivery" : "selfDelivery"
                    );
                } else {
                    deliverId = deliverInstanceDto.getDeliverId();
                }
            }

            //刷新索引
            wmSchoolDeliveryNewService.indexDeliveryPlanById(deliverId);

            return ConsumeStatus.CONSUME_SUCCESS;

        } catch (Exception e) {
            log.error("[CampusContractListener.recvMessage] handle error", e);
            return ConsumeStatus.RECONSUME_LATER;
        }


    }
}
