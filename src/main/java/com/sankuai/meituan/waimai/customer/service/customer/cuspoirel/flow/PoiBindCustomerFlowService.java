package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.flow;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.BaseInfoQueryPhysicalPoiThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiRelConstants;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindFlowStrategy;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindParamBo;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiOplogService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiRelService;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindStrategy;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.bind.*;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.WmPhysicalPoiRel;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.poirel.CustomerPoiBindDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.jeasy.rules.api.RulesEngineParameters;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店绑定客户流程设计服务
 */
@Service
@Slf4j
public class PoiBindCustomerFlowService {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private WmCustomerPoiOplogService wmCustomerPoiOplogService;

    @Autowired
    private BaseInfoQueryPhysicalPoiThriftServiceAdapter baseInfoQueryPhysicalPoiThriftServiceAdapter;

    @Autowired
    private CustomerPoiBindService customerPoiBindService;

    /**
     * 绑定使用的rule引擎
     */
    private static volatile Rules rules = null;

    private static final Object lockObject = new Object();


    protected static final Set<String> WM_POI_FIELDS = Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_TAG,
            WmPoiFieldQueryConstant.WM_POI_FIELD_POI_BIZ_TYPES,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID
    );

    /**
     * 根据流程以及规则的绑定流程
     *
     * @param customerPoiBindDTO
     */
    public void bindByFlowAndRule(CustomerPoiBindDTO customerPoiBindDTO, CustomerOperateBO customerOperateBO) throws WmCustomerException, TException {
        log.info("bindByFlowAndRule,客户门店绑定根据规则判断执行流程,customerId={},wmPoiIds={}", customerPoiBindDTO.getCustomerId(), JSON.toJSONString(customerPoiBindDTO.getWmPoiIds()));
        //步骤1：构建绑定流程上下文
        Integer customerId = customerPoiBindDTO.getCustomerId();
        CustomerPoiBindFlowContext context = buildPoiBindCustomerFlowContext(customerPoiBindDTO, customerOperateBO);

        //步骤2：初始化构建绑定规则
        initBindFlowRule();
        //步骤3：执行绑定策略
        executeBindRule(context);
        //步骤4：根据规计算的流程策略开始处理
        List<BindFlowStrategy> bindFlowStrategyList = context.getBindFlowStrategyList();
        //步骤4.1 校验规则计算的绑定策略是否有效，有效则根据策略执行
        boolean checkBindStrategyFlag = checkMatchBindStrategy(bindFlowStrategyList);
        if (checkBindStrategyFlag) {
            //规则有效则根据策略开始执行
            log.info("bindByFlowAndRule,绑定操作匹配到有效策略，根据策略开始执行绑定操作,bindFlowStrategyList={}", JSON.toJSONString(bindFlowStrategyList));
            //遍历绑定流程策略列表，执行绑定规则策略。
            for (BindFlowStrategy bindFlowStrategy : bindFlowStrategyList) {
                //上下文中的门店IDSet需要从策略中获取并重置
                context.setWmPoiIdSet(bindFlowStrategy.getWmPoiIdSet());
                BindStrategy poiRelFlowStrategy = bindFlowStrategy.getBindStrategy();
                poiRelFlowStrategy.execute(context);
            }
            log.info("bindByFlowAndRule,根据策略开始执行绑定操作完成,customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(context.getWmPoiIdSet()));
            return;
        }

        //步骤4.2 如果校验未通过则执行原绑定流程
        log.error("bindByFlowAndRule,绑定操作未匹配到有效策略，执行原绑定流程，customerPoiBindDTO={}", JSON.toJSONString(customerPoiBindDTO));
        //添加告警
        Cat.logEvent(CustomerPoiRelConstants.BIND_RULE_STRATEGY_EVENT, CustomerPoiRelConstants.BIND_RULE_NO_MATCH_STRATEGY);
        //步骤执行原绑定逻辑
        customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(customerId, customerPoiBindDTO.getWmPoiIds(),
                customerPoiBindDTO.getRemark(), customerPoiBindDTO.getOpUId(), customerPoiBindDTO.getOpUName(), customerPoiBindDTO.isCheckPoiVersion(),
                CustomerTaskSourceEnum.of(customerPoiBindDTO.getOpSource()), customerOperateBO, CustomerPoiBindTypeEnum.DIRECT_BIND));
    }


    /**
     * 校验规则有效性质
     *
     * @param bindFlowStrategyList
     * @return
     */
    private boolean checkMatchBindStrategy(List<BindFlowStrategy> bindFlowStrategyList) {
        if (CollectionUtils.isEmpty(bindFlowStrategyList)) {
            log.error("checkBindRuleStrategy,绑定规则集合为空，校验未通过");
            return false;
        }

        for (BindFlowStrategy bindFlowStrategy : bindFlowStrategyList) {
            BindStrategy bindStrategy = bindFlowStrategy.getBindStrategy();
            if (bindStrategy == null || bindStrategy.getBindCheckStrategy() == null) {
                return false;
            }
        }
        return true;
    }


    /**
     * 初始化绑定规则
     */
    private static void initBindFlowRule() {
        if (rules != null) {
            return;
        }
        //初始化注册规则-注册规则
        synchronized (lockObject) {
            if (rules != null) {
                return;
            }
            rules = new Rules();
            //单店类客户绑定策略
            rules.register(new DanDianDirectBindRule());
            //食堂客户类型绑定策略
            rules.register(new ShiTangDirectBindRule());
            //非美食城、单店类、食堂、食堂承包商、聚合配送商之外客户的绑定策略
            rules.register(new OtherTypeDirectBindRule());
            //承包商客户绑定策略
            rules.register(new ContractorBindRule());
            //聚合配送商客户绑定策略
            rules.register(new AggDistributeBindRule());
            //美食城客户有资质共用标签绑定策略
            rules.register(new MscQuaComDirectBindRule());
            //美食城客户无资质共用标签&绑定中无拼好饭子门店 绑定策略
            rules.register(new MscNoChildSignBindRule());
            //美食城客户无资质共用标签&绑定中存在拼好饭子门店&无关联上线主物理门店 绑定策略
            rules.register(new MscHasPhfChildNoMainPhyBindRule());
            //美食城客户无资质共用标签&绑定中存在拼好饭子门店&存在关联上线主物理门店 绑定策略
            rules.register(new MscHasPhfChildAndMainPhyBindRule());
        }
    }

    /**
     * 执行绑定策略
     *
     * @param context
     */
    private void executeBindRule(CustomerPoiBindFlowContext context) {
        RulesEngineParameters parameters = new RulesEngineParameters().skipOnFirstAppliedRule(true);
        RulesEngine rulesEngine = new DefaultRulesEngine(parameters);
        //执行规则
        Facts facts = new Facts();
        facts.put("context", context);
        rulesEngine.fire(rules, facts);
    }


    /**
     * 初始化设置绑定流程上下文参数
     *
     * @param customerPoiBindDTO
     * @param customerOperateBO
     * @return
     * @throws WmCustomerException
     */
    private CustomerPoiBindFlowContext buildPoiBindCustomerFlowContext(CustomerPoiBindDTO customerPoiBindDTO, CustomerOperateBO customerOperateBO) throws WmCustomerException {

        Integer customerId = customerPoiBindDTO.getCustomerId();
        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerPoiBindDTO.getCustomerId());
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户不存在");
        }

        //初始化构建绑定流程上下文
        CustomerPoiBindFlowContext context = new CustomerPoiBindFlowContext();
        context.setWmCustomerDB(wmCustomerDB);
        context.setCustomerOperateBO(customerOperateBO);
        context.setCustomerId(customerId);
        context.setCheckPoiVersion(customerPoiBindDTO.isCheckPoiVersion());
        context.setOpSource(customerPoiBindDTO.getOpSource());
        context.setOpSourceDetail(customerPoiBindDTO.getOpSourceDetail());
        context.setOpName(customerPoiBindDTO.getOpUName());
        context.setOpUid(customerPoiBindDTO.getOpUId());
        context.setOpSysName(customerPoiBindDTO.getSystemName());
        context.setWmPoiIdSet(customerPoiBindDTO.getWmPoiIds());
        context.setRemark(customerOperateBO.getRemark());
        //设置操作日志对应枚举
        WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum = wmCustomerPoiOplogService.getOpSourceTypeByTaskSource(customerPoiBindDTO.getOpSource(), customerPoiBindDTO.getOpSourceDetail());
        context.setPoiOplogSourceTypeEnum(oplogSourceTypeEnum);
        //美食城客户需要设置的上下文属性
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            //是否有资质共用标签
            context.setHasCustomerQuaComTag(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId()));
            //拼好饭移动餐车子门店列表字段
            context.setPhfChildPoiList(getSupportSubWmPoiIdList(customerPoiBindDTO.getWmPoiIds()));
            //存在拼好饭餐车子门店则需要查询客户下是否存在 绑定的上线状态主门店
            if (MapUtils.isNotEmpty(context.getPhfChildPoiList())) {
                context.setMainPhyWmPoiIdOnlineOnCustomerList(listOnlineMainPhyWmPoiIdsByCustomerId(customerId));
            }

        }
        return context;
    }

    /**
     * 获取子门店
     *
     * @param wmPoiIdSet
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private Map<Long, Long> getSupportSubWmPoiIdList(Set<Long> wmPoiIdSet) throws WmCustomerException {

        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggreList(Lists.newArrayList(wmPoiIdSet), WM_POI_FIELDS);
        // 获取美食城支持的子门店标签
        Map<String, Integer> mscSupportPoiLabels = MccCustomerConfig.getMscSupportPoiLabels();
        if (mscSupportPoiLabels == null || mscSupportPoiLabels.isEmpty()) {
            return Maps.newHashMap();
        }
        List<Integer> labels = mscSupportPoiLabels.keySet().stream().map(x -> Integer.valueOf(x)).collect(Collectors.toList());

        // 获取美食城子门店校验支持的物理城市
        List<Long> subWmPoiIdList = Lists.newArrayList();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            if (StringUtils.isBlank(wmPoiAggre.getLabel_ids())) {
                continue;
            }
            List<Integer> tagIds = Arrays.stream(wmPoiAggre.getLabel_ids().split(","))
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            if (!Collections.disjoint(tagIds, labels)) {
                subWmPoiIdList.add(wmPoiAggre.getWm_poi_id());
            }
        }
        return getWmPoiMapPhysicalPoi(subWmPoiIdList);
    }

    /**
     * 获取当前客户已绑定的主门店
     *
     * @param customerId
     * @return
     */
    private List<Long> listOnlineMainPhyWmPoiIdsByCustomerId(Integer customerId) throws WmCustomerException {
        List<Long> physicalWmPoiIdList = Lists.newArrayList();
        List<Long> wmPoiIdList = wmCustomerPoiRelService.selectWmPoiIdsByCustomerId(customerId);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return physicalWmPoiIdList;
        }
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, WM_POI_FIELDS);
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            return physicalWmPoiIdList;
        }
        Map<String, Integer> mscSupportPoiLabels = MccCustomerConfig.getMscSupportPoiLabels();
        if (mscSupportPoiLabels == null || mscSupportPoiLabels.isEmpty()) {
            return physicalWmPoiIdList;
        }
        List<Integer> labels = mscSupportPoiLabels.values().stream().collect(Collectors.toList());
        List<Long> mainWmPoiIdList = Lists.newArrayList();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            if (wmPoiAggre.getValid() != WmPoiValidEnum.ONLINE.getValue()) {
                continue;
            }
            if (StringUtils.isBlank(wmPoiAggre.getLabel_ids())) {
                continue;
            }
            List<Integer> tagIds = Arrays.stream(wmPoiAggre.getLabel_ids().split(","))
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            if (!Collections.disjoint(tagIds, labels)) {
                mainWmPoiIdList.add(wmPoiAggre.getWm_poi_id());
            }
        }
        if (CollectionUtils.isEmpty(mainWmPoiIdList)) {
            return physicalWmPoiIdList;
        }
        Map<Long, Long> map = getWmPoiMapPhysicalPoi(mainWmPoiIdList);
        if (map == null || map.isEmpty()) {
            return physicalWmPoiIdList;
        } else {
            return Lists.newArrayList(map.values());
        }
    }

    /**
     * 获取门店对应的物理门店
     *
     * @param wmPoiIdList
     * @return
     */
    private Map<Long, Long> getWmPoiMapPhysicalPoi(List<Long> wmPoiIdList) throws WmCustomerException {
        Map<Long, Long> map = Maps.newHashMap();
        // 获取门店的物理门店
        List<WmPhysicalPoiRel> wmPhysicalPoiRelList = baseInfoQueryPhysicalPoiThriftServiceAdapter.getPhysicalPoiRelList(wmPoiIdList);
        if (CollectionUtils.isEmpty(wmPhysicalPoiRelList)) {
            return map;
        }
        for (WmPhysicalPoiRel wmPhysicalPoiRel : wmPhysicalPoiRelList) {
            map.put(wmPhysicalPoiRel.getWmPoiId(), wmPhysicalPoiRel.getWmPhysicalPoiId());
        }
        return map;
    }
}
