package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class AbstractWmEcontractPdfContentInfoBoMaker {
    public SignTemplateEnum extractSignTemplateEnum() {
        SignTemplateWrapper annotation = this.getClass().getAnnotation(SignTemplateWrapper.class);
        SignTemplateEnum signTemplateEnum = annotation.wrapperEnum();
        return signTemplateEnum;
    }

    public List<EcontractDeliveryInfoBo> extractDeliveryInfo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, String pdfTempletType) throws WmCustomerException {
        //识别单多店，获取原始数据
        List<EcontractDeliveryInfoBo> originDataList = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(originContext, middleContext.getSignDataFactor());
        if (middleContext.getSignDataFactor().isDeliveryMultiWmPoi()) {
            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
            originDataList.addAll(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList());
        } else {
            EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
            originDataList.add(deliveryInfoBo);
        }
        Map<String, EcontractDeliveryInfoBo> originDataMap = originDataList.stream().collect(Collectors.toMap(EcontractDeliveryInfoBo::getDeliveryTypeUUID, econtractDeliveryInfoBo -> econtractDeliveryInfoBo));

        //从middle中获取uuid
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        List<String> deliveryInfoUUIDList = pdfDataMap.get(pdfTempletType);

        //从原始数据中提取并返回
        List<EcontractDeliveryInfoBo> resultList = deliveryInfoUUIDList.stream().filter(info -> originDataMap.get(info) != null).map(info -> originDataMap.get(info)).collect(Collectors.toList());
        return resultList;
    }
}
