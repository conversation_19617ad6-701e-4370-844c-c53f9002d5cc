package com.sankuai.meituan.waimai.customer.adapter.daocan;

import com.dianping.cat.Cat;
import com.meituan.mtrace.Tracer;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.customer.thrift.service.CustomerPoiService;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/16 10:12
 */
@Slf4j
@Service
public class CustomerPoiServiceAdapter {

    @Resource
    private CustomerPoiService.Iface customerPoiService;

    private static final String FAIL_NOTIFY_C2_SIGN_SUCCESS = "fail_notify_c2_sign_success";

    /**
     * C2合同签约成功通知到餐
     *
     * @param mtCustomId 平台客户ID
     * @param agentId    代理商ID
     */
    public void notifyDcC2SignSuccess(long mtCustomId, int agentId, long signTime) {
        log.info("CustomerPoiServiceAdapter#notifyDcC2SignSuccess, mtCustomId: {}, agentID: {}, signTime: {}", mtCustomId, agentId, signTime);
        int retryTimes = MccConfig.getRetryTimesToNotifyDcC2SignSuccess();
        for (int i = 0; i < retryTimes; ++i) {
            try {
                customerPoiService.changeSpecialPoiBySignC2Success(mtCustomId, agentId, signTime);
                return;
            } catch (Exception e) {
                log.error("CustomerPoiServiceAdapter#notifyDcC2SignSuccess, 尝试次数: {}, error", i + 1, e);
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                log.error("CustomerPoiServiceAdapter#notifyDcC2SignSuccess, 重试等待被中断", ie);
            }
        }
        Cat.logMetricForCount(FAIL_NOTIFY_C2_SIGN_SUCCESS);
        DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 通知到餐C2合同生效失败, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
    }

}
