package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCityOption;
import com.sankuai.meituan.waimai.gis.client.thrift.service.WmOpenCityService;
import com.sankuai.meituan.waimai.gis.client.thrift.service.request.city.WmOpenCitySearchRequest;
import com.sankuai.meituan.waimai.gis.client.thrift.service.response.city.WmOpenCityPageListResponse;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 物理城市服务适配器
 * <AUTHOR>
 * @date 2024/05/10
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmOpenCityServiceAdapter {

    @Autowired
    private WmOpenCityService wmOpenCityService;


    /**
     * 根据二级城市名称列表查询二级城市Map
     * @param secondCityNameList 二级城市名称列表
     * @return 二级城市Map. key->cityName val->WmOpenCity
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<String, WmOpenCity> getSecondCityMapBySecondCityNameList(List<String> secondCityNameList) throws WmSchCantException {
        try {
            Map<String, WmOpenCity> resultMap = new HashMap<>();
            WmOpenCityOption openCityOption = new WmOpenCityOption();
            openCityOption.setFilterLevels(Collections.singletonList(2));

            WmOpenCitySearchRequest request = new WmOpenCitySearchRequest();
            request.setOption(openCityOption);
            request.setCityNames(secondCityNameList);

            log.info("[WmOpenCityServiceAdapter.getSecondCityMapBySecondCityNameList] request = {}", JSONObject.toJSONString(request));
            WmOpenCityPageListResponse response = wmOpenCityService.search(request);
            log.info("[WmOpenCityServiceAdapter.getSecondCityMapBySecondCityNameList] response = {}", JSONObject.toJSONString(response));

            if (response == null || CollectionUtils.isEmpty(response.getWmOpenCities())) {
                return resultMap;
            }

            for (WmOpenCity wmOpenCity : response.getWmOpenCities()) {
                resultMap.put(wmOpenCity.getCityName(), wmOpenCity);
            }

            log.info("[WmOpenCityServiceAdapter.getSecondCityMapBySecondCityNameList] resultMap = {}", JSONObject.toJSONString(resultMap));
            return resultMap;
        } catch (Exception e) {
            log.error("[WmOpenCityServiceAdapter.getSecondCityMapBySecondCityNameList] Exception. secondCityNameList = {}",
                    JSONObject.toJSONString(secondCityNameList), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询物理城市信息失败");
        }
    }

    /**
     * 根据三级级城市名称列表查询三级级城市Map(有重名情况)
     * @param thirdCityNameList 三级城市名称列表
     * @return 三级级城市Map. key->cityName val->List<WmOpenCity>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<String, List<WmOpenCity>> getThirdCityMapByThirdCityNameList(List<String> thirdCityNameList) throws WmSchCantException {
        try {
            Map<String, List<WmOpenCity>> resultMap = new HashMap<>();
            WmOpenCityOption openCityOption = new WmOpenCityOption();
            openCityOption.setFilterLevels(Collections.singletonList(3));

            WmOpenCitySearchRequest request = new WmOpenCitySearchRequest();
            request.setOption(openCityOption);
            request.setCityNames(thirdCityNameList);

            log.info("[WmOpenCityServiceAdapter.getThirdCityMapByThirdCityNameList] request = {}", JSONObject.toJSONString(request));
            WmOpenCityPageListResponse response = wmOpenCityService.search(request);
            log.info("[WmOpenCityServiceAdapter.getThirdCityMapByThirdCityNameList] response = {}", JSONObject.toJSONString(response));

            if (response == null || CollectionUtils.isEmpty(response.getWmOpenCities())) {
                return resultMap;
            }

            for (WmOpenCity wmOpenCity : response.getWmOpenCities()) {
                resultMap.computeIfAbsent(wmOpenCity.getCityName(), k -> new ArrayList<>()).add(wmOpenCity);
            }

            log.info("[WmOpenCityServiceAdapter.getThirdCityMapByThirdCityNameList] resultMap = {}", JSONObject.toJSONString(resultMap));
            return resultMap;
        } catch (Exception e) {
            log.error("[WmOpenCityServiceAdapter.getThirdCityMapByThirdCityNameList] Exception. thirdCityNameList = {}",
                    JSONObject.toJSONString(thirdCityNameList), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询物理城市信息失败");
        }
    }

    /**
     * 通过城市ID获取物理城市信息
     * @param cityId 城市ID
     * @return 物理城市信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmOpenCity getCityByCityId(Integer cityId) throws WmSchCantException {
        try {
            log.info("[WmOpenCityServiceAdapter.getCityByCityId] cityId = {}", cityId);
            WmOpenCity wmOpenCity = wmOpenCityService.getByCityId(cityId);
            log.info("[WmOpenCityServiceAdapter.getCityByCityId] wmOpenCity = {}", JSONObject.toJSONString(wmOpenCity));
            return wmOpenCity;
        } catch (Exception e) {
            log.error("[WmOpenCityServiceAdapter.getCityByCityId] Exception. cityId = {}", cityId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询物理城市信息失败");
        }
    }

    /**
     * 通过城市ID批量获取物理城市信息
     * @param cityIdList 城市ID列表
     * @return 城市信息 key->城市ID val->物理城市信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<Integer, WmOpenCity> getCityMapByCityIdList(List<Integer> cityIdList) throws WmSchCantException {
        try {
            Map<Integer, WmOpenCity> resultMap = new HashMap<>();
            log.info("[WmOpenCityServiceAdapter.getCityMapByCityIdList] cityIdList = {}", JSONObject.toJSONString(cityIdList));
            List<WmOpenCity> wmOpenCityList = wmOpenCityService.getByCityIds(cityIdList);
            log.info("[WmOpenCityServiceAdapter.getCityMapByCityIdList] wmOpenCityList = {}", JSONObject.toJSONString(wmOpenCityList));

            for (WmOpenCity wmOpenCity : wmOpenCityList) {
                resultMap.put(wmOpenCity.getCityId(), wmOpenCity);
            }
            return resultMap;
        } catch (Exception e) {
            log.error("[WmOpenCityServiceAdapter.getCityMapByCityIdList] Exception. cityIdList = {}", JSONObject.toJSONString(cityIdList), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询物理城市信息失败");
        }
    }



}
