package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms;

import static com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerPush.TO_SIGN_PUSH_MESSAGE_MEDIC_CHANNEL;
import static com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerPush.TO_SIGN_PUSH_MESSAGE_MEDIC_POI_CHANNEL;
import static com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerPush.TO_SIGN_PUSH_MESSAGE_ME_CHANNEL;
import static com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerPush.TO_SIGN_PUSH_MESSAGE_SG_CHANNEL;
import static com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerPush.TO_SIGN_PUSH_MESSAGE_SHANGO_POI_CHANNEL;
import static com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerPush.TO_SIGN_PUSH_MESSAGE_WAIMAI_CHANNEL;
import static com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerPush.TO_SIGN_PUSH_WAIMAI_POI_ME_CHANNEL;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID;
import static com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cms.biz.CMSTemplateValidService;
import com.dianping.cms.dto.TemplateInfoDTO;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.MoreObjects;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.mtcoop.thrift.dto.CoopSignBasicInfo;
import com.sankuai.meituan.mtcoop.thrift.dto.CoopSignBasicReqInfo;
import com.sankuai.meituan.mtcoop.thrift.dto.TQuerySignCoopListRequest;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.adapter.BizUserServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.ShangouMsgAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.WmBrandAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.WmInboxSendMessageAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.adapter.daocan.CommonCoopServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.daocan.DcAccountServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.daocan.TSenderServiceAdapter;
import com.sankuai.meituan.waimai.customer.bo.ShanGouMsgBO;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.bo.account.AcctOrgBO;
import com.sankuai.meituan.waimai.customer.bo.brand.BizOrgCodeBrandTypeRelBO;
import com.sankuai.meituan.waimai.customer.bo.brand.BrandQueryParamBO;
import com.sankuai.meituan.waimai.customer.bo.brand.BrandQueryResultBO;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccGrayConfig;
import com.sankuai.meituan.waimai.customer.constant.common.CatConstant.MultiCustomerPush;
import com.sankuai.meituan.waimai.customer.constant.sign.SingleMultiPoiTypeEnum;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.IWmEcontractApplyService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractC1AutoRenewalService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.pack.WmEcontractCustomerPackService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.customer.util.TairLocker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.SMSConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractC2ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.EcontractDaoCanC1ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.EcontractDaoCanC2ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibmp.infra.amp.attribute.lib.dto.AccountInfoDTO;
import com.sankuai.sgmerchant.msgmanager.thrift.enums.MessageAppTypeEnum;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 获取短信消息拼接
 */
@Slf4j
@Service
public class WmEcontractSmsDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractSmsDataService.class);

    private final String KEFU_PHONE = "********";

    private static String PREFIX = "pushMessage#pack#";

    public static final int NO_MATCHED_BRAND_TYPE = -1;

    private static final Joiner COMMON_JOIN = Joiner.on("、").skipNulls();

    @Resource
    private WmCustomerService wmCustomerService;
    @Resource
    private WmEmployClient wmEmployClient;
    @Resource
    private WmPoiClient wmPoiClient;
    @Autowired
    private WmCrmNoticeService wmCrmNoticeService;
    @Autowired
    private CMSTemplateValidService cmsTemplateValidService;
    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;
    @Autowired
    private EmpServiceAdaptor empServiceAdaptor;
    @Autowired
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;
    @Autowired
    private WmEcontractCustomerPackService wmEcontractCustomerPackService;
    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;
    @Resource(name = "customerTairLocker")
    private TairLocker tairLocker;
    @Resource
    private WmInboxSendMessageAdapter wmInboxSendMessageAdapter;
    @Resource
    private BizUserServiceAdapter bizUserServiceAdapter;
    @Resource
    private WmPoiQueryAdapter wmPoiQueryAdapter;
    @Resource
    private ShangouMsgAdaptor shangouMsgAdaptor;
    @Resource
    private WmBrandAdapter wmBrandAdapter;
    @Resource
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;
    @Resource
    private WmEcontractC1AutoRenewalService wmEcontractC1AutoRenewalService;
    @Resource
    private TSenderServiceAdapter senderServiceAdapter;
    @Resource
    private DcAccountServiceAdapter dcAccountServiceAdapter;
    @Resource
    private IWmCustomerRealService wmLeafCustomerRealService;

    @Resource
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;

    @Resource
    private WmEcontractBatchBizService wmEcontractBatchBizService;

    @Resource
    private IWmEcontractApplyService econtractApplyService;

    @Resource
    private CommonCoopServiceAdapter commonCoopServiceAdapter;


    public void pushMessage(EcontractBatchContextBo batchContextBo, EcontractNotifyBo notifyBo, String applyContext)
        throws WmCustomerException {
        LOGGER.info("商家端发送消息 pushMessage batchContextBo:{} notifyBo:{} applyContext:{}",
            JSON.toJSONString(batchContextBo), JSON.toJSONString(notifyBo), applyContext);
        // 电子合同状态不满足
        String stageName = notifyBo.getStageName();
        if (!stageName.equals(TaskConstant.SMS_SIGNER_A) && !stageName.equals(TaskConstant.SMS_SIGNER_B)
            && !stageName.equals(TaskConstant.SMS_SIGNER_C) && !stageName.equals(TaskConstant.SMS_SIGNER_D)
            && !stageName.equals(TaskConstant.REAL_NAME_AUTH_A) && !stageName.equals(TaskConstant.REAL_NAME_AUTH_B)
            && !stageName.equals(TaskConstant.REAL_NAME_AUTH_C)
            && !stageName.equals(TaskConstant.REAL_NAME_AUTH_D)) {
            LOGGER.info("pushMessage 当前处理步骤 stageName={}", stageName);
            return;
        }
        LOGGER.info("pushMessage current stageName={}", stageName);
        // 客户类型不满足
        Integer customerId = batchContextBo.getCustomerId();
        List<Long> wmCustomerPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(customerId);
        if (CollectionUtils.isEmpty(wmCustomerPoiIdList)) {
            LOGGER.info("当前客户无相关门店");
            return;
        }
        // 兼容新打包流程
        String smsParamInfo = null;
        if (batchContextBo.getSignPackId() > 0
            && wmEcontractCustomerPackService.manualSignUpdateGray(batchContextBo.getCustomerId())) {
            // 获取包信息
            WmEcontractSignPackDB signPackDB = wmEcontractSignPackService.querySignPackByIdMaster(batchContextBo.getSignPackId());
            String lockKey = PREFIX + batchContextBo.getSignPackId();
            // 若包里的任务还未全部就绪 || 加锁失败
            if (!WmEcontractSignPackService.PACK_CAN_PUSH_MSG_STATUS.contains(signPackDB.getStatus())
                || !tairLocker.tryLock(lockKey, MccConfig.pushMsgTairLockExpireTime())) {
                return;
            }
            smsParamInfo = signPackDB.getSmsParamInfo();
        }
        // 向商家端推送消息
        Long wmPoiId = wmCustomerPoiIdList.get(0);
        EcontractBatchTypeEnum batchTypeEnum = batchContextBo.getBatchTypeEnum();
        String extMsg = notifyBo.getExtMsg();
        String agentName = batchContextBo.getKpBo().getSignerName();
        if (batchTypeEnum.getType() == EcontractBatchTypeEnum.ADDEDSERVICE_DISCOUNT.getType()) {
            if (CollectionUtils.isEmpty(wmCustomerPoiIdList) || wmCustomerPoiIdList.size() > 1) {
                LOGGER.warn("增值服务费优惠pushMessage门店数量多于一个 customerId={}", customerId);
                return;
            }
            Cat.logMetricForCount(MultiCustomerPush.VAS_DISCOUNT_PUSH_MESSAGE_CHANNEL);
            addedserviceDiscountPushMsg(wmCustomerPoiIdList, wmPoiId, extMsg);
        } else { // 待签约页灰度门店
            if (wmEcontractC1AutoRenewalService.isC1AutoRenewalBatch(batchContextBo)) {
                LOGGER.info("C1合同到期推送商家端消息 customerId={},batchContextBo:{}", customerId, JSON.toJSONString(batchContextBo));
                wmEcontractC1AutoRenewalService.sendPushForC1AutoRenewal(batchContextBo,notifyBo);
                return;
            }
            Cat.logMetricForCount(MultiCustomerPush.TO_SIGN_PUSH_MESSAGE_CHANNEL);
            if (batchContextBo.getBatchTypeEnum().getType() == C2_CONTRACT.getType()) {
                EcontractTaskBo taskBo = batchContextBo.getTaskIdAndTaskMap().entrySet().iterator().next().getValue();
                EcontractC2ContractInfoBo c2ContractInfoBo = JSON.parseObject(taskBo.getApplyContext(),
                    EcontractC2ContractInfoBo.class);
                agentName = c2ContractInfoBo.getAgentShowName();
            }

            WmPoiDomain wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
            Map<String, String> paramMap = buildSignPushMessageBase(
                batchTypeEnum,
                customerId,
                agentName,
                wmCustomerPoiIdList,
                smsParamInfo,
                wmPoiDomain
            );
            if (CollectionUtils.isEmpty(wmCustomerPoiIdList) || wmCustomerPoiIdList.size() > 1) {
                // 外卖、闪购、医药，多店消息
                LOGGER.info("pushMessage 门店数量多于一个 customerId={}", customerId);
                if (isMultiCustomerMsgGray(customerId)) {
                    multiPoiMsgPush(batchContextBo,
                        customerId,
                        paramMap);
                }
                return;
            }
            // 外卖、闪购、医药，单店消息
            toSignPushMsg(batchTypeEnum,
                customerId,
                agentName,
                applyContext,
                wmCustomerPoiIdList,
                wmPoiId,
                smsParamInfo, paramMap);
        }
    }

    /**
     * 到餐侧发送触达消息
     */
    public void sendDaoCanPushMsg(EcontractBatchContextBo batchContextBo, EcontractNotifyBo notifyBo, String applyContext) throws WmCustomerException {
        LOGGER.info("WmEcontractSmsDataService#sendDaoCanPushMsg, 给到餐侧发送触达消息, batchContextBo:{} notifyBo:{} applyContext:{}",
                JSON.toJSONString(batchContextBo), JSON.toJSONString(notifyBo), applyContext);
        if (batchContextBo.getSignPackId() > 0 && wmEcontractCustomerPackService.manualSignUpdateGray(batchContextBo.getCustomerId())) {
            // 打包任务在这个时候不一定能获取到签约链接, 所以不在此处发消息
            return;
        }
        // 电子合同状态不满足
        String stageName = notifyBo.getStageName();
        if (!stageName.equals(TaskConstant.SMS_SIGNER_A) && !stageName.equals(TaskConstant.SMS_SIGNER_B)
                && !stageName.equals(TaskConstant.SMS_SIGNER_C) && !stageName.equals(TaskConstant.SMS_SIGNER_D)
                && !stageName.equals(TaskConstant.REAL_NAME_AUTH_A) && !stageName.equals(TaskConstant.REAL_NAME_AUTH_B)
                && !stageName.equals(TaskConstant.REAL_NAME_AUTH_C)
                && !stageName.equals(TaskConstant.REAL_NAME_AUTH_D)) {
            LOGGER.info("WmEcontractSmsDataService#sendDaoCanPushMsg, 当前处理步骤, stageName={}", stageName);
            return;
        }

        String contractInfo = extractPushMsgFromNoPackTak(batchContextBo);
        Map<String, String> extMsgMap = JSON.parseObject(notifyBo.getExtMsg(), Map.class);
        String shortLink = extMsgMap.get("SMS");
        List<AccountInfoDTO> dcAccountInfos = dcAccountServiceAdapter.getAccountByCustomerId(batchContextBo.getDaoCanContractContext().getMtCustomerId());
        List<Long> receivers = dcAccountInfos.stream().map(AccountInfoDTO::getAccountId).collect(Collectors.toList());
        senderServiceAdapter.sendMessage(MccConfig.getKDBInternalMessageTemplateId(), receivers, buildKDBInternalMessage(contractInfo, shortLink));
    }

    /**
     * 这个方法目前只给到餐的打包任务发送push消息使用
     *
     * @param signPackDB pack数据
     * @param shortLink  签约链接
     * @throws WmCustomerException 异常
     */
    public void sendPushMsgForDaoCanPackTask(WmEcontractSignPackDB signPackDB, String shortLink) throws WmCustomerException {
        WmEcontractSignBatchDB signBatchDB = getFirstSignBatchByPackId(signPackDB.getId());
        if (signBatchDB == null) {
            log.warn("WmEcontractSmsDataService#sendDaoCanPushMsg, 到餐合同打包任务数据异常, signPackDB: {}", JSON.toJSONString(signPackDB));
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "到餐合同打包任务数据异常");
        }
        EcontractBatchContextBo contextBo = JSON.parseObject(signBatchDB.getBatchContext(), EcontractBatchContextBo.class);
        if (Strings.isNullOrEmpty(shortLink)) {
            shortLink = econtractApplyService.getSmsShortLink(signBatchDB);
        }
        String pushMsg = extractPushMsgFromPackTask(signPackDB);
        List<AccountInfoDTO> dcAccountInfos = dcAccountServiceAdapter.getAccountByCustomerId(contextBo.getDaoCanContractContext().getMtCustomerId());
        List<Long> receivers = dcAccountInfos.stream().map(AccountInfoDTO::getAccountId).collect(Collectors.toList());
        senderServiceAdapter.sendMessage(MccConfig.getKDBInternalMessageTemplateId(), receivers, buildKDBInternalMessage(pushMsg, shortLink));
    }

    private WmEcontractSignBatchDB getFirstSignBatchByPackId(Long packId) {
        List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBatchBizService.queryBatchListByPackIdList(Collections.singletonList(packId));
        if (!CollectionUtils.isEmpty(signBatchDBList)) {
            return signBatchDBList.get(0);
        }
        return null;
    }

    private String extractPushMsgFromPackTask(WmEcontractSignPackDB signPackDB) throws WmCustomerException {
        List<String> contractNumList = new ArrayList<>();
        List<EcontractDaoCanC1ContractInfoBo> dcC1ContractInfoList = new ArrayList<>();
        List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.querySignBatchListByPackId(signPackDB.getId());
        for (WmEcontractSignBatchDB signBatchDB : signBatchDBList) {
            EcontractBatchContextBo contextBo = JSON.parseObject(signBatchDB.getBatchContext(), EcontractBatchContextBo.class);
            if (EcontractBatchTypeEnum.DAOCAN_SERVICE_C1_CONTRACT == contextBo.getBatchTypeEnum()) {
                EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT);
                EcontractDaoCanC1ContractInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDaoCanC1ContractInfoBo.class);
                dcC1ContractInfoList.add(contractInfoBo);
            } else if (EcontractBatchTypeEnum.DAOCAN_SERVICE_C2_CONTRACT == contextBo.getBatchTypeEnum()) {
                contractNumList.add(extractPushMsgFromNoPackTak(contextBo));
            }
        }
        if (!CollectionUtils.isEmpty(dcC1ContractInfoList)) {
            contractNumList.addAll(extractPushMsgFromC1Contract(dcC1ContractInfoList));
        }
        return COMMON_JOIN.join(contractNumList);
    }

    private List<String> extractPushMsgFromC1Contract(List<EcontractDaoCanC1ContractInfoBo> dcC1ContractInfoList) {
        try {
            TQuerySignCoopListRequest request = buildDcContractInfoRequest(dcC1ContractInfoList);
            List<CoopSignBasicInfo> coopSignBasicInfoList = commonCoopServiceAdapter.queryDcContractInfo(request);
            if (CollectionUtils.isEmpty(coopSignBasicInfoList)) {
                return Collections.emptyList();
            }
            return coopSignBasicInfoList.stream().map(this::buildPushMsgForDcC1Contract).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("WmEcontractSmsDataService#extractPushMsgFromC1Contract, error", e);
            return Collections.emptyList();
        }
    }

    private TQuerySignCoopListRequest buildDcContractInfoRequest(List<EcontractDaoCanC1ContractInfoBo> dcC1ContractInfoList) {
        TQuerySignCoopListRequest request = new TQuerySignCoopListRequest();
        List<CoopSignBasicReqInfo> reqInfoList = dcC1ContractInfoList.stream().map(this::buildCoopSignBasicReqInfo).collect(Collectors.toList());
        request.setCoopSignBasicReqInfoList(reqInfoList);
        return request;
    }

    private CoopSignBasicReqInfo buildCoopSignBasicReqInfo(EcontractDaoCanC1ContractInfoBo dcContractInfo) {
        if (StringUtils.isEmpty(dcContractInfo.getContractProof())) {
            return null;
        }
        CoopSignBasicReqInfo coopSignBasicReqInfo = new CoopSignBasicReqInfo();
        coopSignBasicReqInfo.setCoopId(dcContractInfo.getContractProof());
        coopSignBasicReqInfo.setIsNewSign(dcContractInfo.isNewSignContract());
        coopSignBasicReqInfo.setCoopType(dcContractInfo.getCoopType());
        return coopSignBasicReqInfo;
    }

    private String extractPushMsgFromNoPackTak(EcontractBatchContextBo batchContextBo) throws WmCustomerException {
        if (EcontractBatchTypeEnum.DAOCAN_SERVICE_C1_CONTRACT == batchContextBo.getBatchTypeEnum()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT);
            EcontractDaoCanC1ContractInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDaoCanC1ContractInfoBo.class);
            List<String> pushMsgList = extractPushMsgFromC1Contract(Collections.singletonList(contractInfoBo));
            return CollectionUtils.isEmpty(pushMsgList) ? "" : pushMsgList.get(0);
        }
        if (EcontractBatchTypeEnum.DAOCAN_SERVICE_C2_CONTRACT == batchContextBo.getBatchTypeEnum()) {
            return extractPushMsgFromDcC2Contract(batchContextBo);
        }
        throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "合同类型异常");
    }

    private String buildPushMsgForDcC1Contract(CoopSignBasicInfo coopSignBasicInfo) {
        String coopName;
        String coopNum;
        if (coopSignBasicInfo.isIsNewSign()) {
            coopName = coopSignBasicInfo.getNewSignCoopInfo().getFrameCoop().getTFrame().getFrameName();
            coopNum = coopSignBasicInfo.getNewSignCoopInfo().getFrameCoop().getTFrame().getFrameNum();
        } else {
            coopName = coopSignBasicInfo.getReSignCoopInfo().getCommonCoopName();
            coopNum = coopSignBasicInfo.getReSignCoopInfo().getCommonCoopNum();
        }
        return String.format("%s（合同编号：%s）", coopName, coopNum);
    }

    private String extractPushMsgFromDcC2Contract(EcontractBatchContextBo batchContextBo) {
        try {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT);
            EcontractDaoCanC2ContractInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDaoCanC2ContractInfoBo.class);
            LOGGER.info("WmEcontractSmsDataService#extractPushMsgFromDcC2Contract, contractInfoBo: {}", JSON.toJSONString(contractInfoBo));
            return EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getDesc() +
                    "（合同编号：" +
                    contractInfoBo.getContractNum() +
                    "）";
        } catch (Exception e) {
            log.error("WmEcontractSmsDataService#extractPushMsgFromDcC2Contract, error", e);
            return "";
        }
    }

    private Map<String, String> buildKDBInternalMessage(String contractInfo, String shortLink) {
        Map<String, String> param = Maps.newHashMap();
        param.put("contractInfo", contractInfo);
        param.put("shortLink", shortLink);
        return param;
    }

    public boolean isMultiCustomerMsgGray(Integer wmCustomerId) {
        int grayPercent = MccGrayConfig.getMultiCustomerMSgPercent();
        boolean isCustomerGray = (wmCustomerId % 10000) <= grayPercent;
        return isCustomerGray;
    }

    private void multiPoiMsgPush(EcontractBatchContextBo batchContextBo,
                                 Integer customerId,
                                 Map<String, String> paramMap)
        throws WmCustomerException {
        List<Long> signBatchPoiList = Lists.newArrayList();
        //C1、C2合同、资质属实商家承诺函、门店推广技术服务合同、企业订餐协议、打包(袋)服务合作协议等等框架类型任务，不涉及门店ID计算
        if (!Lists.newArrayList(EcontractBatchTypeEnum.C1_CONTRACT,
            EcontractBatchTypeEnum.C2_CONTRACT,
            EcontractBatchTypeEnum.QUA_REAL_LETTER,
            EcontractBatchTypeEnum.POI_PROMOTION_SERVICE,
            EcontractBatchTypeEnum.GROUP_MEAL,
            EcontractBatchTypeEnum.BAG_SERVICE,
            EcontractBatchTypeEnum.FOODCITY_STATEMENT,
            EcontractBatchTypeEnum.INTERIM_SELF,
            EcontractBatchTypeEnum.MEDIC_ORDER_SPLIT,
            EcontractBatchTypeEnum.SUBJECT_CHANGE_SUPPLEMENT,
            EcontractBatchTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT,
            EcontractBatchTypeEnum.NATIONAL_SUBSIDY_PURCHASE,
            EcontractBatchTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT).contains(batchContextBo.getBatchTypeEnum())) {
            signBatchPoiList.addAll(batchContextBo.getWmPoiIdList());
        }
        // for more
        // customer type 310,320 beside type 200
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(signBatchPoiList)) {
            // 310 type account msg 品牌账号
            log.info("signBatchPoiList={}", JSON.toJSONString(signBatchPoiList));
            brandKindAccountPubMsg(paramMap, signBatchPoiList);
        } else {
            // 320 type account msg. 签约任务未有门店场景 
            WmCustomerDB wmCustomer = wmCustomerService.selectCustomerById(customerId);
            Set<Long> accountIdSet = bizUserServiceAdapter.queryCustomerAccount(customerId);
            if (CollectionUtils.isEmpty(accountIdSet)) {
                // 从品牌维度查询账号
                accountIdSet = bizUserServiceAdapter.queryBrandCustomerAccount(customerId);
                if (CollectionUtils.isEmpty(accountIdSet)) {
                    log.warn("无效客户账号:customerId={}", customerId);
                    return;
                }
            }

            if (wmCustomer.getBizOrgCode() == CustomerBizOrgEnum.SHAN_GOU.getCode()) {
                Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_MEDIC_CHANNEL);
                // 查询品牌账号，并去重
                ShanGouMsgBO shangouMsgBO =
                    ShanGouMsgBO.builder()
                        .templateId(MccConfig.getShanGouMsgAccountTemplateId())
                        .param(paramMap)
                        .title(SMSConstant.WM_E_NOTICE_TITLE)
                        .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.PC))
                        .acctIds(new ArrayList<>(accountIdSet))
                        .build();
                shangouMsgAdaptor.pubAccountMessage(shangouMsgBO);
            } else if (wmCustomer.getBizOrgCode() == CustomerBizOrgEnum.MEDICINE.getCode()) {
                Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_SG_CHANNEL);
                ShanGouMsgBO shangouMsgBO =
                    ShanGouMsgBO.builder()
                        .templateId(MccConfig.getMedicMsgAccountTemplateId())
                        .param(paramMap)
                        .title(SMSConstant.WM_E_NOTICE_TITLE)
                        .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.PC))
                        .acctIds(new ArrayList<>(accountIdSet))
                        .build();
                if (MccGrayConfig.pushMedicAccountMsg()) {
                    shangouMsgAdaptor.pubAccountMessage(shangouMsgBO);
                }

            } else if (wmCustomer.getBizOrgCode() == CustomerBizOrgEnum.WAI_MAI.getCode()) {

                Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_ME_CHANNEL);

                extSignHyperLink(new WmPoiDomain(),
                    paramMap,
                    wmCustomer.getBizOrgCode(),
                    SingleMultiPoiTypeEnum.MULTI_POI.getCode());
                String publishMessTemplate = MccConfig.getContractSignPushMessageTemplate();
                wmInboxSendMessageAdapter.sendAccountMessage(new ArrayList<>(accountIdSet),
                    paramMap,
                    publishMessTemplate);
            }
        }
    }

    /**
     * 品牌类别发送消息
     *
     * @param paramMap         参数
     * @param signBatchPoiList 签约任务门店
     * @throws WmCustomerException 异常
     */
    private void brandKindAccountPubMsg(Map<String, String> paramMap,
                                        List<Long> signBatchPoiList)
        throws WmCustomerException {
        Set<Long> accountIdSet = Sets.newHashSet();
        signBatchPoiList.forEach(m -> {
            try {
                List<Long> accountIdList = bizUserServiceAdapter.getAccountIdsByWmPoiId(m);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(accountIdList)) {
                    accountIdList = accountIdList.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                    // 目前没有门店id和品牌账号直接关系，需要通过门店id获取所有的账号id，然后判断账号id是否是品牌账号
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(accountIdList)) {
                        accountIdSet.addAll(accountIdList);
                    }
                }
            } catch (WmCustomerException e) {
                log.error("获取客户异常", e);
            }
        });
        if (CollectionUtils.isEmpty(accountIdSet)) {
            log.warn("当前门店对应accountId账号对无效，poiIdList={}", JSON.toJSONString(signBatchPoiList));
            return;
        }
        HashMap<Long, Integer> accountBizOrgMap = buildAccountBizOrgMap(accountIdSet);
        if (MapUtils.isEmpty(accountBizOrgMap)) {
            log.info("未有账号相关分类,accountIdSet={}", accountIdSet);
            return;
        }
        pubChannelMsgViaAcctBizOrg(paramMap, accountBizOrgMap);
    }

    /**
     * * 构建账号与分类
     *
     * @param accountIdSet 账号
     * @return 分类map
     * @throws WmCustomerException
     */
    private HashMap<Long, Integer> buildAccountBizOrgMap(Set<Long> accountIdSet) throws WmCustomerException {
        HashMap<Long, Integer> accountBrandIdMap = new HashMap<>();
        accountIdSet.forEach(
            m -> {
                try {
                    AcctOrgBO acctOrgBO = bizUserServiceAdapter.queryByAcctId(m);
                    if (Objects.nonNull(acctOrgBO)) {
                        accountBrandIdMap.put(m, acctOrgBO.getBrandId());
                    } else {
                        log.warn("无效acctBrandId:accountId={}", m);
                    }
                } catch (Exception e) {
                    log.warn("获取账号品牌信息异常", e);
                }
            });
        if (MapUtils.isEmpty(accountBrandIdMap)) {
            log.warn("账号对应brandId无效，accountIdSet={}", JSON.toJSONString(accountIdSet));
            return Maps.newHashMap();
        }

        List<Integer> brandIdList =
            Lists.newArrayList(accountBrandIdMap.values());
        // 物理品牌对业务品牌是1对多 ，一个业务品牌只关联一个物理品牌.
        Map<Integer, Integer> brandIdOriginIdMap =
            wmBrandAdapter.getServiceBrandInfoList(brandIdList);

        if (MapUtils.isEmpty(brandIdOriginIdMap)) {
            log.error("获取物理品牌信息无效，brandIdList={}", JSON.toJSONString(brandIdList));
            return Maps.newHashMap();
        }

        BrandQueryParamBO brandQueryBO =
            BrandQueryParamBO.builder()
                .idList(brandIdOriginIdMap.values().stream().map(Integer::longValue).collect(
                    Collectors.toList()))
                .build();
        List<BrandQueryResultBO> originBrandResultBOList = wmBrandAdapter.findBrand(brandQueryBO);
        if (CollectionUtils.isEmpty(originBrandResultBOList)) {
            log.error("未查到品牌对应分类:brandQueryBO={}", JSON.toJSONString(brandQueryBO));
            return Maps.newHashMap();
        }

        Map<Long, Integer> originBrandIdTypeMap =
            originBrandResultBOList.stream().collect(Collectors.toMap(BrandQueryResultBO::getId,
                BrandQueryResultBO::getBrandType, (k1, k2) -> k1));

        // 枚举参考：   @See com.sankuai.meituan.scmbrand.thrift.constant.BrandTypeEnum
        List<BizOrgCodeBrandTypeRelBO> bizOrgCodeBrandTypeRelBOList =
            MccConfig.getBizOrgCodeBrandTypeRel();
        if (CollectionUtils.isEmpty(bizOrgCodeBrandTypeRelBOList)) {
            log.error("未获得品牌与分类配置");
            return Maps.newHashMap();
        }
        HashMap<Integer, Integer> brandTypeBizOrgMap = Maps.newHashMap();
        bizOrgCodeBrandTypeRelBOList.forEach(m -> m.getBrandTypeList().forEach(brandType -> {
            brandTypeBizOrgMap.put(brandType, m.getBizOrgCode());
        }));
        // build account -> bizOrgCode rel
        HashMap<Long, Integer> accountBizOrgMap = new HashMap<>();
        for (Map.Entry<Long, Integer> entry : accountBrandIdMap.entrySet()) {
            // accountBrandIdMap -> brandIdOriginIdMap -> originBrandIdTypeMap -> brandTypeBizOrgMap
            Integer bizBrandId = entry.getValue();
            Integer originBrandId = brandIdOriginIdMap.get(bizBrandId);
            Integer brandType = originBrandIdTypeMap.get(originBrandId.longValue());
            accountBizOrgMap.put(entry.getKey(), brandTypeBizOrgMap.getOrDefault(brandType, NO_MATCHED_BRAND_TYPE));
        }
        return accountBizOrgMap;
    }

    /**
     * 账号分渠道发送消息
     *
     * @param paramMap         消息参数
     * @param accountBizOrgMap 账号分组
     * @throws WmCustomerException 返回
     */
    private void pubChannelMsgViaAcctBizOrg(Map<String, String> paramMap, HashMap<Long, Integer> accountBizOrgMap)
        throws WmCustomerException {
        Set<Long> sgMsgChannelAccountSet = Sets.newHashSet();
        Set<Long> medicMsgChannelAccountSet = Sets.newHashSet();
        Set<Long> wmMeChannelAccountSet = Sets.newHashSet();
        // send msg via sg msg manager
        for (Map.Entry<Long, Integer> acctEntry : accountBizOrgMap.entrySet()) {
            if (Objects.isNull(acctEntry.getValue())) {
                log.warn("当前account的brandType无效:accountId={}", acctEntry.getKey());
                continue;
            }
            if (acctEntry.getValue() == NO_MATCHED_BRAND_TYPE) {
                // 非 外卖、闪购、医药
                log.warn("通用类别品牌，不发送短信");
                continue;
            }
            if (acctEntry.getValue() == CustomerBizOrgEnum.SHAN_GOU.getCode()) {
                sgMsgChannelAccountSet.add(acctEntry.getKey());

            } else if (acctEntry.getValue() == CustomerBizOrgEnum.MEDICINE.getCode()) {
                medicMsgChannelAccountSet.add(acctEntry.getKey());
            } else if (acctEntry.getValue() == CustomerBizOrgEnum.WAI_MAI.getCode()) {
                wmMeChannelAccountSet.add(acctEntry.getKey());
            } else {
                log.info("not expected case,不推送消息");
                return;
            }
        }
        log.info("多店签约账号sgMsgChannelAccountSet={}", JSON.toJSONString(sgMsgChannelAccountSet));
        log.info("多店签约账号medicMsgChannelAccountSet={}", JSON.toJSONString(medicMsgChannelAccountSet));
        log.info("多店签约账号wmMeChannelAccountSet={}", JSON.toJSONString(wmMeChannelAccountSet));

        if (!CollectionUtils.isEmpty(sgMsgChannelAccountSet)) {

            Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_SG_CHANNEL);
            ShanGouMsgBO shangouMsgBO =
                ShanGouMsgBO.builder()
                    .templateId(MccConfig.getShanGouMsgAccountTemplateId())
                    .param(paramMap)
                    .title(SMSConstant.WM_E_NOTICE_TITLE)
                    .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.PC))
                    .acctIds(new ArrayList<>(sgMsgChannelAccountSet))
                    .build();
            shangouMsgAdaptor.pubAccountMessage(shangouMsgBO);
        }

        if (!CollectionUtils.isEmpty(medicMsgChannelAccountSet)) {
            Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_MEDIC_CHANNEL);
            ShanGouMsgBO shangouMsgBO =
                ShanGouMsgBO.builder()
                    .templateId(MccConfig.getMedicMsgAccountTemplateId())
                    .param(paramMap)
                    .title(SMSConstant.WM_E_NOTICE_TITLE)
                    .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.PC))
                    .acctIds(new ArrayList<>(medicMsgChannelAccountSet))
                    .build();
            shangouMsgAdaptor.pubAccountMessage(shangouMsgBO);
        }
        if (!CollectionUtils.isEmpty(wmMeChannelAccountSet)) {
            Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_WAIMAI_CHANNEL);
            extSignHyperLink(new WmPoiDomain(),
                paramMap,
                CustomerBizOrgEnum.WAI_MAI.getCode(),
                SingleMultiPoiTypeEnum.MULTI_POI.getCode());
            String publishMessTemplate = MccConfig.getContractSignPushMessageTemplate();
            wmInboxSendMessageAdapter.sendAccountMessage(Lists.newArrayList(wmMeChannelAccountSet),
                paramMap,
                publishMessTemplate);
        }
    }

    private void addedserviceDiscountPushMsg(List<Long> wmPoiIdList, Long wmPoiId, String extMsg)
        throws WmCustomerException {

        Map<String, String> extMsgMap = JSON.parseObject(extMsg, Map.class);
        String templateEcontractAddedServiceDiscountChannel = !CollectionUtils.isEmpty(extMsgMap)
            ? extMsgMap.get(SMSConstant.TEMPLATE_ECONTRACT_ADDED_SERVICE_DISCOUNT_CHANNEL)
            : StringUtils.EMPTY;
        if (Strings.isNullOrEmpty(templateEcontractAddedServiceDiscountChannel)) {
            LOGGER.warn("addedserviceDiscountPushMsg#短链接内容为空，渠道={}",
                SMSConstant.TEMPLATE_ECONTRACT_ADDED_SERVICE_DISCOUNT_CHANNEL);
            return;
        }
        WmPoiDomain wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
        String msg = "亲爱的商家(" + wmPoiDomain.getName() + "):<br/>"
                + getAddedserviceDiscountPushMsgMessaging(templateEcontractAddedServiceDiscountChannel);
        if (Strings.isNullOrEmpty(msg)) {
            LOGGER.warn("发送消息内容为空");
            return;
        }
        int expireTime = DateUtil.date2Unixtime(DateUtil.toTommorow(new Date()));
        wmCrmNoticeService.pubWmENoticeNoAudit(SMSConstant.TEMPLATE_ECONTRACT_ADDED_SERVICE_DISCOUNT_TITLE, msg,
                expireTime, wmPoiIdList);
    }

    private void toSignPushMsg(EcontractBatchTypeEnum batchTypeEnum, Integer customerId, String agentName,
                               String applyContext, List<Long> wmPoiIdList, Long wmPoiId, String smsParamInfo,
                               Map<String, String> paramMap)
        throws WmCustomerException {

        WmPoiDomain wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);

        // 获取PC的文案
        String pcMsg = "亲爱的商家(" + wmPoiDomain.getName() + "):<br/>" + getToSignMessaging(batchTypeEnum, customerId,
            agentName, wmPoiId, applyContext, MccConfig.toSignPCJumpLink(), smsParamInfo);
        // 获取APP的文案
        String appMsg = "亲爱的商家(" + wmPoiDomain.getName() + "):<br/>" + getToSignMessaging(batchTypeEnum, customerId,
            agentName, wmPoiId, applyContext, MccConfig.toSignAPPJumpLink(), smsParamInfo);
        if (StringUtils.isEmpty(pcMsg) || StringUtils.isEmpty(appMsg)) {
            return;
        }
        // 消息不展示时间
        int expireTime = DateUtil.date2Unixtime(DateUtil.toTommorow(new Date()));
        // 200 账号用ME平台，310、320 账号用闪购msg
        //获取门店信息
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggre(wmPoiId,
            Sets.newHashSet(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_BIZ_ORG_CODE));
        // 商超门店分类(0-默认，外卖：14010,闪购:14060,医药:14090),可使用枚举：com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum,引用waimai_service_poi_client最新版本jar包
        if (Objects.isNull(wmPoiAggre)) {
            log.error("获取门店数据异常:wmPoiAggre={}", JSON.toJSONString(wmPoiAggre));
            return;
        }

        if (MapUtils.isEmpty(paramMap)) {
            log.error("构建单店消息参数无效:customerId={},wmPoiId={},", customerId, wmPoiId);
            return;
        }

        if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.SHAN_GOU.getCode()) {

            Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_SHANGO_POI_CHANNEL);
            ShanGouMsgBO shangouMsgBO =
                ShanGouMsgBO.builder()
                    .templateId(MccConfig.getShanGouMsgPoiTemplateId())
                    .poiIds(Lists.newArrayList(wmPoiId))
                    .param(paramMap)
                    .title(SMSConstant.WM_E_NOTICE_TITLE)
                    .profile(StringUtils.EMPTY)
                    .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.PC))
                    .build();
            shangouMsgAdaptor.pubPoiMessage(shangouMsgBO);

            Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_SHANGO_POI_CHANNEL);
            shangouMsgBO =
                ShanGouMsgBO.builder()
                    .templateId(MccConfig.getShanGouMsgPoiAppTemplateId())
                    .poiIds(Lists.newArrayList(wmPoiId))
                    .param(paramMap)
                    .title(SMSConstant.WM_E_NOTICE_TITLE)
                    .profile(StringUtils.EMPTY)
                    .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.IOS,
                        MessageAppTypeEnum.ANDROID))
                    .build();
            shangouMsgAdaptor.pubPoiMessage(shangouMsgBO);
            return;
        }
        if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.MEDICINE.getCode()) {
            Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_MEDIC_POI_CHANNEL);
            ShanGouMsgBO shangouMsgBO =
                ShanGouMsgBO.builder()
                    .templateId(MccConfig.getMedicPoiTemplateId())
                    .poiIds(Lists.newArrayList(wmPoiId))
                    .param(paramMap)
                    .title(SMSConstant.WM_E_NOTICE_TITLE)
                    .profile(StringUtils.EMPTY)
                    .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.PC))
                    .build();
            shangouMsgAdaptor.pubPoiMessage(shangouMsgBO);

            shangouMsgBO =
                ShanGouMsgBO.builder()
                    .templateId(MccConfig.getMedicsgPoiAppTemplateId())
                    .poiIds(Lists.newArrayList(wmPoiId))
                    .param(paramMap)
                    .title(SMSConstant.WM_E_NOTICE_TITLE)
                    .profile(StringUtils.EMPTY)
                    .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.IOS,
                        MessageAppTypeEnum.ANDROID))
                    .build();
            shangouMsgAdaptor.pubPoiMessage(shangouMsgBO);
            return;
        }
        if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.MEDICINE.getCode()) {
            Cat.logMetricForCount(TO_SIGN_PUSH_MESSAGE_MEDIC_POI_CHANNEL);
            ShanGouMsgBO shangouMsgBO =
                ShanGouMsgBO.builder()
                    .templateId(MccConfig.getMedicPoiTemplateId())
                    .poiIds(Lists.newArrayList(wmPoiId))
                    .param(paramMap)
                    .title(SMSConstant.WM_E_NOTICE_TITLE)
                    .profile(StringUtils.EMPTY)
                    .appTypeEnumList(Lists.newArrayList(MessageAppTypeEnum.PC, MessageAppTypeEnum.IOS,
                        MessageAppTypeEnum.ANDROID))
                    .build();
            shangouMsgAdaptor.pubPoiMessage(shangouMsgBO);
            return;
        }
        if (wmPoiAggre.getBiz_org_code() != PoiOrgEnum.WAI_MAI.getCode()) {
            log.error("门店类别非外卖渠道,消息渠道暂不支持：「{}」", wmPoiAggre.getBiz_org_code());
            return;
        }
        Cat.logMetricForCount(TO_SIGN_PUSH_WAIMAI_POI_ME_CHANNEL);
        extSignHyperLink(wmPoiDomain,
            paramMap,
            wmPoiAggre.getBiz_org_code(),
            SingleMultiPoiTypeEnum.SINGLE_POI.getCode());
        String publishMessTemplate = MccConfig.getContractSignPushMessageTemplate();
        wmInboxSendMessageAdapter.sendPoiMessage((long) wmPoiDomain.getWmPoiId(), paramMap, publishMessTemplate);
    }

    /**
     * @param batchTypeEnum 批量任务
     * @param customerId    客户id
     * @param agentName     代理商id
     * @param wmPoiIdList   门店list
     * @param smsParamInfo  sms参数
     * @param wmPoiDomain   门店信息
     * @return 亲爱的商家$$name#$:  您与$$platform#$的$$module#$($$detail#$）$$other#$，请前往$$shortlink#$
     * * 确认。有问题请联系经理$$name#$$$phone#$。
     * @throws WmCustomerException WmCustomerException异常
     */
    @VisibleForTesting
    protected Map<String, String> buildSignPushMessageBase(

        EcontractBatchTypeEnum batchTypeEnum, Integer customerId,
        String agentName, List<Long> wmPoiIdList,
        String smsParamInfo, WmPoiDomain wmPoiDomain
    )
        throws WmCustomerException {
        Map<String, String> paramMap = new HashMap<>();

        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        WmEmploy wmEmploy = new WmEmploy();
        try {
            wmEmploy = getOwner(wmCustomerDB);
        } catch (TException e) {
            log.error("获取责任人信异常", e);
        }
        if (Objects.nonNull(wmCustomerDB)) {
            paramMap.put("customername", Optional.ofNullable(wmCustomerDB.getCustomerName()).orElse(StringUtils.EMPTY));
        } else {
            paramMap.put("customername", StringUtils.EMPTY);
        }

        boolean isShowKefuInfo = wmEcontractSmsWrapperService.isShowKefuInfo(wmPoiIdList, batchTypeEnum);
        //  判断是否展示闪购虚拟号码
        boolean isShowSgVirtualNum = false;
        if (Objects.nonNull(wmCustomerDB)) {
            isShowSgVirtualNum = wmEcontractSmsWrapperService.isShowVirtualNumber(wmCustomerDB.getCustomerRealType());
        }

        if (StringUtils.isNotEmpty(smsParamInfo)) {
            log.info("pre smsParamInfo={}", smsParamInfo);
            JSONObject resultObject = JSONObject.parseObject(smsParamInfo);
            paramMap.put("platform", resultObject.getString("platform"));
            paramMap.put("module", resultObject.getString("module"));
            paramMap.put("detail", resultObject.getString("detail"));
            paramMap.put("other", resultObject.getString("other"));
        } else {
            String platform = "美团外卖";
            if (C2_CONTRACT.getType() == (batchTypeEnum.getType())) {
                platform = agentName;
            }
            paramMap.put("platform", platform);
            paramMap.put("module", analysisModule(batchTypeEnum));
            paramMap.put("detail", analysisDetail(batchTypeEnum, (long) wmPoiDomain.getWmPoiId(), wmCustomerDB));
            paramMap.put("other", analysisOther(batchTypeEnum));
        }

        if (isShowKefuInfo) {
            paramMap.put("name", StringUtils.EMPTY);
            paramMap.put("phone", KEFU_PHONE);
        } else if (isShowSgVirtualNum) {
            paramMap.put("name", Objects.isNull(wmEmploy) ? StringUtils.EMPTY : wmEmploy.getName());
            paramMap.put("phone", MccConfig.getShanGouBdVirtualNumber());
        } else {
            paramMap.put("name", Objects.isNull(wmEmploy) ? StringUtils.EMPTY : wmEmploy.getName());
            paramMap.put("phone", Objects.isNull(wmEmploy) ? StringUtils.EMPTY
                : MoreObjects.firstNonNull(empServiceAdaptor.getPhone(wmEmploy.getUid()), StringUtils.EMPTY));
        }

        LOGGER.info("buildSignPushMessage paramMap={}", JSON.toJSONString(paramMap));
        return paramMap;
    }

    private void extSignHyperLink(WmPoiDomain wmPoiDomain, Map<String, String> paramMap, Integer bizOrgCode,
                                  Integer singleMultiPoiType) {

        if (Objects.equals(singleMultiPoiType, SingleMultiPoiTypeEnum.SINGLE_POI.getCode())
            && bizOrgCode == CustomerBizOrgEnum.WAI_MAI.getCode()) {
            paramMap.put("PC:shortlink",
                String.format("<a href=\"%s\">%s</a>",
                    MccConfig.toSignPCJumpLink() + wmPoiDomain.getWmPoiId(),
                    "合同中心待签约页"));
            paramMap.put("APP:shortlink",
                String.format("<a href=\"%s\">%s</a>",
                    MccConfig.toSignAPPJumpLink() + wmPoiDomain.getWmPoiId(),
                    "合同中心待签约页"));

        } else if (Objects.equals(singleMultiPoiType, SingleMultiPoiTypeEnum.MULTI_POI.getCode())
            && bizOrgCode == CustomerBizOrgEnum.WAI_MAI.getCode()) {
            paramMap.put("shortlink",
                String.format("<a href=\"%s\">%s</a>",
                    MccConfig.toSignBizOrgPCJumpLink()
                        .stream()
                        .filter(m -> Objects.equals(m.getBizOrgCode(), bizOrgCode)
                            && Objects.equals(m.getSingleMultiPoiType(),
                            singleMultiPoiType))
                        .findFirst()
                        .get()
                        .getSignHyperLink(),
                    "合同中心待签约页"));
        }
    }

    private void pushMsg(EcontractBatchTypeEnum batchTypeEnum, Integer customerId, String agentName,
                         String applyContext, List<Long> wmPoiIdList, Long wmPoiId, String extMsg, String smsParamInfo)
        throws WmCustomerException {

        Map<String, String> extMsgMap = JSON.parseObject(extMsg, Map.class);
        String wmMerchantChannelUrl = !CollectionUtils.isEmpty(extMsgMap)
            ? extMsgMap.get(SMSConstant.WM_MERCHANT_CHANNEL)
            : StringUtils.EMPTY;
        if (Strings.isNullOrEmpty(wmMerchantChannelUrl)) {
            LOGGER.warn("pushMsg#短链接内容为空，渠道={}", SMSConstant.WM_MERCHANT_CHANNEL);
            return;
        }
        WmPoiDomain wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
        String msg = "亲爱的商家(" + wmPoiDomain.getName() + "):<br/>"
                + getMessaging(batchTypeEnum, customerId, agentName, wmPoiId, applyContext, wmMerchantChannelUrl, smsParamInfo);
        if (Strings.isNullOrEmpty(msg)) {
            LOGGER.warn("发送消息内容为空");
            return;
        }
        int expireTime = DateUtil.date2Unixtime(DateUtil.toTommorow(new Date()));
        wmCrmNoticeService.pubWmENoticeNoAudit(SMSConstant.WM_E_NOTICE_TITLE, msg, expireTime, wmPoiIdList);
    }

    private String getMessaging(EcontractBatchTypeEnum batchTypeEnum, Integer customerId, String agentName,
            Long wmPoiId, String applyContext, String wmMerchantChannelUrl, String smsParamInfo) {
        String msg = StringUtils.EMPTY;
        try {
            TemplateInfoDTO templateInfoDTO = cmsTemplateValidService
                    .getTemplateInfo(Integer.valueOf(SMSConstant.TEMPLATE_ECONTRACT));
            LOGGER.info("getMessaging#templateInfoDTO={}", JSON.toJSONString(templateInfoDTO));
            if (templateInfoDTO == null) {
                return StringUtils.EMPTY;
            }
            msg = templateInfoDTO.getContent();
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
            WmEmploy wmEmploy = getOwner(wmCustomerDB);

            List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(customerId);
            boolean isShowKefuInfo = wmEcontractSmsWrapperService.isShowKefuInfo(wmPoiIdList, batchTypeEnum);
            /* 短信参数 */
            Map<String, String> smsParamMap = Maps.newHashMap();

            if (StringUtils.isNotEmpty(smsParamInfo)) {
                JSONObject resultObject = JSONObject.parseObject(smsParamInfo);
                smsParamMap.put("platform", resultObject.getString("platform"));
                smsParamMap.put("module", resultObject.getString("module"));
                smsParamMap.put("detail", resultObject.getString("detail"));
                smsParamMap.put("other", resultObject.getString("other"));
            } else {
                String platform = "美团外卖";
                if (C2_CONTRACT.getType() == (batchTypeEnum.getType())) {
                    platform = agentName;
                }
                smsParamMap.put("platform", platform);
                smsParamMap.put("module", analysisModule(batchTypeEnum));
                smsParamMap.put("detail", analysisDetail(batchTypeEnum, wmPoiId, wmCustomerDB, applyContext));
                smsParamMap.put("other", analysisOther(batchTypeEnum));
            }

            if (isShowKefuInfo) {
                smsParamMap.put("name", "");
                smsParamMap.put("phone", KEFU_PHONE);
            } else {
                smsParamMap.put("name", wmEmploy == null ? "" : wmEmploy.getName());
                smsParamMap.put("phone", wmEmploy == null ? ""
                        : MoreObjects.firstNonNull(empServiceAdaptor.getPhone(wmEmploy.getUid()), ""));
            }
            smsParamMap.put("shortlink", String.format("<a href=\"%s\" target=\"_blank\">%s</a>", wmMerchantChannelUrl,
                    wmMerchantChannelUrl));
            LOGGER.info("smsParamMap={}", JSON.toJSONString(smsParamMap));
            for (Map.Entry<String, String> entry : smsParamMap.entrySet()) {
                msg = msg.replace("$$" + entry.getKey() + "#$", entry.getValue());
            }

        } catch (Exception e) {
            LOGGER.error("getMessaging失败{}", e);
        }
        return msg;
    }

    @VisibleForTesting
    protected String getToSignMessaging(EcontractBatchTypeEnum batchTypeEnum, Integer customerId, String agentName,
            Long wmPoiId, String applyContext, String jumpLink, String smsParamInfo) {
        String msg = StringUtils.EMPTY;
        try {
            TemplateInfoDTO templateInfoDTO = cmsTemplateValidService
                    .getTemplateInfo(Integer.valueOf(SMSConstant.TEMPLATE_ECONTRACT));
            LOGGER.info("getMessaging#templateInfoDTO={}", JSON.toJSONString(templateInfoDTO));
            if (templateInfoDTO == null) {
                return StringUtils.EMPTY;
            }
            msg = templateInfoDTO.getContent();
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
            WmEmploy wmEmploy = getOwner(wmCustomerDB);

            List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(customerId);
            boolean isShowKefuInfo = wmEcontractSmsWrapperService.isShowKefuInfo(wmPoiIdList, batchTypeEnum);
            boolean isShowSgVirtualNum = false;
            if (Objects.nonNull(wmCustomerDB)) {
                isShowSgVirtualNum = wmEcontractSmsWrapperService.isShowVirtualNumber(wmCustomerDB.getCustomerRealType());
            }

            /* 短信参数 */
            Map<String, String> smsParamMap = Maps.newHashMap();

            if (StringUtils.isNotEmpty(smsParamInfo)) {
                JSONObject resultObject = JSONObject.parseObject(smsParamInfo);
                smsParamMap.put("platform", resultObject.getString("platform"));
                smsParamMap.put("module", resultObject.getString("module"));
                smsParamMap.put("detail", resultObject.getString("detail"));
                smsParamMap.put("other", resultObject.getString("other"));
            } else {
                String platform = "美团外卖";
                if (C2_CONTRACT.getType() == (batchTypeEnum.getType())) {
                    platform = agentName;
                }
                smsParamMap.put("platform", platform);
                smsParamMap.put("module", analysisModule(batchTypeEnum));
                smsParamMap.put("detail", analysisDetail(batchTypeEnum, wmPoiId, wmCustomerDB, applyContext));
                smsParamMap.put("other", analysisOther(batchTypeEnum));
            }

            if (isShowKefuInfo) {
                smsParamMap.put("name", "");
                smsParamMap.put("phone", KEFU_PHONE);
            } else if (isShowSgVirtualNum) {
                smsParamMap.put("name", wmEmploy == null ? "" : wmEmploy.getName());
                smsParamMap.put("phone", MccConfig.getShanGouBdVirtualNumber());
            } else {
                smsParamMap.put("name", wmEmploy == null ? "" : wmEmploy.getName());
                smsParamMap.put("phone", wmEmploy == null ? ""
                    : MoreObjects.firstNonNull(empServiceAdaptor.getPhone(wmEmploy.getUid()), ""));
            }

            smsParamMap.put("shortlink",
                String.format("<a href=\"%s\">%s</a>", jumpLink + wmPoiId, "合同中心待签约页"));
            LOGGER.info("#getToSignMessaging#smsParamMap={}", JSON.toJSONString(smsParamMap));
            for (Map.Entry<String, String> entry : smsParamMap.entrySet()) {
                msg = msg.replace("$$" + entry.getKey() + "#$", entry.getValue());
            }

        } catch (Exception e) {
            LOGGER.error("getMessaging失败{}", e);
        }
        log.info("#getToSignMessaging#msg={}", msg);
        return msg;
    }

    private String getAddedserviceDiscountPushMsgMessaging(String templateEcontractAddedServiceDiscount_channel) {

        String msg = StringUtils.EMPTY;
        try {
            TemplateInfoDTO templateInfoDTO = cmsTemplateValidService
                    .getTemplateInfo(Integer.valueOf(SMSConstant.TEMPLATE_ECONTRACT_ADDED_SERVICE_DISCOUNT));

            LOGGER.info("getAddedserviceDiscountPushMsgMessaging#templateInfoDTO={}",
                    JSON.toJSONString(templateInfoDTO));
            if (templateInfoDTO == null) {
                return StringUtils.EMPTY;
            }

            msg = templateInfoDTO.getContent();

            /* 短信参数 */
            Map<String, String> smsParamMap = Maps.newHashMap();
            smsParamMap.put("shortlink", String.format("<a href=\"%s\" target=\"_blank\">%s</a>",
                    templateEcontractAddedServiceDiscount_channel, templateEcontractAddedServiceDiscount_channel));

            LOGGER.info("smsParamMap={}", JSON.toJSONString(smsParamMap));
            for (Map.Entry<String, String> entry : smsParamMap.entrySet()) {
                msg = msg.replace("$$" + entry.getKey() + "#$", entry.getValue());
            }

        } catch (Exception e) {
            LOGGER.error("getMessaging失败{}", e);
        }

        return msg;

    }

    /**
     * 模块解析
     */
    private String analysisModule(EcontractBatchTypeEnum batchTypeEnum) {
        switch (batchTypeEnum) {
        case BATCH_CSD:
            return "合作信息";
        case BATCH_SD:
            return "合作信息";
        case BATCH_CD:
        case BATCH_CDH:
            return "合作信息";
        case BATCH_CS:
            return "合作信息";
        case BATCH_C1C2D:
        case BATCH_C1C2DH:
            return "合作信息";
        case SIGNER_CONFIRM:
            return "客户";
        case CANCEL_CONFIRM:
            return "门店";
        case DELIVERY:
            return "合作方案";
        case SETTLE:
            return "结算";
        case C1_CONTRACT:
            return "合同";
        case C2_CONTRACT:
            return "合同";
        case QUA_REAL_LETTER:
            return "承诺函";
        case BATCH_DELIVERY:
            return "合作方案";
        case POI_PROMOTION_SERVICE:
            return "广告合同";
        case GROUP_MEAL:
            return "企业订餐协议";
        case BUSINESS_CUSTOMER_E_CONTRACT:
            return "企客配送合同";
        case BAG_SERVICE:
            return "打包服务协议";
        case MED_DEPOSIT:
            return "保证金协议";
        case MEDIC_ORDER_SPLIT:
            return "医药分单补充协议";
        case AGENT_SQS_STANDARD:
            return "神抢手收费标准";
        case DRONE_DELIVERY:
            return DRONE_DELIVERY.getName();
        case FRUIT_TOGETHER_DELIVERY:
            return FRUIT_TOGETHER_DELIVERY.getName();
        case FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT:
            return FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT.getName();
        case SPEEDY_DELIVERY_COOPERATION_AGREEMENT:
            return SPEEDY_DELIVERY_COOPERATION_AGREEMENT.getName();
        default:
            return StringUtils.EMPTY;
        }
    }

    private String analysisDetail(EcontractBatchTypeEnum batchTypeEnum, Long wmPoiId, WmCustomerDB wmCustomerDB)
        throws WmCustomerException {
        return analysisDetail(batchTypeEnum, wmPoiId, wmCustomerDB, null);
    }


    /**
     * 说明解析
     */
    private String analysisDetail(EcontractBatchTypeEnum batchTypeEnum, Long wmPoiId, WmCustomerDB wmCustomerDB,
                                  String applyContext) throws WmCustomerException {
        WmPoiDomain wmPoiDomain;
        switch (batchTypeEnum) {
            case BATCH_CSD:
                wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
                return wmPoiDomain == null ? StringUtils.EMPTY : wmPoiDomain.getName();
            case BATCH_CS:
                wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
                return wmPoiDomain == null ? StringUtils.EMPTY : wmPoiDomain.getName();
            case BATCH_CD:
            case BATCH_CDH:
                wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
                return wmPoiDomain == null ? StringUtils.EMPTY : wmPoiDomain.getName();
            case BATCH_C1C2D:
            case BATCH_C1C2DH:
                wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
                return wmPoiDomain == null ? StringUtils.EMPTY : wmPoiDomain.getName();
            case BATCH_SD:
                wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
                return wmPoiDomain == null ? StringUtils.EMPTY : wmPoiDomain.getName();
            case SIGNER_CONFIRM:
                return wmCustomerDB.getCustomerName();
            case CANCEL_CONFIRM:
                wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
                return wmPoiDomain == null ? StringUtils.EMPTY : wmPoiDomain.getName();
            case DELIVERY:
                wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
                return wmPoiDomain == null ? StringUtils.EMPTY : wmPoiDomain.getName();
            case SETTLE:
                return wmCustomerDB.getCustomerName();
            case C1_CONTRACT:
                return wmCustomerDB.getCustomerName();
            case C2_CONTRACT:
                return wmCustomerDB.getCustomerName();
            case QUA_REAL_LETTER:
                return wmCustomerDB.getCustomerName();
            case BATCH_DELIVERY:
                wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
                return wmPoiDomain == null ? StringUtils.EMPTY : wmPoiDomain.getName();
            case POI_PROMOTION_SERVICE:
                return wmCustomerDB.getCustomerName();
            case GROUP_MEAL:
                return wmCustomerDB.getCustomerName();
            case BUSINESS_CUSTOMER_E_CONTRACT:
                return wmCustomerDB.getCustomerName();
            case MED_DEPOSIT:
                return wmCustomerDB.getCustomerName();
            case BAG_SERVICE:
                return wmCustomerDB.getCustomerName();
            case MEDIC_ORDER_SPLIT:
                return wmCustomerDB.getCustomerName();
            case FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT:
            case SPEEDY_DELIVERY_COOPERATION_AGREEMENT:
                return wmCustomerDB.getCustomerName();
            default:
                return StringUtils.EMPTY;
        }
    }

    /**
     * 其他信息解析
     */
    private String analysisOther(EcontractBatchTypeEnum batchTypeEnum) {
        LOGGER.info("其他信息解析={}", batchTypeEnum);
        switch (batchTypeEnum) {
        case BATCH_CSD:
            return "审核通过";
        case BATCH_SD:
            return "审核通过";
        case BATCH_CD:
        case BATCH_CDH:
            return "审核通过";
        case BATCH_CS:
            return "审核通过";
            case BATCH_C1C2D:
            case BATCH_C1C2DH:
                return "审核通过";
            case SIGNER_CONFIRM:
                return "申请签约人信息变更";
            case CANCEL_CONFIRM:
                return "申请更换客户主体";
            case DELIVERY:
                return "审核通过";
            case SETTLE:
                return "录入完成";
            case C1_CONTRACT:
                return "录入完成";
            case C2_CONTRACT:
                return "录入完成";
            case QUA_REAL_LETTER:
                return "录入完成";
            case BATCH_DELIVERY:
                return "审核通过";
            case POI_PROMOTION_SERVICE:
                return "录入完成";
            case GROUP_MEAL:
                return "等待签约";
            case BUSINESS_CUSTOMER_E_CONTRACT:
                return "等待签约";
            case MED_DEPOSIT:
                return "等待签约";
            case BAG_SERVICE:
                return "等待签约";
            case MEDIC_ORDER_SPLIT:
                return "等待签约";
            case AGENT_SQS_STANDARD:
                return "审核通过";
            case DRONE_DELIVERY:
                return "等待签约";
            case FRUIT_TOGETHER_DELIVERY:
                return "等待签约";
            case FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT:
            case SPEEDY_DELIVERY_COOPERATION_AGREEMENT:
                return "等待签约";
            default:
                return StringUtils.EMPTY;
        }
    }

    /**
     * 更新责任人信息
     */
    private WmEmploy getOwner(WmCustomerDB wmCustomerDB) throws TException, WmCustomerException {
        return wmEmployClient.getById(wmCustomerDB.getOwnerUid());
    }
}
