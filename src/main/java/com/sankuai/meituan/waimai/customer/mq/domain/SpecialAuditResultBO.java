package com.sankuai.meituan.waimai.customer.mq.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 20240228
 * @desc 特批审核结果对象
 */
@Data
public class SpecialAuditResultBO {

    /**
     * 任务id
     */
    private Integer applicationId;

    /**
     * 任务系统id
     */
    private Integer ticketId;

    /**
     * 状态
     * com.sankuai.meituan.waimai.thrift.rateApproval.constant.ApprovalStatusEnum
     */
    private Integer state;

    /**
     * 驳回原因
     */
    private String lastOperateReason;

    /**
     * 任务类型id
     */
    private Integer type;

    /**
     * 维度(对象)
     * com.sankuai.meituan.waimai.thrift.constatnt.config.DimensionTypeEnum
     */
    private Integer dimensionId;

    /**
     * 申请人id
     */
    private Integer applyUserId;

    /**
     * 特批申请详细数据-json
     */
    private String applyData;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 最后操作人id
     */
    private Integer lastOperateUserId;

    /**
     * 最后操作时间
     */
    private Integer lastOperateTime;

}
