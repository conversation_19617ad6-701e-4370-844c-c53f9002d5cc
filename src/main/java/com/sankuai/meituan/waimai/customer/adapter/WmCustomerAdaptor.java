package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.customer.QueryCustomerBO;
import com.sankuai.meituan.waimai.customer.bo.customer.WmCustomerBO;
import com.sankuai.meituan.waimai.customer.util.common.ConvertUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerListBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * -@author: h<PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2022/11/22 10:50 PM
 */
@Slf4j
@Service
public class WmCustomerAdaptor {

    @Autowired
    private WmCustomerThriftService wmCustomerThriftService;

    /**
     * 通过关键字，客户名字（模糊）或 客户id，查询。 最多返回30条记录
     *
     * @param queryCustomerBO :queryKeyword  关键字  & customerSearchTypeEnum @see CustomerSearchTypeEnum
     * @return 返回客户BO
     */
    public List<WmCustomerBO> queryCustomerByKeyword(QueryCustomerBO queryCustomerBO) {
        try {
            log.info("查询客户信息入参,queryCustomerBO = {}", JSON.toJSONString(queryCustomerBO));
            List<WmCustomerListBo> customerListBoList = wmCustomerThriftService.searchCustomerListByKeyWord(
                queryCustomerBO.getCustomerKeyword(),
                queryCustomerBO.getSearchCustomerType());
            if (CollectionUtils.isEmpty(customerListBoList)) {
                return Collections.emptyList();
            }
            List<WmCustomerBO> wmCustomerBOList = Lists.newArrayList();
            for (WmCustomerListBo m : customerListBoList
            ) {
                WmCustomerBO wmCustomerBO = WmCustomerBO.builder()
                    .id(m.getId())
                    .customerName(m.getCustomerName())
                    .customerType(m.getCustomerType())
                    .wmPoiCount(m.getWmPoiCount())
                    .mtCustomerId(m.getMtCustomerId())
                    .superCustomerId(m.getSuperCustomerId())
                    .superCustomerName(m.getSuperCustomerName())
                    .subCustomerCount(m.getSubCustomerCount())
                    .customerRealType(m.getCustomerRealType())
                    .build();
                wmCustomerBOList.add(wmCustomerBO);
            }
            log.info("查询客户信息返回,wmCustomerBOList = {}", JSON.toJSONString(wmCustomerBOList));
            return wmCustomerBOList;

        } catch (TException | WmCustomerException e) {
            log.error("调用客户服务异常:", e);
            throw ConvertUtil.wrapGateWayException(e);
        }
    }
}
