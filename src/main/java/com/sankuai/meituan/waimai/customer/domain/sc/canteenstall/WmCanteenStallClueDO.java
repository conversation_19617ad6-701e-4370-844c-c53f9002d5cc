package com.sankuai.meituan.waimai.customer.domain.sc.canteenstall;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 食堂档口线索信息表DO
 * <AUTHOR>
 * @date 2024/05/10
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmCanteenStallClueDO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 档口管理任务ID
     */
    private Integer manageId;
    /**
     * 档口绑定任务ID
     */
    private Integer bindId;
    /**
     * 食堂主键ID
     */
    private Integer canteenPrimaryId;
    /**
     * 门店名称
     */
    private String cluePoiName;
    /**
     * 门店地址
     */
    private String cluePoiAddress;
    /**
     * 门店电话Token
     */
    private String cluePoiPhoneToken;
    /**
     * 门店电话Encryption
     */
    private String cluePoiPhoneEncryption;
    /**
     * 门店坐标
     */
    private String cluePoiCoordinate;
    /**
     * 门店末级品类ID
     */
    private Long clueLeafCateId;
    /**
     * 二级城市ID（外卖城市）
     */
    private Integer clueSecondCityId;
    /**
     * 三级城市ID（外卖行政区）
     */
    private Integer clueThirdCityId;
    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Integer cuid;
    /**
     * 修改人ID
     */
    private Integer muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
}
