package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck.nationalsubsidy;

import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck.PreCheck;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liuyunjie05
 * @create:  2025/5/24 18:17
 */
@Slf4j
@Service
public class NationalSubsidyPurchasePreCheck implements PreCheck {

    @Resource
    private WmContractService wmContractService;

    @Override
    public void preCheck(String module, List<WmEcontractSignManualTaskDB> taskInfos, int commitUid) throws WmCustomerException, TException {
        List<Long> bizIdList = taskInfos.stream().map(WmEcontractSignManualTaskDB::getWmPoiId).collect(Collectors.toList());
        for (Long bizId : bizIdList) {
            wmContractService.startSignPreCheck(taskInfos.get(0).getCustomerId(), bizId, commitUid, "");
        }
    }
}
