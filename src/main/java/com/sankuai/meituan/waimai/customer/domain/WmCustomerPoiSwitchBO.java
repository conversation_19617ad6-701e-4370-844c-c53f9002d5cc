package com.sankuai.meituan.waimai.customer.domain;

import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiSwitchOperateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 客户切换门店关系操作对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WmCustomerPoiSwitchBO {

    private Long switchTaskId;

    private Integer customerId;

    private Set<Long> wmPoiIdSet;

    private Integer opUid;

    private String opName;

    private WmCustomerPoiSwitchOperateTypeEnum typeEnum;
}
