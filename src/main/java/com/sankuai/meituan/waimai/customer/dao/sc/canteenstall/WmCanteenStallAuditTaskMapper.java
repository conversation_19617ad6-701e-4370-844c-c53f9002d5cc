package com.sankuai.meituan.waimai.customer.dao.sc.canteenstall;

import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 食堂档口审批任务主表Mapper
 * <AUTHOR>
 * @date 2024/05/13
 * @email <EMAIL>
 **/
@Component
public interface WmCanteenStallAuditTaskMapper {

    /**
     * 根据主键ID查询食堂档口审批任务主表信息
     * @param id 主键ID
     * @return 食堂档口审批任务主表信息
     */
    WmCanteenStallAuditTaskDO selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 根据主键ID查询食堂档口审批任务主表信息(主库查询)
     * @param id 主键ID
     * @return 食堂档口审批任务主表信息
     */
    WmCanteenStallAuditTaskDO selectByPrimaryKeyByMaster(@Param("id") Integer id);

    /**
     * 新增食堂档口审批任务主表信息
     * @param wmCanteenStallAuditTaskDO 食堂档口审批任务主表信息
     * @return 更新行数
     */
    int insertSelective(WmCanteenStallAuditTaskDO wmCanteenStallAuditTaskDO);

    /**
     * 根据主键ID更新食堂档口审批任务主表信息
     * @param wmCanteenStallAuditTaskDO 食堂档口审批任务主表信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmCanteenStallAuditTaskDO wmCanteenStallAuditTaskDO);

}
