package com.sankuai.meituan.waimai.customer.constant;

/**
 * Tair锁灰度百分比key
 */
public enum TairLockGrayPercentKeyEnum {
    UN_SETTING(0, "unsetting"),
    OLD_CUSTOMER_CONFIRM_START(1, "old.customer.confirm.start"),
    OLD_CUSTOMER_CONFIRM_OPERATE(2, "old.customer.confirm.operate"),
    SWITCH_CUSTOMER(3, "switch.customer"),
    CANCEL_SWITCH_CUSTOMER(4, "cancel.switch.customer"),
    ROLLBACK_SWITCH_CUSTOMER(5, "rollback.switch.customer"),
    SWITCH_FORCE_UNBIND(6, "switch.force.unbind"),
    NEW_CUSTOMER_CONFIRM_OPERATE(7, "new.customer.confirm.operate"),
    NEW_CUSTOMER_CONFIRM_START(8, "new.customer.confirm.start"),
    OLD_CUSTOMER_CONFIRM_OPERATE_SWITCH(9, "old.customer.confirm.operate.switch"),
    NEW_CUSTOMER_CONFIRM_OPERATE_SWITCH(10,"new.customer.confirm.operate.switch"),


    ;


    private int code;
    private String key;

    public int getCode() {
        return code;
    }

    public String getKey() {
        return key;
    }

    TairLockGrayPercentKeyEnum(int code, String key) {
        this.code = code;

        this.key = key;
    }
}
