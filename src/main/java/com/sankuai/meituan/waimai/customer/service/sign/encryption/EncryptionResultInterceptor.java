package com.sankuai.meituan.waimai.customer.service.sign.encryption;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.annotation.Encryption;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.EncryptionTypeConstant;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractEncryptionRecordDB;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by lixuepeng on 2021/8/24
 */
@Intercepts(@Signature(type = Executor.class, method = "query", args = { MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class }))
public class EncryptionResultInterceptor implements Interceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(EncryptionResultInterceptor.class);

    private  EncryptionService encryptionService;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (!MccConfig.isEncryptionSwitch() || !MccConfig.isFillingByEncrypedValue()) {//加密总开关&读密文总开关
            return invocation.proceed();
        }

        List<?> resultSet = (List<?>) invocation.proceed();
        //查询结果为空则返回
        if(CollectionUtils.isEmpty(resultSet)) {
            return resultSet;
        }

        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        Object parameterObject = invocation.getArgs()[1];
        //获取目标SQL
        BoundSql boundSql = ms.getBoundSql(parameterObject);
        if (!isTargetTableName(boundSql)) {//不是目标表名
            return resultSet;
        }

        for (Object record : resultSet) {

            //如果记录为空则跳过
            if(record == null) {
                continue;
            }

            //获取注解
            Encryption encryption = record.getClass().getAnnotation(Encryption.class);
            if (encryption == null) {
                continue;
            }

            MetaObject metaParameterObject = MetaObject.forObject(record);
            //获取记录主键值
            long recordId = metaParameterObject.getValue("id") == null ? 0 : (long) metaParameterObject.getValue("id");

            //非灰度记录
            if (!isFillingByEncrypedValue(recordId)) {
                continue;
            }

            long beginTime = System.currentTimeMillis();
            //获取该对象需要加密的字段
            String[] fields = encryption.fields();
            //获取该对象类型
            int recordType = fitRecordType(encryption, boundSql);
            //是否JSON格式
            boolean isJSON = encryption.isJSON();

            if(encryptionService == null) {
                encryptionService = (EncryptionService) SpringContext.getBean("EncryptionService");
            }

            //遍历对象需要加密的字段，对每个字段值进行解密
            for (String toDecryptField : fields) {
                String decryptedValue = encryptionService.doHandleDecrypt(recordType, recordId, toDecryptField);
                String originalValue = (String) metaParameterObject.getValue(toDecryptField);
                //若不为空，表示解密成功 && 对于JSON的字符串解密后也要是JSON格式 && 明文=密文，赋值密文解密值
                if (StringUtils.isNotEmpty(decryptedValue) && isJSON(decryptedValue, isJSON)
                        && isDataMatching(recordType, recordId, toDecryptField, originalValue, decryptedValue)) {
                    metaParameterObject.setValue(toDecryptField, decryptedValue);
                }
            }
            long endTime = System.currentTimeMillis();
            LOGGER.debug("EncryptionResultInterceptor#intercept cost:{} ms", endTime - beginTime);
        }

        return resultSet;
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        } else {
            return target;
        }
    }

    @Override
    public void setProperties(Properties properties) {

    }

    private int fitRecordType(Encryption encryption, BoundSql boundSql) {
        //针对线上表，进行记录类型适配
        if (encryption.recordType() == EncryptionTypeConstant.TEMP_CONTRACT_SIGN_RECORD
                && boundSql.getSql().contains("wm_templet_contract_sign_audited")) {
            return EncryptionTypeConstant.TEMP_CONTRACT_SIGN_AUDITED_RECORD;
        }
        return encryption.recordType();
    }

    private boolean isFillingByEncrypedValue(long recordId) {
        return recordId % 100 < MccConfig.fillingByEncrypedValuePercent();
    }

    private boolean isTargetTableName(BoundSql boundSql) {
        try {
            List<String> tableNameList = Arrays.asList(MccConfig.encryptionTableNameList().split(","));
            if (CollectionUtils.isNotEmpty(tableNameList)) {
                for (String tableName : tableNameList) {
                    if (boundSql.getSql().contains(tableName)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("EncryptionResultInterceptor#isTargetTableName 判断是否目标表名异常 boundSql:{}", boundSql, e);
        }
        return false;
    }

    private boolean isJSON(String str, boolean isJSON) {
        if (!isJSON) {
            return true;
        }
        try {
            Object object = JSON.parse(str);
            return true;
        } catch (Exception e) {
            LOGGER.error("EncryptionResultInterceptor#isJSON 判断是否JSON异常 str:{}", str, e);
        }
        return false;
    }

    //对比密文和明文是否一致
    private boolean isDataMatching(int recordType, long recordId, String toDecryptField, String originalValue, String decryptedValue) {
        try {
            //如果此时已经开启明文下线开关，则无需比对
            if (MccConfig.isFilterOriginValue()) {
                return true;
            }
            //如果解密后的密文和明文相等，则返回true
            if(originalValue.equals(decryptedValue)) {
                return true;
            }
            //不相等的情况打印日志
            LOGGER.info("EncryptionResultInterceptor#isDataMatching 明文≠密文 originalValue:{} decryptedValue:{}", originalValue, decryptedValue);
            //不相等的情况下重新加密
            if (MccConfig.isOverWriteEncryptionRecord()) {
                //对文本进行加密，得到加密后文本列表
                List<WmEcontractEncryptionRecordDB> encryptionRecordDBList = encryptionService.queryEncrypttionRecordList(toDecryptField, originalValue);
                //保存最新加密信息
                encryptionService.doSaveEncryptionRecords(recordType, recordId, encryptionRecordDBList);
            }
        } catch (Exception e) {
            LOGGER.error("EncryptionResultInterceptor#isDataMatching originalValue:{} decryptedValue:{}", originalValue, decryptedValue, e);
        }
        return false;
    }
}
