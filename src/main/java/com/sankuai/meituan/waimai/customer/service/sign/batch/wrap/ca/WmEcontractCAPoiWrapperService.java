package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.IdentityType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerKPBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WmEcontractCAPoiWrapperService implements IWmEcontractCAWrapperService {

    @Autowired
    WmCustomerService wmCustomerService;
    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    private static Logger logger = LoggerFactory.getLogger(WmEcontractCAPoiWrapperService.class);

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo) throws WmCustomerException {
        if (EcontractBatchTypeEnum.isDcEcontractType(contextBo.getBatchTypeEnum())) {
            return wrapDcStageBatchInfoBo(contextBo);
        }
        CAType caType = CustomerType.CUSTOMER_TYPE_BUSINESS.equals(contextBo.getCustomerInfoBo().getQuaTypeEnum())
                || CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.equals(contextBo.getCustomerInfoBo().getQuaTypeEnum())
                ? CAType.COMPANY
                : CAType.PERSON;
        IdentityType identityType = getIdentityType(contextBo.getCustomerId(), caType);
        CertifyInfoBo certifyInfoBo = getCertifyInfoBo(contextBo, identityType, caType);
        //封装签约人信息
        if (isSyncSignerInfo(contextBo.getCustomerId())) {
            addCompanySignerInfo(certifyInfoBo, contextBo);
        }
        logger.debug("WmEcontractCAPoiWrapperService##wrap certifyInfoBo:{}", JSON.toJSONString(certifyInfoBo));
        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.CA_POI)
                .certifyInfoBo(certifyInfoBo)
                .build();
    }

    private StageBatchInfoBo wrapDcStageBatchInfoBo(EcontractBatchContextBo contextBo) throws WmCustomerException {
        CAType caType = CustomerType.CUSTOMER_TYPE_BUSINESS.equals(contextBo.getDcCustomerInfo().getQuaTypeEnum())
                || CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.equals(contextBo.getDcCustomerInfo().getQuaTypeEnum())
                ? CAType.COMPANY
                : CAType.PERSON;
        IdentityType identityType = getDcIdentityType(contextBo.getDcCustomerInfo(), caType);
        CertifyInfoBo certifyInfoBo = getDcCertifyInfoBo(contextBo, identityType, caType);
        //封装签约人信息
        if (isSyncSignerInfo(contextBo.getCustomerId())) {
            addDcCompanySignerInfo(certifyInfoBo, contextBo);
        }
        logger.info("WmEcontractCAPoiWrapperService##wrapDcStageBatchInfoBo, certifyInfoBo:{}", JSON.toJSONString(certifyInfoBo));
        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.CA_POI)
                .certifyInfoBo(certifyInfoBo)
                .build();
    }

    private CertifyInfoBo getDcCertifyInfoBo(EcontractBatchContextBo contextBo, IdentityType identityType, CAType caType) {
        return new CertifyInfoBo.Builder()
                .setCustomerName(contextBo.getDcCustomerInfo().getCustomerName())
                .setMobile(contextBo.getDcCustomerKp().getPhoneNum())
                .setEmail(contextBo.getDcCustomerKp().getEmail())
                .setCaType(caType)
                .setQuaNum(contextBo.getDcCustomerInfo().getQuaNum())
                .setIdentityType(identityType)
                .build();
    }

    private IdentityType getDcIdentityType(EcontractCustomerInfoBo dcCustomerInfo, CAType caType) {
        if (dcCustomerInfo == null || caType == CAType.COMPANY) {
            return null;
        }
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(dcCustomerInfo.getCustomerSecondType());
        if (certTypeEnum == null) {
            logger.warn("WmEcontractCAPoiWrapperService#getDcIdentityType, 找不到到餐KP对应的证件类型, dcCustomerInfo: {}", JSON.toJSONString(dcCustomerInfo));
            return IdentityType.ID_CARD;
        }
        switch (certTypeEnum) {
            case PASSPORT:
                return IdentityType.PASSPORT;
            case HK_MACAO_REENTRY_PERMIT:
                return IdentityType.HONG_KONG_AND_MACAU_PASSER;
            default:
                return IdentityType.ID_CARD;
        }
    }

    private CertifyInfoBo getCertifyInfoBo(EcontractBatchContextBo contextBo, IdentityType identityType, CAType caType) {
        return new CertifyInfoBo.Builder()
                .setCustomerName(WmEcontractContextUtil.getCustomerName(contextBo))
                .setMobile(contextBo.getKpBo().getSignerPhoneNum())
                .setEmail(contextBo.getKpBo().getSignerEmail())
                .setCaType(caType)
                .setQuaNum(contextBo.getCustomerInfoBo().getQuaNum())
                .setIdentityType(identityType)
                .build();
    }

    private IdentityType getIdentityType(Integer customerId, CAType caType)
            throws WmCustomerException {
        if (customerId == null || customerId == 0 || caType == CAType.COMPANY) {
            return null;
        }
        WmCustomerDB customerDB = wmCustomerService.selectCustomerByIdRT(customerId);
        if (customerDB == null) {
            logger.error("主从延迟查询客户为空 customerId：" + customerId);
            return null;
        }
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(customerDB.getCustomerSecondType());
        switch (certTypeEnum) {
            case PASSPORT:
                return IdentityType.PASSPORT;
            case HK_MACAO_REENTRY_PERMIT:
                return IdentityType.HONG_KONG_AND_MACAU_PASSER;
            default:
                return IdentityType.ID_CARD;
        }
    }

    private void addCompanySignerInfo(CertifyInfoBo certifyInfoBo, EcontractBatchContextBo contextBo) throws WmCustomerException {
        try {
            EcontractCustomerKPBo kpBo = contextBo.getKpBo();
            if (kpBo == null) {
                logger.error("客户信息为空 contextBo:{}" + JSON.toJSONString(contextBo));
                return;
            }
            certifyInfoBo.setIsNewStampIface(1);
            certifyInfoBo.setSignerType(kpBo.getSignerType().getType());//签约人类型
            certifyInfoBo.setSignerName(kpBo.getSignerName());//签约人姓名
            certifyInfoBo.setSignerIdentity(kpBo.getSignerIDCardNum());
            certifyInfoBo.setSignerMobile(kpBo.getSignerPhoneNum());
            CertTypeEnum certTypeEnum = CertTypeEnum.getByType(kpBo.getCertType());
            switch (certTypeEnum) {//https://km.sankuai.com/page/*********
                case ID_CARD:
                    certifyInfoBo.setSignerIdentityType(IdentityType.ID_CARD.getCode());
                    return;
                case ID_CARD_TEMP:
                    certifyInfoBo.setSignerIdentityType(IdentityType.ID_CARD.getCode());
                    return;
                case ID_CARD_COPY:
                    certifyInfoBo.setSignerIdentityType(IdentityType.ID_CARD.getCode());
                    return;
                case DRIVING_LICENCE:
                    certifyInfoBo.setSignerIdentityType(IdentityType.ID_CARD.getCode());
                    return;
                case PASSPORT:
                    certifyInfoBo.setSignerIdentityType(IdentityType.PASSPORT.getCode());
                    return;
                case HK_MACAO_REENTRY_PERMIT:
                    certifyInfoBo.setSignerIdentityType(IdentityType.HONG_KONG_AND_MACAU_PASSER.getCode());
                    return;
                case TAIWAN_REENTRY_PERMIT:
                    certifyInfoBo.setSignerIdentityType(IdentityType.HONG_KONG_AND_MACAU_PASSER.getCode());
                    return;
                case REDIDENCE_PERMIT_OF_HK_MACAO_TAIWAI:
                    certifyInfoBo.setSignerIdentityType(IdentityType.HONG_KONG_AND_MACAU_PASSER.getCode());
                    return;
                default:
                    certifyInfoBo.setSignerIdentityType(IdentityType.ID_CARD.getCode());
                    return;
            }
        } catch (Exception e) {
            logger.error("addCompanySignerInfo error contextBo:{}" + JSON.toJSONString(contextBo), e);
            //失败的情况置为0
            certifyInfoBo.setIsNewStampIface(0);
        }
    }

    private void addDcCompanySignerInfo(CertifyInfoBo certifyInfoBo, EcontractBatchContextBo contextBo) {
        try {
            WmCustomerKp dcCustomerKp = contextBo.getDcCustomerKp();
            if (dcCustomerKp == null) {
                logger.error("WmEcontractCAPoiWrapperService#addDcCompanySignerInfo, 到餐客户信息为空, contextBo:{}", JSON.toJSONString(contextBo));
                return;
            }
            certifyInfoBo.setIsNewStampIface(1);
            certifyInfoBo.setSignerType(dcCustomerKp.getSignerType());//签约人类型
            certifyInfoBo.setSignerName(dcCustomerKp.getCompellation());//签约人姓名
            certifyInfoBo.setSignerIdentity(dcCustomerKp.getCertNumber());
            certifyInfoBo.setSignerMobile(dcCustomerKp.getPhoneNum());
            CertTypeEnum certTypeEnum = CertTypeEnum.getByType(contextBo.getDcCustomerKp().getCertType());
            if (certTypeEnum == null) {
                logger.warn("WmEcontractCAPoiWrapperService#addDcCompanySignerInfo, 找不到到餐KP对应的证件类型, contextBo: {}", JSON.toJSONString(contextBo));
                certifyInfoBo.setSignerIdentityType(IdentityType.ID_CARD.getCode());
                return;
            }
            switch (certTypeEnum) {
                case PASSPORT:
                    certifyInfoBo.setSignerIdentityType(IdentityType.PASSPORT.getCode());
                    return;
                case HK_MACAO_REENTRY_PERMIT:
                case TAIWAN_REENTRY_PERMIT:
                case REDIDENCE_PERMIT_OF_HK_MACAO_TAIWAI:
                    certifyInfoBo.setSignerIdentityType(IdentityType.HONG_KONG_AND_MACAU_PASSER.getCode());
                    return;
                default:
                    certifyInfoBo.setSignerIdentityType(IdentityType.ID_CARD.getCode());
                    return;
            }
        } catch (Exception e) {
            logger.error("WmEcontractCAPoiWrapperService#addDcCompanySignerInfo, error, contextBo:{}", JSON.toJSONString(contextBo), e);
            //失败的情况置为0
            certifyInfoBo.setIsNewStampIface(0);
        }
    }

    /**
     *  MccConfig.econtractSyncCASignerInfoPercent()线上配置的100  所以这个方法恒返回true
     */
    private boolean isSyncSignerInfo(Integer customerId) {
        if (customerId == null || customerId < 0) {
            return false;
        }
        return customerId % 100 < MccConfig.econtractSyncCASignerInfoPercent();
    }
}
