package com.sankuai.meituan.waimai.customer.dao.blade;

import com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractQueryCondition;
import com.sankuai.meituan.waimai.customer.domain.blade.WmBladeGlobalContractInfoWidePo;
import com.sankuai.meituan.waimai.datasource.multi.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2023/12/27 20:24
 */
@Component
@DataSource("bladeDataSource")
public interface WmBladeGlobalContractInfoWideMapper {

    Long totalWithLimitAuthority(@Param("queryCondition") GlobalEcontractQueryCondition queryCondition);

    Long totalWithAllAuthority(@Param("queryCondition") GlobalEcontractQueryCondition queryCondition);

    List<WmBladeGlobalContractInfoWidePo> queryWithLimitAuthority(@Param("queryCondition") GlobalEcontractQueryCondition queryCondition);


    List<WmBladeGlobalContractInfoWidePo> queryWithAllAuthority(@Param("queryCondition")GlobalEcontractQueryCondition queryCondition);
}
