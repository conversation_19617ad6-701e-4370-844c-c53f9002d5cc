package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.mq.domain.WmLabelNotifyBO;
import com.sankuai.meituan.waimai.customer.mq.domain.WmLabelNotifyDetailBO;
import com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService;
import com.sankuai.meituan.waimai.operation.label.constants.LabelSubjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20230920
 * @desc 门店标签变更监听
 */
@Service
@Slf4j
public class WmPoiLabelNotifyListener implements IMessageListener {

    @Autowired
    private CusPoiRelEsBusinessService cusPoiRelEsBusinessService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        log.info("WmPoiLabelNotifyListener,监听到标签侧变更消息, mafkaMessage = {}, partition = {}", message.getBody(), message.getParttion());
        if (null == message.getBody() || StringUtils.isBlank(message.getBody().toString())) {
            log.error("WmPoiLabelNotifyListener,监听标签变更的消息对象为空，请及时关注,message={}", JSON.toJSONString(message));
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            // 对消费的消息进行解析
            String json = message.getBody().toString();
            WmLabelNotifyBO wmLabelNotifyBO = JSONObject.parseObject(json, WmLabelNotifyBO.class);
            if (wmLabelNotifyBO == null) {
                log.error("WmPoiLabelNotifyListener,监听标签变更的消息内容的转换对象wmLabelNotifyBO为空，请及时关注,message={}", JSON.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 表名过滤
            if (wmLabelNotifyBO.getTableName() != null && !wmLabelNotifyBO.getTableName().contains("wm_poi_label_rel")) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 获取消息体中data
            WmLabelNotifyDetailBO wmLabelNotifyDetailBO = wmLabelNotifyBO.getData();
            if (wmLabelNotifyDetailBO == null) {
                log.error("WmPoiLabelNotifyListener,监听标签变更的消息内容的转换对象wmLabelNotifyDetailBO为空，请及时关注,message={}", JSON.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 对象类型过滤
            if (wmLabelNotifyDetailBO.getSubjectType() == null
                    || !wmLabelNotifyDetailBO.getSubjectType().equals(LabelSubjectTypeEnum.POI.getCode())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //只关注子门店标签
            if (!MccCustomerConfig.getSubPoiTagId().contains(wmLabelNotifyDetailBO.getWmLabelId())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Integer wmPoiId = wmLabelNotifyDetailBO.getSubjectId();
            //重新计算门店的「是否子门店」字段并同步到门店关系ES中
            cusPoiRelEsBusinessService.updateChildPoi2Es(wmPoiId);
            log.info("WmPoiLabelNotifyListener,门店标签关系发生重置门店是否子门店属性到客户门店关系ES完成,wmPoiId={}", wmPoiId);
        } catch (Exception e) {
            log.error("WmPoiLabelNotifyListener,门店标签关系发生变更处理发生异常, message={}", message.toString(), e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
