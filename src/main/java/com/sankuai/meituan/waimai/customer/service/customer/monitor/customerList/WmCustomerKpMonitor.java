package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerList;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerListDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerKpESDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class WmCustomerKpMonitor {

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmCustomerESService wmCustomerESService;

    public String check(MonitorCustomerKpESDTO monitorCustomerKpESDTO) {
        try {
            Preconditions.checkNotNull(monitorCustomerKpESDTO, "参数不合法");
            Preconditions.checkNotNull(monitorCustomerKpESDTO.getValid(), "签约人生效状态不合法");
            Preconditions.checkNotNull(monitorCustomerKpESDTO.getCustomerId(), "客户ID不合法");
            Preconditions.checkNotNull(monitorCustomerKpESDTO.getValid(), "签约人生效状态不合法");
            Preconditions.checkNotNull(monitorCustomerKpESDTO.getSignerId(), "签约人ID不合法");
            Integer customerId = monitorCustomerKpESDTO.getCustomerId();
            WmCustomerListDB wmCustomerListDB = wmCustomerESService.queryCustomerByCustomerId(customerId);

            if (monitorCustomerKpESDTO.getValid().intValue() == ValidEnum.VALID_YES.getValue()) {
                Preconditions.checkNotNull(wmCustomerListDB, String.format("未找到对应客户的ES信息:customerId:%s", monitorCustomerKpESDTO.getCustomerId()));

                Preconditions.checkNotNull(wmCustomerListDB.getSignerId(), String.format("未找到对应客户签约人的ES信息:customerId:%s", monitorCustomerKpESDTO.getCustomerId()));

                Preconditions.checkArgument(monitorCustomerKpESDTO.getSignerId().intValue() == wmCustomerListDB.getSignerId().intValue(),
                        String.format("签约人的ID不一致 customerId:%s,%s,%s", customerId, monitorCustomerKpESDTO.getSignerId(), wmCustomerListDB.getSignerId()));

                Preconditions.checkArgument(monitorCustomerKpESDTO.getSignerType().intValue() == wmCustomerListDB.getSignerType().intValue(),
                        String.format("签约人的类型不一致 customerId:%s,%s,%s", customerId, monitorCustomerKpESDTO.getSignerType(), wmCustomerListDB.getSignerType()));

                Preconditions.checkArgument(monitorCustomerKpESDTO.getSignerCertType().intValue() == wmCustomerListDB.getSignerCertType().intValue(),
                        String.format("签约人的证件类型不一致 customerId:%s,%s,%s", customerId, monitorCustomerKpESDTO.getSignerCertType(), wmCustomerListDB.getSignerCertType()));

                Preconditions.checkArgument(monitorCustomerKpESDTO.getSignerCompellation().equals(wmCustomerListDB.getSignerCompellation()),
                        String.format("签约人的名字不一致 customerId:%s,%s,%s", customerId, monitorCustomerKpESDTO.getSignerCompellation(), wmCustomerListDB.getSignerCompellation()));

                Preconditions.checkArgument(monitorCustomerKpESDTO.getSignerCertNumberToken().equals(wmCustomerListDB.getSignerCertNumberToken()),
                        String.format("签约人的证件号不一致 customerId:%s,%s,%s", customerId, monitorCustomerKpESDTO.getSignerCertNumberToken(), wmCustomerListDB.getSignerCertNumberToken()));

                Preconditions.checkArgument(monitorCustomerKpESDTO.getSignerCertNumberEncryption().equals(wmCustomerListDB.getSignerCertNumberEncryption()),
                        String.format("签约人的证件号不一致 customerId:%s,%s,%s", customerId, monitorCustomerKpESDTO.getSignerCertNumberEncryption(), wmCustomerListDB.getSignerCertNumberEncryption()));

                Preconditions.checkArgument(monitorCustomerKpESDTO.getSignerPhoneNumToken().equals(wmCustomerListDB.getSignerPhoneNumToken()),
                        String.format("签约人的手机号不一致 customerId:%s,%s,%s", customerId, monitorCustomerKpESDTO.getSignerPhoneNumToken(), wmCustomerListDB.getSignerPhoneNumToken()));

                Preconditions.checkArgument(monitorCustomerKpESDTO.getSignerPhoneNumEncryption().equals(wmCustomerListDB.getSignerPhoneNumEncryption()),
                        String.format("签约人的手机号不一致 customerId:%s,%s,%s", customerId, monitorCustomerKpESDTO.getSignerPhoneNumEncryption(), wmCustomerListDB.getSignerPhoneNumEncryption()));

            } else {
                if (wmCustomerListDB == null) {
                    WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerByIdFromSlave(customerId);
                    if (wmCustomerDB != null) {
                        return String.format("未找到对应客户的ES信息:customerId:%s", monitorCustomerKpESDTO.getCustomerId());
                    }
                } else {
                    if (wmCustomerListDB.getSignerId() != null || wmCustomerListDB.getSignerType() != null || wmCustomerListDB.getSignerCertType() != null ||
                            StringUtils.isNotBlank(wmCustomerListDB.getSignerCompellation()) || StringUtils.isNotBlank(wmCustomerListDB.getSignerCertNumberToken())
                            || StringUtils.isNotBlank(wmCustomerListDB.getSignerCertNumberEncryption()) || StringUtils.isNotBlank(wmCustomerListDB.getSignerPhoneNumToken())
                            || StringUtils.isNotBlank(wmCustomerListDB.getSignerPhoneNumEncryption())) {
                        return String.format("对应签约人信息未删除:customerId:%s", monitorCustomerKpESDTO.getCustomerId());
                    }
                }
            }
        } catch (IllegalArgumentException e) {
            log.warn("checkCustomerES 异常 monitorCustomerKpESDTO={}", JSONObject.toJSONString(monitorCustomerKpESDTO), e);
            return e.getMessage();
        } catch (Exception e) {
            log.error("checkCustomerES 异常 monitorCustomerKpESDTO={}", JSONObject.toJSONString(monitorCustomerKpESDTO), e);
        }
        return "";
    }
}

