package com.sankuai.meituan.waimai.customer.constant;

import com.google.common.collect.Maps;

import java.util.HashMap;

/**
 * @program: scm
 * @description: 常量
 * @author: jianghuimin02
 * @create: 2020-05-22 11:24
 **/
public class ScFieldConstant {

    public static HashMap<String, String> schoolField = Maps.newHashMap();

    public static HashMap<String, String> schoolLightOffField = Maps.newHashMap();

    public static HashMap<String, String> schoolExtensionField = Maps.newHashMap();

    public static HashMap<String, String> canteenField = Maps.newHashMap();

    public static HashMap<String, String> schoolAreaField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolBuildingField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolCoPlatformField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolCoPlatformMarketField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolDeliveryFiled = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolPerformanceFiled = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolPerformanceUnitFiled = Maps.newLinkedHashMap();

    public static HashMap<String,String>  schoolAorMsgField = Maps.newHashMap();

    public static HashMap<String, String> schoolTimeInfoField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolTimeTermBeginField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolTimeTermEndField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolDeliveryAssignmentField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolDeliveryGoalSetField = Maps.newLinkedHashMap();

    public static HashMap<String, String> schoolDeliveryFollowUpField = Maps.newLinkedHashMap();

    public static HashMap<String, String> canteenStallClueField = Maps.newLinkedHashMap();

    public static HashMap<String, String> canteenStallBindField = Maps.newLinkedHashMap();


    public static final String SCHOOL_FIELD_SCHOOLCODE="schoolCode";
    public static final String SCHOOL_DESC_SCHOOLCODE="学校标志码";

    public static final String SCHOOL_FIELD_SCHOOLNAME="schoolName";
    public static final String SCHOOL_DESC_SCHOOLNAME="学校名称";

    public static final String SCHOOL_FIELD_SCHOOLTYPE="schoolType";
    public static final String SCHOOL_DESC_SCHOOLTYPE="学校类型";

    public static final String SCHOOL_FIELD_GRADE="grade";
    public static final String SCHOOL_DESC_GRADE="供给分级";

    public static final String SCHOOL_FIELD_AORTYPE = "aorType";
    public static final String SCHOOL_DESC_AORTYPE = "业务类型";

    public static final String SCHOOL_FIELD_AORID="aorId";
    public static final String SCHOOL_DESC_AORID="蜂窝ID";

    public static final String SCHOOL_FIELD_AORNAME="aorName";
    public static final String SCHOOL_DESC_AORNAME="蜂窝名称";

    public static final String SCHOOL_FIELD_CITYID="cityId";
    public static final String SCHOOL_DESC_CITYID="城市ID";

    public static final String SCHOOL_FIELD_CITYNAME="cityName";
    public static final String SCHOOL_DESC_CITYNAME="物理城市";

    public static final String SCHOOL_FIELD_CITYTEAM="cityTeam";
    public static final String SCHOOL_DESC_CITYTEAM="城市团队";

    public static final String SCHOOL_FIELD_AREA="area";
    public static final String SCHOOL_DESC_AREA="区域";

    public static final String SCHOOL_FIELD_SCHOOLKP="schoolKp";
    public static final String SCHOOL_DESC_SCHOOLKP="学校KP";

    public static final String SCHOOL_FIELD_SCHOOLKPNUM="schoolKpNum";
    public static final String SCHOOL_DESC_SCHOOLKPNUM="学校KP联系电话";

    public static final String SCHOOL_FIELD_CONTRACT_NUM="contractNum";
    public static final String SCHOOL_DESC_CONTRACT_NUM="合同编号";

    public static final String SCHOOL_FIELD_WDC_CLUE_ID="wdcClueId";
    public static final String SCHOOL_DESC_WDC_CLUE_ID="线索ID";

    public static final String SCHOOL_FIELD_WM_CO_STATUS="wmCoStatus";
    public static final String SCHOOL_DESC_WM_CO_STATUS="合作状态";

    public static final String SCHOOL_FIELD_RESPONSIBLE_PERSON="responsiblePerson";
    public static final String SCHOOL_DESC_RESPONSIBLE_PERSON="学校责任人";

    public static final String SCHOOL_FIELD_RESPONSIBLE_UID = "responsibleUid";
    public static final String SCHOOL_DESC_RESPONSIBLE_UID = "学校责任人UID";

    public static final String SCHOOL_ADDRESS="schoolAddress";
    public static final String SCHOOL_ADDRESS_DESC="学校地址";

    public static final String SCHOOL_TEA_STU_NUM="teaStuNum";
    public static final String SCHOOL_TEA_STU_NUM_DESC="在校师生人数";

    public static final String SCHOOL_SITE_ID="siteId";
    public static final String SCHOOL_SITE_ID_DESC="配送站点ID";

    public static final String SCHOOL_STUDENT_NUM = "studentNum";
    public static final String SCHOOL_STUDENT_NUM_DESC = "在校学生人数";

    public static final String SCHOOL_OUTSIDE_STUDENT_NUM = "outsideStudentNum";
    public static final String SCHOOL_OUTSIDE_STUDENT_NUM_DESC = "蜂窝内校外学生人数";

    public static final String SCHOOL_EXTENSION_OUTSIDE_STUDENT_USER_NUM = "outsideStudentUserNum";
    public static final String SCHOOL_EXTENSION_OUTSIDE_STUDENT_USER_NUM_DESC = "蜂窝内校外学生用户人数";

    public static final String SCHOOL_DEV_TYPE = "schoolDevType";
    public static final String SCHOOL_DEV_TYPE_DESC = "学校开发方式";

    public static final String SCHOOL_LEVEL = "schoolLevel";
    public static final String SCHOOL_LEVEL_DESC = "学校分级";

    public static final String SCHOOL_COOPERATE_TYPE = "schoolCooperateType";
    public static final String SCHOOL_COOPERATE_TYPE_DESC = "学校合作方式";

    public static final String SCHOOL_LIFECYCLE = "schoolLifecycle";
    public static final String SCHOOL_LIFECYCLE_DESC = "学校生命周期";

    public static final String SCHOOL_EXTENSION_RESIDENT_USER_NUM = "residentUserNum";
    public static final String SCHOOL_EXTENSION_RESIDENT_USER_NUM_DESC = "校内常驻用户数";

    public static final String SCHOOL_EXTENSION_RESIDENT_STUDENT_USER_NUM = "residentStudentUserNum";
    public static final String SCHOOL_EXTENSION_RESIDENT_STUDENT_USER_NUM_DESC = "校内常驻学生用户数";

    public static final String SCHOOL_AGREEMENT_CODE = "agreementCode";
    public static final String SCHOOL_AGREEMENT_CODE_DESC = "合同/授权编号";

    public static final String SCHOOL_AGREEMENT_TYPE = "agreementType";
    public static final String SCHOOL_AGREEMENT_TYPE_DESC = "合同/授权编号类型";

    public static final String SCHOOL_AGREEMENT_TIME_START = "agreementTimeStart";
    public static final String SCHOOL_AGREEMENT_TIME_START_DESC = "合同/授权编号开始时间";

    public static final String SCHOOL_AGREEMENT_TIME_END = "agreementTimeEnd";
    public static final String SCHOOL_AGREEMENT_TIME_END_DESC = "合同/授权编号结束时间";

     public static final String SCHOOL_LIGHT_OFF_INFO = "schoolLightOffInfo";
     public static final String SCHOOL_LIGHT_OFF_INFO_DESC = "学校熄灯信息";

    public static final String SCHOOL_DINING_CABINET = "diningCabinet";
    public static final String SCHOOL_DINING_CABINET_DESC = "是否有取餐柜";

    public static final String SCHOOL_AGGRE_SITE_ID = "aggreSiteId";
    public static final String SCHOOL_AGGRE_SITE_ID_DESC = "学校聚合站点ID";

    public static final String SCHOOL_AGGRE_ORDER_ALLOW_DELIVERY = "aggreOrderAllowDelivery";
    public static final String SCHOOL_AGGRE_ORDER_ALLOW_DELIVERY_DESC = "聚合订单是否允许配送进校";

    // --------------------------------学校熄灯要求START ---------------------------------------------
    public static final String SCHOOL_LIGHT_OFF_REQUIREMENT = "lightOffRequirement";
    public static final String SCHOOL_LIGHT_OFF_REQUIREMENT_DESC = "学校熄灯要求";

    public static final String SCHOOL_LIGHT_OFF_TIME = "lightOffTime";
    public static final String SCHOOL_LIGHT_OFF_TIME_DESC = "学校熄灯时间";

    public static final String SCHOOL_SPRING_LIGHT_OFF_TIME = "springLightOffTime";
    public static final String SCHOOL_SPRING_LIGHT_OFF_TIME_DESC = "春季学校熄灯时间";

    public static final String SCHOOL_SUMMER_LIGHT_OFF_TIME = "summerLightOffTime";
    public static final String SCHOOL_SUMMER_LIGHT_OFF_TIME_DESC = "夏季学校熄灯时间";

    public static final String SCHOOL_AUTUME_LIGHT_OFF_TIME = "autumnLightOffTime";
    public static final String SCHOOL_AUTUME_LIGHT_OFF_TIME_DESC = "秋季学校熄灯时间";

    public static final String SCHOOL_WINTER_LIGHT_OFF_TIME = "winterLightOffTime";
    public static final String SCHOOL_WINTER_LIGHT_OFF_TIME_DESC = "冬季学校熄灯时间";

    // --------------------------------学校熄灯要求END ---------------------------------------------

    public static final String CANTEEN_FIELD_CANTEENNAME="canteenName";
    public static final String CANTEEN_DESC_CANTEENNAME="食堂名称";

    public static final String CANTEEN_FIELD_CANTEENTYPE="canteenType";
    public static final String CANTEEN_DESC_CANTEENTYPE="食堂类型";

    public static final String CANTEEN_FIELD_CANTEENTATRI="canteenAttribute";
    public static final String CANTEEN_DESC_CANTEENTATRI="食堂属性";

    public static final String CANTEEN_FIELD_SCHOOLNAME="schoolName";
    public static final String CANTEEN_DESC_SCHOOLNAME="学校名称";

    public static final String CANTEEN_FIELD_CONTRACTORNAME="contractorName";
    public static final String CANTEEN_DESC_CONTRACTORNAME="承包商名称";

    public static final String CANTEEN_FIELD_GRADE="grade";
    public static final String CANTEEN_DESC_GRADE="供给分级";

    public static final String CANTEEN_FIELD_MANAGER="manager";
    public static final String CANTEEN_DESC_MANAGER="食堂经理";

    public static final String CANTEEN_FIELD_MANAGERPHONE="managerPhone";
    public static final String CANTEEN_DESC_MANAGERPHONE="经理电话";

    public static final String CANTEEN_FIELD_MANAGERPHONE_ENCRYPTION="managerPhoneEncryption";
    public static final String CANTEEN_FIELD_MANAGERPHONE_TOKEN="managerPhoneToken";

    public static final String CANTEEN_FIELD_STALLNUM="stallNum";
    public static final String CANTEEN_DESC_STALLNUM="档口数量";

    public static final String CANTEEN_FIELD_SCHOOLID="schoolId";
    public static final String CANTEEN_DESC_SCHOOLID="学校ID";

    public static final String CANTEEN_CANTEEN_STATUS="canteenStatus";
    public static final String CANTEEN_DESC_CANTEEN_STATUS="合作状态";


    public static final String CANTEEN_FIELD_CONTRACTORID = "contractorId";

    private static final String CANTEEN_FIELD_RP="responsiblePerson";
    private static final String CANTEEN_DESC_RP="食堂责任人";

    private static final String CANTEEN_FIELD_CARD_TYPE = "cardType";
    private static final String CANTEEN_DESC_CARD_TYPE = "证件类型";

    private static final String CANTEEN_FIELD_CARD_NO = "cardNo";
    private static final String CANTEEN_DESC_CARD_NO = "证件号码";

    public static final String CANTEEN_FIELD_CARD_NO_ENCRYPTION = "cardNoEncryption";
    public static final String CANTEEN_FIELD_CARD_NO_TOKEN = "cardNoToken";

    public static final String CANTEEN_FIELD_OFFLINE_BIZ_STALL_NUM = "offlineBizStallNum";
    public static final String CANTEEN_DESC_OFFLINE_BIZ_STALL_NUM = "线下营业档口数量";

    public static final String CANTEEN_FIELD_PRE_ONLINE_STALL_NUM = "preOnlineStallNum";
    public static final String CANTEEN_DESC_PRE_ONLINE_STALL_NUM = "可上线档口数量";

    public static final String CANTEEN_FIELD_CANTEEN_VIDEO = "canteenVideo";
    public static final String CANTEEN_DESC_CANTEEN_VIDEO = "食堂视频";

    public static final String SCHOOLAREA_FIELD_AREA = "area";
    public static final String SCHOOLAREA_DESC_AREA = "学校范围";

    public static final String SCHOOL_AREA_AOI_MODE = "aoiMode";
    public static final String SCHOOL_AREA_AOI_MODE_DESC = "范围通行属性";

    public static final String SCHOOL_AREA_AUTO_SYNC = "autoSync";
    public static final String SCHOOL_AREA_AUTO_SYNC_DESC = "自动同步";

    public static final String SCHOOL_AREA_AOI_NAME = "aoiName";
    public static final String SCHOOL_AREA_AOI_NAME_DESC = "AOI名称";

    public static final String SCHOOL_AREA_AOI_ID = "aoiId";
    public static final String SCHOOL_AREA_AOI_ID_DESC = "AOI ID";

    public static final String SCHOOLAREA_FIELD_AUDITSTATUS = "auditStatus";
    public static final String SCHOOLAREA_DESC_AUDITSTATUS = "审核状态";

    public static final String SCHOOLAREA_FIELD_SOURCETYPE = "sourceType";
    public static final String SCHOOLAREA_DESC_SOURCETYPE = "提交渠道";


    public static final String BUILDING_POI_ID = "buildingPoiId";
    public static final String BUILDING_POI_ID_DESC = "楼宇POI ID";

    public static final String BUILDING_TYPE = "buildingType";
    public static final String BUILDING_TYPE_DESC = "楼宇类型";

    public static final String BUILDING_NAME = "buildingName";
    public static final String BUILDING_NAME_DESC = "楼宇名称";

    public static final String BUILDING_NICKNAME = "buildingNickname";
    public static final String BUILDING_NICKNAME_DESC = "楼宇别名";

    public static final String BUILDING_FLOOR = "buildingFloor";
    public static final String BUILDING_FLOOR_DESC = "楼宇楼层";

    public static final String BUILDING_PERSON_NUM = "buildingPersonNum";
    public static final String BUILDING_PERSON_NUM_DESC = "楼宇人数";

    public static final String BUILDING_LOCATION = "buildingLocation";
    public static final String BUILDING_LOCATION_DESC = "楼宇位置";

    public static final String BUILDING_COORDINATE = "buildingCoordinate";
    public static final String BUILDING_COORDINATE_DESC = "楼宇坐标";

    public static final String BUILDING_AREA = "buildingArea";
    public static final String BUILDING_AREA_DESC = "楼宇范围";

    public static final String BUILDING_ELEVATOR = "buildingElevator";
    public static final String BUILDING_ELEVATOR_DESC = "楼宇电梯";

    public static final String BUILDING_STATUS = "buildingStatus";
    public static final String BUILDING_STATUS_DESC = "楼宇状态";

    public static final String STALL_NUM = "stallNum";
    public static final String STALL_NUM_DESC = "档口数量";

    public static final String OFFLINE_BIZ_STALL_NUM = "offlineBizStallNum";
    public static final String OFFLINE_BIZ_STALL_NUM_DESC = "线下营业档口数量";

    public static final String PRE_ONLINE_STALL_NUM = "preOnlineStallNum";
    public static final String PRE_ONLINE_STALL_NUM_DESC = "可上线档口数量";

    public static final String MANAGER = "manager";
    public static final String MANAGER_DESC = "食堂经理";

    public static final String MANAGER_PHONE = "managerPhone";
    public static final String MANAGER_PHONE_DESC = "经理电话";

    /* ------------------------------ SCHOOL PLATFORM V2 START --------------------------------------*/
    public static final String COOPERATION_PLATFORM_V2 = "cooperationPlatform";

    public static final String COOPERATION_PLATFORM_V2_DESC = "合作平台";

    public static final String PLATFORM_NAME_V2 = "platformName";

    public static final String PLATFORM_NAME_V2_DESC = "平台名称";

    public static final String SCHOOL_IN_POI_ORDER_COUNT = "schoolInPoiOrderCount";

    public static final String SCHOOL_IN_POI_ORDER_COUNT_DESC = "校内商家单量";

    public static final String SCHOOL_OUT_POI_ORDER_COUNT = "schoolOutPoiOrderCount";

    public static final String SCHOOL_OUT_POI_ORDER_COUNT_DESC = "校外商家单量";

    public static final String SCHOOL_IN_ONLINE_POI_COUNT = "schoolInOnlinePoiCount";

    public static final String SCHOOL_IN_ONLINE_POI_COUNT_DESC = "校内在线商家数";

    public static final String SCHOOL_OUT_ONLINE_POI_COUNT = "schoolOutOnlinePoiCount";

    public static final String SCHOOL_OUT_ONLINE_POI_COUNT_DESC = "校外在线商家数";

    public static final String DELIVERY_FEE_TYPE_V2 = "deliveryFeeType";

    public static final String DELIVERY_FEE_TYPE_V2_DESC = "合作平台的配送收费方式";

    public static final String DELIVERY_FEE_TYPE_INFO = "deliveryFeeTypeInfo";

    public static final String DELIVERY_FEE_TYPE_INFO_DESC = "合作平台的配送收费方式其他内容";

    public static final String COMPARE_COOPERATION_PLATFORM = "compareCooperationPlatform";

    public static final String COMPARE_COOPERATION_PLATFORM_DESC = "美团收费和合作平台对比";

    public static final String COMPARE_COOPERATION_PLATFORM_INFO = "compareCooperationPlatformInfo";

    public static final String COMPARE_COOPERATION_PLATFORM_INFO_DESC = "美团收费和合作平台对比无法比较内容";

    public static final String PLATFORM_ALLOW_TO_SCHOOL = "platformAllowToSchool";

    public static final String PLATFORM_ALLOW_TO_SCHOOL_DESC = "合作平台是否可进校";

    public static final String SUPPORT_FOOD_UPSTAIRS_V2 = "supportFoodUpstairs";

    public static final String SUPPORT_FOOD_UPSTAIRS_V2_DESC = "合作平台支持送餐上楼";

    public static final String SUPPORT_FOOD_UPSTAIRS_INFO = "supportFoodUpstairsInfo";

    public static final String SUPPORT_FOOD_UPSTAIRS_INFO_DESC = "合作平台支持送餐上楼其他内容";

    public static final String FOOD_UPSTAIRS_FEE_V2 = "foodUpstairsFee";

    public static final String FOOD_UPSTAIRS_FEE_V2_DESC = "送餐上楼费用";

    public static final String FOOD_UPSTAIRS_REASON_V2 = "foodUpstairsReason";

    public static final String FOOD_UPSTAIRS_REASON_V2_DESC = "送餐上楼原因";

    public static final String FOOD_UPSTAIRS_REASON_INFO = "foodUpstairsReasonInfo";

    public static final String FOOD_UPSTAIRS_REASON_INFO_DESC = "送餐上楼原因其他内容";

    public static final String PLATFORM_ESTABLISH_TIME_V2 = "platformEstablishTime";

    public static final String PLATFORM_ESTABLISH_TIME_V2_DESC = "合作平台与学校达成合作时间";

    public static final String PLATFORM_ESTABLISH_ADVANTAGE_V2 = "platformEstablishAdvantage";

    public static final String PLATFORM_ESTABLISH_ADVANTAGE_V2_DESC = "合作平台的优势";

    public static final String ADVANTAGE_ADDED_SERVICE_INFO = "advantageAddedServiceInfo";

    public static final String ADVANTAGE_ADDED_SERVICE_INFO_DESC = "合作平台的有用户增值服务优势的内容";

    public static final String ADVANTAGE_GOOD_EXPERIENCE_INFO = "advantageGoodExperienceInfo";

    public static final String ADVANTAGE_GOOD_EXPERIENCE_INFO_DESC = "合作平台的配送体验好优势的内容";

    public static final String ADVANTAGE_ATTRACTION_INFO = "advantageAttractionInfo";

    public static final String ADVANTAGE_ATTRACTION_INFO_DESC = "合作平台的有吸引力的C端活动优势的内容";

    public static final String ADVANTAGE_PROPAGANDA_INFO = "advantagePropagandaInfo";

    public static final String ADVANTAGE_PROPAGANDA_INFO_DESC = "合作平台的宣传力度大优势的内容";

    public static final String ADVANTAGE_EXTRA_INFO = "advantageExtraInfo";

    public static final String ADVANTAGE_EXTRA_INFO_DESC = "合作平台的其他优势的内容";

    public static final String SUPPLY_DISTRIBUTION = "supplyDistribution";

    public static final String SUPPLY_DISTRIBUTION_DESC = "供给分布";

    public static final String MONOPOLY_FORM = "monopolyForm";

    public static final String MONOPOLY_FORM_DESC = "垄断形式";

    public static final String EXTRA_INFO_V2 = "extraInfo";

    public static final String EXTRA_INFO_V2_DESC = "其他信息";

    /* ------------------------------ SCHOOL PLATFORM V2 END ----------------------------------------*/

    /* ------------------------------ SCHOOL PLATFORM MARKET V2 START --------------------------------------*/
    public static final String PLATFORM_MARKETING_ACTIVITY = "platformMarketingActivity";

    public static final String PLATFORM_MARKETING_ACTIVITY_DESC = "合作平台营销活动";

    public static final String PLATFORM_MARKETING_ACTIVITY_INFO = "platformMarketingActivityInfo";

    public static final String PLATFORM_MARKETING_ACTIVITY_INFO_DESC = "合作平台营销活动其他内容";

    public static final String ACTIVITY_RULE_DESCRIPTION = "activityRuleDescription";

    public static final String ACTIVITY_RULE_DESCRIPTION_DESC = "活动规则描述";

    public static final String ACTIVITY_PIC = "activityPic";

    public static final String ACTIVITY_PIC_DESC = "活动截图";

    public static final String ACTIVITY_COST_SHARING_TYPE = "activityCostSharingType";

    public static final String ACTIVITY_COST_SHARING_TYPE_DESC = "活动成本分摊类型";

    public static final String ACTIVITY_COST_SHARING_POI_MIN = "activityCostSharingPoiMin";

    public static final String ACTIVITY_COST_SHARING_POI_MIN_DESC = "活动成本分摊范围，商家-最小值";

    public static final String ACTIVITY_COST_SHARING_POI_MAX = "activityCostSharingPoiMax";

    public static final String ACTIVITY_COST_SHARING_POI_MAX_DESC = "活动成本分摊范围，商家-最大值";

    public static final String ACTIVITY_COST_SHARING_PLATFORM_MIN = "activityCostSharingPlatformMin";

    public static final String ACTIVITY_COST_SHARING_PLATFORM_MIN_DESC = "活动成本分摊范围，平台-最小值";

    public static final String ACTIVITY_COST_SHARING_PLATFORM_MAX = "activityCostSharingPlatformMax";

    public static final String ACTIVITY_COST_SHARING_PLATFORM_MAX_DESC = "活动成本分摊范围，平台-最大值";

    /* ------------------------------ SCHOOL PLATFORM MARKET V2 END ----------------------------------------*/

    /* ------------------------------ SCHOOL DELIVERY START ----------------------------------------*/

    public static final String ALLOW_SELF_DELIVERY = "allowSelfDelivery";
    public static final String ALLOW_SELF_DELIVERY_DESC = "自配是否可以配送进校";

    public static final String SCHOOL_IN_DELIVERY_TYPE = "schoolInDeliveryType";
    public static final String SCHOOL_IN_DELIVERY_TYPE_DESC = "校外送校内的配送方式";

    public static final String POI_SELF_DELIVERY_TYPE = "poiSelfDeliveryType";
    public static final String POI_SELF_DELIVERY_TYPE_DESC = "具体使用的自配方式";

    public static final String POI_SELF_DELIVERY_INFO = "poiSelfDeliveryInfo";
    public static final String POI_SELF_DELIVERY_INFO_DESC = "商家自配送其他信息";

    public static final String DELIVERY_SPECIFIC_LOCATION = "deliverySpecificLocation";
    public static final String DELIVERY_SPECIFIC_LOCATION_DESC = "该配送方式可将餐品送到的具体位置";

    public static final String DELIVERY_NOT_GATE_REASON = "deliveryNotGateReason";
    public static final String DELIVERY_NOT_GATE_REASON_DESC = "不能送到校门口原因";

    public static final String DELIVERY_NOT_GATE_INFO = "deliveryNotGateInfo";
    public static final String DELIVERY_NOT_GATE_INFO_DESC = "不能送到校门口其他原因";

    public static final String DELIVERY_NOT_ENTER_REASON = "deliveryNotEnterReason";
    public static final String DELIVERY_NOT_ENTER_REASON_DESC = "不能进校原因";

    public static final String DELIVERY_NOT_ENTER_INFO = "deliveryNotEnterInfo";
    public static final String DELIVERY_NOT_ENTER_INFO_DESC = "不能进校其他原因";

    public static final String DELIVERY_ENTER_REASON = "deliveryEnterReason";
    public static final String DELIVERY_ENTER_REASON_DESC = "可以进校的原因";

    public static final String DELIVERY_ENTER_INFO = "deliveryEnterInfo";
    public static final String DELIVERY_ENTER_INFO_DESC = "可以进校的其他原因";

    public static final String DELIVERY_UPSTAIRS_REASON = "deliveryUpstairsReason";
    public static final String DELIVERY_UPSTAIRS_REASON_DESC = "可送餐上楼原因";

    public static final String DELIVERY_UPSTAIRS_INFO = "deliveryUpstairsInfo";
    public static final String DELIVERY_UPSTAIRS_INFO_DESC = "可送餐上楼其他原因";

    public static final String SCHOOL_ALLOW_DELIVERY_V2 = "schoolAllowDelivery";
    public static final String SCHOOL_ALLOW_DELIVERY_V2_DESC = "校方是否允许配送进校";

    public static final String SCHOOL_ALLOW_DELIVERY_V2_INFO = "schoolAllowDeliveryInfo";
    public static final String SCHOOL_ALLOW_DELIVERY_V2_INFO_DESC = "校方是否允许配送进校其他信息";

    /* ------------------------------ SCHOOL DELIVERY END ----------------------------------------*/

    /* ------------------------------ SCHOOL PERFORMANCE START ----------------------------------------*/

    public static final String SCHOOL_ALLOW_DELIVERY_V3 = "schoolAllowDelivery";
    public static final String SCHOOL_ALLOW_DELIVERY_V3_DESC = "校方是否允许配送进校";

    public static final String SCHOOL_ALLOW_DELIVERY_V3_INFO = "schoolAllowDeliveryInfo";
    public static final String SCHOOL_ALLOW_DELIVERY_V3_INFO_DESC = "校方是否允许配送进校其他信息";

    /* ------------------------------ SCHOOL PERFORMANCE END ----------------------------------------*/

    /* ------------------------------ SCHOOL PERFORMANCE UNIT START ----------------------------------------*/

    public static final String SCHOOL_IN_DELIVERY_TYPE_V3 = "schoolInDeliveryType";
    public static final String SCHOOL_IN_DELIVERY_TYPE_V3_DESC = "校外送校内的配送方式";

    public static final String POI_SELF_DELIVERY_TYPE_V3 = "poiSelfDeliveryType";
    public static final String POI_SELF_DELIVERY_TYPE_V3_DESC = "具体使用的自配方式";

    public static final String POI_SELF_DELIVERY_INFO_V3 = "poiSelfDeliveryInfo";
    public static final String POI_SELF_DELIVERY_INFO_V3_DESC = "商家自配送其他信息";

    public static final String DELIVERY_SPECIFIC_LOCATION_V3 = "deliverySpecificLocation";
    public static final String DELIVERY_SPECIFIC_LOCATION_V3_DESC = "该配送方式可将餐品送到的具体位置";

    public static final String DELIVERY_NOT_GATE_REASON_V3 = "deliveryNotGateReason";
    public static final String DELIVERY_NOT_GATE_REASON_V3_DESC = "不能送到校门口原因";

    public static final String DELIVERY_NOT_GATE_INFO_V3 = "deliveryNotGateInfo";
    public static final String DELIVERY_NOT_GATE_INFO_V3_DESC = "不能送到校门口其他原因";

    public static final String DELIVERY_NOT_ENTER_REASON_V3 = "deliveryNotEnterReason";
    public static final String DELIVERY_NOT_ENTER_REASON_V3_DESC = "不能进校原因";

    public static final String DELIVERY_NOT_ENTER_INFO_V3 = "deliveryNotEnterInfo";
    public static final String DELIVERY_NOT_ENTER_INFO_V3_DESC = "不能进校其他原因";

    public static final String DELIVERY_ENTER_REASON_V3 = "deliveryEnterReason";
    public static final String DELIVERY_ENTER_REASON_V3_DESC = "可以进校的原因";

    public static final String DELIVERY_ENTER_INFO_V3 = "deliveryEnterInfo";
    public static final String DELIVERY_ENTER_INFO_V3_DESC = "可以进校的其他原因";

    public static final String DELIVERY_UPSTAIRS_REASON_V3 = "deliveryUpstairsReason";
    public static final String DELIVERY_UPSTAIRS_REASON_V3_DESC = "可送餐上楼原因";

    public static final String DELIVERY_UPSTAIRS_INFO_V3 = "deliveryUpstairsInfo";
    public static final String DELIVERY_UPSTAIRS_INFO_V3_DESC = "可送餐上楼其他原因";

    /* ------------------------------ SCHOOL PERFORMANCE UNIT END ----------------------------------------*/

    public static final String TIME_FIELD_YEARBEGIN = "yearBegin";
    public static final String TIME_DESC_YEARBEGIN  = "学年开始时间";

    public static final String TIME_FIELD_YEAREND = "yearEnd";
    public static final String TIME_DESC_YEAREND  = "学年结束时间";

    public static final String TIME_FIELD_TERM = "term";
    public static final String TIME_DESC_TERM  = "学期";

    public static final String TIME_FIELD_TERM_BEGIN_SITUATION = "termBeginSituation";
    public static final String TIME_DESC_TERM_BEGIN_SITUATION  = "开学情况";

    public static final String TIME_FIELD_TERM_OPEN_NOT_PIC = "openNotPic";
    public static final String TIME_DESC_TERM_OPEN_NOT_PIC  = "未开学相关图片";

    public static final String TIME_FIELD_TERM_OPEN_NOT_PDF = "openNotPdf";
    public static final String TIME_DESC_TERM_OPEN_NOT_PDF = "未开学相关PDF";

    public static final String TIME_FIELD_TERM_OPEN_NOT_INFO_SOURCE = "openNotInfoSource";
    public static final String TIME_DESC_TERM_OPEN_NOT_INFO_SOURCE = "未开学资料来源";

    public static final String TIME_FIELD_TERM_OPEN_NOT_REMARK = "openNotRemark";
    public static final String TIME_DESC_TERM_OPEN_NOT_REMARK  = "未开学备注";

    public static final String TIME_FIELD_TERM_BEGIN_PLAN = "termBeginPlan";
    public static final String TIME_DESC_TERM_BEGIN_PLAN  = "开学安排";

    public static final String TIME_FIELD_TERM_BEGIN_TIME = "termBeginTime";
    public static final String TIME_DESC_TERM_BEGIN_TIME  = "开学时间";

    public static final String TIME_FIELD_TERM_BEGIN_WEEK = "termBeginWeek";
    public static final String TIME_DESC_TERM_BEGIN_WEEK  = "开学周";

    public static final String TIME_FIELD_TERM_BEGIN_PIC = "termBeginPic";
    public static final String TIME_DESC_TERM_BEGIN_PIC  = "开学相关图片";

    public static final String TIME_FIELD_TERM_BEGIN_PDF = "termBeginPdf";
    public static final String TIME_DESC_TERM_BEGIN_PDF  = "开学相关PDF";

    public static final String TIME_FIELD_FIRST_RETURN_TIME = "firstReturnTime";
    public static final String TIME_DESC_FIRST_RETURN_TIME  = "首次返校时间";

    public static final String TIME_FIELD_TERM_BEGIN_REMARK = "termBeginRemark";
    public static final String TIME_DESC_TERM_BEGIN_REMARK  = "开学备注";

    public static final String TIME_FIELD_TERM_END_PLAN = "termEndPlan";
    public static final String TIME_DESC_TERM_END_PLAN  = "放假安排";

    public static final String TIME_FIELD_TERM_END_TIME = "termEndTime";
    public static final String TIME_DESC_TERM_END_TIME  = "放假时间";

    public static final String TIME_FIELD_TERM_END_WEEK = "termEndWeek";
    public static final String TIME_DESC_TERM_END_WEEK = "放假周";

    public static final String TIME_FIELD_TERM_END_PIC = "termEndPic";
    public static final String TIME_DESC_TERM_END_PIC = "放假相关图片";

    public static final String TIME_FIELD_TERM_END_PDF = "termEndPdf";
    public static final String TIME_DESC_TERM_END_PDF = "放假相关PDF";

    public static final String TIME_FIELD_FIRST_LEAVE_TIME = "firstLeaveTime";
    public static final String TIME_DESC_FIRST_LEAVE_TIME  = "首次离校时间";

    public static final String TIME_FIELD_TERM_END_REMARK = "termEndRemark";
    public static final String TIME_DESC_TERM_END_REMARK  = "放假备注";

    public static final String TIME_FIELD_TERM_BEGIN_INFO_SOURCE = "termBeginInfoSource";

    public static final String TIME_DESC_TERM_BEGIN_INFO_SOURCE = "开学资料来源";

    public static final String TIME_FIELD_TERM_END_INFO_SOURCE = "termEndInfoSource";

    public static final String TIME_DESC_TERM_END_INFO_SOURCE = "放假资料来源";


    /* ------------------------------ SCHOOL DELIVERY ASSIGNMENT START --------------------------------------*/
    public static final String ASSIGNMENT_INITIATOR_UID = "initiatorUid";
    public static final String ASSIGNMENT_INITIATOR_UID_DESC = "交付发起人UID";

    public static final String ASSIGNMENT_INITIATION_TIME = "initiationTime";
    public static final String ASSIGNMENT_INITIATION_TIME_DESC = "交付发起时间";

    public static final String ASSIGNMENT_KEY_DECISION_USER_ID = "keyDecisionUserId";
    public static final String ASSIGNMENT_KEY_DECISION_USER_ID_DESC = "关键决策人ID";

    public static final String ASSIGNMENT_SIGN_PARTNER_USER_ID = "signPartnerUserId";
    public static final String ASSIGNMENT_SIGN_PARTNER_USER_ID_DESC = "签约合伙人ID";

    public static final String ASSIGNMENT_EXTRA_SUPPORT = "extraSupport";
    public static final String ASSIGNMENT_EXTRA_SUPPORT_DESC = "额外支持";

    public static final String DEPARTMENT_INTENTION_DTO_LIST = "departmentIntensionDTOList";
    public static final String DEPARTMENT_INTENTION_DTO_LIST_DESC = "校方部门摸排列表";

    public static final String ASSIGNMENT_PUBLIC_STALL_NUM = "publicStallNum";
    public static final String ASSIGNMENT_PUBLIC_STALL_NUM_DESC = "公海档口数";

    public static final String ASSIGNMENT_DELIVERABLE_STALL_NUM = "deliverableStallNum";
    public static final String ASSIGNMENT_DELIVERABLE_STALL_NUM_DESC = "可交付档口数";

    public static final String ASSIGNMENT_NEED_FOOD_CABINET = "needFoodCabinet";
    public static final String ASSIGNMENT_NEED_FOOD_CABINET_DESC = "是否需要取餐柜";

    public static final String ASSIGNMENT_NEED_FOOD_CABINET_NUM = "needFoodCabinetNum";
    public static final String ASSIGNMENT_NEED_FOOD_CABINET_NUM_DESC = "需求的取餐柜数量";

    public static final String ASSIGNMENT_MATIRIAL_DEMAND = "materialDemand";
    public static final String ASSIGNMENT_MATIRIAL_DEMAND_DESC = "物料需求";

    public static final String ASSIGNMENT_NEED_PRINTER_NUM = "needPrinterNum";
    public static final String ASSIGNMENT_NEED_PRINTER_NUM_DESC = "需要的打印机数量";

    public static final String ASSIGNMENT_NEED_DISPLAY_RACK_NUM = "needDisplayRackNum";
    public static final String ASSIGNMENT_NEED_DISPLAY_RACK_NUM_DESC = "需要的展架数量";

    public static final String ASSIGNMENT_NEED_SUNSHADE_NUM = "needSunshadeNum";
    public static final String ASSIGNMENT_NEED_SUNSHADE_NUM_DESC = "需要的遮阳伞数量";

    public static final String ASSIGNMENT_NEED_TRASH_CAN_NUM = "needTrashCanNum";
    public static final String ASSIGNMENT_NEED_TRASH_CAN_NUM_DESC = "需要的垃圾桶数量";

    public static final String ASSIGNMENT_OTHER_MATERIAL_DEMAND = "otherMaterialDemand";
    public static final String ASSIGNMENT_OTHER_MATERIAL_DEMAND_DESC = "物料需求其他信息";

    public static final String ASSIGNMENT_OTHER_COOPERATION_DEMAND = "otherCooperationDemand";
    public static final String ASSIGNMENT_OTHER_COOPERATION_DEMAND_DESC = "其他合作需求";

    public static final String ASSIGNMENT_CSM_UID = "csmUid";
    public static final String ASSIGNMENT_CSM_UID_DESC = "客户成功经理";

    public static final String ASSIGNMENT_AORM_UID = "aormUid";
    public static final String ASSIGNMENT_AORM_UID_DESC = "学校对应蜂窝负责人";

    public static final String ASSIGNMENT_ACM_UID = "acmUid";
    public static final String ASSIGNMENT_ACM_UID_DESC = "聚合渠道经理";

    /* ------------------------------ SCHOOL DELIVERY ASSIGNMENT END --------------------------------------*/


    /* ------------------------------ SCHOOL DELIVERY GOALSET START --------------------------------------*/
    public static final String GOALSET_CONTACT_USER_IDS = "contactUserIds";
    public static final String GOALSET_CONTACT_USER_IDS_DESC = "普遍客户关系梳理";

    public static final String GOALSET_INSC_ONLINE_SIGN_EXPTIME = "inscOnlineSignExptime";
    public static final String GOALSET_INSC_ONLINE_SIGN_EXPTIME_DESC = "校内站线上签约预计完成时间";

    public static final String GOALSET_INSC_OFFLINE_BUILD_EXPTIME = "inscOfflineBuildExptime";
    public static final String GOALSET_INSC_OFFLINE_BUILD_EXPTIME_DESC = "校内站线下建站预计完成时间";

    public static final String GOALSET_BUILD_OFF_CAMPUS_STATION = "buildOffCampusStation";
    public static final String GOALSET_BUILD_OFF_CAMPUS_STATION_DESC = "是否建立校外校外建站";

    public static final String GOALSET_OUTSC_ONLINE_SIGN_EXPTIME = "outscOnlineSignExptime";
    public static final String GOALSET_OUTSC_ONLINE_SIGN_EXPTIME_DESC = "校外站线上签约预计完成时间";

    public static final String GOALSET_OUTSC_OFFLINE_BUILD_EXPTIME = "outscOfflineBuildExptime";
    public static final String GOALSET_OUTSC_OFFLINE_BUILD_EXPTIME_DESC = "校外站线下建站预计完成时间";

    public static final String GOALSET_ONTIME_RATE_TARGET = "ontimeRateTarget";
    public static final String GOALSET_ONTIME_RATE_TARGET_DESC = "相对准时率目标";

    public static final String GOALSET_ONTIME_RATE_TARGET_EXPTIME = "ontimeRateTargetExptime";
    public static final String GOALSET_ONTIME_RATE_TARGET_EXPTIME_DESC = "相对准时率目标预计完成时间";

    public static final String GOALSET_AVG_DELIVERY_TIME_TARGET = "avgDeliveryTimeTarget";
    public static final String GOALSET_AVG_DELIVERY_TIME_TARGET_DESC = "平均配送时长目标";

    public static final String GOALSET_AVG_DELIVERY_TIME_TARGET_EXPTIME = "avgDeliveryTimeTargetExptime";
    public static final String GOALSET_AVG_DELIVERY_TIME_TARGET_EXPTIME_DESC = "平均配送时长目标预计完成时间";

    public static final String GOALSET_FIRST_STALL_BUILD_EXPTIME = "firstStallBulidExptime";
    public static final String GOALSET_FIRST_STALL_BUILD_EXPTIME_DESC = "首批档口建店计划时间";

    public static final String GOALSET_FIRST_STALL_ONLINE_EXPTIME = "firstStallOnlineExptime";
    public static final String GOALSET_FIRST_STALL_ONLINE_EXPTIME_DESC = "首批档口上线计划时间";

    public static final String GOALSET_FIRST_ONLINE_STALL_NUM = "firstOnlineStallNum";
    public static final String GOALSET_FIRST_ONLINE_STALL_NUM_DESC = "首批上线档口数";

    public static final String GOALSET_ONLINE_PENERATE_TARGET = "onlinePenerateTarget";
    public static final String GOALSET_ONLINE_PENERATE_TARGET_DESC = "在线渗透率目标";

    public static final String GOALSET_ONLINE_PENERATE_TARGET_EXPTIME = "onlinePenerateTargetExptime";
    public static final String GOALSET_ONLINE_PENERATE_TARGET_EXPTIME_DESC = "在线渗透率目标预计完成时间";

    public static final String GOALSET_DAILY_ORDER_TARGET = "dailyOrderTarget";
    public static final String GOALSET_DAILY_ORDER_TARGET_DESC = "日均订单量目标";

    public static final String GOALSET_DAILY_ORDER_TARGET_EXPTIME = "dailyOrderTargetExptime";
    public static final String GOALSET_DAILY_ORDER_TARGET_EXPTIME_DESC = "日均订单量目标预计完成时间";

    public static final String GOALSET_MEAL_PENERATE_TARGET = "mealPenerateTarget";
    public static final String GOALSET_MEAL_PENERATE_TARGET_DESC = "人顿渗透率目标";

    public static final String GOALSET_MEAL_PENERATE_TARGET_EXPTIME = "mealPenerateTargetExptime";
    public static final String GOALSET_MEAL_PENERATE_TARGET_EXPTIME_DESC = "人顿渗透率目标预计完成时间";

    public static final String GOALSET_DAILY_YIELD_TARGET = "dailyYieldTarget";
    public static final String GOALSET_DAILY_YIELD_TARGET_DESC = "日均店单产目标";

    public static final String GOALSET_DAILY_YIELD_TARGET_EXPTIME = "dailyYieldTargetExptime";
    public static final String GOALSET_DAILY_YIELD_TARGET_EXPTIME_DESC = "日均店单产目标预计完成时间";

    /* ------------------------------ SCHOOL DELIVERY GOALSET END --------------------------------------*/


    /* ------------------------------ SCHOOL DELIVERY FOLLOWUP START --------------------------------------*/

    public static final String FOLLOWUP_INSC_ONLINE_SIGN_ACHIEVE = "inscOnlineSignAchieve";
    public static final String FOLLOWUP_INSC_ONLINE_SIGN_ACHIEVE_DESC = "校内站线上签约完成情况";

    public static final String FOLLOWUP_INSC_ONLINE_SIGN_FINTIME = "inscOnlineSignFintime";
    public static final String FOLLOWUP_INSC_ONLINE_SIGN_FINTIME_DESC = "校内站线上签约实际完成时间";

    public static final String FOLLOWUP_INSC_ONLINE_SIGN_STATUS = "inscOnlineSignStatus";
    public static final String FOLLOWUP_INSC_ONLINE_SIGN_STATUS_DESC = "校内站线上签约当前状态";

    public static final String FOLLOWUP_INSC_ONLINE_SIGN_EXCEPTION = "inscOnlineSignException";
    public static final String FOLLOWUP_INSC_ONLINE_SIGN_EXCEPTION_DESC = "校内站线上签约异常情况说明";

    public static final String FOLLOWUP_INSC_OFFLINE_BUILD_ACHIEVE = "inscOfflineBuildAchieve";
    public static final String FOLLOWUP_INSC_OFFLINE_BUILD_ACHIEVE_DESC = "校内站线下建站完成情况";

    public static final String FOLLOWUP_INSC_OFFLINE_BUILD_FINTIME = "inscOfflineBuildFintime";
    public static final String FOLLOWUP_INSC_OFFLINE_BUILD_FINTIME_DESC = "校内站线下建站实际完成时间";

    public static final String FOLLOWUP_INSC_OFFLINE_BUILD_STATUS = "inscOfflineBuildStatus";
    public static final String FOLLOWUP_INSC_OFFLINE_BUILD_STATUS_DESC = "校内站线下建站当前状态";

    public static final String FOLLOWUP_INSC_OFFLINE_BUILD_EXCEPTION = "inscOfflineBuildException";
    public static final String FOLLOWUP_INSC_OFFLINE_BUILD_EXCEPTION_DESC = "校内站线下建站异常情况说明";

    public static final String FOLLOWUP_OUTSC_ONLINE_SIGN_ACHIEVE = "outscOnlineSignAchieve";
    public static final String FOLLOWUP_OUTSC_ONLINE_SIGN_ACHIEVE_DESC = "校外站线上签约完成情况";

    public static final String FOLLOWUP_OUTSC_ONLINE_SIGN_FINTIME = "outscOnlineSignFintime";
    public static final String FOLLOWUP_OUTSC_ONLINE_SIGN_FINTIME_DESC = "校外站线上签约实际完成时间";

    public static final String FOLLOWUP_OUTSC_ONLINE_SIGN_STATUS = "outscOnlineSignStatus";
    public static final String FOLLOWUP_OUTSC_ONLINE_SIGN_STATUS_DESC = "校外站线上签约当前状态";

    public static final String FOLLOWUP_OUTSC_ONLINE_SIGN_EXCEPTION = "outscOnlineSignException";
    public static final String FOLLOWUP_OUTSC_ONLINE_SIGN_EXCEPTION_DESC = "校外站线上签约异常情况说明";

    public static final String FOLLOWUP_OUTSC_OFFLINE_BUILD_ACHIEVE = "outscOfflineBuildAchieve";
    public static final String FOLLOWUP_OUTSC_OFFLINE_BUILD_ACHIEVE_DESC = "校外站线下建站完成情况";

    public static final String FOLLOWUP_OUTSC_OFFLINE_BUILD_FINTIME = "outscOfflineBuildFintime";
    public static final String FOLLOWUP_OUTSC_OFFLINE_BUILD_FINTIME_DESC = "校外站线下建站实际完成时间";

    public static final String FOLLOWUP_OUTSC_OFFLINE_BUILD_STATUS = "outscOfflineBuildStatus";
    public static final String FOLLOWUP_OUTSC_OFFLINE_BUILD_STATUS_DESC = "校外站线下建站当前状态";

    public static final String FOLLOWUP_OUTSC_OFFLINE_BUILD_EXCEPTION = "outscOfflineBuildException";
    public static final String FOLLOWUP_OUTSC_OFFLINE_BUILD_EXCEPTION_DESC = "校外站线下建站异常情况说明";

    public static final String FOLLOWUP_FIRST_STALL_ONLINE_ACHIEVE = "firstStallOnlineAchieve";
    public static final String FOLLOWUP_FIRST_STALL_ONLINE_ACHIEVE_DESC = "首批档口上线完成情况";

    public static final String FOLLOWUP_FIRST_STALL_ONLINE_FINTIME = "firstStallOnlineFintime";
    public static final String FOLLOWUP_FIRST_STALL_ONLINE_FINTIME_DESC = "首批档口上线实际完成时间";

    public static final String FOLLOWUP_FIRST_STALL_ONLINE_STATUS = "firstStallOnlineStatus";
    public static final String FOLLOWUP_FIRST_STALL_ONLINE_STATUS_DESC = "首批档口上线当前状态";

    public static final String FOLLOWUP_FIRST_STALL_ONLINE_EXCEPTION = "firstStallOnlineException";
    public static final String FOLLOWUP_FIRST_STALL_ONLINE_EXCEPTION_DESC = "首批档口上线异常情况说明";

    public static final String FOLLOWUP_ONLINE_OPERATION_PLAN = "onlineOperationPlan";
    public static final String FOLLOWUP_ONLINE_OPERATION_PLAN_DESC = "学校线上运营方案";

    public static final String FOLLOWUP_LIFE_CYCLE = "lifeCycle";
    public static final String FOLLOWUP_LIFE_CYCLE_DESC = "生命周期";

    public static final String FOLLOWUP_ONLINE_PENERATE = "onlinePenerate";
    public static final String FOLLOWUP_ONLINE_PENERATE_DESC = "在线渗透率";

    public static final String FOLLOWUP_ONLINE_PENERATE_ACHIEVE = "onlinePenerateAchieve";
    public static final String FOLLOWUP_ONLINE_PENERATE_ACHIEVE_DESC = "在线渗透率目标完成情况";

    public static final String FOLLOWUP_ONLINE_PENERATE_STATUS = "onlinePenerateStatus";
    public static final String FOLLOWUP_ONLINE_PENERATE_STATUS_DESC = "在线渗透率目标当前状态";

    public static final String FOLLOWUP_ONLINE_PENERATE_EXCEPTION = "onlinePenerateException";
    public static final String FOLLOWUP_ONLINE_PENERATE_EXCEPTION_DESC = "在线渗透率异常情况说明";


    public static final String FOLLOWUP_ONTIME_RATE = "ontimeRate";
    public static final String FOLLOWUP_ONTIME_RATE_DESC = "相对准时率";

    public static final String FOLLOWUP_ONTIME_RATE_ACHIEVE = "ontimeRateAchieve";
    public static final String FOLLOWUP_ONTIME_RATE_ACHIEVE_DESC = "相对准时率目标完成情况";

    public static final String FOLLOWUP_ONTIME_RATE_STATUS = "ontimeRateStatus";
    public static final String FOLLOWUP_ONTIME_RATE_STATUS_DESC = "相对准时率目标当前状态";

    public static final String FOLLOWUP_ONTIME_RATE_EXCEPTION = "ontimeRateException";
    public static final String FOLLOWUP_ONTIME_RATE_EXCEPTION_DESC = "相对准时率异常情况说明";


    public static final String FOLLOWUP_AVG_DELIVERY_TIME = "avgDeliveryTime";
    public static final String FOLLOWUP_AVG_DELIVERY_TIME_DESC = "平均配送时长";

    public static final String FOLLOWUP_AVG_DELIVERY_TIME_ACHIEVE = "avgDeliveryTimeAchieve";
    public static final String FOLLOWUP_AVG_DELIVERY_TIME_ACHIEVE_DESC = "平均配送时长目标完成情况";

    public static final String FOLLOWUP_AVG_DELIVERY_TIME_STATUS = "avgDeliveryTimeStatus";
    public static final String FOLLOWUP_AVG_DELIVERY_TIME_STATUS_DESC = "平均配送时长目标当前状态";

    public static final String FOLLOWUP_AVG_DELIVERY_TIME_EXCEPTION = "avgDeliveryTimeException";
    public static final String FOLLOWUP_AVG_DELIVERY_TIME_EXCEPTION_DESC = "平均配送时长异常情况说明";

    public static final String FOLLOWUP_DAILY_ORDER = "dailyOrder";
    public static final String FOLLOWUP_DAILY_ORDER_DESC = "日均订单量";

    public static final String FOLLOWUP_DAILY_ORDER_ACHIEVE = "dailyOrderAchieve";
    public static final String FOLLOWUP_DAILY_ORDER_ACHIEVE_DESC = "日均订单量目标完成情况";

    public static final String FOLLOWUP_DAILY_ORDER_STATUS = "dailyOrderStatus";
    public static final String FOLLOWUP_DAILY_ORDER_STATUS_DESC = "日均订单量目标当前状态";

    public static final String FOLLOWUP_DAILY_ORDER_EXCEPTION = "dailyOrderException";
    public static final String FOLLOWUP_DAILY_ORDER_EXCEPTION_DESC = "日均订单量异常情况说明";


    public static final String FOLLOWUP_MEAL_PENERATE = "mealPenerate";
    public static final String FOLLOWUP_MEAL_PENERATE_DESC = "人顿渗透率";

    public static final String FOLLOWUP_MEAL_PENERATE_ACHIEVE = "mealPenerateAchieve";
    public static final String FOLLOWUP_MEAL_PENERATE_ACHIEVE_DESC = "人顿渗透率目标完成情况";

    public static final String FOLLOWUP_MEAL_PENERATE_STATUS = "mealPenerateStatus";
    public static final String FOLLOWUP_MEAL_PENERATE_STATUS_DESC = "人顿渗透率目标当前状态";

    public static final String FOLLOWUP_MEAL_PENERATE_EXCEPTION = "mealPenerateException";
    public static final String FOLLOWUP_MEAL_PENERATE_EXCEPTION_DESC = "人顿渗透率异常情况说明";


    public static final String FOLLOWUP_DAILY_YIELD = "dailyYield";
    public static final String FOLLOWUP_DAILY_YIELD_DESC = "日均店单产";

    public static final String FOLLOWUP_DAILY_YIELD_ACHIEVE = "dailyYieldAchieve";
    public static final String FOLLOWUP_DAILY_YIELD_ACHIEVE_DESC = "日均店单产目标完成情况";

    public static final String FOLLOWUP_DAILY_YIELD_STATUS  = "dailyYieldStatus";
    public static final String FOLLOWUP_DAILY_YIELD_STATUS_DESC = "日均店单产目标当前状态";

    public static final String FOLLOWUP_DAILY_YIELD_EXCEPTION = "dailyYieldException";
    public static final String FOLLOWUP_DAILY_YIELD_EXCEPTION_DESC = "日均店单产异常情况说明";

    /* ------------------------------ SCHOOL DELIVERY FOLLOWUP END --------------------------------------*/


    /* ------------------------------ CANTEEN STALL CLUE STRAT --------------------------------------*/

    public static final String CANTEEN_STALL_CLUE_POI_NAME = "cluePoiName";
    public static final String CANTEEN_STALL_CLUE_POI_NAME_DESC = "门店名称";

    public static final String CANTEEN_STALL_CLUE_POI_ADDRESS = "cluePoiAddress";
    public static final String CANTEEN_STALL_CLUE_POI_ADDRESS_DESC = "门店地址";

    public static final String CANTEEN_STALL_CLUE_SECOND_CITY_ID = "clueSecondCityId";
    public static final String CANTEEN_STALL_CLUE_SECOND_CITY_ID_DESC = "外卖城市";

    public static final String CANTEEN_STALL_CLUE_THIRD_CITY_ID = "clueThirdCityId";
    public static final String CANTEEN_STALL_CLUE_THIRD_CITY_ID_DESC = "外卖行政区";

    public static final String CANTEEN_STALL_CLUE_LEAF_CATE_ID = "clueLeafCateId";
    public static final String CANTEEN_STALL_CLUE_LEAF_CATE_ID_DESC = "门店品类";

    public static final String CANTEEN_STALL_CLUE_POI_PHONE = "cluePoiPhoneEncryption";
    public static final String CANTEEN_STALL_CLUE_POI_PHONE_DESC = "门店电话";

    public static final String CANTEEN_STALL_CLUE_POI_COORDINATE = "cluePoiCoordinate";
    public static final String CANTEEN_STALL_CLUE_POI_COORDINATE_DESC = "门店坐标";

    /* ------------------------------ CANTEEN STALL CLUE END ----------------------------------------*/


    /* ------------------------------ CANTEEN STALL BIND STRAT ----------------------------------------*/

    public static final String CANTEEN_STALL_BIND_CANTEEN_PRIMARY_ID = "canteenPrimaryId";
    public static final String CANTEEN_STALL_BIND_CANTEEN_PRIMARY_ID_DESC = "食堂主键ID";

    public static final String CANTEEN_STALL_BIND_WM_POI_ID = "wmPoiId";
    public static final String CANTEEN_STALL_BIND_WM_POI_ID_DESC = "外卖门店ID";

    public static final String CANTEEN_STALL_BIND_WDC_CLUE_ID = "wdcClueId";
    public static final String CANTEEN_STALL_BIND_WDC_CLUE_ID_DESC = "WDC线索ID";

    public static final String CANTEEN_STALL_BIND_CLUE_GENERATE_STATUS = "clueGenerateStatus";
    public static final String CANTEEN_STALL_BIND_CLUE_GENERATE_STATUS_DESC = "线索生成状态";

    public static final String CANTEEN_STALL_BIND_CLUE_GENERATE_FAIL_REASON = "clueGenerateFailReason";
    public static final String CANTEEN_STALL_BIND_CLUE_GENERATE_FAIL_REASON_DESC = "线索生成失败原因";

    public static final String CANTEEN_STALL_BIND_CLUE_BIND_STATUS = "clueBindStatus";
    public static final String CANTEEN_STALL_BIND_CLUE_BIND_STATUS_DESC = "线索绑定状态";

    public static final String CANTEEN_STALL_BIND_CLUE_BIND_FAIL_REASON = "clueBindFailReason";
    public static final String CANTEEN_STALL_BIND_CLUE_BIND_FAIL_REASON_DESC = "线索绑定失败原因";

    public static final String CANTEEN_STALL_BIND_WM_POI_BIND_STATUS = "wmPoiBindStatus";
    public static final String CANTEEN_STALL_BIND_WM_POI_BIND_STATUS_DESC = "外卖门店绑定状态";

    public static final String CANTEEN_STALL_BIND_WM_POI_BIND_FAIL_REASON = "wmPoiBindFailReason";
    public static final String CANTEEN_STALL_BIND_WM_POI_BIND_FAIL_REASON_DESC = "外卖门店绑定失败原因";

    public static final String CANTEEN_STALL_BIND_CLUE_FOLLOW_UP_STATUS = "clueFollowUpStatus";
    public static final String CANTEEN_STALL_BIND_CLUE_FOLLOW_UP_STATUS_DESC = "线索跟进状态";

    public static final String CANTEEN_STALL_BIND_AUDIT_STATUS = "auditStatus";
    public static final String CANTEEN_STALL_BIND_AUDIT_STATUS_DESC = "线索跟进审批状态";

    /* ------------------------------ CANTEEN STALL CLUE END ----------------------------------------*/


    static {
        // 档口绑定任务信息
        canteenStallBindField.put(CANTEEN_STALL_BIND_CANTEEN_PRIMARY_ID, CANTEEN_STALL_BIND_CANTEEN_PRIMARY_ID_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_WM_POI_ID, CANTEEN_STALL_BIND_WM_POI_ID_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_WDC_CLUE_ID, CANTEEN_STALL_BIND_WDC_CLUE_ID_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_CLUE_GENERATE_STATUS, CANTEEN_STALL_BIND_CLUE_GENERATE_STATUS_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_CLUE_GENERATE_FAIL_REASON, CANTEEN_STALL_BIND_CLUE_GENERATE_FAIL_REASON_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_CLUE_BIND_STATUS, CANTEEN_STALL_BIND_CLUE_BIND_STATUS_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_CLUE_BIND_FAIL_REASON, CANTEEN_STALL_BIND_CLUE_BIND_FAIL_REASON_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_WM_POI_BIND_STATUS, CANTEEN_STALL_BIND_WM_POI_BIND_STATUS_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_WM_POI_BIND_FAIL_REASON, CANTEEN_STALL_BIND_WM_POI_BIND_FAIL_REASON_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_CLUE_FOLLOW_UP_STATUS, CANTEEN_STALL_BIND_CLUE_FOLLOW_UP_STATUS_DESC);
        canteenStallBindField.put(CANTEEN_STALL_BIND_AUDIT_STATUS, CANTEEN_STALL_BIND_AUDIT_STATUS_DESC);

        // 档口绑定线索信息
        canteenStallClueField.put(CANTEEN_STALL_CLUE_POI_NAME, CANTEEN_STALL_CLUE_POI_NAME_DESC);
        canteenStallClueField.put(CANTEEN_STALL_CLUE_POI_ADDRESS, CANTEEN_STALL_CLUE_POI_ADDRESS_DESC);
        canteenStallClueField.put(CANTEEN_STALL_CLUE_SECOND_CITY_ID, CANTEEN_STALL_CLUE_SECOND_CITY_ID_DESC);
        canteenStallClueField.put(CANTEEN_STALL_CLUE_THIRD_CITY_ID, CANTEEN_STALL_CLUE_THIRD_CITY_ID_DESC);
        canteenStallClueField.put(CANTEEN_STALL_CLUE_LEAF_CATE_ID, CANTEEN_STALL_CLUE_LEAF_CATE_ID_DESC);
        canteenStallClueField.put(CANTEEN_STALL_CLUE_POI_PHONE, CANTEEN_STALL_CLUE_POI_PHONE_DESC);
        canteenStallClueField.put(CANTEEN_STALL_CLUE_POI_COORDINATE, CANTEEN_STALL_CLUE_POI_COORDINATE_DESC);

        // 学校交付人员指定
        schoolDeliveryAssignmentField.put(ASSIGNMENT_INITIATOR_UID, ASSIGNMENT_INITIATOR_UID_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_INITIATION_TIME, ASSIGNMENT_INITIATION_TIME_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_KEY_DECISION_USER_ID, ASSIGNMENT_KEY_DECISION_USER_ID_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_SIGN_PARTNER_USER_ID, ASSIGNMENT_SIGN_PARTNER_USER_ID_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_EXTRA_SUPPORT, ASSIGNMENT_EXTRA_SUPPORT_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_PUBLIC_STALL_NUM, ASSIGNMENT_PUBLIC_STALL_NUM_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_DELIVERABLE_STALL_NUM, ASSIGNMENT_DELIVERABLE_STALL_NUM_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_NEED_FOOD_CABINET, ASSIGNMENT_NEED_FOOD_CABINET_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_NEED_FOOD_CABINET_NUM, ASSIGNMENT_NEED_FOOD_CABINET_NUM_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_MATIRIAL_DEMAND, ASSIGNMENT_MATIRIAL_DEMAND_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_NEED_PRINTER_NUM, ASSIGNMENT_NEED_PRINTER_NUM_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_NEED_DISPLAY_RACK_NUM, ASSIGNMENT_NEED_DISPLAY_RACK_NUM_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_NEED_SUNSHADE_NUM, ASSIGNMENT_NEED_SUNSHADE_NUM_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_NEED_TRASH_CAN_NUM, ASSIGNMENT_NEED_TRASH_CAN_NUM_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_OTHER_MATERIAL_DEMAND, ASSIGNMENT_OTHER_MATERIAL_DEMAND_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_OTHER_COOPERATION_DEMAND, ASSIGNMENT_OTHER_COOPERATION_DEMAND_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_CSM_UID, ASSIGNMENT_CSM_UID_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_AORM_UID, ASSIGNMENT_AORM_UID_DESC);
        schoolDeliveryAssignmentField.put(ASSIGNMENT_ACM_UID, ASSIGNMENT_ACM_UID_DESC);

        // 学校交付目标制定
        schoolDeliveryGoalSetField.put(GOALSET_CONTACT_USER_IDS, GOALSET_CONTACT_USER_IDS_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_INSC_ONLINE_SIGN_EXPTIME, GOALSET_INSC_ONLINE_SIGN_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_INSC_OFFLINE_BUILD_EXPTIME, GOALSET_INSC_OFFLINE_BUILD_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_BUILD_OFF_CAMPUS_STATION, GOALSET_BUILD_OFF_CAMPUS_STATION_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_OUTSC_ONLINE_SIGN_EXPTIME, GOALSET_OUTSC_ONLINE_SIGN_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_OUTSC_OFFLINE_BUILD_EXPTIME, GOALSET_OUTSC_OFFLINE_BUILD_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_ONTIME_RATE_TARGET, GOALSET_ONTIME_RATE_TARGET_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_ONTIME_RATE_TARGET_EXPTIME, GOALSET_ONTIME_RATE_TARGET_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_AVG_DELIVERY_TIME_TARGET, GOALSET_AVG_DELIVERY_TIME_TARGET_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_AVG_DELIVERY_TIME_TARGET_EXPTIME, GOALSET_AVG_DELIVERY_TIME_TARGET_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_FIRST_STALL_BUILD_EXPTIME, GOALSET_FIRST_STALL_BUILD_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_FIRST_STALL_ONLINE_EXPTIME, GOALSET_FIRST_STALL_ONLINE_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_FIRST_ONLINE_STALL_NUM, GOALSET_FIRST_ONLINE_STALL_NUM_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_ONLINE_PENERATE_TARGET, GOALSET_ONLINE_PENERATE_TARGET_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_ONLINE_PENERATE_TARGET_EXPTIME, GOALSET_ONLINE_PENERATE_TARGET_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_DAILY_ORDER_TARGET, GOALSET_DAILY_ORDER_TARGET_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_DAILY_ORDER_TARGET_EXPTIME, GOALSET_DAILY_ORDER_TARGET_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_MEAL_PENERATE_TARGET, GOALSET_MEAL_PENERATE_TARGET_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_MEAL_PENERATE_TARGET_EXPTIME, GOALSET_MEAL_PENERATE_TARGET_EXPTIME_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_DAILY_YIELD_TARGET, GOALSET_DAILY_YIELD_TARGET_DESC);
        schoolDeliveryGoalSetField.put(GOALSET_DAILY_YIELD_TARGET_EXPTIME, GOALSET_DAILY_YIELD_TARGET_EXPTIME_DESC);

        // 学校交付跟进
        schoolDeliveryFollowUpField.put(ASSIGNMENT_INITIATOR_UID, ASSIGNMENT_INITIATOR_UID_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_INSC_ONLINE_SIGN_ACHIEVE, FOLLOWUP_INSC_ONLINE_SIGN_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_INSC_ONLINE_SIGN_FINTIME, FOLLOWUP_INSC_ONLINE_SIGN_FINTIME_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_INSC_ONLINE_SIGN_STATUS, FOLLOWUP_INSC_ONLINE_SIGN_STATUS_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_INSC_ONLINE_SIGN_EXCEPTION, FOLLOWUP_INSC_ONLINE_SIGN_EXCEPTION_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_INSC_OFFLINE_BUILD_ACHIEVE, FOLLOWUP_INSC_OFFLINE_BUILD_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_INSC_OFFLINE_BUILD_FINTIME, FOLLOWUP_INSC_OFFLINE_BUILD_FINTIME_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_INSC_OFFLINE_BUILD_EXCEPTION, FOLLOWUP_INSC_OFFLINE_BUILD_EXCEPTION_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_INSC_OFFLINE_BUILD_STATUS, FOLLOWUP_INSC_OFFLINE_BUILD_STATUS_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_OUTSC_ONLINE_SIGN_ACHIEVE, FOLLOWUP_OUTSC_ONLINE_SIGN_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_OUTSC_ONLINE_SIGN_FINTIME, FOLLOWUP_OUTSC_ONLINE_SIGN_FINTIME_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_OUTSC_ONLINE_SIGN_EXCEPTION, FOLLOWUP_OUTSC_ONLINE_SIGN_EXCEPTION_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_OUTSC_ONLINE_SIGN_STATUS, FOLLOWUP_OUTSC_ONLINE_SIGN_STATUS_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_OUTSC_OFFLINE_BUILD_ACHIEVE, FOLLOWUP_OUTSC_OFFLINE_BUILD_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_OUTSC_OFFLINE_BUILD_FINTIME, FOLLOWUP_OUTSC_OFFLINE_BUILD_FINTIME_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_OUTSC_OFFLINE_BUILD_EXCEPTION, FOLLOWUP_OUTSC_OFFLINE_BUILD_EXCEPTION_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_OUTSC_OFFLINE_BUILD_STATUS, FOLLOWUP_OUTSC_OFFLINE_BUILD_STATUS_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_FIRST_STALL_ONLINE_ACHIEVE, FOLLOWUP_FIRST_STALL_ONLINE_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_FIRST_STALL_ONLINE_FINTIME, FOLLOWUP_FIRST_STALL_ONLINE_FINTIME_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_FIRST_STALL_ONLINE_EXCEPTION, FOLLOWUP_FIRST_STALL_ONLINE_EXCEPTION_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_FIRST_STALL_ONLINE_STATUS, FOLLOWUP_FIRST_STALL_ONLINE_STATUS_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_ONLINE_OPERATION_PLAN, FOLLOWUP_ONLINE_OPERATION_PLAN_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_LIFE_CYCLE, FOLLOWUP_LIFE_CYCLE_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_ONLINE_PENERATE, FOLLOWUP_ONLINE_PENERATE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_ONLINE_PENERATE_ACHIEVE, FOLLOWUP_ONLINE_PENERATE_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_ONLINE_PENERATE_STATUS, FOLLOWUP_ONLINE_PENERATE_STATUS_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_ONLINE_PENERATE_EXCEPTION, FOLLOWUP_ONLINE_PENERATE_EXCEPTION_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_ONTIME_RATE, FOLLOWUP_ONTIME_RATE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_ONTIME_RATE_ACHIEVE, FOLLOWUP_ONTIME_RATE_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_ONTIME_RATE_STATUS, FOLLOWUP_ONTIME_RATE_STATUS_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_ONTIME_RATE_EXCEPTION, FOLLOWUP_ONTIME_RATE_EXCEPTION_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_AVG_DELIVERY_TIME, FOLLOWUP_AVG_DELIVERY_TIME_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_AVG_DELIVERY_TIME_ACHIEVE, FOLLOWUP_AVG_DELIVERY_TIME_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_AVG_DELIVERY_TIME_STATUS, FOLLOWUP_AVG_DELIVERY_TIME_STATUS_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_AVG_DELIVERY_TIME_EXCEPTION, FOLLOWUP_AVG_DELIVERY_TIME_EXCEPTION_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_DAILY_ORDER, FOLLOWUP_DAILY_ORDER_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_DAILY_ORDER_ACHIEVE, FOLLOWUP_DAILY_ORDER_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_DAILY_ORDER_STATUS, FOLLOWUP_DAILY_ORDER_STATUS_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_DAILY_ORDER_EXCEPTION, FOLLOWUP_DAILY_ORDER_EXCEPTION_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_MEAL_PENERATE, FOLLOWUP_MEAL_PENERATE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_MEAL_PENERATE_ACHIEVE, FOLLOWUP_MEAL_PENERATE_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_MEAL_PENERATE_STATUS, FOLLOWUP_MEAL_PENERATE_STATUS_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_MEAL_PENERATE_EXCEPTION, FOLLOWUP_MEAL_PENERATE_EXCEPTION_DESC);

        schoolDeliveryFollowUpField.put(FOLLOWUP_DAILY_YIELD, FOLLOWUP_DAILY_YIELD_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_DAILY_YIELD_ACHIEVE, FOLLOWUP_DAILY_YIELD_ACHIEVE_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_DAILY_YIELD_STATUS, FOLLOWUP_DAILY_YIELD_STATUS_DESC);
        schoolDeliveryFollowUpField.put(FOLLOWUP_DAILY_YIELD_EXCEPTION, FOLLOWUP_DAILY_YIELD_EXCEPTION_DESC);

        // 学校时间信息
        schoolTimeInfoField.put(TIME_FIELD_YEARBEGIN, TIME_DESC_YEARBEGIN);
        schoolTimeInfoField.put(TIME_FIELD_YEAREND, TIME_DESC_YEAREND);
        schoolTimeInfoField.put(TIME_FIELD_TERM, TIME_DESC_TERM);
        schoolTimeInfoField.put(TIME_FIELD_TERM_BEGIN_SITUATION, TIME_DESC_TERM_BEGIN_SITUATION);
        schoolTimeInfoField.put(TIME_FIELD_TERM_OPEN_NOT_PIC, TIME_DESC_TERM_OPEN_NOT_PIC);
        schoolTimeInfoField.put(TIME_FIELD_TERM_OPEN_NOT_PDF, TIME_DESC_TERM_OPEN_NOT_PDF);
        schoolTimeInfoField.put(TIME_FIELD_TERM_OPEN_NOT_INFO_SOURCE, TIME_DESC_TERM_OPEN_NOT_INFO_SOURCE);
        schoolTimeInfoField.put(TIME_FIELD_TERM_OPEN_NOT_REMARK, TIME_DESC_TERM_OPEN_NOT_REMARK);

        // 开学信息
        schoolTimeTermBeginField.put(TIME_FIELD_TERM_BEGIN_PLAN, TIME_DESC_TERM_BEGIN_PLAN);
        schoolTimeTermBeginField.put(TIME_FIELD_TERM_BEGIN_TIME, TIME_DESC_TERM_BEGIN_TIME);
        schoolTimeTermBeginField.put(TIME_FIELD_TERM_BEGIN_WEEK, TIME_DESC_TERM_BEGIN_WEEK);
        schoolTimeTermBeginField.put(TIME_FIELD_TERM_BEGIN_PIC, TIME_DESC_TERM_BEGIN_PIC);
        schoolTimeTermBeginField.put(TIME_FIELD_TERM_BEGIN_PDF, TIME_DESC_TERM_BEGIN_PDF);
        schoolTimeTermBeginField.put(TIME_FIELD_FIRST_RETURN_TIME, TIME_DESC_FIRST_RETURN_TIME);
        schoolTimeTermBeginField.put(TIME_FIELD_TERM_BEGIN_REMARK, TIME_DESC_TERM_BEGIN_REMARK);
        schoolTimeTermBeginField.put(TIME_FIELD_TERM_BEGIN_INFO_SOURCE, TIME_DESC_TERM_BEGIN_INFO_SOURCE);

        // 放假信息
        schoolTimeTermEndField.put(TIME_FIELD_TERM_END_PLAN, TIME_DESC_TERM_END_PLAN);
        schoolTimeTermEndField.put(TIME_FIELD_TERM_END_TIME, TIME_DESC_TERM_END_TIME);
        schoolTimeTermEndField.put(TIME_FIELD_TERM_END_WEEK, TIME_DESC_TERM_END_WEEK);
        schoolTimeTermEndField.put(TIME_FIELD_TERM_END_PIC, TIME_DESC_TERM_END_PIC);
        schoolTimeTermEndField.put(TIME_FIELD_TERM_END_PDF, TIME_DESC_TERM_END_PDF);
        schoolTimeTermEndField.put(TIME_FIELD_FIRST_LEAVE_TIME, TIME_DESC_FIRST_LEAVE_TIME);
        schoolTimeTermEndField.put(TIME_FIELD_TERM_END_REMARK, TIME_DESC_TERM_END_REMARK);
        schoolTimeTermEndField.put(TIME_FIELD_TERM_END_INFO_SOURCE, TIME_DESC_TERM_END_INFO_SOURCE);

        schoolAreaField.put(SCHOOLAREA_FIELD_AREA, SCHOOLAREA_DESC_AREA);
        schoolAreaField.put(SCHOOL_AREA_AOI_MODE, SCHOOL_AREA_AOI_MODE_DESC);
        schoolAreaField.put(SCHOOL_AREA_AOI_ID, SCHOOL_AREA_AOI_ID_DESC);
        schoolAreaField.put(SCHOOL_AREA_AOI_NAME, SCHOOL_AREA_AOI_NAME_DESC);
        schoolAreaField.put(SCHOOL_AREA_AUTO_SYNC, SCHOOL_AREA_AUTO_SYNC_DESC);

        canteenField.put(CANTEEN_FIELD_CANTEENNAME, CANTEEN_DESC_CANTEENNAME);
        canteenField.put(CANTEEN_FIELD_CANTEENTYPE, CANTEEN_DESC_CANTEENTYPE);
        canteenField.put(CANTEEN_FIELD_CANTEENTATRI, CANTEEN_DESC_CANTEENTATRI);
        canteenField.put(CANTEEN_FIELD_GRADE, CANTEEN_DESC_GRADE);
        canteenField.put(CANTEEN_FIELD_CONTRACTORNAME, CANTEEN_DESC_CONTRACTORNAME);
        canteenField.put(CANTEEN_FIELD_SCHOOLNAME, CANTEEN_DESC_SCHOOLNAME);
        canteenField.put(CANTEEN_FIELD_MANAGER, CANTEEN_DESC_MANAGER);
        canteenField.put(CANTEEN_FIELD_MANAGERPHONE, CANTEEN_DESC_MANAGERPHONE);
        canteenField.put(CANTEEN_FIELD_STALLNUM, CANTEEN_DESC_STALLNUM);
        canteenField.put(CANTEEN_FIELD_SCHOOLID, CANTEEN_DESC_SCHOOLID);
        canteenField.put(CANTEEN_FIELD_RP, CANTEEN_DESC_RP);
        canteenField.put(CANTEEN_FIELD_CARD_TYPE, CANTEEN_DESC_CARD_TYPE);
        canteenField.put(CANTEEN_FIELD_CARD_NO, CANTEEN_DESC_CARD_NO);
        canteenField.put(CANTEEN_CANTEEN_STATUS, CANTEEN_DESC_CANTEEN_STATUS);
        canteenField.put(CANTEEN_FIELD_OFFLINE_BIZ_STALL_NUM, CANTEEN_DESC_OFFLINE_BIZ_STALL_NUM);
        canteenField.put(CANTEEN_FIELD_PRE_ONLINE_STALL_NUM, CANTEEN_DESC_PRE_ONLINE_STALL_NUM);
        canteenField.put(CANTEEN_FIELD_CANTEEN_VIDEO, CANTEEN_DESC_CANTEEN_VIDEO);
        canteenField.put(STALL_NUM, STALL_NUM_DESC);
        canteenField.put(OFFLINE_BIZ_STALL_NUM, OFFLINE_BIZ_STALL_NUM_DESC);
        canteenField.put(PRE_ONLINE_STALL_NUM, PRE_ONLINE_STALL_NUM_DESC);
        canteenField.put(MANAGER, MANAGER_DESC);
        canteenField.put(MANAGER_PHONE, MANAGER_PHONE_DESC);


        schoolField.put(SCHOOL_FIELD_SCHOOLCODE, SCHOOL_DESC_SCHOOLCODE);
        schoolField.put(SCHOOL_FIELD_SCHOOLNAME, SCHOOL_DESC_SCHOOLNAME);
        schoolField.put(SCHOOL_FIELD_SCHOOLTYPE, SCHOOL_DESC_SCHOOLTYPE);
        schoolField.put(SCHOOL_FIELD_GRADE, SCHOOL_DESC_GRADE);
        schoolField.put(SCHOOL_FIELD_SCHOOLKP, SCHOOL_DESC_SCHOOLKP);
        schoolField.put(SCHOOL_FIELD_SCHOOLKPNUM, SCHOOL_DESC_SCHOOLKPNUM);
        schoolField.put(SCHOOL_FIELD_AORTYPE, SCHOOL_DESC_AORTYPE);
        schoolField.put(SCHOOL_FIELD_AORID, SCHOOL_DESC_AORID);
        schoolField.put(SCHOOL_FIELD_AORNAME, SCHOOL_DESC_AORNAME);
        schoolField.put(SCHOOL_FIELD_CITYNAME, SCHOOL_DESC_CITYNAME);
        schoolField.put(SCHOOL_FIELD_CONTRACT_NUM,SCHOOL_DESC_CONTRACT_NUM);
        schoolField.put(SCHOOL_FIELD_WDC_CLUE_ID, SCHOOL_DESC_WDC_CLUE_ID);
        schoolField.put(SCHOOL_FIELD_WM_CO_STATUS, SCHOOL_DESC_WM_CO_STATUS);
        schoolField.put(SCHOOL_FIELD_RESPONSIBLE_PERSON, SCHOOL_DESC_RESPONSIBLE_PERSON);
        schoolField.put(SCHOOL_FIELD_RESPONSIBLE_UID, SCHOOL_DESC_RESPONSIBLE_UID);
        schoolField.put(SCHOOL_ADDRESS, SCHOOL_ADDRESS_DESC);
        schoolField.put(SCHOOL_TEA_STU_NUM, SCHOOL_TEA_STU_NUM_DESC);
        schoolField.put(SCHOOL_SITE_ID, SCHOOL_SITE_ID_DESC);
        schoolField.put(SCHOOL_STUDENT_NUM, SCHOOL_STUDENT_NUM_DESC);
        schoolField.put(SCHOOL_OUTSIDE_STUDENT_NUM, SCHOOL_OUTSIDE_STUDENT_NUM_DESC);
        schoolField.put(SCHOOL_DEV_TYPE, SCHOOL_DEV_TYPE_DESC);
        schoolField.put(SCHOOL_LEVEL, SCHOOL_LEVEL_DESC);
        schoolField.put(SCHOOL_COOPERATE_TYPE, SCHOOL_COOPERATE_TYPE_DESC);
        schoolField.put(SCHOOL_LIFECYCLE, SCHOOL_LIFECYCLE_DESC);
        schoolField.put(SCHOOL_AGREEMENT_CODE, SCHOOL_AGREEMENT_CODE_DESC);
        schoolField.put(SCHOOL_AGREEMENT_TYPE, SCHOOL_AGREEMENT_TYPE_DESC);
        schoolField.put(SCHOOL_AGREEMENT_TIME_START, SCHOOL_AGREEMENT_TIME_START_DESC);
        schoolField.put(SCHOOL_AGREEMENT_TIME_END, SCHOOL_AGREEMENT_TIME_END_DESC);
        schoolField.put(SCHOOL_LIGHT_OFF_INFO, SCHOOL_LIGHT_OFF_INFO_DESC);
        schoolField.put(SCHOOL_DINING_CABINET, SCHOOL_DINING_CABINET_DESC);
        schoolField.put(SCHOOL_AGGRE_SITE_ID, SCHOOL_AGGRE_SITE_ID_DESC);
        schoolField.put(SCHOOL_AGGRE_ORDER_ALLOW_DELIVERY, SCHOOL_AGGRE_ORDER_ALLOW_DELIVERY_DESC);

        schoolLightOffField.put(SCHOOL_LIGHT_OFF_REQUIREMENT, SCHOOL_LIGHT_OFF_REQUIREMENT_DESC);
        schoolLightOffField.put(SCHOOL_LIGHT_OFF_TIME, SCHOOL_LIGHT_OFF_TIME_DESC);
        schoolLightOffField.put(SCHOOL_SPRING_LIGHT_OFF_TIME, SCHOOL_SPRING_LIGHT_OFF_TIME_DESC);
        schoolLightOffField.put(SCHOOL_SUMMER_LIGHT_OFF_TIME, SCHOOL_SUMMER_LIGHT_OFF_TIME_DESC);
        schoolLightOffField.put(SCHOOL_AUTUME_LIGHT_OFF_TIME, SCHOOL_AUTUME_LIGHT_OFF_TIME_DESC);
        schoolLightOffField.put(SCHOOL_WINTER_LIGHT_OFF_TIME, SCHOOL_WINTER_LIGHT_OFF_TIME_DESC);

        schoolExtensionField.put(SCHOOL_EXTENSION_RESIDENT_USER_NUM, SCHOOL_EXTENSION_RESIDENT_USER_NUM_DESC);
        schoolExtensionField.put(SCHOOL_EXTENSION_OUTSIDE_STUDENT_USER_NUM, SCHOOL_EXTENSION_OUTSIDE_STUDENT_USER_NUM_DESC);
        schoolExtensionField.put(SCHOOL_EXTENSION_RESIDENT_STUDENT_USER_NUM, SCHOOL_EXTENSION_RESIDENT_STUDENT_USER_NUM_DESC);

        schoolAorMsgField.put(SCHOOL_FIELD_AORNAME, SCHOOL_DESC_AORNAME);
        schoolAorMsgField.put(SCHOOL_FIELD_CITYNAME, SCHOOL_DESC_CITYNAME);
        schoolAorMsgField.put(SCHOOL_FIELD_CITYID,SCHOOL_DESC_CITYID);
        schoolAorMsgField.put(SCHOOL_FIELD_AREA, SCHOOL_DESC_AREA);
        schoolAorMsgField.put(SCHOOL_FIELD_CITYTEAM,SCHOOL_DESC_CITYTEAM);

        schoolBuildingField.put(BUILDING_POI_ID, BUILDING_POI_ID_DESC);
        schoolBuildingField.put(BUILDING_TYPE, BUILDING_TYPE_DESC);
        schoolBuildingField.put(BUILDING_NAME, BUILDING_NAME_DESC);
        schoolBuildingField.put(BUILDING_NICKNAME, BUILDING_NICKNAME_DESC);
        schoolBuildingField.put(BUILDING_FLOOR, BUILDING_FLOOR_DESC);
        schoolBuildingField.put(BUILDING_PERSON_NUM, BUILDING_PERSON_NUM_DESC);
        schoolBuildingField.put(BUILDING_LOCATION, BUILDING_LOCATION_DESC);
        schoolBuildingField.put(BUILDING_COORDINATE, BUILDING_COORDINATE_DESC);
        schoolBuildingField.put(BUILDING_AREA, BUILDING_AREA_DESC);
        schoolBuildingField.put(BUILDING_ELEVATOR, BUILDING_ELEVATOR_DESC);
        schoolBuildingField.put(BUILDING_STATUS, BUILDING_STATUS_DESC);

        schoolCoPlatformField.put(COOPERATION_PLATFORM_V2, COOPERATION_PLATFORM_V2_DESC);
        schoolCoPlatformField.put(PLATFORM_NAME_V2, PLATFORM_NAME_V2_DESC);
        schoolCoPlatformField.put(SCHOOL_IN_POI_ORDER_COUNT, SCHOOL_IN_POI_ORDER_COUNT_DESC);
        schoolCoPlatformField.put(SCHOOL_OUT_POI_ORDER_COUNT, SCHOOL_OUT_POI_ORDER_COUNT_DESC);
        schoolCoPlatformField.put(SCHOOL_IN_ONLINE_POI_COUNT, SCHOOL_IN_ONLINE_POI_COUNT_DESC);
        schoolCoPlatformField.put(SCHOOL_OUT_ONLINE_POI_COUNT, SCHOOL_OUT_ONLINE_POI_COUNT_DESC);
        schoolCoPlatformField.put(DELIVERY_FEE_TYPE_V2, DELIVERY_FEE_TYPE_V2_DESC);
        schoolCoPlatformField.put(DELIVERY_FEE_TYPE_INFO, DELIVERY_FEE_TYPE_INFO_DESC);
        schoolCoPlatformField.put(COMPARE_COOPERATION_PLATFORM, COMPARE_COOPERATION_PLATFORM_DESC);
        schoolCoPlatformField.put(COMPARE_COOPERATION_PLATFORM_INFO, COMPARE_COOPERATION_PLATFORM_INFO_DESC);
        schoolCoPlatformField.put(PLATFORM_ALLOW_TO_SCHOOL, PLATFORM_ALLOW_TO_SCHOOL_DESC);
        schoolCoPlatformField.put(SUPPORT_FOOD_UPSTAIRS_V2, SUPPORT_FOOD_UPSTAIRS_V2_DESC);
        schoolCoPlatformField.put(SUPPORT_FOOD_UPSTAIRS_INFO, SUPPORT_FOOD_UPSTAIRS_INFO_DESC);
        schoolCoPlatformField.put(FOOD_UPSTAIRS_FEE_V2, FOOD_UPSTAIRS_FEE_V2_DESC);
        schoolCoPlatformField.put(FOOD_UPSTAIRS_REASON_V2, FOOD_UPSTAIRS_REASON_V2_DESC);
        schoolCoPlatformField.put(FOOD_UPSTAIRS_REASON_INFO, FOOD_UPSTAIRS_REASON_INFO_DESC);
        schoolCoPlatformField.put(PLATFORM_ESTABLISH_TIME_V2, PLATFORM_ESTABLISH_TIME_V2_DESC);
        schoolCoPlatformField.put(PLATFORM_ESTABLISH_ADVANTAGE_V2, PLATFORM_ESTABLISH_ADVANTAGE_V2_DESC);
        schoolCoPlatformField.put(ADVANTAGE_ADDED_SERVICE_INFO, ADVANTAGE_ADDED_SERVICE_INFO_DESC);
        schoolCoPlatformField.put(ADVANTAGE_GOOD_EXPERIENCE_INFO, ADVANTAGE_GOOD_EXPERIENCE_INFO_DESC);
        schoolCoPlatformField.put(ADVANTAGE_ATTRACTION_INFO, ADVANTAGE_ATTRACTION_INFO_DESC);
        schoolCoPlatformField.put(ADVANTAGE_PROPAGANDA_INFO, ADVANTAGE_PROPAGANDA_INFO_DESC);
        schoolCoPlatformField.put(ADVANTAGE_EXTRA_INFO, ADVANTAGE_EXTRA_INFO_DESC);
        schoolCoPlatformField.put(SUPPLY_DISTRIBUTION, SUPPLY_DISTRIBUTION_DESC);
        schoolCoPlatformField.put(MONOPOLY_FORM, MONOPOLY_FORM_DESC);
        schoolCoPlatformField.put(EXTRA_INFO_V2, EXTRA_INFO_V2_DESC);

        schoolCoPlatformMarketField.put(PLATFORM_MARKETING_ACTIVITY, PLATFORM_MARKETING_ACTIVITY_DESC);
        schoolCoPlatformMarketField.put(PLATFORM_MARKETING_ACTIVITY_INFO, PLATFORM_MARKETING_ACTIVITY_INFO_DESC);
        schoolCoPlatformMarketField.put(ACTIVITY_RULE_DESCRIPTION, ACTIVITY_RULE_DESCRIPTION_DESC);
        schoolCoPlatformMarketField.put(ACTIVITY_PIC, ACTIVITY_PIC_DESC);
        schoolCoPlatformMarketField.put(ACTIVITY_COST_SHARING_TYPE, ACTIVITY_COST_SHARING_TYPE_DESC);
        schoolCoPlatformMarketField.put(ACTIVITY_COST_SHARING_POI_MIN, ACTIVITY_COST_SHARING_POI_MIN_DESC);
        schoolCoPlatformMarketField.put(ACTIVITY_COST_SHARING_POI_MAX, ACTIVITY_COST_SHARING_POI_MAX_DESC);
        schoolCoPlatformMarketField.put(ACTIVITY_COST_SHARING_PLATFORM_MIN, ACTIVITY_COST_SHARING_PLATFORM_MIN_DESC);
        schoolCoPlatformMarketField.put(ACTIVITY_COST_SHARING_PLATFORM_MAX, ACTIVITY_COST_SHARING_PLATFORM_MAX_DESC);

        schoolDeliveryFiled.put(ALLOW_SELF_DELIVERY, ALLOW_SELF_DELIVERY_DESC);
        schoolDeliveryFiled.put(SCHOOL_IN_DELIVERY_TYPE, SCHOOL_IN_DELIVERY_TYPE_DESC);
        schoolDeliveryFiled.put(POI_SELF_DELIVERY_TYPE, POI_SELF_DELIVERY_INFO_DESC);
        schoolDeliveryFiled.put(POI_SELF_DELIVERY_INFO, POI_SELF_DELIVERY_INFO_DESC);
        schoolDeliveryFiled.put(DELIVERY_SPECIFIC_LOCATION, DELIVERY_SPECIFIC_LOCATION_DESC);
        schoolDeliveryFiled.put(DELIVERY_NOT_GATE_REASON, DELIVERY_NOT_GATE_REASON_DESC);
        schoolDeliveryFiled.put(DELIVERY_NOT_GATE_INFO, DELIVERY_NOT_GATE_INFO_DESC);
        schoolDeliveryFiled.put(DELIVERY_NOT_ENTER_REASON, DELIVERY_NOT_ENTER_REASON_DESC);
        schoolDeliveryFiled.put(DELIVERY_ENTER_REASON, DELIVERY_ENTER_REASON_DESC);
        schoolDeliveryFiled.put(DELIVERY_NOT_ENTER_INFO, DELIVERY_NOT_ENTER_INFO_DESC);
        schoolDeliveryFiled.put(DELIVERY_ENTER_INFO, DELIVERY_ENTER_INFO_DESC);
        schoolDeliveryFiled.put(DELIVERY_UPSTAIRS_REASON, DELIVERY_UPSTAIRS_REASON_DESC);
        schoolDeliveryFiled.put(DELIVERY_UPSTAIRS_INFO, DELIVERY_UPSTAIRS_INFO_DESC);
        schoolDeliveryFiled.put(SCHOOL_ALLOW_DELIVERY_V2, SCHOOL_ALLOW_DELIVERY_V2_DESC);
        schoolDeliveryFiled.put(SCHOOL_ALLOW_DELIVERY_V2_INFO, SCHOOL_ALLOW_DELIVERY_V2_INFO_DESC);

        schoolPerformanceFiled.put(SCHOOL_ALLOW_DELIVERY_V3, SCHOOL_ALLOW_DELIVERY_V3_DESC);
        schoolPerformanceFiled.put(SCHOOL_ALLOW_DELIVERY_V3_INFO, SCHOOL_ALLOW_DELIVERY_V3_INFO_DESC);

        schoolPerformanceUnitFiled.put(SCHOOL_IN_DELIVERY_TYPE_V3, SCHOOL_IN_DELIVERY_TYPE_V3_DESC);
        schoolPerformanceUnitFiled.put(POI_SELF_DELIVERY_TYPE_V3, POI_SELF_DELIVERY_TYPE_V3_DESC);
        schoolPerformanceUnitFiled.put(POI_SELF_DELIVERY_INFO_V3, POI_SELF_DELIVERY_INFO_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_SPECIFIC_LOCATION_V3, DELIVERY_SPECIFIC_LOCATION_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_NOT_GATE_REASON_V3, DELIVERY_NOT_GATE_REASON_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_NOT_GATE_INFO_V3, DELIVERY_NOT_GATE_INFO_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_NOT_ENTER_REASON_V3, DELIVERY_NOT_ENTER_REASON_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_ENTER_REASON_V3, DELIVERY_ENTER_REASON_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_NOT_ENTER_INFO_V3, DELIVERY_NOT_ENTER_INFO_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_ENTER_INFO_V3, DELIVERY_ENTER_INFO_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_UPSTAIRS_REASON_V3, DELIVERY_UPSTAIRS_REASON_V3_DESC);
        schoolPerformanceUnitFiled.put(DELIVERY_UPSTAIRS_INFO_V3, DELIVERY_UPSTAIRS_INFO_V3_DESC);

    }
}
