package com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.aggre;

import com.sankuai.meituan.waimai.customer.mq.service.WmCustomerUpdateSendService;
import com.sankuai.meituan.waimai.customer.settle.ddd.base.Entity;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.repository.WmSettlePaperSignRepository;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.repository.WmSettlePoiRepository;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.repository.WmSettleRepository;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleESignContext;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.message.WmSettleApproveModifyMsgToSettleService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettlePdfCreateService;
import com.sankuai.meituan.waimai.customer.settle.service.wallet.WmSettleWalletService;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.diff.ChangedEvent;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettlePoiWalletBo;
import com.sankuai.meituan.waimai.wallet.WalletContext;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Data
@NoArgsConstructor
public class WmSettleEffectContext {

    private int wmCustomerId;
    private int opUid;
    private String opUname = "";

    private List<WmSettleDB> wmSettleByCustomerIdMaster;

    private List<WmSettle> newWmSettleList;
    private List<WmSettleAudited> oldWmSettleAuditedList;
    private List<WmSettleAudited> newWmSettleAuditedList;

    private List<ChangedEvent> poiChangedEventList;
    private List<ChangedEvent> settleChangedEventList;

    private List<WmPoiSettleAuditedDB> wmPoiSettleAuditedList;

    private WmSettlePoiWalletBo wmSettlePoiWalletBo;

    private List<WmPoiSettleDB> wmPoiSettleList;
    private Map<Integer, ChangedEvent> changedEventMap;
    private Set<Integer> deletePoiSet;
    private List<Long> customerRelPoiList;

    private List<WmSettleDB> wmSettleList;
    private List<WmSettleAuditedDB> wmSettleAuditedList;

    private WalletContext walletContext;

    private List<WmSettle> bankSettleList;

    private List<SwitchPoiInfo> settleSwitchPoiInfoList;

    private List<WmSettle> newWmSettleListWithSwitchInfo;
    private List<WmSettleAudited> oldWmSettleAuditedListWithSwitchInfo;

    private List<WmSettle> wmSettleOfflineList;
    private List<WmSettle> wmSettleOfflineListWithSwitchInfo;

    private List<Long> switchWmPoiIdList;

    private List<Integer> switchWmPoiRelWmSettleIdList;

}
