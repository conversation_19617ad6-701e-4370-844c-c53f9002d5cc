package com.sankuai.meituan.waimai.customer.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.util.business.WmCustomerUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @description:WmEcontractSignBatchDB工具类
 * @author: zhangyuanhao02
 * @create: 2025/2/18 18:59
 */
@Slf4j
public class WmEcontractSignBatchDBUtil {
    /**
     * 按照优先级排序（降序排列）
     * @param batchDBList
     */
    public static List<WmEcontractSignBatchDB> sortByPriority(List<WmEcontractSignBatchDB> batchDBList) {
        if (CollectionUtils.isEmpty(batchDBList)) {
            return Collections.emptyList();
        }

        try {
            List<WmEcontractSignBatchDB> copyList = new ArrayList<>(batchDBList);
            // 降序排列
            Collections.sort(copyList, (b1, b2) -> {
                EcontractBatchContextBo contextBo1 = JSONObject.parseObject(b1.getBatchContext(), EcontractBatchContextBo.class);
                EcontractBatchContextBo contextBo2 = JSONObject.parseObject(b2.getBatchContext(), EcontractBatchContextBo.class);
                return Integer.compare(contextBo2.getPriority(), contextBo1.getPriority());
            });

            return copyList;
        } catch (Exception e) {
            // 兜底逻辑不进行排序
            log.error("WmEcontractSignBatchDBUtil#sortByPriority 排序失败, batchDBList:{}", JSONObject.toJSONString(batchDBList), e);
            return batchDBList;
        }
    }

    /**
     * 判断是否需要按照优先级排序（客户类型为闪购单店需要排序）
     * @param batchDBList
     * @return
     */
    public static boolean isNeedSortByPriority(List<WmEcontractSignBatchDB> batchDBList) {
        if (CollectionUtils.isEmpty(batchDBList) || !MccConfig.isSupportSortByPriority()) {
            return false;
        }
        WmEcontractSignBatchDB batchDB = batchDBList.get(0);
        EcontractBatchContextBo contextBo = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
        EcontractCustomerInfoBo customerInfoBo = contextBo.getCustomerInfoBo();
        log.info("WmEcontractSignBatchDBUtil#isNeedSortByPriority customerInfoBo:{}", JSONObject.toJSONString(customerInfoBo));
        Integer customerRealType = customerInfoBo.getCustomerRealType();

        return WmCustomerUtil.isSgDanDian(customerRealType);
    }

}
