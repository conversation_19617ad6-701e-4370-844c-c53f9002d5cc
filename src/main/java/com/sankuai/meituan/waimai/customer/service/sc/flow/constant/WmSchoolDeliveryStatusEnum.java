package com.sankuai.meituan.waimai.customer.service.sc.flow.constant;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditStatusEnum;

/**
 * 学校交付管理状态枚举
 * <AUTHOR>
 * @date 2024/02/12
 * @email <EMAIL>
 */
public enum WmSchoolDeliveryStatusEnum {
    /**
     * 待提审
     */
    PENDING(0, "待提审", (int) SchoolDeliveryAuditStatusEnum.PENDING.getType()),
    /**
     * 审批中
     */
    AUDITING(1, "审批中", (int) SchoolDeliveryAuditStatusEnum.AUDITING.getType()),
    /**
     * 审批驳回
     */
    AUDIT_REJECT(2, "审批驳回", (int) SchoolDeliveryAuditStatusEnum.REJECT.getType()),
    /**
     * 审批通过
     */
    AUDIT_PASS(3, "审批通过", (int) SchoolDeliveryAuditStatusEnum.PASS.getType());


    private final Integer code;

    private final String desc;

    private final Integer auditStatusCode;

    WmSchoolDeliveryStatusEnum(Integer code, String desc, Integer auditStatusCode) {
        this.code = code;
        this.desc = desc;
        this.auditStatusCode = auditStatusCode;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getAuditStatusCode() {
        return auditStatusCode;
    }

    public static WmSchoolDeliveryStatusEnum getByAuditStatusCode(Integer auditStatusCode) {
        if (null == auditStatusCode) {
            return null;
        }
        for (WmSchoolDeliveryStatusEnum statusEnum : WmSchoolDeliveryStatusEnum.values()) {
            if (statusEnum.getAuditStatusCode().equals(auditStatusCode)) {
                return statusEnum;
            }
        }
        return null;
    }

}
