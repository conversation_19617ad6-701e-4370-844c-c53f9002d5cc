package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.PARTB_STAMP_NAME;

import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.DELIVERY_AGGREGATION_SUPPLEMENT_INFO)
@Slf4j
@Service
public class WmEcontractDeliveryAggregationSupplementInfoPdfMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext)
            throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractDeliveryAggregationSupplementInfoPdfMaker");
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.DELIVERY_AGGREGATION_SUPPLEMENT_INFO;

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        Map<String, String> pdfMap = Maps.newHashMap();
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMap.put("partA", StringUtils.defaultIfEmpty(originContext.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partB",PARTB_STAMP_NAME);
        pdfMap.put("partBEstamp", PdfConstant.MT_SIGNKEY);
        pdfInfoBo.setPdfMetaContent(pdfMap);
        return pdfInfoBo;
    }

}
