package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskNodeDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditorBO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskNodeDO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditResultEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.util.ObjectMapperUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 食堂档口审批任务任务系统相关Service
 * <AUTHOR>
 * @date 2024/05/25
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallAuditFlowTicketService {

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    /**
     * 向BD任务系统创建审批任务
     * @param taskDO taskDO
     * @return 任务系统任务ID
     */
    public Integer createAuditTicket(WmCanteenStallAuditTaskDO taskDO, WmCanteenStallAuditorBO auditorBO, WmCanteenStallAuditTaskNodeDO taskNodeDO)
            throws WmSchCantException {
        log.info("[WmCanteenStallAuditFlowTicketService.createAuditTicket] taskDO = {}, auditorBO = {}, taskNodeDO = {}",
                JSONObject.toJSONString(taskDO), JSONObject.toJSONString(auditorBO), JSONObject.toJSONString(taskNodeDO));
        WmTicketDto ticketDto = new WmTicketDto();
        // 任务类型ID(任务系统审批流ID)
        ticketDto.setType(getTicketSystemFlowIdByAuditTaskType(taskDO.getAuditTaskType()));
        // 任务来源(按照约定配置)
        ticketDto.setSource(MccConfig.getCanteenPoiAuditSource());
        // 任务标题
        ticketDto.setTitle(getTicketTitle(taskDO));
        // 任务审批人UID
        ticketDto.setOpUid(auditorBO.getAuditorUid());
        ticketDto.setOpUname(auditorBO.getAuditorName());
        // 任务一级状态码 1:待处理 2:进行中 3:已完成 4:已失效 5:已终止
        ticketDto.setStatus(1);
        // 任务二级状态码(业务方自定义)
        ticketDto.setStage(CanteenStallAuditResultEnum.AUDITING.getType());
        // 创建人UID（0表示系统）
        ticketDto.setCreateUid(taskDO.getCuid().intValue());
        // 业务key（提供给业务方区分任务的key）
        ticketDto.setBusinessKey(taskNodeDO.getId());
        // 业务方保证幂等性的key
        ticketDto.setIdempotencyKey("canteen_stall_audit_" + taskNodeDO.getId());
        // 任务创建时间
        ticketDto.setUnixCtime(TimeUtil.unixtime());
        // 任务更新时间
        ticketDto.setUnixUtime(TimeUtil.unixtime());
        // 设置审批流key自定义审批人
        ticketDto.setProcessVariables(getTicketProcessVariables(taskDO, auditorBO.getAuditorUid()));
        // 任务优先级 1:普通 2:重要 3:无
        ticketDto.setTicketPriority(0);

        return wmCrmTicketThriftServiceAdapter.createTicket(ticketDto);
    }

    /**
     * 计算并获取任务标题
     */
    private String getTicketTitle(WmCanteenStallAuditTaskDO taskDO) {
        CanteenStallAuditTaskTypeEnum taskTypeEnum = CanteenStallAuditTaskTypeEnum.getByType(taskDO.getAuditTaskType());
        return taskTypeEnum.getName();
    }

    /**
     * 根据审批任务类型查询任务系统审批流ID
     * @param auditTaskType  任务类型
     * @return 审批流ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private Integer getTicketSystemFlowIdByAuditTaskType(Integer auditTaskType) throws WmSchCantException {
        CanteenStallAuditTaskTypeEnum taskTypeEnum = CanteenStallAuditTaskTypeEnum.getByType(auditTaskType);
        switch (taskTypeEnum) {
            case STALL_BIND_CLUE_NORMAL:
                return MccScConfig.getCanteenStallClueFollowUpNormalFlowId();
            case STALL_BIND_CLUE_ABNORMAL:
                return MccScConfig.getCanteenStallClueFollowUpAbnormalFlowId();
            default:
                throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "获取任务系统审批流ID失败");
        }
    }

    /**
     * 根据审批任务类型查询任务系统节点ID
     * @param auditTaskType  任务类型
     * @return 节点ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private Integer getTicketSystemNodeIdByAuditTaskType(Integer auditTaskType) throws WmSchCantException {
        CanteenStallAuditTaskTypeEnum taskTypeEnum = CanteenStallAuditTaskTypeEnum.getByType(auditTaskType);
        switch (taskTypeEnum) {
            case STALL_BIND_CLUE_NORMAL:
                return MccScConfig.getCanteenStallClueFollowUpNormalNodeId();
            case STALL_BIND_CLUE_ABNORMAL:
                return MccScConfig.getCanteenStallClueFollowUpAbnormalNodeId();
            default:
                throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "获取任务系统节点ID失败");
        }
    }

    /**
     * 计算并获取任务审批人处理参数(审批人UID、审批流ID、节点ID等)
     * @param taskDO taskDO
     * @param auditorUid 审批人UID
     * @return Map<String, String>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private Map<String, String> getTicketProcessVariables(WmCanteenStallAuditTaskDO taskDO, Integer auditorUid)
            throws WmSchCantException {
        Integer flowId = getTicketSystemFlowIdByAuditTaskType(taskDO.getAuditTaskType());
        Integer nodeId = getTicketSystemNodeIdByAuditTaskType(taskDO.getAuditTaskType());

        Map<String, String> resultMap = new HashMap<>();
        Map<Integer, String> targetUidMap = new HashMap<>();
        targetUidMap.put(nodeId, String.valueOf(auditorUid));

        resultMap.put("processRoutingKey", String.valueOf(flowId));
        resultMap.put("targetUid", JSONObject.toJSONString(targetUidMap));
        return resultMap;
    }


    /**
     * 查询任务系统任务详情, 如审批驳回原因
     * @param auditTaskBO auditTaskBO
     * @return WmTicketDto
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmTicketDto getAuditTicketDTO(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        WmCanteenStallAuditTaskNodeDO taskNodeDO = auditTaskBO.getTaskNodeDO();
        if (taskNodeDO == null) {
            log.error("[WmCanteenStallAuditFlowTicketService.getAuditTicketDTO] taskNodeDO is NULL. auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批信息为空");
        }

        WmTicketDto ticketDto = wmCrmTicketThriftServiceAdapter.getSubTicketByParentTicketId(Integer.parseInt(taskNodeDO.getAuditSystemId()));
        if (ticketDto == null) {
            log.error("[WmCanteenStallAuditFlowTicketService.getAuditTicketDTO] auditSystemId = {}", taskNodeDO.getAuditSystemId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询任务系统结果为空");
        }
        log.info("[WmCanteenStallAuditFlowTicketService.getAuditTicketDTO] ticketDto = {}", ticketDto.toString());
        return ticketDto;
    }


}
