package com.sankuai.meituan.waimai.customer.service.sc.log;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.DbusUtils;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAttributeDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScLogRecordDO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScLogCategory;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScLogOperateType;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 门店食堂属性日志业务逻辑
 */
@Slf4j
@Service
public class WmScCanteenPoiAttributeLogRecordService extends WmScLogRecordService {

    /**
     * 逻辑删除字段
     */
    private final String VALID_COLUMN = "valid";

    /**
     * 当更新的时候记录快照
     *
     * @param utils DTS中转换的对象
     */
    public void insertLogWhenTableInsert(DbusUtils utils) {
        try {
            if (utils == null || MapUtils.isEmpty(utils.getAftMap())) {
                return;
            }

            WmScCanteenPoiAttributeDO bean = transToBo(utils);
            WmScLogRecordDO record = new WmScLogRecordDO();
            record.setOperateType(WmScLogOperateType.INSERT.getCode());
            record.setLogCategory(WmScLogCategory.CANTEEN_POI_ATTRIBUTE.getCode());
            record.setBusinessId(bean.getWmPoiId());
            record.setLogMessage(JSON.toJSONString(utils.getAftMap()));
            insert(record);
        } catch (Exception e) {
            log.error("insertLogWhenTableInsert utils={}", JSON.toJSONString(utils), e);
        }
    }

    /**
     * 当更新的时候记录快照
     * @param utils DTS中转换的对象
     */
    public void insertLogWhenTableUpdate(DbusUtils utils) {
        try {
            if (utils == null || MapUtils.isEmpty(utils.getAftMap()) || MapUtils.isEmpty(utils.getDiffMap())) {
                return;
            }

            Map<String, Object> diffMap = utils.getDiffMap();
            Map<String, Object> afterMap = utils.getAftMap();
            if (!isEffectChange(afterMap, diffMap)) {
                return;
            }

            WmScCanteenPoiAttributeDO bean = transToBo(utils);
            WmScLogRecordDO record = new WmScLogRecordDO();
            record.setOperateType(getOperateTypeWhenUpdate(diffMap, afterMap));
            record.setLogCategory(WmScLogCategory.CANTEEN_POI_ATTRIBUTE.getCode());
            record.setBusinessId(bean.getWmPoiId());
            record.setLogDiffValue(JSON.toJSONString(diffMap));
            record.setLogMessage(JSON.toJSONString(afterMap));
            insert(record);
        } catch (WmSchCantException e) {
            log.error("insertLogWhenTableUpdate utils={} msg={}", JSON.toJSONString(utils), e.getMsg(), e);
        } catch (Exception e) {
            log.error("insertLogWhenTableUpdate utils={}", JSON.toJSONString(utils), e);
        }
    }

    /**
     * 是否有效的变更（valid字段未变更，但是变更后valid=0表示无效变更）
     * @param AfterMap
     * @param diffMap
     * @return
     * @throws WmSchCantException
     */
    private boolean isEffectChange(Map<String, Object> AfterMap, Map<String, Object> diffMap) throws WmSchCantException {
        if (!diffMap.containsKey(VALID_COLUMN)) {
            Object validValue = AfterMap.get(VALID_COLUMN);
            if (validValue == null) {
                throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "数据异常valid值不合法：" + JSON.toJSONString(AfterMap));
            }

            if (ValidEnum.INVALID.getTypeInt() == Integer.parseInt(validValue.toString())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 当更新的时候获取获取业务操作类型
     *
     * @param diffMap
     * @param AfterMap
     * @return
     * @throws WmSchCantException
     */
    private String getOperateTypeWhenUpdate(Map<String, Object> diffMap, Map<String, Object> AfterMap) throws WmSchCantException {
        if (diffMap.containsKey(VALID_COLUMN)) {
            Object validValue = AfterMap.get(VALID_COLUMN);
            if (validValue == null) {
                throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "数据异常valid值不合法：" + JSON.toJSONString(diffMap));
            }

            if (ValidEnum.INVALID.getTypeInt() == Integer.parseInt(validValue.toString())) {
                return WmScLogOperateType.DELETE.getCode();
            } else if (ValidEnum.VALID.getTypeInt() == Integer.parseInt(validValue.toString())) {
                return WmScLogOperateType.INSERT.getCode();
            }
        }
        return WmScLogOperateType.UPDATE.getCode();
    }


    private WmScCanteenPoiAttributeDO transToBo(DbusUtils utils) {
        Map<String, Object> aftMap = utils.getAftMap();
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmScCanteenPoiAttributeDO.class);
    }

}
