package com.sankuai.meituan.waimai.customer.contract.partner.ability.check.impl;

import com.dianping.pigeon.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.MapOpenApiServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.agent.AgentPoiServiceAdapter;
import com.sankuai.meituan.waimai.customer.contract.domain.TemplateContractVersionPo;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.check.AbstractWmPartnerCustomerContractCheckAbilityService;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.partner.C2SignStatusCheckEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.C2SignStatusCheckRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.C2SignStatusCheckResponseDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 客户维度校验
 * @author: liuyunjie05
 * @create: 2024/8/9 09:46
 */
@Slf4j
@Service
public class DaoCanCustomerCheckAbilityServiceImpl extends AbstractWmPartnerCustomerContractCheckAbilityService {

    private static final Integer DEFAULT_SIZE = 100;

    @Resource
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Resource
    private AgentPoiServiceAdapter agentPoiServiceAdapter;

    @Resource
    private MapOpenApiServiceAdapter mapOpenApiServiceAdapter;

    @Override
    public C2SignStatusCheckEnum getCheckType() {
        return C2SignStatusCheckEnum.DAOCAN_C2_CHECK_BY_CUSTOMER;
    }

    @Override
    public C2SignStatusCheckResponseDTO checkC2ContractSignStatus(C2SignStatusCheckRequestDTO requestDTO) throws WmCustomerException {
        checkRequestParam(requestDTO);
        Long mtCustomerId = requestDTO.getMtCustomerId();
        List<Long> mtPoiIdList = mtCustomerThriftServiceAdapter.getMtPoiIdByMtCustomerId(mtCustomerId, BusinessLineEnum.NIB_FOOD.getCode());

        List<Long> noC2ContractMtPoiIdList = new ArrayList<>();
        List<List<Long>> partitionList = Lists.partition(mtPoiIdList, DEFAULT_SIZE);
        for (List<Long> partMtPoiIdList : partitionList) {
            if (noC2ContractMtPoiIdList.size() >= 3) {
                break;
            }
            noC2ContractMtPoiIdList.addAll(processPartition(partMtPoiIdList, mtCustomerId));
        }
        return buildResponse(noC2ContractMtPoiIdList);
    }

    private List<Long> processPartition(List<Long> partMtPoiIdList, Long mtCustomerId) {
        Map<Long, Integer> agentIdMap = agentPoiServiceAdapter.getAgentIdMapByPoiIds(partMtPoiIdList);
        List<Integer> allAgentIdList = agentIdMap.values().stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allAgentIdList)) {
            return Collections.emptyList();
        }
        List<TemplateContractVersionPo> poList = queryContractVersionPoList(mtCustomerId, allAgentIdList);
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>(agentIdMap.keySet());
        }
        List<Integer> hasC2AgentIdList = poList.stream().map(TemplateContractVersionPo::getAgentId).collect(Collectors.toList());
        List<Long> noC2ContractMtPoiIdList = new ArrayList<>();
        for (Integer agentId : allAgentIdList) {
            if (!hasC2AgentIdList.contains(agentId)) {
                noC2ContractMtPoiIdList.addAll(findMtPoiIdFromMap(agentIdMap, agentId));
            }
        }
        return noC2ContractMtPoiIdList;
    }


    @Override
    protected void checkRequestParam(C2SignStatusCheckRequestDTO requestDTO) throws WmCustomerException {
        if (requestDTO == null || requestDTO.getMtCustomerId() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "参数异常, 没有平台客户ID");
        }
    }

    private C2SignStatusCheckResponseDTO buildResponse(List<Long> noC2ContractMtPoiIdList) {
        C2SignStatusCheckResponseDTO responseDTO = new C2SignStatusCheckResponseDTO();
        if (CollectionUtils.isEmpty(noC2ContractMtPoiIdList)) {
            responseDTO.setMtPoiNameList(Collections.emptyList());
        } else {
            responseDTO.setMtPoiNameList(mapOpenApiServiceAdapter.getPdcNameByPdcId(noC2ContractMtPoiIdList));
        }
        return responseDTO;
    }

    private List<Long> findMtPoiIdFromMap(Map<Long, Integer> agentIdMap, Integer agentId) {
        List<Long> mtPoiIdList = new ArrayList<>();
        for (Map.Entry<Long, Integer> singleMap : agentIdMap.entrySet()) {
            if (singleMap.getValue().equals(agentId)) {
                mtPoiIdList.add(singleMap.getKey());
            }
        }
        return mtPoiIdList;
    }

}
