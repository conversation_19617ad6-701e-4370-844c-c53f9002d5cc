package com.sankuai.meituan.waimai.customer.service.sign.pack.check;

import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 电子合同打包(反查关联结算校验)
 */
@Service
public class WmEcontractPackRelSettleCheckService {

    @Resource
    private WmSettleService wmSettleService;

    @Resource
    private WmCustomerPoiService wmCustomerPoiService;

    /**
     * 查询客户关联结算
     * @param customerId 客户ID
     */
    public boolean hasEffectRelSettleByCustomerId(Integer customerId) throws WmCustomerException {
        List<WmSettleAudited> wmSettleAuditedList = wmSettleService.getWmSettleAuditedBasicListByWmCustomerId(customerId);
        return wmSettleAuditedList.size() > 0;
    }

}
