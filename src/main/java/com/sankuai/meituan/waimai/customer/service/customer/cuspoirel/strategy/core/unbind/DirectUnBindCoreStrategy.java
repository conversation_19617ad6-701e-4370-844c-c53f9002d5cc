package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.unbind;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind.CustomerPoiUnBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.atom.PoiUnBindCustomerAtomService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IUnBindCoreStrategy;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * <AUTHOR>
 * @desc 直接绑定核心操作策略
 * @date 20240122
 */
@Slf4j
@Service
public class DirectUnBindCoreStrategy implements IUnBindCoreStrategy {

    @Autowired
    private PoiUnBindCustomerAtomService poiUnBindCustomerAtomService;

    @Override
    public void execute(CustomerPoiUnBindFlowContext context) throws WmCustomerException {

        Integer customerId = context.getCustomerId();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        // 需要解绑门店列表为空则不进行处理&&非确认失败解绑情况
        if (CollectionUtils.isEmpty(context.getNeedUnBindWmPoiIdSet()) && !context.isConfirmFailedToUnBindFlag()) {
            log.info("DirectUnBindCoreStrategy.execute,执行客户门店直接解绑操作，需要解绑门店列表为空，不再执行,customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));
            return;
        }
        //直接解绑
        poiUnBindCustomerAtomService.directUnbindCustomer(customerId, wmPoiIdSet, context.getPoiAndTaskMaps());
        log.info("DirectUnBindCoreStrategy.execute,客户门店直接解绑原子能力执行完成,customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));
    }
}
