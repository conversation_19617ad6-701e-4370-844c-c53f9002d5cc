package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.frog.sdk.data.DmlType;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiListVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealTypeService;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectOpTagEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmColumnInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListInfoDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiSubjectResult;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiSettlementThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WmCustomerPoiCheck implements InfoUpdateCheck {

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmPoiSettlementThriftService.Iface wmPoiSettlementThriftService;

    @Autowired
    private WmCustomerRealTypeService wmCustomerRealTypeService;

    @Autowired
    private CustomerPoiBindService customerPoiBindService;

    @Override
    public String check(String tableName, DmlType operateType, Map<String, WmColumnInfo> columnInfoMap) {
        if (!tableName.equals(WmCustomerRelTableDbusEnum.TABLE_WM_CUSTOMER_POI_REL.getAlias())) {
            return null;
        }
        switch (operateType) {
            case INSERT:
            case UPDATE:
                return checkInfo(columnInfoMap);
            case DELETE:
                return delete(columnInfoMap);
            default:
                return null;
        }
    }


    private String checkInfo(Map<String, WmColumnInfo> columnInfoMap) {
        String id = columnInfoMap.get(WmCustomerPoiListESFields.ID.getDbField()).getNewValue();
        String valid = columnInfoMap.get(WmCustomerPoiListESFields.VALID.getDbField()).getNewValue();
        String relationStatus = columnInfoMap.get(WmCustomerPoiListESFields.RELATION_STATUS.getDbField()).getNewValue();

        if (StringUtils.isBlank(id) || StringUtils.isBlank(valid)) {
            return null;
        }
        if (valid.equals(String.valueOf(ValidEnum.VALID_YES.getValue()))) {
            List<WmCustomerPoiListInfoDTO> list = getData(id);
            if (CollectionUtils.isEmpty(list) || list.size() != 1) {
                return String.format("客户门店关联失败id:%s;", id);
            }
            return checkMx(list.get(0), columnInfoMap);
        } else if (relationStatus.equals(String.valueOf(CustomerRelationStatusEnum.READY_BIND.getCode()))) {
            List<WmCustomerPoiListInfoDTO> list = getData(id);
            if (CollectionUtils.isEmpty(list) || list.size() != 1) {
                return String.format("客户门店预绑定失败%s:%s;", WmCustomerPoiListESFields.ID.getField(), id);
            }
            return checkMx(list.get(0), columnInfoMap);
        } else {
            List<WmCustomerPoiListInfoDTO> list = getData(id);
            if (CollectionUtils.isNotEmpty(list)) {
                return String.format("客户门店取消失败%s:%s;", WmCustomerPoiListESFields.ID.getField(), id);
            }
        }
        return null;
    }


    private String checkMx(WmCustomerPoiListInfoDTO dto, Map<String, WmColumnInfo> columnInfoMap) {
        StringBuffer errMsg = new StringBuffer();
        int switchTaskId = MoreObjects.firstNonNull(Integer.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.SWITCH_TASK_ID.getDbField()).getNewValue()), 0);
        if (dto.getSwitchTaskId().intValue() != switchTaskId) {
            errMsg.append(String.format("客户门店信息不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.SWITCH_TASK_ID.getField(), switchTaskId, dto.getSwitchTaskId()));
        }
        int relationStatus = MoreObjects.firstNonNull(Integer.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.RELATION_STATUS.getDbField()).getNewValue()), 0);
        if (dto.getRelationStatus().intValue() != relationStatus) {
            errMsg.append(String.format("客户门店信息不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.RELATION_STATUS.getField(), relationStatus, dto.getRelationStatus()));
        }
        int valid = MoreObjects.firstNonNull(Integer.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.VALID.getDbField()).getNewValue()), 0);
        if (dto.getValid().intValue() != valid) {
            errMsg.append(String.format("客户门店信息不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.VALID.getField(), valid, dto.getValid()));
        }
        Long customerId = MoreObjects.firstNonNull(Long.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.CUSTOMER_ID.getDbField()).getNewValue()), 0L);
        if (dto.getCustomerId().longValue() != customerId) {
            errMsg.append(String.format("客户门店信息不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.CUSTOMER_ID.getField(), customerId, dto.getCustomerId()));
        }
        long mtCustomerId = wmCustomerDBMapper.selectMtCustomerIdByWmCustomerId(customerId);
        if (dto.getMtCustomerId().longValue() != mtCustomerId) {
            errMsg.append(String.format("客户门店信息不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.MT_CUSTOMER_ID.getField(), mtCustomerId, dto.getMtCustomerId()));
        }

        long wmPoiId = MoreObjects.firstNonNull(Long.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getNewValue()), 0l);
        if (valid == CustomerConstants.VALID) {
            String id = columnInfoMap.get(WmCustomerPoiListESFields.ID.getDbField()).getNewValue();
            String result = checkNewSettle(id, customerId, wmPoiId);
            if (StringUtils.isNotBlank(result)) {
                errMsg.append(result + ";");
            }
            result = validateCustomerPoiBind(customerId.intValue(), wmPoiId);
            if (StringUtils.isNotBlank(result)) {
                errMsg.append(result);
            }
        }

        int oldValid = getIntValue(columnInfoMap.get(WmCustomerPoiListESFields.VALID.getDbField()).getOldValue(), 0);
        if (oldValid == CustomerConstants.VALID && valid == CustomerConstants.UNVALID) {
            String result = checkContractSubject(wmPoiId, customerId.intValue(), ContractSignSubjectOpTagEnum.CUSTOMER_BIND);
            if (StringUtils.isNotBlank(result)) {
                errMsg.append(result + ";");
            }
            result = validateCustomerPoiBind(customerId.intValue(), wmPoiId);
            if (StringUtils.isNotBlank(result)) {
                errMsg.append(result);
            }
        }
        if (oldValid == CustomerConstants.UNVALID && valid == CustomerConstants.VALID) {
            long oldWmPoiId = getLongValue(columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getOldValue(), 0l);
            int oldCustomerId = getIntValue(columnInfoMap.get(WmCustomerPoiListESFields.CUSTOMER_ID.getDbField()).getOldValue(), 0);
            String checkContractSubjectResult = checkContractSubject(oldWmPoiId, oldCustomerId, ContractSignSubjectOpTagEnum.CUSTOMER_UNBIND);
            if (StringUtils.isNotBlank(checkContractSubjectResult)) {
                errMsg.append(checkContractSubjectResult);
            }
        }
        return errMsg.toString();
    }

    private int getIntValue(String value, int defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return Integer.valueOf(value);
    }

    private long getLongValue(String value, long defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return Long.valueOf(value);
    }

    private String delete(Map<String, WmColumnInfo> columnInfoMap) {
        String id = columnInfoMap.get(WmCustomerPoiListESFields.ID.getDbField()).getOldValue();
        List<WmCustomerPoiListInfoDTO> list = getData(id);
        if (CollectionUtils.isNotEmpty(list)) {
            return String.format("客户门店关联关系删除失败id:%s;", id);
        }

        long oldWmPoiId = MoreObjects.firstNonNull(Long.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getOldValue()), 0l);
        int oldCustomerId = MoreObjects.firstNonNull(Integer.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.CUSTOMER_ID.getDbField()).getOldValue()), 0);
        String checkContractSubjectResult = checkContractSubject(oldWmPoiId, oldCustomerId, ContractSignSubjectOpTagEnum.CUSTOMER_UNBIND);
        if (StringUtils.isNotBlank(checkContractSubjectResult)) {
            return checkContractSubjectResult;
        }
        return null;
    }

    private List<WmCustomerPoiListInfoDTO> getData(String id) {
        WmCustomerPoiListVo condition = new WmCustomerPoiListVo();
        condition.setId(Integer.valueOf(id));
        condition.setPageNo(1);
        condition.setPageSize(10);
        return wmCustomerPoiListEsService.queryData(condition);
    }

    private String checkNewSettle(String id, long customerId, long wmPoiId) {
        long customerLabelId = MccCustomerConfig.getNewSettleForCustomerTag();
        long poiLabelId = MccCustomerConfig.getNewSettleForPoiTag();
        Long mtCustomerId = wmCustomerDBMapper.selectMtCustomerIdByWmCustomerId(customerId);
        if (mtCustomerId == null) {
            return String.format("客户门店结算版本失败id:%s", id);
        }
        WmPoiLabelRel customerLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(mtCustomerId, customerLabelId, LabelSubjectTypeEnum.CUSTOMER.getCode());
        log.info("checkNewSettle新结算客户 mtCustomerId={},customerLabel={}", mtCustomerId, JSONObject.toJSONString(customerLabel));
        boolean isNewSettleForCustomer = false;
        String isNewSettleForCustomerStr = "旧";
        if (customerLabel != null && customerLabel.getId() > 0l) {
            isNewSettleForCustomer = true;
            isNewSettleForCustomerStr = "新";
        }

        WmPoiLabelRel poiLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmPoiId, poiLabelId, LabelSubjectTypeEnum.POI.getCode());
        log.info("checkCustomerAndPoiVersion旧结算客户新结算门店 ,wmPoiId={}", wmPoiId);
        boolean isNewSettleForPoi = false;
        String isNewSettleForPoiStr = "旧";

        if (poiLabel != null && poiLabel.getId() > 0l) {
            isNewSettleForPoi = true;
            isNewSettleForPoiStr = "新";
        }

        if (isNewSettleForCustomer != isNewSettleForPoi) {
            return String.format("客户门店结算版本不一致id:%s,客户%s,门店%s", id, isNewSettleForCustomerStr, isNewSettleForPoiStr);
        } else {
            return null;
        }

    }


    /**
     * 校验合同快照
     *
     * @param wmPoiId
     * @param customerId
     * @param tagEnum
     * @return
     */
    private String checkContractSubject(long wmPoiId, int customerId, ContractSignSubjectOpTagEnum tagEnum) {
        if (wmPoiId <= 0l || customerId <= 0) {
            return "";
        }
        Long currenTime = System.currentTimeMillis() / 1000;
        try {
            WmPoiSubjectResult result = wmPoiSettlementThriftService.getPoiSignSubjectInfo(wmPoiId, currenTime.intValue());
            if (result == null) {
                return String.format("校验客户合同快照信息为空 wmPoiId:%s", wmPoiId);
            }
            if (result.getCustomerId() != customerId) {
                return String.format("校验客户合同快照客户id不一致：wmPoiId:%s,result:%s", wmPoiId, JSONObject.toJSONString(result));
            }
            if (tagEnum == ContractSignSubjectOpTagEnum.CUSTOMER_BIND) {
                if (result.tag == ContractSignSubjectOpTagEnum.CUSTOMER_BIND.getCode()) {
                    return "";
                } else {
                    return String.format("校验客户合同绑定不一致：wmPoiId:%s,result:%s", wmPoiId, JSONObject.toJSONString(result));
                }
            }
            if (tagEnum == ContractSignSubjectOpTagEnum.CUSTOMER_UNBIND) {
                if (result.tag == ContractSignSubjectOpTagEnum.CUSTOMER_UNBIND.getCode() && result.getPartBNum() == 0 && result.partLogisticsNum == 0) {
                    return "";
                } else {
                    return String.format("校验客户合同解绑不一致：wmPoiId:%s,result:%s", wmPoiId, JSONObject.toJSONString(result));
                }
            }
            return String.format("校验客户合同快照异常：%s", JSONObject.toJSONString(result));
        } catch (WmServerException e) {
            log.warn("getPoiSignSubjectInfo 失败 wmPoiId={},currenTime={}", wmPoiId, currenTime, e);
        } catch (Exception e) {
            log.error("getPoiSignSubjectInfo 失败 wmPoiId={},currenTime={}", wmPoiId, currenTime, e);
        }
        return String.format("校验客户合同快照服务异常 wmPoiId:%s", wmPoiId);
    }

    /**
     * 校验客户门店绑定是否满足规则要求
     *
     * @param customerId
     * @param wmPoiId
     * @return
     */
    private String validateCustomerPoiBind(int customerId, long wmPoiId) {
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            customerPoiBindService.checkBind(customerId, wmPoiId, CustomerPoiBindTypeEnum.HIGN_SEA_CHECK, CustomerTaskSourceEnum.UN_KNOWN);
            return null;
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            log.warn("validateCustomerPoiBind 失败 customerId={},wmPoiId={}", customerId, wmPoiId, e);
            return e.getMsg();
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            log.error("validateCustomerPoiBind 失败 customerId={},wmPoiId={}", customerId, wmPoiId, e);
            return "客户门店绑定校验失败";
        } finally {
            String source = "MONITOR";
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_VALID.getName(), source, status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_VALID.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_VALID.getTag(), source)
                    .tag(CustomerMetricEnum.CUSTOMER_POI_VALID.getStatus(), status).count();
        }
    }
}
