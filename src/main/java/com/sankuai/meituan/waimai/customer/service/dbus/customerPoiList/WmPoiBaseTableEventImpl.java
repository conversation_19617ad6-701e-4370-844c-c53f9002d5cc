package com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.WmPoiRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiQueryCondtionVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class WmPoiBaseTableEventImpl implements IPoiRelTableEvent {

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;


    @Autowired
    private WmCustomerPoiListEsService esService;

    @Override
    public WmPoiRelTableDbusEnum getTable() {
        return WmPoiRelTableDbusEnum.TABLE_WM_POI_BASE;
    }

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) {
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);

        boolean isUpdate = checkUpdate(utils.getDiffMap());
        if (!isUpdate) {
            log.info("[WmPoiBaseTableEventImpl] handleUpdate类型不为update");
            return null;
        }

        Map<String, Object> after = utils.getAftMap();

        List<WmCustomerPoiDB> list = getWmCustomerPoi((Integer) after.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()));
        if (CollectionUtils.isEmpty(list)) {
            log.info("[WmPoiBaseTableEventImpl] handleUpdate查询门店列表为空, 门店ID={}", (Integer) after.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()));
            return null;
        }
        String name = (String) after.get(WmCustomerPoiListESFields.SHOP_NAME.getDbField());
        String address = (String) after.get(WmCustomerPoiListESFields.ADDRESS.getDbField());
        Integer ownerUid = (Integer) after.get(WmCustomerPoiListESFields.OWNER_UID.getDbField());
        log.info("[WmPoiBaseTableEventImpl] handleUpdate name={},address={},ownerUid={}", name, address, ownerUid);

        Map<Object, Map<String, Object>> map = Maps.newHashMap();
        for (WmCustomerPoiDB db : list) {
            log.info("db={},name={},address={},ownerUid={}", JSON.toJSONString(db), name, address, ownerUid);
            map.put(db.getId(), WmCustomerPoiListEsService.makeMap(new String[]{WmCustomerPoiListESFields.SHOP_NAME.getField(),
                            WmCustomerPoiListESFields.ADDRESS.getField(), WmCustomerPoiListESFields.OWNER_UID.getField()},
                    new Object[]{name, address, ownerUid}));
        }
        BulkResponse response = esService.bulkUpdate(map);
        if (response == null) {
            return "门店信息更新失败";
        } else {
            return null;
        }
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) {
        return null;
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) {
        return null;
    }

    /**
     * 判断门店名称\地址\门店责任人是否发生了变化，如果未发生变化则不更新
     *
     * @param diffMap
     * @return
     */
    private boolean checkUpdate(Map<String, Object> diffMap) {
        if (MapUtils.isEmpty(diffMap)
                || !(diffMap.containsKey(WmCustomerPoiListESFields.SHOP_NAME.getDbField())
                || diffMap.containsKey(WmCustomerPoiListESFields.ADDRESS.getDbField())
                || diffMap.containsKey(WmCustomerPoiListESFields.OWNER_UID.getDbField()))) {
            return false;
        }
        return true;
    }

    private List<WmCustomerPoiDB> getWmCustomerPoi(Integer wmPoiId) {
        WmCustomerPoiQueryCondtionVo vo = new WmCustomerPoiQueryCondtionVo();
        vo.setWmPoiId(wmPoiId.longValue());
        return wmCustomerPoiDBMapper.selectCustomerPoiRelByCondition(vo);
    }
}
