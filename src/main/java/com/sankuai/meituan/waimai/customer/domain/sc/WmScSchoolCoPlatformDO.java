package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 学校合作平台信息DO V2
 * <AUTHOR>
 * @date 2024/06/14
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmScSchoolCoPlatformDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 合作平台
     */
    private Integer cooperationPlatform;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 校内商家单量
     */
    private Integer schoolInPoiOrderCount;
    /**
     * 校外商家单量
     */
    private Integer schoolOutPoiOrderCount;
    /**
     * 校内在线商家数
     */
    private Integer schoolInOnlinePoiCount;
    /**
     * 校外在线商家数
     */
    private Integer schoolOutOnlinePoiCount;
    /**
     * 合作平台的收费方式列表
     */
    private String deliveryFeeType;
    /**
     * 当合作平台的收费方式选择为“其他”时填写的内容
     */
    private String deliveryFeeTypeInfo;
    /**
     * 美团收费和合作平台对比
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolCompareCooperationPlatformEnum}
     */
    private Integer compareCooperationPlatform;
    /**
     * 当美团收费和合作平台对比选择了“无法比较”时填写的内容
     */
    private String compareCooperationPlatformInfo;
    /**
     * 合作平台是否可进校
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolPlatformAllowToSchoolEnum}
     */
    private Integer platformAllowToSchool;
    /**
     * 合作平台支持送餐上楼
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolPlatformSupportFoodUpstairsV2Enum}
     */
    private Integer supportFoodUpstairs;
    /**
     * 当合作平台支持送餐上楼选择了“其他”时填写的内容
     */
    private String supportFoodUpstairsInfo;
    /**
     * 送餐上楼费用
     */
    private String foodUpstairsFee;
    /**
     * 送餐上楼原因
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolPlatformFoodUpstairsReasonEnum}
     */
    private String foodUpstairsReason;
    /**
     * 当送餐上楼原因选择了其他时填写的内容
     */
    private String foodUpstairsReasonInfo;
    /**
     * 合作平台与学校达成合作时间(年-月)
     */
    private String platformEstablishTime;
    /**
     * 合作平台的优势
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolPlatformEstablishAdvantageEnum}
     */
    private String platformEstablishAdvantage;
    /**
     * 当合作平台的优势选择了“有用户增值服务（如代取快递）”填写的内容
     */
    private String advantageAddedServiceInfo;
    /**
     * 当合作平台的优势选择了“配送体验好（时效等）”填写的内容
     */
    private String advantageGoodExperienceInfo;
    /**
     * 当合作平台的优势选择了“有吸引力的C端活动（如发券）”填写的内容
     */
    private String advantageAttractionInfo;
    /**
     * 当合作平台的优势选择了“宣传力度大（如私域流量，扫楼等）”填写的内容
     */
    private String advantagePropagandaInfo;
    /**
     * 合作平台的优势是其他时填写的内容
     */
    private String advantageExtraInfo;
    /**
     * 学校合作平台营销活动列表
     */
    private List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOList;
    /**
     * 供给分布
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolPlatformSupplyDistributionEnum}
     */
    private Integer supplyDistribution;
    /**
     * 垄断形式
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolPlatformMonopolyFormEnum}
     */
    private Integer monopolyForm;
    /**
     * 其他信息
     */
    private String extraInfo;
    /**
     * 是否有效 0-无效，1-有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Long ctime;
    /**
     * 修改时间
     */
    private Long utime;
}
