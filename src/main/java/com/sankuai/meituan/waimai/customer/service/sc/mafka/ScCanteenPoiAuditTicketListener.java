package com.sankuai.meituan.waimai.customer.service.sc.mafka;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.StringUtils;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiTaskAuditMinutiaMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenPoiTaskAuditMinutiaBO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskDO;
import com.sankuai.meituan.waimai.customer.service.sc.WmCanteenService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallAuditService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.WmSchoolDeliveryService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiTaskSimpleService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScTicketNoticeBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.util.ObjectMapperUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 任务系统状态变更监听器
 * 任务审批通过后需要发起电子合同网签
 * 任务通过和驳回的一级状态status都是3，二级状态分别是1001和1002，https://km.sankuai.com/page/370318378
 */
@Slf4j
@Component
public class ScCanteenPoiAuditTicketListener implements IMessageListener {

    @Autowired
    private WmCanteenService wmCanteenService;

    @Autowired
    private WmScCanteenPoiService wmScCanteenPoiService;

    @Autowired
    private WmScCanteenPoiTaskAuditMinutiaMapper wmScCanteenPoiTaskAuditMinutiaMapper;

    @Autowired
    private WmSchoolDeliveryService wmSchoolDeliveryService;

    @Autowired
    private WmCanteenStallAuditService wmCanteenStallAuditService;
    @Autowired
    private WmScCanteenPoiTaskSimpleService wmScCanteenPoiTaskSimpleService;

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;


    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        log.info("任务系统处理结果消息接收器，message = {}, context = {}", JSONObject.toJSONString(message), JSONObject.toJSONString(context));
        String json = (String) message.getBody();
        WmScTicketNoticeBo notice = null;
        try {
            notice = ObjectMapperUtils.parseObject(json, WmScTicketNoticeBo.class);
            // 任务通过和驳回的一级状态status都是3,任务终止状态是5，二级状态分别是1001和1002
            if (notice.getTicketStatus() != ScConstants.CRM_TICKET_SUCCESS
                    && notice.getTicketStatus() != ScConstants.CRM_TICKET_STOP_STATUS) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 学校交付审批任务
            if (notice.getTicketType() == MccScConfig.getSchoolDeliveryAssignmentCreateAuditFlowId()
                    || notice.getTicketType() == MccScConfig.getSchoolDeliveryAssignmentEditAuditFlowId()
                    || notice.getTicketType() == MccScConfig.getSchoolDeliveryGoalSetCreateAuditFlowId()
                    || notice.getTicketType() == MccScConfig.getSchoolDeliveryGoalSetEditAuditFlowId()
                    || notice.getTicketType() == MccScConfig.getSchoolDeliveryFollowUpCreateAuditFlowId()
                    || notice.getTicketType() == MccScConfig.getSchoolDeliveryFollowUpEditAuditFlowId()) {
                schoolDeliveryAuditTaskRoute(notice);
            }

            // 食堂档口审批任务
            if (notice.getTicketType() == MccScConfig.getCanteenStallClueFollowUpNormalFlowId()
                    || notice.getTicketType() == MccScConfig.getCanteenStallClueFollowUpAbnormalFlowId()) {
                canteenStallAuditTaskRoute(notice);
            }

            // 食堂信息新建/修改审批
            if (notice.getTicketType() == MccScConfig.getCanteenInfoEditAuditFlowId()
                    || notice.getTicketType() == MccScConfig.getCanteenInfoSaveAuditFlowId()) {
                canteenInfoAuditRoute(notice);
            }

            // 任务终止时二级任务状态处理为驳回，即按照驳回处理
            if (notice.getTicketStatus() == ScConstants.CRM_TICKET_STOP_STATUS) {
                notice.setTicketStage(ScConstants.CRM_TICKET_REJECT_STAGE);
            }
            // 食堂门店审核任务
            if (MccConfig.getCanteenPoiAuditTicketType() == notice.getTicketType()) {
                canteenPoiBindTaskRoute(notice);
            }
            // 食堂换绑门店任务
            if (notice.getTicketType() == CanteenPoiTaskTypeEnum.TRANSFER_BIND.getTaskType()) {
                canteenPoiTaskRoute(notice);
            }

            /** 判断是否属于换绑或解绑的特定审核流*/
            if (notice.getTicketType() == MccScConfig.getTransferFirstSubTaskAuditNodeId() ||
                    notice.getTicketType() == MccScConfig.getTransferSecondSubTaskAuditNodeId() ||
                    notice.getTicketType() == MccScConfig.getUnbindFirstSubTaskAuditNodeId()||
                    notice.getTicketType() == MccScConfig.getUnbindSecondSubTaskAuditNodeId() ||
                    notice.getTicketType() == MccScConfig.getDeleteFirstTaskAuditNodeId() ||
                    notice.getTicketType() == MccScConfig.getDeleteSecondTaskAuditNodeId()) {
                canteenPoiTaskSimpleRoute(notice);
            }
            // 修改食堂审核
            if (MccConfig.getCanteenAuditTicketType() == notice.getTicketType()) {
                wmCanteenService.auditCallback(notice);
            }
        } catch (IOException e) {
            log.error("接收到任务系统状态变更的Mafka消息，系统对象转换异常 json={}", json, e);
        } catch (Exception e) {
            log.error("接收到任务系统状态变更的Mafka消息,业务逻辑处理异常 json={}", json, e);
        }
        // 统一返回成功，业务重试处理
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void canteenPoiBindTaskRoute(WmScTicketNoticeBo notice) throws TException, WmSchCantException {
        canteenPoiTaskRoute(notice);
    }

    private void schoolDeliveryAuditTaskRoute(WmScTicketNoticeBo noticeBo) throws WmSchCantException, TException {
        int ticketStage = noticeBo.getTicketStage();
        String auditSystemId = String.valueOf(noticeBo.getTicketId());
        switch (ticketStage) {
            // 任务审批通过
            case CRM_TICKET_APPROVE_STAGE:
                wmSchoolDeliveryService.passDeliveryAuditTask(auditSystemId);
                return;
            // 任务终止(包含 1-交付系统撤回审批 和 2-任务系统操作终止)
            case CRM_TICKET_END_STAGE:
                wmSchoolDeliveryService.stopDeliveryAuditTask(auditSystemId);
                return;
            // 任务审批驳回
            case CRM_TICKET_REJECT_STAGE:
                wmSchoolDeliveryService.rejectDeliveryAuditTask(auditSystemId);
                return;
            default:
        }
    }

    private void canteenInfoAuditRoute(WmScTicketNoticeBo noticeBo) throws WmSchCantException, TException {
        int ticketStage = noticeBo.getTicketStage();
        String auditSystemId = String.valueOf(noticeBo.getTicketId());
        switch (ticketStage) {
            // 任务审批通过
            case CRM_TICKET_APPROVE_STAGE:
                wmCanteenService.passCanteenInfoAuditByTicket(auditSystemId);
                return;
            // 任务终止(任务系统操作终止)
            case CRM_TICKET_END_STAGE:
                wmCanteenService.stopCanteenInfoAuditByTicket(auditSystemId);
                return;
            // 任务审批驳回
            case CRM_TICKET_REJECT_STAGE:
                wmCanteenService.rejectCanteenInfoAuditByTicket(auditSystemId);
                return;
            default:
        }
    }

    private void canteenStallAuditTaskRoute(WmScTicketNoticeBo noticeBo) throws WmSchCantException, TException {
        int ticketStage = noticeBo.getTicketStage();
        String auditSystemId = String.valueOf(noticeBo.getTicketId());
        switch (ticketStage) {
            // 任务审批通过
            case CRM_TICKET_APPROVE_STAGE:
                wmCanteenStallAuditService.passClueFollowUpStatusAuditTask(auditSystemId);
                return;
            // 任务终止(包含 1-交付系统撤回审批 和 2-任务系统操作终止)
            case CRM_TICKET_END_STAGE:
                wmCanteenStallAuditService.stopClueFollowUpStatusAuditTask(auditSystemId);
                return;
            // 任务审批驳回
            case CRM_TICKET_REJECT_STAGE:
                wmCanteenStallAuditService.rejectClueFollowUpStatusAuditTask(auditSystemId);
                return;
            default:
        }
    }

    /**
     * 食堂门店任务处理
     * @param notice notice
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    private void canteenPoiTaskRoute(WmScTicketNoticeBo notice) throws WmSchCantException, TException {
        log.info("[ScCanteenPoiAuditTicketListener.canteenPoiTaskRoute] input param: notice = {}", JSONObject.toJSONString(notice));
        WmCanteenPoiTaskAuditMinutiaBO minutiaBO = new WmCanteenPoiTaskAuditMinutiaBO();
        minutiaBO.setAuditSystemType(CanteenAuditSystemEnum.TASK_SYSTEM.getCode());
        minutiaBO.setAuditSystemId(String.valueOf(notice.getTicketId()));
        minutiaBO.setAuditResult(notice.getTicketStage());
        if (StringUtils.isBlank(minutiaBO.getAuditSystemId())) {
            log.info("[食堂管理]返回的任务系统ID为空，不处理,notice={}", JSONObject.toJSONString(notice));
            return;
        }

        WmScCanteenPoiTaskAuditMinutiaDO minutiaDO = wmScCanteenPoiTaskAuditMinutiaMapper.selectByAuditSystem(minutiaBO.getAuditSystemType(), minutiaBO.getAuditSystemId());
        if (minutiaDO == null) {
            log.info("[食堂管理]任务系统消息通知未找到审批信息，不处理,notice={}", JSONObject.toJSONString(notice));
            return;
        }
        wmScCanteenPoiService.commitTask(minutiaBO);
    }


    /**
     * 食堂门店任务处理
     * @param notice notice
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    private void canteenPoiTaskSimpleRoute(WmScTicketNoticeBo notice) throws WmSchCantException, TException {
        log.info("[ScCanteenPoiAuditTicketListener.canteenPoiTaskSimpleRoute] input param: notice = {}", JSONObject.toJSONString(notice));
        WmCanteenPoiTaskAuditMinutiaBO minutiaBO = new WmCanteenPoiTaskAuditMinutiaBO();
        minutiaBO.setAuditSystemType(CanteenAuditSystemEnum.TASK_SYSTEM.getCode());
        minutiaBO.setAuditSystemId(String.valueOf(notice.getTicketId()));
        minutiaBO.setAuditResult(notice.getTicketStage());
        minutiaBO.setTicketType(notice.getTicketType());
        // 对终止特判，任务系统不返回终止的二级状态
        if (notice.getTicketStatus() > 0) {
            if (notice.getTicketStatus() == ScConstants.CRM_TICKET_CANCEL_STATUS || notice.getTicketStatus() == ScConstants.CRM_TICKET_STOP_STATUS) {
                minutiaBO.setAuditResult(ProcessSecondStatusEnum.TERMINATED.getCode());
            }
        }
        if (StringUtils.isBlank(minutiaBO.getAuditSystemId())) {
            log.info("[食堂管理]返回的任务系统ID为空，不处理,notice={}", JSONObject.toJSONString(notice));
            return;
        }

        WmTicketDto sonTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(minutiaBO.getAuditSystemId()));
        if (sonTicketDto == null) {
            log.error("[WmTranferBindSimpleService.canteenPoiTaskSimpleRoute] ticketDto is null. auditSystemId = {}", minutiaBO.getAuditSystemId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }
        // 1.2 获取任务信息（本地）
        WmScCanteenPoiTaskDO taskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTicketId(sonTicketDto.getParentTicketId());
        if (taskDO == null) {
            log.error("[WmTranferBindSimpleService.canteenPoiTaskSimpleRoute] taskDO is null. auditSystemId = {}", sonTicketDto.getParentTicketId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }
        if (!wmScCanteenPoiTaskSimpleService.isAuditNodeToBeProcessed(notice.getTicketType(), taskDO.getAuditStatus())) {
            log.info("[食堂管理]任务系统消息通知未找到审批信息，不处理,notice={}", JSONObject.toJSONString(notice));
            return;
        }

        // 根据 auditSystemId 获取taskid，判断taskid是否存在
        /*WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO =  wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTicketId(Long.valueOf(minutiaBO.getAuditSystemId()));
        if (wmScCanteenPoiTaskDO == null) {
            log.info("[食堂管理]任务系统消息通知未找到审批信息，不处理,notice={}", JSONObject.toJSONString(notice));
            return;
        }*/
        wmScCanteenPoiTaskSimpleService.commitTask(minutiaBO);
    }



}
