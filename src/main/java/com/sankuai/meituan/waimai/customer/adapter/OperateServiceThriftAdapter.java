package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.dianping.mobileossapi.dto.operate.ShortUrlRequestThrift;
import com.dianping.mobileossapi.dto.operate.ShortUrlResultThrift;
import com.dianping.mobileossapi.service.operate.OperateServiceThrift;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/31 14:58
 */
@Service
@Slf4j
public class OperateServiceThriftAdapter {

    @Resource
    private OperateServiceThrift operateServiceThrift;

    private static final String APP_NAME = "com.sankuai.waimai.e.customer";

    public ShortUrlResultThrift generateShortLink(String longUrl) {
        ShortUrlRequestThrift request = new ShortUrlRequestThrift();
        request.setAppName(APP_NAME);
        request.setBizType(MccConfig.getShortLinkBizType());
        request.setExpireDays(MccConfig.getShortUrlExpireDays());
        request.setOriginUrl(longUrl);
        ShortUrlResultThrift result = operateServiceThrift.genShortLinkWithParam(request);
        log.info("OperateServiceThriftAdapter#generateShortLink, request: {}, result: {}", JSON.toJSONString(request), JSON.toJSONString(result));
        return result;
    }

}
