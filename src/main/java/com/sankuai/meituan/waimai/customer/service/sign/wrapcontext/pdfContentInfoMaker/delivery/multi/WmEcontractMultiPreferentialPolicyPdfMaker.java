package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.multi;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

import com.sankuai.meituan.waimai.customer.bo.sign.PreferentialPolicyBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import lombok.extern.slf4j.Slf4j;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.MULTI_PREFERENTIAL_POLICY)
@Slf4j
@Service
public class WmEcontractMultiPreferentialPolicyPdfMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext)
            throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractMultiPreferentialPolicyPdfMaker");
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.MULTI_PREFERENTIAL_POLICY;

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();

        PreferentialPolicyBo preferentialPolicyBo = null;
        for (EcontractDeliveryInfoBo temp : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            if(SUPPORT_MARK.equals(temp.getSupportExclusive())){
                preferentialPolicyBo = PreferentialPolicyBo.extract(temp);
                pdfBizContent.add(MapUtil.Object2Map(preferentialPolicyBo));
            }
        }

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("signTime", org.apache.commons.lang.StringUtils.defaultIfEmpty(signTime, org.apache.commons.lang.StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", org.apache.commons.lang.StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext),
                org.apache.commons.lang.StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        return pdfInfoBo;
    }

}
