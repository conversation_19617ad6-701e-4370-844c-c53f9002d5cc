//package com.sankuai.meituan.waimai.customer.ddd.domain.kp.atom.service.impl;
//
//import com.sankuai.meituan.waimai.customer.ddd.domain.kp.atom.service.IWmCustomerKpAtomService;
//import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
//import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.List;
//@Deprecated
//public class WmCustomerKpAtomService implements IWmCustomerKpAtomService {
//
//    @Autowired
//    private WmCustomerKpService wmCustomerKpService;
//
//    @Override
//    public WmCustomerKp getCustomerKpOfEffectiveSigner(int customerId) {
//
//        return wmCustomerKpService.getCustomerKpOfEffectiveSigner(customerId);
//    }
//
//    @Override
//    public List<WmCustomerKp> getCustomerKpList(int customerId) {
//        return wmCustomerKpService.getCustomerKpList(customerId);
//    }
//
//    @Override
//    public void batchSaveOrUpdateCustomerKp(int customerId, List<WmCustomerKp> wmCustomerKpList, int uid, String uname) throws WmCustomerException, TException {
//        wmCustomerKpService.batchSaveOrUpdateCustomerKp(customerId, wmCustomerKpList, uid, uname);
//    }
//}
