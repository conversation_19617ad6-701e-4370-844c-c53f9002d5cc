package com.sankuai.meituan.waimai.customer.domain.customer;

import lombok.Data;

@Data
public class WmCustomerOwnerApplyRecordQueryBo {
    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 模块ID
     */
    private Integer moduleId;
    /**
     * 操作类型
     */
    private Integer opType;
    /**
     * 创建时间起始
     */
    private Integer createTimeStart;

    /**
     * 操作人ID
     */
    private Integer opUid;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 创建时间结束
     */
    private Integer createTimeEnd;

    /**
     * 申请单ID
     */
    private Integer applyId;

    private int offset;
    private int pageSize = 20;
    private int maxId;
}
