package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementModel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public interface WmAgreementModelDBMapper {

    WmAgreementModel selectByPrimaryKey(Integer id);

    List<WmAgreementModel> selectAgreementByIds(@Param("set") Set<Integer> set);

    int insertSelective(WmAgreementModel record);

    WmAgreementModel selectByAgreementTypeAndVersion(@Param("type") Integer type,@Param("version") Integer version);

    WmAgreementModel selectByAgreementTypeAndVersionMaster(@Param("type") Integer type,@Param("version") Integer version);

    List<WmAgreementModel> queryAgreementModelPage(@Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

}