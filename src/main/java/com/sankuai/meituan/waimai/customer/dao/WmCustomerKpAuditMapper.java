package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAuditVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmCustomerKpAuditMapper {

    int deleteByPrimaryKey(Integer id);

    int deleteByKpId(Integer id);

    int deleteByKpIdList(@Param("idList") List<Integer> idList);

    int insert(WmCustomerKpAudit record);

    WmCustomerKpAudit selectByPrimaryKey(Integer id);

    WmCustomerKpAudit selectByPrimaryKeyRT(Integer id);

    WmCustomerKpAudit selectByCommitId(Integer commitId);

    List<WmCustomerKpAudit> selectByCommitIdAndType(@Param("commitId") Integer commitId, @Param("type") Byte type);

    List<WmCustomerKpAudit> selectByKpIdListAndType(@Param("kpIdList") List<Integer> kpIdList, @Param("type") Byte type);

    @Select("select id, kp_id as kpId, type, commit_id as commitId, result, uid, uname, valid, ctime, utime from wm_customer_kp_audit where kp_id = #{kpId} and type=#{type} and valid=1")
    WmCustomerKpAudit selectByKpIdAndType(@Param("kpId") Integer kpId, @Param("type") Integer type);

    int updateByPrimaryKey(WmCustomerKpAudit record);


    List<WmCustomerKpAudit> selectByKpId(@Param("kpId") Integer kpId);

    List<WmCustomerKpAudit> selectByNoResult(WmCustomerKpAuditVo wmCustomerKpAuditVo);

   int invalidKpAudit(@Param("kpId") Integer kpId);

    /**
     * 根据KpId查询审核中的记录
     * @param kpId
     * @return
     */
    WmCustomerKpAudit selectByKpIdFromMaster(@Param("kpId") Integer kpId);

    /**
     * 更新商家账号
     * @param id
     * @param acctId
     * @return
     */
    int updateAcctIdByPrimaryKey(@Param("id") Integer id, @Param("acctId") Integer acctId);

    /**
     * 失效审核记录并更新结果
     * @param id
     * @param result
     * @return
     */
    int invalidAndSetResult(@Param("id") Integer id, @Param("result") String result);

    /**
     * 根据KPID查询最近最近一条短信授权中任务
     *
     * @param kpId
     * @return
     */
    WmCustomerKpAudit getLatestLegalAuthTaskByKpId(@Param("kpId") Integer kpId);

    /**
     * 计算KP发起提审代理人提审次数-统计所有次数
     *
     * @param kpId
     * @return
     */
    int cntKpAuditAgentRecord(@Param("kpId") Integer kpId);

    /**
     * 根据KPID查询所有提审记录
     *
     * @param kpId
     * @return
     */
    List<WmCustomerKpAudit> listKpAuditByKpId(@Param("kpId") Integer kpId);
}
