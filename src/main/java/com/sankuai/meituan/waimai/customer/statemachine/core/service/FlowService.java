package com.sankuai.meituan.waimai.customer.statemachine.core.service;


import com.sankuai.meituan.waimai.customer.statemachine.api.FlowTrait;
import com.sankuai.meituan.waimai.customer.statemachine.api.OperationInfo;
import com.sankuai.meituan.waimai.customer.statemachine.core.exceptions.TargetStateUnreachableException;
import com.sankuai.meituan.waimai.customer.statemachine.core.flowpath.FlowPathNavigator;
import com.sankuai.meituan.waimai.customer.statemachine.spi.store.FlowStoreManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 流程动作执行服务实现类.
 * <p>
 * Created by jinh<PERSON> on 16/8/9.
 */
public class FlowService<F extends FlowTrait> {

    private static final Logger logger = LoggerFactory.getLogger(FlowService.class);
    private FlowStoreManager flowStoreManager;
    private FlowPathNavigator flowPathNavigator;
    private TransactionTemplate transactionTemplate;

    public FlowService(FlowStoreManager flowStoreManager, FlowPathNavigator flowPathNavigator,
                       DataSourceTransactionManager transactionManager) {
        this.flowStoreManager = flowStoreManager;
        this.flowPathNavigator = flowPathNavigator;
        this.transactionTemplate = new TransactionTemplate(transactionManager);
    }

    public F changeState(final FlowTrait flow, final String targetState, final OperationInfo operationInfo) {

        if (!flowPathNavigator.hasPath(flow, targetState)) {
            throw new TargetStateUnreachableException("FlowId:" + flow.getFlowId() + ",状态" + flow.currentState() + "不可到达状态" + targetState);
        }
        flow.changeState(targetState);
        // 是否有事务管理
        if (null == transactionTemplate.getTransactionManager()) {
            flowStoreManager.updateFlow(flow, operationInfo);
        } else {
            this.transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_NESTED);
            this.transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                @Override
                protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
                    try {
                        flowStoreManager.updateFlow(flow, operationInfo);
                    } catch (Exception e) {
                        logger.error("调用更新状态服务失败", e);
                        transactionStatus.setRollbackOnly();
                        throw new RuntimeException(e.getMessage(), e);
                    }
                }
            });
        }
        return (F) flow;

    }

    public F changeState(final String flowId, final String targetState, final OperationInfo operationInfo) {
        FlowTrait flow = flowStoreManager.queryFlow(flowId);
        return changeState(flow, targetState, operationInfo);
    }

    public boolean canFlow(final String flowId, final String targetState) {
        FlowTrait flow = flowStoreManager.queryFlow(flowId);
        return flowPathNavigator.hasPath(flow, targetState);
    }

    public boolean canFlow(final FlowTrait flow, final String targetState) {
        return flowPathNavigator.hasPath(flow, targetState);
    }
}
