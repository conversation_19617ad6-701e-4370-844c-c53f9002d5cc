package com.sankuai.meituan.waimai.customer.constant.customer;

import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 美食城客户资质子类型与门店资质系统枚举映射关系
 */
@AllArgsConstructor
@Getter
public enum QuaSecondSubTypeAndQuaTypeRelEnum {

    BUSINESS(0,QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE,"营业执照"),
    ID_CARD(CertTypeEnum.ID_CARD.getType(), QuaTypeEnum.QUA_SUBTYPE_CID,"身份证"),
    ID_CARD_TEMP(CertTypeEnum.ID_CARD_TEMP.getType(), QuaTypeEnum.QUA_SUBTYPE_TEMPCARD,"临时身份证"),
    ID_CARD_COPY(CertTypeEnum.ID_CARD_COPY.getType(), QuaTypeEnum.QUA_SUBTYPE_COPYCARD, "身份证复印件"),
    DRIVING_LICENCE(CertTypeEnum.DRIVING_LICENCE.getType(), QuaTypeEnum.QUA_SUBTYPE_DRIVINGLICENCE, "驾驶证"),
    PASSPORT(CertTypeEnum.PASSPORT.getType(), QuaTypeEnum.QUA_SUBTYPE_PASSPORT, "护照"),
    HK_MACAO_REENTRY_PERMIT(CertTypeEnum.HK_MACAO_REENTRY_PERMIT.getType(), QuaTypeEnum.QUA_SUBTYPE_REENTRYPERMIT, "港澳居民往来大陆通行证/回乡证"),
    TAIWAN_REENTRY_PERMIT(CertTypeEnum.TAIWAN_REENTRY_PERMIT.getType(), QuaTypeEnum.QUA_SUBTYPE_MTPS, "台湾居民往来大陆通行证/回乡证"),
    REDIDENCE_PERMIT_OF_HK_MACAO_TAIWAI(CertTypeEnum.REDIDENCE_PERMIT_OF_HK_MACAO_TAIWAI.getType(), QuaTypeEnum.QUA_SUBTYPE_RESIDENCE_PERMIT_OF_HONGKONG_AND_MACAO_RESIDENT,"港澳台居民居住证");



    private final int customerSecondType;

    private final QuaTypeEnum quaTypeEnum;

    private final String desc;

    public static QuaSecondSubTypeAndQuaTypeRelEnum getByCustomerSecondType(int customerSecondType) {
        for (QuaSecondSubTypeAndQuaTypeRelEnum value : values()) {
            if (value.getCustomerSecondType() == customerSecondType) {
                return value;
            }
        }
        return null;
    }





}
