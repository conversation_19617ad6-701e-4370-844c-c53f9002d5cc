package com.sankuai.meituan.waimai.customer.service.sc.poi;

import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 食堂门店绑定/换绑任务服务
 */
@Slf4j
@Service
public class WmScCanteenPoiTaskService {

    @Autowired
    WmCanteenMapper wmCanteenMapper;

    public CanteenAuditProgressEnum getCanPoiAuditProgressByOpUser(int userId, boolean isSuperAdmin, int canteenIdTo) throws WmSchCantException {
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenIdTo);
        if (wmCanteenDB == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂信息不存在");
        }

        if (isSuperAdmin) {
            switch (CanteenAttributeEnum.getByType(wmCanteenDB.getCanteenType())) {
                case SCHOOL_DIRECT:
                case SINGLE_CANTEEN:
                    return CanteenAuditProgressEnum.SUPER_MANAGER_SINGLE_OR_DIRECT_PROGRESS;
                case CONTRACTOR:
                    return CanteenAuditProgressEnum.SUPER_MANAGER_CONTRACTOR_PROGRESS;
                default:
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "审核流程不存在");
            }
        }

        if (userId <= 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "用户ID不存在");
        }

        switch (CanteenAttributeEnum.getByType(wmCanteenDB.getCanteenType())) {
            case SCHOOL_DIRECT:
            case SINGLE_CANTEEN:
                return CanteenAuditProgressEnum.BD_SINGLE_OR_DIRECT_PROGRESS;
            case CONTRACTOR:
                return CanteenAuditProgressEnum.BD_CONTRACTOR_PROGRESS;
            default:
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "审核流程不存在");
        }

    }
}
