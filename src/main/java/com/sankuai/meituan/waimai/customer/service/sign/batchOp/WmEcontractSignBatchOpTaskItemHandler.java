package com.sankuai.meituan.waimai.customer.service.sign.batchOp;

import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.sankuai.meituan.auth.service.UpmService;
import com.sankuai.meituan.auth.vo.RoleOrg;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigTaskParseService;
import com.sankuai.meituan.waimai.customer.util.base.EcontractBatchOpResponseUtil;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskDBMapper;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchOpStageEnum;

import javax.annotation.Nullable;

/**
 * @description: 批量操作相关逻辑-普通任务
 * @author: lixuepebg
 * @create: 2021-05-24
 **/
@Service
public class WmEcontractSignBatchOpTaskItemHandler implements WmEcontractSignBatchOpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractSignBatchOpTaskItemHandler.class);

    public static final String TASK_TYPE = "task";

    public static final String FAIL_TIPS = "任务%s，执行失败，失败原因：";

    @Autowired
    private WmEcontractTaskDBMapper wmEcontractTaskDBMapper;
    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;
    @Autowired
    private UpmService upmService;
    @Autowired
    private WmContractService wmContractService;
    @Autowired
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;


    /**
     * 任务种类(batchIdType)-打包签约任务、普通任务、待手动打包任务
     */
    public String getBatchIdType() {
        return TASK_TYPE;
    }

    /**
     * 根据查询条件(@see EcontractBatchOpRequest)查询任务id列表
     */
    public List<Long> getItemListByOpRequest(EcontractBatchOpRequest opRequest) {
        //全部或者进行中的任务才进行查询
        if (EcontractBatchOpStageEnum.findByValue(opRequest.getOpStage()) == EcontractBatchOpStageEnum.ALL
                || EcontractBatchOpStageEnum.findByValue(opRequest.getOpStage()) == EcontractBatchOpStageEnum.PROCESSING) {
            return queryTaskItemIdList(opRequest);
        }
        return new ArrayList<>();
    }

    /**
     * 重发短信-返回失败列表
     */
    public List<EcontractBatchOpFailTaskDTO> handleResendMsg(EcontractBatchOpRequest opRequest,
                                                             ListeningExecutorService executorService, List<Long> itemList) throws InterruptedException {
        List<EcontractBatchOpFailTaskDTO> failTaskDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            CountDownLatch latch = new CountDownLatch(itemList.size());
            for (Long item : itemList) {
                ListenableFuture batchItemFuture = executorService.submit(new Callable() {
                    @Override
                    public Object call() throws Exception {
                        checkCustomerTaskMatch((int) opRequest.getCustomerId(), item, opRequest.getOpUid());
                        return wmEcontractSignBzService.resendMsg(item);
                    }
                });
                Futures.addCallback(batchItemFuture, new FutureCallback() {
                    @Override
                    public void onSuccess(@Nullable Object result) {//成功情况下不做处理
                        latch.countDown();
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        EcontractBatchOpFailTaskDTO failTaskDTO = new EcontractBatchOpFailTaskDTO();
                        failTaskDTO.setTaskId(item);
                        failTaskDTO.setFailMsg(String.format(FAIL_TIPS, item) + t.getMessage());
                        failTaskDTOList.add(failTaskDTO);
                        latch.countDown();
                    }
                });
            }
            latch.await();
        }
        return failTaskDTOList;
    }

    /**
     * 取消任务-返回失败列表
     */
    public List<EcontractBatchOpFailTaskDTO> handleCancel(EcontractBatchOpRequest opRequest,
                                                          ListeningExecutorService executorService, List<Long> itemList) throws InterruptedException {
        List<EcontractBatchOpFailTaskDTO> failTaskDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            CountDownLatch latch = new CountDownLatch(itemList.size());
            for (Long item : itemList) {
                ListenableFuture batchItemFuture = executorService.submit(new Callable() {
                    @Override
                    public Object call() throws Exception {
                        checkCustomerTaskMatch((int) opRequest.getCustomerId(), item, opRequest.getOpUid());
                        return wmEcontractSignBzService.cancelSign(item, WmEcontractBatchConstant.TASK_MANAGE_TASK_LIST);
                    }
                });
                Futures.addCallback(batchItemFuture, new FutureCallback() {
                    @Override
                    public void onSuccess(@Nullable Object result) {//成功情况下不做处理
                        latch.countDown();
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        EcontractBatchOpFailTaskDTO failTaskDTO = new EcontractBatchOpFailTaskDTO();
                        failTaskDTO.setTaskId(item);
                        failTaskDTO.setFailMsg(String.format(FAIL_TIPS, item) + t.getMessage());
                        failTaskDTOList.add(failTaskDTO);
                        latch.countDown();
                    }
                });
            }
            latch.await();
        }
        return failTaskDTOList;
    }

    /**
     * 手动打包签约-返回失败列表
     * <p>
     * 此类型任务不能发起手动打包签约
     */
    public EcontractBatchOpResponse handleManualPack(EcontractBatchOpRequest opRequest,
                                                     ListeningExecutorService executorService, List<Long> itemList) throws WmCustomerException, TException {
        EcontractBatchOpResponse opResponse = new EcontractBatchOpResponse();
        List<EcontractBatchOpFailTaskDTO> failTaskDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            for (Long item : itemList) {
                EcontractBatchOpFailTaskDTO failTaskDTO = new EcontractBatchOpFailTaskDTO();
                failTaskDTO.setTaskId(item);
                failTaskDTO.setFailMsg(String.format(FAIL_TIPS, item) + "该类型任务不能发起手动打包签约");
                failTaskDTOList.add(failTaskDTO);
            }
        }
        opResponse.setFailTaskDTOs(failTaskDTOList);
        return opResponse;
    }

    private List<Long> queryTaskItemIdList(EcontractBatchOpRequest request) {//获取进行中状态任务
        List<Long> taskIds = new ArrayList<>();
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractBigTaskParseService.
                queryTaskItemByParamWithLabel(request, 0, MccConfig.batchOpQueryPageSize());
        while (CollectionUtils.isNotEmpty(taskDBList)) {
            for (WmEcontractSignTaskDB taskDB : taskDBList) {
                if (!filterTaskItem(request, taskDB)) {
                    continue;
                }
                taskIds.add(taskDB.getId());
            }
            taskDBList = wmEcontractBigTaskParseService.queryTaskItemByParamWithLabel(request,
                    taskDBList.get(taskDBList.size() - 1).getId(), MccConfig.batchOpQueryPageSize());
        }
        LOGGER.info("queryBatchItemIdList request:{}, batchDBList:{}", JSON.toJSONString(request), taskDBList);
        return taskIds;
    }

    /**
     * 创建待打包任务
     *
     * @param opRequest
     * @return
     * @throws WmCustomerException
     * @throws TException          此类型任务不能创建手动待打包签约
     */
    @Override
    public EcontractBatchOpResponse handleCreatePreManualTask(EcontractBatchOpRequest opRequest) throws WmCustomerException, TException {
        EcontractBatchOpResponse opResponse = new EcontractBatchOpResponse();
        List<EcontractBatchOpFailTaskDTO> failTaskDTOList = new ArrayList<>();
        EcontractBatchOpFailTaskDTO failTaskDTO = new EcontractBatchOpFailTaskDTO();
        failTaskDTO.setFailMsg("该类型任务不能创建手动待打包任务");
        failTaskDTOList.add(failTaskDTO);
        opResponse.setFailTaskDTOs(failTaskDTOList);
        return opResponse;
    }

    @Override
    public EcontractBatchOpResponse handleCreateStartSignTask(EcontractBatchOpRequest opRequest) throws WmCustomerException, TException {
        return EcontractBatchOpResponseUtil.createFailResponseWithEmptyFailTaskList("该类型任务不能发起一键发起签约");
    }

    private boolean filterTaskItem(EcontractBatchOpRequest request, WmEcontractSignTaskDB taskDB) {
        Set<EcontractTaskApplyTypeEnum> checkTaskType = WmEcontractBatchConstant.TASK_TYPE_MAP.get(request.getTaskType());
        if (checkTaskType == null) {
            return false;
        }
        if (!checkTaskType.contains(EcontractTaskApplyTypeEnum.getByName(taskDB.getApplyType()))) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(request.getWmPoiIdList())) {//门店id不为空
            List<Long> wmPoiIds = parseWmPoiIdListFromWmEcontractSignTaskBo(taskDB);//任务对应的门店id
            if (CollectionUtils.isEmpty(wmPoiIds) ||
                    !WmEcontractBatchConstant.NEED_CHECK_POI_INFO_TASK_TYPE_SET.contains(EcontractTaskApplyTypeEnum.getByName(taskDB.getApplyType()))) {
                return false;
            }
            if (Collections.disjoint(request.getWmPoiIdList(), wmPoiIds)) { //无交集
                return false;
            }
        }
        return true;
    }

    /**
     * 从taskBo中获取门店ID
     */
    private List<Long> parseWmPoiIdListFromWmEcontractSignTaskBo(WmEcontractSignTaskDB taskDB) {
        List<Long> result = Lists.newArrayList();
        if (taskDB == null) {
            return result;
        }
        String applyContext = taskDB.getApplyContext();
        if (taskDB.getApplyType().equals(EcontractTaskApplyTypeEnum.SETTLE.getName())) {
            List<EcontractSettleInfoBo> econtractSettleInfoBos = JSONArray.parseArray(applyContext, EcontractSettleInfoBo.class);
            if (CollectionUtils.isNotEmpty(econtractSettleInfoBos) &&
                    CollectionUtils.isNotEmpty(econtractSettleInfoBos.get(0).getPoiInfoBoList())) {
                return Lists.newArrayList(Lists.transform(econtractSettleInfoBos.get(0).getPoiInfoBoList(),
                        new Function<EcontractPoiInfoBo, Long>() {
                            @Nullable
                            @Override
                            public Long apply(@Nullable EcontractPoiInfoBo input) {
                                return input.getWmPoiId().longValue();
                            }
                        }));
            }
        } else if (taskDB.getApplyType().equals(EcontractTaskApplyTypeEnum.POIFEE.getName())) {
            if (taskDB.getBizId() > 0) {
                return Lists.newArrayList((long) taskDB.getBizId());
            }
        } else if (taskDB.getApplyType().equals(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName())) {
            EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = JSONObject.parseObject(applyContext, EcontractBatchDeliveryInfoBo.class);
            if (econtractBatchDeliveryInfoBo != null) {
                return Lists.newArrayList(econtractBatchDeliveryInfoBo.getWmPoiIdList());
            }
        }
        return result;
    }

    private void checkCustomerTaskMatch(int wmCustomerId, long taskId, int opUid) throws WmCustomerException, TException {
        BooleanResult result = wmEcontractSignBzService.checkQuaRealLetterTaskMatch(taskId);
        if (result.isRes()) {
            checkQuaRealLetterManagerPermission(opUid);
        }

        BooleanResult checkResult = wmEcontractSignBzService.checkCustomerTaskMatch(wmCustomerId, taskId);
        if (!checkResult.isRes()) {
            throw new WmCustomerException(WmContractErrorCodeConstant.AUTHORIZING, "信息不匹配");
        }
    }

    /**
     * 校验资质属实商家承诺函权限
     *
     * @param opUid
     * @throws WmCustomerException
     */
    public void checkQuaRealLetterManagerPermission(int opUid) throws WmCustomerException {
        if (!WmEmployUtils.isHQ(opUid) && checkUpmRole(opUid, "资质属实商家承诺函管理员")) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN, "您没有操作权限");
        }
    }

    /**
     * 校验UPM权限
     * @param user
     * @param roleName
     * @return
     */
    /**
     * 校验UPM权限
     *
     * @param opUid
     * @param roleName
     * @return
     */
    public boolean checkUpmRole(int opUid, String roleName) {
        try {
            List<RoleOrg> roleOrgs = upmService.getRoles("xianfu_waimai", opUid);
            if (StringUtils.isNotEmpty(roleName) && CollectionUtils.isNotEmpty(roleOrgs)) {
                for (RoleOrg roleOrg : roleOrgs) {
                    if (roleName.equals(roleOrg.getRoleName())) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("调用checkUpmRole异常", e);
        }
        return false;
    }
}
