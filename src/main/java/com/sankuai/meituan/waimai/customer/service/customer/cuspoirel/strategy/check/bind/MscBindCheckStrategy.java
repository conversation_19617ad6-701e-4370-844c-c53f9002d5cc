package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.bind;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.adapter.BaseInfoQueryPhysicalPoiThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmOrgClient;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindCheckResultEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindCheckBO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.WmPhysicalPoiRel;
import com.sankuai.meituan.waimai.qualification.WmQualificationThriftService;
import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskDetailSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskOpSystemEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerMscUsedPoiDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiQualificationInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants.RESULT_CODE_ERROR;

/**
 * <AUTHOR>
 * @date 20240115
 * @desc 美食城客户类型绑定校验
 */
@Slf4j
@Service
public class MscBindCheckStrategy implements IBindCheckStrategy {

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private BaseInfoQueryPhysicalPoiThriftServiceAdapter baseInfoQueryPhysicalPoiThriftServiceAdapter;

    @Autowired
    private WmCustomerNewSettleService wmCustomerNewSettleService;

    @Autowired
    private WmOrgClient wmOrgClient;

    @Autowired
    protected WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private PoiBindBaseCheck poiBindBaseCheck;

    @Autowired
    public WmQualificationThriftService.Iface wmQuaThriftService;

    @Autowired
    private MscCustomerCheckService mscCustomerCheckService;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerService wmCustomerService;

    private static final String BD_BIND_MSC_POI_CHECK_TIPS_MULTI = "含上单中非子门店%s等，您无法绑定此美食城客户。后续请引导美食城商家通过自入驻上单。";

    private static final String BD_BIND_MSC_POI_CHECK_TIPS_SINGLE = "含上单中非子门店%s，您无法绑定此美食城客户。后续请引导美食城商家通过自入驻上单。";


    protected static final Set<String> WM_POI_FIELDS = Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_TAG,
            WmPoiFieldQueryConstant.WM_POI_FIELD_POI_BIZ_TYPES,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID
    );

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("CUSTOMER_POI_MSC_POOL_%d").build();

    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(30, 50, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(20000), THREAD_FACTORY, new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));

    @Override
    public void checkBindByParams(CustomerPoiBindFlowContext context) throws WmCustomerException {
        try {
            CustomerPoiBindCheckBO bindCheckBO = buildCustomerPoiCheckBo(context);
            //校验美食城客户是否可以绑定门店
            checkCustomerBindPoi(bindCheckBO);
        } catch (WmCustomerException e) {
            log.warn("MscBindCheckStrategy,校验发生业务异常,context={}", JSON.toJSONString(context), e);
            throw e;
        } catch (Exception e) {
            log.error("MscBindCheckStrategy,校验发生异常,context={}", JSON.toJSONString(context), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
    }


    @Override
    public boolean hitCheck(Integer customerRealType) {
        return CustomerRealTypeEnum.MEISHICHENG.getValue() == customerRealType;
    }

    private CustomerPoiBindCheckBO buildCustomerPoiCheckBo(CustomerPoiBindFlowContext context) {
        CustomerPoiBindCheckBO customerPoiBindCheckBO = new CustomerPoiBindCheckBO();
        customerPoiBindCheckBO.setWmCustomerDB(context.getWmCustomerDB());
        customerPoiBindCheckBO.setOpSource(context.getOpSource());
        customerPoiBindCheckBO.setOpSourceDetail(context.getOpSourceDetail());
        customerPoiBindCheckBO.setOpUid(context.getOpUid());
        customerPoiBindCheckBO.setOpSystem(context.getOpSysName());
        customerPoiBindCheckBO.setCheckPoiVersion(context.isCheckPoiVersion());
        customerPoiBindCheckBO.setWmPoiIdList(context.getWmPoiIdSet());

        return customerPoiBindCheckBO;
    }

    /**
     * 校验美食城客户是否可以绑定门店
     *
     * @param customerPoiBindCheckBO
     * @throws WmCustomerException
     * @throws TException
     */
    private void checkCustomerBindPoi(CustomerPoiBindCheckBO customerPoiBindCheckBO) throws WmCustomerException, TException {
        WmCustomerDB wmCustomerDB = customerPoiBindCheckBO.getWmCustomerDB();
        List<Long> wmPoiIdList = Lists.newArrayList(customerPoiBindCheckBO.getWmPoiIdList());

        //客户是否有资质共用标
        boolean hasQuaComCustomerTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
        if (!hasQuaComCustomerTag) {
            //未命中灰度则需要校验档口数规则
            if (!wmCustomerGrayService.checkHitMscCustomerUsedPoiGray()) {
                //美食城客户档口数校验
                checkMscCustomerPoiCnt(wmCustomerDB, wmPoiIdList);
            }
        }

        // 基础校验
        poiBindBaseCheck.checkBind(customerPoiBindCheckBO);
        // 重新发起绑定校验
        if (customerPoiBindCheckBO.getOpSourceDetail().equals(CustomerTaskDetailSourceEnum.RE_START_BIND.getDesc())) {
            checkReStartBind(wmCustomerDB.getId(), Sets.newHashSet(wmPoiIdList));
        }
        //功能开启 && 绑定操作
        if (MccCustomerConfig.enableBdBindMSCPOICheck()) {
            bdBindMscPoiCheck(customerPoiBindCheckBO, wmPoiIdList);
        }

        // 校验客户是否生效
        checkCustomerEffective(wmCustomerDB);
        // 获取子门店
        Map<Long, Long> subWmPoiIdMap = getSupportSubWmPoiIdMap(customerPoiBindCheckBO.getWmPoiAggreList());
        // 子门店校验
        if (subWmPoiIdMap != null && !subWmPoiIdMap.isEmpty()) {
            log.info("validSub subWmPoiIdMap={}", JSONObject.toJSONString(subWmPoiIdMap));
            validSub(wmCustomerDB, subWmPoiIdMap);
            wmPoiIdList.removeAll(subWmPoiIdMap.keySet());
        }
        // 普通门店校验
        if (CollectionUtils.isNotEmpty(wmPoiIdList)) {
            log.info("validSub wmPoiIdList={}", JSONObject.toJSONString(wmPoiIdList));
            validNormal(wmCustomerDB, wmPoiIdList);
        }
        // 校验客户与门店的新结算版本是否一致
        checkCustomerAndPoiSettleVersion(wmCustomerDB, customerPoiBindCheckBO.getWmPoiIdList(), customerPoiBindCheckBO.getOpSource());
    }

    /**
     * 校验客户与门店的结算版本是否一致
     *
     * @param wmCustomerDB
     * @param wmPoiIdSet
     * @param opSource
     * @throws WmCustomerException
     */
    private void checkCustomerAndPoiSettleVersion(WmCustomerDB wmCustomerDB, Set<Long> wmPoiIdSet, Integer opSource) throws WmCustomerException {
        //真实有效门店ID列表为空则不校验
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        //基本信息发起的校验不执行结算版本不校验
        if (opSource == null || CustomerTaskSourceEnum.POI_BASE_INFO.getCode() == opSource) {
            return;
        }
        // 校验客户与门店的新结算版本是否一致
        wmCustomerNewSettleService.checkCustomerAndPoiVersion(wmCustomerDB.getMtCustomerId(),
                wmCustomerDB.getCustomerName(), wmPoiIdSet);

    }

    private void bdBindMscPoiCheck(CustomerPoiBindCheckBO customerPoiBindCheckBO, List<Long> wmPoiIdList) throws WmCustomerException {
        int uid = customerPoiBindCheckBO.getOpUid();

        //有效操作人id
        if (uid <= 0) {
            log.info("操作人员uid为0，不符合校验条件");
            return;
        }
        //
        CustomerTaskSourceEnum taskSourceEnum = CustomerTaskSourceEnum.of(customerPoiBindCheckBO.getOpSource());
        String opSystem = customerPoiBindCheckBO.getOpSystem();
        String opDetailSource = customerPoiBindCheckBO.getOpSourceDetail();
        //渠道来源 限定BD入驻-系统来源客户web服务 && 	渠道来源为基本信息
        boolean matchBD_WEBFlag = taskSourceEnum == CustomerTaskSourceEnum.BD_SETTLE && CustomerTaskOpSystemEnum.CUSTOMER_WEB.getDesc().equals(opSystem);

        //bd单店入驻标识
        boolean matchBD_SingleFlag = taskSourceEnum == CustomerTaskSourceEnum.BD_SETTLE && opDetailSource.equals(CustomerTaskDetailSourceEnum.BD_SINGLE_SETTLE.getDesc());


        if (!matchBD_WEBFlag && taskSourceEnum != CustomerTaskSourceEnum.POI_BASE_INFO && !matchBD_SingleFlag) {
            return;
        }

        List<Integer> orgIds = wmOrgClient.getSuperiorBySource(uid, WmVirtualOrgSourceEnum.WAIMAI.getSource());
        //华北大区/中南大区/华南大区/华东大区 开启校验
        if (Collections.disjoint(orgIds, MccCustomerConfig.enableBdBindMSCPOIOrgIds())) {
            log.info("操作人员所属机构:{}不符合要求", JSONObject.toJSONString(orgIds));
            return;
        }

        //如果没有开启全量开关
        if (!MccCustomerConfig.enableBdBindMSCPOICheckTotalFlag()) {
            //没有命中组织节点灰度
            if (Collections.disjoint(orgIds, MccCustomerConfig.enableBdBindMSCPOICheckGrayOrgIds())) {
                log.info("操作人员所属机构:{}未命中灰度", JSONObject.toJSONString(orgIds));
                return;
            }
        }
        List<WmPoiAggre> poiAggres = Lists.newArrayList();
        if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
            poiAggres = wmPoiQueryAdapter
                    .getWmPoiAggreList(wmPoiIdList,
                            Sets.newHashSet(WM_POI_FIELD_VALID, WM_POI_FIELD_LABEL_IDS, WM_POI_FIELD_SUB_WM_POI_TYPE))
                    .stream().filter(a -> a.getValid() == 2)
                    .filter(a -> WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(a.getSub_wm_poi_type()))
                    .collect(Collectors.toList());
        } else {
            poiAggres = wmPoiQueryAdapter.getWmPoiAggreList(wmPoiIdList, Sets.newHashSet(WM_POI_FIELD_VALID, WM_POI_FIELD_LABEL_IDS)).stream()
                    .filter(a -> a.getValid() == 2)
                    .filter(a -> notSonPoi(a.getLabel_ids()))
                    .collect(Collectors.toList());
        }
        //绑定的门店中含商家状态为“上单中”的非子门店
        if (CollectionUtils.isNotEmpty(poiAggres)) {
            //提权前十个门店id，按照"、"拼接组成提示语,数量不同返回不同提示语
            List<Long> poiIds = poiAggres.stream().map(WmPoiAggre::getWm_poi_id).limit(10).collect(Collectors.toList());
            String poiIdsStr = poiIds.stream().map(cur -> String.valueOf(cur)).collect(Collectors.joining("、"));
            if (poiAggres.size() < 10) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, String.format(BD_BIND_MSC_POI_CHECK_TIPS_SINGLE, poiIdsStr));
            } else {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, String.format(BD_BIND_MSC_POI_CHECK_TIPS_MULTI, poiIdsStr));
            }
        }
    }

    /**
     * 重新发起绑定校验
     *
     * @param customerId
     * @param wmPoiIdSet
     * @throws WmCustomerException
     */
    private void checkReStartBind(int customerId, Set<Long> wmPoiIdSet) throws WmCustomerException {
        //查询门店预绑定状态
        List<WmCustomerPoiDB> wmCustomerPoiForPreBind = wmCustomerPoiDBMapper.getWmCustomerPoiForPreBind(customerId, Lists.newArrayList(wmPoiIdSet), Lists.newArrayList(CustomerRelationStatusEnum.CONFIRM_BINDING_FAIL.getCode(), CustomerRelationStatusEnum.TO_APPLY_BIND.getCode()));
        if (wmPoiIdSet.size() != wmCustomerPoiForPreBind.size()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, "门店绑定状态异常,无法重新发起预绑定");
        }
        // 校验是否在客户切换流转中
        for (WmCustomerPoiDB temp : wmCustomerPoiForPreBind) {
            if (temp.getSwitchTaskId() != null && temp.getSwitchTaskId() > 0L) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, "门店在客户切换流程中,请在切换系统中操作");
            }
        }

    }

    /**
     * 校验普通门店
     *
     * @param wmCustomerDB
     * @param wmPoiIdList
     * @throws WmCustomerException
     */
    private void validNormal(WmCustomerDB wmCustomerDB, List<Long> wmPoiIdList) throws WmCustomerException {
        // 校验资质
        poiBindBaseCheck.validateQua(wmCustomerDB, wmPoiIdList);
        // 校验是否有已审核美食城标签
        boolean hasAuditedMscLabel = checkAuditedMscLabel(wmCustomerDB);
        if (!hasAuditedMscLabel) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_BINDING_ERROR, "绑定的客户为美食城类型客户，客户没有【已审核美食城】标签，请在目标客户补录信息重新提审");
        }
    }

    /**
     * 校验子门店
     *
     * @param wmCustomerDB
     * @param subWmPoiIdMap
     * @throws TException
     * @throws WmServerException
     * @throws WmCustomerException
     */
    private void validSub(WmCustomerDB wmCustomerDB, Map<Long, Long> subWmPoiIdMap)
            throws WmCustomerException {
        // 校验资质
        List<Long> subWmPoiIdList = Lists.newArrayList(subWmPoiIdMap.keySet());
        Map<CustomerPoiBindCheckResultEnum, List<Long>> validQuaMap = validSubQua(wmCustomerDB, subWmPoiIdList);
        log.info("validSub validQuaMap={}", JSONObject.toJSONString(validQuaMap));
        if (validQuaMap == null || validQuaMap.isEmpty()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_BINDING_ERROR, String.format("子门店资质校验异常，对应门店:%s",
                    StringUtils.join(subWmPoiIdList, "、")));
        }
        List<Long> consistentWmPoiIdList = validQuaMap.get(CustomerPoiBindCheckResultEnum.PASS);
        List<Long> disconsistentWmPoiIdList = validQuaMap.get(CustomerPoiBindCheckResultEnum.UN_PASS);

        List<Long> mainPhysicalWmPoiIdList = getSupportMainPhysicalWmPoiIdList(wmCustomerDB.getId());
        // 资质一致
        if (CollectionUtils.isNotEmpty(consistentWmPoiIdList)) {
            // 检验是否有已审核美食城标签
            boolean hasAuditedMscLabel = checkAuditedMscLabel(wmCustomerDB);
            // 校验是否有主站
            Map<CustomerPoiBindCheckResultEnum, List<Long>> checMainResult = checkBindMinOnLinePoi(consistentWmPoiIdList, subWmPoiIdMap, mainPhysicalWmPoiIdList);
            List<Long> unPassWmPoiIdList = checMainResult.get(CustomerPoiBindCheckResultEnum.UN_PASS);
            //无已审核美食城标签&无主站标签 则不允许绑定
            if (!hasAuditedMscLabel && CollectionUtils.isNotEmpty(unPassWmPoiIdList)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_BINDING_ERROR, String.format("当前美食城无“已审核美食城标”且未找到正常经营主门店，不一致的门店:%s",
                        StringUtils.join(unPassWmPoiIdList, "、")));
            }
        }
        // 资质不一致
        if (CollectionUtils.isNotEmpty(disconsistentWmPoiIdList)) {
            // 校验是否有主站
            Map<CustomerPoiBindCheckResultEnum, List<Long>> checMainResult = checkBindMinOnLinePoi(disconsistentWmPoiIdList, subWmPoiIdMap, mainPhysicalWmPoiIdList);
            List<Long> unPassWmPoiIdList = checMainResult.get(CustomerPoiBindCheckResultEnum.UN_PASS);
            if (CollectionUtils.isNotEmpty(unPassWmPoiIdList)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_BINDING_ERROR, String.format("当前门店资质与美食城客户不符且未找到正常经营主门店，涉及门店:%s",
                        StringUtils.join(unPassWmPoiIdList, "、")));
            }
        }
    }

    /**
     * 判断客户是否生效
     *
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void checkCustomerEffective(WmCustomerDB wmCustomerDB) throws WmCustomerException {
        if (wmCustomerDB.getEffective() == CustomerConstants.UNEFFECT) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, "绑定的客户为美食城类型客户，客户未生效，请在客户信息审核通过生效后再次绑定");
        }
    }

    /**
     * 判断客户是否有已审核美食城标签
     *
     * @param wmCustomerDB
     * @return
     */
    private boolean checkAuditedMscLabel(WmCustomerDB wmCustomerDB) {
        WmPoiLabelRel wmPoiLabelRel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmCustomerDB.getMtCustomerId(), MccCustomerConfig.getAuditedMSCCustomerLabel(), LabelSubjectTypeEnum.CUSTOMER.getCode());
        if (wmPoiLabelRel == null || wmPoiLabelRel.getId() <= 0L) {
            return false;
        }
        return true;
    }

    /**
     * 校验子门店资质
     * 此方法是拼好饭移动餐车子门店的校验逻辑，这个业务目前已暂停
     *
     * @param wmCustomerDB
     * @param wmPoiIdList
     * @return
     * @throws TException
     * @throws WmServerException
     */
    private Map<CustomerPoiBindCheckResultEnum, List<Long>> validSubQua(WmCustomerDB wmCustomerDB, List<Long> wmPoiIdList) {
        Map<CustomerPoiBindCheckResultEnum, List<Long>> checkMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return checkMap;
        }
        String customerNumber = wmCustomerDB.getCustomerNumber();
        //资质类型
        int quaType = wmCustomerDB.getCustomerType();
        int quaSecendType = wmCustomerDB.getCustomerSecondType();
        log.info("validSubQua 资质编号重复校验：customerNumber={},quaType={},quaSecendType={}", customerNumber, quaType, quaSecendType);

        Set<Long> failWmPoiIds = Sets.newHashSet();
        Set<Long> succWmPoiIds = Sets.newHashSet(wmPoiIdList);
        List<List<Long>> allPoiIds = Lists.partition(wmPoiIdList, MccConfig.checkQuaPageSize());
        CountDownLatch countDownLatch = new CountDownLatch(allPoiIds.size());
        for (final List<Long> wmPoiIs : allPoiIds) {
            try {
                executorService.execute(new TraceRunnable(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Map<Long, List<WmPoiQualificationInfoBo>> resultMap = wmQuaThriftService.getWmPoiQualificationInfosByWmPoiIds(wmPoiIs);
                            log.info("validSubQua wmPoiIs={},resultMap={}", JSONObject.toJSONString(wmPoiIs), JSONObject.toJSONString(resultMap));
                            for (Map.Entry<Long, List<WmPoiQualificationInfoBo>> map : resultMap.entrySet()) {
                                Long wmPoiId = map.getKey();
                                for (WmPoiQualificationInfoBo wmPoiAuditObjectBo : map.getValue()) {
                                    if (wmPoiAuditObjectBo.getType() == QuaTypeEnum.QUA_SUBTYPE_CID.getSubType()
                                            && wmPoiAuditObjectBo.getSecondSubType() == QuaTypeEnum.QUA_SUBTYPE_CID.getSecondSubType()
                                            && (quaType == CustomerType.CUSTOMER_TYPE_IDCARD.getCode() && quaSecendType == CertTypeEnum.ID_CARD.getType())) {
                                        //个人身份证
                                        if (!wmPoiAuditObjectBo.getNumber().equals(customerNumber)) {
                                            failWmPoiIds.add(wmPoiId);
                                        }
                                    } else if (wmPoiAuditObjectBo.getType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSubType()
                                            && wmPoiAuditObjectBo.getSecondSubType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSecondSubType()
                                            && quaType == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                                        //营业执照
                                        if (!wmPoiAuditObjectBo.getNumber().equals(customerNumber)) {
                                            failWmPoiIds.add(wmPoiId);
                                        }
                                    }
                                }
                            }
                        } catch (WmServerException e) {
                            log.warn("wmQuaThriftService.getUpQua wmPoiIs:{} ", JSONObject.toJSONString(wmPoiIs), e);
                        } catch (TException e) {
                            log.warn("wmQuaThriftService.getUpQua wmPoiIs:{} ", JSONObject.toJSONString(wmPoiIs), e);
                        } finally {
                            countDownLatch.countDown();
                        }
                    }
                }));
            } catch (Exception e) {
                log.error("validateQua 多线程执行异常，wmPoiIdList={} ", JSONObject.toJSONString(wmPoiIdList), e);
            }
        }

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("validSubQua 多线程执行超时，wmPoiIdList={} ", JSONObject.toJSONString(wmPoiIdList), e);
        } finally {
            log.info("validSubQua #end");
        }
        if (CollectionUtils.isNotEmpty(failWmPoiIds)) {
            checkMap.put(CustomerPoiBindCheckResultEnum.UN_PASS, Lists.newArrayList(failWmPoiIds));
            succWmPoiIds.removeAll(failWmPoiIds);
        }
        if (CollectionUtils.isNotEmpty(succWmPoiIds)) {
            checkMap.put(CustomerPoiBindCheckResultEnum.PASS, Lists.newArrayList(succWmPoiIds));
        }
        return checkMap;
    }

    /**
     * 判断该美食城下是否有与此待绑定门店的“物理门店”一致、且关联状态为“已绑定”、且状态为“上线”的、、且有“主门店”标的门店
     *
     * @param subMap
     * @param physicalWmPoiIdList
     * @return
     */
    private Map<CustomerPoiBindCheckResultEnum, List<Long>> checkBindMinOnLinePoi(List<Long> subWmPoiIdList, Map<Long, Long> subMap, List<Long> physicalWmPoiIdList) {
        Map<CustomerPoiBindCheckResultEnum, List<Long>> map = Maps.newHashMap();
        if (subMap == null || subMap.isEmpty()) {
            return map;
        }
        for (Long wmPoiId : subWmPoiIdList) {
            Long physicalWmPoiId = subMap.get(wmPoiId);
            CustomerPoiBindCheckResultEnum checkResultEnum = CustomerPoiBindCheckResultEnum.UN_PASS;
            if (physicalWmPoiId != null && CollectionUtils.isNotEmpty(physicalWmPoiIdList) && physicalWmPoiIdList.contains(physicalWmPoiId)) {
                checkResultEnum = CustomerPoiBindCheckResultEnum.PASS;
            }

            if (map.get(checkResultEnum) == null) {
                map.put(checkResultEnum, Lists.newArrayList(wmPoiId));
            } else {
                map.get(checkResultEnum).add(wmPoiId);
            }
        }
        return map;
    }

    /**
     * 获取子门店
     *
     * @param wmPoiAggreList
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private Map<Long, Long> getSupportSubWmPoiIdMap(List<WmPoiAggre> wmPoiAggreList) throws WmCustomerException {
        Map<Long, Long> map = Maps.newHashMap();
        // 获取美食城支持的子门店标签
        Map<String, Integer> mscSupportPoiLabels = MccCustomerConfig.getMscSupportPoiLabels();
        if (mscSupportPoiLabels == null || mscSupportPoiLabels.isEmpty()) {
            return map;
        }
        List<Integer> labels = mscSupportPoiLabels.keySet().stream().map(x -> Integer.valueOf(x)).collect(Collectors.toList());

        // 获取美食城子门店校验支持的物理城市
        List<Integer> mscGrayCity = MccCustomerConfig.getMscPoiBindCustomerNewGrayCity();
        List<Long> subWmPoiIdList = Lists.newArrayList();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            if (StringUtils.isBlank(wmPoiAggre.getLabel_ids())) {
                continue;
            }
            List<Integer> tagIds = Arrays.stream(wmPoiAggre.getLabel_ids().split(","))
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            // 判断是否命中子门店标签且命中灰度城市
            if (!Collections.disjoint(tagIds, labels) && (CollectionUtils.isEmpty(mscGrayCity) || mscGrayCity.contains(wmPoiAggre.getCity_location_id()))) {
                subWmPoiIdList.add(wmPoiAggre.getWm_poi_id());
            }
        }
        if (CollectionUtils.isEmpty(subWmPoiIdList)) {
            return map;
        }
        return getWmPoiMapPhysicalPoi(subWmPoiIdList);
    }

    /**
     * 获取当前客户已绑定的主门店
     *
     * @param customerId
     * @return
     */
    private List<Long> getSupportMainPhysicalWmPoiIdList(Integer customerId) throws WmCustomerException {
        List<Long> physicalWmPoiIdList = Lists.newArrayList();
        List<Long> wmPoiIdList = wmCustomerPoiRelService.selectWmPoiIdsByCustomerId(customerId);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return physicalWmPoiIdList;
        }
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, WM_POI_FIELDS);
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            return physicalWmPoiIdList;
        }
        Map<String, Integer> mscSupportPoiLabels = MccCustomerConfig.getMscSupportPoiLabels();
        if (mscSupportPoiLabels == null || mscSupportPoiLabels.isEmpty()) {
            return physicalWmPoiIdList;
        }
        List<Integer> labels = mscSupportPoiLabels.values().stream().collect(Collectors.toList());
        List<Long> mainWmPoiIdList = Lists.newArrayList();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            if (wmPoiAggre.getValid() != WmPoiValidEnum.ONLINE.getValue()) {
                continue;
            }
            if (StringUtils.isBlank(wmPoiAggre.getLabel_ids())) {
                continue;
            }
            List<Integer> tagIds = Arrays.stream(wmPoiAggre.getLabel_ids().split(","))
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            if (!Collections.disjoint(tagIds, labels)) {
                mainWmPoiIdList.add(wmPoiAggre.getWm_poi_id());
            }
        }
        if (CollectionUtils.isEmpty(mainWmPoiIdList)) {
            return physicalWmPoiIdList;
        }
        Map<Long, Long> map = getWmPoiMapPhysicalPoi(mainWmPoiIdList);
        if (map == null || map.isEmpty()) {
            return physicalWmPoiIdList;
        } else {
            return Lists.newArrayList(map.values());
        }
    }

    /**
     * 获取门店对应的物理门店
     *
     * @param wmPoiIdList
     * @return
     */
    private Map<Long, Long> getWmPoiMapPhysicalPoi(List<Long> wmPoiIdList) throws WmCustomerException {
        Map<Long, Long> map = Maps.newHashMap();
        // 获取门店的物理门店
        List<WmPhysicalPoiRel> wmPhysicalPoiRelList = baseInfoQueryPhysicalPoiThriftServiceAdapter.getPhysicalPoiRelList(wmPoiIdList);
        if (CollectionUtils.isEmpty(wmPhysicalPoiRelList)) {
            return map;
        }
        for (WmPhysicalPoiRel wmPhysicalPoiRel : wmPhysicalPoiRelList) {
            map.put(wmPhysicalPoiRel.getWmPoiId(), wmPhysicalPoiRel.getWmPhysicalPoiId());
        }
        return map;
    }

    /**
     * 根据门店当前标签情况判断是否子门店
     *
     * @param poiLabels
     * @return
     */
    private Boolean notSonPoi(String poiLabels) {
        if (StringUtils.isBlank(poiLabels)) {
            return true;
        }
        //子门店一定有标签，没子门店不一定有标签
        List<Integer> poiRelTagIds = Arrays.stream(poiLabels.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        if (Collections.disjoint(poiRelTagIds, MccCustomerConfig.getSubPoiTagId())) {
            return true;
        }
        return false;
    }

    /**
     * 校验美食城客户的档口数
     *
     * @param wmCustomerDB
     * @param wmPoiIdList
     * @throws WmCustomerException
     */
    private void checkMscCustomerPoiCnt(WmCustomerDB wmCustomerDB, List<Long> wmPoiIdList) throws WmCustomerException {
        //绑定的非子门店数为0则不校验
        Integer notChildPoiCnt = countBindNotChildPoi(wmPoiIdList);
        if (notChildPoiCnt == 0) {
            return;
        }

        String customerRealTypeSpInfoStr = wmCustomerDB.getCustomerRealTypeSpInfo();
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSON.parseObject(customerRealTypeSpInfoStr, CustomerRealTypeSpInfoBo.class);
        //档口数未填写 +美食城客户允许不录入档口数开关为关闭
        if ((customerRealTypeSpInfoBo == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() == null)
                && !MccCustomerConfig.getMscAllowNoPoiCntSwitch()) {
            throw new WmCustomerException(RESULT_CODE_ERROR,
                    String.format("客户ID(%s)未填写档口数或填写的档口数未生效，请确保客户档口数量维护完成后再进行门店绑定", wmCustomerDB.getMtCustomerId()));
        }
        //已录入美食城档口数 +（美食城档口数-已占用档口数<非子门店总数)
        if (customerRealTypeSpInfoBo != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() != null
                && customerRealTypeSpInfoBo.getFoodCityPoiCount() > 0) {
            CustomerMscUsedPoiDTO customerMscUsedPoiDTO = wmCustomerService.getMscUsedPoiDTO(wmCustomerDB.getId());
            if (customerMscUsedPoiDTO.getUsedPoiCnt() != null
                    && customerMscUsedPoiDTO.getUsedPoiCnt() > 0
                    && customerRealTypeSpInfoBo.getFoodCityPoiCount() - customerMscUsedPoiDTO.getUsedPoiCnt() < notChildPoiCnt) {
                throw new WmCustomerException(RESULT_CODE_ERROR,
                        String.format("客户ID(%s)档口数%s，已占用档口数%s，可用档口数不足，请调整后再绑定", wmCustomerDB.getMtCustomerId(), customerRealTypeSpInfoBo.getFoodCityPoiCount(), customerMscUsedPoiDTO.getUsedPoiCnt()));
            }
        }
    }


    /**
     * 计算批量绑定门店列表的非子门店数
     *
     * @param wmPoiIdList
     * @return
     */
    public int countBindNotChildPoi(List<Long> wmPoiIdList) {
        int notChildPoiCnt = 0;
        List<List<Long>> wmPoiIdPartList = org.apache.curator.shaded.com.google.common.collect.Lists.partition(wmPoiIdList, 100);
        for (List<Long> wmPoiIds : wmPoiIdPartList) {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIds, WM_POI_FIELDS);
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                boolean NotChildPoi = MccCustomerConfig.getSubPoiIdentifySwitch()
                        ? !mscCustomerCheckService.checkChildPoiBySubPoiType(wmPoiAggre.getSub_wm_poi_type())
                        : !mscCustomerCheckService.checkChildPoiByTagId(wmPoiAggre.getLabel_ids());
                if (NotChildPoi) {
                    notChildPoiCnt++;
                }
            }
        }
        return notChildPoiCnt;
    }
}
