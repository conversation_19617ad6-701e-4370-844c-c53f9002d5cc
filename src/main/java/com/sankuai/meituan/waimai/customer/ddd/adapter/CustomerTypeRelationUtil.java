package com.sankuai.meituan.waimai.customer.ddd.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.nibcus.inf.customer.client.enums.AttachmentTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;


public class CustomerTypeRelationUtil {

    private static Logger log = LoggerFactory.getLogger(CustomerTypeRelationUtil.class);


    //存储customerSecondType于资质附件类型的对应关系
    private static Map<Integer, AttachmentTypeEnum> map = initMap();

    private static Map<Integer, AttachmentTypeEnum> initMap(){
        Map map = new HashMap<>();
        //身份证正面
        map.put(CertTypeEnum.ID_CARD.getType(), AttachmentTypeEnum.IDENTITY_CARD_FRONT);
        //临时身份证
        map.put(CertTypeEnum.ID_CARD_TEMP.getType(), AttachmentTypeEnum.OTHER_POSITIVE_ID_CARD);

        //身份证复印件
        map.put(CertTypeEnum.ID_CARD_COPY.getType(), AttachmentTypeEnum.OTHER_POSITIVE_ID_CARD);
        //驾驶证
        map.put(CertTypeEnum.DRIVING_LICENCE.getType(), AttachmentTypeEnum.DRIVING_LICENSE);
        //护照
        map.put(CertTypeEnum.PASSPORT.getType(), AttachmentTypeEnum.PASSPORT);
        //港澳居民往来大陆通行证/回乡证
        map.put(CertTypeEnum.HK_MACAO_REENTRY_PERMIT.getType(), AttachmentTypeEnum.HONGKONG_PASSER);
        //台湾居民往来大陆通行证/回乡证
        map.put(CertTypeEnum.TAIWAN_REENTRY_PERMIT.getType(), AttachmentTypeEnum.TAIWAN_PASSER);
        return map;
    }



    /**
     * 根据customerSecondType查询对应的资质附件类型
     * @param customerSecondType
     * @return
     */
    public static AttachmentTypeEnum getAttachmetTypeEnumByCustomerSecondType(int customerSecondType){

        if(customerSecondType==CertTypeEnum.ID_CARD.getType()){
            return AttachmentTypeEnum.IDENTITY_CARD_FRONT;
        }else if(customerSecondType == CertTypeEnum.ID_CARD_TEMP.getType() ){
            return AttachmentTypeEnum.OTHER_POSITIVE_ID_CARD;

        }else if(customerSecondType == CertTypeEnum.ID_CARD_COPY.getType()){
            return AttachmentTypeEnum.OTHER_POSITIVE_ID_CARD;

        }else if(customerSecondType == CertTypeEnum.DRIVING_LICENCE.getType() ){
            return AttachmentTypeEnum.DRIVING_LICENSE;

        }else if(customerSecondType == CertTypeEnum.PASSPORT.getType() ){
            return AttachmentTypeEnum.PASSPORT;

        }else if(customerSecondType == CertTypeEnum.HK_MACAO_REENTRY_PERMIT.getType() ){
            return AttachmentTypeEnum.HONGKONG_PASSER;

        }else if(customerSecondType == CertTypeEnum.TAIWAN_REENTRY_PERMIT.getType() ){
            return AttachmentTypeEnum.TAIWAN_PASSER;
        }else{
            log.error("根据customerSecondType查询对应的资质附件类型ERROR, customerSecondType={}", customerSecondType);
            return AttachmentTypeEnum.IDENTITY_CARD_FRONT;
        }

    }
}
