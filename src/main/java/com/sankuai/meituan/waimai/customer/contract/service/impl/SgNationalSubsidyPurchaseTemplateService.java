package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.nationalsubsidy.EcontractNationalSubsidyPurchaseInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @description: 国补采销合作协议类
 * @author: liuyunjie05
 * @create: 2024/5/21 15:16
 */
@Slf4j
@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.NATIONAL_SUBSIDY_PURCHASE_AGREEMENT})
public class SgNationalSubsidyPurchaseTemplateService extends AbstractWmEContractTempletService {

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        log.info("SgNationalSubsidyPurchaseTemplateService#startSign, contractBo: {}", JSON.toJSONString(contractBo));
        // 如果是发起待打包
        if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {
            try {
                // 前置校验
                ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
                // 获取当前数据
                WmCustomerContractBo oldBo = wmContractService
                        .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
                // 更新or插入
                Integer contractId = insertOrUpdate(contractBo, opUid, opName);
                // 记录变更状态
                int status = MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus();
                if (status != contractBo.getBasicBo().getStatus()) {
                    contractLogService.logStatusChange(
                            MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                            contractBo.getBasicBo(), opUid, opName);
                }
                contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
                // 正式发起待签约任务
                toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
                contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
                contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                        contractBo.getBasicBo(), opUid, opName);
                ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(), contractId, opUid);
                wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
                return contractId;
            } catch (WmCustomerException ec) {
                log.error("SgNationalSubsidyPurchaseTemplateService#startSign, WmCustomerException, 发起待打包合同任务失败 contractBo: {}, msg: {}", JSON.toJSONString(contractBo), ec.getMsg());
                throw ec;
            } catch (Exception e) {
                log.error("SgNationalSubsidyPurchaseTemplateService#startSign, error, contractBo: {}", JSON.toJSONString(contractBo), e);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
            }
        } else {
            return super.startSign(contractBo, opUid, opName);
        }
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, long contractId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(wmCustomerId)
                .applyTypeEnum(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_PURCHASE)
                .bizId(contractId)
                .build();
    }

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_PURCHASE);

        EcontractNationalSubsidyPurchaseInfoBo infoBo = new EcontractNationalSubsidyPurchaseInfoBo();
        WmTempletContractSignBo partyASignerBo = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyASignerBo();
        WmTempletContractSignBo partyBSignerBo = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyBSignerBo();
        infoBo.setPartAStampName(partyASignerBo.getSignName());
        infoBo.setPartBStampName(partyBSignerBo.getSignName());
        infoBo.setContractNum(contractBo.getBasicBo().getContractNum());
        infoBo.setValidate(DateUtil.secondsToString(contractBo.getBasicBo().getDueDate()));


        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(infoBo));
        applyBo.setNationalSubsidyPurchasePartBId(partyBSignerBo.getSignId());
        log.info("WmCommonConfigContractTemplateService#buildEcontractTaskApplyBo, applyBo: {}", JSON.toJSONString(applyBo));
        return applyBo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String agreementEnum = ContractNumberUtil.genNationSubsidyPurchaseAgreementEnum(insertId);
        contractBo.getBasicBo().setContractNum(agreementEnum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), agreementEnum);
        log.info("SgNationalSubsidyPurchaseTemplateService#save, contractId: {}, contractNumber: {}", insertId, agreementEnum);
        return insertId;
    }


    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        log.info("SgNationalSubsidyPurchaseTemplateService#invalid, contractId: {}, opUid: {}, opUname: {}", contractId, opUid, opUname);
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
        wmCustomerContractBo.getBasicBo().setTempletContractId(contractId);
        // 合同废除校验
        ContractCheckFilter.nationalSubsidyPurchaseInvalidFilter().filter(wmCustomerContractBo, opUid, opUname);
        return super.invalid(contractId, opUid, opUname);
    }

    @Override
    public Map<Integer, Boolean> expire(List<Integer> contractIdList, Integer opUid, String opUname) {
        Map<Integer, Boolean> resultMap = new HashMap<>();
        for (Integer contractId : contractIdList) {
            boolean result = this.expireSingleContract(contractId, opUid, opUname);
            resultMap.put(contractId, result);
        }
        return resultMap;
    }

    @Override
    public boolean expireSingleContract(int contractId, Integer opUid, String opUname)  {
        try {
            return super.expireSingleContract(contractId, opUid, opUname);
        } catch (WmCustomerException e) {
            log.warn("SgNationalSubsidyPurchaseTemplateService#expireSingleContract, WmCustomerException", e);
        } catch (TException e) {
            log.warn("SgNationalSubsidyPurchaseTemplateService#expireSingleContract, TException", e);
        } catch (Exception e) {
            log.error("SgNationalSubsidyPurchaseTemplateService#expireSingleContract, error", e);
        }
        return false;
    }


}
