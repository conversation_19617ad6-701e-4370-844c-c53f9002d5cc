package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerLabelService;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;

/**
 * 监听客户变更，同步至客户列表ES（waimai_e_customer）
 */
@Slf4j
@Service
public class WmCustomerChangeListener implements IMessageListener {

    @Autowired
    private WmCustomerESService wmCustomerESService;

    @Autowired
    private WmCustomerLabelService wmCustomerLabelService;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    private static final int UPDATE_ES_FAIL_RETRY_TIME = 3;

    private static ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("customer_change_pool_%d").build();
    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(5, 10, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(10000), threadFactory, new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        log.info("接收客户变更消息,message={},partition={}", mafkaMessage.getBody(), mafkaMessage.getParttion());
        if (null == mafkaMessage.getBody() || StringUtils.isBlank(mafkaMessage.getBody().toString())) {
            log.error("客户变更消息为空,不进行处理");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            JSONObject updateMsg = JSON.parseObject(mafkaMessage.getBody().toString());
            Integer id = updateMsg.getInteger("id");
            if (id % 100 < MccCustomerConfig.customerSyncEsByMQGrayPercent()) {
                String type = updateMsg.getString("type");
                String updateMsgData = updateMsg.getString("data");
                WmCustomerDB db = JSONObject.parseObject(updateMsgData, WmCustomerDB.class);
                if (id % 100 < MccCustomerConfig.customerSyncEsAsyncGrayPercent()) {
                    executorService.execute(new TraceRunnable(new Runnable() {
                        @Override
                        public void run() {
                            switch (type) {
                                case "insert":
                                    operateInsert(db);
                                    break;
                                case "update":
                                    operateUpdate(db);
                                    break;
                                case "delete":
                                    operateDelete(db.getId());
                                    break;
                                default:
                                    break;
                            }
                        }
                    }));
                } else {
                    switch (type) {
                        case "insert":
                            operateInsert(db);
                            break;
                        case "update":
                            operateUpdate(db);
                            break;
                        case "delete":
                            wmCustomerESService.syncToDelEs(db.getId());
                            break;
                        default:
                            break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理客户变更时出现异常, 消息体={}", mafkaMessage.getBody().toString(), e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void operateInsert(WmCustomerDB db) {
        try {
            log.info("mq 监听客户表变更 插入客户ES db={}", JSONObject.toJSONString(db));
            // 更新es时
            wmCustomerESService.refreshToUpsertEs(db);
            // 更新标签

            boolean isSucc = false;
            for (int i = 0; i < UPDATE_ES_FAIL_RETRY_TIME; i++) {
                try {
                    wmCustomerLabelService.customerLabelSync(db.getMtCustomerId(), Long.valueOf(db.getId()));
                    isSucc = true;
                    log.info("customerLabelSync customerId={},mtCustomerId={} update", db.getId(), db.getMtCustomerId());
                    break;
                } catch (Exception e) {
                    log.warn("customerLabelSync error mtCustomerId={}", db.getId(), db.getMtCustomerId(), e);
                }
                Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
            }
            if (!isSucc) {
                log.error("customerLabelSync error mtCustomerId={}", db.getMtCustomerId());
            }
        } catch (Exception e) {
            log.error("同步插入客户es数据失败 db={}", JSONObject.toJSONString(db), e);
        } finally {
            sendCustomerChangeMsg(db);
        }
    }

    private void operateUpdate(WmCustomerDB db) {
        log.info("mq 监听客户表变更 刷新客户ES db={}", JSONObject.toJSONString(db));
        boolean isSucc = false;
        for (int i = 0; i < UPDATE_ES_FAIL_RETRY_TIME; i++) {
            try {
                //更新es时
                wmCustomerESService.refreshToUpsertEs(db);
                isSucc = true;
                log.info("refreshToUpsertEs customerId={},mtCustomerId={} update", db.getId(), db.getMtCustomerId());
                break;
            } catch (Exception e) {
                log.error("同步更新客户es数据失败 db={}", JSONObject.toJSONString(db), e);
            }
            Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
        }
        if (!isSucc) {
            log.error("更新客户ES失败 customerId={}", db.getId());
        }
        sendCustomerChangeMsg(db);
    }

    private void operateDelete(int customerId) {
        log.info("mq 监听客户表变更 删除客户ES customerId={}", customerId);
        boolean isSucc = false;
        for (int i = 0; i < UPDATE_ES_FAIL_RETRY_TIME; i++) {
            try {
                wmCustomerESService.syncToDelEs(customerId);
                isSucc = true;
                log.info("syncToDelEs customerId={},mtCustomerId={} update", customerId);
                break;
            } catch (Exception e) {
                log.warn("syncToDelEs error customerId={}", customerId, e);
            }
            Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
        }
        if (!isSucc) {
            log.error("删除客户ES失败 customerId={}", customerId);
        }
    }

    private void sendCustomerChangeMsg(WmCustomerDB db) {
        try {
            JSONObject extraData = new JSONObject();
            extraData.put("customerRealType", db.getCustomerRealType());
            CustomerMQBody customerMQBody = new CustomerMQBody(db.getId(), CustomerMQEventEnum.CUSTOMER_CHANGE, extraData.toJSONString());
            log.info("sendCustomerChangeMsg customerMQBody = {}", JSON.toJSONString(customerMQBody));
            mafkaMessageSendManager.send(customerMQBody);
        } catch (Exception e) {
            log.warn("发送【客户变更】消息异常 customerId = {}", db.getId(), e);
        }
    }
}
