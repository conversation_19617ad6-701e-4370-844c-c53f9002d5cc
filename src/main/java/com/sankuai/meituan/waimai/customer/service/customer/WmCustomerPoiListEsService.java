package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Uninterruptibles;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.ModuleEnum;
import com.sankuai.meituan.waimai.channel.beacon.lib.mark.State;
import com.sankuai.meituan.waimai.channel.beacon.lib.statemap.DefaultProgressStatusUtil;
import com.sankuai.meituan.waimai.channel.beacon.lib.statemap.ProgressStatus;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerESFields;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiListVo;
import com.sankuai.meituan.waimai.customer.service.customer.proxy.RestHighLevelClientProxy;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerPoiListInfoTransUtil;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.SwitchCustomerTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerPoiChildEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerPoiListSearchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageDataDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListConditionDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListPageData;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.ElasticsearchTimeoutException;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;
import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

@Service
@Slf4j
public class WmCustomerPoiListEsService {

    private static List<Integer> SEARCH_TYPE_LIST = Lists.newArrayList();
    static{
        SEARCH_TYPE_LIST.add(CustomerRelationStatusEnum.READY_BIND.getCode());
        SEARCH_TYPE_LIST.add(CustomerRelationStatusEnum.TO_APPLY_BIND.getCode());
        SEARCH_TYPE_LIST.add(CustomerRelationStatusEnum.CONFIRM_BINDING.getCode());
        SEARCH_TYPE_LIST.add(CustomerRelationStatusEnum.CONFIRM_BINDING_FAIL.getCode());
    }

    private static final long MAX_RETRY_TIME = 5;

    private static final long EAGLE_CLIENT_BULK_TIME_OUT = ConfigUtilAdapter.getLong("eagle_result_client_bulk_time_out", 2);

    @Autowired
    private RestHighLevelClientProxy restHighLevelClient;

    private String CUSTOMER_POI_LIST_INDEX_TYPE = "customer";


    public static final int STATUS_SEARCH_ALL = -1;

    private static final int NULL_VALUE = -1;

    // 客户门店列表索引
    private String getCustomerPoiListIndex() {
        return ConfigUtilAdapter.getString("eagle_customer_poi_list_index_name", "customer_poi_list");
    }

    // 客户门店列表同步ES失败重试等待时间
    private int getSyncESFailWaitTime() {
        return ConfigUtilAdapter.getInt("eagle_customer_poi_list_fail_wait_time", 100);
    }

    public List<WmCustomerPoiListInfoDTO> queryList(WmCustomerPoiListConditionDTO condition) {
        log.info("WmCustomerPoiListEsService.queryList condition={}", JSON.toJSONString(condition));
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(genQueryBuilderForCustomerPoiCondition(condition));
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        builder.from((condition.getPageNo() - 1) * condition.getPageSize());
        //设置大小选项，确定要返回的搜索匹配数。 默认为10。
        builder.size(condition.getPageSize());
        builder.sort(WmCustomerPoiListESFields.ID.getField(), SortOrder.DESC);
        SearchRequest request = new SearchRequest(getCustomerPoiListIndex());
        request.source(builder);
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request);
        } catch (Exception e) {
            log.error("查询客户门店列表失败 condition={}", JSON.toJSONString(condition), e);
            return null;
        }
        SearchHit[] searchHits = response.getHits().getHits();
        log.info("totalHits:{} maxScore:{}", response.getHits().getTotalHits(), response.getHits().getMaxScore());
        return transferResonseToDbList(searchHits);
    }

    private List<WmCustomerPoiListInfoDTO> transferResonseToDbList(SearchHit[] searchHits) {
        List<WmCustomerPoiListInfoDTO> list = Lists.newArrayList();
        for (SearchHit searchHit : searchHits) {
            WmCustomerPoiListInfoDTO wmCustomerDB = WmCustomerPoiListInfoTransUtil.mapAsWmCustomerPoiListInfo(searchHit.getSourceAsMap());
            list.add(wmCustomerDB);
        }
        return list;
    }


    public WmCustomerPoiListPageData queryData(WmCustomerPoiListConditionDTO condition) {
        log.info("WmCustomerPoiListEsService.queryData condition={}", JSON.toJSONString(condition));
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(genQueryBuilderForCustomerPoiCondition(condition));
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        builder.from((condition.getPageNo() - 1) * condition.getPageSize());
        //设置大小选项，确定要返回的搜索匹配数。 默认为10。
        builder.size(condition.getPageSize());
        builder.sort(WmCustomerPoiListESFields.ID.getField(), SortOrder.DESC);
        SearchRequest request = new SearchRequest(getCustomerPoiListIndex());
        request.source(builder);
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request);
        } catch (Exception e) {
            log.error("查询客户门店列表失败 condition={}", JSON.toJSONString(condition), e);
            return null;
        }
        SearchHit[] searchHits = response.getHits().getHits();
        log.info("totalHits:{} maxScore:{}", response.getHits().getTotalHits(), response.getHits().getMaxScore());
        return handleResonseToDbList(condition, response, searchHits);
    }

    private WmCustomerPoiListPageData handleResonseToDbList(WmCustomerPoiListConditionDTO condition, SearchResponse searchResponse, SearchHit[] searchHits) {
        WmCustomerPoiListPageData pageData = new WmCustomerPoiListPageData();
        pageData.setList(Lists.newArrayList());
        PageDataDTO pageInfo = new PageDataDTO();
        pageData.setPageInfo(pageInfo);
        pageInfo.setPages((long) condition.getPageSize());
        pageInfo.setPageNo((long) condition.getPageNo());
        for (SearchHit searchHit : searchHits) {
            WmCustomerPoiListInfoDTO wmCustomerDB = WmCustomerPoiListInfoTransUtil.mapAsWmCustomerPoiListInfo(searchHit.getSourceAsMap());
            pageData.getList().add(wmCustomerDB);
        }
        pageInfo.setTotal(searchResponse.getHits().getTotalHits());
        return pageData;
    }


    private BoolQueryBuilder genQueryBuilderForCustomerPoiCondition(WmCustomerPoiListConditionDTO condition) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        CustomerPoiListSearchTypeEnum searchTypeEnum = CustomerPoiListSearchTypeEnum.of(condition.getSearchType());

        if (searchTypeEnum == CustomerPoiListSearchTypeEnum.ONLY_VALID) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.VALID.getField(), CustomerConstants.VALID));
        } else if (searchTypeEnum == CustomerPoiListSearchTypeEnum.ONLY_UNVALID) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.VALID.getField(), CustomerConstants.UNVALID));
        } else if (searchTypeEnum == CustomerPoiListSearchTypeEnum.VALID_AND_READY) {
            BoolQueryBuilder innerQueryBuilder = QueryBuilders.boolQuery();
            innerQueryBuilder.should(termQuery(WmCustomerPoiListESFields.VALID.getField(), CustomerConstants.VALID));
            BoolQueryBuilder readyBindQueryBuilder = QueryBuilders.boolQuery();
            readyBindQueryBuilder.must(termQuery(WmCustomerPoiListESFields.VALID.getField(), CustomerConstants.UNVALID));
            readyBindQueryBuilder.must(termsQuery(WmCustomerPoiListESFields.RELATION_STATUS.getField(), SEARCH_TYPE_LIST));
            innerQueryBuilder.should(readyBindQueryBuilder);
            queryBuilder.must(innerQueryBuilder);
        }

        if (StringUtil.isNotEmpty(condition.getKeyWord())) {
            BoolQueryBuilder innerQueryBuilder = QueryBuilders.boolQuery();
            innerQueryBuilder.should(QueryBuilders.matchPhraseQuery(WmCustomerPoiListESFields.SHOP_NAME.getField(), condition.getKeyWord()));
            innerQueryBuilder.should(QueryBuilders.matchPhraseQuery(WmCustomerPoiListESFields.ADDRESS.getField(), condition.getKeyWord()));
            queryBuilder.must(innerQueryBuilder);
        }

        if (condition.getCustomerId() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.CUSTOMER_ID.getField(), condition.getCustomerId()));
        }

        if (condition.getWmPoiId() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.WM_POI_ID.getField(), condition.getWmPoiId()));
        }

        if (condition.getOpManagerId() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.OP_MANAGER_ID.getField(), condition.getOpManagerId()));
        }

        if (condition.getRelationStatus() != null && condition.getRelationStatus() != STATUS_SEARCH_ALL) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.RELATION_STATUS.getField(), condition.getRelationStatus()));
        }

        if (condition.getSwitchTaskType() != null && condition.getSwitchTaskType() != STATUS_SEARCH_ALL) {
            if (condition.getSwitchTaskType().intValue() == SwitchCustomerTypeEnum.YES.getCode()) {
                queryBuilder.must(rangeQuery(WmCustomerPoiListESFields.SWITCH_TASK_ID.getField()).from(1, true));
            } else if (condition.getSwitchTaskType().intValue() == SwitchCustomerTypeEnum.NO.getCode()) {
                queryBuilder.must(termQuery(WmCustomerPoiListESFields.SWITCH_TASK_ID.getField(), 0));
            }
        }
        //重置其他查询条件信息-抽出单独方法
        queryBuilder = fullFillOtherQuery(queryBuilder, condition);

        if (condition.getPoiStatus() != null && condition.getPoiStatus() != STATUS_SEARCH_ALL) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.POI_STATUS.getField(), condition.getPoiStatus()));
        }

        if (condition.getIsDelete() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.IS_DELETE.getField(), condition.getIsDelete()));
        }

        if (condition.getBrandId() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.BRAND_ID.getField(), condition.getBrandId()));
        }

        if (condition.getOwnerUid() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.OWNER_UID.getField(), condition.getOwnerUid()));
        }

        if (CollectionUtils.isNotEmpty(condition.getWmPoiIdList())) {
            queryBuilder.must(termsQuery(WmCustomerPoiListESFields.WM_POI_ID.getField(), condition.getWmPoiIdList().toArray()));
        }

        //是否子门店条件查询
        if (condition.getChildPoiFlag() != null && CustomerPoiChildEnum.of(condition.getChildPoiFlag()) != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.CHILD_POI_FLAG.getField(), condition.getChildPoiFlag()));
        }
        return queryBuilder;
    }

    /**
     * 填充其他非关键查询信
     *
     * @param queryBuilder
     * @param condition
     * @return
     */
    private BoolQueryBuilder fullFillOtherQuery(BoolQueryBuilder queryBuilder, WmCustomerPoiListConditionDTO condition) {
        if (condition.getBaseStatus() != null && condition.getBaseStatus() != STATUS_SEARCH_ALL) {
            List<Integer> stateList = getModuleState(ModuleEnum.BASE, condition.getBaseStatus());
            if (CollectionUtils.isNotEmpty(stateList)) {
                queryBuilder.must(termsQuery(WmCustomerPoiListESFields.BASE_STATUS.getField(), stateList.toArray()));
            }
        }
        if (condition.getQuaStatus() != null && condition.getQuaStatus() != STATUS_SEARCH_ALL) {
            List<Integer> stateList = getModuleState(ModuleEnum.QUALIFICATION, condition.getQuaStatus());
            if (CollectionUtils.isNotEmpty(stateList)) {
                queryBuilder.must(termsQuery(WmCustomerPoiListESFields.QUA_STATUS.getField(), stateList.toArray()));
            }
        }

        if (condition.getDeliveryStatus() != null && condition.getDeliveryStatus() != STATUS_SEARCH_ALL) {
            List<Integer> stateList = getModuleState(ModuleEnum.LOGISTICS, condition.getDeliveryStatus());
            if (CollectionUtils.isNotEmpty(stateList)) {
                queryBuilder.must(termsQuery(WmCustomerPoiListESFields.DELIVERY_STATUS.getField(), stateList.toArray()));
            }
        }
        if (condition.getSettleStatus() != null && condition.getSettleStatus() != STATUS_SEARCH_ALL) {
            List<Integer> stateList = getModuleState(ModuleEnum.SETTLE, condition.getSettleStatus());
            if (CollectionUtils.isNotEmpty(stateList)) {
                queryBuilder.must(termsQuery(WmCustomerPoiListESFields.SETTLE_STATUS.getField(), stateList.toArray()));
            }
        }
        if (condition.getServiceStatus() != null && condition.getServiceStatus() != STATUS_SEARCH_ALL) {
            List<Integer> stateList = getModuleState(ModuleEnum.SERVICE, condition.getServiceStatus());
            if (CollectionUtils.isNotEmpty(stateList)) {
                queryBuilder.must(termsQuery(WmCustomerPoiListESFields.SERVICE_STATUS.getField(), stateList.toArray()));
            }
        }

        if (condition.getProductStatus() != null && condition.getProductStatus() != STATUS_SEARCH_ALL) {
            List<Integer> stateList = getModuleState(ModuleEnum.PRODUCT, condition.getProductStatus());
            if (CollectionUtils.isNotEmpty(stateList)) {
                queryBuilder.must(termsQuery(WmCustomerPoiListESFields.PRODUCT_STATUS.getField(), stateList.toArray()));
            }
        }
        return queryBuilder;
    }

    private List<Integer> getModuleState(ModuleEnum moduleEnum, Integer status) {
        ProgressStatus progressStatus = ProgressStatus.of(status);
        if (progressStatus == null) {
            return Lists.newArrayList();
        }

        List<State> stateList = DefaultProgressStatusUtil.getBeaconStateList(moduleEnum, progressStatus);
        if (CollectionUtils.isEmpty(stateList)) {
            return Lists.newArrayList();
        }
        List<Integer> result = Lists.newArrayList();
        for (State state : stateList) {
            if (state != null) {
                result.add(state.getCode());
            } else {
                result.add(NULL_VALUE);
            }
        }
        return result;
    }


    //通过IndexRequest写入数据
    public IndexResponse insert(String id, Map<String, Object> map) {
        long start = System.currentTimeMillis();
        IndexRequest request = new IndexRequest(getCustomerPoiListIndex(), CUSTOMER_POI_LIST_INDEX_TYPE);  //Index的索引名
        request.source(map).id(id.toString());//设置主键
        for (int i = 0; i < MAX_RETRY_TIME; i++) {
            try {
                return restHighLevelClient.index(request);
            } catch (Exception e) {
                log.error("写入客户门店列表ES失败 id={},map={}", id, JSON.toJSONString(map), e);
                Uninterruptibles.sleepUninterruptibly(getSyncESFailWaitTime(), TimeUnit.MILLISECONDS);
            }
        }
        long end = System.currentTimeMillis();
        log.info("insert耗时：" + (end - start) + "ms");
        return null;
    }

    public static Map<String, Object> makeMap(String[] keys, Object[] values) {
        if (keys != null && keys.length != 0 && values != null && values.length != 0 && keys.length == values.length) {
            HashMap map = new HashMap();
            for (int i = 0; i < keys.length; ++i) {
                map.put(keys[i], values[i]);
            }
            return map;
        } else {
            return null;
        }
    }

    public BulkResponse bulkInsert(Map docMap) {
        long start = System.currentTimeMillis();
        String index = getCustomerPoiListIndex();
        log.info("EagleRestBulkInsert start index ={},docMap ={}", index, JSON.toJSONString(docMap));
        BulkRequest request = new BulkRequest();
        Iterator bulkIterator = docMap.keySet().iterator();
        while (bulkIterator.hasNext()) {
            Object id = bulkIterator.next();
            Object docObj = docMap.get(id);
            String jsonDoc = escapeControlChar(JSON.toJSONString(docObj));
            request.add(new IndexRequest(index, CUSTOMER_POI_LIST_INDEX_TYPE, String.valueOf(id)).source(jsonDoc, XContentType.JSON));
        }
        request.timeout(TimeValue.timeValueSeconds(EAGLE_CLIENT_BULK_TIME_OUT));
        try {
            for (int i = 0; i < MAX_RETRY_TIME; i++) {
                try {
                    BulkResponse bulkResponse = restHighLevelClient.bulk(request);
                    if (bulkResponse != null && bulkResponse.hasFailures()) {
                        log.warn("EagleRestBulkInsert error response = {} ", bulkResponse.buildFailureMessage());
                        Uninterruptibles.sleepUninterruptibly(getSyncESFailWaitTime(), TimeUnit.MILLISECONDS);
                    } else {
                        long end = System.currentTimeMillis();
                        log.info("bulkInsert耗时：" + (end - start) + "ms");
                        return bulkResponse;
                    }
                } catch (ElasticsearchTimeoutException e) {
                    log.info("EagleRestBulkInsert 超时处理");
                    if (i == (MAX_RETRY_TIME - 1)) {
                        log.error("EagleRestBulkInsert 超时处理, 超时内容", e);
                        throw e;
                    }
                }
            }
        } catch (Exception ex) {
            log.error("EagleRestBulkInsert 处理异常 index ={},docMap ={} ", index, JSON.toJSONString(docMap), ex);
            log.info("bulkInsert耗时：" + (System.currentTimeMillis() - start) + "ms");
        }
        long end = System.currentTimeMillis();
        log.info("bulkInsert耗时：" + (end - start) + "ms");
        return null;
    }

    public UpdateResponse update(String id, Map<String, Object> map) {
        long start = System.currentTimeMillis();
        UpdateRequest request = new UpdateRequest(getCustomerPoiListIndex(), CUSTOMER_POI_LIST_INDEX_TYPE, id);
        request.doc(map);
        for (int i = 0; i < MAX_RETRY_TIME; i++) {
            try {
                return restHighLevelClient.update(request);
            } catch (ElasticsearchStatusException e) {
                log.warn("更新客户门店列表ES失败 id={},map={}", id, JSON.toJSONString(map), e);
                Uninterruptibles.sleepUninterruptibly(getSyncESFailWaitTime(), TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.warn("更新客户门店列表ES失败 id={},map={}", id, JSON.toJSONString(map), e);
            }
        }
        long end = System.currentTimeMillis();
        log.info("update耗时：" + (end - start) + "ms");
        return null;
    }

    public BulkResponse bulkUpdate(Map docMap) {
        long start = System.currentTimeMillis();
        String index = getCustomerPoiListIndex();
        log.info("EagleRestBulkUpdate start index = {},docMap ={}", index, JSON.toJSONString(docMap));
        if (docMap == null || docMap.isEmpty()) {
            log.info("EagleRestBulkUpdate bulkUpdate失败docMap为空  start index = {},docMap ={}", index, JSON.toJSONString(docMap));
            return null;
        }
        BulkRequest bulkRequest = new BulkRequest();
        Iterator bulkIterator = docMap.keySet().iterator();
        while (bulkIterator.hasNext()) {
            Object id = bulkIterator.next();
            Object docObj = docMap.get(id);
            String jsonDoc = escapeControlChar(JSON.toJSONString(docObj));
            bulkRequest.add(new UpdateRequest(index, CUSTOMER_POI_LIST_INDEX_TYPE, String.valueOf(id)).doc(jsonDoc, XContentType.JSON));
        }
        bulkRequest.timeout(TimeValue.timeValueSeconds(EAGLE_CLIENT_BULK_TIME_OUT));
        try {
            for (int i = 0; i < MAX_RETRY_TIME; i++) {
                try {
                    BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest);
                    if (bulkResponse != null && bulkResponse.hasFailures()) {
                        log.warn("EagleRestBulkUpdate error response = {} ", bulkResponse.buildFailureMessage());
                        Uninterruptibles.sleepUninterruptibly(getSyncESFailWaitTime(), TimeUnit.MILLISECONDS);
                    } else {
                        long end = System.currentTimeMillis();
                        log.info("bulkUpdate耗时：" + (end - start) + "ms");
                        return bulkResponse;
                    }
                } catch (ElasticsearchTimeoutException e) {
                    log.info("EagleRestBulkUpdate 超时处理");
                    if (i == (MAX_RETRY_TIME - 1)) {
                        log.error("EagleRestBulkUpdate 超时处理, 超时内容", e);
                        throw e;
                    }
                }
            }
        } catch (Exception ex) {
            log.error("EagleRestBulkUpdate 处理异常 index = {},docMap ={} ", index, JSON.toJSONString(docMap), ex);
        }
        long end = System.currentTimeMillis();
        log.info("bulkUpdate耗时：" + (end - start) + "ms");
        return null;
    }
    private static String escapeControlChar(String source) {
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < source.length(); ++i) {
            char c = source.charAt(i);
            if (!Character.isISOControl(c)) {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    public BulkResponse bulkUpdateForPoiStatus(ConcurrentMap docMap) {
        long start = System.currentTimeMillis();
        String index = getCustomerPoiListIndex();
        log.info("EagleRestBulkUpdate start index = {},docMap ={}", index, JSON.toJSONString(docMap));
        if (docMap == null || docMap.isEmpty()) {
            log.info("EagleRestBulkUpdate bulkUpdate失败docMap为空  start index = {},docMap ={}", index, JSON.toJSONString(docMap));
            return null;
        }
        BulkRequest bulkRequest = new BulkRequest();
        Iterator bulkIterator = docMap.keySet().iterator();
        while (bulkIterator.hasNext()) {
            Object id = bulkIterator.next();
            Object docObj = docMap.get(id);
            String jsonDoc = escapeControlCharForPoiStatus(JSON.toJSONString(docObj));
            bulkRequest.add(new UpdateRequest(index, CUSTOMER_POI_LIST_INDEX_TYPE, String.valueOf(id)).doc(jsonDoc, XContentType.JSON));
        }
        bulkRequest.timeout(TimeValue.timeValueSeconds(EAGLE_CLIENT_BULK_TIME_OUT));
        try {
            for (int i = 0; i < MAX_RETRY_TIME; i++) {
                try {
                    log.info("EagleRestBulkUpdate start bulk,bulkRequest ={}", index, JSON.toJSONString(bulkRequest));
                    BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest);
                    if (bulkResponse != null && bulkResponse.hasFailures()) {
                        log.warn("EagleRestBulkUpdate error response = {} ", bulkResponse.buildFailureMessage());
                        Uninterruptibles.sleepUninterruptibly(getSyncESFailWaitTime(), TimeUnit.MILLISECONDS);
                    } else {
                        long end = System.currentTimeMillis();
                        log.info("bulkUpdate耗时：" + (end - start) + "ms");
                        return bulkResponse;
                    }
                } catch (ElasticsearchTimeoutException e) {
                    log.info("EagleRestBulkUpdate 超时处理");
                    if (i == (MAX_RETRY_TIME - 1)) {
                        log.error("EagleRestBulkUpdate 超时处理, 超时内容", e);
                        throw e;
                    }
                }
            }
        } catch (Exception ex) {
            log.error("EagleRestBulkUpdate 处理异常 index = {},docMap ={} ", index, JSON.toJSONString(docMap), ex);
        }
        long end = System.currentTimeMillis();
        log.info("bulkUpdate耗时：" + (end - start) + "ms");
        return null;
    }

    private static String escapeControlCharForPoiStatus(String source) {
        StringBuffer sb = new StringBuffer();

        for (int i = 0; i < source.length(); ++i) {
            char c = source.charAt(i);
            if (!Character.isISOControl(c)) {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    public void delete(String id) {
        DeleteRequest deleteRequest = new DeleteRequest(getCustomerPoiListIndex(), CUSTOMER_POI_LIST_INDEX_TYPE, id.toString());
        try {
            DeleteResponse response = restHighLevelClient.delete(deleteRequest);
        } catch (Exception e) {
            log.error("更新客户门店列表ES失败 id={}", id, e);
        }
    }

    public List<WmCustomerPoiListInfoDTO> queryData(WmCustomerPoiListVo condition) {
        log.info("WmCustomerPoiListEsService.queryData condition={}", JSON.toJSONString(condition));
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(genQueryBuilderForCustomerPoiCondition(condition));
        //设置from选项，确定要开始搜索的结果索引。 默认为0。
        builder.from((condition.getPageNo() - 1) * condition.getPageSize());
        //设置大小选项，确定要返回的搜索匹配数。 默认为10。
        builder.size(condition.getPageSize());
        builder.sort(WmCustomerESFields.CustomerBasicFields.id.getField(), SortOrder.DESC);
        SearchRequest request = new SearchRequest(getCustomerPoiListIndex());
        request.source(builder);
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request);
        } catch (Exception e) {
            log.error("查询客户门店列表失败 condition={}", JSON.toJSONString(condition), e);
            return null;
        }
//        log.info("查询客户门店列表结果为:{}", JSON.toJSONString(response));
        SearchHit[] searchHits = response.getHits().getHits();
        log.info("totalHits:{} maxScore:{}", response.getHits().getTotalHits(), response.getHits().getMaxScore());
        return handleResonseToDbList(searchHits);
    }

    private BoolQueryBuilder genQueryBuilderForCustomerPoiCondition(WmCustomerPoiListVo condition) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (condition.getValid() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.VALID.getField(), condition.getValid()));
        } else {
            BoolQueryBuilder innerQueryBuilder = QueryBuilders.boolQuery();
            innerQueryBuilder.should(termQuery(WmCustomerPoiListESFields.VALID.getField(), CustomerConstants.VALID));
            BoolQueryBuilder readyBindQueryBuilder = QueryBuilders.boolQuery();
            readyBindQueryBuilder.must(termQuery(WmCustomerPoiListESFields.VALID.getField(), CustomerConstants.UNVALID));
            readyBindQueryBuilder.must(termQuery(WmCustomerPoiListESFields.RELATION_STATUS.getField(), CustomerRelationStatusEnum.READY_BIND.getCode()));
            innerQueryBuilder.should(readyBindQueryBuilder);
            queryBuilder.must(innerQueryBuilder);
        }

        if (condition.getId() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.ID.getField(), condition.getId()));
        }

        if (condition.getCustomerId() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.CUSTOMER_ID.getField(), condition.getCustomerId()));
        }

        if (condition.getWmPoiId() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.WM_POI_ID.getField(), condition.getWmPoiId()));
        }

        if (condition.getKpId() != null) {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.OP_MANAGER_ID.getField(), condition.getKpId()));
        }

        if (CollectionUtils.isNotEmpty(condition.getWmPoiIdList())) {
            queryBuilder.must(termsQuery(WmCustomerPoiListESFields.WM_POI_ID.getField(), condition.getWmPoiIdList().toArray()));
        }

        return queryBuilder;
    }

    private List<WmCustomerPoiListInfoDTO> handleResonseToDbList(SearchHit[] searchHits) {
        List<WmCustomerPoiListInfoDTO> list = Lists.newArrayList();
        for (SearchHit searchHit : searchHits) {
            WmCustomerPoiListInfoDTO wmCustomerDB = WmCustomerPoiListInfoTransUtil.mapAsWmCustomerPoiListInfo(searchHit.getSourceAsMap());
            list.add(wmCustomerDB);
        }
        return list;
    }

    /**
     * 批量更新ES
     *
     * @param customerId
     * @param wmPoiIdSet
     */
    public void batchUpdateEsSync(Integer customerId, Set<Long> wmPoiIdSet) {
        log.info("batchUpdateEsSync customerId={}, wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));
        try {
            WmCustomerPoiListVo condition = new WmCustomerPoiListVo();
            List<Integer> wmPoiIds = wmPoiIdSet.stream().map(x -> x.intValue()).collect(Collectors.toList());
            condition.setCustomerId(customerId);
            condition.setWmPoiIdList(wmPoiIds);
            condition.setValid(CustomerConstants.UNVALID);
            condition.setPageNo(1);
            condition.setPageSize(wmPoiIdSet.size());
            int retryTime = MccCustomerConfig.getCustomerSyncEsRetryTime();
            int waitTime = MccCustomerConfig.getCustomerSyncEsWaitTime();
            boolean isTimeOutRetry = true;
            for (int i = 0; i < retryTime; i++) {
                List<WmCustomerPoiListInfoDTO> list = queryData(condition);
                log.info("batchUpdateEsSync customerId={}, wmPoiIdSet={},list={}", customerId, JSON.toJSONString(wmPoiIdSet), JSONObject.toJSONString(list));
                if (org.springframework.util.CollectionUtils.isEmpty(list) || list.size() < wmPoiIdSet.size()) {
                    Uninterruptibles.sleepUninterruptibly(waitTime, TimeUnit.MILLISECONDS);
                } else {
                    break;
                }
            }
            log.info("batchUpdateEsSync isTimeOutRetry={},customerId={}, wmPoiIdSet={}", isTimeOutRetry, customerId, JSON.toJSONString(wmPoiIdSet));

        } catch (Exception e) {
            log.warn("batchUpdateEsSync 失败 customerId={}, wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet), e);
        }
    }

    /**
     * 批量插入ES
     *
     * @param customerId
     * @param wmPoiIdSet
     */
    public void batchInsertEsSync(Integer customerId, Set<Long> wmPoiIdSet) {
        log.info("batchInsertEsSync customerId={}, wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));
        try {
            WmCustomerPoiListVo condition = new WmCustomerPoiListVo();
            List<Integer> wmPoiIds = wmPoiIdSet.stream().map(x -> x.intValue()).collect(Collectors.toList());
            condition.setCustomerId(customerId);
            condition.setWmPoiIdList(wmPoiIds);
            condition.setValid(CustomerConstants.VALID);
            condition.setPageNo(1);
            condition.setPageSize(wmPoiIdSet.size());
            int retryTime = MccCustomerConfig.getCustomerSyncEsRetryTime();
            int waitTime = MccCustomerConfig.getCustomerSyncEsWaitTime();
            boolean isTimeOutRetry = true;
            for (int i = 0; i < retryTime; i++) {
                List<WmCustomerPoiListInfoDTO> list = queryData(condition);
                log.info("batchInsertEsSync customerId={}, wmPoiIdSet={},list={}", customerId, JSON.toJSONString(wmPoiIdSet), JSONObject.toJSONString(list));
                if (org.springframework.util.CollectionUtils.isEmpty(list) || list.size() < wmPoiIdSet.size()) {
                    Uninterruptibles.sleepUninterruptibly(waitTime, TimeUnit.MILLISECONDS);
                } else {
                    isTimeOutRetry = false;
                    break;
                }
            }
            log.info("batchInsertEsSync isTimeOutRetry={},customerId={}, wmPoiIdSet={}", isTimeOutRetry, customerId, JSON.toJSONString(wmPoiIdSet));

        } catch (Exception e) {
            log.warn("batchInsertEsSync 失败 customerId={}, wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet), e);
        }
    }


    /**
     * 根据客户ID和状态查询状态为上线的非子门店数
     *
     * @param customerId
     * @param relStatusList
     * @return
     */
    public SearchResponse countByCustomerIdAndRelStatus(Integer customerId, List<Integer> relStatusList, Boolean switchCustomerFlag)
            throws WmCustomerException {
        SearchSourceBuilder builder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(termQuery(WmCustomerPoiListESFields.CUSTOMER_ID.getField(), customerId));
        queryBuilder.must(termsQuery(WmCustomerPoiListESFields.RELATION_STATUS.getField(), relStatusList));
        //商家状态-上线中
        queryBuilder.must(termQuery(WmCustomerPoiListESFields.POI_STATUS.getField(), WmPoiValidEnum.ONLINE.getValue()));
        //是否子门店-否
        queryBuilder.must(termQuery(WmCustomerPoiListESFields.CHILD_POI_FLAG.getField(), CustomerPoiChildEnum.NOT_CHILD_POI.getCode()));
        if (switchCustomerFlag) {
            queryBuilder.must(rangeQuery(WmCustomerPoiListESFields.SWITCH_TASK_ID.getField()).from(1, true));
        } else {
            queryBuilder.must(termQuery(WmCustomerPoiListESFields.SWITCH_TASK_ID.getField(), 0));
        }

        builder.query(queryBuilder);
        builder.from(0);
        builder.size(MccCustomerConfig.getPoiRelEsUsedPoiCntSize());
        builder.sort(WmCustomerESFields.CustomerBasicFields.id.getField(), SortOrder.DESC);
        SearchRequest request = new SearchRequest(getCustomerPoiListIndex());
        request.source(builder);
        try {
            SearchResponse response = restHighLevelClient.search(request);
            if (response == null || response.getHits() == null || response.getHits().totalHits == 0) {
                return null;
            }
            return response;
        } catch (Exception e) {
            log.error("countByCustomerIdAndRelStatus,根据客户ID以及状态集合查询上单中门店ES发生异常,customerId={},relStatusList={}",
                    customerId, JSON.toJSONString(relStatusList), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "查询ES异常");
        }
    }

}
