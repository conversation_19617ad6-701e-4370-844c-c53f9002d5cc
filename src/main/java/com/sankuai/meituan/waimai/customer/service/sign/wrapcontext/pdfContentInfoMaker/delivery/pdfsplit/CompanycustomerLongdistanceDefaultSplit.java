package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY;
import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.COMPANYCUSTOMER_LONGDISTANCE_DEFAULT)
public class CompanycustomerLongdistanceDefaultSplit implements DeliveryPdfSplit {
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));

        if (!(feeMode == LogisticsFeeModeEnum.WAIMAI_V3
                || feeMode == LogisticsFeeModeEnum.WAIMAI_QIKE
                || feeMode == LogisticsFeeModeEnum.WAIMAI_QIKE_V2
                || feeMode == LogisticsFeeModeEnum.SHANGOU_QIKE_V2
                || feeMode == LogisticsFeeModeEnum.YIYAO_QIKE_V2)
                && deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo() != null
                && CollectionUtils.isNotEmpty(tabPdfMap.get(TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE))) {
            List<String> companyCustomerLongDistanceDefaultList = pdfDataMap.get(DeliveryPdfDataTypeEnum.COMPANYCUSTOMER_LONGDISTANCE_DEFAULT.getName());
            if (CollectionUtils.isEmpty(companyCustomerLongDistanceDefaultList)) {
                companyCustomerLongDistanceDefaultList = Lists.newArrayList();
                companyCustomerLongDistanceDefaultList.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                companyCustomerLongDistanceDefaultList.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.COMPANYCUSTOMER_LONGDISTANCE_DEFAULT.getName(), companyCustomerLongDistanceDefaultList);
            log.info("ADD TO COMPANYCUSTOMER_LONGDISTANCE_DEFAULT，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}
