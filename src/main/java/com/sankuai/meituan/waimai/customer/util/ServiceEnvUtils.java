package com.sankuai.meituan.waimai.customer.util;

import com.dianping.lion.client.util.PropertiesUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Properties;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-10 15:26
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
public class ServiceEnvUtils {

    public static final String TEST_ENV = "test";
    public static final String STAGING_ENV = "staging";
    public static final String PROD_ENV = "prod";

    public static String getEnv() {
        Properties props = null;
        try {
            props = PropertiesUtils.loadFromFileSystem("/data/webapps/appenv");
        } catch (Exception e) {
            log.error("failed to load /data/webapps/appenv", e);
        }
        if (props == null) {
            return "【未知环境】";
        }
        String deployenv = StringUtils.trimToNull(props.getProperty("env"));
        return StringUtils.isBlank(deployenv) ? "【未知环境】" : "【" + deployenv + "环境】";
    }

    public static String queryRecordEnv() {
        String env = getEnv();
        if (StringUtils.isEmpty(env)) {
            return "";
        }
        if (env.equals("【test环境】")) {
            return TEST_ENV;
        } else if (env.equals("【staging环境】")) {
            return STAGING_ENV;
        } else if (env.equals("【prod环境】")) {
            return PROD_ENV;
        } else {
            return "";
        }
    }
}
