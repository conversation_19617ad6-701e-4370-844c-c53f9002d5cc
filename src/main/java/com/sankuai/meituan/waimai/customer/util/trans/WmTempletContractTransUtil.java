/*
* Copyright (c) 2016 meituan.com. All Rights Reserved.
*/
package com.sankuai.meituan.waimai.customer.util.trans;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.StringUtil;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.bo.NationalSubsidyPurchaseContractBizData;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignWithOutSignPhoneDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractVersionBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractQueryResponseDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Set;


public class WmTempletContractTransUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmTempletContractTransUtil.class);

    private static final Set<Integer> C2_CONTRACT_LIST = Sets.newHashSet(
            WmTempletContractTypeEnum.C2_E.getCode(),
            WmTempletContractTypeEnum.C2_PAPER.getCode()
    );

    private static final Set<Integer> DAO_CAN_CONTRACT_TYPE_SET = Sets.newHashSet(
            WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode(),
            WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode()
    );

    private static final String CONTRACT_PDF_LINT_PREFIX = "https://econtract.meituan.com";

    public static WmTempletContractBasicBo templetContractBasicDbToBo(WmTempletContractDB db) {
        if (db == null) {
            return null;
        }
        WmTempletContractBasicBo wmTempletContractBasicBo = new WmTempletContractBasicBo();
        wmTempletContractBasicBo.setTempletContractId(db.getId());
        wmTempletContractBasicBo.setParentId(db.getParentId().intValue());
        wmTempletContractBasicBo.setType(db.getType());
        wmTempletContractBasicBo.setStatus(db.getStatus());
        wmTempletContractBasicBo.setContractNum(db.getNumber());
        wmTempletContractBasicBo.setEffectiveDate(db.getEffectiveDate());
        wmTempletContractBasicBo.setExpectEffectiveDate(db.getExpectEffectiveDate() == null ? 0 : db.getExpectEffectiveDate());
        wmTempletContractBasicBo.setDueDate(db.getDueDate());
        wmTempletContractBasicBo.setExtStr(db.getOtherItem());
        wmTempletContractBasicBo.setVersion(db.getVersion() == null ? 0 : db.getVersion());
        wmTempletContractBasicBo.setUtime(db.getUtime());
        wmTempletContractBasicBo.setLogisticsSubject(db.getLogisticsSubject());
        wmTempletContractBasicBo.setBizDate(db.getBizData());
        return wmTempletContractBasicBo;
    }

    public static WmCustomerContractBo templetContractAndSignbToBo(WmTempletContractDB contractDB, List<WmTempletContractSignDB> wmTempletContractSignDBList) {
        if (contractDB == null || CollectionUtils.isEmpty(wmTempletContractSignDBList)) {
            return null;
        }
        WmCustomerContractBo customerContractBo = new WmCustomerContractBo();
        customerContractBo.setBasicBo(templetContractBasicDbToBo(contractDB));
        customerContractBo.setSignBoList(templetSignDbToBoList(wmTempletContractSignDBList));
        return customerContractBo;
    }

    public static WmCustomerContractBo templetContractWithoutSignPhoneAndSignbToBo(WmTempletContractDB contractDB, List<WmTempletContractSignWithOutSignPhoneDB> wmTempletContractSignDBList) {
        if (contractDB == null || CollectionUtils.isEmpty(wmTempletContractSignDBList)) {
            return null;
        }
        WmCustomerContractBo customerContractBo = new WmCustomerContractBo();
        customerContractBo.setBasicBo(templetContractBasicDbToBo(contractDB));
        customerContractBo.setSignBoList(templetSignDbWithOutSignPhoneToBoList(wmTempletContractSignDBList));
        return customerContractBo;
    }

    public static List<WmTempletContractBasicBo> templetContractBasicListDbToBo(List<WmTempletContractDB> dbList) {
        List<WmTempletContractBasicBo> resList = Lists.newArrayList();
        if (dbList == null) {
           return resList;
        }
        for (WmTempletContractDB db : dbList) {
            resList.add(templetContractBasicDbToBo(db));
        }
        return resList;
    }

    public static WmTempletContractDB templetContractBasicBoToDb(WmTempletContractBasicBo basicBo, List<WmTempletContractSignBo> signBoList) {
        if (basicBo == null) {
            return null;
        }
        WmTempletContractDB templetContractDB = new WmTempletContractDB();
        templetContractDB.setId(basicBo.getTempletContractId());
        templetContractDB.setParentId(basicBo.getParentId() == 0 ? null : Long.valueOf(basicBo.getParentId()));
        templetContractDB.setType(basicBo.getType());
        templetContractDB.setStatus(basicBo.getStatus());
        templetContractDB.setNumber(basicBo.getContractNum());
        templetContractDB.setEffectiveDate(basicBo.getEffectiveDate() == 0 ? null : Long.valueOf(basicBo.getEffectiveDate()).intValue());
        // C2合同要改为长期有效, 所以dueDate会设置0, 这里需要修改
        if (C2_CONTRACT_LIST.contains(basicBo.getType())) {
            templetContractDB.setDueDate((int) basicBo.getDueDate());
        } else {
            templetContractDB.setDueDate(basicBo.getDueDate() == 0 ? null : Long.valueOf(basicBo.getDueDate()).intValue());
        }
        templetContractDB.setOtherItem(basicBo.getExtStr());
        templetContractDB.setVersion(basicBo.getVersion());
        // 预计生效时间
        templetContractDB.setExpectEffectiveDate(basicBo.getExpectEffectiveDate());
        // 履约服务主体
        templetContractDB.setLogisticsSubject(basicBo.getLogisticsSubject());
        // 业务属性
        templetContractDB.setBizData(extractBizData(basicBo, signBoList));

        return templetContractDB;
    }

    private static String extractBizData(WmTempletContractBasicBo basicBo, List<WmTempletContractSignBo> signBoList) {
        if (DAO_CAN_CONTRACT_TYPE_SET.contains(basicBo.getType())) {
            return JSON.toJSONString(basicBo.getDaoCanContractInfo());
        }
        if (WmTempletContractTypeEnum.NATIONAL_SUBSIDY_PURCHASE_AGREEMENT.getCode() == basicBo.getType()) {
            return JSON.toJSONString(assembleNationalSubsidyPurchaseAgreementBizData(signBoList));
        }
        return StringUtil.isBlank(basicBo.getBizDate()) ? "" : basicBo.getBizDate();
    }

    private static NationalSubsidyPurchaseContractBizData assembleNationalSubsidyPurchaseAgreementBizData(List<WmTempletContractSignBo> signBoList) {
        WmTempletContractSignBo partyBSignerBo = WmContractSignAggre.Factory.makeWithSignBo(signBoList).getPartyBSignerBo();

        NationalSubsidyPurchaseContractBizData bizData = new NationalSubsidyPurchaseContractBizData();
        bizData.setJhdSubjectId(partyBSignerBo.getSignId());
        bizData.setJhdSubjectName(partyBSignerBo.getSignName());
        return bizData;
    }


    public static WmTempletContractSignBo templetSignDbToBo(WmTempletContractSignDB db) {
        if (db == null) {
            return null;
        }
        WmTempletContractSignBo wmTempletContractSignBo = new WmTempletContractSignBo();
        wmTempletContractSignBo.setId(db.getId());
        wmTempletContractSignBo.setTempletContractId(db.getWmTempletContractId());
        wmTempletContractSignBo.setSignId(db.getSignId());
        wmTempletContractSignBo.setSignName(db.getSignName());
        wmTempletContractSignBo.setSignPeople(db.getSignPeople());
        wmTempletContractSignBo.setSignPhone(db.getSignPhone());
        wmTempletContractSignBo.setSignType(db.getSignType());
        wmTempletContractSignBo.setSignTime(db.getSignTime());
        return wmTempletContractSignBo;
    }

    public static WmTempletContractSignBo templetSignWithOutSignPhoneDbToBo(WmTempletContractSignWithOutSignPhoneDB db) {
        if (db == null) {
            return null;
        }
        WmTempletContractSignBo wmTempletContractSignBo = new WmTempletContractSignBo();
        wmTempletContractSignBo.setId(db.getId());
        wmTempletContractSignBo.setTempletContractId(db.getWmTempletContractId());
        wmTempletContractSignBo.setSignId(db.getSignId());
        wmTempletContractSignBo.setSignName(db.getSignName());
        wmTempletContractSignBo.setSignPeople(db.getSignPeople());
        wmTempletContractSignBo.setSignType(db.getSignType());
        wmTempletContractSignBo.setSignTime(db.getSignTime());
        return wmTempletContractSignBo;
    }

    public static List<WmTempletContractSignBo> templetSignDbToBoList(List<WmTempletContractSignDB> dbList) {
        List<WmTempletContractSignBo> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dbList)) {
            return result;
        }
        for (WmTempletContractSignDB temp : dbList) {
            result.add(templetSignDbToBo(temp));
        }
        return result;
    }

    public static List<CustomerContractQueryResponseDTO> convertToResponseDTOList(Long mtCustomerId, WmTempletContractDB contractDB, List<WmContractVersionDB> contractVersionDBList) {
        String dcContractPdfUrlRoutePrefix = MccConfig.getDaoCanContractPdfUrlRoutePrefix();
        List<CustomerContractQueryResponseDTO> contractList = Lists.newArrayListWithCapacity(contractVersionDBList.size());
        for (WmContractVersionDB wmContractVersionDB : contractVersionDBList) {
            CustomerContractQueryResponseDTO responseDTO = new CustomerContractQueryResponseDTO();
            responseDTO.setMtCustomerId(mtCustomerId);
            responseDTO.setContractType(contractDB.getType());
            responseDTO.setContractNum(wmContractVersionDB.getVersion_number());
            responseDTO.setContractName(WmTempletContractTypeEnum.getByCode(contractDB.getType()).getMsg());
            responseDTO.setStatus(contractDB.getStatus());
            responseDTO.setCreateTime(contractDB.getCtime());
            responseDTO.setPdfUrl(dcContractPdfUrlRoutePrefix + CONTRACT_PDF_LINT_PREFIX + wmContractVersionDB.getPdf_url());

            contractList.add(responseDTO);
        }
        return contractList;
    }

    public static List<WmTempletContractSignBo> templetSignDbWithOutSignPhoneToBoList(List<WmTempletContractSignWithOutSignPhoneDB> dbList) {
        List<WmTempletContractSignBo> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dbList)) {
            return result;
        }
        for (WmTempletContractSignWithOutSignPhoneDB temp : dbList) {
            result.add(templetSignWithOutSignPhoneDbToBo(temp));
        }
        return result;
    }

    public static WmTempletContractSignDB templetSignBoToDb(WmTempletContractSignBo signBo) {
        if (signBo == null) {
            return null;
        }
        WmTempletContractSignDB signDB = new WmTempletContractSignDB();
        signDB.setId(signBo.getId());
        signDB.setWmTempletContractId(signBo.getTempletContractId());
        signDB.setSignId(signBo.getSignId());
        signDB.setSignName(signBo.getSignName());
        signDB.setSignPeople(signBo.getSignPeople());
        signDB.setSignPhone(signBo.getSignPhone());
        signDB.setSignType(signBo.getSignType());
        signDB.setSignTime(signBo.getSignTime());
        return signDB;
    }

    public static List<WmTempletContractSignDB> templetSignBoToDbList(List<WmTempletContractSignBo> signBoList) {
        List<WmTempletContractSignDB> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(signBoList)) {
            return result;
        }
        for (WmTempletContractSignBo temp : signBoList) {
            result.add(templetSignBoToDb(temp));
        }
        return result;
    }

    public static List<WmTempletContractSignDB> templetSignBoToDbList(
            List<WmTempletContractSignBo> signBoList, int opUid) {
        List<WmTempletContractSignDB> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(signBoList)) {
            return result;
        }
        for (WmTempletContractSignBo temp : signBoList) {
            result.add(templetSignBoToDb(temp, opUid));
        }
        return result;
    }

    private static WmTempletContractSignDB templetSignBoToDb(WmTempletContractSignBo temp, int opUid) {
        WmTempletContractSignDB result = templetSignBoToDb(temp);
        if (result != null) {
            result.setOpuid(opUid);
        }
        return result;
    }

    public static WmCustomerContractVersionBo transVersionDbToBo(WmContractVersionDB wmContractVersionDB) {
        if (wmContractVersionDB == null) {
            return null;
        }
        WmCustomerContractVersionBo contractVersionBo = new WmCustomerContractVersionBo();
        contractVersionBo.setId(wmContractVersionDB.getId());
        contractVersionBo.setPdfUrl(wmContractVersionDB.getPdf_url());
        contractVersionBo.setStampPdfUrl(wmContractVersionDB.getStamp_pdf_url());
        contractVersionBo.setTransactionId(wmContractVersionDB.getTransaction_id());
        contractVersionBo.setVersionNumber(wmContractVersionDB.getVersion_number());
        contractVersionBo.setWmContractId(wmContractVersionDB.getWm_contract_id());
        contractVersionBo.setUtime(wmContractVersionDB.getUtime());
        contractVersionBo.setStatus(wmContractVersionDB.getStatus());
        return contractVersionBo;
    }


}