package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractSignService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DueDateAndSignTimeValidator implements IContractValidator {

    @Autowired
    private WmContractSignService wmContractSignService;

    /**
     * 签约时间 <= 今天 <= 合同有效期
     *
     * @param contractBo
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     */
    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {

        // 资质属实商家承诺函/门店推广技术服务合同/拼好饭结算协议/企客配送服务合同/打包(袋)服务合作协议/美食城承诺书/医药分单补充协议/四轮履约补充协议/极速达模式合作协议 有效期长期有效
        WmTempletContractTypeBo typeBo = new WmTempletContractTypeBo(contractBo.getBasicBo().getType());
        if (typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_QUA_REAL_LETTER ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_PHF_CHARGE ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_POI_PROMOTION_SERVICE ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_BUSINESS_CUSTOMER ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_GROUP_MEAL ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_BAG_SERVICE ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_FOODCITY_STATEMENT ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_INTERIM_SELF ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_MEDIC_ORDER_SPLIT ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_SUBJECT_CHANGE_SUPPLEMENT ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_SPEEDY_DELIVERY_COOPERATION_AGREEMENT ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_DAOCAN_SERVICE_C1_CONTRACT ||
                typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_DAOCAN_SERVICE_C2_CONTRACT
        ) {
            return true;
        }

        boolean isLongEffectC2Contract = typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_C2
                && (int) contractBo.getBasicBo().getDueDate() == 0;

        if (isXXYearsLater(contractBo.getBasicBo().getDueDate(), 2)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "为了避免风险，请勿签约超过两年的合同");
        }

        int todayTime = DateUtil.date2Unixtime(DateUtil.today());

        if (!isLongEffectC2Contract && (int) contractBo.getBasicBo().getDueDate() < todayTime) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同有效期不得早于今日。");
        }

        //合同有效期必须大于预计生效时间
        if (!isLongEffectC2Contract && contractBo.getBasicBo().getExpectEffectiveDate() != 0 &&
                contractBo.getBasicBo().getExpectEffectiveDate() > (int) contractBo.getBasicBo().getDueDate()){
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "生效时间必须早于有效期");
        }

        //预计生效时间必须位于T+30之内
        if (contractBo.getBasicBo().getExpectEffectiveDate() != 0){
            int dateStartTime = DateUtil.date2Unixtime(DateTime.now().withMillisOfDay(0).plusDays(1).toDate());
            int dateEndTime = DateUtil.date2Unixtime(DateTime.now().withMillisOfDay(0).plusDays(31).toDate());
            if(contractBo.getBasicBo().getExpectEffectiveDate() < dateStartTime || contractBo.getBasicBo().getExpectEffectiveDate() >= dateEndTime){
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "预计生效时间必须在于T+30之内");
            }
        }

        WmTempletContractSignBo partyASigner = wmContractSignService.getPartyASigner(contractBo.getSignBoList());
        WmTempletContractSignBo partyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());

        int partyASignerTime = DateUtil.date2Unixtime(DateUtil.string2DateDay(partyASigner.getSignTime()));
        int partyBSignerTime = DateUtil.date2Unixtime(DateUtil.string2DateDay(partyBSigner.getSignTime()));

        if (partyASignerTime > todayTime) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "甲方签约时间不可晚于今日。");
        }
        if (partyBSignerTime > todayTime) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "乙方签约时间不可晚于今日。");
        }
        if (!isLongEffectC2Contract && partyASignerTime > contractBo.getBasicBo().getDueDate()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "甲方签约时间不可晚于合同有效期。");
        }
        if (!isLongEffectC2Contract && partyBSignerTime > contractBo.getBasicBo().getDueDate()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "乙方签约时间不可晚于合同有效期。");
        }
        return true;
    }

    private boolean isXXYearsLater(Long dueDate, int xx) {
        DateTime xxYearsLater = DateTime.now().plusYears(xx);
        return dueDate > DateUtil.date2Unixtime(xxYearsLater.toDate());
    }
}
