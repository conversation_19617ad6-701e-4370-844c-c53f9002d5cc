package com.sankuai.meituan.waimai.customer.util;

import java.util.ArrayList;
import java.util.List;

import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.bo.WmSignSubContextBo;


/**
 * Created by lixuepeng on 2021/8/20
 */
public class SubContextUtil {

    public static List<WmSignSubContextBo> assemblySubContext(String originContext) {
        int perSubContextLength = MccConfig.subContextSize();
        List<WmSignSubContextBo> subContextBoList = new ArrayList<>();
        int sort = 0;
        while (perSubContextLength < originContext.length()) {
            WmSignSubContextBo subContextBo = new WmSignSubContextBo();
            subContextBo.setSort(sort);
            subContextBo.setSubContext(originContext.substring(0, perSubContextLength));
            subContextBoList.add(subContextBo);
            originContext = originContext.substring(perSubContextLength, originContext.length());
            sort++;
        }
        WmSignSubContextBo subContextBo = new WmSignSubContextBo();
        subContextBo.setSort(sort);
        subContextBo.setSubContext(originContext);
        subContextBoList.add(subContextBo);

        return subContextBoList;
    }
}
