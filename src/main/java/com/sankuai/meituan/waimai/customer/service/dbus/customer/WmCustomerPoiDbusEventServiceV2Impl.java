package com.sankuai.meituan.waimai.customer.service.dbus.customer;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.dbus.thriftV2.DataBusEventServiceV2;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.meituan.waimai.customer.constant.customer.MafkaTopicEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.MqCustomerConstant;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.mafka.product.MafkaSend;
import com.sankuai.meituan.waimai.customer.service.customer.mafka.product.MafkaSendHandleService;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerChangeNoticeBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerChangeNoticeTopicEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户门店监听
 */
@Slf4j
@Service
public class WmCustomerPoiDbusEventServiceV2Impl implements DataBusEventServiceV2.Iface {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private MafkaSendHandleService mafkaSendHandleService;

    @Resource(name = "customerPoiRelChangeNoticeProducer")
    private MafkaProducer customerPoiRelChangeNoticeProducer;

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) throws TException {
        log.info("[WmCustomerPoiDbusEventServiceV2Impl] handleUpdate::监听客户门店表变更metaJsonData = {}, dataMapJson = {}, diffJson = {}",
                JSON.toJSONString(metaJsonData), dataMapJson, diffJson);
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);
        WmCustomerChangeNoticeMsg msg = makeCustomerChangeCommonNoticeMsgHandleUpdate(utils);
        if (msg == null) {
            return StaticUtils.ok;
        }
        MafkaSend send = new MafkaSend();
        send.setTopic(MafkaTopicEnum.MAFKA_WAIMAI_BRAND_CHANGE_COMMON_NOTICE);
        send.setMsg(JSON.toJSONString(msg));
        mafkaSendHandleService.send(send);
        // 业务全量：发送mafka消息同步至es
        customerPoiRelChangeNoticeSend(msg);
        return StaticUtils.ok;
    }


    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        log.info("[WmCustomerPoiDbusEventServiceV2Impl] handleInsert::监听客户门店表metaJsonData = {}, dataMapJson = {}",
                JSON.toJSONString(metaJsonData), dataMapJson);
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(metaJsonData, dataMapJson);
        WmCustomerChangeNoticeMsg msg = makeCustomerChangeNoticeMsgFromHermesHandleInsert(utils);
        if (msg == null) {
            return StaticUtils.ok;
        }
        MafkaSend send = new MafkaSend();
        send.setTopic(MafkaTopicEnum.MAFKA_WAIMAI_BRAND_CHANGE_COMMON_NOTICE);
        send.setMsg(JSON.toJSONString(msg));
        mafkaSendHandleService.send(send);
        // 业务全量：发送mafka消息同步至es
        customerPoiRelChangeNoticeSend(msg);
        return StaticUtils.ok;
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        log.info("[WmCustomerPoiDbusEventServiceV2Impl] handleDelete::监听客户门店表metaJsonData = {}", JSON.toJSONString(metaJsonData));
        if (org.apache.commons.collections.MapUtils.isEmpty(metaJsonData) || StringUtils.isBlank(dataMapJson)) {
            return StaticUtils.ok;
        }
        return StaticUtils.ok;
    }

    /**
     * 发送mafka消息。topic：waimai.customer.poi.rel.change.notice
     *
     * @param msg 待发送的mafka消息
     */
    private void customerPoiRelChangeNoticeSend(WmCustomerChangeNoticeMsg msg) {
        try {
            // 指定partition发送
            customerPoiRelChangeNoticeProducer.sendMessage(JSON.toJSONString(msg), msg.getCur_value().get(MqCustomerConstant.CUSTOMER_ID));
        } catch (Exception e) {
            log.error("[customerPoiRelChangeNoticeSend] sendMessage消息发送异常，msg={}", JSON.toJSONString(msg), e);
        }
    }


    /**
     * 表新增时组织msg
     *
     * @param utils
     * @return
     */
    private WmCustomerChangeNoticeMsg makeCustomerChangeNoticeMsgFromHermesHandleInsert(DbusUtils utils) {
        try {
            WmCustomerPoiDB bean = transToBo(utils);
            if (bean == null) {
                return null;
            }
            WmCustomerChangeNoticeMsg msg = new WmCustomerChangeNoticeMsg();
            msg.setTopic(WmCustomerChangeNoticeTopicEnum.CUSTOMER_POI_REL.getId());
            msg.setBiz_type(WmCustomerChangeNoticeBizTypeEnum.WM_POI_ID.getId());
            msg.setBiz_id(bean.getWmPoiId().longValue());
            msg.setUtime(bean.getUtime());
            Map<String, Object> curValue = new HashMap<>();
            Map<String, Object> afterMap = utils.getAftMap();
            if (org.apache.commons.collections.MapUtils.isEmpty(afterMap)) {
                return null;
            }
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByIdRT(bean.getCustomerId());
            if (wmCustomerDB == null) {
                log.error("发送客户绑定门店消息的时候未查询到客户信息makeCustomerChangeNoticeMsgFromHermesHandleInsert::utils = {}", JSON.toJSONString(utils));
            }
            Long mtCustomerId = wmCustomerDB != null ? wmCustomerDB.getMtCustomerId() : 0;
            curValue.put(MqCustomerConstant.CUSTOMER_ID, objectToStr(afterMap.get(MqCustomerConstant.CUSTOMER_ID)));
            curValue.put(MqCustomerConstant.MT_CUSTOMER_ID, mtCustomerId);
            curValue.put(MqCustomerConstant.VALID, objectToStr(afterMap.get(MqCustomerConstant.VALID)));
            msg.setCur_value(curValue);
            return msg;
        } catch (WmCustomerException e) {
            log.error("makeCustomerChangeNoticeMsgFromHermesHandleInsert::utils = {},code={},msg={}", JSON.toJSONString(utils), e.getCode(), e.getMsg());
        }
        return null;
    }


    /**
     * 表更新时组织msg
     *
     * @param utils
     * @return
     */
    private WmCustomerChangeNoticeMsg makeCustomerChangeCommonNoticeMsgHandleUpdate(DbusUtils utils) {
        try {
            WmCustomerPoiDB bean = transToBo(utils);
            if (bean == null) {
                return null;
            }
            WmCustomerChangeNoticeMsg msg = new WmCustomerChangeNoticeMsg();
            msg.setTopic(WmCustomerChangeNoticeTopicEnum.CUSTOMER_POI_REL.getId());
            msg.setBiz_type(WmCustomerChangeNoticeBizTypeEnum.WM_POI_ID.getId());
            msg.setBiz_id(bean.getWmPoiId().longValue());
            msg.setUtime(bean.getUtime());
            Map<String, Object> curValue = new HashMap<>();
            Map<String, Object> preValue = new HashMap<>();
            Map<String, Object> afterMap = utils.getAftMap();
            Map<String, Object> diffMap = utils.getDiffMap();
            if (org.apache.commons.collections.MapUtils.isEmpty(afterMap) || org.apache.commons.collections.MapUtils.isEmpty(diffMap)) {
                return null;
            }
            if (!diffMap.containsKey(MqCustomerConstant.VALID)) {
                //只有valid发生变化的时候才发消息
                return null;
            }

            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByIdRT(bean.getCustomerId());
            if (wmCustomerDB == null) {
                log.error("发送客户绑定门店消息的时候未查询到客户信息makeCustomerChangeNoticeMsgFromHermesHandleInsert::utils = {}", JSON.toJSONString(utils));
            }
            Long mtCustomerId = wmCustomerDB != null ? wmCustomerDB.getMtCustomerId() : 0;

            preValue.put(MqCustomerConstant.CUSTOMER_ID, objectToStr(afterMap.get(MqCustomerConstant.CUSTOMER_ID)));
            preValue.put(MqCustomerConstant.MT_CUSTOMER_ID, mtCustomerId);
            preValue.put(MqCustomerConstant.VALID, objectToStr(afterMap.get(MqCustomerConstant.VALID)));

            curValue.put(MqCustomerConstant.CUSTOMER_ID, objectToStr(afterMap.get(MqCustomerConstant.CUSTOMER_ID)));
            curValue.put(MqCustomerConstant.MT_CUSTOMER_ID, mtCustomerId);
            curValue.put(MqCustomerConstant.VALID, objectToStr(afterMap.get(MqCustomerConstant.VALID)));

            if (diffMap.containsKey(MqCustomerConstant.CUSTOMER_ID)) {
                preValue.put(MqCustomerConstant.CUSTOMER_ID, objectToStr(diffMap.get(MqCustomerConstant.CUSTOMER_ID).toString()));
            }
            if (diffMap.containsKey(MqCustomerConstant.VALID)) {
                preValue.put(MqCustomerConstant.VALID, objectToStr(diffMap.get(MqCustomerConstant.VALID).toString()));
            }
            msg.setCur_value(curValue);
            msg.setPre_value(preValue);
            return msg;
        } catch (WmCustomerException e) {
            log.error("makeCustomerChangeNoticeMsgFromHermesHandleInsert::utils = {},code={},msg={}", JSON.toJSONString(utils), e.getCode(), e.getMsg());
        }
        return null;
    }


    private String objectToStr(Object o) {
        if (o == null) {
            return null;
        }
        return o.toString();
    }


    private WmCustomerPoiDB transToBo(DbusUtils utils) {
        Map<String, Object> aftMap = utils.getAftMap();
        String jsonString = "";
        if (MapUtils.isNotEmpty(aftMap)) {
            jsonString = JSON.toJSONString(aftMap);
        } else {
            jsonString = JSON.toJSONString(utils.getDataMap());
        }
        return JSON.parseObject(jsonString, WmCustomerPoiDB.class);
    }

}
