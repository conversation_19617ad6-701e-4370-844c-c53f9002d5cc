package com.sankuai.meituan.waimai.customer.service.agreement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmCustomerGrayServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.constant.sign.SignAreaModuleEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignManagerBzService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.customer.util.trans.PdfUrlTransUtils;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.BusinessShowContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.DeliveryAreaDetailBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.DeliveryAreaSignInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.SignInfoDetailBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.SupplyChainSignInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchPdfInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchQueryThriftParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.WmEcontractSignBatchBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Map.Entry;

import static com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant.SETTLE_TASK_TYPE;

/**
 * 商家端合同签约信息处理类
 * <AUTHOR>
 */
@Slf4j
@Service
public class WmSignInfoForBService {

    @Autowired
    private WmEcontractSignManagerBzService wmEcontractSignManagerBzService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmPoiClient wmPoiClient;

    @Autowired
    private EcontractBizService econtractBizService;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmContractService wmContractService;

    @Autowired
    private WmCustomerGrayServiceAdapter wmCustomerGrayServiceAdapter;

    @Autowired
    private WmEcontractBatchBizService wmEcontractBatchBizService;

    @Autowired
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;


    private static List<EcontractBatchTypeEnum> batchTypeList = Lists.newArrayList(EcontractBatchTypeEnum.GROUP_MEAL,
            EcontractBatchTypeEnum.QUA_REAL_LETTER, EcontractBatchTypeEnum.POI_PROMOTION_SERVICE, EcontractBatchTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT);

    private static List<EcontractBatchTypeEnum> mustSignBatchTypeList =
            Lists.newArrayList(EcontractBatchTypeEnum.C1_CONTRACT,
            EcontractBatchTypeEnum.SETTLE, EcontractBatchTypeEnum.DELIVERY, EcontractBatchTypeEnum.BATCH_DELIVERY,
            EcontractBatchTypeEnum.C2_CONTRACT, EcontractBatchTypeEnum.FOODCITY_STATEMENT,
            EcontractBatchTypeEnum.MEDIC_ORDER_SPLIT);

    public DeliveryAreaSignInfoBo queryDeliveryAreaSignInfoByWmCustomerIdAndBatchStatus(Integer wmCustomerId, List<Long> batchIdList, Integer batchStatus)
            throws TException, WmCustomerException {
        log.info("#queryDeliveryAreaSignInfoByWmCustomerIdAndBatchStatus,wmCustomerId={}, batchIdList={}, batchStatus={}", wmCustomerId,
                JSON.toJSONString(batchIdList), batchStatus);
        // 初始化result
        DeliveryAreaSignInfoBo result = new DeliveryAreaSignInfoBo();
        result.setWmCustomerId(new Long(wmCustomerId));
        Map<Long, List<DeliveryAreaDetailBo>> deliveryAreaDetailBoMap = Maps.newHashMap();
        for(Long batchId : batchIdList){
            deliveryAreaDetailBoMap.put(batchId, Lists.newArrayList());
        }
        result.setDeliveryAreaDetailBoMap(deliveryAreaDetailBoMap);
        // 多店任务降级
        if(ConfigUtilAdapter.getBoolean("queryDeliveryAreaSignInfoByWmCustomerIdAndBatchStatus_limit_multi_poi_open",false)){
            if(wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerId).size() > 1){
                return result;
            }
        }
        // 目标任务类型
        Set<EcontractBatchTypeEnum> checkedBatchType = WmEcontractBatchConstant.REQUIRED_TASK_TYPE_SET_FOR_DELIVERY_AREA;

        // 获取batch详情
        List<WmEcontractSignBatchBo> wmEcontractSignBatchBos = wmEcontractSignManagerBzService
                .batchQuerySimpleBatchResultByBatchId(batchIdList);

        // 过滤获取目标状态batch
        try{
            for(WmEcontractSignBatchBo wmEcontractSignBatchBo : wmEcontractSignBatchBos){
                List<DeliveryAreaDetailBo> deliveryAreaDetailBoList = Lists.newArrayList();
                if(wmEcontractSignBatchBo.getBatchState().equals(EcontractBatchStateEnum.getEcontractBatchStateEnumByType(batchStatus).getName())){
                    EcontractBatchContextBo econtractBatchContextBoTemp = JSONObject.parseObject(wmEcontractSignBatchBo.getBatchContext(), EcontractBatchContextBo.class);
                    if(!checkedBatchType.contains(econtractBatchContextBoTemp.getBatchTypeEnum())){
                        continue;
                    }
                    Map<Integer, SignInfoDetailBo> typeMap = Maps.newHashMap();
                    if(batchStatus == EcontractBatchStateEnum.IN_PROCESSING.getType()){
                        extracted(typeMap, econtractBatchContextBoTemp, true, false);
                    }
                    if(batchStatus == EcontractBatchStateEnum.SUCCESS.getType()){
                        extracted(typeMap, econtractBatchContextBoTemp, false, true);
                    }
                    List<SignInfoDetailBo> signInfoDetailBoList = Lists.newArrayList(typeMap.values());
                    for(SignInfoDetailBo signInfoDetailBo:signInfoDetailBoList){
                        if(!signInfoDetailBo.isHasAreaInfo()){
                            continue;
                        }
                        if((batchStatus == EcontractBatchStateEnum.IN_PROCESSING.getType() && signInfoDetailBo.isHasSigningTask())
                                || (batchStatus == EcontractBatchStateEnum.SUCCESS.getType() && signInfoDetailBo.isHasSigned())){
                            DeliveryAreaDetailBo deliveryAreaDetailBo = new DeliveryAreaDetailBo();
                            deliveryAreaDetailBo.setName(signInfoDetailBo.getAreaShowName());
                            deliveryAreaDetailBo.setAreaMoudle(signInfoDetailBo.getAreaModule());
                            deliveryAreaDetailBo.setType("delivery");//配送范围写死类型，后续如有扩展需要可修改
                            deliveryAreaDetailBoList.add(deliveryAreaDetailBo);
                        }
                    }
                    deliveryAreaDetailBoMap.put(new Long(wmEcontractSignBatchBo.getBatchId()), deliveryAreaDetailBoList);
                }
            }
            result.setDeliveryAreaDetailBoMap(deliveryAreaDetailBoMap);
        }catch(Exception e){
            log.warn("queryDeliveryAreaSignInfoByWmCustomerIdAndBatchStatus exception,wmCustomerId={}", wmCustomerId, e);
            return result;
        }
        log.info("queryDeliveryAreaSignInfoByWmCustomerIdAndBatchStatus,result={}",JSONObject.toJSONString(result));
        return result;
    }


    public SupplyChainSignInfoBo querySupplyChainSignInfoByWmPoiId(long wmPoiId)
            throws WmCustomerException, TException {
        log.info("#querySupplyChainSignInfoByWmPoiId,wmPoiId={}",wmPoiId);
        SupplyChainSignInfoBo result = new SupplyChainSignInfoBo();
        result.setWmPoiId(wmPoiId);
        result.setSignInfoDetailBoList(Lists.newArrayList());
        Set<Integer> customerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        if(customerIdSet == null || customerIdSet.size() != 1){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"门店关联客户异常");
        }
        Integer wmCustomerId = customerIdSet.iterator().next();
        if(ConfigUtilAdapter.getBoolean("querySupplyChainSignInfoByWmPoiId_limit_multi_poi_open",true)
                && wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerId).size() > 1){
            return result;
        }

        WmPoiDomain wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
        if(wmPoiDomain == null){
            return result;
        }
        Set<EcontractBatchTypeEnum> checkedBatchType = wmPoiDomain.getAgentId() > 0
                ? WmEcontractBatchConstant.REQUIRED_TASK_TYPE_SET_FOR_AGENT
                : WmEcontractBatchConstant.REQUIRED_TASK_TYPE_SET_FOR_DIRECT;

        checkedBatchType = Sets.newHashSet(checkedBatchType);

        //女娲灰度客户不用查询结算协议
        if (wmCustomerGrayServiceAdapter.isGrayCustomer(wmCustomerId)) {
            log.info("#querySupplyChainSignInfoByWmPoiId,客户是女娲灰度客户，不查询结算协议:wmCustomerId={}", wmCustomerId);
            checkedBatchType.removeAll(WmEcontractBatchConstant.BATCH_TYPE_MAP.get(SETTLE_TASK_TYPE));
        }

        List<EcontractBatchContextBo> inProcessingBatchContextBo = Lists.newArrayList();
        List<EcontractBatchContextBo> successedBatchContextBo = Lists.newArrayList();

        querySupplyChainSignInfoCommon(wmCustomerId, wmPoiId, checkedBatchType, inProcessingBatchContextBo, successedBatchContextBo);
        try {
            assembleSupplyChainSignInfoBo(wmCustomerId,wmPoiDomain.getAgentId() > 0,result,inProcessingBatchContextBo,successedBatchContextBo);
        } catch (IllegalAccessException e) {
            log.error("querySupplyChainSignInfoByWmPoiId exception,wmPoiId={}",wmPoiId,e);
        }
        log.info("#querySupplyChainSignInfoByWmPoiId,result={}",JSONObject.toJSONString(result));
        return result;
    }

    private void querySupplyChainSignInfoCommon(Integer wmCustomerId,
                                                Long wmPoiId,
                                                Set<EcontractBatchTypeEnum> checkedBatchType,
                                                List<EcontractBatchContextBo> inProcessingBatchContextBo,
                                                List<EcontractBatchContextBo> successedBatchContextBo) throws TException, WmCustomerException {
        SignBatchQueryThriftParam thriftParam = new SignBatchQueryThriftParam();
        thriftParam.setWmCustomerId(wmCustomerId);
        /* 获取签约任务数据 begin */
        List<Long> batchIdList = wmEcontractSignManagerBzService.queryBatchBoIdList(thriftParam);
        if(CollectionUtils.isEmpty(batchIdList)){
            return;
        }
        List<WmEcontractSignBatchBo> wmEcontractSignBatchBos = wmEcontractSignManagerBzService
                .batchQuerySimpleBatchResultByBatchId(batchIdList);
        log.info("querySupplyChainSignInfoCommon#wmEcontractSignBatchBos:{}", JSON.toJSONString(wmEcontractSignBatchBos));
        /* 获取签约任务数据 end */
        EcontractBatchContextBo econtractBatchContextBoTemp = null;
        //按照任务状态分组
        for(WmEcontractSignBatchBo temp : wmEcontractSignBatchBos){
            econtractBatchContextBoTemp = JSONObject.parseObject(temp.getBatchContext(), EcontractBatchContextBo.class);
            if(!checkedBatchType.contains(econtractBatchContextBoTemp.getBatchTypeEnum())){
                continue;
            }
            //进行中
            if(EcontractBatchStateEnum.IN_PROCESSING.getName().equals(temp.getBatchState())
                    && ((wmPoiId > 0 && wmPoiId != null) ? checkWmPoiBelong(wmPoiId,econtractBatchContextBoTemp.getWmPoiIdList()) : true)){
                inProcessingBatchContextBo.add(econtractBatchContextBoTemp);
            }
            //已完成
            else if(EcontractBatchStateEnum.SUCCESS.getName().equals(temp.getBatchState())
                    && ((wmPoiId > 0 && wmPoiId != null) ? checkWmPoiBelong(wmPoiId,econtractBatchContextBoTemp.getWmPoiIdList()) : true)){
                successedBatchContextBo.add(econtractBatchContextBoTemp);
            }
        }
        log.info("querySupplyChainSignInfoCommon,inProcessingBatchContextBo:{},successedBatchContextBo:{}",
                JSON.toJSONString(inProcessingBatchContextBo),
                JSON.toJSONString(successedBatchContextBo));
    }

    private boolean checkWmPoiBelong(long wmPoiId,List<Long> wmPoiIdList){
        if(!ConfigUtilAdapter.getBoolean("querySupplyChainSignInfoByWmPoiId_checkWmPoiBelong_open",true)){
            return true;
        }
        if(CollectionUtils.isEmpty(wmPoiIdList)){
            return true;
        }
        return wmPoiIdList.contains(wmPoiId);
    }


    private void assembleSupplyChainSignInfoBo(Integer wmCustomerId ,boolean isAgent,SupplyChainSignInfoBo result,
            List<EcontractBatchContextBo> inProcessingBatchContextBo,
            List<EcontractBatchContextBo> successedBatchContextBo) throws IllegalAccessException {
        Map<Integer, SignInfoDetailBo> typeMap = Maps.newHashMap();
        //处理必签任务类型
        for(EcontractBatchContextBo temp : inProcessingBatchContextBo){
            extracted(typeMap, temp, true, false);
        }
        for(EcontractBatchContextBo temp : successedBatchContextBo){
            extracted(typeMap, temp, false, false);
        }
        Set<Integer> mustSignTypeSet = Sets.newHashSet(isAgent ? BusinessShowContractTypeEnum.MUST_SIGN_TYPE_SET_FOR_AGENT : BusinessShowContractTypeEnum.MUST_SIGN_TYPE_SET_FOR_DIRECT);
        if (wmCustomerGrayServiceAdapter.isGrayCustomer(wmCustomerId)){
            log.info("assembleSupplyChainSignInfoBo判断是否必签合同，客户为女娲灰度客户，无需判断结算协议 customer_id = {}",wmCustomerId);
            mustSignTypeSet.remove(BusinessShowContractTypeEnum.settle.getType());
        }
        mustSignTypeSet.removeAll(typeMap.keySet());
        SignInfoDetailBo signInfoDetailBo = null;
        //补充必签合同
        for(Integer lackType : mustSignTypeSet){
            signInfoDetailBo = new SignInfoDetailBo();
            signInfoDetailBo.setBusinessShowContractType(lackType);
            if(BusinessShowContractTypeEnum.findByType(lackType) == null){
                continue;
            }
            signInfoDetailBo.setName(BusinessShowContractTypeEnum.findByType(lackType).getName());
            signInfoDetailBo.setMustSign(true);
            typeMap.put(lackType,signInfoDetailBo);
        }
        List<SignInfoDetailBo> signInfoDetailBoList = Lists.newArrayList(typeMap.values());
        Collections.sort(signInfoDetailBoList,new Comparator<SignInfoDetailBo>(){
            @Override
            public int compare(SignInfoDetailBo o1, SignInfoDetailBo o2) {
                return o1.getBusinessShowContractType()-o2.getBusinessShowContractType();
            }
        });
        //查询门店C1、C2合同状态,针对C1、C2已签署状态重置
        try{
            for(SignInfoDetailBo temp : signInfoDetailBoList){
                if(temp.getBusinessShowContractType() == BusinessShowContractTypeEnum.c1contract.getType()){
                    //查当前C1合同状态
                    List<WmTempletContractBasicBo> c1Contract = wmContractService
                            .getAuditedContractBasicListByPoiIdAndType(result.getWmPoiId(),
                                    Lists.newArrayList(
                                            WmTempletContractTypeEnum.C1_E.getCode(),
                                            WmTempletContractTypeEnum.C1_PAPER.getCode()), 0, "系统");
                    if(!CollectionUtils.isEmpty(c1Contract)){
                        temp.setHasSigned(true);
                    }
                }else if(isAgent && temp.getBusinessShowContractType() == BusinessShowContractTypeEnum.c2contract.getType()){
                    //查当前C2合同状态
                    List<WmTempletContractBasicBo> c2Contract = wmContractService
                            .getAuditedContractBasicListByPoiIdAndType(result.getWmPoiId(),
                                    Lists.newArrayList(
                                            WmTempletContractTypeEnum.C2_PAPER.getCode(),
                                            WmTempletContractTypeEnum.C2_E.getCode()), 0, "系统");
                    if(!CollectionUtils.isEmpty(c2Contract)){
                        temp.setHasSigned(true);
                    }
                }
            }
        }catch(Exception e){
            log.error("query contract info error!",e);
        }

        //手动打包任务兼容
        List<SignInfoDetailBo> manualPackSignInfoDetailBoList = Lists.newArrayList();
        Map<Long,List<EcontractBatchContextBo>> inprocessingPackBatchMap = Maps.newHashMap();
        for(EcontractBatchContextBo temp : inProcessingBatchContextBo){
            if(inprocessingPackBatchMap.get(temp.getSignPackId()) == null){
                List<EcontractBatchContextBo> boList = Lists.newArrayList();
                boList.add(temp);
                inprocessingPackBatchMap.put(temp.getSignPackId(), boList);
            }else{
                List<EcontractBatchContextBo> boList = inprocessingPackBatchMap.get(temp.getSignPackId());
                boList.add(temp);
                inprocessingPackBatchMap.put(temp.getSignPackId(), boList);
            }
        }
        log.info("inprocessing手动打包任务分组:{}",JSON.toJSONString(inprocessingPackBatchMap));
        for(EcontractBatchContextBo temp : inProcessingBatchContextBo){
            if(isManualPack(temp.getBatchId())){
                log.info("处理手动打包进行中任务:{}",temp.getBatchId());
                if(inprocessingPackBatchMap.get(temp.getSignPackId()) == null){
                    continue;
                }
                List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.queryBatchListByPackId(temp.getSignPackId());
                boolean isContainsMustSignType = false;
                for(WmEcontractSignBatchDB batchDB : signBatchDBList){
                    EcontractBatchContextBo econtractBatchContextBoTemp = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
                    if(mustSignBatchTypeList.contains(econtractBatchContextBoTemp.getBatchTypeEnum())){
                        isContainsMustSignType = true;
                        break;
                    }
                }
                if(isContainsMustSignType){
                    log.info("手动打包进行中任务包含必签任务:{}",temp.getBatchId());
                    for(WmEcontractSignBatchDB batchDB : signBatchDBList){

                        EcontractBatchContextBo econtractBatchContextBoTemp = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
                        //配送多协议处理 与 其他任务单协议分开处理
                        if((econtractBatchContextBoTemp.getBatchTypeEnum() == EcontractBatchTypeEnum.DELIVERY
                                || econtractBatchContextBoTemp.getBatchTypeEnum() == EcontractBatchTypeEnum.BATCH_DELIVERY
                                || econtractBatchContextBoTemp.getBatchTypeEnum() == EcontractBatchTypeEnum.DRONE_DELIVERY
                                || econtractBatchContextBoTemp.getBatchTypeEnum() == EcontractBatchTypeEnum.FRUIT_TOGETHER_DELIVERY)
                                && PdfUrlTransUtils.isPdfJsonFormat(econtractBatchContextBoTemp.getDownLoadUrl())){
                            BatchPdfInfo batchPdfInfo = JSONObject.parseObject(econtractBatchContextBoTemp.getDownLoadUrl(), BatchPdfInfo.class);
                            Map<BusinessShowContractTypeEnum, String> typeUrlMap = batchPdfInfo.objectToTypeMap();
                            for(Entry<BusinessShowContractTypeEnum, String> entry : typeUrlMap.entrySet()){
                                SignInfoDetailBo generalSignInfo = new SignInfoDetailBo();
                                generalSignInfo.setBusinessShowContractType(entry.getKey().getType());
                                generalSignInfo.setMustSign(true);
                                generalSignInfo.setHasSigned(false);
                                generalSignInfo.setHasSigningTask(true);
                                generalSignInfo.setUrl(econtractBatchContextBoTemp.getDownLoadUrl());
                                generalSignInfo.setBatchId(econtractBatchContextBoTemp.getBatchId());
                                generalSignInfo.setHasAreaInfo(false);
                                manualPackSignInfoDetailBoList.add(generalSignInfo);
                            }
                        } else {
                            SignInfoDetailBo generalSignInfo = new SignInfoDetailBo();
                            generalSignInfo.setBusinessShowContractType(BusinessShowContractTypeEnum.findByBatchType(econtractBatchContextBoTemp.getBatchTypeEnum()).getType());
                            generalSignInfo.setName(BusinessShowContractTypeEnum.findByBatchType(econtractBatchContextBoTemp.getBatchTypeEnum()).getName());
                            generalSignInfo.setMustSign(true);
                            generalSignInfo.setHasSigned(false);
                            generalSignInfo.setHasSigningTask(true);
                            generalSignInfo.setUrl(econtractBatchContextBoTemp.getDownLoadUrl());
                            generalSignInfo.setBatchId(econtractBatchContextBoTemp.getBatchId());
                            generalSignInfo.setHasAreaInfo(false);
                            log.info("#assembleSupplyChainSignInfoBo#generalSignInfo = {}",
                                    JSON.toJSONString(generalSignInfo));
                            manualPackSignInfoDetailBoList.add(generalSignInfo);
                        }
                    }
                }
                inprocessingPackBatchMap.remove(temp.getSignPackId());
            }
        }



        Map<Long,List<EcontractBatchContextBo>> successPackBatchMap = Maps.newHashMap();
        for(EcontractBatchContextBo temp : successedBatchContextBo){
            if(successPackBatchMap.get(temp.getSignPackId()) == null){
                List<EcontractBatchContextBo> boList = Lists.newArrayList();
                boList.add(temp);
                successPackBatchMap.put(temp.getSignPackId(), boList);
            }else{
                List<EcontractBatchContextBo> boList = successPackBatchMap.get(temp.getSignPackId());
                boList.add(temp);
                successPackBatchMap.put(temp.getSignPackId(), boList);
            }
        }
        log.info("assembleSupplyChainSignInfoBo,success手动打包任务分组:{}",JSON.toJSONString(successPackBatchMap));
        for(EcontractBatchContextBo temp : successedBatchContextBo){
            if(isManualPack(temp.getBatchId())){
                log.info("处理手动打包已完成任务:{}",temp.getBatchId());
                if(successPackBatchMap.get(temp.getSignPackId()) == null){
                    continue;
                }
                List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.queryBatchListByPackId(temp.getSignPackId());
                boolean isContainsMustSignType = false;
                for(WmEcontractSignBatchDB batchDB : signBatchDBList){
                    EcontractBatchContextBo econtractBatchContextBoTemp = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
                    if(mustSignBatchTypeList.contains(econtractBatchContextBoTemp.getBatchTypeEnum())){
                        isContainsMustSignType = true;
                        break;
                    }
                }
                if(isContainsMustSignType){
                    log.info("手动打包已完成任务包含必签任务:{}",temp.getBatchId());
                    for(WmEcontractSignBatchDB batchDB : signBatchDBList){
                        EcontractBatchContextBo econtractBatchContextBoTemp = JSONObject.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
                        //配送多协议处理 与 其他任务单协议分开处理
                        if((econtractBatchContextBoTemp.getBatchTypeEnum() == EcontractBatchTypeEnum.DELIVERY
                                || econtractBatchContextBoTemp.getBatchTypeEnum() == EcontractBatchTypeEnum.BATCH_DELIVERY
                                || econtractBatchContextBoTemp.getBatchTypeEnum() == EcontractBatchTypeEnum.DRONE_DELIVERY
                                || econtractBatchContextBoTemp.getBatchTypeEnum() == EcontractBatchTypeEnum.FRUIT_TOGETHER_DELIVERY)
                                && PdfUrlTransUtils.isPdfJsonFormat(econtractBatchContextBoTemp.getDownLoadUrl())){
                            BatchPdfInfo batchPdfInfo = JSONObject.parseObject(econtractBatchContextBoTemp.getDownLoadUrl(), BatchPdfInfo.class);
                            Map<BusinessShowContractTypeEnum, String> typeUrlMap = batchPdfInfo.objectToTypeMap();
                            for(Entry<BusinessShowContractTypeEnum, String> entry : typeUrlMap.entrySet()){
                                SignInfoDetailBo generalSignInfo = new SignInfoDetailBo();
                                generalSignInfo.setBusinessShowContractType(entry.getKey().getType());
                                generalSignInfo.setName(entry.getKey().getName());
                                generalSignInfo.setMustSign(true);
                                generalSignInfo.setHasSigned(true);
                                generalSignInfo.setHasSigningTask(false);
                                generalSignInfo.setUrl(entry.getValue());
                                generalSignInfo.setBatchId(econtractBatchContextBoTemp.getBatchId());
                                generalSignInfo.setHasAreaInfo(false);
                                manualPackSignInfoDetailBoList.add(generalSignInfo);
                            }
                        }else{
                            SignInfoDetailBo generalSignInfo = new SignInfoDetailBo();
                            generalSignInfo.setBusinessShowContractType(BusinessShowContractTypeEnum.findByBatchType(econtractBatchContextBoTemp.getBatchTypeEnum()).getType());
                            generalSignInfo.setName(BusinessShowContractTypeEnum.findByBatchType(econtractBatchContextBoTemp.getBatchTypeEnum()).getName());
                            generalSignInfo.setMustSign(true);
                            generalSignInfo.setHasSigned(true);
                            generalSignInfo.setHasSigningTask(false);
                            generalSignInfo.setUrl(econtractBatchContextBoTemp.getDownLoadUrl());
                            generalSignInfo.setBatchId(econtractBatchContextBoTemp.getBatchId());
                            generalSignInfo.setHasAreaInfo(false);
                            manualPackSignInfoDetailBoList.add(generalSignInfo);
                        }
                    }
                }
                successPackBatchMap.remove(temp.getSignPackId());
            }
        }
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(manualPackSignInfoDetailBoList)){
            result.setSignInfoDetailBoList(manualPackSignInfoDetailBoList);
        }else{
            result.setSignInfoDetailBoList(signInfoDetailBoList);
        }
    }

    private boolean isManualPack(Long batchId) {
        WmEcontractSignBatchDB wmEcontractSignBatchDB = wmEcontractBatchBizService.queryByBatchId(batchId);
        if(wmEcontractSignBatchDB == null){
            return false;
        }else{
            return wmEcontractSignBatchDB.getPackId() > 0;
        }
    }


    private void extracted(Map<Integer, SignInfoDetailBo> typeMap, EcontractBatchContextBo temp, boolean inProcessing,
            boolean isAddAreaInfoForSuccessBo) throws IllegalAccessException {
        String downLoadUrl = temp.getDownLoadUrl();
        BatchPdfInfo batchPdfInfo = new BatchPdfInfo();
        if (StringUtils.isEmpty(downLoadUrl) || downLoadUrl.equals("null")) {
            return;
        }
        // 打包模块
        if (PdfUrlTransUtils.isPdfJsonFormat(downLoadUrl)) {
            batchPdfInfo = JSONObject.parseObject(downLoadUrl, BatchPdfInfo.class);
        }
        // 普通模块
        else {
            if (temp.getBatchTypeEnum() == EcontractBatchTypeEnum.C1_CONTRACT) {
                batchPdfInfo.setC1contract(downLoadUrl);
            } else if (temp.getBatchTypeEnum() == EcontractBatchTypeEnum.C2_CONTRACT) {
                batchPdfInfo.setC2contract(downLoadUrl);
            } else if (temp.getBatchTypeEnum() == EcontractBatchTypeEnum.DELIVERY
                    || temp.getBatchTypeEnum() == EcontractBatchTypeEnum.BATCH_DELIVERY) {
                batchPdfInfo.setDelivery(downLoadUrl);
            } else if (temp.getBatchTypeEnum() == EcontractBatchTypeEnum.SETTLE) {
                batchPdfInfo.setSettle(downLoadUrl);
            } else if (temp.getBatchTypeEnum() == EcontractBatchTypeEnum.FOODCITY_STATEMENT) {
                batchPdfInfo.setFoodcity_statement(downLoadUrl);
            } else if (temp.getBatchTypeEnum() == EcontractBatchTypeEnum.MEDIC_ORDER_SPLIT) {
                batchPdfInfo.setMedic_order_split(downLoadUrl);
            } else if (temp.getBatchTypeEnum() == EcontractBatchTypeEnum.SUBJECT_CHANGE_SUPPLEMENT) {
                batchPdfInfo.setSubject_change_supplement(downLoadUrl);
            } else if (temp.getBatchTypeEnum() == EcontractBatchTypeEnum.DRONE_DELIVERY) {
                batchPdfInfo.setDrone(downLoadUrl);
            } else if (EcontractBatchTypeEnum.isNationalSubsidyDeliveryType(temp.getBatchTypeEnum())) {
                batchPdfInfo.setNational_subsidy_delivery_performance_service(downLoadUrl);
            }
        }
        // pdf粒度的合同数据转换为SignInfoDetailBo
        Map<BusinessShowContractTypeEnum, String> typeUrlMap = batchPdfInfo.objectToTypeMap();
        // 如有配送数据，查询SLA信息
        Map<String, Boolean> hasAreaInfoMap = Maps.newHashMap();
        if (typeUrlMap.containsKey(BusinessShowContractTypeEnum.delivery)
                || typeUrlMap.containsKey(BusinessShowContractTypeEnum.drone)
                || typeUrlMap.containsKey(BusinessShowContractTypeEnum.national_subsidy_delivery_performance_service)) {
            hasAreaInfoMap = hasAreaInfo(temp.getBatchId());
        }
        for (Entry<BusinessShowContractTypeEnum, String> entry : typeUrlMap.entrySet()) {
            SignInfoDetailBo signInfoDetailBo = typeMap.get(entry.getKey().getType());
            if (signInfoDetailBo == null) {
                signInfoDetailBo = new SignInfoDetailBo();
            }
            // 处理已完成的数据
            if (!inProcessing) {
                signInfoDetailBo.setBusinessShowContractType(entry.getKey().getType());
                signInfoDetailBo.setName(entry.getKey().getName());
                signInfoDetailBo.setMustSign(true);
                signInfoDetailBo.setHasSigned(true);
                if (isAddAreaInfoForSuccessBo) {
                    // 将配送范围的信息添加到已完成的任务中
                    addDeliveryAreaInfoToSignInfoDetailBo(entry, signInfoDetailBo, hasAreaInfoMap);
                }
            } else {
                signInfoDetailBo.setBusinessShowContractType(entry.getKey().getType());
                signInfoDetailBo.setName(entry.getKey().getName());
                signInfoDetailBo.setMustSign(true);
                signInfoDetailBo.setHasSigningTask(true);
                signInfoDetailBo.setUrl(entry.getValue());
                signInfoDetailBo.setBatchId(temp.getBatchId());
                addDeliveryAreaInfoToSignInfoDetailBo(entry, signInfoDetailBo, hasAreaInfoMap);
            }
            typeMap.put(entry.getKey().getType(), signInfoDetailBo);
        }
    }

    private void addDeliveryAreaInfoToSignInfoDetailBo(Entry<BusinessShowContractTypeEnum, String> entry,
                                                       SignInfoDetailBo signInfoDetailBo,
                                                       Map<String,Boolean> hasAreaInfoMap){
        String typeEnumName = entry.getKey().toString();
        for (SignAreaModuleEnum signAreaModuleEnum : SignAreaModuleEnum.values()) {
            if (typeEnumName.equals(signAreaModuleEnum.getBusinessShowContractType())
                    && hasAreaInfoMap.containsKey(signAreaModuleEnum.getCode())) {
                signInfoDetailBo.setHasAreaInfo(true);
                signInfoDetailBo.setAreaModule(signAreaModuleEnum.getCode());
                signInfoDetailBo.setAreaShowName(signAreaModuleEnum.getModuleName());
                break;
            }
        }
    }

    private Map<String,Boolean> hasAreaInfo(long batchId){
        Map<String,Boolean> result = Maps.newHashMap();
        String recordKey = wmEcontractSignBzService.queryRecordIdByBatchId(batchId);
        if(StringUtils.isEmpty(recordKey)){
            return result;
        }
        try {
            Map<String, String> spAreaMap = econtractBizService.queryEcontractSpAreaData(recordKey);
            if(MapUtils.isEmpty(spAreaMap)){
                return result;
            }
            for (Entry<String, String> entry : spAreaMap.entrySet()) {
                // 枚举类型中，default为关键字，所以特殊处理
                if (entry.getKey().equals("default")) {
                    result.put(SignAreaModuleEnum.delivery.getCode(), true);
                    continue;
                }
                SignAreaModuleEnum signAreaModuleEnum = SignAreaModuleEnum.getByCode(entry.getKey());
                if (signAreaModuleEnum == null) {
                    continue;
                }
                result.put(signAreaModuleEnum.getCode(), true);
            }
        } catch (EcontractException | TException e) {
            log.error("queryEcontractSpAreaData exception",e);
        }
        return result;
    }

}
