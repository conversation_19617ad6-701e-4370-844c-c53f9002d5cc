package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.repository.WmSettlePaperSignRepository;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.repository.WmSettlePoiRepository;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.repository.WmSettleRepository;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleInputContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.WmSettleInputSaveAtomService;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleLogService;
import com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleModifyBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiSettle;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.BeanDiffUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WmSettleInputSaveAtomServiceImpl implements WmSettleInputSaveAtomService {

    @Autowired
    private WmSettleRepository wmSettleRepository;
    @Autowired
    private WmSettlePoiRepository wmSettlePoiRepository;
    @Autowired
    private WmSettlePaperSignRepository wmSettlePaperSignRepository;
    @Autowired
    private WmSettleLogService wmSettleLogService;

    @Override
    //WmSettleModifyBo.WmSettle
    //OpUid
    //OpUname
    public int saveOrUpdateOfflineSettle(WmSettleInputContext context) throws WmCustomerException {
        WmSettle wmSettle = context.getWmSettleModifyBo().getWmSettle();

        int res;
        if (wmSettle.getId() > 0) {
            res = updateWmSettle(context);
            updateWmPoiSettle(context);
        } else {
            WmSettleDB wmSettleDB = WmSettleTransUtil.wmSettleThrift2DB(wmSettle);
            wmSettleRepository.insertSelective(wmSettleDB);
            res = wmSettleDB.getId();
            wmSettle.setId(wmSettleDB.getId());
            List<WmPoiSettle> addSettleList = WmSettleTransUtil
                    .transWmPoiIdList2PoiSettle(wmSettle.getWmPoiIdList(),
                            wmSettle.getWm_contract_id(),
                            wmSettle.getId());
            context.setAddSettleList(addSettleList);
            insertWmPoiSettleDB(context);
        }
        return res;
    }

    @Override
    //WmSettleModifyBo.WmCustomerId
    //WmSettleModifyBo.SupplementalUrl
    //WmSettleModifyBo.QdbUrl
    //OpUid
    //OpUname
    public int saveOrUpdateWmSettleProtocol(WmSettleInputContext context)
            throws WmCustomerException {
        WmSettleModifyBo wmSettleModifyBo = context.getWmSettleModifyBo();
        int wmCustomerId = wmSettleModifyBo.getWmCustomerId();
        String supplementalUrl = wmSettleModifyBo.getSupplementalUrl();
        String qdbUrl = wmSettleModifyBo.getQdbUrl();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();

        WmSettleProtocolDB wmSettleProtocolDB = wmSettlePaperSignRepository.selectByCustomer(wmCustomerId);
        StringBuffer log = new StringBuffer();
        if (wmSettleProtocolDB == null) {
            wmSettleProtocolDB = new WmSettleProtocolDB();
            wmSettleProtocolDB.setWmCustomerId(wmCustomerId);
            wmSettleProtocolDB.setSupplementalUrl(supplementalUrl);
            wmSettleProtocolDB.setQdbUrl(qdbUrl);
            log.append("===新增补充协议======" +
                    "\n[字段变更]补充协议: =>"+supplementalUrl+";\n");
            if(StringUtils.isNotBlank(qdbUrl)){
                log.append("[\n字段变更\n" +
                        "]钱袋宝协议: =>"+qdbUrl+";\n");
            }
            wmSettleLogService.insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.INSERT, opUid, opUname ,log.toString());
            return wmSettlePaperSignRepository.insert(wmSettleProtocolDB);
        } else {
            if(supplementalUrl.equals(wmSettleProtocolDB.getSupplementalUrl()) && qdbUrl.equals(wmSettleProtocolDB.getQdbUrl())){
                return 0;
            }
            log.append("===修改补充协议======" +
                    "\n[字段变更]补充协议: "+ wmSettleProtocolDB.getSupplementalUrl() +" =>" + supplementalUrl + ";\n");
            if (StringUtils.isNotBlank(qdbUrl)) {
                log.append("\n[字段变更]钱袋宝协议: "+ wmSettleProtocolDB.getQdbUrl() +" =>" + qdbUrl + ";\n");
            }
            wmSettleLogService.insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.UPDATE, opUid, opUname, log.toString());
            wmSettleProtocolDB.setSupplementalUrl(supplementalUrl);
            wmSettleProtocolDB.setQdbUrl(qdbUrl);
            return wmSettlePaperSignRepository.updateWmSettleProtocol(wmSettleProtocolDB);
        }
    }

    /**
     * 更新单个结算
     */
    private int updateWmSettle(WmSettleInputContext context)
            throws WmCustomerException {
        WmSettle wmSettle = context.getWmSettleModifyBo().getWmSettle();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();
        log.info("updateWmSettle wmSettle = {}, opUid = {}, opUname = {}",
                JSON.toJSONString(wmSettle),
                opUid, opUname);
        if (wmSettle == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "保存结算不能为空");
        }
        if (wmSettle.getId() <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "新增不允许包含结算id");
        }
        if (opUid <= 0 || StringUtils.isEmpty(opUname)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "操作人不能为空");
        }

        WmSettleDB wmSettleDB = WmSettleTransUtil.wmSettleThrift2DB(wmSettle);
        wmSettleDB.setValid((byte) 1);//需要更新的结算信息都会valid=1
        log.info("updateWmSettle wmSettleDB = {}", JSON.toJSONString(wmSettleDB));
        wmSettleRepository.updateByPrimaryKeySelectiveWithValid(wmSettleDB);
        return wmSettleDB.getId();
    }

    private boolean updateWmPoiSettle(WmSettleInputContext context)
            throws WmCustomerException {
        WmSettle wmSettle = context.getWmSettleModifyBo().getWmSettle();
        List<Integer>
                storeList = wmSettlePoiRepository.getWmPoiIdListBySettleId(wmSettle.getId());
        List<Integer> storeListFromSwitchCentre = wmSettlePoiRepository.getWmPoiIdListBySettleIdFromSwitchCentre(wmSettle.getWmCustomerId(),wmSettle.getId());
        storeList.addAll(storeListFromSwitchCentre);

        //新增结算关联门店
        List<Integer> addList = BeanDiffUtil.genAddList(wmSettle.getWmPoiIdList(), storeList);
        log.info("updateWmPoiSettle addList = {}", JSON.toJSONString(addList));
        List<WmPoiSettle> addSettleList = WmSettleTransUtil
                .transWmPoiIdList2PoiSettle(addList, wmSettle.getWm_contract_id(), wmSettle.getId());
        context.setAddSettleList(addSettleList);
        insertWmPoiSettleDB(context);

        //修改结算关联门店
        List<Integer> commonList = BeanDiffUtil.genCommonList(wmSettle.getWmPoiIdList(), storeList);
        log.info("updateWmPoiSettle commonList = {}", JSON.toJSONString(commonList));
        List<WmPoiSettle> commonSettleList = WmSettleTransUtil
                .transWmPoiIdList2PoiSettle(commonList, wmSettle.getWm_contract_id(), wmSettle.getId());
        context.setCommonSettleList(commonSettleList);
        updateWmPoiSettleDB(context);

        //删除结算关联门店
        List<Integer> deleteList = BeanDiffUtil.genDeleteList(wmSettle.getWmPoiIdList(), storeList);
        log.info("updateWmPoiSettle deleteList = {}", JSON.toJSONString(deleteList));
        context.setDeleteList(deleteList);
        deleteWmPoiSettleDB(context);
        return true;
    }

    private boolean insertWmPoiSettleDB(WmSettleInputContext context) throws WmCustomerException {
        List<WmPoiSettle> wmPoiSettleList = context.getAddSettleList();
        if (CollectionUtils.isEmpty(wmPoiSettleList)) {
            return true;
        }
        log.info("insertWmPoiSettle wmPoiSettleList = {}", JSON.toJSONString(wmPoiSettleList));

        Set<Long> switchingWmPoiIdSet = context.getSwitchingWmPoiIdSet();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();
        Map<Long, Integer> wmSettlePoiMap = Maps.newHashMap();

        for (WmPoiSettle wmPoiSettle : wmPoiSettleList) {
            //切换中门店
            if (switchingWmPoiIdSet.contains((long) wmPoiSettle.getWmPoiId())) {
                wmSettlePoiMap.put((long) wmPoiSettle.getWmPoiId(), wmPoiSettle.getWmSettleId());
            } else {
                WmPoiSettleDB wmPoiSettleDB = new WmPoiSettleDB();
                wmPoiSettleDB.setWm_contract_id(wmPoiSettle.getWmContractId());
                wmPoiSettleDB.setWm_settle_id(wmPoiSettle.getWmSettleId());
                wmPoiSettleDB.setWm_poi_id(wmPoiSettle.getWmPoiId());
                wmSettlePoiRepository.insertSelective(wmPoiSettleDB);
            }
        }
        wmSettlePoiRepository.insertIntoOfflineToSwitchCentre(context.getWmCustomerId(), wmSettlePoiMap, context.getSettleSwitchPoiInfoList(), opUid,
                opUname);
        return true;
    }

    private boolean updateWmPoiSettleDB(WmSettleInputContext context) throws WmCustomerException {
        List<WmPoiSettle> wmPoiSettleList = context.getCommonSettleList();
        Set<Long> switchingWmPoiIdSet = context.getSwitchingWmPoiIdSet();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();
        Map<Long, Integer> wmSettlePoiMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(wmPoiSettleList)) {
            return true;
        }
        for (WmPoiSettle wmPoiSettle : wmPoiSettleList) {
            //切换中门店
            if (switchingWmPoiIdSet.contains((long) wmPoiSettle.getWmPoiId())) {
                wmSettlePoiMap.put((long) wmPoiSettle.getWmPoiId(), wmPoiSettle.getWmSettleId());
            } else {
                WmPoiSettleDB wmPoiSettleDB = new WmPoiSettleDB();
                wmPoiSettleDB.setWm_contract_id(wmPoiSettle.getWmContractId());
                wmPoiSettleDB.setWm_poi_id(wmPoiSettle.getWmPoiId());
                wmPoiSettleDB.setWm_settle_id(wmPoiSettle.getWmSettleId());
                wmSettlePoiRepository.updateByContractAndPoiId(wmPoiSettleDB);
            }
        }
        wmSettlePoiRepository.updateOfflineToSwitchCentre(context.getWmCustomerId(), wmSettlePoiMap, context.getSettleSwitchPoiInfoList(), opUid,
                opUname);
        return true;
    }

    private boolean deleteWmPoiSettleDB(WmSettleInputContext context) throws WmCustomerException {
        int wmSettleId = context.getWmSettleModifyBo().getWmSettle().getId();
        List<Integer> wmPoiIdList = context.getDeleteList();
        Set<Long> switchingWmPoiIdSet = context.getSwitchingWmPoiIdSet();
        int opUid = context.getOpUid();
        String opUname = context.getOpUname();
        Map<Long, Integer> wmSettlePoiMap = Maps.newHashMap();

        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return true;
        }
        wmSettlePoiRepository.deleteByWmSettleIdAndWmPoiIdList(wmSettleId, wmPoiIdList);

        for (Integer temp : wmPoiIdList) {
            //切换中门店
            if (switchingWmPoiIdSet.contains(temp.longValue())) {
                wmSettlePoiMap.put(temp.longValue(), wmSettleId);
            }
        }
        wmSettlePoiRepository.deleteOfflineToSwitchCentre(context.getWmCustomerId(), wmSettlePoiMap, context.getSettleSwitchPoiInfoList(), opUid,
                opUname);
        return true;
    }
}
