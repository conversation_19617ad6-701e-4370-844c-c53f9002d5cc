package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryCompanyCustomerLongDistanceInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKEV2_FEEMODE)
public class CompanycustomerLongdistanceWaimaiQikeV2PdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker {
    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {
        log.info("CompanycustomerLongdistanceWaimaiQikeV2PdfMaker, customerId:{}, batchId:{}", originContext.getCustomerId(), originContext.getBatchId());
        SignTemplateEnum signTemplateEnum = extractSignTemplateEnum();
        List<EcontractDeliveryInfoBo> deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKEV2_FEEMODE.getName());
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();
        Map<String, String> subMap;
        for (EcontractDeliveryInfoBo infoBo : deliveryInfoList) {
            EcontractDeliveryCompanyCustomerLongDistanceInfoBo econtractDeliveryCompanyCustomerLongDistanceInfoBo = infoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo();
            econtractDeliveryCompanyCustomerLongDistanceInfoBo.setDeliveryArea(null);
            subMap = MapUtil.Object2Map(econtractDeliveryCompanyCustomerLongDistanceInfoBo);
            pdfBizContent.add(subMap);
        }

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("partA", StringUtils.defaultIfEmpty(originContext.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMetaContent.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMetaContent.put("supportCompanyCustomerLongDistance", WmEcontractContextUtil.SUPPORT_MARK);

        //基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKEV2_FEEMODE_TEMPLATE_ID", 127));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKEV2_FEEMODE_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        log.info("CompanycustomerLongdistanceWaimaiQikeV2PdfMaker, pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }
}
