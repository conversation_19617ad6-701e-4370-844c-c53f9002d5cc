package com.sankuai.meituan.waimai.customer.settle.domain;

import static com.sankuai.meituan.waimai.customer.settle.annotation.SettleEncryptEnum.ACC_CARDNO;
import static com.sankuai.meituan.waimai.customer.settle.annotation.SettleEncryptEnum.CERT_NUM;
import static com.sankuai.meituan.waimai.customer.settle.annotation.SettleEncryptEnum.LEGAL_CERT_NUM;
import static com.sankuai.meituan.waimai.customer.settle.annotation.SettleEncryptEnum.LEGAL_ID_CARD;
import static com.sankuai.meituan.waimai.customer.settle.annotation.SettleEncryptEnum.PARTY_A_FINANCE_PHONE;
import static com.sankuai.meituan.waimai.customer.settle.annotation.SettleEncryptEnum.RESERVE_PHONE;

import com.sankuai.meituan.waimai.customer.settle.annotation.SettleEncryptDomain;

@SettleEncryptDomain(fields = {PARTY_A_FINANCE_PHONE,ACC_CARDNO,LEGAL_CERT_NUM,LEGAL_ID_CARD,CERT_NUM,RESERVE_PHONE})
public class WmSettleAuditedDB {
    private Integer id;

    private Integer wm_contract_id;

    private Integer wmCustomerId;

    private Integer wm_settle_id;

    private Float min_pay_amount;

    private Integer pay_period_num;

    private Byte pay_period_unit;

    private Integer province;

    private Integer city;

    private Short bankid;

    private Integer branchid;

    private String branchname;

    private String acc_cardno;

    private String acc_name;

    private Byte acctype;

    private Byte valid;

    private Integer cuid;

    private Integer muid;

    private Integer ctime;

    private Integer utime;

    private Byte settle_type;

    private Byte pay_day_of_month;

    private String party_a_finance_people;

    private String party_a_finance_phone;

    private String name;

    private Integer version;

    private Short cert_type; // required
    private String legal_cert_num; // required
    private String legal_person; // required
    private String legal_id_card; // required
    private String cert_num; // required
    private String reserve_phone; // required
    private Integer card_type; // required
    public Long wm_wallet_id; // required

    private Integer card_valid=-1;
    private String card_invalid_reason;
    private Integer wallet_open_status=-1;
    private String wallet_open_reason;
    private Integer realname_status=-1;
    private String realname_reason;
    private Integer card_binding_status=-1;
    private String card_binding_reason;

    private String login_name;
    private Integer open_time=0;

    private String acc_cardno_encrypted;
    private String party_a_finance_phone_encrypted;
    private String legal_cert_num_encrypted;
    private String legal_id_card_encrypted;
    private String cert_num_encrypted;
    private String reserve_phone_encrypted;

    private String acc_cardno_token;
    private String party_a_finance_phone_token;
    private String legal_cert_num_token;
    private String legal_id_card_token;
    private String cert_num_token;
    private String reserve_phone_token;

    public Integer getWmCustomerId() {
        return wmCustomerId;
    }

    public void setWmCustomerId(Integer wmCustomerId) {
        this.wmCustomerId = wmCustomerId;
    }

    public Integer getWm_settle_id() {
        return wm_settle_id;
    }

    public void setWm_settle_id(Integer wm_settle_id) {
        this.wm_settle_id = wm_settle_id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getWm_contract_id() {
        return wm_contract_id;
    }

    public void setWm_contract_id(Integer wm_contract_id) {
        this.wm_contract_id = wm_contract_id;
    }

    public Float getMin_pay_amount() {
        return min_pay_amount;
    }

    public void setMin_pay_amount(Float min_pay_amount) {
        this.min_pay_amount = min_pay_amount;
    }

    public Integer getPay_period_num() {
        return pay_period_num;
    }

    public void setPay_period_num(Integer pay_period_num) {
        this.pay_period_num = pay_period_num;
    }

    public Byte getPay_period_unit() {
        return pay_period_unit;
    }

    public void setPay_period_unit(Byte pay_period_unit) {
        this.pay_period_unit = pay_period_unit;
    }

    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Short getBankid() {
        return bankid;
    }

    public void setBankid(Short bankid) {
        this.bankid = bankid;
    }

    public Integer getBranchid() {
        return branchid;
    }

    public void setBranchid(Integer branchid) {
        this.branchid = branchid;
    }

    public String getBranchname() {
        return branchname;
    }

    public void setBranchname(String branchname) {
        this.branchname = branchname == null ? null : branchname.trim();
    }

    public String getAcc_cardno() {
        return acc_cardno;
    }

    public void setAcc_cardno(String acc_cardno) {
        this.acc_cardno = acc_cardno == null ? null : acc_cardno.trim();
    }

    public String getAcc_name() {
        return acc_name;
    }

    public void setAcc_name(String acc_name) {
        this.acc_name = acc_name == null ? null : acc_name.trim();
    }

    public Byte getAcctype() {
        return acctype;
    }

    public void setAcctype(Byte acctype) {
        this.acctype = acctype;
    }

    public Byte getValid() {
        return valid;
    }

    public void setValid(Byte valid) {
        this.valid = valid;
    }

    public Integer getCuid() {
        return cuid;
    }

    public void setCuid(Integer cuid) {
        this.cuid = cuid;
    }

    public Integer getMuid() {
        return muid;
    }

    public void setMuid(Integer muid) {
        this.muid = muid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public Byte getSettle_type() {
        return settle_type;
    }

    public void setSettle_type(Byte settle_type) {
        this.settle_type = settle_type;
    }

    public Byte getPay_day_of_month() {
        return pay_day_of_month;
    }

    public void setPay_day_of_month(Byte pay_day_of_month) {
        this.pay_day_of_month = pay_day_of_month;
    }

    public String getParty_a_finance_people() {
        return party_a_finance_people;
    }

    public void setParty_a_finance_people(String party_a_finance_people) {
        this.party_a_finance_people = party_a_finance_people == null ? null : party_a_finance_people.trim();
    }

    public String getParty_a_finance_phone() {
        return party_a_finance_phone;
    }

    public void setParty_a_finance_phone(String party_a_finance_phone) {
        this.party_a_finance_phone = party_a_finance_phone == null ? null : party_a_finance_phone.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Short getCert_type() {
        return cert_type;
    }

    public void setCert_type(Short cert_type) {
        this.cert_type = cert_type;
    }


    public String getLegal_person() {
        return legal_person;
    }

    public void setLegal_person(String legal_person) {
        this.legal_person = legal_person;
    }

    public String getLegal_cert_num() {
        return legal_cert_num;
    }

    public void setLegal_cert_num(String legal_cert_num) {
        this.legal_cert_num = legal_cert_num;
    }


    public String getCert_num() {
        return cert_num;
    }

    public void setCert_num(String cert_num) {
        this.cert_num = cert_num;
    }

    public String getReserve_phone() {
        return reserve_phone;
    }

    public void setReserve_phone(String reserve_phone) {
        this.reserve_phone = reserve_phone;
    }

    public Long getWm_wallet_id() {
        return wm_wallet_id;
    }

    public void setWm_wallet_id(Long wm_wallet_id) {
        this.wm_wallet_id = wm_wallet_id;
    }


    public String getLegal_id_card() {
        return legal_id_card;
    }

    public void setLegal_id_card(String legal_id_card) {
        this.legal_id_card = legal_id_card;
    }

    public Integer getCard_type() {
        return card_type;
    }

    public void setCard_type(Integer card_type) {
        this.card_type = card_type;
    }

    public Integer getCard_valid() {
        return card_valid;
    }

    public void setCard_valid(Integer card_valid) {
        this.card_valid = card_valid;
    }

    public String getCard_invalid_reason() {
        return card_invalid_reason;
    }

    public void setCard_invalid_reason(String card_invalid_reason) {
        this.card_invalid_reason = card_invalid_reason;
    }

    public Integer getWallet_open_status() {
        return wallet_open_status;
    }

    public void setWallet_open_status(Integer wallet_open_status) {
        this.wallet_open_status = wallet_open_status;
    }

    public String getWallet_open_reason() {
        return wallet_open_reason;
    }

    public void setWallet_open_reason(String wallet_open_reason) {
        this.wallet_open_reason = wallet_open_reason;
    }

    public Integer getRealname_status() {
        return realname_status;
    }

    public void setRealname_status(Integer realname_status) {
        this.realname_status = realname_status;
    }

    public String getRealname_reason() {
        return realname_reason;
    }

    public void setRealname_reason(String realname_reason) {
        this.realname_reason = realname_reason;
    }

    public Integer getCard_binding_status() {
        return card_binding_status;
    }

    public void setCard_binding_status(Integer card_binding_status) {
        this.card_binding_status = card_binding_status;
    }

    public String getCard_binding_reason() {
        return card_binding_reason;
    }

    public void setCard_binding_reason(String card_binding_reason) {
        this.card_binding_reason = card_binding_reason;
    }

    public String getLogin_name() {
        return login_name;
    }

    public void setLogin_name(String login_name) {
        this.login_name = login_name;
    }

    public Integer getOpen_time() {
        return open_time;
    }

    public void setOpen_time(Integer open_time) {
        this.open_time = open_time;
    }

    public String getAcc_cardno_encrypted() {
        return acc_cardno_encrypted;
    }

    public void setAcc_cardno_encrypted(String acc_cardno_encrypted) {
        this.acc_cardno_encrypted = acc_cardno_encrypted;
    }

    public String getParty_a_finance_phone_encrypted() {
        return party_a_finance_phone_encrypted;
    }

    public void setParty_a_finance_phone_encrypted(String party_a_finance_phone_encrypted) {
        this.party_a_finance_phone_encrypted = party_a_finance_phone_encrypted;
    }

    public String getLegal_cert_num_encrypted() {
        return legal_cert_num_encrypted;
    }

    public void setLegal_cert_num_encrypted(String legal_cert_num_encrypted) {
        this.legal_cert_num_encrypted = legal_cert_num_encrypted;
    }

    public String getLegal_id_card_encrypted() {
        return legal_id_card_encrypted;
    }

    public void setLegal_id_card_encrypted(String legal_id_card_encrypted) {
        this.legal_id_card_encrypted = legal_id_card_encrypted;
    }

    public String getCert_num_encrypted() {
        return cert_num_encrypted;
    }

    public void setCert_num_encrypted(String cert_num_encrypted) {
        this.cert_num_encrypted = cert_num_encrypted;
    }

    public String getReserve_phone_encrypted() {
        return reserve_phone_encrypted;
    }

    public void setReserve_phone_encrypted(String reserve_phone_encrypted) {
        this.reserve_phone_encrypted = reserve_phone_encrypted;
    }

    public String getAcc_cardno_token() {
        return acc_cardno_token;
    }

    public void setAcc_cardno_token(String acc_cardno_token) {
        this.acc_cardno_token = acc_cardno_token;
    }

    public String getParty_a_finance_phone_token() {
        return party_a_finance_phone_token;
    }

    public void setParty_a_finance_phone_token(String party_a_finance_phone_token) {
        this.party_a_finance_phone_token = party_a_finance_phone_token;
    }

    public String getLegal_cert_num_token() {
        return legal_cert_num_token;
    }

    public void setLegal_cert_num_token(String legal_cert_num_token) {
        this.legal_cert_num_token = legal_cert_num_token;
    }

    public String getLegal_id_card_token() {
        return legal_id_card_token;
    }

    public void setLegal_id_card_token(String legal_id_card_token) {
        this.legal_id_card_token = legal_id_card_token;
    }

    public String getCert_num_token() {
        return cert_num_token;
    }

    public void setCert_num_token(String cert_num_token) {
        this.cert_num_token = cert_num_token;
    }

    public String getReserve_phone_token() {
        return reserve_phone_token;
    }

    public void setReserve_phone_token(String reserve_phone_token) {
        this.reserve_phone_token = reserve_phone_token;
    }
}