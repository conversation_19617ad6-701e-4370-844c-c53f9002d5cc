package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpPoiDB;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WmCustomerKpPoiTempDBMapper {

    void batchInsert(@Param("wmCustomerKpPoiDBList") List<WmCustomerKpPoiDB> wmCustomerKpPoiDBList);

    void deleteByKpIdList(@Param("kpIdList") List<Integer> kpIdList);

    void deleteByKpIdAndWmPoiIdList(@Param("kpId") Integer kpId, @Param("wmPoiIdList") List<Long> wmPoiIdList);

    void deleteByKpIdListAndWmPoiIdList(@Param("kpIdList") List<Integer> kpIdList, @Param("wmPoiIdList") List<Long> wmPoiIdList);

    List<WmCustomerKpPoiDB> selectByKpId(@Param("kpId") Integer kpId);

    Integer selectRelPoiInfo(@Param("kpId") Integer kpId, @Param("wmPoiIdList") List<Long> wmPoiIdList);

    List<WmCustomerKpPoiDB> selectByKpIdList(@Param("kpIdList") List<Integer> kpIdList);

    void updateByKpId(WmCustomerKpPoiDB wmCustomerKpPoiDB);

    void batchUpdateByKpId(@Param("KpPoiDBList") List<WmCustomerKpPoiDB> wmCustomerKpPoiDBList);

    Integer selectWmPoiNumByKpId(@Param("kpId") Integer kpId);
}
