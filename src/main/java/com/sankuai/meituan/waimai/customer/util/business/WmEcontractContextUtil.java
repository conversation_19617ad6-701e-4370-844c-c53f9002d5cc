package com.sankuai.meituan.waimai.customer.util.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 上下文处理工具
 */
public class WmEcontractContextUtil {

    private static final Logger logger = LoggerFactory.getLogger(WmEcontractContextUtil.class);

    private static final String FLOW_DELIVEY = "delivery";
    public static final String SUPPORT_MARK = "support";
    public static final String ADD_PACKAGE_FEE = "1";

    public static final String PARTB_STAMP_NAME="北京三快在线科技有限公司";
    /**
     * 查询对应申请task
     */
    public static EcontractTaskBo selectByApplyType(EcontractBatchContextBo contextBo, EcontractTaskApplyTypeEnum applyTypeEnum)
        throws WmCustomerException {
        AssertUtil.assertMapNotEmpty(contextBo.getTaskIdAndTaskMap(), "任务信息不能为空");

        for (Map.Entry<Long, EcontractTaskBo> entry:contextBo.getTaskIdAndTaskMap().entrySet()) {
            if (entry.getValue() != null && applyTypeEnum.getName().equals(entry.getValue().getApplyType())) {
                return entry.getValue();
            }
        }
        throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "不存在" + applyTypeEnum.getName() + "申请任务");
    }

    public static EcontractTaskBo selectDeliveryEcontractTaskBo(EcontractBatchContextBo contextBo, EcontractSignDataFactor signDataFactor)
            throws WmCustomerException {
        AssertUtil.assertMapNotEmpty(contextBo.getTaskIdAndTaskMap(), "任务信息不能为空");
        switch (contextBo.getBatchTypeEnum()) {
            case NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY:
                return selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
            case NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY:
                return selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY);
            default:
                if (signDataFactor.isDeliveryMultiWmPoi()) {
                    return selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);
                } else {
                    return selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE);
                }
        }
    }

    /**
     *
     * 2018.11.22 逻辑调整
     * 选择签章名称
     * 有营业执照：客户资质中--营业执照的全称
     * 无营业执照：客户资质中--个人身份证件名称
     * ======================================
     * 选择签章名称
     * 有营业执照、签约人类型为法人：营业执照全称
     * 有营业执照、签约人类型为非法人：签约人名称
     * 无营业执照：客户资质中个人身份证名称
     */
    public static String getCustomerName(EcontractBatchContextBo contextBo) {
        return contextBo.getCustomerInfoBo().getCustomerName();
    }

    /**
     * 配送模块发起确认时，多模块打包，兼容配送数据存在单店或者多店类型
     * @param contextBo
     * @param dataWrapperMap
     * @return
     * @throws WmCustomerException
     */
    public static Map<String, EcontractDataWrapperEnum> analysisDataWrapperMapForDelivery(
            EcontractBatchContextBo contextBo, Map<String, EcontractDataWrapperEnum> dataWrapperMap)
            throws WmCustomerException {
        AssertUtil.assertMapNotEmpty(contextBo.getTaskIdAndTaskMap(), "任务信息不能为空");
        for (Map.Entry<Long, EcontractTaskBo> entry:contextBo.getTaskIdAndTaskMap().entrySet()) {
            if (entry.getValue() != null && EcontractTaskApplyTypeEnum.POIFEE.getName().equals(entry.getValue().getApplyType())) {
                dataWrapperMap.put(FLOW_DELIVEY,EcontractDataWrapperEnum.DELIVERY);
            } else if (entry.getValue() != null && EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(entry.getValue().getApplyType())) {
                dataWrapperMap.put(FLOW_DELIVEY,EcontractDataWrapperEnum.BATCH_DELIVERY);
            }
        }
        return dataWrapperMap;
    }



    public static boolean isSupport(EcontractBatchContextBo batchContextBo,Map<String, EcontractDataWrapperEnum>
            dataWrapperMapActually) throws WmCustomerException {
        EcontractDataWrapperEnum econtractDataWrapperEnum= dataWrapperMapActually.get(FLOW_DELIVEY);
        if(econtractDataWrapperEnum!=null){
            if( econtractDataWrapperEnum.getCode()==EcontractDataWrapperEnum.DELIVERY.getCode()){
                EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.POIFEE);
                EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
                if(SUPPORT_MARK.equals(deliveryInfoBo.getSupportExclusive())) {
                   return Boolean.TRUE;
                }

            }else if(econtractDataWrapperEnum.getCode()==EcontractDataWrapperEnum.BATCH_DELIVERY.getCode()){

                EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);
                EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
                for(EcontractDeliveryInfoBo temp : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()){
                    if(SUPPORT_MARK.equals(temp.getSupportExclusive())){
                        return Boolean.TRUE;
                    }
                }

            }

        }

        return Boolean.FALSE;
    }

    /**
     * @param contextBo
     * @return
     * @throws WmCustomerException
     */
    public static EcontractSignDataFactor analysisEcontractSignDataFactor(EcontractBatchContextBo contextBo) throws WmCustomerException{
        AssertUtil.assertMapNotEmpty(contextBo.getTaskIdAndTaskMap(), "任务信息不能为空");
        EcontractSignDataFactor result = new EcontractSignDataFactor();
        for (Map.Entry<Long, EcontractTaskBo> entry:contextBo.getTaskIdAndTaskMap().entrySet()) {
            if (entry.getValue() != null && EcontractTaskApplyTypeEnum.POIFEE.getName().equals(entry.getValue().getApplyType())) {
                EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE);
                EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
                if(deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo() != null){
                    result.setDeliverySupportWholeCity(true);
                }
                if(deliveryInfoBo.getEcontractDeliveryAggregationInfoBo() != null){
                    result.setDeliverySupportAggregation(true);
                }
                if (deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo() != null) {
                    result.setDeliverySupportCompanyCustomerLongDistanceDelivery(true);
                }
            }else if(entry.getValue() != null && EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName().equals(entry.getValue().getApplyType())){
                EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);
                EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
                List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = batchDeliveryInfoBo.getEcontractDeliveryInfoBoList();
                for(EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList){
                    if(temp.getEcontractDeliveryWholeCityInfoBo() != null){
                        result.setDeliverySupportWholeCity(true);
                    }
                    if(temp.getEcontractDeliveryAggregationInfoBo() != null){
                        result.setDeliverySupportAggregation(true);
                    }
                    if (temp.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo() != null) {
                        result.setDeliverySupportCompanyCustomerLongDistanceDelivery(true);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 全城送-签约流程组装
     */
    public static void assembleFlowForDeliverySupportWholeCity(Map<String, EcontractDataWrapperEnum> dataWrapperMapActually, List<String> currentFlowList) {
        currentFlowList.add(EcontractTaskApplySubTypeEnum.DELIVERY_WHOLE_CITY.getName());
        EcontractDataWrapperEnum deliveryDataWrapperEnum = dataWrapperMapActually.get(FLOW_DELIVEY);
        if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_WHOLE_CITY.getName(), EcontractDataWrapperEnum.DELIVERY_WHOLE_CITY);
        } else if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.BATCH_DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_WHOLE_CITY.getName(), EcontractDataWrapperEnum.BATCH_DELIVERY_WHOLE_CITY);
        }
    }


    /**
     * 聚合配送-签约流程组装
     */
    public static void assembleFlowForDeliveryAggregation(Map<String, EcontractDataWrapperEnum> dataWrapperMapActually,
            List<String> currentPoiStampList, List<String> currentMtStampList ,List<String> currentFlowList) {
        currentFlowList.add(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName());
        EcontractDataWrapperEnum deliveryDataWrapperEnum = dataWrapperMapActually.get(FLOW_DELIVEY);
        if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName(), EcontractDataWrapperEnum.DELIVERY_AGGREGATION);
        } else if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.BATCH_DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName(),
                    EcontractDataWrapperEnum.BATCH_DELIVERY_AGGREGATION);
        }
        currentPoiStampList.add(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName());
        currentMtStampList.add(EcontractTaskApplySubTypeEnum.DELIVERY_AGGREGATION.getName());
    }

    /**
     * 企客远距离-签约流程组装
     */
    public static void assembleFlowForDeliveryCompanyCustomerLongDistance(Map<String, EcontractDataWrapperEnum> dataWrapperMapActually, List<String> currentFlowList) {
        currentFlowList.add(EcontractTaskApplySubTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName());
        EcontractDataWrapperEnum deliveryDataWrapperEnum = dataWrapperMapActually.get(FLOW_DELIVEY);
        if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName(), EcontractDataWrapperEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE);
        } else if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.BATCH_DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName(), EcontractDataWrapperEnum.BATCH_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE);
        }
    }

    /**
     * 代理商新费率-签约流程组装
     */
    public static void assembleFlowForDeliveryAgentNew(Map<String, EcontractDataWrapperEnum> dataWrapperMapActually, List<String> currentFlowList) {
        currentFlowList.add(EcontractTaskApplySubTypeEnum.DELIVERY_AGENT_NEW.getName());
        EcontractDataWrapperEnum deliveryDataWrapperEnum = dataWrapperMapActually.get(FLOW_DELIVEY);
        if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_AGENT_NEW.getName(), EcontractDataWrapperEnum.DELIVERY_AGENT_NEW);
        } else if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.BATCH_DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_AGENT_NEW.getName(), EcontractDataWrapperEnum.BATCH_DELIVERY_AGENT_NEW);
        }
    }

    /**
     * 配送服务费折扣协议-签约流程组装
     */
    public static void assembleFlowForDeliveryPerDiscount(Map<String, EcontractDataWrapperEnum> dataWrapperMapActually, List<String> currentFlowList) {
        currentFlowList.add(EcontractTaskApplySubTypeEnum.DELIVERY_PER_DISCOUNT.getName());
        EcontractDataWrapperEnum deliveryDataWrapperEnum = dataWrapperMapActually.get(FLOW_DELIVEY);
        if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_PER_DISCOUNT.getName(), EcontractDataWrapperEnum.DELIVERY_PER_DISCOUNT);
        } else if (deliveryDataWrapperEnum == EcontractDataWrapperEnum.BATCH_DELIVERY) {
            dataWrapperMapActually.put(EcontractTaskApplySubTypeEnum.DELIVERY_PER_DISCOUNT.getName(), EcontractDataWrapperEnum.BATCH_DELIVERY_PER_DISCOUNT);
        }
    }

}
