package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-14 21:26
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class PoiPromotionServiceNoticeTask implements NoticeTask {

    @Autowired
    private WmContractService wmContractService;

    @Override
    public void notice(String module, List<Long> bizIdList, ManualPackNoticeContext context, List<Long> taskIds) throws WmCustomerException, TException {
        log.info("notice#module:{}, taskInfo:{}", module, JSON.toJSONString(context.getAllTaskInfo()));
        for (Long bizId : bizIdList) {
            wmContractService.startSignByWaitingSign(context.getCustomerId(), bizId, context.getCommitUid(), "", context.getManualBatchId());
        }
    }
}
