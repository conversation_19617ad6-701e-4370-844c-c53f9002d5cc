package com.sankuai.meituan.waimai.customer.util.trans;

import com.google.common.collect.Lists;

import com.sankuai.meituan.waimai.customer.domain.WmEcontractPoiSignStateDB;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class WmEcontractPoiStateTransUtil {
    public static List<Long> toWmPoiIdList(List<WmEcontractPoiSignStateDB> poiStateList) {
        if (CollectionUtils.isEmpty(poiStateList)) {
            return Lists.newArrayList();
        }

        List<Long> wmPoiIdList = Lists.newArrayList();
        for (WmEcontractPoiSignStateDB poiStateDB : poiStateList) {
            wmPoiIdList.add(poiStateDB.getWmPoiId());
        }
        return wmPoiIdList;
    }
}
