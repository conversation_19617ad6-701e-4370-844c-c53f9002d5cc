package com.sankuai.meituan.waimai.customer.service.sign.applytask.context;

import java.util.List;

import javax.annotation.Resource;

import com.sankuai.meituan.waimai.customer.constant.contract.CommonTaskConstant;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ValueAddedServiceDiscountInfoBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ValueAddedServiceDiscountInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * 门店申请 - 根据门店查对应的客户
 * <AUTHOR>
 */
@Service
public class WmEcontractPoiWrapService implements IWmEcontractWrapService {

    @Resource
    private WmCustomerService wmCustomerService;

    @Override
    public EcontractTaskContextBo wrap(EcontractTaskApplyBo applyBo, EcontractTaskContextBo contextBo) throws WmCustomerException {
        AssertUtil.assertObjectNotNull(contextBo, "申请信息");
        AssertUtil.assertEqual(contextBo.getBizTypeEnum().getType(), EcontractTaskApplyBizTypeEnum.WM_POI_ID.getType(), "申请类型必须为客户");

        //根据门店查询合同
        Long wmPoiId = Long.valueOf(contextBo.getBizId());
        contextBo.setWmPoiIdList(Lists.newArrayList(wmPoiId));

        //兼容门店切换客户不下线
        Integer wmCustomerId = applyBo.getWmCustomerId();
        if (MoreObjects.firstNonNull(wmCustomerId, 0) > 0) {
            contextBo.setCustomerId(wmCustomerId);
        } else {
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
            contextBo.setCustomerId(wmCustomerDB.getId());
        }

        if (CommonTaskConstant.BATCH_APPLY_TYPE_LIST.contains(contextBo.getApplyTypeEnum().getType())) {
            wrapBatchPoiFee(applyBo, contextBo);
        }

        return contextBo;
    }

    private void wrapBatchPoiFee(EcontractTaskApplyBo applyBo,EcontractTaskContextBo contextBo) {
        String applyInfoBo = applyBo.getApplyInfoBo();
        EcontractBatchDeliveryInfoBo infoBo = JSONObject
            .parseObject(applyInfoBo, EcontractBatchDeliveryInfoBo.class);
        contextBo.setWmPoiIdList(infoBo.getWmPoiIdList());
    }

    //解析task中的门店信息
    public List<Long> analysisWmPoiIdFromEcontractTaskBo(EcontractTaskBo taskBo) {
        //1.settle、poifee、batchpoifee
        List<Long> result = Lists.newArrayList();
        if (taskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.SETTLE.getName())) {
            List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);
            for (EcontractSettleInfoBo settleInfoBo : settleInfoBoList) {
                if (settleInfoBo == null || CollectionUtils.isEmpty(settleInfoBo.getPoiInfoBoList())) {
                    continue;
                }
                for (EcontractPoiInfoBo poiInfoBo : settleInfoBo.getPoiInfoBoList()) {
                    result.add(poiInfoBo.getWmPoiId().longValue());
                }
            }
        } else if (taskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.POIFEE.getName())) {
            result.add(Long.valueOf(taskBo.getBizId()));
        } else if (CommonTaskConstant.BATCH_APPLY_NAME_LIST.contains(taskBo.getApplyType())
                || taskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName())) {
            String applyInfoBo = taskBo.getApplyContext();
            EcontractBatchDeliveryInfoBo infoBo = JSONObject.parseObject(applyInfoBo, EcontractBatchDeliveryInfoBo.class);
            result.addAll(infoBo.getWmPoiIdList());
        } else if (taskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.ADDEDSERVICEDISCOUNT.getName())) {
            ValueAddedServiceDiscountInfoBatchBo batchBo = JSON.parseObject(taskBo.getApplyContext(), ValueAddedServiceDiscountInfoBatchBo.class);
            if (batchBo != null && !CollectionUtils.isEmpty(batchBo.getValueAddedServiceDiscountInfoBoList())) {
                List<ValueAddedServiceDiscountInfoBo> infoBo = batchBo.getValueAddedServiceDiscountInfoBoList();
                for (ValueAddedServiceDiscountInfoBo temp : infoBo) {
                    result.add(Long.valueOf(temp.getWmPoiId()));
                }
            }
        }
        return result;
    }

}
