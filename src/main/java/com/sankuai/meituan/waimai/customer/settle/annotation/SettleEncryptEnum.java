package com.sankuai.meituan.waimai.customer.settle.annotation;

import com.sankuai.meituan.waimai.customer.settle.constant.SettleEncryptConstant;

/**
 * <AUTHOR>
 */
public enum SettleEncryptEnum {
    PARTY_A_FINANCE_PHONE("party_a_finance_phone", SettleEncryptConstant.TYPE_PHONE),
    ACC_CARDNO("acc_cardno", SettleEncryptConstant.TYPE_BANKCARDNO),
    LEGAL_CERT_NUM("legal_cert_num", SettleEncryptConstant.TYPE_IDENTIFY),
    LEGAL_ID_CARD("legal_id_card", SettleEncryptConstant.TYPE_IDENTIFY),
    CERT_NUM("cert_num", SettleEncryptConstant.TYPE_IDENTIFY),
    RESERVE_PHONE("reserve_phone", SettleEncryptConstant.TYPE_PHONE),
    ;

    private String field;
    private String kmsKey;

    SettleEncryptEnum(String field,String kmsKey){
        this.field = field;
        this.kmsKey = kmsKey;
    }

    public String getField() {
        return field;
    }

    public String getKmsKey() {
        return kmsKey;
    }
}
