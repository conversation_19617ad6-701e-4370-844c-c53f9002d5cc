package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.zebra.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Uninterruptibles;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerOplogMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerOplogWithBLOBs;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.OplogSearchPageBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.OplogBoPageData;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class WmCustomerOplogService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerOplogService.class);

    @Autowired
    WmCustomerOplogMapper wmCustomerOplogMapper;

    public OplogBoPageData search(OplogSearchPageBo searchPageBo, int opUid, String opName) throws WmCustomerException, TException {
        if (searchPageBo == null || searchPageBo.getCustomerId() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "请保证参数完整，customerId不能为空");
        }
        if (StringUtils.isNotBlank(searchPageBo.getOpTimeStart())) {
            Date opStartTime = DateUtil.string2DateSecond24(searchPageBo.getOpTimeStart());
            searchPageBo.setOpTimeStartSeconds(DateUtil.date2Unixtime(opStartTime));
        }
        if (StringUtils.isNotBlank(searchPageBo.getOpTimeEnd())) {
            Date opEndTime = DateUtil.string2DateSecond24(searchPageBo.getOpTimeEnd());
            searchPageBo.setOpTimeEndSeconds(DateUtil.date2Unixtime(opEndTime));
        }
        PageHelper.startPage(searchPageBo.getPageNo(), searchPageBo.getPageSize());
        logger.info("查询日志searchPageBo:{}", JSON.toJSONString(searchPageBo));
        List<WmCustomerOplogWithBLOBs> customerOplogWithBLOBs = wmCustomerOplogMapper.queryOpLog(searchPageBo);
        logger.info("查询日志customerOplogWithBLOBs:{}", JSON.toJSONString(customerOplogWithBLOBs));
        List<WmCustomerOplogBo> dataList = Lists.newArrayList();
        for (WmCustomerOplogWithBLOBs withBLOBs : MoreObjects
                .firstNonNull(customerOplogWithBLOBs, Lists.<WmCustomerOplogWithBLOBs>newArrayList())) {
            WmCustomerOplogBo bo = new WmCustomerOplogBo();
            BeanUtils.copyProperties(withBLOBs, bo);
            dataList.add(bo);
        }
        PageData<WmCustomerOplogBo> pageData = PageUtil.page(customerOplogWithBLOBs, dataList);
        return new OplogBoPageData(pageData.getPageInfo(), pageData.getList());
    }

    public Long insert(WmCustomerOplogBo customerOplogBo) throws WmCustomerException {
        if (customerOplogBo == null || customerOplogBo.getCustomerId() == null
                || customerOplogBo.getModuleType() == null || customerOplogBo.getModuleId() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "请保证操作记录信息完整，customerId、moduleType、moduleId不能为空");
        }
        WmCustomerOplogWithBLOBs withBLOBs = new WmCustomerOplogWithBLOBs();
        BeanUtils.copyProperties(customerOplogBo, withBLOBs);
        wmCustomerOplogMapper.insert(withBLOBs);
        return withBLOBs.getId();
    }

    public void batchInsert(List<WmCustomerOplogBo> customerOplogBoList) throws WmCustomerException {
        if (CollectionUtils.isEmpty(customerOplogBoList)) {
            return;
        }
        List<WmCustomerOplogWithBLOBs> dbList = Lists.newArrayList();
        for (WmCustomerOplogBo customerOplogBo : customerOplogBoList) {
            if (customerOplogBo == null || customerOplogBo.getCustomerId() == null
                    || customerOplogBo.getModuleType() == null || customerOplogBo.getModuleId() == null) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "请保证操作记录信息完整，customerId、moduleType、moduleId不能为空");
            }
            WmCustomerOplogWithBLOBs withBLOBs = new WmCustomerOplogWithBLOBs();
            BeanUtils.copyProperties(customerOplogBo, withBLOBs);

            dbList.add(withBLOBs);
        }

        if (MccCustomerConfig.getCustomerTrafficSpikeSwitch()) {
            List<List<WmCustomerOplogWithBLOBs>> dblistPartition = Lists.partition(dbList, 500);
            for (List<WmCustomerOplogWithBLOBs> list : dblistPartition) {
                wmCustomerOplogMapper.batchInsert(list);
                Uninterruptibles.sleepUninterruptibly(1000, TimeUnit.MILLISECONDS);
            }
        } else {
            wmCustomerOplogMapper.batchInsert(dbList);
        }
    }

}
