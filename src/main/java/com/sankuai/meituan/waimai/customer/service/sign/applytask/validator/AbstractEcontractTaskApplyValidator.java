package com.sankuai.meituan.waimai.customer.service.sign.applytask.validator;

import com.google.common.base.Splitter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/6/4 15:36
 */
public abstract class AbstractEcontractTaskApplyValidator implements EcontractTaskApplyValidator {

    public static Splitter SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    @Resource
    private WmPoiClient wmPoiClient;

    protected List<Long> getAllWmPoiId(EcontractTaskApplyBo applyBo) {
        return applyBo.getDataSourceBoList().stream()
                .map(EcontractDataSourceBo::getWmPoiIdAndBizIdList)
                .flatMap(Collection::stream)
                .map(EcontractDataPoiBizBo::getWmPoiId)
                .distinct()
                .collect(Collectors.toList());
    }

    private Map<Long, List<String>> batchGetWmPoiLabelIds(List<Long> wmPoiIdList) throws WmCustomerException {
        List<WmPoiDomain> wmPoiDomains = wmPoiClient.pageGetWmPoiByWmPoiIdList(wmPoiIdList);
        Map<Long, List<String>> wmPoiLabelIdsMap = new HashMap<>();
        for (WmPoiDomain wmPoiDomain : wmPoiDomains) {
            wmPoiLabelIdsMap.put((long) wmPoiDomain.getWmPoiId(), SPLITTER.splitToList(wmPoiDomain.getLabelIds()));
        }
        return wmPoiLabelIdsMap;
    }

    protected List<Long> getNoLabelIdWmPoiId(EcontractTaskApplyBo applyBo, int labelId) throws WmCustomerException {
        List<Long> wmPoiIdList = getAllWmPoiId(applyBo);
        Map<Long, List<String>> wmPoi2LabelIdMap = batchGetWmPoiLabelIds(wmPoiIdList);
        return wmPoi2LabelIdMap.entrySet().stream()
                .filter(entry -> !entry.getValue().contains(String.valueOf(labelId)))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    };


}
