package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementModelDic;
import org.springframework.stereotype.Component;

@Component
public interface WmAgreementModelDicMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(WmAgreementModelDic record);

    int insertSelective(WmAgreementModelDic record);

    WmAgreementModelDic selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(WmAgreementModelDic record);

    int updateByPrimaryKey(WmAgreementModelDic record);

    Integer getCurrentVerisonById(Integer id);
}