package com.sankuai.meituan.waimai.customer.constant.customer;

import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;

/**
 * 客户门店关系操作来源与类型-客户切换
 */
public enum WmCustomerPoiSwitchOperateTypeEnum {
    UNBIND_TASK_CANCEL(1, "任务列表取消解绑", CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_CANCEL_UNBIND, WmCustomerPoiConfirmResultEnum.REJECT.getCode()),
    UNBIND_TASK_FORCE(2, "任务列表强制解绑", CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_FORCE_UNBIND, WmCustomerPoiConfirmResultEnum.APPOVE.getCode()),

    UNBIND_SMS_CONFIRM(3, "签约确认解绑", CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_CONFIRM_UNBIND, WmCustomerPoiConfirmResultEnum.APPOVE.getCode()),

    UNBIND_SMS_REJECT(4, "签约取消解绑", CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_REJECT_UNBIND, WmCustomerPoiConfirmResultEnum.REJECT.getCode()),

    BIND_TASK_CANCEL(5, "任务列表取消绑定", CustomerConstants.CUSTOMER_LOG_TEMPLATE_CANCEL_PRE_BIND, WmCustomerPoiConfirmResultEnum.REJECT.getCode()),

    BIND_SMS_CONFIRM(6, "签约确认绑定", CustomerConstants.CUSTOMER_LOG_TEMPLATE_CONFIRM_PRE_BIND, WmCustomerPoiConfirmResultEnum.APPOVE.getCode()),

    BIND_SMS_REJECT(7, "签约取消绑定", CustomerConstants.CUSTOMER_LOG_TEMPLATE_CANCEL_PRE_BIND_BY_MERCHANT, WmCustomerPoiConfirmResultEnum.REJECT.getCode()),

    ;

    private int code;

    private String desc;

    public String logMsg;

    public int operateType;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getLogMsg() {
        return logMsg;
    }

    public void setLogMsg(String logMsg) {
        this.logMsg = logMsg;
    }

    public int getOperateType() {
        return operateType;
    }

    public void setOperateType(int operateType) {
        this.operateType = operateType;
    }

    WmCustomerPoiSwitchOperateTypeEnum(int code, String desc, String logMsg, int operateType) {
        this.code = code;
        this.desc = desc;
        this.logMsg = logMsg;
        this.operateType = operateType;
    }
}
