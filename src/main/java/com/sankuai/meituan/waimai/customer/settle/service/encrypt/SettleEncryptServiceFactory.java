package com.sankuai.meituan.waimai.customer.settle.service.encrypt;

import com.google.common.collect.Maps;
import com.sankuai.inf.kms.pangolin.api.model.EncryptionRequest;
import com.sankuai.inf.kms.pangolin.api.service.EncryptServiceFactory;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.meituan.waimai.customer.exception.SettleEncryptRuntimeException;
import com.sankuai.meituan.waimai.customer.settle.constant.SettleEncryptConstant;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 结算敏感数据加解密
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SettleEncryptServiceFactory {

    private static final String SETTLE_ENCRYPT_SERVICE_NAME_SPACE = "com.sankuai.conch.certify.token";

    // 每种数据类型使用一个 IEncryptService
    private IEncryptService phoneEncryptService;
    private IEncryptService identifyEncryptService;
    private IEncryptService bankCardNoEncryptService;
    private Map<String, IEncryptService> settleEncryptServiceFactoryMap = Maps.newHashMap();

    public SettleEncryptServiceFactory() {
        log.info("#SettleEncryptServiceFactory init begin");
        phoneEncryptService = EncryptServiceFactory.create(
                EncryptionRequest.Builder.anEncryptionRequest()
                        .withNamespace(SETTLE_ENCRYPT_SERVICE_NAME_SPACE).withKeyName(
                                SettleEncryptConstant.TYPE_PHONE).build());
        identifyEncryptService = EncryptServiceFactory.create(
                EncryptionRequest.Builder.anEncryptionRequest()
                        .withNamespace(SETTLE_ENCRYPT_SERVICE_NAME_SPACE)
                        .withKeyName(SettleEncryptConstant.TYPE_IDENTIFY).build());
        bankCardNoEncryptService = EncryptServiceFactory.create(
                EncryptionRequest.Builder.anEncryptionRequest()
                        .withNamespace(SETTLE_ENCRYPT_SERVICE_NAME_SPACE)
                        .withKeyName(SettleEncryptConstant.TYPE_BANKCARDNO).build());
        settleEncryptServiceFactoryMap.put(SettleEncryptConstant.TYPE_PHONE, phoneEncryptService);
        settleEncryptServiceFactoryMap.put(SettleEncryptConstant.TYPE_IDENTIFY,
                identifyEncryptService);
        settleEncryptServiceFactoryMap.put(SettleEncryptConstant.TYPE_BANKCARDNO,
                bankCardNoEncryptService);
        log.info("#SettleEncryptServiceFactory init finish");
    }

    public IEncryptService getIEncryptService(String type) {
        IEncryptService result = settleEncryptServiceFactoryMap.get(type);
        if(result == null){
            log.error("找不到相关的加解密类!!!");
            throw new SettleEncryptRuntimeException("找不到相关的加解密类!!!");
        }
        return result;
    }

}
