package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.waimai.thrift.push.client.inbox.exception.WmReachPlatformException;
import com.sankuai.waimai.thrift.push.client.inbox.param.NoticeSendParam;
import com.sankuai.waimai.thrift.push.client.inbox.result.InboxMessagesResult;
import com.sankuai.waimai.thrift.push.client.inbox.service.WmInboxSendThriftService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * -@author: h<PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2023/3/16 4:19 PM
 */
@Slf4j
@Service
public class WmInboxSendMessageAdapter {

    /**
     * 根据门店poiId发送消息，acctid不用填留空
     */
    private static final Integer PUB_TYPE_POI_DIM = 1;
    /**
     * 根据账号acctId发送消息 poiId不用填留空
     */
    private static final Integer PUB_TYPE_ACCT_DIM = 2;

    /**
     * 根据账号acctId+wmPoiId发送消息
     */
    private static final Integer PUB_TYPE_POI_ACCT_DIM = 3;

    private static final String RES_SUCCESS = "2000";

    @Autowired
    private WmInboxSendThriftService.Iface wmInboxSendThriftService;


    public void sendPoiMessage(Long wmPoiId, Map<String, String> paramMap, String publishMessTemplate) {

        NoticeSendParam noticeSendParam = new NoticeSendParam();
        noticeSendParam.setPubType(PUB_TYPE_POI_DIM);
        noticeSendParam.setTemplateName(publishMessTemplate);
        //业务ID使用uid
        noticeSendParam.setBizId(wmPoiId);
        noticeSendParam.setPoiId(wmPoiId);
        noticeSendParam.setParamMap(paramMap);
        log.info("#sendPoiMessage#门店：{} 发送Push消息:{}", wmPoiId, JSON.toJSONString(noticeSendParam));
        try {
            InboxMessagesResult inboxMessagesResult = wmInboxSendThriftService.sendMessage(Lists.newArrayList(
                noticeSendParam));
            log.info("发送门店Push消息完成, inboxMessagesResult:{}", JSON.toJSONString(inboxMessagesResult));
            if (!Objects.equals(inboxMessagesResult.getCode(), RES_SUCCESS)) {
                log.error("向门店账号发送Push消息失败, wmPoiId:{}", wmPoiId);
            }
        } catch (WmReachPlatformException e) {
            log.warn("发送门店Push消息失败", e);
        } catch (TException e) {
            log.error("向门店账号发送消息失败, wmPoiId:{}", wmPoiId, e);
        }
    }

    /**
     * 发送商家
     *
     * @param acctIdList
     * @param paramMap
     */
    public void sendAccountMessage(List<Long> acctIdList, Map<String, String> paramMap, String publishMessTemplate) {

        log.info("#sendAccountMessage#账号：{} 发送Push消息:{}", JSON.toJSONString(acctIdList), JSON.toJSONString(paramMap));
        List<NoticeSendParam> noticeSendParamList = new ArrayList<>();
        acctIdList.forEach(m -> {
            NoticeSendParam noticeSendParam = new NoticeSendParam();
            noticeSendParam.setPubType(PUB_TYPE_ACCT_DIM);
            noticeSendParam.setTemplateName(publishMessTemplate);

            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                log.warn("InterruptedException异常", e);
            }
            //业务ID使用uid
            noticeSendParam.setBizId((new Date()).getTime());
            noticeSendParam.setAcctId(m);
            noticeSendParam.setParamMap(paramMap);
            noticeSendParamList.add(noticeSendParam);
        });
        try {
            log.info("#sendAccountMessage#账号,发送Push消息,noticeSendParamList={}",
                JSON.toJSONString(noticeSendParamList));
            InboxMessagesResult inboxMessagesResult = wmInboxSendThriftService.sendMessage(noticeSendParamList);
            log.info("发送账号Push消息完成, inboxMessagesResult:{}", JSON.toJSONString(inboxMessagesResult));
            if (!Objects.equals(inboxMessagesResult.getCode(), RES_SUCCESS)) {
                log.error("向门店账号发送Push消息失败, acctIdList:{}", acctIdList);
            }
        } catch (WmReachPlatformException e) {
            log.warn("发送账号Push消息失败", e);
        } catch (TException e) {
            log.error("向账号发送消息失败, acctIdList:{}", acctIdList, e);
        }
    }

    /**
     * 发送商家+账号
     * @param acctId
     * @param wmPoiId
     * @param paramMap
     * @param publishMessTemplate
     */
    public void sendAccountAndPoiMessage(Long acctId,Long wmPoiId, Map<String, String> paramMap, List<String> publishMessTemplate) {
        for(String template:publishMessTemplate){
            log.info("#sendAccountAndPoiMessage#账号：{},门店：{} 发送Push消息:{}", acctId,wmPoiId, JSON.toJSONString(paramMap));
            List<NoticeSendParam> noticeSendParamList = new ArrayList<>();
            NoticeSendParam noticeSendParam = new NoticeSendParam();
            noticeSendParam.setPubType(PUB_TYPE_POI_ACCT_DIM);
            noticeSendParam.setTemplateName(template);
            noticeSendParam.setBizId(wmPoiId);
            noticeSendParam.setAcctId(acctId);
            noticeSendParam.setPoiId(wmPoiId);
            noticeSendParam.setParamMap(paramMap);
            noticeSendParamList.add(noticeSendParam);
            try {
                log.info("#sendAccountAndPoiMessage#账号,发送Push消息,noticeSendParamList={}",
                        JSON.toJSONString(noticeSendParamList));
                InboxMessagesResult inboxMessagesResult = wmInboxSendThriftService.sendMessage(noticeSendParamList);
                log.info("发送账号Push消息完成, inboxMessagesResult:{}", JSON.toJSONString(inboxMessagesResult));
                if (!Objects.equals(inboxMessagesResult.getCode(), RES_SUCCESS)) {
                    log.error("向门店账号发送Push消息失败, acctId:{},wmPoiId:{}", acctId,wmPoiId);
                }
            } catch (WmReachPlatformException e) {
                log.warn("发送账号Push消息失败", e);
            } catch (TException e) {
                log.error("向账号发送消息失败, acctId:{},wmPoiId:{}", acctId,wmPoiId, e);
            }
        }
    }
}
