package com.sankuai.meituan.waimai.customer.domain.sc.canteenstall;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallManageSubmitStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallManageTaskTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 食堂档口管理任务表DO
 * <AUTHOR>
 * @date 2024/05/10
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmCanteenStallManageDO {
    /**
     * 主键ID(即档口管理任务编号ID)
     */
    private Integer id;
    /**
     * 任务类型
     * {@link CanteenStallManageTaskTypeEnum}
     */
    private Integer taskType;
    /**
     * 任务提交状态
     * {@link CanteenStallManageSubmitStatusEnum}
     */
    private Integer submitStatus;
    /**
     * 食堂主键ID
     */
    private Integer canteenPrimaryId;
    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Integer cuid;
    /**
     * 修改人ID
     */
    private Integer muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
}
