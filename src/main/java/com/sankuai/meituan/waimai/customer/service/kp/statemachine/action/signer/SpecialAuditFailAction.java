package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpBuryingPointService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.UN_VALID;

/**
 * <AUTHOR>
 * @date 20230821
 * @desc KP签约人提审事件
 */
@Service
@Slf4j
public class SpecialAuditFailAction extends KpSignerAbstractAction {


    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    /**
     * KP签约人提审事件
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpSignerBaseSM
     */
    @Override
    public void execute(KpSignerStateMachine from, KpSignerStateMachine to, KpSignerEventEnum eventEnum,
                        KpSignerStatusMachineContext context, KpSignerBaseSM kpSignerBaseSM) {
        log.info("SpecialAuditFailAction.execute,特批审核失败,from={},to={},context={}", from, to, JSON.toJSONString(context));
        boolean haveEffectFlag = context.getExistEffectiveFlag();
        String reason = context.getKpAuditResultBody().getRejectReason();
        WmCustomerKpAudit audit = context.getWmCustomerKpAudit();
        //驳回原因过长需要截取
        if (StringUtils.isNotEmpty(reason)) {
            if (reason.length() > WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_KP) {
                reason = reason.substring(0, WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_KP - WmCustomerConstant.TOO_LONG_MSG.length()) + WmCustomerConstant.TOO_LONG_MSG;
            }
        }
        WmCustomerKp signerKp = context.getOldCustomerKp();
        WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(signerKp.getId());
        //未生效
        if (!haveEffectFlag) {
            signerKp.setState(KpSignerStateMachine.SPECILA_AUDIT_REJECT.getState());
            signerKp.setFailReason(reason);
            //更新数据为驳回失败
            wmCustomerKpDBMapper.updateByPrimaryKey(signerKp);
        } else {
            if (kpTemp.getState() != KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING.getState()) {
                log.error("SpecialAuditFailAction.execute,特批认证审核驳回失败，状态非变更特批审核中，auditId:{}, state:{}", audit.getId(), kpTemp.getState());
                return;
            }
            kpTemp.setState(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_REJECT.getState());
            kpTemp.setFailReason(reason);
            //更新临时变更记录为驳回失败
            wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
        }

        //更新KP提审记录为无效
        audit.setValid(UN_VALID);
        audit.setResult("特批审核驳回");
        wmCustomerKpAuditMapper.updateByPrimaryKey(audit);
        //添加埋点
        wmCustomerKpBuryingPointService.afterSingKp(null, audit.getAcctId(), kpTemp == null ? null : wmCustomerKpAuditService.transformWmCustomerKpTemp(kpTemp), signerKp);
        //添加操作记录
        wmCustomerSensitiveWordsService.readKpWhenSelect(signerKp);
        wmCustomerKpLogService.changeState(signerKp.getCustomerId(), signerKp, "特批认证审核驳回，驳回原因：" + reason, context.getOpUid(), context.getOpUName());
    }
}
