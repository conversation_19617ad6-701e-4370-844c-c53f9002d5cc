package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.scmbrand.thrift.domain.api.BrandQueryListResultDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.api.BrandQueryParamDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.api.BrandQueryResultDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.servicebrand.*;
import com.sankuai.meituan.scmbrand.thrift.service.api.BrandQueryThriftService;
import com.sankuai.meituan.scmbrand.thrift.service.api.ServiceBrandOuterQueryThriftService;
import com.sankuai.meituan.scmbrand.thrift.service.manager.ServiceBrandQueryThriftService;
import com.sankuai.meituan.waimai.customer.bo.brand.BrandQueryParamBO;
import com.sankuai.meituan.waimai.customer.bo.brand.BrandQueryResultBO;
import com.sankuai.meituan.waimai.customer.constant.common.CommonConst;
import com.sankuai.meituan.waimai.customer.util.common.RetryUtil;
import com.sankuai.meituan.waimai.customer.util.trans.acl.AclTransfer;
import com.sankuai.meituan.waimai.poibrand.domain.WmPoiServiceBrand;
import com.sankuai.meituan.waimai.poibrand.service.WmPoiBrandQueryThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * -@description: 查询品牌
 * -@date: 2023/5/1 5:51 PM
 */
@Service
@Slf4j
public class WmBrandAdapter {

    private static final Integer BRAND_QUERY_LIMIT = 300;

    @Resource
    private ServiceBrandQueryThriftService serviceBrandQueryThriftService;

    @Resource
    private BrandQueryThriftService brandQueryThriftService;

    @Resource
    private WmPoiBrandQueryThriftService.Iface wmPoiBrandQueryThriftService;

    @Resource
    private ServiceBrandOuterQueryThriftService serviceBrandOuterQueryThriftService;


        /**
         *  批量查询物理品牌：
         *  物理品牌对业务品牌是1对多 ，一个业务品牌只关联一个物理品牌
         * @param brandIdList 业务品牌
         * @return 物理品牌
         * @throws WmCustomerException
         */
    public Map<Integer,Integer> getServiceBrandInfoList(List<Integer> brandIdList) throws WmCustomerException {

        log.info("获取品牌originBrandId入参:{}", JSON.toJSONString(brandIdList));
        Map<Integer,Integer> brandIdOriginIdMap   = new HashMap<>();
        try {
            List<WmPoiServiceBrand> wmPoiServiceBrandList =
                wmPoiBrandQueryThriftService.mgetWmPoiServiceBrand(brandIdList);
            log.info("获取业务品牌详情返回:{}", JSON.toJSONString(wmPoiServiceBrandList));

            if (CollectionUtils.isNotEmpty(wmPoiServiceBrandList)) {
                brandIdOriginIdMap =
                    wmPoiServiceBrandList.stream().collect(Collectors.toMap(WmPoiServiceBrand::getId,
                        WmPoiServiceBrand::getOrigin_brand_id,(k1,k2)->k1));
            }
        } catch (WmServerException e) {
            log.error("获取业务品牌详情异常:", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "获取业务品牌详情异常!");
        } catch (TException e) {
            log.error("业务品牌详情获取异常:", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "业务品牌详情获取异常!");
        }
        log.info("获取业务品牌详情结果:{}", JSON.toJSONString(brandIdOriginIdMap));
        return brandIdOriginIdMap;
    }


    public List<BrandQueryResultBO> findBrand(BrandQueryParamBO brandQueryBO) throws WmCustomerException {

        log.info("findBrand参数:brandQueryBO={}", JSON.toJSONString(brandQueryBO));

        if (Objects.isNull(brandQueryBO) || CollectionUtils.isEmpty(brandQueryBO.getIdList())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询品牌参数无效");
        }
        BrandQueryParamDTO brandQueryParamDTO = new BrandQueryParamDTO();
        List<List<Long>> brandIdList = Lists.partition(brandQueryBO.getIdList(), BRAND_QUERY_LIMIT);
        List<BrandQueryResultDTO> brandQueryResultDTOList = new ArrayList<>();
        brandIdList.forEach(m -> {
            // originBrandId 物理品牌id
            brandQueryParamDTO.setPageSize(m.size());
            brandQueryParamDTO.setPageNo(NumberUtils.INTEGER_ONE);
            brandQueryParamDTO.setIdList(m);
            try {
                log.info("findBrand入参:{}", JSON.toJSONString(m));

                BrandQueryListResultDTO brandQueryListResultDTO = RetryUtil.call(CommonConst.RETRY_TIME,
                    true,
                    Objects::isNull,
                    () -> brandQueryThriftService.findBrand(brandQueryParamDTO));
                log.info("findBrand返回:{}", JSON.toJSONString(brandQueryListResultDTO));
                if (Objects.isNull(brandQueryListResultDTO)) {
                    log.error("当前品牌无相关信息");
                    return;
                }
                brandQueryResultDTOList.addAll(brandQueryListResultDTO.getList());

            } catch (Exception e) {
                log.error("查询品牌异常", e);
            }
        });

        if (CollectionUtils.isNotEmpty(brandQueryResultDTOList)) {
            List<BrandQueryResultBO> brandQueryResultBOList =
                brandQueryResultDTOList.stream().map(AclTransfer.INSTANCE::brandResultDTOToPBrandResultBO).collect(
                    Collectors.toList());
            log.info("brandQueryResultBOList结果={}", JSON.toJSONString(brandQueryResultBOList));
            return brandQueryResultBOList;

        } else {
            return Lists.newArrayList();
        }
    }

    public List<WmServiceBrandAggreDTO> searchServiceBrandAggre(WmServiceBrandAggreSearchConditionDTO dto) throws WmCustomerException {

        log.info("#searchServiceBrandAggre-批量查询品牌信息-req:{}",JSON.toJSONString(dto));
        List<WmServiceBrandAggreDTO> result = new ArrayList<>();
        try{
            WmServiceBrandAggreListResultDTO wmServiceBrandAggreListResultDTO = serviceBrandOuterQueryThriftService.searchServiceBrandAggre(dto);
            log.info("#searchServiceBrandAggre-批量查询品牌信息-req:{},resp:{}",JSON.toJSONString(dto),JSON.toJSONString(wmServiceBrandAggreListResultDTO));
            if(wmServiceBrandAggreListResultDTO != null){
                result = wmServiceBrandAggreListResultDTO.getWmServiceBrandAggreList();
            }
        }catch (Exception e){
            log.error("#searchServiceBrandAggre-批量查询品牌信息异常-req:{}",JSON.toJSONString(dto),e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"批量查询品牌信息异常");
        }
        return result;
    }
}
