package com.sankuai.meituan.waimai.customer.contract.global.authority.impl;

import com.sankuai.meituan.scmbrand.thrift.constant.BizOrgEnum;
import com.sankuai.meituan.uac.sdk.entity.UacRoleEntity;
import com.sankuai.meituan.waimai.customer.adapter.uac.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.global.authority.WmGlobalEcontractAuthorityService;
import com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractAuthorityCondition;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: UAC配置的权限
 * @author: liuyunjie05
 * @create: 2024/1/12 15:40
 */
@Service
public class WmGlobalEcontractUacAuthServiceImpl implements WmGlobalEcontractAuthorityService {

    @Resource
    private UacAuthRemoteServiceAdapter uacAuthRemoteServiceAdapter;

    @Override
    public void getAuthority(GlobalEcontractAuthorityCondition authorityCondition, String uid) {
        List<UacRoleEntity> allUserRoles = uacAuthRemoteServiceAdapter.getAllUserRolesByUid(uid);
        List<Long> allRoleIds = allUserRoles.stream().map(UacRoleEntity::getRoleId).collect(Collectors.toList());

        authorityCondition.setQueryUid(Long.parseLong(uid));
        authorityCondition.setUserUacRoleList(allUserRoles);
        if (allRoleIds.contains(MccConfig.getSuperDataAdminUacRoleId().longValue())) {
            authorityCondition.setSuperAdmin(true);
            return;
        }
        authorityCondition.setBizLineList(getBizLineList(allRoleIds));
    }

    private List<Integer> getBizLineList(List<Long> allRoleIds) {
        List<Integer> bizLineList = new ArrayList<>();
        if (allRoleIds.contains(MccConfig.getWaiMaiBizLineUacRoleId().longValue())) {
            bizLineList.add(BizOrgEnum.WAIMAI.getCode());
        }
        if (allRoleIds.contains(MccConfig.getShanGouBizLineUacRoleId().longValue())) {
            bizLineList.add(BizOrgEnum.SHANGOU.getCode());
        }
        if (allRoleIds.contains(MccConfig.getYiYaoBizLineUacRoleId().longValue())) {
            bizLineList.add(BizOrgEnum.MEDICINE.getCode());
        }
        return bizLineList;
    }

    @Override
    public int priority() {
        return 1;
    }
}
