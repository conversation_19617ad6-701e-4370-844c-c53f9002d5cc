package com.sankuai.meituan.waimai.customer.service.sc.flow.constant;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditStatusEnum;

/**
 * 食堂档口线索跟进审批状态枚举
 * <AUTHOR>
 * @date 2024/05/25
 * @email <EMAIL>
 */
public enum WmCanteenStallAuditStatusEnum {
    /**
     * 待提审
     */
    PENDING(0, "待提审", (int) CanteenStallAuditStatusEnum.PENDING.getType()),
    /**
     * 审批中
     */
    AUDITING(1, "审批中", (int) CanteenStallAuditStatusEnum.AUDITING.getType()),
    /**
     * 审批驳回
     */
    AUDIT_REJECT(2, "审批驳回", (int) CanteenStallAuditStatusEnum.REJECT.getType()),
    /**
     * 审批通过
     */
    AUDIT_PASS(3, "审批通过", (int) CanteenStallAuditStatusEnum.PASS.getType());


    private final Integer code;

    private final String desc;

    private final Integer auditStatusCode;

    WmCanteenStallAuditStatusEnum(Integer code, String desc, Integer auditStatusCode) {
        this.code = code;
        this.desc = desc;
        this.auditStatusCode = auditStatusCode;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getAuditStatusCode() {
        return auditStatusCode;
    }

    public static WmCanteenStallAuditStatusEnum getByAuditStatusCode(Integer auditStatusCode) {
        if (null == auditStatusCode) {
            return null;
        }
        for (WmCanteenStallAuditStatusEnum statusEnum : WmCanteenStallAuditStatusEnum.values()) {
            if (statusEnum.getAuditStatusCode().equals(auditStatusCode)) {
                return statusEnum;
            }
        }
        return null;
    }


}
