package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet.nationalsubsidy;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryWholeCityInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: 国补-闪购2.2-全城送
 * @author: liuyunjie05
 * @create: 2025/5/27 19:07
 */
@Slf4j
@Service
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.NATIONAL_SUBSIDY_WHOLE_CITY_SG22_FEEMODE)
public class NationalSubsidyWholeCitySg22PdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker  {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {
        List<EcontractDeliveryInfoBo> deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_WHOLE_CITY_PERFORMANCE.getName());
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();
        Map<String, String> subMap;
        for (EcontractDeliveryInfoBo infoBo : deliveryInfoList) {
            EcontractDeliveryWholeCityInfoBo econtractDeliveryWholeCityInfoBo = infoBo.getEcontractDeliveryWholeCityInfoBo();
            econtractDeliveryWholeCityInfoBo.setDeliveryArea(null);
            subMap = MapUtil.Object2Map(econtractDeliveryWholeCityInfoBo);
            if (StringUtils.isNotEmpty(infoBo.getSupportNewModle())) {
                subMap.put("supportNewModle", infoBo.getSupportNewModle());
            }
            if (StringUtils.isNotEmpty(infoBo.getNewModelVersion())) {
                subMap.put("newModelVersion", infoBo.getNewModelVersion());
            }
            subMap.put("wmPoiId", StringUtils.defaultIfEmpty(infoBo.getWmPoiId(), StringUtils.EMPTY));
            pdfBizContent.add(subMap);
        }

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("signTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        pdfMetaContent.put("partBStampName", ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc());
        pdfMetaContent.put("partBEstamp", PdfConstant.MT_SH_SIGNKEY);

        // 基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_WHOLE_CITY_SG22_TEMPLATE_ID", 2376));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_WHOLE_CITY_SG22_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        log.info("NationalSubsidyWholeCitySg22PdfMaker#makePdfContentInfoBo, pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }
}
