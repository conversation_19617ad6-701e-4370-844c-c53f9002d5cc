package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;
import java.util.Map;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBrandAdContractInfoBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.BRAND_AD_CONTRACT)
public class WmEcontractBrandAdContractDataWrapperService implements IWmEcontractDataWrapperService {

    private static final String TEMPLET_NAME = "品牌广告发布合同";

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BRAND_AD_CONTRACT);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(TEMPLET_NAME);
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo, taskBo));
        pdfInfoBo.setPdfBizContent(Lists.<Map<String,String>>newArrayList());
        return Lists.newArrayList(pdfInfoBo);
    }

    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) throws IllegalAccessException {
        log.info("contextBo = {}", JSON.toJSONString(contextBo));
        EcontractBrandAdContractInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBrandAdContractInfoBo.class);
        String endTime = DateUtil.secondsToString(contractInfoBo.getEndTime().intValue());
        String[] endTimeStrArray = endTime.split("-");
        String[] signTimeStrArray = contractInfoBo.getSignTime().split("-");

        Map<String, String> pdfMap = ObjectUtil.Object2Map(contractInfoBo);
        pdfMap.put("endTimeYear", endTimeStrArray.length == 3 ? endTimeStrArray[0] : StringUtils.EMPTY);
        pdfMap.put("endTimeMonth", endTimeStrArray.length == 3 ? endTimeStrArray[1] : StringUtils.EMPTY);
        pdfMap.put("endTimeDay", endTimeStrArray.length == 3 ? endTimeStrArray[2] : StringUtils.EMPTY);
        pdfMap.put("signTimeYear", signTimeStrArray.length == 3 ? signTimeStrArray[0] : StringUtils.EMPTY);
        pdfMap.put("signTimeMonth", signTimeStrArray.length == 3 ? signTimeStrArray[1] : StringUtils.EMPTY);
        pdfMap.put("signTimeDay", signTimeStrArray.length == 3 ? signTimeStrArray[2] : StringUtils.EMPTY);
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.MT_SH_SIGNKEY);

        log.info("pdfMap = {}", JSON.toJSONString(pdfMap));
        return pdfMap;
    }
}
