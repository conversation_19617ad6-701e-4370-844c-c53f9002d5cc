package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.C1_E})
public class WmC1EContractTempletAtomService extends AbstractWmEContractTempletAtomService {

    private static Logger logger = LoggerFactory.getLogger(WmC1EContractTempletAtomService.class);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
        Boolean effect = super.effect(templetContractId, opUid, opUname);
        WmContractAggre.Factory.make(templetContractId).saveWmContractFtlTagDB(opUid, opUname);
        return effect;
    }

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("WmC1EContractTempletAtomService#startSign");
        ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = WmContractAggre.Factory.make(contractBo.getBasicBo().getTempletContractId())
                .getWmCustomerContractBoById(false, opUid, opName);
        Integer contractId = insertOrUpdate(contractBo, opUid, opName);
        contractLogAggre.logUpdate(oldBo, contractBo, opUid, opName);
        if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {

            toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
            contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
            contractLogAggre.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
            ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(), opUid);
            wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
        } else {
            toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), opName);
            WmContractVersionDB versionDB = saveVersion(opUid, opName, contractId, CustomerContractStatus.SIGNING.getCode());
            contractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());
            contractLogAggre.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
            applySign(contractBo, versionDB, opUid, opName);
        }
        return contractId;
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(wmCustomerId)
                .applyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT)
                .bizId(wmCustomerId)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String c1ENum = ContractNumberUtil.genC1ENum(insertId);
        contractBo.getBasicBo().setContractNum(c1ENum);
        logger.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, c1ENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), c1ENum);
        return insertId;
    }

    @Override
    protected EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) {
        WmContractSignAggre wmContractSignAggre = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList());
        WmTempletContractSignBo partyASigner = wmContractSignAggre.getPartyASignerBo();
        WmTempletContractSignBo partyBSigner = wmContractSignAggre.getPartyBSignerBo();

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);

        EcontractContractInfoBo contractInfoBo = new EcontractContractInfoBo();
        contractInfoBo.setContractNum(contractBo.getBasicBo().getContractNum());
        contractInfoBo.setValidate(DateUtil.secondsToString(contractBo.getBasicBo().getDueDate()));

        WmCustomerKp kpOfSigner = wmCustomerKpService.getCustomerKpOfEffectiveSigner(contractBo.getBasicBo().getParentId());
        contractInfoBo.setPartAName(partyASigner.getSignName());
        contractInfoBo.setPartASignTime(partyASigner.getSignTime());

        if (kpOfSigner != null) {
            contractInfoBo.setPartAContact(kpOfSigner.getCompellation());
            contractInfoBo.setPartAContactPhone(kpOfSigner.getPhoneNum());
        }

        contractInfoBo.setPartBName("北京三快在线科技有限公司");
        contractInfoBo.setPartBContact(partyBSigner.getSignPeople());
        contractInfoBo.setPartBContactPhone(partyBSigner.getSignPhone());
        contractInfoBo.setPartBSignTime(partyBSigner.getSignTime());
        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
        return applyBo;
    }

    @Override
    public void toNextStatus(int contractId, int toStatus, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("#toNextStatus# contractId:{} toStatus:{} opUid:{} opUname:{}",
                contractId, toStatus, opUid, opUname);
        WmTempletContractBasicBo basic = WmContractAggre.Factory.make(contractId).getBasicById(false, opUid, opUname);
        int oldStatus = basic.getStatus();
        toNextContractStatus(contractId, toStatus, opUname);
        if (oldStatus == CustomerContractStatus.SIGNING.getCode()) {
            toNextContractVersionStatus(contractId, toStatus);
        }
    }

    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        throw new UnsupportedOperationException("非法操作，不支持废除操作。合同id:" + contractId + ", opUid:" + opUid + ", opUname:" + opUname);
    }
}
