package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.domain.BusinessValidateResult;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidateStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateField;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DateUtil;
import com.sankuai.meituan.wbinf.sg.api.certification.business.BusinessCertification;
import com.sankuai.meituan.wbinf.sg.api.certification.business.CheckBasicInfoRequest;
import com.sankuai.meituan.wbinf.sg.api.certification.business.CheckBasicInfoResponse;
import com.sankuai.meituan.wbinf.sg.api.certification.business.RegisterBasicInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 资质验真校验
 */
@Service
@Slf4j
public class WmCustomerBussinessValidator implements IWmCustomerValidator {

    @Autowired
    private BusinessCertification.Iface businessIface;

    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid)
            throws WmCustomerException {
        if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            BusinessValidateResult businessValidateResult = businessCheck(wmCustomerBasicBo);
            // 如果营业执照为注销或吊销
            if (businessValidateResult.getLicenseStatus() == CustomerConstants.BUSSINESS_LINESS_REVOKE
                    || businessValidateResult.getLicenseStatus() == CustomerConstants.BUSSINESS_LINESS_WRITEOFF) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_BUSINESS_ERROR, "营业执照吊销或注销");
            }
            // 资质验真,只有UNPASS需要提示,NODATA,ERROR放宽条件
            // 强制提交无需验真结果
            if (!force) {
                if (businessValidateResult.getValidateStatus() == ValidateStatus.UNPASS) {
                    validateResultBo.setValidateFieldList(businessValidateResult.getValidateFieldList());
                    return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_CHECK_ERROR, "客户营业执照验真失败");
                }
            }
        }
        return checkPass(validateResultBo);
    }

    /**
     * 资质验真
     * @param wmCustomerBasicBo
     * @return
     * @throws TException
     */
    private BusinessValidateResult businessCheck(WmCustomerBasicBo wmCustomerBasicBo) {
        BusinessValidateResult businessValidateResult = new BusinessValidateResult();
        try {
            CheckBasicInfoRequest request = new CheckBasicInfoRequest().setLicenseNo(wmCustomerBasicBo.getCustomerNumber())
                    .setEnterpriseName(wmCustomerBasicBo.getCustomerName()).setLegalPerson(wmCustomerBasicBo.getLegalPerson())
                    .setExpiredDate(wmCustomerBasicBo.getValidateDate() == 0 ? -1 : wmCustomerBasicBo.getValidateDate() * 1000)
                    .setAddress(wmCustomerBasicBo.getAddress()).setBusinessScope(wmCustomerBasicBo.getBusinessScope());
            log.info("营业执照判真，request = {}", request);
            CheckBasicInfoResponse response = businessIface.checkBasicInfo(ConfigUtilAdapter.getLong("BusinessCertification.partnerId", 11231000),
                    ConfigUtilAdapter.getString("BusinessCertification.digest", "com.sankuai.waimai.m.qualification"), request);
            log.info("营业执照判真，response = {}", response);

            if (response == null) {
                businessValidateResult.setValidateStatus(ValidateStatus.ERROR);
                return businessValidateResult;
            }

            if (response.getRegisterBasicInfo() == null) {
                businessValidateResult.setValidateStatus(ValidateStatus.NO_DATE);
                return businessValidateResult;
            } else {
                businessValidateResult.setLicenseStatus(response.getRegisterBasicInfo().getLicenseStatusCode());
            }

            if (response.isPassed()) {
                businessValidateResult.setValidateStatus(ValidateStatus.PASS);
                return businessValidateResult;
            }
            setValidateFieldList(businessValidateResult, wmCustomerBasicBo, response);
        } catch (Exception e) {
            log.warn("资质判真接口异常", e);
            businessValidateResult.setValidateStatus(ValidateStatus.ERROR);
        }
        return businessValidateResult;
    }

    /**
     * 拼装验真比对值
     * @param businessValidateResult
     * @param wmCustomerBasicBo
     * @param response
     * @return
     */
    private BusinessValidateResult setValidateFieldList(BusinessValidateResult businessValidateResult, WmCustomerBasicBo wmCustomerBasicBo,
                                                        CheckBasicInfoResponse response) {
        businessValidateResult.setValidateStatus(ValidateStatus.UNPASS);
        //错误的字段
        List<String> fields = response.getUnpassedReason();
        if (CollectionUtils.isEmpty(fields)) {
            return businessValidateResult;
        }

        RegisterBasicInfo registerBasicInfo = response.getRegisterBasicInfo();
        registerBasicInfo.setExpiredDate(registerBasicInfo.getExpiredDate() == -1 ? 0 : registerBasicInfo.getExpiredDate() / 1000);
        List<ValidateField> validateFieldList = Lists.newArrayList();
        Set<String> errorFieldSet = Sets.newHashSet(fields);
        if (errorFieldSet.contains("enterpriseName")) {
            validateFieldList
                    .add(new ValidateField("customerName", wmCustomerBasicBo.getCustomerName(), registerBasicInfo.getEnterpriseName(), "名称"));
        }

        if (errorFieldSet.contains("legalPerson")) {
            validateFieldList
                    .add(new ValidateField("legalPerson", wmCustomerBasicBo.getLegalPerson(), registerBasicInfo.getLegalPerson(), "法定代表人/经营者"));
        }

        if (errorFieldSet.contains("address")) {
            validateFieldList.add(new ValidateField("address", wmCustomerBasicBo.getAddress(), registerBasicInfo.getAddress(), "地址"));
        }

        if (errorFieldSet.contains("businessScope")) {
            validateFieldList
                    .add(new ValidateField("businessScope", wmCustomerBasicBo.getBusinessScope(), registerBasicInfo.getBusinessScope(), "经营范围"));
        }
        if (errorFieldSet.contains("expiredDate")) {
            validateFieldList.add(new ValidateField("validateDate",
                    wmCustomerBasicBo.getValidateDate() == 0 ? "长期有效"
                            : DateUtil.Date2String(new Date(wmCustomerBasicBo.getValidateDate() * 1000l), DateUtil.DefaultShortFormat),
                    registerBasicInfo.getExpiredDate() == 0 ? "长期有效"
                            : DateUtil.Date2String(new Date(registerBasicInfo.getExpiredDate() * 1000l), DateUtil.DefaultShortFormat),
                    "有效期"));
        }
        businessValidateResult.setValidateFieldList(validateFieldList);
        return businessValidateResult;
    }
}
