package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学校合作平台营销活动DO V2
 * <AUTHOR>
 * @date 2024/06/14
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmScSchoolCoPlatformMarketDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 学校合作平台ID
     */
    private Long platformPrimaryId;
    /**
     * 合作平台营销活动
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolPlatformMarketingActivityEnum}
     */
    private Integer platformMarketingActivity;
    /**
     * 合作平台营销活动选择了其他时填写的内容
     */
    private String platformMarketingActivityInfo;
    /**
     * 活动规则描述
     */
    private String activityRuleDescription;
    /**
     * 活动截图
     */
    private String activityPic;
    /**
     * 活动成本分摊类型
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolPlatformActivityCostSharingTypeEnum}
     */
    private Integer activityCostSharingType;
    /**
     * 活动成本分摊范围，商家-最小值
     */
    private String activityCostSharingPoiMin;
    /**
     * 活动成本分摊范围，商家-最大值
     */
    private String activityCostSharingPoiMax;
    /**
     * 活动成本分摊范围，平台-最小值
     */
    private String activityCostSharingPlatformMin;
    /**
     * 活动成本分摊范围，平台-最大值
     */
    private String activityCostSharingPlatformMax;
    /**
     * 是否有效 0-无效，1-有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
}
