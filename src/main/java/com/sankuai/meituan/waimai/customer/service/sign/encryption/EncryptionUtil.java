package com.sankuai.meituan.waimai.customer.service.sign.encryption;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.sankuai.inf.kms.pangolin.api.model.EncryptionRequest;
import com.sankuai.inf.kms.pangolin.api.service.EncryptServiceFactory;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;


/**
 * Created by lixuepeng on 2021/8/17
 */

@Service
public class EncryptionUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(EncryptionUtil.class);

    // 每种数据类型使用一个 IEncryptService
    private static IEncryptService EcontractSignEncryptService;

    String ECONTRACT_SIGN_NAME_SPACE = "com.sankuai.conch.certify.token";
    String ECONTRACT_SIGN_KEY_NAME = "unstructuredText";

    public EncryptionUtil() {
        EcontractSignEncryptService = EncryptServiceFactory.create(EncryptionRequest.Builder.anEncryptionRequest()
                .withNamespace(ECONTRACT_SIGN_NAME_SPACE).withKeyName(ECONTRACT_SIGN_KEY_NAME).build());
    }

    /**
     * 加密
     */
    public static String doEncrypt(String toEncryptString) {
        try {
            LOGGER.debug("EncryptionService.doEncrypt start:{}", System.currentTimeMillis());
            String result = EcontractSignEncryptService.encryptUTF8String(toEncryptString);
            LOGGER.debug("EncryptionService.doEncrypt end:{}", System.currentTimeMillis());
            return result;
        } catch (Exception e) {
            LOGGER.error("EncryptionService.doEncrypt 异常", e);
            return "";
        }
    }

    /**
     * 解密
     */
    public static String doDecrypt(String toDecryptString) {
        try {
            LOGGER.debug("EncryptionService.doDecrypt start:{}", System.currentTimeMillis());
            String result =  EcontractSignEncryptService.decryptUTF8String(toDecryptString);
            LOGGER.debug("EncryptionService.doDecrypt end:{}", System.currentTimeMillis());
            return result;
        } catch (Exception e) {
            LOGGER.error("EncryptionService.doDecrypt 异常", e);
            return "";
        }
    }
}
