package com.sankuai.meituan.waimai.customer.contract.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/8 17:44
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TemplateContractVersionPo {

    private Long id;

    private Long mtCustomerId;

    private Long wmCustomerId;

    private Integer agentId;

    private String contractNum;

    private Long templateId;

    private Integer contractType;

    private Long startTime;

    private Long endTime;

    private Long ctime;

    private Long utime;

    private Integer valid;

    private String context;

}
