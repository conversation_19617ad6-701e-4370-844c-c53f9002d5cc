package com.sankuai.meituan.waimai.customer.service.customer.dataConsistence;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmAuditServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAuditVo;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.constatnt.audit.WmAuditTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpAuditConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.dataConsistence.WmCustomerKpAuditDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditRejectReason;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditTask;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * kp审核数据不一致处理
 */
@Slf4j
@Service
public class WmCustomerKpAuditNoResultOperateService {

    private static final List<Byte> auditingStatus = Lists.newArrayList(KpSignerStateMachine.SPECILA_AUDIT_ING.getState(), KpSignerStateMachine.AGENT_AUDIT_ING.getState(),
            KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING.getState(), KpSignerStateMachine.CHANGE_AGENT_AUDIT_ING.getState());

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private WmAuditServiceAdaptor wmAuditServiceAdaptor;


    public void syncCustomerKpAuditNoResult(WmCustomerKpAuditDTO wmCustomerKpAuditDTO) throws WmCustomerException, TException {
        log.info("syncCustomerKpAuditNoResult wmCustomerKpAuditDTO={}", JSONObject.toJSONString(wmCustomerKpAuditDTO));
        WmCustomerKpAuditVo wmCustomerKpAuditVo = new WmCustomerKpAuditVo();
        wmCustomerKpAuditVo.setMinKpId(wmCustomerKpAuditDTO.getMinKpId());
        wmCustomerKpAuditVo.setMaxKpId(wmCustomerKpAuditDTO.getMaxKpId());
        wmCustomerKpAuditVo.setKpIdList(wmCustomerKpAuditDTO.getKpIdList());
        wmCustomerKpAuditVo.setAuditTypeList(Lists.newArrayList(KpAuditConstants.TYPE_AGENT, KpAuditConstants.TYPE_SPECIAL));
        int pageNo = 1;
        int pageSize = 100;
        List<WmCustomerKpAudit> list = Lists.newArrayList();
        while (true) {
            if (MccCustomerConfig.customerKpAuditNoResultStop()) {
                break;
            }
            wmCustomerKpAuditVo.setPageFrom((pageNo - 1) * pageSize);
            wmCustomerKpAuditVo.setPageSize(pageSize);
            List<WmCustomerKpAudit> wmCustomerKpAuditList = wmCustomerKpAuditMapper.selectByNoResult(wmCustomerKpAuditVo);
            if (CollectionUtils.isEmpty(wmCustomerKpAuditList)) {
                break;
            }
            list.addAll(wmCustomerKpAuditList);
            pageNo++;
        }

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<Integer, List<WmCustomerKpAudit>> auditMap = Maps.newHashMap();
        for (WmCustomerKpAudit wmCustomerKpAudit : list) {
            List<WmCustomerKpAudit> auditDBList = auditMap.get(wmCustomerKpAudit.getKpId());
            if (CollectionUtils.isEmpty(auditDBList)) {
                auditMap.put(wmCustomerKpAudit.getKpId(), Lists.<WmCustomerKpAudit>newArrayList());
            }
            auditMap.get(wmCustomerKpAudit.getKpId()).add(wmCustomerKpAudit);
        }

        List<Integer> kpIdList = Lists.newArrayList(auditMap.keySet());
        // 获取kp信息
        List<WmCustomerKp> wmCustomerKpList = wmCustomerKpDBMapper.selectByIdList(kpIdList);
        Map<Integer, WmCustomerKp> kpMap = wmCustomerKpList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        // 获取kpTemp信息
        List<WmCustomerKpTemp> wmCustomerKpTempList = wmCustomerKpTempDBMapper.selectByIdList(kpIdList);
        Map<Integer, WmCustomerKpTemp> kpTempMap = wmCustomerKpTempList.stream().collect(Collectors.toMap(x -> x.getKpId(), x -> x));


        for (Map.Entry<Integer, List<WmCustomerKpAudit>> data : auditMap.entrySet()) {
            Integer kpId = data.getKey();
            List<WmCustomerKpAudit> wmCustomerKpAuditList = data.getValue();
            if (CollectionUtils.isEmpty(wmCustomerKpAuditList)) {
                continue;
            }
            WmCustomerKp wmCustomerKp = kpMap.get(kpId);
            // kp不存在，提审记录置为无效
            if (wmCustomerKp == null) {
                continue;
            }

            WmCustomerKpTemp wmCustomerKpTemp = kpTempMap.get(kpId);
            byte state = wmCustomerKp.getState();
            if (KpSignerStateMachine.EFFECT.getState() == wmCustomerKp.getState() && wmCustomerKpTemp != null) {
                state = wmCustomerKpTemp.getState();
            }
            // 判断是否是流程中
            if (auditingStatus.contains(state)) {
                log.info("syncCustomerKpAuditNoResult kpId={}", kpId);
                for (WmCustomerKpAudit wmCustomerKpAudit : data.getValue()) {
                    syncAuditResult(wmCustomerKpAudit);
                }
            }
        }

    }


    private void syncAuditResult(WmCustomerKpAudit wmCustomerKpAudit) throws TException, WmCustomerException {
        int bizType = WmAuditTaskBizTypeConstant.AGENT_AUTH;
        if (wmCustomerKpAudit.getType() != null && wmCustomerKpAudit.getType().byteValue() == KpAuditConstants.TYPE_SPECIAL) {
            bizType = WmAuditTaskBizTypeConstant.SPECIAL_AUTH;
        }
        WmAuditTask wmAuditTask = wmAuditServiceAdaptor.getTask(wmCustomerKpAudit.getId(), bizType);
        if (wmAuditTask == null || wmAuditTask.getBatchId() <= 0 ||
                wmAuditTask.getStatus() == WmAuditTaskStatusEnum.APPROVE.getCode().byteValue() || wmAuditTask.getStatus() == WmAuditTaskStatusEnum.REJECT.getCode().byteValue()) {
            log.info("syncAuditResult needUpdate kpId={},bizId={}", wmCustomerKpAudit.getKpId(), wmCustomerKpAudit.getId());
            HostEnv env = ProcessInfoUtil.getHostEnv();
            DaxiangUtilV2.push(String.format("[%s]kp审核数据不一致:kpId:%s,bizId:%s",
                    env.name(), wmCustomerKpAudit.getKpId(), wmCustomerKpAudit.getId()), MccCustomerConfig.getAuditAlarmList());
        } else {
            return;
        }

        if (!MccCustomerConfig.customerKpAuditNoResultSwitch()) {
            return;
        }
        // 提审失败，置为无效
        if (wmAuditTask == null || wmAuditTask.getBatchId() <= 0) {
            wmCustomerKpAuditService.kpAuditCallback(wmCustomerKpAudit.getId(), KpAuditConstants.AUDIT_RESULT_TYPE_REJECT, "系统清洗", 0, "系统");
        } else if (wmAuditTask.getStatus() == WmAuditTaskStatusEnum.APPROVE.getCode().byteValue()) {
            wmCustomerKpAuditService.kpAuditCallback(wmCustomerKpAudit.getId(), KpAuditConstants.AUDIT_RESULT_TYPE_APPROVED, "系统清洗", wmAuditTask.getLastReceiveUid(), "系统");
        } else if (wmAuditTask.getStatus() == WmAuditTaskStatusEnum.REJECT.getCode().byteValue()) {
            // 审核驳回
            StringBuffer rejectMsg = new StringBuffer();
            List<WmAuditRejectReason> rejectReasons = wmAuditServiceAdaptor.getRejectReson(wmAuditTask.getId());
            for (WmAuditRejectReason wmAuditRejectReason : rejectReasons) {
                if (StringUtil.isNotBlank(wmAuditRejectReason.getRemark())) {
                    rejectMsg.append(wmAuditRejectReason.getRemark() + ";");
                }
            }
            wmCustomerKpAuditService.kpAuditCallback(wmCustomerKpAudit.getId(), KpAuditConstants.AUDIT_RESULT_TYPE_REJECT, rejectMsg.toString(), wmAuditTask.getLastReceiveUid(), "系统");
        }
    }

}
