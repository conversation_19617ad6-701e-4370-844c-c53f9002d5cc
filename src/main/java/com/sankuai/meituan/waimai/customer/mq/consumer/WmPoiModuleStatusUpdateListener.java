package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.ModuleEnum;
import com.sankuai.meituan.waimai.channel.beacon.thrift.vo.ModuleStateResVo;
import com.sankuai.meituan.waimai.customer.adapter.BeaconQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiQueryCondtionVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.customer.util.ExecutorUtil;
import com.sankuai.meituan.waimai.customer.util.GrayUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerPoiListInfoTransUtil;
import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiQualificationInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@Slf4j
@Service
public class WmPoiModuleStatusUpdateListener implements IMessageListener {


    private static final String WM_POI_ID = "wmPoiId";

    private static final String MODULE_CODE = "moduleCode";


    @Autowired
    private BeaconQueryThriftServiceAdapter beaconQueryThriftServiceAdapter;

    @Autowired
    private WmCustomerPoiListEsService esService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    private static ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("poi_module_pool_%d").build();
    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(10, 100, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(10000), threadFactory, new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));


    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        log.info("receive wmPoiModuleStatusUpdateMsg {}", message);
        String body = String.valueOf(message.getBody());
        if (StringUtils.isEmpty(body)) {
            log.warn("topic:{},partition:{},offset:{}, body is empty!!!", message.getTopic(), message.getParttion(), message.getOffset());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            JSONObject poiModuleStatusUpdate = JSON.parseObject(body);
            Long wmPoiId = poiModuleStatusUpdate.getLong(WM_POI_ID);
            if (wmPoiId == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            Integer moduleCode = poiModuleStatusUpdate.getInteger(MODULE_CODE);
            if (moduleCode == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            int asyncGrayPercent = MccCustomerConfig.getCustomerPoiListESAsyncGrayPercent();
            if (wmPoiId % 100 < asyncGrayPercent) {
                log.info("go asyncUpdate wmPoiId={},moduleCode={},poiModuleStatusUpdate={}", wmPoiId, moduleCode, poiModuleStatusUpdate);
                return asyncUpdate(wmPoiId, moduleCode, poiModuleStatusUpdate);
            }

            List<WmCustomerPoiDB> list = getWmCustomerPoi(wmPoiId);
            if (CollectionUtils.isEmpty(list)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            int grayPercent = MccCustomerConfig.getSettleQueryMasterGrayPercent();
            boolean isFromMaster = moduleCode == ModuleEnum.SETTLE.getCode() && GrayUtil.genRandomInt() <= grayPercent;
            ConcurrentMap<String, Object> poiModuleStatusMap = getPoiModuleStatus(wmPoiId, isFromMaster);
            if (poiModuleStatusMap == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            ConcurrentMap<Object, Map<String, Object>> map = Maps.newConcurrentMap();

            for (WmCustomerPoiDB db : list) {
                log.info("wmCustomerPoiDB={},poiModuleStatusMap={}", JSONObject.toJSONString(db), JSONObject.toJSONString(poiModuleStatusMap));
                map.put(db.getId(), poiModuleStatusMap);
            }
            esService.bulkUpdateForPoiStatus(map);

            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("客户门店列表同步门店模块状态 处理失败 msg={}", JSONObject.toJSONString(message.getBody().toString()), e);
        }
        return ConsumeStatus.RECONSUME_LATER;
    }

    /**
     * 客户门店列表同步门店模块状态异步化
     *
     * @param wmPoiId
     * @param moduleCode
     * @param message
     * @return
     */
    private ConsumeStatus asyncUpdate(Long wmPoiId, Integer moduleCode, JSONObject message) {
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    log.info("asyncUpdate wmPoiId={},moduleCode={},message={}", wmPoiId, moduleCode, message.toString());
                    List<WmCustomerPoiDB> list = getWmCustomerPoi(wmPoiId);
                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }

                    int grayPercent = MccCustomerConfig.getSettleQueryMasterGrayPercent();
                    boolean isFromMaster = moduleCode == ModuleEnum.SETTLE.getCode() && GrayUtil.genRandomInt() <= grayPercent;
                    ConcurrentMap<String, Object> poiModuleStatusMap = getPoiModuleStatus(wmPoiId, isFromMaster);
                    if (poiModuleStatusMap == null) {
                        return;
                    }

                    ConcurrentMap<Object, ConcurrentMap<String, Object>> map = Maps.newConcurrentMap();
                    for (WmCustomerPoiDB db : list) {
                        log.info("wmCustomerPoiDB={},poiModuleStatusMap={}", JSONObject.toJSONString(db), JSONObject.toJSONString(poiModuleStatusMap));
                        map.put(db.getId(), poiModuleStatusMap);
                    }
                    esService.bulkUpdateForPoiStatus(map);
                } catch (Exception e) {
                    log.error("客户门店列表同步门店模块状态 处理失败 msg={}", message.toString(), e);
                }

            }
        }));
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    private List<WmCustomerPoiDB> getWmCustomerPoi(Long wmPoiId) {
        WmCustomerPoiQueryCondtionVo vo = new WmCustomerPoiQueryCondtionVo();
        vo.setWmPoiId(wmPoiId);
        return wmCustomerPoiDBMapper.selectCustomerPoiRelByCondition(vo);
    }

    private ConcurrentMap<String, Object> getPoiModuleStatus(Long wmPoiId, boolean isFromMaster) {
        ModuleStateResVo vo = null;
        if (isFromMaster) {
            vo = beaconQueryThriftServiceAdapter.queryPoiModuleStateRT(wmPoiId);
        } else {
            vo = beaconQueryThriftServiceAdapter.queryPoiModuleState(wmPoiId);
        }

        if (vo == null || CollectionUtils.isEmpty(vo.getModules())) {
            return null;
        }
        ConcurrentMap<String, Object> map = Maps.newConcurrentMap();
        WmCustomerPoiListInfoTransUtil.buildModuleStatus(vo, map);
        return map;
    }
}
