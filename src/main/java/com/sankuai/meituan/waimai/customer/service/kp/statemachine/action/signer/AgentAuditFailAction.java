package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpBuryingPointService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpAuditConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.UN_VALID;

/**
 * <AUTHOR>
 * @date 20240423
 * @desc KP签约人代理人提审失败事件
 */
@Service
@Slf4j
public class AgentAuditFailAction extends KpSignerAbstractAction {

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    /**
     * KP签约人提审事件
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpSignerBaseSM
     */
    @Override
    public void execute(KpSignerStateMachine from, KpSignerStateMachine to, KpSignerEventEnum eventEnum, KpSignerStatusMachineContext context, KpSignerBaseSM kpSignerBaseSM) {
        log.info("AgentAuditFailAction,代理人审核失败action开始执行,from={},to={},context={}", from, to, JSON.toJSONString(context));

        boolean haveEffectFlag = context.getExistEffectiveFlag();
        String reason = context.getKpAuditResultBody().getRejectReason();
        WmCustomerKpAudit audit = context.getWmCustomerKpAudit();
        //驳回原因过长需要截取
        if (StringUtils.isNotEmpty(reason)) {
            if (reason.length() > WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_KP) {
                reason = reason.substring(0, WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_KP - WmCustomerConstant.TOO_LONG_MSG.length()) + WmCustomerConstant.TOO_LONG_MSG;
            }
        }
        WmCustomerKp signerKp = context.getOldCustomerKp();
        //未生效过
        if (!haveEffectFlag) {
            signerKp.setState(KpSignerStateMachine.AGENT_AUDIT_REJECT.getState());
            signerKp.setFailReason(reason);
            wmCustomerKpDBMapper.updateByPrimaryKey(signerKp);
        } else {
            WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(signerKp.getId());
            kpTemp.setState(KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT.getState());
            kpTemp.setFailReason(reason);
            //更新临时变更记录为驳回失败
            wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
            wmCustomerKpBuryingPointService.afterSingKp(null, context.getWmCustomerKpAudit().getAcctId(), kpTemp == null ? null : wmCustomerKpAuditService.transformWmCustomerKpTemp(kpTemp), signerKp);
        }

        //更新KP提审记录为无效
        audit.setValid(UN_VALID);
        audit.setResult("代理人审核驳回");
        wmCustomerKpAuditMapper.updateByPrimaryKey(audit);

        //签约人获取明文信息
        wmCustomerSensitiveWordsService.readKpWhenSelect(signerKp);
        //记录操作日志
        wmCustomerKpLogService.changeState(signerKp.getCustomerId(), signerKp, "代理人审核驳回，驳回原因：" + reason, context.getOpUid(), context.getOpUName());

    }
}
