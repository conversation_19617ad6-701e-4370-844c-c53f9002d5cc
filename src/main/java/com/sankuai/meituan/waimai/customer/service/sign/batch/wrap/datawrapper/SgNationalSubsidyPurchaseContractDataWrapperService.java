package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.nationalsubsidy.EcontractNationalSubsidyPurchaseInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 国补采购协议
 */
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.NATIONAL_SUBSIDY_PURCHASE)
public class SgNationalSubsidyPurchaseContractDataWrapperService implements IWmEcontractDataWrapperService {


    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo)
        throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_PURCHASE);
        Map<String, String> PdfContentInfoMap = generatePdfObject(taskBo);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PURCHASE_TEMPLATE_ID", 30));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PURCHASE_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfMetaContent(PdfContentInfoMap);
        pdfInfoBo.setContractName(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_PURCHASE.getDesc());
        pdfInfoBo.setContractDesc("乙方：" + PdfContentInfoMap.get("partB"));
        return Lists.newArrayList(pdfInfoBo);
    }

    /**
     * 国补采购协议数据
     */
    private Map<String, String> generatePdfObject(EcontractTaskBo taskBo) {
        EcontractNationalSubsidyPurchaseInfoBo contractInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractNationalSubsidyPurchaseInfoBo.class);
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());

        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("contractNumber", StringUtils.defaultIfEmpty(contractInfoBo.getContractNum(), StringUtils.EMPTY));
        pdfMap.put("partAStampName",StringUtils.defaultIfEmpty(contractInfoBo.getPartAStampName(), StringUtils.EMPTY));
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partB", StringUtils.defaultIfEmpty(contractInfoBo.getPartBStampName(), StringUtils.EMPTY));
        pdfMap.put("partBSignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.AGENT_SIGNKEY);
        return pdfMap;
    }
}
