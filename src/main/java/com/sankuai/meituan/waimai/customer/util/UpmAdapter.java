package com.sankuai.meituan.waimai.customer.util;

import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.sankuai.meituan.upm.pigeon.api.RemoteRetrieveService;
import com.sankuai.meituan.upm.pigeon.model.RemotePermRoleBean;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-10-20 19:33
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class UpmAdapter {

    @Reference
    private RemoteRetrieveService remoteRetrieveService;

    public List<RemotePermRoleBean> getUserPermByAppKeyAndUserId(String appkey, Integer userId) throws Exception {
        return remoteRetrieveService.getUserPerm(appkey, userId);
    }
}
