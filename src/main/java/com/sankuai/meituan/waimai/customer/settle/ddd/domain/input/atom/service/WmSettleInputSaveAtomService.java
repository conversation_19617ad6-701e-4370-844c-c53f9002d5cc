package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service;

import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleInputContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * 结算录入-原子化服务接口-录入数据保存
 */
public interface WmSettleInputSaveAtomService {

    /**
     * 保存线下数据-主表
     */
    int saveOrUpdateOfflineSettle(WmSettleInputContext context) throws WmCustomerException;

    /**
     * 保存纸质签约模式-补充协议、钱袋宝协议附件
     */
    int saveOrUpdateWmSettleProtocol(WmSettleInputContext context) throws WmCustomerException;

}
