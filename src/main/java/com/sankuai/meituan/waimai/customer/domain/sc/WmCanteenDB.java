package com.sankuai.meituan.waimai.customer.domain.sc;

import com.sankuai.meituan.waimai.customer.service.sc.annotate.AuditField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: scm
 * @description: 食堂管理数据库类
 * @author: jianghuimin02
 * @create: 2020-04-23 15:44
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmCanteenDB extends WmScCommonDB {

    /**
     * 食堂主键ID
     */
    private Integer id;
    /**
     * 食堂ID
     */
    private Integer canteenId;
    /**
     * 学校id
     */
    @AuditField("学校id")
    private Integer schoolId;
    /**
     * 学校名称
     */
    @AuditField("学校名称")
    private String schoolName;
    /**
     * 承包商名称
     */
    @AuditField("承包商名称")
    private String contractorName;
    /**
     * 承包商id
     */
    @AuditField("承包商id")
    private Integer contractorId;
    /**
     * 食堂名称
     */
    @AuditField("食堂名称")
    private String canteenName;
    /**
     * 食堂类型：校园食堂：1。预留字段：未来会扩展美食城：2、白领食堂：3
     */
    @AuditField("食堂类型值")
    private Integer canteenType;
    /**
     * 类型描述
     */
    @AuditField("食堂类型")
    private String typeDesc;
    /**
     * 食堂属性：学校直营：1、承包商承包：2。
     */
    @AuditField("食堂属性值")
    private Integer canteenAttribute;
    /**
     * 属性描述
     */
    @AuditField("食堂属性")
    private String attributeDesc;
    /**
     * 供给分级：SKR直营：1、KR直营：2、食堂直营：3、SKR承包：4、KR承包：5、食堂承包：6
     */
    private Integer grade;
    /**
     * 分级描述
     */
    private String gradeDesc;
    /**
     * 食堂经理
     */
    @AuditField("食堂经理")
    private String manager;
    /**
     * 经理电话
     */
    @AuditField("经理电话")
    private String managerPhone;
    /**
     * 合作状态：未合作：0、已合作：1
     */
    private Integer canteenStatus;
    /**
     * 状态描述
     */
    private String statusDesc;
    /**
     * 档口数量
     */
    @AuditField("档口数量")
    private Integer stallNum;
    /**
     * 门店数量
     */
    private Integer storeNum;
    /**
     * 食堂责任人
     */
    private String responsiblePerson;
    /**
     * 学校生效状态，1：生效、0：逻辑删除
     */
    private int valid;
    /**
     * 是否按钮灰度，0不置灰；1置灰
     * 这个按钮置灰涉及到 分配责任人按钮  操作记录按钮 食堂详情页面中的保存按钮 门店信息操作中的
     */
    private int gray;
    /**
     * 物理主键ID集合
     */
    private List<Integer> idList;
    /**
     * 食堂ID集合
     */
    private List<Integer> canteenIdList;
    /**
     * 证件类型 1-身份证
     */
    @AuditField()
    private Integer cardType;
    /**
     * 证件类型desc
     */
    @AuditField("证件类型")
    private String cardTypeDesc;
    /**
     * 证件号码
     */
    @AuditField("证件号码")
    private String cardNo;
    /**
     * 有效性 0-未生效，1-生效
     */
    private Integer effective;
    /**
     * 生效类型描述
     */
    private String effectiveDesc;
    /**
     * 审核状态 1-审批中,2-审批通过,3-驳回
     */
    private Integer auditStatus;
    /**
     * 审核状态描述
     */
    private String auditStatusDesc;
    /**
     * 审核明细状态
     */
    private Integer auditDetailStatus;
    /**
     * 审核明细状态描述
     */
    private String auditDetailStatusDesc;
    /**
     * 经理电话加密形式
     */
    @AuditField()
    private String managerPhoneEncryption;
    /**
     * 经理电话token
     */
    @AuditField()
    private String managerPhoneToken;
    /**
     * 原managerPhone是否写，只做判断使用不存储
     * 0-表示写 1-表示不写
     */
    private int notSaveManagerPhone;
    /**
     * 证件号码加密形式
     */
    @AuditField()
    private String cardNoEncryption;
    /**
     * 证件号码加密token
     */
    @AuditField()
    private String cardNoToken;
    /**
     * 原cardNo是否写，只做判断使用不存储
     * 0-表示写 1-表示不写
     */
    private int notSaveCardNo;
    /**
     * 查询条件
     */
    private Integer minId;
    /**
     * 查询条件
     */
    private Integer maxId;
    /**
     * 食堂责任人id
     */
    private String responsiblePersonUid;
    /**
     * 查询条件-合作档口数量左值
     */
    private Integer storeNumStart;
    /**
     * 查询条件-合作档口数量右值
     */
    private Integer storeNumEnd;
    /**
     * 档口总数
     */
    private Integer stallNumTotal;
    /**
     * 合作档口总数
     */
    private Integer storeNumTotal;
    /**
     * 线下营业档口数量
     */
    @AuditField("线下营业档口数量")
    private Integer offlineBizStallNum;
    /**
     * 可上线档口数量
     */
    @AuditField("可上线档口数量")
    private Integer preOnlineStallNum;
    /**
     * 食堂视频
     */
    @AuditField("食堂视频")
    private String canteenVideo;

    private Integer aorType;

    /**
     * 食堂品类，餐饮:1、闪购:2、寝室便利店:3
     */
    @AuditField()
    private Integer category;

    /**
     * 食堂品类描述
     */
    @AuditField("食堂品类")
    private String categoryDesc;
}
