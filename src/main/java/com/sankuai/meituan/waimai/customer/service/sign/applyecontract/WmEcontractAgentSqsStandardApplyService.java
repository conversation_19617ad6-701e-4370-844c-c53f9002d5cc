package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAHNLXWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampHNLXWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
public class WmEcontractAgentSqsStandardApplyService extends AbstractWmEcontractApplyAdapterService {

    private static final String FLOW_AGENT_SQS_STANDARD = EcontractTaskApplyTypeEnum.AGENT_SQS_STANDARD.getName();

    private static List<String> flowList = Lists.newArrayList();

    private static List<String> poiStampList = Lists.newArrayList();

    private static List<String> mtStampList = Lists.newArrayList();


    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_AGENT_SQS_STANDARD);

        poiStampList.add(FLOW_AGENT_SQS_STANDARD);

        dataWrapperMap.put(FLOW_AGENT_SQS_STANDARD, EcontractDataWrapperEnum.AGENT_SQS_STANDARD);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    String type_agent_sqs_standard = "type_agent_sqs_standard";

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, poiStampList));


        return new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(type_agent_sqs_standard)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .callBackUrl(getCallbackUrl())
                .econtractBatchSource(getSource(batchContextBo))
                .build();
    }
}
