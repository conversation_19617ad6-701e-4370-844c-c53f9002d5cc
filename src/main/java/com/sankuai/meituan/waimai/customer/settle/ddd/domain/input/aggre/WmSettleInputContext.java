package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.service.PushDXContext;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolDB;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleModifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiSettle;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class WmSettleInputContext {

    private WmSettleModifyBo wmSettleModifyBo;

    //线下表当前结算主表数据
    private WmSettleDB wmSettleDBOld;
    //线上表当前结算主表数据
    private WmSettleAuditedDB wmSettleAuditedDBOld;
    //线下表当前关联门店
    private List<Integer> wmPoiIdListOld = Lists.newArrayList();
    //当前待保存数据关联门店
    private List<Long> wmPoiIdList = Lists.newArrayList();

    //组装结算主体数据+结算关联门店数据-切换不下线校验使用
    private List<WmSettle> wmSettleList = Lists.newArrayList();
    private List<WmSettleAudited> wmSettleAuditedList = Lists.newArrayList();
    private List<Long> switchingWmPoiIdListFromSourceCustomer = Lists.newArrayList();

    private List<WmSettleDB> wmSettleDBList = Lists.newArrayList();
    private int opUid;
    private String opUname = "";
    private WmCustomerDB wmCustomerDB;

    private List<Integer> wmSettleIdListBySettleName = Lists.newArrayList();
    private List<Integer> wmSettleIdListBySettleNameAudited = Lists.newArrayList();

    private PushDXContext pushDXContext;

    private List<WmPoiSettle> addSettleList = Lists.newArrayList();
    private List<WmPoiSettle> commonSettleList = Lists.newArrayList();
    private List<Integer> deleteList = Lists.newArrayList();

    private int wmCustomerId;

    private Set<Long> wmCustomerPoiSet = Sets.newHashSet();

    private Set<Long> switchingWmPoiIdSet = Sets.newHashSet();

    private List<SwitchPoiInfo> settleSwitchPoiInfoList = Lists.newArrayList();

    public boolean isCommit() {
        return wmSettleModifyBo == null ? false : wmSettleModifyBo.isCommit();
    }

    public boolean isPaperSignMode() {
        return wmCustomerDB == null ? false : wmCustomerDB.getSignMode() == CustomerSignMode.PAPER.getCode();
    }

}
