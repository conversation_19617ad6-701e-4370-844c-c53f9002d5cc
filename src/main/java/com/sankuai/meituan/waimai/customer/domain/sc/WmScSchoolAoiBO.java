package com.sankuai.meituan.waimai.customer.domain.sc;

import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScSchoolAoiDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 学校AOI信息业务对象类
 * <AUTHOR>
 * @date 2023/06/01
 * @email <EMAIL>
 */
@Data
public class WmScSchoolAoiBO {
    /**
     * 学校AOI ID
     */
    private Long aoiId;
    /**
     * 学校AOI名称
     */
    private String aoiName;
    /**
     * 学校AOI范围
     */
    private String aoiArea;
    /**
     * 即时配是否允许配送进校 -1-未知 0-骑行 1-步行 2-禁止 4-可通行但骑步行未知
     */
    private Integer aoiMode;
    /**
     * 即时配是否允许配送进校描述
     */
    private String aoiModeDesc;

    /**
     * 学校AOI信息BO转DTO
     * @param wmScSchoolAoiBOList 学校AOI信息BO列表
     * @return 学校AOI信息DTO列表
     */
    public static List<WmScSchoolAoiDTO> transWmScSchoolAoiBoToDto(List<WmScSchoolAoiBO> wmScSchoolAoiBOList) {
        List<WmScSchoolAoiDTO> wmScSchoolAoiDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScSchoolAoiBOList)) {
            return wmScSchoolAoiDTOList;
        }
        for (WmScSchoolAoiBO wmScSchoolAoiBO : wmScSchoolAoiBOList) {
            WmScSchoolAoiDTO wmScSchoolAoiDTO = new WmScSchoolAoiDTO();
            wmScSchoolAoiDTO.setAoiId(wmScSchoolAoiBO.getAoiId());
            wmScSchoolAoiDTO.setAoiName(wmScSchoolAoiBO.getAoiName());
            wmScSchoolAoiDTO.setAoiMode(wmScSchoolAoiBO.getAoiMode());
            wmScSchoolAoiDTO.setAoiModeDesc(wmScSchoolAoiBO.getAoiModeDesc());
            wmScSchoolAoiDTO.setAoiArea(wmScSchoolAoiBO.getAoiArea());
            wmScSchoolAoiDTOList.add(wmScSchoolAoiDTO);
        }
        return wmScSchoolAoiDTOList;
    }
}
