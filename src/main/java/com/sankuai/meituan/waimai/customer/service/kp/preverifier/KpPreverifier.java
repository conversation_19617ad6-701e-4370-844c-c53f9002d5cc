package com.sankuai.meituan.waimai.customer.service.kp.preverifier;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.util.List;

public interface KpPreverifier {

    /**
     * 校验要添加的KP列表信息
     *
     * @param wmCustomer          客户基础信息对象
     * @param oldWmCustomerKpList DB中现有的KP列表
     * @param addKpList           待添加的KP列表
     * @param deleteKpList        待删除的KP列表
     * @param upgradeKpList       待更新的KP列表
     * @throws WmCustomerException 校验失败抛出的异常
     */
    void verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldWmCustomerKpList, List<WmCustomerKp> addKpList, List<WmCustomerKp> deleteKpList, List<WmCustomerKp> upgradeKpList) throws WmCustomerException;


    /**
     * 按从小到大顺序执行校验器
     */
    int order();

}
