package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EstampInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-11-30 17:13
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@Service
public class WmEcontractStampBJSKWLWrapperService implements IWmEcontractStampWrapperService {
    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo, List<String> flowList) throws WmCustomerException {
        //美团北京三快网络信息
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
                .setCustomerId(WmEcontractConstant.CERTIFY_CUSTOMER_BJSKWL)
                .build();
        //北京三快网络签章
        Map<String, String> estampParamMap = Maps.newHashMap();
        estampParamMap.put(TaskConstant.PDF_ESTAMP_SIGN_KEY, PdfConstant.BJSKWL_SIGNKEY);
        EstampInfoBo estampInfoBo = new EstampInfoBo.Builder()
                .setEstampMap(estampParamMap)
                .build();

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.STAMP_BJSKWL)
                .certifyInfoBo(certifyInfoBo)
                .estampInfoBo(estampInfoBo)
                .metaFlowList(flowList)
                .build();
    }
}
