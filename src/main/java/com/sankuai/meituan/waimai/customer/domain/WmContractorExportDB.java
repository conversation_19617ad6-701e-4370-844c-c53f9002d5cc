package com.sankuai.meituan.waimai.customer.domain;

import lombok.Data;

/**
 * @program: scm
 * @description: 导出查找类
 * @author: jianghuimin02
 * @create: 2020-07-01 15:55
 **/
@Data
public class WmContractorExportDB {
    private int contractorId;
    private Long  mtCustomerId;
    private long  customerId;
    private String customerName;
    private int customerRealType;
    private int customerType;
    private int ownerUid;
    private int auditStatus;
    private int ctime;
    private int canteenNum;
    private int cityNum;
    private int poiNum;//关联门店数
    private int wmCoStatus;//承包商合作状态
    private String contractNum;//合同编号
    private int hqSecondCityId;//总部物理城市Id
    private String hqSecondCity;//总部物理城市
    private String hqDetailAddress;//总部详细地址
    private int schoolNum;//关联学校数
}


