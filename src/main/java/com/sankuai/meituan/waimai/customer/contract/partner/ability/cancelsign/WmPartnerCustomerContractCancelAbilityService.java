package com.sankuai.meituan.waimai.customer.contract.partner.ability.cancelsign;

import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractCancelSignRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractCancelSignResponseDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/9 15:14
 */
public interface WmPartnerCustomerContractCancelAbilityService {


    CustomerContractCancelSignResponseDTO cancelCustomerContractSign(CustomerContractCancelSignRequestDTO requestDTO) throws WmCustomerException;


}
