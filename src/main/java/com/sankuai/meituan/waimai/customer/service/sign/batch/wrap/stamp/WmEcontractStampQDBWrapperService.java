package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp;

import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.CertifyConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EstampInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class WmEcontractStampQDBWrapperService implements IWmEcontractStampWrapperService {
    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo, List<String> flowList) {
        //钱袋宝CA认证
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
            .setCustomerName(CertifyConstant.QIAN_DAI_BAO_CA)
            .build();
        //钱袋宝签章信息
        Map<String, String> estampParamMap = Maps.newHashMap();
        estampParamMap.put(TaskConstant.PDF_ESTAMP_SIGN_KEY, PdfConstant.QDB_SIGNKEY);
        EstampInfoBo estampInfoBo = new EstampInfoBo.Builder()
            .setEstampMap(estampParamMap)
            .build();

        return new StageBatchInfoBo.Builder()
            .stageName(WmEcontractConstant.STAMP_QDB)
            .certifyInfoBo(certifyInfoBo)
            .estampInfoBo(estampInfoBo)
            .metaFlowList(flowList)
            .build();
    }
}
