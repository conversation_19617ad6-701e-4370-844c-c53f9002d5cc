package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.team.client.request.AddTeamNodeUserRequest;
import com.sankuai.waimai.crm.team.client.response.TeamBaseResponse;
import com.sankuai.waimai.crm.team.client.service.TeamThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 业务团队管理服务接口适配器
 * <AUTHOR>
 * @date 2023/08/30
 * @email <EMAIL>
 */
@Slf4j
@Service
public class TeamServiceAdapter {

    @Autowired
    private TeamThriftService teamThriftService;

    /**
     * 获取用户添加到团队的结果
     * @param addTeamNodeUserRequest 请求入参
     * @return TeamBaseResponse
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public TeamBaseResponse addTeamNodeUser(AddTeamNodeUserRequest addTeamNodeUserRequest) throws WmSchCantException {
        try {
            log.info("[TeamServiceAdapter.addTeamNodeUser] addTeamNodeUserRequest = {}", addTeamNodeUserRequest.toString());
            TeamBaseResponse response = teamThriftService.addTeamNodeUser(addTeamNodeUserRequest);
            log.info("[TeamServiceAdapter.addTeamNodeUser] response = {}", response.toString());
            return response;
        } catch (Exception e) {
            log.error("[TeamServiceAdapter.getAddTeamNodeUserResult] Exception. addTeamNodeUserRequest = {}", JSONObject.toJSONString(addTeamNodeUserRequest), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "添加人员到团队失败");
        }
    }

}
