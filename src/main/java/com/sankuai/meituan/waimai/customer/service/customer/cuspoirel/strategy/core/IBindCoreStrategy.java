package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core;

import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * <AUTHOR>
 * @date 20240115
 * @desc 绑定核心操作策略
 */
public interface IBindCoreStrategy {

    /**
     * 执行方法
     *
     * @param context
     * @throws WmCustomerException
     */
    void execute(CustomerPoiBindFlowContext context) throws WmCustomerException;
}
