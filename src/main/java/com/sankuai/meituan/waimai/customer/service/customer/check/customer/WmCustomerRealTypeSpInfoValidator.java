package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户类型特殊扩展数据校验
 */
@Service
@Slf4j
public class WmCustomerRealTypeSpInfoValidator implements IWmCustomerValidator {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        try {
            // 校验客户类型
            return wmCustomerService.validateCustomerRealTypeSpInfo(wmCustomerBasicBo, opUid);
        } catch (Exception e) {
            log.warn("校验客户类型异常", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "校验客户类型异常");
        }
    }
}
