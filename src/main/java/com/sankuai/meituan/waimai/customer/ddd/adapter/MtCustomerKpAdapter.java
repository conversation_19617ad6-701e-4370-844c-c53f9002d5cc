package com.sankuai.meituan.waimai.customer.ddd.adapter;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.dto.SignerDTO;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.nibcus.inf.customer.client.request.SignerGetRequest;
import com.sankuai.nibcus.inf.customer.client.response.CustomerIdsResponse;
import com.sankuai.nibcus.inf.customer.client.response.SignerGetResponse;
import com.sankuai.nibcus.inf.customer.client.service.BusinessCustomerThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MtCustomerKpAdapter {

    @Autowired
    private BusinessCustomerThriftService businessCustomerThriftService;

    public Map<Long, List<SignerDTO>> batchGetSigners(SignerGetRequest signerGetRequest) throws TException, WmCustomerException {
        log.info("#batchGetSigners-根据平台客户ID批量查询生效的签约人信息-req:{}", JSON.toJSONString(signerGetRequest));
        SignerGetResponse signerGetResponse = businessCustomerThriftService.batchGetSigners(signerGetRequest);
        log.info("#batchGetSigners-根据平台客户ID批量查询生效的签约人信息-req:{},resp:{}",JSON.toJSONString(signerGetRequest),JSON.toJSONString(signerGetResponse));
        if (!signerGetResponse.isSuccess()){
            log.info("#batchGetSigners-根据平台客户ID批量查询生效的签约人信息查询失败-req:{},resp:{}",JSON.toJSONString(signerGetRequest),JSON.toJSONString(signerGetResponse));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,signerGetResponse.getMessage());
        }
        return signerGetResponse.getCustomerSigners();
    }

    public Map<Long , List<Long>> batchQueryMtCustomerId(List<Long> mtPoiIds, BusinessLineEnum businessLineEnum) throws WmCustomerException {
        Map<Long , List<Long>> poi2CustomerIdMap = new HashMap<>(mtPoiIds.size());
        for (Long mtPoiId : mtPoiIds) {
            List<Long> customerIds = getCustomerIdsByPoiIdAndBusinessLine(mtPoiId, businessLineEnum);
            poi2CustomerIdMap.put(mtPoiId, customerIds);
        }
        return poi2CustomerIdMap;
    }

    public List<Long> getCustomerIdsByPoiIdAndBusinessLine(Long poiId, BusinessLineEnum businessLineEnum) throws WmCustomerException {
        try {
            log.info("MtCustomerKpAdapter#getCustomerIdsByPoiIdAndBusinessLine, poiId: {}, businessLineEnum: {}", poiId, businessLineEnum);
            CustomerIdsResponse response = businessCustomerThriftService.getCustomerIdsByPoiIdAndBusinessLine(poiId, businessLineEnum);
            log.info("MtCustomerKpAdapter#getCustomerIdsByPoiIdAndBusinessLine, response: {}", JSON.toJSONString(response));
            if (response.isSuccess()) {
                return response.getCustomerIds();
            } else {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, response.getMessage());
            }
        } catch (Exception e) {
            log.error("MtCustomerKpAdapter#getCustomerIdsByPoiIdAndBusinessLine, poiId: {}, businessLineEnum: {}, error",
                    poiId, businessLineEnum, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询门店关联客户ID异常");
        }
    }


}
