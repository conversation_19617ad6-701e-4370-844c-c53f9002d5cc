package com.sankuai.meituan.waimai.customer.service.sc.flow.action.schoolDeliveryAssignment;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskBO;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.auditflow.WmSchoolDeliveryAuditTaskService;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmSchoolDeliveryStatusEnum;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmSchoolDeliveryStatusMachineEvent;
import com.sankuai.meituan.waimai.customer.service.sc.flow.entity.WmSchoolDeliveryStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.sc.flow.exception.WmScStatusMachineException;
import com.sankuai.meituan.waimai.customer.service.sc.flow.machine.WmSchoolDeliveryAssignmentStatusMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryStreamNodeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.squirrelframework.foundation.fsm.AnonymousAction;

/**
 * 学校交付人员指定-撤回审批回退至审批驳回
 * <AUTHOR>
 * @date 2024/02/13
 * @email <EMAIL>
 */
@Component
@Slf4j
public class WmSchoolDeliveryAssignmentBackRejectAction extends AnonymousAction<WmSchoolDeliveryAssignmentStatusMachine, WmSchoolDeliveryStatusEnum, WmSchoolDeliveryStatusMachineEvent, WmSchoolDeliveryStatusMachineContext> {

    @Autowired
    private WmSchoolDeliveryAuditTaskService wmSchoolDeliveryAuditTaskService;

    /**
     * 学校交付人员指定-撤回审批回退至审批驳回
     * @param fromState 流转前状态
     * @param toState 流转后状态
     * @param event 事件
     * @param context 上下文
     */
    @Override
    public void execute(WmSchoolDeliveryStatusEnum fromState,
                        WmSchoolDeliveryStatusEnum toState,
                        WmSchoolDeliveryStatusMachineEvent event,
                        WmSchoolDeliveryStatusMachineContext context,
                        WmSchoolDeliveryAssignmentStatusMachine stateMachine) {
        log.info("[WmSchoolDeliveryAssignmentBackRejectAction.execute] input param: fromState = {}, toState = {}, event = {}, context = {}, stateMachine = {}",
                fromState, toState, event, context, stateMachine);
        WmSchoolDeliveryAuditTaskBO auditTaskBO = context.getAuditTaskBO();
        auditTaskBO.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.DELIVERY_ASSIGNMENT.getCode());
        try {
            wmSchoolDeliveryAuditTaskService.cancelAuditTask(auditTaskBO);
        } catch (WmSchCantException e) {
            log.error("[WmSchoolDeliveryAssignmentBackRejectAction.execute] WmSchCantException. auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO), e);
            throw new WmScStatusMachineException("交付人员指定撤回审批异常");
        }
    }
}
