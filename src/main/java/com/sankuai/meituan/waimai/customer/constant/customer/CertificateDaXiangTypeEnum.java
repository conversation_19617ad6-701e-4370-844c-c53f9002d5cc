package com.sankuai.meituan.waimai.customer.constant.customer;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.AsciiUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants.CUSTOMER_FIELD_VALIDATEDATE;

/**
 * 给大象发送消息类型
 */
public enum CertificateDaXiangTypeEnum {
    /**
     * 存续
     */
    DURATION(1, "存续"),
    /**
     * 注销
     */
    CANCELLATION(2, "注销"),
    /**
     * 吊销
     */
    REVOKE(3, "吊销"),
    /**
     * 过期
     */
    EXPIRED(4, "过期"),
    /**
     * 纸质形式
     */
    PAPER(5, "纸质形式"),
    /**
     * 电照——法人变更
     */
    LEGALPARSONCHANGE(6, "法人变更，其他字段不变更"),
    /**
     * 电照——法人以外字段变更
     */
    LEGALPARSONCHANGEOTHER(7, "法人不变更，其他字段变更");

    private int type;
    private String name;

    CertificateDaXiangTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    private static Map<Integer, CertificateDaXiangTypeEnum> enumByTypeMap = Maps.newHashMap();
    private static Map<String, CertificateDaXiangTypeEnum> enumByNameMap = Maps.newHashMap();

    static {
        for (CertificateDaXiangTypeEnum certificateDaXiangTypeEnum : values()) {
            enumByTypeMap.put(certificateDaXiangTypeEnum.getType(), certificateDaXiangTypeEnum);
            enumByNameMap.put(certificateDaXiangTypeEnum.getName(), certificateDaXiangTypeEnum);
        }
    }

    public static CertificateDaXiangTypeEnum getByType(int type) {
        return enumByTypeMap.get(type);
    }

    public static CertificateDaXiangTypeEnum getByName(String name) {
        return enumByNameMap.get(name);
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getMessage(WmCustomerDB wmCustomerDB, WmCustomerDB wmCustomerDBUpdate, int type) throws WmCustomerException {
        String result = "";
        CertificateDaXiangTypeEnum certificateDaXiangTypeEnum = CertificateDaXiangTypeEnum.getByType(type);
        if (certificateDaXiangTypeEnum == null) {
            return result;
        }
        switch (certificateDaXiangTypeEnum) {
            //注销
            case CANCELLATION:
                result = cancellationMsg(wmCustomerDB);
                break;
            //吊销
            case REVOKE:
                result = revokeMsg(wmCustomerDB);
                break;
            //电照——法人变更
            case LEGALPARSONCHANGE:
                result = legalparsonchangeMsg(wmCustomerDB, wmCustomerDBUpdate);
                break;
            //电照——法人以外字段变更
            case LEGALPARSONCHANGEOTHER:
                result = legalparsonchangeOtherMsg(wmCustomerDB, wmCustomerDBUpdate);
                break;
            //纸质形式
            case PAPER:
                result = paperMsg(wmCustomerDB, wmCustomerDBUpdate);
                break;
            //过期
            case EXPIRED:
                result = expiredMsg(wmCustomerDB);
                break;
            default:
                result = "";
                break;
        }
        return result;
    }

    /**
     * 注销消息通知
     *
     * @param wmCustomerDBe
     * @return
     */
    private static String cancellationMsg(WmCustomerDB wmCustomerDBe) {
        //【客户资质注销】客户名称：XX ，客户ID：XX ，该客户资质已注销，请联系商家更换资质。
        StringBuilder result = new StringBuilder();
        result.append("【客户资质注销】客户名称：").append(wmCustomerDBe.getCustomerName())
                .append(" ，客户ID：").append(wmCustomerDBe.getId())
                .append(" ，该客户资质已注销，请联系商家更换资质。");
        return String.valueOf(result);
    }

    /**
     * 吊销消息通知
     *
     * @param wmCustomerDB
     * @return
     */
    private static String revokeMsg(WmCustomerDB wmCustomerDB) {
        //【客户资质吊销】客户名称：XX ，客户ID：XX ，该客户资质已吊销，请联系商家更换资质。
        StringBuilder result = new StringBuilder();
        result.append("【客户资质吊销】客户名称：").append(wmCustomerDB.getCustomerName())
                .append(" ，客户ID：").append(wmCustomerDB.getId())
                .append(" ，该客户资质已吊销，请联系商家更换资质。");
        return String.valueOf(result);
    }

    /**
     * 法人变更消息通知
     *
     * @param wmCustomerDB
     * @return
     */
    private static String legalparsonchangeMsg(WmCustomerDB wmCustomerDB, WmCustomerDB wmCustomerDBUpdate) {
        //【客户资质法人变更】客户名称：XX ，客户ID：XX ，该客户资质法人已发生变更（从XXX变更为XXX）
        // ，营业执照失效，请联系商家核实并前往”客户信息“重新上传纸质营业执照或重新扫描识别电子执照。
        StringBuilder result = new StringBuilder();
        result.append("【客户资质法人变更】客户名称：").append(wmCustomerDB.getCustomerName())
                .append(" ，客户ID：").append(wmCustomerDB.getId())
                .append(" ，该客户资质法人已发生变更（").append(wmCustomerDB.getLegalPerson())
                .append("变更为").append(wmCustomerDBUpdate.getLegalPerson())
                .append(") ，营业执照失效，请联系商家核实并前往”客户信息“重新上传纸质营业执照或重新扫描识别电子执照。");
        return String.valueOf(result);
    }

    /**
     * 法人以外字段变更消息通知
     *
     * @param wmCustomerDB
     * @return
     */
    private static String legalparsonchangeOtherMsg(WmCustomerDB wmCustomerDB, WmCustomerDB wmCustomerDBUpdate) throws WmCustomerException {
        //【客户资质信息变更】客户名称：XX ，客户ID：XX ，营业执照形式是电子形式，
        // 系统获取信息发生变更消息后已自动将执照名称从XXXXXX变更为XXXXXX，地址从XXXXXX变更为XXXXXX，
        // 有效期从XXXXXX变更为XXXXXX，经营范围从XXXXXX变更为XXXXXX。
        StringBuilder result = new StringBuilder();
        List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(wmCustomerDB, wmCustomerDBUpdate, CustomerConstants.customerCompareDaxaing);
        filterAscii(diffList);
        if (CollectionUtils.isEmpty(diffList)) {
            return Strings.EMPTY;
        }
        result.append("【客户资质信息变更】客户名称：").append(wmCustomerDB.getCustomerName())
                .append(" ，客户ID：").append(wmCustomerDB.getId())
                .append(" ，营业执照形式是电子形式，系统获取信息发生变更消息后已自动将");
        diffList.stream().forEach(wmCustomerDiffCellBo -> {
            result.append(wmCustomerDiffCellBo.getDesc()).append("从");
            //有效期
            if (wmCustomerDiffCellBo.getField().equals(CUSTOMER_FIELD_VALIDATEDATE)) {
                if (wmCustomerDiffCellBo.getPre().equals("0")) {
                    result.append("长期有效");
                } else {
                    result.append(DateUtil.seconds2TimeFormat(Long.parseLong(wmCustomerDiffCellBo.getPre()), DateUtil.DefaultShortFormat));
                }
                result.append("变更为");
                if (wmCustomerDiffCellBo.getAft().equals("0")) {
                    result.append("长期有效");
                } else {
                    result.append(DateUtil.seconds2TimeFormat(Long.parseLong(wmCustomerDiffCellBo.getAft()), DateUtil.DefaultShortFormat));
                }
                result.append(",");
            } else {
                result.append(StringUtils.isNotEmpty(wmCustomerDiffCellBo.getPre()) ? wmCustomerDiffCellBo.getPre() : "空").append("变更为")
                        .append(StringUtils.isNotEmpty(wmCustomerDiffCellBo.getAft()) ? wmCustomerDiffCellBo.getAft() : "空").append("，");
            }
        });
        result.deleteCharAt(result.length() - 1).append("。");
        return String.valueOf(result);
    }

    /**
     * 纸质形式消息通知
     *
     * @param wmCustomerDB
     * @return
     */
    private static String paperMsg(WmCustomerDB wmCustomerDB, WmCustomerDB wmCustomerDBUpdate) throws WmCustomerException {
        //【客户资质字段变更】客户名称：XX ，客户ID：XX ，营业执照形式是纸质形式，系统获取信息发生变更消息，
        // 变更消息为执照名称从XXXXXX变更为XXXXXX，地址从XXXXXX变更为XXXXXX，有效期从XXXXXX变更为XXXXXX，
        // 经营范围从XXXXXX变更为XXXXXX，请联系商家核实并前往”客户信息“手动更新。
        StringBuilder result = new StringBuilder();
        List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(wmCustomerDB, wmCustomerDBUpdate, CustomerConstants.customerCompareDaxaing);
        filterAscii(diffList);
        if (CollectionUtils.isEmpty(diffList)) {
            return Strings.EMPTY;
        }
        result.append("【客户资质字段变更】客户名称：").append(wmCustomerDB.getCustomerName())
                .append(" ，客户ID：").append(wmCustomerDB.getId())
                .append(" ，营业执照形式是纸质形式，系统获取信息发生变更消息，变更消息为");
        diffList.stream().forEach(wmCustomerDiffCellBo -> {
            result.append(wmCustomerDiffCellBo.getDesc()).append("从");
            //有效期
            if (wmCustomerDiffCellBo.getField().equals(CUSTOMER_FIELD_VALIDATEDATE)) {
                if (wmCustomerDiffCellBo.getPre().equals("0")) {
                    result.append("长期有效");
                } else {
                    result.append(DateUtil.seconds2TimeFormat(Long.parseLong(wmCustomerDiffCellBo.getPre()), DateUtil.DefaultShortFormat));
                }
                result.append("变更为");
                if (wmCustomerDiffCellBo.getAft().equals("0")) {
                    result.append("长期有效");
                } else {
                    result.append(DateUtil.seconds2TimeFormat(Long.parseLong(wmCustomerDiffCellBo.getAft()), DateUtil.DefaultShortFormat));
                }
                result.append(",");
            } else {
                result.append(StringUtils.isNotEmpty(wmCustomerDiffCellBo.getPre()) ? wmCustomerDiffCellBo.getPre() : "空").append("变更为")
                        .append(StringUtils.isNotEmpty(wmCustomerDiffCellBo.getAft()) ? wmCustomerDiffCellBo.getAft() : "空").append("，");
            }
        });
        result.append("请联系商家核实并前往”客户信息“手动更新。");
        return String.valueOf(result);
    }

    /**
     * 过期消息通知
     *
     * @param wmCustomerDB
     * @return
     */
    private static String expiredMsg(WmCustomerDB wmCustomerDB) {
        //【客户资质过期】客户名称：XX ，客户ID：XX ，该客户资质已过期，请联系商家更换资质或更新有效期。
        StringBuilder result = new StringBuilder();
        result.append("【客户资质过期】客户名称：").append(wmCustomerDB.getCustomerName())
                .append(" ，客户ID：").append(wmCustomerDB.getId())
                .append(" ，该客户资质已过期，请联系商家更换资质或更新有效期。");
        return String.valueOf(result);
    }

    /**
     * 过滤掉因为特殊字符以及半角/全角问题引起的无效diff
     *
     * @param diffList
     */
    private static void filterAscii(List<WmCustomerDiffCellBo> diffList) {
        if (!MccCustomerConfig.asciiDealWhenCompareSwitch()) {
            return;
        }
        if (CollectionUtil.isEmpty(diffList)) {
            return;
        }
        List<String> fieldKeyList = MccCustomerConfig.asciiDealFieldWhenCompare();
        if (CollectionUtil.isEmpty(fieldKeyList)) {
            return;
        }
        Iterator<WmCustomerDiffCellBo> it = diffList.iterator();
        while (it.hasNext()) {
            WmCustomerDiffCellBo wmCustomerDiffCellBo = it.next();
            if (fieldKeyList.contains(wmCustomerDiffCellBo.getField())) {
                String pre = AsciiUtil.replaceSpecialStr(AsciiUtil.toDBCCase(wmCustomerDiffCellBo.getPre()));
                String aft = AsciiUtil.replaceSpecialStr(AsciiUtil.toDBCCase(wmCustomerDiffCellBo.getAft()));
                if (pre.equals(aft)) {
                    it.remove();
                }
            }
        }
    }

}
