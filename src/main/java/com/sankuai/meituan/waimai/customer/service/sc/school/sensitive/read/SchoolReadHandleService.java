package com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.read;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
public class SchoolReadHandleService {

    @Autowired
    private List<ISchoolReadHandle> iSchoolReadHandleList;

    private Map<KmsKeyNameEnum, ISchoolReadHandle> iReadHandleMap = new HashMap<>();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(iSchoolReadHandleList)) {
            return;
        }
        for (ISchoolReadHandle handel : iSchoolReadHandleList) {
            iReadHandleMap.put(handel.handleType(), handel);
        }
    }


    public void doReadChoiceEncryptToDecrypt(SchoolRead schoolRead)  {
        if (schoolRead == null) {
            return ;
        }
        iReadHandleMap.get(schoolRead.getKeyName()).doReadChoiceEncryptToDecrypt(schoolRead);
    }


    public String doReadEncryptToDecrypt(SchoolRead schoolRead) {
        if (schoolRead == null) {
            return "";
        }
        return iReadHandleMap.get(schoolRead.getKeyName()).doReadEncryptToDecrypt(schoolRead);
    }
}
