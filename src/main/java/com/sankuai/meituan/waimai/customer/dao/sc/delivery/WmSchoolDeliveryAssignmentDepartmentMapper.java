package com.sankuai.meituan.waimai.customer.dao.sc.delivery;

import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDepartmentDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校交付人员指定-部门意向生效信息Mapper
 * <AUTHOR>
 * @date 2024/02/08
 * @email <EMAIL>
 **/
@Component
public interface WmSchoolDeliveryAssignmentDepartmentMapper {

    /**
     * 根据主键ID查询学校交付人员指定部门摸排生效信息
     * @param id 主键ID
     * @return 学校交付人员指定部门摸排生效信息
     */
    WmSchoolDeliveryAssignmentDepartmentDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据学校主键ID查询学校交付人员指定部门摸排生效信息
     * @param schoolPrimaryId 学校主键ID
     * @return 学校交付人员指定部门摸排生效信息
     */
    List<WmSchoolDeliveryAssignmentDepartmentDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据交付ID查询学校交付人员指定部门摸排生效信息
     * @param deliveryId 交付ID
     * @return 学校交付人员指定部门摸排生效信息
     */
    List<WmSchoolDeliveryAssignmentDepartmentDO> selectByDeliveryId(@Param("deliveryId") Integer deliveryId);

    /**
     * 新增学校交付人员指定生效信息
     * @param deliveryAssignmentDepartmentDO 学校交付人员指定部门摸排生效信息
     * @return 更新行数
     */
    int insertSelective(WmSchoolDeliveryAssignmentDepartmentDO deliveryAssignmentDepartmentDO);

    /**
     * 根据主键ID更新学校交付人员指定生效信息
     * @param deliveryAssignmentDepartmentDO 学校交付人员指定部门摸排生效信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmSchoolDeliveryAssignmentDepartmentDO deliveryAssignmentDepartmentDO);

    /**
     * 新增学校交付人员指定生效信息
     * @param departmentDOList 学校交付人员指定部门摸排生效信息列表
     * @return 更新行数
     */
    int batchInsertSelective(@Param("list") List<WmSchoolDeliveryAssignmentDepartmentDO> departmentDOList);

    /**
     * 批量逻辑删除
     * @param primaryIdList 主键ID列表
     */
    void deleteByPrimaryIdList(@Param("primaryIdList") List<Integer> primaryIdList);
}
