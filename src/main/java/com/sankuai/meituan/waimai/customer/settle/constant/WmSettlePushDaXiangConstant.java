package com.sankuai.meituan.waimai.customer.settle.constant;

public class WmSettlePushDaXiangConstant {

    /**
     * 错误类型
     */
    public enum errorType {
        BATCH_ADD((byte) 1, "批量添加"),
        BATCH_DELETE((byte) 2, "批量解绑");

        private byte value;
        private String desc;

        errorType(byte value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public byte getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 错误模块
     */
    public enum errorModule {
        SETTLE_POI((byte) 5, "结算关联门店");

        private byte value;
        private String desc;

        errorModule(byte value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public byte getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 错误原因
     */
    public enum errorReason {
        PERSION((byte) 1, "存在无操作权限的商家"),
        CUSTOMER((byte)2, "存在非本客户关联的商家"),
        OTHER_CONTRACT_REL((byte) 4, "存在门店已关联其他合同"),
        OFFLINE_SETTLE_REL((byte) 5, "存在门店已关联其他结算"),
        NOT_REL_SETTLE((byte) 7, "存在门店没有关联当前结算");

        private byte value;
        private String desc;

        errorReason(byte value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public byte getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }
    }
}
