package com.sankuai.meituan.waimai.customer.service.sign.batchOp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.util.base.EcontractBatchOpResponseUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchOpStageEnum;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignManualTaskDBMapper;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;

import javax.annotation.Nullable;

/**
 * @description: 批量操作相关逻辑-待手动打包任务
 * @author: lixuepebg
 * @create: 2021-05-24
 **/
@Service
public class WmEcontractSignBatchOpManualItemHandler implements WmEcontractSignBatchOpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractSignBatchOpManualItemHandler.class);

    public static final String MANUAL_TASK_TYPE = "manualTask";

    public static final String FAIL_TIPS = "任务%s，执行失败，失败原因：";

    @Autowired
    private WmEcontractSignManualTaskDBMapper wmEcontractSignManualTaskDBMapper;
    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    /**
     * 任务种类(batchIdType)-打包签约任务、普通任务、待手动打包任务
     */
    public String getBatchIdType() {
        return MANUAL_TASK_TYPE;
    }

    /**
     * 根据查询条件(@see EcontractBatchOpRequest)查询任务id列表
     */
    public List<Long> getItemListByOpRequest(EcontractBatchOpRequest opRequest) {
        //全部或者待开始的任务才进行查询
        if (EcontractBatchOpStageEnum.findByValue(opRequest.getOpStage()) == EcontractBatchOpStageEnum.ALL
                || EcontractBatchOpStageEnum.findByValue(opRequest.getOpStage()) == EcontractBatchOpStageEnum.TO_START) {
            return queryManualTaskItemIdList(opRequest);
        }
        return new ArrayList<>();
    }

    /**
     * 重发短信-返回失败列表
     * <p>
     * 此类型任务无重发短信功能
     */
    public List<EcontractBatchOpFailTaskDTO> handleResendMsg(EcontractBatchOpRequest opRequest,
                                                             ListeningExecutorService executorService, List<Long> itemList) throws InterruptedException {
        List<EcontractBatchOpFailTaskDTO> failTaskDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            for (Long item : itemList) {
                EcontractBatchOpFailTaskDTO failTaskDTO = new EcontractBatchOpFailTaskDTO();
                failTaskDTO.setTaskId(item);
                failTaskDTO.setFailMsg(String.format(FAIL_TIPS, item) + "待签约的任务不能重发短信");
                failTaskDTOList.add(failTaskDTO);
            }
        }
        return failTaskDTOList;
    }

    /**
     * 取消任务-返回失败列表
     */
    public List<EcontractBatchOpFailTaskDTO> handleCancel(EcontractBatchOpRequest opRequest,
                                                          ListeningExecutorService executorService, List<Long> itemList) throws InterruptedException {
        List<EcontractBatchOpFailTaskDTO> failTaskDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            CountDownLatch latch = new CountDownLatch(itemList.size());
            for (Long item : itemList) {
                ListenableFuture batchItemFuture = executorService.submit(new Callable() {
                    @Override
                    public Object call() throws Exception {
                        return wmEcontractSignBzService.cancelManualTask(item, opRequest.getOpUid(),
                                EcontractTaskConstants.ManualTaskCancelSource.TASK_MANAGE_MODULE.getSource());
                    }
                });
                Futures.addCallback(batchItemFuture, new FutureCallback() {
                    @Override
                    public void onSuccess(@Nullable Object result) {//成功情况下不做处理
                        latch.countDown();
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        EcontractBatchOpFailTaskDTO failTaskDTO = new EcontractBatchOpFailTaskDTO();
                        failTaskDTO.setTaskId(item);
                        failTaskDTO.setFailMsg(String.format(FAIL_TIPS, item) + t.getMessage());
                        failTaskDTOList.add(failTaskDTO);
                        latch.countDown();
                    }
                });
            }
            latch.await();
        }
        return failTaskDTOList;
    }

    /**
     * 手动打包签约-返回失败列表
     */
    public EcontractBatchOpResponse handleManualPack(EcontractBatchOpRequest opRequest,
                                                     ListeningExecutorService executorService, List<Long> itemList) throws WmCustomerException, TException {

        EcontractBatchOpResponse opResponse = new EcontractBatchOpResponse();
        List<EcontractBatchOpFailTaskDTO> failTaskDTOList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(itemList)) {

            List<Long> doMunualPackItemList = new ArrayList<>();

            //判断itemList大小，默认250个限制，如果超过250个则只发起top250的任务
            if (itemList.size() > MccSignConfig.perManualPackTaskMaxSize()) {
                doMunualPackItemList.addAll(itemList.stream().limit(MccSignConfig.perManualPackTaskMaxSize()).collect(Collectors.toList()));
            } else {
                doMunualPackItemList.addAll(itemList);
            }

            LongResult manualPackResult = wmEcontractSignBzService.applyManualPack(doMunualPackItemList, opRequest.getOpUid(), WmSignConstant.BATCH_PLATFORM);

            //对于不能发起打包的任务返回错误信息
            itemList.removeAll(doMunualPackItemList);
            for (Long item : itemList) {
                EcontractBatchOpFailTaskDTO failTaskDTO = new EcontractBatchOpFailTaskDTO();
                failTaskDTO.setTaskId(item);
                failTaskDTO.setFailMsg(String.format(FAIL_TIPS, item) + "待发起手动打包签约任务数量过多");
                failTaskDTOList.add(failTaskDTO);
            }

            opResponse.setManualPackId(manualPackResult.getValue());
        }

        opResponse.setFailTaskDTOs(failTaskDTOList);
        return opResponse;
    }

    /**
     * 创建待打包任务
     *
     * @param opRequest
     * @return
     * @throws WmCustomerException
     * @throws TException          此类型任务不能创建手动待打包签约
     */
    @Override
    public EcontractBatchOpResponse handleCreatePreManualTask(EcontractBatchOpRequest opRequest) throws WmCustomerException, TException {
        EcontractBatchOpResponse opResponse = new EcontractBatchOpResponse();
        List<EcontractBatchOpFailTaskDTO> failTaskDTOList = new ArrayList<>();
        EcontractBatchOpFailTaskDTO failTaskDTO = new EcontractBatchOpFailTaskDTO();
        failTaskDTO.setFailMsg("该类型任务不能创建手动待打包任务");
        failTaskDTOList.add(failTaskDTO);
        opResponse.setFailTaskDTOs(failTaskDTOList);
        return opResponse;
    }

    @Override
    public EcontractBatchOpResponse handleCreateStartSignTask(EcontractBatchOpRequest opRequest) throws WmCustomerException, TException {
        return EcontractBatchOpResponseUtil.createFailResponseWithEmptyFailTaskList("该类型任务不能发起一键发起签约");
    }

    private List<Long> queryManualTaskItemIdList(EcontractBatchOpRequest request) {//获取待开始状态任务
        List<Long> manualTaskIds = new ArrayList<>();
        List<WmEcontractSignManualTaskDB> manualTaskDBList = wmEcontractSignManualTaskDBMapper.
                queryManualTaskItemParamWithLabel(request, 0, MccConfig.batchOpQueryPageSize());
        while (CollectionUtils.isNotEmpty(manualTaskDBList)) {
            for (WmEcontractSignManualTaskDB manualTaskDB : manualTaskDBList) {
                if (!filterManualTaskItem(request, manualTaskDB)) {
                    continue;
                }
                manualTaskIds.add(manualTaskDB.getId());
            }
            manualTaskDBList = wmEcontractSignManualTaskDBMapper.queryManualTaskItemParamWithLabel(request,
                    manualTaskDBList.get(manualTaskDBList.size() - 1).getId(), MccConfig.batchOpQueryPageSize());
        }
        LOGGER.info("queryBatchItemIdList request:{}, manualTaskDBList:{}", JSON.toJSONString(request), manualTaskDBList);
        return manualTaskIds;
    }

    private boolean filterManualTaskItem(EcontractBatchOpRequest request, WmEcontractSignManualTaskDB manualTaskDB) {
        Set<EcontractTaskApplyTypeEnum> checkTaskType = WmEcontractBatchConstant.TASK_TYPE_MAP.get(request.getTaskType());
        if (checkTaskType == null) {
            return false;
        }
        if (!checkTaskType.contains(EcontractTaskApplyTypeEnum.getByName(manualTaskDB.getModule()))) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(request.getWmPoiIdList())) {//门店id不为空
            List<Long> wmPoiIds = parseWmPoiIdListFromWmEcontractSignManualTaskBo(manualTaskDB);//任务对应的门店id
            if (CollectionUtils.isEmpty(wmPoiIds) ||
                    !WmEcontractBatchConstant.NEED_CHECK_POI_INFO_TASK_TYPE_SET.contains(EcontractTaskApplyTypeEnum.getByName(manualTaskDB.getModule()))) {
                return false;
            }
            if (Collections.disjoint(request.getWmPoiIdList(), wmPoiIds)) { //无交集
                return false;
            }
        }
        return true;
    }

    private List<Long> parseWmPoiIdListFromWmEcontractSignManualTaskBo(WmEcontractSignManualTaskDB manualTaskDB) {
        List<Long> result = Lists.newArrayList();
        if (manualTaskDB.getModule().equals(EcontractTaskApplyTypeEnum.SETTLE.getName())) {
            String applyContext = manualTaskDB.getApplyContext();
            if (!StringUtils.isEmpty(applyContext)) {
                ManualTaskSettleContextBo manualTaskSettleContextBo = JSONObject
                        .parseObject(applyContext, ManualTaskSettleContextBo.class);
                result = manualTaskSettleContextBo.getWmPoiList();
            }
        } else if (manualTaskDB.getModule().equals(EcontractTaskApplyTypeEnum.POIFEE.getName())) {
            result.add(manualTaskDB.getWmPoiId());
        }
        return result;
    }
}
