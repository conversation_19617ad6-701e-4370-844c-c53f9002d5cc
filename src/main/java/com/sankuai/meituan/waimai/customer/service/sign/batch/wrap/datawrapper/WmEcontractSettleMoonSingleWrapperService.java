package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;


import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 单店结算登月版商家信息结算单+授权书
 */
@Service
public class WmEcontractSettleMoonSingleWrapperService {

    private static final String TEMPLET_NAME = "settle_moon_single_info_v3.ftl";

    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo)
        throws IllegalAccessException {
        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());

        //抽取一个开钱包的对象-业务规则上允许部分开钱包
        EcontractSettleInfoBo infoBo = null;
        for(EcontractSettleInfoBo temp : settleInfoBoList){
            if(temp.isSupportWallet()){
                infoBo = temp;
                break;
            }
        }

        EcontractSettleInfoBo econtractSettleInfoBo = infoBo;
        econtractSettleInfoBo.setDiffInfo("");
        Map<String, String> pdfMap = MapUtil.Object2Map(econtractSettleInfoBo);
        pdfMap.put("signTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfMetaContent(pdfMap);
        pdfInfoBo.setPdfTemplateName(ConfigUtilAdapter.getString("PDF_SETTLE_MOON_SIGNLE_INFO_V3", TEMPLET_NAME));
        return Lists.newArrayList(pdfInfoBo);
    }

}
