package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmEcontractDelayProcessDB;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface WmEcontractDelayProcessDBMapper {

    int deleteByPrimaryKey(Long id);

    int insert(WmEcontractDelayProcessDB record);

    int insertSelective(WmEcontractDelayProcessDB record);

    WmEcontractDelayProcessDB selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmEcontractDelayProcessDB record);

    int updateByPrimaryKey(WmEcontractDelayProcessDB record);

    List<WmEcontractDelayProcessDB> queryExpiredRecordWithLimit(@Param("expireTime") int expireTime, @Param("bizType")String bizType, @Param("limit")int limit);

}