package com.sankuai.meituan.waimai.customer.settle.service;

import static com.sankuai.meituan.waimai.thrift.customer.constant.WmSettleConstant.SEARCH_PARAM_IS_OPEN_WALLET_MAP;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.base.Joiner;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.primitives.Ints;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.payment.bankinfo.thrift.idl.BankInfoService;
import com.sankuai.meituan.gis.remote.service.thrift.TAdminDivisionService;
import com.sankuai.meituan.gis.remote.vo.thrift.TAdAPIResponse;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.dao.WmContractVersionDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.ProduceNotifyService;
import com.sankuai.meituan.waimai.customer.service.common.SettleProduceNotifyService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBlackWhiteListService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.settle.constant.WmSettleAuditConstant;
import com.sankuai.meituan.waimai.customer.settle.constant.WmSettlePushDaXiangConstant;
import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettlePaperSignAuditDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleProtocolAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleProtocolDBMapper;
import com.sankuai.meituan.waimai.customer.settle.daoadapter.WmSettleDBMapperAdapter;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettlePaperSignAuditDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleProtocolDB;
import com.sankuai.meituan.waimai.customer.settle.grey.WmSettleNvWaGrayService;
import com.sankuai.meituan.waimai.customer.settle.service.adapter.MerchantSettleQueryAdapter;
import com.sankuai.meituan.waimai.customer.settle.service.adapter.PaymentAgentAdapter;
import com.sankuai.meituan.waimai.customer.settle.service.paycenter.WmBankCardValidationService;
import com.sankuai.meituan.waimai.customer.util.ListUtil;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.WmContractVersionUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.trans.TransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiSwitchModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractConstant;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.SettleCardType;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmSettleConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo.OpType;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmC1WalletLog;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleModifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettlePageBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettlePageQueryParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettlePoiPageBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleProtocol;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleProtocolAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleStatusBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskSettleContextBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditCommitObj;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditMsg;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiSettle;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmAuditApiService;
import com.sankuai.meituan.waimai.thrift.vo.PoiData;
import com.sankuai.meituan.waimai.thrift.vo.SettleData;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditSettleCommitData;
import com.sankuai.meituan.waimai.util.BeanDiffUtil;
import com.sankuai.meituan.waimai.util.StringUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nullable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

/**
 *
 */
@Service
public class WmSettleService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmSettleService.class);

    private final static ExecutorService handleService = new ExecutorServiceTraceWrapper( new ThreadPoolExecutor(10, 200,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue(1024), new ThreadPoolExecutor.AbortPolicy()));

    private static final Map<Integer,Byte> settleStatusMap = Maps.newHashMap();
    static{
        settleStatusMap.put(1,WmSettleConstant.SETTLE_SHANGDAN_STATUS_TO_COMPLETE);
        settleStatusMap.put(2,WmSettleConstant.SETTLE_SHANGDAN_STATUS_ON_STATGE);
        settleStatusMap.put(3,WmSettleConstant.SETTLE_SHANGDAN_STATUS_ON_STATGE);
        settleStatusMap.put(4,WmSettleConstant.SETTLE_SHANGDAN_STATUS_TO_CONFIRM);
        settleStatusMap.put(5,WmSettleConstant.SETTLE_SHANGDAN_STATUS_TO_CONFIRM);
        settleStatusMap.put(6,WmSettleConstant.SETTLE_SHANGDAN_STATUS_EFFECT);
        settleStatusMap.put(7,WmSettleConstant.SETTLE_SHANGDAN_STATUS_TO_AUDIT_REJECT);
        settleStatusMap.put(8,WmSettleConstant.SETTLE_SHANGDAN_STATUS_TO_AUDIT);
    }

    @Autowired
    private WmSettleDBMapper wmSettleDBMapper;
    @Autowired
    private WmSettleDBMapperAdapter wmSettleDBMapperAdapter;
    @Autowired
    private WmSettleAuditedDBMapper wmSettleAuditedDBMapper;
    @Autowired
    private WmPoiSettleDBMapper wmPoiSettleDBMapper;
    @Autowired
    private WmPoiSettleAuditedDBMapper wmPoiSettleAuditedDBMapper;
    @Autowired
    private WmPoiClient wmPoiClient;
    @Autowired
    private WmSettleCheckService wmSettleCheckService;
    @Autowired
    private WmBankCardValidationService wmBankCardValidationService;
    @Autowired
    private WmEcontractSignThriftService wmEcontractSignThriftService;
    @Autowired
    private WmSettlePdfCreateService wmSettlePdfCreateService;
    @Autowired
    private WmContractVersionDBMapper wmContractVersionDBMapper;
    @Autowired
    private WmSettleLogService wmSettleLogService;
    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;
    @Autowired
    private SettleProduceNotifyService settleProduceNotifyService;
    @Autowired
    private WmCustomerService wmCustomerService;
    @Autowired
    private WmAuditApiService.Iface wmAuditApiService;
    @Autowired
    private WmSettleProtocolDBMapper wmSettleProtocolDBMapper;
    @Autowired
    private WmSettleProtocolAuditedDBMapper wmSettleProtocolAuditedDBMapper;
    @Autowired
    private WmSettlePaperSignAuditDBMapper wmSettlePaperSignAuditDBMapper;
    @Autowired
    private TAdminDivisionService.Iface tadminDivisionService;
    @Autowired
    private BankInfoService.Iface bankInfoService;
    @Autowired
    private WmCustomerBlackWhiteListService wmCustomerBlackWhiteListService;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;
    @Autowired
    private WmSettleSwitchService wmSettleSwitchService;
    @Autowired
    private WmSettleNvWaGrayService wmSettleNvWaGrayService;
    @Autowired
    private PaymentAgentAdapter paymentAgentAdapter;

    @Autowired
    private MerchantSettleQueryAdapter merchantSettleQueryAdapter;


  /**
   * 客户id-查-关联结算基本信息
   */
  public List<WmSettle> getWmSettleBasicListByWmCustomerId(int wmCustomerId)
      throws WmCustomerException {
    List<WmSettleDB> wmSettleDBList = wmSettleDBMapper.getWmSettleByCustomerId(wmCustomerId);
    if (CollectionUtils.isEmpty(wmSettleDBList)) {
      return Lists.newArrayList();
    }
    List<WmSettle> wmSettleList = WmSettleTransUtil.WmSettleListDB2Thrift(wmSettleDBList);
    return wmSettleList;
  }

    /**
     * 客户id-查-关联结算基本信息-查主库
     */
    public List<WmSettle> getWmSettleBasicListByWmCustomerIdMaster(int wmCustomerId)
            throws WmCustomerException {
        List<WmSettleDB> wmSettleDBList = wmSettleDBMapper.getWmSettleByCustomerIdMaster(wmCustomerId);
        if (CollectionUtils.isEmpty(wmSettleDBList)) {
            return Lists.newArrayList();
        }
        List<WmSettle> wmSettleList = WmSettleTransUtil.WmSettleListDB2Thrift(wmSettleDBList);
        return wmSettleList;
    }

    /**
     * 客户id-查-关联生效结算基本信息
     */
    public List<WmSettleAudited> getWmSettleAuditedBasicListByWmCustomerId(int wmCustomerId)
            throws WmCustomerException {
        List<WmSettleAuditedDB> wmSettleAuditedDBList = wmSettleAuditedDBMapper
                .getByWmCustomerId(wmCustomerId);
        List<WmSettleAudited> wmSettleAuditedList = WmSettleTransUtil
                .wmSettleAuditedListDB2Thrift(wmSettleAuditedDBList);
        return wmSettleAuditedList;
    }

    /**
     * 客户id-查-关联线下结算（基本信息+关联门店信息）
     */
    public List<WmSettle> getWmSettleByWmCustomerId(int wmCustomerId,boolean withSwitchingData) throws WmCustomerException {
        List<SwitchPoiInfo> settleSwitchPoiInfoList = withSwitchingData ? wmSettleSwitchService.getSettleSwitchPoiInfoList(wmCustomerId) : null;
        List<WmSettle> wmSettleList = getWmSettleBasicListByWmCustomerId(wmCustomerId);
        return wrapWmSettleWithWmPoiIdList(wmSettleList, settleSwitchPoiInfoList);
    }

    public List<WmSettle> getWmSettleByWmCustomerId(int wmCustomerId,Map<Long,String> wmSettleSwitchInfo) throws WmCustomerException {
        if(MapUtils.isEmpty(wmSettleSwitchInfo)){
            return getWmSettleByWmCustomerId(wmCustomerId,false);
        }
        Map<WmPoiSwitchModuleEnum, String> moduleInfo = null;
        List<SwitchPoiInfo> settleSwitchPoiInfoList = Lists.newArrayList();
        for(Entry<Long,String> temp : wmSettleSwitchInfo.entrySet()){
            moduleInfo = Maps.newHashMap();
            moduleInfo.put(WmPoiSwitchModuleEnum.SETTLE,temp.getValue());
            settleSwitchPoiInfoList.add(SwitchPoiInfo.builder().wmPoiId(temp.getKey()).moduleInfo(moduleInfo).build());
        }
        List<WmSettle> wmSettleList = getWmSettleBasicListByWmCustomerId(wmCustomerId);
        return wrapWmSettleWithWmPoiIdList(wmSettleList, settleSwitchPoiInfoList);
    }

    /**
     * 客户id-查-关联线下结算（基本信息+关联门店信息）- 查主库
     */
    public List<WmSettle> getWmSettleByWmCustomerIdMaster(int wmCustomerId) throws WmCustomerException {
        List<SwitchPoiInfo> settleSwitchPoiInfoList = wmSettleSwitchService.getSettleSwitchPoiInfoList(wmCustomerId);
        List<WmSettle> wmSettleList = getWmSettleBasicListByWmCustomerIdMaster(wmCustomerId);
        return wrapWmSettleWithWmPoiIdListMaster(wmSettleList,settleSwitchPoiInfoList);
    }

    public Set<Integer> getOfflineWmPoiIdSetByWmCustomerId(int wmCustomerId)
            throws WmCustomerException {
        return Sets.newHashSet(getOfflineWmPoiIdListByWmCustomerId(wmCustomerId));
    }

    /**
     * 客户id-查-线下结算关联门店集合
     */
    public List<Integer> getOfflineWmPoiIdListByWmCustomerId(int wmCustomerId)
            throws WmCustomerException {
        List<WmSettle> wmSettleList = getWmSettleByWmCustomerId(wmCustomerId,true);
        return getWmPoiIdListFromWmSettleList(wmSettleList);
    }

    public List<Integer> getWmPoiIdListFromWmSettleList(List<WmSettle> wmSettleList) {
        List<Integer> result = Lists.newArrayList();
        for (WmSettle temp : wmSettleList) {
            if (CollectionUtils.isNotEmpty(temp.getWmPoiIdList())) {
                result.addAll(temp.getWmPoiIdList());
            }
        }
        return result;
    }

    public List<Integer> getWmPoiIdListFromWmSettleAuditedList(List<WmSettleAudited> wmSettleAuditedList) {
        List<Integer> result = Lists.newArrayList();
        for (WmSettleAudited temp : wmSettleAuditedList) {
            if (CollectionUtils.isNotEmpty(temp.getWmPoiIdList())) {
                result.addAll(temp.getWmPoiIdList());
            }
        }
        return result;
    }

    /**
     * 客户id-查-关联线上结算（基本信息+关联门店信息）
     * @param wmCustomerId
     * @param withSwitchingData 数据结果是否包含切换中的数据
     * @return
     * @throws WmCustomerException
     */
    public List<WmSettleAudited> getWmSettleAuditedByWmCustomerId(int wmCustomerId,boolean withSwitchingData) throws WmCustomerException {
        List<SwitchPoiInfo> settleSwitchPoiInfoList = withSwitchingData ? wmSettleSwitchService.getSettleSwitchPoiInfoList(wmCustomerId) : null;
        return wrapWmSettleAuditedWithWmPoiIdList(getWmSettleAuditedBasicListByWmCustomerId(wmCustomerId), settleSwitchPoiInfoList);
    }

    public List<WmSettleAudited> getWmSettleAuditedByWmCustomerId(int wmCustomerId,Map<Long,String> wmSettleSwitchInfo) throws WmCustomerException {
        if(MapUtils.isEmpty(wmSettleSwitchInfo)){
            return getWmSettleAuditedByWmCustomerId(wmCustomerId,false);
        }
        Map<WmPoiSwitchModuleEnum, String> moduleInfo = null;
        List<SwitchPoiInfo> settleSwitchPoiInfoList = Lists.newArrayList();
        for(Entry<Long,String> temp : wmSettleSwitchInfo.entrySet()){
            moduleInfo = Maps.newHashMap();
            moduleInfo.put(WmPoiSwitchModuleEnum.SETTLE,temp.getValue());
            settleSwitchPoiInfoList.add(SwitchPoiInfo.builder().wmPoiId(temp.getKey()).moduleInfo(moduleInfo).build());
        }
        return wrapWmSettleAuditedWithWmPoiIdList(getWmSettleAuditedBasicListByWmCustomerId(wmCustomerId), settleSwitchPoiInfoList);
    }

    private Set<Integer> getOnlineWmPoiIdSetByWmCustomerId(int wmCustomerId) throws WmCustomerException {
        List<WmSettleAudited> wmSettleList = getWmSettleAuditedByWmCustomerId(wmCustomerId,true);
        Set<Integer> result = Sets.newHashSet();
        for (WmSettleAudited temp : wmSettleList) {
            if (CollectionUtils.isNotEmpty(temp.getWmPoiIdList())) {
                result.addAll(temp.getWmPoiIdList());
            }
        }
        return result;
    }

    private List<WmSettleAudited> wrapWmSettleAuditedWithWmPoiIdList(
            List<WmSettleAudited> wmSettleAuditedList,List<SwitchPoiInfo> switchPoiInfoList)
            throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmSettleAuditedList)) {
            return Lists.newArrayList();
        }

        if (wmSettleAuditedList.size() == 1) {
            List<Integer> wmPoiIdList = null;
            int customerId = 0;
            for (WmSettleAudited wmSettleAudited : wmSettleAuditedList) {
                if (!ConfigUtilAdapter
                        .getBoolean("settle_compatible_switch_contract_open", false)) {
                    wmPoiIdList = wmPoiSettleAuditedDBMapper
                            .getPoiIdsBySettleId(wmSettleAudited.getWm_settle_id());
                } else {
                    customerId = wmSettleAudited.getWmCustomerId();
                    wmPoiIdList = wmPoiSettleAuditedDBMapper
                            .getPoiIdsBySettleIdAndWmContractId(wmSettleAudited.getWm_settle_id(),
                                    customerId);
                }
                wmSettleAudited.setWmPoiIdList(wmPoiIdList);
//                wmSettleAudited.setWm_poi_ids(wrapWmPoiInfo(wmPoiIdList));
            }
        } else {
            Map<Integer, WmSettleAudited> wmSettleAuditedMap = WmSettleTransUtil
                    .transWmSettleAuditedList2Map(
                            wmSettleAuditedList);

            List<Integer> wmSettleIdList = Lists.newArrayList(
                    Lists.transform(wmSettleAuditedList, new Function<WmSettleAudited, Integer>() {
                        @Nullable
                        @Override
                        public Integer apply(@Nullable WmSettleAudited input) {
                            return input.getWm_settle_id();
                        }
                    }));

            List<WmPoiSettleAuditedDB> wmPoiSettleAuditedList = null;
            if (!ConfigUtilAdapter
                    .getBoolean("settle_compatible_switch_contract_open", false)) {
                wmPoiSettleAuditedList = wmPoiSettleAuditedDBMapper
                        .getBySettleIdList(wmSettleIdList);
            } else {
                wmPoiSettleAuditedList = wmPoiSettleAuditedDBMapper
                        .getBySettleIdListAndWmContractId(wmSettleIdList,
                                wmSettleAuditedList.get(0).getWmCustomerId());
            }
            for (WmPoiSettleAuditedDB wmPoiSettleAuditedDB : wmPoiSettleAuditedList) {
                WmSettleAudited wmSettleAudited = wmSettleAuditedMap
                        .get(wmPoiSettleAuditedDB.getWm_settle_id());
                if (wmSettleAudited == null) {
                    continue;
                }

                List<Integer> wmPoiIdList = wmSettleAudited.getWmPoiIdList();
                if (wmPoiIdList == null) {
                    wmPoiIdList = Lists.newArrayList();
                }
                wmPoiIdList.add(wmPoiSettleAuditedDB.getWm_poi_id());
                wmSettleAudited.setWmPoiIdList(wmPoiIdList);
            }
//            wrapWmSettleAuditedListWithPoiName(wmSettleAuditedList);
        }
        WmSettleTransUtil.aggregateWmSettleOnline(wmSettleAuditedList,switchPoiInfoList);
        wrapWmSettleAuditedListWithPoiName(wmSettleAuditedList);
        return excludeWithoutWmPoiIdByAuditedList(wmSettleAuditedList);
    }

    private List<WmSettleAudited> wrapWmSettleAuditedListWithPoiName(
            List<WmSettleAudited> wmSettleList) throws WmCustomerException {
        List<Integer> wmPoiIdList = Lists.newArrayList();
        for (WmSettleAudited wmSettle : wmSettleList) {
            if (wmSettle.getWmPoiIdList() == null) {
                wmSettle.setWmPoiIdList(new ArrayList<Integer>());
            } else {
                wmPoiIdList.addAll(wmSettle.getWmPoiIdList());
            }
        }
        List<WmPoiDomain> wmPoiDomainList = wmPoiClient.pageGetWmPoiByWmPoiIdList(
                TransUtil.IntegerList2Long(wmPoiIdList));
        Map<Integer, WmPoiDomain> wmPoiDomainMap = WmPoiClient.listToMap(wmPoiDomainList);
        for (WmSettleAudited wmSettle : wmSettleList) {
            List<String> wmPoiInfoList = Lists.newArrayList();
            for (Integer wmPoiId : wmSettle.getWmPoiIdList()) {
                String wmPoiInfos =
                        wmPoiDomainMap.get(wmPoiId).getName() + ":" + wmPoiDomainMap.get(wmPoiId).getWmPoiId();
                CollectionUtils.addIgnoreNull(wmPoiInfoList, wmPoiInfos);
            }
            wmSettle.setWm_poi_ids(StringUtils.join(wmPoiInfoList, ","));
        }
        return wmSettleList;
    }

    /**
     * 组装结算关联门店+组装切换中心数据+过滤未关联门店的结算
     * @param wmSettleList
     * @param switchPoiInfoList
     * @return
     * @throws WmCustomerException
     */
    private List<WmSettle> wrapWmSettleWithWmPoiIdList(List<WmSettle> wmSettleList,List<SwitchPoiInfo> switchPoiInfoList)
            throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return Lists.newArrayList();
        }

        //组装门店信息
        if (wmSettleList.size() == 1) {
            List<Integer> wmPoiIdList = Lists.newArrayList();
            int wmCustomerId = 0;
            for (WmSettle wmSettle : wmSettleList) {

                if (!ConfigUtilAdapter
                        .getBoolean("settle_compatible_switch_contract_open", false)) {
                    wmPoiIdList = wmPoiSettleDBMapper.getWmPoiIdListBySettleId(wmSettle.getId());
                } else {
                    wmCustomerId = wmSettle.getWmCustomerId();
                    wmPoiIdList = wmPoiSettleDBMapper.getWmPoiIdListBySettleIdAndWmContractId(wmSettle.getId(),wmCustomerId);
                }
                wmSettle.setWmPoiIdList(wmPoiIdList);
                wmSettle.setWm_poi_ids(wrapWmPoiInfo(wmPoiIdList));
            }

            wmSettleList = WmSettleTransUtil.aggregateWmSettleOffline(wmSettleList,switchPoiInfoList);

        } else {
            StopWatch sw = new StopWatch();
            sw.start("查询结算信息");

            List<Integer> wmSettleIdList = Lists
                    .newArrayList(Lists.transform(wmSettleList, new Function<WmSettle, Integer>() {
                        @Nullable
                        @Override
                        public Integer apply(@Nullable WmSettle input) {
                            return input.getId();
                        }
                    }));

            Map<Integer, WmSettle> wmSettleIdAndWmSettleMap = WmSettleTransUtil
                    .transWmSettleList2Map(wmSettleList);


            List<WmPoiSettleDB> wmPoiSettleDBList = Lists.newArrayList();
            if (!ConfigUtilAdapter
                    .getBoolean("settle_compatible_switch_contract_open", false)) {
                wmPoiSettleDBList = wmPoiSettleDBMapper
                        .getByWmSettleIdList(wmSettleIdList);
            } else {
                int wmCustomerId = wmSettleList.get(0).getWmCustomerId();
                wmPoiSettleDBList = wmPoiSettleDBMapper
                        .getByWmSettleIdListAndWmContractId(wmSettleIdList, wmCustomerId);
            }
            sw.stop();
            sw.start("过滤门店信息");
            for (WmPoiSettleDB wmPoiSettleDB : wmPoiSettleDBList) {
                WmSettle wmSettle = wmSettleIdAndWmSettleMap.get(wmPoiSettleDB.getWm_settle_id());
                if (wmSettle == null) {
                    continue;
                }

                List<Integer> wmPoiIdList = wmSettle.getWmPoiIdList();
                if (wmPoiIdList == null) {
                    wmPoiIdList = Lists.newArrayList();
                }
                wmPoiIdList.add(wmPoiSettleDB.getWm_poi_id());
                wmSettle.setWmPoiIdList(wmPoiIdList);
            }
            sw.stop();
            sw.start("结算数据组装");

            wmSettleList = WmSettleTransUtil.aggregateWmSettleOffline(wmSettleList,switchPoiInfoList);

            wrapWmSettleListWithPoiName(wmSettleList);
            sw.stop();
            LOGGER.info(sw.prettyPrint());
        }
        return excludeWithoutWmPoiIdList(wmSettleList);
    }

    private List<WmSettle> wrapWmSettleWithWmPoiIdListMaster(List<WmSettle> wmSettleList,List<SwitchPoiInfo> switchPoiInfoList)
            throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return Lists.newArrayList();
        }

        //组装门店信息
        if (wmSettleList.size() == 1) {
            List<Integer> wmPoiIdList = Lists.newArrayList();
            int customerId = 0;
            for (WmSettle wmSettle : wmSettleList) {
                if (!ConfigUtilAdapter
                        .getBoolean("settle_compatible_switch_contract_open", false)){
                    wmPoiIdList = wmPoiSettleDBMapper.getWmPoiIdListBySettleIdMaster(wmSettle.getId());
                }else{
                    customerId = wmSettle.getWmCustomerId();
                    wmPoiIdList = wmPoiSettleDBMapper.getWmPoiIdListBySettleIdAndWmContractIdMaster(wmSettle.getId(),customerId);
                }
                wmSettle.setWmPoiIdList(wmPoiIdList);
                wmSettle.setWm_poi_ids(wrapWmPoiInfo(wmPoiIdList));
            }
            wmSettleList = WmSettleTransUtil.aggregateWmSettleOffline(wmSettleList,switchPoiInfoList);
        } else {
            StopWatch sw = new StopWatch();
            sw.start("查询结算信息");

            List<Integer> wmSettleIdList = Lists
                    .newArrayList(Lists.transform(wmSettleList, new Function<WmSettle, Integer>() {
                        @Nullable
                        @Override
                        public Integer apply(@Nullable WmSettle input) {
                            return input.getId();
                        }
                    }));

            Map<Integer, WmSettle> wmSettleIdAndWmSettleMap = WmSettleTransUtil
                    .transWmSettleList2Map(wmSettleList);

            List<WmPoiSettleDB> wmPoiSettleDBList = Lists.newArrayList();
            if (!ConfigUtilAdapter
                    .getBoolean("settle_compatible_switch_contract_open", false)) {
                wmPoiSettleDBList = wmPoiSettleDBMapper
                        .getByWmSettleIdListMaster(wmSettleIdList);
            } else {
                int customerId = wmSettleList.get(0).getWmCustomerId();
                wmPoiSettleDBList = wmPoiSettleDBMapper
                        .getByWmSettleIdListAndWmContractIdMaster(wmSettleIdList, customerId);
            }
            sw.stop();
            sw.start("过滤门店信息");
            for (WmPoiSettleDB wmPoiSettleDB : wmPoiSettleDBList) {
                WmSettle wmSettle = wmSettleIdAndWmSettleMap.get(wmPoiSettleDB.getWm_settle_id());
                if (wmSettle == null) {
                    continue;
                }

                List<Integer> wmPoiIdList = wmSettle.getWmPoiIdList();
                if (wmPoiIdList == null) {
                    wmPoiIdList = Lists.newArrayList();
                }
                wmPoiIdList.add(wmPoiSettleDB.getWm_poi_id());
                wmSettle.setWmPoiIdList(wmPoiIdList);
            }
            sw.stop();
            sw.start("结算数据组装");

            wmSettleList = WmSettleTransUtil.aggregateWmSettleOffline(wmSettleList,switchPoiInfoList);

            wrapWmSettleListWithPoiName(wmSettleList);
            sw.stop();
            LOGGER.info(sw.prettyPrint());
        }
        return excludeWithoutWmPoiIdList(wmSettleList);
    }


    private List<WmSettle> wrapWmSettleListWithPoiName(List<WmSettle> wmSettleList)
            throws WmCustomerException {
        List<Integer> wmPoiIdList = Lists.newArrayList();
        for (WmSettle wmSettle : wmSettleList) {
            if (wmSettle.getWmPoiIdList() == null) {
                wmSettle.setWmPoiIdList(new ArrayList<Integer>());
            } else {
                wmPoiIdList.addAll(wmSettle.getWmPoiIdList());
            }
        }
        List<WmPoiDomain> wmPoiDomainList = wmPoiClient.pageGetWmPoiByWmPoiIdList(
                TransUtil.IntegerList2Long(wmPoiIdList));
        Map<Integer, WmPoiDomain> wmPoiDomainMap = WmPoiClient.listToMap(wmPoiDomainList);
        String wmPoiInfos = "";
        List<String> wmPoiInfoList = null;
        for (WmSettle wmSettle : wmSettleList) {
            wmPoiInfoList = Lists.newArrayList();
            for (Integer wmPoiId : wmSettle.getWmPoiIdList()) {
                if (wmPoiDomainMap.containsKey(wmPoiId)) {
                    wmPoiInfos =
                            wmPoiDomainMap.get(wmPoiId).getName() + ":" + wmPoiDomainMap.get(wmPoiId)
                                    .getWmPoiId();
                    CollectionUtils.addIgnoreNull(wmPoiInfoList, wmPoiInfos);
                }
            }
            wmSettle.setWm_poi_ids(StringUtils.join(wmPoiInfoList, ","));
        }
        return wmSettleList;
    }

    private boolean canSave(byte status) {
        return status != WmSettleConstant.SETTLE_STATUS_TO_CONFIRM
                && status != WmSettleConstant.SETTLE_STATUS_TO_AUDIT
                && status != WmSettleConstant.SETTLE_STATUS_WAIT_SIGN;
    }

    private boolean canDelete(byte status) {
        return status != WmSettleConstant.SETTLE_STATUS_TO_CONFIRM;
    }

    /**
     * 保存&提交单个结算 仅支持电子签约客户
     */
    public int saveWmSettleAndCommit(int wmCustomerId, WmSettle wmSettle, int opUid, String opUname,
                                     boolean isBrandBD, boolean isEffective, boolean isCommit) throws WmCustomerException {
        WmSettleModifyBo wmSettleModifyBo = new WmSettleModifyBo();
        wmSettleModifyBo.setWmCustomerId(wmCustomerId);
        wmSettleModifyBo.setWmSettle(wmSettle);
        wmSettleModifyBo.setBrandBD(isBrandBD);
        wmSettleModifyBo.setEffective(isEffective);
        wmSettleModifyBo.setCommit(isCommit);
        wmSettleModifyBo.setSupplementalUrl("");
        wmSettleModifyBo.setQdbUrl("");
        return saveWmSettleAndCommit(wmSettleModifyBo, opUid, opUname);
    }

    /**
     * 保存&提交单个结算  纸质签约客户增加补充协议
     */
    public int saveWmSettleAndCommit(WmSettleModifyBo wmSettleModifyBo, int opUid, String opUname) throws WmCustomerException {
        wmSettleModifyBo.setPackWay(MoreObjects.firstNonNull(wmSettleModifyBo.getPackWay(), SignPackWay.DO_SIGN.getCode()));
        int wmCustomerId = wmSettleModifyBo.getWmCustomerId();
        WmSettle wmSettle = wmSettleModifyBo.getWmSettle();
        String supplementalUrl = wmSettleModifyBo.getSupplementalUrl();
        String qdbUrl = wmSettleModifyBo.getQdbUrl();
        //输入参数校验
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户id");
        AssertUtil.assertObjectNotNull(wmSettle, "结算信息");
        AssertUtil.assertIntegerMoreThan0(opUid, "操作人id");
        AssertUtil.assertStringNotEmpty(opUname, "操作人");
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerId);
        if(wmCustomerDB == null){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "客户不存在");
        }

        //校验客户是否有确认中或者审核中的结算，如有，禁止保存
        List<WmSettleDB> wmSettleDBList = wmSettleDBMapper.getWmSettleByCustomerId(wmCustomerId);
        for (WmSettleDB wmSettleDB : wmSettleDBList) {
            if (wmSettleDB.getStatus() == WmSettleConstant.SETTLE_STATUS_TO_CONFIRM
                    || wmSettleDB.getStatus() == WmSettleConstant.SETTLE_STATUS_TO_AUDIT
                    || wmSettleDB.getStatus() == WmSettleConstant.SETTLE_STATUS_WAIT_SIGN) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不允许保存");
            }
        }
        //纸质签约客户校验 补充协议不能为空 若有开钱包的结算 钱袋宝协议也不能为空
        if (wmSettleModifyBo.isCommit() && wmCustomerDB.getSignMode() == CustomerSignMode.PAPER.getCode()) {
            WmSettleProtocolDB wmSettleProtocolDB;
            WmSettleProtocolAuditedDB wmSettleProtocolAuditedDB;
            if(StringUtils.isBlank(supplementalUrl)){
                if(wmSettleModifyBo.isEffective()){
                    wmSettleProtocolAuditedDB = wmSettleProtocolAuditedDBMapper.selectByCustomer(wmCustomerId);
                    if(wmSettleProtocolAuditedDB == null){
                        AssertUtil.assertStringNotEmpty("", "补充协议");
                    }
                    supplementalUrl = wmSettleProtocolAuditedDB.getSupplementalUrl();
                    AssertUtil.assertStringNotEmpty(supplementalUrl, "补充协议");
                }else{
                    wmSettleProtocolDB = wmSettleProtocolDBMapper.selectByCustomer(wmCustomerId);
                    if(wmSettleProtocolDB == null){
                        AssertUtil.assertStringNotEmpty("", "补充协议");
                    }
                    supplementalUrl = wmSettleProtocolDB.getSupplementalUrl();
                    AssertUtil.assertStringNotEmpty(supplementalUrl, "补充协议");
                }
            }
            boolean isWallte = false;
            if (wmSettle.getCard_type() == SettleCardType.WALLET.getCode()) {
                isWallte = true;
            }
            if(!isWallte) {
                for (WmSettleDB wmSettleDB : wmSettleDBList) {
                    if (wmSettleDB.getCard_type() == SettleCardType.WALLET.getCode()) {
                        isWallte = true;
                        break;
                    }
                }
            }
            if(isWallte && StringUtils.isBlank(qdbUrl)) {
                if (wmSettleModifyBo.isEffective()) {
                    wmSettleProtocolAuditedDB = wmSettleProtocolAuditedDBMapper.selectByCustomer(wmCustomerId);
                    if(wmSettleProtocolAuditedDB == null){
                        AssertUtil.assertStringNotEmpty("", "钱袋宝协议");
                    }
                    qdbUrl = wmSettleProtocolAuditedDB.getQdbUrl();
                } else {
                    wmSettleProtocolDB = wmSettleProtocolDBMapper.selectByCustomer(wmCustomerId);
                    if(wmSettleProtocolDB == null){
                        AssertUtil.assertStringNotEmpty("", "钱袋宝协议");
                    }
                    qdbUrl = wmSettleProtocolDB.getQdbUrl();
                }
                AssertUtil.assertStringNotEmpty(qdbUrl, "钱袋宝协议");
            }
        }
        int wmSettleId = wmSettle.getId();
        //校验状态
        if (wmSettleId > 0) {
            WmSettleDB wmSettleDBOld = wmSettleDBMapper.getById(wmSettleId);
            //无线下结算数据
            if (wmSettleDBOld == null) {
                //处理线下表数据被删除的场景
                if(wmSettleAuditedDBMapper.getSettleAuditedBySettleId(wmSettleId) == null){
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "历史结算信息不存在");
                }
            }
            //有线下结算数据-校验当前状态是否可保存
            else{
                if (!canSave(wmSettleDBOld.getStatus())) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不允许保存");
                }
            }
        }

        if (!wmSettleCheckService.checkWmSettleByName(wmSettle)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "结算名称重复");
        }
        if (!wmSettleCheckService
                .checkSettleType(Lists.newArrayList(wmSettle), opUid, wmSettleModifyBo.isBrandBD(), false)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "结算校验失败");
        }

        //财务负责人手机号校验
        if (StringUtils.isNotEmpty(wmSettle.getParty_a_finance_phone())) {
            wmSettleCheckService.checkPhoneNum(Lists.newArrayList(wmSettle.getParty_a_finance_phone()));
        }

        List<Integer> wmPoiCheckList = genWmSettlePoiCheckList(wmSettleModifyBo.isEffective(), wmCustomerId, wmSettle.getId());

        wmSettleCheckService.checkWmPoiIdListInSettleAndPushDX(wmCustomerId,
                wmSettle.getWmPoiIdList(),
                WmSettlePushDaXiangConstant.errorType.BATCH_ADD,
                WmSettlePushDaXiangConstant.errorModule.SETTLE_POI,
                opUid, wmPoiCheckList
        );

        //保存结算
        wmSettle.setWm_contract_id(wmCustomerId);
        wmSettle.setAcc_cardno(wmSettle.getAcc_cardno().replace(" ", ""));
        WmSettleDB store = null;
        List<Integer> storeWmpoiIdList = Lists.newArrayList();
        if (wmSettle.getId() > 0) {
            store = wmSettleDBMapper.getById(wmSettle.getId());
            storeWmpoiIdList = wmPoiSettleDBMapper.getWmPoiIdListBySettleId(wmSettle.getId());
        }
        if (wmSettleModifyBo.isEffective()) {
            throw new WmCustomerException(WmContractErrorCodeConstant.AUTHORIZING, "暂不支持正在生效页数据保存");
        }
        wmSettle.setStatus(WmSettleConstant.SETTLE_STATUS_TO_COMPLETE);
        wmSettleId = saveOrUpdateSettleInfo(wmSettle, opUid, opUname);

        //状态变更-待提交
        updateStatusByWmCustomerId(wmCustomerId, WmSettleConstant.SETTLE_STATUS_TO_COMPLETE);

        wmSettleLogService.addWmSettleLog(wmCustomerId, wmSettle, store == null ? null
                        : WmSettleTransUtil.wmSettleDB2Thrift(store).setWmPoiIdList(storeWmpoiIdList)
                , opUid,
                opUname);

        //新建-修改插入日志
//    if(wmSettle.getId() == 0){
        insertFillInProduceLog(wmSettle.getWmPoiIdList(), opUid, opUname);
//    }

        if (wmSettleModifyBo.isCommit()) {
            if(wmCustomerDB != null) {
                if (wmCustomerDB.getSignMode() == CustomerSignMode.PAPER.getCode()) {
                    commitSettleInfoToAudit(wmCustomerId, wmCustomerDB.getCustomerName(), opUid, opUname, supplementalUrl, qdbUrl);
                } else {
                    commitSettleInfo(wmCustomerId, wmSettleModifyBo.getPackWay(), opUid, opUname,0);
                }
            }
        }
        return wmSettleId;
    }

    public void insertFillInProduceLog(List<Integer> wmPoiIdList, int opUid, String opUname) {
        if (CollectionUtils.isNotEmpty(wmPoiIdList)) {
            for (Integer wmPoiId : wmPoiIdList) {
                settleProduceNotifyService
                        .notifyInsertWithRemark(wmPoiId, ProduceNotifyService.SETTLEMENT, opUid, opUname,
                                "");
            }
        }
    }

    private void insertConfirmingProduceLog(List<Integer> wmPoiIdList, int opUid, String opUname) {
        if (CollectionUtils.isNotEmpty(wmPoiIdList)) {
            for (Integer wmPoiId : wmPoiIdList) {
                settleProduceNotifyService
                        .notifyToConfirmingWithRemark(wmPoiId, ProduceNotifyService.SETTLEMENT, opUid, opUname,
                                "");
            }
        }
    }

    public void insertConfirmingProduceLogAsy(List<Integer> wmPoiIdList, int opUid, String opUname) {
        handleService.submit(() -> insertConfirmingProduceLog(wmPoiIdList, opUid, opUname));
    }

    /**
     * 更新单个结算
     */
    public int updateWmSettle(WmSettle wmSettle, int opUid, String opUname)
            throws WmCustomerException {
        LOGGER
                .info("updateWmSettle wmSettle = {}, opUid = {}, opUname = {}", JSON.toJSONString(wmSettle),
                        opUid, opUname);
        if (wmSettle == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "保存结算不能为空");
        }
        if (wmSettle.getId() <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "新增不允许包含结算id");
        }
        if (opUid <= 0 || StringUtils.isEmpty(opUname)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "操作人不能为空");
        }

        WmSettleDB wmSettleDB = WmSettleTransUtil.wmSettleThrift2DB(wmSettle);
        wmSettleDB.setValid((byte) 1);//需要更新的结算信息都会valid=1
        LOGGER.info("updateWmSettle wmSettleDB = {}", JSON.toJSONString(wmSettleDB));
        wmSettleDBMapperAdapter.updateByPrimaryKeySelectiveWithValid(wmSettleDB);
        return wmSettleDB.getId();
    }

    private boolean insertWmPoiSettleDB(List<WmPoiSettle> wmPoiSettleList, int opUid,
                                        String opUname) {
        if (CollectionUtils.isEmpty(wmPoiSettleList)) {
            return true;
        }

        LOGGER.info("insertWmPoiSettle wmPoiSettleList = {}", JSON.toJSONString(wmPoiSettleList));
        for (WmPoiSettle wmPoiSettle : wmPoiSettleList) {
            WmPoiSettleDB wmPoiSettleDB = new WmPoiSettleDB();
            wmPoiSettleDB.setWm_contract_id(wmPoiSettle.getWmContractId());
            wmPoiSettleDB.setWm_settle_id(wmPoiSettle.getWmSettleId());
            wmPoiSettleDB.setWm_poi_id(wmPoiSettle.getWmPoiId());
            wmPoiSettleDBMapper.insertSelective(wmPoiSettleDB);
        }
        return true;
    }


    private boolean updateWmPoiSettle(WmSettle wmSettle, int opUid, String opUname)
            throws WmCustomerException {
        List<Integer>
                storeList = wmPoiSettleDBMapper.getWmPoiIdListBySettleId(wmSettle.getId());

        //新增结算关联门店
        List<Integer> addList = BeanDiffUtil.genAddList(wmSettle.getWmPoiIdList(), storeList);
        LOGGER.info("updateWmPoiSettle addList = {}", JSON.toJSONString(addList));
        List<WmPoiSettle> addSettleList = WmSettleTransUtil
                .transWmPoiIdList2PoiSettle(addList, wmSettle.getWm_contract_id(), wmSettle.getId());
        insertWmPoiSettleDB(addSettleList, opUid, opUname);

        //修改结算关联门店
        List<Integer> commonList = BeanDiffUtil.genCommonList(wmSettle.getWmPoiIdList(), storeList);
        LOGGER.info("updateWmPoiSettle commonList = {}", JSON.toJSONString(commonList));
        List<WmPoiSettle> commonSettleList = WmSettleTransUtil
                .transWmPoiIdList2PoiSettle(commonList, wmSettle.getWm_contract_id(), wmSettle.getId());

        updateWmPoiSettleDB(commonSettleList, opUid, opUname);

        //删除结算关联门店
        List<Integer> deleteList = BeanDiffUtil.genDeleteList(wmSettle.getWmPoiIdList(), storeList);
        LOGGER.info("updateWmPoiSettle deleteList = {}", JSON.toJSONString(deleteList));
        deleteWmPoiSettleDB(wmSettle.getId(), deleteList, opUid, opUname);
        return true;
    }

    private boolean deleteWmPoiSettleDB(int wmSettleId, List<Integer> wmPoiIdList, int opUid,
                                        String opUname) {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return true;
        }
        wmPoiSettleDBMapper.deleteByWmSettleIdAndWmPoiIdList(wmSettleId, wmPoiIdList);
        return true;
    }

    private boolean updateWmPoiSettleDB(List<WmPoiSettle> wmPoiSettleList, int opUid,
                                        String opUname) {
        if (CollectionUtils.isEmpty(wmPoiSettleList)) {
            return true;
        }

        for (WmPoiSettle wmPoiSettle : wmPoiSettleList) {
            WmPoiSettleDB wmPoiSettleDB = new WmPoiSettleDB();
            wmPoiSettleDB.setWm_contract_id(wmPoiSettle.getWmContractId());
            wmPoiSettleDB.setWm_poi_id(wmPoiSettle.getWmPoiId());
            wmPoiSettleDB.setWm_settle_id(wmPoiSettle.getWmSettleId());
            wmPoiSettleDBMapper.updateByContractAndPoiId(wmPoiSettleDB);
        }
        return true;
    }


    private int saveOrUpdateSettleInfo(WmSettle wmSettle, int opUid, String opUname)
            throws WmCustomerException {
        LOGGER.info("saveOrUpdateSettleInfo wmSettle = {}", JSON.toJSONString(wmSettle));
        int res;
        if (wmSettle.getId() > 0) {
            res = updateWmSettle(wmSettle, opUid, opUname);
            updateWmPoiSettle(wmSettle, opUid, opUname);
        } else {
            WmSettleDB wmSettleDB = WmSettleTransUtil.wmSettleThrift2DB(wmSettle);
            wmSettleDBMapperAdapter.insertSelective(wmSettleDB);
            res = wmSettleDB.getId();
            wmSettle.setId(wmSettleDB.getId());
            List<WmPoiSettle> addSettleList = WmSettleTransUtil
                    .transWmPoiIdList2PoiSettle(wmSettle.getWmPoiIdList(), wmSettle.getWm_contract_id(),
                            wmSettle.getId());
            insertWmPoiSettleDB(addSettleList, opUid, opUname);
        }
        return res;
    }

    public BooleanResult applyWmSettleConfirmFlow(int wmCustomerId,int opUid,long manualBatchId) throws WmCustomerException {
        Pair<Boolean,EcontractTaskApplyBo> applyBoPair = wmSettlePdfCreateService.genSettleTaskApplyBo(wmCustomerId);
        LongResult applyResult = null;
        try {
            LOGGER.info("#applyWmSettleConfirmFlow#applyTask,input={}",JSONObject.toJSONString(applyBoPair.getRight()));
            applyBoPair.getRight().setCommitUid(opUid);
            applyBoPair.getRight().setManualBatchId(manualBatchId);
            applyResult = wmEcontractSignThriftService.applyTask(applyBoPair.getRight());
            WmContractVersionDB versionDB = new WmContractVersionDB();
            versionDB.setWm_contract_id(wmCustomerId);
            //supportWallet
            if(applyBoPair.getLeft()){
                versionDB.setType(CustomerContractConstant.MOON_SETTLE_VERSION_TYPE);
            }else{
                versionDB.setType(CustomerContractConstant.NORMAL_SETTLE_VERSION_TYPE);
            }
            versionDB.setVersion_number(WmContractVersionUtil.genVersionNumForSettle(wmCustomerId));
            versionDB.setStatus(WmSettleConstant.SETTLE_STATUS_TO_COMPLETE);
            versionDB.setTransaction_id(applyResult.getValue() + "");
            wmContractVersionDBMapper.insertSelective(versionDB);
        } catch (Exception ex) {
            LOGGER.error("applyTask异常", ex);
            return new BooleanResult(false);
        }
        return new BooleanResult(true);
    }

    /**
     * 客户id-提交客户关联结算信息
     */
    @Deprecated
    public void commitSettleInfo(int wmCustomerId, int signPackWay, int opUid, String opUname,long manualBatchId)
            throws WmCustomerException {
        //提交校验
        List<Long> wmPoiIdList = commitSettlePreCheckAndGetSettlePoiIdList(wmCustomerId);

        if (signPackWay == SignPackWay.NONE.getCode()) {
            BooleanResult canApplyManualTask = wmEcontractSignBzService
                    .canApplyManualTask(buildManualTaskApplyBo(wmCustomerId, opUid));
            //判断是否可发起手动打包，如果可以的话就提示BD
            if (canApplyManualTask.isRes()) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_PACKWAY_CHOOSE, "");
            }
        }
        if (signPackWay == SignPackWay.WAIT_HAND_PACK.getCode()) {
            //待发起签约
            startSignForHandPack(wmCustomerId, opUid, opUname, wmPoiIdList);
            return;
        }
        //申请任务成功-变更状态
        startSign(wmCustomerId, opUid, opUname, wmPoiIdList,manualBatchId);
    }

    private void startSign(int wmCustomerId, int opUid, String opUname, List<Long> wmPoiIdList,long manualBatchId) throws WmCustomerException {
        updateStatusByWmCustomerId(wmCustomerId, WmSettleConstant.SETTLE_STATUS_TO_CONFIRM);
        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "提交确认");

        //申请电子合同平台任务
        wmBankCardValidationService.validateCardByCustomerId(wmCustomerId);
        BooleanResult booleanResult = applyWmSettleConfirmFlow(wmCustomerId, opUid,manualBatchId);
        if (!booleanResult.isRes()) {
            return;
        }

        insertConfirmingProduceLog(ObjectUtil.longList2IntList(wmPoiIdList), opUid, opUname);

        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "发起确认流程");
    }

    private void startSignForHandPack(int wmCustomerId, int opUid, String opUname, List<Long> wmPoiIdList) throws WmCustomerException {
        updateStatusByWmCustomerId(wmCustomerId, WmSettleConstant.SETTLE_STATUS_WAIT_SIGN);
        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "提交待发起签约流程");
        //发起手动打包过程暂不验卡
//        wmBankCardValidationService.validateCardByCustomerId(wmCustomerId);

        ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(wmCustomerId, opUid);
        ManualTaskSettleContextBo manualTaskSettleContextBo = new ManualTaskSettleContextBo();
        manualTaskSettleContextBo.setWmPoiList(wmPoiIdList);
        manualTaskApplyBo.setApplyContext(JSONObject.toJSONString(manualTaskSettleContextBo));
        wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);

        insertConfirmingProduceLog(ObjectUtil.longList2IntList(wmPoiIdList), opUid, opUname);

        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "发起待发起签约流程");
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(wmCustomerId)
                .applyTypeEnum(EcontractTaskApplyTypeEnum.SETTLE)
                .bizId(wmCustomerId)
                .build();
    }

    public List<Long> commitSettlePreCheckAndGetSettlePoiIdList(int wmCustomerId) throws WmCustomerException {
        Set<Long> wmCustomerPoiSet = Sets.newHashSet(wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(wmCustomerId));
        List<Long> wmPoiIdList = ObjectUtil.intList2LongList(getOfflineWmPoiIdListByWmCustomerId(wmCustomerId));

        //校验结算门店附属于客户关联门店
        if (!wmCustomerPoiSet.containsAll(wmPoiIdList)) {
            List<Long> copyWmPoiIdList = Lists.newArrayList(wmPoiIdList);
            copyWmPoiIdList.removeAll(wmCustomerPoiSet);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,
                    "提交失败。门店id " + StringUtils.join(copyWmPoiIdList, ",") + "不属于该客户");
        }

        //校验单店-结算类型是否可选银行卡
        if (wmCustomerPoiSet.size() == 1) {
            checkCanUseBankCardType(wmCustomerId, wmCustomerPoiSet.iterator().next().intValue());
        }

        //校验多结算钱包
        //该校验关闭，允许同一客户多个结算开钱包状态不一致
        if (!ConfigUtilAdapter.getBoolean("close_checkSettleIsWalletOrNot",false) && !wmSettleCheckService
                .checkSettleIsWalletOrNot(getWmSettleByWmCustomerId(wmCustomerId,true), wmCustomerId)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.SETTLE_WALLET_CONSISTENCY_ERROR, "请确认客户内所有门店都开钱包");
        }
        return wmPoiIdList;
    }

    public void updateStatusByWmCustomerId(int wmCustomerId, byte status) {
        wmSettleDBMapper.updateStatusByWmCustomerId(wmCustomerId, status);
    }

    public int updateWmSettleStatusByWmCustomerId(int wmCustomerId, byte status) {
        return wmSettleDBMapper.updateStatusByWmCustomerId(wmCustomerId, status);
    }

    /**
     * 结算id-读-获取银行卡信息
     */
    public String getCardNo(int wmSettleId, boolean isEffective) throws WmCustomerException {
        if (wmSettleId <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "结算id为空");
        }

        if (!isEffective) {//若为生效，则查询线下表
            WmSettleDB wmSettleDB = wmSettleDBMapper.getById(wmSettleId);
            if (wmSettleDB != null) {
                return wmSettleDB.getAcc_cardno();
            }
        } else {
            WmSettleAuditedDB wmSettleAuditedDB = wmSettleAuditedDBMapper
                    .getSettleAuditedBySettleId(wmSettleId);
            if (wmSettleAuditedDB != null) {
                return wmSettleAuditedDB.getAcc_cardno();
            }
        }
        return "";
    }

    /**
     * 结算id-删除结算
     */
    public void deleteWmSettle(int wmCustomerId, int wmSettleId, int opUid, String opUname,
                               boolean isEffective) throws WmCustomerException {
        if (wmCustomerId <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "合同id不能为空");
        }
        if (wmSettleId <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "结算id不能为空");
        }
        if (opUid <= 0 || StringUtils.isEmpty(opUname)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "操作人id不能为空");
        }

        WmSettleDB wmSettleDB = wmSettleDBMapper.getById(wmSettleId);
        if(wmSettleDB == null){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "该结算已删除，请刷新页面");
        }
        if (!canDelete(wmSettleDB.getStatus())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不允许删除");
        }
        List<Integer> wmPoiIdList = wmPoiSettleDBMapper.getWmPoiIdListBySettleId(wmSettleId);
        if (isEffective) {
            throw new WmCustomerException(WmContractErrorCodeConstant.AUTHORIZING, "暂不支持正在生效页数据删除");
        }

        WmSettle wmSettle = getWmSettleByWmSettleId(wmSettleId);
        List<Long> switchingWmPoiIdListFromOldWmCustomerId = wmSettleSwitchService.getSwitchingWmPoiIdListFromOldWmCustomerId(wmCustomerId);
        if (wmSettle != null && CollectionUtils.isNotEmpty(wmSettle.getWmPoiIdList())) {
            List<Long> commonList = ListUtil.genCommonList(switchingWmPoiIdListFromOldWmCustomerId,
                    ObjectUtil.intList2LongList(wmSettle.getWmPoiIdList()));
            if (!org.springframework.util.CollectionUtils.isEmpty(commonList)) {
                String errorMsg = "提交失败;门店id" + Joiner.on(",").join(wmPoiIdList) + "即将切换至其他客户，结算信息无法修改。如需修改，请先取消切换客户任务。";
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_SWITCH_CUSTOMER, errorMsg);
            }
        }

        deleteWmSettleDB(Lists.newArrayList(wmSettleId), opUid, opUname);
        deleteWmPoiSettleDB(wmSettleId, wmPoiIdList, opUid, opUname);
        wmSettleSwitchService.deleteOfflineSettleInSwitchCentre(wmCustomerId,Lists.newArrayList(wmSettleId),opUid,opUname);
        wmSettleLogService
                .addWmSettleLog(wmCustomerId, null, WmSettleTransUtil.wmSettleDB2Thrift(wmSettleDB), opUid,
                        opUname);
    }

    private void deleteWmSettleDB(ArrayList<Integer> wmSettleIdList, int opUid, String opUname)
            throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmSettleIdList)) {
            return;
        }
        if (opUid <= 0 || StringUtils.isEmpty(opUname)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "操作人不能为空");
        }
        wmSettleDBMapper.batchDeleteByWmSettleIdList(wmSettleIdList);
    }


    /**
     * 查询同客户下其他结算关联的门店
     */
    public List<Integer> genWmSettlePoiCheckList(boolean isEffective, int wmCustomerId,
                                                  int wmSettleId) throws WmCustomerException {
        List<Integer> wmPoiCheckList = Lists.newArrayList();
            if (!isEffective) {
                List<WmSettle> wmSettleList = getWmSettleByWmCustomerId(wmCustomerId,true);
                for (WmSettle wmSettleTemp : wmSettleList) {
                    if (wmSettleTemp.getId() == wmSettleId) {
                        continue;
                    }
                    wmPoiCheckList.addAll(wmSettleTemp.getWmPoiIdList());
                }
            } else {
                List<WmSettleAudited> wmSettleAuditedList = getWmSettleAuditedByWmCustomerId(wmCustomerId,true);
                for (WmSettleAudited wmSettleAuditedTemp : wmSettleAuditedList) {
                    if (wmSettleAuditedTemp.getWm_settle_id() == wmSettleId) {
                        continue;
                    }
                    wmPoiCheckList.addAll(wmSettleAuditedTemp.getWmPoiIdList());
                }
        }
        return wmPoiCheckList;
    }


    private String wrapWmPoiInfo(List<Integer> wmPoiIdList)
            throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return "";
        }

        List<WmPoiDomain> wmPoiDomainList = wmPoiClient
                .pageGetWmPoiByWmPoiIdList(TransUtil.IntegerList2Long(wmPoiIdList));
        List<String> wmPoiInfoList = Lists.newArrayList();
        for (WmPoiDomain wmPoiDomain : wmPoiDomainList) {
            String wmPoiInfos = wmPoiDomain.getName() + ":" + wmPoiDomain.getWmPoiId();
            CollectionUtils.addIgnoreNull(wmPoiInfoList, wmPoiInfos);
        }
        return StringUtils.join(wmPoiInfoList, ",");
    }

    /**
     * 过滤没有门店的结算信息
     */
    private List<WmSettle> excludeWithoutWmPoiIdList(List<WmSettle> wmSettleList) {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return Lists.newArrayList();
        }

        List<WmSettle> excludeWmSettleList = Lists.newArrayList();
        for (WmSettle wmSettle : wmSettleList) {
            if (CollectionUtils.isNotEmpty(wmSettle.getWmPoiIdList())) {
                excludeWmSettleList.add(wmSettle);
            }
        }
        return excludeWmSettleList;
    }

    private List<WmSettleAudited> excludeWithoutWmPoiIdByAuditedList(
            List<WmSettleAudited> wmSettleAuditedList) {
        if (CollectionUtils.isEmpty(wmSettleAuditedList)) {
            return Lists.newArrayList();
        }

        List<WmSettleAudited> excludeWmSettleList = Lists.newArrayList();
        for (WmSettleAudited wmSettleAudited : wmSettleAuditedList) {
            if (CollectionUtils.isNotEmpty(wmSettleAudited.getWmPoiIdList())) {
                excludeWmSettleList.add(wmSettleAudited);
            }
        }
        return excludeWmSettleList;
    }


    /**
     * 客户id-读-分页获取结算可关联的门店
     */
    public WmSettlePoiPageBo getAllWmPoiIdListForSettle(int wmCustomerId, String wmPoiIdOrName,
                                                        int pageNum, int pageSize, boolean isEffective) throws WmCustomerException {
        if (wmCustomerId <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "客户id不能为空");
        }

        pageSize = Math.min(pageSize, WmContractConstant.MAX_PAGESIZE);

        //查询未关联结算的商家
        List<Integer> wmPoiIdList = getUnSettleByWmCustomerIdByEffect(wmCustomerId, isEffective);
        //分页
        return pageGetWmPoiByWmPoiIdListAndQuery(wmPoiIdList, wmPoiIdOrName, pageNum, pageSize);
    }

    private Pair<List<Integer>, List<Integer>> getWmPoiIdByWmCustomerId(int wmCustomerId,
                                                                        boolean isEffective) {
        Pair<List<Integer>, List<Integer>> result = null;
        if (!isEffective) {
            List<Integer> wmSettleIdList = wmSettleDBMapper.getWmSettleIdListByCustomerId(wmCustomerId);
            if (CollectionUtils.isNotEmpty(wmSettleIdList)) {
                result = Pair
                        .of(wmSettleIdList, wmPoiSettleDBMapper.getWmPoiIdListByWmSettleIdList(wmSettleIdList));
            }
        } else {
            List<Integer> wmSettleIdList = wmSettleAuditedDBMapper
                    .getWmSettleIdListByWmCustomerId(wmCustomerId);
            if (CollectionUtils.isNotEmpty(wmSettleIdList)) {
                result = Pair.of(wmSettleIdList,
                        wmPoiSettleAuditedDBMapper.getWmPoiIdListByWmSettleIdList(wmSettleIdList));
            }
        }
        return result;
    }

    private List<WmSettle> getWmSettleListBySettleIdAndWmPoiId(List<Integer> wmSettleIdList,
                                                               List<Integer> wmPoiIdList, boolean isEffective) {
        List<WmSettle> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmSettleIdList) || CollectionUtils.isEmpty(wmPoiIdList)) {
            return result;
        }
        List<Integer> wmSettleIdMatchList = Lists.newArrayList();
        if (!isEffective) {
            wmSettleIdMatchList = wmPoiSettleDBMapper
                    .getWmSettleIdListBySettleAndPoi(wmSettleIdList, wmPoiIdList);
            if (CollectionUtils.isNotEmpty(wmSettleIdMatchList)) {
                result = WmSettleTransUtil.WmSettleListDB2Thrift(
                        wmSettleDBMapper.getWmSettleListByWmSettleIdListWithSort(wmSettleIdMatchList));
            }
        } else {
            wmSettleIdMatchList = wmPoiSettleAuditedDBMapper.
                    getWmSettleIdListBySettleAndPoi(wmSettleIdList, wmPoiIdList);
            if (CollectionUtils.isNotEmpty(wmSettleIdMatchList)) {
                result = WmSettleTransUtil.transWmSettleAuditedList2WmSettleList(
                        WmSettleTransUtil.wmSettleAuditedListDB2Thrift(
                                wmSettleAuditedDBMapper
                                        .getWmSettleListByWmSettleIdListWithSort(wmSettleIdMatchList)));
            }
        }
        return result;
    }

    private List<WmSettle> getWmSettleListBySettleIdAndWmPoiId(List<Integer> wmSettleIdList,
                                                               List<Integer> wmPoiIdList, boolean isEffective,Integer customerId,List<SwitchPoiInfo> switchPoiInfoList) {

        List<WmSettle> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmSettleIdList) || CollectionUtils.isEmpty(wmPoiIdList)) {
            return result;
        }
        List<Integer> wmSettleIdMatchList = Lists.newArrayList();
        List<Integer> wmSettleIdMatchListFromSwitchCentre = Lists.newArrayList();
        Set<Integer> wmSettleIdMatchSet = Sets.newHashSet();
        if (!isEffective) {
            wmSettleIdMatchList = wmPoiSettleDBMapper
                    .getWmSettleIdListBySettleAndPoi(wmSettleIdList, wmPoiIdList);
            wmSettleIdMatchListFromSwitchCentre = WmSettleTransUtil.getMatchedWmPoiSettleFromSwitchCentre(wmPoiIdList,switchPoiInfoList,isEffective);
            wmSettleIdMatchSet.addAll(wmSettleIdMatchList);
            wmSettleIdMatchSet.addAll(wmSettleIdMatchListFromSwitchCentre);
            if (CollectionUtils.isNotEmpty(wmSettleIdMatchSet)) {
                result = WmSettleTransUtil.WmSettleListDB2Thrift(
                        wmSettleDBMapper.queryWmSettleListByWmSettleIdListWithSort(Lists.newArrayList(wmSettleIdMatchSet),customerId));
            }
        } else {
            wmSettleIdMatchList = wmPoiSettleAuditedDBMapper.
                    getWmSettleIdListBySettleAndPoi(wmSettleIdList, wmPoiIdList);
            wmSettleIdMatchListFromSwitchCentre = WmSettleTransUtil.getMatchedWmPoiSettleFromSwitchCentre(wmPoiIdList,switchPoiInfoList,isEffective);
            wmSettleIdMatchSet.addAll(wmSettleIdMatchList);
            wmSettleIdMatchSet.addAll(wmSettleIdMatchListFromSwitchCentre);
            if (CollectionUtils.isNotEmpty(wmSettleIdMatchSet)) {
                result = WmSettleTransUtil.transWmSettleAuditedList2WmSettleList(
                        WmSettleTransUtil.wmSettleAuditedListDB2Thrift(
                                wmSettleAuditedDBMapper
                                        .queryWmSettleListByWmSettleIdListWithSort(Lists.newArrayList(wmSettleIdMatchSet),customerId)));
            }
        }
        return result;
    }

    private WmSettlePageBo pageGetWmSettleWithoutSearch(int wmCustomerId, int pageSize, int pageNum,
                                                        boolean isEffective) throws WmCustomerException {
        StopWatch sw = new StopWatch();
        sw.start("查询结算列表");
        List<WmSettle> returnList = Lists.newArrayList();
        int count = 0;
        List<SwitchPoiInfo> switchPoiInfoList = wmSettleSwitchService
                .getSettleSwitchPoiInfoList(wmCustomerId);

        //查询结算
        if (!isEffective) {
            List<WmSettleDB> wmSettleDBList = wmSettleDBMapper.getWmSettleByCustomerId(wmCustomerId);
            returnList = WmSettleTransUtil.WmSettleListDB2Thrift(wmSettleDBList);
            //过滤没有绑定门店的结算id
            returnList = wrapWmSettleWithWmPoiIdList(returnList,switchPoiInfoList);
            count = returnList.size();
            returnList = PageUtil.paging(returnList, pageNum, pageSize);
        } else {
            List<WmSettleAuditedDB> wmSettleAuditedDBList = wmSettleAuditedDBMapper
                    .getByWmCustomerId(wmCustomerId);

            returnList =
                    WmSettleTransUtil.transWmSettleAuditedList2WmSettleList
                            (WmSettleTransUtil.wmSettleAuditedListDB2Thrift(wmSettleAuditedDBList));

            //过滤没有绑定门店的结算id
            returnList =
                    WmSettleTransUtil.transWmSettleAuditedList2WmSettleList(
                            wrapWmSettleAuditedWithWmPoiIdList(
                                    WmSettleTransUtil.transWmSettleList2WmSettleAuditedList(returnList),switchPoiInfoList));

            count = returnList.size();
            returnList = PageUtil
                    .paging(returnList, pageNum, pageSize);
        }

        //填充开钱包完整信息
        for(WmSettle wmSettle:returnList){
            Integer maxId = wmSettleAuditedDBMapper.getLatestedLogId(0, wmSettle.getId());
            if(maxId!=null) {
                WmC1WalletLog wmC1WalletLog = wmSettleAuditedDBMapper.getLatestedLog(maxId);
                if(wmC1WalletLog!=null) {
                    wmSettle.setWm_wallet_id(wmC1WalletLog.getWm_wallet_id());
                    wmSettle.setLogin_name(wmC1WalletLog.getLogin_name());
                    wmSettle.setOpen_time(wmC1WalletLog.getOpen_time());
                    wmSettle.setWallet_open_status(wmC1WalletLog.getWallet_open_status());
                    wmSettle.setWallet_open_reason(wmC1WalletLog.getWallet_open_reason());
                    wmSettle.setRealname_status(wmC1WalletLog.getRealname_status());
                    wmSettle.setRealname_reason(wmC1WalletLog.getRealname_reason());
                    wmSettle.setCard_binding_status(wmC1WalletLog.getCard_binding_status());
                    wmSettle.setCard_binding_reason(wmC1WalletLog.getCard_binding_reason());
                }
            }
        }

        sw.stop();
        sw.start("一分钱打款");
        // 一分钱打款验卡
        returnList = wmBankCardValidationService.batchQueryAndSet(returnList);
        // 隐去银行卡号的部分数字
        // 仅当敏感信息开关关闭时 使用老逻辑
        if (!MccConfig.encryptSensitiveWords()) {
            returnList = WmSettleTransUtil.encryptWmSettleList(returnList);
        }
        sw.stop();
        LOGGER.info(sw.prettyPrint());

        WmSettlePageBo pageBo = new WmSettlePageBo();
        pageBo.setWmSettleList(returnList);
        pageBo.setCount(count);
        return pageBo;
    }

    private WmSettlePoiPageBo pageGetWmPoiByWmPoiIdListAndQuery(List<Integer> wmPoiIdList,
                                                                String wmPoiIdOrName, int pageNum, int pageSize) throws WmCustomerException {
        WmSettlePoiPageBo pageBo = new WmSettlePoiPageBo();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return pageBo;
        }
        //1.查询列表  注意:由于需要查询结果的列表,因此没有分页查询
        List<Integer> searchList;
        //查询条件为空,自行分页
        if (StringUtil.isBlank(wmPoiIdOrName)) {
            searchList = wmPoiIdList;
            //查询条件为数字,根据商家id搜索
        } else if (StringUtil.isNumeric(wmPoiIdOrName)) {
            LOGGER.info("pageGetWmPoiByWmPoiIdListAndQuery 查询条件为:{}", wmPoiIdOrName);
            List<Integer> commonList = Lists.newArrayList(wmPoiIdList);
            commonList.retainAll(Lists.newArrayList(Ints.tryParse(wmPoiIdOrName)));
            if (CollectionUtils.isEmpty(commonList)) {
                return pageBo;
            }
            searchList = wmPoiClient.getWmPoiIdByNameOrId("", commonList);
            //既不为空，也不是数字，则按照门店名称搜索
        } else {
            LOGGER.info("pageGetWmPoiByWmPoiIdListAndQuery 查询条件为商家名称:{}", wmPoiIdOrName);
            searchList = wmPoiClient.getWmPoiIdByNameOrId(wmPoiIdOrName, wmPoiIdList);
        }

        //2.分页
        List<Integer> pageList = PageUtil.paging(searchList, pageNum, pageSize);

        //3.分页后列表转为WmPoi列表(获取门店名称)
        List<WmPoiDomain> wmPoiDomainList = wmPoiClient
                .pageGetWmPoiByWmPoiIdList(TransUtil.IntegerList2Long(pageList));

        pageBo.setCount(searchList.size());
        pageBo.setPoiInfoList(WmSettleTransUtil.transWmPoiList2WmSettlePoiInfoList(wmPoiDomainList));
        LOGGER.info("pageGetWmPoiByWmPoiIdListAndQuery pageBo = {}", JSON.toJSONString(pageBo));
        return pageBo;
    }

    private List<Integer> getUnSettleByWmCustomerIdByEffect(int wmCustomerId, boolean isEffective)
            throws WmCustomerException {

        List<Integer> wmCustomerPoiList = Lists.newArrayList();
        Set<Integer> wmPoiIdForSettleSet;
        wmCustomerPoiList = ObjectUtil.longList2IntList(wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerId));

        List<Long> switchingWmPoiIdList = wmSettleSwitchService
                .getSwitchingWmPoiIdList(wmCustomerId,true);
        wmCustomerPoiList.addAll(ObjectUtil.longList2IntList(switchingWmPoiIdList));

        if (isEffective) {
            wmPoiIdForSettleSet = getOnlineWmPoiIdSetByWmCustomerId(wmCustomerId);
        } else {
            wmPoiIdForSettleSet = getOfflineWmPoiIdSetByWmCustomerId(wmCustomerId);
        }

        wmCustomerPoiList.removeAll(wmPoiIdForSettleSet);
        return wmCustomerPoiList;
    }

    /**
     * 批量关联结算关联门店
     */
    public List<Integer> getInsertWmPoiIdListByWmCustomerIdAndWmPoiIds(int wmCustomerId,
                                                                       int wmSettleId,
                                                                       String wmPoiIds, int opUid, String opUname,
                                                                       boolean isSuperSettleManager, boolean isEffective) throws WmCustomerException {
        if (wmCustomerId <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "合同id不能为空");
        }
        if (StringUtils.isBlank(wmPoiIds)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "门店id不能为空");
        }
        if (opUid <= 0 || StringUtils.isBlank(opUname)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "操作人不允许为空");
        }

        List<Integer> wmPoiIdList = TransUtil.String2IntList(wmPoiIds, "\n");
        //校验输入1~50
        wmSettleCheckService.checkInputWmPoiIdSize(wmPoiIdList);
        //校验输入均为合同的商家
        wmSettleCheckService
                .checkWmPoiIdListInWmCustomerPoiAndPushDXWithEffect(wmCustomerId, wmPoiIdList, opUid,
                        WmSettlePushDaXiangConstant.errorType.BATCH_ADD, //批量关联
                        WmSettlePushDaXiangConstant.errorModule.SETTLE_POI, isEffective); //结算关联门店
        return wmPoiIdList;

    }

    /**
     * 批量删除结算门店
     */
    public List<Integer> getDeleteWmPoiIdListByWmCustomerIdAndWmPoiIds(int wmCustomerId,
                                                                       int wmSettleId, String wmPoiIds,
                                                                       int opUid, String opUname, boolean isSuperSettleManager,
                                                                       boolean isEffective) throws WmCustomerException {
        if (wmCustomerId <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "合同id不能为空");
        }
        if (StringUtils.isBlank(wmPoiIds)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "门店id不能为空");
        }
        if (opUid <= 0 || StringUtils.isBlank(opUname)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "操作人不允许为空");
        }

        List<Integer> wmPoiIdList = TransUtil.String2IntList(wmPoiIds, "\n");
        //校验输入1~50
        wmSettleCheckService.checkInputWmPoiIdSize(wmPoiIdList);
        //校验输入均为合同的商家
        wmSettleCheckService
                .checkWmPoiIdListInWmCustomerPoiAndPushDXWithEffect(wmCustomerId, wmPoiIdList, opUid,
                        WmSettlePushDaXiangConstant.errorType.BATCH_DELETE, //批量关联
                        WmSettlePushDaXiangConstant.errorModule.SETTLE_POI,
                        isEffective);
        //校验传入的门店包含于结算已关联的门店集合

        List<Integer> wmPoiCheckList = genWmSettlePoiCheckList(isEffective, wmCustomerId, wmSettleId);

        wmSettleCheckService.checkWmPoiIdListInSettleAndPushDX(wmCustomerId, wmPoiIdList,
                WmSettlePushDaXiangConstant.errorType.BATCH_DELETE,
                WmSettlePushDaXiangConstant.errorModule.SETTLE_POI,
                opUid, wmPoiCheckList);

        return wmPoiIdList;
    }

    /**
     * 结算id集合-读-获取线下结算基本信息
     */
    public List<WmSettle> getWmSettleBasicListByWmSettleIdList(List<Integer> wmSettleIdList) {
        return WmSettleTransUtil.WmSettleListDB2Thrift(
                wmSettleDBMapper.getWmSettleListByWmSettleIdListWithSort(wmSettleIdList));
    }

    /**
     * 结算id集合-读-获取线上结算基本信息
     */
    public List<WmSettleAudited> getWmSettleAuditedBasicListByWmSettleIdList(
            List<Integer> wmSettleIdList) {
        return WmSettleTransUtil.wmSettleAuditedListDB2Thrift(
                wmSettleAuditedDBMapper.getWmSettleListByWmSettleIdListWithSort(wmSettleIdList));
    }

    public WmC1WalletLog getWmC1WalletLogByWmSettleId(int wmSettleId){
        Integer maxId = wmSettleAuditedDBMapper.getLatestedLogId(0, wmSettleId);
        WmC1WalletLog wmC1WalletLog=null;
        if(maxId!=null) {
            wmC1WalletLog = wmSettleAuditedDBMapper.getLatestedLog(maxId);
        }
        return wmC1WalletLog;
//    wmSettle.setWm_wallet_id(wmC1WalletLog.getWm_wallet_id());
//    wmSettle.setLogin_name(wmC1WalletLog.getLogin_name());
//    wmSettle.setOpen_time(wmC1WalletLog.getOpen_time());
//    wmSettle.setWallet_open_status(wmC1WalletLog.getWallet_open_status());
//    wmSettle.setWallet_open_reason(wmC1WalletLog.getWallet_open_reason());
//    wmSettle.setRealname_status(wmC1WalletLog.getRealname_status());
//    wmSettle.setRealname_reason(wmC1WalletLog.getRealname_reason());
//    wmSettle.setCard_binding_status(wmC1WalletLog.getCard_binding_status());
//    wmSettle.setCard_binding_reason(wmC1WalletLog.getCard_binding_reason());
    }
    /**
     * 结算id-读-获取线下结算（基本信息+关联门店信息）
     */
    public WmSettle getWmSettleByWmSettleId(int wmSettleId) throws WmCustomerException {
        WmSettleDB wmSettleDB = wmSettleDBMapper.getById(wmSettleId);
        WmSettle wmSettle = WmSettleTransUtil.wmSettleDB2Thrift(wmSettleDB);
        if (wmSettle == null) {
            return null;
        }
        List<SwitchPoiInfo> settleSwitchPoiInfoList = wmSettleSwitchService.getSettleSwitchPoiInfoList(wmSettle.getWmCustomerId());
        List<WmSettle> wmSettleList = wrapWmSettleWithWmPoiIdList(Lists.newArrayList(wmSettle), settleSwitchPoiInfoList);
        return CollectionUtils.isEmpty(wmSettleList) ? null : wmSettleList.get(0);
    }

    /**
     * 结算id-读-获取线上结算（基本信息+关联门店信息）
     */
    public WmSettleAudited getWmSettleAuditedByWmSettleId(int wmSettleId) throws WmCustomerException {
        WmSettleAuditedDB wmSettleAuditedDB = wmSettleAuditedDBMapper.getSettleAuditedBySettleId(wmSettleId);
        WmSettleAudited wmSettleAudited = WmSettleTransUtil.wmSettleAuditedDB2Thrift(wmSettleAuditedDB);
        if (wmSettleAudited == null) {
            return null;
        }
        List<SwitchPoiInfo> settleSwitchPoiInfoList = wmSettleSwitchService.getSettleSwitchPoiInfoList(wmSettleAudited.getWmCustomerId());
        List<WmSettleAudited> wmSettleAuditedList = wrapWmSettleAuditedWithWmPoiIdList(Lists.newArrayList(wmSettleAudited), settleSwitchPoiInfoList);
        return CollectionUtils.isEmpty(wmSettleAuditedList) ? null : wmSettleAuditedList.get(0);
    }

    /**
     * 客户id-读-获取客户绑定结算的整体状态
     */
    public byte getSettleStatusByWmCustomerId(int wmCustomerId) throws WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户id");
        List<WmSettle> wmSettleList = getWmSettleBasicListByWmCustomerId(wmCustomerId);
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return WmSettleConstant.SETTLE_STATUS_TO_COMPLETE;
        }
        return wmSettleList.get(0).getStatus();
    }

    /**
     * 结算id-读-线上结算基本信息
     */
    public WmSettleAudited getWmSettleAuditedBasicByWmSettleId(int wmSettleId) {
        WmSettleAuditedDB wmSettleAuditedDB = wmSettleAuditedDBMapper
                .getSettleAuditedBySettleId(wmSettleId);
        WmSettleAudited result = WmSettleTransUtil.wmSettleAuditedDB2Thrift(wmSettleAuditedDB);
        return result;
    }

    /**
     * 结算id-读-线上结算基本信息-主库
     */
    public WmSettleAudited getWmSettleAuditedBasicByWmSettleIdMaster(int wmSettleId) {
        WmSettleAuditedDB wmSettleAuditedDB = wmSettleAuditedDBMapper
                .getBySettleIdMaster(wmSettleId);
        WmSettleAudited result = WmSettleTransUtil.wmSettleAuditedDB2Thrift(wmSettleAuditedDB);
        return result;
    }

    /**
     * 结算id-读-线下结算基本信息
     */
    public WmSettle getWmSettleBasicByWmSettleId(int wmSettleId) {
        WmSettleDB wmSettleDB = wmSettleDBMapper.getById(wmSettleId);
        WmSettle result = WmSettleTransUtil.wmSettleDB2Thrift(wmSettleDB);
        return result;
    }

    /**
     * 查询门店结算状态,存在批量调用（大连锁客户）场景
     * @param wmPoiId
     * @return
     */
    public WmSettleStatusBo getWmSettleStatusBoByWmPoiIdV2(int wmPoiId) throws WmCustomerException, TException {
        WmSettleStatusBo result = new WmSettleStatusBo();
        //默认待录入
        byte status = WmSettleConstant.SETTLE_SHANGDAN_STATUS_TO_COMPLETE;
        boolean hasEffective = false;
        //获取门店当前关联的客户ID
        Integer wmCustomerId = wmCustomerService.selectWmCustomerIdByWmPoiId((long) wmPoiId);
        if(wmCustomerId != null){
            //门店线上结算ID
            List<Integer> wmSettleAuditedIdList = wmPoiSettleAuditedDBMapper.getWmSettleIdListByWmPoiIdAndWmContractId(wmPoiId,wmCustomerId);
            hasEffective = CollectionUtils.isNotEmpty(wmSettleAuditedIdList);
            if(hasEffective){
                status = WmSettleConstant.SETTLE_SHANGDAN_STATUS_EFFECT;
            }

            //金融侧接管的结算状态查询
            if(wmSettleNvWaGrayService.queryGreyResultByWmPoiId(wmPoiId)){
                int settleStatus = paymentAgentAdapter.getSettleStatus(wmCustomerId, wmPoiId);
                status = settleStatusMap.getOrDefault(settleStatus,WmSettleConstant.SETTLE_SHANGDAN_STATUS_TO_COMPLETE);
            }
            //外卖侧维护结算状态查询
            else{
                //门店线下结算ID
                List<Integer> wmSettleIdList = wmPoiSettleDBMapper.getWmSettleIdListByWmPoiIdAndWmContractId(wmPoiId,wmCustomerId);
                if(wmSettleIdList.size() > 1){
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店关联多个结算");
                }
                //存在线下结算状态
                if (CollectionUtils.isNotEmpty(wmSettleIdList)){
                    int wmSettleId = wmSettleIdList.get(0);
                    Byte statusObj = wmSettleDBMapper.getWmSettleStatusByWmSettleId(wmSettleId);
                    if (statusObj != null) {
                        //状态为初始状态,且有生效数据-属于清洗的历史数据-状态设置为已生效
                        if (statusObj == WmSettleConstant.SETTLE_STATUS_INIT) {
                            status = hasEffective ? WmSettleConstant.SETTLE_SHANGDAN_STATUS_EFFECT
                                    : WmSettleConstant.SETTLE_SHANGDAN_STATUS_ON_STATGE;
                        } else {
                            status = statusObj;
                        }
                    }
                }
            }
        }

        result.setStatus(status);
        result.setHasEffective(hasEffective);
        return result;
    }

    /**
     * 根据门店id获取关联结算的上单状态
     */
    public WmSettleStatusBo getWmSettleStatusBoByWmPoiId(int wmPoiId) throws WmCustomerException {
        WmSettleStatusBo result = new WmSettleStatusBo();

        List<Integer> wmSettleIdList = wmPoiSettleDBMapper.getWmSettleIdListByWmPoiId(wmPoiId);
        List<Integer> wmSettleAuditedIdList = wmPoiSettleAuditedDBMapper
                .getWmSettleIdListByWmPoiId(wmPoiId);
        byte status = WmSettleConstant.SETTLE_SHANGDAN_STATUS_TO_COMPLETE;//默认待录入

        boolean hasEffective = CollectionUtils.isNotEmpty(wmSettleAuditedIdList);

        if (CollectionUtils.isNotEmpty(wmSettleIdList)) {

            int wmSettleId = wmSettleIdList.get(0);

            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId((long) wmPoiId);
            if (wmCustomerDB != null) {
                int wmCustomerId = wmCustomerDB.getId();
                //客户关联结算id集合
                List<Integer> wmSettleIdListByCustomerId = wmSettleDBMapper
                        .getWmSettleIdListByCustomerId(wmCustomerId);

                List<Integer> commonList = com.sankuai.meituan.waimai.customer.util.diff.BeanDiffUtil
                        .genCommonList(wmSettleIdListByCustomerId, wmSettleIdList);
                if (commonList.size() == 1) {
                    wmSettleId = commonList.get(0);
                } else {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店关联多个结算");
                }
            }

            Byte statusObj = wmSettleDBMapper.getWmSettleStatusByWmSettleId(wmSettleId);
            if (statusObj != null) {
                //状态为初始状态,且有生效数据-属于清洗的历史数据-状态设置为已生效
                if (statusObj == WmSettleConstant.SETTLE_STATUS_INIT) {
                    status = hasEffective ? WmSettleConstant.SETTLE_SHANGDAN_STATUS_EFFECT
                            : WmSettleConstant.SETTLE_SHANGDAN_STATUS_ON_STATGE;
                } else {
                    status = statusObj;
                }
            }
            //结算已生效，但线下结算删除的场景，状态设置为已生效
            else if (hasEffective) {
                status = WmSettleConstant.SETTLE_SHANGDAN_STATUS_EFFECT;
            }
        }
        //兼容门店未关联线下表
        else if (hasEffective) {
            status = WmSettleConstant.SETTLE_SHANGDAN_STATUS_EFFECT;
        }

        result.setStatus(status);
        result.setHasEffective(hasEffective);
        return result;
    }

    public WmSettle getWmSettleBasicByWmCustomerIdAndWmPoiId(int wmCustomerId, int wmPoiId) throws WmCustomerException {
        List<Integer> wmSettleIdList = wmSettleDBMapper.getWmSettleIdListByCustomerId(wmCustomerId);
        if (CollectionUtils.isEmpty(wmSettleIdList)) {
            return null;
        }
        List<Integer> matchWmSettleIdList = wmPoiSettleDBMapper.getWmSettleIdListBySettleAndPoi(wmSettleIdList,
                Lists.<Integer> newArrayList(wmPoiId));

//        Integer wmSettleIdFromSwitchCentre = wmSettleSwitchService.getWmSettleIdFromSwitchCentreByWmCustomerIdAndWmPoiId(wmCustomerId, wmPoiId,
//                false);
//        if (wmSettleIdFromSwitchCentre != null) {
//            matchWmSettleIdList.add(wmSettleIdFromSwitchCentre);
//        }
        int matchSize = matchWmSettleIdList.size();

        if (matchSize == 0) {
            return null;
        } else if (matchSize > 1) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店线下关联多个结算");
        }
        return WmSettleTransUtil.wmSettleDB2Thrift(wmSettleDBMapper.getById(matchWmSettleIdList.get(0)));
    }

    public WmSettleAudited getWmSettleAuditedBasicByWmCustomerIdAndWmPoiId(int wmCustomerId, int wmPoiId) throws WmCustomerException {
        List<Integer> wmSettleIdList = wmSettleAuditedDBMapper.getWmSettleIdListByWmCustomerId(wmCustomerId);
        if (CollectionUtils.isEmpty(wmSettleIdList)) {
            return null;
        }
        List<Integer> matchWmSettleIdList = wmPoiSettleAuditedDBMapper.getWmSettleIdListBySettleAndPoi(wmSettleIdList,
                Lists.newArrayList(wmPoiId));

//        Integer wmSettleIdFromSwitchCentre = wmSettleSwitchService.getWmSettleIdFromSwitchCentreByWmCustomerIdAndWmPoiId(wmCustomerId, wmPoiId, true);
//
//        if (wmSettleIdFromSwitchCentre != null) {
//            matchWmSettleIdList.add(wmSettleIdFromSwitchCentre);
//        }
        int matchSize = matchWmSettleIdList.size();

        if (matchSize == 0) {
            return null;
        } else if (matchSize > 1) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店线上关联多个结算");
        }
        return WmSettleTransUtil.wmSettleAuditedDB2Thrift(wmSettleAuditedDBMapper.getSettleAuditedBySettleId(matchWmSettleIdList.get(0)));
    }

    public void refreshWmSettle(boolean isEffective, int begin, int end) {
        //清洗线上表
        if (isEffective) {
            wmSettleAuditedDBMapper.updateWmCustomerIdForRefesh(begin, end);
        }
        //清洗线下表
        else {
            wmSettleDBMapper.updateWmCustomerIdForRefesh(begin, end);
        }
    }

    public List<WmSettle> assembleBankCardValidate(List<WmSettle> wmSettleList) {
        return wmBankCardValidationService.batchQueryAndSet(wmSettleList);
    }

    public List<WmSettleAudited> getWmSettleAuditedBasicListByWmCustomerIdMaster(int wmCustomerId) {
        List<WmSettleAuditedDB> wmSettleAuditedDBList = wmSettleAuditedDBMapper
                .getByWmCustomerIdMaster(wmCustomerId);
        List<WmSettleAudited> wmSettleAuditedList = WmSettleTransUtil
                .wmSettleAuditedListDB2Thrift(wmSettleAuditedDBList);
        return wmSettleAuditedList;
    }

    public BooleanResult resetWmSettleStatus(int wmCustomerId, byte status) {
        return new BooleanResult(wmSettleDBMapper.updateStatusByWmCustomerId(wmCustomerId, status) > 0);
    }

    public WmSettle getWmSettleByWmPoiId(long wmPoiId) throws WmCustomerException{
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if(wmCustomerDB == null){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"门店没有关联客户");
        }
        return getWmSettleBasicByWmCustomerIdAndWmPoiId(wmCustomerDB.getId(),(int)wmPoiId);
    }

    /**
     * 纸质签约提交审核
     */
    public void commitSettleInfoToAudit(int wmCustomerId, String wmCustomerName, int opUid, String
            opUname, String supplementalUrl, String qdbUrl)
            throws WmCustomerException {
        List<Long> wmPoiIdList = ObjectUtil.intList2LongList(getOfflineWmPoiIdListByWmCustomerId(wmCustomerId));

        Set<Long> wmCustomerPoiSet = Sets.newHashSet(wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(wmCustomerId));

        if (!wmCustomerPoiSet.containsAll(wmPoiIdList)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "存在未绑定该客户的门店");
        }

        List<WmSettle> wmSettleList = getWmSettleByWmCustomerId(wmCustomerId,true);
        if(CollectionUtils.isEmpty(wmSettleList)){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "没有结算信息");
        }
        if(StringUtils.isBlank(supplementalUrl)){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "补充协议不能为空");
        }
        //是否有结算开钱包
        boolean moonMode = false;
        for(WmSettle wmSettle : wmSettleList){
            if(wmSettle.getCard_type() == SettleCardType.WALLET.getCode()){
                moonMode = true;
            }
        }
        if (moonMode && StringUtils.isBlank(qdbUrl)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "有开钱包的结算，钱袋宝协议不能为空");
        }

        if(wmCustomerPoiSet.size() == 1){
            checkCanUseBankCardType(wmCustomerId,wmCustomerPoiSet.iterator().next().intValue());
        }

        if (StringUtils.isNotBlank(supplementalUrl) || StringUtils.isNotBlank(qdbUrl)) {
            LOGGER.info("#saveOrUpdateWmSettleProtocol,supplementalUrl={},qdbUrl{}", supplementalUrl, qdbUrl);
            saveOrUpdateWmSettleProtocol(wmCustomerId, supplementalUrl==null?"":supplementalUrl, qdbUrl==null?"":qdbUrl, opUid, opUname);
        }
        //提交审核-变更状态
        WmAuditSettleCommitData wmAuditSettleCommitData = buildWmAuditSettleCommitData(wmCustomerId, wmCustomerName, wmSettleList);

        String commitData = JSONObject.toJSONString(wmAuditSettleCommitData);

        WmSettlePaperSignAuditDB settlePaperSignAuditDB = buildCommitSignModeData(wmCustomerId, opUid, commitData);
        if (wmSettlePaperSignAuditDBMapper.insert(settlePaperSignAuditDB) == 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "提交审核异常，请稍后重试");
        }
        int biz_id = settlePaperSignAuditDB.getId();
        WmAuditCommitObj wmAuditCommitObj = new WmAuditCommitObj();
        wmAuditCommitObj.setBiz_id(settlePaperSignAuditDB.getId());
        wmAuditCommitObj.setBiz_type(WmAuditTaskBizTypeConstant.SETTLE);
        wmAuditCommitObj.setWm_poi_id(0);
        wmAuditCommitObj.setCustomer_id(wmCustomerId);
        wmAuditCommitObj.setSubmit_uid(opUid);
        wmAuditCommitObj.setData(commitData);
        WmAuditMsg wmAuditMsg = null;
        try {
            wmBankCardValidationService.validateCardByCustomerId(wmCustomerId);
            wmAuditMsg = wmAuditApiService.commitAudit(wmAuditCommitObj);
            LOGGER.info("纸质结算提交审核,result={}, wmAuditCommitObj={}", JSONObject.toJSONString(wmAuditMsg), JSONObject.toJSONString(wmAuditCommitObj));
            updateStatusByWmCustomerId(wmCustomerId, WmSettleConstant.SETTLE_STATUS_TO_AUDIT);
            wmSettleLogService.insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.CHANGESTATUS, opUid, opUname, "提交审核");
        } catch (WmServerException | TException e) {
            LOGGER.error("纸质结算提交审核异常,wmAuditCommitObj={},msg={}", JSONObject.toJSONString(wmAuditCommitObj), e.getMessage());
            wmSettlePaperSignAuditDBMapper.delete(biz_id);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "提交审核异常，请稍后重试");
        }
    }


    private SettleData buildData(WmSettle wmSettle) {
        SettleData settleData = new SettleData();
        settleData.setSettleId(wmSettle.getId());
        settleData.setAccCardNo(wmSettle.getAcc_cardno());
        settleData.setAccName(wmSettle.getAcc_name());
        settleData.setAccType(wmSettle.getAcctype());
        settleData.setBranchId((long)wmSettle.getBranchid());
        String provinceName = "";
        try {
            TAdAPIResponse province = tadminDivisionService.getById(wmSettle.getProvince());
            provinceName = province.getChineseFullName();
        } catch (TException e) {
            LOGGER.error("获取省份名称异常，provinceId={},wmSettleId={}", wmSettle.getProvince(), wmSettle.getId());
        }
        String cityName = "";
        try {
            TAdAPIResponse city = tadminDivisionService.getById(wmSettle.getCity());
            cityName = city.getChineseFullName();
        } catch (TException e) {
            LOGGER.error("获取城市名称异常，cityId={},wmSettleId={}", wmSettle.getCity(), wmSettle.getId());
        }
        String bankName = "";
        try {
            bankName = bankInfoService.getBankInfo(wmSettle.getBankid()).getData().get(0).getBankName();
        } catch (TException e) {
            LOGGER.error("获取银行名称异常，bankId={},wmSettleId={}", wmSettle.getBankid(), wmSettle.getId());
        }
        settleData.setBranchName(provinceName+"-"+cityName +"-"+ bankName +"-"+ wmSettle.getBranchname());
        if(wmSettle.getCard_type() == SettleCardType.WALLET.getCode()) {
            settleData.setIsWallet(1);
            if(wmSettle.getAcctype() == 1){
                settleData.setCertType((int)wmSettle.getCert_type());
                settleData.setCertNum(wmSettle.getLegal_cert_num());
                settleData.setLegalIdCard(wmSettle.getLegal_id_card());
                settleData.setLegalPerson(wmSettle.getLegal_person());
            }else if(wmSettle.getAcctype() == 2){
                settleData.setCertType((int)wmSettle.getCert_type());
                settleData.setCertNum(wmSettle.getCert_num());
                settleData.setReservePhone(wmSettle.getReserve_phone());
            }
        }else{
            settleData.setIsWallet(0);
        }
        settleData.setPartyAFinancePeople(wmSettle.getParty_a_finance_people());
        settleData.setPartyAFinancePhone(wmSettle.getParty_a_finance_phone());
        settleData.setSettleType((int)wmSettle.getSettle_type());
        if(wmSettle.getSettle_type() != 2){
            settleData.setPayPeriodNum(wmSettle.getPay_period_num());
            settleData.setPayPeriodUnit(wmSettle.getPay_period_unit());
            settleData.setPayDayOfMonth((int)wmSettle.getPay_day_of_month());
            settleData.setMinPayAmount(wmSettle.getMin_pay_amount());
        }
        List<Long> poiIds = Lists.newArrayList();
        for (Integer id : wmSettle.getWmPoiIdList()) {
            poiIds.add(id.longValue());
        }
        settleData.setPoiIds(poiIds);
        try {
            List<WmPoiDomain> wmPoiDomains = wmPoiClient.pageGetWmPoiByWmPoiIdList(poiIds);
            List<PoiData> poiDataList = Lists.newArrayList();
            for (WmPoiDomain wmPoiDomain : wmPoiDomains) {
                PoiData poiData = new PoiData();
                poiData.setWmPoiId(wmPoiDomain.getWmPoiId());
                poiData.setWmPoiName(wmPoiDomain.getName());
                poiDataList.add(poiData);
            }
            settleData.setPoiList(poiDataList);
        } catch (WmCustomerException e) {
            LOGGER.error("服务化获取门店名称异常", e);
        }

        return settleData;
    }

    private WmAuditSettleCommitData buildWmAuditSettleCommitData(int wmCustomerId, String
            wmCustomerName, List<WmSettle> wmSettleList) {
        WmAuditSettleCommitData wmAuditSettleCommitData = new WmAuditSettleCommitData();
        wmAuditSettleCommitData.setCustomerId(wmCustomerId);
        wmAuditSettleCommitData.setCustomerName(wmCustomerName);

        List<SettleData> settleDataList = Lists.newArrayList();
        WmSettleProtocolDB wmSettleProtocolDB = wmSettleProtocolDBMapper.selectByCustomerMaster(wmCustomerId);
        if (wmSettleProtocolDB != null) {
            if (StringUtils.isNotBlank(wmSettleProtocolDB.getSupplementalUrl())) {
                wmAuditSettleCommitData.setSettleProtocolUrl(Lists.newArrayList(wmSettleProtocolDB.getSupplementalUrl().split(",")));
            }
            if (StringUtils.isNotBlank(wmSettleProtocolDB.getQdbUrl())) {
                wmAuditSettleCommitData.setQdbProtocolUrl(Lists.newArrayList(wmSettleProtocolDB.getQdbUrl().split(",")));
            }

        }
        for (WmSettle wmSettle : wmSettleList) {
            settleDataList.add(buildData(wmSettle));
        }
        wmAuditSettleCommitData.setSettleDatas(settleDataList);
        return wmAuditSettleCommitData;
    }

    /**
     * 纸质客户保存或者修改补充协议，钱袋宝协议
     */
    public int saveOrUpdateWmSettleProtocol(int wmCustomerId, String supplementalUrl, String qdbUrl, int opUid, String opName) {
        WmSettleProtocolDB wmSettleProtocolDB = wmSettleProtocolDBMapper.selectByCustomer(wmCustomerId);
        StringBuffer log = new StringBuffer();
        if (wmSettleProtocolDB == null) {
            wmSettleProtocolDB = new WmSettleProtocolDB();
            wmSettleProtocolDB.setWmCustomerId(wmCustomerId);
            wmSettleProtocolDB.setSupplementalUrl(supplementalUrl);
            wmSettleProtocolDB.setQdbUrl(qdbUrl);
            log.append("===新增补充协议======" +
                    "\n[字段变更]补充协议: =>"+supplementalUrl+";\n");
            if(StringUtils.isNotBlank(qdbUrl)){
                log.append("[\n字段变更\n" +
                        "]钱袋宝协议: =>"+qdbUrl+";\n");
            }
            wmSettleLogService.insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.INSERT, opUid, opName ,log.toString());
            return wmSettleProtocolDBMapper.insert(wmSettleProtocolDB);
        } else {
            if(supplementalUrl.equals(wmSettleProtocolDB.getSupplementalUrl()) && qdbUrl.equals(wmSettleProtocolDB.getQdbUrl())){
                return 0;
            }
            log.append("===修改补充协议======" +
                    "\n[字段变更]补充协议: "+ wmSettleProtocolDB.getSupplementalUrl() +" =>" + supplementalUrl + ";\n");
            if (StringUtils.isNotBlank(qdbUrl)) {
                log.append("\n[字段变更]钱袋宝协议: "+ wmSettleProtocolDB.getQdbUrl() +" =>" + qdbUrl + ";\n");
            }
            wmSettleLogService.insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.UPDATE, opUid, opName, log.toString());
            wmSettleProtocolDB.setSupplementalUrl(supplementalUrl);
            wmSettleProtocolDB.setQdbUrl(qdbUrl);
            return wmSettleProtocolDBMapper.updateWmSettleProtocol(wmSettleProtocolDB);
        }
    }

    /**
     * 纸质客户保存或者修改补充协议，钱袋宝协-线下表复制到线上表
     */
    public int saveOrUpdateWmSettleProtocolAudited(int wmCustomerId) {
        WmSettleProtocolDB wmSettleProtocolDB = wmSettleProtocolDBMapper.selectByCustomer(wmCustomerId);
        if (wmSettleProtocolDB == null) {
            return 0;
        }
        WmSettleProtocolAuditedDB wmSettleProtocolAuditedDB = wmSettleProtocolAuditedDBMapper.selectByCustomer(wmCustomerId);

        if (wmSettleProtocolAuditedDB == null) {
            wmSettleProtocolAuditedDB = new WmSettleProtocolAuditedDB();
            wmSettleProtocolAuditedDB.setWmCustomerId(wmSettleProtocolDB.getWmCustomerId());
            wmSettleProtocolAuditedDB.setSupplementalUrl(wmSettleProtocolDB.getSupplementalUrl());
            wmSettleProtocolAuditedDB.setQdbUrl(wmSettleProtocolDB.getQdbUrl());
            return wmSettleProtocolAuditedDBMapper.insert(wmSettleProtocolAuditedDB);
        } else {
            wmSettleProtocolAuditedDB.setSupplementalUrl(wmSettleProtocolDB.getSupplementalUrl());
            wmSettleProtocolAuditedDB.setQdbUrl(wmSettleProtocolDB.getQdbUrl());
            return wmSettleProtocolAuditedDBMapper.updateWmSettleProtocolAudited(wmSettleProtocolAuditedDB);
        }
    }

    /**
     * 纸质客户获取补充协议，钱袋宝协议
     */
    public WmSettleProtocol getWmSettleProtocol(int wmCustomerId) {
        WmSettleProtocolDB wmSettleProtocolDB = wmSettleProtocolDBMapper.selectByCustomer(wmCustomerId);
        if (wmSettleProtocolDB == null) {
            return null;
        }
        WmSettleProtocol wmSettleProtocol = new WmSettleProtocol();
        BeanUtils.copyProperties(wmSettleProtocolDB, wmSettleProtocol);
        return wmSettleProtocol;
    }

    /**
     * 纸质客户获取补充协议，钱袋宝协议-线上表
     */
    public WmSettleProtocolAudited getWmSettleProtocolAudited(int wmCustomerId) {
        WmSettleProtocolAuditedDB wmSettleProtocolAuditedDB = wmSettleProtocolAuditedDBMapper.selectByCustomer(wmCustomerId);
        if (wmSettleProtocolAuditedDB == null) {
            return null;
        }
        WmSettleProtocolAudited wmSettleProtocolAudited = new WmSettleProtocolAudited();
        BeanUtils.copyProperties(wmSettleProtocolAuditedDB, wmSettleProtocolAudited);
        return wmSettleProtocolAudited;
    }

    private WmSettlePaperSignAuditDB buildCommitSignModeData(int customerId, int opId, String commitData) {
        WmSettlePaperSignAuditDB signModeDB = new WmSettlePaperSignAuditDB();
        signModeDB.setWmCustomerId(customerId);
        signModeDB.setOpId(opId);
        signModeDB.setValid(1);
        signModeDB.setEffective(1);
        signModeDB.setAuditData("");//提审数据不用存DB
        signModeDB.setAuditStatus(WmSettleAuditConstant.AUDIT_IND.getCode());
        return signModeDB;
    }

    private void checkCanUseBankCardType(int wmCustomerId, int wmPoiId) throws WmCustomerException{
        if(!ConfigUtilAdapter
                .getBoolean("CanUseBankCardType_check_open",false)){
            return;
        }
        WmSettle wmSettle = getWmSettleBasicByWmCustomerIdAndWmPoiId(
                wmCustomerId, wmPoiId);
        if(wmSettle!=null && wmSettle.getCard_type() == WmContractConstant.CardType.BANK.getIndex()){
            CustomerBlackWhiteParam param = new CustomerBlackWhiteParam();
            param.setBizId(wmPoiId).setBizType(CustomerConstants.WHITE_LIST_BIZTYPE_SETTLE_CARD)
                    .setType(CustomerConstants.TYPE_WHITE);
            BooleanResult booleanResult = wmCustomerBlackWhiteListService
                    .queryInCustomerBlackWhiteList(param);
            if(!booleanResult.isRes()){
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"请开通钱包");
            }
        }
    }

    public WmSettleAudited getWmSettleAuditedByWalletId(long wmWalletId) {
        List<WmSettleAuditedDB> wmSettleAuditedDBList = wmSettleAuditedDBMapper.getByWalletId(wmWalletId);
        if (CollectionUtils.isEmpty(wmSettleAuditedDBList)) {
            return null;
        }

        WmSettleAudited wmSettleAudited = WmSettleTransUtil.wmSettleAuditedDB2Thrift(wmSettleAuditedDBList.get(0));
        LOGGER.info("getWmSettleAuditedByWalletId wmSettleAudited = {}", JSON.toJSONString(wmSettleAudited));
        return wmSettleAudited;
    }

    public List<WmSettleAudited> getWmSettleAuditedListByWalletId(long wmWalletId) {
        List<WmSettleAuditedDB> wmSettleAuditedDBList = wmSettleAuditedDBMapper.getByWalletId(wmWalletId);
        return WmSettleTransUtil.wmSettleAuditedListDB2Thrift(wmSettleAuditedDBList);
    }

    public WmSettleAudited getWmSettleAuditedByWalletIdMaster(long wmWalletId) {
        List<WmSettleAuditedDB> wmSettleAuditedDBList = wmSettleAuditedDBMapper.getByWalletIdMaster(wmWalletId);
        if (CollectionUtils.isEmpty(wmSettleAuditedDBList)) {
            return null;
        }
        WmSettleAudited wmSettleAudited = WmSettleTransUtil.wmSettleAuditedDB2Thrift(wmSettleAuditedDBList.get(0));
        return wmSettleAudited;
    }

    public BooleanResult releaseExpiredWmPoiSettleRel(int wmCustomerId, String toRelease) {
        wmPoiSettleAuditedDBMapper.deleteByWmContractIdAndWmPoiIds(wmCustomerId,toRelease);
        wmPoiSettleDBMapper.deleteByWmContractIdAndWmPoiIdList(wmCustomerId,toRelease);
        return new BooleanResult(true);
    }


    /**
     * 客户id-读-获取关联结算列表
     */
    public WmSettlePageBo queryWmSettlePageList(Long wmPoiId, String wmPoiName, Integer settleId, String acctName,
                                                Integer customerId, String accCardno,boolean isEffective, Integer pageNum, Integer pageSize) throws WmCustomerException {
        WmSettlePageQueryParam param = new WmSettlePageQueryParam();
        param.setWmPoiId(wmPoiId);
        param.setWmPoiName(wmPoiName);
        param.setSettleId(settleId);
        param.setAcctName(acctName);
        param.setCustomerId(customerId);
        param.setAccCardno(accCardno);
        param.setEffective(isEffective);
        param.setPageNum(pageNum);
        param.setPageSize(pageSize);
        return queryWmSettlePageListWithParam(param);
    }

    public WmSettlePageBo queryWmSettlePageListWithParam(WmSettlePageQueryParam param) throws WmCustomerException{
        if (param.getCustomerId() <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "客户id不能为空");
        }
        int pageSize = Math.min(param.getPageSize(), WmContractConstant.MAX_PAGESIZE);
        //搜索条件为空
        Boolean notSearchWmPoiId = param.getWmPoiId() == null || param.getWmPoiId().longValue() == 0L;
        Boolean notSearchSettleId = param.getSettleId() == null || param.getSettleId().intValue() == 0;
        Boolean notSearchWalletId = param.getWmWalletId() == null || param.getWmWalletId().longValue() <= 0L;
        if (notSearchWmPoiId && notSearchSettleId && notSearchWalletId && StringUtils.isEmpty(param.getWmPoiName())
                && StringUtils.isEmpty(param.getAcctName()) && StringUtils.isEmpty(param.getAccCardno())
                    && param.getIsOpenWallet() == null &&  param.getCardValid() == null) {
            return pageGetWmSettleWithoutSearch(param.getCustomerId(), pageSize, param.getPageNum(),
                    param.isEffective());
        }
        //搜索条件不为空
        return queryWmSettlePoiSearch(param);
    }

    private WmSettlePageBo queryWmSettlePoiSearch(WmSettlePageQueryParam param) throws WmCustomerException{
        Pair<List<Integer>, List<Integer>> pair = getWmPoiIdByWmCustomerId(param.getCustomerId(), param.isEffective());
        //获取当前客户正在切换的信息
        List<SwitchPoiInfo> switchPoiInfoList = wmSettleSwitchService.getSettleSwitchPoiInfoList(param.getCustomerId());
        pair = WmSettleTransUtil.aggregateWmSettlePoiPair(pair,switchPoiInfoList,param.isEffective());
        //结算没有关联的门店则不展示
        if (pair == null || CollectionUtils.isEmpty(pair.getRight())) {
            return new WmSettlePageBo();
        }
        //待查门店集合
        List<Integer> searchList = Lists.newArrayList(pair.getRight());
        //通过门店ID查询
        if (param.getWmPoiId() != null && param.getWmPoiId() != 0L) {
            searchList.retainAll(Lists.newArrayList(param.getWmPoiId().intValue()));
        }
        //通过门店名称查询
        else if(!StringUtils.isEmpty(param.getWmPoiName())){
            searchList = wmPoiClient.pageGetWmPoiIdByNameOrId(param.getWmPoiName(), pair.getRight(), 1, 100);
        }
        //通过结算ID查询-独立查询,其他查询条件默认从全量数据中筛选
        else if (param.getSettleId() != null && param.getSettleId() != 0) {
            searchList = Lists.newArrayList();
        }

        List<WmSettle> wmSettleList = genWmSettleListForSearch(param, pair, switchPoiInfoList, searchList);

        if (CollectionUtils.isEmpty(wmSettleList)) {
            return new WmSettlePageBo();
        }

        //基于当前查出的全量结算数据检索
        List<WmSettle> resultWmSettleList = Lists.newArrayList();
        getPullDownListSearchResult(param, wmSettleList, resultWmSettleList);
        resultWmSettleList = getOpenWalletOrCardValidSearchResult(param, resultWmSettleList);

        // 如果请求的页码超出了范围，则将请求页码改为第一页
        List<WmSettle> returnList = PageUtil.paging(resultWmSettleList, param.getPageNum(), param.getPageSize());
        if (param.isEffective()) {
            returnList =
                    WmSettleTransUtil.transWmSettleAuditedList2WmSettleList(
                            wrapWmSettleAuditedWithWmPoiIdList(
                                    WmSettleTransUtil.transWmSettleList2WmSettleAuditedList(returnList),switchPoiInfoList));

        } else {
            returnList = wrapWmSettleWithWmPoiIdList(returnList,switchPoiInfoList);
        }

        //填充开钱包完整信息
        for(WmSettle wmSettle:returnList){
            Integer maxId = wmSettleAuditedDBMapper.getLatestedLogId(0, wmSettle.getId());
            if(maxId!=null) {
                WmC1WalletLog wmC1WalletLog = wmSettleAuditedDBMapper.getLatestedLog(maxId);
                if(wmC1WalletLog!=null) {
                    wmSettle.setWm_wallet_id(wmC1WalletLog.getWm_wallet_id());
                    wmSettle.setLogin_name(wmC1WalletLog.getLogin_name());
                    wmSettle.setOpen_time(wmC1WalletLog.getOpen_time());
                    wmSettle.setWallet_open_status(wmC1WalletLog.getWallet_open_status());
                    wmSettle.setWallet_open_reason(wmC1WalletLog.getWallet_open_reason());
                    wmSettle.setRealname_status(wmC1WalletLog.getRealname_status());
                    wmSettle.setRealname_reason(wmC1WalletLog.getRealname_reason());
                    wmSettle.setCard_binding_status(wmC1WalletLog.getCard_binding_status());
                    wmSettle.setCard_binding_reason(wmC1WalletLog.getCard_binding_reason());
                }
            }
        }
        // 一分钱打款验卡
        returnList = wmBankCardValidationService.batchQueryAndSet(returnList);
        // 隐去银行卡号的部分数字
        // 仅当敏感信息开关关闭时 使用老逻辑
        if (!MccConfig.encryptSensitiveWords()) {
            returnList = WmSettleTransUtil.encryptWmSettleList(returnList);
        }
        WmSettlePageBo pageBo = new WmSettlePageBo();
        pageBo.setWmSettleList(returnList);
        pageBo.setCount(resultWmSettleList.size());
        return pageBo;
    }

    /**
     * 是否开钱包和验卡状态查询
     * @param param
     * @param resultWmSettleList
     * @return
     */
    private List<WmSettle> getOpenWalletOrCardValidSearchResult(WmSettlePageQueryParam param,
            List<WmSettle> resultWmSettleList) {
        //是否开钱包和验卡状态查询
        if(param.getIsOpenWallet() != null || param.getCardValid() != null){
            List<WmSettle> filteredWmSettleList = Lists.newArrayList();
            for(WmSettle wmSettle : resultWmSettleList){
                //命中是否开钱包查询
                if (param.getIsOpenWallet() != null && !Integer.valueOf(wmSettle.getCard_type())
                        .equals(SEARCH_PARAM_IS_OPEN_WALLET_MAP.get(param.getIsOpenWallet()))) {
                    continue;
                }
                //命中验卡状态查询
                if(param.getCardValid() != null && wmSettle.getCard_valid() != param.getCardValid()){
                    continue;
                }
                filteredWmSettleList.add(wmSettle);
            }
            resultWmSettleList = filteredWmSettleList;
        }
        return resultWmSettleList;
    }

    /**
     * 下拉列表数据查询：账户名称、银行卡号、钱包ID
     * @param param
     * @param wmSettleList
     * @param resultWmSettleList
     */
    private void getPullDownListSearchResult(WmSettlePageQueryParam param, List<WmSettle> wmSettleList,
            List<WmSettle> resultWmSettleList) {
        //下拉列表选项查询
        for (WmSettle wmSettle : wmSettleList) {
            //账户名称查询
            if (StringUtils.isNotEmpty(param.getAcctName())) {
                if (wmSettle.getAcc_name().contains(param.getAcctName())) {
                    resultWmSettleList.add(wmSettle);
                }
            }
            //银行卡号查询
            else if (StringUtils.isNotEmpty(param.getAccCardno())) {
                if (wmSettle.getAcc_cardno().equals(param.getAccCardno())) {
                    resultWmSettleList.add(wmSettle);
                }
            }
            //钱包ID查询
            else if(param.getWmWalletId() != null){
                if(wmSettle.getWm_wallet_id() == param.getWmWalletId()){
                    resultWmSettleList.add(wmSettle);
                }
            }
            //其他
            else {
                resultWmSettleList.add(wmSettle);
            }
        }
    }

    /**
     * 获取结算详细列表数据
     * @param param 查询条件
     * @param pair 结算ID,门店ID聚合整合
     * @param switchPoiInfoList 切换不下线数据
     * @param searchList 待查门店集合
     * @return 结算详细列表数据
     */
    private List<WmSettle> genWmSettleListForSearch(WmSettlePageQueryParam param,
            Pair<List<Integer>, List<Integer>> pair, List<SwitchPoiInfo> switchPoiInfoList,
            List<Integer> searchList) {
        List<WmSettle> wmSettleList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(searchList)) {
            List<Integer> commonList = BeanDiffUtil.genCommonList(pair.getRight(), searchList);
            if (CollectionUtils.isNotEmpty(commonList)) {
                wmSettleList = getWmSettleListBySettleIdAndWmPoiId(pair.getLeft(),
                        commonList, param.isEffective(), param.getCustomerId(), switchPoiInfoList);
            }
        }

        //结算ID查询
        if (param.getSettleId() != null && param.getSettleId() != 0) {
            if (!param.isEffective()) {
                wmSettleList = WmSettleTransUtil.WmSettleListDB2Thrift(
                        wmSettleDBMapper.queryWmSettleListByWmSettleIdListWithSort(
                                Lists.newArrayList(param.getSettleId()),
                                param.getCustomerId()));
            } else {
                wmSettleList = WmSettleTransUtil.transWmSettleAuditedList2WmSettleList(
                        WmSettleTransUtil.wmSettleAuditedListDB2Thrift(wmSettleAuditedDBMapper
                                .queryWmSettleListByWmSettleIdListWithSort(
                                        Lists.newArrayList(param.getSettleId()),
                                        param.getCustomerId())));
            }
        }
        return wmSettleList;
    }

    public void batchUnbindOfflinePoi(int wmCustomerId, String wmPoiIds, int opUid, String opUname) throws WmCustomerException {
        //0.结算状态校验，可编辑状态才可解绑门店
        byte settleStatusByWmCustomerId = getSettleStatusByWmCustomerId(wmCustomerId);

        if (!canSave(settleStatusByWmCustomerId)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不允许修改");
        }

        //1.获取当前客户线下表关联的门店id
        List<Integer> offlineWmPoiIdListByWmCustomerId = getOfflineWmPoiIdListByWmCustomerId(wmCustomerId);
        //2.批量解绑校验
        Set<Long> toUnbindWmPoiIdSet = wmSettleCheckService.checkBatchUnbindOfflinePoi(wmCustomerId, wmPoiIds,
                Sets.newHashSet(Lists.transform(offlineWmPoiIdListByWmCustomerId, new Function<Integer, Long>(

                ) {
                    @Nullable
                    @Override
                    public Long apply(@Nullable Integer input) {
                        return input.longValue();
                    }
                })));
        //3.解绑操作
        wmPoiSettleDBMapper.deleteByWmCustomerIdAndWmPoiIdList(wmCustomerId, Lists.newArrayList(toUnbindWmPoiIdSet));
        //4.切换中心解绑操作
        wmSettleSwitchService.deleteOfflineSettlePoiInSwitchCentre(wmCustomerId, Lists.newArrayList(toUnbindWmPoiIdSet), opUid, opUname);
        wmSettleLogService.insertWmSettleLog(wmCustomerId, OpType.DELETE, opUid, opUname, "批量解绑门店:" + StringUtils.join(toUnbindWmPoiIdSet, ","));
    }

    public WmSettleAudited getWmSettleAuditedBasicByWmPoiIdMaster(int wmPoiId) throws WmCustomerException, TException {
        LOGGER.info("#getWmSettleAuditedBasicByWmPoiIdMaster wmPoiId:{}", wmPoiId);
        Set<Integer> customerIdByPoiId = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(Long.valueOf(wmPoiId)));
        if (CollectionUtils.isEmpty(customerIdByPoiId)) {
            return null;
        }
        List<WmPoiSettleAuditedDB> auditedDBList = wmPoiSettleAuditedDBMapper
                .getByWmContractIdAndWmPoiIdMaster(customerIdByPoiId.iterator().next(), wmPoiId);
        if (CollectionUtils.isEmpty(auditedDBList)) {
            return null;
        }
        if (CollectionUtils.size(auditedDBList) > 1) {
            LOGGER.error("门店线上关联多个结算 wmPoiId:{} auditedDBList:{}", wmPoiId, JSON.toJSON(auditedDBList));
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店线上关联多个结算");
        }
        WmSettleAuditedDB settleAuditedDB = wmSettleAuditedDBMapper.getBySettleIdMaster(auditedDBList.get(0).getWm_settle_id());
        return WmSettleTransUtil.wmSettleAuditedDB2Thrift(settleAuditedDB);
    }

    public List<Integer> getAuditedSettlePoiIdListMaster(Integer wmSettleId) {
        LOGGER.info("#getAuditedSettlePoiIdListMaster wmSettleId:{}", wmSettleId);
        return wmPoiSettleAuditedDBMapper.getPoiIdsBySettleIdMaster(wmSettleId);
    }


    public  List<WmPoiSettleAuditedDB> getByContractIdAndWmPoiIdList(Integer wmCustomerId,List<Long> wmPoiIdList){
        LOGGER.info("#getByContractIdAndWmPoiIdList wmCustomerId={}", wmCustomerId);
        return  wmPoiSettleAuditedDBMapper.getByContractIdAndWmPoiIdList(wmCustomerId,wmPoiIdList);
    }

    public  List<WmPoiSettleAuditedDB> getAllByContractIdAndWmPoiIdList(Integer wmCustomerId,List<Long> wmPoiIdList){
        LOGGER.info("#getAllByContractIdAndWmPoiIdList wmCustomerId={}", wmCustomerId);
        return  wmPoiSettleAuditedDBMapper.getAllByContractIdAndWmPoiIdList(wmCustomerId,wmPoiIdList);
    }

    public  List<WmPoiSettleDB> getByWmContractIdAndWmPoiIdList(Integer wmCustomerId,List<Long> wmPoiIdList){
        LOGGER.info("#getByWmContractIdAndWmPoiIdList wmCustomerId={}", wmCustomerId);
        return  wmPoiSettleDBMapper.getByWmContractIdAndWmPoiIdList(wmCustomerId,wmPoiIdList);
    }

    public BooleanResult mergeRepetitionAuditedSettlePoi(int wmContractId, long wmPoiId) {
        List<WmPoiSettleAuditedDB> wmPoiSettleAuditedDBList = getByContractIdAndWmPoiIdList(wmContractId, Lists.newArrayList(wmPoiId));
        List<Integer> toDeletePrimaryKey = Lists.newArrayList();
        if (wmPoiSettleAuditedDBList.size() > 1) {
            toDeletePrimaryKey = Lists.newArrayList(Lists.transform(wmPoiSettleAuditedDBList, new Function<WmPoiSettleAuditedDB, Integer>() {
                @Nullable
                @Override
                public Integer apply(@Nullable WmPoiSettleAuditedDB input) {
                    return input.getId();
                }
            }));
        } else {
            return new BooleanResult(true);
        }
        Collections.sort(toDeletePrimaryKey);
        for (int i = 0; i < toDeletePrimaryKey.size() - 1; i++) {
            wmPoiSettleAuditedDBMapper.deleteByPrimaryKey(toDeletePrimaryKey.get(i));
        }
        return new BooleanResult(true);
    }

}
