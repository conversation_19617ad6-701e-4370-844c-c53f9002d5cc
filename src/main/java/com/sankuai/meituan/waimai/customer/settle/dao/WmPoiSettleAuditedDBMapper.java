package com.sankuai.meituan.waimai.customer.settle.dao;

import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.datasource.multi.annotation.DataSource;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@DataSource("dbContractWrite")
@Deprecated
public interface WmPoiSettleAuditedDBMapper {
    public final static String INSERT_KEYS = "wm_poi_id, wm_contract_id, wm_settle_id, ctime, utime, valid";
    public final static String SELECT_KEYS = "id, " + INSERT_KEYS;

    int deleteByPrimaryKey(Integer id);

    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(WmPoiSettleAuditedDB record);

    @Options(useGeneratedKeys = true)
    int insertSelective(WmPoiSettleAuditedDB record);

    @DataSource("dbContractRead")
    WmPoiSettleAuditedDB selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(WmPoiSettleAuditedDB record);

    int updateByPrimaryKey(WmPoiSettleAuditedDB record);

    @Update("update wm_poi_settle_audited set wm_settle_id = #{wm_settle_id}, utime = unix_timestamp() where wm_contract_id = ${wm_contract_id} and wm_poi_id = ${wm_poi_id} and valid = 1")
    int updateByWmContractIdAndWmPoiId(WmPoiSettleAuditedDB wmPoiSettleAuditedDB);

    @Select("select wm_poi_id from wm_poi_settle_audited where wm_settle_id = #{wm_settle_id} and valid = 1")
    @DataSource("dbContractRead")
    List<Integer> getPoiIdsBySettleId(@Param("wm_settle_id") Integer wm_settle_id);

    @Select("select wm_poi_id from wm_poi_settle_audited where wm_settle_id = #{wm_settle_id} and wm_contract_id = #{wmContractId} and valid = 1")
    List<Integer> getPoiIdsBySettleIdAndWmContractId(@Param("wm_settle_id") Integer wm_settle_id,@Param("wmContractId") Integer wmContractId);

    @Select("/*master*/select wm_poi_id from wm_poi_settle_audited where wm_settle_id = #{wm_settle_id} and valid = 1")
    @DataSource("dbContractWrite")
    List<Integer> getPoiIdsBySettleIdMaster(@Param("wm_settle_id") Integer wm_settle_id);

    @DataSource("dbContractRead")
    List<WmPoiSettleAuditedDB> getByContractIdAndWmPoiIdList(@Param("wmContractId") Integer wmContractId, @Param
            ("wmPoiIdList") List<Long> wmPoiIdList);

    List<WmPoiSettleAuditedDB> getByContractIdAndWmPoiIdListMaster(@Param("wmContractId") Integer wmContractId, @Param
            ("wmPoiIdList") List<Long> wmPoiIdList);

    @DataSource("dbContractRead")
    List<WmPoiSettleAuditedDB> getAllByContractIdAndWmPoiIdList(@Param("wmContractId") Integer wmContractId, @Param("wmPoiIdList") List<Long> wmPoiIdList);

    @DataSource("dbContractWrite")
    List<WmPoiSettleAuditedDB> getByWmContractIdAndWmPoiIdMaster(@Param("wmContractId") Integer wmContractId, @Param("wmPoiId") Integer wmPoiId);

    List<WmPoiSettleAuditedDB> getByWmSettleIdListAndWmPoiIdList(
        @Param("wmSettleIdList") List<Integer> wmSettleIdList,
        @Param("wmPoiIdList") List<Long> wmPoiIdList);

    @Update("update wm_poi_settle_audited set valid=0, utime=unix_timestamp() where wm_contract_id = #{wmContractId} and wm_poi_id in (${wmPoiIds})")
    void deleteByWmContractIdAndWmPoiIds(@Param("wmContractId") int wmContractId, @Param("wmPoiIds") String wmPoiIds);

    @DataSource("dbContractRead")
    List<WmPoiSettleAuditedDB> batchQueryPoiSettleAudited(@Param("poiList") List<Long> poiList);

    List<WmPoiSettleAuditedDB> getBySettleIdList(@Param("wmSettleIdList")List<Integer> wmSettleIdList);

    List<WmPoiSettleAuditedDB> getBySettleIdListAndWmContractId(@Param("wmSettleIdList")List<Integer> wmSettleIdList,@Param("wmContractId") Integer wmContractId);

    List<Integer> getWmPoiIdListByWmSettleIdList(@Param("wmSettleIdList")List<Integer> wmSettleIdList);

    List<Integer> getWmSettleIdListBySettleAndPoi(@Param("wmSettleIdList")List<Integer> wmSettleIdList, @Param("wmPoiIdList")List<Integer> wmPoiIdList);

    List<Integer> getWmSettleIdListByWmPoiId(int wmPoiId);

    List<Integer> getWmSettleIdListByWmPoiIdAndWmContractId(@Param("wmPoiId")int wmPoiId,@Param("wmContractId")int wmContractId);

    List<Long> batchGetEffectiveWmPoiIdList(@Param("wmPoiIdList")List<Long> wmPoiIdList);

    int deleteByWmSettleIdList(@Param("wmSettleIdList") List<Integer> wmSettleIdList);

    int deleteByWmSettleIdListAndWmPoiIdList(@Param("wmSettleIdList") List<Integer> wmSettleIdList,
        @Param("wmPoiIdList") List<Long> wmPoiIdList);

    int deleteUnnecessaryOnlineRelByWmCustomerIdAndWmPoiIdList(@Param("wmCustomerId") int wmCustomerId, @Param("wmPoiIdList") List<Long> wmPoiIdList);

    List<WmPoiSettleAuditedDB> getUnnecessaryOnlineRelByWmCustomerIdAndWmPoiIdListMaster(@Param("wmCustomerId") int wmCustomerId, @Param("wmPoiIdList") List<Long> wmPoiIdList);

    int getCountByWmContractIdAndWmSettleIdMaster(@Param("wmContractId") Integer wmContractId, @Param("wmSettleId") Integer wmSettleId);


}