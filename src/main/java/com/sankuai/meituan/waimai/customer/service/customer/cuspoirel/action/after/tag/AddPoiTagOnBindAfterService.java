package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.action.after.tag;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerNewSettleService;
import com.sankuai.meituan.waimai.qualification.WmQualificationThriftService;
import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiQualificationInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 20240116
 * @desc 绑定后新增门店标签服务
 */
@Service
@Slf4j
public class AddPoiTagOnBindAfterService {

    @Autowired
    private WmCustomerNewSettleService wmCustomerNewSettleService;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmQualificationThriftService.Iface wmQuaThriftService;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("CUSTOMER_POI_MSC_POOL_%d").build();

    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(30, 50, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(20000), THREAD_FACTORY, new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));

    /**
     * 给门店打新结算标签
     *
     * @param context
     */
    public void addNewSettleTagOnPoi(CustomerPoiBindFlowContext context) throws WmCustomerException {

        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();

        // 校验客户与门店的新结算版本是否一致
        boolean isNewSettle = wmCustomerNewSettleService.checkCustomerAndPoiVersion(wmCustomerDB.getMtCustomerId(), wmCustomerDB.getCustomerName(), wmPoiIdSet);
        //客户无新结算标签则直接返回
        if (!isNewSettle) {
            return;
        }
        // 新结算绑定，给门店打新结算标签
        wmCustomerNewSettleService.addNewSettlePoiTag(wmPoiIdSet, context.getOpUid(), context.getOpName());
        log.info("addNewSettleTagOnPoi,给门店打新结算标签完成，wmPoiIdSet={}", JSON.toJSONString(wmPoiIdSet));
    }

    /**
     * 给门店打美食城标签
     *
     * @param context
     */
    public void addMscTagOnPoi(CustomerPoiBindFlowContext context) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        if (wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            return;
        }
        boolean customerHasMSCLabel = wmCustomerMSCLabelService.checkCustomerHasMSCLabel(wmCustomerDB.getMtCustomerId());
        if (!customerHasMSCLabel) {
            log.info("addMscTagOnPoi,客户无美食城标签则不需要给门店打美食城标,customerId={},mtCustomerId={}", context.getCustomerId(), wmCustomerDB.getMtCustomerId());
            return;
        }

        //是否有美食城资质共用标签为空则需要重新赋值-签约回调场景需要
        if (context.getHasCustomerQuaComTag() == null) {
            boolean hasQuaComTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
            context.setHasCustomerQuaComTag(hasQuaComTag);
        }

        List<Long> needMscTagWmPoiIds = Lists.newArrayList();
        //有资质共用标签则直接打标
        if (context.getHasCustomerQuaComTag()) {
            needMscTagWmPoiIds = Lists.newArrayList(context.getWmPoiIdSet());
        } else {
            //门店资质与客户一致则需要打标
            needMscTagWmPoiIds = getQuaConsistentWmPoiIds(Lists.newArrayList(context.getWmPoiIdSet()), wmCustomerDB);
        }
        //如果需要打标门店列表为空则直接返回
        if (CollectionUtils.isEmpty(needMscTagWmPoiIds)) {
            return;
        }

        //给门店打美食城标签
        wmCustomerMSCLabelService.addMSCPoiTag(Sets.newHashSet(needMscTagWmPoiIds), context.getOpUid(), context.getOpName());
        log.info("addMscTagOnPoi,给门店打美食城标签完成,needMscTagWmPoiIds={}", JSON.toJSONString(needMscTagWmPoiIds));
    }

    /**
     * 给门店添加资质共用标签
     *
     * @param context
     */
    public void addQuaComTagOnPoi(CustomerPoiBindFlowContext context) {
        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        if (wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            return;
        }
        if (!context.getHasCustomerQuaComTag()) {
            return;
        }
        //给门店添加资质共用标签
        wmCustomerMSCLabelService.addMscQuaCommonPoiTag(context.getWmPoiIdSet(), context.getOpUid(), context.getOpName());
        log.info("addQuaComTagOnPoi,给门店打资质共用标签完成,wmPoiIdSet={}", JSON.toJSONString(context.getWmPoiIdSet()));
    }


    /**
     * 计算与客户资质一致的门店列表
     *
     * @param wmPoiIdList
     * @param wmCustomerDB
     * @return
     */
    private List<Long> getQuaConsistentWmPoiIds(List<Long> wmPoiIdList, WmCustomerDB wmCustomerDB) {

        //本批次门店中资质不一致的门店列表
        List<Long> notConsistentWmPoiIds = Lists.newArrayList();
        //根据配置将门店ID分组处理
        List<List<Long>> allPoiIds = Lists.partition(wmPoiIdList, MccConfig.checkQuaPageSize());
        CountDownLatch countDownLatch = new CountDownLatch(allPoiIds.size());
        //分批次判断每批次中门店资质与客户资质不一样的数据
        for (final List<Long> wmPoiIs : allPoiIds) {
            try {
                executorService.execute(new TraceRunnable(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            //获取本批次门店的所有资质列表map信息
                            Map<Long, List<WmPoiQualificationInfoBo>> resultMap = wmQuaThriftService.getWmPoiQualificationInfosByWmPoiIds(wmPoiIs);
                            log.info("getQuaConsistentWmPoiIds,wmPoiIs={},resultMap={}", JSONObject.toJSONString(wmPoiIs), JSONObject.toJSONString(resultMap));
                            for (Map.Entry<Long, List<WmPoiQualificationInfoBo>> map : resultMap.entrySet()) {
                                Long wmPoiId = map.getKey();
                                //判断门店资质与客户资质是否一致,不一致则添加到notConsistentWmPoiIds中
                                if (!checkPoiWithCustomerQuaConsistent(map.getValue(), wmCustomerDB)) {
                                    notConsistentWmPoiIds.add(wmPoiId);
                                }
                            }
                        } catch (WmServerException e) {
                            log.warn("getQuaConsistentWmPoiIds,wmPoiIs:{} ", JSONObject.toJSONString(wmPoiIs), e);
                        } catch (TException e) {
                            log.warn("getQuaConsistentWmPoiIds,wmPoiIs:{} ", JSONObject.toJSONString(wmPoiIs), e);
                        } finally {
                            countDownLatch.countDown();
                        }
                    }
                }));
            } catch (Exception e) {
                log.error("getQuaConsistentWmPoiIds,多线程执行异常，wmPoiIdList={} ", JSONObject.toJSONString(wmPoiIdList), e);
            }
        }

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("getQuaConsistentWmPoiIds,多线程执行超时，wmPoiIdList={} ", JSONObject.toJSONString(wmPoiIdList), e);
        } finally {
            log.info("getQuaConsistentWmPoiIds,#end");
        }

        log.info("getQuaConsistentWmPoiIds,本批次绑定门店中与客户资质不一致的门店列表,notConsistentWmPoiIds={}", JSON.toJSONString(notConsistentWmPoiIds));
        //移除资质不一致的门店数据
        if (CollectionUtils.isNotEmpty(notConsistentWmPoiIds)) {
            wmPoiIdList.removeAll(notConsistentWmPoiIds);
        }

        return wmPoiIdList;
    }

    /**
     * 判断客户资质与门店资质是否一致
     *
     * @param qualificationInfoBoList
     * @param wmCustomerDB
     * @return
     */
    private boolean checkPoiWithCustomerQuaConsistent(List<WmPoiQualificationInfoBo> qualificationInfoBoList,
                                                      WmCustomerDB wmCustomerDB) {
        String customerNumber = wmCustomerDB.getCustomerNumber();
        //资质类型
        int quaType = wmCustomerDB.getCustomerType();
        int quaSecondType = wmCustomerDB.getCustomerSecondType();
        for (WmPoiQualificationInfoBo wmPoiAuditObjectBo : qualificationInfoBoList) {
            //资质为个人证件&客户资质为个人证件&门店资质与客户资质不一样
            if (wmPoiAuditObjectBo.getType() == QuaTypeEnum.QUA_SUBTYPE_CID.getSubType()
                    && wmPoiAuditObjectBo.getSecondSubType() == QuaTypeEnum.QUA_SUBTYPE_CID.getSecondSubType()
                    && (quaType == CustomerType.CUSTOMER_TYPE_IDCARD.getCode() && quaSecondType == CertTypeEnum.ID_CARD.getType())
                    && !wmPoiAuditObjectBo.getNumber().equals(customerNumber)) {
                return false;
            }

            //资质为营业执照&客户资质为营业执照&客户资质编号与门店资质编号不一样
            if (wmPoiAuditObjectBo.getType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSubType()
                    && wmPoiAuditObjectBo.getSecondSubType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSecondSubType()
                    && quaType == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()
                    && !wmPoiAuditObjectBo.getNumber().equals(customerNumber)) {
                return false;
            }
        }
        return true;
    }
}
