package com.sankuai.meituan.waimai.customer.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskAreaDB;


@Component
public interface WmEcontractTaskAreaDBMapper {

    // 插入task数据配送范围信息
    int insert(WmEcontractSignTaskAreaDB signTaskAreaDB);

    // 批量插入task数据配送范围信息
    void batchInsert(@Param("signTaskAreaDBList") List<WmEcontractSignTaskAreaDB> signTaskAreaDBList);

    // 根据task数据主键和门店ID批量获取冷数据配送范围信息
    List<WmEcontractSignTaskAreaDB> selectByTaskIdAndWmPoiIds(@Param("taskId") Long taskId, @Param("wmPoiIds") List<Long> wmPoiIds);
}
