package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.partner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.AbstractWmEcontractApplyAdapterService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.EcontractDaoCanC1ContractInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/5 15:56
 */
@Service
public class DaoCanEcontractC1ContractApplyService extends AbstractWmEcontractApplyAdapterService {

    private static final String FLOW_DINE_IN_SERVICE_C1CONTRACT = EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getName();

    private static final List<String> flowList = Lists.newArrayList(FLOW_DINE_IN_SERVICE_C1CONTRACT);

    private static final Map<String, EcontractDataWrapperEnum> dataWrapperMap =
            Collections.singletonMap(FLOW_DINE_IN_SERVICE_C1CONTRACT, EcontractDataWrapperEnum.DAOCAN_SERVICE_C1_CONTRACT);

    private static final String dao_can_c1_contract_econtract_type = "dine_in_service_C1";

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo) throws TException, IllegalAccessException, WmCustomerException {

        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));

        return new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(dao_can_c1_contract_econtract_type)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .callBackUrl(getCallbackUrl()).econtractBatchSource(getSource(batchContextBo))
                .build();
    }

}
