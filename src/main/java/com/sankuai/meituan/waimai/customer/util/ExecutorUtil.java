package com.sankuai.meituan.waimai.customer.util;

import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.cip.crane.netty.utils.SleepUtils;

public class ExecutorUtil {

    private static Logger logger = LoggerFactory.getLogger(ExecutorUtil.class);

    public static <T> T execWithRetry(Executor<T> executor, int retryTimes) {
        return execWithRetry(executor, retryTimes, 100);
    }

    public static <T> T execWithRetry(Executor<T> executor, int retryTimes, int retryInterval) {
        int times = 0;
        T result = null;
        while (times++ < retryTimes) {
            try {
                result = executor.exec();
                logger.info("执行结果：{}", JSON.toJSONString(result));
                if (!executor.shouldRetry(result)) {
                    return result;
                }
            } catch (Exception e) {
                logger.warn("执行失败 msg:{}", e.getMessage(), e);
                if (times >= retryTimes) {
                    logger.error("执行失败 msg:{}", e.getMessage(), e);
                } else {
                    SleepUtils.sleep(retryInterval);
                }
            } finally {
                if (times >= retryTimes) {
                    executor.execComplete(result);
                }
            }
        }
        logger.warn("执行失败 retryTimes:{}", retryTimes);
        return result;
    }

    public static void execAsync(Executor executor, long afterMillis) {
        new Thread(() -> {
            try {
                if (afterMillis > 0) {
                    SleepUtils.sleep(afterMillis);
                }
                executor.exec();
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }).start();
    }


    public static void execAsyncWithTraceId(Executor executor, long afterMillis) {
        new Thread(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    if (afterMillis > 0) {
                        SleepUtils.sleep(afterMillis);
                    }
                    executor.exec();
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        })).start();
    }

    public interface Executor<T> {
        T exec() throws Exception;

        boolean shouldRetry(T t) throws Exception;

        default void execComplete(T t) {

        }
    }

}
