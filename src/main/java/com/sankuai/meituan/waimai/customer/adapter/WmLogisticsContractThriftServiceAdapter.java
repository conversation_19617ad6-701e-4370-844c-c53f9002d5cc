package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.logistics.contract.client.constants.LogisticsOpSourceEnum;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.LogisticsOperatorDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SinglePoiGetSignDataRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.contractplatform.SinglePoiGetTechFeeSignRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.newchannel.BatchSignItemNewChannel;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.newchannel.ConfirmSignByChannelRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.newchannel.ManualBatchSignByChannelRequestDTO;
import com.sankuai.meituan.waimai.logistics.contract.client.dto.newchannel.SignItemNewChannel;
import com.sankuai.meituan.waimai.logistics.contract.client.exception.WmLogisticsContractException;
import com.sankuai.meituan.waimai.logistics.contract.client.service.contractplatform.WmContractPlatformThriftService;
import com.sankuai.meituan.waimai.logistics.contract.client.service.newchannel.WmLogisticsNewChannelThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCallbackBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配送新合同服务
 * <p>
 * Created by lixuepeng on 2023/5/23
 */
@Slf4j
@Service
public class WmLogisticsContractThriftServiceAdapter {

    @Autowired
    private WmContractPlatformThriftService wmContractPlatformThriftService;

    @Autowired
    private WmLogisticsNewChannelThriftService wmLogisticsNewChannelThriftService;

    public static final Long OP_UID = 0L;

    public static final String OP_UNAME = "商家处理";

    public String getSignDataMultiPoiWithRetry(List<EcontractDataPoiBizBo> econtractDataPoiBizBoList, boolean areaSplit) throws WmCustomerException {
        if (CollectionUtils.isEmpty(econtractDataPoiBizBoList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "参数不合法，门店ID-数据来源业务ID列表为空");
        }
        int retryTimes = MccConfig.queryEcontractDataRetryTimes();
        for (int i = 0; i < retryTimes; i++) {
            try {
                List<SinglePoiGetSignDataRequestDTO> requestDTOList = econtractDataPoiBizBoList.stream().map(x -> {
                    SinglePoiGetSignDataRequestDTO requestDTO = new SinglePoiGetSignDataRequestDTO();
                    requestDTO.setWmPoiId(x.getWmPoiId());
                    requestDTO.setSessionId(x.getBizId());
                    requestDTO.setAreaSplit(areaSplit);
                    return requestDTO;
                }).collect(Collectors.toList());
                log.info("#getSignDataMultiPoiWithRetry requestDTOList:{}", JSONObject.toJSONString(requestDTOList));
                String result = wmContractPlatformThriftService.getSignDataMultiPoi(requestDTOList);
                log.info("#getSignDataMultiPoiWithRetry result:{}", result);
                return result;
            } catch (TException ex) {
                // 捕获到超时异常则重试
                log.error("#getSignDataMultiPoiWithRetry 获取签约数据异常 econtractDataPoiBizBoList:{} areaSplit:{}", econtractDataPoiBizBoList, areaSplit, ex);
            } catch (WmLogisticsContractException e) {
                log.error("#getSignDataMultiPoiWithRetry 获取签约数据异常 econtractDataPoiBizBoList:{} areaSplit:{}", econtractDataPoiBizBoList, areaSplit, e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "获取签约数据异常");
            }
        }
        return null;
    }

    public String getTechFeeSignDataMultiPoiWithRetry(List<EcontractDataPoiBizBo> econtractDataPoiBizBoList) throws WmCustomerException {
        if (CollectionUtils.isEmpty(econtractDataPoiBizBoList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "参数不合法，门店ID-数据来源业务ID列表为空");
        }
        int retryTimes = MccConfig.queryEcontractDataRetryTimes();
        for (int i = 0; i < retryTimes; i++) {
            try {
                List<SinglePoiGetTechFeeSignRequestDTO> requestDTOList = econtractDataPoiBizBoList.stream().map(x -> {
                    SinglePoiGetTechFeeSignRequestDTO requestDTO = new SinglePoiGetTechFeeSignRequestDTO();
                    requestDTO.setWmPoiId(x.getWmPoiId());
                    requestDTO.setSessionId(x.getBizId());
                    return requestDTO;
                }).collect(Collectors.toList());
                log.info("#getTechFeeSignDataMultiPoi requestDTOList:{}", JSONObject.toJSONString(requestDTOList));
                String result = wmContractPlatformThriftService.getTechFeeSignDataMultiPoi(requestDTOList);
                log.info("#getTechFeeSignDataMultiPoiWithRetry result:{}", result);
                return result;
            } catch (TException ex) {
                // 捕获到超时异常则重试
                log.error("getTechFeeSignDataMultiPoiWithRetry TException 获取技术服务费和基本信息签约数据异常 econtractDataPoiBizBoList:{}", econtractDataPoiBizBoList, ex);
            } catch (WmLogisticsContractException e) {
                log.error("#getTechFeeSignDataMultiPoiWithRetry WmLogisticsContractException 获取技术服务费和基本信息签约数据异常 econtractDataPoiBizBoList:{}", econtractDataPoiBizBoList, e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "获取签约数据异常");
            }
        }
        return null;
    }

    public String getTechFeeSignDataMultiPoiWithRetry(List<EcontractDataPoiBizBo> econtractDataPoiBizBoList, EcontractTaskApplyTypeEnum applyTypeEnum) throws WmCustomerException {
        if (CollectionUtils.isEmpty(econtractDataPoiBizBoList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "参数不合法，门店ID-数据来源业务ID列表为空");
        }
        int retryTimes = MccConfig.queryEcontractDataRetryTimes();
        for (int i = 0; i < retryTimes; i++) {
            Transaction transactionOnce = Cat.newTransaction("phf.sign", "techQuery_once");
            try {
                List<SinglePoiGetTechFeeSignRequestDTO> requestDTOList = econtractDataPoiBizBoList.stream().map(x -> {
                    SinglePoiGetTechFeeSignRequestDTO requestDTO = new SinglePoiGetTechFeeSignRequestDTO();
                    requestDTO.setWmPoiId(x.getWmPoiId());
                    requestDTO.setSessionId(x.getBizId());
                    requestDTO.setApplyType(applyTypeEnum.getName());
                    return requestDTO;
                }).collect(Collectors.toList());
                log.info("#getTechFeeSignDataMultiPoi requestDTOList:{}", JSONObject.toJSONString(requestDTOList));
                String result = wmContractPlatformThriftService.getTechFeeSignDataMultiPoi(requestDTOList);
                log.info("#getTechFeeSignDataMultiPoiWithRetry result:{}", result);
                transactionOnce.setSuccessStatus();
                return result;
            } catch (TException ex) {
                // 捕获到超时异常则重试
                log.error("getTechFeeSignDataMultiPoiWithRetry TException 获取技术服务费和基本信息签约数据异常 econtractDataPoiBizBoList:{}", econtractDataPoiBizBoList, ex);
            } catch (WmLogisticsContractException e) {
                log.error("#getTechFeeSignDataMultiPoiWithRetry WmLogisticsContractException 获取技术服务费和基本信息签约数据异常 econtractDataPoiBizBoList:{}", econtractDataPoiBizBoList, e);
                transactionOnce.setStatus("fail");
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "获取签约数据异常");
            } finally {
                transactionOnce.complete();
            }
        }
        return null;
    }

    /**
     * 取消待打包任务
     *
     * @param taskDB
     * @param opUid
     * @throws WmCustomerException
     */
    public void cancelManualWaitSignByChannel(WmEcontractSignManualTaskDB taskDB, int opUid) throws WmCustomerException {
        try {
            ManualBatchSignByChannelRequestDTO request = new ManualBatchSignByChannelRequestDTO();
            BatchSignItemNewChannel itemNewChannel = new BatchSignItemNewChannel();
            itemNewChannel.setBizId(taskDB.getWmPoiId());
            itemNewChannel.setManualConfirmId(taskDB.getId());
            itemNewChannel.setCustomerId(taskDB.getCustomerId());
            itemNewChannel.setApplyContext(taskDB.getApplyContext());
            request.setBatchSignItemList(Collections.singletonList(itemNewChannel));
            request.setBatchManualConfirmId(0L);
            request.setApplyTypeEnum(taskDB.getModule());
            LogisticsOperatorDTO operatorDTO = LogisticsOperatorDTO.builder().opId((long) opUid).opName("电子合同系统").opSource(LogisticsOpSourceEnum.CUSTOMER_SYSTEM).build();
            request.setOperatorDTO(operatorDTO);
            log.info("#cancelManualWaitSignByChannel request:{}", JSONObject.toJSONString(request));
            wmLogisticsNewChannelThriftService.cancelManualWaitSignByChannel(request);
        } catch (WmLogisticsContractException e) {
            log.error("#cancelManualWaitSignByChannel 取消待打包任务异常 taskDB:{} opUid:{}", JSON.toJSONString(taskDB), opUid, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMsg());
        }
    }

    /**
     * 判断是否可以发起打包任务
     *
     * @param taskDBList
     * @param applyType
     * @param opUid
     * @throws WmCustomerException
     */
    public void trySignManualPackageInfoByChannel(List<WmEcontractSignManualTaskDB> taskDBList, String applyType, int opUid) throws WmCustomerException {
        try {
            ManualBatchSignByChannelRequestDTO request = new ManualBatchSignByChannelRequestDTO();
            List<BatchSignItemNewChannel> itemNewChannelList = new ArrayList<>();
            taskDBList.forEach(taskDB -> {
                BatchSignItemNewChannel itemNewChannel = new BatchSignItemNewChannel();
                itemNewChannel.setBizId(taskDB.getWmPoiId());
                itemNewChannel.setManualConfirmId(taskDB.getId());
                itemNewChannel.setCustomerId(taskDB.getCustomerId());
                itemNewChannel.setApplyContext(taskDB.getApplyContext());
                itemNewChannelList.add(itemNewChannel);
            });
            request.setBatchSignItemList(itemNewChannelList);
            request.setBatchManualConfirmId(0l);
            request.setApplyTypeEnum(applyType);
            LogisticsOperatorDTO operatorDTO = LogisticsOperatorDTO.builder().opId((long) opUid).opName("电子合同系统").opSource(LogisticsOpSourceEnum.CUSTOMER_SYSTEM).build();
            request.setOperatorDTO(operatorDTO);
            log.info("#trySignManualPackageInfoByChannel request:{}", JSONObject.toJSONString(request));
            wmLogisticsNewChannelThriftService.trySignManualPackageInfoByChannel(request);
        } catch (WmLogisticsContractException e) {
            log.error("#trySignManualPackageInfoByChannel 校验是否可以发起签约 taskDBList:{} opUid:{}", JSON.toJSONString(taskDBList), opUid, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMsg());
        }
    }

    /**
     * 发起打包任务
     *
     * @param taskDBList
     * @param applyType
     * @param opUid
     * @throws WmCustomerException
     */
    public void manualBatchApplySignByChannel(List<WmEcontractSignManualTaskDB> taskDBList, long manualBatchId, String applyType, int opUid) throws WmCustomerException {
        try {
            ManualBatchSignByChannelRequestDTO request = new ManualBatchSignByChannelRequestDTO();
            List<BatchSignItemNewChannel> itemNewChannelList = new ArrayList<>();
            taskDBList.forEach(taskDB -> {
                BatchSignItemNewChannel itemNewChannel = new BatchSignItemNewChannel();
                itemNewChannel.setBizId(taskDB.getWmPoiId());
                itemNewChannel.setManualConfirmId(taskDB.getId());
                itemNewChannel.setCustomerId(taskDB.getCustomerId());
                itemNewChannel.setApplyContext(taskDB.getApplyContext());
                itemNewChannelList.add(itemNewChannel);
            });
            request.setBatchSignItemList(itemNewChannelList);
            request.setBatchManualConfirmId(manualBatchId);
            request.setApplyTypeEnum(applyType);
            LogisticsOperatorDTO operatorDTO = LogisticsOperatorDTO.builder().opId((long) opUid).opName("电子合同系统").opSource(LogisticsOpSourceEnum.CUSTOMER_SYSTEM).build();
            request.setOperatorDTO(operatorDTO);
            log.info("#manualBatchApplySignByChannel request:{}", JSONObject.toJSONString(request));
            wmLogisticsNewChannelThriftService.manualBatchApplySignByChannel(request);
        } catch (WmLogisticsContractException e) {
            log.error("#trySignManualPackageInfoByChannel 校验是否可以发起签约 taskDBList:{} opUid:{}", JSON.toJSONString(taskDBList), opUid, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMsg());
        }
    }

    /**
     * 确认签约
     *
     * @param callbackBo
     * @param taskBo
     * @throws WmCustomerException
     */
    public void confirmSignByChannel(EcontractCallbackBo callbackBo, EcontractTaskBo taskBo) throws WmCustomerException {
        try {
            ConfirmSignByChannelRequestDTO request = new ConfirmSignByChannelRequestDTO();
            SignItemNewChannel signItemNewChannel = new SignItemNewChannel();
            signItemNewChannel.setConfirmId(taskBo.getId());
            signItemNewChannel.setPdfUrl(callbackBo.getPdfUrl());
            signItemNewChannel.setBizId((long) taskBo.getBizId());
            request.setBatchSignItem(signItemNewChannel);
            LogisticsOperatorDTO operatorDTO = LogisticsOperatorDTO.builder().opId(OP_UID).opName(OP_UNAME).opSource(LogisticsOpSourceEnum.CUSTOMER_SYSTEM).build();
            request.setOperatorDTO(operatorDTO);
            request.setApplyTypeEnum(taskBo.getApplyType());
            log.info("#confirmSignByChannel request:{}", JSONObject.toJSONString(request));
            wmLogisticsNewChannelThriftService.confirmSignByChannel(request);
        } catch (WmLogisticsContractException e) {
            log.error("#confirmSignByChannel 确认签约 callbackBo:{} taskBo:{}", JSON.toJSONString(callbackBo), JSON.toJSONString(taskBo), e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMsg());
        }
    }

    /**
     * 取消签约
     *
     * @param callbackBo
     * @param taskBo
     */
    public void cancelSignByChannel(EcontractCallbackBo callbackBo, EcontractTaskBo taskBo) throws WmCustomerException {
        try {
            ConfirmSignByChannelRequestDTO request = new ConfirmSignByChannelRequestDTO();
            SignItemNewChannel signItemNewChannel = new SignItemNewChannel();
            signItemNewChannel.setConfirmId(taskBo.getId());
            signItemNewChannel.setPdfUrl(callbackBo.getPdfUrl());
            signItemNewChannel.setBizId((long) taskBo.getBizId());
            request.setBatchSignItem(signItemNewChannel);
            LogisticsOperatorDTO operatorDTO = LogisticsOperatorDTO.builder().opId(OP_UID).opName(OP_UNAME).opSource(LogisticsOpSourceEnum.CUSTOMER_SYSTEM).build();
            request.setOperatorDTO(operatorDTO);
            request.setApplyTypeEnum(taskBo.getApplyType());
            log.info("#cancelSignByChannel request:{}", JSONObject.toJSONString(request));
            wmLogisticsNewChannelThriftService.cancelSignByChannel(request);
        } catch (WmLogisticsContractException e) {
            log.error("#cancelSignByChannel 取消签约 callbackBo:{} taskBo:{}", JSON.toJSONString(callbackBo), JSON.toJSONString(taskBo), e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMsg());
        }
    }
}
