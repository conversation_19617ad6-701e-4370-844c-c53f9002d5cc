/*
* Copyright (c) 2016 meituan.com. All Rights Reserved.
*/
package com.sankuai.meituan.waimai.customer.settle.service.paycenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.MoreObjects;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.pay.mwallet.proxy.thrift.MwalletProxyService;
import com.meituan.pay.mwallet.proxy.thrift.entity.auth.VerifyBankCardReqTo;
import com.meituan.pay.mwallet.proxy.thrift.entity.auth.VerifyBankCardResTo;
import com.meituan.pay.mwallet.proxy.thrift.entity.auth.VerifyBankCardSignResTo;
import com.meituan.pay.mwallet.proxy.util.BeanToMapUtil;
import com.meituan.pay.mwallet.util.SignAndEncUtil;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meituan.waimai.customer.adapter.CMSThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleLogService;
import com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.m.util.HttpUtil;
import com.sankuai.meituan.waimai.m.util.MD5Util;
import com.sankuai.meituan.waimai.thrift.config.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticeMsgFormatEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticeMustReadEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticePushTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.SMSConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmCardValidationInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.service.WmSettleManagerThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.WmSettleThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.dto.WmNoticeDto;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.ParseException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.*;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_NAME;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_UID;

/**
 * Desc:
 * ------------------------------------
 * Author:<EMAIL>
 * Date:16/9/9
 * Time:下午3:30
 */
@Service
public class WmBankCardValidationService {

    private static final Logger log = LoggerFactory.getLogger(WmBankCardValidationService.class);
    private final static ExecutorService refreshCacheService =  new ThreadPoolExecutor(10, 200,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue(1024), new ThreadPoolExecutor.AbortPolicy());

    final static String VALIDATE_URL = "/api/verifyCard/verify";
    final static String QUERY_URL = "/api/verifyCard/query";
    final static String BATCH_QUERY_URL = "/api/op/verifyCard/batchQuery";
    final static String CALLBACK_URL = "/customer/v1/callback/settle/payCenter/bankCardValidate";
    final static String BU_NAME = "waimai";
    final static String KEY_STR = "823b286f241ac50b77c64ded264ba8c4";

    final static String SUCCESS = "success"; // 校验成功
    final static String FAILED = "failed"; // 失败
    final static String VALIDATING = "validating"; //校验中

    final static String VALIDATE_TYPE_CUSTOMER = "2";
    public static final String WALLET_APP_KEY = "com.sankuai.waimai.contractmanager";
    public static final String WALLET_PRIVATE_KEY = "mwallet.mcertify.rsa.private.key";
    //支付商户号
    public static final String POI_IPHPAYMERCHANTNO_KEY = "poi_iphpaymerchantno";
    public static final long DEFAULT_IPHPAYMERCHANTNO = 11000000538606L;

    final static String MESSAGE_TO_BD =
            "【银行卡信息错误】您录入的结算（客户ID%s，结算ID %s，门店ID %s等）银行卡信息未通过一分钱打款校验，请及时改正以免影响商家打款；失败原因是：%s";
    final static String SUCCESS_OP_LOG = "结算id %s （银行卡：%s）一分钱打款成功，该结算信息可用";
    final static String FAILED_OP_LOG = "结算id %s（银行卡：%s） 一分钱打款失败，该结算信息不可用，原因是：%s";
    final static String TITLE = "结算验卡";

    final static String CARD_SUCCESS = "【钱包操作】钱包一分钱验卡：成功     结算ID：%d";
    final static String CARD_FAIED = "【钱包操作】钱包一分钱验卡：失败      结算ID：%d\n  失败原因：%s";

    private static final Set<String> WM_POI_FIELDS_AGGRE_WM_POI_FIELD_NAME = ImmutableSet.of(WM_POI_FIELD_NAME, WM_POI_FIELD_OWNER_UID);

    @Autowired
    private WmEmployService.Iface wmEmployService;

    @Autowired
    private WmSettleThriftService wmSettleThriftService;

    @Autowired
    private WmSettleManagerThriftService wmSettleManagerThriftService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmSettleLogService wmSettleLogService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    @Autowired
    private MwalletProxyService.Iface mwallletProxyClient;
    @Autowired
    private WmCrmNoticeService  wmCrmNoticeService;

    @Autowired
    private EmpServiceAdaptor empServiceAdaptor;

    @Autowired
    private CMSThriftServiceAdapter cmsThriftServiceAdapter;

    public WmCardValidationInfoBo query(WmSettle settle) {

        WmCardValidationInfoBo cardValidationInfoBo = new WmCardValidationInfoBo();
        String serialNum = genSerialNum(settle);
        Map<String, String> params = Maps.newHashMap();
        params.put("partner", BU_NAME);
        params.put("serialNo", serialNum);
        try {
            String cardValidateHost = ConfigUtilAdapter.getString("bankcard.validate.host");
            log.info("请求支付中心查询验卡结果,url={},params={},settle={}", cardValidateHost + QUERY_URL,
                    JSON.toJSONString(params), JSON.toJSON(settle));
            String result = HttpUtil.get(cardValidateHost + QUERY_URL, "partner=" + BU_NAME + "&serialNo=" + serialNum);
            log.info("支付中心返回信息,result={},contractId={},settleId={}", result, settle.getWm_contract_id(), settle.getId());
            Map<String, Object> resultMap = JSON.parseObject(result);
            if ("success".equals(resultMap.get("status"))) {
                cardValidationInfoBo = parseAndSetResult(settle, resultMap.get("data"));
            } else {
                cardValidationInfoBo.setCardStatus(VALIDATING);
            }
        } catch (IOException e) {
            log.error("请求支付中心失败,contractId={},settleId={}", settle.getWm_contract_id(), settle.getId(), e);
            return cardValidationInfoBo;
        } catch (ParseException e) {
            log.error("请求支付中心失败,解析异常.contractId={},settleId={}", settle.getWm_contract_id(), settle.getId(), e);
            return cardValidationInfoBo;
        } catch (Exception e) {
            log.error("请求支付中心失败,未知异常.contractId={},settleId={}", settle.getWm_contract_id(), settle.getId(), e);
            return cardValidationInfoBo;
        }

        return cardValidationInfoBo;
    }

    private String genSerialNum(WmSettle settle) {
        // 客户Id_结算Id_MD5(银行卡卡号_银行卡开户名_银行Id_所在城市Id_支行名称)

        String sourceMd5 = new StringBuilder("").append(settle.getAcc_cardno())
                .append("_").append(settle.getAcc_name())
                .append("_").append(settle.getBankid())
                .append("_").append(settle.getCity())
                .append("_").append(settle.getBranchname())
                .append("_").append(settle.getAcctype())
                .toString();
        return settle.getWmCustomerId() + "_" + settle.getId() + "_"
                            + MD5Util.generateCheckString(sourceMd5);
    }


    public void validate(WmSettle settle) {
        String serialNum = genSerialNum(settle);
        Map<String, String> params = Maps.newHashMap();
        params.put("partner", BU_NAME);
        params.put("serialNo", serialNum);
        params.put("cardno", settle.getAcc_cardno());
        params.put("name", settle.getAcc_name());
        params.put("bankid", String.valueOf(settle.getBankid()));
        params.put("cityid", String.valueOf(settle.getCity()));
        params.put("branchid", "0");
        params.put("branchname", settle.getBranchname());
        params.put("notifyurl", ConfigUtilAdapter.getString("customer_host_inner") + CALLBACK_URL);
        params.put("acctype", String.valueOf(settle.getAcctype()));
        params.put("sign", getMd5SignStringObject(params));
        try {
            String cardValidateHost = ConfigUtilAdapter.getString("bankcard.validate.host");
            log.info("请求支付中心验卡,url={},params={}", cardValidateHost + VALIDATE_URL,
                JSON.toJSONString(params));
            String result = HttpUtil.post(cardValidateHost + VALIDATE_URL, params);
            log.info("支付中心验卡同步返回信息,result={},contractId={},settleId={}", result,
                settle.getWm_contract_id(), settle.getId());
            Map<String, Object> resultMap = JSON.parseObject(result);
            switch (resultMap.get("status").toString()) {
                case "success":
                    break;
                case "to_be_confirmed":
                    break;
                case "fail":
                    Map<String, Object> errorInfo = JSON
                        .parseObject(resultMap.get("error").toString());
                    WmCustomerDB wmCustomerDB = wmCustomerService
                        .selectCustomerById(settle.getWmCustomerId());
                    if (wmCustomerDB == null || wmCustomerDB.getOwnerUid() == 0) {
                        break;
                    }
                    WmEmploy wmEmploy = wmEmployService.getById(wmCustomerDB.getOwnerUid());
                    Integer poiId = CollectionUtils.isEmpty(settle.getWmPoiIdList()) ? 0
                        : settle.getWmPoiIdList().get(0);

                    String content=String.format(MESSAGE_TO_BD,wmCustomerDB.getId(), settle.getId(), poiId, errorInfo
                            .get("message"));
                    wmCrmNoticeService.bankInfoError(String.valueOf(wmEmploy.getUid()),wmCustomerDB.getId(),
                            null,content);

                    String poiName = "";
                    String ownerName = "";
                    String ownerPhone = "";
                    if(poiId>0) {
                        WmPoiAggre wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(poiId.intValue(), WM_POI_FIELDS_AGGRE_WM_POI_FIELD_NAME);
                        if(wmPoiAggre!=null){
                            poiName = wmPoiAggre.getName();
                            long ownerUid = wmPoiAggre.getOwner_uid();
                            WmEmploy wmEmployOwner = wmEmployService.getById((int) ownerUid);
                            if(wmEmployOwner!=null){
                                ownerPhone = MoreObjects.firstNonNull(empServiceAdaptor.getPhone((int)ownerUid),"");
                                ownerName = wmEmployOwner.getName();
                            }
                        }
                    }

                    List<String> receiverList = new ArrayList<>();
                    receiverList.add(settle.getParty_a_finance_phone());//结算信息中的财务联系手机号
                    WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(settle.getWmCustomerId());
                    if(wmCustomerKp!=null) {
                        receiverList.add(wmCustomerKp.getPhoneNum());//客户KP签约人手机号
                    }
                    String testUsers = MccConfig.getTestSmsUser();
                    if (StringUtils.isNotEmpty(testUsers)) {
                        receiverList = Lists.newArrayList(testUsers.split(","));
                    }
                    Map<String, Object> smsParamMap = Maps.newHashMap();
                    smsParamMap.put("poi_name", poiName);
                    smsParamMap.put("poi_id", poiId);
                    smsParamMap.put("reason", errorInfo.get("message"));
                    smsParamMap.put("bd_name", ownerName);
                    smsParamMap.put("phone", ownerPhone);
                    sendSms(receiverList, smsParamMap);
                    String opLogMsg = String
                        .format(FAILED_OP_LOG, settle.getId(), settle.getAcc_cardno(),
                            errorInfo.get("message"));
                    wmSettleLogService
                        .insertWmSettleLog(wmCustomerDB.getId(), WmCustomerOplogBo.OpType.UPDATE, 0,
                            "系统", opLogMsg);
                    wmSettleManagerThriftService.updateWmSettleCardValid(settle.getWmCustomerId(), settle.getId(), 1, (String) errorInfo.get("message"));
                    break;
                default:
                    log.error("支付中心同步返回异常信息,成功返回,但status={},result={},contractId={},settleId={}",
                        resultMap.get("status").toString(), result, settle.getWm_contract_id(),
                        settle.getId());
            }
        } catch (IOException e) {
            log.error("请求支付中心同步返回失败,contractId={},settleId={}", settle.getWm_contract_id(),
                settle.getId(), e);
        } catch (ParseException e) {
            log.error("请求支付中心同步返回失败,解析异常.contractId={},settleId={}", settle.getWm_contract_id(),
                settle.getId(), e);
        } catch (Exception e) {
            log.error("请求支付中心同步返回失败,未知异常.contractId={},settleId={}", settle.getWm_contract_id(),
                settle.getId(), e);
        }
    }


    /**
     * 构造登月提醒消息结构体
     */
    private WmNoticeDto createWmNoticeDto(String receiveUids, String tittle, String message){
        WmNoticeDto wmNoticeDto = new WmNoticeDto();
        wmNoticeDto.setUid(0);
        wmNoticeDto.setUname("先富系统");
        wmNoticeDto.setPushType(WmNoticePushTypeEnum.PUSH_TYPE_LIST.getValue());
//        wmNoticeDto.setOrgStruct();无需设定
        wmNoticeDto.setReceiveUids(receiveUids);
        wmNoticeDto.setMsgFormat(WmNoticeMsgFormatEnum.MSG_FORMAT_NORMAL.getValue());
        JSONObject jb = new JSONObject();
        jb.put("title", StringUtils.isNotBlank(tittle)?"【"+ tittle +"】先富系统消息,点击请查看详情~":"先富系统消息,点击请查看详情~");
        jb.put("content", message);
        JSONArray jr = new JSONArray();
        jr.add(jb);
        wmNoticeDto.setMsg(jr.toJSONString());
        wmNoticeDto.setWmNoticeTypeId(com.sankuai.meituan.util.ConfigUtilAdapter.getInt("dengyue.message.notice.typeid", 73));
        wmNoticeDto.setMustRead(WmNoticeMustReadEnum.NO.getValue());
        wmNoticeDto.setPushMedia(com.sankuai.meituan.util.ConfigUtilAdapter.getString("dengyue.message.push.media", "2,3"));//默认是不发大象的
        wmNoticeDto.setValid((byte)1);
        log.info("createWmNoticeDto, wmNoticeDto={}", wmNoticeDto);
        return wmNoticeDto;

    }

    /**
     * 给商家发送登月提醒短信
     */
    private void sendSms(List<String> receiverList, Map<String, Object> smsParamMap){
        List<String> sendList = Lists.newArrayList();
        Map<String,String> params = Maps.newHashMap();
        if(MapUtils.isNotEmpty(smsParamMap)){
            for(Map.Entry<String,Object> entry: smsParamMap.entrySet()){
                params.put(entry.getKey(),String.valueOf(entry.getValue()));
            }
        }

        for(String mobile : receiverList){
            if(StringUtils.isBlank(mobile) || sendList.contains(mobile)){
                continue;
            }

            try {
                cmsThriftServiceAdapter.sendMessage(mobile, "", SMSConstant.TEMPLATE_DEGNYUE_C1, params);
            } catch (Exception e) {
                log.warn("sendSms error", e);
            }
            sendList.add(mobile);
        }

    }
    public List<WmSettle> batchQueryAndSet(List<WmSettle> settleList) {

        if (CollectionUtils.isEmpty(settleList)) {
            return settleList;
        }

        if(ConfigUtilAdapter.getBoolean("query_bankcard_validate_close",false)){
            for(WmSettle temp : settleList){
                WmSettleTransUtil.assembleWmCardValidationInfoBo(temp);
            }
            return settleList;
        }

        Map<String, WmSettle> serialNoSettleMap = Maps.newHashMap();
        for (WmSettle settle : settleList) {
            serialNoSettleMap.put(genSerialNum(settle), settle);
        }

        // 请求支付中心
        Joiner joiner = Joiner.on(",");
        String serialNumsStr = joiner.join(serialNoSettleMap.keySet());

        Map<String, String> params = Maps.newHashMap();
        params.put("partner", BU_NAME);
        params.put("serialNums", serialNumsStr);
        try {
            String cardValidateHost = ConfigUtilAdapter.getString("bankcard.validate.host");
            log.info("请求支付中心查询验卡结果(批量),url={},params={}", cardValidateHost + BATCH_QUERY_URL,
                    JSON.toJSONString(params));
            String result = HttpUtil.get(cardValidateHost + BATCH_QUERY_URL, "partner=" + BU_NAME + "&serialNos=" + serialNumsStr);
            log.info("支付中心同步返回信息(批量),result={},contractId={}", result, settleList.get(0).getWm_contract_id());
            Map<String, Object> resultMap = JSON.parseObject(result);
            if ("success".equals(resultMap.get("status"))) {
                Map<String, Object> resultDate = JSON.parseObject(resultMap.get("data").toString());
                if (MapUtils.isNotEmpty(resultDate)) {
                    for (Map.Entry<String, WmSettle> entry : serialNoSettleMap.entrySet()) {
                        entry.getValue().setCardValidationInfoBo(parseAndSetResult(entry.getValue(), resultDate.get(entry.getKey())));
                    }
                }
            }
        } catch (IOException e) {
            log.error("请求支付中心同步返回失败(批量),contractId={}", settleList.get(0).getWm_contract_id(), e);
        } catch (ParseException e) {
            log.error("请求支付中心同步返回失败(批量),解析异常.contractId={}", settleList.get(0).getWm_contract_id(), e);
        } catch (Exception e) {
            log.error("请求支付中心同步返回失败(批量),未知异常.contractId={}", settleList.get(0).getWm_contract_id(), e);
        }

        return settleList;
    }

    public void validateCallbackByThrift(int wmSettleId,String status,String reason) throws TException, WmCustomerException{
        WmSettle wmSettle = wmSettleThriftService.getWmSettleByWmSettleId(wmSettleId);
        if(wmSettle == null){
            return;
        }
        int customerId = wmSettle.getWmCustomerId();
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        validateCallbackCommon(status, reason, wmCustomerDB, wmSettle);
    }

    public void validateCallback(String partner, String serialNo, String status, String reason) throws TException, WmCustomerException {
        Splitter splitter = Splitter.on("_");
        List<String> serialNoList = Lists.newArrayList(splitter.split(serialNo));
        if (!partner.equals("waimai") || serialNoList.size() != 3) {
            log.error("支付中心回调异常,partner={},serialNo={}", partner, serialNo);
        }
        int customerId = Integer.valueOf(serialNoList.get(0));
        int settleId = Integer.valueOf(serialNoList.get(1));
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        WmSettle wmSettle = wmSettleThriftService.getWmSettleByWmSettleId(settleId);
        if(wmCustomerDB == null || wmSettle == null){
            return;
        }
        validateCallbackCommon(status, reason, wmCustomerDB, wmSettle);
    }

    private void validateCallbackCommon(String status, String reason, WmCustomerDB wmCustomerDB, WmSettle wmSettle)
            throws WmCustomerException, TException {
        int customerId = wmCustomerDB.getId();
        int settleId = wmSettle.getId();
        String opLogMsg;
        String cardMsg;
        if (status.equals("success")) {
            opLogMsg = String.format(SUCCESS_OP_LOG, wmSettle.getId(), wmSettle.getAcc_cardno());
            cardMsg = String.format(CARD_SUCCESS, wmSettle.getId());
            wmSettleManagerThriftService.updateWmSettleCardValid(customerId, settleId, 0, "" );
            log.info("支付中心回调信息,验证成功.customerId={},settleId={}", customerId, settleId);
        } else {
            log.info("支付中心回调信息,验证失败.customerId={},settleId={}", customerId, settleId);
            wmSettleManagerThriftService.updateWmSettleCardValid(customerId, settleId, 1, reason );
            opLogMsg = String
                .format(FAILED_OP_LOG, wmSettle.getId(), wmSettle.getAcc_cardno(), reason);
            cardMsg = String.format(CARD_FAIED, wmSettle.getId(), reason);
            WmEmploy wmEmploy = null;
            try {
                wmEmploy = wmEmployService.getById(wmCustomerDB.getOwnerUid());
                if(wmEmploy!=null){
                    Integer poiId = CollectionUtils.isEmpty(wmSettle.getWmPoiIdList()) ? 0
                            : wmSettle.getWmPoiIdList().get(0);
                    String poiName = "";
                    String ownerName = "";
                    String ownerPhone = "";
                    if(poiId>0) {
                        WmPoiAggre wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(poiId.intValue(), WM_POI_FIELDS_AGGRE_WM_POI_FIELD_NAME);
                        if(wmPoiAggre!=null){
                            long ownerUid = wmPoiAggre.getOwner_uid();
                            poiName = wmPoiAggre.getName();
                            WmEmploy wmEmployOwner = wmEmployService.getById((int) ownerUid);
                            if(wmEmployOwner!=null){//非空判断
                                ownerName = wmEmployOwner.getName();
                                ownerPhone = MoreObjects.firstNonNull(empServiceAdaptor.getPhone((int)ownerUid),"");
                            }
                        }
                    }

                    String content=String.format(MESSAGE_TO_BD,customerId, settleId, poiId, reason);
                    wmCrmNoticeService.bankInfoError(String.valueOf(wmEmploy.getUid()),wmCustomerDB.getId(),
                            null,content);

                    List<String> receiverList = new ArrayList<>();
                    receiverList.add(wmSettle.getParty_a_finance_phone());//结算信息中的财务联系手机号
                    WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(wmSettle.getWmCustomerId());
                    if(wmCustomerKp!=null) {
                        receiverList.add(wmCustomerKp.getPhoneNum());//客户KP签约人手机号
                    }
                    String testUsers = MccConfig.getTestSmsUser();
                    if (StringUtils.isNotEmpty(testUsers)) {
                        receiverList = Lists.newArrayList(testUsers.split(","));
                    }
                    Map<String, Object> smsParamMap = Maps.newHashMap();
                    smsParamMap.put("poi_name", poiName);
                    smsParamMap.put("poi_id", poiId);
                    smsParamMap.put("reason", reason);
                    smsParamMap.put("bd_name", ownerName);
                    smsParamMap.put("phone", ownerPhone);
                    sendSms(receiverList, smsParamMap);
                }
            } catch (Exception e) {
                log.error("验卡失败通知BD异常.customerId={}", customerId, e);
            }
        }

//        wmSettleLogService
//            .insertWmSettleLog(wmCustomerDB.getId(), WmCustomerOplogBo.OpType.UPDATE, 0,
//                "系统", opLogMsg);
        wmSettleLogService
            .insertWmSettleLog(wmCustomerDB.getId(), WmCustomerOplogBo.OpType.WALLET, 0,
                "系统", cardMsg);
    }

    private WmCardValidationInfoBo parseAndSetResult(WmSettle settle, Object object) {
        if (null == object) {
            return null;
        }
        Map<String, Object> resultDate = JSON.parseObject(object.toString());
        WmCardValidationInfoBo cardValidationInfoBo = new WmCardValidationInfoBo();
        cardValidationInfoBo.setCardStatus(VALIDATING);  // 默认校验中
        switch (resultDate.get("status").toString()) {
            case "S":
                cardValidationInfoBo.setCardStatus(SUCCESS);
                break;
            case "N":
                break;
            case "F":
                cardValidationInfoBo.setCardStatus(FAILED);
                cardValidationInfoBo.setFailedReason(resultDate.get("reason").toString());
                break;
            default:
                log.error("支付中心返回异常信息,成功返回,但status={},contractId={},settleId={}",
                        resultDate.get("status").toString(), settle.getWm_contract_id(), settle.getId());
        }

        return cardValidationInfoBo;
    }


    public void batchValidate(List<WmSettle> settleList) {

        if (CollectionUtils.isEmpty(settleList)) {
            return;
        }

        for (WmSettle settle : settleList) {
            if(!ConfigUtilAdapter.getBoolean("settleCard_validate_use_thrift_open",false)){
                validate(settle);
            }else{
                validateCustomerCardByThrift(settle);
            }
        }
    }

    private void validateCustomerCardByThrift(WmSettle settle) {
        if (settle == null || settle.getId() == 0) {
            return;
        }
        long bizSerialNo = genBizSerialNoForCustomer(settle);
        //业务线私钥,签名和解密用
        String privateKey = null;
        try {
            privateKey = Kms.getByName(WALLET_APP_KEY, WALLET_PRIVATE_KEY);
        } catch (KmsResultNullException e) {
            log.error("获取密钥失败, 使用默认密钥。");
        }
        if (StringUtils.isEmpty(privateKey)) {
            log.error("获取密钥失败, 使用默认密钥。");
            privateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKo3fcrgiqUnLdJZ42Zdc0A4i3NfiSuP3iSTMBeeUtiMh9pT6TbjFQmrCbuSPHqoBvvJQQgnQ4U0U3DjBHYOA0m9CZTQC60ooJdWGbrbOtRsbpASRkiOH61f2rJX0lbDtBg7hDrwat8nVKlE/UYMftQRgZmU09KY5NJrXIl3MPU5AgMBAAECgYBIhU/w6e3oI2MgZe++oz4BNJYlV6THjIompxcsWOYPIQCYgnuwSNujvN1urwVZdNFU4Q/1+1SHIh4S0IYMDjhTtgGKooAnzTFPdsi0ieKCHhhPkLK4xdkubgDhs41tEoDS+I4xmLhqCVJujRkTNQ17VrjhzOo8oo73sqOV61t9AQJBAOBh1wMFTmWDwBtxACd9siwl5J1wOXGc+UW21Gpde7OiElDH9GeFzDjw3pHUKA0BAh5T2brO5e+Qm+TwrI3tABECQQDCM7yp4mP+fs7nQwV0r/MaB4WxEeWXdY2xqdHlB5AMLxd9QQ0FrTaP4K9Ty1u8oRQD+SLoq4Sz7medxUlPR0qpAkA2O2+ca0vnfVCRUV6YLOlDgmUfKGC59RbKosX0b2PWpvWGUM3ht5UErjMdTAniGrxIWDvnytoIbQtA6mrKYt4BAkA8okavjB3IYfNbxVM4wY7Xe63EQWA7z9oztF/ycFALXdCprNvR/+jfNznGoeG8nVZQf0Lk/lhqjW8QlX50IEAxAkBEbRFFGCQZFzPvajOti0gI5AzzTk/SunJdpqSldLi70cq37pBDqGqq6uG+xctlajx6i8ZrrJwKMyc4BZoL2c7K";
        }
        //钱包公钥,验签和加密用
        String publicKey = "";
        long iphPayMerchantNo = ConfigUtilAdapter
                .getLong(POI_IPHPAYMERCHANTNO_KEY, DEFAULT_IPHPAYMERCHANTNO);

        //构造请求参数对象
        VerifyBankCardReqTo reqTo = new VerifyBankCardReqTo();
        reqTo.setIphPayMerchantNo(iphPayMerchantNo);
        reqTo.setBizSerialNo(bizSerialNo);
        reqTo.setVerifyMode("STANDARD");
        reqTo.setBankId(settle.getBankid());
        reqTo.setAccountName(settle.getAcc_name());
        reqTo.setBankcardNum(settle.getAcc_cardno());
        reqTo.setAccountType(settle.getAcctype());

        // 将bean转成map， 用于生成签名
        Map<String, String> convertMap = null;
        try {
            convertMap = BeanToMapUtil.convertBeanForSign(reqTo);
        } catch (Exception e) {
            log.error("convert bean to map exception", e);
            return;
        }

        // 生成签名
        String sign = null;
        try {
            sign = SignAndEncUtil.sign(convertMap, privateKey, "UTF-8");
        } catch (Exception e) {
            log.error("SignAndEncUtil#sign#exception", e);
            return;
        }
        reqTo.setSign(sign);
        reqTo.setSignType("RSA");
        log.info("validateCustomerCardByThrift#reqTo={}", reqTo);
        VerifyBankCardSignResTo signResTo = null;
        try {
            signResTo = mwallletProxyClient.verifyBankCard(reqTo);
        } catch (TException e) {
            log.error("verifyBankCard异常", e);
            return;
        }
        log.info("validateCustomerCardByThrift#signResTo={}", JSONObject.toJSONString(signResTo));
        VerifyBankCardResTo resTo = signResTo.getPlain();
        if (resTo == null || "fail".equals(resTo.getStatus())) {
            log.warn("validateCustomerCardByThrift验卡异常");
        }
    }

    private long genBizSerialNoForCustomer(WmSettle settle) {
        StringBuilder sb = new StringBuilder("");
        sb.append(settle.getId()).append(VALIDATE_TYPE_CUSTOMER).append(DateUtil.unixTime());
        return Long.parseLong(sb.toString());
    }


    public void validateCardByCustomerId(int customerId) {
        refreshCacheService.execute(new ValidateCardTask(customerId, wmSettleThriftService, this));
    }


    private String getMd5SignStringObject(Map<String, String> params) {
        Object[] keyarr = params.keySet().toArray();
        Arrays.sort(keyarr);
        String signstr = "";
        for (Object key : keyarr) {
            signstr = signstr + (String)key + "=" + params.get(key) + "&";
        }
        signstr = signstr.substring(0, signstr.length()-1);
        signstr = signstr + "&" + "signkey=" + KEY_STR;
        return getMD5(signstr.getBytes());
    }
    private static String getMD5(byte[] source) {
        String s = null;
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f'};// 用来将字节转换成16进制表示的字符
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            md.update(source);
            byte tmp[] = md.digest();// MD5 的计算结果是一个 128 位的长整数，
            // 用字节表示就是 16 个字节
            char str[] = new char[16 * 2];// 每个字节用 16 进制表示的话，使用两个字符， 所以表示成 16
            // 进制需要 32 个字符
            int k = 0;// 表示转换结果中对应的字符位置
            for (int i = 0; i < 16; i++) {// 从第一个字节开始，对 MD5 的每一个字节// 转换成 16
                // 进制字符的转换
                byte byte0 = tmp[i];// 取第 i 个字节
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];// 取字节中高 4 位的数字转换,// >>>
                // 为逻辑右移，将符号位一起右移
                str[k++] = hexDigits[byte0 & 0xf];// 取字节中低 4 位的数字转换
            }
            s = new String(str);// 换后的结果转换为字符串
        } catch (NoSuchAlgorithmException e) {
            log.error("getMD5 失败", e);
        }
        return s;
    }

    public static void main(String[] args) {
        WmBankCardValidationService wmBankCardValidationService = new WmBankCardValidationService();
        WmSettle settle = new WmSettle();
        settle.setWm_contract_id(1);
        settle.setId(2);
        settle.setAcc_cardno("*********");
        settle.setAcc_name("abc");
        settle.setBankid((short)2);
        settle.setCity(3);
        settle.setBranchname("北京银行望京支行");
        settle.setAcctype((byte) 1);
        wmBankCardValidationService.validate(settle);
        System.out.println("sdkk");
    }
}