package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.wdc.domain.service.common.vo.WdcPoiView;
import com.sankuai.meituan.waimai.wdc.domain.thrift.service.WdcPoiQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

/**
 * WDC门店数据查询服务
 * <AUTHOR>
 * @date 2024/05/31
 * @email <EMAIL>
 */
@Service
@Slf4j
public class WdcPoiQueryServiceAdapter {

    @Autowired
    private WdcPoiQueryService.Iface wdcPoiQueryService;

    /**
     * 根据线索ID查询WDC门店信息
     * @param wdcClueId 线索ID
     * @return WDC门店信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WdcPoiView getWdcPoiViewByWdcClueId(Long wdcClueId) throws WmSchCantException {
        try {
            List<Long> wdcClueIdList = new ArrayList<>();
            wdcClueIdList.add(wdcClueId);
            log.info("[WdcPoiQueryServiceAdapter.getWdcPoiViewByWdcClueId] wdcClueIdList = {}", JSONObject.toJSONString(wdcClueIdList));
            List<WdcPoiView> wdcPoiViewList = wdcPoiQueryService.findWdcPoiViewByWdcIds(wdcClueIdList);
            log.info("[WdcPoiQueryServiceAdapter.getWdcPoiViewByWdcClueId] wdcPoiViewList = {}", JSONObject.toJSONString(wdcPoiViewList));

            if (CollectionUtils.isEmpty(wdcPoiViewList) || wdcPoiViewList.size() > 1) {
                log.error("[WdcPoiQueryServiceAdapter.getWdcPoiViewByWdcClueId] wdcPoiViewList is error. wdcPoiViewList = {}", JSONObject.toJSONString(wdcPoiViewList));
                throw new WmSchCantException(SERVER_ERROR, "查询WDC门店信息异常");
            }

            return wdcPoiViewList.get(0);
        } catch (TException e) {
            log.error("[WdcPoiQueryServiceAdapter.getWdcPoiViewByWdcClueId] TException. wdcClueId = {}", wdcClueId, e);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询WDC门店信息异常");
        }
    }

    /**
     * 批量根据线索ID查询WDC门店信息
     * @param wdcClueIdList 线索ID列表
     * @return Map<Long, WdcPoiView>  key->wdcId  val->WdcPoiView
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<Long, WdcPoiView> getWdcPoiViewMapByWdcClueIdList(List<Long> wdcClueIdList) throws WmSchCantException {
        try {
            log.info("[WdcPoiQueryServiceAdapter.getWdcPoiViewMapByWdcClueIdList] wdcClueIdList = {}", JSONObject.toJSONString(wdcClueIdList));
            List<WdcPoiView> wdcPoiViewList = wdcPoiQueryService.findWdcPoiViewByWdcIds(wdcClueIdList);
            log.info("[WdcPoiQueryServiceAdapter.getWdcPoiViewMapByWdcClueIdList] wdcPoiViewList = {}", JSONObject.toJSONString(wdcPoiViewList));

            Map<Long, WdcPoiView> resMap = new HashMap<>();
            for (WdcPoiView view : wdcPoiViewList) {
                resMap.put(view.getWdcId(), view);
            }
            return resMap;
        } catch (TException e) {
            log.error("[WdcPoiQueryServiceAdapter.getWdcPoiViewMapByWdcClueIdList] TException. wdcClueIdList = {}", JSONObject.toJSONString(wdcClueIdList), e);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询WDC门店信息异常");
        }
    }


}
