package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingSearchCondition;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校楼宇信息Mapper
 * <AUTHOR>
 * @date 2023/06/05
 * @email <EMAIL>
 **/
@Component
public interface WmScSchoolBuildingMapper {
    /**
     * 根据学校主键ID查询学校楼宇信息列表
     * @param schoolPrimaryId 学校主键ID
     * @return 学校楼宇信息列表
     */
    List<WmScSchoolBuildingDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据条件查询学校楼宇信息列表(楼宇名称模糊匹配)
     * @param condition 查询条件
     * @return 学校楼宇信息列表
     */
    List<WmScSchoolBuildingDO> selectByCondition(WmScSchoolBuildingSearchCondition condition);

    /**
     * 通过楼宇主键ID查询楼宇信息
     * @param id 楼宇主键ID
     * @return 楼宇信息
     */
    WmScSchoolBuildingDO selectByPrimaryId(@Param("id") Integer id);

    /**
     * 根据学校主键ID和楼宇名称查询楼宇列表(楼宇名称精确匹配)
     * @param schoolPrimaryId 学校主键ID
     * @param buildingName 楼宇名称
     * @return 学校楼宇信息列表
     */
    List<WmScSchoolBuildingDO> selectBySchoolPrimaryIdAndBuildingName(@Param("schoolPrimaryId") Integer schoolPrimaryId,
                                                                      @Param("buildingName") String buildingName);

    /**
     * 根据主键ID进行更新
     * @param wmScSchoolBuildingDoUpdate wmScSchoolBuildingDoUpdate
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmScSchoolBuildingDO wmScSchoolBuildingDoUpdate);

    /**
     * 新增学校楼宇信息
     * @param wmScSchoolBuildingDoInsert wmScSchoolBuildingDoInsert
     * @return 更新行数
     */
    int insertSelective(WmScSchoolBuildingDO wmScSchoolBuildingDoInsert);

    /**
     * 批量新增学校楼宇信息
     * @param list 学校楼宇列表
     * @return 更新行数
     */
    int batchInsertSelective(@Param("list") List<WmScSchoolBuildingDO> list);

    /**
     * 逻辑删除单个学校楼宇
     * @param id 楼宇主键ID
     * @param muid 操作人ID
     * @return 修改行数
     */
    int invalidByPrimaryKey(@Param("id") Long id, @Param("muid") Long muid);

}
