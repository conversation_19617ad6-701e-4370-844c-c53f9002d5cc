package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete.nationalsubsidy;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete.DeliveryPdfDelete;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @description: 国补闪购2.2费率模式闪购新企客企客远距离合同
 * @author: liuyunjie05
 * @create: 2025/5/26 19:12
 */
@Slf4j
@Service
@PdfDeleter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_LONG_DISTANCE_PERFORMANCE)
public class NationalSubsidySgNewQikeLongDistanceDelete implements DeliveryPdfDelete {

    @Override
    public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
        if (CollectionUtils.isEmpty(pdfDataMap.get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_LONG_DISTANCE_PERFORMANCE.getName()))) {
            Collection<SignTemplateEnum> tabList = tabPdfMap.get(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE);
            if (CollectionUtils.isNotEmpty(tabList)) {
                log.info("NationalSubsidySgNewQikeLongDistanceDelete#delete, remove, tabList: {}", JSON.toJSONString(tabList));
                tabList.removeIf(value -> value.equals(SignTemplateEnum.NATIONAL_SUBSIDY_LONG_DISTANCE_SG_QIKE_V2_FEEMODE));
            }
        }
    }
}
