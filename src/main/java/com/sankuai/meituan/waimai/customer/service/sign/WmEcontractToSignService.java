package com.sankuai.meituan.waimai.customer.service.sign;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.SubmitManualTaskResultBO;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.common.StatusCodeEnum;
import com.sankuai.meituan.waimai.customer.constant.contract.ManualTaskSubmitStatusEnum;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractBatchDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmEContractSignBaseDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBaseBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.customer.service.sign.toSign.WmEcontractToSignTipsHandler;
import com.sankuai.meituan.waimai.customer.util.RedisKvUtil;
import com.sankuai.meituan.waimai.customer.util.base.BaseResponseUtil;
import com.sankuai.meituan.waimai.customer.util.base.EncryptUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import com.sankuai.meituan.waimai.kv.groupmbase.client.WmKvTairClient;
import com.sankuai.meituan.waimai.kv.groupmbase.exception.WmKvException;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.ApplyBaseConfirmDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.ToSignTaskDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.request.ToSignCustomerTaskReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.ApplyBaseConfirmResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.ApplyBaseConfirmStatusResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.ToSignTaskQueryResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBaseInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerKPBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ToSignCertifyInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * @description: APP/PC待签约页
 * @author: lixuepeng
 * @create: 2021-12-29
 **/
@Slf4j
@Service
public class WmEcontractToSignService implements InitializingBean {

    private static final String APPLY_BASE_KEY_CATAGORY = "applybase_lock_key";

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractToSignService.class);

    Map<String, WmEcontractToSignTipsHandler> tipsHandlerMap = new HashMap<>();

    private static final ListeningExecutorService tipsExecutorService;

    @Autowired
    private WmCustomerService wmCustomerService;
    @Autowired
    private WmCustomerKpService wmCustomerKpService;
    @Autowired
    private WmEcontractBaseBizService wmEcontractBaseBizService;
    @Autowired
    private WmEcontractTaskBizService wmEcontractTaskBizService;
    @Autowired
    private WmEcontractBatchBizService wmEcontractBatchBizService;
    @Autowired
    List<WmEcontractToSignTipsHandler> wmEcontractToSignTipsHandlerList;
    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;
    @Autowired
    private WmEcontractManualBatchBizService wmEcontractManualBatchBizService;
    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;
    @Autowired
    private EcontractBizService econtractBizService;
    @Autowired
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;
    @Autowired
    private RedisKvUtil redisKvUtil;

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    static {
        tipsExecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.toSignTipsThreadPoolSize()));
    }

    @Autowired(required = false)
    @Qualifier("m_WmKvTairClient")
    private WmKvTairClient wmKvTairClient = null;

    @Resource
    private WmEcontractBatchDBMapper wmEcontractBatchDBMapper;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat(
        "ApplyBaseTaskExecutorService_%d").build();

    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
        new ThreadPoolExecutor(10, 30, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(200), THREAD_FACTORY,
            new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));


    @Override
    public void afterPropertiesSet() throws Exception {
        Preconditions.checkNotNull(wmEcontractToSignTipsHandlerList);
        for (WmEcontractToSignTipsHandler tipsHandler : wmEcontractToSignTipsHandlerList) {
            String name = tipsHandler.getName();
            Assert.hasText(name, "WmEcontractToSignTipsHandler: " + tipsHandler + " name is empty!");
            LOGGER.info("WmEcontractToSignService tipsHandler:{}  name:{}", tipsHandler, name);
            tipsHandlerMap.put(name, tipsHandler);
        }
    }

    /**
     * 根据门店id和任务类型获取待签约的recordKey列表
     * 只关注WmEcontractBatchConstant.TO_SIGN_TASK_TYPE_MENU_NAME_MAP
     * @param wmPoiId
     * @param type
     * @return
     */
    public List<String> queryToSignTaskRecordKeysByWmPoiIdAndType(Long wmPoiId, String kpPhoneNum, Integer type) {
        try {
            // 获取门店客户信息
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
            if (wmCustomerDB == null) {// 无客户信息
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询门店对应客户信息为空");
            }
            // 获取该客户下的签约中任务对应的batchId列表
            List<EcontractTaskApplyTypeEnum> typeEnumList = new ArrayList<>(WmEcontractBatchConstant.TO_SIGN_TASK_TYPE_MAP.get(type));
            List<String> typeList = typeEnumList.stream().map(EcontractTaskApplyTypeEnum::getName).collect(Collectors.toList());

            // 支持展示在商家端单店视角的配置化合同
            List<String> configContractCodeList = wmFrameContractConfigService.allConfigFrameContract().stream()
                    .filter(configInfo -> configInfo.getSourceAuthInfo().getCanDisplayInSingleView())
                    .map(ContractConfigInfo::getContractCode)
                    .collect(Collectors.toList());
            typeList.addAll(configContractCodeList);

            List<Long> batchIdList = wmEcontractTaskBizService.getToSignBatchIdList(wmCustomerDB.getId(), typeList);
            // 根据batchId列表获取签约任务信息
            List<WmEcontractSignBatchDB> batchDBList = wmEcontractBatchBizService.batchQueryByBatchId(batchIdList);
            // 过滤-根据任务状态及根据KP信息过滤
            List<WmEcontractSignBatchDB> finalBatchDBList = filterByStateAndKpPhoneNum(batchDBList, kpPhoneNum);
            // 返回对应的recordKey列表
            if (CollectionUtils.isNotEmpty(finalBatchDBList)) {
                return finalBatchDBList.stream().map(WmEcontractSignBatchDB::getRecordKey).collect(Collectors.toList());
            }
        }  catch (WmCustomerException ce) {
            LOGGER.error("获取待签约任务recordKey列表异常, WmCustomerException, wmPoiId:{}, type:{}, msg: {}", wmPoiId, type, ce.getMessage());
        } catch (Exception e) {
            LOGGER.error("获取待签约任务recordKey列表异常, Exception, wmPoiId:{}, type:{}", wmPoiId, type, e);
        }
        return new ArrayList<>();
    }


    /**
     * 根据门店ID获取实名验证信息
     * @param wmPoiId
     * @return
     */
    public ToSignCertifyInfoBo queryToSignCertifyInfoBoByWmPoiId(Long wmPoiId) {
        try {
            ToSignCertifyInfoBo certifyInfoBo = new ToSignCertifyInfoBo();
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
            if (wmCustomerDB == null) {// 无客户信息
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询门店对应客户信息为空");
            }
            WmEContractSignBaseDB baseDB = wmEcontractBaseBizService.getByCustomerId(wmCustomerDB.getId());
            EcontractBaseInfoBo baseInfoBo = JSON.parseObject(baseDB.getBaseContext(), EcontractBaseInfoBo.class);
            certifyInfoBo.setCertType(baseInfoBo.getCustomerInfoBo().getQuaTypeEnum().getCode());
            certifyInfoBo.setCertType(baseInfoBo.getCustomerInfoBo().getQuaTypeEnum().getCode());
            certifyInfoBo.setCertPhone(baseInfoBo.getKpBo().getSignerPhoneNum());
            switch (baseInfoBo.getCustomerInfoBo().getQuaTypeEnum()) {
                case CUSTOMER_TYPE_BUSINESS:
                    certifyInfoBo.setCompanyName(baseInfoBo.getCustomerInfoBo().getCustomerName());
                    certifyInfoBo.setCompanyNum(baseInfoBo.getCustomerInfoBo().getQuaNum());
                    certifyInfoBo.setSignerName(EncryptUtil.encryptName(baseInfoBo.getKpBo().getSignerName()));
                    certifyInfoBo.setSignerCardType(baseInfoBo.getKpBo().getCertType());
                    certifyInfoBo.setSignerCardNum(EncryptUtil.encryptCardNum(baseInfoBo.getKpBo().getCertNumber()));
                    break;
                case CUSTOMER_TYPE_BUSINESS_ABROAD:
                    certifyInfoBo.setCompanyName(baseInfoBo.getCustomerInfoBo().getCustomerName());
                    certifyInfoBo.setCompanyNum(baseInfoBo.getCustomerInfoBo().getQuaNum());
                    certifyInfoBo.setSignerName(EncryptUtil.encryptName(baseInfoBo.getKpBo().getSignerName()));
                    certifyInfoBo.setSignerCardType(baseInfoBo.getKpBo().getCertType());
                    certifyInfoBo.setSignerCardNum(EncryptUtil.encryptCardNum(baseInfoBo.getKpBo().getCertNumber()));
                    break;
                case CUSTOMER_TYPE_IDCARD:
                    certifyInfoBo.setSignerName(EncryptUtil.encryptName(baseInfoBo.getCustomerInfoBo().getCustomerName()));
                    certifyInfoBo.setSignerCardType(baseInfoBo.getCustomerInfoBo().getCustomerSecondType());
                    certifyInfoBo.setSignerCardNum(EncryptUtil.encryptCardNum(baseInfoBo.getCustomerInfoBo().getCustomerNumber()));
                    break;
                default:
                    break;
            }
            return certifyInfoBo;
        } catch (WmCustomerException ce) {
            LOGGER.error("获取待签约任务recordKey列表异常 wmPoiId:{}", wmPoiId, ce.getMessage());
        } catch (Exception e) {
            LOGGER.error("根据门店ID获取实名验证信息异常 wmPoiId:{}", wmPoiId, e);
        }
        return null;
    }

    /**
     * 根据门店ID获取待签约页顶部提示
     * @param wmPoiId
     * @return
     * @throws WmCustomerException
     * @throws InterruptedException
     */
    public String queryToSignTipsByWmPoiId(Long wmPoiId) throws WmCustomerException, InterruptedException {
        // 获取门店对应客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if (wmCustomerDB == null) {// 无客户信息
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询门店对应客户信息为空");
        }

        List<Long> wmPoiIdList = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(wmCustomerDB.getId());

        //门店列表为空||门店数量大于1||门店包含门店当前门店ID
        if (CollectionUtils.isEmpty(wmPoiIdList) || wmPoiIdList.size() > 1 || !wmPoiIdList.contains(wmPoiId)) {
            return "";
        }

        List<String> tipsList = Collections.synchronizedList(new ArrayList<>());// 需要提示去签约的合同名称
        CountDownLatch latch = new CountDownLatch(tipsHandlerMap.keySet().size());
        WmCustomerException wmCustomerException = new WmCustomerException();
        wmCustomerException.setCode(CustomerErrorCodeConstants.SUCCESS);
        for (Map.Entry<String, WmEcontractToSignTipsHandler> entry : tipsHandlerMap.entrySet()) {
            ListenableFuture<Boolean> future = tipsExecutorService.submit(new Callable() {
                @Override
                public Boolean call() throws Exception {
                    // 需要签约的场景判断是否存在生效数据
                    if (entry.getValue().isNeedSign(wmCustomerDB.getId(), wmPoiId)) {
                        return entry.getValue().hasEffectiveRecord(wmCustomerDB.getId(), wmPoiId);
                    }
                    return true;
                }
            });
            Futures.addCallback(future, new FutureCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean result) {
                    if (!result) {
                        tipsList.add(entry.getKey());
                    }
                    latch.countDown();
                }
                @Override
                public void onFailure(Throwable t) {
                    LOGGER.error("queryTips error wmPoiId:{}, name:{} ", wmPoiId, entry.getKey(), t);
                    wmCustomerException.setCode(CustomerErrorCodeConstants.SYSTEM_ERROR);
                    wmCustomerException.setMsg("获取待签约页顶部提示异常");
                    latch.countDown();
                }
            });
        }
        latch.await();

        // 如果存在异常情况
        if (wmCustomerException.getCode() != CustomerErrorCodeConstants.SUCCESS) {
            throw wmCustomerException;
        }
        // 存在需要签约的合同
        if (CollectionUtils.isNotEmpty(tipsList)) {
            return MccConfig.toSignTips().replace("{X}", StringUtils.join(tipsList.toArray(), ""));
        }
        return "";
    }

    public boolean checkPhoneAuthWithRecordKeys(List<String> recordKeys, String kpPhoneNum) throws WmCustomerException {
        List<WmEcontractSignBatchDB> batchDBList = wmEcontractBatchBizService.queryByRecordKeys(recordKeys);
        // 获取到的任务信息为空
        if (CollectionUtils.isEmpty(batchDBList)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "未查询到有效的任务信息");
        }
        // 任务信息数量和recordKey不一致
        if (recordKeys.size() != batchDBList.size()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询到的任务信息异常");
        }
        // 遍历任务列表，如果任务对应的KP电话和入参不一致，则返回失败
        EcontractBatchContextBo batchContextBo = null;
        for (WmEcontractSignBatchDB batchDB : batchDBList) {
            batchContextBo =  JSON.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
            if (!kpPhoneNum.equals(batchContextBo.getKpBo().getSignerPhoneNum())) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "任务对应手机号和获取验证码手机号不一致");
            }
        }
        return true;
    }

    private List<WmEcontractSignBatchDB> filterByStateAndKpPhoneNum(List<WmEcontractSignBatchDB> batchDBList, String kpPhoneNum) {
        if (CollectionUtils.isEmpty(batchDBList)) {
            return new ArrayList<>();
        }
        List<WmEcontractSignBatchDB> finalBatchDBList = new ArrayList<>();
        for (WmEcontractSignBatchDB batchDB : batchDBList) {
            // 非进行中任务
            if (!EcontractBatchStateEnum.IN_PROCESSING.getName().equals(batchDB.getBatchState())) {
                continue;
            }
            EcontractBatchContextBo batchContextBo = JSON.parseObject(batchDB.getBatchContext(),
                EcontractBatchContextBo.class);
            if (batchContextBo.getKpBo() == null || !kpPhoneNum.equals(batchContextBo.getKpBo().getSignerPhoneNum())) {
                continue;
            }
            finalBatchDBList.add(batchDB);
        }
        return finalBatchDBList;
    }


    /**
     * 根据门店id和任务类型获取待签约的recordKey列表
     * 只关注WmEcontractBatchConstant.TO_SIGN_TASK_TYPE_MENU_NAME_MAP
     *
     * @param toSignCustomerTaskReq 请求
     * @return 返回recordkey等相关信息
     */
    public ToSignTaskQueryResp queryToSignTaskRecordKeysViaCustomer(ToSignCustomerTaskReq toSignCustomerTaskReq) {
        try {
            WmCustomerDB wmCustomer = wmCustomerService.selectCustomerById(toSignCustomerTaskReq.getCustomerId());
            if (Objects.isNull(wmCustomer)) {
                log.error("customerId对应客户信息无效");
                return BaseResponseUtil.fail(StatusCodeEnum.BIZ_ERROR, ToSignTaskQueryResp.class);
            }
            // 获取该客户下的签约中任务对应的batchId list
            List<EcontractTaskApplyTypeEnum> typeEnumList =
                    new ArrayList<>(WmEcontractBatchConstant.TO_SIGN_TASK_TYPE_MAP.get(
                            toSignCustomerTaskReq.getTaskType()));
            List<String> typeNameList = typeEnumList.stream()
                    .map(EcontractTaskApplyTypeEnum::getName)
                    .collect(Collectors.toList());
            List<String> allConfigContractCode = wmFrameContractConfigService.allConfigFrameContract()
                    .stream()
                    .filter(configInfo -> configInfo.getSourceAuthInfo().getCanDisplayInMultiView())
                    .map(ContractConfigInfo::getContractCode)
                    .collect(Collectors.toList());
            typeNameList.addAll(allConfigContractCode);
            // sign_task维度取batch_id
            List<Long> taskBatchIdList = wmEcontractTaskBizService.getToSignBatchIdList(wmCustomer.getId(),
                    typeNameList);
            log.info("customerId={},签约中batchIdList={}", wmCustomer.getId(), JSON.toJSONString(taskBatchIdList));
            // 根据batchId列表获取签约任务信息
            List<WmEcontractSignBatchDB> taskBatchList =
                    wmEcontractBatchBizService.batchQueryByBatchId(taskBatchIdList);
            log.info("customerId={},签约中taskBatchList={}", wmCustomer.getId(), JSON.toJSONString(taskBatchList));
            // 过滤-根据任务状态及根据KP信息过滤
            List<WmEcontractSignBatchDB> finalBatchList = filterByStateAndKpPhoneNum(taskBatchList,
                    toSignCustomerTaskReq.getKpPhoneNum());
            log.info("customerId={},最终签约中任务finalBatchList={}", wmCustomer.getId(), JSON.toJSONString(finalBatchList));
            // 返回对应的列表
            if (CollectionUtils.isEmpty(finalBatchList)) {
                return BaseResponseUtil.success(Collections.emptyList(), ToSignTaskQueryResp.class);
            }
            List<ToSignTaskDTO> toSignTaskList = finalBatchList.stream().map(
                    m -> ToSignTaskDTO.builder().recordKey(m.getRecordKey()).build()
            ).collect(Collectors.toList());
            return BaseResponseUtil.success(toSignTaskList, ToSignTaskQueryResp.class);
        } catch (Exception e) {
            log.error("处理客户签约任务信息异常：req={}", toSignCustomerTaskReq, e);
        }
        return BaseResponseUtil.success(Collections.emptyList(), ToSignTaskQueryResp.class);
    }


    /**
     * count to sign task
     *
     * @param toSignCustomerTaskReq 参数
     * @return 返回计数
     */
    public Integer queryToSignTaskCountViaCustomer(ToSignCustomerTaskReq toSignCustomerTaskReq, List<String> allConfigContractCode) {
        try {
            WmCustomerDB wmCustomer = wmCustomerService.selectCustomerById(toSignCustomerTaskReq.getCustomerId());
            if (Objects.isNull(wmCustomer)) {
                log.error("customerId对应客户信息无效");
                return NumberUtils.INTEGER_ZERO;
            }
            // 获取该客户下的签约中任务对应的batchId list
            List<EcontractTaskApplyTypeEnum> typeList =
                    new ArrayList<>(WmEcontractBatchConstant.TO_SIGN_TASK_TYPE_MAP.get(
                            toSignCustomerTaskReq.getTaskType()));
            List<String> typeNameList = typeList.stream()
                    .map(EcontractTaskApplyTypeEnum::getName)
                    .collect(Collectors.toList());
            typeNameList.addAll(allConfigContractCode);
            // sign_task维度取batch_id
            List<Long> taskBatchIdList = wmEcontractTaskBizService.getToSignBatchIdList(wmCustomer.getId(),
                    typeNameList);
            log.info("customerId={},签约中计数batchIdList={}", wmCustomer.getId(), JSON.toJSONString(taskBatchIdList));
            if (CollectionUtils.isNotEmpty(taskBatchIdList)) {
                return taskBatchIdList.size();
            }
        } catch (Exception e) {
            log.error("处理客户签约任务计算异常：req={}", toSignCustomerTaskReq, e);
        }
        return NumberUtils.INTEGER_ZERO;
    }


    /**
     * 根据customerId获取实名验证信息
     *
     * @param customerId 客户id
     * @return kp信息
     */
    public ToSignCertifyInfoBo queryToSignCertifyInfoViaCustomer(int customerId) throws WmCustomerException {
        ToSignCertifyInfoBo certifyInfoBo = new ToSignCertifyInfoBo();

        // get basic customer info
        EcontractCustomerInfoBo customerInfoBo = getEcontractCustomerInfoBo(customerId);

        // get the singer info
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(customerId);
        EcontractCustomerKPBo kpBo = null;
        if (Objects.nonNull(wmCustomerKp)) {
            kpBo = new EcontractCustomerKPBo.Builder()
                .kpTypeEnum(KpTypeEnum.SIGNER)
                .kpSignerTypeEnum(KpSignerTypeEnum.getByType(wmCustomerKp.getSignerType()))
                .certType(wmCustomerKp.getCertType())
                .certNumber(wmCustomerKp.getCertNumber())
                .signerName(wmCustomerKp.getCompellation())
                .signerIDCardNum(wmCustomerKp.getCertNumber())
                .signerPhoneNum(wmCustomerKp.getPhoneNum())
                .signerBankName(wmCustomerKp.getBankName())
                .signerBankCardNum(wmCustomerKp.getCreditCard())
                .signerEmail(wmCustomerKp.getEmail())
                .build();
        }

        EcontractBaseInfoBo baseInfoBo = new EcontractBaseInfoBo.Builder()
            .customerId(customerId)
            .customerInfoBo(customerInfoBo)
            .kpBo(kpBo)
            .build();

        certifyInfoBo.setCertType(baseInfoBo.getCustomerInfoBo().getQuaTypeEnum().getCode());
        certifyInfoBo.setCertPhone(baseInfoBo.getKpBo().getSignerPhoneNum());

        switch (baseInfoBo.getCustomerInfoBo().getQuaTypeEnum()) {
            case CUSTOMER_TYPE_BUSINESS:
            case CUSTOMER_TYPE_BUSINESS_ABROAD:
                certifyInfoBo.setCompanyName(baseInfoBo.getCustomerInfoBo().getCustomerName());
                certifyInfoBo.setCompanyNum(baseInfoBo.getCustomerInfoBo().getQuaNum());
                certifyInfoBo.setSignerName(EncryptUtil.encryptName(baseInfoBo.getKpBo().getSignerName()));
                certifyInfoBo.setSignerCardType(baseInfoBo.getKpBo().getCertType());
                certifyInfoBo.setSignerCardNum(EncryptUtil.encryptCardNum(baseInfoBo.getKpBo().getCertNumber()));
                break;
            case CUSTOMER_TYPE_IDCARD:
                certifyInfoBo.setSignerName(EncryptUtil.encryptName(baseInfoBo.getCustomerInfoBo()
                    .getCustomerName()));
                certifyInfoBo.setSignerCardType(baseInfoBo.getCustomerInfoBo().getCustomerSecondType());
                certifyInfoBo.setSignerCardNum(EncryptUtil.encryptCardNum(baseInfoBo.getCustomerInfoBo()
                    .getCustomerNumber()));
                break;
            default:
                break;
        }
        return certifyInfoBo;
    }

    private EcontractCustomerInfoBo getEcontractCustomerInfoBo(int customerId) throws WmCustomerException {
        EcontractCustomerInfoBo customerInfoBo;
        WmCustomerDB basicBo = wmCustomerService.selectEffectCustomerById(customerId);

        if (Objects.isNull(basicBo)) {
            log.error("客户未生效，暂时不可签约:{}", customerId);
            throw new WmCustomerException(BIZ_PARA_ERROR, "客户未生效，请联系对接联络人辅助修改");
        }

        customerInfoBo = new EcontractCustomerInfoBo.Builder()
            .customerName(basicBo.getCustomerName())
            .address(basicBo.getAddress())
            .legalPerson(basicBo.getLegalPerson())
            .quaNum(basicBo.getCustomerNumber())
            .quaTypeEnum(CustomerType.getByCode(basicBo.getCustomerType()))
            .customerSecondType(basicBo.getCustomerSecondType())
            .customerNumber(basicBo.getCustomerNumber())
            .build();
        return customerInfoBo;
    }

    public ApplyBaseConfirmResp applyManualSubmitViaCustomer(int customerId, int opUid) {
        log.info("处理提交草稿任务,customerId={},opUid={}", customerId, opUid);
        ApplyBaseConfirmDTO applyBaseConfirmDTO = ApplyBaseConfirmDTO.builder()
            .applyConfirmUniKey(StringUtils.EMPTY)
            .build();
        try {
            // 对当前客户的，同一操作人，进行防重操作
            String reqKey = String.valueOf(customerId) + "-" + String.valueOf(opUid);
            StoreKey reqStoreKey = new StoreKey(APPLY_BASE_KEY_CATAGORY, reqKey);
            String preReqKey = redisKvUtil.get(reqStoreKey);

            if (StringUtils.isNotBlank(preReqKey)) {
                log.info("已提交当前用户任务，快速返回:reqKey={}", reqKey);
                applyBaseConfirmDTO.setApplyConfirmUniKey(preReqKey);
                return BaseResponseUtil.success(applyBaseConfirmDTO, ApplyBaseConfirmResp.class);
            }
            String confirmUniKey = getUniqueId();
            redisKvUtil.setValue(reqStoreKey, confirmUniKey, MccConfig.applySubmitRequestKeyTimeout());

            AtomicReference<SubmitManualTaskResultBO> atomicSubmitTaskResultBO = new AtomicReference<>(
                SubmitManualTaskResultBO.builder()
                    .status(ManualTaskSubmitStatusEnum.SUBMIT_TASK_START_INIT.getCode())
                    .manualBatchIdList(Lists.newArrayList())
                    .actionTime((new Date()).getTime())
                    .build());
            wmKvTairClient.set(confirmUniKey,
                JSON.toJSONString(atomicSubmitTaskResultBO),
                MccConfig.applySubmitStatusQueryTimeout());

            int manualTaskStartTime = MccConfig.manualTaskConvertStartTime();
            int manualTaskTimeSplit = DateUtil.date2Unixtime(DateUtil.toDay(DateTime.now()
                .plusDays(manualTaskStartTime + 1)
                .toDate()));
            List<LongResult> allTaskIdList = wmEcontractSignBzService.getManualTaskIdList(
                customerId, manualTaskTimeSplit);
            log.info("待处理任务:allTaskIdList={}", JSON.toJSONString(allTaskIdList));
            if (CollectionUtils.isEmpty(allTaskIdList)) {
                //未有待发起任务，即认为处理完成
                atomicSubmitTaskResultBO.set(SubmitManualTaskResultBO.builder()
                    .status(ManualTaskSubmitStatusEnum.SUBMIT_TASK_START_COMPLETED.getCode())
                    .manualBatchIdList(Lists.newArrayList())
                    .build());

                wmKvTairClient.set(confirmUniKey,
                    JSON.toJSONString(atomicSubmitTaskResultBO),
                    MccConfig.applySubmitStatusQueryTimeout());
            }

            Set<Long> allDistinctTaskIdList = allTaskIdList.stream()
                .map(LongResult::getValue)
                .collect(Collectors.toSet());

            int actualTaskLimitNum = MccConfig.getActualTaskPartitionNum();

            List<List<Long>> partitionTaskList = Lists.partition(Lists.newArrayList(allDistinctTaskIdList),
                actualTaskLimitNum);
            log.info("待处理任务切分:partitionTaskList={}", JSON.toJSONString(partitionTaskList));

            CountDownLatch latch = new CountDownLatch(partitionTaskList.size());
            for (List<Long> partTasks : partitionTaskList) {
                // 异步执行
                executorService.submit(() -> {
                    try {
                        LongResult res = wmEcontractSignBzService.applyManualPack(partTasks,
                            opUid,
                            WmSignConstant.OTHER);
                        log.info("提交打包返回：res={}", JSON.toJSONString(res));
                        atomicSubmitTaskResultBO.set(JSON.parseObject(wmKvTairClient.get(confirmUniKey, 1000),
                            SubmitManualTaskResultBO.class));
                        atomicSubmitTaskResultBO.get().getManualBatchIdList().add(res.getValue());
                        List<Long> currentManualBatchIdList = atomicSubmitTaskResultBO.get().getManualBatchIdList();
                        /// 处理中的状态标记
                        atomicSubmitTaskResultBO.get()
                            .setStatus(ManualTaskSubmitStatusEnum.SUBMIT_TASK_PROCESSING.getCode());
                        atomicSubmitTaskResultBO.get().setManualBatchIdList(currentManualBatchIdList);
                        log.info("current result status: {}", JSON.toJSONString(atomicSubmitTaskResultBO));
                        wmKvTairClient.set(confirmUniKey,
                            JSON.toJSONString(atomicSubmitTaskResultBO));
                        latch.countDown();

                    } catch (Exception e) {
                        log.error("异步处理申请打包任务异常", e);
                    } finally {
                        log.warn("异步处理申请打包任务完成");
                        latch.countDown();
                    }
                });
            }
            if (MccConfig.needToAwait()) {
                latch.await(60, TimeUnit.SECONDS);
            }
            atomicSubmitTaskResultBO.get().setStatus(ManualTaskSubmitStatusEnum.SUBMIT_TASK_SUBMIT_FINISH.getCode());
            wmKvTairClient.set(confirmUniKey,
                JSON.toJSONString(atomicSubmitTaskResultBO));
            applyBaseConfirmDTO.setApplyConfirmUniKey(confirmUniKey);
            log.info("处理提交草稿任务返回,customerId={},opUid={},confirmUniKey={}", customerId, opUid, confirmUniKey);
            return BaseResponseUtil.success(applyBaseConfirmDTO, ApplyBaseConfirmResp.class);

        } catch (Exception e) {
            log.error("applyBaseViaCustomer调用自动打包处理异常:customerId= {}", customerId, e);
        }
        return BaseResponseUtil.success(applyBaseConfirmDTO, ApplyBaseConfirmResp.class);
    }

    private String getUniqueId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 出现异常，即返回成功兜底，不要卡流程。
     *
     * @param confirmUniqueKey 任务key
     * @return 返回
     */
    public ApplyBaseConfirmStatusResp applyManualSubmitConfirmStatus(String confirmUniqueKey) {

        try {
            // 无任务，亦认为任务成功
            String cachedManualJobResult = wmKvTairClient.get(confirmUniqueKey, 1000);
            if (StringUtils.isBlank(cachedManualJobResult) || MccConfig.isSubmitStatusDegrade()) {
                log.info("当前任务流水=「{}」，无任务:", confirmUniqueKey);
                return BaseResponseUtil.success(Boolean.TRUE, ApplyBaseConfirmStatusResp.class);
            }
            SubmitManualTaskResultBO submitManualTaskResultBO = JSON.parseObject(wmKvTairClient.get(
                confirmUniqueKey,
                1000), SubmitManualTaskResultBO.class);
            log.info("submitManualTaskResultBO ={}", JSON.toJSONString(submitManualTaskResultBO));
            if (!ManualTaskSubmitStatusEnum.SUBMIT_TASK_SUBMIT_FINISH.getCode()
                .equals(submitManualTaskResultBO.getStatus())) {
                log.info("未到已提交状态，请等待...");
                return BaseResponseUtil.success(Boolean.FALSE, ApplyBaseConfirmStatusResp.class);
            }
            
            // new  pack rel： wm_econtract_sign_manual_batch -> wm_econtract_sign_pack -> wm_econtract_sign_batch(record_key)
            if (CollectionUtils.isNotEmpty(submitManualTaskResultBO.getManualBatchIdList())) {
                List<WmEcontractSignManualBatchDB> wmEcontractSignManualBatchDBList = wmEcontractManualBatchBizService.getManualBatchBatch(
                    submitManualTaskResultBO.getManualBatchIdList());
                log.info("wmEcontractSignManualBatchDBList ={}", JSON.toJSONString(wmEcontractSignManualBatchDBList));

                // 新旧打包
                List<WmEcontractSignManualBatchDB> oldPackManualBatchList = wmEcontractSignManualBatchDBList.stream()
                    .filter(m -> m.getPackId() == null || m.getPackId() == 0)
                    .collect(
                        Collectors.toList());
                log.info("oldPackManualBatchList ={}", JSON.toJSONString(oldPackManualBatchList));

                List<WmEcontractSignManualBatchDB> newPackManualBatchList = wmEcontractSignManualBatchDBList.stream()
                    .filter(m -> m.getPackId() != null && m.getPackId() != 0)
                    .collect(
                        Collectors.toList());

                log.info("newPackManualBatchList ={}", JSON.toJSONString(newPackManualBatchList));
                // case A ，新打包
                if (CollectionUtils.isNotEmpty(newPackManualBatchList)) {
                    List<WmEcontractSignPackDB> wmEcontractSignPackList = wmEcontractSignPackService.querySignPackByIdList(
                        org.apache.commons.collections4.CollectionUtils.emptyIfNull(
                            newPackManualBatchList).stream().map(WmEcontractSignManualBatchDB::getPackId).collect(
                            Collectors.toList()));

                    if (CollectionUtils.isNotEmpty(wmEcontractSignPackList)) {
                        Boolean result = getNewPackStatusResult(submitManualTaskResultBO, wmEcontractSignPackList);
                        log.info("newPackStatusResult#result ={}", result);
                        return BaseResponseUtil.success(result, ApplyBaseConfirmStatusResp.class);
                    }
                }
                // pre pack rel：
                // case B 手动打包,旧打包 ， packId为0
                if (CollectionUtils.isNotEmpty(oldPackManualBatchList)) {
                    // 取valid为1的记录为空即为完成
                    Boolean result = oldPackStatusResult(submitManualTaskResultBO, oldPackManualBatchList);
                    log.info("oldPackStatusResult#result ={}", result);
                    return BaseResponseUtil.success(result, ApplyBaseConfirmStatusResp.class);
                }
                // 没有未处理完的，即可返回成功
                log.info("没有未处理完的，即可返回成功, uniqueKey = {} ", confirmUniqueKey);
            } else {
                // 为空时候，则等待是否有新任务
                if (Objects.isNull(submitManualTaskResultBO.getActionTime())
                    || submitManualTaskResultBO.getActionTime() <= 0) {
                    SubmitManualTaskResultBO manualTaskResultBO = JSON.parseObject(wmKvTairClient.get(
                        confirmUniqueKey,
                        1000), SubmitManualTaskResultBO.class);
                    if (Objects.nonNull(manualTaskResultBO)) {
                        manualTaskResultBO.setActionTime((new Date()).getTime());
                        wmKvTairClient.set(confirmUniqueKey,
                            StringUtils.EMPTY,
                            MccConfig.applySubmitStatusQueryTimeout());
                    } else {
                        return BaseResponseUtil.success(Boolean.TRUE, ApplyBaseConfirmStatusResp.class);
                    }
                }
                if (Objects.nonNull(submitManualTaskResultBO.getActionTime())
                    && (new Date()).getTime() - submitManualTaskResultBO.getActionTime()
                    <= MccConfig.defaultManualTaskSubmitLoadingTime()) {
                    return BaseResponseUtil.success(Boolean.FALSE, ApplyBaseConfirmStatusResp.class);
                }
            }
        } catch (Exception ex) {
            log.error("请求状态处理异常:", ex);
            return BaseResponseUtil.success(Boolean.TRUE, ApplyBaseConfirmStatusResp.class);
        }

        try {
            wmKvTairClient.set(confirmUniqueKey,
                StringUtils.EMPTY,
                MccConfig.applySubmitStatusQueryTimeout());
        } catch (WmKvException e) {
            log.error("set tair value exception", e);
        } catch (Exception ex) {
            log.warn("tair设置异常", ex);
        }
        return BaseResponseUtil.success(Boolean.TRUE, ApplyBaseConfirmStatusResp.class);
    }

    private Boolean getNewPackStatusResult(SubmitManualTaskResultBO submitManualTaskResultBO,
                                           List<WmEcontractSignPackDB> wmEcontractSignPackList)
        throws EcontractException, TException {
        Boolean result = Boolean.TRUE;

        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBList = wmEcontractBatchBizService.queryBatchListByPackIdList(
            wmEcontractSignPackList.stream()
                .map(WmEcontractSignPackDB::getId)
                .collect(Collectors.toList()));
        log.info("wmEcontractSignBatchDBList ={}", JSON.toJSONString(wmEcontractSignBatchDBList));

        if (CollectionUtils.isEmpty(wmEcontractSignBatchDBList)) {
            result = Boolean.FALSE;
        }
        List<String> recordKeyList = wmEcontractSignBatchDBList.stream()
            .map(WmEcontractSignBatchDB::getRecordKey)
            .collect(Collectors.toList());
        List<EcontractRecordBo> econtractRecordBoList = econtractBizService.queryEcontractRecordList(
            recordKeyList);
        log.info("econtractRecordBoList ={}", JSON.toJSONString(econtractRecordBoList));
        if (CollectionUtils.isNotEmpty(econtractRecordBoList)) {
            List<EcontractRecordBo> econtractRecordDestList = econtractRecordBoList.stream()
                .filter(n -> TaskConstant.CONFIRM_STAMP_D.equals(n.getEcontractStage())).collect(
                    Collectors.toList());
            if (CollectionUtils.isEmpty(econtractRecordDestList)) {
                result = Boolean.FALSE;
            }
            if (econtractRecordDestList.size() != econtractRecordBoList.size()) {
                result = Boolean.FALSE;
            }
            if (Objects.nonNull(submitManualTaskResultBO.getActionTime())
                && (new Date()).getTime() - submitManualTaskResultBO.getActionTime()
                <= MccConfig.defaultManualTaskSubmitLoadingTime()
                * submitManualTaskResultBO.getManualBatchIdList().size()) {
                result = Boolean.FALSE;
            }
        }
        return result;
    }

    private Boolean oldPackStatusResult(SubmitManualTaskResultBO submitManualTaskResultBO,
                                        List<WmEcontractSignManualBatchDB> oldPackManualBatchList) {
        Boolean result = Boolean.TRUE;
        List<WmEcontractSignManualTaskDB> wmEcontractSignManualTaskDBList = wmEcontractManualTaskBizService.batchGetByManualBatchIds(
            oldPackManualBatchList.stream().map(WmEcontractSignManualBatchDB::getId).collect(
                Collectors.toList()));
        log.info("empty means completed: wmEcontractSignManualTaskDBList ={}",
            JSON.toJSONString(wmEcontractSignManualTaskDBList));
        if (Objects.nonNull(submitManualTaskResultBO.getActionTime())
            && (new Date()).getTime() - submitManualTaskResultBO.getActionTime()
            <= MccConfig.defaultManualTaskSubmitLoadingTime()
            * submitManualTaskResultBO.getManualBatchIdList().size()) {
            result = Boolean.FALSE;
        }
        if (CollectionUtils.isEmpty(wmEcontractSignManualTaskDBList)) {
            result = Boolean.TRUE;
        }
        return result;
    }

}
