package com.sankuai.meituan.waimai.customer.service.kp.sensitive.write;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.YesOrNoEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CertTypeTokenIdentifyRelationEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.TokenIdentifyEnum;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.EncryptResult;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncrypt;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncryptHandleService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 证件号
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
@Slf4j
public class IdentifyIdKpEntryWriteHandle implements IKpWriteHandle {

    @Autowired
    private KeyEncryptHandleService keyEncryptHandleService;


    @Override
    public KmsKeyNameEnum handleType() {
        return KmsKeyNameEnum.IDENTIFY_ID;
    }

    @Override
    public void doWriteKpWhenInsertOrUpdate(KpEntryWrite kpEntryWrite) throws WmCustomerException {
        log.debug("execute::kpEntryWrite = {}", JSON.toJSONString(kpEntryWrite));
        if (kpEntryWrite == null) {
            return;
        }
        writeKpIdentifyIdWhenInsertOrUpdate(kpEntryWrite.getKp());
        writeKpIdentifyIdWhenInsertOrUpdate(kpEntryWrite.getKpTemp());
    }

    @Override
    public void writeKpSourceWhenUpdate(KpEntryWrite kpEntryWrite) {
        log.debug("execute::writeKpSourceWhenUpdate = {}", JSON.toJSONString(kpEntryWrite));
        if (kpEntryWrite == null) {
            return;
        }
        writeKpIdentifyIdSourceWhenUpdate(kpEntryWrite.getKpTemp());
        writeKpIdentifyIdSourceWhenUpdate(kpEntryWrite.getKp());
    }


    /**
     * 客户KP证件号新老字段写控制
     * 是否写原字段 原字段写开关控制
     *
     * @param kp
     */
    private void writeKpIdentifyIdSourceWhenUpdate(WmCustomerKp kp) {
        if (kp == null) {
            return;
        }
        if (!MccCustomerConfig.encryptionCustomerKpCertNumberOldWriteSwitch()
                && tryEncryption(KmsKeyNameEnum.IDENTIFY_ID, kp.getCertNumber(), kp.getCertType())) {
            //开关关闭不再写字段
            kp.setNotSaveCertNumber(YesOrNoEnum.YES.getCode());
        } else {
            kp.setNotSaveCertNumber(YesOrNoEnum.NO.getCode());
        }
    }

    /**
     * 客户KP手机号新老字段写控制
     * 是否写原字段 原字段写开关控制
     *
     * @param kp
     */
    private void writeKpIdentifyIdSourceWhenUpdate(WmCustomerKpTemp kp) {
        if (kp == null) {
            return;
        }
        if (!MccCustomerConfig.encryptionCustomerKpCertNumberOldWriteSwitch()
                && tryEncryption(KmsKeyNameEnum.IDENTIFY_ID, kp.getCertNumber(), kp.getCertType())) {
            kp.setNotSaveCertNumber(YesOrNoEnum.YES.getCode());
        } else {
            kp.setNotSaveCertNumber(YesOrNoEnum.NO.getCode());
        }
    }


    /**
     * 客户KP证件卡号新老字段写控制
     * 是否写原字段 原字段写开关控制，通过变量控制（变量传入Mapper文件中进行判断）
     * 是否写新字段 新字段写开关控制
     *
     * @param kp
     */
    private void writeKpIdentifyIdWhenInsertOrUpdate(WmCustomerKp kp) throws WmCustomerException {
        if (kp == null) {
            return;
        }
        if (MccCustomerConfig.encryptionCustomerKpCertNumberNewWriteSwitch()) {
            //写新字段
            if (StringUtils.isBlank(kp.getCertNumber())) {
                kp.setCertNumberEncryption("");
                kp.setCertNumberToken("");
            } else {
                KeyEncrypt keyEncrypt = new KeyEncrypt();
                keyEncrypt.setKeyName(KmsKeyNameEnum.IDENTIFY_ID);
                keyEncrypt.setValueForEncrypt(kp.getCertNumber());
                int certType = getCertificateType(CertTypeEnum.getByType(kp.getCertType()), kp.getCertNumber());
                if (certType <= 0) {
                    log.info("writeKpIdentifyIdWhenInsertOrUpdate::kpId = {},certType={}", kp.getId(), kp.getCertType());
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "证件号不合法，请核查后重新录入");
                }
                keyEncrypt.setCertType(certType);
                EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
                if (result == null) {
                    return;
                }
                kp.setCertNumberEncryption(result.getEncryption());
                kp.setCertNumberToken(result.getToken());
            }
        }
        writeKpIdentifyIdSourceWhenUpdate(kp);
    }

    /**
     * 确定token加密证件类型ID
     *
     * @param certTypeEnum
     * @param valueForEncrypt
     * @return
     */
    public int getCertificateType(CertTypeEnum certTypeEnum, String valueForEncrypt) {
        if (StringUtils.isBlank(valueForEncrypt)) {
            return 0;
        }
        CertTypeTokenIdentifyRelationEnum relation = CertTypeTokenIdentifyRelationEnum.getEnumPreMap().get(certTypeEnum);
        if (relation == null) {
            return 0;
        }
        if (relation.getTokenCode() != null) {
            return relation.getTokenCode();
        }
        switch (certTypeEnum) {
            case REDIDENCE_PERMIT_OF_HK_MACAO_TAIWAI:
                if (valueForEncrypt.startsWith(WmCustomerConstant.RESIDENCE_PERMIT_FOR_TAIWAN_RESIDENTS_PRE)) {
                    return TokenIdentifyEnum.RESIDENCE_PERMIT_FOR_TAIWAN_RESIDENTS.getCode();
                } else {
                    return TokenIdentifyEnum.RESIDENCE_PERMIT_FOR_HONG_KONG_AND_MACAO_RESIDENTS.getCode();
                }
            default:
                return 0;
        }
    }

    /**
     * 客户KP证件号新老字段写控制
     * 是否写原字段 原字段写开关控制
     * 是否写新字段 新字段写开关控制
     *
     * @param kp
     */
    private void writeKpIdentifyIdWhenInsertOrUpdate(WmCustomerKpTemp kp) throws WmCustomerException {
        if (kp == null) {
            return;
        }
        if (MccCustomerConfig.encryptionCustomerKpCertNumberNewWriteSwitch()) {
            //写新字段
            if (StringUtils.isBlank(kp.getCertNumber())) {
                kp.setCertNumberEncryption("");
                kp.setCertNumberToken("");
            } else {
                KeyEncrypt keyEncrypt = new KeyEncrypt();
                keyEncrypt.setKeyName(KmsKeyNameEnum.IDENTIFY_ID);
                keyEncrypt.setValueForEncrypt(kp.getCertNumber());
                keyEncrypt.setCertType(getCertificateType(CertTypeEnum.getByType(kp.getCertType()), kp.getCertNumber()));
                EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
                if (result == null) {
                    return;
                }
                kp.setCertNumberEncryption(result.getEncryption());
                kp.setCertNumberToken(result.getToken());
            }
        }
        writeKpIdentifyIdSourceWhenUpdate(kp);
    }

    /**
     * 对字段尝试加密
     *
     * @param key
     * @param value
     * @param certType
     * @return
     */
    private boolean tryEncryption(KmsKeyNameEnum key, String value, int certType) {
        try {
            KeyEncrypt keyEncrypt = new KeyEncrypt();
            keyEncrypt.setKeyName(key);
            keyEncrypt.setValueForEncrypt(value);
            keyEncrypt.setCertType(getCertificateType(CertTypeEnum.getByType(certType), value));
            EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
            if (result == null) {
                return false;
            }
        } catch (WmCustomerException e) {
            log.info("tryEncryption::key = {}, value = {},code={},msg={}", key, value, e.getCode(), e.getMsg());
            return false;
        }
        return true;
    }
}
