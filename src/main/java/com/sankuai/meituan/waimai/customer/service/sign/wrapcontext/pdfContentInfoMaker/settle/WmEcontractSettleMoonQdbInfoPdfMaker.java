package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.settle;

import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.SETTLE_MOON_QDB_INFO_V3)
@Service
@Slf4j
public class WmEcontractSettleMoonQdbInfoPdfMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext,
            EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractSettleMoonQdbInfoPdfMaker");
        SignTemplateEnum signTemplateEnum = SignTemplateEnum.SETTLE_MOON_QDB_INFO_V3;

        EcontractTaskBo taskBo = WmEcontractContextUtil
                .selectByApplyType(originContext, EcontractTaskApplyTypeEnum.SETTLE);

        Map<String, String> pdfMap = generateQDBPdf(originContext, taskBo);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfMetaContent(pdfMap);
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        return pdfInfoBo;
    }

    private Map<String, String> generateQDBPdf(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());

        //抽取一个开钱包的对象-业务规则上允许部分开钱包
        EcontractSettleInfoBo infoBo = null;
        for(EcontractSettleInfoBo temp : settleInfoBoList){
            if(temp.isSupportWallet()){
                infoBo = temp;
                break;
            }
        }

        Map<String, String> map = Maps.newHashMap();
        map.put("qdbNumber", StringUtils.defaultIfEmpty(infoBo.getQdbNumber(), StringUtils.EMPTY));
        map.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        map.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        map.put("partAAddress", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getAddress(), StringUtils.EMPTY));
        map.put("partAContact", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerName(), StringUtils.EMPTY));
        map.put("partAPhone", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerPhoneNum(), StringUtils.EMPTY));
        map.put("partAEmail", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerEmail(), StringUtils.EMPTY));
        map.put("signTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        map.put("partB", StringUtils.defaultIfEmpty(infoBo.getPartB(), StringUtils.EMPTY));
        map.put("partBAddress", StringUtils.defaultIfEmpty(infoBo.getPartBAddress(), StringUtils.EMPTY));
        map.put("partBPhone", StringUtils.defaultIfEmpty(infoBo.getPartBPhone(), StringUtils.EMPTY));
        map.put("partAstamp", PdfConstant.POI_SIGNKEY);
        map.put("qdbEstamp", PdfConstant.QDB_SIGNKEY);
        return map;
    }


}
