package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.google.common.base.MoreObjects;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractBasicBo;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractBo;
import com.sankuai.meituan.waimai.c2contract.service.WmC2ContractAuditedThriftService;
import com.sankuai.meituan.waimai.contract.thrift.service.WmAgentContractThriftService;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.customer.util.trans.TransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.PoiGrayRollbackCheckResBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.CustomerPaperContractRemarkBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.MultiFileJsonBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractScanBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Service
public class WmContractSyncService {

    private static Logger logger = LoggerFactory.getLogger(WmContractSyncService.class);

    @Autowired
    WmTempletContractDBMapper wmTempletContractDBMapper;

    @Autowired
    WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    @Autowired
    WmTempletContractSignDBMapper wmTempletContractSignDBMapper;

    @Autowired
    WmTempletContractSignAuditedDBMapper wmTempletContractSignAuditedDBMapper;

    @Autowired
    ContractLogService contractLogService;

    @Autowired
    WmContractVersionService wmContractVersionService;

    @Autowired
    WmCustomerService wmCustomerService;

    @Autowired
    WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    WmSettleService wmSettleService;

    @Autowired
    WmAgentContractThriftService.Iface wmAgentContractThriftService;

    @Autowired
    WmContractPoiProduceService wmContractPoiProduceService;

    @Autowired
    WmEmployeeService wmEmployeeService;

    @Autowired
    protected WmCustomerOplogService wmCustomerOplogService;

    int SCAN_SIZE = 50;

    @Autowired
    private WmContractSignService wmContractSignService;

    @Autowired
    private WmC2ContractAuditedThriftService.Iface wmC2ContractAuditedThriftService;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    /**
     * 保存线下合同（合同同步）
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer saveContractForSync(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("同步保存合同  contractBo:{} opUid:{} opUname:{}", JSON.toJSON(contractBo), opUid, opName);
        if (WmTempletContractTypeEnum.C1_E.getCode() != contractBo.getBasicBo().getType()
                && WmTempletContractTypeEnum.C2_E.getCode() != contractBo.getBasicBo().getType()
                && WmTempletContractTypeEnum.C1_PAPER.getCode() != contractBo.getBasicBo().getType()
                && WmTempletContractTypeEnum.C2_PAPER.getCode() != contractBo.getBasicBo().getType()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,
                    "该合同不支持同步  parentId:" + contractBo.getBasicBo().getParentId() + "  type:" + contractBo.getBasicBo().getType());
        }
        contractBo.getBasicBo().setDueDate(contractBo.getBasicBo().getDueDate() - 1);
        WmCustomerContractBo oldBo = new WmCustomerContractBo();
        WmTempletContractDB wmTempletContractDBForSave = WmTempletContractTransUtil.templetContractBasicBoToDb(contractBo.getBasicBo(), contractBo.getSignBoList());
        wmTempletContractDBForSave.setOpuid(opUid);
        List<WmTempletContractSignDB> wmTempletContractSignDBList = WmTempletContractTransUtil
                .templetSignBoToDbList(contractBo.getSignBoList());

        if (CollectionUtils.isEmpty(wmTempletContractSignDBList)
                || wmTempletContractSignDBList.size() != 2) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约信息不能为空！");
        }
        initContractStatusForSync(contractBo, wmTempletContractDBForSave);
        List<WmTempletContractDB> templetContractDBs = getContractByCustomerIdAndType(contractBo.getBasicBo().getParentId(), wmTempletContractDBForSave.getType());
        if (!CollectionUtils.isEmpty(templetContractDBs)) {
            contractBo.getBasicBo().setTempletContractId(templetContractDBs.get(0).getId());
            contractBo.getBasicBo().setContractNum(templetContractDBs.get(0).getNumber());
            wmTempletContractDBForSave.setId(templetContractDBs.get(0).getId());
            //修改不更新编号
            wmTempletContractDBForSave.setNumber(templetContractDBs.get(0).getNumber());
        }

        //如果数据中存在合同信息则更新，不存在则插入
        if (wmTempletContractDBForSave.getId() != null && wmTempletContractDBForSave.getId() > 0
                && !CollectionUtils.isEmpty(templetContractDBs)) {
            WmTempletContractDB contractOld = templetContractDBs.get(0);
            if (contractOld.getStatus().equals(CustomerContractStatus.SIGNING.getCode())) {
                logger.warn("3.0合同处于签约中，2.0合同不可同步至3.0， 3.0 ：{} ", JSON.toJSON(contractOld));
                return wmTempletContractDBForSave.getId().intValue();
            }
            if (contractOld.getStatus().equals(CustomerContractStatus.AUDITING.getCode())) {
                logger.warn("3.0合同处于审核中，2.0合同不可同步至3.0， 3.0 ：{} ", JSON.toJSON(contractOld));
                return wmTempletContractDBForSave.getId().intValue();
            }
            oldBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(contractOld));
            List<WmTempletContractSignDB> wmTempletContractSignOld = wmTempletContractSignDBMapper.selectByWmTempletContractId(wmTempletContractDBForSave.getId());
            oldBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(wmTempletContractSignOld));
            wmTempletContractSignDBList.get(0).setWmTempletContractId(wmTempletContractDBForSave.getId().intValue());
            wmTempletContractSignDBList.get(0).setOpuid(opUid);
            wmTempletContractSignDBList.get(1).setWmTempletContractId(wmTempletContractDBForSave.getId().intValue());
            wmTempletContractSignDBList.get(1).setOpuid(opUid);
            wmTempletContractDBForSave.setVersion(contractOld.getVersion());
            wmTempletContractDBMapper.updateByPrimaryKeySelective(wmTempletContractDBForSave);
            //补充空字段
            wmCustomerService.fillContractSignInfo(wmTempletContractSignDBList);
            doSaveContractSignInfo(wmTempletContractSignDBList);
            paperEffect(wmTempletContractDBForSave, wmTempletContractSignDBList, opUid, opName);
        } else {
            wmTempletContractDBForSave.setNumber(UUID.randomUUID().toString().replaceAll("-", ""));
            wmTempletContractDBMapper.insertSelective(wmTempletContractDBForSave);
            wmTempletContractDBForSave.setNumber(ContractNumberUtil.genNum(wmTempletContractDBForSave.getType(), wmTempletContractDBForSave.getId().intValue()));
            wmTempletContractDBMapper.updateNumberById(wmTempletContractDBForSave.getId(), wmTempletContractDBForSave.getNumber());
            for (WmTempletContractSignDB db : wmTempletContractSignDBList) {
                db.setWmTempletContractId(wmTempletContractDBForSave.getId().intValue());
                db.setOpuid(opUid);
            }
            //补充空字段
            wmCustomerService.fillContractSignInfo(wmTempletContractSignDBList);
            doSaveContractSignInfo(wmTempletContractSignDBList);
            paperEffect(wmTempletContractDBForSave, wmTempletContractSignDBList, opUid, opName);
            contractBo.getBasicBo().setContractNum(wmTempletContractDBForSave.getNumber());
        }
        contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
        contractBo.getBasicBo().setTempletContractId(wmTempletContractDBForSave.getId());
        return wmTempletContractDBForSave.getId().intValue();
    }

    public void paperEffect(WmTempletContractDB wmTempletContractDBForSave, List<WmTempletContractSignDB> wmTempletContractSignDBList, int opUid, String opName) {
        if (WmTempletContractTypeEnum.isEContract(wmTempletContractDBForSave.getType())) {
            return;
        }
        if (!Integer.valueOf(CustomerContractStatus.EFFECT.getCode()).equals(wmTempletContractDBForSave.getStatus())) {
            return;
        }
        //纸质合同直接生效
        Long contractDBForSaveId = wmTempletContractDBForSave.getId();
        logger.info("线下合同：" + JSON.toJSONString(wmTempletContractDBForSave));
        WmTempletContractDB onlineContract = wmTempletContractAuditedDBMapper.selectByPrimaryKey(contractDBForSaveId);
        if (onlineContract == null) {
            wmTempletContractAuditedDBMapper.insertSelective(wmTempletContractDBForSave);
        } else {
            wmTempletContractDBForSave.setVersion(onlineContract.getVersion());
            wmTempletContractAuditedDBMapper.updateByPrimaryKeySelective(wmTempletContractDBForSave);
            wmTempletContractSignAuditedDBMapper.invalid(contractDBForSaveId, 0);
        }
        List<WmTempletContractSignDB> wmTempletContractSignDBS = wmTempletContractSignDBMapper
                .selectByWmTempletContractId(contractDBForSaveId);
        WmTempletContractSignDB partyASignerDb = wmContractSignService.getPartyASignerDb(wmTempletContractSignDBS);
        WmTempletContractSignDB partyBSignerDb = wmContractSignService.getPartyBSignerDb(wmTempletContractSignDBS);

        WmTempletContractSignDB partyASignerInput = wmContractSignService.getPartyASignerDb(wmTempletContractSignDBList);
        WmTempletContractSignDB partyBSignerInput = wmContractSignService.getPartyBSignerDb(wmTempletContractSignDBList);
        partyASignerInput.setId(partyASignerDb.getId());
        partyBSignerInput.setId(partyBSignerDb.getId());

        doSaveContractSignAuditedInfo(wmTempletContractSignDBList);

        contractLogService.logUpdate(wmTempletContractDBForSave.getParentId().intValue(),
                contractDBForSaveId.intValue(), opUid, opName, "合同编号：" + wmTempletContractDBForSave.getNumber() + "\n纸质2.0合同同步到3.0直接生效");
    }

    public void initContractStatusForSync(WmCustomerContractBo contractBo, WmTempletContractDB wmTempletContractDBForSave) {
        wmTempletContractDBForSave.setStatus(CustomerContractStatus.STAGE.getCode());
        if (WmTempletContractTypeEnum.C1_PAPER.getCode() == wmTempletContractDBForSave.getType()
                && CustomerContractStatus.EFFECT.getCode() == contractBo.getBasicBo().getStatus()) {
            wmTempletContractDBForSave.setStatus(CustomerContractStatus.EFFECT.getCode());
        } else if (WmTempletContractTypeEnum.C2_PAPER.getCode() == wmTempletContractDBForSave.getType()
                && CustomerContractStatus.EFFECT.getCode() == contractBo.getBasicBo().getStatus()) {
            wmTempletContractDBForSave.setStatus(CustomerContractStatus.EFFECT.getCode());
        }
    }

    private void adaptContractNumOutput(WmCustomerContractBo contractBo) throws WmCustomerException {
        if (WmTempletContractTypeEnum.isPaperContract(contractBo.getBasicBo().getType())) {
            //纸质编号不处理，透传给2.0
            return;
        }
        if (contractBo.getBasicBo().getType() == WmTempletContractTypeEnum.C1_E.getCode()) {
            String[] split = contractBo.getBasicBo().getContractNum().split("-");
            contractBo.getBasicBo().setContractNum(split[0] + "-" + split[1] + "-" + split[3].substring(1));
        } else {
            String[] split = contractBo.getBasicBo().getContractNum().split("-");
            contractBo.getBasicBo().setContractNum(split[0] + "-" + split[1] + "-" + split[2] + "-" + split[3].substring(1));
        }
    }

    public List<WmTempletContractDB> getEffectContractByCustomerIdAndType(int customerId, List<Integer> typeList) {
        return wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, typeList);
    }

    public List<WmTempletContractDB> getContractByCustomerIdAndTypes(long customerId, List<Integer> typeList) {
        return wmTempletContractDBMapper.selectByParentIdAndTypes(customerId, typeList);
    }

    public List<WmTempletContractDB> getContractByCustomerIdAndType(long customerId, int type) {
        return wmTempletContractDBMapper.selectByParentIdAndType(customerId, type);
    }

    /**
     * 获取生效数据同步至2.0
     */
    public List<WmCustomerContractBo> getEffectiveContractByCusIdForSync(int customerId, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("根据客户id获取生效的合同  cusId:{} opUid:{} opUname:{}", customerId, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypesMaster(
                customerId,
                Lists.newArrayList(
                        WmTempletContractTypeEnum.C1_E.getCode(),
                        WmTempletContractTypeEnum.C1_PAPER.getCode(),
                        WmTempletContractTypeEnum.C2_E.getCode(),
                        WmTempletContractTypeEnum.C2_PAPER.getCode()
                )
        );

        List<WmCustomerContractBo> wmCustomerContractBoList = Lists.newArrayList();
        for (WmTempletContractDB wmTempletContractDB : MoreObjects
                .firstNonNull(wmTempletContractDBList, Lists.<WmTempletContractDB>newArrayList())) {
            WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
            List<WmTempletContractSignDB> templetContractSignDBS = wmTempletContractSignAuditedDBMapper
                    .selectByWmTempletContractIdMaster(wmTempletContractDB.getId());
            wmTempletContractDB.setDueDate(wmTempletContractDB.getDueDate() + 1);
            wmCustomerContractBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB));
            wmCustomerContractBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(templetContractSignDBS));
            adaptContractNumOutput(wmCustomerContractBo);
            wmCustomerContractBoList.add(wmCustomerContractBo);
        }
        return wmCustomerContractBoList;
    }

    /**
     * 当客户、卡片、结算、合同都为生效中时才能回滚（即任何一个模块处于流程中都不允许回滚）
     * <p>
     * 客户正式表里的状态为最新状态
     * kp新建时最新状态在正式表里，生效后，kp最新状态在tmp表里
     */
    public PoiGrayRollbackCheckResBo checkPoiCanBeGrayRollback(long wmPoiId, int opUid, String opName) throws WmCustomerException {
        logger.info("校验门店是否能灰度回滚  poiId:{}  opUid:{}  opUname:{}", wmPoiId, opUid, opName);
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if (wmCustomerDB == null) {
            return new PoiGrayRollbackCheckResBo(true, Lists.newArrayList(wmPoiId));
        }
        List<Long> poiIds = wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerDB.getId());

        if (wmCustomerDB.getId() >= 10000000) {
            logger.info("客户ID大于一千万，不允许回滚 poiId:{}  customerId:{}  status:{} ", wmPoiId, wmCustomerDB.getId(), wmCustomerDB.getAuditStatus());
            return new PoiGrayRollbackCheckResBo(false, poiIds);
        }

        //客户状态校验
        boolean canRollback = stringArrayToIntList(ConfigUtilAdapter.getString("customer_canrollback_status_array",
                String.valueOf(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode())).split(","))
                .contains(wmCustomerDB.getAuditStatus());
        if (!canRollback) {
            logger.info("客户状态不允许回滚 poiId:{} customerId:{}  status:{} ", wmPoiId, wmCustomerDB.getId(), wmCustomerDB.getAuditStatus());
            return new PoiGrayRollbackCheckResBo(canRollback, poiIds);
        }

        //KP状态校验
        WmCustomerKp effectiveSigner = wmCustomerKpService.getCustomerSignerKp(wmCustomerDB.getId());
        if (effectiveSigner != null) {
            byte kpStatus = effectiveSigner.getState();
            if (effectiveSigner.getState() == KpSignerStateMachine.EFFECT.getState()) {
                WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(effectiveSigner.getId());
                wmCustomerSensitiveWordsService.readKpWhenSelect(kpTemp);
                if (kpTemp != null) {
                    kpStatus = kpTemp.getState();
                }
            }
            canRollback = stringArrayToByteList(ConfigUtilAdapter.getString("customer_kp_canrollback_status_array",
                    String.valueOf(KpSignerStateMachine.EFFECT.getState())).split(","))
                    .contains(kpStatus);
            if (!canRollback) {
                logger.info("KP状态不允许回滚  poiId:{} customerId:{}  status:{}  kpId:{}", wmPoiId, wmCustomerDB.getId(), effectiveSigner.getState(), effectiveSigner.getId());
                return new PoiGrayRollbackCheckResBo(canRollback, poiIds);
            }
        }
        //结算状态校验
        byte settleStatus = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerDB.getId());
        canRollback = stringArrayToByteList(ConfigUtilAdapter.getString("customer_settle_canrollback_status_array",
                WmSettleConstant.SETTLE_STATUS_EFFECT + "," + WmSettleConstant.SETTLE_STATUS_INIT).split(","))
                .contains(settleStatus);
        if (!canRollback) {
            logger.info("结算状态不允许回滚 poiId:{} customerId:{}  status:{}", wmPoiId, wmCustomerDB.getId(), settleStatus);
            return new PoiGrayRollbackCheckResBo(canRollback, poiIds);
        }

        //合同状态校验
        List<WmTempletContractDB> templetContractDBList = getContractByCustomerIdAndTypes(wmCustomerDB.getId().longValue(), Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode()
        ));
        for (WmTempletContractDB db : MoreObjects.firstNonNull(templetContractDBList, Lists.<WmTempletContractDB>newArrayList())) {
            if (db.getStatus() == CustomerContractStatus.STAGE.getCode()) {
                continue;
            }
            canRollback = stringArrayToIntList(ConfigUtilAdapter.getString("customer_contract_canrollback_status_array",
                    String.valueOf(CustomerContractStatus.EFFECT.getCode())).split(","))
                    .contains(db.getStatus());
            if (!canRollback) {
                logger.info("合同状态不允许回滚  poiId:{} customerId:{}  status:{}  templetId:{}", wmPoiId, wmCustomerDB.getId(), db.getStatus(), db.getId());
                return new PoiGrayRollbackCheckResBo(canRollback, poiIds);
            }
        }
        return new PoiGrayRollbackCheckResBo(canRollback, poiIds);
    }

    private List<Integer> stringArrayToIntList(String[] strings) {
        List<Integer> integerList = Lists.newArrayList();
        for (String item : strings) {
            integerList.add(Integer.valueOf(item));
        }
        return integerList;
    }

    private List<Byte> stringArrayToByteList(String[] strings) {
        List<Byte> bytes = Lists.newArrayList();
        for (String item : strings) {
            bytes.add(Byte.valueOf(item));
        }
        return bytes;
    }

    public void syncContractScan(WmCustomerContractScanBo contractScanBo, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("##syncContractScan  contractScanBo:{} opUid:{}  opUname:{}", JSON.toJSON(contractScanBo), opUid, opName);
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByIdRT(contractScanBo.getCustomerId());
        if (wmCustomerDB == null) {
            logger.warn("客户不存在 id:" + contractScanBo.getCustomerId());
            return;
        }
        syncQuaOther(contractScanBo, opUid, opName);
        syncPaperDelivery(contractScanBo, opUid, opName);
        syncPaperExclusive(contractScanBo, opUid, opName);
    }

    private void syncPaperDelivery(WmCustomerContractScanBo contractScanBo, int opUid, String opName) {
        WmTempletContractDB offlineContractDB = wmTempletContractDBMapper
                .selectByParentIdAndTypeFromMaster((long) contractScanBo.getCustomerId(), WmTempletContractTypeEnum.DELIVERY_PAPER.getCode());
        WmTempletContractDB auditedContractDB = wmTempletContractAuditedDBMapper
                .selectByParentIdAndTypeFromMaster((long) contractScanBo.getCustomerId(), WmTempletContractTypeEnum.DELIVERY_PAPER.getCode());
        if (auditedContractDB == null) {
            auditedContractDB = new WmTempletContractDB();
        }
        if (offlineContractDB == null) {
            offlineContractDB = new WmTempletContractDB();
            offlineContractDB.setParentId((long) contractScanBo.getCustomerId());
            offlineContractDB.setType(WmTempletContractTypeEnum.DELIVERY_PAPER.getCode());
            offlineContractDB.setStatus(CustomerContractStatus.STAGE.getCode());
            offlineContractDB.setNumber(UUID.randomUUID().toString().replaceAll("-", ""));
            offlineContractDB.setDueDate(Integer.MAX_VALUE);
            offlineContractDB.setEffectiveDate(0);
        }
        auditedContractDB.setOpuid(opUid);
        offlineContractDB.setOpuid(opUid);
        addScanToDb(auditedContractDB, offlineContractDB, contractScanBo.getDeliveryScan(), opUid, opName);
    }

    private void syncPaperExclusive(WmCustomerContractScanBo contractScanBo, int opUid, String opName) {
        WmTempletContractDB offlineContractDB = wmTempletContractDBMapper
                .selectByParentIdAndTypeFromMaster((long) contractScanBo.getCustomerId(), WmTempletContractTypeEnum.EXCLUSIVE_AGREEMENT_PAPER.getCode());
        WmTempletContractDB auditedContractDB = wmTempletContractAuditedDBMapper
                .selectByParentIdAndTypeFromMaster((long) contractScanBo.getCustomerId(), WmTempletContractTypeEnum.EXCLUSIVE_AGREEMENT_PAPER.getCode());
        if (auditedContractDB == null) {
            auditedContractDB = new WmTempletContractDB();
        }
        if (offlineContractDB == null) {
            offlineContractDB = new WmTempletContractDB();
            offlineContractDB.setParentId((long) contractScanBo.getCustomerId());
            offlineContractDB.setType(WmTempletContractTypeEnum.EXCLUSIVE_AGREEMENT_PAPER.getCode());
            offlineContractDB.setStatus(CustomerContractStatus.STAGE.getCode());
            offlineContractDB.setNumber(UUID.randomUUID().toString().replaceAll("-", ""));
            offlineContractDB.setDueDate(Integer.MAX_VALUE);
            offlineContractDB.setEffectiveDate(0);
        }
        offlineContractDB.setOpuid(opUid);
        auditedContractDB.setOpuid(opUid);
        addScanToDb(auditedContractDB, offlineContractDB, contractScanBo.getExclusiveScan(), opUid, opName);
    }

    private void addScanToDb(WmTempletContractDB auditedContractDB, WmTempletContractDB offlineContractDB, List<String> scanForSync, int opUid, String opName) {
        if (CollectionUtils.isEmpty(scanForSync)) {
            return;
        }
        CustomerPaperContractRemarkBo auditedRemarkBo = JSON.parseObject(auditedContractDB.getOtherItem(), CustomerPaperContractRemarkBo.class);
        CustomerPaperContractRemarkBo offlineRemarkBo = JSON.parseObject(offlineContractDB.getOtherItem(), CustomerPaperContractRemarkBo.class);
        if (!isEmptyScanInDb(offlineRemarkBo)) {
            logger.info("类型：{}  3.0已有该附件，不接收此次同步", WmTempletContractTypeEnum.getByCode(offlineContractDB.getType()).getMsg());
            return;
        }
        if (isOutofSize(scanForSync, auditedRemarkBo) || isOutofSize(scanForSync, offlineRemarkBo)) {
            logger.info("类型：{}  总附件数超过50，不接收此次同步", WmTempletContractTypeEnum.getByCode(offlineContractDB.getType()).getMsg());
            return;
        }

        if (isEmptyScanInDb(auditedRemarkBo)) {
            auditedRemarkBo = new CustomerPaperContractRemarkBo();
            auditedRemarkBo.setContractScan(new MultiFileJsonBo());
        }
        if (isEmptyScanInDb(offlineRemarkBo)) {
            offlineRemarkBo = new CustomerPaperContractRemarkBo();
            offlineRemarkBo.setContractScan(new MultiFileJsonBo());
        }

        offlineRemarkBo.getContractScan().getList().addAll(transStrListToFileList(scanForSync));
        auditedRemarkBo.getContractScan().getList().addAll(transStrListToFileList(scanForSync));

        offlineContractDB.setOtherItem(JSON.toJSONString(offlineRemarkBo));
        auditedContractDB.setOtherItem(JSON.toJSONString(auditedRemarkBo));

        saveOrUpdateScan(auditedContractDB, offlineContractDB);

        logger.info("类型：{}  同步了附件：{}", WmTempletContractTypeEnum.getByCode(offlineContractDB.getType()).getMsg(), scanForSync);
        contractLogService.logUpdate(offlineContractDB.getParentId().intValue(), offlineContractDB.getId().intValue(), opUid, opName,
                "合同编号：" + offlineContractDB.getNumber() + "\n合同类型：" + WmTempletContractTypeEnum.getByCode(offlineContractDB.getType()).getMsg()
                        + "\n同步了附件：" + JSON.toJSONString(scanForSync));
    }

    private void saveOrUpdateScan(WmTempletContractDB auditedContractDB, WmTempletContractDB offlineContractDB) {
        //存在则更新，不存在则插入
        //这里只生效同步过来的附件
        if (offlineContractDB.getId() != null && offlineContractDB.getId() > 0l) {
            wmTempletContractDBMapper.updateByPrimaryKeySelective(offlineContractDB);
            //如果没有线上生效的附件，则生成
            if (auditedContractDB.getId() == null || auditedContractDB.getId() <= 0l) {
                logger.info("没有线上生效的附件，需要生成生效数据");
                String otherItemToAudited = auditedContractDB.getOtherItem();
                TransUtil.transferAll(offlineContractDB, auditedContractDB);
                auditedContractDB.setOtherItem(otherItemToAudited);
                wmTempletContractAuditedDBMapper.insertSelective(auditedContractDB);
            } else {
                wmTempletContractAuditedDBMapper.updateByPrimaryKeySelective(auditedContractDB);
            }
        } else {
            logger.info("没有线下附件，需要生成线下/线上数据");
            wmTempletContractDBMapper.insertSelective(offlineContractDB);
            TransUtil.transferAll(offlineContractDB, auditedContractDB);
            wmTempletContractAuditedDBMapper.insertSelective(auditedContractDB);
        }
    }

    private boolean isEmptyScanInDb(CustomerPaperContractRemarkBo remarkBo) {
        return remarkBo == null || remarkBo.getContractScan() == null
                || size(remarkBo.getContractScan().getList()) == 0;
    }

    private boolean isOutofSize(List<String> scanForSync, CustomerPaperContractRemarkBo remarkBo) {
        return remarkBo != null && remarkBo.getContractScan() != null
                && size(remarkBo.getContractScan().getList()) + size(scanForSync) > SCAN_SIZE;
    }

    private List<MultiFileJsonBo.CustomerFile> transStrListToFileList(List<String> scanForSync) {
        return Lists.transform(scanForSync, new Function<String, MultiFileJsonBo.CustomerFile>() {
            @Nullable
            @Override
            public MultiFileJsonBo.CustomerFile apply(@Nullable String input) {
                return new MultiFileJsonBo.CustomerFile("", input);
            }
        });
    }

    private void syncQuaOther(WmCustomerContractScanBo contractScanBo, int opUid, String opName) throws WmCustomerException {
        if (CollectionUtils.isEmpty(contractScanBo.getOtherScan())) {
            return;
        }
        Set<String> customerQuaOtherList = wmCustomerService.getCustomerQuaOtherListMaster(contractScanBo.getCustomerId());
        if (size(customerQuaOtherList) > 0) {
            logger.info("已经有其他附件了，此次不同步");
            return;
        }
        if (size(customerQuaOtherList) + size(contractScanBo.getOtherScan()) > SCAN_SIZE) {
            logger.info("其他附件超过50张，不同步");
            return;
        }
        customerQuaOtherList.addAll(contractScanBo.getOtherScan());
        wmCustomerService.saveOrUpdateCustomerCommonQuaOtherToDB(contractScanBo.getCustomerId(), customerQuaOtherList);
        WmCustomerOplogBo customerOplogBo = new WmCustomerOplogBo()
                .setCustomerId(contractScanBo.getCustomerId())
                .setModuleType(WmCustomerOplogBo.OpModuleType.CUSTOMER.type)
                .setModuleId(contractScanBo.getCustomerId())
                .setOpType(WmCustomerOplogBo.OpType.UPDATE.type)
                .setLog("从2.0同步了其他附件：" + contractScanBo.getOtherScan())
                .setOpUid(opUid)
                .setOpUname(opName);
        logger.info("同步了其他附件：" + JSON.toJSONString(customerOplogBo));
        try {
            wmCustomerOplogService.insert(customerOplogBo);
        } catch (WmCustomerException e) {
            logger.error(e.getMsg(), e);
        }
    }

    private int size(Collection collection) {
        return collection == null ? 0 : collection.size();
    }

    public void fixEmptyAuditedSign(List<Long> contractIdList, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("修复合同缺失签约人线上表数据 opUid：{}  opName:{}   contractIdList:{}", opUid, opName, contractIdList);
        for (Long contractId : contractIdList) {
            WmTempletContractDB contractDB = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
//            if (contractDB == null || !contractDB.getStatus().equals(CustomerContractStatus.EFFECT.getCode())) {
//                continue;
//            }
            if (contractDB == null || ConfigUtilAdapter.getBoolean("fixEmptyAuditedSign_check_effective", true)
                    && !contractDB.getStatus().equals(CustomerContractStatus.EFFECT.getCode())) {
                continue;
            }
            List<WmTempletContractSignDB> contractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractId(contractId);

            WmTempletContractDB contractAuditedDB = wmTempletContractAuditedDBMapper.selectByPrimaryKey(contractId);
            List<WmTempletContractSignDB> wmTempletContractSignAuditedDBList = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(contractId);
            if (!CollectionUtils.isEmpty(contractSignDBList) && contractAuditedDB != null && CollectionUtils.isEmpty(wmTempletContractSignAuditedDBList)) {
                logger.info("合同id：{}  需要修复签约人数据", contractId);
                doSaveContractSignAuditedInfo(contractSignDBList);
            } else if (size(contractSignDBList) == 2
                    && contractAuditedDB != null && size(wmTempletContractSignAuditedDBList) == 2) {
                if (!contractSignDBList.get(0).getId().equals(wmTempletContractSignAuditedDBList.get(0).getId())
                        || !contractSignDBList.get(1).getId().equals(wmTempletContractSignAuditedDBList.get(1).getId())) {
                    logger.info("合同id：{}  需要修复签约人数据，删除线上表数据", contractId);
                    wmTempletContractSignAuditedDBMapper.deleteByTempletId(contractId);
                    doSaveContractSignAuditedInfo(contractSignDBList);
                }
            } else if (size(contractSignDBList) == 2
                    && contractAuditedDB != null && size(wmTempletContractSignAuditedDBList) < 2) {
                logger.info("合同id：{}  需要修复签约人数据，删除线上表数据", contractId);
                wmTempletContractSignAuditedDBMapper.deleteByTempletId(contractId);
                doSaveContractSignAuditedInfo(contractSignDBList);
            }
        }
        logger.info("修复合同缺失签约人线上表数据 完成 opUid：{}  opName:{} ", opUid, opName);
    }


    public String fixC2AgentId(Long customerId, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("修复C2合同合作商ID opUid：{}  opName:{}   customerId:{}", opUid, opName, customerId);
        if (customerId > 1000 * 10000) {
            return "客户ID大于1千万，customerId:" + customerId;
        }

        //  1.判断合同状态,只处理符合状态的合同（不在流程中）
        StringBuilder msg = new StringBuilder();
        List<WmTempletContractDB> needFixTempletContractList = Lists.newArrayList();
        List<String> fixC2AgentIdStatus = Splitter.on(',').splitToList(ConfigUtilAdapter.getString("shangdan3.fixC2AgentId.Status", ""));
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndTypesMaster(customerId,
                Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(), WmTempletContractTypeEnum.C2_PAPER.getCode()));
        for (WmTempletContractDB wmTempletContractDB : wmTempletContractDBList) {
            if (fixC2AgentIdStatus.contains(wmTempletContractDB.getStatus().toString())) {
                needFixTempletContractList.add(wmTempletContractDB);
            } else  {
                msg.append("C2合同").append(wmTempletContractDB.getNumber()).append("在流程中。");
            }
        }

        //  2.处理合同合作商ID
        //  2.1 获取2.0系统的C2合同，并判断是否需要处理。
        WmC2ContractBo wmC2ContractBo = null;
        try {
            wmC2ContractBo = wmC2ContractAuditedThriftService.getC2ContractAuditedByWmContractId(customerId.intValue());
        } catch (WmServerException e) {
            return "从2.0系统获取C2合同失败，customerId:" + customerId;
        }
        if (wmC2ContractBo == null || wmC2ContractBo.getWmC2ContractBasicBo() == null || wmC2ContractBo.getWmC2ContractSignBo() == null) {
            return "2.0系统的C2合同信息不完整，customerId:" + customerId;
        }
        if (wmC2ContractBo.getWmC2ContractBasicBo().getPartyBId() == wmC2ContractBo.getWmC2ContractSignBo().getPartyBId()) {
            return "2.0系统的C2的合作商ID一致，customerId:" + customerId;
        }
        WmC2ContractBasicBo basicBo = wmC2ContractBo.getWmC2ContractBasicBo();

        //  2.2 更新线下表和线上表
        for (WmTempletContractDB wmTempletContractDB : needFixTempletContractList) {
            // 2.2.1 更新线下表
            List<WmTempletContractSignDB> contractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractId(wmTempletContractDB.getId());
            for (WmTempletContractSignDB signDB : contractSignDBList) {
                if ("B".equals(signDB.getSignType()) && signDB.getSignId() != basicBo.getPartyBId() && signDB.getSignName().equals(basicBo.getPartyBName())) {
                    signDB.setSignId(basicBo.getPartyBId());
                    wmTempletContractSignDBMapper.updateByPrimaryKeySelective(signDB);
                }
            }

            // 2.2.1 更新线上表
            contractSignDBList = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(wmTempletContractDB.getId());
            for (WmTempletContractSignDB signDB : contractSignDBList) {
                if ("B".equals(signDB.getSignType()) && signDB.getSignId() != basicBo.getPartyBId() && signDB.getSignName().equals(basicBo.getPartyBName())) {
                    signDB.setSignId(basicBo.getPartyBId());
                    wmTempletContractSignAuditedDBMapper.updateByPrimaryKeySelective(signDB);
                }
            }
            msg.append("更新").append(wmTempletContractDB.getNumber()).append("成功。");
        }
        return msg.toString();
    }

    private void doSaveContractSignInfo(List<WmTempletContractSignDB> signDBList) {
        if (MccConfig.isEncryptionInsertBySingle()) {
            for (WmTempletContractSignDB signDB : signDBList) {
                wmTempletContractSignDBMapper.insert(signDB);
            }
        } else {
            wmTempletContractSignDBMapper.batchSave(signDBList);
        }
    }

    private void doSaveContractSignAuditedInfo(List<WmTempletContractSignDB> signDBList) {
        if (MccConfig.isEncryptionInsertBySingle()) {
            for (WmTempletContractSignDB signDB : signDBList) {
                wmTempletContractSignAuditedDBMapper.insert(signDB);
            }
        } else {
            wmTempletContractSignAuditedDBMapper.batchInsertOrUpdate(signDBList);
        }
    }
}
