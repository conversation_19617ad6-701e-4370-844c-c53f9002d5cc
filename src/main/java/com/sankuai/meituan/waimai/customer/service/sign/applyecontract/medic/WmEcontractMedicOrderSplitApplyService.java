package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.medic;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.AbstractWmEcontractApplyAdapterService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampShenzhenBaiShouWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * -@author: huangjianlou
 * -@description: pdf -> 商家CA认证 -> 乙方盖章（深圳百寿） -> 发短信 —> 甲方盖章（商家）-> 合同归档 -> 完成合同
 * -@date: 2022/12/28 10:57 PM
 */
@Service
public class WmEcontractMedicOrderSplitApplyService extends AbstractWmEcontractApplyAdapterService {

    private static final String FLOW_MEDIC_ORDER_SPLIT = EcontractTaskApplyTypeEnum.MEDIC_ORDER_SPLIT.getName();

    private static List<String> flowList = Lists.newArrayList();

    private static List<String> poiStampList = Lists.newArrayList();

    private static List<String> mtStampList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_MEDIC_ORDER_SPLIT);

        poiStampList.add(FLOW_MEDIC_ORDER_SPLIT);

        mtStampList.add(FLOW_MEDIC_ORDER_SPLIT);

        dataWrapperMap.put(FLOW_MEDIC_ORDER_SPLIT, EcontractDataWrapperEnum.MEDIC_ORDER_SPLIT);
    }


    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Resource
    private WmEcontractStampShenzhenBaiShouWrapperService wmEcontractStampShenzhenBaiShouWrapperService;

    public static final String TYPE_MEDIC_ORDER_SPLIT_STATEMENT = "type_medic_order_split_statement";


    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, poiStampList));
        batchInfoBoList.add(wmEcontractStampShenzhenBaiShouWrapperService.wrap(batchContextBo, mtStampList));

        return new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(TYPE_MEDIC_ORDER_SPLIT_STATEMENT)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .callBackUrl(getCallbackUrl()).econtractBatchSource(getSource(batchContextBo))
                .build();
    }


}
