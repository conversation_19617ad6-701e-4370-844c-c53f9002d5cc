package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.filter;

import java.util.List;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.IWmEcontractDataCollector;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;

/**
 * <AUTHOR>
 */
@Slf4j
public class EcontractDataCollectFilter {

    private List<IWmEcontractDataCollector> collectors = Lists.newArrayList();

    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext)
            throws WmCustomerException, IllegalAccessException, TException {
        for (IWmEcontractDataCollector collector : collectors) {
            try {
                collector.collect(originContext, middleContext, targetContext);
            } catch (WmCustomerException e) {
                log.info(e.getMsg(), e);
                throw e;
            }
        }
    }

    private void registry(IWmEcontractDataCollector collector) {
        collectors.add(collector);
    }

    private static EcontractDataCollectFilter econtractDataCollectFilter = new EcontractDataCollectFilter();

    static {
        // 1.pdf收集
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataPdfMappingCollector"));
        // 2.确认tab
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataFlowCollector"));
        // 3.数据-模板映射
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractPdfDataSplitCollector"));
        // 4.确定签约流程
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataSignTypeCollector"));
        // 5.pdf数据组装
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractPdfContentCollector"));
        // 6.签章数据组装
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataStampCollector"));
        // 7.数据触达组装
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataReachCollector"));
        // 8.SLA数据组装
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataSlaInfoCollector"));
        // 9.配送范围数据组装
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataAreaInfoCollector"));
        // 10.diff数据组装
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataShowDiffCollector"));
        // 11.特殊交互数据组装
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataSpInteractionCollector"));
        // 12.数据重写
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataRewriteCollector"));
        // 13.任务来源
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataSourceCollector"));
        // 14.外部签约数据来源信息
        econtractDataCollectFilter.registry((IWmEcontractDataCollector) SpringBeanUtil.getBean("wmEcontractDataApplySubDataCollector"));
    }

    public static EcontractDataCollectFilter getCollectFilter() {
        return econtractDataCollectFilter;
    }
}
