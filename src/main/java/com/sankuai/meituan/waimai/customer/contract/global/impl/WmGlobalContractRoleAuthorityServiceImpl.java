package com.sankuai.meituan.waimai.customer.contract.global.impl;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.uac.UacRoleRemoteServiceAdapter;
import com.sankuai.meituan.waimai.customer.contract.dao.WmGlobalContractRoleAuthorityDBMapper;
import com.sankuai.meituan.waimai.customer.contract.global.IWmGlobalContractRoleAuthorityService;
import com.sankuai.meituan.waimai.customer.domain.global.WmGlobalContractRoleAuthorityDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.PageInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.ContractRoleInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.ContractRoleSaveParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.EContractRoleInfoResponse;
import com.sankuai.meituan.waimai.thrift.customer.constant.global.GlobalContractEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.UacRoleEntity;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限表service
 *
 * @Author: wangyongfang
 * @Date: 2024-01-15
 */
@Service
public class WmGlobalContractRoleAuthorityServiceImpl implements IWmGlobalContractRoleAuthorityService {

    @Autowired
    private WmGlobalContractRoleAuthorityDBMapper wmGlobalContractRoleAuthorityDBMapper;

    @Autowired
    private UacRoleRemoteServiceAdapter uacRoleRemoteServiceAdapter;

    @Override
    public EContractRoleInfoResponse selectAllListByPageParam(int startPageNum, int pageSize) {
        List<ContractRoleInfo> pageContractRoleInfos = selectPageContractRoleInfo(startPageNum, pageSize);
        if (CollectionUtils.isEmpty(pageContractRoleInfos)) {
            return buildEContractRoleInfoResponse(new ArrayList<>(), startPageNum, pageSize);
        }
        List<Integer> contractTypeCodes = pageContractRoleInfos.stream().map(ContractRoleInfo::getContractTypeCode).collect(Collectors.toList());
        List<WmGlobalContractRoleAuthorityDB> wmGlobalContractRoleAuthorityDBList = wmGlobalContractRoleAuthorityDBMapper.selectByContractTypeCodes(contractTypeCodes);
        return buildEContractRoleInfoResponse(assembleContractRoleInfoList(pageContractRoleInfos, wmGlobalContractRoleAuthorityDBList), startPageNum, pageSize);
    }

    /**
     * 填充权限字段
     *
     * @param contractRoleInfos
     * @param wmGlobalContractRoleAuthorityDBList
     * @return
     */
    @VisibleForTesting
    protected List<ContractRoleInfo> assembleContractRoleInfoList(List<ContractRoleInfo> contractRoleInfos, List<WmGlobalContractRoleAuthorityDB> wmGlobalContractRoleAuthorityDBList) {
        // 没有权限，则直接返回
        if (CollectionUtils.isEmpty(wmGlobalContractRoleAuthorityDBList)) {
            return contractRoleInfos;
        }
        Map<Integer, List<WmGlobalContractRoleAuthorityDB>> wmGlobalContractRoleAuthorityDBListMap =
                wmGlobalContractRoleAuthorityDBList.stream()
                        .collect(Collectors.groupingBy(WmGlobalContractRoleAuthorityDB::getContractTypeCode));

        Map<Long, UacRoleEntity> uacRoleEntityMap = fetchUacRoleEntityMapByIds(wmGlobalContractRoleAuthorityDBList.stream()
                        .map(e -> Long.parseLong(e.getUacRoleCode()))
                        .collect(Collectors.toList()));
        for (ContractRoleInfo contractRoleInfo : contractRoleInfos) {
            if (CollectionUtils.isNotEmpty(wmGlobalContractRoleAuthorityDBListMap.get(contractRoleInfo.getContractTypeCode()))) {
                List<UacRoleEntity> uacRoleEntityList = wmGlobalContractRoleAuthorityDBListMap.get(contractRoleInfo.getContractTypeCode())
                        .stream()
                        .map(e -> uacRoleEntityMap.get(Long.parseLong(e.getUacRoleCode())))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                contractRoleInfo.setRoleList(uacRoleEntityList);
            }
        }
        return contractRoleInfos;
    }

    private Map<Long, UacRoleEntity> fetchUacRoleEntityMapByIds(List<Long> roleIds) {
        List<UacRoleEntity> uacRoleEntityList = uacRoleRemoteServiceAdapter.queryUacRoleByIds(roleIds);
        if (CollectionUtils.isEmpty(uacRoleEntityList)) {
            return new HashMap<>();
        }
        return uacRoleEntityList.stream().collect(Collectors.toMap(UacRoleEntity::getRoleId, e -> e));
    }

    /**
     * 全集存储在枚举中
     *
     * @param startPageNum
     * @param pageSize
     * @return
     */
    @VisibleForTesting
    protected List<ContractRoleInfo> selectPageContractRoleInfo(int startPageNum, int pageSize) {
        if (startPageNum <= 0 || pageSize <= 0) {
            throw new IllegalArgumentException("startPageNum and pageSize must be greater than 0");
        }
        List<GlobalContractEnum> allContractCategoryEnumList = Arrays.stream(GlobalContractEnum.values())
                .filter(o -> GlobalContractEnum.THIRD_PARTY_CONTRACT != o)
                .collect(Collectors.toList());
        List<List<GlobalContractEnum>> partitionList = Lists.partition(allContractCategoryEnumList, pageSize);
        if (startPageNum > partitionList.size()) {
            return new ArrayList<>();
        }
        return partitionList.get(startPageNum - 1).stream()
                        .map(contractCategoryEnum -> buildContractRoleInfo(contractCategoryEnum))
                        .collect(Collectors.toList());
    }

    @Override
    public boolean saveContractRole(ContractRoleSaveParam contractRoleSaveParam) {
        if (contractRoleSaveParam.getContractTypeCode() < 0) {
            throw new IllegalArgumentException("Invalid contract type code");
        }
        // 判断是否为第三方合同，根据合同类型编码获取合同描述，如果合同描述与第三方合同相等，则执行下面的逻辑。
        if (GlobalContractEnum.THIRD_PARTY_CONTRACT.getContractDesc()
                .equals(GlobalContractEnum.getContractDesc(contractRoleSaveParam.getContractTypeCode()))) {
            throw new IllegalArgumentException("输入的合同类型不存在");
        }
        List<WmGlobalContractRoleAuthorityDB> wmGlobalContractRoleAuthorityDBList = wmGlobalContractRoleAuthorityDBMapper.selectByContractTypeCodes(Lists.newArrayList(contractRoleSaveParam.getContractTypeCode()));
        return updateWmGlobalContractRoleAuthorityDBList(contractRoleSaveParam, wmGlobalContractRoleAuthorityDBList);
    }

    @Transactional(rollbackFor = Exception.class)
    private boolean updateWmGlobalContractRoleAuthorityDBList(ContractRoleSaveParam contractRoleSaveParam, List<WmGlobalContractRoleAuthorityDB> wmGlobalContractRoleAuthorityDBList) {
        List<WmGlobalContractRoleAuthorityDB> insertList = new ArrayList<>();
        List<WmGlobalContractRoleAuthorityDB> deleteList = new ArrayList<>();
        splitInsertAndDelete(contractRoleSaveParam, wmGlobalContractRoleAuthorityDBList, insertList, deleteList);
        batchSaveWmGlobalContractRoleAuthorityDB(insertList);
        deleteWmGlobalContractRoleAuthorityDB(deleteList);
        return true;
    }

    /**
     * 拆分插入和更新的列表
     *
     * @param contractRoleSaveParam
     * @param wmGlobalContractRoleAuthorityDBList
     * @param insertList
     * @param deleteList
     */
    @VisibleForTesting
    protected void splitInsertAndDelete(ContractRoleSaveParam contractRoleSaveParam,
                                      List<WmGlobalContractRoleAuthorityDB> wmGlobalContractRoleAuthorityDBList,
                                      List<WmGlobalContractRoleAuthorityDB> insertList,
                                      List<WmGlobalContractRoleAuthorityDB> deleteList) {

        Set<String> roleNameList = Optional.ofNullable(contractRoleSaveParam.getRoleIdList()).orElse(new ArrayList<>())
                .stream()
                .map(String::valueOf).collect(Collectors.toSet());
        Set<String> exitRoleNameSet = new HashSet<>();
        // 删除不在新的角色列表中的
        if (CollectionUtils.isNotEmpty(wmGlobalContractRoleAuthorityDBList)) {
            for (WmGlobalContractRoleAuthorityDB wmGlobalContractRoleAuthorityDB : wmGlobalContractRoleAuthorityDBList) {
                // 如果存在，但是角色名称不在新的角色列表中，则删除
                if (!roleNameList.contains(wmGlobalContractRoleAuthorityDB.getUacRoleCode())) {
                    deleteList.add(wmGlobalContractRoleAuthorityDB);
                }
                exitRoleNameSet.add(wmGlobalContractRoleAuthorityDB.getUacRoleCode());
            }
        }
        for (String roleName : roleNameList) {
            // 如果不存在，则插入
            if (!exitRoleNameSet.contains(roleName)) {
                insertList.add(buildWmGlobalContractRoleAuthorityDB(contractRoleSaveParam.getContractTypeCode(), roleName));
            }
        }
    }

    /**
     * 构建插入合同角色权限实体
     *
     * @param contractTypeCode
     * @param roleName
     * @return
     */
    private WmGlobalContractRoleAuthorityDB buildWmGlobalContractRoleAuthorityDB(Integer contractTypeCode, String roleName) {
       return WmGlobalContractRoleAuthorityDB.builder()
               .contractTypeCode(contractTypeCode)
               .uacRoleCode(roleName)
               .valid(1)
               .build();
    }

    /**
     * 批量插入
     *
     * @param wmGlobalContractRoleAuthorityDBList
     * @return
     */
    private boolean batchSaveWmGlobalContractRoleAuthorityDB(List<WmGlobalContractRoleAuthorityDB> wmGlobalContractRoleAuthorityDBList) {
        if (CollectionUtils.isEmpty(wmGlobalContractRoleAuthorityDBList)) {
            return true;
        }
        wmGlobalContractRoleAuthorityDBList.forEach(wmGlobalContractRoleAuthorityDB -> wmGlobalContractRoleAuthorityDBMapper.insertSelective(wmGlobalContractRoleAuthorityDB));
        return true;
    }

    /**
     * 批量插入
     *
     * @param wmGlobalContractRoleAuthorityDBList
     * @return
     */
    private boolean deleteWmGlobalContractRoleAuthorityDB(List<WmGlobalContractRoleAuthorityDB> wmGlobalContractRoleAuthorityDBList) {
        if (CollectionUtils.isEmpty(wmGlobalContractRoleAuthorityDBList)) {
            return true;
        }
        List<Long> ids = wmGlobalContractRoleAuthorityDBList.stream().map(WmGlobalContractRoleAuthorityDB::getId).collect(Collectors.toList());
        wmGlobalContractRoleAuthorityDBMapper.deleteByIds(ids);
        return true;
    }

    /**
     * 将DB数据转换为接口数据
     *
     * @param contractCategoryEnum
     * @return
     */
    private ContractRoleInfo buildContractRoleInfo(GlobalContractEnum contractCategoryEnum) {
        ContractRoleInfo contractRoleInfo = new ContractRoleInfo();
        contractRoleInfo.setContractTypeCode(contractCategoryEnum.getContractTypeCode());
        contractRoleInfo.setContractType(contractCategoryEnum.getContractDesc());
        contractRoleInfo.setRoleList(new ArrayList<>());
        return contractRoleInfo;
    }

    /**
     * 构造返回结果
     *
     * @param contractRoleInfos
     * @param startPageNum
     * @param pageSize
     * @return
     */
    private EContractRoleInfoResponse buildEContractRoleInfoResponse(List<ContractRoleInfo> contractRoleInfos,
                                                                     int startPageNum, int pageSize) {
        // 类型总数
        long totalSize = GlobalContractEnum.values().length - 1;
        PageInfoDTO pageInfoDTO = PageInfoDTO.builder()
                .totalCount(totalSize)
                .pageNo(startPageNum)
                .pageSize(pageSize)
                .build();
        EContractRoleInfoResponse eContractRoleInfoResponse = new EContractRoleInfoResponse();
        eContractRoleInfoResponse.setContractRoleInfoList(contractRoleInfos);
        eContractRoleInfoResponse.setPageInfoDTO(pageInfoDTO);
        return eContractRoleInfoResponse;
    }
}
