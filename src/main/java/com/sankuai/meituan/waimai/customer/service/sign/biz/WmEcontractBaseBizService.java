package com.sankuai.meituan.waimai.customer.service.sign.biz;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractBaseDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmEContractSignBaseDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBaseInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerKPBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Objects;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class WmEcontractBaseBizService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractBaseBizService.class);

    @Resource
    private WmEcontractBaseDBMapper wmEcontractBaseDBMapper;

    @Resource
    private WmCustomerService wmCustomerService;

    @Resource
    private WmCustomerKpService wmCustomerKpService;

    public WmEContractSignBaseDB getByCustomerId(Integer customerId) throws WmCustomerException {
        WmEContractSignBaseDB baseDB = wmEcontractBaseDBMapper.queryByCustomerId(customerId);
        if (baseDB == null) {
            baseDB = new WmEContractSignBaseDB();
            baseDB.setCustomerId(customerId);
        }
        EcontractBaseInfoBo baseInfoBo = new EcontractBaseInfoBo();
        baseInfoBo.setCustomerId(customerId);

        WmCustomerDB basicBo = wmCustomerService.selectEffectCustomerById(customerId);
        EcontractCustomerInfoBo customerInfoBo = null;
        if (basicBo != null) {
            customerInfoBo = new EcontractCustomerInfoBo.Builder()
                    .customerName(basicBo.getCustomerName())
                    .address(basicBo.getAddress())
                    .legalPerson(basicBo.getLegalPerson())
                    .quaNum(basicBo.getCustomerNumber())
                    .quaTypeEnum(CustomerType.getByCode(basicBo.getCustomerType()))
                    .customerSecondType(basicBo.getCustomerSecondType())
                    .customerNumber(basicBo.getCustomerNumber())
                    .customerRealType(basicBo.getCustomerRealType())
                    .build();
        }
        baseInfoBo.setCustomerInfoBo(customerInfoBo);

        WmCustomerKp signerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(customerId);
        EcontractCustomerKPBo kpBo = null;
        if (signerKp != null) {
            kpBo = new EcontractCustomerKPBo.Builder()
                .kpTypeEnum(KpTypeEnum.SIGNER)
                .kpSignerTypeEnum(KpSignerTypeEnum.getByType(signerKp.getSignerType()))
                .certType(signerKp.getCertType())
                .certNumber(signerKp.getCertNumber())
                .signerName(signerKp.getCompellation())
                .signerIDCardNum(signerKp.getCertNumber())
                .signerPhoneNum(signerKp.getPhoneNum())
                .signerBankName(signerKp.getBankName())
                .signerBankCardNum(signerKp.getCreditCard())
                .signerEmail(signerKp.getEmail())
                .build();
        }
        baseInfoBo.setKpBo(kpBo);

        baseDB.setBaseContext(JSON.toJSONString(baseInfoBo));
        LOGGER.info("WmEcontractBaseBizService.getByCustomerId = {}", JSON.toJSONString(baseDB));
        return baseDB;
    }

    /**
     * 根据customerId查询签约基本信息
     */
    public WmEContractSignBaseDB queryByCustomerId(Integer customerId) throws WmCustomerException {
        return getByCustomerId(customerId);
    }

    /**
     * 保存Base信息
     */
    public int save(WmEContractSignBaseDB baseDB) {
        if (baseDB.getId() == null || baseDB.getId() == 0L) {
            return insert(baseDB);
        } else {
            if (Objects.nonNull(baseDB) && Objects.nonNull(baseDB.getBaseContext())) {
                Cat.logEvent(MetricConstant.METRIC_SIGN_BASE_UPDATE,
                    "update",
                    WmCustomerConstant.SUCCESS, "");

                MetricHelper.build()
                    .name(MetricConstant.METRIC_SIGN_BASE_UPDATE)
                    .count();
            }
            return updateByCustomerId(baseDB);
        }
    }

    /**
     * 新建客户base信息
     */
    public int insert(WmEContractSignBaseDB baseDB) {
        return wmEcontractBaseDBMapper.insert(baseDB);
    }

    /**
     * 根据客户ID更新客户base信息
     */
    public int updateByCustomerId(WmEContractSignBaseDB baseDB) {
        return wmEcontractBaseDBMapper.updateByCustomerId(baseDB);
    }

    /**
     * 删除base信息
     * @param customerId
     *      客户ID
     */
    public int deleteBase(Integer customerId) {
        WmEContractSignBaseDB signBaseDB = new WmEContractSignBaseDB();
        signBaseDB.setBaseContext(JSON.toJSONString(new EcontractBaseInfoBo.Builder().build()));
        return wmEcontractBaseDBMapper.updateByCustomerId(signBaseDB);
    }

}
