package com.sankuai.meituan.waimai.customer.constant.customer;

/**
 * 企客操作类型
 */
public enum WmCustomerQikeOperateTypeEnum {
    CREATE_CUSTOMER(1, "创建企客客户"),
    BIND_POI(2, "企客门店绑定"),
    UNBIND_POI(3, "企客门店解绑");
    private int code;

    private String desc;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    WmCustomerQikeOperateTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WmCustomerQikeOperateTypeEnum of(Integer code) {
        if (code == null) {
            return null;
        }
        for (WmCustomerQikeOperateTypeEnum wmCustomerQikeOperateTypeEnum : values()) {
            if (code == wmCustomerQikeOperateTypeEnum.getCode()) {
                return wmCustomerQikeOperateTypeEnum;
            }
        }
        return null;
    }
}
