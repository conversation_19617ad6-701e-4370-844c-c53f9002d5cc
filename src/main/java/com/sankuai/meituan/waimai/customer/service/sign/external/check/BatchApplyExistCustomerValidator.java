package com.sankuai.meituan.waimai.customer.service.sign.external.check;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.bo.sign.BatchApplyManualPackContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class BatchApplyExistCustomerValidator implements IBatchApplyManualPackValidator {

    @Autowired
    private WmCustomerService wmCustomerService;

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchApplyExistCustomerValidator.class);

    @Override
    public void valid(BatchApplyManualPackContext context) throws WmCustomerException {
        LOGGER.info("BatchApplyExistCustomerValidator before: customerId = {}", JSON.toJSONString(context.getCustomerIdList()));
        List<Long> customerIdList = context.getCustomerIdList();
        Map resultMap = context.getResultMap();

        List<WmCustomerBasicBo> wmCustomerBasicBoList = Lists.newArrayList();
        try {
            wmCustomerBasicBoList = wmCustomerService.getCustomerListByIdOrMtCustomerId(Sets.newHashSet(customerIdList));
        } catch (WmCustomerException e) {
            LOGGER.warn("查询客户异常", e);
            for (Long customerId : customerIdList) {
                resultMap.put(customerId, "系统异常");
            }
        }
        context.setWmCustomerBasicBoList(wmCustomerBasicBoList);
        if (CollectionUtils.isEmpty(wmCustomerBasicBoList)) {
            context.setNeedProcess(false);
            return;
        }

        List<Long> existCustomerIdList = Lists.newArrayList();
        for (WmCustomerBasicBo wmCustomerBasicBo : wmCustomerBasicBoList) {
            existCustomerIdList.add(Long.valueOf(wmCustomerBasicBo.getId()));
            existCustomerIdList.add(wmCustomerBasicBo.getMtCustomerId());
        }

        Set<Long> notExistCustomerIdSet = Sets.difference(Sets.newHashSet(customerIdList), Sets.newHashSet(existCustomerIdList));
        for (Long customerId : notExistCustomerIdSet) {
            resultMap.put(customerId, "客户不存在");
        }
        customerIdList.retainAll(existCustomerIdList);
        LOGGER.info("BatchApplyExistCustomerValidator after: customerId = {}, notExistCustomerId = {}", JSON.toJSONString(customerIdList), JSON.toJSONString(notExistCustomerIdSet));
    }
}
