package com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.aggre.factory;

import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleDB;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import java.util.List;

import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.aggre.WmSettleBusinessQueryContext;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleSwitchService;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class WmSettleBusinessQueryContextFactory {

    @Autowired
    private WmSettleSwitchService wmSettleSwitchService;
    @Autowired
    private WmPoiSettleAuditedDBMapper wmPoiSettleAuditedDBMapper;

    public WmSettleBusinessQueryContext makeContext(List<Long> wmPoiIdList, Long targetWmCustomerId) throws WmCustomerException {
        WmSettleBusinessQueryContext context = new WmSettleBusinessQueryContext();

        List<SwitchPoiInfo> settleSwitchPoiInfoListFilter = wmSettleSwitchService.getSettleSwitchPoiInfoListByWmPoiList(targetWmCustomerId.intValue(),wmPoiIdList);
        context.setSettleSwitchPoiInfoListFilter(settleSwitchPoiInfoListFilter);
        context.setOriginWmPoiIdList(wmPoiIdList);

        //没有切换信息-查询结算数据库数据
        if(CollectionUtils.isEmpty(settleSwitchPoiInfoListFilter)){
            List<WmPoiSettleAuditedDB> wmPoiSettleAuditedDBList = wmPoiSettleAuditedDBMapper
                    .getByContractIdAndWmPoiIdList(targetWmCustomerId.intValue(), wmPoiIdList);
            List<Long> wmPoiIdOnlineList = ObjectUtil.intList2LongList(wmPoiSettleAuditedDBList.stream().map(i -> i.getWm_poi_id())
                    .collect(Collectors.toList()));
            context.setWmPoiIdOnlineList(wmPoiIdOnlineList);
        }

        log.info("#WmSettleBusinessQueryContextFactory#makeContext={}", JSONObject.toJSONString(context));
        return context;
    }
}
