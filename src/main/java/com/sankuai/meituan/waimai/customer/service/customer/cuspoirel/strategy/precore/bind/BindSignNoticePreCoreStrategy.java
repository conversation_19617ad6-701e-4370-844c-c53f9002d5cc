package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.bind;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerTaskQueryBO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindSignNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 * @desc 预绑定前置核心操作策略
 * @date 20240116
 */
@Slf4j
@Service
public class BindSignNoticePreCoreStrategy implements IBindPreCoreStrategy {

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Override
    public void execute(CustomerPoiBindFlowContext context) throws WmCustomerException {
        log.info("BindSignNoticePreCoreStrategy.execute,预绑定签约回调通知开始执行前置核心处理");
        bindSignNoticePreAction(context);
    }

    /**
     * 门店预绑定客户
     *
     * @param context
     */
    public void bindSignNoticePreAction(CustomerPoiBindFlowContext context) throws WmCustomerException {
        //获取本批次的门店ID列表
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        //设置客户DB信息以及门店ID列表
        context.setWmCustomerDB(context.getWmCustomerDB());

        // 获取客户任务
        WmCustomerTaskQueryBO customerTaskQueryBo = new WmCustomerTaskQueryBO();
        customerTaskQueryBo.setTaskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode());
        customerTaskQueryBo.setCustomerId(context.getCustomerId());
        customerTaskQueryBo.setWmPoiIds(Lists.newArrayList(wmPoiIdSet));
        customerTaskQueryBo.setSignTaskId(context.getBindSignNoticeDTO().getWmCustomerPoiSmsRecordDB().getId());
        customerTaskQueryBo.setBizTaskId(0);
        customerTaskQueryBo.setStatus(CustomerTaskStatusEnum.DOING.getCode());
        Map<Long, Integer> poiAndTaskMaps = customerTaskService.getTaskWmPoiMapByCondition(customerTaskQueryBo);
        //设置关联绑定任务ID至上下文
        context.setPoiAndTaskMaps(poiAndTaskMaps);

        BindSignNoticeDTO bindSignNoticeDTO = context.getBindSignNoticeDTO();
        Long signBindRelTaskId = bindSignNoticeDTO.getSignBindRelTaskId();

        //更新签约短信记录状态
        wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(bindSignNoticeDTO.getSignResult(), signBindRelTaskId);
        log.info("BindSignNoticePreCoreStrategy.bindSignNoticePreAction,更新绑定签约短信记录完成,signBindRelTaskId={}", signBindRelTaskId);

    }
}
