package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class ContractDuplicateNumberAtomValidator implements IContractValidator {

    @Autowired
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {
        //电子合同系统自动生成编号，不需要校验
        if (WmTempletContractTypeEnum.isEContract(contractBo.getBasicBo().getType())) {
            return true;
        }

        List<WmTempletContractDB> templetContractDBList = wmTempletContractDBMapper
                .selectByContractNumber(contractBo.getBasicBo().getContractNum());
        if (!CollectionUtils.isEmpty(templetContractDBList)) {
            if (templetContractDBList.size() > 1) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "已存在该编号合同");
            }
            if (contractBo.getBasicBo().getTempletContractId() <= 0) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "已存在该编号合同");
            }
            if (templetContractDBList.get(0).getId() != contractBo.getBasicBo().getTempletContractId()) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "已存在该编号合同");
            }
        }
        return true;
    }

}
