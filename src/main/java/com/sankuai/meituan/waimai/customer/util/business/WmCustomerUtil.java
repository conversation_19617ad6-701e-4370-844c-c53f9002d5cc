package com.sankuai.meituan.waimai.customer.util.business;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;

import java.util.Objects;
import java.util.Set;

/**
 * WmCustomerUtil
 *
 * <AUTHOR>
 * @date 2020/12/3
 */
public class WmCustomerUtil {

    public static Boolean checkIsYaoPin(int customerRealType) {
        String yaopinTypeIds = MccConfig.getCanSwitchCustomerTypeYaoPinList();
        Set<String> yaopinTypesIdsSet = Sets.newHashSet(Splitter.on(",").trimResults().splitToList(yaopinTypeIds));
        return yaopinTypesIdsSet.contains(String.valueOf(customerRealType));
    }

    public static Boolean checkSwitch(int customerRealTypeBefore, int customerRealTypeAfter) {
        return checkIsYaoPin(customerRealTypeBefore) && !checkIsYaoPin(customerRealTypeAfter)
                || checkIsYaoPin(customerRealTypeAfter) && !checkIsYaoPin(customerRealTypeBefore);
    }

    /**
     * 判断客户类型是否为闪购单店
     */
    public static boolean isSgDanDian(Integer customerRealType) {
        return Objects.nonNull(customerRealType) && customerRealType.equals(CustomerRealTypeEnum.DANDIAN_SG.getValue());
    }
}
