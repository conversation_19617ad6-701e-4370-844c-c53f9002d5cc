package com.sankuai.meituan.waimai.customer.service.sc.delivery.auditTask;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScMetadataMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryFollowUpMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryStreamDetailMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryFollowUpDO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryStreamDetailDO;
import com.sankuai.meituan.waimai.customer.domain.sc.metadata.WmScMetadataDO;
import com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.auditflow.WmSchoolDeliveryAuditTaskService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.basic.WmSchoolDeliveryFollowUpBasicServiceImpl;
import com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmSchoolDeliveryFlowAbility;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliveryFollowUpDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliverySaveParamDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

@Slf4j
@Service
public class WmSchoolDeliveryFollowUpTaskServiceImpl implements WmSchoolDeliveryTaskService {

    @Autowired
    private WmSchoolDeliveryFollowUpMapper wmSchoolDeliveryFollowUpMapper;

    @Autowired
    private WmSchoolDeliveryStreamDetailMapper wmSchoolDeliveryStreamDetailMapper;

    @Autowired
    private WmScMetadataMapper wmScMetadataMapper;

    @Autowired
    private WmSchoolDeliveryFlowAbility wmSchoolDeliveryFlowAbility;

    @Autowired
    private WmSchoolDeliveryAuditTaskService wmSchoolDeliveryAuditTaskService;

    @Autowired
    private WmScSchoolService wmScSchoolService;

    @Autowired
    private WmSchoolDeliveryFollowUpBasicServiceImpl wmSchoolDeliveryFollowUpBasicService;

    @Autowired
    private WmScSchoolAuthService wmScSchoolAuthService;


    @Override
    public Integer getTaskTypeByDeliveryNodeType() {
        return SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode();
    }

    /**
     * 根据交付编号ID获取审批任务类型
     * @param deliveryId 交付编号ID
     * @return 审批任务类型
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditTaskTypeEnum}
     */
    @Override
    public Integer getAuditTaskTypeByDeliveryId(Integer deliveryId) {
        WmSchoolDeliveryStreamDetailDO detailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(
                deliveryId, SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        // 提交审批时状态为“待生效”则为新建; 状态为“生效”则为编辑
        if (detailDO != null && detailDO.getEffective().equals((int) SchoolDeliveryEffectiveStatusEnum.EFFECTIVE.getType())) {
            return (int) SchoolDeliveryAuditTaskTypeEnum.DELIVERY_FOLLOWUP_EDIT.getType();
        }
        return (int) SchoolDeliveryAuditTaskTypeEnum.DELIVERY_FOLLOWUP_CREATE.getType();
    }

    /**
     * 根据交付编号ID查询提审前审批状态
     * @param deliveryId 交付编号ID
     * @return 提审前审批状态
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditStatusEnum}
     */
    @Override
    public Integer getLastAuditStatusByDeliveryId(Integer deliveryId) {
        WmSchoolDeliveryStreamDetailDO streamDetailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(
                deliveryId, SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        return streamDetailDO.getAuditStatus();
    }

    /**
     * 根据交付编号ID查询提审前生效数据版本号
     * @param deliveryId 交付编号ID
     * @return 提审前生效数据版本号
     */
    @Override
    public Integer getEffectiveDataVersionByDeliveryId(Integer deliveryId) {
        WmSchoolDeliveryFollowUpDO followUpDO = wmSchoolDeliveryFollowUpMapper.selectByDeliveryId(deliveryId);
        return followUpDO == null ? 0 : followUpDO.getDataVersion();
    }

    /**
     * 交付跟进-提交审批
     * @param saveParamDTO saveParamDTO
     * @param deliveryId 交付编号ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public void submitAuditTask(WmSchoolDeliverySaveParamDTO saveParamDTO, Integer deliveryId) throws WmSchCantException, TException {
        log.info("[WmSchoolDeliveryFollowUpTaskService.submitAuditTask] saveParamDTO = {}, deliveryId = {}", JSONObject.toJSONString(saveParamDTO), deliveryId);
        if (deliveryId == null || deliveryId <= 0 || saveParamDTO == null || saveParamDTO.getSchoolPrimaryId() == null) {
            log.error("[WmSchoolDeliveryFollowUpTaskService.submitAuditTask] input param INVALID. saveParamDTO = {}, deliveryId = {}",
                    JSONObject.toJSONString(saveParamDTO), deliveryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参未空");
        }

        // 1-将提审快照信息记录到元数据表
        WmScMetadataDO metadataDOBefore = wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(), deliveryId);
        WmSchoolDeliveryFollowUpDTO followUpDTOAfter = wmSchoolDeliveryFollowUpBasicService.saveDeliveryFollowUpSnapshot(saveParamDTO, deliveryId, metadataDOBefore, (int) SchoolDeliveryOpTypeEnum.SUBMIT_AUDIT.getType());

        // 2-驱动交付跟进状态机(提交审批)
        WmSchoolDeliveryAuditTaskBO auditTaskBO = new WmSchoolDeliveryAuditTaskBO();
        auditTaskBO.setDeliveryId(deliveryId);
        auditTaskBO.setSchoolPrimaryId(saveParamDTO.getSchoolPrimaryId());
        auditTaskBO.setDataVersion(followUpDTOAfter.getDataVersion());
        auditTaskBO.setAuditCreateUid(saveParamDTO.getUserId());
        wmSchoolDeliveryFlowAbility.submitDeliveryFollowUp(auditTaskBO);

        // 3-发送大象消息(异步)
        wmSchoolDeliveryFollowUpBasicService.sendSubmitTaskReminderMsgAsync(followUpDTOAfter);

        // 4-记录操作日志(提交审批 & 暂存)
        WmSchoolDeliveryFollowUpDTO followUpDTOBeforeSnapshot = metadataDOBefore == null ? null : JSONObject.parseObject(metadataDOBefore.getDataJson(), WmSchoolDeliveryFollowUpDTO.class);
        WmSchoolDeliveryFollowUpDO followUpDOBeforeEffective = wmSchoolDeliveryFollowUpMapper.selectByDeliveryId(deliveryId);

        wmSchoolDeliveryFollowUpBasicService.recordTempSaveLog(deliveryId, saveParamDTO, followUpDTOBeforeSnapshot, followUpDTOAfter);
        wmSchoolDeliveryFollowUpBasicService.recordCommitLog(saveParamDTO, WmScTransUtil.transDeliveryFollowUpDOToDTO(followUpDOBeforeEffective), followUpDTOAfter);
    }

    /**
     * 撤回审批-交付跟进
     * @param auditTaskBO auditTaskBO
     */
    @Override
    public void cancelAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException {
        log.info("[WmSchoolDeliveryFollowUpTaskService.cancelAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 0-用户操作鉴权(若交付终结触发无需鉴权)
        if (auditTaskBO.getOpUserUid() != null && auditTaskBO.getOpUserUid() > 0) {
            wmScSchoolAuthService.checkDeliveryFollowUpCancelAuditTaskAuth(auditTaskBO.getTaskDO().getSchoolPrimaryId(), auditTaskBO.getOpUserUid());
        }

        // 1-驱动状态机对审批状态进行回退
        wmSchoolDeliveryFlowAbility.auditCancelDeliveryFollowUp(auditTaskBO);

        // 2-记录操作日志
        wmSchoolDeliveryFollowUpBasicService.recordAuditTaskCancelLog(auditTaskBO);
    }

    /**
     * 驳回审批-交付跟进
     * @param auditTaskBO auditTaskBO
     */
    @Override
    public void rejectAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException {
        log.info("[WmSchoolDeliveryFollowUpTaskService.rejectAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-驱动状态机对审批状态进行更新(审批中->审批驳回)
        wmSchoolDeliveryFlowAbility.auditRejectDeliveryFollowUp(auditTaskBO);

        // 2-记录操作日志
        wmSchoolDeliveryFollowUpBasicService.recordAuditTaskRejectLog(auditTaskBO);
    }

    /**
     * 审批终止-交付跟进
     * @param auditTaskBO auditTaskBO
     */
    @Override
    public void stopAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException {
        log.info("[WmSchoolDeliveryFollowUpTaskService.stopAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-驱动状态机对审批状态进行更新(审批中->审批驳回)
        wmSchoolDeliveryFlowAbility.auditStopDeliveryFollowUp(auditTaskBO);

        // 2-记录操作日志
        wmSchoolDeliveryFollowUpBasicService.recordAuditTaskStopLog(auditTaskBO);
    }

    /**
     * 审批通过(子任务)-交付跟进
     * @param auditTaskBO auditTaskBO
     */
    @Override
    public Integer passAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException {
        log.info("[WmSchoolDeliveryFollowUpTaskService.passAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-更新审批主子表审批状态并获取下一个审批节点
        Integer nextAuditNode = wmSchoolDeliveryAuditTaskService.passAuditTask(auditTaskBO);

        // 2-记录操作日志
        wmSchoolDeliveryFollowUpBasicService.recordAuditTaskPassLog(auditTaskBO, nextAuditNode);
        return nextAuditNode;
    }

    /**
     * 审批生效-交付跟进
     * @param auditTaskBO auditTaskBO
     */
    @Override
    public void effectAuditTask(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException, TException {
        log.info("[WmSchoolDeliveryFollowUpTaskService.effectAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-驱动状态机对审批状态进行更新(审批中->审批通过)
        wmSchoolDeliveryFlowAbility.auditPassDeliveryFollowUp(auditTaskBO);

        // 2-记录操作日志
        WmSchoolDeliveryFollowUpDTO followUpDTOAfter = wmSchoolDeliveryFollowUpBasicService.recordAuditTaskEffectLog(auditTaskBO);

        // 3-将提审数据新增/更新到生效表中
        wmSchoolDeliveryFollowUpBasicService.upsertDeliveryFollowUpAuditEffectInfo(auditTaskBO, followUpDTOAfter);

        // 4-将学校当前交付状态更新为"已交付"
        wmScSchoolService.updateSchoolCurrentDeliveryStatus(auditTaskBO.getTaskDO().getSchoolPrimaryId(),
                (int) SchoolDeliveryCurrentStatusEnum.DELIVERED.getType());
    }
}
