package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.poiflow;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScGradeLabelConstant;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallWmPoiBindBO;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenGradeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 食堂档口门店绑定标签相关服务
 * <AUTHOR>
 * @date 2024/05/31
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallPoiLabelService {

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    /**
     * 删除门店标签
     * @param poiLabelDBList 门店标签关系列表
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void deleteCanteenPoiLabel(List<WmScCanteenPoiLabelDB> poiLabelDBList, Integer userId, String userName) {
        log.info("[WmCanteenStallPoiLabelService.deleteCanteenPoiLabel] poiLabelDBList = {}, userId = {}, userName = {}",
                JSONObject.toJSONString(poiLabelDBList), userId, userName);
        if (CollectionUtils.isEmpty(poiLabelDBList)) {
            log.info("[WmCanteenStallPoiLabelService.deleteCanteenPoiLabel] poiLabelDBList is empty, return. userId = {}, userName = {}", userId, userName);
            return;
        }

        // 1-组装Map(key->lavelId, val->wmPoiIdList)
        Map<Long, List<Long>> labelMap = new HashMap<>();
        for (WmScCanteenPoiLabelDB labelDB : poiLabelDBList) {
            labelMap.computeIfAbsent(labelDB.getLabelId(), k -> new ArrayList<>()).add(labelDB.getWmPoiId());
        }

        // 2-调用门店标签接口进行掉标处理
        for (Map.Entry<Long, List<Long>> entry : labelMap.entrySet()) {
            log.info("[WmCanteenStallPoiLabelService.deleteCanteenPoiLabel] labelId = {}, wmPoiIdList = {}, userId = {}, userName = {}",
                    entry.getKey(), JSONObject.toJSONString(entry.getValue()), userId, userName);
            wmPoiFlowlineLabelThriftServiceAdapter.batchDeleteLabelToPois(entry.getKey(), entry.getValue(), userId, userName);
        }
    }

    /**
     * 打上门店标签
     * @param canteenPrimaryId 食堂主键ID
     * @param wmPoiId 门店ID
     * @param userId 操作人ID
     * @param userName 操作人名称
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> addCanteenPoiLabel(Integer canteenPrimaryId, Long wmPoiId, Integer userId, String userName) throws WmSchCantException {
        log.info("[WmCanteenStallPoiLabelService.addCanteenPoiLabel] input param: canteenPrimaryId = {}, wmPoiId = {}, userId = {}, userName = {}",
                canteenPrimaryId, wmPoiId, userId, userName);
        // 1-计算门店打标列表
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        List<Long> labelIdList = new ArrayList<>();
        // A标: 校园食堂商家标
        labelIdList.add(WmScGradeLabelConstant.getLabelA());
        // B标: 供给分级标
        if (!wmCanteenDB.getGrade().equals((int) CanteenGradeEnum.EMPTY_CONTRACT.getType())) {
            labelIdList.add(WmScGradeLabelConstant.getLabelIdByGrade(wmCanteenDB.getGrade()));
        }

        // 2-调用门店标签接口进行打标处理
        for (Long labelId : labelIdList) {
            wmPoiFlowlineLabelThriftServiceAdapter.addLabelToWmPoi(labelId.intValue(), wmPoiId, userId, userName);
        }
        return labelIdList;
    }

}
