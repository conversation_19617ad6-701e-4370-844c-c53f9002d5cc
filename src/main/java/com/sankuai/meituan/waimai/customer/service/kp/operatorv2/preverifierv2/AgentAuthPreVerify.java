package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.UpmAuthCheckService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.HaveAgentAuthEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 代理人授权校验
 */
@Slf4j
@Service
public class AgentAuthPreVerify extends KpPreverify {

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    /**
     * 签约人为代理人，是否有代理人授权书字段必填，选无的时候校验是否有upm权限，新增和修改是两个权限
     * 是否有代理人授权书为有，则代理商授权书字段必填
     *
     * @param wmCustomer
     * @param oldCustomerKpList
     * @param insertKp
     * @param updateKp
     * @param deleteKp
     * @param uid
     * @param uname
     * @return
     * @throws WmCustomerException
     */
    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp,
                         WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {

        if (insertKp == null && updateKp == null) {
            return null;
        }

        WmCustomerKp oldSigner = getOldSigner(oldCustomerKpList);

        CustomerRoleTypeEnum roleTypeEnum = CustomerRoleTypeEnum.NOT_UPLOAD_AGENT_AUTH_FOR_INSERT_ROLE;
        WmCustomerKp signerKp = insertKp;
        String errMsg = "新建代理人必须上传授权书照片，您无权限不上传";
        if (signerKp == null) {
            signerKp = updateKp;
            if (oldSigner != null && KpSignerStateMachine.EFFECT.getState() == oldSigner.getState()) {
                // 在有生效签约人数据的情况下，保存时如果姓名更换则判断当前用户是否有权限不上传代理人授权书，如校验不通过则提示“无权限不上传授权书照片”。
                if (signerKp.getCompellation().equals(oldSigner.getCompellation())) {
                    return null;
                }
                roleTypeEnum = CustomerRoleTypeEnum.NOT_UPLOAD_AGENT_AUTH_FOR_UPDATE_ROLE;
                errMsg = "无权限不上传授权书照片";
            }
        }

        if (signerKp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
            return null;
        }

        //短信授权不需要校验代理人授权书
        if (signerKp.getSignerType() == KpSignerTypeEnum.AGENT.getType() &&
                signerKp.getLegalAuthType() != null && signerKp.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            return null;
        }

        if (signerKp.getHaveAgentAuth() == null) {
            ThrowUtil.throwClientError("代理人授权书不能为空！");
        }

        HaveAgentAuthEnum haveAgentAuthEnum = HaveAgentAuthEnum.of(signerKp.getHaveAgentAuth());
        if (haveAgentAuthEnum == HaveAgentAuthEnum.HAVE
                && (StringUtils.isBlank(signerKp.getAgentAuth())
                && !wmCustomerKpService.checkAllowNoAgentAuthPic(wmCustomer, signerKp))) {
            ThrowUtil.throwClientError("授权书照片不能为空！");
        }

        boolean authCheck = upmAuthCheckService.hasRolePermission(uid, roleTypeEnum.getCode());
        if (haveAgentAuthEnum == HaveAgentAuthEnum.NONE && !authCheck
                && !wmCustomerKpService.checkAllowNoAgentAuthPic(wmCustomer, signerKp)){
            ThrowUtil.throwClientError(errMsg);
        }
        return null;
    }




    private WmCustomerKp getOldSigner(List<WmCustomerKp> oldCustomerKpList) {
        for (WmCustomerKp wmCustomerKp : oldCustomerKpList) {
            if (wmCustomerKp.getKpType() == KpTypeEnum.SIGNER.getType()) {
                return wmCustomerKp;
            }
        }
        return null;
    }

}
