package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.SUBJECT_CHANGE_SUPPLEMENT)
public class WmEcontractSujectChangeSupplementWrapperService implements IWmEcontractDataWrapperService {
    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.SUBJECT_CHANGE_SUPPLEMENT);
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        // 新模式
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("SUBJECTCHANGESUPPLEMENT_TEMPLATE_ID", 77)); // 指定模版
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("SUBJECTCHANGESUPPLEMENT_TEMPLATE_VERSION")); // (可选)指定版本，不指定或传null、传0的话则默认为当前已发布的版本
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo, taskBo));
        pdfInfoBo.setPdfBizContent(Lists.<Map<String, String>>newArrayList());
        return Lists.newArrayList(pdfInfoBo);
    }

    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        log.info("taskBo = {}", JSON.toJSONString(taskBo));
        JSONObject jsonObject = JSON.parseObject(taskBo.getApplyContext());
        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("partAName", jsonObject.getString("partAName"));
        pdfMap.put("partBName", jsonObject.getString("partBName"));
        pdfMap.put("partCName", jsonObject.getString("partCName"));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.HNLX_SIGNKEY);
        pdfMap.put("partCEstamp", PdfConstant.MT_SIGNKEY);
        log.info("pdfMap = {}", JSON.toJSONString(pdfMap));
        return pdfMap;
    }
}
