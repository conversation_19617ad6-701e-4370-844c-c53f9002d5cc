package com.sankuai.meituan.waimai.customer.settle.service;

import static com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil.PDF_MAPPING;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.settle.bo.WmQdbVo;
import com.sankuai.meituan.waimai.customer.settle.bo.WmSettleVo;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.customer.util.trans.TransUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

@Service
public class WmSettlePdfTransService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmSettlePdfTransService.class);


    public static final String DAY_FORMAT = "yyyy年MM月dd日";

    public static final Integer DEFAULT_SCALE = 2;

    public static final Double DEFAULT_MIN_NUMBER = -0.00001;//判断配送分成、保底收入是否为0

    @Autowired
    private WmSettleBankService wmSettleBankService;
    @Autowired
    private WmPoiClient wmPoiClient;

    public WmQdbVo genQdbVo(int wmCustomerId) {
        WmQdbVo wmQdbVo = new WmQdbVo();

        wmQdbVo.setQdbNumber(genQdbNumber(wmCustomerId));
        wmQdbVo.setPartyB(ConfigUtilAdapter.getString("qdb_name", "北京钱袋宝支付技术有限公司"));
        wmQdbVo.setPartyBAddress(ConfigUtilAdapter.getString("qdb_address", "北京市海淀区太月园1号楼五层"));
        wmQdbVo.setPartyBPhone(ConfigUtilAdapter.getString("qdb_phone", "********"));
        wmQdbVo.setAuditTime(TimeUtil.format(DAY_FORMAT, (int) (System.currentTimeMillis() / 1000)));

        return wmQdbVo;
    }

    /**
     * 结算对象bo对象转为pdf对象
     *
     * @param wmSettle
     * @return
     */
    public WmSettleVo wmSettleBo2Vo(WmSettle wmSettle) {

        WmSettleVo wmSettleVo = new WmSettleVo();
        wmSettleVo.setWmCustomerId(wmSettle.getWmCustomerId() + "");
        wmSettleVo.setPartyAFinancePeople(wmSettle.getParty_a_finance_people());
        wmSettleVo.setPartyAFinancePhone(wmSettle.getParty_a_finance_phone());
        wmSettleVo.setAccName(wmSettle.getAcc_name());
        wmSettleVo.setAccCardNo(wmSettle.getAcc_cardno());
        wmSettleVo.setProvince(wmSettleBankService.getProvinceNameByProvinceId(wmSettle.getProvince()));
        wmSettleVo.setCity(wmSettleBankService.getCityNameByLocationId(wmSettle.getCity()));
        wmSettleVo.setBank(wmSettleBankService.getBankByBankId(wmSettle.getBankid()));
        wmSettleVo.setBranchname(wmSettle.getBranchname());
        wmSettleVo.setSettleType((int) wmSettle.getSettle_type());
//        wmSettleVo.setPayPeriod(wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_SELF ? 0 : getPayPeriod(wmSettle.getPay_period_num(), wmSettle.getPay_period_unit()));

        String baseString = "自外卖信息首次发布之日起:";
        if(wmSettle.getSettle_type() == 4){
            wmSettleVo.setPayPeriodStr(baseString+"每月最后一天");
        }
        else if(wmSettle.getSettle_type() == 3){
            wmSettleVo.setPayPeriodStr(baseString+"每月"+wmSettle.getPay_day_of_month()+"日");
        }
        else{
            wmSettleVo.setPayPeriodStr(baseString+PDF_MAPPING.get(wmSettle.getPay_period_unit()+"_"+wmSettle.getPay_period_num()));
        }

        wmSettleVo.setMinPayAmountType(wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_SELF ? 0 : getMinPayAmountType(wmSettle.getMin_pay_amount()));
        BigDecimal bg = new BigDecimal(wmSettle.getMin_pay_amount()).setScale(2, RoundingMode.HALF_UP);
        wmSettleVo.setMinPayAmount(wmSettleVo.getMinPayAmountType().equals(4) ? bg.toString() : "");
        wmSettleVo.setAccType(wmSettle.getAcctype());
        wmSettleVo.setLegalPerson(wmSettle.getLegal_person());
        wmSettleVo.setLegalIdCard(wmSettle.getLegal_id_card());
        wmSettleVo.setLegalCertNum(wmSettle.getLegal_cert_num());
        wmSettleVo.setReservePhone(wmSettle.getReserve_phone());
        wmSettleVo.setCertNum(wmSettle.getCert_num());

        return wmSettleVo;
    }

    public Map<Integer, WmSettleVo> batchWmSettle2VoMap(List<WmSettle> wmSettleList) {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return Maps.newHashMap();
        }

        Map<Integer, WmSettleVo> wmSettleVoMap = Maps.newHashMap();
        for (WmSettle wmSettle : wmSettleList) {
            if (wmSettle == null || CollectionUtils.isEmpty(wmSettle.getWmPoiIdList())) {
                continue;
            }
            WmSettleVo wmSettleVo = wmSettleBo2Vo(wmSettle);
            for (Integer wmPoiId : wmSettle.getWmPoiIdList()) {
                wmSettleVoMap.put(wmPoiId, wmSettleVo);
            }
        }
        return wmSettleVoMap;
    }

    public String genWmPoiNamesStr(List<Integer> wmPoiIdList) {
        List<WmPoiDomain> wmPoiDomainList = Lists.newArrayList();
        try {
            wmPoiDomainList = wmPoiClient
                .pageGetWmPoiByWmPoiIdList(TransUtil.IntegerList2Long(wmPoiIdList));
        } catch (WmCustomerException e) {
            LOGGER.error("pageGetWmPoiByWmPoiIdList异常", e);
        }
        StringBuilder sb = new StringBuilder("");
        for (int k = 0; k < wmPoiDomainList.size(); k++) {
            if (k == wmPoiDomainList.size() - 1) {
                sb.append(wmPoiDomainList.get(k).getName());
            } else {
                sb.append(wmPoiDomainList.get(k).getName()).append(",");
            }
        }
        return sb.toString();
    }

    /**
     * 获取邮箱标示符
     *
     * @param email
     * @return
     */
    public String getEmailFlag(String email) {
        if (StringUtils.isEmpty(email)) {
            return "";
        }
        String[] emails = StringUtils.split(email, "@");
        if (emails.length != 2) {
            return "";
        }
        return emails[0];
    }

    /**
     * 获取邮箱域名
     *
     * @param email
     * @return
     */
    public String getEmailDomain(String email) {
        if (StringUtils.isEmpty(email)) {
            return "";
        }
        String[] emails = StringUtils.split(email, "@");
        if (emails.length != 2) {
            return "";
        }
        return emails[1];
    }

    private Integer getPayPeriod(int payPeriodNum, int payPeriodUnit) {
        if (payPeriodNum == 1 && payPeriodUnit == 1) {
            return 7;
        } else if (payPeriodNum == 2 && payPeriodUnit == 1) {
            return 14;
        } else if (payPeriodNum == 3 && payPeriodUnit == 3) {
            return 3;
        } else if (payPeriodNum == 1 && payPeriodUnit == 2) {
            return 30;
        } else if (payPeriodNum == 1 && payPeriodUnit == 3) {
            return 1;
        } else {
            return 0;
        }
    }

    private Integer getMinPayAmountType(double minPayAmount) {
        if (minPayAmount - 100 < 0.000001 && minPayAmount - 100 > -0.000001) {
            return 1;
        } else if (minPayAmount - 300 < 0.000001 && minPayAmount - 300 > -0.000001) {
            return 2;
        } else if (minPayAmount - 500 < 0.000001 && minPayAmount - 500 > -0.000001) {
            return 3;
        } else {
            return 4;
        }
    }

//    private String getQrcode() {
//        byte[] bytes = StreamUtil.file2Bytes(WmContractPdfTransService.class.getResource("/pdfTemp/jpg/qrcode.png").getFile());
//        return EncodeUtil.base64Encode(bytes);
//    }
//
//    private String getWmContractNumQrCode(String wmContractNum) {
//        byte[] bytes = QrCodeCreateUtil.createBarToByte(wmContractNum, 120, 40);
//        return EncodeUtil.base64Encode(bytes);
//    }


    public static String genQdbNumber(int wmCustomerId) {
        return "QDB-WMDZ-" + ContractNumberUtil.getSuffix(wmCustomerId);
    }

    public List<EcontractPoiInfoBo> genEcontractPoiInfoBoList(List<Integer> wmPoiIdList) {
        List<EcontractPoiInfoBo> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return result;
        }
        List<WmPoiDomain> wmPoiDomainList = Lists.newArrayList();
        try {
            wmPoiDomainList = wmPoiClient.pageGetWmPoiByWmPoiIdList(TransUtil.IntegerList2Long(wmPoiIdList));
        } catch (WmCustomerException e) {
            LOGGER.error("pageGetWmPoiByWmPoiIdList异常", e);
        }
        EcontractPoiInfoBo infoTemp = null;
        for (WmPoiDomain temp : wmPoiDomainList) {
            infoTemp = new EcontractPoiInfoBo.Builder()
                    .wmPoiId(temp.getWmPoiId()).name(temp.getName())
                    .address(temp.getAddress()).build();
            result.add(infoTemp);
        }
        return result;
    }
}
