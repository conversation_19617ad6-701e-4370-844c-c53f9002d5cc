package com.sankuai.meituan.waimai.customer.mq.partition;

import com.alibaba.fastjson.JSONObject;
import com.meituan.kafka.javaclient.common.utils.BrokerVerifiableProperties;
import com.meituan.mafka.client.producer.Partitioner.MafkaPartitionerImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WmCustomerChangeNotifyPartitioner extends MafkaPartitionerImpl {

    public WmCustomerChangeNotifyPartitioner(BrokerVerifiableProperties props) {
        super(props);
    }


    @Override
    public int partition(Object key, int numPartitions) {
        try {
            Integer id = Integer.parseInt(key.toString());
            int partition = id % numPartitions;
            log.info("[客户信息变更]通知客户信息变更{}发送到指定分区{}", id, partition);
            return partition;
        } catch (Exception e) {
            log.error("[客户信息变更]通知客户信息变更-生产者分区策略处理异常 key:{} numPartitions:{}",
                    JSONObject.toJSONString(key), numPartitions, e);
            return 0;
        }
    }
}
