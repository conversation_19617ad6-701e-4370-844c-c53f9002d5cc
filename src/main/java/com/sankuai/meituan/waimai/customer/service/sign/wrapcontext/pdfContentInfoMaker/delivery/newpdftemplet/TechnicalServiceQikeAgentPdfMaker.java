package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.TECHNICAL_SERVICE_QIKEAGENT_FEEMODE)
public class TechnicalServiceQikeAgentPdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker  {
    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException, TException {
        log.info("#TechnicalServiceQikeAgentPdfMaker, customerId:{}, batchId:{}", originContext.getCustomerId(), originContext.getBatchId());
        SignTemplateEnum signTemplateEnum = extractSignTemplateEnum();
        List<EcontractDeliveryInfoBo> deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_QIKEAGENT_FEEMODE.getName());
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();
        Map<String, String> subMap;
        for (EcontractDeliveryInfoBo infoBo : deliveryInfoList) {
            infoBo.setDeliveryArea(null);
            infoBo.setEcontractDeliveryWholeCityInfoBo(null);
            infoBo.setEcontractDeliveryAggregationInfoBo(null);
            subMap = MapUtil.Object2Map(infoBo);
            pdfBizContent.add(subMap);
        }

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("signTime", org.apache.commons.lang.StringUtils.defaultIfEmpty(signTime, org.apache.commons.lang.StringUtils.EMPTY));
        pdfMetaContent.put("partAOfficialSeal", org.apache.commons.lang.StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), org.apache.commons.lang.StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        //基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("TECHNICAL_SERVICE_QIKEAGENT_FEEMODE_TEMPLATE_ID", 168));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("TECHNICAL_SERVICE_QIKEAGENT_FEEMODE_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        log.info("#TechnicalServiceQikeAgentPdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }
}
