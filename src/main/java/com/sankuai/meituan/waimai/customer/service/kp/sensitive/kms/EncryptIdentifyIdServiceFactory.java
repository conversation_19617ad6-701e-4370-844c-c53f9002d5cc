package com.sankuai.meituan.waimai.customer.service.kp.sensitive.kms;

import com.sankuai.inf.kms.pangolin.api.model.EncryptionRequest;
import com.sankuai.inf.kms.pangolin.api.service.EncryptServiceFactory;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import org.springframework.stereotype.Service;

/**
 * 银行卡号
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
public class EncryptIdentifyIdServiceFactory implements IEncryptServiceFactory {

    private IEncryptService iEncryptService;

    public EncryptIdentifyIdServiceFactory() {
        iEncryptService = EncryptServiceFactory.create(EncryptionRequest.Builder.anEncryptionRequest()
                .withNamespace(NAMESPACE).withKeyName(KmsKeyNameEnum.IDENTIFY_ID.getCode()).build());
    }

    public IEncryptService getEncryptServer() {
        return iEncryptService;
    }
}
