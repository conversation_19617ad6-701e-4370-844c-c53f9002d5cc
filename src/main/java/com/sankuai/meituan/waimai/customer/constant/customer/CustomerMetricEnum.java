package com.sankuai.meituan.waimai.customer.constant.customer;

import org.apache.commons.lang.StringUtils;

/**
 * 客户服务埋点
 */
public enum CustomerMetricEnum {

    CUSTOMER_SAVE(1, "customer.save", "source", "status"),
    CUSTOMER_KP_SAVE(2, "customer.kp.save", "type", "status"),
    CUSTOMER_POI_BIND(3, "customer.poi.bind", "source", "status"),
    CUSTOMER_POI_UNBIND(4, "customer.poi.unbind", "source", "status"),
    CUSTOMER_VALID(5, "customer.valid", "source", "status"),
    CUSTOMER_POI_VALID(6, "customer.poi.valid", "source", "status"),
    POI_OFFLINE(7, "poi.offline", "source", "status"),
    CUSTOMER_SIGN_MODE_CHANGE(8, "customer.sign.mode.change", "source", "status"),
    CUSTOMER_SWITCH_CHECK(9, "customer.switch.check", "type", "status"),
    CUSTOMER_POI_BIND_START_SIGN(10, "customer.poi.bind.start.sign", "source", "status"),
    CUSTOMER_TASK_CHECK(11, "customer.task.check", "type", "status")
    ;


    private int code;

    private String name;

    private String tag;

    private String status;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    CustomerMetricEnum(int code, String name, String tag, String status) {
        this.code = code;
        this.name = name;
        this.tag = tag;
        this.status = status;

    }
}
