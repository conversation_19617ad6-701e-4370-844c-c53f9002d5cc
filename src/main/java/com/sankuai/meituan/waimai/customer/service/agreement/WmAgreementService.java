package com.sankuai.meituan.waimai.customer.service.agreement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.healthm.health.operation.contract.thrift.dto.PrescriptionInfoDTO;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.health.merchant.center.thrift.dto.ShowSecurityDepositContractDTO;
import com.sankuai.meituan.waimai.customer.dao.WmAgreementAccountSignDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmAgreementModelDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmAgreementPoiSignDBMapper;
import com.sankuai.meituan.waimai.customer.domain.ContractSignType;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementAccountSign;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementFormDB;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementSignLogDB;
import com.sankuai.meituan.waimai.customer.service.agreement.message.WmAgreementNoticeService;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmPoiLogisticsAgreementInfo;
import com.sankuai.meituan.waimai.scm.cloud.service.MtCloudService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.AgreementConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.AgreementTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractVersionBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.tsp.phf.contract.dto.contract.ContractResDTO;

import joptsimple.internal.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 协议service
 * <AUTHOR>
 */
@Service
public class WmAgreementService<T> {

    private static Logger logger = LoggerFactory.getLogger(WmAgreementService.class);

    @Autowired
    private WmAgreementModelDBMapper wmAgreementModelDBMapper;

    @Autowired
    private WmAgreementPoiSignDBMapper wmAgreementPoiSignDBMapper;

    @Autowired
    private WmAgreementNoticeService wmAgreementNoticeService;

    @Autowired
    private WmAgreementCacheService wmAgreementCacheService;

    @Autowired
    private WmAgreementAccountSignDBMapper wmAgreementAccountSignDBMapper;

    @Resource(name = "mtCloudService")
    private MtCloudService mtCloudService;

    /**
     * 根据协议id获取协议模板
     * @param agreementId
     * @return
     */
    public WmAgreementModel getAgreementById(int agreementId){
        return wmAgreementModelDBMapper.selectByPrimaryKey(agreementId);
    }

    /**
     * 根据协议类型获取协议模板
     * @param agreementType
     * @return
     */
    public WmAgreementModel getAgreementByType(int agreementType){
        //获取当前生效版本
        Map<AgreementTypeEnum,Integer> typeEnumIntegerMap =  wmAgreementCacheService.getConfiguredVersionFromLocalCache();
        int currentId = 0;
        for(Map.Entry<AgreementTypeEnum, Integer> entry : typeEnumIntegerMap.entrySet()){
            AgreementTypeEnum mapKey = entry.getKey();
            if (mapKey == AgreementTypeEnum.getByType(agreementType)){
                currentId = entry.getValue();
                break;
            }
        }
        return wmAgreementModelDBMapper.selectByPrimaryKey(currentId);
    }

    /**
     * 根据协议id获取协议模板
     * @param set
     * @return
     */
    public List<WmAgreementModel> getAgreementsByIdList(Set<Integer> set){
        return wmAgreementModelDBMapper.selectAgreementByIds(set);
    }

    /**
     * 插入协议签约记录
     * @param record
     * @return
     */
    public int insertAgreemenSign(WmAgreementPoiSign record){
        return wmAgreementPoiSignDBMapper.insert(record);
    }

    /**
     * 插入协议签约记录-需传入时间，数据同步使用
     * @param record
     * @return
     */
    public int insertAgreemenSignWithTime(WmAgreementPoiSign record){
        return wmAgreementPoiSignDBMapper.insertWithTime(record);
    }

    /**
     * 更新协议签约
     * @param record
     * @return
     */
    public int updateAgreementById(WmAgreementPoiSign record){
        return wmAgreementPoiSignDBMapper.updateByPrimaryKey(record);
    }

    /**
     * 更新协议签约-需传入时间，数据同步使用
     * @param record
     * @return
     */
    public int updateAgreementByIdWithTime(WmAgreementPoiSign record){
        return wmAgreementPoiSignDBMapper.updateByPrimaryKeyWithTime(record);
    }

    /**
     * 根据门店id和协议类型获取 已签署完成状态的协议
     * @param wmPoiId
     * @param type
     * @return
     */
    public List<WmAgreementPoiSign> getPoiSignByWmpoiId(Long wmPoiId,Integer type){
        return wmAgreementPoiSignDBMapper.selectPoiSignByWmPoiId(wmPoiId,type);
    }

    /**
     * 根据门店和某个协议类型获取签约记录(最多只会由一条记录)
     * @param wmPoiId
     * @param type
     * @return
     */
    public WmAgreementPoiSign selectPoiSignByWmPoiIdAndType(Long wmPoiId,Integer type){
        return wmAgreementPoiSignDBMapper.selectPoiSignByWmPoiIdAndType(wmPoiId,type);
    }

    /**
     * 主库-根据门店和某个协议类型获取签约记录(最多只会由一条记录)
     * @param wmPoiId
     * @param type
     * @return
     */
    public WmAgreementPoiSign selectPoiSignByWmPoiIdAndTypeMaster(Long wmPoiId,Integer type){
        return wmAgreementPoiSignDBMapper.selectPoiSignByWmPoiIdAndTypeMaster(wmPoiId,type);
    }

    /**
     * 批量查询门店签署的协议不限制状态
     * @param wmPoiIdList 门店集合
     * @param typeList 协议类型结合
     * @return
     */
    public List<WmAgreementPoiSign> getRecordsByWmpoiIdAndTypeList(List<Long> wmPoiIdList,List<Integer> typeList){
        return wmAgreementPoiSignDBMapper.getRecordsByWmpoiIdAndTypeList(wmPoiIdList,typeList);
    }

    /**
     * 根据商家账号获取协议信息
     * @param accountId
     * @return
     */
    public List<WmAgreementPoiSign> getPoiSignByAccountId(Long accountId){
        return wmAgreementPoiSignDBMapper.selectPoiSignByAccountId(accountId);
    }

    /**
     * 批量签约协议
     * @param agreementId
     * @param accId
     * @param wmPoiIds
     * @param type
     */
    public void batchSignAgreement(int agreementId,Long accId,List<Long> wmPoiIds,int type,WmAgreementMsgParamBo msgParamBo) {
        logger.info("签约门店：wmPoiIds：{}",JSONObject.toJSONString(wmPoiIds));
        //签约门店
        List<Long> signPoiIds = Lists.newArrayList(wmPoiIds);
        WmAgreementModel model = this.getAgreementById(agreementId);
        if(model == null){
            logger.info("没有查询到协议 agreementId={}",agreementId);
            return;
        }
        //check协议是否已经签署
        List<Long> updateList = Lists.newArrayList();
        List<WmAgreementPoiSign> poiSignList = wmAgreementPoiSignDBMapper.getRecordsByWmpoiIdAndTypeList(wmPoiIds,Lists.newArrayList(model.getAgreeModelType()));
        if (!CollectionUtils.isEmpty(poiSignList)){
            //已有记录
            for (WmAgreementPoiSign poiSign:poiSignList){
                updateList.add(poiSign.getWmPoiId());
            }
        }
        logger.info("已有签约门店：updateList：{}",JSONObject.toJSONString(updateList));
        //去除需要更新的
        wmPoiIds.removeAll(updateList);
        logger.info("签约门店：wmPoiIds：{}",JSONObject.toJSONString(wmPoiIds));
        //需要新增的
        if (!CollectionUtils.isEmpty(wmPoiIds)){
            List<WmAgreementPoiSign> records = Lists.newArrayList();
            for (Long wmPoiId:wmPoiIds){
                WmAgreementPoiSign poiSign = new WmAgreementPoiSign();
                poiSign.setWmPoiId(wmPoiId);
                poiSign.setAccountId(accId);
                poiSign.setAgreementType(model.getAgreeModelType());
                poiSign.setAgreementId(agreementId);
                poiSign.setStatus(type == 1 ? AgreementConstants.SUCCESS:AgreementConstants.CANCEL);
                records.add(poiSign);
            }
            wmAgreementPoiSignDBMapper.batchInsert(records);
        }

        //需要更新的
        if (!CollectionUtils.isEmpty(updateList)){
            Set<Long> wmPoiIdSet = Sets.newHashSet(updateList);
            Integer status = type == 1 ? AgreementConstants.SUCCESS:AgreementConstants.CANCEL;
            wmAgreementPoiSignDBMapper.batchUpdatePoiSignStatus(wmPoiIdSet,status,model.getAgreeModelType(),accId,agreementId);
        }

        //发送签约消息
        sendSignMsg(model.getAgreeModelType(),model.getAgreeModelVersion(),signPoiIds,type,msgParamBo);
    }

    private void sendSignMsg(int agreementType,int agreeModelVersion,List<Long> signPoiIds,int opType,WmAgreementMsgParamBo msgParamBo){
        //发送签约消息
        WmAgreementOperateNotifyMsg msg = new WmAgreementOperateNotifyMsg();
        msg.setAgreementType(agreementType);
        msg.setAgreeModelVersion(agreeModelVersion);
        // 1通过 2驳回
        msg.setOpType(opType);
        msg.setRejectReason(opType == 2? "商家拒绝": Strings.EMPTY);
        //消息来源，协议平台
        msg.setSource(1);
        msg.setPageSource(msgParamBo.getPageSource());
        msg.setUtime(TimeUtil.unixtime());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ip",msgParamBo.getSignIp());
        jsonObject.put("acctId",msgParamBo.getAcctId());
        msg.setExtData(jsonObject.toJSONString());
        msg.setWmPoiIdList(signPoiIds);
        wmAgreementNoticeService.sendMsg(msg);
    }

    public BooleanResult addWmAgreementModel(WmAgreementModel wmAgreementModel) throws WmCustomerException {
        logger.info("addWmAgreementModel,wmAgreementModel={}",JSONObject.toJSONString(wmAgreementModel));
        AssertUtil.assertObjectNotNull(wmAgreementModel);
        AssertUtil.assertStringNotEmpty(wmAgreementModel.getAgreeModelName(),"模块名称");
        AssertUtil.assertStringNotEmpty(wmAgreementModel.getAgreeModelContentUrl(),"H5链接");
        WmAgreementModel exitingVersion = wmAgreementModelDBMapper
                .selectByAgreementTypeAndVersionMaster(wmAgreementModel.getAgreeModelType(),
                        wmAgreementModel.getAgreeModelVersion());
        if(exitingVersion != null){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"当前已存在该版本信息,请勿重复操作");
        }
        int result = wmAgreementModelDBMapper.insertSelective(wmAgreementModel);
        return new BooleanResult(result > 0);
    }


    /**
     * 获取协议列表
     *
     * @param wmAgreementFormBo
     * @return
     */
    public WmAgreementListPageData getAgreementList(WmAgreementFormBo wmAgreementFormBo) throws WmCustomerException{

        AssertUtil.assertObjectNotNull(wmAgreementFormBo);

        WmAgreementFormDB wmAgreementFormDB = wmAgreementFormBoToDB(wmAgreementFormBo);

        PageData<WmAgreementListBo> page = getWmAgreementPageData(wmAgreementFormDB);

        if (page == null) {
            return getDefaultPageIfNoResult();
        }
        return new WmAgreementListPageData(page.getPageInfo(), page.getList());
    }

    /**
     * 获取协议列表分页数据
     *
     * @param wmAgreementFormDB
     * @return
     */
    private PageData<WmAgreementListBo> getWmAgreementPageData(WmAgreementFormDB wmAgreementFormDB) {
        List<WmAgreementPoiSign> wmAgreementListDBList = Lists.newArrayList();

        PageHelper.startPage(wmAgreementFormDB.getPageNo(), wmAgreementFormDB.getPageSize());

        logger.info("协议列表查询参数：wmAgreementFormDB{}",JSONObject.toJSONString(wmAgreementFormDB));
        wmAgreementListDBList = wmAgreementPoiSignDBMapper.selectAgreementList(wmAgreementFormDB);

        List<WmAgreementListBo> wmAgreementListBoList = agreementListDBToListBo(wmAgreementListDBList);

        if (CollectionUtils.isEmpty(wmAgreementListBoList)) {
            return null;
        }
        PageData<WmAgreementListBo> page = PageUtil
                .page(wmAgreementListDBList, wmAgreementListBoList);
        logger.info("获取协议列表,返回page={}", JSONObject.toJSONString(page));
        return page;
    }

    /**
     * 没有查询到设置默认值
     * @return
     */
    private WmAgreementListPageData getDefaultPageIfNoResult() {
        Map<String, Long> pageInfo = new HashMap<>();
        pageInfo.put("total", 0L);
        pageInfo.put("pages", 1L);
        pageInfo.put("pageNo", 1L);
        return new WmAgreementListPageData(pageInfo, Lists.<WmAgreementListBo>newArrayList());
    }

    /**
     * 转换DB对象TO ListBo
     * @param poiSignList
     * @return
     */
    public static List<WmAgreementListBo> agreementListDBToListBo(List<WmAgreementPoiSign> poiSignList) {
        if (CollectionUtils.isEmpty(poiSignList)) {
            return null;
        }
        List<WmAgreementListBo> list = Lists.newArrayList();
        for (WmAgreementPoiSign wmAgreementPoiSign : poiSignList) {
            WmAgreementListBo wmAgreementListBo = new WmAgreementListBo();
            wmAgreementListBo.setId(wmAgreementPoiSign.getId());
            wmAgreementListBo.setWmPoiId(wmAgreementPoiSign.getWmPoiId());
            wmAgreementListBo.setAccountId(wmAgreementPoiSign.getAccountId());
            wmAgreementListBo.setAgreementId(wmAgreementPoiSign.getAgreementId());
            wmAgreementListBo.setAgreementType(wmAgreementPoiSign.getAgreementType());
            wmAgreementListBo.setStatus(wmAgreementPoiSign.getStatus());
            wmAgreementListBo.setCtime(wmAgreementPoiSign.getCtime());
            wmAgreementListBo.setUtime(wmAgreementPoiSign.getUtime());
            list.add(wmAgreementListBo);
        }
        return list;
    }

    /**
     * 转换FormBo TO FormDB
     * @param wmAgreementFormBo
     * @return
     */
    public static WmAgreementFormDB wmAgreementFormBoToDB(WmAgreementFormBo wmAgreementFormBo) {
        if (wmAgreementFormBo == null) {
            return null;
        }

        WmAgreementFormDB wmAgreementFormDB = new WmAgreementFormDB();
        wmAgreementFormDB.setWmPoiId(wmAgreementFormBo.getWmPoiId());
        wmAgreementFormDB.setStatus(wmAgreementFormBo.getStatus());
        wmAgreementFormDB.setType(wmAgreementFormBo.getType());
        wmAgreementFormDB.setPageNo(wmAgreementFormBo.getPageNo());
        wmAgreementFormDB.setPageSize(wmAgreementFormBo.getPageSize());

        return wmAgreementFormDB;
    }


    public List<WmAgreementPoiSign> selectAgreementList(WmAgreementFormDB wmAgreementFormDB){
      return   wmAgreementPoiSignDBMapper.selectAgreementList(wmAgreementFormDB);
    }

    public void assemblyContractVersionBoList(List<WmCustomerContractVersionBo> boList, List<T> toAssemblyList, ContractSignType contractSignType) {
        if (!CollectionUtils.isEmpty(toAssemblyList)) {
            for (T t : toAssemblyList) {
                WmCustomerContractVersionBo wmCustomerContractVersionBo = new WmCustomerContractVersionBo();
                if (t instanceof WmPoiLogisticsAgreementInfo) {
                    WmPoiLogisticsAgreementInfo logisticsAgreementInfo = (WmPoiLogisticsAgreementInfo) t;
                    wmCustomerContractVersionBo.setStatus((byte) logisticsAgreementInfo.getStatus());
                    wmCustomerContractVersionBo.setVersionNumber(logisticsAgreementInfo.getPoiVersion());
                    wmCustomerContractVersionBo.setPdfUrl(logisticsAgreementInfo.getVersionURL());
                    wmCustomerContractVersionBo.setSignTime(logisticsAgreementInfo.getSignTime());
                    wmCustomerContractVersionBo.setType(contractSignType.getType());
                    wmCustomerContractVersionBo.setTypeName(contractSignType.getName());
                    boList.add(wmCustomerContractVersionBo);
                } else if (t instanceof ContractResDTO) {
                    ContractResDTO contractResDTO = (ContractResDTO) t;
                    //https://km.sankuai.com/page/789302449
                    wmCustomerContractVersionBo.setStatus((byte) (contractResDTO.getStatus() == 103 ? 1 : 0));
                    wmCustomerContractVersionBo.setVersionNumber(contractResDTO.getCode());
                    wmCustomerContractVersionBo.setPdfUrl(contractResDTO.getPdfUrl());
                    DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    wmCustomerContractVersionBo.setSignTime(df.format(DateUtil.fromUnixTime(contractResDTO.getStartTime())));
                    wmCustomerContractVersionBo.setType(contractSignType.getType());
                    wmCustomerContractVersionBo.setTypeName(contractSignType.getName());
                    boList.add(wmCustomerContractVersionBo);
                } else if (t instanceof ShowSecurityDepositContractDTO) {
                    ShowSecurityDepositContractDTO contractResDTO = (ShowSecurityDepositContractDTO) t;
                    wmCustomerContractVersionBo.setStatus((byte) (contractResDTO.getStatus() == 1 ? 1 : 0));
                    wmCustomerContractVersionBo.setVersionNumber(String.valueOf(contractResDTO.getId()));
                    String pdfUrl = mtCloudService.getAbsoluteDownUrl(parsePdfName(contractResDTO.getPdfUrl()), false, 0,
                            true, 0, "协议平台");
                    wmCustomerContractVersionBo.setPdfUrl(pdfUrl);
                    wmCustomerContractVersionBo.setSignTime(contractResDTO.getDate());
                    wmCustomerContractVersionBo.setType(contractSignType.getType());
                    wmCustomerContractVersionBo.setTypeName(contractSignType.getName());
                    boList.add(wmCustomerContractVersionBo);
                } else if (t instanceof PrescriptionInfoDTO) {
                    PrescriptionInfoDTO prescriptionInfoDTO = (PrescriptionInfoDTO) t;
                    wmCustomerContractVersionBo.setStatus((byte) (prescriptionInfoDTO.getContractStatus() == 1 ? 1 : 0));
                    wmCustomerContractVersionBo.setVersionNumber(String.valueOf(prescriptionInfoDTO.getId()));
                    String pdfUrl = mtCloudService.getAbsoluteDownUrl(parsePdfName(prescriptionInfoDTO.getPdfUrl()), false, 0,
                            true, 0, "协议平台");
                    wmCustomerContractVersionBo.setPdfUrl(pdfUrl);
                    DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    wmCustomerContractVersionBo.setSignTime(df.format(DateUtil.fromUnixTime(prescriptionInfoDTO.getContractEffectTime()/1000)));
                    wmCustomerContractVersionBo.setType(contractSignType.getType());
                    wmCustomerContractVersionBo.setTypeName(contractSignType.getName());
                    boList.add(wmCustomerContractVersionBo);
                }
            }
        }
    }

    public void accountSignAgreement(WmAggrementAccountSignRequestParam param) throws TException, WmCustomerException {
        //参数校验
        AssertUtil.assertLongMoreThan0(param.getAcctId(), "账号");
        AssertUtil.assertIntegerMoreThan0(param.getOpType(), "签署结果");
        AssertUtil.assertCollectionNotEmpty(param.getAgreementIds(), "协议列表");
        //获取aggrementType
        Map<Integer,WmAgreementModel> aggrementModelMap = getAggrementModelMaps(param.getAgreementIds());
        //记录账号签约状态
        insertOrUpdateAccountSign(param, aggrementModelMap);
    }

    public Map<Integer,WmAgreementModel> getAggrementModelMaps(List<Integer> aggrementIds){
        List<WmAgreementModel> aggrementModelList = getAgreementsByIdList(new HashSet<>(aggrementIds));
        logger.debug("getAggrementModelMaps#aggrementModelList:{}", JSON.toJSONString(aggrementModelList));
        Map<Integer,WmAgreementModel> aggrementModelMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(aggrementModelList)){
            aggrementModelMap = aggrementModelList.stream().collect(Collectors.toMap(WmAgreementModel::getId, aggrementModel -> aggrementModel));
        }
        return aggrementModelMap;
    }


    public void insertOrUpdateAccountSign(WmAggrementAccountSignRequestParam param, Map<Integer,WmAgreementModel> aggrementModelMap){
        //获取已有记录
        List<WmAgreementAccountSign> agreementAccountSignList =
                wmAgreementAccountSignDBMapper.queryRecordByAggrementIdAndAccountId(param.getAgreementIds(),param.getAcctId());
        logger.debug("insertOrUpdateAccountSign#agreementAccountSignList:{}", JSON.toJSONString(agreementAccountSignList));
        List<WmAgreementAccountSign> insertList = Lists.newArrayList();
        List<WmAgreementAccountSign> updateList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(agreementAccountSignList)){
            Map<Integer, WmAgreementAccountSign> accountSignMap =
                    agreementAccountSignList.stream().collect(Collectors.toMap(WmAgreementAccountSign::getAgreementId, accountSign -> accountSign));
            param.getAgreementIds().stream().forEach(aggrementId -> {
                WmAgreementAccountSign wmAgreementAccountSign = WmAgreementAccountSign.builder()
                        .accountId(param.getAcctId())
                        .agreementId(aggrementId)
                        .agreementType(aggrementModelMap.get(aggrementId).getAgreeModelType())
                        .status(param.getOpType())
                        .build();
                if(null != accountSignMap.get(aggrementId)){
                    //update记录
                    wmAgreementAccountSign.setId(accountSignMap.get(aggrementId).getId());
                    updateList.add(wmAgreementAccountSign);
                } else {
                    //insert记录
                    insertList.add(wmAgreementAccountSign);
                }
            });
            //批量执行更新
            if(CollectionUtils.isNotEmpty(updateList)){
                wmAgreementAccountSignDBMapper.batchUpdateById(updateList);
            }
            //批量执行新增
            if(CollectionUtils.isNotEmpty(insertList)){
                wmAgreementAccountSignDBMapper.batchInsert(insertList);
            }
        }else{
            param.getAgreementIds().stream().forEach(aggrementId -> {
                WmAgreementAccountSign wmAgreementAccountSign = WmAgreementAccountSign.builder()
                        .accountId(param.getAcctId())
                        .agreementId(aggrementId)
                        .agreementType(aggrementModelMap.get(aggrementId).getAgreeModelType())
                        .status(param.getOpType())
                        .build();
                insertList.add(wmAgreementAccountSign);
            });
            //批量执行新增
            wmAgreementAccountSignDBMapper.batchInsert(insertList);
        }
    }

    /**
     * 账号维度获取签约日志记录
     * @param accountId
     * @param agreeTypes
     * @return
     */
    public WmAggrementAccountSignResp getAccountSignResultByAccountId(Long accountId, String agreeTypes) throws TException, WmCustomerException {
        logger.info("getAccountSignResultByAccountId#accountId:{}，agreeTypes:{}", accountId, agreeTypes);
        // request check
        if(accountId <= 0 || StringUtils.isEmpty(agreeTypes)){
            logger.warn("getAccountSignResultByAccountId#入参异常，accountId:{}，agreeTypes:{}",accountId,agreeTypes);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"当前已存在该版本信息,请勿重复操作");
        }
        String[] aggreTypeStr = agreeTypes.split(",");
        List<Integer> agreeTypeList = Lists.newArrayList();
        for(String str : aggreTypeStr){
            if(!NumberUtils.isDigits(str)){
                logger.warn("getAccountSignResultByAccountId#协议类型非法，accountId:{}，agreeTypes:{}",accountId,agreeTypes);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"协议类型输入错误");
            }
            agreeTypeList.add(Integer.valueOf(str));
        }

        Map<Integer, Integer> recordMap = Maps.newHashMap();
        // signRecordMap
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(agreeTypeList) && accountId != null && accountId > 0) {
            List<WmAgreementAccountSign> records = wmAgreementAccountSignDBMapper.batchQueryByAccountIdAndAggrementTypes(agreeTypeList, accountId);
            logger.debug("getAccountSignResultByAccountId#records:{}", JSON.toJSONString(records));
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(records)){
                recordMap = records.stream().collect(Collectors.toMap(WmAgreementAccountSign::getAgreementType, record -> record.getStatus()));
            }else{
                for(Integer agreeType : agreeTypeList){
                    recordMap.put(agreeType, 0);
                }
            }
        }
        // signTitleMap
        Map<Integer, String> signTitleMap = Maps.newHashMap();
        List<WmAgreementModel> agreementModelList = queryAllAgreementModel();
        if(CollectionUtils.isNotEmpty(agreementModelList)){
            for(WmAgreementModel model : agreementModelList){
                signTitleMap.put(model.getAgreeModelType(), model.getAgreeModelVersionDesc());
            }
        }
        // url
        String url = getUrl();
        // WmAggrementAccountSignResp
        WmAggrementAccountSignResp resp = new WmAggrementAccountSignResp();
        resp.setSignRecordMap(recordMap);
        resp.setSignTitleMap(signTitleMap);
        resp.setUrl(url);
        logger.info("getAccountSignResultByAccountId#result:{}", JSON.toJSONString(resp));
        return resp;
    }

    public String getUrl(){
        //后续扩展可修改逻辑，暂时按照动态配置的方式获取
        return ConfigUtilAdapter.getString("account_sign_result_url", "/igate/customer/agreement/pc/detail.html");
    }

    public List<WmAgreementModel> queryAllAgreementModel(){
        List<WmAgreementModel> allList = Lists.newArrayList();
        List<WmAgreementModel> parList = Lists.newArrayList();
        int offset = 0;
        int pageSize = 100;
        parList = wmAgreementModelDBMapper.queryAgreementModelPage(offset,pageSize);
        allList.addAll(parList);
        while(parList.size() != 0){
            offset += pageSize;
            parList = wmAgreementModelDBMapper.queryAgreementModelPage(offset,pageSize);
            allList.addAll(parList);
        }
        logger.info("queryAllAgreementModel#allList:{}",JSON.toJSONString(allList));
        return allList;
    }

    /**
     * 美团云pdf链接抽取文件名
     * @param source
     * @return
     */
    private static String parsePdfName(String source) {
        //source=/download/mos/524b09a67c64ffe24db763c566a7d3e6.pdf
        //返回524b09a67c64ffe24db763c566a7d3e6.pdf
        if (org.apache.commons.lang3.StringUtils.isBlank(source)) {
            return source;
        }

        if (!source.endsWith(".pdf")) {
            logger.error("仅pdf链接可使用,source={}", source);
            return "";
        }
        String fileName = source.substring(source.lastIndexOf("/") + 1);
        return fileName;
    }
}
