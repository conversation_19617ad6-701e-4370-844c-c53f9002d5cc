package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.TECHNICAL_SERVICE_FEE_V2)
@Slf4j
@Service
public class WmEcontractTechnicalServiceFeeV2PdfMaker extends
        AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker {

    @Autowired
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext)
            throws WmCustomerException, IllegalAccessException, TException {
        log.info("#WmEcontractTechnicalServiceFeeV2PdfMaker");
        SignTemplateEnum signTemplateEnum = extractSignTemplateEnum();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDeliveryInfoBo deliveryInfoBo;
        if(wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(originContext.getCustomerId())){
            List<EcontractDeliveryInfoBo> deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT.getName());
            deliveryInfoBo = deliveryInfoList.get(0);
        } else{
            deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        }

        if(deliveryInfoBo == null){
            log.warn("WmEcontractTechnicalServiceFeeV2PdfMaker deliveryInfoBo为空，流程中止，customerId:{}", originContext.getCustomerId());
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "原始数据缺失，pdf封装失败");
        }

        deliveryInfoBo.setDeliveryArea(null);
        deliveryInfoBo.setEcontractDeliveryWholeCityInfoBo(null);
        deliveryInfoBo.setEcontractDeliveryAggregationInfoBo(null);
        Map<String, String> map = MapUtil.Object2Map(deliveryInfoBo);

        //商家签章信息
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        map.put("signTime", org.apache.commons.lang.StringUtils.defaultIfEmpty(signTime, org.apache.commons.lang.StringUtils.EMPTY));
        map.put("partAStampName", org.apache.commons.lang.StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext),
                org.apache.commons.lang.StringUtils.EMPTY));
        map.put("partAEstamp", PdfConstant.POI_SIGNKEY);

        //基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        pdfInfoBo.setPdfMetaContent(map);
        log.info("#WmEcontractTechnicalServiceFeeV2PdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }

}
