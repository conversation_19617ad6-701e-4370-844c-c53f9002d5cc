package com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.read.CanteenRead;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.read.CanteenReadHandleService;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.write.CanteenEntryWrite;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.write.CanteenEntryWriteHandleService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 食堂敏感词处理
 *
 * <AUTHOR>
 * @date 2021/8/3
 */
@Service
@Slf4j
public class WmScCanteenSensitiveWordsService {


    @Autowired
    private CanteenReadHandleService canteenReadHandleService;


    @Autowired
    private CanteenEntryWriteHandleService canteenEntryWriteHandleService;


    /**
     * 食堂是否写原字段 原字段写开关控制
     *
     * @param bean 食堂KP对象
     * @param key  原字段标记
     */
    private void writeSourceWhenUpdate(WmCanteenDB bean, KmsKeyNameEnum key) {
        CanteenEntryWrite canteenEntryWrite = new CanteenEntryWrite();
        canteenEntryWrite.setKeyName(key);
        canteenEntryWrite.setCanteen(bean);
        canteenEntryWriteHandleService.writeSourceWhenUpdate(canteenEntryWrite);
    }

    /**
     * 食堂是否写原字段 原字段写开关控制
     *
     * @param bean 食堂对象
     */
    public void writeSourceWhenUpdate(WmCanteenDB bean) {
        writeSourceWhenUpdate(bean, KmsKeyNameEnum.PHONE_NO);
        writeSourceWhenUpdate(bean, KmsKeyNameEnum.IDENTIFY_ID);
    }


    /**
     * 食堂是否写原字段 原字段写开关控制
     *
     * @param bean 食堂对象对象
     * @param key  原字段标记
     */
    private void writeSourceWhenUpdate(WmScCanteenAuditDO bean, KmsKeyNameEnum key) {
        CanteenEntryWrite canteenEntryWrite = new CanteenEntryWrite();
        canteenEntryWrite.setKeyName(key);
        canteenEntryWrite.setCanteenAudit(bean);
        canteenEntryWriteHandleService.writeSourceWhenUpdate(canteenEntryWrite);
    }


    /**
     * 食堂是否写原字段 原字段写开关控制
     *
     * @param bean 食堂对象
     */
    public void writeSourceWhenUpdate(WmScCanteenAuditDO bean) {
        writeSourceWhenUpdate(bean, KmsKeyNameEnum.PHONE_NO);
        writeSourceWhenUpdate(bean, KmsKeyNameEnum.IDENTIFY_ID);
    }

    /**
     * 食堂敏感词新老字段写控制
     * 是否写原字段 原字段写开关控制，通过变量控制（变量传入Mapper文件中进行判断）
     * 是否写新字段 新字段写开关控制
     *
     * @param bean
     * @param key
     */
    public void writeWhenInsertOrUpdate(WmCanteenDB bean, KmsKeyNameEnum key) throws WmSchCantException {
        CanteenEntryWrite canteenEntryWrite = new CanteenEntryWrite();
        canteenEntryWrite.setKeyName(key);
        canteenEntryWrite.setCanteen(bean);
        canteenEntryWriteHandleService.doWriteWhenInsertOrUpdate(canteenEntryWrite);
    }

    /**
     * 食堂敏感词新老字段写控制
     *
     * @param bean
     * @throws WmCustomerException
     */
    public void writeWhenInsertOrUpdate(WmCanteenDB bean) throws WmSchCantException {
        writeWhenInsertOrUpdate(bean, KmsKeyNameEnum.PHONE_NO);
        writeWhenInsertOrUpdate(bean, KmsKeyNameEnum.IDENTIFY_ID);
    }

    /**
     * 食堂敏感词新老字段写控制
     * 是否写原字段 原字段写开关控制
     * 是否写新字段 新字段写开关控制
     *
     * @param bean
     * @param key
     */
    public void writeWhenInsertOrUpdate(WmScCanteenAuditDO bean, KmsKeyNameEnum key) throws WmSchCantException {
        CanteenEntryWrite canteenEntryWrite = new CanteenEntryWrite();
        canteenEntryWrite.setKeyName(key);
        canteenEntryWrite.setCanteenAudit(bean);
        canteenEntryWriteHandleService.doWriteWhenInsertOrUpdate(canteenEntryWrite);
    }


    /**
     * 食堂敏感词新老字段写控制
     *
     * @param bean
     * @throws WmCustomerException
     */
    public void writeWhenInsertOrUpdate(WmScCanteenAuditDO bean) throws WmSchCantException {
        writeWhenInsertOrUpdate(bean, KmsKeyNameEnum.PHONE_NO);
        writeWhenInsertOrUpdate(bean, KmsKeyNameEnum.IDENTIFY_ID);
    }


    /**
     * 客户KP敏感词读处理
     *
     * @param list
     */
    private void readWhenSelect(List<WmCanteenDB> list, KmsKeyNameEnum key) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (WmCanteenDB bean : list) {
            if (bean == null) {
                continue;
            }
            readWhenSelect(bean, key);
        }
    }

    /**
     * 食堂敏感词读处理
     *
     * @param list
     */
    public void readWhenSelect(List<WmCanteenDB> list) {
        readWhenSelect(list, KmsKeyNameEnum.PHONE_NO);
        readWhenSelect(list, KmsKeyNameEnum.IDENTIFY_ID);
    }

    /**
     * 食堂敏感词读处理
     *
     * @param bean
     */
    private void readWhenSelect(WmCanteenDB bean, KmsKeyNameEnum key) {
        if (bean == null) {
            return;
        }
        CanteenRead read = new CanteenRead();
        read.setKeyName(key);
        read.setCanteen(bean);
        canteenReadHandleService.doReadChoiceEncryptToDecrypt(read);
    }

    /**
     * 食堂敏感词读处理
     *
     * @param bean
     */
    public void readWhenSelect(WmCanteenDB bean) {
        if (bean == null) {
            return;
        }
        readWhenSelect(bean, KmsKeyNameEnum.PHONE_NO);
        readWhenSelect(bean, KmsKeyNameEnum.IDENTIFY_ID);
    }

    /**
     * 客户KP敏感词读处理
     *
     * @param bean
     */
    public void readWhenSelect(WmScCanteenAuditDO bean, KmsKeyNameEnum key) {
        if (bean == null) {
            return;
        }
        CanteenRead read = new CanteenRead();
        read.setKeyName(key);
        read.setCanteenAudit(bean);
        canteenReadHandleService.doReadChoiceEncryptToDecrypt(read);
    }

    /**
     * 食堂敏感词读处理
     *
     * @param bean
     */
    public void readWhenSelect(WmScCanteenAuditDO bean) {
        if (bean == null) {
            return;
        }
        readWhenSelect(bean, KmsKeyNameEnum.PHONE_NO);
        readWhenSelect(bean, KmsKeyNameEnum.IDENTIFY_ID);
    }

}
