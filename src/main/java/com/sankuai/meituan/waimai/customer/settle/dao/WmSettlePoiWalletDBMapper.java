package com.sankuai.meituan.waimai.customer.settle.dao;

import com.sankuai.meituan.waimai.customer.settle.domain.WmSettlePoiWalletDB;
import com.sankuai.meituan.waimai.datasource.multi.annotation.DataSource;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
@DataSource("dbContractWrite")
public interface WmSettlePoiWalletDBMapper {

    @DataSource("dbContractRead")
    Set<Long> getWalletIdListByWmPoiId(@Param("wmPoiId")Integer wmPoiId);

    void batchInsert(List<WmSettlePoiWalletDB> wmSettlePoiWalletDBList);

    @DataSource("dbContractRead")
    List<Integer> getByWalletIdAndWmPoiId(@Param("walletId")Long walletId, @Param("wmPoiIdList")List<Integer> wmPoiIdList);
}
