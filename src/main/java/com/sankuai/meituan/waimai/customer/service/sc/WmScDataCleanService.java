package com.sankuai.meituan.waimai.customer.service.sc;


import com.alibaba.fastjson.JSONObject;
import com.sankuai.deliverystaff.station.label.view.StationLabelExistView;
import com.sankuai.meituan.waimai.customer.adapter.BmStationLabelServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmCampusContactServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryAssignmentMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryGoalSetMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryStreamDetailMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryStreamMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.customer.domain.sc.metadata.WmScMetadataDO;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallBindService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallClueService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallManageService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.WmSchoolDeliveryService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.basic.WmSchoolDeliveryFollowUpBasicServiceImpl;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusMachine;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallNotFitRuleTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryGoalStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryOpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryStreamNodeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.metadata.WmScMetadataBusinessTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.metadata.WmScMetadataSceneTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScTicketNoticeBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallNotFitRuleUnbindDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliveryFollowUpDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliverySaveParamDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contact.ContactDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.constant.ScFieldConstant.*;
import static com.sankuai.meituan.waimai.customer.service.sc.thrift.WmCanteenThriftServiceImpl.CANTEENID_BEGIN;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.*;

/**
 * @desc 校园食堂数据清洗服务
 * <AUTHOR>
 * @date 2023/10/23
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScDataCleanService {

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmScLogMapper wmScLogMapper;

    @Autowired
    private WmScLogSchoolInfoService wmScLogSchoolInfoService;

    @Autowired
    private WmScSchoolExtensionMapper wmScSchoolExtensionMapper;

    @Autowired
    private WmScLogService wmScLogService;

    @Autowired
    private BmStationLabelServiceAdapter bmStationLabelServiceAdapter;

    @Autowired
    private WmSchoolDeliveryService wmSchoolDeliveryService;

    @Autowired
    private WmSchoolDeliveryStreamMapper wmSchoolDeliveryStreamMapper;

    @Autowired
    private WmSchoolDeliveryStreamDetailMapper wmSchoolDeliveryStreamDetailMapper;

    @Autowired
    private WmScMetadataMapper wmScMetadataMapper;

    @Autowired
    private WmSchoolDeliveryAssignmentMapper wmSchoolDeliveryAssignmentMapper;

    @Autowired
    private WmSchoolDeliveryFollowUpBasicServiceImpl wmSchoolDeliveryFollowUpBasicService;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmSchoolDeliveryGoalSetMapper wmSchoolDeliveryGoalSetMapper;

    @Autowired
    private WmCampusContactServiceAdapter wmCampusContactServiceAdapter;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Autowired
    private WmCanteenStallManageService wmCanteenStallManageService;

    @Autowired
    private WmCanteenStallBindService wmCanteenStallBindService;

    @Autowired
    private WmCanteenStallClueService wmCanteenStallClueService;

    @Autowired
    private WmScCanteenService wmScCanteenService;

    @Autowired
    private WmScSchoolService wmScSchoolService;

    @Autowired
    private CanteenStatusMachine canteenStatusMachine;

    @Autowired
    private WmScCanteenAuditMapper wmScCanteenAuditMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;


    /**
     * 将天转换为秒（x天 * 24小时/天 * 60分钟/小时 * 60秒/分钟）
     */
    private static final long DAYS_IN_SECONDS = 24L * 60L * 60L;

    /**
     * 更新学校聚合站点是否可配送进校属性(定时任务)
     * @param schoolId 学校ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Boolean refreshSchoolAggreSiteAllowDelivery(Integer schoolId) throws WmSchCantException, TException {
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.warn("[WmScDataCleanService.refreshSchoolAggreSiteAllowDelivery] wmSchoolDB is null. schoolId = {}", schoolId);
            return false;
        }
        log.info("[WmScDataCleanService.refreshSchoolAggreSiteAllowDelivery] schoolId = {}", schoolId);
        List<Integer> aggreSiteIdList = StringUtils.isBlank(wmSchoolDB.getAggreSiteId()) ?
                new ArrayList<>() : Arrays.stream(wmSchoolDB.getAggreSiteId().split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        // 计算当前的聚合站点是否可配送进校"属性
        Integer aggreOrderAllowDeliveryRes = getSchoolAggreOrderAllowDelivery(aggreSiteIdList);
        if (wmSchoolDB.getAggreOrderAllowDelivery().equals(aggreOrderAllowDeliveryRes)) {
            log.info("[WmScDataCleanService.refreshSchoolAggreSiteAllowDelivery] aggreOrderAllowDeliveryRes is the same. schoolId = {}, aggreOrderAllowDeliveryRes = {}",
                    schoolId, aggreOrderAllowDeliveryRes);
            return false;
        }

        WmSchoolDB wmSchoolDBUpdate = new WmSchoolDB();
        wmSchoolDBUpdate.setId(wmSchoolDB.getId());
        wmSchoolDBUpdate.setAggreOrderAllowDelivery(aggreOrderAllowDeliveryRes);
        int result = wmSchoolMapper.updateSchool(wmSchoolDBUpdate);
        if (result > 0) {
            BeanUtils.copyProperties(wmSchoolDB, wmSchoolDBUpdate);
            wmSchoolDBUpdate.setAggreOrderAllowDelivery(aggreOrderAllowDeliveryRes);
            String logUpdate = wmScLogService.composeSchoolUpdateLog(wmSchoolDB, wmSchoolDBUpdate);
            if (StringUtils.isNotBlank(logUpdate)) {
                wmScLogService.insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOL_LOG, wmSchoolDB.getId(),
                        0, "系统自动更新", logUpdate, "");
            }
        }

        return true;
    }

    /**
     * 获取学校"聚合站点是否可配送进校"属性
     * @param aggreSiteIdList 聚合站点列表
     * @return 1-是 2-否 0-没有聚合站点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Integer getSchoolAggreOrderAllowDelivery(List<Integer> aggreSiteIdList) throws WmSchCantException, TException {
        log.info("[WmScSchoolService.getSchoolAggreOrderAllowDelivery] input param: aggreSiteIdList = {}", JSONObject.toJSONString(aggreSiteIdList));
        // 如果没有聚合站点, 则返回"没有聚合站点"
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(aggreSiteIdList)) {
            return (int) SchoolAggreOrderAllowDeliveryEnum.NONE_ARRGE_SITE.getType();
        }
        // 有聚合站点但查不到任何信息, 则返回"否"
        List<StationLabelExistView> stationLabelExistViewList = bmStationLabelServiceAdapter.getSchoolAggreSiteExistNow(aggreSiteIdList);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(stationLabelExistViewList)) {
            return (int) SchoolAggreOrderAllowDeliveryEnum.NO.getType();
        }

        Map<Integer, Boolean> map = new HashMap<>();
        for (StationLabelExistView view : stationLabelExistViewList) {
            map.put(view.getStationId().intValue(), view.isHasExistLabel());
        }
        log.info("[WmScSchoolService.getSchoolAggreOrderAllowDelivery] map = {}", JSONObject.toJSONString(map));
        if (MapUtils.isEmpty(map)) {
            log.info("[WmScSchoolService.getSchoolAggreOrderAllowDelivery] map is empty, return NO.");
            return (int) SchoolAggreOrderAllowDeliveryEnum.NO.getType();
        }
        boolean flag = true;
        for (Map.Entry<Integer, Boolean> entry : map.entrySet()) {
            if (!entry.getValue()) {
                flag = false;
                break;
            }
        }
        // 当学校维护的“聚合站点ID”中全部有“可进校”属性，则“聚合订单是否允许配送进校”取值为“是”
        if (flag) {
            log.info("[WmScSchoolService.getSchoolAggreOrderAllowDelivery] flag is true, return YES.");
            return (int) SchoolAggreOrderAllowDeliveryEnum.YES.getType();
        }
        // 当学校维护的“聚合站点ID”中任意无“可进校”属性或无法取值，则“聚合订单是否允许配送进校”取值为“否”
        log.info("[WmScSchoolService.getSchoolAggreOrderAllowDelivery] return NO.");
        return (int) SchoolAggreOrderAllowDeliveryEnum.NO.getType();
    }

    /**
     * 学校交付任务新建与终结(定时任务)
     * @param schoolId 学校ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void initiateAndEndSchoolDeliveryStream(Integer schoolId, Integer userId, String userName, String opSource) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.initiateAndEndSchoolDeliveryStream] schoolId = {}", schoolId);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.info("[WmScDataCleanService.initiateAndEndSchoolDeliveryStream] wmSchoolDB is null. schoolId = {}", schoolId);
            return;
        }

        Integer schoolPrimaryId = WmScTransUtil.getSchoolPrimaryIdBySchoolId(schoolId);
        // 学校的合同/授权有效期为空
        if (wmSchoolDB.getAgreementTimeStart() == null
                || wmSchoolDB.getAgreementTimeStart().equals(0)
                || wmSchoolDB.getAgreementTimeEnd() == null
                || wmSchoolDB.getAgreementTimeEnd().equals(0)) {
            wmSchoolDeliveryService.endSchoolDeliveryStream(userId, userName, opSource, wmSchoolDB);
            return;
        }

        // 当前日期是否在合同/授权有效期内
        if (wmSchoolDB.getAgreementTimeEnd() >= System.currentTimeMillis() / 1000L
                && wmSchoolDB.getAgreementTimeStart() <= System.currentTimeMillis() / 1000L) {
            wmSchoolDeliveryService.initiateSchoolDeliveryStream(schoolPrimaryId, userId, userName, opSource);
        } else {
            wmSchoolDeliveryService.endSchoolDeliveryStream(userId, userName, opSource, wmSchoolDB);
        }
    }

    /**
     * 学校合作续签提醒(定时任务)
     * @param schoolId 学校ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void sendSchoolCooperationRenewalReminderMsg(Integer schoolId) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.sendSchoolCooperationRenewalReminderMsg] schoolId = {}", schoolId);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.info("[WmScDataCleanService.sendSchoolCooperationRenewalReminderMsg] wmSchoolDB is null. schoolId = {}", schoolId);
            return;
        }

        // 1-若当前日期不在合同/授权有效期内,不处理
        if (wmSchoolDB.getAgreementTimeEnd() < System.currentTimeMillis() / 1000L
                || wmSchoolDB.getAgreementTimeStart() > System.currentTimeMillis() / 1000L) {
            log.info("[WmScDataCleanService.sendSchoolCooperationRenewalReminderMsg] NOT IN agreement time range. wmSchoolDB = {}", JSONObject.toJSONString(wmSchoolDB));
            return;
        }

        // 合同/授权有效期的结束日期 - 当前日期, 向学校负责人发送大象消息
        if (wmSchoolDB.getAgreementTimeEnd() - System.currentTimeMillis() / 1000L < (DAYS_IN_SECONDS * MccScConfig.getSchoolCooperationRenewalRemindDays().longValue())) {
            String misId = wmSchoolDB.getResponsiblePerson();
            String reciver = String.format("%<EMAIL>", misId);
            String msg = "你所负责的学校" + wmSchoolDB.getSchoolName()
                    + "（" + wmSchoolDB.getSchoolId() + "）"
                    + "按照学校管理中录入的合同/授权有效期计算，剩余合作时间已经小于90天，请及时发起沟通进行续签动作";
            DaxiangUtilV2.push(msg, reciver);
        }
    }

    /**
     * 学校交付跟进目标状态预警和延期提醒(定时任务)
     * @param schoolId 学校ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void sendSchoolDeliveryFollowUpStatusReminderMsg(Integer schoolId) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.sendSchoolDeliveryFollowUpStatusReminderMsg] schoolId = {}", schoolId);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.info("[WmScDataCleanService.sendSchoolDeliveryFollowUpStatusReminderMsg] wmSchoolDB is null. schoolId = {}", schoolId);
            return;
        }
        Integer schoolPrimaryId = WmScTransUtil.getSchoolPrimaryIdBySchoolId(schoolId);

        // 1-查询仍在流程中的交付流
        WmSchoolDeliveryStreamDO streamDO = wmSchoolDeliveryStreamMapper.selectBySchoolPrimaryIdOnStram(schoolPrimaryId);
        if (streamDO == null || !streamDO.getStreamNode().equals(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode())) {
            log.info("[WmScDataCleanService.sendSchoolDeliveryFollowUpStatusReminderMsg] delivery not match. streamDO = {}", JSONObject.toJSONString(streamDO));
            return;
        }

        // 2-查询当前交付跟进的审批状态
        WmSchoolDeliveryStreamDetailDO streamDetailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(streamDO.getId(), SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        if (streamDetailDO != null && streamDetailDO.getAuditStatus().equals((int) SchoolDeliveryAuditStatusEnum.AUDITING.getType())) {
            log.info("[WmScDataCleanService.sendSchoolDeliveryFollowUpStatusReminderMsg] delivery followup auditing. streamDetailDO = {}", JSONObject.toJSONString(streamDetailDO));
            return;
        }

        // 3-查询交付跟进最新快照信息
        WmScMetadataDO metadataDO = wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(), streamDO.getId());
        if (metadataDO == null) {
            log.error("[WmScDataCleanService.sendSchoolDeliveryFollowUpStatusReminderMsg] metadataDO is NULL. deliveryId = {}", streamDO.getId());
            return;
        }
        WmSchoolDeliveryFollowUpDTO followUpDTO = JSONObject.parseObject(metadataDO.getDataJson(), WmSchoolDeliveryFollowUpDTO.class);

        // 4-1 聚合配送模块进行延期和预警检测
        List<String> aggreDeliveryWarnList = getAggreDeliveryModuleWarnList(followUpDTO);
        // 4-2 食堂档口模块进行延期和预警检测
        List<String> canteenStallWarnList = getCanteenStallModuleWarnList(followUpDTO);
        // 4-3 运营效果模块进行延期和预警检测
        List<String> operationIndexWarnList = getOperationIndexModuleWarnList(followUpDTO);

        WmSchoolDeliveryAssignmentDO assignmentDO = wmSchoolDeliveryAssignmentMapper.selectByDeliveryId(followUpDTO.getDeliveryId());
        // 5-1 客户成功经理发送通知
        List<String> csmWarnList = new ArrayList<>();
        csmWarnList.addAll(aggreDeliveryWarnList);
        csmWarnList.addAll(canteenStallWarnList);
        csmWarnList.addAll(operationIndexWarnList);
        // 5-2 聚合渠道经理发送通知
        List<String> acmWarnList = new ArrayList<>(aggreDeliveryWarnList);
        // 5-3 学校对应蜂窝负责人发送通知
        List<String> aormWarnList = new ArrayList<>(canteenStallWarnList);

        // 6-按照mis对消息聚合并发送
        Map<Integer, List<String>> map = new HashMap<>();
        map.computeIfAbsent(assignmentDO.getCsmUid().intValue(), k -> new ArrayList<>()).addAll(csmWarnList);
        map.computeIfAbsent(assignmentDO.getAcmUid().intValue(), k -> new ArrayList<>()).addAll(acmWarnList);
        map.computeIfAbsent(assignmentDO.getAormUid().intValue(), k -> new ArrayList<>()).addAll(aormWarnList);
        log.info("[WmScDataCleanService.sendSchoolDeliveryFollowUpStatusReminderMsg] map = {}", JSONObject.toJSONString(map));

        for (Map.Entry<Integer, List<String>> entry : map.entrySet()) {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                List<String> warnListWithoutDuplicates = entry.getValue().stream()
                        .distinct()
                        .collect(Collectors.toList());
                sendSchoolDeliveryFollowUpStatusReminderMsg(entry.getKey(), warnListWithoutDuplicates, wmSchoolDB);
            }
        }
    }

    public void sendSchoolDeliveryFollowUpStatusReminderMsg(Integer uid, List<String> warnList, WmSchoolDB wmSchoolDB) throws WmSchCantException {
        if (CollectionUtils.isEmpty(warnList)) {
            log.info("[WmScDataCleanService.sendSchoolDeliveryFollowUpStatusReminderMsg] warnList is empty. uid = {}", uid);
            return;
        }
        String msg = getSchoolDeliveryFollowUpReminderMsg(warnList, wmSchoolDB);
        WmEmploy employ = wmScEmployAdaptor.getWmEmployByUid(uid);
        String reciver = String.format("%<EMAIL>", employ.getMisId());
        DaxiangUtilV2.push(msg, reciver);
    }

    public String getSchoolDeliveryFollowUpReminderMsg(List<String> warnList, WmSchoolDB wmSchoolDB) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("你负责的学校\"").append(wmSchoolDB.getSchoolName()).append("（").append(wmSchoolDB.getSchoolId()).append("）\"中");
        List<String> warnLimitList = warnList.stream()
                .limit(3)
                .collect(Collectors.toList());
        warnLimitList.replaceAll(s -> "【" + s + "】");
        stringBuilder.append(StringUtils.join(warnLimitList, "、"));
        stringBuilder.append("等").append(warnList.size()).append("个指标值已处于预警或延期状态，请关注并进入系统填写情况说明。");
        stringBuilder.append("[前往系统|").append(MccScConfig.getSchoolDeliveryFollowUpPageUrlPrefix()).append(wmSchoolDB.getId()).append("]");
        return stringBuilder.toString();
    }

    public List<String> getAggreDeliveryModuleWarnList(WmSchoolDeliveryFollowUpDTO followUpDTO) {
        List<String> aggreDeliveryWarnList = new ArrayList<>();
        // 校内站线上签约当前状态
        if (followUpDTO.getInscOnlineSignStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getInscOnlineSignStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            aggreDeliveryWarnList.add(FOLLOWUP_INSC_ONLINE_SIGN_STATUS_DESC);
        }

        // 校内站线下建站当前状态
        if (followUpDTO.getInscOfflineBuildStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getInscOfflineBuildStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            aggreDeliveryWarnList.add(FOLLOWUP_INSC_OFFLINE_BUILD_STATUS_DESC);
        }

        // 校外站线上签约当前状态
        if (followUpDTO.getOutscOnlineSignStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getOutscOnlineSignStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            aggreDeliveryWarnList.add(FOLLOWUP_OUTSC_ONLINE_SIGN_STATUS_DESC);
        }

        // 校外站线下建站当前状态
        if (followUpDTO.getOutscOfflineBuildStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getOutscOfflineBuildStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            aggreDeliveryWarnList.add(FOLLOWUP_OUTSC_OFFLINE_BUILD_STATUS_DESC);
        }
        return aggreDeliveryWarnList;
    }

    public List<String> getCanteenStallModuleWarnList(WmSchoolDeliveryFollowUpDTO followUpDTO) {
        List<String> canteenStallWarnList = new ArrayList<>();
        // 校内站线上签约当前状态
        if (followUpDTO.getFirstStallOnlineStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getFirstStallOnlineStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            canteenStallWarnList.add(FOLLOWUP_FIRST_STALL_ONLINE_STATUS_DESC);
        }
        return canteenStallWarnList;
    }

    public List<String> getOperationIndexModuleWarnList(WmSchoolDeliveryFollowUpDTO followUpDTO) {
        List<String> operationIndexWarnList = new ArrayList<>();
        // 在线档口渗透率
        if (followUpDTO.getOnlinePenerateStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getOnlinePenerateStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            operationIndexWarnList.add(FOLLOWUP_ONLINE_PENERATE_STATUS_DESC);
        }

        // 相对准时率
        if (followUpDTO.getOntimeRateStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getOntimeRateStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            operationIndexWarnList.add(FOLLOWUP_ONTIME_RATE_STATUS_DESC);
        }

        // 平均配送时长
        if (followUpDTO.getAvgDeliveryTimeStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getAvgDeliveryTimeStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            operationIndexWarnList.add(FOLLOWUP_AVG_DELIVERY_TIME_STATUS_DESC);
        }

        // 日均订单量
        if (followUpDTO.getDailyOrderStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getDailyOrderStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            operationIndexWarnList.add(FOLLOWUP_DAILY_ORDER_STATUS_DESC);
        }

        // 人顿渗透率
        if (followUpDTO.getMealPenerateStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getMealPenerateStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            operationIndexWarnList.add(FOLLOWUP_MEAL_PENERATE_STATUS_DESC);
        }

        // 日均店单产
        if (followUpDTO.getDailyYieldStatus().equals((int) SchoolDeliveryGoalStatusEnum.WARN.getType())
                || followUpDTO.getDailyYieldStatus().equals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType())) {
            operationIndexWarnList.add(FOLLOWUP_DAILY_YIELD_STATUS_DESC);
        }

        return operationIndexWarnList;
    }

    /**
     * 学校交付跟进目标完成情况和目标当前状态更新(定时任务)
     * @param schoolId 学校ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void refreshSchoolDelveryGoalAchieveAndGoalStatus(Integer schoolId, WmSchoolDeliveryGoalSetDO goalSetDO, String userName) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.refreshSchoolDelveryGoalAchieveAndGoalStatus] schoolId = {}", schoolId);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.info("[WmScDataCleanService.refreshSchoolDelveryGoalAchieveAndGoalStatus] wmSchoolDB is null. schoolId = {}", schoolId);
            return;
        }

        // 1-查询仍在流程中的交付流, 包括生效和未生效的交付跟进
        Integer schoolPrimaryId = WmScTransUtil.getSchoolPrimaryIdBySchoolId(schoolId);
        WmSchoolDeliveryStreamDO streamDO = wmSchoolDeliveryStreamMapper.selectBySchoolPrimaryIdOnStram(schoolPrimaryId);
        if (streamDO == null
                || streamDO.getStreamNode().equals(SchoolDeliveryStreamNodeEnum.DELIVERY_ASSIGNMENT.getCode())
                || streamDO.getStreamNode().equals(SchoolDeliveryStreamNodeEnum.DELIVERY_GOALSET.getCode())) {
            log.info("[WmScDataCleanService.refreshSchoolDelveryGoalAchieveAndGoalStatus] delivery not match. streamDO = {}", JSONObject.toJSONString(streamDO));
            return;
        }
        if (goalSetDO == null) {
            goalSetDO = wmSchoolDeliveryGoalSetMapper.selectByDeliveryId(streamDO.getId());
        }

        // 2-查询交付跟进最新快照信息
        WmScMetadataDO metadataDOBefore = wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(), streamDO.getId());
        WmSchoolDeliveryFollowUpDTO followUpDTOBefore = metadataDOBefore == null ? null : JSONObject.parseObject(metadataDOBefore.getDataJson(), WmSchoolDeliveryFollowUpDTO.class);
        WmSchoolDeliveryFollowUpDTO followUpDTOInsert = new WmSchoolDeliveryFollowUpDTO();
        if (followUpDTOBefore != null) {
            BeanUtils.copyProperties(followUpDTOBefore, followUpDTOInsert);
        } else {
            setFollowUpDTOInsertDefaultValue(followUpDTOInsert);
        }
        followUpDTOInsert.setSchoolPrimaryId(schoolPrimaryId);
        followUpDTOInsert.setDeliveryId(streamDO.getId());

        // 3-组装从数据侧和交付人员指定同步的数据
        WmScSchoolExtensionDO extensionDO = wmScSchoolExtensionMapper.selectBySchoolId(schoolId);
        WmSchoolDeliveryAssignmentDO assignmentDO = wmSchoolDeliveryAssignmentMapper.selectByDeliveryId(streamDO.getId());
        syncSchoolExtensionDataToDeliveryFollowUpDO(followUpDTOInsert, extensionDO);
        syncSchoolDeliveryAssignmentToDeliveryFollowUpDO(followUpDTOInsert, assignmentDO);

        // 4-计算目标完成情况(运营监控模块 & 在线渗透率 & 生命周期)
        wmSchoolDeliveryFollowUpBasicService.calculateDeliveryFollowUpGoalAchieve(followUpDTOInsert, goalSetDO);

        // 5-计算目标当前状态(运营监控模块)
        wmSchoolDeliveryFollowUpBasicService.calculateDeliveryFollowUpOperationIndexGoalStatus(followUpDTOInsert, goalSetDO);

        // 6-计算目标当前状态(聚合配送模块 & 食堂档口模块)
        wmSchoolDeliveryFollowUpBasicService.calculateDeliveryFollowUpAggreCanteenGoalStatus(followUpDTOInsert, goalSetDO);

        // 7-新增交付跟进快照数据
        WmScMetadataDO metadataDOInsert = getMetadataDOInsertByMetadataDOBeforeAndFollowUpDO(metadataDOBefore, followUpDTOInsert);
        wmScMetadataMapper.insertSelective(metadataDOInsert);

        // 8-记录操作日志
        WmSchoolDeliverySaveParamDTO saveParamDTO = new WmSchoolDeliverySaveParamDTO();
        saveParamDTO.setUserId(0);
        saveParamDTO.setUserName(userName);
        saveParamDTO.setSchoolPrimaryId(schoolPrimaryId);
        wmSchoolDeliveryFollowUpBasicService.recordTempSaveLog(streamDO.getId(), saveParamDTO, followUpDTOBefore, followUpDTOInsert);
    }

    public void syncSchoolDeliveryAssignmentToDeliveryFollowUpDO(WmSchoolDeliveryFollowUpDTO followUpDO, WmSchoolDeliveryAssignmentDO assignmentDO) {
        followUpDO.setInitiatorUid(assignmentDO.getInitiatorUid().intValue());
        followUpDO.setInitiationTime(assignmentDO.getInitiationTime());
        followUpDO.setAcmUid(assignmentDO.getAcmUid().intValue());
        followUpDO.setCsmUid(assignmentDO.getCsmUid().intValue());
        followUpDO.setAormUid(assignmentDO.getAormUid().intValue());
    }

    public void syncSchoolExtensionDataToDeliveryFollowUpDO(WmSchoolDeliveryFollowUpDTO followUpDTO, WmScSchoolExtensionDO extensionDO) {
        log.info("[WmScDataCleanService.syncSchoolExtensionDataToDeliveryFollowUpDO] extensionDO = {}", JSONObject.toJSONString(extensionDO));
        if (extensionDO == null) {
            followUpDTO.setOntimeRate("");
            followUpDTO.setAvgDeliveryTime("");
            followUpDTO.setDailyOrder("");
            followUpDTO.setMealPenerate("");
            followUpDTO.setDailyYield("");
            return;
        }
        // 相对准时率(*100 保留两位小数)
        followUpDTO.setOntimeRate(extensionDO.getOntimeRate() == null ?
                "" : BigDecimal.valueOf(extensionDO.getOntimeRate()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString());
        // 平均配送时长(保留两位小数)
        followUpDTO.setAvgDeliveryTime(extensionDO.getAvgDeliveryTime() == null ?
                "" : BigDecimal.valueOf(extensionDO.getAvgDeliveryTime()).setScale(2, RoundingMode.HALF_UP).toString());
        // 日均订单量(保留两位小数)
        followUpDTO.setDailyOrder(extensionDO.getDailyOrder() == null ?
                "" : BigDecimal.valueOf(extensionDO.getDailyOrder()).setScale(2, RoundingMode.HALF_UP).toString());
        // 人顿渗透率(*100 保留两位小数)
        followUpDTO.setMealPenerate(extensionDO.getMealPenerate() == null ?
                "" : BigDecimal.valueOf(extensionDO.getMealPenerate()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString());
        // 日均店单产(保留两位小数)
        followUpDTO.setDailyYield(extensionDO.getDailyYield() == null ?
                "" : BigDecimal.valueOf(extensionDO.getDailyYield()).setScale(2, RoundingMode.HALF_UP).toString());
        log.info("[WmScDataCleanService.syncSchoolExtensionDataToDeliveryFollowUpDO] followUpDTO = {}, extensionDO = {}", JSONObject.toJSONString(followUpDTO), JSONObject.toJSONString(extensionDO));
    }

    public WmScMetadataDO getMetadataDOInsertByMetadataDOBeforeAndFollowUpDO(WmScMetadataDO metadataDOBefore, WmSchoolDeliveryFollowUpDTO followUpDTOInsert) throws WmSchCantException {
        Integer dataVersion = wmSchoolDeliveryService.getCurrentDataVersion(metadataDOBefore);
        Integer templateVersion = wmSchoolDeliveryService.getCurrentTemplateVersion(metadataDOBefore, WmScMetadataBusinessTypeEnum.DELIVERY_FOLLOWUP.getCode());
        log.info("[WmScDataCleanService.getMetadataDOInsertByMetadataDOBeforeAndFollowUpDO] followUpDTOInsert = {}, dataVersion = {}, templateVersion = {}",
                JSONObject.toJSONString(followUpDTOInsert), dataVersion, templateVersion);
        // cuid/muid=0表示操作人为系统，展示为: 系统(system)于2024-04-15 14:34:51暂存
        return WmScMetadataDO.builder()
                .scene(WmScMetadataSceneTypeEnum.SCHOOL_DELIVERY.getCode())
                .businessId(followUpDTOInsert.getDeliveryId())
                .businessType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode())
                .dataJson(JSONObject.toJSONString(followUpDTOInsert))
                .opType((int) SchoolDeliveryOpTypeEnum.TEMP_SAVE.getType())
                .dataVersion(dataVersion)
                .templateVersion(templateVersion)
                .cuid(0L)
                .muid(0L)
                .build();
    }

    /**
     * 学校交付普遍客户关系梳理人员ID同步更新-删除场景(定时任务)
     * @param schoolId 学校ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void refreshSchoolDeliveryContactUserIds(Integer schoolId) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.refreshSchoolDeliveryContactUserIds] input param: schoolId = {}", schoolId);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.info("[WmScDataCleanService.refreshSchoolDeliveryContactUserIds] wmSchoolDB is null. schoolId = {}", schoolId);
            return;
        }
        Integer schoolPrimaryId = WmScTransUtil.getSchoolPrimaryIdBySchoolId(schoolId);

        // 1-查询仍在流程中的交付流
        WmSchoolDeliveryStreamDO streamDO = wmSchoolDeliveryStreamMapper.selectBySchoolPrimaryIdOnStram(schoolPrimaryId);
        if (streamDO == null || streamDO.getStreamNode().equals(SchoolDeliveryStreamNodeEnum.DELIVERY_ASSIGNMENT.getCode())) {
            log.info("[WmScDataCleanService.refreshSchoolDeliveryContactUserIds] delivery not match. streamDO = {}", JSONObject.toJSONString(streamDO));
            return;
        }

        // 2-查询交付目标制定最新快照信息和生效信息
        WmScMetadataDO metadataDO = wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(SchoolDeliveryStreamNodeEnum.DELIVERY_GOALSET.getCode(), streamDO.getId());
        WmSchoolDeliveryGoalSetDO goalSetDO = wmSchoolDeliveryGoalSetMapper.selectByDeliveryId(streamDO.getId());
        if (metadataDO == null && goalSetDO == null) {
            log.info("[WmScDataCleanService.refreshSchoolDeliveryContactUserIds] metadataDO&goalSetDO is NULL. deliveryId = {}", streamDO.getId());
            return;
        }

        // 3-交付目标制定生效信息-普遍客户关系人员ID更新
        refreshSchoolDeliveryContactUserIdsByEffectiveInfo(goalSetDO);
    }

    public void refreshSchoolDeliveryContactUserIdsByEffectiveInfo(WmSchoolDeliveryGoalSetDO goalSetDO) throws WmSchCantException {
        if (goalSetDO == null) {
            log.info("[WmScDataCleanService.refreshSchoolDeliveryContactUserIdsByEffectiveInfo] goalSetDO is null, return.");
            return;
        }
        // 学校侧普遍客户ID列表
        List<String> userIdsSchool = Arrays.asList(goalSetDO.getContactUserIds().split(","));
        Set<String> userIdSetSchool = new HashSet<>(userIdsSchool);

        // CRM侧普遍客户ID列表
        Set<String> userIdSetCRM = getUserIdSetFromCRM(userIdsSchool);
        if (userIdSetSchool.equals(userIdSetCRM)) {
            log.info("[WmScDataCleanService.refreshSchoolDeliveryContactUserIdsByEffectiveInfo] contact user id effect not change. userIds = {}", JSONObject.toJSONString(userIdsSchool));
            return;
        }

        log.info("[WmScDataCleanService.refreshSchoolDeliveryContactUserIdsByEffectiveInfo] contact user changed. userIdsSchool = {}, userIdsCRM = {}",
                JSONObject.toJSONString(userIdSetSchool), JSONObject.toJSONString(userIdSetCRM));
        goalSetDO.setContactUserIds(String.join( ",", userIdSetCRM));
        wmSchoolDeliveryGoalSetMapper.updateByPrimaryKeySelective(goalSetDO);
    }

    public Set<String> getUserIdSetFromCRM(List<String> userIdsSchool) throws WmSchCantException {
        List<ContactDto> contactDtoList = wmCampusContactServiceAdapter.getSchoolContactsByUserIds(userIdsSchool);
        // CRM侧普遍客户ID列表
        return contactDtoList.stream().map(ContactDto::getId).collect(Collectors.toSet());
    }

    private void setFollowUpDTOInsertDefaultValue(WmSchoolDeliveryFollowUpDTO followUpDTOInsert) {
        followUpDTOInsert.setInscOnlineSignAchieve(0);
        followUpDTOInsert.setInscOfflineBuildAchieve(0);
        followUpDTOInsert.setOutscOnlineSignAchieve(0);
        followUpDTOInsert.setOutscOfflineBuildAchieve(0);
        followUpDTOInsert.setFirstStallOnlineAchieve(0);
    }


    /**
     * 食堂档口绑定任务-解绑不合规的线索和门店(定时任务)
     * @param canteenId 食堂ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public List<WmCanteenStallNotFitRuleUnbindDTO> unbindWmPoiAndWdClueNotFitRule(Integer canteenId) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.unbindWmPoiAndWdClueNotFitRule] input param: canteenId = {}", canteenId);
        if (canteenId == null || canteenId <= CANTEENID_BEGIN) {
            log.error("[WmScDataCleanService.unbindWmPoiAndWdClueNotFitRule] invalid canteenId = {}", canteenId);
            return new ArrayList<>();
        }

        Integer canteenPrimaryId = WmScTransUtil.getCanteenPrimaryIdByCanteenId(canteenId);
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (wmCanteenDB == null) {
            log.info("[WmScDataCleanService.unbindWmPoiAndWdClueNotFitRule] canteen is not exist. canteenId = {}", canteenId);
            return new ArrayList<>();
        }

        // 1-根据食堂ID查询不合规的档口绑定任务
        List<WmCanteenStallNotFitRuleUnbindDTO> unbindDTOList = wmCanteenStallBindService.getStallBindNotFitRuleByCanteenPrimaryId(canteenPrimaryId);
        if (CollectionUtils.isEmpty(unbindDTOList) || MccScConfig.getAllowAutoUnbindBreakRuleTypes().isEmpty()) {
            return unbindDTOList;
        }

        // 2-执行解绑动作
        wmCanteenStallBindService.unbindClueAndWmPoiByNotFitRule(unbindDTOList);

        // 3-发送大象通知
        sendWmPoiAndClueUnbindReminderMsg(unbindDTOList, canteenPrimaryId);

        log.info("[WmScDataCleanService.unbindWmPoiAndWdClueNotFitRule] unbindDTOList = {}", JSONObject.toJSONString(unbindDTOList));
        return unbindDTOList;
    }

    /**
     * 食堂信息更新时间优化任务(定时任务)
     * @param canteenId 食堂ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public int canteenAutoUpdate(Integer canteenId) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.canteenAutoUpdate] input param: canteenId = {}", canteenId);
        if (canteenId == null || canteenId <= CANTEENID_BEGIN) {
            log.error("[WmScDataCleanService.canteenAutoUpdate] invalid canteenId = {}", canteenId);
            return 0;
        }

        Integer canteenPrimaryId = WmScTransUtil.getCanteenPrimaryIdByCanteenId(canteenId);
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenByIdIgnoreValid(canteenPrimaryId);
        if (wmCanteenDB == null) {
            log.info("[WmScDataCleanService.canteenAutoUpdate] canteen is not exist. canteenId = {}", canteenId);
            return 0;
        }

        // 1.根据食堂ID查询近三个月内发生变更的uTime信息
        WmScOplogBo wmScOplogBo = new WmScOplogBo();
        wmScOplogBo.setModuleId(canteenPrimaryId);
        //模块类型:学校1；食堂:2
        wmScOplogBo.setModuleType((short) 2);
        WmScLogDB wmScLogDB = wmScLogMapper.selectNewestOfLastThreeMonthOpLog(wmScOplogBo);
        if (wmScLogDB == null) {
            log.info("[WmScDataCleanService.canteenAutoUpdate] wmScLogDB is not exist. canteenId = {}", canteenId);
            return 0;
        }

        // 2.执行更新操作
        //执行更新食堂信息
        ArrayList<WmScLogDB> wmScLogDBArrayList = new ArrayList<>();
        wmScLogDBArrayList.add(wmScLogDB);
        int result = wmScLogSchoolInfoService.changeCanteenUTime(wmScLogDBArrayList);

        log.info("[WmScDataCleanService.canteenAutoUpdate] result = {}", result);
        return 1;
    }

    /**
     * 发送门店和线索解绑大象消息
     * @param unbindDTOList unbindDTOList
     * @param canteenPrimaryId 食堂主键ID
     */
    public void sendWmPoiAndClueUnbindReminderMsg(List<WmCanteenStallNotFitRuleUnbindDTO> unbindDTOList, Integer canteenPrimaryId) {
        if (CollectionUtils.isEmpty(unbindDTOList) || MccScConfig.getAllowAutoUnbindBreakRuleTypes().isEmpty()) {
            return;
        }

        // 1-查询食堂责任人MIS
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (StringUtils.isBlank(wmCanteenDB.getResponsiblePerson())) {
            log.info("[WmScDataCleanService.sendWmPoiAndClueUnbindReminderMsg] canteen responsible person is null. unbindDTOList = {}, canteenPrimaryId = {}",
                    JSONObject.toJSONString(unbindDTOList), canteenPrimaryId);
            return;
        }

        // 2-过滤不允许自动解绑的类型
        List<Integer> allowAutoUnbindBreakRuleTypes = MccScConfig.getAllowAutoUnbindBreakRuleTypes();
        List<WmCanteenStallNotFitRuleUnbindDTO> filteredUnbindDTOList = unbindDTOList.stream()
                .filter(unbindDTO -> allowAutoUnbindBreakRuleTypes.contains(unbindDTO.getUnFitRuleType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredUnbindDTOList)) {
            log.info("[WmScDataCleanService.sendWmPoiAndClueUnbindReminderMsg] filteredUnbindDTOList is empty, return. unbindDTOList = {}, canteenPrimaryId = {}",
                    JSONObject.toJSONString(unbindDTOList), canteenPrimaryId);
            return;
        }

        // 3-组装并发送大象消息
        String msg = composeWmPoiAndClueUnbindMsg(filteredUnbindDTOList);
        log.info("[WmScDataCleanService.sendWmPoiAndClueUnbindReminderMsg] msg = {}, misId = {}", msg, wmCanteenDB.getResponsiblePerson());

        DaxiangUtilV2.push(msg, String.format("%<EMAIL>", wmCanteenDB.getResponsiblePerson()));
    }

    private String composeWmPoiAndClueUnbindMsg(List<WmCanteenStallNotFitRuleUnbindDTO> filteredUnbindDTOList) {
        // 门店释放
        List<WmCanteenStallNotFitRuleUnbindDTO> unbindDTOListByPoiDelete = new ArrayList<>();
        // 门店下线
        List<WmCanteenStallNotFitRuleUnbindDTO> unbindDTOListByPoiOffline = new ArrayList<>();
        // 门店坐标不在学校范围内
        List<WmCanteenStallNotFitRuleUnbindDTO> unbindDTOListByPoiNotInSchool = new ArrayList<>();

        for (WmCanteenStallNotFitRuleUnbindDTO unbindDTO : filteredUnbindDTOList) {
            CanteenStallNotFitRuleTypeEnum typeEnum = CanteenStallNotFitRuleTypeEnum.getByCode(unbindDTO.getUnFitRuleType());
            switch (typeEnum) {
                case POI_DELETED:
                    unbindDTOListByPoiDelete.add(unbindDTO);
                    break;
                case POI_OFFLINE:
                    unbindDTOListByPoiOffline.add(unbindDTO);
                    break;
                case POI_OR_CLUE_NOT_IN_SCHOOL:
                    unbindDTOListByPoiNotInSchool.add(unbindDTO);
                    break;
                default:
                    break;
            }
        }

        List<String> msgList = new ArrayList<>();
        String poiDeleteMsg = composeWmPoiAndClueUnbindMsgWithPoiDelete(unbindDTOListByPoiDelete);
        if (StringUtils.isNotBlank(poiDeleteMsg)) {
            msgList.add(poiDeleteMsg);
        }

        String poiOfflineMsg = composeWmPoiAndClueUnbindMsgWithPoiOffline(unbindDTOListByPoiOffline);
        if (StringUtils.isNotBlank(poiOfflineMsg)) {
            msgList.add(poiOfflineMsg);
        }

        String poiNotInSchoolMsg = composeWmPoiAndClueUnbindMsgWithPoiNotInSchool(unbindDTOListByPoiNotInSchool);
        if (StringUtils.isNotBlank(poiNotInSchoolMsg)) {
            msgList.add(poiNotInSchoolMsg);
        }
        return StringUtils.join(msgList, "\n");
    }


    private String composeWmPoiAndClueUnbindMsgWithPoiDelete(List<WmCanteenStallNotFitRuleUnbindDTO> unbindDTOListByPoiDelete) {
        if (CollectionUtils.isEmpty(unbindDTOListByPoiDelete)) {
            return Strings.EMPTY;
        }

        String msg = CanteenStallNotFitRuleTypeEnum.POI_DELETED.getMsg() + "：\n";

        List<String> poiNameList = new ArrayList<>();
        for (WmCanteenStallNotFitRuleUnbindDTO unbindDTO : unbindDTOListByPoiDelete) {
            poiNameList.add(unbindDTO.getWmPoiName() + "（" + unbindDTO.getWmPoiId() + "）");
        }

        // 添加门店已释放，相关线索解除和食堂绑定
        StringBuffer addMsg = new StringBuffer();
        addMsg.append("门店已释放，相关线索解除和食堂绑定。\n");
        try{
            List<String> wdcClueIdList = new ArrayList<>();
            for (WmCanteenStallNotFitRuleUnbindDTO unbindDTO : unbindDTOListByPoiDelete) {
                wdcClueIdList.add(String.valueOf(unbindDTO.getWdcClueId()));
            }
            addMsg.append("线索：" + StringUtils.join(wdcClueIdList, "、") + "\n");
        }catch (Exception e){
            log.error("门店释放-线索解绑消息文本异常");
            return msg + "门店：" + StringUtils.join(poiNameList, "、");
        }
        return msg + "门店：" + StringUtils.join(poiNameList, "、") + "\n" + addMsg;
    }


    private String composeWmPoiAndClueUnbindMsgWithPoiOffline(List<WmCanteenStallNotFitRuleUnbindDTO> unbindDTOListByPoiOffline) {
        if (CollectionUtils.isEmpty(unbindDTOListByPoiOffline)) {
            return Strings.EMPTY;
        }

        String msg = CanteenStallNotFitRuleTypeEnum.POI_OFFLINE.getMsg() + "：\n";

        List<String> poiNameList = new ArrayList<>();
        for (WmCanteenStallNotFitRuleUnbindDTO unbindDTO : unbindDTOListByPoiOffline) {
            poiNameList.add(unbindDTO.getWmPoiName() + "（" + unbindDTO.getWmPoiId() + "）");
        }

        List<String> wdcClueIdList = new ArrayList<>();
        for (WmCanteenStallNotFitRuleUnbindDTO unbindDTO : unbindDTOListByPoiOffline) {
            if (unbindDTO.getWdcClueId() == null || unbindDTO.getWdcClueId() <= 0L) {
                continue;
            }
            wdcClueIdList.add(String.valueOf(unbindDTO.getWdcClueId()));
        }

        if (CollectionUtils.isNotEmpty(wdcClueIdList)) {
            return msg + "门店：" + StringUtils.join(poiNameList, "、") + "\n" + "线索：" + StringUtils.join(wdcClueIdList, "、") + "\n";
        }
        return msg + "门店：" + StringUtils.join(poiNameList, "、") + "\n";
    }


    private String composeWmPoiAndClueUnbindMsgWithPoiNotInSchool(List<WmCanteenStallNotFitRuleUnbindDTO> unbindDTOListByPoiNotInSchool) {
        if (CollectionUtils.isEmpty(unbindDTOListByPoiNotInSchool)) {
            return Strings.EMPTY;
        }

        String msg = CanteenStallNotFitRuleTypeEnum.POI_OR_CLUE_NOT_IN_SCHOOL.getMsg() + "：\n";

        List<String> poiNameList = new ArrayList<>();
        for (WmCanteenStallNotFitRuleUnbindDTO unbindDTO : unbindDTOListByPoiNotInSchool) {
            poiNameList.add(unbindDTO.getWmPoiName() + "（" + unbindDTO.getWmPoiId() + "）");
        }

        List<String> wdcClueIdList = new ArrayList<>();
        for (WmCanteenStallNotFitRuleUnbindDTO unbindDTO : unbindDTOListByPoiNotInSchool) {
            if (unbindDTO.getWdcClueId() == null || unbindDTO.getWdcClueId() <= 0L) {
                continue;
            }
            wdcClueIdList.add(String.valueOf(unbindDTO.getWdcClueId()));
        }

        if (CollectionUtils.isNotEmpty(wdcClueIdList)) {
            return msg + "门店：" + StringUtils.join(poiNameList, "、") + "\n" + "线索：" + StringUtils.join(wdcClueIdList, "、") + "\n";
        }
        return msg + "门店：" + StringUtils.join(poiNameList, "、") + "\n";
    }


    /**
     * 初始化食堂档口管理和档口绑定任务(定时任务)
     * @param canteenId 食堂ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void initiateCanteenStallByCanteenId(Integer canteenId) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.initiateCanteenStallByCanteenId] input param: canteenId = {}", canteenId);
        if (canteenId == null || canteenId <= CANTEENID_BEGIN) {
            log.error("[WmScDataCleanService.initiateCanteenStallByCanteenId] invalid canteenId = {}", canteenId);
            return;
        }

        Integer canteenPrimaryId = WmScTransUtil.getCanteenPrimaryIdByCanteenId(canteenId);
        // 1-食堂为空则直接返回
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (wmCanteenDB == null) {
            log.info("[WmScDataCleanService.initiateCanteenStallByCanteenId] canteen is not exist. canteenId = {}", canteenId);
            return;
        }

        // 2-若食堂已有档口绑定任务则直接返回
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindMapper.selectByCanteenPrimaryId(canteenPrimaryId);
        if (CollectionUtils.isNotEmpty(bindDOList)) {
            log.info("[WmScDataCleanService.initiateCanteenStallByCanteenId] bindDOList = {}", JSONObject.toJSONString(bindDOList));
            return;
        }

        wmCanteenStallManageService.initiateCanteenStallManageAndBind(canteenId);
    }

    /**
     * 食堂档口管理线索打标一致性监控(兜底任务)
     * @param canteenId 食堂ID
     * @return 更新的线索ID列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> refreshCanteenStallWdcClueLabel(Integer canteenId) throws WmSchCantException, TException {
        log.info("[WmScDataCleanService.refreshCanteenStallWdcClueLabel] input param: canteenId = {}", canteenId);
        if (canteenId == null || canteenId <= CANTEENID_BEGIN) {
            log.error("[WmScDataCleanService.refreshCanteenStallWdcClueLabel] invalid canteenId = {}", canteenId);
            return new ArrayList<>();
        }

        Integer canteenPrimaryId = WmScTransUtil.getCanteenPrimaryIdByCanteenId(canteenId);
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (wmCanteenDB == null) {
            log.info("[WmScDataCleanService.refreshCanteenStallWdcClueLabel] canteen is not exist. canteenId = {}", canteenId);
            return new ArrayList<>();
        }

        return wmCanteenStallClueService.refreshCanteenStallWdcClueLabel(canteenPrimaryId);
    }


    /**
     * 终止食堂信息审批任务(一次性任务)
     * @param canteenId 食堂ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void tempRejectCanteenAuditByCanteenId(Integer canteenId) throws TException, WmSchCantException {
        log.info("[WmScDataCleanService.tempRejectCanteenAuditByCanteenId] input param: canteenId = {}", JSONObject.toJSONString(canteenId));
        Integer canteenPrimaryId = WmScTransUtil.getCanteenPrimaryIdByCanteenId(canteenId);
        // 1-查询食堂信息
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (wmCanteenDB == null) {
            log.info("[WmScDataCleanService.tempRejectCanteenAuditByCanteenId] canteen is not exist. canteenId = {}", canteenId);
            return;
        }

        // 2-是否命中灰度
        Boolean hitGray = wmScSchoolService.isSchoolInCanteenStallAccuracyGrayList(canteenPrimaryId);
        if (hitGray) {
            log.info("[WmScDataCleanService.tempRejectCanteenAuditByCanteenId] canteen hitGray. canteenId = {}", canteenId);
            return;
        }

        // 3-食堂是否在审批中
        if (wmCanteenDB.getAuditStatus().equals((int) CanteenAuditStatusEnum.AUDITING.getType())
                || wmCanteenDB.getAuditDetailStatus().equals((int) CanteenDetailStatusEnum.INSERT_AUDITING_STATUS.getType())
                || wmCanteenDB.getAuditDetailStatus().equals((int) CanteenDetailStatusEnum.UPDATE_AUDITING_STATUS.getType())) {
            log.info("[WmScDataCleanService.tempRejectCanteenAuditByCanteenId] canteen is auditing. canteenId = {}", canteenId);

            WmScCanteenAuditDO canteenAuditDO = wmScCanteenAuditMapper.selectByCanteenId(wmCanteenDB.getId());
            if (canteenAuditDO == null) {
                log.error("[WmScDataCleanService.tempRejectCanteenAuditByCanteenId] canteenAuditDO is null. canteenId = {}", wmCanteenDB.getId());
                return;
            }

            canteenAuditCallback(wmCanteenDB, canteenAuditDO);
        }
    }

    private void canteenAuditCallback(WmCanteenDB wmCanteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) throws TException, WmSchCantException {
        WmScTicketNoticeBo noticeBo = new WmScTicketNoticeBo();
        // 任务系统审批驳回
        noticeBo.setTicketStage(ScConstants.CRM_TICKET_REJECT_STAGE);
        // 1-状态流转
        canteenStatusMachine.auditCallback(noticeBo, wmCanteenDB, wmScCanteenAuditDO);

        // 2-更新食堂信息表
        wmScCanteenSensitiveWordsService.writeWhenInsertOrUpdate(wmCanteenDB);
        wmCanteenMapper.updateCanteen(wmCanteenDB);

        // 3-更新食堂信息审批表
        wmScCanteenAuditDO.setAuditTime((int)(System.currentTimeMillis()/1000));
        wmScCanteenAuditDO.setCtime((int)(System.currentTimeMillis()/1000));
        wmScCanteenAuditDO.setUtime((int)(System.currentTimeMillis()/1000));
        wmScCanteenAuditDO.setUserId(0);
        wmScCanteenAuditDO.setUserName("审核系统");
        wmScCanteenAuditDO.setAuditResult("食堂档口准确性提升初始化");
        wmScCanteenSensitiveWordsService.writeWhenInsertOrUpdate(wmScCanteenAuditDO);
        wmScCanteenAuditMapper.updateByPrimaryKeySelective(wmScCanteenAuditDO);

        auditRejectLog(wmCanteenDB.getId());
    }

    private void auditRejectLog(Integer id){
        String auditLog = "审批中 => 审批驳回\\n驳回原因: " + "食堂档口准确性提升初始化";
        auditLog(id, auditLog);
    }

    private void auditLog(Integer id, String auditLog) {
        wmScLogService.insertScOptLog(OptTypeEnum.CANTEEN_AUDIT_STATUS_CHANGE.getType(), SC_CANTEEN_LOG, id, 0, "任务系统", auditLog, "");
    }

}
