package com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.autoSignCalTask;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmContractAgentInfo;
import com.sankuai.meituan.waimai.customer.util.DateUtils;
import com.sankuai.meituan.waimai.thrift.agent.domain.WmCommercialAgentInfo;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.CustomerPaperContractRemarkBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * C2合同自动创建task
 *
 * Created by lixuepeng on 2021/6/28
 */
public class AutoSignC2CalTask extends AutoSignCalTask {

    public AutoSignC2CalTask(int customerId, List<Long> wmPoiIds, long switchId, int opUid, String opName, List<Integer> existAgentIds, List<Integer> agentIds) {
        super(customerId, wmPoiIds, switchId, opUid, opName, existAgentIds, agentIds);
    }

    @Override
    public boolean preSign() {
        try {
            //基本参数校验
            Preconditions.checkArgument(customerId > 0 && CollectionUtils.isNotEmpty(wmPoiIds) && StringUtils.isNotEmpty(opName),"参数不合法");
            //客户类型校验
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
            Preconditions.checkArgument(wmCustomerDB.getSignMode() == 2, "新客户签约模式不为电子签约");
            //代理商信息校验
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(agentIds) , "门店列表对应无对应的代理商信息");
            //是否已全部签署C2合同
            Preconditions.checkArgument(!existAgentIds.containsAll(agentIds) , "门店对应的代理商合同全都已签署");
            return true;
        } catch (Exception e) {
            logger.error("自动发起C2合同-前置校验失败，customerId:{}, wmPoiIds:{}, opName:{}, 原因:{}", customerId, wmPoiIds, opName, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void fillSignData() throws Exception {
        logger.info("自动发起C2合同-填充数据，customerId:{}, wmPoiIds:{}, opName:{}", customerId, wmPoiIds, opName);
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        WmCustomerKp wmCustomerKp =  wmCustomerKpService.getCustomerSignerKp(customerId);
        for (Integer agentId : agentIds) {
            //只处理未签约
            if (!existAgentIds.contains(agentId)) {
                logger.info("自动发起C2合同-填充数据，需要发起签约的代理商:{}", agentId);
                CollectionUtils.addIgnoreNull(contractBoList, initSaveContractBo(wmCustomerDB, wmCustomerKp, agentId));
            }
        }
    }

    @Override
    public void doSign() {
        try {
            if (CollectionUtils.isEmpty(contractBoList)) {
                return;
            }
            for (WmCustomerContractBo contractBo : contractBoList) {
                resultList.add((long) wmContractService.saveAndStartSign(contractBo, opUid, opName));
            }
        } catch (Exception e) {
            logger.error("AutoSignCalTask doSign contractBoList:{}, opUid:{}, opName:{}", JSON.toJSONString(contractBoList), opUid, opName, e);
        }
    }

    @Override
    public void afterSign() {

    }

    //获取客户下已存在的C2合同
    private List<Integer> existAgentIds() {
        try {
            List<Integer> existAgentIds = new ArrayList<>();
            List<WmCustomerContractBo> wmCustomerContractBos = wmContractService.getContractBoListByCusIdAndType((long) customerId,
                    Lists.newArrayList(WmTempletContractTypeEnum.C2_PAPER.getCode(), WmTempletContractTypeEnum.C2_E.getCode()), opUid, opName);
            if (CollectionUtils.isEmpty(wmCustomerContractBos)) {
                return existAgentIds;
            }
            for (WmCustomerContractBo customerContractBo : wmCustomerContractBos) {
                for (WmTempletContractSignBo signBo : customerContractBo.getSignBoList()) {
                    if ("B".equals(signBo.getSignType())) {
                        existAgentIds.add(signBo.getSignId());
                    }
                }
            }
            return existAgentIds;
        } catch (Exception e) {
            logger.error("获取客户下已存在的C2合同异常 customerId:{}, 异常:{}", customerId, e);
        }
        return new ArrayList<>();
    }

    //获取门店对应的代理商信息
    private List<Integer> agentIds() {
        try {
            return wmContractService.getAgentIdsByWmPoiIds(wmPoiIds);
        } catch (Exception e) {
            logger.error("获取门店对应的代理商信息 wmPoiIds:{}", wmPoiIds, e);
        }
        return new ArrayList<>();
    }

    private WmCustomerContractBo initSaveContractBo(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, Integer agentId) {
        logger.info("自动发起C2合同-填充数据 初始化 customerId:{}, agentId:{}", customerId, agentId);
        try {
            WmCustomerContractBo contractBo = new WmCustomerContractBo();

            //基础Bo
            WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
            basicBo.setContractNum("电子合同保存后自动生成编号");
            basicBo.setDueDate(0); // 长期有效
            CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
            basicBo.setExtStr(JSON.toJSONString(paperContractRemarkBo));
            basicBo.setParentId(customerId);
            basicBo.setType(WmTempletContractTypeEnum.C2_E.getCode());

            //签约甲乙方Bo
            List<WmTempletContractSignBo> signList = Lists.newArrayList();
            //甲方
            WmTempletContractSignBo partyA = new WmTempletContractSignBo();
            partyA.setSignId(customerId);
            partyA.setSignName(wmCustomerDB.getCustomerName());
            partyA.setSignPeople(wmCustomerKp.getCompellation());
            partyA.setSignPhone(wmCustomerKp.getPhoneNum());
            partyA.setSignTime(DateUtils.getNDay(0));
            partyA.setSignType("A");
            signList.add(partyA);
            //乙方
            WmTempletContractSignBo partyB = new WmTempletContractSignBo();
            partyB.setSignId(agentId);
            WmContractAgentInfo agentInfo = wmContractAgentService.queryAgentInfoById(agentId);
            partyB.setSignName(agentInfo.getName());
            partyB.setSignPeople(agentInfo.getLegalPerson());
            partyB.setSignPhone("");
            partyB.setSignTime(DateUtils.getNDay(0));
            partyB.setSignType("B");
            signList.add(partyB);

            //封装
            contractBo.setBasicBo(basicBo);
            contractBo.setSignBoList(signList);
            contractBo.setIgnoreExistAnotherSignTypeContract(true);
            contractBo.setPackWay(SignPackWay.DO_SIGN.getCode());

            return contractBo;
        } catch (Exception e) {
            logger.error("自动发起C2合同-填充数据 初始化异常 customerId:{}, agentId:{}", customerId, agentId, e);
        }
        return null;
    }
}
