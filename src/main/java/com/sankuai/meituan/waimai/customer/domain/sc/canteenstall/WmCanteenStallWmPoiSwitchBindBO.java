package com.sankuai.meituan.waimai.customer.domain.sc.canteenstall;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SwitchTaskReasonTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 食堂门店换绑业务对象
 * 用于描述食堂门店换绑操作所需的信息。
 */
@Data
public class WmCanteenStallWmPoiSwitchBindBO {

    /**
     * 目标食堂ID
     * 门店换绑后要绑定到的目标食堂。
     */
    private Integer canteenIdTo;

    /**
     * 原食堂ID
     * 门店已经绑定的现有食堂。
     */
    private Integer canteenIdFrom;

    /**
     * 需要换绑的门店ID列表
     * 所有需要进行换绑操作的门店ID。
     */
    private List<Long> wmPoiIdList;

    /**
     * 换绑原因
     * 描述门店换绑的原因
     */
    private SwitchTaskReasonTypeEnum changeReason;

    /**
     * 具体换绑原因说明
     * 当换绑原因选择为 'OTHER' 时，需提供的具体说明。
     */
    private String specificChangeReason;

    /**
     * 证明材料的URL
     */
    private String proofMaterialUrl;

    /**
     * 操作用户ID
     */
    private String misId;

    /**
     * 操作用户名
     */
    private String userName;
}
