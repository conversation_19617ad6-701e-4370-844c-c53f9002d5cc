package com.sankuai.meituan.waimai.customer.service.sc.monitor;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScGradeLabelConstant;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiAttributeMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAttributeDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAttributeSearchCondition;
import com.sankuai.meituan.waimai.customer.service.sc.WmScCanteenService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTagService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenGradeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.monitor.MonitorScCanteenPoiTagDTO;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 食堂门店打标监控
 *
 * <AUTHOR>
 * @date 2022年03月01日
 */
@Slf4j
@Service
public class WmScCanteenPoiTagMonitor {

    @Autowired
    private WmScCanteenService WmScCanteenService;

    @Autowired
    private WmScTagService wmScTagService;

    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;

    public String check(MonitorScCanteenPoiTagDTO param) {
        String MSG_PREFIX = "食堂门店打标监控:";
        try {
            log.info("{}check param={}", MSG_PREFIX, JSON.toJSONString(param));
            String result = "";
            Long wmPoiId = param.getWmPoiId();
            Integer canteenPrimaryId = param.getCanteenPrimaryId();
            Integer valid = param.getValid();
            String paramStr = JSON.toJSONString(param);
            if (wmPoiId == null || wmPoiId <= 0) {
                return String.format("%s门店ID不合法，参数=%s", MSG_PREFIX, paramStr);
            }

            if (canteenPrimaryId == null || canteenPrimaryId <= 0) {
                return String.format("%s食堂ID不合法，参数=%s", MSG_PREFIX, paramStr);
            }

            if (valid == null || valid < 0) {
                return String.format("%s valid参数不合法，参数=%s", MSG_PREFIX, paramStr);
            }

            WmCanteenDB wmCanteenDB = WmScCanteenService.getById(canteenPrimaryId, false);
            if (wmCanteenDB == null) {
                return Strings.EMPTY;
            }

            // 食堂门店标签
            List<Long> labelIds = new ArrayList<>();
            labelIds.add(WmScGradeLabelConstant.getLabelA());
            if (CanteenGradeEnum.getByType(wmCanteenDB.getGrade()) != null
                    && WmScGradeLabelConstant.getLabelIdByGrade(wmCanteenDB.getGrade()) != null) {
                labelIds.add(WmScGradeLabelConstant.getLabelIdByGrade(wmCanteenDB.getGrade()));
            }

            WmScCanteenPoiAttributeSearchCondition condition = new WmScCanteenPoiAttributeSearchCondition();
            condition.setWmPoiId(wmPoiId);
            condition.setValid(ValidEnum.VALID.getTypeInt());
            List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDOList = wmScCanteenPoiAttributeMapper.selectByCondition(condition);

            // 门店绑定或换绑食堂
            if (ValidEnum.VALID.getTypeInt() == valid || CollectionUtils.isNotEmpty(wmScCanteenPoiAttributeDOList)) {
                for (Long labelId : labelIds) {
                    if (!wmScTagService.hasTag(wmPoiId, labelId)) {
                        result = String.format("%s门店绑定/换绑到食堂上但未打上食堂标签，门店ID=%s，标签ID=%s，食堂ID=%s",
                                MSG_PREFIX, wmPoiId, labelId, canteenPrimaryId);
                        log.error(result);
                        return result;
                    }
                }
                return Strings.EMPTY;
            }

            // 门店解绑食堂
            for (Long labelId : labelIds) {
                if (wmScTagService.hasTag(wmPoiId, labelId)) {
                    result = String.format("%s门店从食堂上解绑但是未解绑食堂标签，门店ID=%s，标签ID=%s，食堂ID=%s",
                            MSG_PREFIX, wmPoiId, labelId, canteenPrimaryId);
                    log.error(result);
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("{}check param={}", MSG_PREFIX, JSON.toJSONString(param), e);
        }
        return Strings.EMPTY;
    }


}
