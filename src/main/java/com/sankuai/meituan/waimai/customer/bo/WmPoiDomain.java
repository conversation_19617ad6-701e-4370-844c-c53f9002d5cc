package com.sankuai.meituan.waimai.customer.bo;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-8-18.
 */
public class WmPoiDomain {
    private int wmPoiId;

    private int cityId;

    private String name;

    private String address;

    private long latitude;

    private long longitude;

    private long ownerUid;

    private String ownerUname;

    private int valid;

    private int supportPay;

    private short batchAuditCommitted;

    private short agentId;

    private String ecomAccountPhonenum;

    private long aorId;

    private int source;

    private String wmLogisticsIds;

    private int toShopStatus;

    private long brandId;

    private byte brandType;

    private int ownerType;

    private byte isDelete;

    private short ecLabel;

    private Integer customerId;

    private String primaryTagId;

    private int sellerUid;

    /**
     * 二级物理城市ID
     */
    private int cityLocationId;

    /**
     * 三级物理城市ID
     */
    private int locationId;

    /**
     * 标签ID
     */
    private String labelIds;

    public int getWmPoiId() {
        return wmPoiId;
    }

    public void setWmPoiId(int wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public long getLatitude() {
        return latitude;
    }

    public void setLatitude(long latitude) {
        this.latitude = latitude;
    }

    public long getLongitude() {
        return longitude;
    }

    public void setLongitude(long longitude) {
        this.longitude = longitude;
    }

    public long getOwnerUid() {
        return ownerUid;
    }

    public void setOwnerUid(long ownerUid) {
        this.ownerUid = ownerUid;
    }

    public String getOwnerUname() {
        return ownerUname;
    }

    public void setOwnerUname(String ownerUname) {
        this.ownerUname = ownerUname;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public int getSupportPay() {
        return supportPay;
    }

    public void setSupportPay(int supportPay) {
        this.supportPay = supportPay;
    }

    public short getBatchAuditCommitted() {
        return batchAuditCommitted;
    }

    public void setBatchAuditCommitted(short batchAuditCommitted) {
        this.batchAuditCommitted = batchAuditCommitted;
    }

    public short getAgentId() {
        return agentId;
    }

    public void setAgentId(short agentId) {
        this.agentId = agentId;
    }

    public String getEcomAccountPhonenum() {
        return ecomAccountPhonenum;
    }

    public void setEcomAccountPhonenum(String ecomAccountPhonenum) {
        this.ecomAccountPhonenum = ecomAccountPhonenum;
    }

    public long getAorId() {
        return aorId;
    }

    public void setAorId(long aorId) {
        this.aorId = aorId;
    }

    public int getSource() {
        return source;
    }

    public void setSource(int source) {
        this.source = source;
    }


    public String getWmLogisticsIds() {
        return wmLogisticsIds;
    }

    public void setWmLogisticsIds(String wmLogisticsIds) {
        this.wmLogisticsIds = wmLogisticsIds;
    }

    public int getToShopStatus() {
        return toShopStatus;
    }

    public void setToShopStatus(int toShopStatus) {
        this.toShopStatus = toShopStatus;
    }

    public long getBrandId() {
        return brandId;
    }

    public void setBrandId(long brandId) {
        this.brandId = brandId;
    }

    public int getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(int ownerType) {
        this.ownerType = ownerType;
    }

    public byte getBrandType() {
        return brandType;
    }

    public void setBrandType(byte brandType) {
        this.brandType = brandType;
    }

    public byte getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(byte isDelete) {
        this.isDelete = isDelete;
    }

    public short getEcLabel() {
        return ecLabel;
    }

    public void setEcLabel(short ecLabel) {
        this.ecLabel = ecLabel;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getPrimaryTagId() {
        return primaryTagId;
    }

    public void setPrimaryTagId(String primaryTagId) {
        this.primaryTagId = primaryTagId;
    }

    public int getSellerUid() {
        return sellerUid;
    }

    public void setSellerUid(int sellerUid) {
        this.sellerUid = sellerUid;
    }

    public int getCityLocationId() {
        return cityLocationId;
    }

    public void setCityLocationId(int cityLocationId) {
        this.cityLocationId = cityLocationId;
    }

    public int getLocationId() {
        return locationId;
    }

    public void setLocationId(int locationId) {
        this.locationId = locationId;
    }

    public String getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(String labelIds) {
        this.labelIds = labelIds;
    }
}


