package com.sankuai.meituan.waimai.customer.domain;


public class AbstractDtsMessage<D> {
    private String tableName;
    private Long timestamp;
    private Long scn;
    private String type;
    private String sourceIP;
    private D data;
    private String diffMapJson;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Long getScn() {
        return scn;
    }

    public void setScn(Long scn) {
        this.scn = scn;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSourceIP() {
        return sourceIP;
    }

    public void setSourceIP(String sourceIP) {
        this.sourceIP = sourceIP;
    }

    public D getData() {
        return data;
    }

    public void setData(D data) {
        this.data = data;
    }

    public String getDiffMapJson() {
        return diffMapJson;
    }

    public void setDiffMapJson(String diffMapJson) {
        this.diffMapJson = diffMapJson;
    }
}
