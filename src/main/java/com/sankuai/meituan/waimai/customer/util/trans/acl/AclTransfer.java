package com.sankuai.meituan.waimai.customer.util.trans.acl;

import com.sankuai.meituan.scmbrand.thrift.domain.api.BrandQueryResultDTO;
import com.sankuai.meituan.waimai.bizuser.thrift.AcctOrg;
import com.sankuai.meituan.waimai.customer.bo.ShanGouMsgBO;
import com.sankuai.meituan.waimai.customer.bo.account.AcctOrgBO;
import com.sankuai.meituan.waimai.customer.bo.brand.BrandQueryResultBO;
import com.sankuai.sgmerchant.msgmanager.thrift.command.PubMessageByPoiIdListCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * -@author: huang<PERSON><PERSON><PERSON>
 * -@description: Anti-Corruption Layer防腐层 相关转换
 * -@date: 2023/4/24 5:42 PM
 */
@Mapper(componentModel = "spring")
public interface AclTransfer {

    AclTransfer INSTANCE = Mappers.getMapper(AclTransfer.class);

    /***
     * 闪购
     * @param shangouMsgBO
     * @return
     */
    PubMessageByPoiIdListCommand shangouMsgBOToPubMessageCmd(ShanGouMsgBO shangouMsgBO);

    /**
     * 品牌返回值
     *
     * @param brandQueryResultDTO 入参数
     * @return 返回BrandQueryResultBO
     */
    BrandQueryResultBO brandResultDTOToPBrandResultBO(BrandQueryResultDTO brandQueryResultDTO);

    /**
     * 账号转换BO
     *
     * @param acctOrg 入参数
     * @return 返回actOrgBO
     */
    @Mapping(target = "brandId", expression = "java(acctOrg.getBrandId().value)")
    AcctOrgBO acctOrgToAcctOrgBO(AcctOrg acctOrg);

}
