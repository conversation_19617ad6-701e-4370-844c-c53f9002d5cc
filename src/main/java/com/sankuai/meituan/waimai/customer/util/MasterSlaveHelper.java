package com.sankuai.meituan.waimai.customer.util;

import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Field;
import java.util.Properties;

import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dianping.zebra.Constants;
import com.dianping.zebra.util.StringUtils;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import static org.apache.ibatis.reflection.MetaObject.DEFAULT_OBJECT_FACTORY;
import static org.apache.ibatis.reflection.MetaObject.DEFAULT_OBJECT_WRAPPER_FACTORY;

@SuppressWarnings("rawtypes")
@Intercepts(@Signature(type = Executor.class, method = "query", args = { MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class }))
public class MasterSlaveHelper implements Interceptor {

    private static Logger                     logger                   = LoggerFactory.getLogger(MasterSlaveHelper.class);

    String                                    MASTER_QUERY             = "master";

    String                                    SLAVE_QUERY              = "slave";

    String                                    MS_TAG                   = "MS_";
    static Field                              metaParameters;

    private static final ThreadLocal<Boolean> openMasterConnect        = new ThreadLocal<>();
    //是否开启方法区的主库读
    private static final ThreadLocal<Boolean> openMasterConnectOnChain = new ThreadLocal<>();

    static {
        try {
            metaParameters = BoundSql.class.getDeclaredField("metaParameters");
            metaParameters.setAccessible(true);
        } catch (NoSuchFieldException e) {
            logger.error("获取BoundSql的metaParameters属性失败了", e);
        }
    }

    private static class NullObject {
    }

    public static void openMasterConnect() {
        openMasterConnect.set(Boolean.TRUE);
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        //获取拦截方法的参数
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameterObject = args[1];
        RowBounds rowBounds = (RowBounds) args[2];
        ResultHandler resultHandler = (ResultHandler) args[3];
        BoundSql sourceSql = ms.getBoundSql(parameterObject);
        logger.debug("MasterSlaveHelper ms.getId:{}", ms.getId());
        if ((needOpenMaster() || MASTER_QUERY.equalsIgnoreCase(ConfigUtilAdapter.getString(getMsConfigKey(ms), SLAVE_QUERY)))
                && !hasOpenedMaster(sourceSql)) {
            logger.debug("主从切换  切主库读 srcSql：{}", sourceSql.getSql());
            Executor executor = (Executor) invocation.getTarget();
            CacheKey sourceSqlCacheKey = executor.createCacheKey(ms, parameterObject, rowBounds, sourceSql);
            String sqlInMasterQuery = Constants.SQL_FORCE_WRITE_ATLAS_HINT + sourceSql.getSql();
            BoundSql targetSql = new BoundSql(ms.getConfiguration(), sqlInMasterQuery, sourceSql.getParameterMappings(), parameterObject);

            copyMetaParameters(sourceSql, targetSql);

            logger.debug("主从切换  切主库读 targetSql：{}", targetSql.getSql());
            return executor.query(ms, parameterObject, RowBounds.DEFAULT, resultHandler, sourceSqlCacheKey, targetSql);
        }
        return invocation.proceed();
    }

    private boolean hasOpenedMaster(BoundSql sourceSql) {
        return StringUtils.trimToEmpty(sourceSql.getSql()).contains(Constants.SQL_FORCE_WRITE_ATLAS_HINT);
    }

    private Boolean needOpenMaster() {
        return MoreObjects.firstNonNull(openMasterConnect.get(), false) || MoreObjects.firstNonNull(openMasterConnectOnChain.get(), false);
    }

    private String getMsConfigKey(MappedStatement ms) {
        return MS_TAG + ms.getId();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }

    private void copyMetaParameters(BoundSql sourceSql, BoundSql targetSql) throws IllegalAccessException {
        MetaObject metaObject = (MetaObject) metaParameters.get(sourceSql);
        if (metaObject != MetaObject.NULL_META_OBJECT) {
            metaParameters.set(targetSql, metaObject);
        }
    }

    public static <T> T doInMaster(MasterQueryDomain<T> domain) throws WmCustomerException{
        openMasterQueryDomain();
        try {
            return domain.doInMaster();
        } finally {
            closeMasterQueryDomain();
        }
    }

    private static void closeMasterQueryDomain() {
        openMasterConnectOnChain.remove();
    }

    private static void openMasterQueryDomain() {
        openMasterConnectOnChain.set(true);
    }

    public interface MasterQueryDomain<T> {
        /**
         * 方法块查主库
         */
        T doInMaster() throws WmCustomerException;
    }
}
