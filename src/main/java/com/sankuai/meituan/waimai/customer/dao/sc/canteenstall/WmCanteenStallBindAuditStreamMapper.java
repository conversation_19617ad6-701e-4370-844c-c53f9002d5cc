package com.sankuai.meituan.waimai.customer.dao.sc.canteenstall;

import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindAuditStreamDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallManageBindRelDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 食堂档口绑定审批状态流Mapper
 * <AUTHOR>
 * @date 2024/05/28
 * @email <EMAIL>
 **/
@Component
public interface WmCanteenStallBindAuditStreamMapper {

    /**
     * 根据主键ID查询食堂档口绑定审批状态流
     * @param id 主键ID
     * @return 食堂档口绑定审批状态流
     */
    WmCanteenStallBindAuditStreamDO selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 新增食堂档口绑定审批状态流
     * @param wmCanteenStallManageBindRelDO 食堂档口管理绑定关联关系
     * @return 更新行数
     */
    int insertSelective(WmCanteenStallBindAuditStreamDO wmCanteenStallManageBindRelDO);

    /**
     * 根据审批任务ID查询食堂档口绑定审批状态流列表
     * @param auditTaskId 审批任务ID
     * @return List<WmCanteenStallBindAuditStreamDO>
     */
    List<WmCanteenStallBindAuditStreamDO> selectByAuditTaskIdWithAuditing(@Param("auditTaskId") Integer auditTaskId);

    /**
     * 根据审批任务ID查询最新的食堂档口绑定审批状态流
     * @param bindId 档口绑定ID
     * @return WmCanteenStallManageBindRelDO
     */
    WmCanteenStallBindAuditStreamDO selectLatestRecordByBindId(@Param("bindId") Integer bindId);

}