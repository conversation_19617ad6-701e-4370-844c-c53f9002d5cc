package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.aspect.AreaDataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@AreaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE)
public class WmEcontractDeliveryCompanyCustomerLongDistanceAreaDataWrapperService implements IWmEcontractAreaDataWrapperService {

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {

        List<EcontractContentBo> result = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);

        if (deliveryInfoBo == null || deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo() == null
                || StringUtils.isEmpty(deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo().getDeliveryArea())) {
            return result;
        }

        EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBo = JSONObject.parseObject(deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo().getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
        // 根据配送方式封装特别声明文案（企客远距离）
        econtractWmPoiSpAreaBo.setSlaText(WmEcontractConstant.DEFAULT_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE_DOCUMENT);
        List<EcontractWmPoiSelfDeliveryPlanBo> selfDeliveryPlanBoList = econtractWmPoiSpAreaBo.getSelfDeliveryPlanBoList();
        if (CollectionUtils.isEmpty(selfDeliveryPlanBoList)) {
            return result;
        }

        EcontractContentBo econtractContentBo = WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(econtractWmPoiSpAreaBo);
        return Lists.newArrayList(econtractContentBo);
    }
}
