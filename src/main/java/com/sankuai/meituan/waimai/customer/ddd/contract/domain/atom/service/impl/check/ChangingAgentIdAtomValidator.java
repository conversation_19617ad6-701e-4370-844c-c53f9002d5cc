package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check;

import java.util.List;

import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * 校验C2合同合作商修改时，合作商id是不是出现在其他C2合同里（线上和线下同时校验）
 */
@Service
public class ChangingAgentIdAtomValidator implements IContractValidator {

    @Autowired
    WmTempletContractDBMapper wmTempletContractDBMapper;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        WmTempletContractTypeBo typeBo = new WmTempletContractTypeBo(contractBo.getBasicBo().getType());
        if (typeBo.getCooperateMode() != WmTempletContractTypeBo.COOPERATEMODE_C2 || contractBo.getBasicBo().getTempletContractId() <= 0) {
            return true;
        }
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectValidByParentIdAndTypes(
                (long) contractBo.getBasicBo().getParentId(),
                Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(), WmTempletContractTypeEnum.C2_PAPER.getCode()));
        WmTempletContractSignBo modifyPartyBSigner = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyBSignerBo();
        for (WmTempletContractDB db : MoreObjects.firstNonNull(wmTempletContractDBList, Lists.<WmTempletContractDB> newArrayList())) {
            //不校验自己
            if (db.getId() == contractBo.getBasicBo().getTempletContractId()) {
                continue;
            }
            WmContractSignAggre wmContractSignAggre = WmContractSignAggre.Factory.make(db.getId());

            WmTempletContractSignBo partyBSignerAudited = wmContractSignAggre.selectAuditedPartyBSignerBo();
            if (partyBSignerAudited != null && modifyPartyBSigner.getSignId() == partyBSignerAudited.getSignId()
                    && db.getType() == contractBo.getBasicBo().getType()) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,
                        "提交失败，该客户与合作商" + partyBSignerAudited.getSignName() + "已存在" + db.getNumber() + "合同，请勿重复添加");
            }

            WmTempletContractSignBo partyBSignerOffline = wmContractSignAggre.selectPartyBSigner();
            if (partyBSignerOffline != null && modifyPartyBSigner.getSignId() == partyBSignerOffline.getSignId()
                    && db.getType() == contractBo.getBasicBo().getType()) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,
                        "提交失败，该客户与合作商" + partyBSignerOffline.getSignName() + "已存在" + db.getNumber() + "合同，请勿重复添加");
            }
        }
        return true;
    }

}
