package com.sankuai.meituan.waimai.customer.domain.task;

/**
 * <AUTHOR>
 * @date 20230307
 * @desc 客户任务信息对象
 */
public class WmCustomerTask {
    private Integer id;

    private Integer customerId;

    private Integer sceneType;

    private Integer taskType;

    private Integer bizTaskId;

    private Integer bizType;

    private Integer bizId;

    private Integer status;

    private String opSystem;

    private Integer opSource;

    private String opDetailSource;

    private Integer valid;

    private Integer cuid;

    private Integer muid;

    private Integer version;

    private Integer ctime;

    private Integer utime;

    private String extra;

    /**
     * 签约系统任务ID-短信签约使用
     */
    private Integer signTaskId;

    /**
     * 处理说明
     */
    private String processMsg;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Integer getSceneType() {
        return sceneType;
    }

    public void setSceneType(Integer sceneType) {
        this.sceneType = sceneType;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getBizTaskId() {
        return bizTaskId;
    }

    public void setBizTaskId(Integer bizTaskId) {
        this.bizTaskId = bizTaskId;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getBizId() {
        return bizId;
    }

    public void setBizId(Integer bizId) {
        this.bizId = bizId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOpSystem() {
        return opSystem;
    }

    public void setOpSystem(String opSystem) {
        this.opSystem = opSystem;
    }

    public Integer getOpSource() {
        return opSource;
    }

    public void setOpSource(Integer opSource) {
        this.opSource = opSource;
    }

    public String getOpDetailSource() {
        return opDetailSource;
    }

    public void setOpDetailSource(String opDetailSource) {
        this.opDetailSource = opDetailSource;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getCuid() {
        return cuid;
    }

    public void setCuid(Integer cuid) {
        this.cuid = cuid;
    }

    public Integer getMuid() {
        return muid;
    }

    public void setMuid(Integer muid) {
        this.muid = muid;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Integer getSignTaskId() {
        return signTaskId;
    }

    public void setSignTaskId(Integer signTaskId) {
        this.signTaskId = signTaskId;
    }

    public String getProcessMsg() {
        return processMsg;
    }

    public void setProcessMsg(String processMsg) {
        this.processMsg = processMsg;
    }
}