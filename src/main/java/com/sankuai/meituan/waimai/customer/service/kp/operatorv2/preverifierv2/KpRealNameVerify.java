package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.dboperator.KpDBOperateImpl;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.PreAuthResultTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-03 20:15
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class KpRealNameVerify extends KpPreverify {

    private static final Logger LOGGER = LoggerFactory.getLogger(KpRealNameVerify.class);

    @Autowired
    private KpDBOperateImpl kpDBOperate;

    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        String preAuthErrorMsg = null;
        if (insertKp != null) {
            //preAuthErrorMsg = kpDBOperate.preAuthAndErrorMsg(insertKp, uid, uname);
            PreAuthResultBO result = kpDBOperate.preAuthAndErrorMsgNew(insertKp, uid, uname);
            preAuthErrorMsg = (result == null ? null : (result.getResult() == PreAuthResultTypeEnum.FAIL.getType() ? result.getMsg() : null));
            LOGGER.info("预认证结果:{},preAuthErrorMsg={},customerId={},kpId={}", (preAuthErrorMsg == null), preAuthErrorMsg, insertKp.getCustomerId(), insertKp.getId());
        } else if (updateKp != null) {
            //preAuthErrorMsg = kpDBOperate.preAuthAndErrorMsg(updateKp, uid, uname);
            PreAuthResultBO result = kpDBOperate.preAuthAndErrorMsgNew(updateKp, uid, uname);
            preAuthErrorMsg = (result == null ? null : (result.getResult() == PreAuthResultTypeEnum.FAIL.getType() ? result.getMsg() : null));
            LOGGER.info("预认证结果:{},preAuthErrorMsg={},customerId={},kpId={}", (preAuthErrorMsg == null), preAuthErrorMsg, updateKp.getCustomerId(), updateKp.getId());
        } else if (deleteKp != null) {
            //preAuthErrorMsg = kpDBOperate.preAuthAndErrorMsg(deleteKp, uid, uname);
            PreAuthResultBO result = kpDBOperate.preAuthAndErrorMsgNew(deleteKp, uid, uname);
            preAuthErrorMsg = (result == null ? null : (result.getResult() == PreAuthResultTypeEnum.FAIL.getType() ? result.getMsg() : null));
            LOGGER.info("预认证结果:{},preAuthErrorMsg={},customerId={},kpId={}", (preAuthErrorMsg == null), preAuthErrorMsg, deleteKp.getCustomerId(), deleteKp.getId());
        } else {
            ThrowUtil.throwClientError("KP信息为空");
        }
        if (StringUtils.isNotBlank(preAuthErrorMsg)) {
            ThrowUtil.throwClientError("KP实名认证失败");
        }
        return new Object();
    }
}
