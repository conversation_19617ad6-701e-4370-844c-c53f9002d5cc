package com.sankuai.meituan.waimai.customer.adapter;

import java.util.Map;

import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.customer.domain.SmsOperateResult;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.dianping.cms.biz.CMSService;
import com.dianping.cms.dto.CMSResultDTO;
import com.dianping.cms.dto.CMSSendDTO;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileTokenAndEncryptDataTo;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileTokenAndEncryptReqTo;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileTokenAndEncryptResTo;
import com.sankuai.conch.certify.tokenaccess.thrift.TokenAccessThriftService;
import com.sankuai.conch.certify.tokenaccess.util.EncryptUtil;

/**
 * 短信接口迁移 - token加密
 * wiki：https://km.sankuai.com/page/287956997
 */
@Service
public class CMSThriftServiceAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(CMSThriftServiceAdapter.class);

    @Autowired
    private CMSService cmsService;

    @Autowired
    private TokenAccessThriftService.Iface tokenAccessThriftService;

    private static final String SUCCESS_STATUS = "success";

    // 服务方公钥 线下/线上不一致
    private static final String OFFLINE_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC/dH7oGzjR19bGgrJkVGs96S5uf2kX2Owv7MXVO3SMlp2jOz4Shh7ZponHseDl0I6YKR9SPEZ/hBdoz9cToQkNSmbmer96IMGLfAhSONHvoXxfEGsRAxhrY5h7P/T8+xTVJl0GfgBGWKeMm/r3BsRBOTEAKOkE8tbGuoEqWo/2YQIDAQAB";
    // 多个 appkey 可以用同一个 clientId。 customer_server 和 contractmanager_server 共用一个 clientId。公密钥一样。
    private static final long CLIENT_ID = 2343;

    public SmsOperateResult sendMessage(String mobile, String interCode, String smsTemplateId, Map<String, String> pair) throws WmCustomerException {
        LOGGER.info("CMSThriftServiceAdapter#sendMessage mobile = {}, interCode = {}, smsTemplateId = {}, pair = {}", mobile, interCode, smsTemplateId, JSON.toJSONString(pair));

        if (StringUtils.isBlank(mobile) || StringUtils.isBlank(smsTemplateId)) {
            LOGGER.error("发送短信异常 mobile = {}, smsTemplateId = {}", mobile, smsTemplateId);
            throw new WmCustomerException(-1, "发送短信异常");
        }

        GetMobileTokenAndEncryptDataTo mobileTokenAndEncrypt = getMobileTokenAndEncrypt(interCode, mobile);
        SmsOperateResult smsOperateResult = new SmsOperateResult();
        try {

            CMSSendDTO cmsSendDTO = new CMSSendDTO();
            cmsSendDTO.setPair(pair);
            cmsSendDTO.setInterCode(interCode);
            cmsSendDTO.setMobileToken(mobileTokenAndEncrypt.getMobileNoToken());
            cmsSendDTO.setMobileEncrypt(mobileTokenAndEncrypt.getMobileNoEncrypt());
            LOGGER.info("CMSThriftServiceAdapter#sendMessage cmsSendDTO =  {}", JSON.toJSONString(cmsSendDTO));
            CMSResultDTO cmsResultDTO = cmsService.singleSend(Integer.parseInt(smsTemplateId), cmsSendDTO);
            LOGGER.info("CMSThriftServiceAdapter#sendMessage cmsResultDTO =  {}", JSON.toJSONString(cmsResultDTO));
            smsOperateResult.setMsgId(cmsResultDTO.getMsgId());
            smsOperateResult.setCode(cmsResultDTO.getCode());
            smsOperateResult.setDesc(cmsResultDTO.getStatusDesc());
            smsOperateResult.setMobile(mobile);
            return smsOperateResult;
        } catch (Exception e) {
            LOGGER.error("发送短信异常 mobile = {}, interCode = {}, smsTemplateId = {}", mobile, interCode, smsTemplateId, e);
            throw new WmCustomerException(-1, "发送短信异常");
        }
    }

    public SmsOperateResult sendMessageThrowException(String mobile, String interCode, String smsTemplateId, Map<String, String> pair) throws WmCustomerException {
        LOGGER.info("CMSThriftServiceAdapter#sendMessageThrowException mobile = {}, interCode = {}, smsTemplateId = {}, pair = {}", mobile, interCode, smsTemplateId, JSON.toJSONString(pair));

        if (StringUtils.isBlank(mobile) || StringUtils.isBlank(smsTemplateId)) {
            LOGGER.error("发送短信异常 mobile = {}, smsTemplateId = {}", mobile, smsTemplateId);
            throw new WmCustomerException(-1, "发送短信异常");
        }

        GetMobileTokenAndEncryptDataTo mobileTokenAndEncrypt = getMobileTokenAndEncrypt(interCode, mobile);
        SmsOperateResult smsOperateResult = new SmsOperateResult();
        CMSSendDTO cmsSendDTO = new CMSSendDTO();
        cmsSendDTO.setPair(pair);
        cmsSendDTO.setInterCode(interCode);
        cmsSendDTO.setMobileToken(mobileTokenAndEncrypt.getMobileNoToken());
        cmsSendDTO.setMobileEncrypt(mobileTokenAndEncrypt.getMobileNoEncrypt());
        LOGGER.info("CMSThriftServiceAdapter#sendMessageThrowException cmsSendDTO =  {}", JSON.toJSONString(cmsSendDTO));
        CMSResultDTO cmsResultDTO = cmsService.singleSend(Integer.parseInt(smsTemplateId), cmsSendDTO);
        LOGGER.info("CMSThriftServiceAdapter#sendMessageThrowException cmsResultDTO =  {}", JSON.toJSONString(cmsResultDTO));
        if (cmsResultDTO.getCode() != 200) {
            LOGGER.warn("发送短信失败 mobile = {}, interCode = {}, smsTemplateId = {}", mobile, interCode, smsTemplateId);
            throw new WmCustomerException(-1, cmsResultDTO.getStatusDesc());
        }
        smsOperateResult.setMsgId(cmsResultDTO.getMsgId());
        smsOperateResult.setCode(cmsResultDTO.getCode());
        smsOperateResult.setDesc(cmsResultDTO.getStatusDesc());
        smsOperateResult.setMobile(mobile);
        return smsOperateResult;
    }

    private GetMobileTokenAndEncryptDataTo getMobileTokenAndEncrypt(String interCode, String mobile) throws WmCustomerException {
        if (StringUtils.isNotBlank(interCode)) {
            mobile = interCode + "_" + mobile;
        }

        try {
            GetMobileTokenAndEncryptReqTo reqTo = new GetMobileTokenAndEncryptReqTo();
            reqTo.setClientId(CLIENT_ID);
            reqTo.setMobileNoEncrypt(EncryptUtil.encrypt(mobile, MccConfig.getTokenProviderPublicKey(OFFLINE_PUBLIC_KEY)));
            LOGGER.info("CMSThriftServiceAdapter#getMobileTokenAndEncrypt reqTo = {}", JSON.toJSONString(reqTo));
            GetMobileTokenAndEncryptResTo mobileTokenAndEncrypt = tokenAccessThriftService.getMobileTokenAndEncrypt(reqTo);
            LOGGER.info("CMSThriftServiceAdapter#getMobileTokenAndEncrypt mobileTokenAndEncrypt = {}", JSON.toJSONString(mobileTokenAndEncrypt));
            if (!SUCCESS_STATUS.equals(mobileTokenAndEncrypt.getStatus())) {
                throw new WmCustomerException(-1, "获取手机号加密Token失败");
            }
            return mobileTokenAndEncrypt.getData();
        } catch (Exception e) {
            LOGGER.error("获取手机号加密Token失败 mobile = {}", mobile, e);
            throw new WmCustomerException(-1, "获取手机号加密Token失败");
        }
    }
}
