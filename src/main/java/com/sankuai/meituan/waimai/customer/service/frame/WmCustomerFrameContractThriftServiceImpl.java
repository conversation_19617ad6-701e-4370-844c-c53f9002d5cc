package com.sankuai.meituan.waimai.customer.service.frame;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.frame.WmCustomerFrameContractService;
import com.sankuai.meituan.waimai.customer.contract.frame.WmCustomerFrameContractAuthService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.request.ContractSignSubjectInfoQueryRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.response.ContractSignSubjectInfo;
import com.sankuai.meituan.waimai.thrift.customer.service.contract.WmCustomerFrameContractThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/2 17:00
 */
@Service
@Slf4j
public class WmCustomerFrameContractThriftServiceImpl implements WmCustomerFrameContractThriftService {

    @Resource
    private WmCustomerFrameContractService wmCustomerFrameContractService;

    @Resource
    private WmCustomerFrameContractAuthService wmCustomerFrameContractAuthService;


    @Override
    public BaseResponse<List<BusinessGroupInfo>> getAuthBusinessGroupLine(AuthGroupLineQueryRequestDTO authGroupLineQueryRequestDTO) {
        try {
            List<BusinessGroupInfo> businessGroupLineList = wmCustomerFrameContractService.getAuthBusinessGroupLine(authGroupLineQueryRequestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#getAuthBusinessGroupLine, authBusinessGroupLineList: {}", JSON.toJSONString(businessGroupLineList));
            return BaseResponse.success(businessGroupLineList);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#getAuthBusinessGroupLine, warn, e", e);
            return BaseResponse.error(-1, e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#getAuthBusinessGroupLine, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<List<CooperateModeQueryRespDTO>> getAllCooperateMode(CooperateModeQueryRequestDTO cooperateModeQueryRequestDTO) throws WmCustomerException {
        try {
            List<CooperateModeQueryRespDTO> cooperateModeQueryRespDTOs = wmCustomerFrameContractService.getAllCooperateMode(cooperateModeQueryRequestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#getAllCooperateMode, getAllCooperateMode: {}", JSON.toJSONString(cooperateModeQueryRespDTOs));
            return BaseResponse.success(cooperateModeQueryRespDTOs);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#getAllCooperateMode, warn, e", e);
            return BaseResponse.error(-1, e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#getAllCooperateMode, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<List<TaskTypeQueryRespDTO>> getAllTaskType(TaskTypeQueryRequestDTO taskTypeQueryRequestDTO) {
        try {
            List<TaskTypeQueryRespDTO> taskTypeQueryRespDTOs = wmCustomerFrameContractService.getAllTaskType(taskTypeQueryRequestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#getAllTaskType, getAllTaskType: {}", JSON.toJSONString(taskTypeQueryRespDTOs));
            return BaseResponse.success(taskTypeQueryRespDTOs);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#getAllTaskType, warn, e", e);
            return BaseResponse.error(-1, e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#getAllTaskType, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }


    @Override
    public BaseResponse<List<AgentDTO>> queryAgent(AgentQueryRequestDTO agentQueryRequestDTO) {
        try {
            List<AgentDTO> agentDTOs = wmCustomerFrameContractService.queryAgent(agentQueryRequestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#queryAgent, queryAgent: {}", JSON.toJSONString(agentDTOs));
            return BaseResponse.success(agentDTOs);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#queryAgent, warn, e", e);
            return BaseResponse.error(-1, e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#queryAgent, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }

    }

    @Override
    public BaseResponse<DcCustomerInfoPageData> getDcContractCustomerInfo(DcCustomerInfoPageQueryRequestDTO requestDTO) {
        try {
            DcCustomerInfoPageData dcCustomerInfoPageData = wmCustomerFrameContractService.getDcContractCustomerInfo(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#getDcContractCustomerInfo, dcCustomerInfoPageData Size: {}", dcCustomerInfoPageData.getDcContractCustomerVoList().size());
            return BaseResponse.success(dcCustomerInfoPageData);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#getDcContractCustomerInfo, warn, e", e);
            return BaseResponse.error(-1, e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#getDcContractCustomerInfo, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<Boolean> authDc(AuthDcRequestDTO requestDTO) {
        try {
            Boolean isAuth = wmCustomerFrameContractAuthService.authDc(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#authDc, authDc: {}", JSON.toJSONString(isAuth));
            return BaseResponse.success(isAuth);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#authDc, warn, e", e);
            return BaseResponse.error(-1, e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#authDc, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<String> getContractRouteRef(ContractRouteRefRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmCustomerFrameContractThriftServiceImpl#getContractRouteRef, requestDTO: {}", JSON.toJSONString(requestDTO));
            String contractRouteRef = wmCustomerFrameContractService.getContractRouteRef(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#getContractRouteRef, contractRouteRef: {}", contractRouteRef);
            return BaseResponse.success(contractRouteRef);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#getContractRouteRef, warn, e", e);
            return BaseResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#getContractRouteRef, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<List<DcContractAuthInfo>> authDcContract(AuthDcRequestDTO requestDTO) throws WmCustomerException {
        try {
            List<DcContractAuthInfo> dcContractAuthInfos = wmCustomerFrameContractAuthService.authDcContract(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#authDcContract, dcContractAuthInfos: {}", dcContractAuthInfos);
            return BaseResponse.success(dcContractAuthInfos);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#authDcContract, warn, e", e);
            return BaseResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#authDcContract, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<DcCustomerIdQueryRespDTO> queryDcCustomerId(DcCustomerIdQueryDTO requestDTO) throws WmCustomerException {
        try {
            DcCustomerIdQueryRespDTO dcCustomerIdRespDTO = wmCustomerFrameContractService.queryDcCustomerId(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#queryDcCustomerId, queryDcCustomerId: {}", dcCustomerIdRespDTO);
            return BaseResponse.success(dcCustomerIdRespDTO);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#queryDcCustomerId, warn, e", e);
            return BaseResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#queryDcCustomerId, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<QueryUserRoleRespDTO> queryUserRole(QueryUserRoleRequestDTO requestDTO) throws WmCustomerException {
        try {
            QueryUserRoleRespDTO respDTO = wmCustomerFrameContractAuthService.queryUserRole(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#queryUserRole, respDTO: {}", respDTO);
            return BaseResponse.success(respDTO);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#queryUserRole, warn, e", e);
            return BaseResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#queryUserRole, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<String> queryDcC1PdfUrl(DcC1PdfUrlRequestDTO requestDTO) throws WmCustomerException {
        try {
            String pdfUrl = wmCustomerFrameContractService.queryDcC1PdfUrl(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#queryDcC1PdfUrl, respDTO: {}", pdfUrl);
            return BaseResponse.success(pdfUrl);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#queryDcC1PdfUrl, warn, e", e);
            return BaseResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#queryDcC1PdfUrl, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public BaseResponse<DcContractCustomerVo> queryDcCustomerInfo(DcCustomerInfoRequestDTO requestDTO ) throws WmCustomerException {
        try {
            DcContractCustomerVo customerInfoBo = wmCustomerFrameContractService.queryDcCustomerInfo(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#queryDcCustomerInfo, respDTO: {}", customerInfoBo);
            return BaseResponse.success(customerInfoBo);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#queryDcCustomerInfo, warn, e", e);
            return BaseResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#queryDcCustomerInfo, error, e", e);
            return BaseResponse.error(-1, "查询失败");
        }
    }

    @Override
    public boolean hasCreateDcC1ContractAuth(DcContractAuthRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmCustomerFrameContractThriftServiceImpl#hasCreateDcC1ContractAuth, requestDTO: {}", JSON.toJSONString(requestDTO));
            boolean hasAuth = wmCustomerFrameContractService.hasCreateDcC1ContractAuth(requestDTO);
            log.info("WmCustomerFrameContractThriftServiceImpl#hasCreateDcC1ContractAuth, hasAuth: {}", hasAuth);
            return hasAuth;
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#hasCreateDcC1ContractAuth, warn, e", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#hasCreateDcC1ContractAuth, error, e", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "校验异常");
        }
    }

    @Override
    public List<ContractSignSubjectInfo> queryContractSignSubjectInfo(ContractSignSubjectInfoQueryRequestDTO requestDTO) throws WmCustomerException {
        try {
            log.info("WmCustomerFrameContractThriftServiceImpl#queryContractSignSubjectInfo, requestDTO: {}", JSON.toJSONString(requestDTO));
            List<ContractSignSubjectInfo> subjectInfoList = wmCustomerFrameContractService.queryContractSignSubjectInfo(requestDTO.getCustomerId());
            log.info("WmCustomerFrameContractThriftServiceImpl#queryContractSignSubjectInfo, subjectInfoList: {}", JSON.toJSONString(subjectInfoList));
            return subjectInfoList;
        } catch (WmCustomerException e) {
            log.warn("WmCustomerFrameContractThriftServiceImpl#queryContractSignSubjectInfo, warn, e", e);
            throw e;
        } catch (Exception e) {
            log.error("WmCustomerFrameContractThriftServiceImpl#queryContractSignSubjectInfo, error, e", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "查询签约主体信息失败");
        }
    }
}
