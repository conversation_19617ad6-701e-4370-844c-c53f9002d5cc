package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CommitAuditStatusValidator implements IContractValidator {

    @Autowired
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {
        if (contractBo.getBasicBo().getTempletContractId() <= 0) {
            return true;
        }
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper
                .selectByPrimaryKey(contractBo.getBasicBo().getTempletContractId());
        if (wmTempletContractDB != null
                && wmTempletContractDB.getStatus() == CustomerContractStatus.AUDITING.getCode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态为审核中，不可修改。");
        }
        return true;
    }

}
