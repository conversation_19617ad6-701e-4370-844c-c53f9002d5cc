package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY_PERFORMANCE_SERVICE;

/**
 * @description: 闪购2.2 履约服务费协议删除（新快送）
 * @author: zhangyuanhao02
 * @create: 2025/3/17 11:39
 */
@Slf4j
@Service
@PdfDeleter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS)
public class PerformanceServiceSg22NksDelete implements DeliveryPdfDelete {

    @Override
    public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
        if (CollectionUtils.isEmpty(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS.getName()))) {
            Collection<SignTemplateEnum> tabList = tabPdfMap.get(TAB_DELIVERY_PERFORMANCE_SERVICE);
            if (CollectionUtils.isNotEmpty(tabList)) {
                tabList.removeIf(value -> value.equals(SignTemplateEnum.PERFORMANCE_SERVICE_SG_22_NKS));
            }
        }
    }
}
