package com.sankuai.meituan.waimai.customer.settle.dao;

import com.sankuai.meituan.waimai.customer.settle.domain.WmSettlePaperSignAuditDB;
import com.sankuai.meituan.waimai.datasource.multi.annotation.DataSource;
import org.apache.ibatis.annotations.Options;
import org.springframework.stereotype.Component;

@Component
@DataSource("dbContractWrite")
public interface WmSettlePaperSignAuditDBMapper {
    public final static String INSERT_KEYS ="wm_customer_id,op_id,valid,effective,audit_data,audit_status,audit_time,audit_result,ctime,utime";
    public final static String SELECT_KEYS ="id, " + INSERT_KEYS;

    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(WmSettlePaperSignAuditDB wmSettlePaperSignAuditDB);

    @DataSource("dbContractRead")
    WmSettlePaperSignAuditDB selectByCustomerId(Integer id);

    @DataSource("dbContractWrite")
    int updateWmSettlePaperSignAuditDB(WmSettlePaperSignAuditDB wmSettlePaperSignAuditDB);

    @DataSource("dbContractWrite")
    int updateWmSettlePaperSignAuditByCustomerId(WmSettlePaperSignAuditDB wmSettlePaperSignAuditDB);

    @DataSource("dbContractWrite")
    int delete(int id);

}