package com.sankuai.meituan.waimai.customer.service.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDB;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDao;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiAuditStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class WmScCanteenPoiAuditService {

    @Autowired
    private WmScCanteenPoiAuditDao wmScCanteenPoiAuditDao;


    /**
     * 提审记录逻辑删除且审核状态变为待提交
     */
    public void setPoiAuditUnValidAndUnSubmit(WmScCanteenPoiAuditDB wmScCanteenPoiAuditDB) {
        WmScCanteenPoiAuditDB wmScCanteenPoiAuditDBUpdate = new WmScCanteenPoiAuditDB();
        wmScCanteenPoiAuditDBUpdate.setId(wmScCanteenPoiAuditDB.getId());
        wmScCanteenPoiAuditDBUpdate.setValid(ValidEnum.VALID_NO.getValue());
        wmScCanteenPoiAuditDBUpdate.setAuditStatus((int) CanteenPoiAuditStatusEnum.WAITING_SUBMIT.getCode());
        wmScCanteenPoiAuditDao.update(wmScCanteenPoiAuditDBUpdate);
    }
}
