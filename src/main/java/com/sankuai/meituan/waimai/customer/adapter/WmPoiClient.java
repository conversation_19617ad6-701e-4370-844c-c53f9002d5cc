package com.sankuai.meituan.waimai.customer.adapter;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiTransUtil;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchParam;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchResult;
import com.sankuai.meituan.waimai.poisearch.thrift.execption.WmPoiSearchException;
import com.sankuai.meituan.waimai.poisearch.thrift.service.WmPoiSearchThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.domain.OperationDescription;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmPoiServerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiOnOfflineThriftService;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.util.StringUtil;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;

/**
 * Created by lihaowei on 16-8-18.
 */
@Service
public class WmPoiClient {

    private static Logger log = LoggerFactory.getLogger(WmPoiClient.class);

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    @Autowired
    private WmPoiOnOfflineThriftService wmPoiOnOfflineThriftService;

    @Autowired
    private WmPoiSearchThriftService.Iface wmPoiSearchThriftService;

    static final ImmutableSet<String> WM_POI_FIELDS_AGGRE_OLD_WMPOI = ImmutableSet.of(
            WM_POI_FIELD_WM_POI_ID,
            WM_POI_FIELD_CITY_ID,
            WM_POI_FIELD_NAME,
            WM_POI_FIELD_ADDRESS,
            WM_POI_FIELD_ECOM_ACCOUNT_PHONENUM,
            WM_POI_FIELD_AOR_ID,
            WM_POI_FIELD_LATITUDE,
            WM_POI_FIELD_LONGITUDE,
            WM_POI_FIELD_OWNER_UID,
            WM_POI_FIELD_VALID,
            WM_POI_FIELD_IS_DELETE,
            WM_POI_FIELD_SUPPORT_PAY,
            WM_POI_FIELD_BATCH_AUDIT_COMMITTED,
            WM_POI_FIELD_SOURCE,
            WM_POI_FIELD_AGENT_ID,
            WM_POI_FIELD_WM_LOGISTICS_IDS,   //配送方式
            WM_POI_FIELD_SELF_PICKUP_SUPPORT,
            WM_POI_FIELD_BRAND_ID,
            WM_POI_FIELD_BRAND_TYPE,
            WM_POI_FIELD_OWNER_TYPE,
            WM_POI_FIELD_EC_LABEL,
            WM_POI_FIELD_CITY_LOCATION_ID, //二级物理城市ID
            WM_POI_FIELD_LABEL_IDS, // 标签ID
            WM_POI_FIELD_LOCATION_ID  //三级物理城市ID

    );

    /**
     * 分页查询WmPoiDomain
     */
    public List<WmPoiDomain> pageGetWmPoiByWmPoiIdList(List<Long> wmPoiIdList) throws WmCustomerException {
        List<List<Long>> pageList = Lists.partition(wmPoiIdList, 300);
        List<WmPoiDomain> wmPoiDomainList = Lists.newArrayList();
        for (List<Long> page : pageList) {
            wmPoiDomainList.addAll(mgetWmPoiByWmPoiIdList(page));
        }
        return wmPoiDomainList;
    }

    /**
     * 分页查询WmPoiDomain-自定义specificField
     */
    public List<WmPoiDomain> pageGetWmPoiByWmPoiIdList(List<Long> wmPoiIdList,Set<String> specificField) throws WmCustomerException {
        List<List<Long>> pageList = Lists.partition(wmPoiIdList, 300);
        List<WmPoiDomain> wmPoiDomainList = Lists.newArrayList();
        for (List<Long> page : pageList) {
            wmPoiDomainList.addAll(mgetWmPoiByWmPoiIdList(page,specificField));
        }
        return wmPoiDomainList;
    }


    /**
     * 对象转换List => Map
     */
    public static Map<Integer, WmPoiDomain> listToMap(List<WmPoiDomain> wmPoiDomainList) {
        Map<Integer, WmPoiDomain> map = Maps.newHashMap();
        for (WmPoiDomain wmPoiDomain:wmPoiDomainList) {
            map.put(wmPoiDomain.getWmPoiId(), wmPoiDomain);
        }
        return map;
    }

    /**
     * 批量查询商家
     */

    public List<WmPoiDomain> mgetWmPoiByWmPoiIdList(List<Long> wmPoiIdList) throws WmCustomerException {
        List<WmPoiDomain> wmPoiDomainList;
        try {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                    wmPoiIdList, WM_POI_FIELDS_AGGRE_OLD_WMPOI);
            wmPoiDomainList = WmPoiTransUtil.batchTransWmPoiAggre(wmPoiAggreList);
        } catch (TException | WmServerException e) {
            log.error("调用poi服务化接口失败，wmPoiIdList = " + StringUtil.list2String(wmPoiIdList, ","), e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "网络异常");
        }
        return wmPoiDomainList;
    }


    /**
     * 批量查询商家-自定义specificField
     * @param wmPoiIdList
     * @param specificField
     * @return
     * @throws WmCustomerException
     */
    public List<WmPoiDomain> mgetWmPoiByWmPoiIdList(List<Long> wmPoiIdList,Set<String> specificField) throws WmCustomerException {
        List<WmPoiDomain> wmPoiDomainList;
        try {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                    wmPoiIdList, specificField);
            wmPoiDomainList = WmPoiTransUtil.batchTransWmPoiAggre(wmPoiAggreList);
        } catch (TException | WmServerException e) {
            log.error("调用poi服务化接口失败，wmPoiIdList = " + StringUtil.list2String(wmPoiIdList, ","), e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "网络异常");
        }
        return wmPoiDomainList;
    }

    /**
     * 查询单店商家
     */
    public WmPoiDomain getWmPoiById(long wmPoiId) throws WmCustomerException {
        try {
            WmPoiAggre wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(wmPoiId, WM_POI_FIELDS_AGGRE_OLD_WMPOI);
            return WmPoiTransUtil.transWmPoiAggre(wmPoiAggre);
        } catch (TException | WmServerException e) {
            log.error("调用poi服务化查询门店id失败 wmPoiId = " + wmPoiId, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "网络异常");
        }
    }

    /**
     * 查询多店商家
     */
    public List<WmPoiDomain> getWmPoiByIds(List<Long> wmPoiIds)
            throws WmCustomerException {
        try {
            List<List<Long>> partition = Lists.partition(wmPoiIds, 200);
            List<WmPoiDomain> res = Lists.newArrayList();
            for (List<Long> pois : partition) {
                List<WmPoiAggre> wmPoiAggre = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(pois, WM_POI_FIELDS_AGGRE_OLD_WMPOI);
                res.addAll(WmPoiTransUtil.batchTransWmPoiAggre(wmPoiAggre));
            }
            return res;
        } catch (TException | WmServerException e) {
            log.error("调用poi服务化查询门店id失败 wmPoiId = " + wmPoiIds, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "网络异常");
        }
    }


    /**
     * 是否大连锁门店
     * @param wmPoiDomain
     * @return
     */
    public boolean isKAPoi(WmPoiDomain wmPoiDomain) {
        return wmPoiDomain.getBrandId() > 0 && wmPoiDomain.getOwnerType() == 1;
    }

    /**
     * 是否连锁门店
     * @param wmPoiDomain
     * @return
     */
    public boolean isLianSuoPoi(WmPoiDomain wmPoiDomain) {
        String[] ownerTypes = ConfigUtilAdapter.getString("waimai_poi_owner_type_for_contractonline_check", "1,5,6,8,9,10,11,12,13").split(",");
        Set<Integer> ownerTypeSet = Sets.newHashSet();
        for (String type : ownerTypes) {
            if (NumberUtils.isDigits(type)) {
                ownerTypeSet.add(Integer.valueOf(type));
            }
        }
        return  wmPoiDomain.getOwnerType() > 0
                && ownerTypeSet.contains(wmPoiDomain.getOwnerType());
    }

    /**
     * 生意贷门店是否为连锁商家,此处定义的连锁商家包括大连锁、小连锁、闪购连锁的门店归属
     * @param wmPoiDomain
     * @return
     */
    public boolean isKaOrCkaForBusinessLoans(WmPoiDomain wmPoiDomain){
        String[] ownerTypes = ConfigUtilAdapter.getString("waimai_poi_owner_type_for_isKaOrCkaForBusinessLoans", "1,3,5,6,8,9,10,11,12,13,14").split(",");
        Set<Integer> ownerTypeSet = Sets.newHashSet();
        for (String type : ownerTypes) {
            if (NumberUtils.isDigits(type)) {
                ownerTypeSet.add(Integer.valueOf(type));
            }
        }
        return  wmPoiDomain.getOwnerType() > 0
                && ownerTypeSet.contains(wmPoiDomain.getOwnerType());
    }


    /**
     * 根据门店id、门店名称、门店列表查询门店
     * @param name 门店名称
     * @param inputList 查询列表
     * @return 查询到的结果
     */
    public List<Integer> getWmPoiIdByNameOrId(String name, List<Integer> inputList) {
        WmPoiSearchParam wmPoiSearchParam = new WmPoiSearchParam();
        //搜索门店范围内的
        List<Long> searchList = ObjectUtil.intList2LongList(inputList);
        if (CollectionUtils.isEmpty(searchList) && searchList!=null) {
            searchList.add(0L);
        }
        wmPoiSearchParam.setWmPoiIds(searchList);

        //根据poiId、poiName查询
        wmPoiSearchParam.setName(name);
        wmPoiSearchParam.setWithDelete(true);

        List<Integer> wmPoiIdList;
        try {
            WmPoiSearchResult wmPoiSearchResult = wmPoiSearchThriftService.search(wmPoiSearchParam);
            wmPoiIdList = ObjectUtil.longList2IntList(wmPoiSearchResult.getWmPoiIdList());
        } catch (TException | WmPoiSearchException e) {
            log.error("从ES查询门店失败", e);
            return Lists.newArrayList();
        }

        return wmPoiIdList;
    }

    /**
     * 分页根据门店名称/id分页查询门店
     */
    public List<Integer> pageGetWmPoiIdByNameOrId(String name, List<Integer> inputList, int pageNo, int pageSize) {
        WmPoiSearchParam wmPoiSearchParam = new WmPoiSearchParam();
        //搜索门店范围内的
        List<Long> searchList = ObjectUtil.intList2LongList(inputList);
        if (CollectionUtils.isEmpty(searchList) && searchList!=null) {
            searchList.add(0L);
        }
        wmPoiSearchParam.setWmPoiIds(searchList);

        //根据poiId、poiName查询
        wmPoiSearchParam.setName(name);
        wmPoiSearchParam.setPageNo(pageNo).setPageSize(pageSize);
        wmPoiSearchParam.setWithDelete(true);

        log.info("wmPoiSearchParam = {}", JSON.toJSONString(wmPoiSearchParam));
        List<Integer> wmPoiIdList = Lists.newArrayList();
        try {
            WmPoiSearchResult wmPoiSearchResult = wmPoiSearchThriftService.search(wmPoiSearchParam);
            if (CollectionUtils.isNotEmpty(wmPoiSearchResult.getWmPoiIdList())) {
                for (Long wmPoiId : wmPoiSearchResult.getWmPoiIdList()) {
                    wmPoiIdList.add(wmPoiId.intValue());
                }
            }
        } catch (TException | WmPoiSearchException e) {
            log.error("从ES查询门店失败", e);
            return new ArrayList<>();
        }
        log.info("wmPoiIdList = {}", wmPoiIdList);
        return wmPoiIdList;
    }

    public void offlinePoi(int wmPoiId, String reason, Integer opUid, String opUname,long offLineCode) {
        log.info("offlinePoi 下线门店 wmPoiId = {}, reason = {}, opUid = {}, opUname = {}", wmPoiId, reason, opUid, opUname);
        try {
            WmPoiDomain wmPoiDomain = getWmPoiById(wmPoiId);
            if (wmPoiDomain.getValid() == 1) {//valid=1门店上线状态
                OperationDescription wmPoiOffline = new OperationDescription();
                wmPoiOffline.setReason(reason);
                wmPoiOffline.setOpUname(opUname);
                wmPoiOffline.setOpUid(opUid.longValue());
                wmPoiOffline.setOfflineType(2);
                wmPoiOffline.setOfflineCode(offLineCode);
                wmPoiOnOfflineThriftService.setWmPoiOffline(wmPoiId,wmPoiOffline);
            }
        } catch (WmCustomerException | TException | WmPoiServerException e) {
            log.error("下线商家失败 wmPoiId = " + wmPoiId, e);
        }
    }

    /**
     * 获取指定品牌责任人的门店
     *
     * @param cutomerId
     * @param sellerUidList
     * @param pageFrom
     * @param pageSize
     * @return
     */
    public List<Long> getWmPoiIdListByBrandOwner(int cutomerId, List<Integer> sellerUidList, int pageFrom, int pageSize) throws WmCustomerException {
        WmPoiSearchParam wmPoiSearchParam = new WmPoiSearchParam();
        StringBuffer sql = new StringBuffer("select wmPoiId from poi.poi_index_rw where isDelete = 0 and customerId=" + cutomerId);
        if (CollectionUtils.isNotEmpty(sellerUidList)) {
            sql.append("and sellerUid in(" + StringUtils.join(sellerUidList, ",") + ")");
        }
        sql.append(" limit " + pageFrom + "," + pageSize);
        wmPoiSearchParam.setSql(sql.toString()); // 只需要设置SQL参数，所有逻辑在SQL中体现。
        try {
            WmPoiSearchResult wmPoiSearchResult = wmPoiSearchThriftService.search(wmPoiSearchParam);
            log.info("result:" + JSON.toJSONString(wmPoiSearchResult));
            if (wmPoiSearchResult != null && CollectionUtils.isNotEmpty(wmPoiSearchResult.getWmPoiIdList())) {
                return wmPoiSearchResult.getWmPoiIdList();
            } else {
                return Lists.newArrayList();
            }
        } catch (WmPoiSearchException e) {
            log.error("搜索商家信息时发生异常,getWmPoiIdListByBrandOwner sql:{}", sql, e);
            throw new WmCustomerException(500, e.getMsg());
        } catch (Exception e) {
            log.error("搜索商家信息时发生异常,getWmPoiIdListByBrandOwner sql:{}", sql, e);
            throw new WmCustomerException(500, "搜索商家信息时发生异常");
        }
    }

    /**
     * 获取非指定门店责任人的门店
     * @param cutomerId
     * @param ownerUidList
     * @param pageFrom
     * @param pageSize
     * @return
     */
    public List<Long> getWmPoiIdListByNotOwnerUid(int cutomerId, List<Integer> ownerUidList, int pageFrom, int pageSize) throws WmCustomerException {
        WmPoiSearchParam wmPoiSearchParam = new WmPoiSearchParam();
        StringBuffer sql = new StringBuffer("select wmPoiId from poi.poi_index_rw where isDelete = 0 and customerId=" + cutomerId);
        sql.append(" and ownerUid not in(" + StringUtils.join(ownerUidList, ",") + ")");
        sql.append(" limit " + pageFrom + "," + pageSize);
        wmPoiSearchParam.setSql(sql.toString()); // 只需要设置SQL参数，所有逻辑在SQL中体现。
        try {
            WmPoiSearchResult wmPoiSearchResult = wmPoiSearchThriftService.search(wmPoiSearchParam);
            log.info("getWmPoiIdListByNotOwnerUid result:{}" , JSON.toJSONString(wmPoiSearchResult));
            if (wmPoiSearchResult != null) {
                return wmPoiSearchResult.getWmPoiIdList();
            } else {
                return Lists.newArrayList();
            }
        } catch (WmPoiSearchException e) {
            log.error("搜索商家信息时发生异常, getWmPoiIdListByNotOwnerUid sql:{}", sql, e);
            throw new WmCustomerException(500, e.getMsg());
        } catch (Exception e) {
            log.error("搜索商家信息时发生异常,getWmPoiIdListByNotOwnerUid sql:{}", sql, e);
            throw new WmCustomerException(500, "搜索商家信息时发生异常");
        }
    }

    /**
     * 获取指定客户下非指定业务线的门店列表
     * @param customerId
     * @param bizOrgCode
     * @param pageFrom
     * @param pageSize
     * @return
     * @throws WmCustomerException
     */
    public List<Long> getWmPoiIdListByNotBizOrgCode(int customerId, int bizOrgCode, int pageFrom, int pageSize) throws WmCustomerException {
        WmPoiSearchParam wmPoiSearchParam = new WmPoiSearchParam();
        StringBuffer sql = new StringBuffer("select wmPoiId from poi.poi_index_rw where isDelete = 0 and customerId=" + customerId);
                sql.append(" and bizOrgCode!=" + bizOrgCode);
        sql.append(" limit " + pageFrom + "," + pageSize);
        wmPoiSearchParam.setSql(sql.toString()); // 只需要设置SQL参数，所有逻辑在SQL中体现。
        try {
            WmPoiSearchResult wmPoiSearchResult = wmPoiSearchThriftService.search(wmPoiSearchParam);
            log.info("getWmPoiIdListByNotBizOrgCode result:{}" , JSON.toJSONString(wmPoiSearchResult));
            if (wmPoiSearchResult != null) {
                return wmPoiSearchResult.getWmPoiIdList();
            } else {
                return Lists.newArrayList();
            }
        } catch (WmPoiSearchException e) {
            log.error("搜索商家信息时发生异常, getWmPoiIdListByNotBizOrgCode sql:{}", sql, e);
            throw new WmCustomerException(500, e.getMsg());
        } catch (Exception e) {
            log.error("搜索商家信息时发生异常,getWmPoiIdListByNotBizOrgCode sql:{}", sql, e);
            throw new WmCustomerException(500, "搜索商家信息时发生异常");
        }
    }

    /**
     * 获取带目标门店标签的门店ID
     * @param subjectIds
     * @param subjectType
     * @param labelIds
     * @return
     */
    public List<Long> getTargetLablePoiIds(List<Long> subjectIds, int subjectType, List<Integer> labelIds) throws WmCustomerException {
        log.info("获取带目标门店标签的门店id，subjectIds:{}，subjectType:{}，labelId:{}", JSON.toJSONString(subjectIds), subjectType, JSON.toJSONString(labelIds));
        try {
            List<List<Long>> pageList = Lists.partition(subjectIds, 100);
            List<Long> wmPoiIdList = new ArrayList<>();
            for (List<Long> page : pageList) {
                // 获取门店标签
                List<WmPoiAggre> wmPoiAggres = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(page,
                        ImmutableSet.of(WM_POI_FIELD_LABEL_IDS, WM_POI_FIELD_SUB_WM_POI_TYPE));

                if (CollectionUtils.isNotEmpty(wmPoiAggres)) {
                    // 灰度打开 则根据子门店类型判断是否是子门店
                    if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
                        wmPoiAggres.forEach(wmPoiAggre -> {
                            if (!WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(wmPoiAggre.getSub_wm_poi_type())) {
                                wmPoiIdList.add(wmPoiAggre.getWm_poi_id());
                            }
                        });
                    } else {
                        // 获取带目标门店标签的门店
                        wmPoiAggres.forEach(wmPoiAggre -> {
                            if (com.dianping.lion.client.util.StringUtils.isNotEmpty(wmPoiAggre.getLabel_ids())) {
                                List<Integer> tagIds = Arrays.stream(wmPoiAggre.getLabel_ids().split(",")).map(Integer::valueOf).collect(Collectors.toList());
                                tagIds.retainAll(labelIds);
                                if(tagIds.size() > 0) {
                                    wmPoiIdList.add(wmPoiAggre.getWm_poi_id());
                                }
                            }
                        });
                    }
                }
            }
            log.info("获取带目标门店标签的门店id result:{}", JSON.toJSONString(wmPoiIdList));
            return wmPoiIdList;
        } catch (WmServerException e) {
            log.warn("获取带目标门店标签的门店id业务异常，subjectIds:{}，subjectType:{}，labelId:{}", JSON.toJSONString(subjectIds), subjectType, JSON.toJSONString(labelIds) , e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "获取带目标门店标签的门店id业务异常:"+e.getMessage());
        } catch (TException e) {
            log.error("获取带目标门店标签的门店id系统异常, subjectIds:{}，subjectType:{}，labelId:{}", JSON.toJSONString(subjectIds), subjectType, JSON.toJSONString(labelIds) , e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常请稍后重试");
        }
    }

    /**
     * 批量查询上线状态的带主站门店标签的经营门店集合
     * @param wmPoiList
     * @return
     * @throws WmCustomerException
     */
    public List<Long> getOnlineMainPoiIds(List<Long> wmPoiList) throws WmCustomerException {
        log.info("批量查询上线状态的带主站门店标签的经营门店集合, wmPoiList={}", JSON.toJSONString(wmPoiList));
        List<List<Long>> partition = Lists.partition(wmPoiList, 100);
        List<Long> res = new ArrayList<>();
        for (List<Long> part : partition) {
            try {
                List<WmPoiAggre> wmPoiAggres;
                wmPoiAggres = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(part, ImmutableSet.of(WM_POI_FIELD_VALID, WM_POI_FIELD_LABEL_IDS));
                if (wmPoiAggres != null) {
                    wmPoiAggres.forEach(aggre -> {
                        if (StringUtils.isNotBlank(aggre.getLabel_ids())) {
                            List<Integer> tagIds = Arrays.stream(aggre.getLabel_ids().split(",")).map(Integer::valueOf).collect(Collectors.toList());
                            if(aggre.getValid() == WmPoiValidEnum.ONLINE.getValue() && tagIds.contains(MccConfig.getMainPoiTagId())){
                                res.add(aggre.getWm_poi_id());
                            }
                        }
                    });
                }
            } catch (WmServerException e) {
                log.warn("批量查询上线状态的门店集合业务异常, wmPoiList={}", JSON.toJSONString(wmPoiList), e);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "批量查询上线状态的门店集合业务异常:"+e.getMessage());
            } catch (TException e) {
                log.warn("获取带目标门店标签的门店id系统异常, wmPoiList={}", JSON.toJSONString(wmPoiList), e);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常请稍后重试");
            }
        }
        log.info("批量查询上线状态的带主站门店标签的经营门店集合, result={}", JSON.toJSONString(res));
        return res;
    }
}
