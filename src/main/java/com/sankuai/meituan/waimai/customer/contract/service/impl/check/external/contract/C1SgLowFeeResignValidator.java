package com.sankuai.meituan.waimai.customer.contract.service.impl.check.external.contract;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractSignOpSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.shangou.merchant.compliance.thrift.client.SgPoiContractClient;
import com.sankuai.shangou.merchant.compliance.thrift.constant.admit.ContractResignSourceEnum;
import com.sankuai.shangou.merchant.compliance.thrift.dto.OperatorDTO;
import com.sankuai.shangou.merchant.compliance.thrift.param.poiContractReq.CheckResignReq;
import com.sankuai.shangou.merchant.compliance.thrift.param.poiContractReq.HasFlowDataReq;
import com.sankuai.shangou.merchant.compliance.thrift.param.poiContractReq.ResignReq;
import com.sankuai.shangou.merchant.compliance.thrift.result.CheckResignRes;
import com.sankuai.shangou.merchant.compliance.thrift.result.HasFlowDataRes;
import com.sankuai.shangou.merchant.compliance.thrift.result.contract.CheckResignResult;
import com.sankuai.shangou.merchant.compliance.thrift.result.contract.HasFlowDataResult;
import com.sankuai.shangou.merchant.compliance.thrift.result.contract.ResignResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class C1SgLowFeeResignValidator implements IContractValidator {

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Resource
    private SgPoiContractClient sgPoiContractClient;

    private static final List<Integer> C1_CONTRACT_TYPE_LIST = Lists.newArrayList(
            WmTempletContractTypeEnum.C1_E.getCode(),
            WmTempletContractTypeEnum.C1_PAPER.getCode());

    private static final ExecutorServiceTraceWrapper sgRenewExecutorService = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(10,
                    10,
                    0,
                    TimeUnit.MILLISECONDS,
                    new ArrayBlockingQueue<>(1000),
                    new ThreadFactoryBuilder().setNameFormat("renew-sg-fee").build(),
                    new ThreadPoolExecutor.DiscardPolicy())
    );

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        log.info("C1SgLowFeeResignValidator#valid, contractBo: {}, opUid: {}, opName: {}", JSON.toJSONString(contractBo), opUid, opName);
        if (!MccConfig.resignSgLowFee()) {
            return true;
        }
        if (!C1_CONTRACT_TYPE_LIST.contains(contractBo.getBasicBo().getType())) {
            return true;
        }

        EcontractSignOpSourceEnum opSource = contractBo.getOpSource();
        // 兼容历史逻辑 闪购散店修改C1合同不触发低费率校验
        if (opSource == null || opSource == EcontractSignOpSourceEnum.FLASH_CONTRACT_SYSTEM) {
            return true;
        }

        WmCustomerBasicBo customerBasicBo = wmCustomerService.getCustomerById(contractBo.getBasicBo().getParentId());
        Integer bizOrgCode = customerBasicBo.getBizOrgCode();
        if (Objects.nonNull(bizOrgCode) && bizOrgCode != CustomerBizOrgEnum.SHAN_GOU.getCode()) {
            return true;
        }

        // 是否有流程中数据
        HasFlowDataResult hasFlowDataResult = sgPoiContractClient.hasFlowData(buildHasFlowDataReq(ContractResignSourceEnum.C1_DUE_RENEW, customerBasicBo.getId()));
        if (!hasFlowDataResult.isSuccess()) {
            log.error("查询流程中数据失败，msg: {}", hasFlowDataResult.getMessage());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询流程中数据失败");
        }
        log.info("C1SgLowFeeResignValidator#valid, hasFlowData, hasFlowDataResult: {}, opUid: {}, opName: {}", JSON.toJSONString(hasFlowDataResult), opUid, opName);
        List<HasFlowDataRes> inFlowData = hasFlowDataResult.getData().stream().filter(HasFlowDataRes::getHasFlowData).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(inFlowData)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_SG_POI_IN_PROCESS, JSON.toJSONString(inFlowData));
        }

        // 闪购侧接口-是否有门店需要换签
        CheckResignResult checkResignResult = sgPoiContractClient.checkNeedResign(buildCheckResignReq(ContractResignSourceEnum.C1_DUE_RENEW, customerBasicBo.getId(), buildOperatorDTO(opUid, opName)));
        if (!checkResignResult.isSuccess()) {
            log.error("查询是否有门店需要换签失败，msg: {}", hasFlowDataResult.getMessage());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询是否有门店需要换签失败");
        }
        log.info("C1SgLowFeeResignValidator#valid, checkNeedResign, checkResignResult: {}, opUid: {}, opName: {}", JSON.toJSONString(checkResignResult), opUid, opName);
        List<CheckResignRes> checkResignResList = checkResignResult.getCheckResignResList();
        List<CheckResignRes> needRedignPoiList = checkResignResList.stream().filter(CheckResignRes::getNeedResign).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needRedignPoiList)) {
            return true;
        }


        if (Objects.nonNull(opSource) && opSource.getCode() == EcontractSignOpSourceEnum.MULTI_MODIFY_PLATFORM.getCode()) {
            // 闪购侧接口-换签费率
            sgRenewExecutorService.execute(() -> reSignSgFee(customerBasicBo.getId()));
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_SG_LOW_FEE_C1_RENEW, String.format("该客户下有%s个门店要更新费率后才能续签C1，当前系统已自动发起费率更新，待客户签约后，再次发起C1合同续签", needRedignPoiList.size()));
        } else {
            // 透传闪购侧返回结果
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_SG_LOW_FEE_C1_RENEW, JSON.toJSONString(needRedignPoiList));
        }
    }

    private void reSignSgFee(Integer customerId) {
        try {
            ResignReq resignReq = buildResignReq(ContractResignSourceEnum.C1_DUE_RENEW, customerId);
            log.info("C1SgLowFeeResignValidator#reSignSgFee, resignReq: {}", JSON.toJSONString(resignReq));
            ResignResult resignResult = sgPoiContractClient.resign(resignReq);
            log.info("C1SgLowFeeResignValidator#reSignSgFee, resignResult: {}", JSON.toJSONString(resignResult));
        } catch (Exception e) {
            log.error("C1SgLowFeeResignValidator#reSignSgFee, error", e);
        }
    }

    private OperatorDTO buildOperatorDTO(int opUid, String opName) {
        String mis = "";
        try {
            WmEmploy employ = wmScEmployAdaptor.getWmEmployByUid(opUid);
            mis = employ.getMisId();
        } catch (WmSchCantException e) {
            log.warn("未查询到mis号, opUid = {}", opUid, e);
        }
        return OperatorDTO.builder()
                .opId((long) opUid)
                .mis(mis)
                .opName(opName)
                .build();
    }

    private HasFlowDataReq buildHasFlowDataReq(ContractResignSourceEnum contractResignSourceEnum, int customerId) {
        return HasFlowDataReq.builder()
                .sourceId(contractResignSourceEnum)
                .customerId(customerId)
                .build();
    }

    private CheckResignReq buildCheckResignReq(ContractResignSourceEnum contractResignSourceEnum, int customerId, OperatorDTO operator) {
        return CheckResignReq.builder()
                .sourceId(contractResignSourceEnum)
                .customerId(customerId)
                .operator(operator)
                .build();
    }

    private ResignReq buildResignReq(ContractResignSourceEnum contractResignSourceEnum, int customerId) {
        return ResignReq.builder()
                .sourceId(contractResignSourceEnum)
                .customerId(customerId)
                .build();
    }
}
