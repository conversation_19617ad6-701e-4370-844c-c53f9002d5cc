package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.dservice.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.aspect.RepeatSubmission;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.atom.service.WmSettleCommonAtomService;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleAuditContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleESignContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.WmSettleInputContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.factory.WmSettleAuditContextFactory;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.factory.WmSettleESignContextFactory;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.aggre.factory.WmSettleInputContextFactory;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.impl.WmSettleInputAuditAtomServiceImpl;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.impl.WmSettleInputCheckAtomServiceImpl;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.impl.WmSettleInputESignAtomServiceImpl;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.atom.service.impl.WmSettleInputSaveAtomServiceImpl;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.dservice.WmSettleInputCommitDomainService;
import com.sankuai.meituan.waimai.customer.settle.grey.WmHeronSettleGrayService;
import com.sankuai.meituan.waimai.customer.settle.grey.WmSettleGreyService;
import com.sankuai.meituan.waimai.heron.settle.constants.CBizTypeEnum;
import com.sankuai.meituan.waimai.heron.settle.constants.CChannelTypeEnum;
import com.sankuai.meituan.waimai.heron.settle.dto.WmSettleCommitAuditInputDTO;
import com.sankuai.meituan.waimai.heron.settle.dto.WmSettleCommitSignInputDTO;
import com.sankuai.meituan.waimai.heron.settle.dto.WmSettleProtocolInputDTO;
import com.sankuai.meituan.waimai.heron.settle.dto.base.BaseOperateDataInputDTO;
import com.sankuai.meituan.waimai.heron.settle.dto.base.BooleanResultDTO;
import com.sankuai.meituan.waimai.heron.settle.exception.WmHeronSettleException;
import com.sankuai.meituan.waimai.heron.settle.service.WmSettleExportThriftService;
import com.sankuai.meituan.waimai.kv.groupm.service.BaseKvService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmSettleConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleModifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WmSettleInputCommitDomainServiceImpl implements WmSettleInputCommitDomainService {

    private Executor executor = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(5, 10, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(100)));

    private static String AYS_COMMIT_TAIR_KEY = "settle_ays_commit_wmcustomerid_{wmCustomerId}";

    @Autowired
    private WmSettleInputContextFactory       inputContextFactory;
    @Autowired
    private WmSettleAuditContextFactory       auditContextFactory;
    @Autowired
    private WmSettleESignContextFactory       eSignContextFactory;
    @Autowired
    private WmSettleInputCheckAtomServiceImpl wmSettleInputCheckAtomService;
    @Autowired
    private WmSettleInputAuditAtomServiceImpl wmSettleInputAuditAtomService;
    @Autowired
    private WmSettleInputSaveAtomServiceImpl  wmSettleInputSaveAtomService;
    @Autowired
    private WmSettleInputESignAtomServiceImpl wmSettleInputESignAtomService;
    @Autowired
    private WmEcontractSignBzService          wmEcontractSignBzService;
    @Autowired
    private WmSettleCommonAtomService         wmSettleCommonAtomService;
    @Autowired
    private WmHeronSettleGrayService          wmHeronSettleGrayService;
    @Autowired
    private WmSettleExportThriftService       wmSettleExportThriftService;
    @Autowired
    private WmSettleGreyService wmSettleGreyService;
    @Autowired
    private WmCustomerService wmCustomerService;
    @Autowired
    private WmEmployeeService wmEmployeeService;
    @Autowired
    @Qualifier("mBaseKvService")
    private BaseKvService baseKvService;

    @Override
    public BooleanResult commitAuditWmSettle(int wmCustomerId, String wmCustomerName, int opUid, String opUname, String supplementalUrl,
                                             String qdbUrl,boolean supportAsyCommit)
            throws WmCustomerException {
        WmSettleInputContext inputContext = inputContextFactory.makeContext(wmCustomerId, wmCustomerName, opUid, opUname, supplementalUrl, qdbUrl);
        wmSettleInputCheckAtomService.checkCommitSettleNotNull(inputContext);

        if (wmHeronSettleGrayService.routHeronSettleGray(wmCustomerId, opUid)) {
            log.info("routHeronSettleGray  纸质结算提审 wmCustomerId:[{}],opUid:[{}],opUname:[{}]", wmCustomerId, opUid, opUname);

            BaseOperateDataInputDTO baseOperateDataInputDTO = new BaseOperateDataInputDTO();
            baseOperateDataInputDTO.setOpId(opUid);
            baseOperateDataInputDTO.setOpName(opUname);
            baseOperateDataInputDTO.setChannel(CChannelTypeEnum.CHANNEL_TYPE_WAIMAI.getValue());
            baseOperateDataInputDTO.setCBizTypeEnumList(Lists.newArrayList(CBizTypeEnum.CUSTOMER_SWITCH));

            WmCustomerDB wmCustomerDB = inputContext.getWmCustomerDB();

            try {
                WmSettleModifyBo wmSettleModifyBo = inputContext.getWmSettleModifyBo();

                //保存协议
                WmSettleProtocolInputDTO wmSettleProtocolInputDTO = new WmSettleProtocolInputDTO();
                wmSettleProtocolInputDTO.setQdbUrl(wmSettleModifyBo.getQdbUrl());
                wmSettleProtocolInputDTO.setSupplementalUrl(wmSettleModifyBo.getSupplementalUrl());
                wmSettleProtocolInputDTO.setWmCustomerId(wmCustomerId);

                BooleanResultDTO protocolBooleanResultDTO = wmSettleExportThriftService.saveOrUpdateWmSettleProtocol(wmSettleProtocolInputDTO,
                        baseOperateDataInputDTO);

                log.info("routHeronSettleGray commitAuditWmSettle 保存协议成功   protocolBooleanResultDTO:[{}]",
                        JSON.toJSONString(protocolBooleanResultDTO));

                WmSettleCommitAuditInputDTO wmSettleCommitAuditInputDto = new WmSettleCommitAuditInputDTO();
                wmSettleCommitAuditInputDto.setWmCustomerId(wmCustomerId);
                wmSettleCommitAuditInputDto.setWmCustomerName(wmCustomerDB.getCustomerName());

                List<WmSettle> wmSettleList = inputContext.getWmSettleList();
                List<Integer> wmPoiIdListForAsyCommit = Lists.newArrayList();
                for(WmSettle temp : wmSettleList){
                    wmPoiIdListForAsyCommit.addAll(temp.getWmPoiIdList());
                }

                if(supportAsyCommit && wmSettleGreyService.routeAsyCommitGrey(wmCustomerId,wmPoiIdListForAsyCommit.size())){
                    commitAuditWmSettleAsy(wmSettleCommitAuditInputDto, baseOperateDataInputDTO);
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_SETTLE_ASY_COMMIT, "操作成功，若保存成功会进入审核/签约流程，若保存失败将大象消息通知");
                }

                BooleanResultDTO booleanResultDTO = wmSettleExportThriftService.commitAuditWmSettle(wmSettleCommitAuditInputDto,
                        baseOperateDataInputDTO);

                log.info("routHeronSettleGray commitAuditWmSettle 提审成功   protocolBooleanResultDTO:[{}]", JSON.toJSONString(protocolBooleanResultDTO));

                return new BooleanResult(booleanResultDTO.isRes());
            } catch (WmHeronSettleException e) {
                log.error("routHeronSettleGray  纸质结算提审失败", e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMessage());
            }catch (TException e){
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "纸质签约提审失败");
            }

        } else {

            wmSettleInputCheckAtomService.checkCommitSettleNotNull(inputContext);
            wmSettleInputCheckAtomService.checkCommitStatus(inputContext);
            wmSettleInputCheckAtomService.checkWmPoiOnlineStatus(inputContext);
            wmSettleInputCheckAtomService.checkWmPoiBelong(inputContext);
            wmSettleInputCheckAtomService.checkPaperSettle(inputContext);
            wmSettleInputCheckAtomService.checkBankCardType(inputContext);
            wmSettleInputCheckAtomService.checkSettleCanEditInCustomerSwitch(inputContext);
            wmSettleInputSaveAtomService.saveOrUpdateWmSettleProtocol(inputContext);

            WmSettleAuditContext auditContext = auditContextFactory.makeContext(wmCustomerId, wmCustomerName, opUid, opUname, supplementalUrl,
                    qdbUrl);

            wmSettleInputAuditAtomService.saveWmSettlePaperSignAuditDB(auditContext);
            wmSettleInputAuditAtomService.buildWmAuditSettleCommitData(auditContext);
            validateCardByCustomerId(wmCustomerId);
            wmSettleInputAuditAtomService.commitAudit(auditContext);
            wmSettleCommonAtomService.updateStatusByWmCustomerIdAndNoticeSwitchCentre(wmCustomerId, WmSettleConstant.SETTLE_STATUS_TO_AUDIT);
        }
        return new BooleanResult(true);
    }

    @Override
    @RepeatSubmission(seedExp = "wmCustomerId", needWait = false, seedPrefix = "waimaicustomer_settle_commitWmSettleWithPackWay", expire = 600)
    public BooleanResult commitWmSettleWithPackWay(int wmCustomerId, int signPackWay, int opUid, String opUname, boolean supportAsyCommit) throws WmCustomerException {
        WmSettleInputContext inputContext = new WmSettleInputContext();

        if(ConfigUtilAdapter.getBoolean("commitWmSettleWithPackWay_reduceUnnecessaryQuery_open",true)){
            inputContext.setWmCustomerId(wmCustomerId);
            wmSettleInputCheckAtomService.checkCommitSettleNotNullAndStatus(inputContext);
        }else{
            inputContext = inputContextFactory.makeContext(wmCustomerId, signPackWay, opUid, opUname);
            wmSettleInputCheckAtomService.checkCommitSettleNotNull(inputContext);
            wmSettleInputCheckAtomService.checkCommitStatus(inputContext);
        }

        if (wmHeronSettleGrayService.routHeronSettleGray(wmCustomerId, opUid)) {
            log.info("routHeronSettleGray commitWmSettleWithPackWay wmCustomerId:[{}],opUid:[{}],opUname:[{}]", wmCustomerId, opUid, opUname);
            commitSettleInfo(wmCustomerId, signPackWay, opUid, opUname, 0,supportAsyCommit);

        } else {
            wmSettleInputCheckAtomService.checkWmPoiOnlineStatus(inputContext);
            wmSettleInputCheckAtomService.checkSettleCanEditInCustomerSwitch(inputContext);
            commitSettleInfo(wmCustomerId, signPackWay, opUid, opUname, 0,false);
        }
        return new BooleanResult(true);
    }

    @Override
    public void commitSettleInfo(int wmCustomerId, int signPackWay, int opUid, String opUname, long manualBatchId,boolean supportAsyCommit) throws WmCustomerException {

        if (wmHeronSettleGrayService.routHeronSettleGray(wmCustomerId, opUid)) {
            log.info("routHeronSettleGray commitSettleInfo wmCustomerId:[{}],opUid:[{}],opUname:[{}]", wmCustomerId, opUid, opUname);
            WmSettleESignContext eSignContext = eSignContextFactory.makeContext(wmCustomerId, signPackWay, opUid, opUname, manualBatchId,
                    Lists.newArrayList());
            ManualTaskApplyBo manualTaskApplyBo = eSignContext.getManualTaskApplyBo();

            BaseOperateDataInputDTO baseOperateDataInputDTO = new BaseOperateDataInputDTO();
            baseOperateDataInputDTO.setOpId(opUid);
            baseOperateDataInputDTO.setOpName(opUname);
            baseOperateDataInputDTO.setChannel(CChannelTypeEnum.CHANNEL_TYPE_WAIMAI.getValue());
            baseOperateDataInputDTO.setStatusCompatible(true);
            baseOperateDataInputDTO.setIndependentCheckStatus(true);
            baseOperateDataInputDTO.setCBizTypeEnumList(Lists.newArrayList(CBizTypeEnum.CUSTOMER_SWITCH));

            if (eSignContext.isNotChoosePackWay()) {
                BooleanResult canApplyManualTask = wmEcontractSignBzService.canApplyManualTask(manualTaskApplyBo);
                //判断是否可发起手动打包，如果可以的话就提示BD
                if (canApplyManualTask.isRes()) {
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_PACKWAY_CHOOSE, "");
                }
            }

            WmSettleInputContext wmSettleInputContextForAsyCommit = inputContextFactory.makeContextForAsyCommit(wmCustomerId);
            List<WmSettle> wmSettleListForAsyCommit = wmSettleInputContextForAsyCommit.getWmSettleList();
            List<Integer> wmPoiIdListForAsyCommit = Lists.newArrayList();
            for(WmSettle temp : wmSettleListForAsyCommit){
                wmPoiIdListForAsyCommit.addAll(temp.getWmPoiIdList());
            }

            if (eSignContext.isWaitHandPack()) {
                log.info("routHeronSettleGray commitSettleInfo 发起手动打包 wmCustomerId:[{}],opUid:[{}],opUname:[{}]", wmCustomerId, opUid, opUname);
                try {
                    WmSettleCommitSignInputDTO wmSettleCommitSignInputDTO = new WmSettleCommitSignInputDTO();
                    wmSettleCommitSignInputDTO.setWmCustomerId(wmCustomerId);
                    if(supportAsyCommit && wmSettleGreyService.routeAsyCommitGrey(wmCustomerId,wmPoiIdListForAsyCommit.size())){
                        applySignPackTaskWmSettleAsy(wmSettleCommitSignInputDTO, baseOperateDataInputDTO);
                        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_SETTLE_ASY_COMMIT, "操作成功，若保存成功会进入审核/签约流程，若保存失败将大象消息通知");
                    }else{
                        wmSettleExportThriftService.applySignPackTaskWmSettle(wmSettleCommitSignInputDTO, baseOperateDataInputDTO);
                    }
                    return;
                } catch (WmHeronSettleException  e) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMessage());
                }catch (TException e){
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "发起手动打包失败");
                }

            }

            try {
                WmSettleCommitSignInputDTO wmSettleCommitSignInputDTO = new WmSettleCommitSignInputDTO();
                wmSettleCommitSignInputDTO.setWmCustomerId(wmCustomerId);
                wmSettleCommitSignInputDTO.setManualBatchId(manualBatchId);
                if(supportAsyCommit && wmSettleGreyService.routeAsyCommitGrey(wmCustomerId,wmPoiIdListForAsyCommit.size())){
                    commitSignWmSettleAsy(wmSettleCommitSignInputDTO, baseOperateDataInputDTO);
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_SETTLE_ASY_COMMIT, "操作成功，若保存成功会进入审核/签约流程，若保存失败将大象消息通知");
                }
                wmSettleExportThriftService.commitSignWmSettle(wmSettleCommitSignInputDTO, baseOperateDataInputDTO);
            } catch (WmHeronSettleException e) {
                log.warn("routHeronSettleGray commitSettleInfo 发起电子签约异常", e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMessage());
            }
            catch (TException e){
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "提交电子签约失败");
            }

        } else {
            WmSettleInputContext inputContext = inputContextFactory.makeContext(wmCustomerId, signPackWay, opUid, opUname);
            wmSettleInputCheckAtomService.checkCommit(inputContext);
            List<Long> wmPoiIdList = inputContext.getWmPoiIdList();

            WmSettleESignContext eSignContext = eSignContextFactory.makeContext(wmCustomerId, signPackWay, opUid, opUname, manualBatchId,
                    wmPoiIdList);
            ManualTaskApplyBo manualTaskApplyBo = eSignContext.getManualTaskApplyBo();

            if (eSignContext.isNotChoosePackWay()) {
                BooleanResult canApplyManualTask = wmEcontractSignBzService.canApplyManualTask(manualTaskApplyBo);
                //判断是否可发起手动打包，如果可以的话就提示BD
                if (canApplyManualTask.isRes()) {
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN_PACKWAY_CHOOSE, "");
                }
            }
            if (eSignContext.isWaitHandPack()) {
                wmSettleInputESignAtomService.startSignForHandPack(eSignContext);
                wmSettleCommonAtomService.updateStatusByWmCustomerIdAndNoticeSwitchCentre(wmCustomerId, WmSettleConstant.SETTLE_STATUS_WAIT_SIGN);
                return;
            }
            wmSettleCommonAtomService.updateStatusByWmCustomerIdAndNoticeSwitchCentre(wmCustomerId, WmSettleConstant.SETTLE_STATUS_TO_CONFIRM);
            validateCardByCustomerId(wmCustomerId);
            wmSettleInputESignAtomService.applyWmSettleConfirmFlow(eSignContext);
        }

    }

    @Override
    public void tryCommitManualTask(int wmCustomerId) throws WmCustomerException {
        WmSettleInputContext inputContext = inputContextFactory.makeContext(wmCustomerId);
        wmSettleInputCheckAtomService.checkCommit(inputContext);
    }

    @Override
    @RepeatSubmission(seedExp = "wmCustomerId", needWait = false, seedPrefix = "waimaicustomer_settle_commitWmSettle", expire = 600)
    public void commitWmSettle(int wmCustomerId, int opUid, String opUname) throws WmCustomerException {
        commitWmSettleWithPackWay(wmCustomerId, SignPackWay.DO_SIGN.getCode(), opUid, opUname,false);
    }

    private void validateCardByCustomerId(int wmCustomerId) {
        try {
            wmSettleCommonAtomService.validateCardByCustomerId(wmCustomerId);
        } catch (Exception e) {
            log.error("validateCardByCustomerId异常", e);
        }
    }

    private void applySignPackTaskWmSettleAsy(WmSettleCommitSignInputDTO wmSettleCommitSignInputDTO, BaseOperateDataInputDTO baseOperateDataInputDTO)
            throws WmCustomerException {
        String key = AYS_COMMIT_TAIR_KEY.replace("{wmCustomerId}",wmSettleCommitSignInputDTO.getWmCustomerId().toString());
        if (baseKvService.get(key) != null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "操作失败，有保存中的结算，请稍后操作");
        }
        executor.execute(() -> {
            String errorMsg = "";
            try {
                baseKvService.set(key, String.valueOf(wmSettleCommitSignInputDTO.getWmCustomerId()), 43_200_000);
                wmSettleExportThriftService.applySignPackTaskWmSettle(wmSettleCommitSignInputDTO,
                        baseOperateDataInputDTO);
            } catch (Exception e) {
                errorMsg = getErrorMsg(wmSettleCommitSignInputDTO.getWmCustomerId(),e);
            } finally {
                baseKvService.del(key);
            }
            if (StringUtils.isNotBlank(errorMsg)) {
                sendMsgToCusOwner(wmSettleCommitSignInputDTO.getWmCustomerId(), baseOperateDataInputDTO.getOpId(), errorMsg);
            }
        });
    }

    private void commitSignWmSettleAsy(WmSettleCommitSignInputDTO wmSettleCommitSignInputDTO,
            BaseOperateDataInputDTO baseOperateDataInputDTO) throws WmCustomerException{
        String key = AYS_COMMIT_TAIR_KEY.replace("{wmCustomerId}",wmSettleCommitSignInputDTO.getWmCustomerId().toString());
        if (baseKvService.get(key) != null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "操作失败，有保存中的结算，请稍后操作");
        }
        executor.execute(() -> {
            String errorMsg = "";
            try {
                baseKvService.set(key, String.valueOf(wmSettleCommitSignInputDTO.getWmCustomerId()), 43_200_000);
                wmSettleExportThriftService.commitSignWmSettle(wmSettleCommitSignInputDTO, baseOperateDataInputDTO);
            } catch (Exception e) {
                errorMsg = getErrorMsg(wmSettleCommitSignInputDTO.getWmCustomerId(),e);
            } finally {
                baseKvService.del(key);
            }
            if (StringUtils.isNotBlank(errorMsg)) {
                sendMsgToCusOwner(wmSettleCommitSignInputDTO.getWmCustomerId(), baseOperateDataInputDTO.getOpId(), errorMsg);
            }
        });
    }

    private void commitAuditWmSettleAsy(WmSettleCommitAuditInputDTO wmSettleCommitAuditInputDto,
            BaseOperateDataInputDTO baseOperateDataInputDTO) throws WmCustomerException{
        String key = AYS_COMMIT_TAIR_KEY.replace("{wmCustomerId}",wmSettleCommitAuditInputDto.getWmCustomerId().toString());
        if (baseKvService.get(key) != null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "操作失败，有保存中的结算，请稍后操作");
        }
        executor.execute(() -> {
            String errorMsg = "";
            try {
                baseKvService.set(key, String.valueOf(wmSettleCommitAuditInputDto.getWmCustomerId()), 43_200_000);
                wmSettleExportThriftService.commitAuditWmSettle(wmSettleCommitAuditInputDto,
                        baseOperateDataInputDTO);
            } catch (Exception e) {
                errorMsg = getErrorMsg(wmSettleCommitAuditInputDto.getWmCustomerId(),e);
            } finally {
                baseKvService.del(key);
            }
            if (StringUtils.isNotBlank(errorMsg)) {
                sendMsgToCusOwner(wmSettleCommitAuditInputDto.getWmCustomerId(), baseOperateDataInputDTO.getOpId(), errorMsg);
            }
        });
    }

    private String getErrorMsg(int wmCustomerId, Exception e) {
        String template = "客户ID:%s，保存结算校验不通过，原因是：[%s]";
        String errorMsg = "";
        if (e instanceof WmHeronSettleException) {
            errorMsg = String.format(template, wmCustomerId, ((WmHeronSettleException) e).getMsg());
        } else if (e instanceof TException) {
            errorMsg = String.format(template, wmCustomerId, "网络异常，请稍后重试");
        } else {
            errorMsg = String.format(template, wmCustomerId, "系统异常，请稍后重试");
        }
        return errorMsg;
    }

    private void sendMsgToCusOwner(Integer customerId, Integer opId,String msg) {
        try {
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
            if (wmCustomerDB == null) {
                return;
            }
            List<String> misIdList = Lists.newArrayList();
            addUsefulMisId(wmCustomerDB.getOwnerUid(),misIdList);
            addUsefulMisId(opId,misIdList);
            DaxiangUtil.push("<EMAIL>", msg, misIdList);
        } catch (WmCustomerException e) {
            log.warn("一键同步结算异常信息发送客户责任人异常 code = {}, msg = {}", e.getCode(), e.getMsg(), e);
        }
    }

    private void addUsefulMisId(int uid,List<String> misIdList){
        if(uid > 0){
            String misId = wmEmployeeService.getMisId(uid);
            if(StringUtils.isNotEmpty(misId)){
                misIdList.add(misId);
            }
        }
    }


}
