package com.sankuai.meituan.waimai.customer.service.kp.dboperator;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.util.StringUtil;
import com.sankuai.meituan.waimai.contract.thrift.service.WmContractMcertifyThriftService;
import com.sankuai.meituan.waimai.contract.thrift.vo.IntResult;
import com.sankuai.meituan.waimai.customer.adapter.WmAuditApiAdaptor;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.*;
import com.sankuai.meituan.waimai.customer.util.AppContext;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.poibizflow.thrift.wallet.WmWalletCertifyThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.wallet.bo.WmWalletRealNamePreAuthParam;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditCommitObj;
import com.sankuai.meituan.waimai.thrift.domain.WmContractRealNamePreAuthParam;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmAuditApiService;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditAgentAuthCommitData;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditSpecialAuthCommitData;

import com.site.lookup.util.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 */
@Service
public class KpDBOperateImpl implements KpDBOperate {

    private static final Logger LOGGER = LoggerFactory.getLogger(KpDBOperateImpl.class);

    private static final byte PR_AURH_BIZ_TYPE_CUSTOMER = 5;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    /**
     * 变更签约人失败的状态
     */
    private static final Set<Byte> CHANGE_SIGNER_FAIL_STATES = ImmutableSet.of(KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT.getState(),
            KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState(),
            KpSignerStateMachine.CHANGE_PREAUTH_FAIL.getState(),
            KpSignerStateMachine.CHANGE_SPECILA_AUDIT_REJECT.getState(),
            KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT.getState()
    );

    /**
     * 签约人KP变更与原签约人人状态关系Map
     */
    private static final Map<Byte, Byte> KP_SIGNER_CHANGE_STAET_MAP = MapUtil.of(
            KpSignerStateMachine.PREAUTH_FAIL.getState(), KpSignerStateMachine.CHANGE_PREAUTH_FAIL.getState(),
            KpSignerStateMachine.SPECILA_AUDIT_ING.getState(), KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING.getState(),
            KpSignerStateMachine.SPECILA_AUDIT_REJECT.getState(), KpSignerStateMachine.CHANGE_SPECILA_AUDIT_REJECT.getState(),
            KpSignerStateMachine.AGENT_AUDIT_ING.getState(), KpSignerStateMachine.CHANGE_AGENT_AUDIT_ING.getState(),
            KpSignerStateMachine.AGENT_AUDIT_REJECT.getState(), KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT.getState(),
            KpSignerStateMachine.EFFECT.getState(), KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState()
    );

    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmContractMcertifyThriftService.Iface wmContractMcertifyThriftService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private WmAuditApiService.Iface wmAuditApiService;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    private WmWalletCertifyThriftService wmWalletCertifyThriftService;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    protected WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;

    @Autowired
    private WmAuditApiAdaptor wmAuditApiAdaptor;
    
    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public DBOperateResult operate(WmCustomerDB wmCustomer, List<WmCustomerKp> oldWmCustomerKpList, List<WmCustomerKp> addKpList, List<WmCustomerKp> deleteKpList, List<WmCustomerKp> upgradeKpList,
                                   int uid, String uname) throws WmCustomerException {
        try {
            Integer customerId = wmCustomer.getId();
            if (CollectionUtils.isEmpty(addKpList)) {
                for (WmCustomerKp wmCustomerKp : addKpList) {
                    wmCustomerKp.setCustomerId(customerId);
                }
            }

            //删除
            if (!CollectionUtils.isEmpty(deleteKpList)) {
                for (WmCustomerKp wmCustomerKp : deleteKpList) {
                    wmCustomerKpDBMapper.deleteByPrimaryKey(wmCustomerKp.getId());
                    LOGGER.info("删除KP:{}", wmCustomerKp.getId());
                    wmCustomerKpLogService.deleteKpLog(customerId, wmCustomerKp, uid, uname);
                }
            }

            //签约人KP
            WmCustomerKp signerKp = null;
            //新增
            if (!CollectionUtils.isEmpty(addKpList)) {
                for (WmCustomerKp wmCustomerKp : addKpList) {
                    if (KpTypeEnum.SIGNER.getType() == wmCustomerKp.getKpType()) {
                        //签约人类型
                        signerKp = wmCustomerKp;
                        continue;
                    }
                    wmCustomerKp.setEffective(KpConstants.EFFECTIVE);
                    wmCustomerKpLogService.insertKpLog(wmCustomerKp, uid, uname);
                    wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(wmCustomerKp);
                    wmCustomerKpDBMapper.insert(wmCustomerKp);
                    LOGGER.info("wmCustomerKpDBMapper.insert kpId={}", wmCustomerKp.getId());
                }
            }

            Map<Integer, WmCustomerKp> oldKpMap = Maps.uniqueIndex(oldWmCustomerKpList, new Function<WmCustomerKp, Integer>() {
                @Override
                public Integer apply(@Nullable WmCustomerKp input) {
                    return input == null ? -1 : input.getId();
                }
            });

            //更新
            if (!CollectionUtils.isEmpty(upgradeKpList)) {
                LOGGER.info("upgradeKpList={}", JSON.toJSONString(upgradeKpList));
                for (WmCustomerKp wmCustomerKp : upgradeKpList) {
                    //签约人类型
                    if (KpTypeEnum.SIGNER.getType() == wmCustomerKp.getKpType()) {
                        signerKp = wmCustomerKp;
                        continue;
                    }
                    //普通类型KP
                    WmCustomerKp oldCustomerKp = oldKpMap.get(wmCustomerKp.getId());
                    if (oldCustomerKp == null) {
                        LOGGER.warn("未找到旧的签约人，kpId={}", wmCustomerKp.getId());
                        continue;
                    }
                    List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(oldCustomerKp, wmCustomerKp, differentCustomerKpService.getKpDiffFieldsMap());
                    kpPropertiesSet(oldCustomerKp, wmCustomerKp);
                    wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(oldCustomerKp);
                    wmCustomerKpDBMapper.updateByPrimaryKey(oldCustomerKp);
                    wmCustomerKpLogService.updateKpLog(oldCustomerKp, diffCellBos, uid, uname);
                }
            }

            if (wmCustomer.getCustomerRealType() != CustomerRealTypeEnum.CONTRACTOR.getValue()) {
                if (signerKp == null) {
                    ThrowUtil.throwClientError("未找到签约人KP");
                }
            }
            if (wmCustomer != null
                    && wmCustomer.getCustomerRealType() != null
                    && wmCustomer.getCustomerRealType() == CustomerRealTypeEnum.CONTRACTOR.getValue()) {
                return null;
            }
            upsertSignerKp(oldKpMap.get(signerKp.getId()), signerKp, uid, uname);
        } catch (WmCustomerException e) {
            LOGGER.warn("code:{}, msg:{}", e.getCode(), e.getMsg(), e);
            return new DBOperateResult(e.getCode(), e.getMsg());
        } catch (Exception e) {
            LOGGER.error("保存KP异常", e);
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * KP更新字段设置
     *
     * @param oldCustomerKp 数据库中现有的KP信息
     * @param wmCustomerKp  要更新的KP信息
     */
    public void kpPropertiesSet(WmCustomerKp oldCustomerKp, WmCustomerKp wmCustomerKp) {
        LOGGER.info("kpPropertiesSet oldCustomerKp = {}, wmCustomerKp = {}", JSON.toJSONString(oldCustomerKp), JSON.toJSONString(wmCustomerKp));
        oldCustomerKp.setKpType(wmCustomerKp.getKpType());
        oldCustomerKp.setSignerType(wmCustomerKp.getSignerType());
        oldCustomerKp.setCertType(wmCustomerKp.getCertType());
        oldCustomerKp.setCertNumber(wmCustomerKp.getCertNumber());
        oldCustomerKp.setCompellation(wmCustomerKp.getCompellation());
        oldCustomerKp.setPhoneNum(wmCustomerKp.getPhoneNum());
        oldCustomerKp.setCreditCard(wmCustomerKp.getCreditCard());
        oldCustomerKp.setEmail(wmCustomerKp.getEmail());
        oldCustomerKp.setAgentAuth(wmCustomerKp.getAgentAuth());
        oldCustomerKp.setAgentFrontIdcard(wmCustomerKp.getAgentFrontIdcard());
        oldCustomerKp.setAgentBackIdcard(wmCustomerKp.getAgentBackIdcard());
        oldCustomerKp.setFailReason(wmCustomerKp.getFailReason());
        oldCustomerKp.setSpecialAttachment(wmCustomerKp.getSpecialAttachment());
        oldCustomerKp.setState(wmCustomerKp.getState());
        oldCustomerKp.setBankName(wmCustomerKp.getBankName());
        oldCustomerKp.setBankId(wmCustomerKp.getBankId());
        oldCustomerKp.setBrandIds(wmCustomerKp.getBrandIds());
        oldCustomerKp.setVisitKPPro(wmCustomerKp.getVisitKPPro());
        oldCustomerKp.setWdcClueId(wmCustomerKp.getWdcClueId());
        oldCustomerKp.setLegalIdcardCopy(wmCustomerKp.getLegalIdcardCopy());
        oldCustomerKp.setHaveAgentAuth(wmCustomerKp.getHaveAgentAuth());
        oldCustomerKp.setVersion(wmCustomerKp.getVersion());
        oldCustomerKp.setEffective(wmCustomerKp.getEffective());
        //更新来源属性
        oldCustomerKp.setOperateSource(wmCustomerKp.getOperateSource());
        oldCustomerKp.setLegalAuthType(wmCustomerKp.getLegalAuthType());
    }

    /**
     * 更新签约人KP
     * @param oldCustomerKp 数据库中现有的KP信息
     * @param signerKp      待添加/更新签约人KP对象
     */
    private void upsertSignerKp(WmCustomerKp oldCustomerKp, WmCustomerKp signerKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        LOGGER.info("upsertSignerKp oldCustomerKp = {}, signerKp = {}", JSON.toJSONString(oldCustomerKp), JSON.toJSONString(signerKp));
        boolean sendEffectiveMq = false;
        try {
            List<WmCustomerDiffCellBo> kpUpdateFields = null;
            if (oldCustomerKp != null) {
                kpUpdateFields = differentCustomerKpService.getSignerKpUpdateFields(oldCustomerKp, signerKp);
                if (kpUpdateFields.isEmpty()) {
                    //说明没有更新
                    if (KpSignerStateMachine.EFFECT.getState() == oldCustomerKp.getState()) {
                        //授权失败状态保存需要清空标识
                        WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpId(oldCustomerKp.getId());
                        if (kpTempDB != null && CHANGE_SIGNER_FAIL_STATES.contains(kpTempDB.getState())) {
                            LOGGER.info("未修改任何记录，清空临时数据，kpId={}, tempId={}", oldCustomerKp.getId(), kpTempDB.getId());
                            wmCustomerKpTempDBMapper.deleteByPrimaryKey(kpTempDB.getId());
                            wmCustomerKpLogService.insertOplog(oldCustomerKp.getCustomerId(), oldCustomerKp, "【删除临时变更数据Id:" + kpTempDB.getId() + "】", uid, uname);
                        }
                        return;
                    } else if (KpSignerStateMachine.PREAUTH_FAIL.getState() != oldCustomerKp.getState()) {
                        //新建时，如果状态为认证失败，则提交保存时需要重新认证，否则这里直接返回（状态为非认证失败且无修改）
                        return;
                    }
                }
            }

            Map<String, WmCustomerDiffCellBo> kpUpdateFieldsMap = Maps.uniqueIndex(kpUpdateFields == null ? Lists.<WmCustomerDiffCellBo>newArrayList() : kpUpdateFields, new Function<WmCustomerDiffCellBo, String>() {
                @Override
                public String apply(@Nullable WmCustomerDiffCellBo input) {
                    return input == null ? "" : input.getField();
                }
            });
            //String preAuthErrorMsg = preAuthAndErrorMsg(signerKp, uid, uname);
            PreAuthResultBO result = preAuthAndErrorMsgNew(signerKp, uid, uname);
            String preAuthErrorMsg = (result == null ? null : (result.getResult() == PreAuthResultTypeEnum.FAIL.getType() ? result.getMsg() : null));
            LOGGER.info("预认证结果:{},preAuthErrorMsg={},customerId={},kpId={}", (preAuthErrorMsg == null), preAuthErrorMsg, signerKp.getCustomerId(), signerKp.getId());
            if (oldCustomerKp == null) {
                signerKp.setValid(KpConstants.VALID);
                signerKp.setCtime(DateUtil.unixTime());
                wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(signerKp);
                wmCustomerKpDBMapper.insert(signerKp);
                //为了使用id，先插入到数据库
                LOGGER.info("signerKp id:{}", signerKp.getId());
            }
            signerKp.setFailReason("");
            if (preAuthErrorMsg != null) {
                signerKp.setState(KpSignerStateMachine.PREAUTH_FAIL.getState());
                signerKp.setFailReason(preAuthErrorMsg);
                if (StringUtil.isNotBlank(signerKp.getSpecialAttachment())) {
                    //特批证明不为空，提审特批证明
                    signerKp.setState(KpSignerStateMachine.SPECILA_AUDIT_ING.getState());
                    signerKp.setFailReason("");
                    commitSpecialAudit(signerKp, uid, uname);
                }
            } else {
                if (KpSignerTypeEnum.AGENT.getType() == signerKp.getSignerType()) {
                    //代理商商提审
                    signerKp.setState(KpSignerStateMachine.AGENT_AUDIT_ING.getState());
                    signerKp.setFailReason("");
                    commitAgentAudit(signerKp, uid, uname);
                } else {
                    //非代理商预认证通过的直接生效
                    signerKp.setState(KpSignerStateMachine.EFFECT.getState());
                    signerKp.setEffective(KpConstants.EFFECTIVE);
                    signerKp.setFailReason("");
                    sendEffectiveMq = true;
                }
            }

            if (oldCustomerKp == null) {
                wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(signerKp);
                wmCustomerKpDBMapper.updateByPrimaryKey(signerKp);
                wmCustomerKpLogService.insertKpLog(signerKp, uid, uname);
            } else {
                if (KpSignerStateMachine.EFFECT.getState() == oldCustomerKp.getState()) {
                    //旧的签约人正在生效中，且修改了证件类型和证件编号，则需要更新线下表
                    WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpIdMaster(oldCustomerKp.getId());
                    wmCustomerSensitiveWordsService.readKpWhenSelect(kpTempDB);
                    LOGGER.info("签约人正在生效中，新增/更新线下表，kpId={}, state={}, kpTempDB={}", signerKp.getId(), signerKp.getState(), JSON.toJSONString(kpTempDB));
                    if (kpTempDB != null) {
                        //说明是变更签约人的更新
                        int id = kpTempDB.getId();
                        LOGGER.info("id1={}", id);
                        BeanUtils.copyProperties(signerKp, kpTempDB);
                        LOGGER.info("id2={}", kpTempDB.getId());
                        kpTempDB.setId(id);
                        //状态转换
                        kpTempDB.setState(KP_SIGNER_CHANGE_STAET_MAP.get(signerKp.getState()));
                        kpTempDB.setValid(KpConstants.VALID);
                        LOGGER.info("kpTempDB={}", JSON.toJSONString(kpTempDB));
                        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTempDB);
                        wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTempDB);
                        wmCustomerKpLogService.updateSignerKpLog(signerKp, kpTempDB, kpUpdateFields, uid, uname);
                    } else {
                        kpTempDB = new WmCustomerKpTemp();
                        BeanUtils.copyProperties(signerKp, kpTempDB);
                        kpTempDB.setKpId(signerKp.getId());
                        //状态转换
                        kpTempDB.setState(KP_SIGNER_CHANGE_STAET_MAP.get(signerKp.getState()));
                        kpTempDB.setValid(KpConstants.VALID);
                        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTempDB);
                        wmCustomerKpTempDBMapper.insert(kpTempDB);
                        LOGGER.info("kpTempDB id={}", kpTempDB.getId());
                        wmCustomerKpLogService.updateSignerKpLog(signerKp, kpTempDB, kpUpdateFields, uid, uname);
                    }

                    if (kpTempDB.getState() == KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState()) {
                        //如果KP的签约人由非法人变更为法人，则不需要走原签约人授权流程
                        boolean fromUnSignerToSigner = wmCustomerKpAuditService.isFromUnSignerToSigner(oldCustomerKp.getSignerType(), kpTempDB.getSignerType());
                        if (!fromUnSignerToSigner
                                && (kpUpdateFieldsMap.containsKey(KpConstants.KpFields.CERT_TYPE) || kpUpdateFieldsMap.containsKey(KpConstants.KpFields.CERT_NUMBER))) {
                            LOGGER.info("变动了证件编码需要原签约人授权，singerKp={}, kpTemp={}", JSON.toJSONString(oldCustomerKp), JSON.toJSONString(kpTempDB));
                            //等待原签约人授权
                            originSignerAuth(oldCustomerKp, signerKp, uid, uname);
                            throw new WmCustomerException(501, "由于签约人变更，需原签约人授权。该签约人认证成功后自动发送授权短信给原签约人。");
                        } else {
                            //不需原签约人授权，直接将数据更新为生效表数据，并删除该条临时记录
                            wmCustomerKpService.tempKpEffect(kpTempDB, oldCustomerKp);                            wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.NO.getCode(), oldCustomerKp.getId());
                            kpTempDB.setState(KpSignerStateMachine.EFFECT.getState());
                            kpTempDB.setValid(KpConstants.UN_VALID);
                            LOGGER.info("不需原签约人授权，直接生效，kpTempDB={}，oldCustomerKp={}", JSON.toJSONString(kpTempDB), JSON.toJSONString(oldCustomerKp));
                            //更新为逻辑删除
                            wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(kpTempDB);
                            wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTempDB);
                            wmCustomerKpLogService.updateSignerKpLog(oldCustomerKp, kpTempDB, kpUpdateFields, uid, uname);
                        }
                    } else {
                        wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.NO.getCode(), oldCustomerKp.getId());
                    }
                } else {
                    List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(oldCustomerKp, signerKp, differentCustomerKpService.getKpDiffFieldsMap());
                    kpPropertiesSet(oldCustomerKp, signerKp);
                    wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(oldCustomerKp);
                    wmCustomerKpDBMapper.updateByPrimaryKey(oldCustomerKp);
                    wmCustomerKpLogService.updateKpLog(oldCustomerKp, diffCellBos, uid, uname);
                }
            }
            //如果预认证失败，提示上传特批证明
            if (signerKp.getState() == KpSignerStateMachine.PREAUTH_FAIL.getState()) {
                throw new WmCustomerException(501, signerKp.getFailReason());
            }
        } finally {
            if (sendEffectiveMq) {
                //签约人KP生效发送MQ通知
                if (AppContext.isLazyProcess()) {
                    final WmCustomerKp kp = signerKp;
                    AppContext.offerLazyTask(new AppContext.LazyTask() {
                        @Override
                        public void lazyProcess() {
                            mafkaMessageSendManager.send(new CustomerMQBody(kp.getCustomerId(), CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
                        }

                        @Override
                        public String taskDesc() {
                            return "自入驻绑定门店至3.0，签约人KP生效发送消息";
                        }
                    });
                } else {
                    mafkaMessageSendManager.send(new CustomerMQBody(signerKp.getCustomerId(), CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
                }
            }

        }
    }


    /**
     * 更新变更签约人信息为生效KP
     */
    @Override
    public void tempKpEffect(WmCustomerKpTemp kpTempDB, WmCustomerKp oldCustomerKp) {
        oldCustomerKp.setSignerType(kpTempDB.getSignerType());
        oldCustomerKp.setCompellation(kpTempDB.getCompellation());
        oldCustomerKp.setCertType(kpTempDB.getCertType());
        oldCustomerKp.setCertNumber(kpTempDB.getCertNumber());
        oldCustomerKp.setPhoneNum(kpTempDB.getPhoneNum());
        oldCustomerKp.setBankId(kpTempDB.getBankId());
        oldCustomerKp.setBankName(kpTempDB.getBankName());
        oldCustomerKp.setCreditCard(kpTempDB.getCreditCard());
        oldCustomerKp.setEmail(kpTempDB.getEmail());
        oldCustomerKp.setSpecialAttachment(kpTempDB.getSpecialAttachment());
        oldCustomerKp.setAgentFrontIdcard(kpTempDB.getAgentFrontIdcard());
        oldCustomerKp.setAgentBackIdcard(kpTempDB.getAgentBackIdcard());
        oldCustomerKp.setAgentAuth(kpTempDB.getAgentAuth());
        oldCustomerKp.setSignTaskType(kpTempDB.getSignTaskType());
        oldCustomerKp.setRemark(kpTempDB.getRemark());
        oldCustomerKp.setPhoneNumEncryption(kpTempDB.getPhoneNumEncryption());
        oldCustomerKp.setPhoneNumToken(kpTempDB.getPhoneNumToken());
        oldCustomerKp.setCertNumberEncryption(kpTempDB.getCertNumberEncryption());
        oldCustomerKp.setCertNumberToken(kpTempDB.getCertNumberToken());
        oldCustomerKp.setCreditCardEncryption(kpTempDB.getCreditCardEncryption());
        oldCustomerKp.setCreditCardToken(kpTempDB.getCreditCardToken());
        oldCustomerKp.setHaveAgentAuth(kpTempDB.getHaveAgentAuth());
        oldCustomerKp.setLegalIdcardCopy(kpTempDB.getLegalIdcardCopy());
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdateNoEx(oldCustomerKp);
        wmCustomerKpDBMapper.updateByPrimaryKeySelective(oldCustomerKp);
    }

    /**
     * 校验实名认证是否通过
     *
     * @param wmCustomerKp
     * @param uid
     * @param uname
     * @return
     */
    public boolean checkRealNamePass(WmCustomerKp wmCustomerKp, int uid, String uname) {
        //实名认证流程
        PreAuthResultBO result = preAuthAndErrorMsgNew(wmCustomerKp, uid, uname);
        String preAuthErrorMsg = (result == null ? null : (result.getResult() == PreAuthResultTypeEnum.FAIL.getType() ? result.getMsg() : null));
        if(StringUtils.isEmpty(preAuthErrorMsg)){
            return true;
        }
        return false;
    }


    /**
     * 签约人预认证,接入wiki：https://wiki.sankuai.com/pages/viewpage.action?pageId=841260179
     *
     * @param wmCustomerKp 签约人KP
     * @return 成功返回null，否则返回预认证失败信息
     */
    public PreAuthResultBO preAuthAndErrorMsgNew(WmCustomerKp wmCustomerKp, int uid, String uname) {
        PreAuthResultBO result = new PreAuthResultBO();
        result.setResult(PreAuthResultTypeEnum.SUCCESS.getType());
        result.setMsg(PreAuthResultTypeEnum.SUCCESS.getName());

        boolean isIdCard = wmCustomerKp.getCertType() == CertTypeEnum.ID_CARD.getType();
        boolean isAgentIdCardTempOrCopy = wmCustomerKp.getSignerType() == KpSignerTypeEnum.AGENT.getType() && (wmCustomerKp.getCertType() == CertTypeEnum.ID_CARD_TEMP.getType() || wmCustomerKp.getCertType() == CertTypeEnum.ID_CARD_COPY.getType());
        if (!isIdCard && !isAgentIdCardTempOrCopy) {
            result.setResult(PreAuthResultTypeEnum.FAIL.getType());
            result.setMsg("证件类型非身份证，需上传银行流水");
            return result;
        }
        String threeElemsAuthRes = threeElemsAuth(wmCustomerKp);
        result.setMsg(threeElemsAuthRes);
        result.setAuthType(CertificateAuthTypeEnum.OPERATOR_THREE_ELEMENT_AUTH.getType());
        //有异常则判断是否尝试四要素
        if (threeElemsAuthRes != null) {
            LOGGER.info("运营商三要素认证失败，reason:{}", threeElemsAuthRes);
            if (StringUtils.isNotEmpty(wmCustomerKp.getCreditCard())) {
                LOGGER.info("运营商三要素认证失败，尝试四要素认证");
                String fourElemsAuth = fourElemsAuth(wmCustomerKp);
                result.setMsg(threeElemsAuthRes);
                result.setAuthType(CertificateAuthTypeEnum.STRICT_FOUR_ELEMENT_AUTH.getType());
                wmCustomerKpLogService.insertPreAuthOplog(wmCustomerKp, fourElemsAuth == null ? "四要素认证成功" : "四要素认证失败", fourElemsAuth, uid, uname);
                if (fourElemsAuth != null) {
                    LOGGER.info("个人四要素认证失败，reason:{}", fourElemsAuth);
                    result.setResult(PreAuthResultTypeEnum.FAIL.getType());
                    result.setMsg(PreAuthResultTypeEnum.FAIL.getName());
                    result.setMsg(fourElemsAuth);
                    return result;
                }
                LOGGER.info("个人四要素认证成功");
                if (wmCustomerKpRealAuthService.isNeedEnterpriseFourElePreAuth(wmCustomerKp, true)) {
                    return enterpriseFourElePreAuthAndErrorMsg(wmCustomerKp, uid, uname);
                }
                return result;
            }
            wmCustomerKpLogService.insertPreAuthOplog(wmCustomerKp, "三要素认证失败", threeElemsAuthRes, uid, uname);
            result.setResult(PreAuthResultTypeEnum.FAIL.getType());
            result.setMsg(threeElemsAuthRes + "，可录入银行信息尝试四要素认证");
            return result;

        }
        wmCustomerKpLogService.insertPreAuthOplog(wmCustomerKp, "三要素认证成功", threeElemsAuthRes, uid, uname);
        LOGGER.info("运营商三要素认证成功");
        if (wmCustomerKpRealAuthService.isNeedEnterpriseFourElePreAuth(wmCustomerKp, true)) {
            return enterpriseFourElePreAuthAndErrorMsg(wmCustomerKp, uid, uname);
        }
        return result;
    }

    /**
     * 企业四要素认证
     *
     * @param wmCustomerKp
     * @param uid
     * @param uname
     * @return
     */
    public PreAuthResultBO enterpriseFourElePreAuthAndErrorMsg(WmCustomerKp wmCustomerKp, int uid, String uname) {
        PreAuthResultBO result = wmCustomerKpRealAuthService.enterpriseFourElePreAuthAndErrorMsg(wmCustomerKp, uid);
        return enterpriseFourElePreAuthAndErrorMsg(result, wmCustomerKp, uid, uname);
    }


    /**
     * 企业四要素认证
     *
     * @param wmCustomerKp
     * @param uid
     * @param uname
     * @return
     */
    public PreAuthResultBO enterpriseFourElePreAuthAndErrorMsgForClean(WmCustomerKp wmCustomerKp, int uid, String uname) {
        PreAuthResultBO result = wmCustomerKpRealAuthService.enterpriseFourElePreAuthAndErrorMsgForClean(wmCustomerKp, uid);
        return enterpriseFourElePreAuthAndErrorMsg(result, wmCustomerKp, uid, uname);
    }


    private PreAuthResultBO enterpriseFourElePreAuthAndErrorMsg(PreAuthResultBO result, WmCustomerKp wmCustomerKp, int uid, String uname){
        if (result == null) {
            return null;
        }
        if (PreAuthResultTypeEnum.SUCCESS.getType() == result.getResult()) {
            //企业四要素认证通过
            wmCustomerKpLogService.insertPreAuthOplog(wmCustomerKp, "企业四要素认证通过", "", uid, uname);
        } else if (PreAuthResultTypeEnum.FAIL.getType() == result.getResult()) {
            //企业四要素认证失败
            wmCustomerKpLogService.insertPreAuthOplog(wmCustomerKp, "企业四要素认证失败", result.getMsg(), uid, uname);
        } else {
            //企业四要素认证无结果
            wmCustomerKpLogService.insertPreAuthOplog(wmCustomerKp, "企业四要素认证无结果", result.getMsg(), uid, uname);
        }
        return result;
    }

    private String fourElemsAuth(WmCustomerKp wmCustomerKp) {
        WmContractRealNamePreAuthParam preAuthParam = new WmContractRealNamePreAuthParam()
                .setBizType(PR_AURH_BIZ_TYPE_CUSTOMER)
                .setBizId(wmCustomerKp.getId())
                // 全渠道
                .setName(wmCustomerKp.getCompellation())
                //6216261000000000018
                .setBankCardNo(wmCustomerKp.getCreditCard())
                //341126197709218366
                .setIdCardNo(wmCustomerKp.getCertNumber())
                //***********
                .setPhone(wmCustomerKp.getPhoneNum())
                .setBankName(wmCustomerKp.getBankName());
        return callPreAuth(preAuthParam);
    }

    private String threeElemsAuth(WmCustomerKp wmCustomerKp) {
        WmContractRealNamePreAuthParam preAuthParam = new WmContractRealNamePreAuthParam()
                .setBizType(PR_AURH_BIZ_TYPE_CUSTOMER)
                .setBizId(wmCustomerKp.getId())
                // 全渠道
                .setName(wmCustomerKp.getCompellation())
                .setThreeElementsAuth(true)
                //341126197709218366
                .setIdCardNo(wmCustomerKp.getCertNumber())
                //***********
                .setPhone(wmCustomerKp.getPhoneNum())
                .setBankName(wmCustomerKp.getBankName());
        return callPreAuth(preAuthParam);
    }

    private String callPreAuth(WmContractRealNamePreAuthParam preAuthParam) {
        try {
            if (ConfigUtilAdapter.getBoolean("preAuth_use_new_service_open", false)) {
                WmWalletRealNamePreAuthParam wmWalletRealNamePreAuthParam = new WmWalletRealNamePreAuthParam();
                BeanUtils.copyProperties(preAuthParam, wmWalletRealNamePreAuthParam);
                wmWalletCertifyThriftService.preAuth(wmWalletRealNamePreAuthParam);
            } else {
                IntResult result = wmContractMcertifyThriftService.preAuth(preAuthParam);
                LOGGER.info("preAuth preAuthParam={}, result={}", JSON.toJSONString(preAuthParam), result);
            }
            return null;
        } catch (WmServerException e) {
            LOGGER.warn("预认证失败preAuthParam={}, result={}", JSON.toJSONString(preAuthParam), e);
            return e.getMsg();
        } catch (WmPoiBizException e) {
            LOGGER.warn("预认证失败preAuthParam={}, result={}", JSON.toJSONString(preAuthParam), e);
            return e.getMsg();
        } catch (Exception e) {
            LOGGER.error("调用预认证服务异常，preAuthParam={}", JSON.toJSONString(preAuthParam), e);
            return "预认证服务异常";
        }
    }

    /**
     * 提交代理人蜂鸟审核
     *
     * @param wmCustomerKp 签约人KP
     */
    public void commitAgentAudit(WmCustomerKp wmCustomerKp, int uid, String uname) throws WmCustomerException {
        LOGGER.info("提交代理人特证审核 wmCustomerKp = {}", JSON.toJSONString(wmCustomerKp));
        wmCustomerKpLogService.changeState(wmCustomerKp.getCustomerId(), wmCustomerKp, "代理人授权提交审核", uid, uname);
        commitAudit(wmCustomerKp, KpAuditConstants.TYPE_AGENT, uid, uname);
    }

    /**
     * 提交特批审核
     *
     * @param signerKp 签约人对象
     */
    public void commitSpecialAudit(WmCustomerKp signerKp, int uid, String uname) throws WmCustomerException {
        LOGGER.info("提交特批认证审核，signerKp = {}", JSON.toJSONString(signerKp));
        wmCustomerKpLogService.changeState(signerKp.getCustomerId(), signerKp, "特批认证提交审核", uid, uname);
        commitAudit(signerKp, KpAuditConstants.TYPE_SPECIAL, uid, uname);
    }

    private void commitAudit(WmCustomerKp wmCustomerKp, byte auditType, int uid, String uname) throws WmCustomerException {
        LOGGER.info("commitAudit,wmCustomerKp = {}, auditType = {}, uid = {}, uname = {}", JSON.toJSONString(wmCustomerKp), auditType, uid, uname);
        //新增KP本地提审记录
        WmCustomerKpAudit audit = wmCustomerKpAuditService.insertKpAudit(wmCustomerKp, auditType, uid, uname);
        int bizType;
        String dataJson;
        if (auditType == KpAuditConstants.TYPE_AGENT) {
            bizType = WmAuditTaskBizTypeConstant.AGENT_AUTH;
            WmAuditAgentAuthCommitData data = buildAgentAuditCommitData(wmCustomerKp);
            dataJson = JSON.toJSONString(data);
        } else if (auditType == KpAuditConstants.TYPE_SPECIAL) {
            bizType = WmAuditTaskBizTypeConstant.SPECIAL_AUTH;
            WmAuditSpecialAuthCommitData data = buildSpecialAuditCommitData(wmCustomerKp);
            dataJson = JSON.toJSONString(data);
        } else {
            throw new WmCustomerException(500, "不合法的审核类型：" + auditType);
        }
        LOGGER.info("commitAudit,开始提审,customerKp={},auditId={}", JSON.toJSONString(wmCustomerKp), audit.getId());
        //构建提审对象
        WmAuditCommitObj commitObj = new WmAuditCommitObj()
                .setBiz_type(bizType)
                .setBiz_id(audit.getId())
                .setWm_poi_id(0)
                .setCustomer_id(wmCustomerKp.getCustomerId())
                .setSubmit_uid(uid)
                .setData(dataJson);
        //提交审核
        wmAuditApiAdaptor.commitAudit(commitObj);
    }

    /**
     * 签约人原签约人授权
     *
     * @param oldKpSigner 旧签约人信息
     * @param signerKp    新签约人信息
     */
    public void originSignerAuth(WmCustomerKp oldKpSigner, WmCustomerKp signerKp, int uid, String uname) {
        wmCustomerKpAuditService.commitOriginSignerAuth(oldKpSigner, signerKp, uid, uname);
    }

    /**
     * 构建特批提审数据对象
     *
     * @param wmCustomerKp
     * @return
     */
    public WmAuditSpecialAuthCommitData buildSpecialAuditCommitData(WmCustomerKp wmCustomerKp) {
        //构造特批提审数据
        WmAuditSpecialAuthCommitData data = new WmAuditSpecialAuthCommitData();
        data.setName(wmCustomerKp.getCompellation());
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(wmCustomerKp.getCertType());
        data.setCardType(Integer.valueOf(wmCustomerKp.getCertType()));
        data.setCardTypeStr(certTypeEnum != null ? certTypeEnum.getName() : "未知证件类型");
        data.setCardNo(wmCustomerKp.getCertNumber());
        data.setPhoneNum(wmCustomerKp.getPhoneNum());
        data.setBankNum(wmCustomerKp.getCreditCard());
        data.setSpecialAuthUrl(Arrays.asList(wmCustomerKp.getSpecialAttachment().split(";")));
        try {
            WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerKp.getCustomerId());
            if (wmCustomer != null) {
                if (wmCustomer.getBizOrgCode() != null && wmCustomer.getBizOrgCode().intValue() > 0) {
                    data.setCustomerBusinessLine(wmCustomer.getBizOrgCode());
                }
                CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomer.getCustomerRealType());
                data.setCustomerType(customerRealTypeEnum.getName());
                data.setCustomerTypeCode(customerRealTypeEnum.getValue());
            }
        } catch (WmCustomerException e) {
            LOGGER.error("特批认证提审获取客户信息失败 wmCustomerKp={}", JSONObject.toJSONString(wmCustomerKp), e);
        }
        return data;
    }

    /**
     * 构建代理人提审数据对象
     *
     * @param wmCustomerKp
     * @return
     */
    public WmAuditAgentAuthCommitData buildAgentAuditCommitData(WmCustomerKp wmCustomerKp) {
        //构造代理人提审数据
        WmAuditAgentAuthCommitData data = new WmAuditAgentAuthCommitData();
        data.setName(wmCustomerKp.getCompellation());
        data.setCardType(Integer.valueOf(wmCustomerKp.getCertType()));
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(wmCustomerKp.getCertType());
        data.setCardTypeStr(certTypeEnum == null ? "未知证件类型" : certTypeEnum.getName());
        data.setCardNo(wmCustomerKp.getCertNumber());
        data.setPhoneNum(wmCustomerKp.getPhoneNum());
        data.setBankNum(wmCustomerKp.getCreditCard());
        data.setAgentAuthUrl(wmCustomerKp.getAgentAuth());
        data.setAgentCardUrl(wmCustomerKp.getAgentFrontIdcard());
        data.setAgentCardBackUrl(wmCustomerKp.getAgentBackIdcard());
        //代理人提审添加授权方式字段
        Integer legalAuthType = wmCustomerKp.getLegalAuthType();
        if (legalAuthType != null && legalAuthType == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            data.setLegalAuthType(LegalAuthTypeEnum.MESSAGE_AUTH.getCode());
            data.setLegalAuthTypeStr(LegalAuthTypeEnum.MESSAGE_AUTH.getDesc());
        } else {
            data.setLegalAuthType(LegalAuthTypeEnum.PAPER_AUTH.getCode());
            data.setLegalAuthTypeStr(LegalAuthTypeEnum.PAPER_AUTH.getDesc());
        }
        try {
            WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerKp.getCustomerId());
            if (wmCustomer != null) {
                if (!StringUtil.isBlank(wmCustomer.getLegalPerson())) {
                    data.setLegalPerson(wmCustomer.getLegalPerson());
                }
                if (wmCustomer.getBizOrgCode() != null && wmCustomer.getBizOrgCode().intValue() > 0) {
                    data.setCustomerBusinessLine(wmCustomer.getBizOrgCode());
                }
                CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomer.getCustomerRealType());
                data.setCustomerType(customerRealTypeEnum.getName());
                data.setCustomerTypeCode(customerRealTypeEnum.getValue());
            }
        } catch (WmCustomerException e) {
            LOGGER.error("代理人提审获取客户信息失败 wmCustomerKp={}", JSONObject.toJSONString(wmCustomerKp), e);
        }
        data.setLegalPersonIdCardCopy(wmCustomerKp.getLegalIdcardCopy());
        return data;
    }

}
