package com.sankuai.meituan.waimai.customer.service.sign.callback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.constant.SignPackStatusConstant;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignMsgService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsDataService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.handler.WmEcontractCallbackFailHandler;
import com.sankuai.meituan.waimai.customer.service.sign.callback.handler.WmEcontractCallbackSuccessHandler;
import com.sankuai.meituan.waimai.customer.service.sign.pack.WmEcontractCustomerPackService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractBatchOpTypeConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIResponse;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WmEcontractCallbackService {

    @Resource
    private WmEcontractCallbackSuccessHandler wmEcontractCallbackSuccessHandler;

    @Resource
    private WmEcontractCallbackFailHandler wmEcontractCallbackFailHandler;

    @Resource
    private WmEcontractSmsDataService wmEcontractSmsDataService;

    @Autowired
    private WmEcontractSignMsgService wmEcontractSignMsgService;

    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Autowired
    private WmEcontractCustomerPackService wmEcontractCustomerPackService;

    @Autowired
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;

    @Autowired
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    @Autowired
    private WmEcontractTaskBizService wmEcontractTaskService;

    @Autowired
    private EcontractBizService econtractBizService;

    private static final List<EcontractBatchTypeEnum> DAO_CAN_PUSH_MSG_TYPE_LIST = Lists.newArrayList(
            EcontractBatchTypeEnum.DAOCAN_SERVICE_C1_CONTRACT,
            EcontractBatchTypeEnum.DAOCAN_SERVICE_C2_CONTRACT
    );


    /**
     * 电子合同平台回调后回调业务系统
     */
    public Boolean handleCallback(EcontractBatchContextBo batchContextBo, EcontractNotifyBo notifyBo) throws WmCustomerException, TException {
        if(batchContextBo.getSignPackId() > 0 && wmEcontractCustomerPackService.manualSignUpdateGray(batchContextBo.getCustomerId())){
            return handleManualCallback(batchContextBo, notifyBo);
        } else {
            return handleSimpleCallback(batchContextBo, notifyBo);
        }
    }

    private Boolean handleSimpleCallback(EcontractBatchContextBo batchContextBo, EcontractNotifyBo notifyBo)
            throws WmCustomerException, TException{
        AssertUtil.assertObjectNotNull(notifyBo, "电子合同平台回调");
        AssertUtil.assertObjectNotNull(batchContextBo, "打包任务上下文");
        if (TaskConstant.TASK_SUCCESS.equals(notifyBo.getState())) {
            return wmEcontractCallbackSuccessHandler.handleCallback(batchContextBo, notifyBo);
        } else if (TaskConstant.TASK_FAIL.equals(notifyBo.getState())) {
            return wmEcontractCallbackFailHandler.handleCallback(batchContextBo, notifyBo);
        } else if (TaskConstant.TASK_RUNNIG.equals(notifyBo.getState())) {// 短信重发回调
            Set<String> set = Sets.newHashSet();
            if (StringUtils.isNotEmpty(notifyBo.getExecuteName())) {
                set.add(notifyBo.getExecuteName());
            } else {
                for (Map.Entry<Long, EcontractTaskBo> entry : batchContextBo.getTaskIdAndTaskMap().entrySet()) {
                    set.add(entry.getValue().getApplyType());
                }
            }

            List<String> executiveList= Lists.newArrayList(set);
            String applyConext= Strings.EMPTY;
            for (Map.Entry<Long, EcontractTaskBo> entry : batchContextBo.getTaskIdAndTaskMap().entrySet()) {
                if (executiveList.contains(entry.getValue().getApplyType())) {
                    if (batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.C1_CONTRACT)
                            || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.C2_CONTRACT)
                            || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.QUA_REAL_LETTER)
                            || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT)
                            || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.GROUP_MEAL)
                            || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.MED_DEPOSIT)
                            || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.POI_PROMOTION_SERVICE)) {
                        applyConext = entry.getValue().getApplyContext();
                    }
                }
            }
            // 对应合同类型
            if (isSupportPushMsgBatchType(batchContextBo)) {
                wmEcontractSmsDataService.pushMessage(batchContextBo, notifyBo, applyConext);
            }
            if (DAO_CAN_PUSH_MSG_TYPE_LIST.contains(batchContextBo.getBatchTypeEnum())) {
                wmEcontractSmsDataService.sendDaoCanPushMsg(batchContextBo, notifyBo, applyConext);
            }
            wmEcontractSignMsgService.handleContractMsg(batchContextBo, notifyBo.getStageName());
            return Boolean.TRUE;
        } else{
            log.info("handleCallback 其他状态, notifyBo = {}", JSON.toJSONString(notifyBo));
            return Boolean.TRUE;
        }
    }

    private boolean isSupportPushMsgBatchType (EcontractBatchContextBo batchContextBo) {
        return batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.C1_CONTRACT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.C2_CONTRACT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.SETTLE)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.DELIVERY)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_CSD)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_CS)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_CD)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_CDH)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_SD)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BATCH_DELIVERY)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.ADDEDSERVICE_DISCOUNT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.QUA_REAL_LETTER)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.GROUP_MEAL)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.MED_DEPOSIT)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.POI_PROMOTION_SERVICE)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.AGENT_SQS_STANDARD)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.DRONE_DELIVERY)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.FRUIT_TOGETHER_DELIVERY)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.VIP_CARD)
                || batchContextBo.getBatchTypeEnum().equals(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_PURCHASE)
                || EcontractBatchTypeEnum.isNationalSubsidyDeliveryType(batchContextBo.getBatchTypeEnum())
                ;
    }

    //TODO：当前打包回调，不支持批量部分签约，后续如支持需改造此方法
    private Boolean handleManualCallback(EcontractBatchContextBo batchContextDB, EcontractNotifyBo notifyBo) throws WmCustomerException, TException {
        log.info("handleManualCallback#处理打包任务回调，客户id:{}，打包任务packId:{}，子任务batchId:{}，回调结果:{}", batchContextDB.getCustomerId(),
                batchContextDB.getSignPackId(), batchContextDB.getBatchId(), JSON.toJSONString(notifyBo));
        // 流程态处理，各子任务自行运转，无依赖，将失败情况拎出来，整个打包视角去处理
        if (judgePackInprocess(notifyBo)) {
            handleSimpleCallback(batchContextDB, notifyBo);
            return true;
        }

        // 获取包信息
        WmEcontractSignPackDB signPackDB = wmEcontractSignPackService.querySignPackByIdMaster(batchContextDB.getSignPackId());
        // 终态处理，各子任务全部到达终态后运转，有依赖
        boolean isReachEndStatus = WmEcontractSignPackService.PACK_END_STATUS.contains(signPackDB.getStatus());
        if (isReachEndStatus) {
            if (signPackDB.getStatus() == SignPackStatusConstant.SUCCESS) {
                successPackHandler(batchContextDB);
            } else if (signPackDB.getStatus() == SignPackStatusConstant.FAIL) {
                failPackHandler(batchContextDB, signPackDB);
            } else if (signPackDB.getStatus() == SignPackStatusConstant.CANCEL) {
                log.info("回调流程中暂不处理cancel状态，customerId:{}，packId:{}, batchId:{}, packStatus:{}",
                        batchContextDB.getCustomerId(), batchContextDB.getSignPackId(), batchContextDB.getBatchId(), signPackDB.getStatus());
            } else {
                log.error("未知pack状态，customerId:{}，packId:{}, batchId:{}, packStatus:{}",
                        batchContextDB.getCustomerId(), batchContextDB.getSignPackId(), batchContextDB.getBatchId(), signPackDB.getStatus());
            }
        } else {
            log.info("手动打包批次状态未到达终态，暂不处理回调，customerId:{}，packId:{}, batchId:{}, packStatus:{}",
                    batchContextDB.getCustomerId(), batchContextDB.getSignPackId(), batchContextDB.getBatchId(), signPackDB.getStatus());
        }
        return true;
    }

    private void successPackHandler(EcontractBatchContextBo batchContextDB) throws WmCustomerException {
        //pack如果为success，batch一定都为success，无需再校验batch状态，直接处理回调即可
        List<WmEcontractSignBatchDB> batchList = wmEcontractBigBatchParseService.queryBatchListByPackId(batchContextDB.getSignPackId());
        if (CollectionUtils.isNotEmpty(batchList)) {
            //并发处理会出现数据库死锁问题，因此串行处理
            for (WmEcontractSignBatchDB signBatchDB : batchList) {
                try {
                    EcontractNotifyBo originNotifyBo = JSONObject.parseObject(signBatchDB.getNotifyInfo(), EcontractNotifyBo.class);
                    EcontractBatchContextBo econtractBatchContextBo = parseBatchContext(signBatchDB, originNotifyBo);
                    handleSimpleCallback(econtractBatchContextBo, originNotifyBo);
                } catch (Exception e) {
                    log.error("handleManualCallback子任务回调异常，子任务batchId:{}, 打包批次signPackId:{}", signBatchDB.getId(), signBatchDB.getPackId(), e);
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约结果处理失败");
                }
            }
        }
    }


    private void failPackHandler(EcontractBatchContextBo batchContextDB, WmEcontractSignPackDB signPackDB) throws WmCustomerException {
        try {
            //pack如果为fail，判断除本请求任务外的所有task和batch是否已经更新，如果未更新，则更新
            List<WmEcontractSignBatchDB> batchList = wmEcontractBigBatchParseService.queryBatchListByPackId(batchContextDB.getSignPackId());
            //1.更新task状态
            List<Long> taskIdList = Lists.newArrayList();
            batchList.stream().forEach(batchDB -> {
                EcontractBatchContextBo batchContextBo = JSON.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
                for (Map.Entry<Long, EcontractTaskBo> entry : batchContextBo.getTaskIdAndTaskMap().entrySet()) {
                    taskIdList.add(entry.getKey());
                }
            });
            List<EcontractTaskBo> taskBoList = wmEcontractTaskService.getByIdList(taskIdList);
            List<Long> failTaskIdList = taskBoList.stream()
                    .map(EcontractTaskBo::getId)
                    .collect(Collectors.toList());
            log.info("failPackHandler#failTaskIdList:{}",JSON.toJSONString(failTaskIdList));
            if (CollectionUtils.isNotEmpty(failTaskIdList)) {
                wmEcontractTaskService.batchUpdateState(failTaskIdList, EcontractTaskStateEnum.FAIL.getName());
            }

            //2.更新batch状态
            List<Long> failBatchIdList = batchList.stream()
                    .map(WmEcontractSignBatchDB::getId)
                    .collect(Collectors.toList());
            log.info("failPackHandler#failBatchIdList:{}",JSON.toJSONString(failBatchIdList));
            if (CollectionUtils.isNotEmpty(failBatchIdList)) {
                wmEcontractBatchBaseService.batchUpdateStateByIdList(failBatchIdList, EcontractBatchStateEnum.FAIL.getName());
            }

            //3.更新电子合同状态
            List<String> recordKeyList = batchList.stream()
                    .map(WmEcontractSignBatchDB::getRecordKey)
                    .collect(Collectors.toList());
            log.info("failPackHandler#recordKeyList:{}",JSON.toJSONString(recordKeyList));
            if(CollectionUtils.isNotEmpty(recordKeyList)){
                EcontractAPIResponse resp = econtractBizService.batchOpByRecordKeys(recordKeyList, EcontractBatchOpTypeConstant.CANCEL);
                if (resp.getCode() == EcontractAPIResponseConstant.SUCCESS_CODE && EcontractAPIResponseConstant.SUCCESS.equals(resp.getStatus())) {
                    log.info("pack状态失败，批量取消包下子任务成功，customerId:{}，packId:{}, batchId:{}, packStatus:{}",
                            batchContextDB.getCustomerId(), batchContextDB.getSignPackId(), batchContextDB.getBatchId(), signPackDB.getStatus());
                } else {
                    log.error("pack状态失败，批量取消包下子任务失败，失败原因:{}，customerId:{}，packId:{}, batchId:{}, packStatus:{}",
                            resp.getMsg(), batchContextDB.getCustomerId(), batchContextDB.getSignPackId(), batchContextDB.getBatchId(), signPackDB.getStatus());
                    Cat.logMetricForCount("manual_pack_econtract_cancel_fail");
                }
            }

            //4.回调各模块
            for(EcontractTaskBo taskBo : taskBoList){
                wmEcontractCallbackFailHandler.callbackFail(taskBo.getId(), taskBo, taskBo.getApplyType(), EcontractAPIResponseConstant.SERVER_ERROR, "打包任务处理异常", null);
            }
            log.info("failPackHandler#回调各模块成功，customerId:{}，packId:{}, batchId:{}",batchContextDB.getCustomerId(), batchContextDB.getSignPackId(), batchContextDB.getBatchId());
        } catch (Exception e) {
            log.error("pack状态失败，更新子任务状态失败，customerId:{}，packId:{}, batchId:{}, packStatus:{}",
                    batchContextDB.getCustomerId(), batchContextDB.getSignPackId(), batchContextDB.getBatchId(), signPackDB.getStatus(), e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约结果处理失败");
        }
    }

    private EcontractBatchContextBo parseBatchContext(WmEcontractSignBatchDB batchDB, EcontractNotifyBo notifyBo){
        EcontractBatchContextBo batchContextBo = JSON.parseObject(batchDB.getBatchContext(), EcontractBatchContextBo.class);
        batchContextBo.setBatchId(batchDB.getId());
        log.info("notifyBo={},batchContextBo={}", JSON.toJSONString(notifyBo), JSON.toJSONString(batchContextBo));
        if (StringUtils.isNotEmpty(notifyBo.getDownLoadUrl()) && !notifyBo.getDownLoadUrl().equals(batchContextBo.getDownLoadUrl())) {
            batchContextBo.setDownLoadUrl(notifyBo.getDownLoadUrl());
            wmEcontractBatchBaseService.updateBatchContext(batchContextBo.getBatchId(), JSONObject.toJSONString(batchContextBo));
        }
        return batchContextBo;
    }

    private boolean judgePackInprocess(EcontractNotifyBo notifyBo){
        if(TaskConstant.TASK_SUCCESS.equals(notifyBo.getState()) && !WmEcontractConstant.EFFECT.equals(notifyBo.getStageName())){
            return true;
        }
        return false;
    }
}
