package com.sankuai.meituan.waimai.customer.service.sign.pack;

import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskOpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignStateJudgeBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;

public interface IWmEcontractPackJudgeService {

    public EcontractTaskOpTypeEnum judge(EcontractTaskApplyBo applyBo, EcontractSignStateJudgeBo judgeBo);

}
