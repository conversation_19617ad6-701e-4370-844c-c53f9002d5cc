package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.PARTB_STAMP_NAME;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryAggregationInfoBo;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.signTemplet.SignTempletManagerService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractSignTempletConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignVersionBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PdfTemplet;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 配送信息生成pdf
 */
@Service
@Slf4j
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_DELIVERY_AGGREGATION)
public class WmEcontractBatchDeliveryAggregationDataWrapperService implements IWmEcontractDataWrapperService{

    public static final String SUPPORT_MARK = "support";

    private static final String TEMPLET_BASE_NAME = "delivery_multi_base_info";
    private static final String TEMPLET_DELIVERY_SLA_OTHER_NAME = "delivery_sla_other_info";

    private static final String       DEFAULT_TEMPLET_PREFERENTIAL_APPLY_NAME   = "delivery_preferential_apply_info_v2";
    private static final String       DEFAULT_TEMPLET_PERFORMANCE_GUARANCE_NAME = "delivery_performance_guarance_info_v2";

    private String DEFAULT_TEMPLET_DELIVERY_MULTI_AGGREGATION_INFO_NAME = "delivery_multi_aggregation_info";
    private String DEFAULT_TEMPLET_DELIVERY_AGGREGATION_SUPPLEMENT_INFO_NAME = "delivery_aggregation_supplement_info";

    @Autowired
    private SignTempletManagerService signTempletManagerService;

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo)
        throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        List<PdfContentInfoBo> result = Lists.newArrayList();

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();

        String signVersion = ConfigUtilAdapter.getString(EcontractSignTempletConstant.CURRENT_DELIVERY_SIGN_VERSION);

        PdfTemplet deliveryPdfTemplet = getDefaultPdfTemplate();
        if(StringUtils.isNotEmpty(signVersion)){
            deliveryPdfTemplet = signTempletManagerService.getPdfTemplet(EcontractSignTempletConstant.BIZ_TYPE_DELIVERY, signVersion + "B");
            if(deliveryPdfTemplet == null){
                log.error("签约模板组装异常,taskBo={}",JSONObject.toJSONString(taskBo));
                deliveryPdfTemplet = getDefaultPdfTemplate();
            }
        }

        //多店方案信息页
        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = batchDeliveryInfoBo
                .getEcontractDeliveryInfoBoList();
        EcontractDeliveryAggregationInfoBo econtractDeliveryAggregationInfoBoTemp = null;
        for(EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList){
            econtractDeliveryAggregationInfoBoTemp = temp
                    .getEcontractDeliveryAggregationInfoBo();
            if(econtractDeliveryAggregationInfoBoTemp == null){
                continue;
            }
            pdfBizContent.add(MapUtil.Object2Map(econtractDeliveryAggregationInfoBoTemp));
        }
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(deliveryPdfTemplet.getAggregationFeeInfoTemplet());
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        result.add(pdfInfoBo);
        //多店补充协议页
        result.add(genDefaultPdfTemplate(deliveryPdfTemplet.getAggregationSupplementInfoTemplet(),contextBo));
        return result;
    }

    private PdfTemplet getDefaultPdfTemplate() {
        return new PdfTemplet.Builder().feeInfoTemplet(TEMPLET_BASE_NAME).preferentialApplyInfoTemplet(DEFAULT_TEMPLET_PREFERENTIAL_APPLY_NAME)
                .performanceGuaranceInfoTemplet(DEFAULT_TEMPLET_PERFORMANCE_GUARANCE_NAME)
                .slaOtherInfoTemplet(TEMPLET_DELIVERY_SLA_OTHER_NAME)
                .aggregationFeeInfoTemplet(DEFAULT_TEMPLET_DELIVERY_MULTI_AGGREGATION_INFO_NAME)
                .aggregationSupplementInfoTemplet(DEFAULT_TEMPLET_DELIVERY_AGGREGATION_SUPPLEMENT_INFO_NAME)
                .build();
    }

    private PdfContentInfoBo genDefaultPdfTemplate(String templateName){
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(templateName);
        pdfInfoBo.setPdfMetaContent(Maps.<String, String>newHashMap());
        return pdfInfoBo;
    }

    private PdfContentInfoBo genDefaultPdfTemplate(String templateName,EcontractBatchContextBo contextBo){
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(templateName);
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo));
        return pdfInfoBo;
    }

    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo) {
        Map<String, String> pdfMap = Maps.newHashMap();
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName",StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partB",PARTB_STAMP_NAME);
        pdfMap.put("partBEstamp", PdfConstant.MT_SIGNKEY);
        return pdfMap;
    }

}
