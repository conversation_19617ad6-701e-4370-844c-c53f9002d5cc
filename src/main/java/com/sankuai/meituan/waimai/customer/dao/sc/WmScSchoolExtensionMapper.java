package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolExtensionDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 学校扩展属性Mapper
 * <AUTHOR>
 * @date 2023/05/31
 * @email <EMAIL>
 **/
@Component
public interface WmScSchoolExtensionMapper {
    /**
     * 根据学校ID查询相关信息
     * @param schoolId 学校ID
     * @return WmScSchoolExtensionDO
     */
    WmScSchoolExtensionDO selectBySchoolId(@Param("schoolId") Integer schoolId);

    /**
     * 根据主键ID查询相关信息
     * @param id 主键ID
     * @return WmScSchoolExtensionDO
     */
    WmScSchoolExtensionDO selectById(@Param("id") Long id);
}
