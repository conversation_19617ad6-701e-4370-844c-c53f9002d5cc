package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.IWmEcontractDataWrapperService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractSignTempletConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @description: 拼好饭-代理商保底金额，PDF参数组装
 * @author: zhangyuanhao02
 * @create: 2024/12/19 20:39
 */
@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.PHF_AGENT_SPECIAL_PRICE)
public class WmEcontractPhfAgentSpecialPriceWrapperServiceImpl extends AbstarctWmEcontractPhfDataWrapperService implements IWmEcontractDataWrapperService  {

    private final String  DATE_FORMAL = "yyyy年MM月dd日";

    private static final String PHF_AGENT_SPECIAL_PRICE_PDF_TEMPLATE_VERSION = "phf_agent_special_price_pdf_template_version";

    private static final String PHF_AGENT_SPECIAL_PRICE_PDF_TEMPLATE_ID = "phf_agent_special_price_pdf_template_id";

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.PHF_DELIVERY);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);

        if(CollectionUtils.isEmpty(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList())){
            log.warn("WmEcontractPhfFormalSelfWrapperServiceImpl deliveryInfoBo为空，流程中止，customerId:{}", contextBo.getCustomerId());
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "原始数据缺失，pdf封装失败");
        }

        String signVersion = ConfigUtilAdapter.getString(EcontractSignTempletConstant.CURRENT_DELIVERY_SIGN_VERSION_FOR_NEW_MODULE);
        EcontractSignVersionBo econtractSignVersionBo = contextBo
                .getEcontractSignVersionBo();
        if(econtractSignVersionBo == null){
            econtractSignVersionBo = new EcontractSignVersionBo();
        }
        econtractSignVersionBo.setDeliverySignVersion(signVersion + "B");
        contextBo.setEcontractSignVersionBo(econtractSignVersionBo);

        int templateId = ConfigUtilAdapter.getInt(PHF_AGENT_SPECIAL_PRICE_PDF_TEMPLATE_ID, 19);
        int templateVersion = ConfigUtilAdapter.getInt(PHF_AGENT_SPECIAL_PRICE_PDF_TEMPLATE_VERSION, 0);
        String phfContractName = MccConfig.getPhfContractNameInFormal();

        // 同一个客户下多个门店，一个门店一份合同
        EcontractCustomerInfoBo customerInfoBo = contextBo.getCustomerInfoBo();
        List<PdfContentInfoBo> pdfContentInfoBos = Lists.newArrayList();
        for (EcontractDeliveryInfoBo infoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {

            EcontractDeliveryPhfInfoBo phfInfoBo = infoBo.getEcontractDeliveryPhfInfoBo();
            Map<String,String> pdfMap = generatePdfMetaContent(customerInfoBo, phfInfoBo);

            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            String contractDesc = "适用于 " + phfInfoBo.getWmPoiName();
            pdfInfoBo.setPdfTemplateId(templateId);
            pdfInfoBo.setPdfTemplateVersion(templateVersion);
            pdfInfoBo.setPdfMetaContent(pdfMap);
            pdfInfoBo.setContractDesc(contractDesc);
            pdfInfoBo.setContractName(phfContractName);
            pdfContentInfoBos.add(pdfInfoBo);
        }

        log.info("#WmEcontractPhfFormalSelfWrapperServiceImpl, pdfContentInfoBos={}", JSONObject.toJSONString(pdfContentInfoBos));
        return pdfContentInfoBos;
    }

    @Override
    public Map<String, String> generatePdfMetaContent(EcontractCustomerInfoBo customerInfoBo, EcontractDeliveryPhfInfoBo infoBo) throws WmCustomerException {
        Map<String, String> map = Maps.newHashMap();
        String signTime = dateToStr(new Date(), DATE_FORMAL);

        // 门店名称
        map.put("wmPoiName", infoBo.getWmPoiName());
        // 保底金额
        map.put("specialPrice", infoBo.getSpecialPrice());
        // 商家主体
        map.put("partAName", customerInfoBo.getCustomerName());
        // 商家签章
        map.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        // 商家签约日期
        map.put("partASignTime", signTime);

        // 全局合同使用
        map.put("wmPoiId", infoBo.getWmPoiId());
        String wmPoiInfo = infoBo.getWmPoiName() + " ID " + infoBo.getWmPoiId();
        map.put("poiInfo", StringUtils.defaultIfEmpty(wmPoiInfo, ""));

        return map;
    }
}
