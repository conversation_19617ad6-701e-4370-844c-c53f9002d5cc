package com.sankuai.meituan.waimai.customer.service.sc.mafka.product.strategy;

import com.sankuai.meituan.waimai.customer.constant.sc.ScMafkaTopicEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校园食堂系统发送mq的统一收口逻辑
 */
@Service
public class ScMafkaSendHandleService {

    @Autowired
    private List<IScMafkaSend> iScMafkaSendHandleList;

    private Map<ScMafkaTopicEnum, IScMafkaSend> iTableEventHandleMap = new HashMap<>();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(iScMafkaSendHandleList)) {
            return;
        }
        for (IScMafkaSend handel : iScMafkaSendHandleList) {
            iTableEventHandleMap.put(handel.getTopic(), handel);
        }
    }


    public void send(ScMafkaSend send) {
        if (send == null) {
            return;
        }
        iTableEventHandleMap.get(send.getTopic()).send(send);
    }


}
