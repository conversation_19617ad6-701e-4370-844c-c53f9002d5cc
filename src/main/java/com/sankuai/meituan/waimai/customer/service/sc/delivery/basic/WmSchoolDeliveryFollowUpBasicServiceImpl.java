package com.sankuai.meituan.waimai.customer.service.sc.delivery.basic;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.waimai.customer.adapter.WmBrandMetadataServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScMetadataMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.*;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.customer.domain.sc.metadata.WmScMetadataDO;
import com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService;
import com.sankuai.meituan.waimai.customer.service.sc.WmSchoolServerService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.WmSchoolDeliveryService;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.domain.WmUserVirtualOrgRel;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolLifecycleEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.metadata.WmScMetadataBusinessTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.metadata.WmScMetadataSceneTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.sankuai.meituan.waimai.infra.constants.WmOrgConstant.Position.WM_ORG_CITY_MANAGER;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

@Slf4j
@Service
public class WmSchoolDeliveryFollowUpBasicServiceImpl implements WmSchoolDeliveryBasicService {

    @Autowired
    private WmSchoolDeliveryFollowUpMapper wmSchoolDeliveryFollowUpMapper;

    @Autowired
    private WmScMetadataMapper wmScMetadataMapper;

    @Autowired
    private WmSchoolDeliveryAuditTaskMapper wmSchoolDeliveryAuditTaskMapper;

    @Autowired
    private WmSchoolDeliveryStreamMapper wmSchoolDeliveryStreamMapper;

    @Autowired
    private WmSchoolDeliveryStreamDetailMapper wmSchoolDeliveryStreamDetailMapper;

    @Autowired
    private WmSchoolDeliveryAuditTaskNodeMapper wmSchoolDeliveryAuditTaskNodeMapper;

    @Autowired
    private WmBrandMetadataServiceAdapter wmBrandMetadataServiceAdapter;

    @Autowired
    private WmSchoolDeliveryService wmSchoolDeliveryService;

    @Autowired
    private WmScLogSchoolInfoService wmScLogSchoolInfoService;

    @Autowired
    private WmScOuterService wmScOuterService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmSchoolDeliveryAssignmentMapper wmSchoolDeliveryAssignmentMapper;

    @Autowired
    private WmSchoolServerService wmSchoolServerService;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmSchoolDeliveryGoalSetMapper wmSchoolDeliveryGoalSetMapper;

    @Autowired
    private WmScSchoolService wmScSchoolService;

    /**
     * 在线档口渗透率低值 = 0.4(用于计算学校生命周期)
     */
    private static final double ONLINE_PENERATION_RATE_LOW = 0.4;
    /**
     * 在线档口渗透率高值 = 0.75(用于计算学校生命周期)
     */
    private static final double ONLINE_PENERATION_RATE_HIGH = 0.75;
    /**
     * 日期形式
     */
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public Integer getServiceTypeByDeliveryNodeType() {
        return SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode();
    }

    /**
     * 根据交付编号ID查询生效的学校交付跟进信息
     * @param deliveryId 交付编号ID
     * @return 生效的学校交付信息
     */
    @Override
    public WmSchoolDeliveryDTO getEffectiveDeliveryInfoByDeliveryId(Integer deliveryId) {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getEffectiveDeliveryInfoByDeliveryId] input param: deliveryId = {}", deliveryId);
        // 1-数据查询
        WmSchoolDeliveryFollowUpDO followUpDO = wmSchoolDeliveryFollowUpMapper.selectByDeliveryId(deliveryId);
        WmSchoolDeliveryGoalSetDO goalSetDO = wmSchoolDeliveryGoalSetMapper.selectByDeliveryId(deliveryId);

        // 2-数据组装
        WmSchoolDeliveryFollowUpDTO followUpDTO = WmScTransUtil.transDeliveryFollowUpDOToDTO(followUpDO);
        composeEffectiveGoalSetDOToFollowUpDTO(followUpDTO, goalSetDO);

        // 3-返回结果
        WmSchoolDeliveryDTO deliveryDTO = new WmSchoolDeliveryDTO();
        deliveryDTO.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        deliveryDTO.setDeliveryFollowUpDTO(followUpDTO);
        return deliveryDTO;
    }

    private void composeEffectiveGoalSetDOToFollowUpDTO(WmSchoolDeliveryFollowUpDTO followUpDTO, WmSchoolDeliveryGoalSetDO goalSetDO) {
        followUpDTO.setInscOnlineSignExptime(goalSetDO.getInscOnlineSignExptime());
        followUpDTO.setInscOfflineBuildExptime(goalSetDO.getInscOfflineBuildExptime());

        followUpDTO.setBuildOffCampusStation(goalSetDO.getBuildOffCampusStation());
        followUpDTO.setOutscOnlineSignExptime(goalSetDO.getOutscOnlineSignExptime());
        followUpDTO.setOutscOfflineBuildExptime(goalSetDO.getOutscOfflineBuildExptime());
        followUpDTO.setFirstStallOnlineExptime(goalSetDO.getFirstStallOnlineExptime());

        followUpDTO.setOnlinePenerateTarget(goalSetDO.getOnlinePenerateTarget());
        followUpDTO.setOnlinePenerateTargetExptime(goalSetDO.getOnlinePenerateTargetExptime());

        followUpDTO.setOntimeRateTarget(goalSetDO.getOntimeRateTarget());
        followUpDTO.setOntimeRateTargetExptime(goalSetDO.getOntimeRateTargetExptime());

        followUpDTO.setAvgDeliveryTimeTarget(goalSetDO.getAvgDeliveryTimeTarget());
        followUpDTO.setAvgDeliveryTimeTargetExptime(goalSetDO.getAvgDeliveryTimeTargetExptime());

        followUpDTO.setDailyOrderTarget(goalSetDO.getDailyOrderTarget());
        followUpDTO.setDailyOrderTargetExptime(goalSetDO.getDailyOrderTargetExptime());

        followUpDTO.setMealPenerateTarget(goalSetDO.getMealPenerateTarget());
        followUpDTO.setMealPenerateTargetExptime(goalSetDO.getMealPenerateTargetExptime());

        followUpDTO.setDailyYieldTarget(goalSetDO.getDailyYieldTarget());
        followUpDTO.setDailyYieldTargetExptime(goalSetDO.getDailyYieldTargetExptime());
    }

    /**
     * 根据交付编号ID查询最新暂存的学校交付跟进信息
     * @param deliveryId 交付编号ID
     * @return 生效的学校交付信息
     */
    @Override
    public WmSchoolDeliveryDTO getTempDeliveryInfoByDeliveryId(Integer deliveryId) {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getTempDeliveryInfoByDeliveryId] input param: deliveryId = {}", deliveryId);
        // 1-数据查询
        WmScMetadataDO metadataDO = wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(
                SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(), deliveryId);

        // 2-数据组装
        WmSchoolDeliveryFollowUpDTO followUpDTO = metadataDO == null ? null : JSONObject.parseObject(
                metadataDO.getDataJson(), WmSchoolDeliveryFollowUpDTO.class);

        // 3-返回结果
        WmSchoolDeliveryDTO deliveryDTO = new WmSchoolDeliveryDTO();
        deliveryDTO.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        deliveryDTO.setDeliveryFollowUpDTO(followUpDTO);
        return deliveryDTO;
    }

    /**
     * 根据任务ID查询的学校交付跟进信息
     * @param auditTaskId 任务ID
     * @return 生效的学校交付信息
     */
    @Override
    public WmSchoolDeliveryDTO getAuditDeliveryInfoByAuditTaskId(Integer auditTaskId) throws WmSchCantException {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getAuditDeliveryInfoByAuditTaskId] input param: auditTaskId = {}", auditTaskId);
        // 1-查询提审的数据版本
        WmSchoolDeliveryAuditTaskDO auditTaskDO = wmSchoolDeliveryAuditTaskMapper.selectByPrimaryKey(auditTaskId.longValue());
        if (auditTaskDO == null) {
            log.error("[WmSchoolDeliveryFollowUpBasicServiceImpl.getAuditDeliveryInfoByAuditTaskId] auditTaskDO is null. auditTaskId = {}", auditTaskId);
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "审批任务为空");
        }

        // 2-查询提审的快照数据
        WmScMetadataDO metadataDO = wmScMetadataMapper.selectByBusinessIdAndBusinessTypeAndDataVersion(
                auditTaskDO.getDeliveryId(), SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(), auditTaskDO.getDataVersion());
        if (metadataDO == null) {
            log.error("[WmSchoolDeliveryFollowUpBasicServiceImpl.getAuditDeliveryInfoByAuditTaskId] metadataDO is null. auditTaskId = {}", auditTaskId);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "提审快照数据为空");
        }

        // 3-数据组装
        WmSchoolDeliveryFollowUpDTO followUpDTO = JSONObject.parseObject(metadataDO.getDataJson(), WmSchoolDeliveryFollowUpDTO.class);

        // 4-返回结果
        WmSchoolDeliveryDTO deliveryDTO = new WmSchoolDeliveryDTO();
        deliveryDTO.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        deliveryDTO.setDeliveryFollowUpDTO(followUpDTO);
        return deliveryDTO;
    }

    /**
     * 根据学校ID查询交付流程中的交付跟进状态信息
     * @param schoolPrimaryId 学校主键ID
     * @return WmSchoolDeliveryStatusDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public WmSchoolDeliveryStatusDTO getDeliveryStatusBySchoolPrimaryIdOnStream(Integer schoolPrimaryId) throws WmSchCantException {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getDeliveryStatusBySchoolPrimaryIdOnStream] schoolPrimaryId = {}", schoolPrimaryId);
        // 1-根据学校ID查询学校交付流程主表
        WmSchoolDeliveryStreamDO streamDO = wmSchoolDeliveryStreamMapper.selectBySchoolPrimaryIdOnStram(schoolPrimaryId);
        if (streamDO == null) {
            log.error("[WmSchoolDeliveryFollowUpBasicServiceImpl.getDeliveryStatusBySchoolPrimaryIdOnStream] streamDO is NULL. schoolPrimaryId = {}", schoolPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "该学校暂无流程中的交付信息");
        }

        // 2-根据交付编号ID查询学校交付流程子表
        WmSchoolDeliveryStreamDetailDO streamDetailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(
                streamDO.getId(), SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        if (streamDetailDO == null) {
            log.error("WmSchoolDeliveryFollowUpBasicServiceImpl.getDeliveryStatusBySchoolPrimaryIdOnStream] streamDetailDO is NULL. deliveryId = {}", streamDO.getId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "当前交付节点信息查询为空");
        }

        // 3-查询最近的审批任务(审批主表)
        WmSchoolDeliveryAuditTaskDO auditTaskDO = wmSchoolDeliveryAuditTaskMapper.selectLatestAuditTaskByDeliveryIdAndDeliveryNodeType(
                streamDO.getId(), SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());

        // 4-查询最近的审批子任务(审批子表)
        WmSchoolDeliveryAuditTaskNodeDO taskNodeDO = auditTaskDO == null ? null : wmSchoolDeliveryAuditTaskNodeMapper.selectLatestTaskNodeByAuditTaskId(auditTaskDO.getId());

        // 5-组装数据并返回
        WmSchoolDeliveryStatusDTO deliveryStatusDTO = new WmSchoolDeliveryStatusDTO();
        deliveryStatusDTO.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        deliveryStatusDTO.setDeliveryId(streamDO.getId());
        deliveryStatusDTO.setAuditStatus(streamDetailDO.getAuditStatus());
        deliveryStatusDTO.setEffectStatus(streamDetailDO.getEffective());
        deliveryStatusDTO.setAuditTaskId(auditTaskDO == null ? null : auditTaskDO.getId());
        deliveryStatusDTO.setAuditSystemId(taskNodeDO == null ? null : taskNodeDO.getAuditSystemId());
        deliveryStatusDTO.setSchoolPrimaryId(schoolPrimaryId);
        return deliveryStatusDTO;
    }

    /**
     * 根据交付编号ID查询交付跟进状态信息
     * @param deliveryId 学校主键ID
     * @return WmSchoolDeliveryStatusDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public WmSchoolDeliveryStatusDTO getDeliveryStatusByDeliveryId(Integer deliveryId) throws WmSchCantException {
        // 1-根据交付编号ID查询学校交付流程子表
        WmSchoolDeliveryStreamDetailDO streamDetailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(
                deliveryId, SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());

        // 2-查询最近的审批任务(审批主表)
        WmSchoolDeliveryAuditTaskDO auditTaskDO = wmSchoolDeliveryAuditTaskMapper.selectLastestAuditTaskByDeliveryId(deliveryId);

        // 3-查询最近的审批子任务(审批子表)
        WmSchoolDeliveryAuditTaskNodeDO taskNodeDO = auditTaskDO == null ? null : wmSchoolDeliveryAuditTaskNodeMapper.selectLatestTaskNodeByAuditTaskId(auditTaskDO.getId());

        // 4-组装数据并返回
        WmSchoolDeliveryStatusDTO deliveryStatusDTO = new WmSchoolDeliveryStatusDTO();
        deliveryStatusDTO.setEffectStatus(streamDetailDO.getEffective());
        deliveryStatusDTO.setAuditStatus(streamDetailDO.getAuditStatus());
        deliveryStatusDTO.setDeliveryId(deliveryId);
        deliveryStatusDTO.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        deliveryStatusDTO.setAuditTaskId(auditTaskDO == null ? null : auditTaskDO.getId());
        deliveryStatusDTO.setAuditSystemId(taskNodeDO == null ? null : taskNodeDO.getAuditSystemId());
        deliveryStatusDTO.setSchoolPrimaryId(streamDetailDO.getSchoolPrimaryId());
        return deliveryStatusDTO;
    }

    /**
     * 根据交付编号ID查询生效数据的表单版本号
     * @param deliveryId 交付编号ID
     * @return 表单版本号
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public Integer getEffectiveDeliveryTemplateVersionByDeliveryId(Integer deliveryId) throws WmSchCantException {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getEffectiveDeliveryTemplateVersionByDeliveryId] deliveryId = {}", deliveryId);
        // 1-通过交付编号ID查询交付生效表
        WmSchoolDeliveryFollowUpDO followUpDO = wmSchoolDeliveryFollowUpMapper.selectByDeliveryId(deliveryId);
        if (followUpDO == null) {
            log.error("[WmSchoolDeliveryFollowUpBasicServiceImpl.getEffectiveDeliveryTemplateVersionByDeliveryId] followUpDO is NULL. deliveryId = {}", deliveryId);
            throw new WmSchCantException(SERVER_ERROR, "学校交付跟进生效信息为空");
        }

        // 2-通过交付编号ID和数据版本号查询元数据表
        WmScMetadataDO wmScMetadataDO = wmScMetadataMapper.selectByBusinessIdAndBusinessTypeAndDataVersion(
                followUpDO.getDeliveryId(),
                SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(),
                followUpDO.getDataVersion()
        );

        return wmScMetadataDO.getTemplateVersion();
    }

    /**
     * 根据交付编号ID查询最新暂存数据的表单版本号
     * @param deliveryId 交付编号ID
     * @return 表单版本号
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public Integer getTempDeliveryTemplateVersionByDeliveryId(Integer deliveryId) throws WmSchCantException {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getTempDeliveryTemplateVersionByDeliveryId] deliveryId = {}", deliveryId);
        // 1-通过交付编号ID查询最新暂存数据(可能为空)
        WmScMetadataDO wmScMetadataDO = wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(
                SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(), deliveryId);

        // 2-若数据为空则查询元数据最新表单版本
        if (wmScMetadataDO != null) {
            return wmScMetadataDO.getTemplateVersion();
        }
        return wmBrandMetadataServiceAdapter.getNewestSchoolDeliveryTemplateVersionByBusinessType(WmScMetadataBusinessTypeEnum.DELIVERY_FOLLOWUP.getCode());
    }

    /**
     * 根据交付ID查询交付跟进操作人相关信息
     * @param deliveryId 交付编号ID
     * @return 操作人相关信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public WmSchoolDeliveryOpUserDTO getDeliveryOpUserDTOByDeliveryId(Integer deliveryId) throws WmSchCantException {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getDeliveryOpUserDTOByDeliveryId] deliveryId = {}", deliveryId);
        // 1-查询最新的暂存信息(可能为空)
        WmScMetadataDO metadataDO = wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(
                SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(),
                deliveryId
        );
        if (metadataDO == null) {
            return null;
        }

        // 2-数据组装与返回
        WmSchoolDeliveryOpUserDTO opUserDTO = new WmSchoolDeliveryOpUserDTO();
        opUserDTO.setOpUserUid(metadataDO.getCuid().intValue());
        opUserDTO.setOpType(metadataDO.getOpType());
        opUserDTO.setOpTime(metadataDO.getCtime());
        return opUserDTO;
    }

    /**
     * 学校交付跟进信息暂存
     * @param deliveryId 交付编号ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public void tempSaveDeliveryInfo(WmSchoolDeliverySaveParamDTO saveParamDTO, Integer deliveryId) throws WmSchCantException {
        // 1-查询最新快照数据确定数据版本号和表单版本号
        WmScMetadataDO metadataDOBefore = wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(), deliveryId);

        // 2-保存交付跟进快照信息
        WmSchoolDeliveryFollowUpDTO followUpDTOAfter = saveDeliveryFollowUpSnapshot(saveParamDTO, deliveryId, metadataDOBefore, (int) SchoolDeliveryOpTypeEnum.TEMP_SAVE.getType());

        // 3-记录暂存操作日志
        WmSchoolDeliveryFollowUpDTO followUpDTOBefore = metadataDOBefore == null ? null : JSONObject.parseObject(metadataDOBefore.getDataJson(), WmSchoolDeliveryFollowUpDTO.class);
        recordTempSaveLog(deliveryId, saveParamDTO, followUpDTOBefore, followUpDTOAfter);
    }

    /**
     * 保存交付跟进快照信息
     * @param saveParamDTO saveParamDTO
     * @param deliveryId 交付编号ID
     * @param metadataDOBefore 最新的暂存数据
     * @param opType 操作类型
     * @return WmSchoolDeliveryFollowUpDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmSchoolDeliveryFollowUpDTO saveDeliveryFollowUpSnapshot(WmSchoolDeliverySaveParamDTO saveParamDTO, Integer deliveryId, WmScMetadataDO metadataDOBefore, Integer opType)
            throws WmSchCantException {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.saveDeliveryFollowUpSnapshot] saveParamDTO = {}, deliveryId = {}, metadataDOBefore = {}, opType = {}",
                JSONObject.toJSONString(saveParamDTO), deliveryId, JSONObject.toJSONString(metadataDOBefore), opType);
        WmSchoolDeliveryFollowUpDTO followUpDTO = saveParamDTO.getWmSchoolDeliveryDTO().getDeliveryFollowUpDTO();
        followUpDTO.setDeliveryId(deliveryId);
        followUpDTO.setSchoolPrimaryId(saveParamDTO.getSchoolPrimaryId());

        // 1-根据快照数据确定数据版本号和表单版本号
        Integer dataVersion = wmSchoolDeliveryService.getCurrentDataVersion(metadataDOBefore);
        Integer templateVersion = wmSchoolDeliveryService.getCurrentTemplateVersion(metadataDOBefore, WmScMetadataBusinessTypeEnum.DELIVERY_FOLLOWUP.getCode());
        followUpDTO.setDataVersion(dataVersion);

        // 2-重新计算配送与食堂模块指标当前状态
        refreshFollowUpAggreCanteenGoalStatus(followUpDTO);

        // 3-构造元数据业务对象数据
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.saveDeliveryFollowUpSnapshot] followUpDTO = {}", JSONObject.toJSONString(followUpDTO));
        WmScMetadataDO metadataDOInsert = new WmScMetadataDO();
        metadataDOInsert.setScene(WmScMetadataSceneTypeEnum.SCHOOL_DELIVERY.getCode());
        metadataDOInsert.setBusinessId(deliveryId);
        metadataDOInsert.setBusinessType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        metadataDOInsert.setDataVersion(dataVersion);
        metadataDOInsert.setOpType(opType);
        metadataDOInsert.setTemplateVersion(templateVersion);
        metadataDOInsert.setCuid(saveParamDTO.getUserId().longValue());
        metadataDOInsert.setMuid(saveParamDTO.getUserId().longValue());
        metadataDOInsert.setDataJson(JSONObject.toJSONString(followUpDTO));

        // 4-新增一行元数据快照
        wmScMetadataMapper.insertSelective(metadataDOInsert);
        return followUpDTO;
    }

    /**
     * 交付跟进提审发送大象消息
     * @param followUpDTO followUpDTO
     */
    public void sendSubmitTaskReminderMsgAsync(WmSchoolDeliveryFollowUpDTO followUpDTO) {
        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                List<String> reciverList = new ArrayList<>();
                SchoolBo schoolBo = wmSchoolServerService.selectSchoolById(followUpDTO.getSchoolPrimaryId());
                WmSchoolDeliveryAssignmentDO assignmentDO = wmSchoolDeliveryAssignmentMapper.selectByDeliveryId(followUpDTO.getDeliveryId());
                // 校企KA：学校负责人
                reciverList.add(String.format("%<EMAIL>", schoolBo.getResponsiblePerson()));

                // 交付人员指定中指定的：“学校对应的蜂窝负责人”
                WmEmploy aormEmply = wmScEmployAdaptor.getWmEmployByUid(assignmentDO.getAormUid().intValue());
                if (aormEmply != null) {
                    reciverList.add(String.format("%<EMAIL>", aormEmply.getMisId()));
                }

                // 交付人员指定中指定的：“学校对应的蜂窝负责人”的上级且岗位为“外卖城市负责人”
                List<WmUserVirtualOrgRel> cityManagerList = wmVirtualOrgServiceAdaptor.getUserListByUidAndPositionId(
                        assignmentDO.getAormUid().intValue(),
                        WM_ORG_CITY_MANAGER,
                        WmVirtualOrgSourceEnum.WAIMAI.getSource()
                );
                for (WmUserVirtualOrgRel contactPointEmploy : cityManagerList) {
                    reciverList.add(String.format("%<EMAIL>", contactPointEmploy.getMisId()));
                }

                // 交付人员指定中指定的“聚合渠道经理”
                WmEmploy acmEmply = wmScEmployAdaptor.getWmEmployByUid(assignmentDO.getAcmUid().intValue());
                if (acmEmply != null) {
                    reciverList.add(String.format("%<EMAIL>", acmEmply.getMisId()));
                }

                // 交付人员指定中指定的“聚合渠道经理”上级且岗位为“校园配送渠道区域负责人”
                List<WmUserVirtualOrgRel> aggreDeliveryAreaManagerList = wmVirtualOrgServiceAdaptor.getUserListByUidAndPositionId(
                        assignmentDO.getAcmUid().intValue(),
                        MccScConfig.getSchoolDeliveryChannelAreaManagerPositionId(),
                        WmVirtualOrgSourceEnum.XIAO_YUAN_JU_HE_PEI_SONG.getSource());
                for (WmUserVirtualOrgRel aggreDeliveryArea : aggreDeliveryAreaManagerList) {
                    reciverList.add(String.format("%<EMAIL>", aggreDeliveryArea.getMisId()));
                }

                String msg = "学校：" + schoolBo.getSchoolName() + "（" + schoolBo.getSchoolId() + "）" + "，交付跟进信息已提审，请您知悉。"
                        + "[查看详情 |" + MccScConfig.getSchoolDeliveryFollowUpPageUrlPrefix() + followUpDTO.getSchoolPrimaryId() + "]";

                log.info("[WmSchoolDeliveryFollowUpService.sendSubmitTaskReminderMsgAsync] msg = {}, reciverList = {}", msg, JSONObject.toJSONString(reciverList));
                DaxiangUtilV2.push(msg, reciverList);
            } catch (Exception e) {
                log.error("[WmSchoolDeliveryFollowUpService.sendSubmitTaskReminderMsgAsync] record LOG Exception. followUpDTO = {}", JSONObject.toJSONString(followUpDTO), e);
            }
        })).start();
    }

    /**
     * 记录信息暂存操作日志
     */
    public void recordTempSaveLog(Integer deliveryId, WmSchoolDeliverySaveParamDTO saveParamDTO,
                                  WmSchoolDeliveryFollowUpDTO followUpDTOBefore,
                                  WmSchoolDeliveryFollowUpDTO followUpDTOAfter) {
        WmSchoolDeliveryLogBO deliveryLogBO = WmSchoolDeliveryLogBO.builder()
                .followUpDTOBefore(followUpDTOBefore)
                .followUpDTOAfter(followUpDTOAfter)
                .userId(saveParamDTO.getUserId())
                .userName(saveParamDTO.getUserName())
                .schoolPrimaryId(saveParamDTO.getSchoolPrimaryId())
                .deliveryId(deliveryId).build();

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.recordTempSaveLog] deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO));
                wmScLogSchoolInfoService.recordSchoolDeliveryFollowUpTempSaveLog(deliveryLogBO);
            } catch (Exception e) {
                log.error("[WmSchoolDeliveryFollowUpService.recordSchoolDeliveryFollowUpTempSaveLog] record LOG Exception. deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO), e);
            }
        })).start();
    }

    /**
     * 记录提交审批操作日志
     */
    public void recordCommitLog(WmSchoolDeliverySaveParamDTO saveParamDTO, WmSchoolDeliveryFollowUpDTO followUpDTOBefore, WmSchoolDeliveryFollowUpDTO followUpDTOAfter) {
        WmSchoolDeliveryLogBO deliveryLogBO = WmSchoolDeliveryLogBO.builder()
                .followUpDTOBefore(followUpDTOBefore)
                .followUpDTOAfter(followUpDTOAfter)
                .userId(saveParamDTO.getUserId())
                .userName(saveParamDTO.getUserName())
                .schoolPrimaryId(saveParamDTO.getSchoolPrimaryId()).build();

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                WmSchoolDeliveryAuditTaskDO auditTaskDO = wmSchoolDeliveryAuditTaskMapper.selectLastestAuditTaskByDeliveryId(followUpDTOAfter.getDeliveryId());
                wmScLogSchoolInfoService.recordSchoolDeliveryFollowUpSubmitLog(deliveryLogBO, auditTaskDO.getId());
            } catch (Exception e) {
                log.error("[WmSchoolDeliveryAssignmentService.recordSchoolDeliveryFollowUpCommitLog] record LOG Exception. deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO), e);
            }
        })).start();
    }

    /**
     * 记录任务审批生效操作日志
     */
    public WmSchoolDeliveryFollowUpDTO recordAuditTaskEffectLog(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-根据业务ID和数据版本号查询提审时快照信息
        WmScMetadataDO metadataDO = wmScMetadataMapper.selectByBusinessIdAndBusinessTypeAndDataVersion(taskDO.getDeliveryId(), taskDO.getDeliveryNodeType(), taskDO.getDataVersion());
        if (metadataDO == null) {
            log.error("[WmSchoolDeliveryFollowUpService.recordAuditTaskEffectLog] metadataDO is NULL. auditTaskBO = {}",
                    JSONObject.toJSONString(auditTaskBO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "提审时快照信息查询结果为空");
        }
        WmSchoolDeliveryFollowUpDTO followUpDTOAfter = JSONObject.parseObject(metadataDO.getDataJson(), WmSchoolDeliveryFollowUpDTO.class);

        // 2-查询交付跟进生效信息
        WmSchoolDeliveryFollowUpDO followUpDOBefore = wmSchoolDeliveryFollowUpMapper.selectByDeliveryId(taskDO.getDeliveryId());

        // 3-记录操作日志
        WmSchoolDeliveryLogBO deliveryLogBO = WmSchoolDeliveryLogBO.builder().userId(0).userName("任务系统")
                .schoolPrimaryId(taskDO.getSchoolPrimaryId())
                .deliveryId(taskDO.getDeliveryId())
                .auditTaskId(taskDO.getId())
                .followUpDTOBefore(WmScTransUtil.transDeliveryFollowUpDOToDTO(followUpDOBefore))
                .followUpDTOAfter(followUpDTOAfter).build();

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                log.info("[WmSchoolDeliveryFollowUpService.recordAuditTaskEffectLog] deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO));
                wmScLogSchoolInfoService.recordSchoolDeliveryFollowUpTaskEffectLog(deliveryLogBO, taskDO.getId());
            } catch (Exception e) {
                log.error("[WmSchoolDeliveryFollowUpService.recordAuditTaskEffectLog] record LOG Exception. deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO), e);
            }
        })).start();

        return followUpDTOAfter;
    }

    /**
     * 将提审数据新增/更新到生效表中
     * @param auditTaskBO auditTaskBO
     * @param followUpDTOAfter followUpDTOAfter
     */
    public void upsertDeliveryFollowUpAuditEffectInfo(WmSchoolDeliveryAuditTaskBO auditTaskBO, WmSchoolDeliveryFollowUpDTO followUpDTOAfter) {
        log.info("[WmSchoolDeliveryFollowUpService.upsertDeliveryFollowUpAuditEffectInfo] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询交付跟进生效信息
        WmSchoolDeliveryFollowUpDO followUpDOBefore = wmSchoolDeliveryFollowUpMapper.selectByDeliveryId(taskDO.getDeliveryId());

        // 2-将交付跟进暂存快照数据转DO
        WmSchoolDeliveryFollowUpDO followUpDOAfter = WmScTransUtil.transDeliveryFollowUpDTOToDO(followUpDTOAfter, taskDO.getDataVersion());

        // 3-若已有生效信息则更新, 否则新增生效信息
        if (followUpDOBefore == null) {
            wmSchoolDeliveryFollowUpMapper.insertSelective(followUpDOAfter);
        } else {
            followUpDOAfter.setId(followUpDOBefore.getId());
            wmSchoolDeliveryFollowUpMapper.updateByPrimaryKeySelective(followUpDOAfter);
        }
    }

    /**
     * 记录任务审批取消操作日志
     */
    public void recordAuditTaskCancelLog(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryLogBO deliveryLogBO = WmSchoolDeliveryLogBO.builder()
                .userId(auditTaskBO.getOpUserUid())
                .userName(auditTaskBO.getOpUserName())
                .schoolPrimaryId(auditTaskBO.getSchoolPrimaryId())
                .deliveryId(auditTaskBO.getDeliveryId())
                .auditTaskId(auditTaskBO.getAuditTaskId()).build();

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.recordAuditTaskCancelLog] deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO));
                wmScLogSchoolInfoService.recordSchoolDeliveryFollowUpCancelTaskLog(deliveryLogBO);
            } catch (Exception e) {
                log.error("[WmSchoolDeliveryAssignmentService.recordAuditTaskCancelLog] record LOG Exception. deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO), e);
            }
        })).start();
    }

    /**
     * 记录任务审批驳回操作日志
     */
    public void recordAuditTaskRejectLog(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryLogBO deliveryLogBO = WmSchoolDeliveryLogBO.builder()
                .userId(0)
                .userName("任务系统")
                .schoolPrimaryId(auditTaskBO.getTaskDO().getSchoolPrimaryId())
                .deliveryId(auditTaskBO.getTaskDO().getDeliveryId())
                .auditTaskId(auditTaskBO.getTaskDO().getId()).build();

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                log.info("[WmSchoolDeliveryFollowUpService.recordAuditTaskRejectLog] deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO));
                wmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditRejectLog(deliveryLogBO);
            } catch (Exception e) {
                log.error("[WmSchoolDeliveryFollowUpService.recordAuditTaskRejectLog] record LOG Exception. deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO), e);
            }
        })).start();
    }

    /**
     * 记录任务审批终结操作日志
     */
    public void recordAuditTaskStopLog(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryLogBO deliveryLogBO = WmSchoolDeliveryLogBO.builder()
                .userId(0)
                .userName("任务系统")
                .schoolPrimaryId(auditTaskBO.getTaskDO().getSchoolPrimaryId())
                .deliveryId(auditTaskBO.getTaskDO().getDeliveryId())
                .auditTaskId(auditTaskBO.getTaskDO().getId()).build();

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                log.info("[WmSchoolDeliveryFollowUpService.recordAuditTaskStopLog] deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO));
                wmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditStopLog(deliveryLogBO);
            } catch (Exception e) {
                log.error("[WmSchoolDeliveryFollowUpService.recordAuditTaskStopLog] record LOG Exception. deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO), e);
            }
        })).start();
    }

    /**
     * 记录任务审批通过操作日志
     */
    public void recordAuditTaskPassLog(WmSchoolDeliveryAuditTaskBO auditTaskBO, Integer nextAuditNode) {
        WmSchoolDeliveryLogBO deliveryLogBO = WmSchoolDeliveryLogBO.builder()
                .userId(0)
                .userName("任务系统")
                .schoolPrimaryId(auditTaskBO.getTaskDO().getSchoolPrimaryId())
                .deliveryId(auditTaskBO.getTaskDO().getDeliveryId())
                .auditTaskId(auditTaskBO.getTaskDO().getId())
                .nextAuditNode(nextAuditNode).build();

        // 单起一个异步线程进行处理
        new Thread(new TraceRunnable(() -> {
            try {
                log.info("[WmSchoolDeliveryFollowUpService.recordAuditTaskPassLog] deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO));
                wmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditPassLog(deliveryLogBO);
            } catch (Exception e) {
                log.error("[WmSchoolDeliveryFollowUpService.recordAuditTaskPassLog] record LOG Exception. deliveryLogBO = {}", JSONObject.toJSONString(deliveryLogBO), e);
            }
        })).start();
    }

    /**
     * 计算交付目标完成情况
     * @param followUpDTO 交付跟进
     * @param goalSetDO 交付目标制定
     */
    public void calculateDeliveryFollowUpGoalAchieve(WmSchoolDeliveryFollowUpDTO followUpDTO, WmSchoolDeliveryGoalSetDO goalSetDO) {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.calculateDeliveryFollowUpGoalAchieve] followUpDTO = {}, goalSetDO = {}", JSONObject.toJSONString(followUpDTO), JSONObject.toJSONString(goalSetDO));
        // 相对准时率目标完成情况
        followUpDTO.setOntimeRateTarget(goalSetDO.getOntimeRateTarget());
        followUpDTO.setOntimeRateTargetExptime(goalSetDO.getOntimeRateTargetExptime());
        if (StringUtils.isBlank(followUpDTO.getOntimeRate())) {
            followUpDTO.setOntimeRateAchieve((int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        } else {
            boolean ontimeRateMeetStandard = Double.parseDouble(goalSetDO.getOntimeRateTarget()) <= Double.parseDouble(followUpDTO.getOntimeRate());
            followUpDTO.setOntimeRateAchieve(ontimeRateMeetStandard ? (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType()
                    : (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        }

        // 平均配送时长目标完成情况
        followUpDTO.setAvgDeliveryTimeTarget(goalSetDO.getAvgDeliveryTimeTarget());
        followUpDTO.setAvgDeliveryTimeTargetExptime(goalSetDO.getAvgDeliveryTimeTargetExptime());
        if (StringUtils.isBlank(followUpDTO.getAvgDeliveryTime())) {
            followUpDTO.setAvgDeliveryTimeAchieve((int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        } else {
            boolean avgDeliveryTimeMeetStandard = Double.parseDouble(goalSetDO.getAvgDeliveryTimeTarget()) >= Double.parseDouble(followUpDTO.getAvgDeliveryTime());
            followUpDTO.setAvgDeliveryTimeAchieve(avgDeliveryTimeMeetStandard ? (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType()
                    : (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        }

        // 日均订单量目标完成情况
        followUpDTO.setDailyOrderTarget(goalSetDO.getDailyOrderTarget());
        followUpDTO.setDailyOrderTargetExptime(goalSetDO.getDailyOrderTargetExptime());
        if (StringUtils.isBlank(followUpDTO.getDailyOrder())) {
            followUpDTO.setDailyOrderAchieve((int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        } else {
            boolean dailyOrderMeetStandard = Double.parseDouble(goalSetDO.getDailyOrderTarget()) <= Double.parseDouble(followUpDTO.getDailyOrder());
            followUpDTO.setDailyOrderAchieve(dailyOrderMeetStandard ? (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType()
                    : (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        }

        // 人顿渗透率目标完成情况
        followUpDTO.setMealPenerateTarget(goalSetDO.getMealPenerateTarget());
        followUpDTO.setMealPenerateTargetExptime(goalSetDO.getMealPenerateTargetExptime());
        if (StringUtils.isBlank(followUpDTO.getMealPenerate())) {
            followUpDTO.setMealPenerateAchieve((int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        } else {
            boolean mealPenerateMeetStandard = Double.parseDouble(goalSetDO.getMealPenerateTarget()) <= Double.parseDouble(followUpDTO.getMealPenerate());
            followUpDTO.setMealPenerateAchieve(mealPenerateMeetStandard ? (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType()
                    : (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        }

        // 日均店单产目标完成情况
        followUpDTO.setDailyYieldTarget(goalSetDO.getDailyYieldTarget());
        followUpDTO.setDailyYieldTargetExptime(goalSetDO.getDailyYieldTargetExptime());
        if (StringUtils.isBlank(followUpDTO.getDailyYield())) {
            followUpDTO.setDailyYieldAchieve((int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        } else {
            boolean dailyYieldMeetStandard = Double.parseDouble(goalSetDO.getDailyYieldTarget()) <= Double.parseDouble(followUpDTO.getDailyYield());
            followUpDTO.setDailyYieldAchieve(dailyYieldMeetStandard ? (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType()
                    : (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());
        }

        // 在线渗透率(学校关联的处于上线状态的门店/交付人员指定环节录入的公海档口数)
        String onlinePenerate = getSchoolDeliveryOnlinePenerate(followUpDTO.getSchoolPrimaryId(), followUpDTO.getDeliveryId());
        followUpDTO.setOnlinePenerate(onlinePenerate);
        followUpDTO.setOnlinePenerateTarget(goalSetDO.getOnlinePenerateTarget());
        followUpDTO.setOnlinePenerateTargetExptime(goalSetDO.getOnlinePenerateTargetExptime());

        // 在线渗透率目标完成情况
        boolean onlinePenerateMeetStandard = Double.parseDouble(goalSetDO.getOnlinePenerateTarget()) <= Double.parseDouble(followUpDTO.getOnlinePenerate());
        followUpDTO.setOnlinePenerateAchieve(onlinePenerateMeetStandard ? (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType()
                : (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType());

        // 生命周期
        Integer lifeCycle = getSchoolDeliveryLifeCycle(followUpDTO.getOnlinePenerate());
        followUpDTO.setLifeCycle(lifeCycle);
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.calculateDeliveryFollowUpGoalAchieve] followUpDTO = {}", JSONObject.toJSONString(followUpDTO));
    }

    /**
     * 根据在线渗透率计算学校生命周期
     * @param onlinePenerate 在线渗透率
     * @return 学校生命周期
     */
    public Integer getSchoolDeliveryLifeCycle(String onlinePenerate) {
        // 渗透率小于40%-合作学校
        if (Double.parseDouble(onlinePenerate) < ONLINE_PENERATION_RATE_LOW * 100.0) {
            return (int) SchoolLifecycleEnum.COOPERATE_SCHOOL.getType();
        }
        // 渗透率大于等于75%-深度合作学校
        if (Double.parseDouble(onlinePenerate) >= ONLINE_PENERATION_RATE_HIGH * 100.0) {
            return (int) SchoolLifecycleEnum.DEEP_COOPERATE_SCHOOL.getType();
        }
        // 渗透率大于等于40% 小于75%-有效合作学校
        return (int) SchoolLifecycleEnum.VALID_COOPERATE_SCHOOL.getType();
    }

    /**
     * 计算学校在线渗透率 = 学校关联的在线状态的门店数(不包括子门店) / 交付人员指定录入的公海档口数
     * @param schoolPrimaryId 学校主键ID
     * @param deliveryId 交付编号ID
     * @return 学校在线渗透率
     */
    public String getSchoolDeliveryOnlinePenerate(Integer schoolPrimaryId, Integer deliveryId) {
        List<Long> wmPoiIds = wmScOuterService.getWmPoiIdsBySchoolId(schoolPrimaryId);
        // 获取门店信息
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIds, Sets.newHashSet(
                WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
                WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
                WmPoiFieldQueryConstant.WM_POI_FIELD_VALID,
                WmPoiFieldQueryConstant.WM_POI_FIELD_SUB_WM_POI_TYPE)
        );

        List<WmPoiAggre> wmPoiIdOnlineList = new ArrayList<>();

        // 命中灰度 - 不包括子门店
        if (wmScSchoolService.isSchoolInCanteenStallAccuracyGrayList(schoolPrimaryId)) {
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                if (wmPoiAggre.getValid() == WmPoiValidEnum.ONLINE.getValue()
                        && wmPoiAggre.getSub_wm_poi_type() <= 0) {
                    wmPoiIdOnlineList.add(wmPoiAggre);
                }
            }
        } else {
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                if (wmPoiAggre.getValid() == WmPoiValidEnum.ONLINE.getValue()) {
                    wmPoiIdOnlineList.add(wmPoiAggre);
                }
            }
        }

        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getSchoolDeliveryOnlinePenerate] wmPoiIds = {}, wmPoiIdOnlineList = {}", JSONObject.toJSONString(wmPoiIds), JSONObject.toJSONString(wmPoiIdOnlineList));
        WmSchoolDeliveryAssignmentDO assignmentDO = wmSchoolDeliveryAssignmentMapper.selectByDeliveryId(deliveryId);
        double onlinePenerate = wmPoiIdOnlineList.size() / assignmentDO.getPublicStallNum().doubleValue();
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.getSchoolDeliveryOnlinePenerate] onlinePenerate = {}", onlinePenerate);
        return String.format("%.2f", onlinePenerate * 100.0);
    }

    /**
     * 计算交付目标当前状态-运营监控模块
     * @param followUpDTO 交付跟进DTO
     * @param goalSetDO 交付目标制定
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void calculateDeliveryFollowUpOperationIndexGoalStatus(WmSchoolDeliveryFollowUpDTO followUpDTO, WmSchoolDeliveryGoalSetDO goalSetDO)
            throws WmSchCantException {
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.calculateDeliveryFollowUpOperationIndexGoalStatus] followUpDTO = {}, goalSetDO = {}", JSONObject.toJSONString(followUpDTO), JSONObject.toJSONString(goalSetDO));
        // 运营效果监控-在线渗透率目标当前状态
        Integer onlinePenerateStatus = getDeliveryFollowUpOperationIndexGoalStatus(followUpDTO.getOnlinePenerateAchieve(), goalSetDO.getOnlinePenerateTargetExptime());
        followUpDTO.setOnlinePenerateStatus(onlinePenerateStatus);
        if (onlinePenerateStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setOnlinePenerateException("");
        }

        // 运营效果监控-相对准时率目标当前状态
        Integer ontimeRateStatus = getDeliveryFollowUpOperationIndexGoalStatus(followUpDTO.getOntimeRateAchieve(), goalSetDO.getOntimeRateTargetExptime());
        followUpDTO.setOntimeRateStatus(ontimeRateStatus);
        if (ontimeRateStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setOntimeRateException("");
        }

        // 运营效果监控-平均配送时长目标当前状态
        Integer avgDeliveryTimeStatus = getDeliveryFollowUpOperationIndexGoalStatus(followUpDTO.getAvgDeliveryTimeAchieve(), goalSetDO.getAvgDeliveryTimeTargetExptime());
        followUpDTO.setAvgDeliveryTimeStatus(avgDeliveryTimeStatus);
        if (avgDeliveryTimeStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setAvgDeliveryTimeException("");
        }

        // 运营效果监控-日均订单量目标当前状态
        Integer dailyOrderStatus = getDeliveryFollowUpOperationIndexGoalStatus(followUpDTO.getDailyOrderAchieve(), goalSetDO.getDailyOrderTargetExptime());
        followUpDTO.setDailyOrderStatus(dailyOrderStatus);
        if (dailyOrderStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setDailyOrderException("");
        }

        // 运营效果监控-人顿渗透率目标当前状态
        Integer mealPenerateStatus = getDeliveryFollowUpOperationIndexGoalStatus(followUpDTO.getMealPenerateAchieve(), goalSetDO.getMealPenerateTargetExptime());
        followUpDTO.setMealPenerateStatus(mealPenerateStatus);
        if (mealPenerateStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setMealPenerateException("");
        }

        // 运营效果监控-日均店单产目标当前状态
        Integer dailyYieldStatus = getDeliveryFollowUpOperationIndexGoalStatus(followUpDTO.getDailyYieldAchieve(), goalSetDO.getDailyYieldTargetExptime());
        followUpDTO.setDailyYieldStatus(dailyYieldStatus);
        if (dailyYieldStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setDailyYieldException("");
        }
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.calculateDeliveryFollowUpOperationIndexGoalStatus] followUpDTO = {}", JSONObject.toJSONString(followUpDTO));
    }

    /**
     * 根据目标完成情况和预计完成时间计算目标当前状态(运营指标监控)
     * @param goalAchieve 目标完成情况
     * @param expTime 预计完成时间
     * @return 目标当前状态
     */
    public Integer getDeliveryFollowUpOperationIndexGoalStatus(Integer goalAchieve, String expTime) {
        // 达标
        if (goalAchieve.equals((int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType())) {
            return getDeliveryFollowUpOperationIndexGoalStatusByMeetStandard(expTime);
        }
        // 未达标
        return getDeliveryFollowUpOperationIndexGoalStatusByNotMeetStandard(expTime);
    }

    public Integer getDeliveryFollowUpOperationIndexGoalStatusByMeetStandard(String expTime) {
        LocalDate expTimeDate = LocalDate.parse(expTime, formatter);
        LocalDate currentDate = LocalDate.now();
        // 当前时间在计划完成时间之前或当天-正常
        if (currentDate.isBefore(expTimeDate) || currentDate.isEqual(expTimeDate)) {
            return (int) SchoolDeliveryGoalStatusEnum.NORMAL.getType();
        }
        // 当前时间在计划完成时间之后-延期
        return (int) SchoolDeliveryGoalStatusEnum.DELAY.getType();
    }

    public Integer getDeliveryFollowUpOperationIndexGoalStatusByNotMeetStandard(String expTime) {
        LocalDate expTimeDate = LocalDate.parse(expTime, formatter);
        LocalDate currentDate = LocalDate.now();
        // 当前时间在计划完成时间5日之前-正常(不含5日)
        if (currentDate.isBefore(expTimeDate.minusDays(4))) {
            return (int) SchoolDeliveryGoalStatusEnum.NORMAL.getType();
        }
        // 当前时间在计划完成时间之后-延期
        if (currentDate.isAfter(expTimeDate)) {
            return (int) SchoolDeliveryGoalStatusEnum.DELAY.getType();
        }
        // 当前时间在计划完成时间0-5日之间-预警
        return (int) SchoolDeliveryGoalStatusEnum.WARN.getType();
    }

    /**
     * 重新计算配送与食堂模块指标当前状态
     * @param followUpDTO followUpDTO
     */
    public void refreshFollowUpAggreCanteenGoalStatus(WmSchoolDeliveryFollowUpDTO followUpDTO) {
        WmSchoolDeliveryGoalSetDO goalSetDO = wmSchoolDeliveryGoalSetMapper.selectByDeliveryId(followUpDTO.getDeliveryId());
        // 聚合配送模块-校内站线上签约当前状态
        Integer inscOnlineStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getInscOnlineSignFintime(), goalSetDO.getInscOnlineSignExptime());
        followUpDTO.setInscOnlineSignStatus(inscOnlineStatus);
        if (inscOnlineStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setInscOnlineSignException("");
        }
        // 聚合配送模块-校内站线下建站当前状态
        Integer inscOfflineStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getInscOfflineBuildFintime(), goalSetDO.getInscOfflineBuildExptime());
        followUpDTO.setInscOfflineBuildStatus(inscOfflineStatus);
        if (inscOfflineStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setInscOfflineBuildException("");
        }
        if (goalSetDO.getBuildOffCampusStation().equals((int) SchoolDeliveryBuildOffCampusEnum.YES.getType())) {
            // 聚合配送模块-校外站线上签约当前状态
            Integer outscOnlineStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getOutscOnlineSignFintime(), goalSetDO.getOutscOnlineSignExptime());
            followUpDTO.setOutscOnlineSignStatus(outscOnlineStatus);
            if (outscOnlineStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
                followUpDTO.setOutscOnlineSignException("");
            }

            // 聚合配送模块-校外站线下建站当前状态
            Integer outscOfflineStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getOutscOfflineBuildFintime(), goalSetDO.getOutscOfflineBuildExptime());
            followUpDTO.setOutscOfflineBuildStatus(outscOfflineStatus);
            if (outscOfflineStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
                followUpDTO.setOutscOfflineBuildException("");
            }
        }

        // 食堂档口模块-首批档口上线当前状态
        Integer firstStallStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getFirstStallOnlineFintime(), goalSetDO.getFirstStallOnlineExptime());
        followUpDTO.setFirstStallOnlineStatus(firstStallStatus);
        if (firstStallStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setFirstStallOnlineException("");
        }
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.refreshFollowUpAggreCanteenGoalStatus] followUpDTO = {}", JSONObject.toJSONString(followUpDTO));
    }


    /**
     * 计算交付目标当前状态-聚合配送模块 & 食堂档口模块
     * @param followUpDTO 交付跟进
     * @param goalSetDO 交付目标制定
     */
    public void calculateDeliveryFollowUpAggreCanteenGoalStatus(WmSchoolDeliveryFollowUpDTO followUpDTO, WmSchoolDeliveryGoalSetDO goalSetDO) {
        // 聚合配送模块-校内站线上签约当前状态
        followUpDTO.setInscOnlineSignExptime(goalSetDO.getInscOnlineSignExptime());
        Integer inscOnlineStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getInscOnlineSignFintime(), goalSetDO.getInscOnlineSignExptime());
        followUpDTO.setInscOnlineSignStatus(inscOnlineStatus);
        if (inscOnlineStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setInscOnlineSignException("");
        }

        // 聚合配送模块-校内站线下建站当前状态
        followUpDTO.setInscOfflineBuildExptime(goalSetDO.getInscOfflineBuildExptime());
        Integer inscOfflineStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getInscOfflineBuildFintime(), goalSetDO.getInscOfflineBuildExptime());
        followUpDTO.setInscOfflineBuildStatus(inscOfflineStatus);
        if (inscOfflineStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setInscOfflineBuildException("");
        }

        followUpDTO.setBuildOffCampusStation(goalSetDO.getBuildOffCampusStation());
        if (goalSetDO.getBuildOffCampusStation().equals((int) SchoolDeliveryBuildOffCampusEnum.YES.getType())) {
            // 聚合配送模块-校外站线上签约当前状态
            followUpDTO.setOutscOnlineSignExptime(goalSetDO.getOutscOnlineSignExptime());
            Integer outscOnlineStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getOutscOnlineSignFintime(), goalSetDO.getOutscOnlineSignExptime());
            followUpDTO.setOutscOnlineSignStatus(outscOnlineStatus);
            if (outscOnlineStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
                followUpDTO.setOutscOnlineSignException("");
            }

            // 聚合配送模块-校外站线下建站当前状态
            followUpDTO.setOutscOfflineBuildExptime(goalSetDO.getOutscOfflineBuildExptime());
            Integer outscOfflineStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getOutscOfflineBuildFintime(), goalSetDO.getOutscOfflineBuildExptime());
            followUpDTO.setOutscOfflineBuildStatus(outscOfflineStatus);
            if (outscOfflineStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
                followUpDTO.setOutscOfflineBuildException("");
            }
        } else {
            // 聚合配送模块-校外站线上签约当前状态
            followUpDTO.setOutscOnlineSignAchieve(0);
            followUpDTO.setOutscOnlineSignStatus(0);
            followUpDTO.setOutscOnlineSignFintime("");
            followUpDTO.setOutscOnlineSignException("");
            // 聚合配送模块-校外站线下建站当前状态
            followUpDTO.setOutscOfflineBuildAchieve(0);
            followUpDTO.setOutscOfflineBuildStatus(0);
            followUpDTO.setOutscOfflineBuildFintime("");
            followUpDTO.setOutscOfflineBuildException("");
        }

        // 食堂档口模块-首批档口上线当前状态
        followUpDTO.setFirstStallOnlineExptime(goalSetDO.getFirstStallOnlineExptime());
        Integer firstStallStatus = getDeliveryFollowUpAggreCanteenGoalStatus(followUpDTO.getFirstStallOnlineFintime(), goalSetDO.getFirstStallOnlineExptime());
        followUpDTO.setFirstStallOnlineStatus(firstStallStatus);
        if (firstStallStatus.equals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType())) {
            followUpDTO.setFirstStallOnlineException("");
        }
        log.info("[WmSchoolDeliveryFollowUpBasicServiceImpl.calculateDeliveryFollowUpAggreCanteenGoalStatus] followUpDTO = {}", JSONObject.toJSONString(followUpDTO));
    }

    /**
     * 根据实际完成时间和预计完成时间计算目标当前状态(聚合配送 & 食堂档口模块)
     * @param finTime 实际完成时间
     * @param expTime 预计完成时间
     * @return 目标当前状态
     */
    public Integer getDeliveryFollowUpAggreCanteenGoalStatus(String finTime, String expTime) {
        // 实际完成时间存在
        if (StringUtils.isNotBlank(finTime)) {
            return getDeliveryFollowUpAggreCanteenGoalStatusWithFinTime(finTime, expTime);
        }
        // 实际完成时间不存在
        return getDeliveryFollowUpAggreCanteenGoalStatusWithoutFinTime(expTime);
    }

    public Integer getDeliveryFollowUpAggreCanteenGoalStatusWithFinTime(String finTime, String expTime) {
        LocalDate finTimeDate = LocalDate.parse(finTime, formatter);
        LocalDate expTimeDate = LocalDate.parse(expTime, formatter);
        // 实际完成时间在计划完成时间之前或当天-正常
        if (finTimeDate.isBefore(expTimeDate) || finTimeDate.isEqual(expTimeDate)) {
            return (int) SchoolDeliveryGoalStatusEnum.NORMAL.getType();
        }
        // 实际完成时间在计划完成时间之后-延期
        return (int) SchoolDeliveryGoalStatusEnum.DELAY.getType();
    }

    public Integer getDeliveryFollowUpAggreCanteenGoalStatusWithoutFinTime(String expTime) {
        LocalDate expTimeDate = LocalDate.parse(expTime, formatter);
        LocalDate currentDate = LocalDate.now();
        // 当前时间在计划完成时间5日之前-正常(不含5日)
        if (currentDate.isBefore(expTimeDate.minusDays(4))) {
            return (int) SchoolDeliveryGoalStatusEnum.NORMAL.getType();
        }
        // 当前时间在计划完成时间之后-延期
        if (currentDate.isAfter(expTimeDate)) {
            return (int) SchoolDeliveryGoalStatusEnum.DELAY.getType();
        }
        // 当前时间在计划完成时间0-5日之间-预警
        return (int) SchoolDeliveryGoalStatusEnum.WARN.getType();
    }

    /**
     * 根据交付编号ID和数据版本查询特定版本的快照数据
     * @param deliveryId 交付编号ID
     * @param dataVersion 数据版本
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public WmSchoolDeliveryDTO getDeliverySnapshotByDeliveryIdAndDataVersion(Integer deliveryId, Integer dataVersion) throws WmSchCantException {
        WmScMetadataDO wmScMetadataDO = wmScMetadataMapper.selectByBusinessIdAndBusinessTypeAndDataVersion(deliveryId, SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode(), dataVersion);
        if (wmScMetadataDO == null) {
            return null;
        }

        WmSchoolDeliveryFollowUpDTO followUpDTO = JSONObject.parseObject(wmScMetadataDO.getDataJson(), WmSchoolDeliveryFollowUpDTO.class);
        WmSchoolDeliveryDTO deliveryDTO = new WmSchoolDeliveryDTO();
        deliveryDTO.setDeliveryFollowUpDTO(followUpDTO);
        deliveryDTO.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        return deliveryDTO;
    }

}
