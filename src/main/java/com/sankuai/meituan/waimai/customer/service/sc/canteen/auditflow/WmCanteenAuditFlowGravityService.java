package com.sankuai.meituan.waimai.customer.service.sc.canteen.auditflow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.gravity.thrift.VariableUtils;
import com.sankuai.meituan.gravity.thrift.server.*;
import com.sankuai.meituan.waimai.customer.adapter.GravityThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenInfoAuditBO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.EffectiveStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditResultEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

@Slf4j
@Service
public class WmCanteenAuditFlowGravityService {

    @Autowired
    private GravityThriftServiceAdapter gravityThriftServiceAdapter;

    @Autowired
    private WmCanteenAuditFlowCanteenService canteenAuditFlowCanteenService;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    /**
     * 驱动Gravity对流程驳回结束
     * @param auditBO auditBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void driveGravityProcessByAuditReject(WmCanteenInfoAuditBO auditBO) throws WmSchCantException, TException {
        log.info("[WmCanteenAuditFlowGravityService.driveGravityProcessByAuditReject] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();
        // 1-查询Gravity流程的正在活动中任务
        TaskResponse taskResponse = gravityThriftServiceAdapter.getSingleTask(auditDO.getGravityId(), CanteenInfoAuditNodeTypeEnum.getByType(auditDO.getAuditNode()).getAcitivityId());
        if (taskResponse == null) {
            log.error("[WmCanteenAuditFlowGravityService.driveGravityProcessByAuditReject] taskResponse is NULL. gravityId = {}, avtivityId = {}",
                    auditDO.getGravityId(), CanteenInfoAuditNodeTypeEnum.getByType(auditDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程任务不存在");
        }

        // 2-驱动Gravity流程进行撤回结束
        TaskActionRequest taskActionRequest = new TaskActionRequest();
        taskActionRequest.setAssignee(String.valueOf(auditBO.getOpUserUid()));
        taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("auditResult", (int) CanteenInfoAuditResultEnum.AUDIT_REJECT.getType()));
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(auditDO.getCanteenId());
        if (wmCanteenDB.getEffective().equals((int) EffectiveStatusEnum.EFFECTIVE.getType())) {
            taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("changeStallNum", canteenAuditFlowCanteenService.getIsChangeStallNum(wmCanteenDB, auditDO) ? 1:0));
            taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("downBeyondThreshold", canteenAuditFlowCanteenService.getStallNumDownBeyondThreshold(wmCanteenDB, auditDO) ? 1:0));
        }
        ResultResponse resultResponse = gravityThriftServiceAdapter.completeTaskWithReturn(taskResponse.getId(), taskActionRequest);

        if (!resultResponse.isSuccess()) {
            log.error("[WmCanteenAuditFlowGravityService.driveGravityProcessByAuditReject] resultResponse not success. gravityId = {}, avtivityId = {}",
                    auditDO.getGravityId(), CanteenStallAuditNodeTypeEnum.getByType(auditDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程驱动失败");
        }
    }

    /**
     * 驱动Gravity对流程通过结束
     * @param auditBO auditBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void driveGravityProcessByAuditPass(WmCanteenInfoAuditBO auditBO) throws WmSchCantException, TException {
        log.info("[WmCanteenAuditFlowGravityService.driveGravityProcessByAuditPass] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();
        // 1-查询Gravity流程的正在活动中任务
        TaskResponse taskResponse = gravityThriftServiceAdapter.getSingleTask(auditDO.getGravityId(), CanteenInfoAuditNodeTypeEnum.getByType(auditDO.getAuditNode()).getAcitivityId());
        if (taskResponse == null) {
            log.error("[WmCanteenAuditFlowGravityService.driveGravityProcessByAuditPass] taskResponse is NULL. gravityId = {}, avtivityId = {}",
                    auditDO.getGravityId(), CanteenInfoAuditNodeTypeEnum.getByType(auditDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程任务不存在");
        }

        // 2-驱动Gravity流程进行通过结束
        TaskActionRequest taskActionRequest = new TaskActionRequest();
        taskActionRequest.setAssignee(String.valueOf(auditBO.getOpUserUid()));
        taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("auditResult", (int) CanteenInfoAuditResultEnum.AUDIT_PASS.getType()));
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(auditDO.getCanteenId());
        if (wmCanteenDB.getEffective().equals((int) EffectiveStatusEnum.EFFECTIVE.getType())) {
            taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("changeStallNum", canteenAuditFlowCanteenService.getIsChangeStallNum(wmCanteenDB, auditDO) ? 1:0));
            taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("downBeyondThreshold", canteenAuditFlowCanteenService.getStallNumDownBeyondThreshold(wmCanteenDB, auditDO) ? 1:0));
        }

        ResultResponse resultResponse = gravityThriftServiceAdapter.completeTaskWithReturn(taskResponse.getId(), taskActionRequest);
        if (!resultResponse.isSuccess()) {
            log.error("[WmCanteenAuditFlowGravityService.driveGravityProcessByAuditPass] resultResponse not success. gravityId = {}, avtivityId = {}",
                    auditDO.getGravityId(), CanteenStallAuditNodeTypeEnum.getByType(auditDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程驱动失败");
        }
    }

    /**
     * 根据GravityId查询流程实例获取当前审批节点
     * @param gravityId GravityId流程实例ID
     * @return 当前审批节点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public CanteenInfoAuditNodeTypeEnum getGravityAuditNodeByGravityId(String gravityId) throws WmSchCantException {
        List<TaskResponse> taskResponseList = gravityThriftServiceAdapter.getTasksByProcessInstanceIds(gravityId);
        log.info("[WmCanteenAuditFlowGravityService.getGravityAuditNodeByGravityId] taskResponseList = {}", taskResponseList);
        if (taskResponseList.size() > 1) {
            log.error("[WmCanteenAuditFlowGravityService.getGravityTaskResponseByGravityId] taskResponseList error. gravityId = {}", gravityId);
            throw new WmSchCantException(SERVER_ERROR, "查询活动任务失败");
        }

        if (CollectionUtils.isNotEmpty((taskResponseList))) {
            return CanteenInfoAuditNodeTypeEnum.getByActivityId(taskResponseList.get(0).getTaskDefinitionKey());
        }

        ProcessInstanceResponse instanceResponse = gravityThriftServiceAdapter.getProcessInstanceById(gravityId);
        if (instanceResponse == null) {
            log.error("[WmCanteenAuditFlowGravityService.getGravityTaskResponseByGravityId] instanceResponse is null. gravityId = {}", gravityId);
            throw new WmSchCantException(SERVER_ERROR, "查询活动任务失败");
        }
        log.info("[WmCanteenAuditFlowGravityService.getGravityAuditNodeByGravityId] instanceResponse = {}", instanceResponse.toString());
        return CanteenInfoAuditNodeTypeEnum.getByActivityId(instanceResponse.getActivityId());
    }




    /**
     * 创建Gravity审批流程实例
     * @param wmScCanteenAuditDO wmScCanteenAuditDO
     * @return gravity流程实例ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String createAuditGravityInstance(WmScCanteenAuditDO wmScCanteenAuditDO, WmCanteenDB wmCanteenDB) throws WmSchCantException {
        log.info("[WmCanteenAuditFlowGravityService.createAuditGravityInstance] input param: wmScCanteenAuditDO = {}, wmCanteenDB = {}",
                JSONObject.toJSONString(wmScCanteenAuditDO), JSONObject.toJSONString(wmCanteenDB));
        ProcessInstanceCreateRequest request = new ProcessInstanceCreateRequest();
        // 业务ID = 审批任务主键ID
        request.setBusinessKey(String.valueOf(wmScCanteenAuditDO.getId()));
        request.setProcessDefinitionKey(CanteenInfoAuditTaskTypeEnum.getByType(wmScCanteenAuditDO.getAuditTaskType()).getFlowDefiniteKey());

        List<VariableStruct> variables = new ArrayList<>();
        variables.add(VariableUtils.buildVariableStruct("canteenAttribute", wmScCanteenAuditDO.getCanteenAttribute()));
        // 修改食堂审批
        if (wmCanteenDB.getEffective().equals((int) EffectiveStatusEnum.EFFECTIVE.getType())) {
            variables.add(VariableUtils.buildVariableStruct("changeStallNum", canteenAuditFlowCanteenService.getIsChangeStallNum(wmCanteenDB, wmScCanteenAuditDO) ? 1:0));
            variables.add(VariableUtils.buildVariableStruct("downBeyondThreshold", canteenAuditFlowCanteenService.getStallNumDownBeyondThreshold(wmCanteenDB, wmScCanteenAuditDO) ? 1:0));
        }
        request.setVariables(variables);

        ProcessResultResponse resultResponse = gravityThriftServiceAdapter.createProcessInstanceWithReturn(request);
        if (!resultResponse.isSuccess() || resultResponse.getData() == null) {
            log.error("[WmCanteenAuditFlowGravityService.createAuditGravityInstance] request = {}, resultResponse = {}",
                    JSON.toJSONString(request), JSON.toJSONString(resultResponse));
            throw new WmSchCantException(SERVER_ERROR, "Gravity流程实例创建失败");
        }
        return resultResponse.getData().getId();
    }


}
