package com.sankuai.meituan.waimai.customer.adapter.sg;

import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.shangou.partner.sdk.base.ErrEm;
import com.sankuai.shangou.partner.sdk.dto.NationalSubsidySelfOpEntityDTO;
import com.sankuai.shangou.partner.sdk.response.NationalSubsidySelfOpEntityResp;
import com.sankuai.shangou.partner.sdk.thrift.NationalSubsidySupplierThrift;
import lombok.extern.slf4j.Slf4j;
import org.mortbay.util.ajax.JSON;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/5/24 11:03
 */
@Slf4j
@Service
public class NationalSubsidySupplierThriftAdapter {

    @Resource
    private NationalSubsidySupplierThrift nationalSubsidySupplierThrift;

    /**
     * 查询采购协议的集合店主体
     *
     * @param customerId 客户ID
     * @return 集合店主体信息
     * @throws WmCustomerException 异常
     */
    public List<NationalSubsidySelfOpEntityDTO> queryNationalSubsidySubject(Long customerId) throws WmCustomerException {
        NationalSubsidySelfOpEntityResp resp = nationalSubsidySupplierThrift.queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId);
        log.info("NationalSubsidySupplierThriftAdapter#queryNationalSubsidySubject, resp: {}", JSON.toString(resp));
        if (ErrEm.SUCCESS_CODE.getCode() != resp.getCode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, resp.getMessage());
        }
        return resp.getRelatedSelfOpEntityList();
    }

}
