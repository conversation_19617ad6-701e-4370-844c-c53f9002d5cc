package com.sankuai.meituan.waimai.customer.contract.global;

import com.sankuai.meituan.waimai.thrift.customer.domain.global.ContractRoleSaveParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.EContractRoleInfoResponse;

import java.util.List;

/**
 * 权限表service接口
 *
 * @Author: wangyongfang
 * @Date: 2024-01-15
 */
public interface IWmGlobalContractRoleAuthorityService {

    /**
     *  分页查询所有的全局合同角色权限列表
     *
     * @param startPageNum
     * @param pageSize
     * @return
     */
    public EContractRoleInfoResponse selectAllListByPageParam(int startPageNum, int pageSize);

    /**
     * 存储全局合同角色权限
     *
     * @param contractRoleSaveParam
     * @return
     */
    public boolean saveContractRole(ContractRoleSaveParam contractRoleSaveParam);

}
