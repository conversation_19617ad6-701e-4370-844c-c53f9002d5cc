package com.sankuai.meituan.waimai.customer.service.sc.area;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.inf.octo.mns.MnsInvoker;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmAreaUpdateInfo;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScAreaTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScUpdateTypeEnum;
import com.sankuai.sgagent.thrift.model.ProtocolRequest;
import com.sankuai.sgagent.thrift.model.SGService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 校园食堂范围处理服务
 */
@Slf4j
@Service
public class WmScAreaService {

    @Autowired
    private SchoolAreaServiceImpl schoolAreaService;

    private static final String APP_KEY = "com.sankuai.waimai.e.customer";

    private static final String SERVICE_NAME = "com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolThriftService";

    private static final int SERVICE_PORT = 8452;

    private static final String SERVICE_METHOD_V2 = "refreshAreaV2";

    private static final String GENERIC = "json-common";

    private static final int TIME_OUT = 10000;

    private static final String PARAM_TYPES = "com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmAreaUpdateInfo";

    private static final String PROTOCOL = "thrift";

    /**
     * thrift代理类缓存
     */
    private static Map<String, ThriftClientProxy> thriftClientProxyMap = Maps.newHashMap();

    /**
     * 根据坐标点查询命中业务主键列表
     * @param x 经度
     * @param y 纬度
     * @param areaTypeEnum com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScAreaTypeEnum
     * @return 命中业务主键列表
     */
    public List<Integer> findV2(int x, int y, WmScAreaTypeEnum areaTypeEnum) {
        log.info("findV2 x = {}, y = {}, areaTypeEnum = {}", x, y, areaTypeEnum);
        List<Integer> result = Lists.newArrayList();
        result.addAll(schoolAreaService.selectV2(x, y, areaTypeEnum));
        return result;
    }

    /**
     * 接收数据变更刷新范围本地换存-内部使用
     *
     * @param info
     */
    public void refreshV2(WmAreaUpdateInfo info) {
        log.info("[WmScAreaService.refreshV2] input param: info={}", JSONObject.toJSONString(info));
        Set<String> serviceInfo = Sets.newHashSet();
        ProtocolRequest request = new ProtocolRequest();
        request.setProtocol(PROTOCOL).setLocalAppkey(APP_KEY).setRemoteAppkey(APP_KEY);
        List<SGService> list = MnsInvoker.getServiceList(request);
        for (SGService sgService : list) {
            serviceInfo.add(sgService.getIp());
        }
        log.info("[WmScAreaService.refreshV2] app key = {}, service list = {}", APP_KEY, JSONObject.toJSONString(serviceInfo));
        thriftGenericV2(serviceInfo, info);
    }

    public void thriftGenericV2(Set<String> serviceList, WmAreaUpdateInfo info) {
        log.info("[thriftGenericV2] serviceList = {}, info = {}", JSONObject.toJSONString(serviceList), JSONObject.toJSONString(info));
        for (String ip : serviceList) {
            try {
                ThriftClientProxy clientProxy = thriftClientProxyMap.get(ip);
                if (clientProxy == null) {
                    clientProxy = new ThriftClientProxy();
                    clientProxy.setAppKey(APP_KEY);
                    clientProxy.setRemoteAppkey(APP_KEY);
                    clientProxy.setGenericServiceName(SERVICE_NAME);
                    clientProxy.setServerIpPorts(String.format("%s:%s", ip, SERVICE_PORT));
                    //clientProxy.setRemoteServerPort(SERVICE_PORT);
                    clientProxy.setFilterByServiceName(true);
                    clientProxy.setGeneric(GENERIC);
                    clientProxy.setTimeout(TIME_OUT);
                    clientProxy.setRemoteUniProto(true);
                    // 触发初始化
                    clientProxy.afterPropertiesSet();
                    thriftClientProxyMap.put(ip, clientProxy);
                }
                GenericService genericClient = (GenericService) clientProxy.getObject();
                List<String> paramTypes = Lists.newArrayList(PARAM_TYPES);
                List<String> paramValues = Lists.newArrayList(JacksonUtils.serialize(info));
                genericClient.$invoke(SERVICE_METHOD_V2, paramTypes, paramValues);
            } catch (Exception e) {
                log.error("thriftGenericV2失败, ip={}, info:{}", ip, JSONObject.toJSONString(info), e);
            }
        }
    }

    /**
     * 接收数据变更刷新范围本地缓存v2
     * @param info WmAreaUpdateInfo
     */
    public void refreshRtreeV2(WmAreaUpdateInfo info) {
        log.info("[WmScAreaService#refreshRTreeV2] input param: info={}", JSONObject.toJSONString(info));
        if (info == null) {
            return;
        }
        if (info.getId() < 0) {
            return;
        }
        WmScUpdateTypeEnum updateTypeEnum = WmScUpdateTypeEnum.of(info.getUpdateType());
        if (updateTypeEnum == null) {
            return;
        }
        WmScAreaTypeEnum areaTypeEnum = WmScAreaTypeEnum.of(info.getAreaType());
        if (areaTypeEnum == null) {
            return;
        }
        switch (updateTypeEnum) {
            case ADD:
                schoolAreaService.addV2(info.getId(), info.getArea(), areaTypeEnum);
                break;
            case DELETE:
                schoolAreaService.deleteV2(info.getId(), areaTypeEnum);
                break;
            case UPDATE:
                schoolAreaService.updateV2(info.getId(), info.getArea(), areaTypeEnum);
                break;
            case INIT:
                schoolAreaService.initV2();
                break;
            default:
                break;
        }
    }

}


