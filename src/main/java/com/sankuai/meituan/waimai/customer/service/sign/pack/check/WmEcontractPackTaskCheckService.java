package com.sankuai.meituan.waimai.customer.service.sign.pack.check;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.WmCustomerGrayServiceAdapter;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmSubjectChangeSupplementEContractTempletService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.pack.AbstractWmEcontractPoiJudgeService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 电子合同打包task校验
 */
@Service
public class WmEcontractPackTaskCheckService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractWmEcontractPoiJudgeService.class);

    @Resource
    private WmEcontractTaskBizService wmEcontractTaskBizService;

    @Resource
    private WmEcontractPackRelContractCheckService wmEcontractPackRelContractCheckService;

    @Resource
    private WmEcontractPackRelDeliveryCheckService wmEcontractPackRelDeliveryCheckService;

    @Resource
    private WmEcontractPackRelSettleCheckService wmEcontractPackRelSettleCheckService;

    @Autowired
    private WmCustomerGrayServiceAdapter wmCustomerGrayServiceAdapter;

    @Autowired
    private WmSubjectChangeSupplementEContractTempletService wmSubjectChangeSupplementEContractTempletService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    public Map<Long, EcontractTaskBo> getEffectHoldingTaskByCustomerId(Integer customerId,boolean hasIsPoiAgentAndHasC2Boolean) throws WmCustomerException {
        List<EcontractTaskBo> taskBoList = wmEcontractTaskBizService.getByCustomerIdAndState(customerId, EcontractTaskStateEnum.HOLDING.getName());
        Map<Long, EcontractTaskBo> taskBoMap = Maps.newHashMap();
        for (EcontractTaskBo taskBo:taskBoList) {
            boolean c2Boolean=!EcontractTaskApplyTypeEnum.C2CONTRACT.getName().equals(taskBo.getApplyType())
                    &&hasIsPoiAgentAndHasC2Boolean;
            if (!EcontractTaskApplyTypeEnum.C1CONTRACT.getName().equals(taskBo.getApplyType())
                    && !EcontractTaskApplyTypeEnum.SETTLE.getName().equals(taskBo.getApplyType())
                    && !EcontractTaskApplyTypeEnum.POIFEE.getName().equals(taskBo.getApplyType())
                    && !EcontractTaskApplyTypeEnum.SUBJECT_CHANGE_SUPPLEMENT.getName().equals(taskBo.getApplyType())
                    && c2Boolean) {
                taskBoMap.put(taskBo.getId(), taskBo);
            }

            List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(customerId);
            if (EcontractTaskApplyTypeEnum.FOODCITY_STATEMENT.getName().equals(taskBo.getApplyType())
                    || EcontractTaskApplyTypeEnum.FOODCITY_POI_TABLE.getName().equals(taskBo.getApplyType())
                    || (EcontractTaskApplyTypeEnum.SUBJECT_CHANGE_SUPPLEMENT.getName().equals(taskBo.getApplyType())
                    && !wmSubjectChangeSupplementEContractTempletService.judgeAddedSubjectChangeSupplementToAutoPack(customerId, wmPoiIdList, false))
                    || EcontractTaskApplyTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT.getName().equals(taskBo.getApplyType())
                    || EcontractTaskApplyTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT.getName().equals(taskBo.getApplyType())
                    || EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName().equals(taskBo.getApplyType())) {
                taskBoMap.put(taskBo.getId(), taskBo);
            }
        }
        return taskBoMap;
    }

    public Map<Long, EcontractTaskBo> getEffectCSPTaskByCustomerId(Integer customerId,boolean hasIsPoiAgentAndHasC2Boolean) throws WmCustomerException {
        List<EcontractTaskBo> taskBoList = wmEcontractTaskBizService.getByCustomerIdAndState(customerId, EcontractTaskStateEnum.HOLDING.getName());
        List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(customerId);
        Map<Long, EcontractTaskBo> taskBoMap = Maps.newHashMap();
        for (EcontractTaskBo taskBo:taskBoList) {
            if (EcontractTaskApplyTypeEnum.C1CONTRACT.getName().equals(taskBo.getApplyType())
                || EcontractTaskApplyTypeEnum.SETTLE.getName().equals(taskBo.getApplyType())
                || EcontractTaskApplyTypeEnum.POIFEE.getName().equals(taskBo.getApplyType())
                    || (EcontractTaskApplyTypeEnum.SUBJECT_CHANGE_SUPPLEMENT.getName().equals(taskBo.getApplyType())
                    && wmSubjectChangeSupplementEContractTempletService.judgeAddedSubjectChangeSupplementToAutoPack(customerId, wmPoiIdList, false))
                    || (EcontractTaskApplyTypeEnum.C2CONTRACT.getName().equals(taskBo.getApplyType())
                    &&hasIsPoiAgentAndHasC2Boolean)){
                taskBoMap.put(taskBo.getId(), taskBo);
            }
        }
        return taskBoMap;
    }

    public boolean hasEffectDate(Integer customerId) throws TException, WmCustomerException {
        if (wmEcontractPackRelContractCheckService.hasEffectRelContractByCustomerId(customerId)) {
            LOGGER.info("wmEcontractPackTaskCheckService 存在关联合同信息");
            return Boolean.TRUE;
        }

        if (wmEcontractPackRelContractCheckService.hasEffectRelContractC2ByCustomerId(customerId)) {
            LOGGER.info("wmEcontractPackTaskCheckService 存在关联C2合同信息");
            return Boolean.TRUE;
        }

        if (wmEcontractPackRelDeliveryCheckService.hasEffectRelDeliveryByCustomerId(customerId)) {
            LOGGER.info("wmEcontractPackTaskCheckService 存在关联配送信息");
            return Boolean.TRUE;
        }

        //女娲二期灰度客户无需判断关联结算信息
        if (!wmCustomerGrayServiceAdapter.isGrayCustomer(customerId) && wmEcontractPackRelSettleCheckService.hasEffectRelSettleByCustomerId(customerId)) {
            LOGGER.info("wmEcontractPackTaskCheckService 存在关联结算信息");
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public boolean hasZRZFirstEffectData(Integer customerId) throws TException, WmCustomerException {
        if (wmEcontractPackRelContractCheckService.hasEffectRelContractByCustomerId(customerId)) {
            LOGGER.info("wmEcontractPackTaskCheckService 存在关联合同信息");
            return Boolean.TRUE;
        }

        if (wmEcontractPackRelDeliveryCheckService.hasEffectRelDeliveryByCustomerId(customerId)) {
            LOGGER.info("wmEcontractPackTaskCheckService 存在关联配送信息");
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
