package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp;

import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.CertifyConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EstampInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class WmEcontractStampMTWrapperService implements IWmEcontractStampWrapperService {

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo, List<String> flowList) {
        //美团CA信息
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
            .setCustomerName(CertifyConstant.MEI_TUAN_CA)
            .build();
        //美团签章
        Map<String, String> estampParamMap = Maps.newHashMap();
        estampParamMap.put(TaskConstant.PDF_ESTAMP_SIGN_KEY, PdfConstant.MT_SIGNKEY);
        EstampInfoBo estampInfoBo = new EstampInfoBo.Builder()
            .setEstampMap(estampParamMap)
            .build();

        return new StageBatchInfoBo.Builder()
            .stageName(WmEcontractConstant.STAMP_MT)
            .certifyInfoBo(certifyInfoBo)
            .estampInfoBo(estampInfoBo)
            .metaFlowList(flowList)
            .build();
    }

}
