package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenAttributeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ContractorGradeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.EffectiveStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @program: scm
 * @description: 方法
 * @author: jianghuimin02
 * @create: 2020-05-22 16:17
 **/
@Slf4j
@Service
public class WmScCanteenServerService {

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScTagService wmScTagService;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    /**
     * 学校等级修改 -> 食堂等级修改
     * @param schoolBo schoolBo
     * @param wmSchoolDBIn WmSchoolDB
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void schoolUpdateToCanteen(SchoolBo schoolBo, WmSchoolDB wmSchoolDBIn) throws WmSchCantException, TException {
        int schoolId = wmSchoolDBIn.getId();
        WmCanteenDB wmCanteenDBSearch = new WmCanteenDB();
        wmCanteenDBSearch.setSchoolId(schoolId);
        wmCanteenDBSearch.setEffective((int) EffectiveStatusEnum.EFFECTIVE.getType());
        List<WmCanteenDB> wmCanteenDBS = wmCanteenMapper.selectCanteenList(wmCanteenDBSearch);
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDBS);
        log.info("校园食堂项目:学校修改是否导致食堂等级修改:查询数据:{}", JSON.toJSONString(wmCanteenDBS));
        for (WmCanteenDB wmCanteenDB : wmCanteenDBS) {
            if (wmCanteenDB.getCanteenAttribute() == CanteenAttributeEnum.SCHOOL_DIRECT.getType()) {
                wmCanteenDB.setGrade(schoolBo.getGrade());
                wmScCanteenSensitiveWordsService.writeWhenInsertOrUpdate(wmCanteenDB);
                int result = wmCanteenMapper.updateCanteen(wmCanteenDB);

                wmScTagService.editCanteenAddTag(wmCanteenDB, schoolBo.getGrade(), wmSchoolDBIn.getUserId(), wmCanteenDB.getUserName());
                log.info("校园食堂项目:学校修改是否导致食堂等级修改:更新结果:{}:更新数据:{}", result, JSON.toJSONString(wmCanteenDB));
            }
        }
    }

    /**
     * 联动修改食堂中学校的名字
     *
     * @param schoolBo schoolBo
     */
    public void linkUpdateSchoolName(SchoolBo schoolBo){
        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setSchoolId(schoolBo.getId());
        wmCanteenDB.setSchoolName(schoolBo.getSchoolName());
        wmCanteenMapper.linkUpdateSchoolName(wmCanteenDB);
        log.info("校园食堂项目:修改学校名称联动修改食堂中的学校名称:{}", JSON.toJSONString(wmCanteenDB));
    }

    /**
     * 根据承包商关联学校所属学校数量计算食堂承包商的供给分级
     * SKR承包 学校数量≥10
     * <p>
     * KR承包 2≤学校数量＜10
     * <p>
     * 食堂承包 学校数量＜2
     *
     * @param contractId 食堂承包商ID（客户ID）
     * @return com.sankuai.meituan.waimai.thrift.customer.constant.sc.ContractorGradeEnum
     */
    public int computeContractorGrade(int contractId) {
        int schoolNum = 0;
        if (contractId == 0) {
            return 0;
        }
        List<Integer> idList = new ArrayList<>();
        idList.add(contractId);
        List<WmCanteenDB> list = wmCanteenMapper.getCanteenByContractorIds(idList);
        wmScCanteenSensitiveWordsService.readWhenSelect(list);
        if (CollectionUtils.isEmpty(list)) {
            return ContractorGradeEnum.COMMON_CONTRACT.getType();
        }
        List<Integer> schoolIdList = Lists.transform(list, new Function<WmCanteenDB, Integer>() {
            @Nullable
            @Override
            public Integer apply(@Nullable WmCanteenDB input) {
                return input.getSchoolId();
            }
        });

        Set<Integer> schoolIdSet = new HashSet<>(schoolIdList);
        schoolNum = schoolIdSet.size();
        if (schoolNum < 2) {
            return ContractorGradeEnum.COMMON_CONTRACT.getType();
        } else if (schoolNum < 10) {
            return ContractorGradeEnum.KR_CONTRACT.getType();
        } else {
            return ContractorGradeEnum.SKR_CONTRACT.getType();
        }
    }

    @Deprecated
    public int computeContractorGradeNew(int cityNum){
        if(cityNum < 2){
            return ContractorGradeEnum.COMMON_CONTRACT.getType();
        }
        if(cityNum >=2 && cityNum < 5){
            return ContractorGradeEnum.KR_CONTRACT.getType();
        }
        if(cityNum >= 5){
            return ContractorGradeEnum.SKR_CONTRACT.getType();
        }
        return 0;
    }

    /**
     * 逻辑删除食堂
     * @param id 食堂的自增id（主键）
     */
    public void deleteCanteenById(int id) {
        wmCanteenMapper.deleteCanteenById(id);
        log.info("校园食堂项目:删除食堂成功:id:{}",id);
    }
}
