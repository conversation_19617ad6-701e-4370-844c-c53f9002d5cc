package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTSHWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTSHWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 配送服务合同
 */
@Service
public class WmEcontractDeliveryServiceContractApplyService extends AbstractWmEcontractApplyAdapterService {

    private static final String FLOW_DELIVERY_SERVICE_CONTRACT = EcontractTaskApplyTypeEnum.DELIVERY_SERVICE_CONTRACT.getName();

    private static List<String> flowList = Lists.newArrayList();

    private static List<String> poiStampList = Lists.newArrayList();

    private static List<String> mtStampList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_DELIVERY_SERVICE_CONTRACT);

        poiStampList.add(FLOW_DELIVERY_SERVICE_CONTRACT);

        mtStampList.add(FLOW_DELIVERY_SERVICE_CONTRACT);

        dataWrapperMap.put(FLOW_DELIVERY_SERVICE_CONTRACT, EcontractDataWrapperEnum.DELIVERY_SERVICE_CONTRACT);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAMTWrapperService wmEcontractCAMTWrapperService;

    @Resource
    private WmEcontractCAMTSHWrapperService wmEcontractCAMTSHWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Resource
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Resource
    private WmEcontractStampMTSHWrapperService wmEcontractStampMTSHWrapperService;

    String type_delivery_service_contract = "type_delivery_service_contract";

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo) throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        if (MccConfig.isDeliveryContractUseShStamp()) {
            batchInfoBoList.add(wmEcontractCAMTSHWrapperService.wrap(batchContextBo));
        } else {
            batchInfoBoList.add(wmEcontractCAMTWrapperService.wrap(batchContextBo));
        }
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, poiStampList));
        if (MccConfig.isDeliveryContractUseShStamp()) {
            batchInfoBoList.add(wmEcontractStampMTSHWrapperService.wrap(batchContextBo, mtStampList));
        } else {
            batchInfoBoList.add(wmEcontractStampMTWrapperService.wrap(batchContextBo, mtStampList));
        }

        return new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(type_delivery_service_contract)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .callBackUrl(getCallbackUrl()).econtractBatchSource(getSource(batchContextBo))
                .build();
    }
}
