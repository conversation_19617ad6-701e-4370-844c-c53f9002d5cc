package com.sankuai.meituan.waimai.customer.adapter.daocan;

import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.meituan.mtcoop.thrift.dto.*;
import com.sankuai.meituan.mtcoop.thrift.exception.TCoopException;
import com.sankuai.meituan.mtcoop.thrift.exception.TIllegalArgumentException;
import com.sankuai.meituan.mtcoop.thrift.service.CommonCoopService;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractErrorCode;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/14 16:50
 */
@Slf4j
@Service
public class CommonCoopServiceAdapter {

    private static final int SUCCESS_CODE = 200;

    @Resource
    private CommonCoopService.Iface commonCoopService;

    /**
     * 查询到餐合同链接
     *
     * @param request 请求入参
     * @return 合同链接
     * @throws WmCustomerException 异常
     */
    public String queryCoopPreviewHtml(TQueryCoopPreviewHtmlRequest request) throws WmCustomerException {
        try {
            log.info("CommonCoopServiceAdapter#queryCoopPreviewHtml, request: {}", JSON.toJSONString(request));
            TQueryCoopPreviewHtmlResponse response = commonCoopService.queryCoopPreviewHtml(request, 0);
            log.info("CommonCoopServiceAdapter#queryCoopPreviewHtml, response: {}", JSON.toJSONString(response));
            if (response == null) {
                throw new WmCustomerException(WmContractErrorCodeConstant.SYSTEM_ERROR, "查询合同链接异常");
            }
            if (response.getCode() == SUCCESS_CODE) {
                return response.getPreviewHtml();
            }
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, response.getMsg());
        } catch (WmCustomerException e) {
            throw e;
        } catch (Exception e) {
            log.error("CommonCoopServiceAdapter#queryCoopPreviewHtml, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.SYSTEM_ERROR, "查询合同链接异常");
        }
    }

    /**
     * 发起到餐合同签约
     *
     * @param request 请求入参
     * @param uid     操作人ID
     * @throws WmCustomerException 异常
     */
    public void submitCoopWithResign(TSubmitCoopWithResignRequest request, Integer uid) throws WmCustomerException {
        try {
            request.setSsoId(uid);
            log.info("CommonCoopServiceAdapter#submitCoopWithResign, request: {}", JSON.toJSONString(request));
            commonCoopService.submitCoopWithResignCoop(request);
            log.info("CommonCoopServiceAdapter#submitCoopWithResign, success");
        } catch (Exception e) {
            log.error("CommonCoopServiceAdapter#submitCoopWithResign, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "到餐发起合同异常");
        }
    }

    /**
     * 发起到餐合同签约-V2 泛化调用
     *
     * @param request
     * @param uid
     * @throws WmCustomerException
     */
    public void submitCoopWithResignV2(TSubmitCoopWithResignRequest request, Integer uid) throws WmCustomerException {
        ThriftClientProxy clientProxy = new ThriftClientProxy();
        try {
            clientProxy.setAppKey("com.sankuai.waimai.e.customer");
            clientProxy.setRemoteAppkey("com.sankuai.cos.mtcoop");
            clientProxy.setGenericServiceName("com.sankuai.meituan.mtcoop.thrift.service.CommonCoopService");
            clientProxy.setFilterByServiceName(true);
            clientProxy.setGeneric("json-common");
            clientProxy.setTimeout(10000);
            clientProxy.afterPropertiesSet();//触发初始化
            GenericService genericClient = (GenericService) clientProxy.getObject();
            List<String> thriftParams = new ArrayList<>();
            thriftParams.add(JacksonUtils.serialize(request));
            thriftParams.add(JacksonUtils.serialize(uid));
            log.info("submitCoopWithResignV2 req:{}", JSON.toJSONString(thriftParams));
            genericClient.$invoke("submitCoopWithResign", null, thriftParams);
            log.info("submitCoopWithResignV2 sucess");
        } catch (Exception e) {
            log.error("submitCoopWithResignV2 getThriftGeneric，submitCoopWithResignV2 request:{}", JSON.toJSONString(request), e);
        } finally {
            clientProxy.destroy();
        }
    }

    /**
     * 取消到餐合同签约
     *
     * @param request 请求入参
     * @param uid     操作人ID
     * @throws WmCustomerException 异常
     */
    public void cancelSignDcContract(TRevokeSignCoopListRequest request, Integer uid) throws WmCustomerException {
        try {
            log.info("CommonCoopServiceAdapter#cancelSignDcContract, request: {}", JSON.toJSONString(request));
            TCommonResponse response = commonCoopService.revokeSignCoopList(request, uid);
            log.info("CommonCoopServiceAdapter#cancelSignDcContract, response: {}", JSON.toJSONString(response));
            if (response == null) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "到餐取消签约异常");
            }
            if (response.getCode() != SUCCESS_CODE) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, response.getMsg());
            }
        } catch (WmCustomerException e) {
            throw e;
        } catch (Exception e) {
            log.error("CommonCoopServiceAdapter#cancelSignDcContract, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "到餐取消签约异常");
        }
    }

    /**
     * 到餐合同确认签约
     *
     * @param request 请求入参
     * @param uid     操作人ID
     * @throws WmCustomerException 异常
     */
    public void confirmSignDcContract(TBizSignCoopListRequest request, Integer uid) throws WmCustomerException {
        try {
            log.info("CommonCoopServiceAdapter#confirmSignDcContract, request: {}", JSON.toJSONString(request));
            TBizSignCoopListResponse response = commonCoopService.bizSignCoopList(request, uid);
            log.info("CommonCoopServiceAdapter#confirmSignDcContract, response: {}", JSON.toJSONString(response));
            if (response == null) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "到餐确认签约异常");
            }
            if (response.getCode() != SUCCESS_CODE) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, response.getMsg());
            }
        } catch (WmCustomerException e) {
            throw e;
        } catch (Exception e) {
            log.error("CommonCoopServiceAdapter#confirmSignDcContract, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "到餐确认签约异常");
        }
    }

    /**
     * 查询到餐合同信息
     *
     * @param request 入参
     * @return 合同信息
     */
    public List<CoopSignBasicInfo> queryDcContractInfo(TQuerySignCoopListRequest request) {
        try {
            log.info("CommonCoopServiceAdapter#queryDcContractInfo, request: {}", JSON.toJSONString(request));
            TQuerySignCoopListResponse response = commonCoopService.querySignCoopList(request);
            log.info("CommonCoopServiceAdapter#queryDcContractInfo, response: {}", JSON.toJSONString(response));
            if (response == null || response.getCode() != SUCCESS_CODE) {
                log.error("CommonCoopServiceAdapter#queryDcContractInfo, 查询到餐合同信息异常");
                return Collections.emptyList();
            }
            return response.getCoopSignBasicInfoList();
        } catch (Exception e) {
            log.error("CommonCoopServiceAdapter#queryDcContractInfo, 查询到餐合同信息异常, error", e);
            return Collections.emptyList();
        }
    }

}
