package com.sankuai.meituan.waimai.customer.util;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-3-8.
 */
public class SetUtil {

    /**
     * set1比set2多的
     * @param set1
     * @param set2
     * @param <T>
     * @return
     */
    public static <T> Set<T> genAddSet(Set<T> set1, Set<T> set2) {
        Set<T> addSet = new HashSet<>();
        addSet.addAll(set1);
        addSet.removeAll(set2);
        return addSet;
    }

    /**
     * set2比set1多的
     * @param set1
     * @param set2
     * @param <T>
     * @return
     */
    public static <T> Set<T> gendeleteSet(Set<T> set1, Set<T> set2) {
        Set<T> deleteSet = new HashSet<>();
        deleteSet.addAll(set2);
        deleteSet.removeAll(set1);
        return deleteSet;
    }

    /**
     * set1 set2 共有
     * @param set1
     * @param set2
     * @param <T>
     * @return
     */
    public static <T> Set<T> genCommonSet(Set<T> set1, Set<T> set2) {
        Set<T> commonSet = new HashSet<>();
        commonSet.addAll(set1);
        commonSet.retainAll(set2);
        return commonSet;
    }
}
