package com.sankuai.meituan.waimai.customer.ddd.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.meituan.mtrace.thread.TraceCallable;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.common.CommonConst;
import com.sankuai.meituan.waimai.customer.util.common.RetryUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiTransUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.util.StringUtil;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;

/**
 * Created by lihaowei on 16-8-18.
 */
@Service
public class WmPoiQueryAdapter {

    private static Logger log = LoggerFactory.getLogger(WmPoiQueryAdapter.class);

    /**
     * IO密集型，经验配置， 2N+1 个线程，最大线程数可设置为核心线程数的3倍。
     */
    private final static ExecutorService handleService = new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(9, 27,
        60L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue(1024), new ThreadPoolExecutor.AbortPolicy()));

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    static final ImmutableSet<String> WM_POI_FIELDS_AGGRE_OLD_WMPOI = ImmutableSet.of(
            WM_POI_FIELD_WM_POI_ID,
            WM_POI_FIELD_CITY_ID,
            WM_POI_FIELD_NAME,
            WM_POI_FIELD_ADDRESS,
            WM_POI_FIELD_ECOM_ACCOUNT_PHONENUM,
            WM_POI_FIELD_AOR_ID,
            WM_POI_FIELD_LATITUDE,
            WM_POI_FIELD_LONGITUDE,
            WM_POI_FIELD_OWNER_UID,
            WM_POI_FIELD_VALID,
            WM_POI_FIELD_SUPPORT_PAY,
            WM_POI_FIELD_BATCH_AUDIT_COMMITTED,
            WM_POI_FIELD_SOURCE,
            WM_POI_FIELD_AGENT_ID,
            WM_POI_FIELD_LABEL_IDS,
            WM_POI_FIELD_CONTACTS,
            WM_POI_FIELD_WM_LOGISTICS_IDS,   //配送方式
            WM_POI_FIELD_SELF_PICKUP_SUPPORT,
            WM_POI_FIELD_BRAND_ID,
            WM_POI_FIELD_BRAND_TYPE,
            WM_POI_FIELD_OWNER_TYPE,
            WM_POI_FIELD_SUB_WM_POI_TYPE

    );

    /**
     * 分页查询WmPoiDomain
     */
    public List<WmPoiDomain> pageGetWmPoiByWmPoiIdList(List<Long> wmPoiIdList) throws WmCustomerException {
        List<List<Long>> pageList = Lists.partition(wmPoiIdList, 300);
        List<WmPoiDomain> wmPoiDomainList = Lists.newArrayList();
        for (List<Long> page : pageList) {
            wmPoiDomainList.addAll(mgetWmPoiByWmPoiIdList(page));
        }
        return wmPoiDomainList;
    }

    /**
     * 批量查询商家
     */

    public List<WmPoiDomain> mgetWmPoiByWmPoiIdList(List<Long> wmPoiIdList) throws WmCustomerException {
        List<WmPoiDomain> wmPoiDomainList;
        try {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                    wmPoiIdList, WM_POI_FIELDS_AGGRE_OLD_WMPOI);
            wmPoiDomainList = WmPoiTransUtil.batchTransWmPoiAggre(wmPoiAggreList);
        } catch (TException | WmServerException e) {
            log.error("调用poi服务化接口失败，wmPoiIdList = " + StringUtil.list2String(wmPoiIdList, ","), e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "网络异常");
        }
        return wmPoiDomainList;
    }

    public WmPoiAggre getWmPoiAggreByWmPoiId(long wmPoiId) throws WmCustomerException {
        try {
            if (wmPoiId <= 0L) {
                return null;
            }
            WmPoiAggre wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(wmPoiId, WM_POI_FIELDS_AGGRE_OLD_WMPOI);
            if (wmPoiAggre == null) {
                throw new WmCustomerException(-1, "无门店信息，wmPoiId:" + wmPoiId);
            }
            return wmPoiAggre;
        } catch (WmServerException e) {
            log.error("获取aggre信息异常 msg:{}", e.getMsg(), e);
            throw new WmCustomerException(-1, e.getMsg());
        } catch (TException e) {
            log.error("获取aggre信息异常 msg:{}", e.getMessage(), e);
            throw new WmCustomerException(-1, e.getMessage());
        }
    }

    /**
     * 查询单店商家
     */
    public WmPoiDomain getWmPoiById(long wmPoiId)
            throws WmCustomerException {
        try {
            log.info("wmPoiId:[{}]", wmPoiId);
            WmPoiAggre wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(wmPoiId, WM_POI_FIELDS_AGGRE_OLD_WMPOI);
            log.info("wmPoiAggre getWmPoiById:[{}]", JSON.toJSONString(wmPoiAggre));
            return WmPoiTransUtil.transWmPoiAggre(wmPoiAggre);
        } catch (TException | WmServerException e) {
            log.error("调用poi服务化查询门店id失败 wmPoiId = " + wmPoiId, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "网络异常");
        }
    }


    /**
     * 查询门店信息
     *
     * @param wmPoiIdList 门店ID集合
     * @param field       门店字段
     * @return 返回门店list
     */
    public List<WmPoiAggre> queryPoiAggre(List<Long> wmPoiIdList, Set<String> field) {
        log.info("查询门店getWmPoiAggrem入参, wmPoiIdList =「{}」,field = {} ", wmPoiIdList, field);

        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Lists.newArrayList();
        }
        List<WmPoiAggre> result = new ArrayList<>();
        List<List<Long>> partitionList = com.google.common.collect.Lists.partition(wmPoiIdList,
            MccConfig.getPoiQueryPartitionLimit());

        final CountDownLatch countDownLatch = new CountDownLatch(partitionList.size());
        List<Future<List<WmPoiAggre>>> futureList = Lists.newArrayList();

        for (List<Long> partition : partitionList) {
            try {
                futureList.add(handleService.submit(new TraceCallable<List<WmPoiAggre>>(new Callable<List<WmPoiAggre>>() {
                    @Override
                    public List<WmPoiAggre> call() throws Exception {
                        log.info("mgetWmPoiAggreByWmPoiIdWithSpecificField参数{}", partition);
                        //如果response返回的code不是成功code，则进行重试
                        List<WmPoiAggre> wmPoiAggres = RetryUtil.call(CommonConst.RETRY_TIME,
                            true,
                            Objects::isNull,
                            () -> wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(partition, field));
                        countDownLatch.countDown();
                        log.info("mgetWmPoiAggreByWmPoiIdWithSpecificField返回，wmPoiAggres={}",
                            JSON.toJSONString(wmPoiAggres));
                        return wmPoiAggres;
                    }
                })));
            } catch (Exception e) {
                log.error("获取门店信息异常，partition = {}", JSON.toJSONString(partition), e);
            }
        }
        boolean cdlRes = false;
        try {
            cdlRes = countDownLatch.await(1, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("await异常", e);
        }
        log.info("cdlRes =  {}", cdlRes);

        for (Future<List<WmPoiAggre>> future : futureList) {
            List<WmPoiAggre> res = new ArrayList<>();
            try {
                res = future.get(500, TimeUnit.MILLISECONDS);
                log.info("res={}", res);
            } catch (Exception ex) {
                log.info("获取future异常:", ex);
            }
            if (CollectionUtils.isNotEmpty(res)) {
                result.addAll(res);
            }
        }

        log.info("查询门店getWmPoiAggrem出参,result = {} ", JSON.toJSONString(result));
        return result;
    }

    /**
     * 查询门店信息
     *
     * @param wmPoiIdList 门店ID集合
     * @param field       门店字段
     * @return
     */
    public List<WmPoiAggre> getWmPoiAggre(List<Long> wmPoiIdList, Set<String> field) {
        log.info("查询门店getWmPoiAggrem入参, wmPoiIdList =「{}」,field = {} ", wmPoiIdList, field);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Lists.newArrayList();
        }
        List<WmPoiAggre> result = new ArrayList<>();
        List<List<Long>> partitionList = com.google.common.collect.Lists.partition(wmPoiIdList, 50);
        for (List<Long> partition : partitionList) {
            try {
                List<WmPoiAggre> wmPoiAggres = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                    partition, field);
                result.addAll(wmPoiAggres);
            } catch (WmServerException e) {
                log.error("获取门店信息失败，wmPoiId = {}, e.code = {}, e.msg = {}", JSON.toJSONString(partition), e.code, e.msg, e);
            } catch (TException e) {
                log.error("获取门店信息超时失败，wmPoiId = {}", JSON.toJSONString(partition), e);
            }

        }
        log.info("查询门店getWmPoiAggrem出参,result = {} ", result);
        return result;
    }

    /**
     * 查询单个门店信息
     *
     * @param wmPoiId
     * @param field
     * @return
     */
    public WmPoiAggre getWmPoiAggreField(Long wmPoiId, Set<String> field) {
        if (wmPoiId == null) {
            return null;
        }
        List<Long> idList = new ArrayList<>();
        idList.add(wmPoiId);
        try {
            List<WmPoiAggre> wmPoiAggres = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                    idList, field);
            if (CollectionUtils.isNotEmpty(wmPoiAggres)) {
                return wmPoiAggres.get(0);
            }
        } catch (WmServerException e) {
            log.error("获取门店信息失败，wmPoiId = {}, e.code = {}, e.msg = {}", wmPoiId, e.code, e.msg, e);
        } catch (TException e) {
            log.error("获取门店信息超时失败，wmPoiId = {}", wmPoiId, e);
        }
        return null;
    }

    /**
     * 查询门店信息
     *
     * @param wmPoiId 门店ID集合
     * @param field   门店字段
     * @return
     */
    public WmPoiAggre getWmPoiAggre(Long wmPoiId, Set<String> field) {
        if (wmPoiId == null) {
            return null;
        }
        try {
            log.info("[getWmPoiAggre] 查询入参数：wmPoiId={}，field={}", wmPoiId, JSON.toJSONString(field));
            WmPoiAggre wmPoiAggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(wmPoiId, field);
            log.info("[getWmPoiAggre] 查询结果={}", JSON.toJSONString(wmPoiAggre));
            return wmPoiAggre;
        } catch (WmServerException e) {
            log.error("获取门店信息失败，wmPoiId = {}, e.code = {}, e.msg = {}", wmPoiId, e.code, e.msg, e);
        } catch (TException e) {
            log.error("获取门店信息超时失败，wmPoiId = {}", JSON.toJSONString(wmPoiId), e);
        }
        return null;
    }


    /**
     * 查询门店信息
     *
     * @param wmPoiIdList 门店ID集合
     * @param field       门店字段
     * @return
     */
    public List<WmPoiAggre> getWmPoiAggreList(List<Long> wmPoiIdList, Set<String> field) throws WmCustomerException {
        log.info("查询门店getWmPoiAggreList入参, wmPoiIdList =「{}」,field = {} ", wmPoiIdList, field);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Lists.newArrayList();
        }
        List<WmPoiAggre> result = new ArrayList<>();
        List<List<Long>> partitionList = com.google.common.collect.Lists.partition(wmPoiIdList, 50);
        for (List<Long> partition : partitionList) {
            try {
                List<WmPoiAggre> wmPoiAggres = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                        partition, field);
                result.addAll(wmPoiAggres);
            } catch (WmServerException e) {
                log.error("获取门店信息失败，wmPoiId = {}, e.code = {}, e.msg = {}", JSON.toJSONString(partition), e.code, e.msg, e);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "获取门店信息异常:" + e.getMessage());
            } catch (TException e) {
                log.error("获取门店信息超时失败，wmPoiId = {}", JSON.toJSONString(partition), e);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "获取门店信息异常");
            }
        }
        log.info("查询门店getWmPoiAggreList出参,result = {} ", result);
        return result;
    }

    /**
     * 查询门店信息Map
     * @param wmPoiIdList 门店ID列表
     * @param fieldSet 属性set
     * @return Map<Long, WmPoiAggre> key->wmPoiId val->WmPoiAggre
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<Long, WmPoiAggre> getWmPoiAggreMap(List<Long> wmPoiIdList, Set<String> fieldSet) throws WmSchCantException {
        try {
            log.info("[WmPoiQueryAdapter.getWmPoiAggreMap] wmPoiIdList = {}, fieldSet = {}", JSONObject.toJSONString(wmPoiIdList), JSONObject.toJSONString(fieldSet));
            if (CollectionUtils.isEmpty(wmPoiIdList)) {
                return new HashMap<>();
            }

            Map<Long, WmPoiAggre> resultMap = new HashMap<>();
            List<WmPoiAggre> wmPoiAggreList = getWmPoiAggreList(wmPoiIdList, fieldSet);
            if (CollectionUtils.isEmpty(wmPoiAggreList)) {
                return resultMap;
            }

            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                resultMap.put(wmPoiAggre.getWm_poi_id(), wmPoiAggre);
            }
            return resultMap;
        } catch (Exception e) {
            log.error("[WmPoiQueryAdapter.getWmPoiAggreMap] Exception. wmPoiIdList = {}, fieldSet = {}", JSONObject.toJSONString(wmPoiIdList), JSONObject.toJSONString(fieldSet), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "获取门店信息异常");
        }
    }

}
