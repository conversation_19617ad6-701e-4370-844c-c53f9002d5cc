package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.Data;

import java.util.List;

/**
 * 批量修改学校责任人BO
 * <AUTHOR>
 * @date 2023/09/17
 * @email <EMAIL>
 */
@Data
public class WmSchoolUpdateResponsiblePersonBO {
    /**
     * 学校主键ID列表
     */
    private List<Integer> idList;
    /**
     * 要更新的学校责任人MIS
     */
    private String responsiblePerson;
    /**
     * 要更新的学校责任人UID
     */
    private Integer responsibleUid;
    /**
     * 操作用户UID
     */
    private Integer userId;
    /**
     * 操作用户名称
     */
    private String userName;

    public static final class Builder {

        private List<Integer> idList;

        private String responsiblePerson;

        private Integer responsibleUid;

        private Integer userId;

        private String userName;

        public Builder() {
        }

        public WmSchoolUpdateResponsiblePersonBO.Builder idList(List<Integer> val) {
            this.idList = val;
            return this;
        }

        public WmSchoolUpdateResponsiblePersonBO.Builder responsiblePerson(String val) {
            this.responsiblePerson = val;
            return this;
        }

        public WmSchoolUpdateResponsiblePersonBO.Builder responsibleUid(Integer val) {
            this.responsibleUid = val;
            return this;
        }

        public WmSchoolUpdateResponsiblePersonBO.Builder userId(Integer val) {
            this.userId = val;
            return this;
        }

        public WmSchoolUpdateResponsiblePersonBO.Builder userName(String val) {
            this.userName = val;
            return this;
        }

        public WmSchoolUpdateResponsiblePersonBO build() {
            WmSchoolUpdateResponsiblePersonBO bo = new WmSchoolUpdateResponsiblePersonBO();
            bo.setIdList(this.idList);
            bo.setUserId(this.userId);
            bo.setUserName(this.userName);
            bo.setResponsiblePerson(this.responsiblePerson);
            bo.setResponsibleUid(this.responsibleUid);
            return bo;
        }
    }

}
