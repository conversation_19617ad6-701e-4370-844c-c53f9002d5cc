package com.sankuai.meituan.waimai.customer.constant.sign;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2023/4/27 1:26 PM
 */
@AllArgsConstructor
@NoArgsConstructor
public enum SingleMultiPoiTypeEnum {
    /**
     * 单店、多店类别
     */

    SINGLE_POI(1, "单店"),
    MULTI_POI(2, "多店");


    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
