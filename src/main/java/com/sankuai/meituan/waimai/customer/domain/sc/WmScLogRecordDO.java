package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.Data;

@Data
public class WmScLogRecordDO {

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 快照日志
     */
    private String logMessage;

    /**
     * 变更字段以及原值
     */
    private String logDiffValue;

    /**
     * 日志类别
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScLogCategory}
     */
    private Integer logCategory;

    /**
     * 业务操作类型
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScLogOperateType}
     */
    private String operateType;

    /**
     * 操作人ID
     */
    private Long opId;

    /**
     * 操作人名字
     */
    private String opName;

    /**
     * 创建时间
     */
    private Integer ctime;


}