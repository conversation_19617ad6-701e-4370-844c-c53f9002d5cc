package com.sankuai.meituan.waimai.customer.service.sign.pack;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmSubjectChangeSupplementEContractTempletService;
import com.sankuai.meituan.waimai.customer.domain.WmEContractSignBaseDB;
import com.sankuai.meituan.waimai.customer.service.common.RepeatObjectService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.ManualPackConditionService;
import com.sankuai.meituan.waimai.customer.service.sign.pack.check.WmEcontractPackPoiStateCheckService;
import com.sankuai.meituan.waimai.customer.service.sign.pack.check.WmEcontractPackTaskCheckService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBaseInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBaseStateContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiStateJudgeResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * base信息提交打包成待生成batch信息的结构
 * EcontractBaseStateContextBo:
 * 待打包Map:Map<Long, List<Long>>
 *
 */
@Service
@Slf4j
public class WmEcontractBasePackService {

    public static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractBasePackService.class);

    @Resource
    private WmEcontractTaskBizService wmEcontractTaskBizService;

    @Resource
    private WmEcontractPackTaskCheckService wmEcontractPackTaskCheckService;

    @Resource
    private WmEcontractPackPoiStateCheckService wmEcontractPackPoiStateCheckService;

    @Resource
    private WmCustomerPoiService wmCustomerPoiService;

    @Resource
    private ManualPackConditionService manualPackConditionService;

    @Autowired
    private WmSubjectChangeSupplementEContractTempletService wmSubjectChangeSupplementEContractTempletService;


    public EcontractBaseStateContextBo pack(WmEContractSignBaseDB baseDB) throws WmCustomerException, TException {
        EcontractBaseStateContextBo baseStateContextBo = new EcontractBaseStateContextBo();
        /*客户信息*/
        baseStateContextBo.setCustomerId(baseDB.getCustomerId());
        /*客户信息*/
        initBase(baseDB, baseStateContextBo);
        /*初始化task*/
        initTask(baseDB, baseStateContextBo);
        /*初始化打包后的结果*/
        initBatch(baseDB, baseStateContextBo);
        return baseStateContextBo;
    }

    /**
     * 初始化base信息
     */
    private EcontractBaseStateContextBo initBase(WmEContractSignBaseDB baseDB, EcontractBaseStateContextBo baseStateContextBo) {
        EcontractBaseInfoBo baseInfoBo = JSON.parseObject(baseDB.getBaseContext(), EcontractBaseInfoBo.class);
        baseStateContextBo.setCustomerInfoBo(baseInfoBo.getCustomerInfoBo());
        baseStateContextBo.setKpBo(baseInfoBo.getKpBo());
        return baseStateContextBo;
    }

    /**
     * 初始化任务信息
     */
    private EcontractBaseStateContextBo initTask(WmEContractSignBaseDB baseDB, EcontractBaseStateContextBo baseStateContextBo) {
        List<EcontractTaskBo> taskBoList = wmEcontractTaskBizService.getByCustomerIdAndState(baseDB.getCustomerId(), EcontractTaskStateEnum.HOLDING.getName());
        if (CollectionUtils.isEmpty(taskBoList)) {
            return baseStateContextBo;
        }
        Map<Long, List<Long>> manualTaskMap = baseStateContextBo.getManualTaskMap();
        Set<Long> manualTaskSet = baseStateContextBo.getManualTaskSet();
        List<Long> temp = null;
        for (EcontractTaskBo taskBo:taskBoList) {
            if(taskBo.getManualBatchId()!=null && taskBo.getManualBatchId()>0){
                temp = manualTaskMap.get(taskBo.getManualBatchId());
                if(temp == null){
                    manualTaskMap.put(taskBo.getManualBatchId(),Lists.newArrayList(taskBo.getId()));
                }else{
                    temp.add(taskBo.getId());
                }
                manualTaskSet.add(taskBo.getId());
            }
            baseStateContextBo.getTaskIdAndTaskMap().put(taskBo.getId(), taskBo);
        }
        log.info("initTask baseStateContextBo:[{}]",JSON.toJSONString(baseStateContextBo));
        return baseStateContextBo;
    }

    /**
     * 初始化batch信息
     */
    private EcontractBaseStateContextBo initBatch(WmEContractSignBaseDB baseDB, EcontractBaseStateContextBo baseStateContextBo)
        throws TException, WmCustomerException {
        if (wmCustomerPoiService.hasMultiPoi(baseDB.getCustomerId())) {
            return initMultiPoiBatch(baseDB, baseStateContextBo);
        } else {
            return initSingleBatch(baseDB, baseStateContextBo);
        }
    }

    /**
     * 单店初始化打包信息
     * 1.如果是多店任务则不打包
     *
     * 首先：处理不需要打包的任务
     * 其次
     */
    private EcontractBaseStateContextBo initSingleBatch(WmEContractSignBaseDB baseDB, EcontractBaseStateContextBo baseStateContextBo)
        throws WmCustomerException, TException {
        boolean hasEffectData = wmEcontractPackTaskCheckService.hasEffectDate(baseDB.getCustomerId());
        if (hasEffectData) {
            return initApproved(baseDB, baseStateContextBo);
        }
        List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(baseDB.getCustomerId());
        baseStateContextBo.setHasIsPoiAgentAndHasC2Boolean(wmCustomerPoiService.isSinglePoiAgentAndHasC2Boolean(baseDB.getCustomerId()));
        baseStateContextBo.setHasSubjectChangeSupplement(wmSubjectChangeSupplementEContractTempletService.judgeAddedSubjectChangeSupplementToAutoPack(baseDB.getCustomerId(), wmPoiIdList, hasEffectData));
        //非打包可处理的任务
        Map<Long, EcontractTaskBo> taskBoMap = wmEcontractPackTaskCheckService.getEffectHoldingTaskByCustomerId
                (baseDB.getCustomerId(),baseStateContextBo.getHasIsPoiAgentAndHasC2Boolean());
        log.info("taskBoMap:[{}]",JSON.toJSONString(taskBoMap));
        List<List<Long>> unPackageTaskList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(taskBoMap)) {
            for (Map.Entry<Long, EcontractTaskBo> entry : taskBoMap.entrySet()) {
                baseStateContextBo.getBatchTaskList().add(Lists.newArrayList(entry.getKey()));
                unPackageTaskList.add(Lists.newArrayList(entry.getKey()));
            }
        }


        Map<Long, EcontractTaskBo> cspTaskBoMap = wmEcontractPackTaskCheckService.getEffectCSPTaskByCustomerId(baseDB
                .getCustomerId(),baseStateContextBo.getHasIsPoiAgentAndHasC2Boolean());
        log.info("cspTaskBoMap:[{}]",JSON.toJSONString(cspTaskBoMap));
        //需要打包的可处理任务
        EcontractPoiStateJudgeResultBo resultBo = wmEcontractPackPoiStateCheckService.getBatchedPoiByCustomerId
                (baseDB.getCustomerId(),baseStateContextBo.getHasIsPoiAgentAndHasC2Boolean());
        log.info("resultBo:[{}]",JSON.toJSONString(resultBo));
        //存在生效数据，则直接拆成不同的任务
        if (resultBo.isHasApproved() || wmEcontractPackTaskCheckService.hasEffectDate(baseDB.getCustomerId())) {
            if (MapUtils.isNotEmpty(cspTaskBoMap)) {
                for (Map.Entry<Long, EcontractTaskBo> entry:cspTaskBoMap.entrySet()) {
                    baseStateContextBo.getBatchTaskList().add(Lists.newArrayList(entry.getKey()));
                }
            }
        //不存在生效的数据，只处理打包的任务，其他任务继续等待打包
        } else {
            if (resultBo.isBatchSuccess()) {
                //优先使用multiBatchedTaskList
                if (CollectionUtils.isNotEmpty(resultBo.getMultiBatchedTaskList())) {
                    baseStateContextBo
                        .setBatchTaskList(resultBo.getMultiBatchedTaskList());
                    if(CollectionUtils.isNotEmpty(unPackageTaskList)){
                        baseStateContextBo.getBatchTaskList().addAll(unPackageTaskList);
                    }
                } else {
                    baseStateContextBo.getBatchTaskList().add(resultBo.getBatchedTaskList());
                }
            }
        }
        log.info("initSingleBatch baseStateContextBo:[{}]",JSON.toJSONString(baseStateContextBo));
        return baseStateContextBo;
    }

    /**
     * 初始化已经存在生效数据的任务
     */
    private EcontractBaseStateContextBo initApproved(WmEContractSignBaseDB baseDB, EcontractBaseStateContextBo baseStateContextBo) {
        return initNotBatch(baseDB, baseStateContextBo);
    }

    /**
     * 初始化多店任务
     */
    private EcontractBaseStateContextBo initMultiPoiBatch(WmEContractSignBaseDB baseDB, EcontractBaseStateContextBo baseStateContextBo) {
        return initNotBatch(baseDB, baseStateContextBo);
    }

    /**
     * 初始化不需要打包任务的Batch信息
     */
    private EcontractBaseStateContextBo initNotBatch(WmEContractSignBaseDB baseDB, EcontractBaseStateContextBo baseStateContextBo) {

        Map<Long, EcontractTaskBo> taskIdAndTaskMap = baseStateContextBo.getTaskIdAndTaskMap();
        Map<Long, List<Long>> manualTaskMap = baseStateContextBo.getManualTaskMap();
        List<List<Long>> batchTaskList = baseStateContextBo.getBatchTaskList();
        Set<Long> manualTaskSet = baseStateContextBo.getManualTaskSet();
        if(MapUtils.isEmpty(taskIdAndTaskMap)){
            return baseStateContextBo;
        }

        for(Entry<Long, EcontractTaskBo> temp : taskIdAndTaskMap.entrySet()){
            if(!manualTaskSet.contains(temp.getKey())){
                batchTaskList.add(Lists.newArrayList(temp.getValue().getId()));
            }
        }

        for(Entry<Long, List<Long>> temp : manualTaskMap.entrySet()){
            //batchId各模块状态已具备打包条件
            if(manualPackConditionService.isAllNeedPackModuleReached(temp.getKey())){
                batchTaskList.add(temp.getValue());
            }
        }
        log.info("initNotBatch batchTaskList:{}",JSON.toJSONString(batchTaskList));
        return baseStateContextBo;
    }

}
