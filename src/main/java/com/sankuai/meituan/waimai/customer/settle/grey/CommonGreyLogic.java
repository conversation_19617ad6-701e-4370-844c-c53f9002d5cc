package com.sankuai.meituan.waimai.customer.settle.grey;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.mtauth.manager.WmAdminDataAuthManager;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-05-20 15:43
 * Email: <EMAIL>
 * Desc:
 */
@Service
@Slf4j
public class CommonGreyLogic {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmAdminDataAuthManager wmAdminDataAuthManager;

    public boolean isGrey(int wmCustomerId, int opUid, Map<String, String> configMap) {
        if (wmCustomerId == 0 && opUid == 0) {
            return false;
        }
        //customerId维度灰度
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(configMap.get("customerId"))) {
            String greyForcustomerId = ConfigUtilAdapter
//                    .getString("settle_module_ddd_grey_customerId");
                    .getString(configMap.get("customerId"));
            if (!StringUtils.isEmpty(greyForcustomerId)) {
                String[] splitGreyForcustomerId = greyForcustomerId.split(",|，");
                List<Integer> orgList = Lists.newArrayList(
                        Lists.transform(Lists.newArrayList(splitGreyForcustomerId), new Function<String, Integer>() {
                            @Nullable
                            @Override
                            public Integer apply(@Nullable String input) {
                                return Integer.valueOf(input);
                            }
                        }));
                if (orgList.contains(wmCustomerId)) {
                    log.info("routeDDDGrey#true");
                    return true;
                }
            }
        } else {
            return false;
        }

        //优先使用入参操作人,无操作人信息时使用客户负责人信息
        Integer uid = null;
        if (opUid != 0) {
            uid = opUid;
        } else {
            Integer ownerUid = wmCustomerService.selectCustomerById4Grey(wmCustomerId);
            if (ownerUid == null || ownerUid == 0) {
                return false;
            }
            uid = ownerUid;
        }

        boolean isHq = wmAdminDataAuthManager.isHQ(uid);

        //总部权限使用开关
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(configMap.get("isHq"))) {
            // if (ConfigUtilAdapter.getBoolean("settle_module_ddd_grey_isHq", false) && isHq) {
            if (ConfigUtilAdapter.getBoolean(configMap.get("isHq"), false) && isHq) {
                log.info("routeDDDGrey#true");
                return true;
            }
            if (isHq) {
                return false;
            }
        } else {
            return false;
        }

        //BD权限开关
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(configMap.get("ordId"))){
            String canUseManualPackOrgId = com.sankuai.meituan.util.ConfigUtilAdapter
//                    .getString("settle_module_ddd_grey_OrgId");
                    .getString(configMap.get("ordId"));
            if (StringUtils.isEmpty(canUseManualPackOrgId)) {
                return false;
            }

            List<Integer> orgIds = wmAdminDataAuthManager
                    .getOrgIds(uid, WmOrgConstant.OrgType.WM_ORG_CITY);//总部权限会查出所有的外卖城市
            if (CollectionUtils.isEmpty(orgIds)) {
                return false;
            }

            String[] orgString = canUseManualPackOrgId.split(",|，");
            List<Integer> orgList = Lists.newArrayList(
                    Lists.transform(Lists.newArrayList(orgString), new Function<String, Integer>() {
                        @Nullable
                        @Override
                        public Integer apply(@Nullable String input) {
                            return Integer.valueOf(input);
                        }
                    }));

            boolean result = CollectionUtils.containsAny(orgList, orgIds);
            if (result) {
                log.info("routeDDDGrey#true");
            }
            return result;
        }else{
            return false;
        }
    }
}
