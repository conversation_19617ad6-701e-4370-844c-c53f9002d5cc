package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.aspect.TairLock;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.TairLockGrayPercentKeyEnum;
import com.sankuai.meituan.waimai.customer.constant.TairLockGroup;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.*;
import com.sankuai.meituan.waimai.customer.dao.*;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.*;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiSwitchTaskStatusEnum;
import com.sankuai.meituan.waimai.poibizflow.constant.customerSwitch.*;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.*;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.mq.CustomerSwitchCancelNotifyMsg;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.mq.CustomerSwitchResultMsg;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerSmsTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerSwitchDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.FromCustomerSwitchResultMsg;
import com.sankuai.meituan.waimai.customer.mq.service.CustomerSwitchSendService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiSwitchTaskTypeEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.mq.CustomerSwitchNotifyMsg;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.mq.ToCustomerSwitchConfirmSuccMsg;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerForceUnbindUnconfirmDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.CustomerUnBindWmPoiTaskSearchParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractFoodcityPoiTableBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiControl;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiFlowlineQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WmCustomerSwitchService {

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Autowired
    private WmEmployService.Iface wmEmployService;

    @Autowired
    private WmPoiFlowlineQueryThriftService.Iface wmPoiFlowlineQueryThriftService;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private CustomerSwitchSendService customerSwitchSendService;

    @Autowired
    private WmPoiSwitchThriftService wmPoiSwitchThriftService;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    Splitter splitter = Splitter.on(CustomerConstants.SPLIT_SYMBOL).trimResults();

    @Autowired
    private WmCustomerPoiOplogMapper wmCustomerPoiOplogMapper;

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    /**
     * 强制解绑-客户切换不下线列表-批量强制解绑
     *
     * @param data
     * @throws TException
     * @throws WmCustomerException
     */
    @TairLock(group = TairLockGroup.OLD_CUSTOMER_CONFIRM_OPERATE_SWITCH, seedExp = "data.taskId")
    public void forceUnbindUnconfirm(WmCustomerForceUnbindUnconfirmDTO data, Map<Long, Integer> poiAndTaskUnBindMaps)
            throws TException, WmCustomerException {
        log.info("forceUnbindUnconfirm,data={}", JSONObject.toJSONString(data));
        boolean auth = upmAuthCheckService.hasRolePermission(data.getOpUid(), CustomerRoleTypeEnum.FROMCUSTOMER_CONFRIMUNBIND_AUTO_ROLE.getCode());
        if (!auth) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "没有客户切换不下线任务原客户强制解绑权限");
        }
        try {
            SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskId(data.getTaskId(), data.getOpUid(), data.getOpName());
            if (switchTaskBo == null || switchTaskBo.getFromCustomerSwitchStatus() != FromCustomerSwitchStatusEnum.CONFRIMING) {
                log.warn("forceUnbindUnconfirm 任务不合法 switchTaskBo={}", JSONObject.toJSONString(switchTaskBo));
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "仅原客户确认解绑状态为确认中的任务可操作强制解绑");
            }
        } catch (WmPoiBizException e) {
            log.error("forceUnbindUnconfirm getSwitchTaskByTaskId失败 data={}", JSONObject.toJSONString(data), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, e.getMsg());
        }

        int fromCustomerId = data.getFromCustomerId();
        List<Long> wmPoiIdList = data.getWmPoiIdList();
        /** 1.原客户未签约任务取消 **/
        forceUnbindSign(fromCustomerId, wmPoiIdList);
        /** 2.原客户确认成功 **/
        fromCustomerConfirmSucc(new FromCustomerConfirmVo(data.getTaskId(), fromCustomerId, data.getToCustomerId(), data.getWmPoiIdList(), data.getOpUid(), data.getOpName(), data.getStepVersion()), poiAndTaskUnBindMaps);
        /** 3.记录操作日志 **/
        wmCustomerService.insertCustomerOpLog(fromCustomerId, data.getOpUid(),
                String.format(CustomerConstants.OPNAME_TEMPLATE_FORCE_UNBIND_TO_UNCONFIRM_FOR_CUS_SWITCH, data.getOpName()), WmCustomerOplogBo.OpType.CHANGESTATUS,
                String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_FORCE_UNBIND_TO_UNCONFIRM_FOR_CUS_SWITCH, StringUtils.join(wmPoiIdList, CustomerConstants.SPLIT_SYMBOL)), "");
    }

    /**
     * 进行原客户门店解绑确认处理
     *
     * @param msg
     */
    @TairLock(group = TairLockGroup.OLD_CUSTOMER_CONFIRM_START, seedExp = "msg.taskId", grayKey = "msg.taskId", grayPercentKey = TairLockGrayPercentKeyEnum.OLD_CUSTOMER_CONFIRM_START)
    public void fromCustomerConfirm(CustomerSwitchNotifyMsg msg) {
        log.info("fromCustomerConfirm,msg={}", JSONObject.toJSONString(msg));
        try {
            SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskIdRT(msg.getTaskId(), msg.getOpUid(), msg.getOpName());
            if (switchTaskBo == null || switchTaskBo.getFromCustomerSwitchStatus() == FromCustomerSwitchStatusEnum.CONFRIMED
                    || switchTaskBo.getFromCustomerSwitchStatus() == FromCustomerSwitchStatusEnum.CONFRIM_CANCEL) {
                log.error("fromCustomerConfirm,msg={},未找到对应的切换任务", JSONObject.toJSONString(msg));
                return;
            }
            if (msg.getTaskId() % 100 < MccCustomerConfig.oldCustomerStartCheckGrayPercent()) {
                boolean result = hasFromCusUnbindSignTask(msg);
                if (result) {
                    log.warn("fromCustomerConfirm hasFromCusUnbindSignTask不通过 msg={}", JSONObject.toJSONString(msg));
                    return;
                }
            }

            int fromCustomerId = Integer.valueOf(msg.getFromBizId());
            int toCustomerId = Integer.valueOf(msg.getToBizId());

            WmCustomerDB fromCustomer = wmCustomerService.selectCustomerById(fromCustomerId);
            if (fromCustomer == null) {
                fromCustomer = wmCustomerService.selectCustomerByIdRT(fromCustomerId);
            }

            //创建原客户与门店解绑任务
            CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                    .taskSceneType(CustomerTaskSceneType.CUSTOMER_SWITCH.getCode())
                    .taskType(CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode())
                    .opUserId(msg.getOpUid()).opUserName(msg.getOpName())
                    .opSource(CustomerTaskSourceEnum.CUSTOMER_SWITCH.getCode())
                    .opDetailSource(CustomerTaskDetailSourceEnum.CUSTOMER_SWITCH.getDesc())
                    .opSystem(CustomerTaskOpSystemEnum.CUSTOMER_SWITCH_SYS.getDesc())
                    .bizTaskId((int) msg.getTaskId())
                    .build();

            // 原客户签约方式为纸质，不需要发起原客户确认
            if (fromCustomer.getSignMode().equals(CustomerSignMode.PAPER.getCode())) {
                boolean auth = upmAuthCheckService.hasRolePermission(msg.getOpUid(), CustomerRoleTypeEnum.PAPER_SIGNMODE_UNBINDPOI_MANAGER.getCode());
                if (!auth) {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "原客户为纸质签约客户，操作人没有纸质签约客户解绑门店的权限");
                }
                //创建原客户门店解绑任务
                Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerUnBindTask(fromCustomerId, Sets.newHashSet(msg.getWmPoiIds()), customerOperateBO);

                fromCustomerConfirmSucc(new FromCustomerConfirmVo(msg.getTaskId(), fromCustomerId, toCustomerId, msg.getWmPoiIds(), msg.getOpUid(), msg.getOpName(), switchTaskBo.getStepVersion()), poiAndTaskMaps);
                return;
            }

            //创建原客户门店解绑任务-原客户为电子签约
            Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerUnBindTask(fromCustomerId, Sets.newHashSet(msg.getWmPoiIds()), customerOperateBO);

            // 原客户无生效KP，不需要发起原客户确认
            WmCustomerKp kpOfEffectiveSigner = wmCustomerKpService.getCustomerKpOfEffectiveSigner(fromCustomerId);
            if (kpOfEffectiveSigner == null) {
                fromCustomerConfirmSucc(new FromCustomerConfirmVo(msg.getTaskId(), fromCustomerId, toCustomerId, msg.getWmPoiIds(), msg.getOpUid(), msg.getOpName(), switchTaskBo.getStepVersion()), poiAndTaskMaps);
                return;
            }

            // 判断是否勾选原客户自动确认解绑，若勾选无需原客户确认，否则发起原客户确认
            if (msg.getSwitchOptions().contains(CustomerSwitchOptionsEnum.FROM_CUSTOMER_CONFRIM_UNBIND_AUTO)) {
                forceUnbindUnconfirm(new WmCustomerForceUnbindUnconfirmDTO(msg.getTaskId(), fromCustomerId, toCustomerId, msg.getWmPoiIds(), msg.getOpUid(), msg.getOpName(), switchTaskBo.getVersion(), switchTaskBo.getStepVersion()), poiAndTaskMaps);
            } else {
                fromCustomerConfirm(new FromCustomerConfirmVo(msg.getTaskId(), fromCustomerId, toCustomerId, msg.getWmPoiIds(), msg.getOpUid(), msg.getOpName(), switchTaskBo.getStepVersion()), poiAndTaskMaps);
            }

        } catch (WmCustomerException e) {
            log.error("进行原客户门店解绑确认处理失败 msg={}", JSONObject.toJSONString(msg), e);
        } catch (Exception e) {
            log.error("进行原客户门店解绑确认处理失败 msg={}", JSONObject.toJSONString(msg), e);
        }
    }

    /**
     * 原客户确认解绑短信任务核验是否已经存在
     *
     * @param msg
     * @return
     */
    private boolean hasFromCusUnbindSignTask(CustomerSwitchNotifyMsg msg) {
        String status = WmCustomerConstant.SUCCESS;
        try {
            CustomerUnBindWmPoiTaskSearchParam searchParam = new CustomerUnBindWmPoiTaskSearchParam();
            searchParam.setWmCustomerId(Integer.valueOf(msg.getFromBizId()));
            searchParam.setWmPoiId(msg.getWmPoiIds());
            log.info("hasInProcessingCustomerUnBindWmPoiTask param:{}", JSONObject.toJSONString(searchParam));
            Map<Long, Boolean> result = wmEcontractSignBzService.hasInProcessingCustomerUnBindWmPoiTask(searchParam);
            if (result == null) {
                return false;
            }
            log.info("hasInProcessingCustomerUnBindWmPoiTask result:{}", JSONObject.toJSONString(result));
            boolean hasTask = result.values().stream()
                    .filter(res -> res)
                    .findAny().orElse(false);
            if (hasTask) {
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
            }
            return hasTask;
        } catch (WmCustomerException e) {
            log.warn("hasInProcessingCustomerUnBindWmPoiTask error. taskId:{}", msg.getTaskId());
            return false;
        } finally {
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getName())
                    .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getTag(), "confirm")
                    .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getStatus(), status).count();
        }
    }

    /**
     * 解绑-客户切换
     *
     * @param wmCustomerPoiSwitchBo
     * @throws WmCustomerException
     */
    @TairLock(group = TairLockGroup.OLD_CUSTOMER_CONFIRM_OPERATE_SWITCH, seedExp = "wmCustomerPoiSwitchBo.switchTaskId")
    public void unBind(WmCustomerPoiSwitchBO wmCustomerPoiSwitchBo) throws WmCustomerException {
        log.info("unBind wmCustomerPoiSwitchBo={}", JSONObject.toJSONString(wmCustomerPoiSwitchBo));
        Integer opUid = wmCustomerPoiSwitchBo.getOpUid();
        String opName = wmCustomerPoiSwitchBo.getOpName();
        Set<Long> wmPoiIdSet = wmCustomerPoiSwitchBo.getWmPoiIdSet();
        WmCustomerPoiSwitchOperateTypeEnum typeEnum = wmCustomerPoiSwitchBo.getTypeEnum();
        try {
            SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskIdRT(wmCustomerPoiSwitchBo.getSwitchTaskId(), opUid, opName);
            log.info("unBind switchTaskBo={}", JSONObject.toJSONString(switchTaskBo));
            if (switchTaskBo == null) {
                log.error("客户切换{}失败 未找到切换任务 wmCustomerPoiSwitchBo={}", typeEnum.getDesc(), JSONObject.toJSONString(wmCustomerPoiSwitchBo));
                MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getName())
                        .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getTag(), CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getDesc())
                        .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getStatus(), WmCustomerConstant.SYSTEM_EXCEPTION).count();
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户门店解绑短信处理失败");
            }
            if (switchTaskBo.getStatus() != WmPoiSwitchTaskStatusEnum.ORIGIN_CUS_CONFIRMING) {
                log.error("客户切换{}失败 切换任务不满足条件 wmCustomerPoiSwitchBo={}", typeEnum.getDesc(), JSONObject.toJSONString(wmCustomerPoiSwitchBo));
                MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getName())
                        .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getTag(), CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getDesc())
                        .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getStatus(), WmCustomerConstant.SYSTEM_EXCEPTION).count();
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户门店解绑短信处理失败");
            }
            Integer fromCustomerId = Integer.parseInt(switchTaskBo.getFromBizId());
            Integer toCustomerId = Integer.parseInt(switchTaskBo.getToBizId());
            if (typeEnum.getOperateType() == WmCustomerPoiConfirmResultEnum.APPOVE.getCode()) {
                fromCustomerConfirmSucc(new FromCustomerConfirmVo(switchTaskBo.getTaskId(), fromCustomerId, toCustomerId,
                        Lists.newArrayList(wmPoiIdSet), opUid, opName, switchTaskBo.getStepVersion()), null);
            } else {
                fromCustomerConfirmFail(new FromCustomerConfirmVo(switchTaskBo.getTaskId(), fromCustomerId, toCustomerId,
                        Lists.newArrayList(wmPoiIdSet), opUid, opName, switchTaskBo.getStepVersion()));
            }
            wmCustomerService.insertCustomerOpLog(fromCustomerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                    String.format(typeEnum.getLogMsg(), StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), "");
        } catch (WmPoiBizException e) {
            log.error("客户切换{}失败 wmCustomerPoiSwitchBo={}", typeEnum.getDesc(), JSONObject.toJSONString(wmCustomerPoiSwitchBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "解绑任务处理失败");
        } catch (TException e) {
            log.error("客户切换{}失败 wmCustomerPoiSwitchBo={}", typeEnum.getDesc(), JSONObject.toJSONString(wmCustomerPoiSwitchBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "解绑任务处理失败");
        }
    }

    /**
     * 绑定-客户切换
     *
     * @param wmCustomerPoiSwitchBo
     * @throws WmCustomerException
     */
    @TairLock(group = TairLockGroup.NEW_CUSTOMER_CONFIRM_OPERATE_SWITCH, seedExp = "wmCustomerPoiSwitchBo.switchTaskId")
    public void bind(WmCustomerPoiSwitchBO wmCustomerPoiSwitchBo) throws WmCustomerException {
        log.info("bind wmCustomerPoiSwitchBo={}", JSONObject.toJSONString(wmCustomerPoiSwitchBo));
        Integer opUid = wmCustomerPoiSwitchBo.getOpUid();
        String opName = wmCustomerPoiSwitchBo.getOpName();
        Set<Long> wmPoiIdSet = wmCustomerPoiSwitchBo.getWmPoiIdSet();
        WmCustomerPoiSwitchOperateTypeEnum typeEnum = wmCustomerPoiSwitchBo.getTypeEnum();
        try {
            SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskIdRT(wmCustomerPoiSwitchBo.getSwitchTaskId(), opUid, opName);
            log.info("bind switchTaskBo={}", JSONObject.toJSONString(switchTaskBo));
            if (switchTaskBo == null) {
                log.error("客户切换{}失败 未找到切换任务 wmCustomerPoiSwitchBo={}", typeEnum.getDesc(), JSONObject.toJSONString(wmCustomerPoiSwitchBo));
                MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getName())
                        .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getTag(), CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getDesc())
                        .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getStatus(), WmCustomerConstant.SYSTEM_EXCEPTION).count();
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户门店绑定短信处理失败");
            }
            if (switchTaskBo.getStatus() != WmPoiSwitchTaskStatusEnum.NEW_CUS_PRE_BINDING) {
                log.error("客户切换{}失败 切换任务不满足条件 wmCustomerPoiSwitchBo={}", typeEnum.getDesc(), JSONObject.toJSONString(wmCustomerPoiSwitchBo));
                MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getName())
                        .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getTag(), CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getDesc())
                        .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getStatus(), WmCustomerConstant.SYSTEM_EXCEPTION).count();
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户门店绑定短信处理失败");
            }
            Integer fromCustomerId = Integer.parseInt(switchTaskBo.getFromBizId());
            Integer toCustomerId = Integer.parseInt(switchTaskBo.getToBizId());
            if (typeEnum.getOperateType() == WmCustomerPoiConfirmResultEnum.APPOVE.getCode()) {
                newCustomerPreBindSucc(new NewCustomerPreBindConfirmVo(switchTaskBo.getTaskId(), fromCustomerId, toCustomerId, Lists.newArrayList(wmPoiIdSet),
                        opUid, opName, switchTaskBo.getStepVersion()));

            } else {
                newCustomerPreBindFail(new NewCustomerPreBindConfirmVo(switchTaskBo.getTaskId(), fromCustomerId, toCustomerId, Lists.newArrayList(wmPoiIdSet),
                        opUid, opName, switchTaskBo.getStepVersion()), typeEnum);
            }
            wmCustomerService.insertCustomerOpLog(toCustomerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                    String.format(typeEnum.getLogMsg(), StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), "");
        } catch (WmPoiBizException e) {
            log.error("客户切换{}失败 wmCustomerPoiSwitchBo={}", typeEnum.getDesc(), JSONObject.toJSONString(wmCustomerPoiSwitchBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "绑定任务处理失败");
        } catch (TException e) {
            log.error("客户切换{}失败 wmCustomerPoiSwitchBo={}", typeEnum.getDesc(), JSONObject.toJSONString(wmCustomerPoiSwitchBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "绑定任务处理失败");
        }
    }

    @TairLock(group = TairLockGroup.NEW_CUSTOMER_CONFIRM_START, seedExp = "switchTaskId", grayKey = "switchTaskId", grayPercentKey = TairLockGrayPercentKeyEnum.NEW_CUSTOMER_CONFIRM_START)
    public void reStartCustomerPreBindPoiForSwitch(long switchTaskId, int customerId, Set<Long> wmPoiIdSet, int opUid, String opName, int source) throws WmCustomerException {
        log.info("#reStartCustomerPreBindPoiForSwitch,switchTaskId={},customerId={},wmPoiIdSet={},opUid={},opName={},source={}", switchTaskId, customerId, wmPoiIdSet, opUid, opName, source);

        // 获取客户信息
        WmCustomerDB customerInfo = wmCustomerService.selectCustomerById(customerId);
        //美食城客户绑定门店需要KP确认环节
        if (!Integer.valueOf(CustomerRealTypeEnum.MEISHICHENG.getValue()).equals(customerInfo.getCustomerRealType())) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, "非美食城客户无法重新发起预绑定");
        }
        //查询门店预绑定状态
        List<WmCustomerPoiDB> wmCustomerPoiForPreBind = wmCustomerPoiDBMapper.getWmCustomerPoiForPreBind(customerId, Lists.newArrayList(wmPoiIdSet), Lists.newArrayList(CustomerRelationStatusEnum.CONFIRM_BINDING_FAIL.getCode(), CustomerRelationStatusEnum.TO_APPLY_BIND.getCode()));
        if (wmPoiIdSet.size() != wmCustomerPoiForPreBind.size()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, "门店绑定状态异常,无法重新发起预绑定");
        }

        try {
            //创建任务并返回门店和任务ID关系MAP
            CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                    .opUserId(opUid).opUserName(opName)
                    .opSource(CustomerTaskSourceEnum.CUSTOMER_SWITCH.getCode())
                    .opDetailSource(CustomerTaskDetailSourceEnum.CUSTOMER_SWITCH.getDesc())
                    .opSystem(CustomerTaskOpSystemEnum.CUSTOMER_SWITCH_SYS.getDesc())
                    .taskSceneType(CustomerTaskSceneType.CUSTOMER_SWITCH.getCode())
                    .taskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode())
                    .bizTaskId((int) switchTaskId).build();
            Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerBindTask(customerId, wmPoiIdSet, customerOperateBO);
            //发起签约
            EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
            econtractTaskApplyBo.setBizId(String.valueOf(customerId));
            econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
            econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.FOODCITY_POI_TABLE);
            EcontractFoodcityPoiTableBo econtractFoodcityPoiTableBo = new EcontractFoodcityPoiTableBo();
            econtractFoodcityPoiTableBo.setPartAName(customerInfo.getCustomerName());
            Map<String, Long> wmPoiInfo = Maps.newHashMap();
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(Lists.newArrayList(wmPoiIdSet),
                    Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_NAME));
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                wmPoiInfo.put(wmPoiAggre.getName(), wmPoiAggre.getWm_poi_id());
            }
            econtractFoodcityPoiTableBo.setPoiTableName(wmPoiInfo);
            econtractFoodcityPoiTableBo.setPartAOfficialSeal(customerInfo.getCustomerName());
            econtractFoodcityPoiTableBo.setPartBOfficialSeal("北京三快在线科技有限公司");
            econtractFoodcityPoiTableBo.setWmPoiIdList(Lists.newArrayList(wmPoiIdSet));
            econtractTaskApplyBo.setApplyInfoBo(JSONObject.toJSONString(econtractFoodcityPoiTableBo));
            econtractTaskApplyBo.setCommitUid(opUid);
            // 发送短信
            LongResult result = wmEcontractSignBzService.applyTask(econtractTaskApplyBo);
            // 记录短信记录
            WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = new WmCustomerPoiSmsRecordDB();
            wmCustomerPoiSmsRecordDB.setCustomerId(customerId);
            wmCustomerPoiSmsRecordDB.setTaskId(result.getValue());
            wmCustomerPoiSmsRecordDB.setWmPoiIds(StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL));
            wmCustomerPoiSmsRecordDB.setTaskStatus(EcontractTaskStateEnum.TO_COMMIT.getType());
            wmCustomerPoiSmsRecordDB.setValid(CustomerConstants.VALID);
            wmCustomerPoiSmsRecordDB.setType(WmCustomerSmsTypeEnum.NEW_CUSTOMER_BIND.getCode());
            wmCustomerPoiSmsRecordMapper.insertSmsRecord(wmCustomerPoiSmsRecordDB);
            wmCustomerPoiRelService.applyPreBind(wmPoiIdSet, switchTaskId, customerId, poiAndTaskMaps);
            //任务ID非空则设置签约任务ID字段
            if (!CollectionUtils.isEmpty(poiAndTaskMaps)) {
                List<Integer> taskIds = Lists.newArrayList(poiAndTaskMaps.values());
                customerTaskService.updateTaskSignTaskId(customerId, taskIds, wmCustomerPoiSmsRecordDB.getId());
            }
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                    String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_APPLY_PRE_BIND, StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), "");
        } catch (Exception e) {
            log.error("发送客户门店预绑定确认短信异常", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "发送客户门店预绑定确认短信异常");
        }
    }

    /**
     * 取消切换参数校验
     *
     * @param msg
     * @return
     */
    private List<Long> checkCancelTask(CustomerSwitchNotifyMsg msg) {
        List<Long> wmPoiIdList = Lists.newArrayList();
        if (msg == null || CollectionUtils.isEmpty(msg.getWmPoiIds()) || StringUtils.isBlank(msg.getFromBizId()) || StringUtils.isBlank(msg.getToBizId())) {
            return wmPoiIdList;
        }
        try {
            SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskIdRT(msg.getTaskId(), msg.getOpUid(), msg.getOpName());
            if (switchTaskBo == null || switchTaskBo.getTaskType() != WmPoiSwitchTaskTypeEnum.CUS_SWITCH ||
                    !switchTaskBo.getFromBizId().equals(msg.getFromBizId()) || !switchTaskBo.getToBizId().equals(msg.getToBizId()) ||
                    switchTaskBo.getStatus() == WmPoiSwitchTaskStatusEnum.CUS_SWITCH_CANCEL || switchTaskBo.getStatus() == WmPoiSwitchTaskStatusEnum.CUS_SWITCH_SUC) {
                return wmPoiIdList;
            }
            final List<Long> taskWmPoiIdList = wmPoiSwitchThriftService.getWmPoiIdListByTaskId(msg.getTaskId());
            if (CollectionUtils.isEmpty(taskWmPoiIdList)) {
                return wmPoiIdList;
            }
            wmPoiIdList = msg.getWmPoiIds().stream().filter(x -> taskWmPoiIdList.contains(x)).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("cancelSwitch 失败：msg={}", JSONObject.toJSONString(msg), e);
        }
        return wmPoiIdList;
    }

    /**
     * 取消切换
     *
     * @param msg
     */
    public void cancelSwitch(CustomerSwitchNotifyMsg msg) {
        log.info("cancelSwitch msg:{}", JSONObject.toJSONString(msg));
        List<Long> wmPoiIdList = checkCancelTask(msg);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            log.error("cancelSwitch取消失败，参数校验不通过 msg:{}", JSONObject.toJSONString(msg));
            return;
        }
        int fromCustomerId = Integer.parseInt(msg.getFromBizId());
        int toCustomerId = Integer.parseInt(msg.getToBizId());
        // 删除预绑定记录,解除门店待解绑
        wmCustomerPoiRelService.cancelSwitch(new HashSet<>(wmPoiIdList), fromCustomerId, toCustomerId, msg.getTaskId());
        List<WmCustomerPoiSmsRecordDB> fromSMS = wmCustomerPoiSmsRecordMapper.selectSmsRecordListByCustomerIdMaster(fromCustomerId);
        List<WmCustomerPoiSmsRecordDB> toSMS = wmCustomerPoiSmsRecordMapper.selectSmsRecordListByCustomerIdMaster(toCustomerId);
        List<WmCustomerPoiSmsRecordDB> wmCustomerPoiSmsRecordDBS = Lists.newArrayList();
        wmCustomerPoiSmsRecordDBS.addAll(fromSMS);
        wmCustomerPoiSmsRecordDBS.addAll(toSMS);
        if (CollectionUtils.isEmpty(wmCustomerPoiSmsRecordDBS)) {
            log.info("无签约短信 msg={}", JSONObject.toJSONString(msg));
            return;
        }
        for (WmCustomerPoiSmsRecordDB smsRecordDB : wmCustomerPoiSmsRecordDBS) {
            List<String> poiIds = splitter.splitToList(smsRecordDB.getWmPoiIds());
            if (CollectionUtils.isEmpty(poiIds)) {
                continue;
            }
            List<Long> poiIdList = poiIds.stream().map(x -> Long.valueOf(x)).collect(Collectors.toList());
            poiIdList.retainAll(wmPoiIdList);
            if (CollectionUtils.isEmpty(poiIdList)) {
                continue;
            }
            try {
                cancelUnBind(fromCustomerId, toCustomerId, smsRecordDB.getTaskId(), poiIdList);
            } catch (WmCustomerException e) {
                log.error("cancelUnBind 失败：customerId={},smsTaskId:{},poiIdList={}", fromCustomerId, smsRecordDB.getTaskId(), poiIdList, e);
            } catch (Exception e) {
                log.error("cancelUnBind 失败：customerId={},smsTaskId:{},poiIdList={}", fromCustomerId, smsRecordDB.getTaskId(), poiIdList, e);
            }
        }

        //取消客户切换绑定和解绑任务
    }

    /**
     * 客户切换 -客户切换不下线
     *
     * @param msg
     */
    @Transactional(rollbackFor = Exception.class)
    @TairLock(group = TairLockGroup.SWITCH_CUSTOMER, seedExp = "msg.taskId+'_'+msg.wmPoiId", grayKey = "msg.taskId", grayPercentKey = TairLockGrayPercentKeyEnum.SWITCH_CUSTOMER)
    public void switchCustomer(ToCustomerSwitchConfirmSuccMsg msg) throws WmCustomerException {
        log.info("switchCustomer msg:{}", JSONObject.toJSONString(msg));
        long taskId = msg.getTaskId();
        Set<Long> wmPoiIdSet = Sets.newHashSet(msg.getWmPoiId());
        long fromCustomerId = Long.parseLong(msg.getFromBizId());
        long toCustomerId = Long.parseLong(msg.getToBizId());
        Integer opUid = msg.getOpUid();
        String opName = msg.getOpName();
        WmCustomerPoiAggre customerPoiAggre = WmCustomerPoiAggre.Factory.make();
        CustomerSwitchResultMsg resultMsg = new CustomerSwitchResultMsg();
        resultMsg.setTaskId(taskId);
        resultMsg.setWmPoiId(msg.getWmPoiId());
        resultMsg.setModule(CustomerSwitchModuleTypeEnum.CUSTOMER.getCode());

        boolean result = checkCanCustomerSwitch(msg);
        if (!result) {
            log.warn("switchCustomer checkCanCustomerSwitch不通过 msg={}", JSONObject.toJSONString(msg));
            return;
        }

        try {
            SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskIdRT(taskId, 0, "客户系统");
            if (switchTaskBo == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到切换任务，taskId=" + taskId);
            }

            customerPoiAggre.customerUnBindPoiForCusSwitch(wmPoiIdSet, fromCustomerId, opUid, opName, taskId, false);
            //新客户为美食城且有资质共用标则不掉标，其他均需要掉资质共用标
            batchDelQuaComPoiTag(wmPoiIdSet, toCustomerId, opUid, opName);
            customerPoiAggre.customerBindPoiForCusSwitch(toCustomerId, wmPoiIdSet, opUid, opName, taskId, false, switchTaskBo.getStepVersion());
            resultMsg.setResult(OperateResultEnum.SUCCESS.getCode());
            customerSwitchSendService.sendCustomerSwitchNotify(resultMsg);
        } catch (WmCustomerException e) {
            log.warn("客户切换失败 readySwitchMsg={},errMsg={}", JSONObject.toJSONString(msg), e.getMsg(), e);
            resultMsg.setResult(OperateResultEnum.FAIL.getCode());
            resultMsg.setErrMsg(e.getMsg());
            customerSwitchSendService.sendCustomerSwitchNotify(resultMsg);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, e.getMsg());
        } catch (Exception e) {
            log.warn("客户切换失败 readySwitchMsg={}", JSONObject.toJSONString(msg), e);
            resultMsg.setResult(OperateResultEnum.FAIL.getCode());
            resultMsg.setErrMsg("未知原因");
            customerSwitchSendService.sendCustomerSwitchNotify(resultMsg);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未知原因");
        }
    }

    /**
     * 校验是否可以进行客户切换
     *
     * @param msg
     * @return
     */
    private boolean checkCanCustomerSwitch(ToCustomerSwitchConfirmSuccMsg msg) {
        long taskId = msg.getTaskId();
        long wmPoiId = msg.getWmPoiId();
        Long fromCustomerId = new Long(msg.getFromBizId());
        String status = WmCustomerConstant.SUCCESS;
        try {
            SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskIdRT(taskId, 0, "客户系统");
            if (switchTaskBo == null) {
                log.error("根据切换任务id:{}查询切换任务基本信息为空,校验不通过",taskId);
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            if (switchTaskBo.getVersion() != null && switchTaskBo.getVersion().intValue() < CustomerSwitchVesionEnum.V4.getCode()) {
                return true;
            }
            if (switchTaskBo.getStatus() != WmPoiSwitchTaskStatusEnum.CUS_SWITCH_REL_EFFECTING) {
                log.error("切换任务id:{}任务状态非执行客户切换中,校验不通过",taskId);
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            SwitchTaskPoiBo switchTaskPoiBo = wmPoiSwitchThriftService.getSwitchTaskPoiRT(taskId, wmPoiId);
            if (switchTaskPoiBo == null) {
                log.error("根据切换任务id:{}门店id:{}查询切换任务基本信息为空,校验不通过",taskId,wmPoiId);
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            if (switchTaskPoiBo.getPoiSwitchCustomerStatus() == PoiCustomerSwitchRelEffectiveStatusEnum.FINISHED.getCode()) {
                log.error("根据切换任务id:{}门店id:{}查询切换任务客户模块状态为切换完成,校验不通过",taskId,wmPoiId);
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            if (switchTaskPoiBo.getSwitchCondition() == null || switchTaskPoiBo.getSwitchCondition().intValue() != CustomerSwitchConditionEnum.SATISFIED.getCode()) {
                log.error("根据切换任务id:{}门店id:{}查询切换条件为不满足,校验不通过",taskId,wmPoiId);
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }

            List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectCustomerPoiRelRT(fromCustomerId, wmPoiId);
            if (CollectionUtils.isEmpty(wmCustomerPoiDBList) || wmCustomerPoiDBList.size() > 1) {
                log.error("根据原客户id:{}门店id:{}查询客户门店关系,校验不通过",fromCustomerId,wmPoiId);
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            WmCustomerPoiDB wmCustomerPoiDB = wmCustomerPoiDBList.get(0);
            if (wmCustomerPoiDB.getSwitchTaskId() == null || wmCustomerPoiDB.getSwitchTaskId().longValue() != taskId) {
                log.error("根据原客户id:{}门店id:{}查询对应切换任务id:{},校验不通过",fromCustomerId,wmPoiId,taskId);
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            return true;
        } catch (WmPoiBizException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            log.warn("checkCancelSwitch失败 msg={}", JSONObject.toJSONString(msg), e);
        } catch (TException e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            log.error("checkCancelSwitch失败 msg={}", JSONObject.toJSONString(msg), e);
        } finally {
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getName())
                    .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getTag(), "switch")
                    .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getStatus(), status).count();
        }
        return true;
    }

    /**
     * 发起原客户门店解绑确认
     *
     * @param vo
     * @throws TException
     * @throws WmCustomerException
     */
    private void fromCustomerConfirm(FromCustomerConfirmVo vo, Map<Long, Integer> poiAndTaskUnBindMaps) throws TException, WmCustomerException {
        log.info("fromCustomerConfirm vo:{}", JSONObject.toJSONString(vo));
        Integer customerId = vo.getFromCustomerId();
        if (customerId == null || customerId == 0 || CollectionUtils.isEmpty(vo.getWmPoiIdList())) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }
        Set<Long> wmPoiIdSet = new HashSet<>(vo.getWmPoiIdList());
        // 校验门店是否存在
        checkNotExistWmPoiId(vo.getWmPoiIdList());
        WmCustomerPoiAggre customerPoiAggre = WmCustomerPoiAggre.Factory.make();
        // 校验客户门店绑定关系
        customerPoiAggre.validCustomerUnbindPoi(wmPoiIdSet, customerId.intValue(), vo.getTaskId());
        log.info("门店ID:{} 切换客户，需要短信通知KP确认,customerId={},opUid={}", wmPoiIdSet, customerId.intValue(), vo.getOpUid());
        // 发送短信通知生效KP
        customerPoiAggre.sendUnBindSmsGroupByKpManager(customerId.intValue(), wmPoiIdSet, vo.getOpUid(), poiAndTaskUnBindMaps);
        // 更新客户门店解绑关系锁定状态及待解绑状态
        wmCustomerPoiRelService.confirmUnBind(new HashSet<>(vo.getWmPoiIdList()), vo.getTaskId(), customerId, poiAndTaskUnBindMaps);
        // 插入日志
        wmCustomerService.insertCustomerOpLog(customerId.intValue(), vo.getOpUid(),
                String.format(CustomerConstants.OPNAME_TEMPLATE_FORCE_UNBIND_TO_UNCONFIRM_FOR_CUS_SWITCH, vo.getOpName()), WmCustomerOplogBo.OpType.CHANGESTATUS,
                String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_TO_CONFIRM, StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), "");
    }


    /**
     * 原客户确认成功
     *
     * @param vo
     * @param poiAndTaskUnBindMaps 客户和门店解绑关联的任务ID
     * @throws TException
     * @throws WmCustomerException
     */
    public void fromCustomerConfirmSucc(FromCustomerConfirmVo vo, Map<Long, Integer> poiAndTaskUnBindMaps) throws TException, WmCustomerException {
        log.info("fromCustomerConfirmSucc vo:{}", JSONObject.toJSONString(vo));
        /** 1.目标客户未生效则发起审 **/
        startToCustomerReady(vo.getToCustomerId(), vo.getOpUid(), vo.getOpName());

        //创建新客户门店绑定任务
        CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                .taskSceneType(CustomerTaskSceneType.CUSTOMER_SWITCH.getCode())
                .taskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode())
                .opSource(CustomerTaskSourceEnum.CUSTOMER_SWITCH.getCode())
                .opSystem(CustomerTaskOpSystemEnum.CUSTOMER_SWITCH_SYS.getDesc())
                .opDetailSource(CustomerTaskDetailSourceEnum.CUSTOMER_SWITCH.getDesc())
                .bizTaskId(vo.getTaskId().intValue())
                .opUserId(vo.getOpUid()).opUserName(vo.getOpName())
                .build();

        //美食城流程类型
        if (CustomerSwitchStepVersionEnum.MSC_STEP_VERSION.getCode().equals(vo.getStepVersion())) {
            Set<Long> wmPoiIdSet = Sets.newHashSet(vo.getWmPoiIdList());
            //任务状态机更新为---新客待发起预绑定
            try {
                wmPoiSwitchThriftService.changeStatusInFourStep(vo.getTaskId(), WmPoiSwitchTaskStatusEnum.ORIGIN_CUS_CONFIRM_SUC.getCode(), vo.getOpUid(), vo.getOpName());
            } catch (WmPoiBizException e) {
                log.error("changeStatusInFourStep exception", e);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "商家确认解绑任务处理失败");
            }
            //创建客户任务
            Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerBindTask(vo.getToCustomerId(), Sets.newHashSet(vo.getWmPoiIdList()), customerOperateBO);

            //新客下发起预绑定签约任务
            WmCustomerPoiAggre.Factory.make().preBindCustomerPoiAndApplyConfirm(vo.getToCustomerId(), wmPoiIdSet, vo.getTaskId(), vo.getOpUid(), vo.getOpName(), false, poiAndTaskMaps);
            //老客下更新门店状态WmCustomerPoiRelService
            wmCustomerPoiRelService.oldCustomerToUnbinding(wmPoiIdSet, vo.getFromCustomerId(), vo.getTaskId(), poiAndTaskUnBindMaps);
            //任务状态机更新为---新客预绑定确认中
            try {
                wmPoiSwitchThriftService.changeStatusInFourStep(vo.getTaskId(), WmPoiSwitchTaskStatusEnum.NEW_CUS_PRE_BINDING.getCode(), vo.getOpUid(), vo.getOpName());
            } catch (WmPoiBizException e) {
                log.error("changeStatusInFourStep exception", e);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "商家确认解绑任务处理失败");
            }
        } else {
            /** 2.通知原客户解绑确认成功 **/
            customerSwitchSendService.sendFromCustomerConfirmNotify(new FromCustomerSwitchResultMsg(vo.getTaskId(), vo.getFromCustomerId(), OperateResultEnum.SUCCESS.getCode(), vo.getOpUid(), vo.getOpName()));
            //创建新客户门店绑定任务
            Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerBindTask(vo.getToCustomerId(), Sets.newHashSet(vo.getWmPoiIdList()), customerOperateBO);
            /** 3. 插入新客户门店待绑定记录,更新客户门店解绑关系锁定状态及待解绑状态 **/
            wmCustomerPoiRelService.fromSwitchSucc(new HashSet<>(vo.getWmPoiIdList()), vo.getFromCustomerId(), vo.getToCustomerId(), vo.getTaskId(), poiAndTaskMaps, poiAndTaskUnBindMaps);
        }
    }

    /**
     * 新客户确认绑定成功
     *
     * @param vo
     */
    public void newCustomerPreBindSucc(NewCustomerPreBindConfirmVo vo) {
        log.info("#newCustomerPreBindSucc,vo={}", JSONObject.toJSONString(vo));
        //继续发出原客户解绑确认
        customerSwitchSendService.sendFromCustomerConfirmNotify(new FromCustomerSwitchResultMsg(vo.getTaskId(), vo.getFromCustomerId(), OperateResultEnum.SUCCESS.getCode(), vo.getOpUid(), vo.getOpName()));
        //门店在新客下状态更新为3:待绑定
        wmCustomerPoiRelService.newCustomerToReadyBind(new HashSet<>(vo.getWmPoiIdList()), vo.getToCustomerId(), vo.getTaskId());
    }

    /**
     * 新客户确认绑定失败
     *
     * @param vo
     */
    public void newCustomerPreBindFail(NewCustomerPreBindConfirmVo vo, WmCustomerPoiSwitchOperateTypeEnum typeEnum) throws TException, WmPoiBizException {
        log.info("#newCustomerPreBindFail,vo={}", JSONObject.toJSONString(vo));
        Long taskId = vo.getTaskId();
        Set<Long> wmPoiIdSet = new HashSet<>(vo.getWmPoiIdList());
        Integer toCustomerId = vo.getToCustomerId();
        //新客更新绑定状态
        wmCustomerPoiRelService.cancelPreBind(wmPoiIdSet, taskId, toCustomerId);
        //设置切换任务状态机为---新客户预绑定失败
        wmPoiSwitchThriftService.changeStatusInFourStep(taskId, WmPoiSwitchTaskStatusEnum.NEW_CUS_PRE_BIND_FAIL.getCode(), vo.getOpUid(), vo.getOpName());
        Integer taskStatus = CustomerTaskStatusEnum.CANCEL.getCode();
        if (typeEnum == WmCustomerPoiSwitchOperateTypeEnum.BIND_SMS_REJECT) {
            taskStatus = CustomerTaskStatusEnum.FAIL.getCode();
        }
        //同步任务状态
        customerTaskService.updateStatusByCustomerIdAndPoiIdAndType(toCustomerId, wmPoiIdSet, taskId, CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode(), taskStatus);
    }

    /**
     * 原客户确认失败
     *
     * @param vo
     * @throws TException
     * @throws WmCustomerException
     */
    public void fromCustomerConfirmFail(FromCustomerConfirmVo vo) throws TException, WmCustomerException {
        log.info("fromCustomerConfirmFail vo:{}", JSONObject.toJSONString(vo));
        /** 1.更新客户门店解绑关系锁定状态及待解绑状态 **/
        wmCustomerPoiRelService.rejectUnBind(new HashSet<>(vo.getWmPoiIdList()), vo.getFromCustomerId(), vo.getTaskId());
        /** 2.通知原客户解绑确认失败 **/
        customerSwitchSendService.sendFromCustomerConfirmNotify(new FromCustomerSwitchResultMsg(vo.getTaskId(), vo.getFromCustomerId(), OperateResultEnum.FAIL.getCode(), vo.getOpUid(), vo.getOpName()));
    }


    /**
     * 发起新客户审批
     *
     * @param customerId
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    private void startToCustomerReady(Integer customerId, Integer opUid, String opName)
            throws WmCustomerException, TException {
        /** 新客户未生效则发起审批 **/
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if (wmCustomerDB != null && wmCustomerDB.isUnEffectived() && wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode()) {
            log.info("KP签约完成，目标客户为待提审，触发客户提审  toBizId：{}", customerId);
            //提审客户
            wmCustomerService.commitAudit(customerId, opUid, opName, true);
        }
    }

    /**
     * 强制解绑签约任务处理
     *
     * @param fromCustomerId
     * @param wmPoiIdList
     * @throws TException
     * @throws WmCustomerException
     */
    private void forceUnbindSign(int fromCustomerId, List<Long> wmPoiIdList) throws TException, WmCustomerException {
        log.info("forceUnbindSign fromCustomerId:{},wmPoiId={}", fromCustomerId, JSONObject.toJSONString(wmPoiIdList));
        /** 1.获取当前客户解绑签约任务 **/
        List<WmCustomerPoiSmsRecordDB> wmCustomerPoiSmsRecordDBS = wmCustomerPoiSmsRecordMapper.selectSmsRecordListByCustomerIdMaster(fromCustomerId);
        if (!CollectionUtils.isEmpty(wmCustomerPoiSmsRecordDBS)) {
            for (WmCustomerPoiSmsRecordDB smsRecordDB : wmCustomerPoiSmsRecordDBS) {
                List<String> poiIds = splitter.splitToList(smsRecordDB.getWmPoiIds());
                if (CollectionUtils.isEmpty(poiIds)) {
                    continue;
                }
                List<Long> poiIdList = poiIds.stream().map(x -> Long.valueOf(x)).collect(Collectors.toList());
                poiIdList.retainAll(wmPoiIdList);
                if (CollectionUtils.isEmpty(poiIdList)) {
                    continue;
                }
                Long taskId = smsRecordDB.getTaskId();
                /** 2.将客户签约任务取消 **/
                wmEcontractSignBzService.cancelSign(taskId, WmEcontractBatchConstant.FORCE_UNBIND, false);
                /** 3.更新客户解绑确认短信状态为处理成功 **/
                wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.SUCCESS.getType(), taskId);
            }
        }
    }

    /**
     * 校验门店是否存在
     *
     * @param wmPoiIdList
     * @throws WmCustomerException
     * @throws TException
     */
    private void checkNotExistWmPoiId(List<Long> wmPoiIdList) throws WmCustomerException, TException {
        List<Long> notExistList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdList) {
            try {
                WmPoiControl wmPoiControl = wmPoiFlowlineQueryThriftService.getWmPoiControlByWmPoiIdRT(wmPoiId);
                if (wmPoiControl == null) {
                    log.info("客户绑定门店,校验门店ID{}不存在", wmPoiId);
                    notExistList.add(wmPoiId);
                } else {
                    if (wmPoiControl.getIs_delete() == 1) {
                        log.info("客户绑定门店,校验门店ID{}已删除", wmPoiId);
                        notExistList.add(wmPoiId);
                    }
                }
            } catch (WmServerException e) {
                log.error("查询门店是否删除异常");
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询门店是否删除异常");
            }
        }
        if (!CollectionUtils.isEmpty(notExistList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                    String.format("门店ID:%s不存在", StringUtils.join(notExistList, CustomerConstants.SPLIT_SYMBOL)));
        }
    }

    /**
     * 字符串形式的wmPoiIds转成List
     *
     * @param wmPoiIds
     * @return
     * @throws WmCustomerException
     */
    public List<Long> convertWmPoiIds(String wmPoiIds) throws WmCustomerException {
        if (StringUtils.isEmpty(wmPoiIds)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "当前任务ID的门店为空");
        }
        String[] wmPoiIdArray = wmPoiIds.split(CustomerConstants.SPLIT_SYMBOL);
        List<Long> wmPoiIdList = Lists.newArrayList();
        for (String wmPoiId : wmPoiIdArray) {
            wmPoiIdList.add(Long.parseLong(wmPoiId));
        }
        return wmPoiIdList;
    }

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    public WmEmploy getWmEmploy(Integer userId) {
        try {
            return wmEmployService.getById(userId);
        } catch (Exception e) {
            log.error("调用getWmEmploy异常", e);
        }
        return null;
    }


    /**
     * 取消解绑
     */
    public void cancelUnBind(int fromCustomerId, int toCustomerId, Long taskId, List<Long> wmPoiIdList)
            throws TException, WmCustomerException {
        log.info("cancelUnBind fromCustomerId:{},toCustomerId:{},taskId:{},wmPoiIdList:{}", fromCustomerId, toCustomerId, taskId, JSONObject.toJSONString(wmPoiIdList));
        /** 1.客户校验 **/
        VersionCheckUtil.versionCheck(fromCustomerId, 0);
        /** 2.电子签约取消 **/
        wmEcontractSignBzService.cancelSign(taskId, false);
        /** 3.取消kp确认短信 **/
        wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.SUCCESS.getType(), taskId);
    }

    /**
     * 处理门店取消客户切换任务
     *
     * @param switchCancelNotifyMsg
     */
    public void cancelPoiSwitch(CustomerSwitchCancelNotifyMsg switchCancelNotifyMsg) throws WmPoiBizException, TException {
        log.info("cancelPoiSwitch switchCancelNotifyMsg={}", JSONObject.toJSONString(switchCancelNotifyMsg));
        try {
            Preconditions.checkArgument(switchCancelNotifyMsg.getWmPoiId() != null && switchCancelNotifyMsg.getWmPoiId() > 0, "门店ID不能为空");
            Boolean rollback = switchCancelNotifyMsg.getRollback();
            Long fromCustomerId = new Long(switchCancelNotifyMsg.getFromBizId());
            Preconditions.checkArgument(fromCustomerId != null, "原客户ID不能为空");
            Long toCustomerId = new Long(switchCancelNotifyMsg.getToBizId());
            Preconditions.checkArgument(toCustomerId != null, "目标客户ID不能为空");
            boolean result = checkCanCancelSwitch(switchCancelNotifyMsg);
            if (!result) {
                log.warn("cancelPoiSwitch  checkCanCancelSwitch失败 msg={}", JSONObject.toJSONString(switchCancelNotifyMsg));
                return;
            }
            if (rollback != null && rollback) {
                rollbackByPoi(switchCancelNotifyMsg);
            } else {
                cancelByPoi(switchCancelNotifyMsg);
            }
            // 通知客户切换任务系统，ACK取消成功
            notifyCancelResult(switchCancelNotifyMsg, true);
        } catch (WmCustomerException | TException | RuntimeException e) {
            log.error("门店取消客户切换任务异常 notifyMsg:{}", JSONObject.toJSONString(switchCancelNotifyMsg), e);
            // 通知客户切换任务系统，ACK取消失败
            notifyCancelResult(switchCancelNotifyMsg, false);
        }
    }

    /**
     * 校验是否可以进行客户取消/回滚
     *
     * @param switchCancelNotifyMsg
     * @return
     */
    private boolean checkCanCancelSwitch(CustomerSwitchCancelNotifyMsg switchCancelNotifyMsg) {
        String status = WmCustomerConstant.SUCCESS;
        long taskId = switchCancelNotifyMsg.getTaskId();
        long wmPoiId = switchCancelNotifyMsg.getWmPoiId();
        String type = switchCancelNotifyMsg.getRollback() != null && switchCancelNotifyMsg.getRollback().booleanValue() == true ? "rollback" : "cancel";
        try {
            SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskIdRT(taskId, 0, "客户系统");
            if (switchTaskBo == null) {
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            if (switchTaskBo.getVersion() != null && switchTaskBo.getVersion().intValue() < CustomerSwitchVesionEnum.V4.getCode()) {
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            SwitchTaskPoiBo switchTaskPoiBo = wmPoiSwitchThriftService.getSwitchTaskPoiRT(taskId, wmPoiId);
            log.info("checkCanCancelSwitch switchTaskPoiBo={}", JSONObject.toJSONString(switchTaskPoiBo));
            if (switchTaskPoiBo == null) {
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            if (switchTaskPoiBo.getSwitchStatus() != PoiCustomerSwitchStatusEnum.SWITCH_CANCELING.getCode()) {
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            Map<Integer, PoiCancelTaskStatusEnum> moduleResultMap = PoiCancelTaskStatusEnum.getResultByBits(switchTaskPoiBo.getModuleCancelResult());
            if (moduleResultMap.containsKey(CustomerSwitchModuleTypeEnum.CUSTOMER.getCode())) {
                status = WmCustomerConstant.BUSINESS_EXCEPTION;
                return false;
            }
            if (type.equals("rollback")) {
                if (switchTaskPoiBo.getSwitchCondition() == null || switchTaskPoiBo.getSwitchCondition().intValue() != CustomerSwitchConditionEnum.SATISFIED.getCode()) {
                    status = WmCustomerConstant.BUSINESS_EXCEPTION;
                    return false;
                }
            }
            return true;
        } catch (WmPoiBizException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            log.warn("checkCancelSwitch失败 switchCancelNotifyMsg={}", JSONObject.toJSONString(switchCancelNotifyMsg), e);
        } catch (TException e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            log.error("checkCancelSwitch失败 switchCancelNotifyMsg={}", JSONObject.toJSONString(switchCancelNotifyMsg), e);
        } finally {
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getName())
                    .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getTag(), type)
                    .tag(CustomerMetricEnum.CUSTOMER_SWITCH_CHECK.getStatus(), status).count();
        }
        return true;
    }

    @TairLock(group = TairLockGroup.CANCEL_SWITCH_CUSTOMER, seedExp = "switchCancelNotifyMsg.taskId+'_'+switchCancelNotifyMsg.getWmPoiId()", grayKey = "switchCancelNotifyMsg.taskId", grayPercentKey = TairLockGrayPercentKeyEnum.CANCEL_SWITCH_CUSTOMER)
    private void cancelByPoi(CustomerSwitchCancelNotifyMsg switchCancelNotifyMsg) {
        Long fromCustomerId = new Long(switchCancelNotifyMsg.getFromBizId());
        Long toCustomerId = new Long(switchCancelNotifyMsg.getToBizId());

        // 未执行客户切换，恢复原客户-门店绑定关系
        wmCustomerPoiRelService.cancelSwitch(Sets.newHashSet(switchCancelNotifyMsg.getWmPoiId()), fromCustomerId.intValue(), toCustomerId.intValue(), switchCancelNotifyMsg.getTaskId());
        log.info("(客户切换门店取消操作)未执行客户切换，恢复原客户-门店绑定关系 notifyMsg:{}", JSONObject.toJSONString(switchCancelNotifyMsg));
    }

    @TairLock(group = TairLockGroup.ROLLBACK_SWITCH_CUSTOMER, seedExp = "switchCancelNotifyMsg.taskId+'_'+switchCancelNotifyMsg.getWmPoiId()", grayKey = "switchCancelNotifyMsg.taskId", grayPercentKey = TairLockGrayPercentKeyEnum.ROLLBACK_SWITCH_CUSTOMER)
    private void rollbackByPoi(CustomerSwitchCancelNotifyMsg switchCancelNotifyMsg) throws TException, WmCustomerException {
        Long fromCustomerId = new Long(switchCancelNotifyMsg.getFromBizId());
        Long toCustomerId = new Long(switchCancelNotifyMsg.getToBizId());
        Boolean rollback = switchCancelNotifyMsg.getRollback();
        // 已执行客户切换，需要回滚数据
        WmCustomerPoiAggre customerPoiAggre = WmCustomerPoiAggre.Factory.make();
        // 目标客户解绑
        customerPoiAggre.customerUnBindPoiForCusSwitch(Sets.newHashSet(switchCancelNotifyMsg.getWmPoiId()), toCustomerId, 0, "客户切换任务系统回滚", switchCancelNotifyMsg.getTaskId(), rollback);
        log.info("(客户切换门店取消回滚操作)目标客户[{}]与门店[{}]解绑成功", toCustomerId, switchCancelNotifyMsg.getWmPoiId());
        // 原客户绑定
        customerPoiAggre.customerBindPoiForCusSwitch(fromCustomerId, Sets.newHashSet(switchCancelNotifyMsg.getWmPoiId()), 0, "客户切换任务系统回滚", switchCancelNotifyMsg.getTaskId(), true, null);
        log.info("(客户切换门店取消回滚操作)原客户[{}]与门店[{}]绑定成功", fromCustomerId, switchCancelNotifyMsg.getWmPoiId());
    }


    /**
     * ACK取消结果
     *
     * @param switchCancelNotifyMsg
     * @param cancelSuc
     */
    private void notifyCancelResult(CustomerSwitchCancelNotifyMsg switchCancelNotifyMsg, boolean cancelSuc) {
        RefreshPoiCancelTaskModuleReqBo cancelResultBo = new RefreshPoiCancelTaskModuleReqBo();
        cancelResultBo.setOpUname(switchCancelNotifyMsg.getOpName());
        cancelResultBo.setOpUid(switchCancelNotifyMsg.getOpUid());
        cancelResultBo.setWmPoiId(switchCancelNotifyMsg.getWmPoiId());
        cancelResultBo.setTaskId(switchCancelNotifyMsg.getTaskId());
        cancelResultBo.setModule(CustomerSwitchModuleTypeEnum.CUSTOMER.getCode());
        cancelResultBo.setCancelTaskStatus(cancelSuc ? PoiCancelTaskStatusEnum.SWITCH_CANCELED.getCode() : PoiCancelTaskStatusEnum.SWITCH_CANCEL_FAILED.getCode());
        try {
            wmPoiSwitchThriftService.refreshPoiCancelTaskModuleResult(cancelResultBo);
        } catch (WmPoiBizException | TException e) {
            log.error("门店取消客户切换任务取消结果ACK失败，notifyMsg:{}, cancelSuc:{}", JSONObject.toJSONString(switchCancelNotifyMsg), cancelSuc, e);
//            throw e;
        }
    }


    /**
     * 检查门店切换客户是否成功
     *
     * @param fromCustomerId
     * @param toCustomerId
     * @param wmPoiId
     * @return
     */
    public boolean checkPoiSwitchCustomerSuc(Long fromCustomerId, Long toCustomerId, Long wmPoiId) {
        List<WmCustomerPoiDB> toCustomerPoiRelList = wmCustomerPoiDBMapper.selectCustomerPoiRelRT(toCustomerId, wmPoiId);
        boolean toCustomerBindPoiSuc = org.apache.commons.collections4.CollectionUtils.isNotEmpty(toCustomerPoiRelList) && toCustomerPoiRelList.size() == 1;
        List<WmCustomerPoiDB> fromCustomerPoiRelList = wmCustomerPoiDBMapper.selectCustomerPoiRelRT(fromCustomerId, wmPoiId);
        boolean fromCustomerUnbindPoiSuc = org.apache.commons.collections4.CollectionUtils.isEmpty(fromCustomerPoiRelList);
        return toCustomerBindPoiSuc && fromCustomerUnbindPoiSuc;
    }

    /**
     * 检查门店取消切换客户是否成功
     *
     * @param fromCustomerId
     * @param toCustomerId
     * @param wmPoiId
     * @return
     */
    public boolean checkPoiCancelSwitchCustomerSuc(Long fromCustomerId, Long toCustomerId, Long wmPoiId) {
        List<WmCustomerPoiDB> toCustomerPoiRelList = wmCustomerPoiDBMapper.selectCustomerPoiRelRT(toCustomerId, wmPoiId);
        boolean toCustomerUnbindPoiSuc = org.apache.commons.collections4.CollectionUtils.isEmpty(toCustomerPoiRelList);
        List<WmCustomerPoiDB> fromCustomerPoiRelList = wmCustomerPoiDBMapper.selectCustomerPoiRelRT(fromCustomerId, wmPoiId);
        boolean fromCustomerBindPoiSuc = org.apache.commons.collections4.CollectionUtils.isNotEmpty(fromCustomerPoiRelList) && fromCustomerPoiRelList.size() == 1;
        return toCustomerUnbindPoiSuc && fromCustomerBindPoiSuc;
    }


    /**
     * 检查门店切换客户是否成功
     *
     * @param monitorCustomerSwitchDTO
     * @return
     */
    public boolean checkPoiSwitchCustomerSucV2(MonitorCustomerSwitchDTO monitorCustomerSwitchDTO) {
        long wmPoiId = monitorCustomerSwitchDTO.getWmPoiId();
        Integer switchTime = monitorCustomerSwitchDTO.getSwitchTime();
        Long toCustomerId = monitorCustomerSwitchDTO.getToCustomerId();
        return checkCustomerPoiRel(wmPoiId, switchTime, toCustomerId);
    }

    /**
     * 检查门店取消切换客户是否成功
     *
     * @param monitorCustomerSwitchDTO
     * @return
     */
    public boolean checkPoiCancelSwitchCustomerSucV2(MonitorCustomerSwitchDTO monitorCustomerSwitchDTO) {
        long wmPoiId = monitorCustomerSwitchDTO.getWmPoiId();
        Integer cancelTime = monitorCustomerSwitchDTO.getCancelTime();
        Long fromCustomerId = monitorCustomerSwitchDTO.getFromCustomerId();
        return checkCustomerPoiRel(wmPoiId, cancelTime, fromCustomerId);
    }

    /**
     * 校验客户门店关系是否符合预期
     *
     * @param wmPoiId
     * @param opTime
     * @param customerId
     * @return
     */
    private boolean checkCustomerPoiRel(long wmPoiId, Integer opTime, Long customerId) {
        // 历史数据不校验
        if (opTime == null) {
            return true;
        }
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectCustomerPoiRelByWmPoiIdRT(wmPoiId);
        // 门店绑定多个客户，校验不通过
        if (!CollectionUtils.isEmpty(wmCustomerPoiDBList) && wmCustomerPoiDBList.size() > 1) {
            return false;
        }
        // 取消切换或切换成功之后，门店没有客户，需要排除取消完成之后用户手动解绑的情况
        if (CollectionUtils.isEmpty(wmCustomerPoiDBList)) {
            List<WmCustomerPoiSmsRecordDB> wmCustomerPoiSmsRecordDBS = wmCustomerPoiSmsRecordMapper.selectSmsRecordList(wmPoiId, opTime);
            // 排除用户进行手动解绑
            if (CollectionUtils.isEmpty(wmCustomerPoiSmsRecordDBS)) {
                WmCustomerPoiOplogBo wmCustomerPoiOplogBo = new WmCustomerPoiOplogBo();
                wmCustomerPoiOplogBo.setWmPoiId(wmPoiId);
                wmCustomerPoiOplogBo.setOperateType(WmCustomerPoiOplogOperateTypeEnum.UNBIND.getCode());
                wmCustomerPoiOplogBo.setCtime(opTime.intValue());
                List<WmCustomerPoiOplogDB> wmCustomerPoiOplogDBS = wmCustomerPoiOplogMapper.selectByCondition(wmCustomerPoiOplogBo);
                // 未知解绑操作
                if (CollectionUtils.isEmpty(wmCustomerPoiOplogDBS)) {
                    return false;
                }
                List<Integer> notAllowSourceList = Lists.newArrayList(WmCustomerPoiOplogSourceTypeEnum.CONFIRM_UNBIND.getCode(),
                        WmCustomerPoiOplogSourceTypeEnum.FORCE_UNBIND.getCode(), WmCustomerPoiOplogSourceTypeEnum.CUSTOMER_SWITCH.getCode());
                // 异常解绑动作(无短信确认任务确触发短信确认解绑/短信强制解绑、客户切换解绑)
                Optional<WmCustomerPoiOplogDB> result = wmCustomerPoiOplogDBS.stream().filter(x -> notAllowSourceList.contains(x.getSourceType())).findFirst();
                if (result.isPresent()) {
                    return false;
                }
            }

        } else {
            WmCustomerPoiDB wmCustomerPoiDB = wmCustomerPoiDBList.get(0);
            // 当前门店仍绑定在预期客户上，校验通过
            if (wmCustomerPoiDB.getCustomerId() != null && wmCustomerPoiDB.getCustomerId().intValue() == customerId.intValue()) {
                return true;
            } else {
                WmCustomerPoiOplogBo wmCustomerPoiOplogBo = new WmCustomerPoiOplogBo();
                wmCustomerPoiOplogBo.setWmPoiId(wmPoiId);
                wmCustomerPoiOplogBo.setOperateType(WmCustomerPoiOplogOperateTypeEnum.BIND.getCode());
                wmCustomerPoiOplogBo.setCtime(opTime.intValue());
                List<WmCustomerPoiOplogDB> wmCustomerPoiOplogDBS = wmCustomerPoiOplogMapper.selectByCondition(wmCustomerPoiOplogBo);
                // 未知解绑操作
                if (CollectionUtils.isEmpty(wmCustomerPoiOplogDBS)) {
                    return false;
                }
                // 异常绑定动作(客户切换绑定)
                Optional<WmCustomerPoiOplogDB> result = wmCustomerPoiOplogDBS.stream().filter(x -> x.getSourceType() == WmCustomerPoiOplogSourceTypeEnum.CUSTOMER_SWITCH.getCode())
                        .findFirst();
                if (result.isPresent()) {
                    return false;
                }
            }
        }
        return true;
    }


    /**
     * 判断短信是否是切换中任务
     *
     * @param wmCustomerPoiSmsRecordDB
     * @return
     * @throws WmCustomerException
     */
    public Long checkFromSwitchUnBind(WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB) throws WmCustomerException {
        Long switchTaskId = null;
        List<Long> wmPoiIdList = convertWmPoiIds(wmCustomerPoiSmsRecordDB.getWmPoiIds());
        int customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        int type = wmCustomerPoiSmsRecordDB.getType();
        if (type != WmCustomerSmsTypeEnum.OLD_CUSTOMER_UNBIND.getCode()) {
            return null;
        }
        try {
            int smsId = wmCustomerPoiSmsRecordDB.getId();
            Integer bizTaskId = customerTaskService.getSwitchTaskIdBySmsIdAndTaskType(smsId, CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI);
            if (bizTaskId != null && bizTaskId.intValue() > 0) {
                switchTaskId = Long.valueOf(bizTaskId);
            } else {
                // 获取流程中切换任务
                SwitchPoiQueryDTO switchPoiQueryDTO = new SwitchPoiQueryDTO();
                switchPoiQueryDTO.setWmPoiIdList(wmPoiIdList);
                switchPoiQueryDTO.setStatusList(Lists.newArrayList(WmPoiSwitchTaskStatusEnum.ORIGIN_CUS_CONFIRMING.getCode()));
                switchPoiQueryDTO.setFromBizId(String.valueOf(customerId));
                switchTaskId = wmPoiSwitchThriftService.getSwitchTaskIdPoiDoingLatest(switchPoiQueryDTO);
            }
        } catch (WmPoiBizException e) {
            log.error("checkFromSwitchUnbind error wmCustomerPoiSmsRecordDB={}", JSONObject.toJSONString(wmCustomerPoiSmsRecordDB), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "判断任务来源失败");
        } catch (TException e) {
            log.error("checkFromSwitchUnbind error wmCustomerPoiSmsRecordDB={}", JSONObject.toJSONString(wmCustomerPoiSmsRecordDB), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "判断任务来源失败");
        }
        return switchTaskId;
    }

    /**
     * 判断短信是否是切换中任务
     *
     * @param wmCustomerPoiSmsRecordDB
     * @return
     * @throws WmCustomerException
     */
    public Long checkFromSwitchBind(WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB) throws WmCustomerException {
        Long switchTaskId = null;
        List<Long> wmPoiIdList = convertWmPoiIds(wmCustomerPoiSmsRecordDB.getWmPoiIds());
        int customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        if (wmCustomerPoiSmsRecordDB.getType() != WmCustomerSmsTypeEnum.NEW_CUSTOMER_BIND.getCode()) {
            return null;
        }
        try {
            int smsId = wmCustomerPoiSmsRecordDB.getId();
            Integer bizTaskId = customerTaskService.getSwitchTaskIdBySmsIdAndTaskType(smsId, CustomerTaskTypeEnum.CUSTOMER_BIND_POI);
            if (bizTaskId != null && bizTaskId.intValue() > 0) {
                switchTaskId = Long.valueOf(bizTaskId);
            } else {
                // 获取流程中切换任务
                SwitchPoiQueryDTO switchPoiQueryDTO = new SwitchPoiQueryDTO();
                switchPoiQueryDTO.setWmPoiIdList(wmPoiIdList);
                switchPoiQueryDTO.setStatusList(Lists.newArrayList(WmPoiSwitchTaskStatusEnum.NEW_CUS_PRE_BINDING.getCode(), WmPoiSwitchTaskStatusEnum.NEW_CUS_TO_APPLY_PRE_BIND.getCode()));
                switchPoiQueryDTO.setToBizId(String.valueOf(customerId));
                switchTaskId = wmPoiSwitchThriftService.getSwitchTaskIdPoiDoingLatest(switchPoiQueryDTO);
            }
        } catch (WmPoiBizException e) {
            log.error("checkFromSwitchBind error wmCustomerPoiSmsRecordDB={}", JSONObject.toJSONString(wmCustomerPoiSmsRecordDB), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "判断任务来源失败");
        } catch (TException e) {
            log.error("checkFromSwitchBind error wmCustomerPoiSmsRecordDB={}", JSONObject.toJSONString(wmCustomerPoiSmsRecordDB), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "判断任务来源失败");
        }
        return switchTaskId;
    }

    /**
     * 客户切换根据新客户判断是否需要批量掉标
     *
     * @param wmPoiIdSet
     * @param toCustomerId
     */
    private void batchDelQuaComPoiTag(Set<Long> wmPoiIdSet, Long toCustomerId, Integer opUid, String opName) {
        try {
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(toCustomerId.intValue());
            if (wmCustomerDB == null) {
                log.warn("batchDelQuaComPoiTag,客户切换原客户解绑完成未查询到有效的新客户信息,toCustomerId={}", toCustomerId);
                return;
            }
            boolean hasQuaComCustomerTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
            if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() && hasQuaComCustomerTag) {
                return;
            }
            //如果门店有资质共用标签则都需要删除
            wmCustomerMSCLabelService.deleteMscQuaCommonPoiTag(wmPoiIdSet, opUid, opName);
        } catch (Exception e) {
            log.error("batchDelQuaComPoiTag,批量删除资质公用门店标发生异常,wmPoiIdSet={},toCustomerId={}", JSON.toJSONString(wmPoiIdSet), toCustomerId);
        }

    }
}
