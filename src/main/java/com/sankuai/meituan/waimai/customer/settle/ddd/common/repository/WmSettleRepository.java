package com.sankuai.meituan.waimai.customer.settle.ddd.common.repository;

import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.daoadapter.WmSettleAuditedDBMapperAdapter;
import com.sankuai.meituan.waimai.customer.settle.daoadapter.WmSettleDBMapperAdapter;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * wm_settle、wm_settle_audited 资源操作
 */
@Service
public class WmSettleRepository {

    @Autowired
    private WmSettleDBMapper wmSettleDBMapper;
    @Autowired
    private WmSettleDBMapperAdapter wmSettleDBMapperAdapter;
    @Autowired
    private WmSettleAuditedDBMapper wmSettleAuditedDBMapper;
    @Autowired
    private WmSettleAuditedDBMapperAdapter wmSettleAuditedDBMapperAdapter;

    public void updateByPrimaryKeySelectiveWithValid(WmSettleDB wmSettleDB) {
        wmSettleDBMapperAdapter.updateByPrimaryKeySelectiveWithValid(wmSettleDB);
    }

    public int insertSelective(WmSettleDB wmSettleDB) {
        return wmSettleDBMapperAdapter.insertSelective(wmSettleDB);
    }

    public List<WmSettleAuditedDB> batchGetWmSettleAuditedByWmSettleIds(String wmSettleIds) {
        return wmSettleAuditedDBMapper.batchGetWmSettleAuditedByWmSettleIds(wmSettleIds);
    }

    public int insertSelective(WmSettleAuditedDB wmSettleAuditedDB) {
        return wmSettleAuditedDBMapperAdapter.insertSelective(wmSettleAuditedDB);
    }

    public void updateByWmSettleId(WmSettleAuditedDB wmSettleAuditedDB) {
        wmSettleAuditedDBMapperAdapter.updateByWmSettleId(wmSettleAuditedDB);
    }

    public void deleteByWmContractIdAndWmSettleId(Integer wmCustomerId, String wmSettleIds) {
        wmSettleAuditedDBMapper.deleteByWmContractIdAndWmSettleId(wmCustomerId,wmSettleIds);
    }
}
