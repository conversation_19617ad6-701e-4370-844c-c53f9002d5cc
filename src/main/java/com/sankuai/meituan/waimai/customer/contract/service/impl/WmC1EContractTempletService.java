package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.C1_E})
public class WmC1EContractTempletService extends AbstractWmEContractTempletService {

    private static Logger logger = LoggerFactory.getLogger(WmC1EContractTempletService.class);

    private static final String DEFAULT_PARTB_NAME = "北京三快在线科技有限公司";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
        Boolean effect = super.effect(templetContractId, opUid, opUname);
        wmContractService.saveWmContractFtlTagDB(templetContractId, opUid, opUname);
        WmTempletContractDB contractInDb = wmTempletContractDBMapper.selectByPrimaryKey(templetContractId);
        wmContractService.recordCustomerPoiSubject(contractInDb, opUid, opUname);
        logger.info("c1e effect结束时间:{},templetContractId:{}", System.currentTimeMillis(),templetContractId);
        return effect;
    }

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = wmContractService
                .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
        Integer contractId = insertOrUpdate(contractBo, opUid, opName);

        //新建并发起签约时，同步暂存状态
        int status = MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus();
        if (status != contractBo.getBasicBo().getStatus()) {
            contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
        }

        contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
        if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {
            try {
                toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
                contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
                contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                        contractBo.getBasicBo(), opUid, opName);
                ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(), opUid);
                wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
            } catch (WmCustomerException ec) {
                logger.error("【框架合同】发起待打包合同任务失败 contractBo:{} msg:{}", JSON.toJSONString(contractBo), ec.getMsg());
                throw ec;
            } catch (Exception e) {
                logger.error("【框架合同】发起待打包合同任务失败 contractBo:{}", JSON.toJSONString(contractBo), e);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
            }
        } else {
            toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), opName);
            WmContractVersionDB versionDB = saveVersion(opUid, opName, contractId, CustomerContractStatus.SIGNING.getCode());
            contractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());
            contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
            applySign(contractBo, versionDB, opUid, opName);
        }
        return contractId;
    }

    @Override
    public Long startSignForManualPack(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
        if (contractBo.getPackWay() != SignPackWay.WAIT_HAND_PACK.getCode()) {
            //该方法只处理手动打包场景
            return null;
        }
        WmCustomerContractBo oldBo = wmContractService
                .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
        Integer contractId = insertOrUpdate(contractBo, opUid, opName);

        //新建并发起签约时，同步暂存状态
        int status = MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus();
        if (status != contractBo.getBasicBo().getStatus()) {
            contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
        }

        contractLogService.logUpdate(oldBo, contractBo, opUid, opName);


        toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
        contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
        contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);

        ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(), opUid);
        LongResult longResult = wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
        return longResult.getValue();
    }

    @Override
    public Integer startSignForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        if (contractBo == null || contractBo.getBasicBo() == null || contractBo.getBasicBo().getTempletContractId() <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同不存在不能发起签约");
        }
        Integer contractId = (int) contractBo.getBasicBo().getTempletContractId();
        WmCustomerContractBo oldBo = wmContractService.getWmCustomerContractBoById(contractId, false, opUid, opName);

        if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {
            toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
            contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
            contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
            ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(), opUid);
            wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
        } else {
            toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), opName);
            WmContractVersionDB versionDB = saveVersion(opUid, opName, contractId, CustomerContractStatus.SIGNING.getCode());
            contractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());
            contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                    contractBo.getBasicBo(), opUid, opName);
            applySign(contractBo, versionDB, opUid, opName);
        }
        return contractId;
    }


    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(wmCustomerId)
                .applyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT)
                .bizId(wmCustomerId)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        if(ConfigUtilAdapter.getBoolean("c1renew_open_switch",true)){
            ContractCheckFilter.c1RenewOldFeemodeCustomerFilter().whiteListFilter(contractBo, opUid, opName);// 针对C1续签新增旧费率门店卡控校验
        }
        Integer insertId = super.save(contractBo, opUid, opName);
        String c1ENum = ContractNumberUtil.genC1ENum(insertId);
        contractBo.getBasicBo().setContractNum(c1ENum);
        logger.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, c1ENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), c1ENum);
        return insertId;
    }

    @Override
    protected EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) {
        WmTempletContractSignBo partyASigner = wmContractSignService.getPartyASigner(contractBo.getSignBoList());
        WmTempletContractSignBo partyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());
        WmTempletContractBasicBo wmTempletContractBasicBo = contractBo.getBasicBo();

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);
        applyBo.setWmCustomerId(contractBo.getBasicBo().getParentId());

        EcontractContractInfoBo contractInfoBo = new EcontractContractInfoBo();
        contractInfoBo.setContractNum(contractBo.getBasicBo().getContractNum());
        contractInfoBo.setValidate(DateUtil.secondsToString(contractBo.getBasicBo().getDueDate()));

        WmCustomerKp kpOfSigner = wmCustomerKpService.getCustomerKpOfEffectiveSigner(contractBo.getBasicBo().getParentId());
        contractInfoBo.setPartAName(partyASigner.getSignName());
        contractInfoBo.setPartASignTime(partyASigner.getSignTime());

        if (kpOfSigner != null) {
            contractInfoBo.setPartAContact(kpOfSigner.getCompellation());
            contractInfoBo.setPartAContactPhone(kpOfSigner.getPhoneNum());
        }

        if (MccConfig.getXingHuoGrayCustomerIdsBoolean(contractBo.getBasicBo().getParentId())) {
            contractInfoBo.setPerformanceServiceFeeName(StringUtils.isEmpty(wmTempletContractBasicBo.getLogisticsSubject
                    ()) ? DEFAULT_PARTB_NAME : wmTempletContractBasicBo.getLogisticsSubject());
            contractInfoBo.setPartBName(StringUtils.isEmpty(partyBSigner.getSignName()) ? DEFAULT_PARTB_NAME : partyBSigner
                    .getSignName());
        } else {
            contractInfoBo.setPartBName(DEFAULT_PARTB_NAME);
        }
        contractInfoBo.setPartBContact(partyBSigner.getSignPeople());
        contractInfoBo.setPartBContactPhone(partyBSigner.getSignPhone());
        contractInfoBo.setPartBSignTime(partyBSigner.getSignTime());
        boolean isMedicineCustomer = false;
        try{
            isMedicineCustomer = wmCustomerService.isMedicineTypeCustomer(contractBo.getBasicBo().getParentId());
        }catch(WmCustomerException e){
            logger.warn("获取客户:{}类型异常",contractBo.getBasicBo().getParentId(), e);
        }
        contractInfoBo.setMedicineTypeCustomer(isMedicineCustomer);
        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
        return applyBo;
    }

    public void toNextStatus(int contractId, int toStatus, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("#toNextStatus# contractId:{} toStatus:{} opUid:{} opUname:{}",
                contractId, toStatus, opUid, opUname);
        WmTempletContractBasicBo basic = wmContractService.getBasicById(contractId, false, opUid, opUname);
        logger.info("查询线下合同信息 id:{} ：{}", contractId, JSON.toJSON(basic));
        int oldStatus = basic.getStatus();
        toNextContractStatus(contractId, toStatus, opUname);
        if (oldStatus == CustomerContractStatus.SIGNING.getCode() || oldStatus == CustomerContractStatus.TO_EFFECT.getCode()) {
            toNextContractVersionStatus(contractId, toStatus);
        }
    }


    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        throw new UnsupportedOperationException("非法操作，不支持废除操作。合同id:" + contractId + ", opUid:" + opUid + ", opUname:" + opUname);
    }

    @Override
    public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        if (ConfigUtilAdapter.getBoolean("c1renew_open_switch", true)) {
            ContractCheckFilter.c1RenewOldFeemodeCustomerFilter().whiteListFilter(contractBo, opUid, opName);// 针对C1续签新增旧费率门店卡控校验
        }
        ContractCheckFilter.c1SgLowFeeResignFilter().filter(contractBo, opUid, opName);
        return super.update(contractBo, opUid, opName);
    }
}
