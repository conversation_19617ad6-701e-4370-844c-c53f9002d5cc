package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.*;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.UN_VALID;

/**
 * <AUTHOR>
 * @date 20240422
 * @desc 审核通过直接生效
 */
@Service
@Slf4j
public class AuditSuc2EffectAction extends KpSignerAbstractAction {

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    /**
     * 证件类型为身份证
     */
    private static final List<Integer> ID_CERT_TYPE = Lists.newArrayList(Integer.valueOf(CertTypeEnum.ID_CARD.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_TEMP.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_COPY.getType()));


    /**
     * 动作执行
     *
     * @param fromState      原状态
     * @param toState        新状态
     * @param eventEnum      事件
     * @param context        上下文
     * @param kpSignerBaseSM 状态机
     */
    @Override
    public void execute(KpSignerStateMachine fromState, KpSignerStateMachine toState,
                        KpSignerEventEnum eventEnum, KpSignerStatusMachineContext context,
                        KpSignerBaseSM kpSignerBaseSM) {
        log.info("AuditSuc2EffectAction,代理人审核通过直接生效action开始执行,from={},to={},context={}", fromState, toState, JSON.toJSONString(context));

        boolean haveEffectFlag = context.getExistEffectiveFlag();
        WmCustomerKp singerKp = context.getWmCustomerKp();
        WmCustomerKpAudit audit = context.getWmCustomerKpAudit();

        try {
            //读取签约人KP明文信息
            wmCustomerSensitiveWordsService.readKpWhenSelect(singerKp);
            //未生效过
            if (!haveEffectFlag) {
                wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "签约人生效", context.getOpUid(), context.getOpUName());
                singerKp.setState(KpSignerStateMachine.EFFECT.getState());
                singerKp.setEffective(KpConstants.EFFECTIVE);
                //签约人KP生效发送MQ通知
                singerKp.setFailReason("");
                WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(singerKp.getId());
                wmCustomerKpBuryingPointService.afterSingKp(null, audit.getAcctId(), kpTemp == null ? null : wmCustomerKpAuditService.transformWmCustomerKpTemp(kpTemp), singerKp);
                wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(singerKp);
                wmCustomerKpDBMapper.updateByPrimaryKey(singerKp);
            } else {
                //生效过
                WmCustomerKpTemp kpTemp = context.getTempKp();
                wmCustomerSensitiveWordsService.readKpWhenSelect(kpTemp);
                wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "变更签约人生效", context.getOpUid(), context.getOpUName());
                WmCustomerKp tempKp = wmCustomerKpAuditService.transformWmCustomerKpTemp(kpTemp);
                tempKp.setEffective(KpConstants.EFFECTIVE);
                wmCustomerKpBuryingPointService.afterSingKp(null, audit.getAcctId(), tempKp, singerKp);

                wmCustomerKpService.tempKpEffect(kpTemp, singerKp);
                //签约人KP生效发送MQ通知
                kpTemp.setValid(UN_VALID);
                wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTemp);
                wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
            }
            String result = KpAuditConstants.TYPE_SPECIAL == audit.getType() ? "特批审核通过" : "代理人审核通过";
            audit.setResult(result);
            audit.setValid(UN_VALID);
            wmCustomerKpAuditMapper.updateByPrimaryKey(audit);

            /**
             * 满足如下条件掉客户四要素标签
             *  1.审核通过
             *  2.生效
             *  3.签约类型为非签约人或者证件类型为非身份证
             */
            if (context.isSendEffectiveMq() && (singerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()
                    || !ID_CERT_TYPE.contains((int) singerKp.getCertType()))) {
                wmCustomerKpRealAuthService.deleteFourEleTag(singerKp.getCustomerId());
            }
            //发生消息标记为true则需要发消息
            if (context.isSendEffectiveMq()) {
                mafkaMessageSendManager.send(new CustomerMQBody(singerKp.getCustomerId(), CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
            }
        } catch (Exception e) {
            log.error("AuditSuc2EffectAction.execute,审核成功直接生效流程异常,context={}", JSON.toJSONString(context), e);
            throw new StatusMachineException("审核成功直接生效流程异常");
        }


    }
}
