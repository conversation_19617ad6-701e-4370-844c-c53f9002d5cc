package com.sankuai.meituan.waimai.customer.service.sc.mafka;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.ImmutableList;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.adapter.WdcPoiQueryServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallClueBindService;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import com.sankuai.meituan.waimai.wdc.domain.service.common.vo.WdcPoiView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class WdcInvalidStallListener implements IMessageListener {

    @Autowired
    private WdcPoiQueryServiceAdapter wdcPoiQueryServiceAdapter;
    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;
    @Autowired
    private WmCanteenStallClueBindService wmCanteenStallClueBindService;
    @Autowired
    private WmEmployClient wmEmployClient;
    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        try {
            log.info("[WdcInvalidStallListener.recvMessage] input param: message={}, partition={}", message.getBody(), message.getParttion());

            if (!MccScConfig.getWdcConsumerSwitch()) {
                log.info("[WdcInvalidStallListener.recvMessage] switch down");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            if (null == message.getBody() || StringUtils.isBlank(message.getBody().toString())) {
                log.error("[WmBaseInfoPoiOperateEventListener.recvMessage] message body is null, return. message = {}", JSONObject.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            JSONObject jsonObject = JSONObject.parseObject(message.getBody().toString());
            Long wdcId = jsonObject.getLong("wdcId");
            if (Objects.isNull(wdcId)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            List<WmCanteenStallBindDO> wmCanteenStallBindDOS = wmCanteenStallBindMapper.selectByWdcClueId(wdcId);
            if (CollectionUtils.isEmpty(wmCanteenStallBindDOS)) {
                log.info("wdcId:{}, not bind with stall", wdcId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            WdcPoiView wdcPoiView = wdcPoiQueryServiceAdapter.getWdcPoiViewByWdcClueId(wdcId);
            if (Objects.isNull(wdcPoiView)) {
                log.warn("WdcPoiUpdateListener cant find wdc poi with wdcId={}", wdcId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            boolean wdcValid = wdcPoiView.getValid() == 0;
            if (wdcValid) {
                log.info("wdcId:{} is valid, dont process", wdcId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            for (WmCanteenStallBindDO bindDO : wmCanteenStallBindDOS) {
                if ((bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType())
                        || bindDO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BINDING.getType()))
                        && bindDO.getWdcClueId() > 0) {
                    wmCanteenStallClueBindService.unbindWdcClue(bindDO, 0, "系统(system)", "wdc倒闭");

                    try{
                        //门店释放添加大象通知
                        if(Objects.isNull(bindDO.getCanteenPrimaryId())) {
                            throw new Exception("canteenPrimaryId is null");
                        }
                        Integer canteenPrimaryId = bindDO.getCanteenPrimaryId();
                        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
                        if(Objects.isNull(wmCanteenDB)){
                            throw new Exception("canteenDB is null");
                        }
                        if(Objects.isNull(wmCanteenDB.getResponsiblePerson())){
                            throw new Exception("canteenDB responsiblePerson is null");
                        }
                        String responsiblePersonMisId = wmCanteenDB.getResponsiblePerson();
                        String msg = "线索已失效，相关线索解除和食堂绑定。\n 线索编号:" + bindDO.getWdcClueId();
                        DaxiangUtilV2.push(msg, responsiblePersonMisId);
                    }catch (Exception e){
                        log.error("WdcInvalidStallListener.recvMessage 门店释放添加大象通知失败", e);
                        return ConsumeStatus.RECONSUME_LATER;
                    }
                }
            }
            return ConsumeStatus.CONSUME_SUCCESS;

        } catch (Exception e) {
            log.error("[WdcInvalidStallListener.recvMessage] handle error", e);
            return ConsumeStatus.RECONSUME_LATER;
        }

    }




}
