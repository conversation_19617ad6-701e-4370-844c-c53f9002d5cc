package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import java.util.List;

import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractSpInteractionWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * 组装特殊交互
 */
@Service
@Slf4j
public class WmEcontractDataSpInteractionCollector implements IWmEcontractDataCollector {

    @Autowired
    private WmEcontractSpInteractionWrapperService wmEcontractSpInteractionWrapperService;

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext)
            throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractDataSpInteractionCollector");
        List<String> flowList = targetContext.getFlowList();
        //无配送模块流程则不处理
        if (!flowList.contains(SignTemplateConstant.TAB_DELIVERY)) {
            return;
        }
        // TODO liuyunjie05 2025/6/3 国补的合同暂时不处理
        if (EcontractBatchTypeEnum.isNationalSubsidyDeliveryType(originContext.getBatchTypeEnum())) {
            return;
        }

        EcontractSignDataFactor signDataFactor = middleContext.getSignDataFactor();
        List<StageBatchInfoBo> stageInfoBoList = targetContext.getStageInfoBoList();
        //配送多店
        if (signDataFactor.isDeliveryMultiWmPoi()) {
            wmEcontractSpInteractionWrapperService.addContext(stageInfoBoList, originContext, EcontractDataWrapperEnum.BATCH_DELIVERY);
        }
        //配送单店
        else {
            wmEcontractSpInteractionWrapperService.addContext(stageInfoBoList, originContext, EcontractDataWrapperEnum.DELIVERY);
        }
    }
}
