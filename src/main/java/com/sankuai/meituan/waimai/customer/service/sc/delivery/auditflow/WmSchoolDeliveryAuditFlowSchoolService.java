package com.sankuai.meituan.waimai.customer.service.sc.delivery.auditflow;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.*;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.auditTask.WmSchoolDeliveryTaskService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.sankuai.meituan.waimai.customer.service.sc.delivery.auditflow.WmSchoolDeliveryAuditFlowTicketService.TICKET_SYSTEM_DEFAULT_REMARK;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

@Slf4j
@Service
public class WmSchoolDeliveryAuditFlowSchoolService {


    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmSchoolDeliveryAuditTaskMapper wmSchoolDeliveryAuditTaskMapper;

    @Autowired
    private List<WmSchoolDeliveryTaskService> wmSchoolDeliveryTaskServiceList;

    @Autowired
    private WmSchoolDeliveryAuditFlowGravityService wmSchoolDeliveryAuditFlowGravityService;

    @Autowired
    private WmSchoolDeliveryAuditTaskNodeMapper wmSchoolDeliveryAuditTaskNodeMapper;

    @Autowired
    private WmSchoolDeliveryStreamDetailMapper wmSchoolDeliveryStreamDetailMapper;

    @Autowired
    private WmSchoolDeliveryStreamMapper wmSchoolDeliveryStreamMapper;

    private Optional<WmSchoolDeliveryTaskService> getTaskServiceByDeliveryNodeType(Integer deliveryNodeType) {
        if (deliveryNodeType == null) {
            return Optional.empty();
        }
        return wmSchoolDeliveryTaskServiceList.stream().filter(i -> i.getTaskTypeByDeliveryNodeType().equals(deliveryNodeType)).findAny();
    }

    /**
     * 审批任务主表创建主任务
     * @param auditTaskBO auditTaskBO
     * @return WmSchoolDeliveryAuditTaskDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmSchoolDeliveryAuditTaskDO createDeliveryAuditTaskBySubmit(WmSchoolDeliveryAuditTaskBO auditTaskBO) throws WmSchCantException {
        log.info("[WmSchoolDeliveryAuditTaskService.createDeliveryAuditTaskBySubmit] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        Optional<WmSchoolDeliveryTaskService> taskService = getTaskServiceByDeliveryNodeType(auditTaskBO.getDeliveryNodeType());
        if (!taskService.isPresent()) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "未获取到对应服务");
        }

        WmSchoolDeliveryAuditTaskDO taskDO = new WmSchoolDeliveryAuditTaskDO();
        taskDO.setDeliveryId(auditTaskBO.getDeliveryId());
        taskDO.setSchoolPrimaryId(auditTaskBO.getSchoolPrimaryId());
        taskDO.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.getByCode(auditTaskBO.getDeliveryNodeType()).getCode());
        taskDO.setAuditTaskType(taskService.get().getAuditTaskTypeByDeliveryId(auditTaskBO.getDeliveryId()));
        taskDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDITING.getType());
        taskDO.setDataVersion(auditTaskBO.getDataVersion());
        taskDO.setLastAuditStatus(taskService.get().getLastAuditStatusByDeliveryId(auditTaskBO.getDeliveryId()));
        taskDO.setCuid(auditTaskBO.getAuditCreateUid().longValue());
        taskDO.setLastDataVersion(taskService.get().getEffectiveDataVersionByDeliveryId(auditTaskBO.getDeliveryId()));

        WmEmploy employ = wmScEmployAdaptor.getWmEmployByUid(auditTaskBO.getAuditCreateUid());
        taskDO.setCmis(employ.getMisId());

        int result = wmSchoolDeliveryAuditTaskMapper.insertSelective(taskDO);
        if (result <= 0 || taskDO.getId() == null) {
            log.error("[WmSchoolDeliveryAuditTaskService.createDeliveryAuditTaskBySubmit] insertSelective error. taskDO = {}", JSONObject.toJSONString(taskDO));
            throw new WmSchCantException(SERVER_ERROR, "新增审批任务失败");
        }
        log.info("[WmSchoolDeliveryAuditTaskService.createDeliveryAuditTaskBySubmit] result = {}", result);
        return taskDO;
    }

    /**
     * 更新审批任务主表中GravityId和审批节点
     * @param gravityId gravityId
     * @param taskDO taskDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void updateAuditTaskGravityIdAndAuditNode(String gravityId, WmSchoolDeliveryAuditTaskDO taskDO) throws WmSchCantException {
        log.info("[WmSchoolDeliveryService.updateAuditTaskByGravityResponse] gravityId = {}, taskDO = {}", gravityId, JSONObject.toJSONString(taskDO));
        // 1-根据GravityId查询流程实例获取当前审批节点
        SchoolDeliveryAuditNodeTypeEnum nodeTypeEnum = wmSchoolDeliveryAuditFlowGravityService.getGravityAuditNodeByGravityId(gravityId);

        // 2-更新审批任务主表
        taskDO.setGravityId(gravityId);
        taskDO.setAuditNode(nodeTypeEnum.getType());
        wmSchoolDeliveryAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmSchoolDeliveryService.updateAuditTaskByGravityResponse] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务主表更新任务状态为已终止
     * @param auditTaskBO auditTaskBO
     */
    public void updateAuditTaskAuditResultByAuditEnd(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDIT_END.getType());
        wmSchoolDeliveryAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskAuditResultByAuditEnd] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务主表更新任务状态为已通过
     * @param auditTaskBO auditTaskBO
     */
    public void updateAuditTaskAuditResultByAuditEffect(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDIT_PASS.getType());
        wmSchoolDeliveryAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskAuditResultByAuditEffect] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务主表更新任务状态为已驳回
     * @param auditTaskBO auditTaskBO
     */
    public void updateAuditTaskAuditResultByAuditReject(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDIT_REJECT.getType());
        wmSchoolDeliveryAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskAuditResultByAuditReject] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务主表更新任务状态为已终止
     * @param auditTaskBO auditTaskBO
     */
    public void updateAuditTaskAuditResultByAuditStop(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDIT_END.getType());
        wmSchoolDeliveryAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskAuditResultByAuditStop] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务主表更新审批节点
     * @param auditTaskBO auditTaskBO
     * @param currentAuditNode 当前审批节点
     */
    public void updateDeliveryAuditTaskAuditNode(WmSchoolDeliveryAuditTaskBO auditTaskBO, Integer currentAuditNode) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditNode(currentAuditNode);
        wmSchoolDeliveryAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateDeliveryAuditTaskAuditNode] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务子表更新任务状态为已终止
     * @param auditTaskBO auditTaskBO
     */
    public void updateAuditTaskNodeAuditResultByAuditEnd(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskNodeDO taskNodeDO = auditTaskBO.getTaskNodeDO();
        taskNodeDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDIT_END.getType());
        taskNodeDO.setMuid(auditTaskBO.getOpUserUid().longValue());
        taskNodeDO.setAuditTime(TimeUtil.unixtime());
        taskNodeDO.setAuditRemark("(撤回审批)");
        wmSchoolDeliveryAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskNodeAuditResultByAuditEnd] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
    }

    /**
     * 审批任务子表更新任务状态为已驳回
     * @param auditTaskBO auditTaskBO
     * @param ticketDto ticketDto 任务系统DTO
     */
    public void updateAuditTaskNodeAuditResultByAuditReject(WmSchoolDeliveryAuditTaskBO auditTaskBO, WmTicketDto ticketDto) {
        WmSchoolDeliveryAuditTaskNodeDO taskNodeDO = auditTaskBO.getTaskNodeDO();
        taskNodeDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDIT_REJECT.getType());
        taskNodeDO.setAuditRemark(TICKET_SYSTEM_DEFAULT_REMARK.equals(ticketDto.getRemark()) ? "" : ticketDto.getRemark());
        taskNodeDO.setMuid((long) ticketDto.getOpUid());
        taskNodeDO.setAuditTime(ticketDto.getUnixUtime());
        wmSchoolDeliveryAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskNodeAuditResultByAuditReject] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
    }

    /**
     * 审批任务子表更新任务状态为已终止
     * @param auditTaskBO auditTaskBO
     * @param ticketDto ticketDto 任务系统DTO
     */
    public void updateAuditTaskNodeAuditResultByAuditStop(WmSchoolDeliveryAuditTaskBO auditTaskBO, WmTicketDto ticketDto) {
        WmSchoolDeliveryAuditTaskNodeDO taskNodeDO = auditTaskBO.getTaskNodeDO();
        taskNodeDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDIT_END.getType());
        taskNodeDO.setAuditRemark(TICKET_SYSTEM_DEFAULT_REMARK.equals(ticketDto.getRemark()) ? "" : ticketDto.getRemark());
        taskNodeDO.setMuid((long) ticketDto.getOpUid());
        taskNodeDO.setAuditTime(ticketDto.getUnixUtime());
        wmSchoolDeliveryAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskNodeAuditResultByAuditStop] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
    }

    /**
     * 审批任务子表更新任务状态为已通过
     * @param auditTaskBO auditTaskBO
     * @param ticketDto ticketDto 任务系统DTO
     */
    public void updateDeliveryAuditTaskNodeByAuditPass(WmSchoolDeliveryAuditTaskBO auditTaskBO, WmTicketDto ticketDto) {
        WmSchoolDeliveryAuditTaskNodeDO taskNodeDO = auditTaskBO.getTaskNodeDO();
        taskNodeDO.setAuditResult((int) SchoolDeliveryAuditResultEnum.AUDIT_PASS.getType());
        taskNodeDO.setAuditRemark(TICKET_SYSTEM_DEFAULT_REMARK.equals(ticketDto.getRemark()) ? "" : ticketDto.getRemark());
        taskNodeDO.setMuid((long) ticketDto.getOpUid());
        taskNodeDO.setAuditTime(ticketDto.getUnixUtime());
        wmSchoolDeliveryAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateDeliveryAuditTaskNodeByAuditPass] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));

    }

    /**
     * 审批任务主表创建子任务
     * @param taskDO taskDO
     * @param auditorInfoBO auditorInfoBO
     * @return WmSchoolDeliveryAuditTaskNodeDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmSchoolDeliveryAuditTaskNodeDO createDeliveryAuditTaskNodeBySubmit(WmSchoolDeliveryAuditTaskDO taskDO, WmSchoolDeliveryAuditorInfoBO auditorInfoBO)
            throws WmSchCantException {
        WmSchoolDeliveryAuditTaskNodeDO taskNodeDO = new WmSchoolDeliveryAuditTaskNodeDO();
        taskNodeDO.setAuditTaskId(taskDO.getId());
        taskNodeDO.setAuditNode(taskDO.getAuditNode());
        taskNodeDO.setAuditSystemType((int) SchoolDeliveryAuditSystemTypeEnum.CRM_TICKET_SYSTEM.getType());
        taskNodeDO.setAuditorUid(auditorInfoBO.getAuditorUid().longValue());
        taskNodeDO.setAuditorName(auditorInfoBO.getAuditorName());
        taskNodeDO.setAuditorMis(auditorInfoBO.getAuditorMis());
        taskNodeDO.setCuid(taskDO.getCuid());

        int result = wmSchoolDeliveryAuditTaskNodeMapper.insertSelective(taskNodeDO);
        if (result <= 0 || taskNodeDO.getId() == null) {
            log.error("[WmSchoolDeliveryAuditTaskService.createDeliveryAuditTaskNodeBySubmit] insertSelective error. taskDO = {}", JSONObject.toJSONString(taskDO));
            throw new WmSchCantException(SERVER_ERROR, "新增审批子任务失败");
        }
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryAuditTaskNodeBySubmit] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
        return taskNodeDO;
    }

    /**
     * 更新审批任务子表中的任务系统ID
     * @param ticketId 任务系统ID(主任务ID)
     * @param taskNodeDO taskNodeDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void updateAuditTaskNodeAuditSystemId(Integer ticketId, WmSchoolDeliveryAuditTaskNodeDO taskNodeDO) throws WmSchCantException {
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskNodeAuditSystemId] ticketId = {}, taskNodeDO = {}", ticketId, JSONObject.toJSONString(taskNodeDO));
        taskNodeDO.setAuditSystemId(String.valueOf(ticketId));
        wmSchoolDeliveryAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateAuditTaskNodeAuditSystemId] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
    }

    /**
     * 创建学校交付流子表记录(审批通过/审批驳回/待提审->审批中)
     * @param deliveryId 交付编号ID
     * @param deliveryNodeType 交付节点
     * @param userId 用户ID
     */
    public void createDeliveryStreamDetailBySubmit(Integer deliveryId, Integer deliveryNodeType, Integer userId) {
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryStreamDetailBySubmit] deliveryId = {}, deliveryNodeType = {}, userId = {}", deliveryId, deliveryNodeType, userId);
        // 1-提审前审批状态和生效状态
        WmSchoolDeliveryStreamDetailDO streamDetailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(
                deliveryId, deliveryNodeType);

        // 2-新增状态变更数据(生效状态=提审前生效状态, 审批状态=审批中)
        WmSchoolDeliveryStreamDetailDO streamDetailDOInsert = new WmSchoolDeliveryStreamDetailDO();
        streamDetailDOInsert.setDeliveryId(deliveryId);
        streamDetailDOInsert.setSchoolPrimaryId(streamDetailDO.getSchoolPrimaryId());
        streamDetailDOInsert.setDeliveryNodeType(deliveryNodeType);
        streamDetailDOInsert.setEffective(streamDetailDO.getEffective());
        streamDetailDOInsert.setAuditStatus((int) SchoolDeliveryAuditStatusEnum.AUDITING.getType());
        streamDetailDOInsert.setCuid(userId.longValue());
        streamDetailDOInsert.setMuid(userId.longValue());
        wmSchoolDeliveryStreamDetailMapper.insertSelective(streamDetailDOInsert);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryStreamDetailBySubmit] streamDetailDOInsert = {}", JSONObject.toJSONString(streamDetailDOInsert));
    }

    /**
     * 学校交付流子表记录记录状态回退(审批通过/审批驳回/待提审->审批中)
     */
    public void createDeliveryStreamDetailByAuditCancel(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询当前生效状态
        WmSchoolDeliveryStreamDetailDO streamDetailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(
                taskDO.getDeliveryId(), taskDO.getDeliveryNodeType());

        // 2-新增状态变更数据(生效状态=当前生效状态, 审批状态=提审前审批状态)
        WmSchoolDeliveryStreamDetailDO streamDetailDOInsert = new WmSchoolDeliveryStreamDetailDO();
        streamDetailDOInsert.setDeliveryId(streamDetailDO.getDeliveryId());
        streamDetailDOInsert.setSchoolPrimaryId(streamDetailDO.getSchoolPrimaryId());
        streamDetailDOInsert.setDeliveryNodeType(taskDO.getDeliveryNodeType());
        streamDetailDOInsert.setEffective(streamDetailDO.getEffective());
        streamDetailDOInsert.setAuditStatus(taskDO.getLastAuditStatus());
        streamDetailDOInsert.setCuid(auditTaskBO.getOpUserUid().longValue());
        streamDetailDOInsert.setMuid(auditTaskBO.getOpUserUid().longValue());
        wmSchoolDeliveryStreamDetailMapper.insertSelective(streamDetailDOInsert);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryStreamDetailByAuditCancel] streamDetailDOInsert = {}", JSONObject.toJSONString(streamDetailDOInsert));
    }

    /**
     * 学校交付流子表记录记录状态审批驳回(审批中->审批驳回)
     */
    public void createDeliveryStreamDetailByAuditReject(WmSchoolDeliveryAuditTaskBO auditTaskBO, WmTicketDto ticketDto) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询当前生效状态
        WmSchoolDeliveryStreamDetailDO streamDetailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(
                taskDO.getDeliveryId(), taskDO.getDeliveryNodeType());

        // 2-新增状态变更数据(生效状态=当前生效状态, 审批状态=审批驳回)
        WmSchoolDeliveryStreamDetailDO streamDetailDOInsert = new WmSchoolDeliveryStreamDetailDO();
        streamDetailDOInsert.setCuid((long) ticketDto.getOpUid());
        streamDetailDOInsert.setMuid((long) ticketDto.getOpUid());
        streamDetailDOInsert.setDeliveryId(streamDetailDO.getDeliveryId());
        streamDetailDOInsert.setSchoolPrimaryId(streamDetailDO.getSchoolPrimaryId());
        streamDetailDOInsert.setDeliveryNodeType(taskDO.getDeliveryNodeType());
        streamDetailDOInsert.setEffective(streamDetailDO.getEffective());
        streamDetailDOInsert.setAuditStatus((int) SchoolDeliveryAuditStatusEnum.REJECT.getType());
        wmSchoolDeliveryStreamDetailMapper.insertSelective(streamDetailDOInsert);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryStreamDetailByAuditReject] streamDetailDOInsert = {}", JSONObject.toJSONString(streamDetailDOInsert));
    }

    /**
     * 学校交付流子表记录记录状态审批通过(审批中->审批通过)
     */
    public void createDeliveryStreamDetailByAuditEffect(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-新增状态变更数据(生效状态=生效, 审批状态=审批通过)
        WmSchoolDeliveryStreamDetailDO streamDetailDOInsert = new WmSchoolDeliveryStreamDetailDO();
        streamDetailDOInsert.setDeliveryId(taskDO.getDeliveryId());
        streamDetailDOInsert.setSchoolPrimaryId(taskDO.getSchoolPrimaryId());
        streamDetailDOInsert.setDeliveryNodeType(taskDO.getDeliveryNodeType());
        streamDetailDOInsert.setEffective((int) SchoolDeliveryEffectiveStatusEnum.EFFECTIVE.getType());
        streamDetailDOInsert.setAuditStatus((int) SchoolDeliveryAuditStatusEnum.PASS.getType());
        wmSchoolDeliveryStreamDetailMapper.insertSelective(streamDetailDOInsert);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryStreamDetailByAuditEffect] streamDetailDOInsert = {}", JSONObject.toJSONString(streamDetailDOInsert));
    }

    /**
     * 交付流主表更新交付节点
     */
    public void updateDeliveryStreamCurrentNodeByAuditEffect(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询交付流主表信息
        WmSchoolDeliveryStreamDO streamDO = wmSchoolDeliveryStreamMapper.selectByPrimaryKey(taskDO.getDeliveryId().longValue());

        // 2-交付流最新节点=审批通过节点时才更新
        if (streamDO.getStreamNode().equals(taskDO.getDeliveryNodeType())) {
            streamDO.setStreamNode(SchoolDeliveryStreamNodeEnum.getNextNode(streamDO.getStreamNode()));
            wmSchoolDeliveryStreamMapper.updateByPrimaryKeySelective(streamDO);
            log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateDeliveryStreamCurrentNodeByAuditEffect] streamDO = {}", JSONObject.toJSONString(streamDO));
        }
    }

    /**
     * 学校交付流子表记录下一交付流节点状态(待提审 & 未生效)
     */
    public void createDeliveryStreamDetailNextNodeByAuditEffect(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        Integer nextDeliveryNode = SchoolDeliveryStreamNodeEnum.getNextNode(taskDO.getDeliveryNodeType());
        // 1-查询下一个交付节点是否有交付流子表记录
        WmSchoolDeliveryStreamDetailDO streamDetailDO = wmSchoolDeliveryStreamDetailMapper.selectLatestRecordByDeliveryIdAndDeliveryNodeTpye(
                taskDO.getDeliveryId(), nextDeliveryNode);
        if (streamDetailDO != null || nextDeliveryNode.equals(SchoolDeliveryStreamNodeEnum.DELIVERY_COMPLETE.getCode())) {
            log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryStreamDetailNextNodeByAuditEffect] streamDetailDO not null. streamDetailDO = {}, nextDeliveryNode = {}",
                    JSONObject.toJSONString(streamDetailDO), nextDeliveryNode);
            return;
        }

        // 2-若交付流子表记录为空且不是交付跟进生效, 则新增一行数据
        WmSchoolDeliveryStreamDetailDO streamDetailDOInsert = new WmSchoolDeliveryStreamDetailDO();
        streamDetailDOInsert.setDeliveryId(taskDO.getDeliveryId());
        streamDetailDOInsert.setSchoolPrimaryId(taskDO.getSchoolPrimaryId());
        streamDetailDOInsert.setDeliveryNodeType(nextDeliveryNode);
        streamDetailDOInsert.setEffective((int) SchoolDeliveryEffectiveStatusEnum.UNEFFECTIVE.getType());
        streamDetailDOInsert.setAuditStatus((int) SchoolDeliveryAuditStatusEnum.PENDING.getType());
        wmSchoolDeliveryStreamDetailMapper.insertSelective(streamDetailDOInsert);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryStreamDetailNextNodeByAuditEffect] streamDetailDOInsert = {}", JSONObject.toJSONString(streamDetailDOInsert));
    }

    /**
     * 交付流程子表新增交付跟进待提审和待生效数据
     */
    public void createDeliveryFollowUpStreamDetailByRollBack(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        WmSchoolDeliveryStreamDetailDO streamDetailDOInsert = new WmSchoolDeliveryStreamDetailDO();
        streamDetailDOInsert.setDeliveryId(taskDO.getDeliveryId());
        streamDetailDOInsert.setSchoolPrimaryId(taskDO.getSchoolPrimaryId());
        streamDetailDOInsert.setDeliveryNodeType(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        streamDetailDOInsert.setEffective((int) SchoolDeliveryEffectiveStatusEnum.UNEFFECTIVE.getType());
        streamDetailDOInsert.setAuditStatus((int) SchoolDeliveryAuditStatusEnum.PENDING.getType());
        wmSchoolDeliveryStreamDetailMapper.insertSelective(streamDetailDOInsert);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.createDeliveryFollowUpStreamDetailByRollBack] streamDetailDOInsert = {}", JSONObject.toJSONString(streamDetailDOInsert));
    }

    /**
     * 交付流程主表更新到交付跟进
     */
    public void updateDeliveryStreamCurrentNodeByRollBack(WmSchoolDeliveryAuditTaskBO auditTaskBO) {
        WmSchoolDeliveryAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-判断当前状态是否是交付跟进
        WmSchoolDeliveryStreamDO streamDO = wmSchoolDeliveryStreamMapper.selectByPrimaryKey(taskDO.getDeliveryId().longValue());
        if (streamDO.getStreamNode().equals(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode())) {
            log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateDeliveryStreamCurrentNodeByRollBack] streamNode = followUp, return. auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
            return;
        }

        // 2-将交付流当前节点更新为交付跟进
        streamDO.setStreamNode(SchoolDeliveryStreamNodeEnum.DELIVERY_FOLLOWUP.getCode());
        wmSchoolDeliveryStreamMapper.updateByPrimaryKeySelective(streamDO);
        log.info("[WmSchoolDeliveryAuditFlowSchoolService.updateDeliveryStreamCurrentNodeByRollBack] streamDO = {}", JSONObject.toJSONString(streamDO));
    }


}
