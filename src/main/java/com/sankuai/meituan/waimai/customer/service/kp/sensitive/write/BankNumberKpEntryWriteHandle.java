package com.sankuai.meituan.waimai.customer.service.kp.sensitive.write;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.constant.YesOrNoEnum;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.EncryptResult;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncrypt;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncryptHandleService;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 银行卡号
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
@Slf4j
public class BankNumberKpEntryWriteHandle implements IKpWriteHandle {

    @Autowired
    private KeyEncryptHandleService keyEncryptHandleService;


    @Override
    public KmsKeyNameEnum handleType() {
        return KmsKeyNameEnum.BANK_CARD_NO;
    }

    @Override
    public void doWriteKpWhenInsertOrUpdate(KpEntryWrite kpEntryWrite) throws WmCustomerException {
        log.debug("execute::kpEntryWrite = {}", JSON.toJSONString(kpEntryWrite));
        if (kpEntryWrite == null) {
            return;
        }
        writeKpCreditCardWhenInsertOrUpdate(kpEntryWrite.getKp());
        writeKpCreditCardWhenInsertOrUpdate(kpEntryWrite.getKpTemp());
    }

    @Override
    public void writeKpSourceWhenUpdate(KpEntryWrite kpEntryWrite) {
        log.debug("execute::writeKpSourceWhenUpdate = {}", JSON.toJSONString(kpEntryWrite));
        if (kpEntryWrite == null) {
            return;
        }
        writeKpCreditCardSourceWhenUpdate(kpEntryWrite.getKpTemp());
        writeKpCreditCardSourceWhenUpdate(kpEntryWrite.getKp());
    }


    /**
     * 客户KP银行卡号新老字段写控制
     * 是否写原字段 原字段写开关控制
     *
     * @param kp
     */
    private void writeKpCreditCardSourceWhenUpdate(WmCustomerKp kp) {
        if (kp == null) {
            return;
        }
        if (!MccCustomerConfig.encryptionCustomerKpCreditCardOldWriteSwitch()
                && tryEncryption(KmsKeyNameEnum.BANK_CARD_NO, kp.getCreditCard())) {
            //开关关闭不再写原字段,且原字段加密成功则原字段不再写
            kp.setNotSaveCreditCard(YesOrNoEnum.YES.getCode());
        } else {
            kp.setNotSaveCreditCard(YesOrNoEnum.NO.getCode());
        }
    }

    /**
     * 客户KP手机号新老字段写控制
     * 是否写原字段 原字段写开关控制
     *
     * @param kp
     */
    private void writeKpCreditCardSourceWhenUpdate(WmCustomerKpTemp kp) {
        if (kp == null) {
            return;
        }
        if (!MccCustomerConfig.encryptionCustomerKpCreditCardOldWriteSwitch()
                && tryEncryption(KmsKeyNameEnum.BANK_CARD_NO, kp.getCreditCard())) {
            //开关关闭不再写原字段,且原字段加密成功则原字段不再写
            kp.setNotSaveCreditCard(YesOrNoEnum.YES.getCode());
        } else {
            kp.setNotSaveCreditCard(YesOrNoEnum.NO.getCode());
        }
    }


    /**
     * 客户KP银行卡号新老字段写控制
     * 是否写原字段 原字段写开关控制，通过变量控制（变量传入Mapper文件中进行判断）
     * 是否写新字段 新字段写开关控制
     *
     * @param kp
     */
    private void writeKpCreditCardWhenInsertOrUpdate(WmCustomerKp kp) throws WmCustomerException {
        if (kp == null) {
            return;
        }
        if (MccCustomerConfig.encryptionCustomerKpCreditCardNewWriteSwitch()) {
            //写新字段
            if (StringUtils.isBlank(kp.getCreditCard())) {
                kp.setCreditCardEncryption("");
                kp.setCreditCardToken("");
            } else {
                KeyEncrypt keyEncrypt = new KeyEncrypt();
                keyEncrypt.setKeyName(KmsKeyNameEnum.BANK_CARD_NO);
                keyEncrypt.setValueForEncrypt(kp.getCreditCard());
                EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
                if (result == null) {
                    return;
                }
                kp.setCreditCardEncryption(result.getEncryption());
                kp.setCreditCardToken(result.getToken());
            }
        }
        writeKpCreditCardSourceWhenUpdate(kp);
    }

    /**
     * 客户KP手机号新老字段写控制
     * 是否写原字段 原字段写开关控制
     * 是否写新字段 新字段写开关控制
     *
     * @param kp
     */
    private void writeKpCreditCardWhenInsertOrUpdate(WmCustomerKpTemp kp) throws WmCustomerException {
        if (kp == null) {
            return;
        }
        if (MccCustomerConfig.encryptionCustomerKpCreditCardNewWriteSwitch()) {
            //写新字段
            if (StringUtils.isBlank(kp.getCreditCard())) {
                kp.setCreditCardEncryption("");
                kp.setCreditCardToken("");
            } else {
                KeyEncrypt keyEncrypt = new KeyEncrypt();
                keyEncrypt.setKeyName(KmsKeyNameEnum.BANK_CARD_NO);
                keyEncrypt.setValueForEncrypt(kp.getCreditCard());
                EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
                if (result == null) {
                    return;
                }
                kp.setCreditCardEncryption(result.getEncryption());
                kp.setCreditCardToken(result.getToken());
            }
        }
        writeKpCreditCardSourceWhenUpdate(kp);
    }

    /**
     * 对字段尝试加密
     *
     * @param key
     * @param value
     * @return
     */
    private boolean tryEncryption(KmsKeyNameEnum key, String value) {
        try {
            KeyEncrypt keyEncrypt = new KeyEncrypt();
            keyEncrypt.setKeyName(key);
            keyEncrypt.setValueForEncrypt(value);
            EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
            if (result == null) {
                return false;
            }
        } catch (WmCustomerException e) {
            log.info("tryEncryption::key = {}, value = {},code={},msg={}", key, value, e.getCode(), e.getMsg());
            return false;
        }
        return true;
    }

}
