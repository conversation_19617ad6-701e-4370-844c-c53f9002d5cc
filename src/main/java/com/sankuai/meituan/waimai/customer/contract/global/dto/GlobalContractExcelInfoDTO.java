package com.sankuai.meituan.waimai.customer.contract.global.dto;

import com.sankuai.meituan.waimai.poi.annotation.ExcelExportField;
import lombok.Data;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/11 14:47
 */
@Data
public class GlobalContractExcelInfoDTO {

    @ExcelExportField(title = "统一合同ID", priority = 10)
    private String globalContractId;

    @ExcelExportField(title = "框架合同编号", priority = 20)
    private String frameContractNumber;

    @ExcelExportField(title = "签约任务ID", priority = 30)
    private String signBatchId;

    @ExcelExportField(title = "合同类型", priority = 40)
    private String contractType;

    @ExcelExportField(title = "客户信息", priority = 50)
    private String customerInfo;

    @ExcelExportField(title = "门店信息", priority = 60)
    private String wmPoiInfo;

    @ExcelExportField(title = "模板信息", priority = 70)
    private String templateInfo;

    @ExcelExportField(title = "合同状态", priority = 80)
    private String contractStatus;

//    @ExcelProperty("")
//    private String phoneNumber;

    @ExcelExportField(title = "签约人姓名", priority = 90)
    private String signerName;

    @ExcelExportField(title = "合同创建时间", priority = 100)
    private String createTime;

    @ExcelExportField(title = "合同签约时间", priority = 110)
    private String signTime;

    @ExcelExportField(title = "创建人姓名", priority = 120)
    private String creator;

    @ExcelExportField(title = "框架合同ID", priority = 130)
    private String frameContractId;

    @ExcelExportField(title = "框架合同版本号", priority = 140)
    private String frameContractVersion;

    @ExcelExportField(title = "合同类别", priority = 150)
    private String contractCategory;

    @ExcelExportField(title = "合同模板版本", priority = 160)
    private String templateVersion;

    @ExcelExportField(title = "甲方名称", priority = 170)
    private String partAName;

    @ExcelExportField(title = "乙方名称", priority = 180)
    private String partBName;

    @ExcelExportField(title = "合同过期时间", priority = 190)
    private String dueTime;

    @ExcelExportField(title = "合同链接", priority = 200)
    private String pdfUrl;

    @ExcelExportField(title = "recordKey", priority = 210)
    private String recordKey;

}
