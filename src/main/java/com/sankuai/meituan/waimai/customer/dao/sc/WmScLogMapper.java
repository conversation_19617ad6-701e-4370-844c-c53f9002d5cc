package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScLogDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScOplogBo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: scm
 * @description: 日志处理类
 * @author: jianghuimin02
 * @create: 2020-05-18 20:10
 **/
@Component
public interface WmScLogMapper {

    /**
     * 查询操作日志
     */
    List<WmScLogDB> queryScOpLog(@Param("query") WmScOplogBo wmScOplogBo);


    /**
     * 查询近三个月内食堂/学校最新的操作日志记录
     */
    WmScLogDB selectNewestOfLastThreeMonthOpLog(@Param("query") WmScOplogBo wmScOplogBo);

    /**
     * 查询学校交付操作日志
     * @param wmScOplogBo wmScOplogBo
     * @return List<WmScLogDB>
     */
    List<WmScLogDB> selectSchoolDeliveryOpLog(@Param("query") WmScOplogBo wmScOplogBo);

    /**
     * 查询食堂档口绑定操作日志
     * @param wmScOplogBo wmScOplogBo
     * @return List<WmScLogDB>
     */
    List<WmScLogDB> selectCanteenStallBindOpLog(@Param("query") WmScOplogBo wmScOplogBo);


    /**
     * 插入操作日志
     */
    int insertScLog(WmScLogDB wmScLogDB);

    /**
     * 批量插入操作日志
     * @param wmScLogDBList wmScLogDBList
     */
    int batchInsertScLog(@Param("list") List<WmScLogDB> wmScLogDBList);
}
