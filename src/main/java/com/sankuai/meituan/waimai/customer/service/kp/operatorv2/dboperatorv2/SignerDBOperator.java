package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.util.StringUtil;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpBuryingPointService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpRealAuthService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.kp.dboperator.KpDBOperateImpl;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerFlowAbility;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.util.AppContext;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditSubmitUidConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum.AGENT;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-06 17:15
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class SignerDBOperator extends KpDBOperator {

    private static final Logger LOGGER = LoggerFactory.getLogger(KpDBOperateImpl.class);

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    /**
     * 变更签约人失败的状态
     */
    private static final Set<Byte> CHANGE_SIGNER_FAIL_STATES = ImmutableSet.of(KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT.getState(),
            KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState(),
            KpSignerStateMachine.CHANGE_PREAUTH_FAIL.getState(),
            KpSignerStateMachine.CHANGE_SPECILA_AUDIT_REJECT.getState(),
            KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT.getState()
    );

    /**
     * 签约人KP变更与原签约人人状态关系Map
     */
    private static final Map<Byte, Byte> KP_SIGNER_CHANGE_STAET_MAP = MapUtil.of(
            KpSignerStateMachine.PREAUTH_FAIL.getState(), KpSignerStateMachine.CHANGE_PREAUTH_FAIL.getState(),
            KpSignerStateMachine.SPECILA_AUDIT_ING.getState(), KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING.getState(),
            KpSignerStateMachine.SPECILA_AUDIT_REJECT.getState(), KpSignerStateMachine.CHANGE_SPECILA_AUDIT_REJECT.getState(),
            KpSignerStateMachine.AGENT_AUDIT_ING.getState(), KpSignerStateMachine.CHANGE_AGENT_AUDIT_ING.getState(),
            KpSignerStateMachine.AGENT_AUDIT_REJECT.getState(), KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT.getState(),
            KpSignerStateMachine.EFFECT.getState(), KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState()
    );

    @Override
    public Object insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> insertKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        WmCustomerKp insertKp = getOperateKp(insertKpList);
        if (null == insertKp) {
            ThrowUtil.throwClientError("待新增签约人KP信息为空");
        }
        // 食堂承包商没有签约人KP的概念
        if (wmCustomer != null
                && wmCustomer.getCustomerRealType() != null
                && wmCustomer.getCustomerRealType() == CustomerRealTypeEnum.CONTRACTOR.getValue()) {
            return null;
        }

        WmCustomerKp oldCustomerKp = getOldKp(oldCustomerKpList, insertKp.getId());
        if (oldCustomerKp != null) {
            ThrowUtil.throwClientError("待新增KP已存在，请更新");
        }
        //非代理人需要设置为0
        if (insertKp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
            insertKp.setLegalAuthType(LegalAuthTypeEnum.UN_SETTING.getCode());
        }

        //新增的签约代理人KP，无授权方式则默认纸面授权
        if (insertKp.getSignerType() == KpSignerTypeEnum.AGENT.getType()
                && (insertKp.getLegalAuthType() == null || insertKp.getLegalAuthType() == LegalAuthTypeEnum.UN_SETTING.getCode())) {
            insertKp.setLegalAuthType(LegalAuthTypeEnum.PAPER_AUTH.getCode());
        }

        //新流程签约人代理人KP-短信授权需要校验法人数据是否存在
        if (KpSignerTypeEnum.AGENT.getType() == insertKp.getSignerType()
                && insertKp.getLegalAuthType() != null && insertKp.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()
                && !wmCustomerKpService.checkKpLegalExist(insertKp.getCustomerId())) {
            ThrowUtil.throwClientError("代理人短信授权情况下，没有录入法人信息（KP类型是法人的数据），不可保存");
        }

        // KP签约人状态机能力模型
        // 获取签约人KP能力
        KpSignerFlowAbility kpSignerFlowAbility = getMatchKpSignerFlowAbility(insertKp.getCertType());
        // 构建上下文
        KpSignerStatusMachineContext context = KpSignerStatusMachineContext.builder()
                .customerId(insertKp.getCustomerId()).oldCustomerKp(null).wmCustomerKp(insertKp)
                .existEffectiveFlag(false).opType(KpOperationTypeConstant.INSERT).wmCustomerDB(wmCustomer).opUid(uid)
                .opUName(uname).build();
        return kpSignerFlowAbility.insertKpSignerWithSM(context);
        
    }

    /**
     * 客户生效后已录入kp继续原流程
     *
     * @param wmCustomerDB
     * @param wmCustomerKp
     * @throws WmCustomerException
     */
    public void recordedSignerDriven(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, Boolean legalEffectice) throws WmCustomerException, TException, WmServerException {
        //签约代理人+短信授权=KP法人未生效，不能继续走流程
        if (checkSignerAgentMsgAuth(wmCustomerKp)
                && !wmCustomerKpService.checkHaveEffectiveLegalKp(wmCustomerDB.getId())) {
            LOGGER.info("recordedSignerDriven,客户生效后已录入kp继续原流程,签约代理人且短信授权校验KP法人未生效，不能继续走流程,customerId={},kpId={}", wmCustomerDB.getId(), wmCustomerKp.getId());
            return;
        }
        // 实名认证，sendEffectiveMq授权消息发送标识，默认值false
        boolean sendEffectiveMq = preRealNameOperate(wmCustomerKp, wmCustomerDB, WmAuditSubmitUidConstant.CUSTOMER.getType(), WmAuditSubmitUidConstant.CUSTOMER.getName(), false);

        // 操作完成后更新db中的数据
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(wmCustomerKp);
        wmCustomerKpDBMapper.updateByPrimaryKeySelective(wmCustomerKp);
        if (legalEffectice) {
            wmCustomerKpLogService.changeState(wmCustomerKp.getCustomerId(), wmCustomerKp, "KP法人变更为生效，签约代理人KP继续走生效流程", 0, "系统");
        } else {
            wmCustomerKpLogService.changeState(wmCustomerKp.getCustomerId(), wmCustomerKp, "客户状态变更为生效，KP继续走生效流程", 0, "系统");
        }

        // 如果预认证失败，提示上传特批证明
        if (wmCustomerKp.getState() == KpSignerStateMachine.PREAUTH_FAIL.getState()) {
            throw new WmCustomerException(501, wmCustomerKp.getFailReason());
        }

        // 发送授权信息
        sendEffectiveMq(sendEffectiveMq, wmCustomerKp.getCustomerId());

    }

    @Override
    public Object update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> updateKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        WmCustomerKp updateKp = getOperateKp(updateKpList);
        if (null == updateKp) {
            ThrowUtil.throwClientError("待更新签约人KP信息为空");
        }

        WmCustomerKp oldCustomerKp = getOldKp(oldCustomerKpList, updateKp.getId());
        if (oldCustomerKp == null) {
            ThrowUtil.throwClientError("无法获取待更新KP信息");
        }

        //校验当前授权方式下是否可操作
        checkSignerAuthType(updateKp, oldCustomerKp);
        //设置默认值：授权方式、版本号以及操作来源
        updateKp = defaultSignerAuthTypeAndVersion(updateKp, oldCustomerKp);

        List<WmCustomerDiffCellBo> kpUpdateFields = differentCustomerKpService.getSignerKpUpdateFields(oldCustomerKp, updateKp);
        // 页面信息未修改
        if (kpUpdateFields.isEmpty()) {
            //说明没有更新
            if (KpSignerStateMachine.EFFECT.getState() == oldCustomerKp.getState()) {
                //授权失败状态保存需要清空标识
                WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpId(oldCustomerKp.getId());
                if (kpTempDB != null && CHANGE_SIGNER_FAIL_STATES.contains(kpTempDB.getState())) {
                    LOGGER.info("未修改任何记录，清空临时数据，customerId:{},kpId={}, tempId={}", wmCustomer.getId(), oldCustomerKp.getId(), kpTempDB.getId());
                    wmCustomerKpTempDBMapper.deleteByPrimaryKey(kpTempDB.getId());
                    wmCustomerKpLogService.insertOplog(oldCustomerKp.getCustomerId(), oldCustomerKp, "【删除临时变更数据Id:" + kpTempDB.getId() + "】", uid, uname);
                }
                return null;
            } else if (KpSignerStateMachine.PREAUTH_FAIL.getState() != oldCustomerKp.getState()) {
                //新建时，如果状态为认证失败，则提交保存时需要重新认证，否则这里直接返回（状态为非认证失败且无修改）
                return null;
            }
        }

        // 是否命中KP签约人状态机
        // 获取签约人KP能力
        KpSignerFlowAbility kpSignerFlowAbility = getMatchKpSignerFlowAbility(updateKp.getCertType());
        // 判断是否需要暂存流程
        boolean needTempSave = (KpSignerStateMachine.EFFECT.getState() != oldCustomerKp.getState()
                && wmCustomer.isUnEffectived())
                || (checkSignerAgentMsgAuth(updateKp)
                        && !wmCustomerKpService.checkHaveEffectiveLegalKp(wmCustomer.getId()));
        // 构建上下文
        KpSignerStatusMachineContext context = KpSignerStatusMachineContext.builder()
                .customerId(updateKp.getCustomerId()).oldCustomerKp(oldCustomerKp).wmCustomerKp(updateKp)
                .existEffectiveFlag(oldCustomerKp.getState() == KpSignerStateMachine.EFFECT.getState())
                .needTempSaveFlag(needTempSave).opType(KpOperationTypeConstant.UPDATE).wmCustomerDB(wmCustomer)
                .opUid(uid).opUName(uname).build();
        return kpSignerFlowAbility.updateKpSignerWithSM(context);

    }

    @Override
    public Object delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> deleteKpList, int uid, String uname) throws WmCustomerException {
        WmCustomerKp deleteKp = getOperateKp(deleteKpList);
        if (null != deleteKp) {
            deleteKp(wmCustomer, deleteKpList.get(0), uid, uname);
        }
        //更新客户门店属性
        wmCustomerPoiAttributeService.updateSignerKpAsy(deleteKpList.get(0).getCustomerId());
        return null;
    }

    /**
     * 签约人KP实名操作流程： 使用场景如下
     * 1 新增签约人KP调用
     * 2 客户生效后签约人KP继续执行流程
     * 
     * @param wmCustomerKp
     * @param wmCustomerDB
     * @param uid
     * @param uname
     * @param sendEffectiveMq
     * @return
     * @throws WmCustomerException
     * @throws TException
     * @throws WmServerException
     */
    private boolean preRealNameOperate(WmCustomerKp wmCustomerKp, WmCustomerDB wmCustomerDB, int uid, String uname, boolean sendEffectiveMq) 
            throws WmCustomerException, TException, WmServerException {

        // 非身份证
        if (!CertTypeEnum.checkIdCardSet(wmCustomerKp.getCertType())) {
            wmCustomerKp.setState(KpSignerStateMachine.PREAUTH_FAIL.getState());
            wmCustomerKp.setFailReason(WmCustomerConstant.KP_NO_BANK_STATEMENT_ERROR_MSG);
            // 特批证件非空 或 允许不上传银行流水开关开启 => 继续走特批
            if (StringUtil.isNotBlank(wmCustomerKp.getSpecialAttachment()) || wmCustomerGrayService
                    .checkHitAllowNoBankStatementKpOperate((long)wmCustomerKp.getCustomerId())) {
                // 特批证明不为空，提审特批证明
                wmCustomerKp.setState(KpSignerStateMachine.SPECILA_AUDIT_ING.getState());
                wmCustomerKp.setFailReason("");
                kpDBOperate.commitSpecialAudit(wmCustomerKp, uid, uname);
            }
            return false;
        }
        // 实名认证
        //String preAuthErrorMsg = kpDBOperate.preAuthAndErrorMsg(wmCustomerKp, uid, uname);
        PreAuthResultBO result = kpDBOperate.preAuthAndErrorMsgNew(wmCustomerKp, uid, uname);
        String preAuthErrorMsg = (result == null ? null : (result.getResult() == PreAuthResultTypeEnum.FAIL.getType() ? result.getMsg() : null));
        LOGGER.info("insert signer kp 预认证结果:{},preAuthErrorMsg={},customerId={},kpId={}", (preAuthErrorMsg == null), preAuthErrorMsg, wmCustomerKp.getCustomerId(), wmCustomerKp.getId());
        if (StringUtils.isEmpty(preAuthErrorMsg)) {
            // 实名认证成功
            if (wmCustomerKp.getSignerType() == KpSignerTypeEnum.AGENT.getType()) {
                //代理商提审
                wmCustomerKp.setState(KpSignerStateMachine.AGENT_AUDIT_ING.getState());
                wmCustomerKp.setFailReason("");
                kpDBOperate.commitAgentAudit(wmCustomerKp, uid, uname);
            } else {
                //非代理商预认证通过的直接生效
                wmCustomerKp.setState(KpSignerStateMachine.EFFECT.getState());
                wmCustomerKp.setEffective(KpConstants.EFFECTIVE);
                wmCustomerKp.setFailReason("");
                sendEffectiveMq = true;
            }
        } else {
            wmCustomerKp.setState(KpSignerStateMachine.PREAUTH_FAIL.getState());
            wmCustomerKp.setFailReason(preAuthErrorMsg);
            if (StringUtil.isNotBlank(wmCustomerKp.getSpecialAttachment())) {
                //特批证明不为空，提审特批证明
                wmCustomerKp.setState(KpSignerStateMachine.SPECILA_AUDIT_ING.getState());
                wmCustomerKp.setFailReason("");
                kpDBOperate.commitSpecialAudit(wmCustomerKp, uid, uname);
            }
        }
        //四要素认证标签处理
        wmCustomerKpRealAuthService.signerFourAuthTagDeal(wmCustomerDB, result, uid, uname);
        return sendEffectiveMq;
    }

    private void sendEffectiveMq(boolean sendEffectiveMq, int customerId) {
        if (sendEffectiveMq) {
            // 签约人KP生效发送MQ通知
            if (AppContext.isLazyProcess()) {
                AppContext.offerLazyTask(new AppContext.LazyTask() {
                    @Override
                    public void lazyProcess() {
                        mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
                    }

                    @Override
                    public String taskDesc() {
                        return "自入驻绑定门店至3.0，签约人KP生效发送消息";
                    }
                });
            } else {
                mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
            }
        }
    }

    /**
     * KP法人生效后-签约人KP【签约代理人+短信授权+已录入状态】继续流程
     *
     * @param wmCustomer
     */
    public void legalKpEffectiveDrivenSignerKpFlow(WmCustomerDB wmCustomer) {
        try {
            WmCustomerKp wmCustomerKp = wmCustomerKpDBMapper.getKpByCustomerIdAndKpType(wmCustomer.getId(), KpTypeEnum.SIGNER.getType());
            //只处理签约人+代理人+短信授权+已录入状态的数据
            if (wmCustomerKp == null || wmCustomerKp.getSignerType() != AGENT.getType()
                    || wmCustomerKp.getState() != KpSignerStateMachine.RECORDED.getState()
                    || wmCustomerKp.getLegalAuthType() == null
                    || wmCustomerKp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
                LOGGER.info("legalKpEffectiveDrivenSignerKpFlow,KP法人生效处理签约人KP信息，无符合条件的数据，不进行处理,wmCustomerKp={}", JSON.toJSONString(wmCustomerKp));
                return;
            }

            wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
            /** KP签约人原流程推动 **/
            recordedSignerDriven(wmCustomer, wmCustomerKp, true);
            LOGGER.info("legalKpEffectiveDrivenSignerKpFlow,KP法人生效处理签约人KP流程完成,customerId={},signerKpId={}", wmCustomer.getId(), wmCustomerKp.getId());
        } catch (Exception e) {
            LOGGER.error("legalKpEffectiveDrivenSignerKpFlow,KP法人生效处理签约人KP信息发生异常,wmCustomer={}", JSON.toJSONString(wmCustomer), e);
        }
    }

    /**
     * 判断当前签约人KP是否为代理人+短信签约
     *
     * @param wmCustomerKp
     * @return
     */
    public boolean checkSignerAgentMsgAuth(WmCustomerKp wmCustomerKp) {
        //客户未生效过，如果KP法人未生效需要修改为已录入
        if (wmCustomerKp.getKpType() == KpTypeEnum.SIGNER.getType()
                && wmCustomerKp.getSignerType() == KpSignerTypeEnum.AGENT.getType()
                && wmCustomerKp.getLegalAuthType() != null
                && wmCustomerKp.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            return true;
        }
        return false;
    }

    /**
     * 校验代理人&纸面授权&无授权书&电销系统
     *
     * @param wmCustomerKp
     * @return
     */
    public boolean checkPaperHaveNoAuth(WmCustomerKp wmCustomerKp) {
        if (wmCustomerKp.getKpType() == KpTypeEnum.SIGNER.getType()
                && wmCustomerKp.getSignerType() == KpSignerTypeEnum.AGENT.getType()
                && wmCustomerKp.getLegalAuthType() != null
                && wmCustomerKp.getLegalAuthType() == LegalAuthTypeEnum.PAPER_AUTH.getCode()
                && StringUtils.isBlank(wmCustomerKp.getAgentAuth())
                && wmCustomerKp.getOperateSource() != null
                && wmCustomerKp.getOperateSource() == CustomerDeviceType.MERCHANT_SYS.getCode()) {
            return true;
        }

        return false;
    }

    /**
     * 签约人为代理人情况下判断是否可操作
     *
     * @param updateKp
     * @param oldCustomerKp
     * @throws WmCustomerException
     */
    private void checkSignerAuthType(WmCustomerKp updateKp, WmCustomerKp oldCustomerKp) throws WmCustomerException {
        //修改为签约代理人+短信授权=添加规则校验
        if (checkSignerAgentMsgAuth(updateKp)) {
            Integer customerId = oldCustomerKp.getCustomerId();
            //生效过
            if (oldCustomerKp.getState() == KpSignerStateMachine.EFFECT.getState()
                    && !wmCustomerKpService.checkHaveEffectiveLegalKp(oldCustomerKp.getCustomerId())) {
                LOGGER.info("代理人短信授权情况下，KP类型是法人的数据没有生效，不可保存, customerId:{},id:{} info:{}", customerId, updateKp.getId(), JSON.toJSONString(updateKp));
                ThrowUtil.throwClientError("代理人短信授权情况下，法人信息没有生效（KP类型是法人的数据），不可保存");
            } else {
                if (!wmCustomerKpService.checkKpLegalExist(customerId)) {
                    LOGGER.info("代理人短信授权情况下，KP类型是法人的数据没有生效，不可保存, customerId:{},id:{} info:{}", customerId, updateKp.getId(), JSON.toJSONString(updateKp));
                    ThrowUtil.throwClientError("代理人短信授权情况下，没有录入法人信息（KP类型是法人的数据），不可保存");
                }
            }
        }
    }

    private WmCustomerKp defaultSignerAuthTypeAndVersion(WmCustomerKp updateKp, WmCustomerKp oldCustomerKp) {
        //非代理人需要设置为0
        if (updateKp.getSignerType() != KpSignerTypeEnum.AGENT.getType()) {
            updateKp.setLegalAuthType(LegalAuthTypeEnum.UN_SETTING.getCode());
        }
        //需要更新的签约代理人KP，无授权方式则默认纸面授权
        if (updateKp.getSignerType() == KpSignerTypeEnum.AGENT.getType()
                && (updateKp.getLegalAuthType() == null || updateKp.getLegalAuthType() == LegalAuthTypeEnum.UN_SETTING.getCode())) {
            updateKp.setLegalAuthType(LegalAuthTypeEnum.PAPER_AUTH.getCode());
        }

        //版本号非V3则需要更新为V3
        updateKp.setVersion(KpVersionEnum.V3.getCode());
        if (oldCustomerKp.getVersion() == null || !oldCustomerKp.getVersion().equals(KpVersionEnum.V3.getCode())) {
            oldCustomerKp.setVersion(KpVersionEnum.V3.getCode());
            wmCustomerKpDBMapper.updateVersion(KpVersionEnum.V3.getCode(), updateKp.getId());
        }
        //如果更新KP对象无操作来源参数直接设置成 未知
        if (updateKp.getOperateSource() == null) {
            updateKp.setOperateSource(CustomerDeviceType.UNKNOWN.getCode());
        }

        return updateKp;
    }
}
