package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerBrandCondition;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerBrandDB;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmCustomerBrandDBMapper {

    void batchInsert(List<WmCustomerBrandDB> list);

    int batchDelete(@Param("customerId") Integer customerId, @Param("brandIdList") List<Integer> brandIdList);

    List<Integer> selectBrandIdListByCustomerId(@Param("customerId") Integer customerId);

    List<Integer> selectListByCustomerIdAndBrandIdList(@Param("customerId") Integer customerId, @Param("brandIdList") List<Integer> brandIdList);

    List<WmCustomerBrandDB> selectCustomerIdListByBrandIdList(@Param("brandIdList") List<Integer> brandIdList);

    List<WmCustomerBrandDB> listByCondition(WmCustomerBrandCondition customerBrandCondition);

}
