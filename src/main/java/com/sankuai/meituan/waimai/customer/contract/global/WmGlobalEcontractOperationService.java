package com.sankuai.meituan.waimai.customer.contract.global;

import com.dianping.mobileossapi.dto.operate.ShortUrlResultThrift;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.customer.adapter.OperateServiceThriftAdapter;
import com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalContractExcelInfoDTO;
import com.sankuai.meituan.waimai.customer.service.common.ExcelExportService;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.GlobalContractInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/11 15:01
 */
@Service
@Slf4j
public class WmGlobalEcontractOperationService {

    @Resource
    private ExcelExportService excelExportService;

    @Resource
    private OperateServiceThriftAdapter operateServiceThriftAdapter;

    private static final String PDF_DOMAIN_NAME = "econtract.meituan.com";

    public String exportAndUploadExcel(List<GlobalContractInfo> globalContractInfos) {
        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            List<GlobalContractExcelInfoDTO> excelInfoDTOList = convertToExcelInfoDTOList(globalContractInfos);
            SSOUtil.SsoUser user = SSOUtil.getUser();
            String excelUrl = excelExportService.exportExcel(excelInfoDTOList, GlobalContractExcelInfoDTO.class, os, user);
            return generateShortLink(excelUrl);
        } catch (Exception e) {
            log.error("WmGlobalEcontractOperationService#exportAndUploadExcel, error", e);
            return null;
        }
    }

    private String generateShortLink(String longLink) {
        ShortUrlResultThrift shortLink = operateServiceThriftAdapter.generateShortLink(longLink);
        if (shortLink == null || shortLink.getStatus() == 0) {
            return longLink;
        }
        return shortLink.getShortUrl();
    }

    private List<GlobalContractExcelInfoDTO> convertToExcelInfoDTOList(List<GlobalContractInfo> globalContractInfos) {
        return globalContractInfos.stream()
                .map(this::convertToExcelInfoDTO)
                .collect(Collectors.toList());
    }

    private GlobalContractExcelInfoDTO convertToExcelInfoDTO(GlobalContractInfo globalContractInfo) {
        GlobalContractExcelInfoDTO excelInfoDTO = new GlobalContractExcelInfoDTO();
        BeanUtils.copyProperties(globalContractInfo, excelInfoDTO);
        excelInfoDTO.setCreateTime(formatTime(globalContractInfo.getCreateTime(), "-"));
        excelInfoDTO.setSignTime(formatTime(globalContractInfo.getSignTime(), "-"));
        excelInfoDTO.setDueTime(formatTime(globalContractInfo.getDueTime(), "长期有效"));
        excelInfoDTO.setPdfUrl(PDF_DOMAIN_NAME + globalContractInfo.getPdfUrl());
        return excelInfoDTO;
    }

    private String formatTime(long unixTime, String defaultStr) {
        if (unixTime <= 0L) {
            return defaultStr;
        }
        return TimeUtil.format((int) unixTime);
    }

}
