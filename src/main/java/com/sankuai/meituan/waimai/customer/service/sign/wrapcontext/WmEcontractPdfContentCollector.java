package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.annotation.PostConstruct;

import org.apache.thrift.TException;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.ParamInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo.Builder;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

/**
 * pdf信息组装
 */
@Service
@Slf4j
public class WmEcontractPdfContentCollector implements IWmEcontractDataCollector {

    @Autowired
    private List<IWmEcontractPdfContentInfoBoMaker> pdfMakerList;

    static Map<SignTemplateEnum, IWmEcontractPdfContentInfoBoMaker> enumAndWrapperMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        for (IWmEcontractPdfContentInfoBoMaker pdfMaker : pdfMakerList) {
            SignTemplateWrapper annotation = AopUtils.getTargetClass(pdfMaker).getAnnotation(SignTemplateWrapper.class);
            if (annotation == null || annotation.wrapperEnum() == null) {
                continue;
            }
            enumAndWrapperMap.put(annotation.wrapperEnum(), pdfMaker);
        }
    }

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext)
            throws WmCustomerException, IllegalAccessException, TException {
        log.info("WmEcontractPdfContentCollector#collect, middleContext: {}", JSONObject.toJSONString(middleContext));
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        List<StageBatchInfoBo> stageInfoBoList = targetContext.getStageInfoBoList();

        Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap = Maps.newHashMap();
        Map<String, ParamInfoBo> paramInfoBoMap = Maps.newHashMap();

        Collection<SignTemplateEnum> pdfEnumList = null;
        IWmEcontractPdfContentInfoBoMaker maker = null;
        List<PdfContentInfoBo> pdfInfoList = null;
        PdfContentInfoBo pdfContentInfoBo = null;
        for (Entry<String, Collection<SignTemplateEnum>> entry : tabPdfMap.entrySet()) {
            pdfEnumList = entry.getValue();
            pdfInfoList = Lists.newArrayList();
            for (SignTemplateEnum temp : pdfEnumList) {
                maker = enumAndWrapperMap.get(temp);
                if (maker == null) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, temp + "找不到处理类");
                }
                log.info("WmEcontractPdfContentCollector#collect, maker: {}", maker.getClass().getSimpleName());
                pdfContentInfoBo = maker.makePdfContentInfoBo(originContext, middleContext);
                if (pdfContentInfoBo != null) {
                    pdfInfoList.add(pdfContentInfoBo);
                }
            }
            pdfContentInfoBoMap.put(entry.getKey(), pdfInfoList);
        }
        StageBatchInfoBo stageBatchInfoBo = new Builder().stageName(WmEcontractConstant.CREATE_PDF)
                                                         .pdfContentInfoBoMap(pdfContentInfoBoMap)
                                                         .paramInfoBoMap(paramInfoBoMap)
                                                         .metaFlowList(Lists.newArrayList(tabPdfMap.keySet()))
                                                         .build();
        stageInfoBoList.add(stageBatchInfoBo);
    }
}
