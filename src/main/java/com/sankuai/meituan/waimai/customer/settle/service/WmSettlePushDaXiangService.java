package com.sankuai.meituan.waimai.customer.settle.service;

import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.settle.bo.WmPushDaXiangBo;
import com.sankuai.meituan.waimai.customer.settle.constant.WmSettlePushDaXiangConstant;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class WmSettlePushDaXiangService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmSettlePushDaXiangService.class);

    @Autowired
    private WmEmployService.Iface wmEmployService;

    @Autowired
    private WmCustomerService wmCustomerService;
    @Autowired
    private WmCrmNoticeService wmCrmNoticeService;

    /**
     * 发大象，通知错误信息
     *
     * @param errorType
     * @param errorModule
     * @param errorReason
     * @param opUid
     * @param wmPoiIdList
     * @return
     */
    public boolean pushDaXiangForErrorMsg(int wmCustomerId, WmSettlePushDaXiangConstant.errorType errorType, WmSettlePushDaXiangConstant.errorModule errorModule, WmSettlePushDaXiangConstant.errorReason errorReason, int opUid, List<Integer> wmPoiIdList) {
        WmPushDaXiangBo wmPushDaXiangBo = new WmPushDaXiangBo();
        wmPushDaXiangBo.setWmCustomerId(wmCustomerId);
        wmPushDaXiangBo.setErrorType(errorType);
        wmPushDaXiangBo.setErrorModule(errorModule);
        wmPushDaXiangBo.setErrorReason(errorReason);
        wmPushDaXiangBo.setOpUid(opUid);
        wmPushDaXiangBo.setWmPoiIdList(wmPoiIdList);
        boolean result = false;
        try {
            result = pushDaXiangForErrorMsg(wmPushDaXiangBo);
        } catch (WmCustomerException | TException e) {
            LOGGER.error("pushDaXiangForErrorMsg error",e);
        }
        return result;
    }

    /**
     * 无权限操作的门店，发大象通知
     * @param opUid
     * @param noAuthWmPoiIdList
     * @throws TException
     * @throws WmServerException
     */
    public void sendDaXiangForNonAuth(int opUid, List<Integer> noAuthWmPoiIdList) throws TException, WmServerException {

//        int size = noAuthWmPoiIdList.size();
//        if (size > 500) {
//            noAuthWmPoiIdList = noAuthWmPoiIdList.subList(0, 500);
//        }
//        WmEmploy owner = wmEmployService.getById(opUid);
//        if (owner != null) {
//            DaxiangUtil.push("<EMAIL>",
//                    String.format("无权限操作%s等%s个门店，请确保您是原合同负责人", StringUtils.join(noAuthWmPoiIdList, "、"), size),
//                    owner.getEmail());
//        }
    }

    /**
     * 发大象，通知错误信息
     *
     * @param wmPushDaXiangBo
     * @return
     */
    public boolean pushDaXiangForErrorMsg(WmPushDaXiangBo wmPushDaXiangBo) throws TException, WmCustomerException {
        if (wmPushDaXiangBo == null || wmPushDaXiangBo.getWmCustomerId() <= 0) {
            return false;
        }
        int wmCustomerId = wmPushDaXiangBo.getWmCustomerId();
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerId);
        int ownerUid = 0;
        if(wmCustomerDB !=null) {
            ownerUid = wmCustomerDB.getOwnerUid();
        }
        if(ownerUid == 0){
            LOGGER.warn("客户无负责人信息,wmCustomerId={}",wmCustomerId);
            return false;
        }
        WmEmploy wmEmploy;
        try{
            wmEmploy = wmEmployService.getById(wmPushDaXiangBo.getOpUid());
        }catch(WmServerException | TException e) {
            LOGGER.error("BD操作错误发大象失败 wmCustomerId = {}", wmCustomerId, e);
            return false;
        }

        //拼装大象消息内容
        StringBuilder sb = new StringBuilder();
        //公共头部
        String title="【客户"+wmPushDaXiangBo.getErrorType().getDesc()+"有误】";
        sb.append(title);
        //合同编号
        sb.append(" 客户ID：");
        sb.append(wmCustomerDB.getId());
        //操作模块：1.关联门店 2.线下垫款 3.平台使用费 4.配送分成设置
        sb.append(" 操作模块：");
        sb.append(wmPushDaXiangBo.getErrorModule().getDesc());
        sb.append(" ");
        //牵涉商家
        sb.append(wmPushDaXiangBo.getErrorType().getDesc());
        sb.append(wmPushDaXiangBo.getErrorReason().getDesc());
        sb.append("，商家id:");
        sb.append(StringUtils.join(wmPushDaXiangBo.getWmPoiIdList(), ","));
        //通知时间
        sb.append(" 时间");
        sb.append(TimeUtil.format(TimeUtil.MINUTE_FORMAT, TimeUtil.unixtime()));

        //发消息
        wmCrmNoticeService.customerOperationNotify(String.valueOf(wmEmploy.getUid()),title,wmCustomerId,sb.toString());
        return true;
    }
}
