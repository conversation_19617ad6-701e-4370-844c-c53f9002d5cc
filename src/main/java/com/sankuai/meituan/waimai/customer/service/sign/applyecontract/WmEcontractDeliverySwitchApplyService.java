package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import javax.annotation.Resource;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
public class WmEcontractDeliverySwitchApplyService extends AbstractWmEcontractApplyAdapterService {

    @Resource
    private WmEcontractDeliverySupportApplyService wmEcontractDeliverySupportApplyService;

    @Resource
    private WmEcontractDeliveryApplyService wmEcontractDeliveryApplyService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo,
            EcontractSignDataFactor econtractSignDataFactor)
            throws TException, IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);

        if (econtractSignDataFactor == null) {
            econtractSignDataFactor = new EcontractSignDataFactor();
        }

        EcontractDeliveryWholeCityInfoBo econtractDeliveryWholeCityInfoBo = deliveryInfoBo
                .getEcontractDeliveryWholeCityInfoBo();
        EcontractDeliveryAggregationInfoBo econtractDeliveryAggregationInfoBo = deliveryInfoBo
                .getEcontractDeliveryAggregationInfoBo();
        EcontractDeliveryCompanyCustomerLongDistanceInfoBo econtractDeliveryCompanyCustomerLongDistanceInfoBo = deliveryInfoBo
                .getEcontractDeliveryCompanyCustomerLongDistanceInfoBo();
        if (econtractDeliveryWholeCityInfoBo != null) {
            econtractSignDataFactor.setDeliverySupportWholeCity(true);
        }
        if (econtractDeliveryAggregationInfoBo != null) {
            econtractSignDataFactor.setDeliverySupportAggregation(true);
        }
        if (econtractDeliveryCompanyCustomerLongDistanceInfoBo != null) {
            econtractSignDataFactor.setDeliverySupportCompanyCustomerLongDistanceDelivery(true);
        }
        if (WmEcontractContextUtil.SUPPORT_MARK.equals(deliveryInfoBo.getSupportExclusive())) {
            econtractSignDataFactor.setDeliverySupportExclusive(true);
        }

        // 需要签章
        if (WmEcontractContextUtil.SUPPORT_MARK.equals(deliveryInfoBo.getSupportExclusive())
                || econtractSignDataFactor.isDeliverySupportAggregation()) {
            return wmEcontractDeliverySupportApplyService.wrapEcontractBo(batchContextBo, econtractSignDataFactor);
        } else {
            return wmEcontractDeliveryApplyService.wrapEcontractBo(batchContextBo, econtractSignDataFactor);
        }
    }

}
