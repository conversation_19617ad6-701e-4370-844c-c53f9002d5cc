package com.sankuai.meituan.waimai.customer.service.sc.mafka;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.WmSchoolDeliveryNewService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CampusDeliverListener implements IMessageListener {

    @Autowired
    private WmSchoolDeliveryNewService wmSchoolDeliveryNewService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext messagetContext) {
        try {
            log.info("[CampusDeliverListener.recvMessage] input param: message={}, partition={}", message.getBody(), message.getParttion());

            if (!MccScConfig.getCampusDeliverConsumerSwitch()) {
                log.info("[CampusDeliverListener.recvMessage] switch down");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            if (null == message.getBody() || StringUtils.isBlank(message.getBody().toString())) {
                log.error("[CampusDeliverListener.recvMessage] message body is null, return. message = {}", JSONObject.toJSONString(message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            JSONObject jsonObject = JSONObject.parseObject(message.getBody().toString());
            Long deliverId = jsonObject.getLong("deliverId");

            wmSchoolDeliveryNewService.indexDeliveryPlanById(deliverId);
            return ConsumeStatus.CONSUME_SUCCESS;

        } catch (Exception e) {
            log.error("[CampusDeliverListener.recvMessage] consumer failed", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }
}
