package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.zebra.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.meituan.waimai.agent.yak.contract.client.exception.ContractException;
import com.meituan.waimai.agent.yak.contract.client.request.common.AgentIdRequest;
import com.meituan.waimai.agent.yak.contract.client.response.contract.ContractContextResponse;
import com.meituan.waimai.agent.yak.contract.client.service.ContractThrift;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.mtcoop.thrift.dto.TFrame;
import com.sankuai.meituan.mtcoop.thrift.dto.TFrameCoop;
import com.sankuai.meituan.mtcoop.thrift.exception.TCoopException;
import com.sankuai.meituan.mtcoop.thrift.exception.TIllegalArgumentException;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.util.StringUtil;
import com.sankuai.meituan.waimai.contract.thrift.domain.WmAgentContract;
import com.sankuai.meituan.waimai.contract.thrift.service.WmAgentContractThriftService;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.adapter.agent.CustomerIdMappingServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.daocan.FrameCoopServiceAdapter;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccGrayConfig;
import com.sankuai.meituan.waimai.customer.constant.ContractFtlTagConstant;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.bo.BatchContractUpdateContext;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.contract.config.dto.WmManualSignTaskContext;
import com.sankuai.meituan.waimai.customer.contract.dao.*;
import com.sankuai.meituan.waimai.customer.contract.domain.*;
import com.sankuai.meituan.waimai.customer.contract.frame.WmCustomerFrameContractAuthService;
import com.sankuai.meituan.waimai.customer.contract.frame.util.DcContractStatusMapUtil;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.external.contract.BatchUpdateContractCheckFilter;
import com.sankuai.meituan.waimai.customer.contract.service.impl.wrapper.ContractWrapperBuilder;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerRelMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.ddd.contract.DDDGrayUtil;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.dservice.IWmContractReadDomainService;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.dservice.IWmContractWriteDomainService;
import com.sankuai.meituan.waimai.customer.domain.*;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.util.*;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.base.EncryptUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.domain.ContractTagConstant;
import com.sankuai.meituan.waimai.heron.agentcontract.dto.contract.WmAgentFwContractQueryByAgentIdRequestDTO;
import com.sankuai.meituan.waimai.heron.agentcontract.dto.contract.WmAgentFwContractQueryResponseDTO;
import com.sankuai.meituan.waimai.heron.agentcontract.exception.WmAgentFwException;
import com.sankuai.meituan.waimai.heron.agentcontract.service.WmAgentFwContractThriftService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.WmSupplyChainDataSyncThriftService;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerPoiLogisticsSubjectBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticeMainBodyTypeEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticeMsgFormatEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticeMustReadEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticePushMediaEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.OpCustomerResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.OpResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.CommonResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.request.CustomerContractExpireRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.request.CustomerContractInvalidRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractQueryResponseDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.request.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.response.StartSignByPoiResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModuleStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.StatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.partner.DaoCanContractInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpRequest;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCallbackBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskSettleContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.dto.PublishNoticeDto;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmNoticePublishThriftService;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import com.sankuai.shangou.merchant.compliance.thrift.client.SgPoiContractClient;
import com.sankuai.shangou.merchant.compliance.thrift.constant.admit.ContractResignSourceEnum;
import com.sankuai.shangou.merchant.compliance.thrift.param.poiContractReq.ResignReq;
import joptsimple.internal.Strings;
import org.apache.thrift.TException;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.contract.service.TempletServiceRouter.getService;


@Service
public class WmContractService {

    private static Logger logger = LoggerFactory.getLogger(WmContractService.class);

    public static final int READ_OPERATION = 1;

    public static final int WRITE_OPERATION = 2;

    @Autowired
    private WmAgentFwContractThriftService wmAgentFwContractThriftService;

    @Autowired
    WmTempletContractDBMapper wmTempletContractDBMapper;

    @Autowired
    WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    @Autowired
    WmTempletContractSignDBMapper wmTempletContractSignDBMapper;

    @Autowired
    WmTempletContractSignAuditedDBMapper wmTempletContractSignAuditedDBMapper;

    @Autowired
    ContractLogService contractLogService;

    @Autowired
    WmContractVersionService wmContractVersionService;

    @Autowired
    private WmContractFtlTagMapper wmContractFtlTagMapper;

    @Autowired
    private WmPoiClient wmPoiClient;

    @Autowired
    private WmContractSignService wmContractSignService;

    @Autowired
    WmCustomerService wmCustomerService;

    @Autowired
    WmCustomerThriftService wmCustomerThriftService;

    @Autowired
    WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    WmCustomerKpService wmCustomerKpService;

    @Autowired
    WmSettleService wmSettleService;

    @Autowired
    WmAgentContractThriftService.Iface wmAgentContractThriftService;

    @Autowired
    WmContractPoiProduceService wmContractPoiProduceService;

    @Autowired
    WmEmployeeService wmEmployeeService;
    @Autowired
    WmNoticePublishThriftService.Iface wmNoticePublishThriftService;

    @Autowired
    protected WmCustomerOplogService wmCustomerOplogService;

    @Autowired
    WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    IWmContractWriteDomainService wmContractWriteDomainService;

    @Autowired
    IWmContractReadDomainService contractReadDomainService;

    @Autowired
    WmCustomerBlackWhiteListService wmCustomerBlackWhiteListService;

    @Autowired
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;

    @Resource
    WmEmployClient wmEmployClient;

    @Autowired
    EmpServiceAdaptor empServiceAdaptor;

    @Autowired
    WmContractOnlineService wmContractOnlineService;

    @Autowired
    WmContractValidCallbackService wmContractValidCallbackService;

    @Autowired
    TairLocker tairLocker;

    @Autowired
    private WmCustomerPoiSubjectService wmCustomerPoiSubjectService;

    @Autowired
    private WmContractWhiteListServiceAdapter wmContractWhiteListServiceAdapter;

    @Autowired
    private WmSupplyChainDataSyncThriftService wmSupplyChainDataSyncThriftService;

    @Autowired
    private WmTempletContractRelMapper wmTempletContractRelMapper;

    @Autowired
    private WmTempletContractExtensionMapper wmTempletContractExtensionMapper;

    @Autowired
    private WmBusinessCustomerTempletService wmBusinessCustomerTempletService;

    @Autowired
    private WmCustomerRelMapper wmCustomerRelMapper;

    @Autowired
    IWmCustomerRealService wmLeafCustomerRealService;

    @Autowired
    private WmContractCustomerService wmContractCustomerService;

    @Autowired
    private WmPoiPromotionServiceEContractTempletService wmPoiPromotionServiceEContractTempletService;

    @Autowired
    private BanmaOpenPoiClient banmaOpenPoiClient;

    @Autowired
    private WmSubjectChangeSupplementEContractTempletService wmSubjectChangeSupplementEContractTempletServicel;
    @Autowired
    private ContractThrift contractThrift;
    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmLabelAdaptor wmLabelAdaptor;

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    @Resource
    private FrameCoopServiceAdapter frameCoopServiceAdapter;

    @Resource
    private CustomerIdMappingServiceAdapter customerIdMappingServiceAdapter;

    @Resource
    private WmAndDcCustomerRelService wmAndDcCustomerRelService;

    @Resource
    private WmPartnerContractService wmPartnerContractService;

    @Resource
    private WmCustomerFrameContractAuthService wmCustomerFrameContractAuthService;

    @Resource
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;
    @Autowired
    private SgPoiContractClient sgPoiContractClient;


    private static final String DEFAULT_NOTICE_MSG_TITLE = "合同签约成功";

    private static final String SH_SANKUAI_ZHISONG = "上海三快智送科技有限公司";

    // 查询到餐C1合同接口最大数量
    private final Integer QUERY_DC_C1_CONTRACT_MAX_SIZE = 100;

    private static Map<Integer, String> noticeMsgTitleMap = Maps.newHashMap();

    static {
        noticeMsgTitleMap.put(WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(), "资质属实商家承诺函签约成功");
        noticeMsgTitleMap.put(WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(), "门店推广技术服务合同签约成功");
    }

    private static Splitter SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    private static final int PAGE_SIZE = 100;

    private static final int INSERT = 0;

    private static final int UPDATE = 1;

    private static final ExecutorService executorServiceForEffect = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(10, 50, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(100), new ThreadPoolExecutor.AbortPolicy()));

    static Set<Integer> autoSaveSignContractTypeSet = Sets.newHashSet(
            WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode()
    );

    static Set<Integer> canResendAndCancelContractTypeSet = Sets.newHashSet(
            WmTempletContractTypeEnum.DELIVERY_SERVICE_CONTRACT_E.getCode(),
            WmTempletContractTypeEnum.DELIVERY_SITE_CONTRACT_E.getCode(),
            WmTempletContractTypeEnum.AD_ANNUAL_FRAMEWORK_CONTRACT_E.getCode(),
            WmTempletContractTypeEnum.BRAND_AD_CONTRACT_E.getCode(),
            WmTempletContractTypeEnum.AD_ORDER_E.getCode(),
            WmTempletContractTypeEnum.PHF_CHARGE_E.getCode()
    );

    private static final Set<Integer> CONTRACT_TYPES_NEED_EFFECT_NOTICE = Sets.newHashSet(
            WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
            WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
    );

    private static final ExecutorService executorService = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(10, 50, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(100), new ThreadPoolExecutor.AbortPolicy()));

    private final static ExecutorService handleService = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(10, 50, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(1024), new ThreadPoolExecutor.AbortPolicy()));

    private static final ExecutorServiceTraceWrapper sgRenewExecutorService = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(10,
                    10,
                    0,
                    TimeUnit.MILLISECONDS,
                    new ArrayBlockingQueue<>(1000),
                    new ThreadFactoryBuilder().setNameFormat("renew-sg-fee").build(),
                    new ThreadPoolExecutor.DiscardPolicy())
    );

    public WmTempletContractBasicBo getBasicById(long templetId, boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("##getBasicById  templetId : {} isEffective : {}  opUid : {}  opUname : {}", templetId, isEffective, opUid, opName);
        WmTempletContractDB wmTempletContractDB;
        if (isEffective) {
            wmTempletContractDB = wmTempletContractAuditedDBMapper.selectByPrimaryKey(templetId);
        } else {
            wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(templetId);
        }
        return WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB);
    }

    public WmCustomerContractBo getWmCustomerContractBoById(long templetId, boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("##getWmCustomerContractBoById  templetId : {} isEffective : {}  opUid : {}  opUname : {}", templetId, isEffective, opUid, opName);
        WmCustomerContractBo customerContractBo = new WmCustomerContractBo();
        if (templetId <= 0) {
            return customerContractBo;
        }
        WmTempletContractDB wmTempletContractDB;
        List<WmTempletContractSignDB> templetContractSignDBS;
        if (isEffective) {
            wmTempletContractDB = wmTempletContractAuditedDBMapper.selectByPrimaryKey(templetId);
            templetContractSignDBS = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(templetId);
        } else {
            wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(templetId);
            templetContractSignDBS = wmTempletContractSignDBMapper.selectByWmTempletContractId(templetId);
        }
        customerContractBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB));
        customerContractBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(templetContractSignDBS));
        logger.info("WmContractService#getWmCustomerContractBoById, customerContractBo: {}", JSON.toJSONString(customerContractBo));
        return customerContractBo;
    }

    public List<WmCustomerContractBo> getWmCustomerContractBoListByIdList(List<Long> templetIdList, boolean isEffective, int opUid, String opName) {
        logger.info("##getWmCustomerContractBoListByIdList  contractIdList : {} isEffective : {}  opUid : {}  opUname : {}", JSON.toJSONString(templetIdList), isEffective, opUid, opName);
        List<WmCustomerContractBo> wmCustomerContractBoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(templetIdList)) {
            return wmCustomerContractBoList;
        }

        List<WmTempletContractDB> wmTempletContractDBList = Lists.newArrayList();
        List<WmTempletContractSignDB> wmTempletContractSignDBList = Lists.newArrayList();
        if (isEffective) {
            wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByPrimaryKeyList(templetIdList);
            wmTempletContractSignDBList = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractIdList(templetIdList);
        } else {
            wmTempletContractDBList = wmTempletContractDBMapper.selectByPrimaryKeyList(templetIdList);
            wmTempletContractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractIdList(templetIdList);
        }

        Multimap<Long, WmTempletContractSignDB> contractSignDBMap = Multimaps.index(wmTempletContractSignDBList, new Function<WmTempletContractSignDB, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable WmTempletContractSignDB input) {
                return Long.valueOf(input.getWmTempletContractId());
            }
        });
        for (WmTempletContractDB wmTempletContractDB : wmTempletContractDBList) {
            WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
            wmCustomerContractBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB));
            wmCustomerContractBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(Lists.newArrayList(contractSignDBMap.get(wmTempletContractDB.getId()))));

            wmCustomerContractBoList.add(wmCustomerContractBo);
        }
        return wmCustomerContractBoList;
    }

    public List<WmTempletContractBasicBo> getWmCustomerContractBasicListByPoiIdIdAndTypes(long wmPoiId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("查询合同基本信息列表  wmPoiId:{}, types：{}， opUid:{}, opUname:{}", wmPoiId, types, opUid, opName);
        WmCustomerDB customerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if (customerDB != null) {
            return getContractBasicBoListByCusIdAndType(customerDB.getId(), types, opUid, opName);
        }
        return Lists.newArrayList();
    }

    public List<WmTempletContractBasicBo> getAuditedContractBasicListByPoiIdAndTypeRT(long wmPoiId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("查询合同基本信息列表  wmPoiId:{}, types：{}， opUid:{}, opUname:{}", wmPoiId, types, opUid, opName);
        WmCustomerDB customerDB = MasterSlaveHelper.doInMaster(() -> wmCustomerService.selectCustomerByWmPoiId(wmPoiId));
        if (customerDB != null) {
            logger.info(" 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerDB.getId(), types, opUid, opName);
            List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper
                    .getBasicListByParentIdAndTypesMaster(customerDB.getId(), types);
            return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
        }
        return Lists.newArrayList();
    }

    public List<WmTempletContractBasicBo> getAuditedContractBasicListByPoiIdAndType(long wmPoiId, List<Integer> types, int opUid,
                                                                                    String opName) throws WmCustomerException {
        logger.info("查询合同基本信息列表#slave  wmPoiId:{}, types：{}， opUid:{}, opUname:{}", wmPoiId, types, opUid, opName);
        WmCustomerDB customerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if (customerDB != null) {
            logger.info(" 查询合同基本信息#slave  customerId:{}, types：{}， opUid:{}, opUname:{}", customerDB.getId(), types, opUid, opName);
            List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper
                    .getBasicListByParentIdAndTypes(customerDB.getId(), types);
            return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
        }
        return Lists.newArrayList();
    }

    /**
     * 根据客户id获取合同列表
     */
    public ContractBoPageData getWmCustomerContractBoListByCusId(int customerId, int pageNo, int pageSize, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("查询合同列表  customerId:{}, pageNo:{}, pageSize:{} opUid:{}, opUname:{}", customerId, pageNo, pageSize, opUid, opName);
        PageData<WmTempletContractDB> basicPage = pageGetBasicByCusId(customerId, pageNo, pageSize);
        List<WmCustomerContractBo> showResult = Lists.newArrayList();
        for (WmTempletContractDB o : basicPage.getList()) {
            WmCustomerContractBo one = new WmCustomerContractBo();
            one.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(o));
            List<WmTempletContractSignDB> contractSignDBList = wmTempletContractSignDBMapper
                    .selectByWmTempletContractId(o.getId());
            List<WmTempletContractSignBo> contractSignBoList = WmTempletContractTransUtil.templetSignDbToBoList(contractSignDBList);
            one.setSignBoList(contractSignBoList);
            showResult.add(one);
        }
        PageData<WmCustomerContractBo> boPageData = PageUtil.page(basicPage.getList(), showResult);

        return new ContractBoPageData(boPageData.getPageInfo(), boPageData.getList());
    }

    /**
     * 根据客户id获取合同列表
     */
    public ContractBoPageData queryWmCustomerContractBoList(QueryWmCustomerContractReq req) throws WmCustomerException, TException {
        logger.info("queryWmCustomerContractBoList 查询合同列表  req:{}", JSON.toJSONString(req));
        Integer businessGroupLine = req.getBusinessGroupLine();
        PageData<WmTempletContractDB> basicPage;
        if (Objects.nonNull(businessGroupLine) && businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())) {
            if (StringUtils.isBlank(req.getContractType())) {
                throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "不支持查询全部类型合同");
            }
            basicPage = queryDcCustomerContract(req);
        } else {
            basicPage = queryCustomerContract(req);
        }

        List<WmCustomerContractBo> showResult = Lists.newArrayList();
        for (WmTempletContractDB o : basicPage.getList()) {
            WmCustomerContractBo one = new WmCustomerContractBo();
            one.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(o));
            // 到餐C1合同，可能为空
            List<WmTempletContractSignDB> contractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractId(o.getId());
            List<WmTempletContractSignBo> contractSignBoList = WmTempletContractTransUtil.templetSignDbToBoList(contractSignDBList);

            Integer contractType = o.getType();
            if (contractType.equals(WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode())
                    && CollectionUtils.isEmpty(contractSignBoList)) {
                buildDcC1SignBo(one);
            } else {
                one.setSignBoList(contractSignBoList);
            }
            showResult.add(one);
        }
        PageData<WmCustomerContractBo> boPageData = PageUtil.page(basicPage.getList(), showResult);

        return new ContractBoPageData(boPageData.getPageInfo(), boPageData.getList());
    }

    /**
     * 构建到餐签约人信息
     * @param wmCustomerContractBo
     */
    private void buildDcC1SignBo(WmCustomerContractBo wmCustomerContractBo) {
        List<WmTempletContractSignBo> signBoList = new ArrayList<>();
        WmTempletContractSignBo signA = getDcC1SignA();
        WmTempletContractSignBo signB = getDcC1SignB();
        signBoList.add(signA);
        signBoList.add(signB);
        wmCustomerContractBo.setSignBoList(signBoList);
    }

    /**
     * 获取到餐C1乙方信息
     * @return
     */
    private WmTempletContractSignBo getDcC1SignB() {
        WmTempletContractSignBo wmTempletContractSignBo = new WmTempletContractSignBo();
        wmTempletContractSignBo.setSignType("B");
        wmTempletContractSignBo.setSignName(MccConfig.getDcC1SignInfo());

        return wmTempletContractSignBo;
    }

    /**
     * 查询到餐甲方签约人信息
     * @return
     */
    private WmTempletContractSignBo getDcC1SignA() {
        WmTempletContractSignBo wmTempletContractSignBo = new WmTempletContractSignBo();
        wmTempletContractSignBo.setSignType("A");
        wmTempletContractSignBo.setSignPeople(MccConfig.getDcC1SignInfo());
        return wmTempletContractSignBo;
    }

    public List<WmTempletContractBasicBo> listWmTempletContract(ListWmCustomerContractReq req)throws WmCustomerException{
        if(CollectionUtils.isEmpty(req.getContractTypes())){
            throw new WmCustomerException(500,"合同类型不能为空");
        }
        if(req.getStartDueDate() == null && req.getEndDueDate()==null){
            throw new WmCustomerException(500,"合同到期时间不能为空");
        }
        if(req.getSize() == null || req.getSize() > 500){
            throw new WmCustomerException(500,"size不能为空或者大于500");
        }
        List<WmTempletContractDB> templetContractDBList =wmTempletContractAuditedDBMapper
                .listWmTempletContract(req.getStartDueDate(),req.getEndDueDate(),req.getContractTypes(),req.getStartId(),req.getSize());
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(templetContractDBList);
    }

    private PageData pageGetBasicByCusId(long customerId, int pageNo, int pageSize)
            throws WmCustomerException, TException {
        PageHelper.startPage(pageNo, pageSize);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper
                .selectByParentIdAndTypes(customerId, Lists.newArrayList(
                        WmTempletContractTypeEnum.C1_E.getCode(),
                        WmTempletContractTypeEnum.C1_PAPER.getCode(),
                        WmTempletContractTypeEnum.C2_E.getCode(),
                        WmTempletContractTypeEnum.C2_PAPER.getCode(),
                        WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(),
                        WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(),
                        WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(),
                        WmTempletContractTypeEnum.GROUP_MEAL_E.getCode(),
                        WmTempletContractTypeEnum.BAG_SERVICE_E.getCode(),
                        WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode(),
                        WmTempletContractTypeEnum.INTERIM_SELF_E.getCode(),
                        WmTempletContractTypeEnum.MEDIC_ORDER_SPLIT_E.getCode(),
                        WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode(),
                        WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
                        WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
                ));
        PageHelper.clearPage();
        return PageUtil.page(wmTempletContractDBList);
    }

    private PageData queryCustomerContract(QueryWmCustomerContractReq req)
            throws WmCustomerException, TException {
        PageHelper.startPage(req.getPageNo(), req.getPageSize());

        List<ContractConfigInfo> configInfoList = wmFrameContractConfigService.allConfigFrameContract();
        List<Integer> allConfigContractId;
        if (req.getDeviceType() != null && CustomerDeviceType.APP == req.getDeviceType()) {
            allConfigContractId = configInfoList.stream()
                    .filter(contractConfigInfo -> contractConfigInfo.getSourceAuthInfo() != null
                            && contractConfigInfo.getSourceAuthInfo().getCanDisplayInBee())
                    .map(ContractConfigInfo::getContractId)
                    .collect(Collectors.toList());

        } else {
            allConfigContractId = configInfoList.stream()
                    .map(ContractConfigInfo::getContractId)
                    .collect(Collectors.toList());
        }

        List<Integer> types = new ArrayList<>();
        if (StringUtils.isNotBlank(req.getContractType())) {
            Integer contractType = Integer.valueOf(req.getContractType());
            for (WmTempletContractTypeEnum bizType : WmTempletContractTypeEnum.values()) {
                if (bizType.getCode() / 1000 == contractType) {
                    types.add(bizType.getCode());
                }
            }
            if (allConfigContractId.contains(contractType)) {
                types.add(contractType);
            }
        } else {
            types = Lists.newArrayList(
                    WmTempletContractTypeEnum.C1_E.getCode(),
                    WmTempletContractTypeEnum.C1_PAPER.getCode(),
                    WmTempletContractTypeEnum.C2_E.getCode(),
                    WmTempletContractTypeEnum.C2_PAPER.getCode(),
                    WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(),
                    WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(),
                    WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(),
                    WmTempletContractTypeEnum.GROUP_MEAL_E.getCode(),
                    WmTempletContractTypeEnum.BAG_SERVICE_E.getCode(),
                    WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode(),
                    WmTempletContractTypeEnum.INTERIM_SELF_E.getCode(),
                    WmTempletContractTypeEnum.MEDIC_ORDER_SPLIT_E.getCode(),
                    WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode(),
                    WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
                    WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode(),
                    WmTempletContractTypeEnum.NATIONAL_SUBSIDY_PURCHASE_AGREEMENT.getCode()
            );
            types.addAll(allConfigContractId);
        }
        if (StringUtils.isBlank(req.getContractNum())) {
            req.setContractNum(null);
        }
        List<WmTempletContractDB> wmTempletContractDBList;
        if (StringUtils.isBlank(req.getPartBSignName())) {
            wmTempletContractDBList = wmTempletContractDBMapper
                    .queryContract((long) req.getCustomerId(), types, req.getContractNum(), req.getStartTime(), req.getEndTime());
        } else {
            String partBSignName = "%" + req.getPartBSignName() + "%";
            wmTempletContractDBList = wmTempletContractDBMapper
                    .queryContractWithPartNum((long) req.getCustomerId(), types, req.getContractNum(), req.getStartTime(), req.getEndTime(), partBSignName);
        }
        PageHelper.clearPage();
        return PageUtil.page(wmTempletContractDBList);
    }

    /**
     * 查询到餐合同列表
     * @param req
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    private PageData queryDcCustomerContract(QueryWmCustomerContractReq req)
            throws WmCustomerException, TException {
        if (Objects.isNull(req.getCustomerId())) {
            throw new WmCustomerException(500, "客户id不能为空");
        }

        // 默认只展示C1
        Integer type = null;
        if (StringUtils.isNotBlank(req.getContractType())) {
            Integer contractType = Integer.valueOf(req.getContractType());
            for (WmTempletContractTypeEnum bizType : WmTempletContractTypeEnum.values()) {
                if (bizType.getCode() / 1000 == contractType) {
                    type = bizType.getCode();
                    break;
                }
            }
        }
        if (StringUtils.isBlank(req.getContractNum())) {
            req.setContractNum(null);
        }

        List<WmTempletContractDB> wmTempletContractDBList = null;
        if (Objects.nonNull(type) && type.equals(WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode())) {
            // 查询到餐C1合同
            wmTempletContractDBList = queryDcC1ContractList(req);
        } else if (Objects.nonNull(type) && type.equals(WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode())){
            // C2合同查询数据库
            PageHelper.startPage(req.getPageNo(), req.getPageSize());
            if (StringUtils.isBlank(req.getPartBSignName())) {
                wmTempletContractDBList = wmTempletContractDBMapper.queryContractWithType((long) req.getCustomerId(), type, req.getContractNum());
            } else {
                String partBSignName = "%" + req.getPartBSignName() + "%";
                wmTempletContractDBList = wmTempletContractDBMapper.queryContractWithTypeAndPartNum((long) req.getCustomerId(), type, req.getContractNum(), partBSignName);
            }
            PageHelper.clearPage();
        }

        return PageUtil.page(wmTempletContractDBList);
    }

    /**
     * 查询到餐C1合同列表
     * @param req
     * @return
     * @throws TException
     * @throws TCoopException
     * @throws TIllegalArgumentException
     */
    private List<WmTempletContractDB> queryDcC1ContractList(QueryWmCustomerContractReq req) throws WmCustomerException {
        try {
            int wmCustomerId = req.getCustomerId();
            // 获取到餐平台客户ID
            List<Long> dcPlatformIdList = getDcPlatformIdList(wmCustomerId);
            // 获取到餐客户ID
            Map<Long, Long> dcCustomer2PlatformIdMap = getDcCustomer2PlatformIdMap(req.getOpUid(), dcPlatformIdList);
            if (!CollectionUtils.isEmpty(dcCustomer2PlatformIdMap)) {
                return getDcC1ContractListWithCondition(req, dcCustomer2PlatformIdMap);
            }
            // 仅查询先富
            return getDcC1ContractListWithCondition(req);
        } catch (Exception e) {
            logger.error("queryDcCustomerContract req:{}, error:{}", req, e.getMessage());
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "到餐C1合同查询失败");
        }
    }

    /**
     * 获取到餐客户ID与平台客户Id的Map
     * @param uid
     * @param dcPlatformIdList
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private Map<Long, Long> getDcCustomer2PlatformIdMap(Integer uid, List<Long> dcPlatformIdList) throws TException, WmCustomerException {
        Map<Long, Long> dcCustomer2PlatformIdMap = new HashMap<>();
        for (Long dcPlatformId : dcPlatformIdList) {
            if (Objects.nonNull(dcPlatformId)
                    && wmCustomerFrameContractAuthService.hasDcWriteAuthorityByMtCustomerId(dcPlatformId, uid)) {
                Long originCustomerId = customerIdMappingServiceAdapter.getOriginCustomerIdByMtCustomerId(dcPlatformId);
                dcCustomer2PlatformIdMap.put(originCustomerId, dcPlatformId);
            }
        }

        logger.info("getDcCustomerIdList dcCustomer2PlatformIdMap:{}",JSON.toJSON(dcCustomer2PlatformIdMap));
        return dcCustomer2PlatformIdMap;
    }

    /**
     * 查询到餐平台客户ID列表
     * @param wmCustomerId
     * @return
     */
    private List<Long> getDcPlatformIdList(Integer wmCustomerId ) throws TException, WmCustomerException {
        List<Long> dcPlatformIdList = wmAndDcCustomerRelService.getDcPlatformIdByWmCustomerId(wmCustomerId);
        logger.info("queryDcC1ContractList wmCustomerId:{}, dcPlatformIdList:{}", wmCustomerId, JSON.toJSON(dcPlatformIdList));
        if (CollectionUtils.isEmpty(dcPlatformIdList)) {
            return Collections.emptyList();
        }
        return dcPlatformIdList;
    }

    /**
     * 条件查询到餐C1合同列表（包含到餐侧发起的C1）
     * @return
     */
    private List<WmTempletContractDB> getDcC1ContractListWithCondition(QueryWmCustomerContractReq req, Map<Long, Long> dcCustomer2PlatformIdMap) throws TException, TCoopException, TIllegalArgumentException {
        // todo zyh 全量查询oom问题
        List<WmTempletContractDB> contractList = new ArrayList<>();
        Set<Long> dcCustomerIdSet = dcCustomer2PlatformIdMap.keySet();
        for (Long dcCustomerId : dcCustomerIdSet) {
            Long dcPlatformId = dcCustomer2PlatformIdMap.get(dcCustomerId);
            if (Objects.isNull(dcCustomerId) || Objects.isNull(dcCustomerId)) {
                continue;
            }

            List<WmTempletContractDB> dcC1Contract = getDcC1Contract(req, dcCustomerId.intValue(), dcPlatformId);
            contractList.addAll(dcC1Contract);
            if (isExtendListMaxSize(contractList)) {
                contractList = contractList.subList(0, QUERY_DC_C1_CONTRACT_MAX_SIZE);
                break;
            }
        }
        List<WmTempletContractDB> collect = contractList.stream()
                .sorted(Comparator.comparing(WmTempletContractDB::getCtime).reversed()).collect(Collectors.toList());

        return PageUtil.listToPage(collect, req.getPageNo(), req.getPageSize());
    }

    /**
     * 判断是否超过最大查询数量
     * @param contractList
     * @return
     */
    private boolean isExtendListMaxSize(List<WmTempletContractDB> contractList) {
        if (contractList.size() >= QUERY_DC_C1_CONTRACT_MAX_SIZE) {
            return true;
        }
        return false;
    }

    /**
     * 通过到餐客户ID查询到餐C1合同列表
     * @return
     */
    private List<WmTempletContractDB> getDcC1Contract(QueryWmCustomerContractReq req, Integer dcCustomerId, Long dcPlatformId) throws TException, TCoopException, TIllegalArgumentException {
        List<TFrameCoop> dcC1ContractList = frameCoopServiceAdapter.getDcC1ContractList(dcCustomerId);
        int wmCustomerId = req.getCustomerId();
        Map<String, WmTempletContractDB> contractProof2WmTempletContractDB = buildC1ContractProofMap(wmCustomerId);

        List<WmTempletContractDB> wmTempletContractDBList = new ArrayList<>();
        for (TFrameCoop tFrameCoop : dcC1ContractList) {
            if (Objects.isNull(tFrameCoop)) {
                continue;
            }

            String c1ContractProof = tFrameCoop.getTFrame().getFrameId();
            WmTempletContractDB wmTempletContractDB = null;
            if (!StringUtils.isEmpty(c1ContractProof) && contractProof2WmTempletContractDB.containsKey(c1ContractProof)) {
                wmTempletContractDB = contractProof2WmTempletContractDB.get(c1ContractProof);
                wmTempletContractDB.setNumber(tFrameCoop.getTFrame().getFrameNum());
            } else {
                // 到餐侧发起的C1合同
                wmTempletContractDB = convertToWmTempletContractDB(tFrameCoop, wmCustomerId);
                fillDcC1BizData(wmTempletContractDB, tFrameCoop, dcPlatformId);
            }
            wmTempletContractDBList.add(wmTempletContractDB);
        }

        // 条件筛选 + 排序
        return wmTempletContractDBList.stream()
                .filter(e -> filterDcC1ContractByContractNum(req.getContractNum(), e))
                .sorted(Comparator.comparing(WmTempletContractDB::getCtime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 查询先富发起的到餐C1合同Page
     * @param req
     * @return
     */
    private List<WmTempletContractDB> getDcC1ContractListWithCondition(QueryWmCustomerContractReq req) {
        PageHelper.startPage(req.getPageNo(), req.getPageSize());
        int wmCustomerId = req.getCustomerId();
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.queryContractWithType((long) wmCustomerId,
                WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode(), req.getContractNum());

        PageHelper.clearPage();
        return wmTempletContractDBList;
    }

    /**
     * 构建到餐合同凭证与WmTempletContractDB的映射关系
     * @param wmCustomerId
     * @return
     */
    private Map<String, WmTempletContractDB> buildC1ContractProofMap(Integer wmCustomerId) {
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndType((long) wmCustomerId, WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return Maps.newHashMap();
        }

        HashMap<String, WmTempletContractDB> contractProof2WmTempletContractDB = new HashMap<>();
        for (WmTempletContractDB contractDB : wmTempletContractDBList) {
            try {
                String bizData = contractDB.getBizData();
                DaoCanContractInfo daoCanContractInfo = JSON.parseObject(bizData, DaoCanContractInfo.class);
                String contractProof = daoCanContractInfo.getContractProof();
                if (!StringUtils.isEmpty(contractProof)) {
                    contractProof2WmTempletContractDB.put(contractProof, contractDB);
                }
            } catch (Exception e) {
                logger.warn("getWmTempletContractByC1ContractProof error:{}, id:{}",e.getMessage(),contractDB.getId());
            }
        }

        return contractProof2WmTempletContractDB;
    }

    /**
     * 到餐C1合同过滤（仅支持合同编号）
     * @param contractNum
     * @param wmTempletContractDB
     * @return
     */
    private boolean filterDcC1ContractByContractNum(String contractNum, WmTempletContractDB wmTempletContractDB) {
        if (!StringUtils.isEmpty(contractNum)) {
            return wmTempletContractDB.getNumber().equals(contractNum);
        }

        return true;
    }

    /**
     * 转换到餐C1合同信息
     * @param tFrameCoop
     * @param wmCustomerId
     * @return
     */
    private WmTempletContractDB convertToWmTempletContractDB(TFrameCoop tFrameCoop, int wmCustomerId) {
        TFrame tFrame = tFrameCoop.getTFrame();

        WmTempletContractDB wmTempletContractDB = new WmTempletContractDB();
        wmTempletContractDB.setId((long) -1);   // 默认id为-1，如果是先富发起的需要进行覆盖
        wmTempletContractDB.setParentId((long) wmCustomerId);
        wmTempletContractDB.setType(WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode());
        wmTempletContractDB.setStatus(DcContractStatusMapUtil.mapDcContractStatusToWm(tFrame.getStatus()));
        wmTempletContractDB.setNumber(tFrame.getFrameNum()); // 框架合同编号
        wmTempletContractDB.setEffectiveDate(0);        // 生效日期
        wmTempletContractDB.setDueDate(0); // 到期日期
        wmTempletContractDB.setCtime(checkTimeStamp(tFrame.getAddTime()));    // 创建时间
        wmTempletContractDB.setUtime(checkTimeStamp(tFrame.getModTime())); // 修改时间
        wmTempletContractDB.setOpuid(tFrame.getAddUserId());
        wmTempletContractDB.setValid((byte) 1);
        wmTempletContractDB.setExpectEffectiveDate(0); // 预计过期时间

        // todo zyh
        DaoCanContractInfo daoCanContractInfo = new DaoCanContractInfo();
        daoCanContractInfo.setContractProof(tFrame.getFrameId());
        wmTempletContractDB.setBizData(JSON.toJSONString(daoCanContractInfo));

        return wmTempletContractDB;
    }

    /**
     * 检查时间戳转换成秒级时间戳
     * @param timeStamp
     * @return
     */
    private int checkTimeStamp(long timeStamp) {
        if (timeStamp > Integer.MAX_VALUE) {
            return (int) (timeStamp / 1000);
        }

        return (int) timeStamp;
    }

    /**
     * 填充到餐C1业务数据
     * @param wmTempletContractDB
     * @param tFrameCoop
     */
    private void fillDcC1BizData(WmTempletContractDB wmTempletContractDB, TFrameCoop tFrameCoop, Long dcPlatformId) {
        TFrame tFrame = tFrameCoop.getTFrame();
        DaoCanContractInfo daoCanContractInfo = new DaoCanContractInfo();
        daoCanContractInfo.setContractProof(tFrame.getFrameId());
        daoCanContractInfo.setMtCustomerId(dcPlatformId);
        wmTempletContractDB.setBizData(JSON.toJSONString(daoCanContractInfo));
    }

    public List<CustomerContractQueryResponseDTO> queryContractWithAgentId(Long mtCustomerId, Long wmCustomerId, Integer contractType, List<Integer> agentIdList) {
        logger.info("WmContractService#queryContractWithAgentId, wmCustomerId: {}, contractType: {}, agentIdList: {}",
                wmCustomerId, contractType, JSON.toJSONString(agentIdList));
        List<WmTempletContractDB> contractDBList = wmTempletContractDBMapper.selectByParentIdAndType(wmCustomerId, contractType);
        if (CollectionUtils.isEmpty(contractDBList)) {
            return Collections.emptyList();
        }
        List<CustomerContractQueryResponseDTO> contractList = new ArrayList<>();
        for (WmTempletContractDB contractDB : contractDBList) {
            Integer count = wmTempletContractSignDBMapper.countByTemplateIdAndAgentIds(contractDB.getId(), agentIdList);
            if (count == null || count == 0) {
                continue;
            }
            List<WmContractVersionDB> contractVersionDBList = wmContractVersionService.queryByWmContractIdAndType(contractDB.getId().intValue(), CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
            List<CustomerContractQueryResponseDTO> responseDTOList = WmTempletContractTransUtil.convertToResponseDTOList(mtCustomerId, contractDB, contractVersionDBList);
            contractList.addAll(responseDTOList);
        }
        return contractList;
    }


    /**
     * 电子合同回调处理
     */
    public Boolean econtractCallback(EcontractCallbackBo callbackBo) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            return wmContractWriteDomainService.econtractCallback(callbackBo);
        }
        logger.info("电子合同回调 callbackBo:{}", JSON.toJSONString(callbackBo));
        WmContractVersionDB versionDB = getVersionByTaskId(callbackBo.getTaskId().toString());
        if (versionDB == null) {
            logger.warn("WmContractVersionDB is null,taskId={}", callbackBo.getTaskId());
            return true;
        }
        if (EcontractTaskStateEnum.IN_PROCESSING.equals(callbackBo.getState()) && StringUtils.isNotBlank(callbackBo.getPdfUrl())) {
            wmContractVersionService.updatePdfUrl(versionDB.getId(), callbackBo.getPdfUrl(), "");
        } else if (EcontractTaskStateEnum.SUCCESS.equals(callbackBo.getState())) {
            signSuccess(callbackBo, versionDB);
        } else if (EcontractTaskStateEnum.FAIL.equals(callbackBo.getState()) || EcontractTaskStateEnum.CANCEL.equals(callbackBo.getState())) {
            signFail(callbackBo, versionDB);
        }
        return true;
    }

    public boolean dcContractCallback(EcontractCallbackBo callbackBo,  WmEcontractSignTaskDB taskInfo) throws WmCustomerException, TException {
        logger.info("WmContractService#dcContractCallback, callbackBo: {}, taskInfo: {}", JSON.toJSONString(callbackBo), JSON.toJSONString(taskInfo));
        String contractType = callbackBo.getApplyContractType();
        if (!EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getName().equals(contractType)) {
            logger.error("WmContractService#dcContractCallback, 任务类型异常");
            return false;
        }
        WmContractVersionDB versionDB = getVersionByTaskId(callbackBo.getTaskId().toString());
        if (versionDB == null) {
            logger.warn("WmContractService#dcContractCallback, WmContractVersionDB is null, taskId={}", callbackBo.getTaskId());
            return true;
        }
        if (EcontractTaskStateEnum.IN_PROCESSING.equals(callbackBo.getState()) && StringUtils.isNotBlank(callbackBo.getPdfUrl())) {
            wmContractVersionService.updatePdfUrl(versionDB.getId(), callbackBo.getPdfUrl(), "");
        } else if (EcontractTaskStateEnum.SUCCESS.equals(callbackBo.getState())) {
            signSuccess(callbackBo, versionDB);
        } else if (EcontractTaskStateEnum.FAIL.equals(callbackBo.getState()) || EcontractTaskStateEnum.CANCEL.equals(callbackBo.getState())) {
            signFail(callbackBo, versionDB);
        }
        return true;
    }

    private void signSuccess(EcontractCallbackBo callbackBo, WmContractVersionDB versionDB) throws WmCustomerException, TException {
        logger.info("电子合同签约通过， contractId:{}", versionDB.getWm_contract_id());
        if (StringUtils.isNotBlank(callbackBo.getPdfUrl())) {
            wmContractVersionService.updatePdfUrl(versionDB.getId(), callbackBo.getPdfUrl(), "");
        }
        Integer type = getTypeByTempletId(versionDB.getWm_contract_id());
        boolean isConfigContract = wmFrameContractConfigService.queryContractConfigInfo(type) != null;

        // 企客签约完成后通知配送，
        wmBusinessCustomerTempletService.notifySignResult(versionDB.getWm_contract_id(), type, true, "");

        // 判断当前合同的预计生效日期，当前时间已过预计生效日期则直接生效，没过则进入待生效状态
        WmTempletContractBasicBo wmTempletContractBasicBo = getBasicById(versionDB.getWm_contract_id(), false, 0,
                "电子合同平台回调");

        // 预计生效时间为0或者小于当前回调时间，立即生效
        if (TimeUtil.unixtime() >= wmTempletContractBasicBo.getExpectEffectiveDate()) {
            //立即生效
            getService(type, isConfigContract ? ContractSourceEnum.CONFIG.getCode() : ContractSourceEnum.CODE.getCode()).effect(versionDB.getWm_contract_id(), 0, "电子合同平台回调");
            // 给负责人发送大象通知
            noticeCusOwner(versionDB, type);
            // 企业订餐补充协议通知
            noticeGroupMeal(type, versionDB);
            // 配送产品临时调整补充协议通知
            noticeBanmaOpen(type, versionDB, 1);
        } else {
            // 待生效(只有C1,C2,广告，承诺函)
            if (type == WmTempletContractTypeEnum.C1_E.getCode() || type == WmTempletContractTypeEnum.C2_E.getCode()
                    || type == WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode()
                    || type == WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode()
                    || type == WmTempletContractTypeEnum.DELIVERY_SERVICE_CONTRACT_E.getCode()
                    || type == WmTempletContractTypeEnum.DELIVERY_SITE_CONTRACT_E.getCode()
                    || type == WmTempletContractTypeEnum.AD_ORDER_E.getCode()
                    || type == WmTempletContractTypeEnum.BRAND_AD_CONTRACT_E.getCode()
                    || type == WmTempletContractTypeEnum.AD_ANNUAL_FRAMEWORK_CONTRACT_E.getCode()
                    || type == WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode()
                    || type == WmTempletContractTypeEnum.GROUP_MEAL_E.getCode()
                    || type == WmTempletContractTypeEnum.BAG_SERVICE_E.getCode()
                    || type == WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode()
                    || type == WmTempletContractTypeEnum.INTERIM_SELF_E.getCode()
                    || type == WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode()
                    || type == WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode()
                    || type == WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
            ) {
                getService(type).toEffect(versionDB.getWm_contract_id(), 0, "电子合同平台回调");
            } else {
                if (isConfigContract) {
                    getService(type, ContractSourceEnum.CONFIG.getCode()).toEffect(versionDB.getWm_contract_id(), 0, "电子合同平台回调");
                } else {
                    logger.warn("暂不支持待生效状态回调 contract_id:{}", versionDB.getWm_contract_id());
                }
            }
        }
    }

    private void noticeCusOwner(WmContractVersionDB versionDB, Integer type) throws WmCustomerException, TException {
        if (WmTempletContractTypeEnum.DAO_CAN_AGREEMENT_SET.contains(type)) {
            return;
        }
        WmTempletContractBasicBo wmTempletContractBasicBo = getBasicById(versionDB.getWm_contract_id(), false, 0, "电子合同平台回调");
        WmCustomerDB customerDB = wmCustomerService.selectCustomerById(wmTempletContractBasicBo.getParentId());
        try {

            PublishNoticeDto publishNoticeDto = new PublishNoticeDto();

            publishNoticeDto.setUid(0);
            publishNoticeDto.setReceiveUids(String.valueOf(customerDB.getOwnerUid()));
            publishNoticeDto.setMsgFormat(WmNoticeMsgFormatEnum.MSG_FORMAT_PLAIN.getValue());
            String content = "";
            if (wmTempletContractBasicBo.getType() == WmTempletContractTypeEnum.MEDIC_ORDER_SPLIT_E.getCode()) {
                content = String.format("【合同签约成功】 客户%s（%s）的%s签约成功，合同编号：%s",
                        customerDB.getCustomerName(), customerDB.getId(),
                        WmTempletContractTypeEnum.MEDIC_ORDER_SPLIT_E.getMsg(),
                        wmTempletContractBasicBo.getContractNum());
            } else if (CONTRACT_TYPES_NEED_EFFECT_NOTICE.contains(wmTempletContractBasicBo.getType())) {
                content = String.format("客户%s（%s）的%s签约成功，合同编号：%s",
                        customerDB.getCustomerName(), customerDB.getId(),
                        WmTempletContractTypeEnum.getByCode(wmTempletContractBasicBo.getType()).getMsg(),
                        wmTempletContractBasicBo.getContractNum());
            } else if (ContractConstant.CONTRACT_MAP.get(wmTempletContractBasicBo.getType()) != null) {
                content = String.format("【合同签约成功】合同类型：%s, 合同编号：%s, 客户：%s（%s）",
                        ContractConstant.CONTRACT_MAP.get(wmTempletContractBasicBo.getType()),
                        wmTempletContractBasicBo.getContractNum(), customerDB.getCustomerName(),
                        customerDB.getId());
            } else {
                content = String.format("【合同签约成功】合同编号：%s, 客户：%s（%s）",
                        wmTempletContractBasicBo.getContractNum(), customerDB.getCustomerName(),
                        customerDB.getId());
            }
            String message = "[{" + "'title':'【合同签约成功】'," + "'content':'" + content + "'}]";
            publishNoticeDto.setMsg(message);
            publishNoticeDto.setWmNoticeTypeId(ConfigUtilAdapter.getInt("contract_notice_type", 0));
            publishNoticeDto.setMustRead(WmNoticeMustReadEnum.NO.getValue());
            String pushMedia = WmNoticePushMediaEnum.PUSH_MEDIA_DAXIANG.getValue() + "," + WmNoticePushMediaEnum
                    .PUSH_MEDIA_BEE_APP.getValue();
            publishNoticeDto.setPushMedia(ConfigUtilAdapter.getString("receive_terminal", pushMedia));

            Map<String, String> map = Maps.newHashMap();
            map.put(WmNoticeMainBodyTypeEnum.CUSTOMER_ID.getMainBodyType(), String.valueOf(customerDB.getId()));
            publishNoticeDto.setBusinessMainBody(map);

            logger.info("WmContractService#noticeCusOwner, publishNoticeDto: {}", JSON.toJSONString(publishNoticeDto));
            wmNoticePublishThriftService.publishNotice(publishNoticeDto);
            logger.info("通知发送成功，内容:{}，客户id:{}，合同id:{}", JSON.toJSONString(publishNoticeDto),
                    versionDB.getCustomer_id(), versionDB.getWm_contract_id());
        } catch (Exception e) {
            logger.warn("发送【合同签约成功】失败", e);
        }
    }

    private void noticeGroupMeal(int type, WmContractVersionDB versionDB) throws WmCustomerException, TException {
        if (type == WmTempletContractTypeEnum.GROUP_MEAL_E.getCode()) {
            try {
                WmTempletContractBasicBo wmTempletContractBasicBo = getBasicById(versionDB.getWm_contract_id(), false, 0, "电子合同平台回调");
                WmCustomerDB customerDB = wmCustomerService.selectCustomerById(wmTempletContractBasicBo.getParentId());
                String msgTitle = noticeMsgTitleMap.getOrDefault(wmTempletContractBasicBo.getType(), DEFAULT_NOTICE_MSG_TITLE);
                String content = String.format("【%s】合同编号：%s, 客户：%s（%s）", msgTitle,
                        wmTempletContractBasicBo.getContractNum(), customerDB.getCustomerName(), customerDB.getId());
                List<String> misIds;
                if (StringUtils.isEmpty(MccConfig.getGroupMealContractNotifyMisIds())) {
                    misIds = empServiceAdaptor.queryMisIdsByOrgId(MccConfig.getGroupMealContractNotifyOrgId());
                } else {
                    misIds = Arrays.asList(MccConfig.getGroupMealContractNotifyMisIds().split(","));
                }
                DaxiangUtilV2.push(content, misIds);
            } catch (Exception e) {
                logger.error("推送企业订餐补充协议消息失败，versionDB:{}", versionDB, e);
            }
        }
    }

    private void noticeBanmaOpen(int type, WmContractVersionDB versionDB, int status) throws
            WmCustomerException, TException {
        if (type == WmTempletContractTypeEnum.INTERIM_SELF_E.getCode()) {
            try {
                WmTempletContractBasicBo wmTempletContractBasicBo = getBasicById(versionDB.getWm_contract_id(),
                        false, 0, "电子合同平台回调");
                if (!StringUtils.isEmpty(wmTempletContractBasicBo.getBizDate())) {
                    Map<String, String> wmPoiIdBizIdMap = JSON.parseObject(wmTempletContractBasicBo.getBizDate(),
                            HashMap.class);
                    for (Map.Entry<String, String> entry : wmPoiIdBizIdMap.entrySet()) {
                        banmaOpenPoiClient.callbackContract(Long.parseLong(entry.getKey()),
                                Long.parseLong(entry.getValue()), status);
                    }
                }
                // 清空bizdata字段
                wmTempletContractDBMapper.updateBizDataById(wmTempletContractBasicBo.getTempletContractId(),
                        wmTempletContractBasicBo.getVersion(), "");
            } catch (Exception e) {
                logger.error("推送配送产品临时调整补充协议异常，versionDB:{}", versionDB, e);
            }
        }
    }

    private void signFail(EcontractCallbackBo callbackBo, WmContractVersionDB versionDB) throws
            WmCustomerException, TException {
        Integer type = getTypeByTempletId(versionDB.getWm_contract_id());
        ContractConfigInfo configInfo = wmFrameContractConfigService.queryContractConfigInfo(type);
        getService(type, configInfo == null ? ContractSourceEnum.CODE.getCode() : ContractSourceEnum.CONFIG.getCode())
                .signFail(versionDB.getWm_contract_id(), callbackBo.getFailMsg(), 0, "电子合同平台");
        // 配送产品临时调整补充协议通知
        noticeBanmaOpen(type, versionDB, 2);
    }

    private WmContractVersionDB getVersionByTaskId(String taskId) throws WmCustomerException {
        WmContractVersionDB byVersionNumberMaster = wmContractVersionService.getByTransactionIdMaster(taskId);
        return byVersionNumberMaster;
    }

    public Integer getTypeByTempletId(long templetId)
            throws WmCustomerException, TException {
        return wmTempletContractDBMapper.selectTypeByPrimaryKey(templetId);
    }

    public List<WmTempletContractBasicBo> selectAuditedContractBasicByParentIdAndTypes(Long parentId,
                                                                                       List<Integer> types) {
        List<WmTempletContractDB> basicKist = wmTempletContractAuditedDBMapper
                .getBasicListByParentIdAndTypes(parentId.intValue(), types);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(basicKist);
    }

    public List<WmTempletContractDB> getEffectContractByCustomerIdAndType(int customerId, List<Integer> typeList) {
        return wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, typeList);
    }

    public List<WmTempletContractDB> getContractByCustomerIdAndTypes(long customerId, List<Integer> typeList) {
        return wmTempletContractDBMapper.selectByParentIdAndTypes(customerId, typeList);
    }

    public List<WmTempletContractDB> getContractByCustomerIdAndType(long customerId, int type) {
        return wmTempletContractDBMapper.selectByParentIdAndType(customerId, type);
    }

    public void saveWmContractFtlTagDB(long templetContractId, int opUid, String opUname) {
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.getByIdMaster(templetContractId);
        if (wmTempletContractDB != null) {
            WmContractFtlTagDB wmContractPdfDB = wmContractFtlTagMapper.queryByContractIdAndType(wmTempletContractDB.getParentId().intValue(), ContractFtlTagConstant.C1_CANCEL_3000);
            if (wmContractPdfDB == null) {
                logger.info("不存在3000返还 插入3000返还记录  templetId:{}, opUid:{} opUname:{}", templetContractId, opUid, opUname);
                WmContractFtlTagDB wmContractFtlTagDB = new WmContractFtlTagDB();
                wmContractFtlTagDB.setContract_id(wmTempletContractDB.getParentId().intValue());
                wmContractFtlTagDB.setFtl_tag_type(ContractFtlTagConstant.C1_CANCEL_3000);
                wmContractFtlTagDB.setCtime(TimeUtil.unixtime());
                wmContractFtlTagDB.setUtime(TimeUtil.unixtime());
                wmContractFtlTagDB.setValid(ContractFtlTagConstant.VALID);
                wmContractFtlTagMapper.insert(wmContractFtlTagDB);
            } else if (null != wmContractPdfDB && ContractTagConstant.INVALID == wmContractPdfDB.getValid()) {
                wmContractPdfDB.setValid(ContractTagConstant.VALID);
                wmContractPdfDB.setUtime(TimeUtil.unixtime());
                wmContractFtlTagMapper.updateByContractIdAndType(wmContractPdfDB);
            }
        }
    }

    /**
     * 上线检查点
     * <p>
     * <p>
     * 一、直营门店	对应客户C1合同生效	  若不满足则提示：合同未生效，不可上线
     * <p>
     * 二、代理门店
     * <p>
     * 1、如下类型商家：对应客户C1合同生效：
     * 大连锁
     * 总部商超连锁
     * 总部生鲜连锁
     * 总部鲜花连锁
     * 总部药品连锁
     * 总部母婴连锁
     * 总部美妆连锁
     * 总部服饰鞋帽连锁
     * 总部日用品连锁
     * <p>
     * 2、非上述类型的商家：对应客户C1C2合同生效
     * <p>
     * 3、代理门店，所归属的合作商的C3合同生效
     * 若以上条件不满足则提示：合同未生效，不可上线
     * <p>
     * 三、C1合同有效期校验
     * 1、C1合同有效期到期后，若门店为下线状态，则不可再上线；
     * 2、若门店为上线状态，则继续保持上线状态，不会触发下线
     * 若满足条件1则提示：合同已到期，不可上线
     */
    public CustomerModuleStatus checkContractForPoiSetup(int customerId, long wmPoiId) throws WmCustomerException {
        logger.info("门店上线检查点【合同】  cusId:{}, wmPoiId:{}", customerId, wmPoiId);
        WmPoiDomain poiDomain = getWmPoiDomain(wmPoiId);
        logger.info("门店上线检查点【合同】  cusId:{}, poiDomain:{}", customerId, JSON.toJSON(poiDomain));
        if (poiDomain.getAgentId() <= 0) {
            //直营门店
            return getC1PoiModuleStatus(customerId, poiDomain);
        }
        //合作商门店
        return getAgentPoiStatus(customerId, poiDomain);
    }

    /**
     * https://km.sankuai.com/page/371459684
     */
    public CustomerModuleStatus checkContractForPoiSetupNew(int customerId, long wmPoiId) throws TException, WmCustomerException {
        logger.info("门店上线检查点【合同】  cusId:{}, wmPoiId:{}", customerId, wmPoiId);
        WmPoiDomain poiDomain = getWmPoiDomain(wmPoiId);
        logger.info("门店上线检查点【合同】  cusId:{}, poiDomain:{}", customerId, JSON.toJSON(poiDomain));
        //是否大连锁
        boolean lianSuoPoi = wmPoiClient.isLianSuoPoi(poiDomain);
        //直营or大连锁只需判断C1
        if (poiDomain.getAgentId() <= 0 || lianSuoPoi) {
            return getC1PoiModuleStatusNew(customerId, poiDomain);
        }
        //其他门店
        return getAgentPoiStatusNew(customerId, poiDomain);
    }

    private CustomerModuleStatus getAgentPoiStatus(int customerId, WmPoiDomain poiDomain) throws WmCustomerException {
        List<WmTempletContractDB> allContract = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode())
        );
        if (CollectionUtils.isEmpty(allContract)) {
            logger.info("合作商门店，对应客户不存在C1和C2合同，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
            return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同未生效,不可上线");
        }
        boolean hasC1 = false;
        boolean hasC2 = false;
        boolean hasC3 = hasC3(poiDomain);
        if (!hasC3) {
            logger.info(" hasC3：{}  customerId：{}  poiId:{}", hasC3, customerId, poiDomain.getWmPoiId());
            return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同未生效,不可上线");
        }
        for (WmTempletContractDB db : allContract) {
            int typeInDb = new WmTempletContractTypeBo(db.getType()).getType();
            if (typeInDb == WmTempletContractTypeBo.TYPE_C1) {
                Integer dueDate = db.getDueDate();
                if (DateUtil.unixTime() >= dueDate && poiDomain.getValid() == 0) {
                    logger.info("合作商门店，对应客户C1合同到期且门店已为下线状态，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
                    return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同已到期,不可上线");
                }
                hasC1 = true;
            }
            if (typeInDb == WmTempletContractTypeBo.TYPE_C2) {
                WmTempletContractSignBo partyBSigner = wmContractSignService
                        .getAuditedPartyBSigner(db.getId());
                if (partyBSigner.getSignId() == poiDomain.getAgentId()) {
                    hasC2 = true;
                }
            }
        }
        logger.info("hasC1：{}  hasC2：{}  hasC3：{}  customerId：{}  poiId:{}", hasC1, hasC2, hasC3, customerId, poiDomain.getWmPoiId());
        //是否是连锁门店
        boolean lianSuoPoi = wmPoiClient.isLianSuoPoi(poiDomain);
        logger.info("是否大连锁或品牌连锁门店：{} customerId：{}  poiId:{}", lianSuoPoi, customerId, poiDomain.getWmPoiId());
        if (lianSuoPoi && hasC1 || !lianSuoPoi && hasC1 && hasC2) {
            return new CustomerModuleStatus(StatusEnum.EFFECTIVE, "");
        }
        return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同未生效,不可上线");
    }

    private CustomerModuleStatus getAgentPoiStatusNew(int customerId, WmPoiDomain poiDomain) throws TException, WmCustomerException {
        StringBuffer sb = new StringBuffer();
        //海南门店客户校验主体补充协议
        if (wmSubjectChangeSupplementEContractTempletServicel.ziruzhuHnPoiAutoPackGray(new Long(poiDomain.getWmPoiId())) &&
                wmSubjectChangeSupplementEContractTempletServicel.subjectChangeSupplementOnlineCheck(customerId, poiDomain)) {
            sb.append("客户需签署《主体变更补充协议》后才能上线");
        }

        //美食城客户校验美食城承诺书
        WmCustomerDB wmCustomerDB = wmCustomerService.selectPlatformCustomerByIdFromSlave(customerId);

        //美食城客户校验美食城承诺书
        if (wmCustomerDB != null && ConfigUtilAdapter.getBoolean("msc_poiopen_check", false)) {
            if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() ) {
                List<WmTempletContractDB> foodcityStatement =
                        wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId,
                                Lists.newArrayList(WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode()));
                if (CollectionUtils.isEmpty(foodcityStatement) && !(isSubPoi(poiDomain.getWmPoiId()) || (isShareQualificationCustomer(customerId) || isShareQualificationPoi(poiDomain.getWmPoiId())))) {
                    sb.append("未签约《美食城承诺书》");
                }
            }
        }
        List<WmTempletContractDB> allContract =
                wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(
                        WmTempletContractTypeEnum.C1_E.getCode(),
                        WmTempletContractTypeEnum.C1_PAPER.getCode(),
                        WmTempletContractTypeEnum.C2_E.getCode(),
                        WmTempletContractTypeEnum.C2_PAPER.getCode())
                );
        Map<Integer, List<WmTempletContractDB>> contractMap = assemblyContractMap(allContract);

        //获取C1合同信息
        List<WmTempletContractDB> c1Contract = contractMap.get(WmTempletContractTypeBo.TYPE_C1);
        if (CollectionUtils.isEmpty(c1Contract)) {//无生效数据
            sb.append("美团与客户合同未生效");
        } else {//有生效数据但是已过期
            Integer dueDate = c1Contract.get(0).getDueDate();
            if (DateUtil.unixTime() >= dueDate && !isSubPoi(poiDomain.getWmPoiId())) {
                sb.append("美团与客户合同已过期");
            }
        }

        //获取C2合同信息
        List<WmTempletContractDB> c2Contract = contractMap.get(WmTempletContractTypeBo.TYPE_C2);
        List<WmTempletContractDB> c2ContractForAgent = filterC2Contract(c2Contract, poiDomain.getAgentId());
        if (CollectionUtils.isEmpty(c2ContractForAgent)) {//无生效数据
            if (sb.length() > 0) {
                sb.append(",").append("合作商与客户合同未生效");
            } else {
                sb.append("合作商与客户合同未生效");
            }
        }

        //判断C3
        boolean hasC3 = hasC3(poiDomain);
        if (!hasC3) {
            if (sb.length() > 0) {
                sb.append(",").append("美团与合作商合同未生效");
            } else {
                sb.append("美团与合作商合同未生效");
            }
        }

        //返回
        return sb.length() > 0 ? new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, sb.toString()) : new CustomerModuleStatus(StatusEnum.EFFECTIVE, "");
    }

    private boolean hasC3(WmPoiDomain poiDomain) throws WmCustomerException {
        try {
            if (ConfigUtilAdapter.getBoolean("getWmAgentContractByAgentId_replace", false)) {
                //接口迁移，迁移无异常getByWmAgentId_migrate使用默认值，false为降级值
                if (ConfigUtilAdapter.getBoolean("getByWmAgentId_migrate", true) && MccConfig.agentTransferProp()) {
                    AgentIdRequest agentIdRequest = new AgentIdRequest();
                    agentIdRequest.setWmAgentId((int) poiDomain.getAgentId());
                    agentIdRequest.setEffective(1);
                    ContractContextResponse response = contractThrift.getByWmAgentId(agentIdRequest);
                    logger.info("WmContractService getByWmAgentId resp:{}", JSON.toJSONString(response));
                    return response != null;
                } else {
                    WmAgentFwContractQueryByAgentIdRequestDTO requestDTO = new WmAgentFwContractQueryByAgentIdRequestDTO();
                    requestDTO.setWmAgentId((int) poiDomain.getAgentId());
                    requestDTO.setEffective(1);
                    WmAgentFwContractQueryResponseDTO responseDTO = wmAgentFwContractThriftService.getByWmAgentId(requestDTO);
                    return responseDTO != null;
                }
            } else {
                WmAgentContract wmAgentContract = wmAgentContractThriftService
                        .getWmAgentContractByAgentId(poiDomain.getAgentId());
                return wmAgentContract != null && wmAgentContract.getValid() == 1;
            }
        } catch (WmServerException e) {
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (TException e) {
            logger.error(e.getMessage(), e);
        } catch (WmAgentFwException e) {
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (ContractException e) {
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        }
        return false;
    }

    private CustomerModuleStatus getC1PoiModuleStatus(int customerId, WmPoiDomain poiDomain) {
        List<WmTempletContractDB> c1Contract = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode()
                )
        );
        if (!CollectionUtils.isEmpty(c1Contract)) {
            Integer dueDate = c1Contract.get(0).getDueDate();
            if (DateUtil.unixTime() >= dueDate && poiDomain.getValid() == 0) {
                logger.info("直营门店，对应客户C1合同到期且门店已为下线状态，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
                return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同已到期,不可上线");
            }
            return new CustomerModuleStatus(StatusEnum.EFFECTIVE, "");
        }
        logger.info("直营门店，对应客户不存在C1合同，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
        return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同未生效,不可上线");
    }

    private CustomerModuleStatus getC1PoiModuleStatusNew(int customerId, WmPoiDomain poiDomain) throws TException, WmCustomerException {
        StringBuilder inEffectiveReason = new StringBuilder();
        //海南门店客户校验主体补充协议
        if (ConfigUtilAdapter.getBoolean("subjectchangesupplement_onlinecheck_switch", false)) {
            if (wmSubjectChangeSupplementEContractTempletServicel.ziruzhuHnPoiAutoPackGray(new Long(poiDomain.getWmPoiId())) &&
                    wmSubjectChangeSupplementEContractTempletServicel.subjectChangeSupplementOnlineCheck(customerId, poiDomain)) {
                inEffectiveReason.append("客户需签署《主体变更补充协议》后才能上线");
            }
        }

        //美食城客户校验美食城承诺书
        WmCustomerDB wmCustomerDB = wmCustomerService.selectPlatformCustomerByIdFromSlave(customerId);

        if (wmCustomerDB != null && ConfigUtilAdapter.getBoolean("msc_poiopen_check", false)) {
            if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
                List<WmTempletContractDB> foodcityStatement = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId,
                        Lists.newArrayList(WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode()));
                if (CollectionUtils.isEmpty(foodcityStatement) && !(isSubPoi(poiDomain.getWmPoiId()) || (isShareQualificationCustomer(customerId) || isShareQualificationPoi(poiDomain.getWmPoiId())))) {
                    if (inEffectiveReason.length() > 0) {
                        inEffectiveReason.append(",").append("未签约《美食城承诺书》");
                    } else {
                        inEffectiveReason.append("未签约《美食城承诺书》");
                    }
                }
            }
        }

        List<String> labelIdList = SPLITTER.splitToList(poiDomain.getLabelIds());
        boolean hasDistributorPoiLabel = labelIdList.contains(String.valueOf(MccConfig.getNationalSubsidyDistributorPoiLabelId()));
        if (hasDistributorPoiLabel) {
            String purchaseContractCheckResult = checkNationSubsidyPurchaseContract(customerId);
            if (!Strings.isNullOrEmpty(purchaseContractCheckResult)) {
                if (inEffectiveReason.length() > 0) {
                    inEffectiveReason.append(",").append(purchaseContractCheckResult);
                } else {
                    inEffectiveReason.append(purchaseContractCheckResult);
                }
            }
        } else {
            List<WmTempletContractDB> c1Contract = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(
                            WmTempletContractTypeEnum.C1_E.getCode(),
                            WmTempletContractTypeEnum.C1_PAPER.getCode()
                    )
            );
            //c1不为空，判断有效期；
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(c1Contract)) {
                Integer dueDate = c1Contract.get(0).getDueDate();
                if (DateUtil.unixTime() >= dueDate && !isSubPoi(poiDomain.getWmPoiId())) {
                    logger.info("直营门店，对应客户C1合同到期且门店已为下线状态，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
                    if (inEffectiveReason.length() > 0) {
                        inEffectiveReason.append(",").append("美团与客户合同已过期");
                    } else {
                        inEffectiveReason.append("美团与客户合同已过期");
                    }
                }
            } else {
                logger.info("直营门店，对应客户不存在C1合同，不可上线 customerId：{}, poiId:{}", customerId, poiDomain.getWmPoiId());
                if (inEffectiveReason.length() > 0) {
                    inEffectiveReason.append(",").append("美团与客户合同未生效");
                } else {
                    inEffectiveReason.append("美团与客户合同未生效");
                }
            }
        }
        return inEffectiveReason.length() > 0 ? new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, inEffectiveReason.toString()) : new CustomerModuleStatus(StatusEnum.EFFECTIVE, "");
    }
    
    private String checkNationSubsidyPurchaseContract(int customerId) {
        StringBuilder inEffectiveReason = new StringBuilder();
        // 经销商合作模式：只校验采销合作协议
        List<WmTempletContractDB> purchaseAgreements = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId,
                Lists.newArrayList(WmTempletContractTypeEnum.NATIONAL_SUBSIDY_PURCHASE_AGREEMENT.getCode()));

        if (CollectionUtils.isEmpty(purchaseAgreements)) {
            inEffectiveReason.append("《采销合作协议》缺失，需签约后重新发起");
        } else {
            // 检查采销合作协议是否到期
            boolean hasValidAgreement = purchaseAgreements.stream()
                    .anyMatch(agreement -> DateUtil.unixTime() < agreement.getDueDate());
            if (!hasValidAgreement) {
                inEffectiveReason.append("《采销合作协议》已过期，需签约后重新发起");
            }
        }
        return inEffectiveReason.toString();
    }

    private WmPoiDomain getWmPoiDomain(long wmPoiId) throws WmCustomerException {
        WmPoiDomain poiDomain = wmPoiClient.getWmPoiById(wmPoiId);
        if (poiDomain == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询门店信息为空");
        }
        return poiDomain;
    }

    @Transactional
    public Boolean invalidContract(int customerId, List<Long> poiIds, int opUid, String opName)
            throws WmCustomerException, TException {
        List<WmTempletContractDB> wmTempletContractDBList = getContractByCustomerIdAndTypes((long) customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode(),
                WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(),
                WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(),
                WmTempletContractTypeEnum.DELIVERY_SERVICE_CONTRACT_E.getCode(),
                WmTempletContractTypeEnum.DELIVERY_SITE_CONTRACT_E.getCode(),
                WmTempletContractTypeEnum.AD_ANNUAL_FRAMEWORK_CONTRACT_E.getCode(),
                WmTempletContractTypeEnum.BRAND_AD_CONTRACT_E.getCode(),
                WmTempletContractTypeEnum.AD_ORDER_E.getCode(),
                WmTempletContractTypeEnum.PHF_CHARGE_E.getCode(),
                WmTempletContractTypeEnum.GROUP_MEAL_E.getCode(),
                WmTempletContractTypeEnum.BAG_SERVICE_E.getCode(),
                WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode(),
                WmTempletContractTypeEnum.MEDIC_ORDER_SPLIT_E.getCode(),
                WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode(),
                WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
                WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
        ));
        boolean hasNotifyPoiProduce = false;
        for (WmTempletContractDB db : wmTempletContractDBList) {
            wmTempletContractDBMapper.invalidContract(db.getId(), opUid);
            wmTempletContractSignDBMapper.invalid(db.getId(), opUid);
            contractLogService.logDelete(customerId, db.getId().intValue(), opUid, opName, "客户删除，导致合同删除");
            if (new WmTempletContractTypeBo(db.getType()).getType() == WmTempletContractTypeBo.TYPE_C1
                    && !hasNotifyPoiProduce && db.getStatus() != CustomerContractStatus.STAGE.getCode()) {
                hasNotifyPoiProduce = true;
                wmContractPoiProduceService.logPoiProduceForContractDEL(customerId, poiIds, opUid, opName);
            }
        }
        wmTempletContractDBList = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode(),
                WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(),
                WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(),
                WmTempletContractTypeEnum.DELIVERY_SERVICE_CONTRACT_E.getCode(),
                WmTempletContractTypeEnum.DELIVERY_SITE_CONTRACT_E.getCode(),
                WmTempletContractTypeEnum.AD_ANNUAL_FRAMEWORK_CONTRACT_E.getCode(),
                WmTempletContractTypeEnum.BRAND_AD_CONTRACT_E.getCode(),
                WmTempletContractTypeEnum.AD_ORDER_E.getCode(),
                WmTempletContractTypeEnum.PHF_CHARGE_E.getCode(),
                WmTempletContractTypeEnum.GROUP_MEAL_E.getCode(),
                WmTempletContractTypeEnum.BAG_SERVICE_E.getCode(),
                WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode(),
                WmTempletContractTypeEnum.MEDIC_ORDER_SPLIT_E.getCode(),
                WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode(),
                WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
                WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
        ));
        for (WmTempletContractDB db : wmTempletContractDBList) {
            wmTempletContractAuditedDBMapper.invalidContract(db.getId(), opUid);
            wmTempletContractSignAuditedDBMapper.invalid(db.getId(), opUid);
        }
        invalidQuaRealLetterWhiteList(customerId, opUid);
        return true;
    }

    private void invalidQuaRealLetterWhiteList(long customerId, int opUid) throws WmCustomerException {
        CustomerBlackWhiteParam param = new CustomerBlackWhiteParam();
        param.setBizId(customerId).setBizType(CustomerConstants.WHITE_LIST_BIZTYPE_QUA_REAL_LETTER).setType(CustomerConstants.TYPE_WHITE).setOpUid(opUid);
        wmCustomerBlackWhiteListService.deleteCustomerBlackWhiteList(param);
    }

    public Integer createWmCustomerContract(CreateWmCustomerContractReq req)throws WmCustomerException, TException {
        logger.info("createWmCustomerContract req:{}", JSON.toJSONString(req));
        Integer result = getService(req.getType()).createWmCustomerContract(req);
        logger.info("createWmCustomerContract result:{}", result);
        return result;
    }

    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("WmContractService#startSign, contractBo: {}", JSON.toJSONString(contractBo));
        if (CollectionUtils.isEmpty(contractBo.getRequestDTOList())) {
            return saveAndStartSign(contractBo, opUid, opName);
        } else {
            return wmPartnerContractService.saveAndStartSign(contractBo, opUid, opName);
        }
    }

    public Integer saveAndStartSign(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("WmContractService#saveAndStartSign, contractBo:{}", JSON.toJSONString(contractBo));
        if (ContractSourceEnum.isConfigSource(contractBo.getBasicBo().getContractSource())) {
            fillConfigContractContext(contractBo);
        }
        return getService(contractBo.getBasicBo().getType(), contractBo.getBasicBo().getContractSource()).startSign(contractBo, opUid, opName);
    }

    private void fillConfigContractContext(WmCustomerContractBo contractBo) throws WmCustomerException {
        ContractConfigInfo configInfo = wmFrameContractConfigService.queryContractConfigInfo(contractBo.getBasicBo().getType());
        if (configInfo == null) {
            throw new WmCustomerException(-1, "查询配置化信息异常");
        }
        contractBo.getBasicBo().setConfigContractInfo(configInfo);
        logger.info("WmContractService#fillConfigContractContext contractBo:{}", JSON.toJSONString(contractBo));
    }

    public Long saveAndStartSignForManualPack(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("WmContractService#saveAndStartSignForManualPack contractBo:{}", JSON.toJSONString(contractBo));
        return getService(contractBo.getBasicBo().getType()).startSignForManualPack(contractBo, opUid, opName);
    }

    public Integer updateAndSignContract(WmCustomerContractBatchBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("WmContractService#updateAndSignContract, contractBo: {}", JSON.toJSONString(contractBo));

        //基础参数校验
        checkParam(contractBo);

        //判断纸质合同
        if (checkPaperContract(contractBo.getCustomerId(), opUid, opName)) {
            logger.info("该客户下有未废除的纸质合同，不能修改合同");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该客户下有未废除的纸质合同，不能修改合同");
        }

        //判断电子合同状态
        WmCustomerContractBo c1Contract = getC1ContractUpdate(contractBo.getCustomerId(), opUid, opName);
        if (c1Contract == null) {
            logger.info("该客户下未查询到需要修改的合同，customer_id = {}", contractBo.getCustomerId());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前无电子合同，不支持批量修改信息");
        }

        if (c1Contract.getBasicBo().getStatus() == CustomerContractStatus.SIGNING.getCode() ||
                c1Contract.getBasicBo().getStatus() == CustomerContractStatus.WAITING_SIGN.getCode()) {
            logger.info("该客户下C1电子合同处于签约中或者待发起签约状态，不能修改合同");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同处于待发起签约或者签约状态，如需修改请先在客户页面取消签约");
        }

        //合同基本信息
        WmTempletContractBasicBo basicBo = c1Contract.getBasicBo();
        String dueDateStr = TimeUtil.format(TimeUtil.DAY_FORMAT, (int) contractBo.getDueDate());
        basicBo.setDueDate(DateUtil.date2Unixtime(DateUtil.string2DateSecond24(dueDateStr + " 23:59:59")));
        basicBo.setExpectEffectiveDate(contractBo.getExpectEffectiveDate());

        //签约人信息
        List<WmTempletContractSignBo> signBoList = c1Contract.getSignBoList();
        for (WmTempletContractSignBo signBo : signBoList) {
            //乙方
            if ("B".equals(signBo.getSignType())) {
                signBo.setSignPeople(contractBo.getSignPeople());
                signBo.setSignPhone(contractBo.getSignPhone());
            }
        }

        // 填充签约人和履约主体
        WmPoiSignSubjectBo subjectBo = getSignSubjectBo(contractBo.getCustomerId(), WmTempletContractTypeEnum.C1_E.getCode());
        if (subjectBo == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "获取当前客户签约主体为空");
        }

        basicBo.setLogisticsSubject(subjectBo.getPartlogisticsName());
        for (WmTempletContractSignBo signBo : signBoList) {
            if ("B".equals(signBo.getSignType())) {
                signBo.setSignName(subjectBo.getPartBName());
            }
        }

        //打包方式
        c1Contract.setPackWay(contractBo.getPackWay());
        c1Contract.setOpSource(contractBo.getOpSourceEnum());
        logger.info("批量平台发起签约：c1Contract={}", JSON.toJSONString(c1Contract));
        return getService(c1Contract.getBasicBo().getType())
                .startSign(c1Contract, opUid, opName);
    }

    /**
     * check是否有纸质合同
     *
     * @param customerId
     * @param opUid
     * @param opName
     * @return
     */
    private Boolean checkPaperContract(Integer customerId, Integer opUid, String opName) {
        List<WmCustomerContractBo> customerContractBos = null;
        try {
            customerContractBos = getCusContractBoListByCusIdAndType(customerId, Lists.newArrayList(WmTempletContractTypeEnum.C1_PAPER.getCode()), opUid, opName);
        } catch (Exception e) {
            logger.error("根据客户id查询C1纸质合同异常，customer_id = {]", customerId);
        }
        return !CollectionUtils.isEmpty(customerContractBos);
    }

    /**
     * 查询c1电子合同
     *
     * @param customerId
     * @param opUid
     * @param opName
     * @return
     */
    public WmCustomerContractBo getC1ContractUpdate(Integer customerId, Integer opUid, String opName) {
        List<WmCustomerContractBo> customerContracts = null;
        try {
            customerContracts = getCusContractBoListByCusIdAndType(customerId, Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode()), opUid, opName);
        } catch (Exception e) {
            logger.error("根据客户id查询C1电子合同异常，customer_id = {]", customerId);
        }
        if (!CollectionUtils.isEmpty(customerContracts)) {
            return customerContracts.get(0);
        }
        return null;
    }

    /**
     * 查询生效的C1合同有效期
     * @param customerId
     * @param opUid
     * @param opName
     * @return
     */
    public WmCustomerContractBo getEffectC1ContractUpdate(Integer customerId, Integer opUid, String opName) {
        List<WmCustomerContractBo> customerContracts = null;
        try {
            customerContracts = getCusEffectContractBoListByCusIdAndType(customerId, Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode()), opUid, opName);
        } catch (Exception e) {
            logger.error("根据客户id查询生效的C1电子合同异常，customer_id = {]", customerId);
        }
        if (!CollectionUtils.isEmpty(customerContracts)) {
            return customerContracts.get(0);
        }
        return null;
    }

    public Integer startSignOnly(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("startSignOnly contractBo:{}", JSON.toJSONString(contractBo));
        return getService(contractBo.getBasicBo().getType())
                .startSignForContractHeron(contractBo, opUid, opName);
    }

    public Integer saveAndCommitAudit(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("WmContractService#commitAudit contractBo:{}", JSON.toJSONString(contractBo));
        return getService(contractBo.getBasicBo().getType())
                .commitAudit(contractBo, opUid, opName);
    }

    public Integer commitAuditOnly(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("commitAuditOnly contractBo:{}", JSON.toJSONString(contractBo));
        return getService(contractBo.getBasicBo().getType())
                .commitAuditForContractHeron(contractBo, opUid, opName);
    }

    public Integer update(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("update contractBo type:{}", contractBo.getBasicBo().getType());
        return getService(contractBo.getBasicBo().getType())
                .update(contractBo, opUid, opName);
    }

    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("save contractBo type:{}", contractBo.getBasicBo().getType());
        return getService(contractBo.getBasicBo().getType()).save(contractBo, opUid, opName);
    }

    public Boolean approve(long templetContractId, int opUid, String opUname)
            throws WmCustomerException, TException {
        logger.info("合同审核通过， contractId:{}, opUid:{} opUname:{}", templetContractId, opUid, opUname);
        Integer type = getTypeByTempletId(templetContractId);
        return getService(type)
                .effect(templetContractId, opUid, opUname);
    }

    public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname)
            throws WmCustomerException, TException {
        Integer type = getTypeByTempletId(templetContractId);
        return getService(type)
                .reject(templetContractId, rejectReason, opUid, opUname);
    }

    public LongResult applyManualTaskForBatchPlatform(EcontractBatchOpRequest opRequest, int contractType)
            throws WmCustomerException, TException {
        return getService(contractType).applyManualTaskForBatchPlatform(opRequest);
    }

    public List<WmTempletContractBasicBo> getContractBoListByCusIdAndType(int customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper
                .selectByParentIdAndTypes((long) customerId, types);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    public WmTempletContractBasicBo selectByParentIdAndTypeFromMaster(Long customerId, Integer type) {
        logger.info("selectByParentIdAndTypeFromMaster customerId={},type={}", customerId, type);
        WmTempletContractDB wmTempletContractDB = wmTempletContractAuditedDBMapper.selectByParentIdAndTypeFromMaster(customerId, type);
        return WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB);
    }

    public List<WmTempletContractBasicBo> selectByParentIdAndTypes(Long customerId, List<Integer> type) {
        logger.info("selectByParentIdAndTypes customerId={},type={}", customerId, type);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByParentIdAndTypes
                (customerId, type);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    /**
     * 尝试是否可发起C1签约
     *
     * @param customerId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void startC1SignPreCheck(Integer customerId, int opUid, String opUname) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            contractReadDomainService.startC1SignPreCheck(customerId, opUid, opUname);
            return;
        }
        WmCustomerContractBo wmCustomerContractBo = buildC1ContractBoForSign(customerId);
        if (wmCustomerContractBo == null) {
            return;
        }
        ContractCheckFilter.contractUpdateValidFilter(WmTempletContractTypeEnum.C1_E).filter(wmCustomerContractBo, opUid, opUname);

        if (wmCustomerContractBo.getBasicBo().getStatus() == CustomerContractStatus.SIGNING.getCode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态为"
                    + CustomerContractStatus.getByCode(wmCustomerContractBo.getBasicBo().getStatus()).getDesc() + "，不可修改。");
        }
    }

    /**
     * 尝试是否可发起签约
     *
     * @param customerId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void startSignPreCheck(Integer customerId, Long contractId, int opUid, String opUname) throws WmCustomerException, TException {
        WmCustomerContractBo wmCustomerContractBo = buildContractBoForSign(customerId, contractId);
        if (wmCustomerContractBo == null) {
            return;
        }
        WmTempletContractTypeEnum typeEnum = WmTempletContractTypeEnum.getByCode(wmCustomerContractBo.getBasicBo().getType());
        ContractCheckFilter.contractUpdateValidFilter(typeEnum).filter(wmCustomerContractBo, opUid, opUname);
        if (wmCustomerContractBo.getBasicBo().getStatus() == CustomerContractStatus.SIGNING.getCode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态为"
                    + CustomerContractStatus.getByCode(wmCustomerContractBo.getBasicBo().getStatus()).getDesc() + "，不可修改。");
        }
    }

    /**
     * 校验待打包签约是否可以发起签约
     */
    public void startSignPreCheck(WmManualSignTaskContext manualSignTaskContext) throws WmCustomerException, TException {
        logger.info("WmContractService#startSignPreCheck, manualSignTaskContext: {}", JSON.toJSONString(manualSignTaskContext));
        WmCustomerContractBo wmCustomerContractBo = buildContractBoForSign(manualSignTaskContext.getCustomerId(), manualSignTaskContext.getContractId());
        if (wmCustomerContractBo == null) {
            return;
        }
        WmTempletContractTypeEnum typeEnum = WmTempletContractTypeEnum.getByCode(wmCustomerContractBo.getBasicBo().getType());
        ContractCheckFilter.contractUpdateValidFilter(typeEnum, manualSignTaskContext.getContractSource()).filter(wmCustomerContractBo, manualSignTaskContext.getOpUid(), manualSignTaskContext.getOpUname());
        if (wmCustomerContractBo.getBasicBo().getStatus() == CustomerContractStatus.SIGNING.getCode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态为"
                    + CustomerContractStatus.getByCode(wmCustomerContractBo.getBasicBo().getStatus()).getDesc() + "，不可修改。");
        }
    }

    /**
     * 直接发起签约（对于合同处于待发起签约状态）
     *
     * @param customerId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void startC1SignByWaitingSign(Integer customerId, int opUid, String opUname, long manualBatchId) throws WmCustomerException, TException {
        if (DDDGrayUtil.useNewService()) {
            wmContractWriteDomainService.startC1SignByWaitingSign(customerId, opUid, opUname, manualBatchId);
            return;
        }
        logger.info("C1合同处于待发起签约，直接发起签约 customerId:{} opUid:{} opUname:{} manualBatchId:{}", customerId, opUid, opUname, manualBatchId);
        WmCustomerContractBo wmCustomerContractBo = buildC1ContractBoForSign(customerId);
        if (wmCustomerContractBo == null) {
            return;
        }
        wmCustomerContractBo.setManualBatchId(manualBatchId);
        saveAndStartSign(wmCustomerContractBo, opUid, opUname);
    }

    /**
     * 直接发起签约（对于合同处于待发起签约状态）
     *
     * @param customerId
     * @param contractId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void startSignByWaitingSign(Integer customerId, Long contractId, int opUid, String opUname, long manualBatchId) throws WmCustomerException, TException {
        logger.info("合同处于待发起签约直接发起签约 customerId:{} contractId:{} opUid:{} opUname:{} manualBatchId:{}",
                customerId, contractId, opUid, opUname, manualBatchId);
        WmCustomerContractBo wmCustomerContractBo = buildContractBoForSign(customerId, contractId);
        if (wmCustomerContractBo == null) {
            return;
        }
        wmCustomerContractBo.setManualBatchId(manualBatchId);
        saveAndStartSign(wmCustomerContractBo, opUid, opUname);
    }

    public void startSignByWaitingSign(WmManualSignTaskContext manualSignTaskContext) throws WmCustomerException, TException {
        logger.info("WmContractService#startSignByWaitingSign, 待发起签约直接发起签约, manualSignTaskContext: {}", JSON.toJSONString(manualSignTaskContext));
        WmCustomerContractBo wmCustomerContractBo = buildContractBoForSign(manualSignTaskContext.getCustomerId(), manualSignTaskContext.getContractId());
        if (wmCustomerContractBo == null) {
            return;
        }
        wmCustomerContractBo.setManualBatchId(manualSignTaskContext.getManualBatchId());
        wmCustomerContractBo.getBasicBo().setContractSource(manualSignTaskContext.getContractSource());
        saveAndStartSign(wmCustomerContractBo, manualSignTaskContext.getOpUid(), manualSignTaskContext.getOpUname());
    }

    /**
     * 发起到餐合同签约
     *
     * @param manualSignTaskContext 入参
     * @throws WmCustomerException 异常
     * @throws TException          异常
     */
    public void startDcSignByWaitingSign(WmManualSignTaskContext manualSignTaskContext) throws WmCustomerException, TException {
        logger.info("WmContractService#startDcSignByWaitingSign, 待发起签约直接发起签约, manualSignTaskContext: {}", JSON.toJSONString(manualSignTaskContext));
        WmCustomerContractBo wmCustomerContractBo = buildContractBoForSign(manualSignTaskContext.getCustomerId(), manualSignTaskContext.getContractId());
        if (wmCustomerContractBo == null) {
            return;
        }
        wmCustomerContractBo.setManualBatchId(manualSignTaskContext.getManualBatchId());
        wmCustomerContractBo.getBasicBo().setContractSource(manualSignTaskContext.getContractSource());
        wmCustomerContractBo.getBasicBo().setDaoCanContractInfo(buildDaoCanContractInfo(manualSignTaskContext));
        saveAndStartSign(wmCustomerContractBo, manualSignTaskContext.getOpUid(), manualSignTaskContext.getOpUname());
    }

    private DaoCanContractInfo buildDaoCanContractInfo(WmManualSignTaskContext signTaskContext) throws WmCustomerException {
        WmEcontractSignManualTaskDB manualTask = wmEcontractManualTaskBizService.getManualTaskByCustomerIdAndModuleAndBizId(signTaskContext.getCustomerId(),
                signTaskContext.getContractId(), signTaskContext.getModule(), signTaskContext.getManualBatchId());
        ManualTaskSettleContextBo contextBo = JSON.parseObject(manualTask.getApplyContext(), ManualTaskSettleContextBo.class);
        DaoCanContractInfo dcContractInfo = new DaoCanContractInfo();
        if (contextBo.getDaoCanContractContext() != null) {
            BeanUtils.copyProperties(contextBo.getDaoCanContractContext(), dcContractInfo);
        }
        dcContractInfo.setContractType(convertToTempletContractType(signTaskContext.getModule()).getCode());
        return dcContractInfo;
    }

    private WmTempletContractTypeEnum convertToTempletContractType(String module) throws WmCustomerException {
        EcontractTaskApplyTypeEnum applyTypeEnum = EcontractTaskApplyTypeEnum.getByName(module);
        if (EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT == applyTypeEnum) {
            return WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT;
        }
        if (EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT == applyTypeEnum) {
            return WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT;
        }
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不认识的任务类型: " + module);
    }

    /**
     * 取消待发起签约的C1合同（对于1合同处于待发起签约状态）
     *
     * @param customerId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void cancelC1SignByWaitingSign(Integer customerId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("C1合同处于待发起签约，取消待发起签约  customerId:{}  failReason:{} opUid:{} opUname:{}", customerId, failReason, opUid, opUname);
        List<WmTempletContractDB> contractDBList = getContractByCustomerIdAndType(customerId, WmTempletContractTypeEnum.C1_E.getCode());
        if (CollectionUtils.isEmpty(contractDBList)) {
            logger.warn("C1合同不存在，或者被删除  customerId:{}", customerId);
            return;
        }
        wmEcontractSignBzService.cancelManualTask(customerId, EcontractTaskApplyTypeEnum.C1CONTRACT);
        Long contractId = contractDBList.get(0).getId();
        Integer type = getTypeByTempletId(contractId);
        getService(type).signFail(contractId, failReason, opUid, opUname);
    }

    /**
     * 取消待发起签约（对于合同处于待发起签约状态）
     *
     * @param customerId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void cancelSignByWaitingSign(Integer customerId, Long contractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("取消待发起签约 customerId:{} contractId:{} failReason:{} opUid:{} opUname:{}", customerId, contractId, failReason, opUid, opUname);
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        // 未查询到合同
        if (wmTempletContractDB == null) {
            logger.info("合同不存在或被删除 customerId:{} contractId:{}", customerId, contractId);
            return;
        }
        // 客户ID和入参客户ID不一致
        if (wmTempletContractDB.getParentId() != (long) customerId) {
            logger.info("客户ID和入参客户ID不一致 parentId:{} customerId:{}", wmTempletContractDB.getParentId(), customerId);
            return;
        }
        // 任务状态不是待发起签约状态
        if (wmTempletContractDB.getStatus() != CustomerContractStatus.WAITING_SIGN.getCode()) {
            logger.info("合同状态不是待签约状态 customerId:{} contractId:{}", customerId, contractId);
            return;
        }
        ContractConfigInfo configInfo = wmFrameContractConfigService.queryContractConfigInfo(wmTempletContractDB.getType());
        if (configInfo != null) {
            wmEcontractSignBzService.cancelManualTaskWithBizId4Config(customerId, contractId, configInfo.getContractCode());
            getService(wmTempletContractDB.getType(), ContractSourceEnum.CONFIG.getCode()).signFail(contractId, failReason, opUid, opUname);
        } else {
            EcontractTaskApplyTypeEnum typeEnum = WmEcontractBatchConstant.CONTRACT_TASK_TYPE_MAP.get(WmTempletContractTypeEnum.getByCode(wmTempletContractDB.getType()));
            wmEcontractSignBzService.cancelManualTaskWithBizId(customerId, contractId, typeEnum);
            getService(wmTempletContractDB.getType()).signFail(contractId, failReason, opUid, opUname);
        }
    }

    private WmCustomerContractBo buildC1ContractBoForSign(Integer customerId) throws WmCustomerException {
        List<WmTempletContractDB> contractDBList = getContractByCustomerIdAndType(customerId, WmTempletContractTypeEnum.C1_E.getCode());
        if (CollectionUtils.isEmpty(contractDBList)) {
            logger.warn("合同不存在，或者被删除  customerId:{}", customerId);
            return null;
        }
        List<WmTempletContractSignDB> wmTempletContractSignDBS = wmTempletContractSignDBMapper.selectByWmTempletContractId(contractDBList.get(0).getId());
        WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDBList.get(0), wmTempletContractSignDBS);
        wmCustomerContractBo.setPackWay(SignPackWay.DO_SIGN.getCode());
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(true);
//        wmCustomerContractBo.setOpSource(EcontractSignOpSourceEnum.MULTI_MODIFY_PLATFORM);
        return wmCustomerContractBo;
    }

    private WmCustomerContractBo buildContractBoForSign(Integer customerId, Long contractId) throws WmCustomerException {
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        // 未查询到合同
        if (wmTempletContractDB == null) {
            logger.info("合同不存在或被删除 customerId:{} contractId:{}", customerId, contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同" + contractId + "不存在");
        }
        // 客户ID和入参客户ID不一致
        if (wmTempletContractDB.getParentId() != (long) customerId) {
            logger.info("客户ID和入参客户ID不一致 parentId:{} customerId:{}", wmTempletContractDB.getParentId(), customerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户ID和入参客户ID不一致");
        }
        // 任务状态不是待发起签约状态
        if (wmTempletContractDB.getStatus() != CustomerContractStatus.WAITING_SIGN.getCode()) {
            logger.info("合同状态不是待签约状态 customerId:{} contractId:{}", customerId, contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同" + contractId + "状态非待打包状态");
        }
        List<WmTempletContractSignDB> wmTempletContractSignDBS = wmTempletContractSignDBMapper.selectByWmTempletContractId(contractId);
        WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(wmTempletContractDB, wmTempletContractSignDBS);
        wmCustomerContractBo.setPackWay(SignPackWay.DO_SIGN.getCode());
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(true);
        return wmCustomerContractBo;
    }

    /**
     * 获取过期时间为unixTime的C1合同
     *
     * @param unixTime
     * @param startPageNum
     * @param pageSize
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    public List<WmTempletContractBasicBo> getBasicListWhenDueDateEquals(int unixTime, int startPageNum, int pageSize) throws WmCustomerException, TException {
        if (startPageNum < 0 || pageSize <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "startPageNum或者pageSize不能小于0");
        }

        logger.info("#getBasicListWhenDueDateEquals: unixTime = {}", unixTime);
        List<WmTempletContractDB> basicListWhenDueDateEquals = wmTempletContractAuditedDBMapper.getBasicListWhenDueDateEquals(unixTime, startPageNum, pageSize);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(basicListWhenDueDateEquals);
    }

    /**
     * 批量更改合同有效期
     *
     * @param wmCustomerContractUpdateBoList
     * @param pack
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     */
    public OpCustomerResultBo batchUpdateCustomerContractDueDate(List<WmCustomerContractUpdateBo> wmCustomerContractUpdateBoList, boolean pack, int opUid, String opName, OpCustomerResultBo opCustomerResultBo) throws WmCustomerException {
        logger.info("#batchUpdateCustomerContractDueDate: wmCustomerContractUpdateBoList.size = {}, pack = {}, opUid = {}, opName = {}", wmCustomerContractUpdateBoList.size(), pack, opUid, opName);
        BatchContractUpdateContext context = new BatchContractUpdateContext();
        initContext(context, wmCustomerContractUpdateBoList, pack, opUid, opName, opCustomerResultBo);
        BatchUpdateContractCheckFilter.batchUpdateContractCheckFilter().filter(context);
        if (context.isNeedProcess()) {
            // 执行批量操作
            doBatchUpdate(context);
        }
        logger.info("#batchUpdateCustomerContractDueDate: opCustomerResultBo = {}", JSON.toJSONString(opCustomerResultBo));
        return opCustomerResultBo;
    }

    private BatchContractUpdateContext initContext(BatchContractUpdateContext context, List<WmCustomerContractUpdateBo> wmCustomerContractUpdateBoList, boolean pack, int opUid, String opName, OpCustomerResultBo opCustomerResultBo) {
        wmCustomerContractUpdateBoList = wmCustomerContractUpdateBoList.stream().distinct().collect(Collectors.toList());
        context.setWmCustomerContractUpdateBoList(wmCustomerContractUpdateBoList);
        context.setPack(pack);
        context.setOpUid(opUid);
        context.setOpName(opName);
        context.setResultMap(opCustomerResultBo.getResultMap());
        return context;
    }

    private void doBatchUpdate(BatchContractUpdateContext context) {
        List<WmCustomerContractUpdateBo> wmCustomerContractUpdateBoList = context.getWmCustomerContractUpdateBoList();
        boolean pack = context.isPack();
        int opUid = context.getOpUid();
        String opName = context.getOpName();
        Map resultMap = context.getResultMap();
        Map<Long, WmCustomerContractBo> contractMap = context.getContractMap();

        Map<Long, Long> contractDueDateMap = wmCustomerContractUpdateBoList.stream().collect(Collectors.toMap(WmCustomerContractUpdateBo::getContractId, WmCustomerContractUpdateBo::getDueDate));
        logger.info("doBatchUpdate contractDueDateMap = {}, pack = {}", JSON.toJSON(contractDueDateMap), pack);

        final CountDownLatch countDownLatch = new CountDownLatch(wmCustomerContractUpdateBoList.size());
        for (WmCustomerContractUpdateBo wmCustomerContractUpdateBo : wmCustomerContractUpdateBoList) {
            Long contractId = wmCustomerContractUpdateBo.getContractId();
            Long dueDate = contractDueDateMap.get(contractId);
            WmCustomerContractBo wmCustomerContractBo = contractMap.get(contractId);

            WmTempletContractBasicBo basicBo = wmCustomerContractBo.getBasicBo();
            basicBo.setDueDate(dueDate);

            executorService.execute(() -> {
                try {
                    if (WmTempletContractTypeEnum.C1_PAPER.getCode() == wmCustomerContractBo.getBasicBo().getType()) {
                        wmContractWriteDomainService.saveAndCommitAudit(wmCustomerContractBo, opUid, opName);
                    } else if (WmTempletContractTypeEnum.C1_E.getCode() == wmCustomerContractBo.getBasicBo().getType()) {
                        if (pack) {
                            wmCustomerContractBo.setPackWay(SignPackWay.WAIT_HAND_PACK.getCode());
                        } else {
                            wmCustomerContractBo.setPackWay(SignPackWay.DO_SIGN.getCode());
                        }
                        wmContractWriteDomainService.saveAndStartSign(wmCustomerContractBo, opUid, opName);
                    } else {
                        resultMap.put(contractId, "此类型合同不支持批量修改");
                    }
                } catch (WmCustomerException e) {
                    resultMap.put(contractId, e.getMsg());
                } catch (TException e) {
                    resultMap.put(contractId, "系统异常");
                } catch (Exception e) {
                    resultMap.put(contractId, "系统异常");
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.warn("多线程等待结果超时异常", e);
        }
    }


    public List<WmCustomerContractBo> getCusContractBoListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndTypes(customerId, types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
        wmTempletContractDBList.forEach(contractDd -> {
            List<WmTempletContractSignDB> wmTempletContractSignDBS = wmTempletContractSignDBMapper.selectByWmTempletContractId(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    /**
     * 获取客户生效的C1合同有效期
     * @param customerId
     * @param types
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    public List<WmCustomerContractBo> getCusEffectContractBoListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getCusEffectContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByParentIdAndTypesMaster(customerId, types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
        wmTempletContractDBList.forEach(contractDd -> {
            List<WmTempletContractSignDB> wmTempletContractSignDBS = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public Map<Long, Boolean> hasAuditedC2ContractForAgentAndWmPoiBatch(Map<Long, Integer> wmPoiIdAndAgentIdMap) throws WmCustomerException, TException {

        logger.info("批量查询门店是否有生效C2合同  map:{}", JSON.toJSONString(wmPoiIdAndAgentIdMap));
        if (CollectionUtils.isEmpty(wmPoiIdAndAgentIdMap)) {
            return Maps.newHashMap();
        }
        int auditedContractStatusSize = ConfigUtilAdapter.getInt("batch_get_c2_audited_contract_status_size", 200);
        if (wmPoiIdAndAgentIdMap.size() > auditedContractStatusSize) {
            logger.warn("入参长度超限，限制(<=)：" + auditedContractStatusSize);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "入参长度超限，限制(<=)：" + auditedContractStatusSize);
        }
        Map<Long, Boolean> result = Maps.newHashMap();
        for (Map.Entry<Long, Integer> wmPoiIdAndAgentIdEntry : wmPoiIdAndAgentIdMap.entrySet()) {
            result.put(wmPoiIdAndAgentIdEntry.getKey(),
                    hasAuditedC2ContractForAgentAndWmPoi(wmPoiIdAndAgentIdEntry.getKey(), wmPoiIdAndAgentIdEntry.getValue()));
        }
        return result;
    }

    public Boolean hasAuditedC2ContractForAgentAndWmPoi(long wmPoiId, int agentId) throws WmCustomerException, TException {

        logger.info("查询门店是否有生效C2合同  wmPoiId:{} agentId:{}", wmPoiId, agentId);
        Boolean needC2ForPoiOnline;
        try {
            needC2ForPoiOnline = wmContractOnlineService.needC2ForPoiSwitchAgent(wmPoiId);
        } catch (WmCustomerException | TException e) {
            logger.warn("查询门店信息异常", e);
            return false;
        }
        boolean res = !needC2ForPoiOnline
                || getC2ContractId(wmPoiId, agentId, true).size() > 0;
        logger.info("查询门店是否有生效C2合同  wmPoiId:{} agentId:{}  res:{}", wmPoiId, agentId, res);
        return res;
    }

    public Boolean hasAuditedC2Contract(long wmPoiId, int agentId) throws WmCustomerException, TException {
        logger.info("查询门店是否有生效C2合同  wmPoiId:{} agentId:{}", wmPoiId, agentId);
        boolean res = getC2ContractId(wmPoiId, agentId, true).size() > 0;
        logger.info("查询门店是否有生效C2合同  wmPoiId:{} agentId:{}  res:{}", wmPoiId, agentId, res);
        return res;
    }

    private List<Long> getC2ContractId(long wmPoiId, int agentId, boolean isEffective) throws WmCustomerException, TException {
        List<Long> result = Lists.newArrayList();
        Set<Integer> customerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        if (CollectionUtils.isEmpty(customerIdSet)) {
            return result;
        }
        if (customerIdSet.size() > 1) {
            logger.error("门店：{} 绑定多个客户，客户ID：{}", wmPoiId, customerIdSet);
        }
        try {
            for (int cusId : customerIdSet) {
                List<WmCustomerContractBo> contractList;
                if(cusId % 100 < MccConfig.getC2ContractIdFixGrayPercent()){
                    if (isEffective) {
                        contractList = getAuditedContractBoWithoutSignPhoneListByCusIdAndType(cusId,
                                Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(), WmTempletContractTypeEnum.C2_PAPER.getCode()), 0, "合作商切换");
                    } else {
                        contractList = getContractBoWithoutSignPhoneListByCusIdAndType((long) cusId,
                                Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(),
                                        WmTempletContractTypeEnum.C2_PAPER.getCode()), 0, "合作商切换");
                    }
                }else {
                    if (isEffective) {
                        contractList = getAuditedContractBoListByCusIdAndType(cusId,
                                Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(), WmTempletContractTypeEnum.C2_PAPER.getCode()), 0, "合作商切换");
                    } else {
                        contractList = getContractBoListByCusIdAndType((long) cusId,
                                Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(),
                                        WmTempletContractTypeEnum.C2_PAPER.getCode()), 0, "合作商切换");
                    }
                }


                if (CollectionUtils.isEmpty(contractList)) {
                    continue;
                }
                for (WmCustomerContractBo customerContractBo : contractList) {
                    WmTempletContractSignBo bSignerBo = wmContractSignService.getPartyBSigner(customerContractBo.getSignBoList());
                    if (bSignerBo != null && bSignerBo.getSignId() == agentId) {
                        logger.info("门店:{} 存在生效的合作商:{} 对应的C2合同", wmPoiId, agentId);
                        long contractId = customerContractBo.getBasicBo().getTempletContractId();
                        result.add(contractId);
                    }
                }

            }
        } catch (WmCustomerException e) {
            logger.warn(e.getMessage(), e);
        } catch (TException e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    public void invalidC2ContractForAgentAndWmPoi(long wmPoiId, int agentId, int opUid, String opUname) throws WmCustomerException, TException {

        logger.info("根据门店id废除C2合同 wmPoiId:{} agentId:{} opUid:{} opUname:{}", wmPoiId, agentId, opUid, opUname);
        List<Long> c2ContractIdList = getC2ContractId(wmPoiId, agentId, false);
        if (CollectionUtils.isEmpty(c2ContractIdList)) {
            logger.info("无生效的C2合同 wmPoiId:{} agentId:{}", wmPoiId, agentId);
            return;
        }
        for (Long c2ContractId : c2ContractIdList) {
            wmContractValidCallbackService.invalid(c2ContractId, opUid, opUname);
        }
    }

    public List<Integer> getC2PoiIdListByWmAgentId(int agentId) throws WmCustomerException, TException {

        logger.info("根据合作商id查询与该合作商签署C2合同的门店列表  agentId:{} ", agentId);
        List<WmTempletContractDB> contractDBList = wmTempletContractAuditedDBMapper.selectC2ContractListByAgentId(agentId);
        if (CollectionUtils.isEmpty(contractDBList)) {
            logger.info("根据合作商id查询与该合作商签署C2合同的门店列表  res:empty agentId:{}", agentId);
            return Lists.newArrayList();
        }
        List<Integer> result = Lists.newArrayList();
        for (WmTempletContractDB contractDB : contractDBList) {
            List<Long> wmPoiList = wmCustomerPoiService.selectWmPoiIdsByCustomerId(contractDB.getParentId().intValue());
            if (CollectionUtils.isEmpty(wmPoiList)) {
                continue;
            }
            List<WmPoiDomain> wmPoiDomainList = wmPoiClient.getWmPoiByIds(wmPoiList);
            for (WmPoiDomain domain : wmPoiDomainList) {
                if (domain.getAgentId() > 0) {
                    result.add(domain.getWmPoiId());
                }
            }
        }
        logger.info("根据合作商id查询与该合作商签署C2合同的门店列表   agentId:{} res:{}", agentId, JSON.toJSONString(result));
        return result;
    }

    public List<Integer> getAgentIdsByWmPoiIds(List<Long> wmPoiIds) {
        logger.info("根据门店id列表获取对应的代理商id列表  wmPoiIds:{} ", wmPoiIds);
        try {
            if (CollectionUtils.isEmpty(wmPoiIds)) {
                return new ArrayList<>();
            }
            List<WmPoiDomain> wmPoiDomainList = wmPoiClient.getWmPoiByIds(wmPoiIds);
            if (CollectionUtils.isEmpty(wmPoiDomainList)) {
                return new ArrayList<>();
            }
            Set<Integer> agentIdSet = new HashSet<>();
            for (WmPoiDomain domain : wmPoiDomainList) {
                //代理商门店且非连锁
                if (domain.getAgentId() > 0 && !wmPoiClient.isLianSuoPoi(domain)) {
                    agentIdSet.add((int) domain.getAgentId());
                }
            }
            return new ArrayList<>(agentIdSet);
        } catch (Exception e) {
            logger.error("根据门店id列表获取对应的代理商id列表 异常 wmPoiIds:{} ", wmPoiIds, e);
        }
        return new ArrayList<>();
    }

    public BooleanResult saveCustomerBlackWhiteList(CustomerBlackWhiteBo bo)
            throws WmCustomerException {
        AssertUtil.assertObjectNotNull(bo);
        return wmCustomerBlackWhiteListService.saveCustomerBlackWhiteList(bo);
    }

    public LongResult getC1ContractStartSignTime(long wmPoiId) throws WmCustomerException, TException {

        Set<Integer> customerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        if (CollectionUtils.isEmpty(customerIdSet)) {
            return new LongResult(0);
        }

        Integer customerId = Lists.newArrayList(customerIdSet).get(0);
        List<WmEcontractSignManualTaskDB> wmEcontractSignManualTaskDBList = wmEcontractManualTaskBizService.getManualTaskByCustomerIdAndModule(customerId, EcontractTaskApplyTypeEnum.C1CONTRACT.getName());
        if (!CollectionUtils.isEmpty(wmEcontractSignManualTaskDBList)) {
            WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB = wmEcontractSignManualTaskDBList.get(0);
            return new LongResult(wmEcontractSignManualTaskDB.getCtime());
        }

        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndType((long) customerId, WmTempletContractTypeEnum.C1_E.getCode());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return new LongResult(0);
        }

        WmContractVersionDB wmContractVersionDB = wmContractVersionService.getLastByWmContractIdAndTypeAndStatusList(
                wmTempletContractDBList.get(0).getId().intValue(),
                CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE,
                Lists.newArrayList((byte) CustomerContractStatus.SIGNING.getCode(), (byte) CustomerContractStatus.SIGN_FAIL.getCode(), (byte) CustomerContractStatus.EFFECT.getCode()));
        if (wmContractVersionDB == null) {
            return new LongResult(0);
        }
        return new LongResult(wmContractVersionDB.getCtime());
    }

    public OpResultBo saveAndSignContract(long wmPoiId, int type, int signSubjectCode, int opUid, String opUname) throws WmCustomerException, TException {


        logger.info("saveAndSignContract wmPoiId = {}, type = {}, opUid = {}, opUname = {}", wmPoiId, type, opUid, opUname);
        if (!autoSaveSignContractTypeSet.contains(type)) {
            throw new WmCustomerException(CustomerContractErrCodeEnum.NOT_SUPPORT_TYPE.getCode(), CustomerContractErrCodeEnum.NOT_SUPPORT_TYPE.getDesc());
        }

        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if (wmCustomerDB == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无对应客户");
        }
        if (SignType.PAPER.getCode() == wmCustomerDB.getSignMode()) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "纸质签约,不可走电子签约流程");
        }

        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByParentIdAndTypes(Long.valueOf(wmCustomerDB.getId()), Lists.newArrayList(type));
        if (!CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return new OpResultBo(CustomerContractRetCodeEnum.EFFECTED.getCode(), CustomerContractRetCodeEnum.EFFECTED.getDesc());
        }

        wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndTypes(Long.valueOf(wmCustomerDB.getId()), Lists.newArrayList(type));
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return doSaveAndSign(wmCustomerDB, type, signSubjectCode, opUid, opUname);
        }
        return doSaveOrResendMsg(wmTempletContractDBList.get(0), opUid, opUname);
    }

    private OpResultBo doSaveOrResendMsg(WmTempletContractDB wmTempletContractDB, int opUid, String opUname) {
        if (CustomerContractStatus.SIGNING.getCode() == wmTempletContractDB.getStatus()) {
            return handleSigningContract(wmTempletContractDB);
        } else if (CustomerContractStatus.SIGN_FAIL.getCode() == wmTempletContractDB.getStatus()) {
            return handleSignFailContract(wmTempletContractDB, opUid, opUname);
        } else {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "暂不支持该合同状态操作");
        }
    }

    private OpResultBo handleSignFailContract(WmTempletContractDB wmTempletContractDB, int opUid, String opUname) {
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmTempletContractDB.getParentId().intValue());
        if (wmCustomerKp == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无客户KP");
        }

        List<WmTempletContractSignDB> wmTempletContractSignDBList = wmTempletContractSignDBMapper.selectByWmTempletContractId(wmTempletContractDB.getId());
        OpResultBo opResultBo;
        try {
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(wmTempletContractDB, wmTempletContractSignDBList);
            Integer contractId = saveAndStartSign(wmCustomerContractBo, opUid, opUname);
            String signPhoneNum = wmCustomerKp.getPhoneNum().length() < 4 ? wmCustomerKp.getPhoneNum() : wmCustomerKp.getPhoneNum().substring(wmCustomerKp.getPhoneNum().length() - 4);
            opResultBo = new OpResultBo(Long.valueOf(contractId), CustomerContractRetCodeEnum.SAVE_SIGN_SUCCESS.getCode(), "已发起签约", signPhoneNum);
        } catch (WmCustomerException e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), e.getMsg());
        } catch (Exception e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.SYSTEM_ERROR.getCode(), "系统异常");
        }
        return opResultBo;
    }

    private OpResultBo handleSigningContract(WmTempletContractDB wmTempletContractDB) {
        if (!tairLocker.tryLockToday(generateLockKey(wmTempletContractDB.getParentId()))) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "重发短信次数已经超过当天上限,每个客户每天只能重发一次短信");
        }
        WmContractVersionDB wmContractVersionDB = wmContractVersionService.getByIdAndTypeMaster(wmTempletContractDB.getId().intValue(), CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
        if (wmContractVersionDB == null) {
            tairLocker.unLock(generateLockKey(wmTempletContractDB.getParentId()));
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "合同尚未签约,不可重发短信");
        }
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmTempletContractDB.getParentId().intValue());
        if (wmCustomerKp == null) {
            tairLocker.unLock(generateLockKey(wmTempletContractDB.getParentId()));
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无客户KP");
        }

        OpResultBo opResultBo;
        try {
            wmEcontractSignBzService.resendMsg(Long.valueOf(wmContractVersionDB.getTransaction_id()));
            String signPhoneNum = wmCustomerKp.getPhoneNum().substring(wmCustomerKp.getPhoneNum().length() - 4);
            opResultBo = new OpResultBo(wmTempletContractDB.getId(), CustomerContractRetCodeEnum.SEND_MSG_SUCCESS.getCode(), "已发起签约", signPhoneNum);
        } catch (WmCustomerException e) {
            tairLocker.unLock(generateLockKey(wmTempletContractDB.getParentId()));
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), e.getMsg());
        } catch (Exception e) {
            tairLocker.unLock(generateLockKey(wmTempletContractDB.getParentId()));
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.SYSTEM_ERROR.getCode(), "系统异常");
        }
        return opResultBo;
    }

    private String generateLockKey(Long customerId) {
        return new StringBuilder()
                .append("AD_CONTRACT_")
                .append(customerId)
                .toString();
    }

    private OpResultBo doSaveAndSign(WmCustomerDB wmCustomerDB, int type, int signSubjectCode, int opUid, String opUname) throws TException, WmCustomerException {
        WmTempletContractBasicBo wmTempletContractBasicBo = new WmTempletContractBasicBo();
        wmTempletContractBasicBo.setParentId(wmCustomerDB.getId());
        wmTempletContractBasicBo.setTempletContractId(0);
        wmTempletContractBasicBo.setType(type);
        wmTempletContractBasicBo.setDueDate(0L);
        wmTempletContractBasicBo.setContractNum("电子合同保存后自动生成编号");

        CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
        paperContractRemarkBo.setContractScan(new MultiFileJsonBo());
        paperContractRemarkBo.setOtherContractScan(new MultiFileJsonBo());
        wmTempletContractBasicBo.setExtStr(JSON.toJSONString(paperContractRemarkBo));

        WmTempletContractSignBo wmTempletContractSignBoA = new WmTempletContractSignBo();
        wmTempletContractSignBoA.setTempletContractId(0);
        wmTempletContractSignBoA.setSignType("A");
        wmTempletContractSignBoA.setSignId(wmCustomerDB.getId());
        wmTempletContractSignBoA.setSignName(wmCustomerDB.getCustomerName());
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmCustomerDB.getId());
        if (wmCustomerKp == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无客户KP");
        }
        wmTempletContractSignBoA.setSignPeople(wmCustomerKp.getCompellation());
        wmTempletContractSignBoA.setSignPhone(wmCustomerKp.getPhoneNum());

        WmTempletContractSignBo wmTempletContractSignBoB = new WmTempletContractSignBo();
        wmTempletContractSignBoB.setTempletContractId(0);
        wmTempletContractSignBoB.setSignType("B");
        wmTempletContractSignBoB.setSignId(0);
        SignSubjectEnum signSubjectEnum = SignSubjectEnum.getByCode(signSubjectCode);
        if (signSubjectEnum == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "该签约主体暂不支持");
        }
        wmTempletContractSignBoB.setSignName(signSubjectEnum.getDesc());
        WmEmploy wmEmploy = wmEmployClient.getById(wmCustomerDB.getOwnerUid());
        if (wmEmploy == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "签约人乙方信息不存在");
        }
        wmTempletContractSignBoB.setSignPeople(wmEmploy.getName());
        wmTempletContractSignBoB.setSignPhone(empServiceAdaptor.getPhone(wmCustomerDB.getOwnerUid()));

        String today = DateUtil.secondsToString(DateUtil.unixTime());
        wmTempletContractSignBoA.setSignTime(today);
        wmTempletContractSignBoB.setSignTime(today);

        List<WmTempletContractSignBo> wmTempletContractSignBoList = Lists.newArrayList(wmTempletContractSignBoA, wmTempletContractSignBoB);
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(wmTempletContractBasicBo);
        wmCustomerContractBo.setSignBoList(wmTempletContractSignBoList);
        wmCustomerContractBo.setPackWay(SignPackWay.DO_SIGN.getCode());
        wmCustomerContractBo.setManualBatchId(0L);
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(false);

        OpResultBo opResultBo;
        try {
            Integer contractId = saveAndStartSign(wmCustomerContractBo, opUid, opUname);
            String signPhoneNum = wmCustomerKp.getPhoneNum().length() < 4 ? wmCustomerKp.getPhoneNum() : wmCustomerKp.getPhoneNum().substring(wmCustomerKp.getPhoneNum().length() - 4);
            opResultBo = new OpResultBo(Long.valueOf(contractId), CustomerContractRetCodeEnum.SEND_MSG_SUCCESS.getCode(), "已发起签约", signPhoneNum);
        } catch (WmCustomerException e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), e.getMsg());
        } catch (Exception e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.SYSTEM_ERROR.getCode(), "系统异常");
        }
        return opResultBo;
    }

    public List<WmCustomerContractBo> getAuditedContractBoListByCusIdAndTypeRT(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getAuditedContractBoListByCusIdAndTypeRT 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypesMaster(customerId, types);
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return new ArrayList<>();
        }
        List<WmCustomerContractBo> res = new ArrayList<>();
        wmTempletContractDBList.forEach(contractDd -> {
            List<WmTempletContractSignDB> wmTempletContractSignDBS = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractIdMaster(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            if (wmCustomerContractBo != null) {
                res.add(wmCustomerContractBo);
            }
        });
        return res;
    }

    public List<WmCustomerContractBo> getAuditedContractBoListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes((int) customerId,
                types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
//        logger.info("#getAuditedContractBoListByCusIdAndType#wmTempletContractDBList#size:{},customerId:{}", wmTempletContractDBList.size(), customerId);
        wmTempletContractDBList.forEach(contractDd -> {
//            logger.info("#getAuditedContractBoListByCusIdAndType#selectByWmTempletContractId customerId:{}, contracId:{}", customerId, contractDd.getId());
            List<WmTempletContractSignDB> wmTempletContractSignDBS = wmTempletContractSignAuditedDBMapper
                    .selectByWmTempletContractId(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public List<WmCustomerContractBo> getAuditedContractBoWithoutSignPhoneListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes((int) customerId,
                types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
        wmTempletContractDBList.forEach(contractDd -> {
            List<WmTempletContractSignWithOutSignPhoneDB> wmTempletContractSignDBS = wmTempletContractSignAuditedDBMapper
                    .selectByWmTempletContractIdWithOutSignPhone(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractWithoutSignPhoneAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public List<WmCustomerContractBo> getContractBoListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectValidByParentIdAndTypes(customerId, types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
        wmTempletContractDBList.forEach(contractDd -> {
            List<WmTempletContractSignDB> wmTempletContractSignDBS = wmTempletContractSignDBMapper.selectByWmTempletContractId(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public List<WmCustomerContractBo> getContractBoWithoutSignPhoneListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoWithoutSignPhoneListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectValidByParentIdAndTypes(customerId, types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
        wmTempletContractDBList.forEach(contractDd -> {
            List<WmTempletContractSignWithOutSignPhoneDB> wmTempletContractSignDBS = wmTempletContractSignDBMapper.selectByWmTempletContractIdWithOutSignPhone(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractWithoutSignPhoneAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public List<WmTempletContractBasicBo> getContractBasicBoListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper
                .selectByParentIdAndTypes(customerId, types);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    public List<WmTempletContractBasicBo> getAuditedContractBasicBoListByCusIdAndType(long customerId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getAuditedContractBasicBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByParentIdAndTypes(customerId, types);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    public int getLatestC1ClosingDate(Set<Long> customerIdSet) {

        return wmTempletContractAuditedDBMapper.getLatestC1ClosingDate(customerIdSet);
    }

    /**
     * 获取预计生效时间为unixTime的C1电子合同
     *
     * @param unixTime
     * @param startPageNum
     * @param pageSize
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    public List<WmTempletContractBasicBo> getBasicListWhenExpectEffectDateEquals(int unixTime, List<Integer> types, int startPageNum, int pageSize) throws WmCustomerException, TException {
        if (startPageNum < 0 || pageSize <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "startPageNum或者pageSize不能小于0");
        }

        logger.info("#getBasicListWhenExpectEffectDateEquals: unixTime = {}", unixTime);
        List<WmTempletContractDB> basicListWhenDueDateEquals = wmTempletContractDBMapper.getBasicListWhenExpectEffectDateEquals(unixTime, types, (startPageNum - 1) * pageSize, pageSize);
        logger.info("#getBasicListWhenExpectEffectDateEquals: result = {}", JSON.toJSONString(basicListWhenDueDateEquals));
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(basicListWhenDueDateEquals);
    }

    /**
     * 执行合同待生效到生效逻辑
     *
     * @param unixTime
     * @throws WmCustomerException
     * @throws TException
     */
    public void executeContractToEffect(int unixTime) {
        logger.info("#executeContractToEffect#,生效时间unixTime = {}", unixTime);
        try {
            List<Long> contractFailList = Lists.newArrayList();
            int startPageNum = 1;
            List<WmTempletContractBasicBo> basicBoList = geToEffectBasicList(unixTime, startPageNum);

            while (!CollectionUtils.isEmpty(basicBoList)) {
                List<Future> futureList = new ArrayList<>();
                //处理待生效合同
                for (WmTempletContractBasicBo basicBo : basicBoList) {
                    Future future = executorServiceForEffect.submit(() -> {
                        try {
                            getService(basicBo.getType()).effect(basicBo.getTempletContractId(), -1, "定时JOB触发合同待生效到生效");
                        } catch (WmCustomerException | TException e) {
                            logger.error("多线程处理合同生效异常：contract_id = {}", basicBo.getTempletContractId());
                            contractFailList.add(basicBo.getTempletContractId());
                        }
                    });
                    futureList.add(future);
                }
                //等待所有子线程执行完成
                if (!CollectionUtils.isEmpty(futureList)) {
                    for (Future f : futureList) {
                        f.get();
                    }
                }
                //拉取下一批待处理任务
                basicBoList = geToEffectBasicList(unixTime, startPageNum);
            }

            if (!CollectionUtils.isEmpty(contractFailList)) {
                // 发大象消息
                DaxiangUtil.push("<EMAIL>",
                        String.format("合同ID:%s,从待生效到生效异常",
                                org.apache.commons.lang3.StringUtils.join(contractFailList, ",")),
                        MccConfig.getContractEffectNotifyMisidList());
            }
        } catch (Exception e) {
            logger.error("executeContractToEffect调用异常", e);
        }
    }

    /**
     * 查询预计生效时间等于当前时间的合同
     *
     * @param dayStartTime
     * @param startPageNum
     * @return
     */
    private List<WmTempletContractBasicBo> geToEffectBasicList(int dayStartTime, int startPageNum) {
        try {
            List<Integer> contractTypeList = Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(),
                    WmTempletContractTypeEnum.C2_E.getCode(),
                    WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(),
                    WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(),
                    WmTempletContractTypeEnum.DELIVERY_SERVICE_CONTRACT_E.getCode(),
                    WmTempletContractTypeEnum.DELIVERY_SITE_CONTRACT_E.getCode(),
                    WmTempletContractTypeEnum.AD_ANNUAL_FRAMEWORK_CONTRACT_E.getCode(),
                    WmTempletContractTypeEnum.BRAND_AD_CONTRACT_E.getCode(),
                    WmTempletContractTypeEnum.AD_ORDER_E.getCode(),
                    WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode(),
                    WmTempletContractTypeEnum.GROUP_MEAL_E.getCode(),
                    WmTempletContractTypeEnum.BAG_SERVICE_E.getCode(),
                    WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode(),
                    WmTempletContractTypeEnum.INTERIM_SELF_E.getCode(),
                    WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode(),
                    WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
                    WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
            );
            List<Integer> allConfigContractIdList = wmFrameContractConfigService.allConfigFrameContract().stream()
                    .map(ContractConfigInfo::getContractId)
                    .collect(Collectors.toList());
            contractTypeList.addAll(allConfigContractIdList);
            List<WmTempletContractBasicBo> basicBoList = this.getBasicListWhenExpectEffectDateEquals(dayStartTime,
                    contractTypeList,
                    startPageNum, PAGE_SIZE);
            return basicBoList;
        } catch (Exception e) {
            logger.error("获取待生效合同列表异常了", e);
        }
        return null;
    }


    public Map<Long, WmCustomerContractBo> getWmCustomerContractBoByWmPoiIds(List<Long> wmPoiIds, List<Integer> types) {
        logger.info("#getWmCustomerContractBoByWmPoiIds wmPoiIds = {}, types = {}", JSON.toJSONString(wmPoiIds), JSON.toJSONString(types));
        if (CollectionUtils.isEmpty(wmPoiIds) || CollectionUtils.isEmpty(types)) {
            return Maps.newHashMap();
        }

        // 门店ID -> 客户ID 映射
        Map<Long, Integer> wmPoiIdCustomerIdMap = wmCustomerPoiService.selectByWmPoiIdList(wmPoiIds);
        logger.info("#getWmCustomerContractBoByWmPoiIds wmPoiIdCustomerIdMap = {}", JSON.toJSONString(wmPoiIdCustomerIdMap));

        // 客户ID -> 合同基本信息 映射
        List<Long> wmCustomerIdList = Lists.newArrayList();
        wmPoiIdCustomerIdMap.entrySet().stream().forEach(item -> wmCustomerIdList.add(item.getValue().longValue()));
        if (CollectionUtils.isEmpty(wmCustomerIdList)) {
            return Maps.newHashMap();
        }
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByParentIdsAndTypes(wmCustomerIdList, types);
        Map<Long, WmTempletContractDB> wmTempletContractDBMap = wmTempletContractDBList.stream().collect(Collectors.toMap(item -> item.getParentId(), item -> item));
        logger.info("#getWmCustomerContractBoByWmPoiIds wmTempletContractDBMap = {}", JSON.toJSONString(wmTempletContractDBMap));

        // 合同ID -> 合同签约人信息 映射
        List<Long> wmTempletContractIdList = wmTempletContractDBList.stream().map(WmTempletContractDB::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wmTempletContractIdList)) {
            return Maps.newHashMap();
        }
        List<WmTempletContractSignDB> wmTempletContractSignDBList = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractIdList(wmTempletContractIdList);
        Map<Long, List<WmTempletContractSignDB>> wmTempletContractSignDBMap = Maps.newHashMap();
        wmTempletContractSignDBList.stream().forEach(item -> {
            List<WmTempletContractSignDB> list = wmTempletContractSignDBMap.getOrDefault(item.getWmTempletContractId().longValue(), Lists.newArrayList());
            list.add(item);
            wmTempletContractSignDBMap.put(item.getWmTempletContractId().longValue(), list);
        });
        logger.info("#getWmCustomerContractBoByWmPoiIds wmTempletContractSignDBMap = {}", JSON.toJSONString(wmTempletContractSignDBMap));

        // 门店ID -> 合同 映射
        Map<Long, WmCustomerContractBo> customerContractBoMap = Maps.newHashMap();
        for (Long wmPoiId : wmPoiIds) {
            Integer val = wmPoiIdCustomerIdMap.get(wmPoiId);
            Long customerId = val == null ? null : Long.valueOf(val);
            WmTempletContractDB wmTempletContractDB = wmTempletContractDBMap.get(customerId);
            if (wmTempletContractDB != null) {
                WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
                wmCustomerContractBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB));
                wmCustomerContractBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(wmTempletContractSignDBMap.get(wmTempletContractDB.getId())));
                customerContractBoMap.put(wmPoiId, wmCustomerContractBo);
            }
        }
        logger.info("#getWmCustomerContractBoByWmPoiIds customerContractBoMap = {}", JSON.toJSONString(customerContractBoMap));
        return customerContractBoMap;
    }

    private boolean isXXYearsLater(Long dueDate, int xx) {
        DateTime xxYearsLater = DateTime.now().plusYears(xx);
        return dueDate > com.sankuai.meituan.util.DateUtil.date2Unixtime(xxYearsLater.toDate());
    }

    /**
     * 批量平台修改C1合同参数校验
     *
     * @param contractBo
     * @throws WmCustomerException
     */
    private void checkParam(WmCustomerContractBatchBo contractBo) throws WmCustomerException {

        if (contractBo == null || contractBo.getCustomerId() <= 0) {
            logger.info("批量修改客户id不存在");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户不存在");
        }

        if (StringUtil.isBlank(contractBo.getSignPeople()) || StringUtil.isBlank(contractBo.getSignPhone())) {
            logger.info("签约人信息不全");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "乙方签约人信息不全");
        }

        if (contractBo.getPackWay() <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "打包方式不能为空");
        }
    }

    /**
     * 快照记录
     *
     * @param contractInDb
     * @param opUid
     * @param opUname
     */
    public void recordCustomerPoiSubject(WmTempletContractDB contractInDb, int opUid, String opUname) {
        logger.info("记录签约主体快照：contractInDb={},opUid={},opUname={}", JSON.toJSONString(contractInDb), opUid, opUname);
        if (contractInDb == null) {
            return;
        }
        String partB = getContractPartB(contractInDb);
        String partLogistic = contractInDb.getLogisticsSubject();

        //履约主体如果为空，则默认北京三快，针对存量签约中或者审核中的数据
        if (StringUtil.isBlank(partLogistic)) {
            partLogistic = ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc();
        }

        logger.info("记录签约主体快照：contractInDb={},partB={},partLogistic={}", JSON.toJSONString(contractInDb), partB, partLogistic);
        List<Long> wmPoiIdList = null;
        if (MccConfig.getCustomerSubjectMasterSwitch()) {
            try {
                wmPoiIdList =
                        MasterSlaveHelper.doInMaster(() -> wmCustomerPoiService.selectWmPoiIdsByCustomerId(contractInDb.getParentId()
                                .intValue()));
            } catch (WmCustomerException e) {
                logger.warn("未查询到门店id customer_id:{}", contractInDb.getParentId());
            }
        } else {
            wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerId(contractInDb.getParentId().intValue());
        }
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return;
        }
        saveCustomerPoiSubject(contractInDb.getParentId().intValue(), wmPoiIdList, ContractSignSubjectOpTagEnum.CONTRACT_EFFECT.getCode(), partB, partLogistic);
    }

    /**
     * 获取签约主体B
     *
     * @param contractInDb
     * @return
     */
    public String getContractPartB(WmTempletContractDB contractInDb) {
//        logger.info("getContractPartB#customerId:{}，contractId:{}", contractInDb.getParentId(), contractInDb.getId());
        List<WmTempletContractSignDB> signDBList = wmTempletContractSignAuditedDBMapper.selectByWmTempletContractIdRT(contractInDb.getId());
        if (!CollectionUtils.isEmpty(signDBList)) {
            for (WmTempletContractSignDB signDB : signDBList) {
                if ("B".equals(signDB.getSignType())) {
                    return signDB.getSignName();
                }
            }
        }
        return Strings.EMPTY;
    }

    /**
     * 保存主体快照
     *
     * @param customerId
     * @param wmPoiIdList
     * @param opType
     * @param partB
     * @param partLogistic
     */
    public void saveCustomerPoiSubject(int customerId, List<Long> wmPoiIdList, int opType, String partB, String partLogistic) {
        //服务灰度过程中不写快照，服务全量后开启
        if (!CollectionUtils.isEmpty(wmPoiIdList) && MccConfig.getRecordSubjectSwitchGray()) {
            //分组处理，每组200条
            List<List<Long>> wmPoiIdPartList = Lists.partition(wmPoiIdList, MccConfig.getBatchSubjectPartNum());
            for (List<Long> list : wmPoiIdPartList) {
                List<WmCustomerPoiLogisticsSubjectDB> subjectDBS = Lists.newArrayList();
                //写入门店快照表
                for (Long wmPoiId : list) {
                    WmCustomerPoiLogisticsSubjectDB subjectDB = new WmCustomerPoiLogisticsSubjectDB();
                    subjectDB.setCustomerId(customerId);
                    subjectDB.setWmPoiId(wmPoiId);
                    subjectDB.setPartBNum(ContractSignSubjectEnum.getByValue(partB) != null ? ContractSignSubjectEnum.getByValue(partB).getCode() : 0);
                    subjectDB.setPartLogisticsNum(ContractSignSubjectEnum.getByValue(partLogistic) != null ? ContractSignSubjectEnum.getByValue(partLogistic).getCode() : 0);
                    //当前时间生效
                    subjectDB.setEffectTime(TimeUtil.unixtime());
                    subjectDB.setTag(opType);
                    subjectDB.setValid(1);
                    subjectDBS.add(subjectDB);
                }
                wmCustomerPoiSubjectService.batchSavePoiSubject(subjectDBS);
            }
        }
    }

    /**
     * 批量插入门店主体快照数据(洗数据)
     *
     * @param wmPoiIds
     */
    public void batchSwitchPoiSubject(String wmPoiIds, Integer effectTime) {

        List<String> stringList = Splitter.on(",").trimResults().splitToList(wmPoiIds);
        logger.info("初始化门店快照数据 stringList = {}", JSON.toJSONString(stringList));
        Set<String> wmPoiIdsSwitch = Sets.newHashSet(stringList);
        if (wmPoiIdsSwitch != null && !wmPoiIdsSwitch.isEmpty()) {
            for (String wmPoiId : wmPoiIdsSwitch) {
                handleService.execute(() -> {
                    try {
                        initPoiSubjectData(Long.parseLong(wmPoiId), effectTime);
                    } catch (Exception e) {
                        logger.error("初始化门店主体快照异常：wmPoiId= {}", wmPoiId, e);
                    }
                });
            }
        }
    }

    //初始化快照数据
    private void initPoiSubjectData(Long wmPoiId, Integer effectTime) {
        WmCustomerPoiLogisticsSubjectDB subjectDB = new WmCustomerPoiLogisticsSubjectDB();
        subjectDB.setWmPoiId(wmPoiId);
        Set<Integer> customerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        if (!CollectionUtils.isEmpty(customerIdSet)) {
            subjectDB.setCustomerId(Lists.newArrayList(customerIdSet).get(0));
        } else {
            subjectDB.setCustomerId(0);
        }
        subjectDB.setPartBNum(ContractSignSubjectEnum.BEIJING_SANKUAI.getCode());
        subjectDB.setPartLogisticsNum(ContractSignSubjectEnum.SHANGHAI_ZHISONG.getCode());
        subjectDB.setEffectTime(effectTime);
        subjectDB.setTag(ContractSignSubjectOpTagEnum.INIT_DATA.getCode());
        subjectDB.setValid(1);
        wmCustomerPoiSubjectService.savePoiSubject(subjectDB);
    }

    private void initPoiSubjectDataWithTagId(Long wmPoiId, int tagId, Integer effectTime) {
        WmCustomerPoiLogisticsSubjectDB subjectDB = new WmCustomerPoiLogisticsSubjectDB();
        subjectDB.setWmPoiId(wmPoiId);
        Set<Integer> customerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        if (!CollectionUtils.isEmpty(customerIdSet)) {
            subjectDB.setCustomerId(Lists.newArrayList(customerIdSet).get(0));
        } else {
            subjectDB.setCustomerId(0);
        }
        subjectDB.setPartBNum(ContractSignSubjectEnum.BEIJING_SANKUAI.getCode());
        subjectDB.setPartLogisticsNum(ContractSignSubjectEnum.SHANGHAI_ZHISONG.getCode());
        subjectDB.setEffectTime(effectTime);
        subjectDB.setTag(tagId);
        subjectDB.setValid(1);
        wmCustomerPoiSubjectService.savePoiSubject(subjectDB);
    }


    /**
     * 获取客户签约主体--只支持C1电子合同
     *
     * @param customerId
     * @return
     */
    public WmPoiSignSubjectBo getSignSubjectBo(Integer customerId, int contractType) throws TException, WmCustomerException {
        //参数校验
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            logger.info("未查询到客户信息：customerId = {} ", customerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前客户信息不存在");
        }

        if (contractType != WmTempletContractTypeEnum.C1_E.getCode()) {
            logger.info("合同类型不是C1电子，不进行主体赋值：customerId={} ", customerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约主体获取只支持C1电子合同");
        }

        // 跨境B2C药品客户有固定履约主题
        if (checkIsB2CCustomer(customerId)) {
            logger.info("当前客户为跨境B2C药品客户：customerId={}", customerId);
            WmPoiSignSubjectBo wmPoiSignSubjectBo = new WmPoiSignSubjectBo();
            wmPoiSignSubjectBo.setCustomerId(wmCustomerDB.getId());
            wmPoiSignSubjectBo.setPartBName(MccConfig.getB2CPartBName());
            wmPoiSignSubjectBo.setPartlogisticsName(ContractSignSubjectEnum.SHENZHEN_SHOUKANG.getDesc());
            return wmPoiSignSubjectBo;
        }

        if (!MccConfig.getXingHuoGrayCustomerIdsBoolean(customerId)) {
            logger.info("客户id不在灰度范围内：customerId={} ", customerId);
            return getUnGraySubjectBo(wmCustomerDB);
        }

        //判断是否在白名单中
        Optional<WmPoiSignSubjectBo> optional = wmContractWhiteListServiceAdapter.getContractWhiteList(wmCustomerDB.getMtCustomerId());
        //从白名单获取
        if (optional.isPresent()) {
            return optional.get();
        }
        //根据客户类型获取
        return getSignSubjectBoByCustomerType(wmCustomerDB);
    }

    //非灰度范围内默认主体
    private WmPoiSignSubjectBo getUnGraySubjectBo(WmCustomerDB wmCustomerDB) {
        WmPoiSignSubjectBo subjectBo = new WmPoiSignSubjectBo();
        subjectBo.setCustomerId(wmCustomerDB.getId());
        subjectBo.setPartBName(ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc());
        subjectBo.setPartlogisticsName(ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc());
        return subjectBo;

    }

    //根据主体规则获取签约主体
    private WmPoiSignSubjectBo getSignSubjectBoByCustomerType(WmCustomerDB wmCustomerDB) {
        WmPoiSignSubjectBo subjectBo = new WmPoiSignSubjectBo();
        subjectBo.setCustomerId(wmCustomerDB.getId());
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue() || wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue()) {
            subjectBo.setPartBName(ContractSignSubjectEnum.SHENZHEN_SHOUKANG.getDesc());
            subjectBo.setPartlogisticsName(ContractSignSubjectEnum.SHENZHEN_SHOUKANG.getDesc());
        } else {
            subjectBo.setPartBName(ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc());
            subjectBo.setPartlogisticsName(ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc());
        }
        return subjectBo;
    }

    /**
     * 修复签约主体数据
     *
     * @param subjectBo
     */
    public BooleanResult fixSubject(WmCustomerPoiLogisticsSubjectBo subjectBo) {
        WmCustomerPoiLogisticsSubjectDB subjectDB = new WmCustomerPoiLogisticsSubjectDB();
        BeanUtils.copyProperties(subjectBo, subjectDB);
        wmCustomerPoiSubjectService.updatePoiSubject(subjectDB);
        return new BooleanResult(true);
    }

    //校验客户主体一致性
    public Boolean checkSubjectConsistency(int customerId, long wmPoiId, int partB, int partLogistics) throws WmCustomerException {
        logger.info("客户签约主体一致性检查：customerId = {},wmPoiId = {},partB = {},partLogistics = {}", customerId, wmPoiId,
                partB, partLogistics);

        //获取最新的一条主体记录校验
        WmCustomerPoiLogisticsSubjectDB subjectDB = wmCustomerPoiSubjectService.getLatestPoiSubject(customerId,
                wmPoiId);
        partB = subjectDB.getPartBNum();
        partLogistics = subjectDB.getPartLogisticsNum();

        List<WmCustomerContractBo> customerContractBoList = this.getAuditedContractBoListByCusIdAndType(customerId,
                Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(),
                        WmTempletContractTypeEnum.C1_PAPER.getCode()),
                0, "frog系统");
        // partB=0 && partLogistics=0，解绑操作或者绑定客户没有生效C1合同
        if (partB == 0 && partLogistics == 0) {
            Set<Integer> customerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
            if (CollectionUtils.isEmpty(customerIdSet)) {
                return Boolean.TRUE;
            }
            if (!CollectionUtils.isEmpty(customerContractBoList) && customerId == Lists.newArrayList(customerIdSet)
                    .get(0)) {
                logger.warn("客户有生效线上合同,但主体为空：customer_id = {}", customerId);
                return Boolean.FALSE;
            }
        } else if (partB != 0 && partLogistics != 0) {
            // partB!=0 && partLogistics!=0,绑定或者生效合同
            if (CollectionUtils.isEmpty(customerContractBoList)) {
                logger.warn("客户没有生效线上合同：customer_id = {}", customerId);
                return Boolean.FALSE;
            }
            WmCustomerContractBo customerContractBo = customerContractBoList.get(0);
            List<WmTempletContractSignBo> signDBList = customerContractBo.getSignBoList();
            if (CollectionUtils.isEmpty(signDBList)) {
                logger.warn("客户没有生效签约人：customer_id = {}", customerId);
                return Boolean.FALSE;
            }
            // 历史数据履约主体为空，拿北京三快做比对
            String logisticsSubject = StringUtils.isEmpty(customerContractBo.getBasicBo().getLogisticsSubject()) ? ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc() : customerContractBo.getBasicBo().getLogisticsSubject();
            String partBName = getPartBName(signDBList);
            if (ContractSignSubjectEnum.getByValue(logisticsSubject).getCode() != partLogistics || ContractSignSubjectEnum.getByValue(partBName).getCode() != partB) {
                logger.warn("客户签约主体不匹配：customer_id = {},partB = {},partLogistics = {}", customerId, partB, partLogistics);
                return Boolean.FALSE;
            }
        } else {
            //其中1个为0，为异常情况
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    //获取合同签约乙方
    public String getPartBName(List<WmTempletContractSignBo> signBoList) {
        for (WmTempletContractSignBo signDB : signBoList) {
            if ("B".equals(signDB.getSignType())) {
                return signDB.getSignName();
            }
        }
        return Strings.EMPTY;
    }

    public RetrySmsResponse resendMsgWithResponse(Long contractId, Integer opUid, String opUname) throws WmCustomerException, TException {
        logger.info("#resendMsgWithResponse contractId = {}, opUid = {}, opUname = {}", contractId, opUid, opUname);
        // 判断合同类型
        WmTempletContractBasicBo wmTempletContractBasicBo = getBasicById(contractId, false, opUid, opUname);
        Preconditions.checkArgument(
                wmTempletContractBasicBo != null && canResendAndCancelContractTypeSet.contains(wmTempletContractBasicBo.getType()),
                CustomerErrorCodeConstants.BIZ_ERROR, "该合同类型不予许重发短信");
        // 判断合同版本
        WmContractVersionDB wmContractVersionDB = wmContractVersionService.getByIdAndTypeMaster(contractId.intValue(), CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
        Preconditions.checkArgument(wmContractVersionDB != null, CustomerErrorCodeConstants.BIZ_ERROR, "合同尚未签约，不可重发短信");
        RetrySmsResponse retrySmsResponse = wmEcontractSignBzService.resendMsg(Long.valueOf(wmContractVersionDB.getTransaction_id()));
        retrySmsResponse.setMobiles(EncryptUtil.encryptMobiles(retrySmsResponse.getMobiles()));
        return retrySmsResponse;
    }

    public BooleanResult cancelSign(Long contractId, Integer opUid, String opUname) throws WmCustomerException, TException {
        logger.info("#cancelSign contractId = {}, opUid = {}, opUname = {}", contractId, opUid, opUname);
        // 判断合同类型
        WmTempletContractBasicBo wmTempletContractBasicBo = getBasicById(contractId, false, opUid, opUname);
        Preconditions.checkArgument(
                wmTempletContractBasicBo != null && canResendAndCancelContractTypeSet.contains(wmTempletContractBasicBo.getType()),
                CustomerErrorCodeConstants.BIZ_ERROR, "该合同类型不予许取消签约");
        // 判断合同版本
        WmContractVersionDB wmContractVersionDB = wmContractVersionService.getByIdAndTypeMaster(contractId.intValue(), CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
        Preconditions.checkArgument(wmContractVersionDB != null, CustomerErrorCodeConstants.BIZ_ERROR, "合同尚未签约，不可取消签约");
        // 取消签约
        BooleanResult result = wmEcontractSignBzService.cancelSign(Long.valueOf(wmContractVersionDB.getTransaction_id()));
        // 操作日志
        WmCustomerOplogBo customerOplogBo = new WmCustomerOplogBo(wmTempletContractBasicBo.getParentId(), WmCustomerOplogBo.OpModuleType.CONTRACT, contractId.intValue());
        String log = "合同编号：" + wmTempletContractBasicBo.getContractNum() + "\n签约失败原因：BD主动取消签约";
        customerOplogBo.setOpType(WmCustomerOplogBo.OpType.CHANGESTATUS.type).setOpUid(opUid).setOpUname(opUname).setLog(log);
        wmCustomerOplogService.insert(customerOplogBo);
        return result;
    }

    public List<WmTempletContractBasicBo> getBasicBoListByIdList(List<Long> contractIdList, boolean isEffective, Integer opUid, String opUname) {
        logger.info("getBasicBoListByIdList contractIdList = {}, isEffective = {}, opUid = {}, opUname = {}", JSON.toJSONString(contractIdList), isEffective, opUid, opUname);
        List<WmTempletContractDB> wmTempletContractDBList;
        if (isEffective) {
            wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByPrimaryKeyList(contractIdList);
        } else {
            wmTempletContractDBList = wmTempletContractDBMapper.selectByPrimaryKeyList(contractIdList);
        }
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    public List<WmTempletContractBasicBo> getBasicBoListByIdListWithoutValid(List<Long> contractIdList, boolean isEffective, Integer opUid, String opUname) {
        logger.info("getBasicBoListByIdListWithoutValid contractIdList = {}, isEffective = {}, opUid = {}, opUname = {}", JSON.toJSONString(contractIdList), isEffective, opUid, opUname);
        List<WmTempletContractDB> wmTempletContractDBList;
        if (isEffective) {
            wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByPrimaryKeyListWithoutValid(contractIdList);
        } else {
            wmTempletContractDBList = wmTempletContractDBMapper.selectByPrimaryKeyListWithoutValid(contractIdList);
        }
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    public LongResult saveAndSignWithExtraData(ContractSaveBo contractSaveBo, Integer opUid, String opUname) throws TException, WmCustomerException {
        logger.info("saveAndSignWithExtraData contractSaveBo = {}, opUid = {}, opUname = {}", JSON.toJSONString(contractSaveBo), opUid, opUname);
        WmCustomerContractBo contractBo = buildWmCustomerContractBo(contractSaveBo);
        logger.info("saveAndSignWithExtraData contractBo = {}, opUid = {}, opUname = {}", JSON.toJSONString(contractSaveBo), opUid, opUname);
        Integer contractId = getService(contractSaveBo.getContractType()).startSign(contractBo, opUid, opUname);
        return new LongResult(contractId);
    }

    private WmCustomerContractBo buildWmCustomerContractBo(ContractSaveBo contractSaveBo) throws WmCustomerException, TException {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        contractBo.setExtraData(contractSaveBo.getExtraData());
        ContractWrapperBuilder.builder()
                .setCustomerId(contractSaveBo.getCustomerId())
                .setContractId(contractSaveBo.getContractId())
                .setContractType(contractSaveBo.getContractType())
                .setContractBo(contractBo)
                .build()
                .wrap();
        return contractBo;
    }

    public BooleanResult invalidByIdList(List<Long> idList, Integer opUid, String opUname) throws WmCustomerException, TException {
        logger.info("invalidByIdList idList = {}, opUid = {}, opUname = {}", JSON.toJSONString(idList), opUid, opUname);
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectByPrimaryKeyList(idList);
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return new BooleanResult(true);
        }

        // 判断操作者opUid是否有权限修改idList中的合同
        for (WmTempletContractDB db : wmTempletContractDBList) {
            int checkAuthPermission = wmCustomerThriftService.checkAuthTypeCommon(opUid, db.getParentId().intValue());
            // 返回值若不是可读可写，则无权限操作该idList
            if (checkAuthPermission != WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode()) {
                if (ConfigUtilAdapter.getBoolean("interface_permission_isolation_switch", false)) {
                    logger.error("invalidByIdList 操作者没有权限修改该合同数据,流程终止 opUid = {}, customerId={}", opUid,
                            db.getParentId()
                                    .intValue());
                    throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "操作者没有权限修改该合同数据");
                } else {
                    logger.info("invalidByIdList 操作者没有权限修改该合同数据,流程继续 opUid = {}, customerId={}", opUid, db.getParentId()
                            .intValue());
                }
            }
        }

        Map<Long, Long> contractIdCusMap = wmTempletContractDBList.stream().collect(Collectors.toMap(item -> item.getId(), item -> item.getParentId()));
        for (WmTempletContractDB db : wmTempletContractDBList) {
            wmTempletContractDBMapper.invalidContract(db.getId(), opUid);
            wmTempletContractSignDBMapper.invalid(db.getId(), opUid);
            contractLogService.logDelete(contractIdCusMap.get(db.getId()).intValue(), db.getId().intValue(), opUid, opUname, "废除合同 " + db.getNumber());
        }

        wmTempletContractDBList = wmTempletContractAuditedDBMapper.selectByPrimaryKeyList(idList);
        for (WmTempletContractDB db : wmTempletContractDBList) {
            wmTempletContractAuditedDBMapper.invalidContract(db.getId(), opUid);
            wmTempletContractSignAuditedDBMapper.invalid(db.getId(), opUid);
        }
        return new BooleanResult(true);
    }

    /**
     * 只查询C1合同
     *
     * @param customerId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void invalidContractAndCancleSign(Integer customerId, Integer opUid, String opUname) throws WmCustomerException, TException {
        logger.info("#invalidContractAndCancleSign customerId = {}, opUid = {}, opUname = {}", customerId, opUid, opUname);
        //查询线下合同
        List<WmTempletContractDB> wmTempletContractDBList = getContractByCustomerIdAndTypes((long) customerId,
                Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode()));

        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            logger.warn("客户下没有合同，无需进行废除:customerId = {}", customerId);
            return;
        }

        WmTempletContractDB contractDB = wmTempletContractDBList.get(0);
        //判断合同状态
        if (contractDB.getStatus() == CustomerContractStatus.WAITING_SIGN.getCode()) {
            //取消待发起签约
            cancelC1SignByWaitingSign(contractDB.getParentId().intValue(), "客户类型切换触发取消签约", opUid, opUname);
        } else if (contractDB.getStatus() == CustomerContractStatus.SIGNING.getCode()) {
            // 取消签约中
            WmContractVersionDB wmContractVersionDB = wmContractVersionService.getByIdAndTypeMaster(contractDB.getId()
                    .intValue(), CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
            if (wmContractVersionDB != null) {
                wmEcontractSignBzService.cancelSign(Long.valueOf(wmContractVersionDB.getTransaction_id()));
                // 操作日志
                WmCustomerOplogBo customerOplogBo = new WmCustomerOplogBo(contractDB.getParentId()
                        .intValue(), WmCustomerOplogBo.OpModuleType.CONTRACT, contractDB.getId().intValue());
                String log = "合同编号：" + contractDB.getNumber() + "\n签约失败原因：系统取消签约";
                customerOplogBo.setOpType(WmCustomerOplogBo.OpType.CHANGESTATUS.type).setOpUid(opUid)
                        .setOpUname(opUname).setLog(log);
                wmCustomerOplogService.insert(customerOplogBo);
            }
        }
        //废除合同
        invalidC1EContract(customerId, opUid, opUname);
    }

    private void invalidC1EContract(Integer customerId, int opUid, String opUname) {
        logger.info("客户类型切换触发废除合同：customerId = {},opUid = {}", customerId, opUid);
        //废除线下合同
        List<WmTempletContractDB> wmTempletContractDBList = getContractByCustomerIdAndTypes((long) customerId,
                Lists.newArrayList(
                        WmTempletContractTypeEnum.C1_E.getCode()));
        if (!CollectionUtils.isEmpty(wmTempletContractDBList)) {
            for (WmTempletContractDB db : wmTempletContractDBList) {
                wmTempletContractDBMapper.invalidContract(db.getId(), opUid);
                wmTempletContractSignDBMapper.invalid(db.getId(), opUid);
                contractLogService.logDelete(customerId, db.getId()
                        .intValue(), opUid, opUname, "废除合同 " + db.getNumber());

            }
        }
        //废除线上合同
        List<WmTempletContractDB> wmTempletContractAuditedDBList = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode()));
        if (!CollectionUtils.isEmpty(wmTempletContractAuditedDBList)) {
            for (WmTempletContractDB db : wmTempletContractAuditedDBList) {
                wmTempletContractAuditedDBMapper.invalidContract(db.getId(), opUid);
                wmTempletContractSignAuditedDBMapper.invalid(db.getId(), opUid);
            }
        }
    }

    public void handleBusinessCustomerContract(Integer wmCustomerId) throws TException, WmCustomerException {
        // 客户是否生效
        WmCustomerDB wmCustomerDB = wmCustomerService.selectEffectCustomerById(wmCustomerId);
        // kp是否生效
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmCustomerId);
        // 调用poibizflow处理企客合同流程
        if (null != wmCustomerDB && null != wmCustomerKp) {
            WmCustomerContractBo contractBo = warpBusinessCustomerSyncData(wmCustomerId);
            if (null == contractBo) {
                logger.info("handleBusinessCustomerContract#客户ID:{}名下无企客合同，不进行数据同步", wmCustomerId);
                return;
            }
            if (!wmBusinessCustomerTempletService.isPushBusinessContractInfoToBanma(contractBo.getBasicBo().getTempletContractId())) {
                logger.info("handleBusinessCustomerContract#客户ID:{}，合同ID:{}不符合推送条件，不进行数据同步", wmCustomerId, contractBo.getBasicBo().getTempletContractId());
                return;
            }
            // 查询外卖客户是否绑定对应的配送客户，如果未绑定是create流程，如果绑定时update流程
            WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType((long) wmCustomerId, CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
            if (null == wmCustomerRelDB || wmCustomerRelDB.getCustomer_biz_id() <= 0) {
                // create流程
                wmBusinessCustomerTempletService.createCustomerDataSyncToBanma(contractBo);
                wmBusinessCustomerTempletService.createContractDataSyncToBanma(contractBo);
                logger.info("handleBusinessCustomerContract#新建企客客户、企客合同数据推送，客户ID:{}，合同ID:{}", wmCustomerId, contractBo.getBasicBo().getTempletContractId());
                // 调用打标平台给客户打标
                wmLeafCustomerRealService.batchAddCustomerLabel((long) contractBo.getBasicBo().getParentId(),
                        com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter.getLong("business_customer_label_id", 0), 0, "客户生效消息");
            } else {
                WmTempletContractExtension wmTempletContractExtension = wmTempletContractExtensionMapper.selectByPrimaryKey(contractBo.getBasicBo().getTempletContractId());
                if (wmTempletContractExtension.getCreateStatus() == INSERT) {
                    // insert合同
                    wmBusinessCustomerTempletService.createContractDataSyncToBanma(contractBo);
                    logger.info("handleBusinessCustomerContract#新建企客合同数据推送，客户ID:{}，合同ID:{}", wmCustomerId, contractBo.getBasicBo().getTempletContractId());
                } else {
                    // update合同
                    wmBusinessCustomerTempletService.updateContractDataSyncToBanma(contractBo, contractBo.getBasicBo().getStatus());
                    logger.info("handleBusinessCustomerContract#更新企客合同数据推送，客户ID:{}，合同ID:{}", wmCustomerId, contractBo.getBasicBo().getTempletContractId());
                }


            }
            if (contractBo.getBasicBo().getTempletContractId() <= 0) {
                logger.warn("handleBusinessCustomerContract#客户ID:{}名下无企客合同，无法更新推送状态", wmCustomerId);
            } else {
                wmBusinessCustomerTempletService.upsertContractPushLog(contractBo.getBasicBo().getTempletContractId(), 1);
            }
        }
    }

    public WmCustomerContractBo warpBusinessCustomerSyncData(Integer wmCustomerId) {
        // 构造合同推送数据
        WmCustomerContractBo wmCustomerContractBo = null;
        List<WmTempletContractDB> contractList = wmTempletContractDBMapper.selectByParentIdAndType((long) wmCustomerId, WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(contractList)) {
            // 数据查询
            WmTempletContractDB businessCustomerContractBasic = contractList.get(0);
            List<WmTempletContractSignDB> contractSigns = wmTempletContractSignDBMapper.selectByWmTempletContractId(businessCustomerContractBasic.getId());
            // 数据转化
            wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(businessCustomerContractBasic, contractSigns);
            logger.info("warpBusinessCustomerSyncData#wmCustomerContractBo:{}", JSON.toJSONString(wmCustomerContractBo));
        } else {
            logger.info("warpBusinessCustomerSyncData#客户{}名下无企客合同", wmCustomerId);
        }
        return wmCustomerContractBo;
    }

    public void saveBusinessCustomerContractExtraData(Long wmCustomerId, String contractExtraContent, boolean internalPdf) throws TException, WmCustomerException {
        wmBusinessCustomerTempletService.saveBusinessCustomerContractExtraData(wmCustomerId, contractExtraContent, internalPdf);
    }


    public void businessCustomerContractApplySign(Long wmCustomerId) throws TException, WmCustomerException {
        wmBusinessCustomerTempletService.businessCustomerContractApplySign(wmCustomerId);
    }

    public void pushBusinessContractEffectResult(Long wmCustomerId, Long psContractVersionId) throws TException, WmCustomerException {
        wmBusinessCustomerTempletService.pushBusinessContractEffectResult(wmCustomerId, psContractVersionId);
    }

    public void pushAbolishBusinessCustomerContractResult(Long wmCustomerId, Boolean abolishResult, String failMsg)
            throws TException, WmCustomerException {
        wmBusinessCustomerTempletService.pushAbolishBusinessCustomerContractResult(wmCustomerId, abolishResult, failMsg);
    }

    public WmTempletContractDB getValidC1Contract(Long wmCustomerId) throws TException, WmCustomerException {
        List<WmTempletContractDB> wmTempletContractAuditedDBS = wmTempletContractAuditedDBMapper
                .selectByParentIdAndTypes(wmCustomerId,
                        Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(),
                                WmTempletContractTypeEnum.C1_PAPER.getCode()));
        if (!CollectionUtils.isEmpty(wmTempletContractAuditedDBS)) {
            return wmTempletContractAuditedDBS.get(0);
        }
        logger.info("createCustomerDataSyncToBanma#客户:{}名下无有效C1合同", wmCustomerId);
        return null;
    }

    public Long queryPsContractVersionId(Integer wmCustomerId) throws TException, WmCustomerException {
        if (wmCustomerId <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户ID无效");
        }
        WmTempletContractDB businessCustomerContractBasic = null;
        // 先查线上表
        List<WmTempletContractDB> auditedContractList = wmTempletContractAuditedDBMapper.selectValidByParentIdAndTypes((long) wmCustomerId,
                Lists.newArrayList(WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode(), WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode()));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(auditedContractList)) {
            logger.info("audit表无生效企客合同，客户:{}", wmCustomerId);
            List<WmTempletContractDB> templetContractList = wmTempletContractDBMapper.selectValidByParentIdAndTypes((long) wmCustomerId,
                    Lists.newArrayList(WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode(), WmTempletContractTypeEnum.BUSINESS_CUSTOMER_E.getCode()));
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(templetContractList)) {
                businessCustomerContractBasic = templetContractList.get(0);
            }
        } else {
            businessCustomerContractBasic = auditedContractList.get(0);
        }
        if (null == businessCustomerContractBasic) {
            logger.info("queryPsContractVersionId#客户:{}名下无企客合同", wmCustomerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户名下无合法企客合同");
        }
        logger.info("客户:{}对应企客合同:{}", wmCustomerId, JSON.toJSONString(businessCustomerContractBasic));
        Long psContractVersionId = null;
        WmTempletContractRel wmTempletContractRel =
                wmTempletContractRelMapper.selectByWmContractIdAndBizType(businessCustomerContractBasic.getId(), ContractBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (null != wmTempletContractRel) {
            psContractVersionId = wmTempletContractRel.getContract_biz_id();
        }
        logger.info("企客合同:{}对应的配送cvid:{}", businessCustomerContractBasic.getId(), wmTempletContractRel == null ? "无对应cvid" : wmTempletContractRel.getContract_biz_id());
        return psContractVersionId;
    }

    public Long pushBusinessCustomerPaperContractInfo(PushBusinessCustomerPaperContractInfoV2 req)
            throws TException, WmCustomerException {
        Long wmCustomerId = req.getWmCustomerId();
        Long psContractVersionId = req.getPsContractVersionId();
        Long dueDate = req.getDueDate();
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo wmTempletContractBasicBo = new WmTempletContractBasicBo();
        wmTempletContractBasicBo.setParentId(wmCustomerId.intValue());
        wmTempletContractBasicBo.setType(WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode());
        if(dueDate!=null && dueDate>0){
            wmTempletContractBasicBo.setDueDate(dueDate);
        }
        wmCustomerContractBo.setBasicBo(wmTempletContractBasicBo);
        // 纸质合同校验
        ContractCheckFilter.businessCustomerContractPaperSaveFilter().filter(wmCustomerContractBo, 0, "");
        logger.info("pushBusinessCustomerPaperContractInfo#纸质合同创建，外卖客户id:{}，配送合同版本id:{}，req:{}", wmCustomerId, psContractVersionId,JSON.toJSONString(req));
        return wmBusinessCustomerTempletService.pushBusinessCustomerPaperContractInfo(wmCustomerId, psContractVersionId,dueDate);
    }

    public Long getWmContractIdByWmCustomerId(Long wmCustomerId) throws TException, WmCustomerException {
        if (wmCustomerId <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "外卖客户ID无效");
        }
        List<WmTempletContractDB> contractList = wmTempletContractDBMapper.selectByParentIdAndType(wmCustomerId, WmTempletContractTypeEnum.BUSINESS_CUSTOMER_PAPER.getCode());
        List<WmTempletContractDB> removeInvalidContractList = contractList.stream().filter(contract -> contract.getStatus() != CustomerContractStatus.INVALID.getCode()).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(removeInvalidContractList)) {
            WmTempletContractDB businessCustomerContractBasic = contractList.get(0);
            return businessCustomerContractBasic.getId();
        } else {
            logger.info("queryPsContractVersionId#客户{}名下无有效企客合同", wmCustomerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户名下无有效企客合同");
        }
    }

    public Integer queryC1ContractEndDate(Long wmCustomerId) throws WmCustomerException, TException {
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(new Long(wmCustomerId).intValue(),
                Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(), WmTempletContractTypeEnum.C1_PAPER.getCode()));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmTempletContractDBList)) {
            WmTempletContractDB wmTempletContractDB = wmTempletContractDBList.get(0);
            return wmTempletContractDB == null ? null : wmTempletContractDB.getDueDate();
        } else {
            logger.info("queryC1ContractEndDate#客户{}名下无有效C1合同", wmCustomerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户名下无有效C1合同");
        }
    }

    public void pushC1ContractEffectInfo(Integer wmContractId) throws WmCustomerException, TException {
        wmBusinessCustomerTempletService.pushC1ContractEffectInfo(wmContractId);
    }

    public void cancelBusinessCustomerSign(Integer wmContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        wmBusinessCustomerTempletService.cancelSign(wmContractId, failReason, opUid, opUname);
    }

    public void batchAddWmCustomerPoiLogisticsSubjectWithTag(List<Long> wmPoiIds, int tagId,
                                                             int effectTime) {
        if (CollectionUtils.isEmpty(wmPoiIds)) {
            return;
        }
        for (Long wmPoiId : wmPoiIds) {
            handleService.execute(() -> {
                try {
                    initPoiSubjectDataWithTagId(wmPoiId, tagId, effectTime);
                } catch (Exception e) {
                    logger.error("初始化门店主体快照异常：wmPoiId= {}", wmPoiId, e);
                }
            });
        }
    }

    private Map<Integer, List<WmTempletContractDB>> assemblyContractMap(List<WmTempletContractDB> allContract) {
        Map<Integer, List<WmTempletContractDB>> contractMap = new HashMap<>();
        if (CollectionUtils.isEmpty(allContract)) {
            return contractMap;
        }
        for (WmTempletContractDB contract : allContract) {
            int typeInDB = new WmTempletContractTypeBo(contract.getType()).getType();
            if (contractMap.get(typeInDB) == null) {
                List<WmTempletContractDB> tempContract = new ArrayList<>();
                tempContract.add(contract);
                contractMap.put(typeInDB, tempContract);
            } else {
                contractMap.get(typeInDB).add(contract);
            }
        }
        return contractMap;
    }

    private List<WmTempletContractDB> filterC2Contract(List<WmTempletContractDB> c2Contract, int agentId) {
        List<WmTempletContractDB> c2ContractForAgent = new ArrayList<>();
        if (CollectionUtils.isEmpty(c2Contract)) {
            return c2ContractForAgent;
        }
        for (WmTempletContractDB contract : c2Contract) {
            WmTempletContractSignBo partyBSigner;
            if(contract.getId()%100 < MccGrayConfig.contractOnlineCheckWithoutSignPhoneGray()){
                partyBSigner = wmContractSignService.getAuditedPartyBSignerWithOutSignPhone(contract.getId());
                Cat.logEvent(MetricConstant.ONLINE_CHECK_LIST,
                        "withoutSignPhone",
                        WmCustomerConstant.SUCCESS, "");

            }else {
                partyBSigner = wmContractSignService.getAuditedPartyBSigner(contract.getId());
                Cat.logEvent(MetricConstant.ONLINE_CHECK_LIST,
                        "withSignPhone",
                        WmCustomerConstant.SUCCESS, "");
            }
            if (partyBSigner.getSignId() == agentId) {
                c2ContractForAgent.add(contract);
            }
        }
        return c2ContractForAgent;
    }

    public List<WmTempletContractBasicBo> selectByParentIdAndTypesAndStatus(Long parentId, List<Integer> types,
                                                                            Integer status) {
        List<WmTempletContractDB> wmTempletContractDBList =
                wmTempletContractAuditedDBMapper.selectByParentIdAndTypesAndStatus(parentId, types, status);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    public boolean isCanPackSignByTemplateType(int templateType) {
        String templateTypeStr = MccConfig.packSignGrayTemplateType();
        List<String> templateTypeList = Arrays.asList(templateTypeStr.split(","));
        if (templateTypeList.contains(String.valueOf(templateType))) {
            return true;
        }
        return false;
    }

    /**
     * 根据门店ID获取某类型合同的状态
     *
     * @param wmPoiIdList
     * @param type
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    public Map<Long, CustomerContractStatus> queryContractStatusByWmPoiIdAndType(List<Long> wmPoiIdList,
                                                                                 int type) throws
            WmCustomerException, TException {
        logger.info("根据门店ID列表获取对应类型合同状态 wmPoiIdList={}, type={}", wmPoiIdList, type);
        Map<Long, CustomerContractStatus> contractStatusMap = new HashMap<>();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return contractStatusMap;
        }
        // 根据门店ID列表获取门店ID->客户ID映射
        Map<Long, Integer> wmPoiIdCustomerIdMap = wmCustomerPoiService.selectByWmPoiIdList(wmPoiIdList);
        // 获取客户ID列表
        List<Long> wmCustomerIdList = Lists.newArrayList();
        wmPoiIdCustomerIdMap.entrySet().stream().forEach(item -> wmCustomerIdList.add(item.getValue().longValue()));
        if (CollectionUtils.isEmpty(wmCustomerIdList)) {
            return contractStatusMap;
        }

        // 根据客户ID列表获取线上合同信息
        List<WmTempletContractDB> wmTempletContractAuditDBList =
                wmTempletContractAuditedDBMapper.selectByParentIdsAndTypes(wmCustomerIdList, Arrays.asList(type));
        Map<Long, WmTempletContractDB> wmTempletContractAuditDBMap = wmTempletContractAuditDBList.stream()
                .collect(Collectors.toMap(item -> item.getParentId(), item -> item, (key1, key2) -> key2));
        logger.info("#queryContractStatusByWmPoiIdAndType wmTempletContractAuditDBMap = {}",
                JSON.toJSONString(wmTempletContractAuditDBMap));

        // 根据客户ID列表获取线下合同信息
        List<WmTempletContractDB> wmTempletContractDBList =
                wmTempletContractDBMapper.selectByParentIdsAndTypes(wmCustomerIdList, Arrays.asList(type));
        Map<Long, WmTempletContractDB> wmTempletContractDBMap = wmTempletContractDBList.stream()
                .collect(Collectors.toMap(item -> item.getParentId(), item -> item, (key1, key2) -> key2));
        logger.info("#queryContractStatusByWmPoiIdAndType wmTempletContractDBMap = {}",
                JSON.toJSONString(wmTempletContractDBMap));

        // 遍历门店列表，设置各门店某类型合同状态
        for (Long wmPoiId : wmPoiIdList) {
            // 获取门店对应客户ID
            Long wmCustomerId = wmPoiIdCustomerIdMap.get(wmPoiId).longValue();
            // 优先取线上，线上存在则存在存在生效
            if (wmTempletContractAuditDBMap.get(wmCustomerId) != null) {
                contractStatusMap.put(wmPoiId, CustomerContractStatus.EFFECT);
                continue;
            }
            // 其次取下线，判断是否进行中
            if (wmTempletContractDBMap.get(wmCustomerId) != null
                    && wmTempletContractDBMap.get(wmCustomerId)
                    .getStatus() == CustomerContractStatus.SIGNING.getCode()) {
                contractStatusMap.put(wmPoiId, CustomerContractStatus.SIGNING);
                continue;
            }
            // 无线上or线下进行中，则认为未签约
            contractStatusMap.put(wmPoiId, CustomerContractStatus.NO_DATA);
        }
        return contractStatusMap;
    }

    /**
     * 根据门店ID和业务ID发起配送产品临时调整补充协议
     *
     * @param wmPoiId
     * @param bizId
     * @throws WmCustomerException
     * @throws TException
     */
    public CommonResult applyInterimSelfAgreementByWmPoiId(Long wmPoiId, Long bizId) throws
            WmCustomerException, TException {

        // 根据门店ID获取门店客户信息
        int customerId = wmLeafCustomerRealService.selectWmCustomerIdByWmPoiId(wmPoiId);

        // 校验是否电子签约客户，否则抛异常
        WmCustomerDB customerDB = wmCustomerService.selectCustomerById(customerId);
        if (customerDB.getSignMode() == CustomerSignMode.PAPER.getCode()) {
            return new CommonResult(1, "纸质合同无法在线上发起临时切自配协议的创建");
        }

        // 判断客户下是否有生效的配送产品临时调整补充协议
        List<WmTempletContractDB> wmTempletContractAuditDBList = wmTempletContractAuditedDBMapper
                .selectByParentIdAndTypesMaster(Long.valueOf(customerId),
                        Arrays.asList(WmTempletContractTypeEnum.INTERIM_SELF_E.getCode()));
        // 若存在生效的协议，则直接回调配送侧
        if (!CollectionUtils.isEmpty(wmTempletContractAuditDBList)) {
            banmaOpenPoiClient.callbackContract(wmPoiId, bizId, 1);
            return new CommonResult(0, "");
        }

        // 判断客户下是否有进行中的配送产品临时调整补充协议
        List<WmTempletContractDB> wmTempletContractDBList =
                wmTempletContractDBMapper.selectByParentIdAndTypesMaster(
                        Long.valueOf(customerId), Arrays.asList(WmTempletContractTypeEnum.INTERIM_SELF_E.getCode()));
        if (!CollectionUtils.isEmpty(wmTempletContractDBList)) {
            // 若存在进行中的协议，则更新bizData
            if (wmTempletContractDBList.get(0).getStatus() == CustomerContractStatus.SIGNING.getCode()) {
                updateBizData(wmTempletContractDBList.get(0).getId(), wmPoiId, bizId);
                return new CommonResult(0, "");
            }
            // 若存在已取消的协议，则在原合同基础上进行保存
            if (wmTempletContractDBList.get(0).getStatus() == CustomerContractStatus.SIGN_FAIL.getCode()) {
                WmCustomerContractBo wmCustomerContractBo = initSaveContractBoForInterimSelf(customerId, wmPoiId,
                        bizId, wmTempletContractDBList.get(0));
                saveAndStartSign(wmCustomerContractBo, 0, "烽火台");
                return new CommonResult(0, "");
            }
        }

        // 无线上or线下数据，则生成新的签约记录
        WmCustomerContractBo wmCustomerContractBo = initSaveContractBoForInterimSelf(customerId, wmPoiId, bizId,
                null);
        saveAndStartSign(wmCustomerContractBo, 0, "烽火台");
        return new CommonResult(0, "");
    }

    private WmCustomerContractBo initSaveContractBoForInterimSelf(Integer customerId, Long wmPoiId, Long
            bizId, WmTempletContractDB oldContractDB) throws WmCustomerException, TException {
        logger.info("自动发起配送产品临时调整补充协议-填充数据 初始化 customerId:{}", customerId);
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(customerId);
        // 基础Bo
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        // 插入
        if (oldContractDB == null) {
            basicBo.setContractNum("电子合同保存后自动生成编号");
        } else {
            basicBo.setTempletContractId(oldContractDB.getId());
            basicBo.setContractNum(oldContractDB.getNumber());
        }
        basicBo.setDueDate(0);//长期有效期
        CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
        basicBo.setExtStr(JSON.toJSONString(paperContractRemarkBo));
        basicBo.setParentId(customerId);
        basicBo.setType(WmTempletContractTypeEnum.INTERIM_SELF_E.getCode());
        Map<String, String> wmPoiIdBizIdMap = new HashMap<>();
        wmPoiIdBizIdMap.put(String.valueOf(wmPoiId), String.valueOf(bizId));
        basicBo.setBizDate(JSON.toJSONString(wmPoiIdBizIdMap));

        //签约甲乙方Bo
        List<WmTempletContractSignBo> signList = Lists.newArrayList();
        //甲方
        WmTempletContractSignBo partyA = new WmTempletContractSignBo();
        partyA.setSignId(customerId);
        partyA.setSignName(wmCustomerDB.getCustomerName());
        partyA.setSignPeople(wmCustomerKp.getCompellation());
        partyA.setSignPhone(wmCustomerKp.getPhoneNum());
        partyA.setSignTime(DateUtils.getNDay(0));
        partyA.setSignType("A");
        signList.add(partyA);
        //乙方
        WmTempletContractSignBo partyB = new WmTempletContractSignBo();
        partyB.setSignId(0);
        partyB.setSignName(SH_SANKUAI_ZHISONG);
        partyB.setSignPeople("烽火台");
        partyB.setSignPhone("");
        partyB.setSignTime(DateUtils.getNDay(0));
        partyB.setSignType("B");
        signList.add(partyB);

        //封装
        contractBo.setBasicBo(basicBo);
        contractBo.setSignBoList(signList);
        contractBo.setIgnoreExistAnotherSignTypeContract(true);
        contractBo.setPackWay(SignPackWay.DO_SIGN.getCode());

        return contractBo;
    }

    private void updateBizData(Long templetContractId, Long wmPoiId, Long bizId) throws
            WmCustomerException, TException {
        try {
            int update = 0;
            for (int i = 0; i < MccConfig.bizDataRetryTimes(); i++) {
                WmTempletContractDB wmTempletContractDB =
                        wmTempletContractDBMapper.selectByPrimaryKeyMaster(templetContractId);
                Map<String, String> wmPoiIdBizIdMap = new HashMap<>();
                if (StringUtils.isEmpty(wmTempletContractDB.getBizData())) {
                    wmPoiIdBizIdMap.put(String.valueOf(wmPoiId), String.valueOf(bizId));
                } else {
                    wmPoiIdBizIdMap = JSON.parseObject(wmTempletContractDB.getBizData(), HashMap.class);
                    wmPoiIdBizIdMap.put(String.valueOf(wmPoiId), String.valueOf(bizId));
                }
                update = wmTempletContractDBMapper.updateBizDataById(templetContractId,
                        wmTempletContractDB.getVersion(), JSON.toJSONString(wmPoiIdBizIdMap));
                if (update > 0) {
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("#updateBizDate Exception templetContractId:{}, wmPoiId:{}, bizId:{}", templetContractId,
                    wmPoiId, bizId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "更新门店信息异常");
        }
    }

    private boolean checkIsB2CCustomer(Integer customerId) throws TException, WmCustomerException {
        logger.info("判断客户是否B2C医药客户：customerId={}", customerId);
        if (customerId == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN, "判断客户类型客户id为空");
        }
        WmCustomerBasicBo customerBasicBo = wmCustomerService.getCustomerById(customerId);
        if (customerBasicBo == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN, "客户不存在");
        }
        return customerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.B2C_DRUG.getValue();
    }


    public StartSignByPoiResp startSignByPoi(StartSignByPoiReqParam requestParam) {
        WmCustomerDB wmCustomerDB;//客户信息
        List<WmTempletContractDB> wmTempletContractAuditDBList;//生效合同信息
        List<WmTempletContractDB> wmTempletContractDBList;//合同信息
        String signPhoneNum = org.apache.commons.lang3.StringUtils.EMPTY;//返回手机号
        try {
            //入参校验
            wmPoiPromotionServiceEContractTempletService.poiPromotionContractSignParamValid(requestParam);
            //基础数据准备
            wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(requestParam.getPoiId());
            wmTempletContractAuditDBList = wmTempletContractAuditedDBMapper.selectByParentIdAndTypes(
                    (long) requestParam.getContractBo().getBasicBo().getParentId(),
                    Lists.newArrayList(requestParam.getContractBo().getBasicBo().getType()));
            wmTempletContractDBList = wmTempletContractDBMapper.selectByParentIdAndTypes(
                    (long) requestParam.getContractBo().getBasicBo().getParentId(),
                    Lists.newArrayList(requestParam.getContractBo().getBasicBo().getType()));

            //获取kp手机号
            WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmCustomerDB.getId());
            if (wmCustomerKp != null) {
                signPhoneNum = wmCustomerKp.getPhoneNum()
                        .length() < 4 ? wmCustomerKp.getPhoneNum() : wmCustomerKp.getPhoneNum()
                        .substring(wmCustomerKp.getPhoneNum().length() - 4);
            }

            //客户合法性校验（4204104，4204105）
            wmContractCustomerService.poiPromotionContractCustomerValid(wmCustomerDB);

            //判断是否已签署新主体（北京三快网络）的合同（4204101）
            wmPoiPromotionServiceEContractTempletService.poiPromotionContractSignSubjectValid(wmTempletContractAuditDBList);

            //处理同一客户下的其他门店已发起签约中的任务（4204102，4204103）
            boolean isResendMsg =
                    wmPoiPromotionServiceEContractTempletService.poiPromotionContractProcessingHandle(wmTempletContractDBList.get(0));
            if (isResendMsg) {
                return new StartSignByPoiResp(PoiPromotionContractEnum.SUCCESS.getCode(),
                        PoiPromotionContractEnum.SUCCESS.getDesc(), signPhoneNum);
            }

            //合同合法性校验（4204106，4204107）
            wmPoiPromotionServiceEContractTempletService.poiPromotionContractCommonValid(wmTempletContractDBList,
                    requestParam.getContractBo());

            //发起换签
            saveAndStartSign(requestParam.getContractBo(), requestParam.getOpUid(), requestParam.getOpName());
        } catch (WmCustomerException customerException) {
            logger.warn("门店推广合同发起续签失败，poiId:{}，code:{}，msg:{}",
                    requestParam.getPoiId(), customerException.getCode(), customerException.getMessage());
            return new StartSignByPoiResp(customerException.getCode(), customerException.getMessage());
        } catch (Exception exception) {
            logger.error("门店推广合同发起续签系统异常，poiId:{}", requestParam.getPoiId(), exception);
            return new StartSignByPoiResp(PoiPromotionContractEnum.ERROR.getCode(),
                    PoiPromotionContractEnum.ERROR.getDesc());
        }

        return new StartSignByPoiResp(PoiPromotionContractEnum.SUCCESS.getCode(),
                PoiPromotionContractEnum.SUCCESS.getDesc(), signPhoneNum);
    }


    public List<WmCustomerContractBo> getWmCustomerContractBoByPoiId(QueryContractByPoiIdReqParam requestParam) throws WmCustomerException, TException {
        Long poiId = requestParam.getPoiId();
        List<WmCustomerContractBo> resultList = Lists.newArrayList();
        try {
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(poiId);
            if (wmCustomerDB == null) {
                throw new WmCustomerException(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无对应客户");
            }
            logger.info("根据门店查询合同，客户id={}", wmCustomerDB.getId());
            boolean isEffective = requestParam.getIsEffective();
            if (isEffective) {
                List<WmTempletContractDB> wmTempletAuditedContractDBList =
                        wmTempletContractAuditedDBMapper.selectByParentIdAndTypes(new Long(wmCustomerDB.getId()),
                                requestParam.getContractTypes());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmTempletAuditedContractDBList)) {
//                    logger.info("#getWmCustomerContractBoByPoiId#wmTempletAuditedContractDBList#size:{},customerId:{}", wmTempletAuditedContractDBList.size(), wmCustomerDB.getId());
                    wmTempletAuditedContractDBList.stream().forEach(auditedContractDB -> {
//                        logger.info("#getWmCustomerContractBoByPoiId#selectByWmTempletContractId customerId:{}, contracId:{}", wmCustomerDB.getId(), auditedContractDB.getId());
                        WmCustomerContractBo resultBo = new WmCustomerContractBo();
                        List<WmTempletContractSignDB> contractAuditedSignDBs =
                                wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(auditedContractDB.getId());
                        resultBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(auditedContractDB));
                        resultBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(contractAuditedSignDBs));
                        resultList.add(resultBo);
                    });
                }
            } else {
                List<WmTempletContractDB> wmTempletContractDBList =
                        wmTempletContractDBMapper.selectByParentIdAndTypes(new Long(wmCustomerDB.getId()),
                                requestParam.getContractTypes());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmTempletContractDBList)) {
                    wmTempletContractDBList.stream().forEach(contractDB -> {
                        WmCustomerContractBo resultBo = new WmCustomerContractBo();
                        List<WmTempletContractSignDB> contractSignDBs =
                                wmTempletContractSignDBMapper.selectByWmTempletContractId(contractDB.getId());
                        resultBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(contractDB));
                        resultBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(contractSignDBs));
                        resultList.add(resultBo);
                    });
                }
            }
            logger.info("根据门店查询合同，resultList={}", JSON.toJSONString(resultList));
        } catch (Exception e) {
            logger.warn("getWmCustomerContractBoByPoiId error，poiId={}", requestParam.getPoiId(), e);
            throw new WmCustomerException(CustomerContractRetCodeEnum.SYSTEM_ERROR.getCode(), "获取合同数据异常");
        }
        return resultList;
    }

    /**
     * 是否是特殊子门店
     * @param wmPoiId
     * @return
     */
    private boolean isSubPoi(long wmPoiId) throws WmCustomerException{
        boolean result = false;
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId);
        List<String> poiLabelList = Splitter.on(",").trimResults().splitToList(wmPoiAggre.getLabel_ids());
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(poiLabelList)){
            List<String> specialSubTagList = MccConfig.getSpecialSubPoiTagIds();
            result = org.apache.commons.collections4.CollectionUtils.containsAny(poiLabelList, specialSubTagList);
        }
        logger.info("isSubPoi wmPoiId:{},result:{}",wmPoiAggre.getWm_poi_id(),result);
        return result;
    }


    private boolean isShareQualificationCustomer(int customerId) throws TException, WmCustomerException {
        int labelIdList = com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter.getInt("share_qualification_customer_labelid", 717);
        WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerById(customerId);
        boolean hasTargetLabel = wmLabelAdaptor.hasTargetLable(Lists.newArrayList(wmCustomerBasicBo.getMtCustomerId()), LabelSubjectTypeEnum.CUSTOMER.getCode(), labelIdList);
        return hasTargetLabel;
    }

    private boolean isShareQualificationPoi(long wmPoiId) throws TException, WmCustomerException {
        int labelIdList = com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter.getInt("share_qualification_poi_labelid", 718);
        boolean hasTargetLabel = wmLabelAdaptor.hasTargetLable(Lists.newArrayList(wmPoiId), LabelSubjectTypeEnum.POI.getCode(), labelIdList);
        return hasTargetLabel;
    }


    /**
     * 闪购低费率换签接口
     * @param param
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public Boolean sgResignLowFee(SgResignLowFeeParam param) throws TException, WmCustomerException {
        logger.info("WmContractService#sgResignLowFee(), param:{}", JSON.toJSONString(param));
        checkSgResignLowFeeParam(param);
        // 写操作，校验权限
        checkContractAuth(param.getCustomerId(), param.getOpUid(), WRITE_OPERATION);

        // 闪购侧接口-换签费率
        ResignReq resignReq = buildResignReq(ContractResignSourceEnum.C1_DUE_RENEW, param);
        logger.info("WmContractService#sgResignLowFe, resignReq: {}", JSON.toJSONString(resignReq));
        sgRenewExecutorService.execute(() -> sgPoiContractClient.resign(resignReq));

        return true;
    }

    private void checkSgResignLowFeeParam(SgResignLowFeeParam param) throws WmCustomerException {
        Preconditions.checkArgument(Objects.nonNull(param), -1, "参数不能为空");
        Preconditions.checkArgument(Objects.nonNull(param.getCustomerId()) && param.getCustomerId() >= 0, -1, "customerId 错误");
    }

    private ResignReq buildResignReq(ContractResignSourceEnum contractResignSourceEnum, SgResignLowFeeParam param) {
        return ResignReq.builder()
                .sourceId(contractResignSourceEnum)
                .customerId(param.getCustomerId())
                .build();
    }

    public void checkContractAuth(int wmCustomerId, int userId, int opType) throws TException, WmCustomerException {
        logger.info("#合同信息权限校验新接口 wmCustomerId={}, userId={}, opType={}", wmCustomerId, userId, opType);
        int authType = wmCustomerThriftService.checkAuthTypeCommon(userId, wmCustomerId);
        logger.info("任务列表权限校验新接口 userId={} authType:{}", userId, authType);
        // 读写权限，直接返回
        if (authType == WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode()) {
            return;
        }
        // 读权限且操作为读，直接返回
        if (authType == WmCustomerAuthTypeEnum.READ_ONLY.getCode() && opType == READ_OPERATION) {
            return;
        }
        // 其他情况直接抛异常
        throw new WmCustomerException(WmContractErrorCodeConstant.AUTHORIZING, "您不是门店所属客户的客户责任人，没有权限操作此页面");
    }

    public BaseResponse<Boolean> invalidCustomerContract(CustomerContractInvalidRequestDTO requestDTO) {
        try {
            logger.info("WmContractService#invalidCustomerContract, requestDTO: {}", JSON.toJSONString(requestDTO));
            checkCustomerContractInvalidRequestDTO(requestDTO);
            boolean invalid = getService(requestDTO.getContractType()).invalid(requestDTO.getContractId(),
                    Math.toIntExact(requestDTO.getOperatorDTO().getOpId()), requestDTO.getOperatorDTO().getOpName());
            if (invalid) {
                return BaseResponse.success(true);
            } else {
                return BaseResponse.error(WmContractErrorCodeConstant.SYSTEM_ERROR, "合同失效异常");
            }
        } catch (WmCustomerException e) {
            logger.warn("WmContractService#invalidCustomerContract, warn", e);
            return BaseResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("WmContractService#invalidCustomerContract, error", e);
            return BaseResponse.error(WmContractErrorCodeConstant.SYSTEM_ERROR, "系统异常");
        }
    }

    private void checkCustomerContractInvalidRequestDTO(CustomerContractInvalidRequestDTO requestDTO) throws WmCustomerException {
        if (requestDTO == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.ERROR_GET_PARAM, "参数异常");
        }
        if (requestDTO.getContractId() == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.ERROR_GET_PARAM, "合同ID为空");
        }
        if (requestDTO.getContractType() == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.ERROR_GET_PARAM, "合同类型为空");
        }
        if (requestDTO.getOperatorDTO() == null || requestDTO.getOperatorDTO().getOpId() == null || Strings.isNullOrEmpty(requestDTO.getOperatorDTO().getOpName())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.ERROR_GET_PARAM, "操作人信息异常");
        }
    }

    public Map<Integer, Boolean> expireCustomerContract(CustomerContractExpireRequestDTO requestDTO) throws WmCustomerException {
        try {
            logger.info("WmContractService#expireCustomerContract, requestDTO: {}", JSON.toJSONString(requestDTO));
            checkCustomerContractExpireRequestDTO(requestDTO);
            return getService(requestDTO.getContractType()).expire(requestDTO.getContractIdList(),
                    Math.toIntExact(requestDTO.getOperatorDTO().getOpId()), requestDTO.getOperatorDTO().getOpName());
        } catch (WmCustomerException e) {
            logger.warn("WmContractService#expireCustomerContract, WmCustomerException", e);
            throw e;
        } catch (TException e) {
            logger.warn("WmContractService#expireCustomerContract, TException", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMessage());
        } catch (Exception e) {
            logger.warn("WmContractService#expireCustomerContract, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.SYSTEM_ERROR, "操作失败");
        }
    }

    private void checkCustomerContractExpireRequestDTO(CustomerContractExpireRequestDTO requestDTO) throws WmCustomerException {
        if (requestDTO == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.ERROR_GET_PARAM, "参数异常");
        }
        if (requestDTO.getContractIdList() == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.ERROR_GET_PARAM, "合同ID为空");
        }
        if (requestDTO.getContractType() == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.ERROR_GET_PARAM, "合同类型为空");
        }
        if (requestDTO.getOperatorDTO() == null || requestDTO.getOperatorDTO().getOpId() == null || Strings.isNullOrEmpty(requestDTO.getOperatorDTO().getOpName())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.ERROR_GET_PARAM, "操作人信息异常");
        }
    }
}
