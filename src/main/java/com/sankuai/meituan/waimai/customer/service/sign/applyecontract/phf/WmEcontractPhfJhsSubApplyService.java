package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTSHWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignFlowConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 聚好送，签约数据组装类
 * @author: zhangyuanhao02
 * @create: 2024/12/19 20:05
 */
@Slf4j
@Service
public class WmEcontractPhfJhsSubApplyService extends AbstractWmEcontractPhfSubApplyAdapterService{

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Resource
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Resource
    private WmEcontractStampMTSHWrapperService wmEcontractStampMTSHWrapperService;

    @Override
    public EcontractTaskApplySubTypeEnum getSubTypeEnum() {
        return EcontractTaskApplySubTypeEnum.JHS;
    }

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        List<String> flowList = getFlowList(batchContextBo);

        // PDF参数
        StageBatchInfoBo createPdfStageBatchInfoBo = wmEcontractDateWrapperService.wrapMultiContractWithSingleTemplate(
                batchContextBo, flowList, EcontractDataWrapperEnum.JHS);
        batchInfoBoList.add(createPdfStageBatchInfoBo);

        // CA认证参数
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));

        // 商家签章
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, flowList));

        // 北京三快
        batchInfoBoList.add(wmEcontractStampMTWrapperService.wrap(batchContextBo, flowList));

        // 上海三快
        batchInfoBoList.add(wmEcontractStampMTSHWrapperService.wrap(batchContextBo, flowList));

        // 短信参数
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));

        EcontractBatchBo econtractBatchBo =  new EcontractBatchBo.Builder()
                .token(MccConfig.getPhfEcontractToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(SignFlowConstant.TYPE_PHF_BATCH_SIGN_FLOW)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .econtractBatchSource(getSource(batchContextBo))
                .build();
        return econtractBatchBo;
    }

}
