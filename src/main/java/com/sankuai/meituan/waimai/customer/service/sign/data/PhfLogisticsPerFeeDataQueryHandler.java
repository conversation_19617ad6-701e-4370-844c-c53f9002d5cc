package com.sankuai.meituan.waimai.customer.service.sign.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractBatchDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractDeliverySecondProductPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractSingleDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractSingleDeliverySecondProductPerInfoBo;
import com.sankuai.meituan.banma.thrift.deliveryproduct.common.PhfProductPackUtils;
import com.sankuai.meituan.waimai.customer.adapter.DeliveryContractAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.data.util.LogisticsFeeDataQueryUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CChargeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.single.EcontractSingleDeliveryBaseAndFeeInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 查询拼好饭签约数据（履约）
 * @author: zhangyuanhao02
 * @create: 2025/2/11 11:12
 */
@Slf4j
@Service
public class PhfLogisticsPerFeeDataQueryHandler implements EcontractDataQueryHandler<EcontractDeliveryInfoBo> {
    @Autowired
    private DeliveryContractAdapter deliveryContractAdapter;


    private static final ExecutorService executorService = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(
            10, 32,
            5L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactoryBuilder().setNameFormat("phf-per-fee-data-query-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy()
    ));

    @Override
    public EcontractDataSourceEnum sorceEnum() {
        return EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE;
    }

    @Override
    public Map<Long, EcontractDeliveryInfoBo> queryData(Map<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> map, long manualBatchId) throws WmCustomerException {
        throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "方法未实现");
    }

    @Override
    public void queryAndAssembleData(Map<EcontractDataSourceEnum, EcontractDataSourceBo> map, long manualBatchId, Map<Long, EcontractDeliveryInfoBo> resultMap) throws WmCustomerException {
        log.info("#PhfLogisticsPerFeeDataQueryHandler queryData request map:{}, manualBatchId:{}", JSON.toJSONString(map), manualBatchId);

        // 校验对应来源的门店数据是否正确
        EcontractDataSourceBo dataSourceBo = map.get(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE);
        if (Objects.isNull(dataSourceBo) || CollectionUtils.isEmpty(dataSourceBo.getWmPoiIdAndBizIdList())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "履约服务费数据来源无对应门店数据");
        }

        List<EcontractDataPoiBizBo> wmPoiIdAndBizIdList = dataSourceBo.getWmPoiIdAndBizIdList();
        boolean needAssembleWmPoiGroup = wmPoiIdAndBizIdList.size() == 1 && CollectionUtils.isEmpty(dataSourceBo.getWmPoiIdGroupList());
        if (needAssembleWmPoiGroup) {
            LogisticsFeeDataQueryUtil.assembleWmPoiIdGroupList(dataSourceBo);
        }

        if (CollectionUtils.isEmpty(dataSourceBo.getWmPoiIdGroupList())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "履约服务费数据来源无对应门店分组数据");
        }

        // 履约服务费数据来源信息
        ElectronicContractBatchDeliveryPerInfoBo perInfoBo = queryByGroup(dataSourceBo);
        Map<Long, ElectronicContractSingleDeliveryPerInfoBo> batchPerInfoMap = perInfoBo.getBatchPerInfoMap();

        // key为门店ID
        for (Map.Entry<Long, ElectronicContractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
            EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
            if (resultMap.containsKey(entry.getKey())) {
                deliveryInfoBo = resultMap.get(entry.getKey());
            }

            // 组装履约服务费信息
            assemblyPerInfoBo(perInfoBo, deliveryInfoBo, entry.getKey());
            log.info("queryData deliveryInfoBo:{}",JSON.toJSONString(deliveryInfoBo));
            resultMap.put(entry.getKey(), deliveryInfoBo);
        }

        log.info("#PhfLogusticsPerFeeDataQueryHandler queryData resultMap:{}", JSON.toJSONString(resultMap));
    }

    /**
     * 分组查询履约服务费
     * @param dataSourceBo
     * @return
     */
    public ElectronicContractBatchDeliveryPerInfoBo queryByGroup(EcontractDataSourceBo dataSourceBo) throws WmCustomerException {
        log.info("queryByGroup dataSourceBo:{}",JSON.toJSONString(dataSourceBo));
        // 获取分组的poibizBo
        List<List<EcontractDataPoiBizBo>> poiBizBoGroupList = LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(
                dataSourceBo.getWmPoiIdGroupList(), dataSourceBo.getWmPoiIdAndBizIdList());

        Map<Long, ElectronicContractSingleDeliveryPerInfoBo> allBatchPerInfoMap = new HashMap<>();

        Transaction transaction = Cat.newTransaction("phf.sign", "queryLogisticsPerFeeData");
        try {
            List<Future> futureList = new ArrayList<>();
            for (List<EcontractDataPoiBizBo> econtractDataPoiBizBos : poiBizBoGroupList) {
                Map<Long, Long> perPoiAndBizMap = econtractDataPoiBizBos.stream().collect(Collectors.toMap(EcontractDataPoiBizBo::getWmPoiId, EcontractDataPoiBizBo::getBizId));
                futureList.add(executorService.submit(() -> deliveryContractAdapter.queryPerFeeWithRetry(perPoiAndBizMap)));
            }

            int maxDelayTime = MccConfig.getQueryPhfLogisticsFeeDataMaxDelayTime();
            List<ElectronicContractBatchDeliveryPerInfoBo> queryResultList = new ArrayList<>();
            for (Future future : futureList) {
                queryResultList.add((ElectronicContractBatchDeliveryPerInfoBo) future.get(maxDelayTime, TimeUnit.SECONDS));
            }

            for (ElectronicContractBatchDeliveryPerInfoBo perInfoBo : queryResultList) {
                if (Objects.isNull(perInfoBo) || Objects.isNull(perInfoBo.getBatchPerInfoMap())) {
                    log.error("queryByGroup 履约服务费信息为空, poiBizBoGroupList:{}", JSONObject.toJSONString(poiBizBoGroupList));
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "履约服务费信息为空");
                }
                allBatchPerInfoMap.putAll(perInfoBo.getBatchPerInfoMap());
            }

        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("PhfLogisticsPerFeeDataQueryHandler#queryByGroup 查询拼好饭履约服务费数据异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询拼好饭履约服务费数据异常");
        } finally {
            transaction.complete();
        }

        ElectronicContractBatchDeliveryPerInfoBo perInfoBo = new ElectronicContractBatchDeliveryPerInfoBo();
        perInfoBo.setBatchPerInfoMap(allBatchPerInfoMap);

        log.info("PhfLogisticsPerFeeDataQueryHandler#queryBuGroup allBatchPerInfoMap:{}", JSONObject.toJSONString(allBatchPerInfoMap));
        return perInfoBo;
    }


    /**
     * 组装当前门店的履约服务费信息
     * @param batchPerInfoBo
     * @param targetInfoBo
     * @param wmPoiId
     */
    private void assemblyPerInfoBo(ElectronicContractBatchDeliveryPerInfoBo batchPerInfoBo, EcontractDeliveryInfoBo targetInfoBo, Long wmPoiId) throws WmCustomerException {
        if (Objects.isNull(batchPerInfoBo)) {
            log.info("PhfLogisticsFeeDataQueryHandler#assemblyPerInfoBo 无履约服务费信息，当前信息: {}", JSONObject.toJSONString(batchPerInfoBo));
            return;
        }

        Map<Long, ElectronicContractSingleDeliveryPerInfoBo> batchPerInfoMap = batchPerInfoBo.getBatchPerInfoMap();
        ElectronicContractSingleDeliveryPerInfoBo perInfoBo = batchPerInfoMap.get(wmPoiId);

        if (Objects.isNull(perInfoBo)) {
            log.error("PhfLogisticsFeeDataQueryHandler#assemblyPerInfoBo 未查到当前门店履约服务费，门店Id:{} , batchPerInfoMap: {}", wmPoiId, JSONObject.toJSONString(batchPerInfoMap));
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "未查到当前门店履约服务费");
        }


        Map<Integer, ElectronicContractSingleDeliverySecondProductPerInfoBo> secondProductPerInfoMap = perInfoBo.getSecondProductPerInfoMap();
        if (MapUtils.isEmpty(secondProductPerInfoMap)) {
            log.error("PhfLogisticsFeeDataQueryHandler#assemblyPerInfoBo 履约服务费二级产品信息为空，门店Id:{} , secondProductPerInfoMap: {}", wmPoiId, JSONObject.toJSONString(secondProductPerInfoMap));
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "履约服务费二级产品信息为空");
        }

        EcontractDeliveryPhfInfoBo econtractDeliveryPhfInfoBo = new EcontractDeliveryPhfInfoBo();
        if (Objects.nonNull(targetInfoBo.getEcontractDeliveryPhfInfoBo())) {
            econtractDeliveryPhfInfoBo = targetInfoBo.getEcontractDeliveryPhfInfoBo();
        }
        Map<Integer, EcontractDeliveryPhfPerInfoBo> targetPerInfo = econtractDeliveryPhfInfoBo.getPerInfo();
        if (Objects.isNull(targetPerInfo)) {
            targetPerInfo = new HashMap<>();
        }

        Set<Integer> deliveryTypeCodes = secondProductPerInfoMap.keySet();

        // key为配送方式EcontractDeliveryTypeEnum的code
        for (Integer deliveryTypeCode : deliveryTypeCodes) {
            ElectronicContractSingleDeliverySecondProductPerInfoBo secondProductPerInfoBo = secondProductPerInfoMap.get(deliveryTypeCode);
            // key为收费类型
            EcontractDeliveryPhfPerInfoBo targetPhfPerInfoBo = assemblyChargePerInfo(secondProductPerInfoBo);

            targetPerInfo.put(deliveryTypeCode, targetPhfPerInfoBo);
        }

        // 设置履约合同
        econtractDeliveryPhfInfoBo.setPerInfo(targetPerInfo);
        targetInfoBo.setEcontractDeliveryPhfInfoBo(econtractDeliveryPhfInfoBo);
        log.info("PhfLogisticsPerFeeDataQueryHandler#assemblyPerInfoBo econtractDeliveryPhfInfoBo:{}", JSONObject.toJSONString(econtractDeliveryPhfInfoBo));
    }

    /**
     * 当前收费类型下的履约服务费信息
     * @param secondProductPerInfoBo
     * @return
     */
    private EcontractDeliveryPhfPerInfoBo assemblyChargePerInfo(ElectronicContractSingleDeliverySecondProductPerInfoBo secondProductPerInfoBo) throws WmCustomerException {
        Map<Integer, ElectronicContractDeliverySecondProductPerInfoBo> perInfoMap = secondProductPerInfoBo.getPerInfoMap();
        log.info("PhfLogisticsFeeDataQueryHandler#assemblyChargePerInfo perInfoMap:{}", JSONObject.toJSONString(perInfoMap, SerializerFeature.WriteMapNullValue));
        Set<Integer> cchargeTypes = perInfoMap.keySet();

        Map<String, EcontractdeliveryPhfChargeInfoBo> targetChargeInfoBoMap = new HashMap<>();
        for (Integer cchargeType : cchargeTypes) {
            ElectronicContractDeliverySecondProductPerInfoBo chargeSecondProductPerInfoBo = perInfoMap.get(cchargeType);
            EcontractdeliveryPhfChargeInfoBo econtractdeliveryPhfChargeInfoBo = new EcontractdeliveryPhfChargeInfoBo();
            // 复制费率信息
            BeanUtils.copyProperties(chargeSecondProductPerInfoBo, econtractdeliveryPhfChargeInfoBo);
            targetChargeInfoBoMap.put(convertChargeType(cchargeType), econtractdeliveryPhfChargeInfoBo);
        }

        log.info("PhfLogisticsFeeDataQueryHandler#assemblyChargePerInfo targetChargeInfoBoMap:{}", JSONObject.toJSONString(targetChargeInfoBoMap));
        EcontractDeliveryPhfPerInfoBo perInfoBo = new EcontractDeliveryPhfPerInfoBo();
        perInfoBo.setChargePerInfo(targetChargeInfoBoMap);

        return perInfoBo;
    }

    /**
     * 转换收费类型(拼好饭正式版-履约合同放的是二级服务产品id，需要再次映射（单送、拼单一起送、拼单分开送）)
     * @param secondProductId
     * @return
     */
    private String convertChargeType(Integer secondProductId) throws WmCustomerException {
        String chargeType = PhfProductPackUtils.getChargeTypeByProductId(secondProductId);
        CChargeTypeEnum cChargeTypeEnum = CChargeTypeEnum.findByCode(chargeType);
        if (Objects.isNull(cChargeTypeEnum)) {
            log.error("未知的收费类型: chargeType:{}, secondProductId:{}", chargeType, secondProductId);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "未知的收费类型");
        }

        return cChargeTypeEnum.getCode();
    }
}
