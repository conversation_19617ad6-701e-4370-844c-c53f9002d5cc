package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.IWmEcontractDataWrapperService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 拼好饭正式版技术合同
 * @author: zhangyuanhao02
 * @create: 2024/12/23 14:28
 */
@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.PHF_FORMAL_TECH)
public class WmEcontractPhfFormalTechWrapperServiceImpl extends AbstarctWmEcontractPhfDataWrapperService implements IWmEcontractDataWrapperService {

    // 拼好送正式版技术合同，模版ID
    private static final String PHF_FORMAL_TECH_TEMPLATE_ID = "phf_formal_tech_template_id";

    // 拼好送正式版技术合同，模版版本
    private static final String PHF_FORMAL_TECH_TEMPLATE_VERSION = "phf_formal_tech_template_version";

    // 合同名称
    private static final String CONTRACT_NAME = "拼好送入驻服务协议";

    // 描述门店名称的最大数量
    private static final int DESC_POI_NAME_NUM = 3;

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.PHF_DELIVERY);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);

        if(CollectionUtils.isEmpty(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList())){
            log.warn("WmEcontractPhfFormalTechWrapperServiceImpl deliveryInfoBo为空，流程中止，customerId:{}", contextBo.getCustomerId());
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "原始数据缺失，pdf封装失败");
        }

        String signVersion = ConfigUtilAdapter.getString(EcontractSignTempletConstant.CURRENT_DELIVERY_SIGN_VERSION_FOR_NEW_MODULE);
        EcontractSignVersionBo econtractSignVersionBo = contextBo
                .getEcontractSignVersionBo();
        if(econtractSignVersionBo == null){
            econtractSignVersionBo = new EcontractSignVersionBo();
        }
        econtractSignVersionBo.setDeliverySignVersion(signVersion + "B");
        contextBo.setEcontractSignVersionBo(econtractSignVersionBo);

        // 按门店分批次，一个批次一个合同PDF
        EcontractCustomerInfoBo customerInfoBo = contextBo.getCustomerInfoBo();
        List<PdfContentInfoBo> pdfContentInfoBos = Lists.newArrayList();

        // 获取门店分组信息
        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = batchDeliveryInfoBo.getEcontractDeliveryInfoBoList();
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = batchDeliveryInfoBo.getWmPoiIdGroupMap();
        List<List<Long>> wmPoiIdGroupList = wmPoiIdGroupMap.get(EcontractDataSourceEnum.PHF_LOGISTICS_FEE.getType());
        List<List<EcontractDeliveryInfoBo>> wmPoiInfoBoGroupList = extractWmPoiInfoBoGroupList(econtractDeliveryInfoBoList, wmPoiIdGroupList);

        int templateId = ConfigUtilAdapter.getInt(PHF_FORMAL_TECH_TEMPLATE_ID, 170);
        int templateVersion = ConfigUtilAdapter.getInt(PHF_FORMAL_TECH_TEMPLATE_VERSION, 0);

        for (List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos : wmPoiInfoBoGroupList) {
            Map<String,String> pdfMetaContentMap = generatePdfMetaContent(customerInfoBo, null);
            fillWmPoiIdAndInfo(pdfMetaContentMap, econtractDeliveryInfoBos);

            List<Map<String,String>> pdfBizContent  = generatePdfBizContent(econtractDeliveryInfoBos);

            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            String contractDesc = generateContractDesc(econtractDeliveryInfoBos);
            pdfInfoBo.setPdfTemplateId(templateId);
            pdfInfoBo.setPdfTemplateVersion(templateVersion);
            pdfInfoBo.setPdfMetaContent(pdfMetaContentMap);
            pdfInfoBo.setPdfBizContent(pdfBizContent);
            pdfInfoBo.setContractDesc(contractDesc);
            pdfInfoBo.setContractName(CONTRACT_NAME);
            pdfContentInfoBos.add(pdfInfoBo);
        }

        log.info("#WmEcontractPhfFormalSelfWrapperServiceImpl, pdfContentInfoBos={}", JSONObject.toJSONString(pdfContentInfoBos));
        return pdfContentInfoBos;
    }


    @Override
    public Map<String, String> generatePdfMetaContent(EcontractCustomerInfoBo customerInfoBo, EcontractDeliveryPhfInfoBo infoBo) throws WmCustomerException {
        Map<String, String> pdfMetaContentMap = new HashMap<>();
        String signTime = dateToStr(new Date(), DATE_FORMAT);
        // 商家主体
        pdfMetaContentMap.put("partAName", customerInfoBo.getCustomerName());
        // 商家签章
        pdfMetaContentMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        // 商家签约日期
        pdfMetaContentMap.put("partASignTime", signTime);

        // 美团北京主体
        pdfMetaContentMap.put("partBName", ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc());
        // 美团北京签章
        pdfMetaContentMap.put("partBEstamp", PdfConstant.MT_SIGNKEY);
        // 美团北京签约日期
        pdfMetaContentMap.put("partBSignTime", signTime);

        return pdfMetaContentMap;
    }


    /**
     * 生成合同描述
     * @param econtractDeliveryInfoBos
     * @return
     */
    private String generateContractDesc(List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos) {
        int pdfDescPoiNameSize = Math.min(econtractDeliveryInfoBos.size(), DESC_POI_NAME_NUM);
        List<String> poiNameList = econtractDeliveryInfoBos.subList(0, pdfDescPoiNameSize).stream()
                .map(e -> e.getEcontractDeliveryPhfInfoBo().getWmPoiName())
                .collect(Collectors.toList());
        String poiNames = String.join(",", poiNameList);
        String contractDesc = "适用于" + poiNames;
        if (econtractDeliveryInfoBos.size() > DESC_POI_NAME_NUM) {
            contractDesc += "...";
        }

        return contractDesc;
    }

    /**
     * 生成BizContent
     * @param econtractDeliveryInfoBos
     * @return
     */
    private List<Map<String, String>> generatePdfBizContent(List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos) throws WmCustomerException {
        List<Map<String, String>> pdfBizContentList = Lists.newArrayList();
        for (EcontractDeliveryInfoBo econtractDeliveryInfoBo : econtractDeliveryInfoBos) {
            Map<String, String> bizContentMap = Maps.newHashMap();
            // 填充基础信息
            fillBaseInfo(bizContentMap, econtractDeliveryInfoBo.getEcontractDeliveryPhfInfoBo());
            // 填充技术费率信息
            fillTechFeeInfo(bizContentMap, econtractDeliveryInfoBo.getEcontractDeliveryPhfInfoBo());
            pdfBizContentList.add(bizContentMap);
        }

        return pdfBizContentList;
    }

    /**
     * 填充基础信息
     * @param bizContentMap
     * @param infoBo
     */
    private void fillBaseInfo(Map<String, String> bizContentMap, EcontractDeliveryPhfInfoBo infoBo) {
        bizContentMap.put("wmPoiId", infoBo.getWmPoiId());
        bizContentMap.put("wmPoiName", infoBo.getWmPoiName());
        bizContentMap.put("wmPoiNameAndId", infoBo.getWmPoiName() + " ID " + infoBo.getWmPoiId());
    }
}
