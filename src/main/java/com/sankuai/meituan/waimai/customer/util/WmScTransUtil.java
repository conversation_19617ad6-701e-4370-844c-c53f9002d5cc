package com.sankuai.meituan.waimai.customer.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.banma.aoi.thrift.vo.result.BmAoiAttrView;
import com.sankuai.meituan.banma.aoi.thrift.vo.result.BmLbsPoi;
import com.sankuai.meituan.banma.aoi.thrift.vo.view.BmPoint;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScLableEnum;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallManageDO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.poicategory.bo.WmPoiCateDic;
import com.sankuai.meituan.waimai.thrift.constants.WmYesNoCons;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenStallNumChangeReasonEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolAoiModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallBindDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallClueDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallManageDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contact.ContactDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: scm
 * @description: 转换类
 * @author: jianghuimin02
 * @create: 2020-04-24 17:35
 **/
public class WmScTransUtil {

    public static final int SCHOOL_ID_BEGIN = 10000;

    public static final int CANTEEN_ID_BEGIN = 100000;


    public static List<SchoolBo> schoolTransDbToBo(List<WmSchoolDB> wmSchoolDBS) {
        List<SchoolBo> schoolBos = Lists.newArrayList();
        for (WmSchoolDB wmSchoolDB : wmSchoolDBS) {
            SchoolBo schoolBo = new SchoolBo();
            BeanUtils.copyProperties(wmSchoolDB, schoolBo);

            // 食堂品类数量对象copy
            // 校园品类、校园品类描述对象copy

            schoolBos.add(schoolBo);
        }
        return schoolBos;
    }


    public static List<CanteenBo> canteenTransDbToBo(List<WmCanteenDB> canteenDBS) {
        List<CanteenBo> canteenBos = Lists.newArrayList();
        for (WmCanteenDB wmCanteenDB : canteenDBS) {
            CanteenBo canteenBo = new CanteenBo();
            BeanUtils.copyProperties(wmCanteenDB, canteenBo);
            canteenBos.add(canteenBo);
        }
        return canteenBos;
    }

    public static List<WmScOplogBo> scLogTransDbToBo(List<WmScLogDB> scLogDBS) {
        List<WmScOplogBo> scOplogBos = Lists.newArrayList();
        for (WmScLogDB wmScLogDB : scLogDBS) {
            WmScOplogBo wmScOplogBo = new WmScOplogBo();
            BeanUtils.copyProperties(wmScLogDB, wmScOplogBo);
            wmScOplogBo.setContent(wmScLogDB.getLog());
            wmScOplogBo.setOpUser(wmScLogDB.getOpUname());
            wmScOplogBo.setModuleType(wmScLogDB.getModuleType());
            wmScOplogBo.setLogTimeInterval(wmScLogDB.getCtime());
            scOplogBos.add(wmScOplogBo);
        }
        return scOplogBos;
    }

    public static List<WmCanPoiBo> toCanteenPoiBoList(List<WmPoiAggre> fields) {
        if (CollectionUtils.isEmpty(fields)) {
            return Collections.emptyList();
        }
        List<WmCanPoiBo> result = Lists.newArrayListWithCapacity(fields.size());
        for (WmPoiAggre field : fields) {
            if (StringUtils.isEmpty(field.getName())) {
                continue;
            }
            WmCanPoiBo bo = new WmCanPoiBo();
            bo.setWmPoiId(field.getWm_poi_id());
            bo.setWmPoiName(field.getName());
            bo.setWmPoiStatus(field.getValid());
            bo.setWmPoiStatusDes(CanteenPoiStatusEnum.getName(field.getValid()));
            result.add(bo);
        }
        return result;
    }
    public static List<WmCanPoiBo> toCanteenPoiBoListV2(List<WmPoiAggre> fields) {
        if (CollectionUtils.isEmpty(fields)) {
            return Collections.emptyList();
        }
        List<WmCanPoiBo> result = Lists.newArrayListWithCapacity(fields.size());
        for (WmPoiAggre field : fields) {
            WmCanPoiBo bo = new WmCanPoiBo();
            bo.setWmPoiId(field.getWm_poi_id());
            bo.setWmPoiName(field.getName());
            bo.setWmPoiStatus(field.getValid());
            bo.setWmPoiStatusDes(CanteenPoiStatusEnum.getName(field.getValid()));
            result.add(bo);
        }
        return result;
    }

    public static List<WmScCanteenPoiLabelDB> toPoiLabelList(int canteenId, List<Long> poiIdList, Long... labelIds) {
        List<WmScCanteenPoiLabelDB> result = Lists.newArrayList();
        for (Long poiId : poiIdList) {
            for (Long labelId : labelIds) {
                result.add(WmScCanteenPoiLabelDB.builder()
                        .canteenId(canteenId)
                        .labelId(labelId)
                        .wmPoiId(poiId)
                        .labelType(WmScLableEnum.getLabelType(labelId))
                        .ctime(TimeUtil.unixtime())
                        .utime(TimeUtil.unixtime())
                        .valid(WmYesNoCons.YES.getValue())
                        .build());
            }
        }
        return result;
    }

    /**
     * 根据学校主键ID得到学校ID
     * @param schoolPrimaryId 学校主键ID
     * @return 学校ID
     */
    public static Integer getSchoolIdBySchoolPrimaryId(Integer schoolPrimaryId) {
        return schoolPrimaryId + SCHOOL_ID_BEGIN;
    }

    /**
     * 根据学校ID得到学校主键ID
     * @param schoolId 学校ID
     * @return 学校主键ID
     */
    public static Integer getSchoolPrimaryIdBySchoolId(Integer schoolId) {
        return schoolId - SCHOOL_ID_BEGIN;
    }

    /**
     * 根据食堂ID得到食堂主键ID
     * @param canteenId 食堂ID
     * @return 食堂主键ID
     */
    public static Integer getCanteenPrimaryIdByCanteenId(Integer canteenId) {
        return canteenId - CANTEEN_ID_BEGIN;
    }

    /**
     * 根据食堂主键ID计算食堂ID
     * @param canteenPrimaryId 食堂主键ID
     * @return 食堂ID
     */
    public static Integer getCanteenIdByCanteenPrimaryId(Integer canteenPrimaryId) {
        return canteenPrimaryId + CANTEEN_ID_BEGIN;
    }

    /**
     * 学校交付历史DO列表转DTO列表
     * @param deliveryHistoryDOList 学校交付历史DO列表
     * @return 学校交付历史DTO列表
     */
    public static List<WmSchoolDeliveryHistoryDTO> transSchoolDeliveryHistoryDOsToDTOs(List<WmSchoolDeliveryHistoryDO> deliveryHistoryDOList) {
        List<WmSchoolDeliveryHistoryDTO> deliveryHistoryDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deliveryHistoryDOList)) {
            return deliveryHistoryDTOList;
        }

        for (WmSchoolDeliveryHistoryDO historyDO : deliveryHistoryDOList) {
            WmSchoolDeliveryHistoryDTO historyDTO = new WmSchoolDeliveryHistoryDTO();
            historyDTO.setId(historyDO.getId());
            historyDTO.setDeliveryId(historyDO.getDeliveryId());
            historyDTO.setSchoolPrimaryId(historyDO.getSchoolPrimaryId());
            historyDTO.setSchoolType(historyDO.getSchoolType());
            historyDTO.setSchoolName(historyDO.getSchoolName());
            historyDTO.setSchoolLevel(historyDO.getSchoolLevel());
            historyDTO.setSchoolOwnerUid(historyDO.getSchoolOwnerUid());
            historyDTO.setAgreementType(historyDO.getAgreementType());
            historyDTO.setAgreementTimeStart(historyDO.getAgreementTimeStart());
            historyDTO.setAgreementTimeEnd(historyDO.getAgreementTimeEnd());
            historyDTO.setTeaStuNum(historyDO.getTeaStuNum());
            historyDTO.setAorId(historyDO.getAorId());
            historyDTO.setAcmUid(historyDO.getAcmUid().longValue());
            historyDTO.setDeliveryEndTime(historyDO.getDeliveryEndTime());
            historyDTO.setAormUid(historyDO.getAormUid().longValue());
            historyDTO.setCsmUid(historyDO.getCsmUid().longValue());

            deliveryHistoryDTOList.add(historyDTO);
        }

        return deliveryHistoryDTOList;
    }

    public static List<WmSchoolDeliveryContactUserDTO> transContactDtoListToContactUserDTOList(List<ContactDto> contactDtoList) {
        List<WmSchoolDeliveryContactUserDTO> userDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(contactDtoList)) {
            return userDTOList;
        }

        for (ContactDto contactDto : contactDtoList) {
            WmSchoolDeliveryContactUserDTO userDTO = new WmSchoolDeliveryContactUserDTO();
            userDTO.setId(contactDto.getId());
            userDTO.setName(contactDto.getName());
            userDTO.setDuty(contactDto.getPosition());
            userDTO.setOtherDutyName(contactDto.getOtherPositionName());
            userDTO.setPhoneNum(contactDto.getPhone());
            userDTO.setBenefitAnalysis(contactDto.getBenefitAnalysis());
            userDTO.setManagementStyle(contactDto.getManagementStyle());
            userDTO.setDepartment(contactDto.getDepartment());
            userDTO.setOtherDepartmentName(contactDto.getOtherDepartmentName());
            userDTO.setContactRole(contactDto.getRole());
            userDTO.setPosition(contactDto.getStandpoint());

            userDTOList.add(userDTO);
        }
        return userDTOList;
    }

    public static WmSchoolDeliveryAssignmentDTO transDeliveryAssignmentDOToDTO(WmSchoolDeliveryAssignmentDO assignmentDO,
                                                                               List<WmSchoolDeliveryAssignmentDepartmentDO> departmentDOList) {
        if (assignmentDO == null) {
            return null;
        }

        WmSchoolDeliveryAssignmentDTO assignmentDTO = new WmSchoolDeliveryAssignmentDTO();
        assignmentDTO.setId(assignmentDO.getId());
        assignmentDTO.setSchoolPrimaryId(assignmentDO.getSchoolPrimaryId());
        assignmentDTO.setDeliveryId(assignmentDO.getDeliveryId());
        assignmentDTO.setInitiatorUid(assignmentDO.getInitiatorUid().intValue());
        assignmentDTO.setInitiationTime(assignmentDO.getInitiationTime());
        assignmentDTO.setKeyDecisionUserId(assignmentDO.getKeyDecisionUserId());
        assignmentDTO.setSignPartnerUserId(assignmentDO.getSignPartnerUserId());
        assignmentDTO.setExtraSupport(assignmentDO.getExtraSupport());
        assignmentDTO.setDepartmentIntensionDTOList(transDepartmentIntensionDOListToDTOList(departmentDOList));
        assignmentDTO.setPublicStallNum(assignmentDO.getPublicStallNum());
        assignmentDTO.setDeliverableStallNum(assignmentDO.getDeliverableStallNum());
        assignmentDTO.setNeedFoodCabinet(assignmentDO.getNeedFoodCabinet());
        assignmentDTO.setNeedFoodCabinetNum(assignmentDO.getNeedFoodCabinetNum());
        assignmentDTO.setMaterialDemand(assignmentDO.getMaterialDemand());
        assignmentDTO.setNeedPrinterNum(assignmentDO.getNeedPrinterNum());
        assignmentDTO.setNeedDisplayRackNum(assignmentDO.getNeedDisplayRackNum());
        assignmentDTO.setNeedSunshadeNum(assignmentDO.getNeedSunshadeNum());
        assignmentDTO.setNeedTrashCanNum(assignmentDO.getNeedTrashCanNum());
        assignmentDTO.setOtherMaterialDemand(assignmentDO.getOtherMaterialDemand());
        assignmentDTO.setOtherCooperationDemand(assignmentDO.getOtherCooperationDemand());
        assignmentDTO.setCsmUid(assignmentDO.getCsmUid().intValue());
        assignmentDTO.setAormUid(assignmentDO.getAormUid().intValue());
        assignmentDTO.setAcmUid(assignmentDO.getAcmUid().intValue());

        return assignmentDTO;
    }

    public static WmSchoolDeliveryAssignmentDO transDeliveryAssignmentDTOToDO(WmSchoolDeliveryAssignmentDTO assignmentDTO, Integer dataVersion) {
        WmSchoolDeliveryAssignmentDO assignmentDO = new WmSchoolDeliveryAssignmentDO();
        if (assignmentDTO == null) {
            return assignmentDO;
        }

        assignmentDO.setId(assignmentDTO.getId());
        assignmentDO.setSchoolPrimaryId(assignmentDTO.getSchoolPrimaryId());
        assignmentDO.setDeliveryId(assignmentDTO.getDeliveryId());
        assignmentDO.setInitiatorUid(assignmentDTO.getInitiatorUid().longValue());
        assignmentDO.setInitiationTime(assignmentDTO.getInitiationTime());
        assignmentDO.setKeyDecisionUserId(assignmentDTO.getKeyDecisionUserId());
        assignmentDO.setSignPartnerUserId(assignmentDTO.getSignPartnerUserId());
        assignmentDO.setExtraSupport(assignmentDTO.getExtraSupport());
        assignmentDO.setPublicStallNum(assignmentDTO.getPublicStallNum());
        assignmentDO.setDeliverableStallNum(assignmentDTO.getDeliverableStallNum());
        assignmentDO.setNeedFoodCabinet(assignmentDTO.getNeedFoodCabinet());
        assignmentDO.setNeedFoodCabinetNum(assignmentDTO.getNeedFoodCabinetNum());
        assignmentDO.setMaterialDemand(assignmentDTO.getMaterialDemand());
        assignmentDO.setNeedPrinterNum(assignmentDTO.getNeedPrinterNum());
        assignmentDO.setNeedDisplayRackNum(assignmentDTO.getNeedDisplayRackNum());
        assignmentDO.setNeedSunshadeNum(assignmentDTO.getNeedSunshadeNum());
        assignmentDO.setNeedTrashCanNum(assignmentDTO.getNeedTrashCanNum());
        assignmentDO.setOtherMaterialDemand(assignmentDTO.getOtherMaterialDemand());
        assignmentDO.setOtherCooperationDemand(assignmentDTO.getOtherCooperationDemand());
        assignmentDO.setDataVersion(dataVersion);
        assignmentDO.setCsmUid(assignmentDTO.getCsmUid().longValue());
        assignmentDO.setAormUid(assignmentDTO.getAormUid().longValue());
        assignmentDO.setAcmUid(assignmentDTO.getAcmUid().longValue());

        return assignmentDO;
    }

    public static List<WmSchoolDepartmentIntensionDTO> transDepartmentIntensionDOListToDTOList(List<WmSchoolDeliveryAssignmentDepartmentDO> departmentDOList) {
        if (CollectionUtils.isEmpty(departmentDOList)) {
            return new ArrayList<>();
        }

        List<WmSchoolDepartmentIntensionDTO> departmentIntensionDTOList = new ArrayList<>();
        for (WmSchoolDeliveryAssignmentDepartmentDO departmentDO : departmentDOList) {
            WmSchoolDepartmentIntensionDTO dto = new WmSchoolDepartmentIntensionDTO();
            dto.setId(departmentDO.getId());
            dto.setDeliveryId(departmentDO.getDeliveryId());
            dto.setSchoolPrimaryId(departmentDO.getSchoolPrimaryId());
            dto.setDepartmentName(departmentDO.getDepartmentName());
            dto.setDepartmentIntension(departmentDO.getDepartmentIntension());
            departmentIntensionDTOList.add(dto);
        }
        return departmentIntensionDTOList;
    }

    public static List<WmSchoolDeliveryAssignmentDepartmentDO> transDepartmentIntensionDTOListToDOList(List<WmSchoolDepartmentIntensionDTO> departmentDTOList) {
        if (CollectionUtils.isEmpty(departmentDTOList)) {
            return new ArrayList<>();
        }

        List<WmSchoolDeliveryAssignmentDepartmentDO> departmentDOList = new ArrayList<>();
        for (WmSchoolDepartmentIntensionDTO departmentDTO : departmentDTOList) {
            WmSchoolDeliveryAssignmentDepartmentDO departmentDO = new WmSchoolDeliveryAssignmentDepartmentDO();
            departmentDO.setDeliveryId(departmentDTO.getDeliveryId());
            departmentDO.setSchoolPrimaryId(departmentDTO.getSchoolPrimaryId());
            departmentDO.setDepartmentName(departmentDTO.getDepartmentName());
            departmentDO.setDepartmentIntension(departmentDTO.getDepartmentIntension());
            departmentDOList.add(departmentDO);
        }
        return departmentDOList;
    }

    public static WmSchoolDeliveryGoalSetDTO transDeliveryGoalSetDOToDTO(WmSchoolDeliveryGoalSetDO goalSetDO) {
        if (goalSetDO == null) {
            return null;
        }

        WmSchoolDeliveryGoalSetDTO goalSetDTO = new WmSchoolDeliveryGoalSetDTO();
        goalSetDTO.setId(goalSetDO.getId());
        goalSetDTO.setSchoolPrimaryId(goalSetDO.getSchoolPrimaryId());
        goalSetDTO.setDeliveryId(goalSetDO.getDeliveryId());
        goalSetDTO.setContactUserIds(goalSetDO.getContactUserIds());
        goalSetDTO.setInscOnlineSignExptime(goalSetDO.getInscOnlineSignExptime());
        goalSetDTO.setInscOfflineBuildExptime(goalSetDO.getInscOfflineBuildExptime());
        goalSetDTO.setBuildOffCampusStation(goalSetDO.getBuildOffCampusStation());
        goalSetDTO.setOutscOnlineSignExptime(goalSetDO.getOutscOnlineSignExptime());
        goalSetDTO.setOutscOfflineBuildExptime(goalSetDO.getOutscOfflineBuildExptime());

        goalSetDTO.setOntimeRateTarget(goalSetDO.getOntimeRateTarget());
        goalSetDTO.setOntimeRateTargetExptime(goalSetDO.getOntimeRateTargetExptime());

        goalSetDTO.setAvgDeliveryTimeTarget(goalSetDO.getAvgDeliveryTimeTarget());
        goalSetDTO.setAvgDeliveryTimeTargetExptime(goalSetDO.getAvgDeliveryTimeTargetExptime());

        goalSetDTO.setFirstStallBulidExptime(goalSetDO.getFirstStallBulidExptime());
        goalSetDTO.setFirstStallOnlineExptime(goalSetDO.getFirstStallOnlineExptime());
        goalSetDTO.setFirstOnlineStallNum(goalSetDO.getFirstOnlineStallNum());

        goalSetDTO.setOnlinePenerateTarget(goalSetDO.getOnlinePenerateTarget());
        goalSetDTO.setOnlinePenerateTargetExptime(goalSetDO.getOnlinePenerateTargetExptime());

        goalSetDTO.setDailyOrderTarget(goalSetDO.getDailyOrderTarget());
        goalSetDTO.setDailyOrderTargetExptime(goalSetDO.getDailyOrderTargetExptime());

        goalSetDTO.setMealPenerateTarget(goalSetDO.getMealPenerateTarget());
        goalSetDTO.setMealPenerateTargetExptime(goalSetDO.getMealPenerateTargetExptime());

        goalSetDTO.setDailyYieldTarget(goalSetDO.getDailyYieldTarget());
        goalSetDTO.setDailyYieldTargetExptime(goalSetDO.getDailyYieldTargetExptime());

        return goalSetDTO;
    }

    public static WmSchoolDeliveryGoalSetDO transDeliveryGoalSetDTOToDO(WmSchoolDeliveryGoalSetDTO goalSetDTO, Integer dataVersion) {
        WmSchoolDeliveryGoalSetDO goalSetDO = new WmSchoolDeliveryGoalSetDO();
        if (goalSetDTO == null) {
            return goalSetDO;
        }

        goalSetDO.setSchoolPrimaryId(goalSetDTO.getSchoolPrimaryId());
        goalSetDO.setDeliveryId(goalSetDTO.getDeliveryId());
        goalSetDO.setContactUserIds(goalSetDTO.getContactUserIds());
        goalSetDO.setInscOnlineSignExptime(goalSetDTO.getInscOnlineSignExptime());
        goalSetDO.setInscOfflineBuildExptime(goalSetDTO.getInscOfflineBuildExptime());
        goalSetDO.setBuildOffCampusStation(goalSetDTO.getBuildOffCampusStation());
        goalSetDO.setOutscOnlineSignExptime(goalSetDTO.getOutscOnlineSignExptime());
        goalSetDO.setOutscOfflineBuildExptime(goalSetDTO.getOutscOfflineBuildExptime());

        goalSetDO.setOntimeRateTarget(goalSetDTO.getOntimeRateTarget());
        goalSetDO.setOntimeRateTargetExptime(goalSetDTO.getOntimeRateTargetExptime());

        goalSetDO.setAvgDeliveryTimeTarget(goalSetDTO.getAvgDeliveryTimeTarget());
        goalSetDO.setAvgDeliveryTimeTargetExptime(goalSetDTO.getAvgDeliveryTimeTargetExptime());

        goalSetDO.setFirstStallBulidExptime(goalSetDTO.getFirstStallBulidExptime());
        goalSetDO.setFirstStallOnlineExptime(goalSetDTO.getFirstStallOnlineExptime());
        goalSetDO.setFirstOnlineStallNum(goalSetDTO.getFirstOnlineStallNum());

        goalSetDO.setOnlinePenerateTarget(goalSetDTO.getOnlinePenerateTarget());
        goalSetDO.setOnlinePenerateTargetExptime(goalSetDTO.getOnlinePenerateTargetExptime());

        goalSetDO.setDailyOrderTarget(goalSetDTO.getDailyOrderTarget());
        goalSetDO.setDailyOrderTargetExptime(goalSetDTO.getDailyOrderTargetExptime());

        goalSetDO.setMealPenerateTarget(goalSetDTO.getMealPenerateTarget());
        goalSetDO.setMealPenerateTargetExptime(goalSetDTO.getMealPenerateTargetExptime());

        goalSetDO.setDailyYieldTarget(goalSetDTO.getDailyYieldTarget());
        goalSetDO.setDailyYieldTargetExptime(goalSetDTO.getDailyYieldTargetExptime());
        goalSetDO.setDataVersion(dataVersion);
        return goalSetDO;
    }


    public static WmSchoolDeliveryFollowUpDTO transDeliveryFollowUpDOToDTO(WmSchoolDeliveryFollowUpDO followUpDO) {
        if (followUpDO == null) {
            return null;
        }

        WmSchoolDeliveryFollowUpDTO followUpDTO = new WmSchoolDeliveryFollowUpDTO();
        followUpDTO.setId(followUpDO.getId());
        followUpDTO.setSchoolPrimaryId(followUpDO.getSchoolPrimaryId());
        followUpDTO.setDeliveryId(followUpDO.getDeliveryId());

        followUpDTO.setInitiatorUid(followUpDO.getInitiatorUid().intValue());
        followUpDTO.setInitiationTime(followUpDO.getInitiationTime());

        followUpDTO.setInscOnlineSignAchieve(followUpDO.getInscOnlineSignAchieve());
        followUpDTO.setInscOnlineSignFintime(followUpDO.getInscOnlineSignFintime());
        followUpDTO.setInscOnlineSignStatus(followUpDO.getInscOnlineSignStatus());
        followUpDTO.setInscOnlineSignException(followUpDO.getInscOnlineSignException());

        followUpDTO.setInscOfflineBuildAchieve(followUpDO.getInscOfflineBuildAchieve());
        followUpDTO.setInscOfflineBuildFintime(followUpDO.getInscOfflineBuildFintime());
        followUpDTO.setInscOfflineBuildStatus(followUpDO.getInscOfflineBuildStatus());
        followUpDTO.setInscOfflineBuildException(followUpDO.getInscOfflineBuildException());

        followUpDTO.setOutscOnlineSignAchieve(followUpDO.getOutscOnlineSignAchieve());
        followUpDTO.setOutscOnlineSignFintime(followUpDO.getOutscOnlineSignFintime());
        followUpDTO.setOutscOnlineSignStatus(followUpDO.getOutscOnlineSignStatus());
        followUpDTO.setOutscOnlineSignException(followUpDO.getOutscOnlineSignException());

        followUpDTO.setOutscOfflineBuildAchieve(followUpDO.getOutscOfflineBuildAchieve());
        followUpDTO.setOutscOfflineBuildFintime(followUpDO.getOutscOfflineBuildFintime());
        followUpDTO.setOutscOfflineBuildStatus(followUpDO.getOutscOfflineBuildStatus());
        followUpDTO.setOutscOfflineBuildException(followUpDO.getOutscOfflineBuildException());

        followUpDTO.setFirstStallOnlineAchieve(followUpDO.getFirstStallOnlineAchieve());
        followUpDTO.setFirstStallOnlineFintime(followUpDO.getFirstStallOnlineFintime());
        followUpDTO.setFirstStallOnlineStatus(followUpDO.getFirstStallOnlineStatus());
        followUpDTO.setFirstStallOnlineException(followUpDO.getFirstStallOnlineException());

        followUpDTO.setOnlineOperationPlan(followUpDO.getOnlineOperationPlan());
        followUpDTO.setLifeCycle(followUpDO.getLifeCycle());
        followUpDTO.setOnlinePenerate(followUpDO.getOnlinePenerate());
        followUpDTO.setOnlinePenerateAchieve(followUpDO.getOnlinePenerateAchieve());
        followUpDTO.setOnlinePenerateStatus(followUpDO.getOnlinePenerateStatus());
        followUpDTO.setOnlinePenerateException(followUpDO.getOnlinePenerateException());

        followUpDTO.setOntimeRate(followUpDO.getOntimeRate());
        followUpDTO.setOntimeRateAchieve(followUpDO.getOntimeRateAchieve());
        followUpDTO.setOntimeRateStatus(followUpDO.getOntimeRateStatus());
        followUpDTO.setOntimeRateException(followUpDO.getOntimeRateException());

        followUpDTO.setAvgDeliveryTime(followUpDO.getAvgDeliveryTime());
        followUpDTO.setAvgDeliveryTimeAchieve(followUpDO.getAvgDeliveryTimeAchieve());
        followUpDTO.setAvgDeliveryTimeStatus(followUpDO.getAvgDeliveryTimeStatus());
        followUpDTO.setAvgDeliveryTimeException(followUpDO.getAvgDeliveryTimeException());

        followUpDTO.setDailyOrder(followUpDO.getDailyOrder());
        followUpDTO.setDailyOrderAchieve(followUpDO.getDailyOrderAchieve());
        followUpDTO.setDailyOrderStatus(followUpDO.getDailyOrderStatus());
        followUpDTO.setDailyOrderException(followUpDO.getDailyOrderException());

        followUpDTO.setMealPenerate(followUpDO.getMealPenerate());
        followUpDTO.setMealPenerateAchieve(followUpDO.getMealPenerateAchieve());
        followUpDTO.setMealPenerateStatus(followUpDO.getMealPenerateStatus());
        followUpDTO.setMealPenerateException(followUpDO.getMealPenerateException());

        followUpDTO.setDailyYield(followUpDO.getDailyYield());
        followUpDTO.setDailyYieldAchieve(followUpDO.getDailyYieldAchieve());
        followUpDTO.setDailyYieldStatus(followUpDO.getDailyYieldStatus());
        followUpDTO.setDailyYieldException(followUpDO.getDailyYieldException());

        followUpDTO.setCsmUid(followUpDO.getCsmUid().intValue());
        followUpDTO.setAormUid(followUpDO.getAormUid().intValue());
        followUpDTO.setAcmUid(followUpDO.getAcmUid().intValue());
        return followUpDTO;
    }

    public static SchoolBo transSchoolDeliveryHistoryDOToSchoolBo(WmSchoolDeliveryHistoryDO historyDO, String aorName) {
        SchoolBo schoolBo = new SchoolBo();
        if (historyDO == null) {
            return schoolBo;
        }

        schoolBo.setSchoolName(historyDO.getSchoolName());
        schoolBo.setAorId(historyDO.getAorId());
        schoolBo.setSchoolLevel(historyDO.getSchoolLevel());
        schoolBo.setSchoolType(historyDO.getSchoolType());
        schoolBo.setTeaStuNum(historyDO.getTeaStuNum());
        schoolBo.setAgreementType(historyDO.getAgreementType());
        schoolBo.setAgreementTimeStart(historyDO.getAgreementTimeStart());
        schoolBo.setAgreementTimeEnd(historyDO.getAgreementTimeEnd());
        schoolBo.setId(historyDO.getSchoolPrimaryId());
        schoolBo.setAorName(aorName);
        return schoolBo;
    }

    public static WmSchoolDeliveryFollowUpDO transDeliveryFollowUpDTOToDO(WmSchoolDeliveryFollowUpDTO followUpDTO, Integer dataVersion) {
        WmSchoolDeliveryFollowUpDO followUpDO = new WmSchoolDeliveryFollowUpDO();
        if (followUpDTO == null) {
            return followUpDO;
        }

        followUpDO.setSchoolPrimaryId(followUpDTO.getSchoolPrimaryId());
        followUpDO.setDeliveryId(followUpDTO.getDeliveryId());

        followUpDO.setInitiatorUid(followUpDTO.getInitiatorUid().longValue());
        followUpDO.setInitiationTime(followUpDTO.getInitiationTime());

        followUpDO.setInscOnlineSignAchieve(followUpDTO.getInscOnlineSignAchieve());
        followUpDO.setInscOnlineSignFintime(followUpDTO.getInscOnlineSignFintime());
        followUpDO.setInscOnlineSignStatus(followUpDTO.getInscOnlineSignStatus());
        followUpDO.setInscOnlineSignException(followUpDTO.getInscOnlineSignException());

        followUpDO.setInscOfflineBuildAchieve(followUpDTO.getInscOfflineBuildAchieve());
        followUpDO.setInscOfflineBuildFintime(followUpDTO.getInscOfflineBuildFintime());
        followUpDO.setInscOfflineBuildStatus(followUpDTO.getInscOfflineBuildStatus());
        followUpDO.setInscOfflineBuildException(followUpDTO.getInscOfflineBuildException());

        followUpDO.setOutscOnlineSignAchieve(followUpDTO.getOutscOnlineSignAchieve());
        followUpDO.setOutscOnlineSignFintime(followUpDTO.getOutscOnlineSignFintime());
        followUpDO.setOutscOnlineSignStatus(followUpDTO.getOutscOnlineSignStatus());
        followUpDO.setOutscOnlineSignException(followUpDTO.getOutscOnlineSignException());

        followUpDO.setOutscOfflineBuildAchieve(followUpDTO.getOutscOfflineBuildAchieve());
        followUpDO.setOutscOfflineBuildFintime(followUpDTO.getOutscOfflineBuildFintime());
        followUpDO.setOutscOfflineBuildStatus(followUpDTO.getOutscOfflineBuildStatus());
        followUpDO.setOutscOfflineBuildException(followUpDTO.getOutscOfflineBuildException());

        followUpDO.setFirstStallOnlineAchieve(followUpDTO.getFirstStallOnlineAchieve());
        followUpDO.setFirstStallOnlineFintime(followUpDTO.getFirstStallOnlineFintime());
        followUpDO.setFirstStallOnlineStatus(followUpDTO.getFirstStallOnlineStatus());
        followUpDO.setFirstStallOnlineException(followUpDTO.getFirstStallOnlineException());

        followUpDO.setOnlineOperationPlan(followUpDTO.getOnlineOperationPlan());
        followUpDO.setLifeCycle(followUpDTO.getLifeCycle());

        followUpDO.setOnlinePenerate(followUpDTO.getOnlinePenerate());
        followUpDO.setOnlinePenerateAchieve(followUpDTO.getOnlinePenerateAchieve());
        followUpDO.setOnlinePenerateStatus(followUpDTO.getOnlinePenerateStatus());
        followUpDO.setOnlinePenerateException(followUpDTO.getOnlinePenerateException());

        followUpDO.setOntimeRate(followUpDTO.getOntimeRate());
        followUpDO.setOntimeRateAchieve(followUpDTO.getOntimeRateAchieve());
        followUpDO.setOntimeRateStatus(followUpDTO.getOntimeRateStatus());
        followUpDO.setOntimeRateException(followUpDTO.getOntimeRateException());

        followUpDO.setAvgDeliveryTime(followUpDTO.getAvgDeliveryTime());
        followUpDO.setAvgDeliveryTimeAchieve(followUpDTO.getAvgDeliveryTimeAchieve());
        followUpDO.setAvgDeliveryTimeStatus(followUpDTO.getAvgDeliveryTimeStatus());
        followUpDO.setAvgDeliveryTimeException(followUpDTO.getAvgDeliveryTimeException());

        followUpDO.setDailyOrder(followUpDTO.getDailyOrder());
        followUpDO.setDailyOrderAchieve(followUpDTO.getDailyOrderAchieve());
        followUpDO.setDailyOrderStatus(followUpDTO.getDailyOrderStatus());
        followUpDO.setDailyOrderException(followUpDTO.getDailyOrderException());

        followUpDO.setMealPenerate(followUpDTO.getMealPenerate());
        followUpDO.setMealPenerateAchieve(followUpDTO.getMealPenerateAchieve());
        followUpDO.setMealPenerateStatus(followUpDTO.getMealPenerateStatus());
        followUpDO.setMealPenerateException(followUpDTO.getMealPenerateException());

        followUpDO.setDailyYield(followUpDTO.getDailyYield());
        followUpDO.setDailyYieldAchieve(followUpDTO.getDailyYieldAchieve());
        followUpDO.setDailyYieldStatus(followUpDTO.getDailyYieldStatus());
        followUpDO.setDailyYieldException(followUpDTO.getDailyYieldException());

        followUpDO.setCsmUid(followUpDTO.getCsmUid().longValue());
        followUpDO.setAormUid(followUpDTO.getAormUid().longValue());
        followUpDO.setAcmUid(followUpDTO.getAcmUid().longValue());

        followUpDO.setDataVersion(dataVersion);
        return followUpDO;
    }

    /**
     * 学校平台合作信息SaveDTO转DO
     *
     * @param wmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO
     * @return WmScSchoolCoPlatformDO
     */
    public static WmScSchoolCoPlatformDO transWmScSchoolCoPlatformSaveDTOToDO(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO) {
        WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert = new WmScSchoolCoPlatformDO();
        if (wmScSchoolCoPlatformSaveDTO == null) {
            return wmScSchoolCoPlatformDOInsert;
        }
        wmScSchoolCoPlatformDOInsert.setId(wmScSchoolCoPlatformSaveDTO.getId());
        wmScSchoolCoPlatformDOInsert.setSchoolPrimaryId(wmScSchoolCoPlatformSaveDTO.getSchoolPrimaryId());
        wmScSchoolCoPlatformDOInsert.setCooperationPlatform(wmScSchoolCoPlatformSaveDTO.getCooperationPlatform());
        wmScSchoolCoPlatformDOInsert.setPlatformName(wmScSchoolCoPlatformSaveDTO.getPlatformName());
        wmScSchoolCoPlatformDOInsert.setSchoolInPoiOrderCount(wmScSchoolCoPlatformSaveDTO.getSchoolInPoiOrderCount());
        wmScSchoolCoPlatformDOInsert.setSchoolOutPoiOrderCount(wmScSchoolCoPlatformSaveDTO.getSchoolOutPoiOrderCount());
        wmScSchoolCoPlatformDOInsert.setSchoolInOnlinePoiCount(wmScSchoolCoPlatformSaveDTO.getSchoolInOnlinePoiCount());
        wmScSchoolCoPlatformDOInsert.setSchoolOutOnlinePoiCount(wmScSchoolCoPlatformSaveDTO.getSchoolOutOnlinePoiCount());
        wmScSchoolCoPlatformDOInsert.setDeliveryFeeTypeInfo(wmScSchoolCoPlatformSaveDTO.getDeliveryFeeTypeInfo());
        wmScSchoolCoPlatformDOInsert.setCompareCooperationPlatform(wmScSchoolCoPlatformSaveDTO.getCompareCooperationPlatform());
        wmScSchoolCoPlatformDOInsert.setCompareCooperationPlatformInfo(wmScSchoolCoPlatformSaveDTO.getCompareCooperationPlatformInfo());
        wmScSchoolCoPlatformDOInsert.setPlatformAllowToSchool(wmScSchoolCoPlatformSaveDTO.getPlatformAllowToSchool());
        wmScSchoolCoPlatformDOInsert.setSupportFoodUpstairs(wmScSchoolCoPlatformSaveDTO.getSupportFoodUpstairs());
        wmScSchoolCoPlatformDOInsert.setSupportFoodUpstairsInfo(wmScSchoolCoPlatformSaveDTO.getSupportFoodUpstairsInfo());
        wmScSchoolCoPlatformDOInsert.setFoodUpstairsFee(wmScSchoolCoPlatformSaveDTO.getFoodUpstairsFee());
        wmScSchoolCoPlatformDOInsert.setFoodUpstairsReasonInfo(wmScSchoolCoPlatformSaveDTO.getFoodUpstairsReasonInfo());
        wmScSchoolCoPlatformDOInsert.setPlatformEstablishTime(wmScSchoolCoPlatformSaveDTO.getPlatformEstablishTime());
        wmScSchoolCoPlatformDOInsert.setAdvantageAddedServiceInfo(wmScSchoolCoPlatformSaveDTO.getAdvantageAddedServiceInfo());
        wmScSchoolCoPlatformDOInsert.setAdvantageGoodExperienceInfo(wmScSchoolCoPlatformSaveDTO.getAdvantageGoodExperienceInfo());
        wmScSchoolCoPlatformDOInsert.setAdvantageAttractionInfo(wmScSchoolCoPlatformSaveDTO.getAdvantageAttractionInfo());
        wmScSchoolCoPlatformDOInsert.setAdvantagePropagandaInfo(wmScSchoolCoPlatformSaveDTO.getAdvantagePropagandaInfo());
        wmScSchoolCoPlatformDOInsert.setAdvantageExtraInfo(wmScSchoolCoPlatformSaveDTO.getAdvantageExtraInfo());
        wmScSchoolCoPlatformDOInsert.setSupplyDistribution(wmScSchoolCoPlatformSaveDTO.getSupplyDistribution());
        wmScSchoolCoPlatformDOInsert.setMonopolyForm(wmScSchoolCoPlatformSaveDTO.getMonopolyForm());
        wmScSchoolCoPlatformDOInsert.setExtraInfo(wmScSchoolCoPlatformSaveDTO.getExtraInfo());
        wmScSchoolCoPlatformDOInsert.setCuid(wmScSchoolCoPlatformSaveDTO.getUserId().longValue());
        wmScSchoolCoPlatformDOInsert.setMuid(wmScSchoolCoPlatformSaveDTO.getUserId().longValue());
        // 合作平台的收费方式列表
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmScSchoolCoPlatformSaveDTO.getDeliveryFeeType())) {
            wmScSchoolCoPlatformDOInsert.setDeliveryFeeType(JSONObject.toJSONString(wmScSchoolCoPlatformSaveDTO.getDeliveryFeeType()));
        } else {
            wmScSchoolCoPlatformDOInsert.setDeliveryFeeType("");
        }
        // 合作平台的优势列表
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmScSchoolCoPlatformSaveDTO.getPlatformEstablishAdvantage())) {
            wmScSchoolCoPlatformDOInsert.setPlatformEstablishAdvantage(StringUtils.join(wmScSchoolCoPlatformSaveDTO.getPlatformEstablishAdvantage(), ","));
        } else {
            wmScSchoolCoPlatformDOInsert.setPlatformEstablishAdvantage("");
        }
        // 送餐上楼原因（多选）
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmScSchoolCoPlatformSaveDTO.getFoodUpstairsReason())) {
            wmScSchoolCoPlatformDOInsert.setFoodUpstairsReason(StringUtils.join(wmScSchoolCoPlatformSaveDTO.getFoodUpstairsReason(), ","));
        } else {
            wmScSchoolCoPlatformDOInsert.setFoodUpstairsReason("");
        }
        // 学校合作平台营销活动列表
        buildWmScSchoolCoPlatformMarketDOList(wmScSchoolCoPlatformSaveDTO, wmScSchoolCoPlatformDOInsert);
        return wmScSchoolCoPlatformDOInsert;
    }

    /**
     * 转换合作平台营销活动相关信息
     *
     * @param wmScSchoolCoPlatformSaveDTO dto参数
     * @param wmScSchoolCoPlatformDO      do参数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private static void buildWmScSchoolCoPlatformMarketDOList(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO, WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(wmScSchoolCoPlatformSaveDTO.getWmScSchoolCoPlatformMarketSaveDTOList())) {
            return;
        }
        List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOList = new ArrayList<>();
        for (WmScSchoolCoPlatformMarketSaveDTO wmScSchoolCoPlatformMarketSaveDTO : wmScSchoolCoPlatformSaveDTO.getWmScSchoolCoPlatformMarketSaveDTOList()) {
            if (wmScSchoolCoPlatformMarketSaveDTO == null) {
                continue;
            }
            WmScSchoolCoPlatformMarketDO wmScSchoolCoPlatformMarketDo = new WmScSchoolCoPlatformMarketDO();
            wmScSchoolCoPlatformMarketDo.setId(wmScSchoolCoPlatformMarketSaveDTO.getId());
            wmScSchoolCoPlatformMarketDo.setPlatformPrimaryId(wmScSchoolCoPlatformMarketSaveDTO.getPlatformPrimaryId());
            wmScSchoolCoPlatformMarketDo.setPlatformMarketingActivity(wmScSchoolCoPlatformMarketSaveDTO.getPlatformMarketingActivity());
            wmScSchoolCoPlatformMarketDo.setPlatformMarketingActivityInfo(wmScSchoolCoPlatformMarketSaveDTO.getPlatformMarketingActivityInfo());
            wmScSchoolCoPlatformMarketDo.setActivityRuleDescription(wmScSchoolCoPlatformMarketSaveDTO.getActivityRuleDescription());
            wmScSchoolCoPlatformMarketDo.setActivityPic(wmScSchoolCoPlatformMarketSaveDTO.getActivityPic());
            wmScSchoolCoPlatformMarketDo.setActivityCostSharingType(wmScSchoolCoPlatformMarketSaveDTO.getActivityCostSharingType());
            wmScSchoolCoPlatformMarketDo.setActivityCostSharingPoiMin(wmScSchoolCoPlatformMarketSaveDTO.getActivityCostSharingPoiMin());
            wmScSchoolCoPlatformMarketDo.setActivityCostSharingPoiMax(wmScSchoolCoPlatformMarketSaveDTO.getActivityCostSharingPoiMax());
            wmScSchoolCoPlatformMarketDo.setActivityCostSharingPlatformMin(wmScSchoolCoPlatformMarketSaveDTO.getActivityCostSharingPlatformMin());
            wmScSchoolCoPlatformMarketDo.setActivityCostSharingPlatformMax(wmScSchoolCoPlatformMarketSaveDTO.getActivityCostSharingPlatformMax());
            wmScSchoolCoPlatformMarketDo.setCuid(wmScSchoolCoPlatformMarketSaveDTO.getUserId().longValue());
            wmScSchoolCoPlatformMarketDo.setMuid(wmScSchoolCoPlatformMarketSaveDTO.getUserId().longValue());
            wmScSchoolCoPlatformMarketDOList.add(wmScSchoolCoPlatformMarketDo);
        }
        wmScSchoolCoPlatformDO.setWmScSchoolCoPlatformMarketDOList(wmScSchoolCoPlatformMarketDOList);
    }

    /**
     * 将学校平台合作信息DO列表转DTO列表
     *
     * @param wmScSchoolCoPlatformDOList wmScSchoolCoPlatformDOList
     * @return List<WmScSchoolCoPlatformDTO>
     */
    public static List<WmScSchoolCoPlatformDTO> transSchoolCoPlatformDOListToDTOList(List<WmScSchoolCoPlatformDO> wmScSchoolCoPlatformDOList) {
        List<WmScSchoolCoPlatformDTO> wmScSchoolCoPlatformDTOList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(wmScSchoolCoPlatformDOList)) {
            return wmScSchoolCoPlatformDTOList;
        }

        for (WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO : wmScSchoolCoPlatformDOList) {
            if (wmScSchoolCoPlatformDO == null) {
                continue;
            }
            WmScSchoolCoPlatformDTO wmScSchoolCoPlatformDTO = new WmScSchoolCoPlatformDTO();
            wmScSchoolCoPlatformDTO.setId(wmScSchoolCoPlatformDO.getId());
            wmScSchoolCoPlatformDTO.setSchoolPrimaryId(wmScSchoolCoPlatformDO.getSchoolPrimaryId());
            wmScSchoolCoPlatformDTO.setCooperationPlatform(wmScSchoolCoPlatformDO.getCooperationPlatform());
            wmScSchoolCoPlatformDTO.setPlatformName(wmScSchoolCoPlatformDO.getPlatformName());
            wmScSchoolCoPlatformDTO.setSchoolInPoiOrderCount(wmScSchoolCoPlatformDO.getSchoolInPoiOrderCount());
            wmScSchoolCoPlatformDTO.setSchoolOutPoiOrderCount(wmScSchoolCoPlatformDO.getSchoolOutPoiOrderCount());
            wmScSchoolCoPlatformDTO.setSchoolInOnlinePoiCount(wmScSchoolCoPlatformDO.getSchoolInOnlinePoiCount());
            wmScSchoolCoPlatformDTO.setSchoolOutOnlinePoiCount(wmScSchoolCoPlatformDO.getSchoolOutOnlinePoiCount());
            wmScSchoolCoPlatformDTO.setDeliveryFeeTypeInfo(wmScSchoolCoPlatformDO.getDeliveryFeeTypeInfo());
            wmScSchoolCoPlatformDTO.setCompareCooperationPlatform(wmScSchoolCoPlatformDO.getCompareCooperationPlatform());
            wmScSchoolCoPlatformDTO.setCompareCooperationPlatformInfo(wmScSchoolCoPlatformDO.getCompareCooperationPlatformInfo());
            wmScSchoolCoPlatformDTO.setPlatformAllowToSchool(wmScSchoolCoPlatformDO.getPlatformAllowToSchool());
            wmScSchoolCoPlatformDTO.setSupportFoodUpstairs(wmScSchoolCoPlatformDO.getSupportFoodUpstairs());
            wmScSchoolCoPlatformDTO.setSupportFoodUpstairsInfo(wmScSchoolCoPlatformDO.getSupportFoodUpstairsInfo());
            wmScSchoolCoPlatformDTO.setFoodUpstairsFee(wmScSchoolCoPlatformDO.getFoodUpstairsFee());
            wmScSchoolCoPlatformDTO.setFoodUpstairsReasonInfo(wmScSchoolCoPlatformDO.getFoodUpstairsReasonInfo());
            wmScSchoolCoPlatformDTO.setPlatformEstablishTime(wmScSchoolCoPlatformDO.getPlatformEstablishTime());
            wmScSchoolCoPlatformDTO.setAdvantageAddedServiceInfo(wmScSchoolCoPlatformDO.getAdvantageAddedServiceInfo());
            wmScSchoolCoPlatformDTO.setAdvantageGoodExperienceInfo(wmScSchoolCoPlatformDO.getAdvantageGoodExperienceInfo());
            wmScSchoolCoPlatformDTO.setAdvantageAttractionInfo(wmScSchoolCoPlatformDO.getAdvantageAttractionInfo());
            wmScSchoolCoPlatformDTO.setAdvantagePropagandaInfo(wmScSchoolCoPlatformDO.getAdvantagePropagandaInfo());
            wmScSchoolCoPlatformDTO.setAdvantageExtraInfo(wmScSchoolCoPlatformDO.getAdvantageExtraInfo());
            wmScSchoolCoPlatformDTO.setSupplyDistribution(wmScSchoolCoPlatformDO.getSupplyDistribution());
            wmScSchoolCoPlatformDTO.setMonopolyForm(wmScSchoolCoPlatformDO.getMonopolyForm());
            wmScSchoolCoPlatformDTO.setExtraInfo(wmScSchoolCoPlatformDO.getExtraInfo());
            // 合作平台的收费方式
            List<WmScSchoolCoPlatformFeeTypeDTO> wmScSchoolCoPlatformFeeTypeDTOList;
            wmScSchoolCoPlatformFeeTypeDTOList = JSONObject.parseArray(wmScSchoolCoPlatformDO.getDeliveryFeeType(), WmScSchoolCoPlatformFeeTypeDTO.class);
            wmScSchoolCoPlatformDTO.setWmScSchoolCoPlatformFeeTypeDTOList(wmScSchoolCoPlatformFeeTypeDTOList);
            // 送餐上楼原因
            if (StringUtils.isNotBlank(wmScSchoolCoPlatformDO.getFoodUpstairsReason())) {
                wmScSchoolCoPlatformDTO.setFoodUpstairsReason(Arrays.stream(wmScSchoolCoPlatformDO.getFoodUpstairsReason().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            }
            // 合作平台的优势
            if (StringUtils.isNotBlank(wmScSchoolCoPlatformDO.getPlatformEstablishAdvantage())) {
                wmScSchoolCoPlatformDTO.setPlatformEstablishAdvantage(Arrays.stream(wmScSchoolCoPlatformDO.getPlatformEstablishAdvantage().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            }
            // 学校合作平台营销活动列表
            buildWmScSchoolCoPlatformMarketDTOList(wmScSchoolCoPlatformDO, wmScSchoolCoPlatformDTO);

            wmScSchoolCoPlatformDTOList.add(wmScSchoolCoPlatformDTO);
        }
        return wmScSchoolCoPlatformDTOList;
    }

    /**
     * 转换合作平台营销活动相关信息
     *
     * @param wmScSchoolCoPlatformDO  do参数
     * @param wmScSchoolCoPlatformDTO dto参数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private static void buildWmScSchoolCoPlatformMarketDTOList(WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO, WmScSchoolCoPlatformDTO wmScSchoolCoPlatformDTO) {
        if (wmScSchoolCoPlatformDO == null) {
            return;
        }
        List<WmScSchoolCoPlatformMarketDTO> wmScSchoolCoPlatformMarketDTOList = new ArrayList<>();
        for (WmScSchoolCoPlatformMarketDO wmScSchoolCoPlatformMarketDo : wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList()) {
            if (wmScSchoolCoPlatformMarketDo == null) {
                continue;
            }
            WmScSchoolCoPlatformMarketDTO wmScSchoolCoPlatformMarketDTO = new WmScSchoolCoPlatformMarketDTO();
            wmScSchoolCoPlatformMarketDTO.setId(wmScSchoolCoPlatformMarketDo.getId());
            wmScSchoolCoPlatformMarketDTO.setPlatformPrimaryId(wmScSchoolCoPlatformMarketDo.getPlatformPrimaryId());
            wmScSchoolCoPlatformMarketDTO.setPlatformMarketingActivity(wmScSchoolCoPlatformMarketDo.getPlatformMarketingActivity());
            wmScSchoolCoPlatformMarketDTO.setPlatformMarketingActivityInfo(wmScSchoolCoPlatformMarketDo.getPlatformMarketingActivityInfo());
            wmScSchoolCoPlatformMarketDTO.setActivityRuleDescription(wmScSchoolCoPlatformMarketDo.getActivityRuleDescription());
            wmScSchoolCoPlatformMarketDTO.setActivityPic(wmScSchoolCoPlatformMarketDo.getActivityPic());
            wmScSchoolCoPlatformMarketDTO.setActivityCostSharingType(wmScSchoolCoPlatformMarketDo.getActivityCostSharingType());
            wmScSchoolCoPlatformMarketDTO.setActivityCostSharingPoiMin(wmScSchoolCoPlatformMarketDo.getActivityCostSharingPoiMin());
            wmScSchoolCoPlatformMarketDTO.setActivityCostSharingPoiMax(wmScSchoolCoPlatformMarketDo.getActivityCostSharingPoiMax());
            wmScSchoolCoPlatformMarketDTO.setActivityCostSharingPlatformMin(wmScSchoolCoPlatformMarketDo.getActivityCostSharingPlatformMin());
            wmScSchoolCoPlatformMarketDTO.setActivityCostSharingPlatformMax(wmScSchoolCoPlatformMarketDo.getActivityCostSharingPlatformMax());
            wmScSchoolCoPlatformMarketDTOList.add(wmScSchoolCoPlatformMarketDTO);
        }
        wmScSchoolCoPlatformDTO.setWmScSchoolCoPlatformMarketDTOList(wmScSchoolCoPlatformMarketDTOList);
    }

    /**
     * 学校履约管控信息SaveDTO转DO
     *
     * @param wmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO
     * @return WmScSchoolPerformanceDO
     */
    public static WmScSchoolPerformanceDO transWmScSchoolPerformanceSaveDTOToDO(WmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO) {
        WmScSchoolPerformanceDO wmScSchoolPerformanceDOInsert = new WmScSchoolPerformanceDO();

        wmScSchoolPerformanceDOInsert.setSchoolPrimaryId(wmScSchoolPerformanceSaveDTO.getSchoolPrimaryId());
        wmScSchoolPerformanceDOInsert.setCuid(wmScSchoolPerformanceSaveDTO.getUserId().longValue());
        wmScSchoolPerformanceDOInsert.setMuid(wmScSchoolPerformanceSaveDTO.getUserId().longValue());

        List<WmScSchoolPerformanceUnitDO> wmScSchoolPerformanceUnitDOList = new ArrayList<>();
        for (WmScSchoolPerformanceUnitSaveDTO wmScSchoolPerformanceUnitSaveDTO : wmScSchoolPerformanceSaveDTO.getWmScSchoolPerformanceUnitSaveDTOList()) {
            WmScSchoolPerformanceUnitDO wmScSchoolPerformanceUnitDO = new WmScSchoolPerformanceUnitDO();
            wmScSchoolPerformanceUnitDO.setId(wmScSchoolPerformanceUnitSaveDTO.getId());
            wmScSchoolPerformanceUnitDO.setPerformancePrimaryId(wmScSchoolPerformanceUnitSaveDTO.getPerformancePrimaryId());
            wmScSchoolPerformanceUnitDO.setSchoolInDeliveryType(wmScSchoolPerformanceUnitSaveDTO.getSchoolInDeliveryType());
            wmScSchoolPerformanceUnitDO.setPoiSelfDeliveryType(wmScSchoolPerformanceUnitSaveDTO.getPoiSelfDeliveryType());
            wmScSchoolPerformanceUnitDO.setPoiSelfDeliveryInfo(wmScSchoolPerformanceUnitSaveDTO.getPoiSelfDeliveryInfo());
            wmScSchoolPerformanceUnitDO.setDeliverySpecificLocation(wmScSchoolPerformanceUnitSaveDTO.getDeliverySpecificLocation());
            wmScSchoolPerformanceUnitDO.setDeliveryNotGateReason(wmScSchoolPerformanceUnitSaveDTO.getDeliveryNotGateReason());
            wmScSchoolPerformanceUnitDO.setDeliveryNotGateInfo(wmScSchoolPerformanceUnitSaveDTO.getDeliveryNotGateInfo());
            wmScSchoolPerformanceUnitDO.setDeliveryNotEnterReason(wmScSchoolPerformanceUnitSaveDTO.getDeliveryNotEnterReason());
            wmScSchoolPerformanceUnitDO.setDeliveryNotEnterInfo(wmScSchoolPerformanceUnitSaveDTO.getDeliveryNotEnterInfo());
            wmScSchoolPerformanceUnitDO.setDeliveryEnterReason(wmScSchoolPerformanceUnitSaveDTO.getDeliveryEnterReason());
            wmScSchoolPerformanceUnitDO.setDeliveryEnterInfo(wmScSchoolPerformanceUnitSaveDTO.getDeliveryEnterInfo());
            wmScSchoolPerformanceUnitDO.setDeliveryUpstairsReason(wmScSchoolPerformanceUnitSaveDTO.getDeliveryUpstairsReason());
            wmScSchoolPerformanceUnitDO.setDeliveryUpstairsInfo(wmScSchoolPerformanceUnitSaveDTO.getDeliveryUpstairsInfo());
            wmScSchoolPerformanceUnitDO.setCuid(wmScSchoolPerformanceUnitSaveDTO.getUserId().longValue());
            wmScSchoolPerformanceUnitDO.setMuid(wmScSchoolPerformanceUnitSaveDTO.getUserId().longValue());
            wmScSchoolPerformanceUnitDOList.add(wmScSchoolPerformanceUnitDO);
        }
        wmScSchoolPerformanceDOInsert.setWmScSchoolPerformanceUnitDOList(wmScSchoolPerformanceUnitDOList);
        wmScSchoolPerformanceDOInsert.setSchoolAllowDelivery(wmScSchoolPerformanceSaveDTO.getSchoolAllowDelivery());
        wmScSchoolPerformanceDOInsert.setSchoolAllowDeliveryInfo(wmScSchoolPerformanceSaveDTO.getSchoolAllowDeliveryInfo());
        return wmScSchoolPerformanceDOInsert;
    }

    /**
     * 学校履约管控信息DO转SaveDTO
     *
     * @param wmScSchoolPerformanceDO wmScSchoolPerformanceDO
     * @return WmScSchoolPerformanceDTO
     */
    public static WmScSchoolPerformanceDTO transSchoolPerformanceDOToDTO(WmScSchoolPerformanceDO wmScSchoolPerformanceDO) {
        WmScSchoolPerformanceDTO PerformanceDTO = new WmScSchoolPerformanceDTO();
        if (wmScSchoolPerformanceDO == null) {
            return PerformanceDTO;
        }

        PerformanceDTO.setId(wmScSchoolPerformanceDO.getId());
        PerformanceDTO.setSchoolPrimaryId(wmScSchoolPerformanceDO.getSchoolPrimaryId());
        PerformanceDTO.setSchoolAllowDelivery(wmScSchoolPerformanceDO.getSchoolAllowDelivery());
        PerformanceDTO.setSchoolAllowDeliveryInfo(wmScSchoolPerformanceDO.getSchoolAllowDeliveryInfo());

        List<WmScSchoolPerformanceUnitDTO> wmScSchoolPerformanceUnitDTOList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(wmScSchoolPerformanceDO.getWmScSchoolPerformanceUnitDOList())) {
            return PerformanceDTO;
        }
        for (WmScSchoolPerformanceUnitDO wmScSchoolPerformanceUnitDO : wmScSchoolPerformanceDO.getWmScSchoolPerformanceUnitDOList()) {
            WmScSchoolPerformanceUnitDTO wmScSchoolPerformanceUnitDTO = new WmScSchoolPerformanceUnitDTO();
            wmScSchoolPerformanceUnitDTO.setId(wmScSchoolPerformanceUnitDO.getId());
            wmScSchoolPerformanceUnitDTO.setPerformancePrimaryId(wmScSchoolPerformanceUnitDO.getPerformancePrimaryId());
            wmScSchoolPerformanceUnitDTO.setSchoolInDeliveryType(wmScSchoolPerformanceUnitDO.getSchoolInDeliveryType());
            wmScSchoolPerformanceUnitDTO.setPoiSelfDeliveryType(wmScSchoolPerformanceUnitDO.getPoiSelfDeliveryType());
            wmScSchoolPerformanceUnitDTO.setPoiSelfDeliveryInfo(wmScSchoolPerformanceUnitDO.getPoiSelfDeliveryInfo());
            wmScSchoolPerformanceUnitDTO.setDeliverySpecificLocation(wmScSchoolPerformanceUnitDO.getDeliverySpecificLocation());
            wmScSchoolPerformanceUnitDTO.setDeliveryNotGateReason(wmScSchoolPerformanceUnitDO.getDeliveryNotGateReason());
            wmScSchoolPerformanceUnitDTO.setDeliveryNotGateInfo(wmScSchoolPerformanceUnitDO.getDeliveryNotGateInfo());
            wmScSchoolPerformanceUnitDTO.setDeliveryNotEnterReason(wmScSchoolPerformanceUnitDO.getDeliveryNotEnterReason());
            wmScSchoolPerformanceUnitDTO.setDeliveryNotEnterInfo(wmScSchoolPerformanceUnitDO.getDeliveryNotEnterInfo());
            wmScSchoolPerformanceUnitDTO.setDeliveryEnterReason(wmScSchoolPerformanceUnitDO.getDeliveryEnterReason());
            wmScSchoolPerformanceUnitDTO.setDeliveryEnterInfo(wmScSchoolPerformanceUnitDO.getDeliveryEnterInfo());
            wmScSchoolPerformanceUnitDTO.setDeliveryUpstairsReason(wmScSchoolPerformanceUnitDO.getDeliveryUpstairsReason());
            wmScSchoolPerformanceUnitDTO.setDeliveryUpstairsInfo(wmScSchoolPerformanceUnitDO.getDeliveryUpstairsInfo());
            wmScSchoolPerformanceUnitDTOList.add(wmScSchoolPerformanceUnitDTO);
        }
        PerformanceDTO.setWmScSchoolPerformanceUnitDTOList(wmScSchoolPerformanceUnitDTOList);

        return PerformanceDTO;
    }

    /**
     * 食堂档口线索DO列表转DTO列表
     * @param clueDOList clueDOList
     * @param phoneNumTextMap 手机号明文Map
     * @return List<WmCanteenStallClueDTO>
     */
    public static List<WmCanteenStallClueDTO> transCanteenStallClueDOsToDTOs(List<WmCanteenStallClueDO> clueDOList,
                                                                             Map<String, String> phoneNumTextMap,
                                                                             Map<Long, String> poiCateMap,
                                                                             Map<Integer, WmOpenCity> clueCityMap) {
        List<WmCanteenStallClueDTO> clueDetailDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(clueDOList)) {
            return clueDetailDTOList;
        }

        for (WmCanteenStallClueDO clueDO : clueDOList) {
            WmCanteenStallClueDTO detailDTO = new WmCanteenStallClueDTO();
            detailDTO.setId(clueDO.getId());
            detailDTO.setManageId(clueDO.getManageId());
            detailDTO.setBindId(clueDO.getBindId());
            detailDTO.setCanteenPrimaryId(clueDO.getCanteenPrimaryId());
            detailDTO.setCluePoiName(clueDO.getCluePoiName());
            detailDTO.setClueLeafCateId(clueDO.getClueLeafCateId());
            detailDTO.setCluePoiCate(poiCateMap.get(clueDO.getClueLeafCateId()));
            detailDTO.setCluePoiAddress(clueDO.getCluePoiAddress());
            detailDTO.setSecondCityId(clueDO.getClueSecondCityId());
            detailDTO.setSecondCityName(clueCityMap.get(clueDO.getClueSecondCityId()).getCityName());
            detailDTO.setThirdCityId(clueDO.getClueThirdCityId());
            detailDTO.setThirdCityName(clueCityMap.get(clueDO.getClueThirdCityId()).getCityName());
            detailDTO.setCluePoiCoordinate(clueDO.getCluePoiCoordinate());
            detailDTO.setCluePoiPhoneNum(phoneNumTextMap.get(clueDO.getCluePoiPhoneEncryption()));
            clueDetailDTOList.add(detailDTO);
        }

        return clueDetailDTOList;
    }


    public static List<WmCanteenStallManageDTO> transCanteenStallManageDOsToDTOs(List<WmCanteenStallManageDO> manageDOList) {
        List<WmCanteenStallManageDTO> manageDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(manageDOList)) {
            return manageDTOList;
        }

        for (WmCanteenStallManageDO manageDO : manageDOList) {
            WmCanteenStallManageDTO manageDTO = new WmCanteenStallManageDTO();
            manageDTO.setId(manageDO.getId());
            manageDTO.setCanteenPrimaryId(manageDO.getCanteenPrimaryId());
            manageDTO.setCtime(manageDO.getCtime());
            manageDTO.setCuid(manageDO.getCuid());
            manageDTO.setSubmitStatus(manageDO.getSubmitStatus());
            manageDTO.setTaskType(manageDO.getTaskType());
            manageDTOList.add(manageDTO);
        }
        return manageDTOList;
    }


    public static List<WmCanteenStallBindDTO> transCanteenStallBindDOsToDTOs(List<WmCanteenStallBindDO> bindDOList) {
        List<WmCanteenStallBindDTO> bindDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(bindDOList)) {
            return bindDTOList;
        }

        for (WmCanteenStallBindDO bindDO : bindDOList) {
            WmCanteenStallBindDTO dto = new WmCanteenStallBindDTO();
            dto.setId(bindDO.getId());
            dto.setCanteenPrimaryId(bindDO.getCanteenPrimaryId());
            dto.setWmPoiId(bindDO.getWmPoiId());

            dto.setWdcClueId(bindDO.getWdcClueId());
            dto.setClueGenerateStatus(bindDO.getClueGenerateStatus());
            dto.setClueGenerateFailReason(bindDO.getClueGenerateFailReason());

            dto.setClueBindStatus(bindDO.getClueBindStatus());
            dto.setClueBindFailReason(bindDO.getClueBindFailReason());

            dto.setWmPoiBindStatus(bindDO.getWmPoiBindStatus());
            dto.setWmPoiBindFailReason(bindDO.getWmPoiBindFailReason());

            dto.setAuditStatus(bindDO.getAuditStatus());
            dto.setClueFollowUpStatus(bindDO.getClueFollowUpStatus());
            dto.setCtime(bindDO.getCtime());
            dto.setUtime(bindDO.getUtime());
            bindDTOList.add(dto);
        }
        return bindDTOList;
    }


    public static WmCanteenStallAuditTaskDTO transCanteenStallAuditTaskDOToDTO(WmCanteenStallAuditTaskDO taskDO) {
        WmCanteenStallAuditTaskDTO taskDTO = new WmCanteenStallAuditTaskDTO();
        if (taskDO == null) {
            return taskDTO;
        }

        taskDTO.setAuditTaskType(taskDO.getAuditTaskType());
        taskDTO.setId(taskDO.getId());
        taskDTO.setCanteenPrimaryId(taskDO.getCanteenPrimaryId());
        taskDTO.setAbnormalReason(taskDO.getAbnormalReason());

        if (StringUtils.isNotBlank(taskDO.getProofPicture())) {
            List<String> picList = Arrays.stream(taskDO.getProofPicture().split(","))
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            taskDTO.setProofPictureList(picList);
        }
        return taskDTO;
    }

    /**
     * 品类树列表将品类转换为品类String
     * @param wmPoiCateDicList 品类树列表
     * @return 品类String
     */
    public static String transPoiCateDicListToCate(List<WmPoiCateDic> wmPoiCateDicList) {
        if (CollectionUtils.isEmpty(wmPoiCateDicList)) {
            return "-";
        }

        List<String> cateNameList = wmPoiCateDicList.stream()
                .map(WmPoiCateDic::getName)
                .collect(Collectors.toList());
        return StringUtils.join(cateNameList, "/");
    }


    /**
     * 将履约侧消息结构转换为学校系统的WmScSchoolAoiBO
     * @param bmAoiAttrView bmAoiAttrView
     * @return WmScSchoolAoiBO
     */
    public static WmScSchoolAoiBO transBmAoiAttrViewToSchoolAoiBo(BmAoiAttrView bmAoiAttrView) {
        WmScSchoolAoiBO wmScSchoolAoiBO = new WmScSchoolAoiBO();
        if (bmAoiAttrView == null) {
            return wmScSchoolAoiBO;
        }

        wmScSchoolAoiBO.setAoiId(bmAoiAttrView.getBmAoiId());
        wmScSchoolAoiBO.setAoiName(bmAoiAttrView.getName() == null ? "" : bmAoiAttrView.getName());
        // 如果从履约侧没有获取到通行属性, 则设置为默认值-2
        wmScSchoolAoiBO.setAoiMode(bmAoiAttrView.getDetailedTrafficType() == null ? -2 : bmAoiAttrView.getDetailedTrafficType().getValue());
        // 设置美配通行属性枚举值
        SchoolAoiModeEnum schoolAoiModeEnum = SchoolAoiModeEnum.getByType(wmScSchoolAoiBO.getAoiMode());
        wmScSchoolAoiBO.setAoiModeDesc(schoolAoiModeEnum == null ? "-" : schoolAoiModeEnum.getName());
        wmScSchoolAoiBO.setAoiArea(transBmPointListToAreaStr(bmAoiAttrView.getPolygon()));
        return wmScSchoolAoiBO;
    }

    /**
     * 将履约侧坐标点列表转换为学校侧坐标点
     * @param bmPointList 履约侧坐标点列表
     * @return 转换为学校侧坐标点str
     */
    public static String transBmPointListToAreaStr(List<BmPoint> bmPointList) {
        if (CollectionUtils.isEmpty(bmPointList)) {
            return "";
        }

        List<WmScPoint> wmScPointList = new ArrayList<>();
        for (BmPoint bmPoint : bmPointList) {
            WmScPoint wmScPoint = new WmScPoint();
            wmScPoint.setX(bmPoint.getLat());
            wmScPoint.setY(bmPoint.getLng());
            wmScPointList.add(wmScPoint);
        }
        return JSON.toJSONString(wmScPointList);
    }

    /**
     * 学校楼宇信息BO列表转DTO列表
     * @param wmScSchoolBuildingDetailBOList 学校楼宇信息BO列表
     * @return List<WmScSchoolBuildingDetailDTO>
     */
    public static List<WmScSchoolBuildingDetailDTO> transSchoolBuildingDetailBoToDto(List<WmScSchoolBuildingDetailBO> wmScSchoolBuildingDetailBOList) {
        List<WmScSchoolBuildingDetailDTO> wmScSchoolBuildingDetailDTOList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(wmScSchoolBuildingDetailBOList)) {
            return wmScSchoolBuildingDetailDTOList;
        }
        for (WmScSchoolBuildingDetailBO wmScSchoolBuildingDetailBO : wmScSchoolBuildingDetailBOList) {
            WmScSchoolBuildingDetailDTO wmScSchoolBuildingDetailDTO = new WmScSchoolBuildingDetailDTO();
            BeanUtils.copyProperties(wmScSchoolBuildingDetailBO, wmScSchoolBuildingDetailDTO);
            wmScSchoolBuildingDetailDTOList.add(wmScSchoolBuildingDetailDTO);
        }
        return wmScSchoolBuildingDetailDTOList;
    }


    /**
     * 根据学校楼宇DO列表转换得到楼宇BO列表
     * @param wmScSchoolBuildingDOList wmScSchoolBuildingDOList
     * @return List<WmScSchoolBuildingDetailBO>
     */
    public static List<WmScSchoolBuildingDetailBO> transWmScSchoolBuildingDetailDOListToBOList(List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList) {
        List<WmScSchoolBuildingDetailBO> wmScSchoolBuildingDetailBOList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            return wmScSchoolBuildingDetailBOList;
        }
        for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
            WmScSchoolBuildingDetailBO wmScSchoolBuildingDetailBO = new WmScSchoolBuildingDetailBO();
            wmScSchoolBuildingDetailBO.setBuildingId(wmScSchoolBuildingDO.getId());
            wmScSchoolBuildingDetailBO.setBuildingPoiId(wmScSchoolBuildingDO.getBuildingPoiId());
            wmScSchoolBuildingDetailBO.setBuildingName(wmScSchoolBuildingDO.getBuildingName());
            wmScSchoolBuildingDetailBO.setBuildingNickname(wmScSchoolBuildingDO.getBuildingNickname());
            wmScSchoolBuildingDetailBO.setBuildingArea(wmScSchoolBuildingDO.getBuildingArea());
            wmScSchoolBuildingDetailBO.setBuildingType(wmScSchoolBuildingDO.getBuildingType());
            wmScSchoolBuildingDetailBO.setBuildingFloor(wmScSchoolBuildingDO.getBuildingFloor());
            wmScSchoolBuildingDetailBO.setBuildingPersonNum(wmScSchoolBuildingDO.getBuildingPersonNum());
            wmScSchoolBuildingDetailBO.setBuildingLocation(wmScSchoolBuildingDO.getBuildingLocation());
            wmScSchoolBuildingDetailBO.setBuildingCoordinate(wmScSchoolBuildingDO.getBuildingCoordinate());
            wmScSchoolBuildingDetailBO.setBuildingElevator(wmScSchoolBuildingDO.getBuildingElevator());
            wmScSchoolBuildingDetailBO.setBuildingStatus(wmScSchoolBuildingDO.getBuildingStatus());
            wmScSchoolBuildingDetailBOList.add(wmScSchoolBuildingDetailBO);
        }
        return wmScSchoolBuildingDetailBOList;
    }

    /**
     * 学校楼宇信息DO转DTO
     * @param wmScSchoolBuildingDO wmScSchoolBuildingDO
     * @return WmScSchoolBuildingDetailDTO
     */
    public static WmScSchoolBuildingDetailDTO transSchoolBuildingDOToDetailDTO(WmScSchoolBuildingDO wmScSchoolBuildingDO) {
        WmScSchoolBuildingDetailDTO dto = new WmScSchoolBuildingDetailDTO();
        if (wmScSchoolBuildingDO == null) {
            return dto;
        }

        dto.setBuildingArea(wmScSchoolBuildingDO.getBuildingArea());
        dto.setBuildingFloor(wmScSchoolBuildingDO.getBuildingFloor());
        dto.setBuildingElevator(wmScSchoolBuildingDO.getBuildingElevator());
        dto.setBuildingNickname(wmScSchoolBuildingDO.getBuildingNickname());
        dto.setBuildingName(wmScSchoolBuildingDO.getBuildingName());
        dto.setBuildingPersonNum(wmScSchoolBuildingDO.getBuildingPersonNum());
        dto.setBuildingCoordinate(wmScSchoolBuildingDO.getBuildingCoordinate());
        dto.setBuildingId(wmScSchoolBuildingDO.getId());
        dto.setBuildingLocation(wmScSchoolBuildingDO.getBuildingLocation());
        dto.setBuildingType(wmScSchoolBuildingDO.getBuildingType());
        dto.setBuildingStatus(wmScSchoolBuildingDO.getBuildingStatus());
        dto.setBuildingPoiId(wmScSchoolBuildingDO.getBuildingPoiId());

        return dto;
    }


    /**
     * 将履约侧POI信息列表转化为学校POI信息列表
     * @param bmLbsPoiList 履约侧POI列表
     * @return List<WmScSchoolBuildingPoiBO>
     */
    public static List<WmScSchoolBuildingPoiBO> transBmLbsPoiListToSchoolBuildingPoiBOList(List<BmLbsPoi> bmLbsPoiList) {
        List<WmScSchoolBuildingPoiBO> wmScSchoolBuildingPoiBOList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(bmLbsPoiList)) {
            return wmScSchoolBuildingPoiBOList;
        }

        for (BmLbsPoi bmLbsPoi : bmLbsPoiList) {
            WmScSchoolBuildingPoiBO wmScSchoolBuildingPoiBO = new WmScSchoolBuildingPoiBO();
            wmScSchoolBuildingPoiBO.setPoiId(bmLbsPoi.getId());
            wmScSchoolBuildingPoiBO.setPoiName(bmLbsPoi.getName());
            wmScSchoolBuildingPoiBO.setPoiCoordinate(getPoiCoordinateByLatAndLng(bmLbsPoi.getLat(), bmLbsPoi.getLng()));
            wmScSchoolBuildingPoiBOList.add(wmScSchoolBuildingPoiBO);
        }
        return wmScSchoolBuildingPoiBOList;
    }

    /**
     * 根据经纬度组织构建学校坐标点Str
     * @param lat 纬度
     * @param lng 经度
     * @return 学校坐标点Str。格式: [{"x":12341,"y":8612312}]
     */
    public static String getPoiCoordinateByLatAndLng(int lat, int lng) {
        List<WmScPoint> wmScPointList = new ArrayList<>();
        WmScPoint wmScPoint = new WmScPoint();
        wmScPoint.setX(lat);
        wmScPoint.setY(lng);
        wmScPointList.add(wmScPoint);
        return JSONObject.toJSONString(wmScPointList);
    }


    public static WmCanteenAuditBO transCanteenAuditDOToCanteenAuditBO(WmScCanteenAuditDO auditDO) {
        if (auditDO == null) {
            return null;
        }

        WmCanteenAuditBO canteenAuditBO = new WmCanteenAuditBO();
        canteenAuditBO.setId(auditDO.getId());
        canteenAuditBO.setCanteenId(auditDO.getCanteenId());
        canteenAuditBO.setSchoolId(auditDO.getSchoolId());
        canteenAuditBO.setContractorId(auditDO.getContractorId());
        canteenAuditBO.setCanteenName(auditDO.getCanteenName());
        canteenAuditBO.setCanteenType(auditDO.getCanteenType());
        canteenAuditBO.setCanteenAttribute(auditDO.getCanteenAttribute());
        canteenAuditBO.setAttributeDesc(auditDO.getAttributeDesc());
        canteenAuditBO.setGrade(auditDO.getGrade());
        canteenAuditBO.setCanteenClient(auditDO.getCanteenClient());
        canteenAuditBO.setTypeDesc(auditDO.getTypeDesc());
        canteenAuditBO.setManager(auditDO.getManager());
        canteenAuditBO.setManagerPhone(auditDO.getManagerPhone());
        canteenAuditBO.setCanteenStatus(auditDO.getCanteenStatus());
        canteenAuditBO.setStallNum(auditDO.getStallNum());
        canteenAuditBO.setStoreNum(auditDO.getStoreNum());
        canteenAuditBO.setCityName(auditDO.getCityName());
        canteenAuditBO.setResponsiblePerson(auditDO.getResponsiblePerson());
        canteenAuditBO.setSchoolName(auditDO.getSchoolName());
        canteenAuditBO.setContractorName(auditDO.getContractorName());
        canteenAuditBO.setExt(auditDO.getExt());
        canteenAuditBO.setCardType(auditDO.getCardType());
        canteenAuditBO.setCardTypeDesc(auditDO.getCardTypeDesc());
        canteenAuditBO.setCardNo(auditDO.getCardNo());
        canteenAuditBO.setEffective(auditDO.getEffective());
        canteenAuditBO.setAuditStatus(auditDO.getAuditStatus());
        canteenAuditBO.setAuditTime(auditDO.getAuditTime());
        canteenAuditBO.setAuditResult(auditDO.getAuditResult());
        canteenAuditBO.setTaskId(auditDO.getTaskId());
        canteenAuditBO.setAuditUserId(auditDO.getAuditUserId());
        canteenAuditBO.setManagerPhoneEncryption(auditDO.getManagerPhoneEncryption());
        canteenAuditBO.setManagerPhoneToken(auditDO.getManagerPhoneToken());
        canteenAuditBO.setCardNoEncryption(auditDO.getCardNoEncryption());
        canteenAuditBO.setCardNoToken(auditDO.getCardNoToken());
        canteenAuditBO.setOfflineBizStallNum(auditDO.getOfflineBizStallNum());
        canteenAuditBO.setPreOnlineStallNum(auditDO.getPreOnlineStallNum());
        canteenAuditBO.setCanteenVideo(auditDO.getCanteenVideo());

        canteenAuditBO.setStallNumChangeReason(auditDO.getStallNumChangeReason());
        CanteenStallNumChangeReasonEnum stallNumChangeReasonEnum = CanteenStallNumChangeReasonEnum.getByType(auditDO.getStallNumChangeReason());
        canteenAuditBO.setStallNumChangeReasonDesc(stallNumChangeReasonEnum == null ? "-" : stallNumChangeReasonEnum.getName());
        canteenAuditBO.setStallNumChangeInfo(auditDO.getStallNumChangeInfo());

        canteenAuditBO.setOfflineBizStallNumChangeReason(auditDO.getOfflineBizStallNumChangeReason());
        CanteenStallNumChangeReasonEnum offlineBizStallNumChangeReasonEnum = CanteenStallNumChangeReasonEnum.getByType(auditDO.getOfflineBizStallNumChangeReason());
        canteenAuditBO.setOfflineBizStallNumChangeInfo(auditDO.getOfflineBizStallNumChangeInfo());
        canteenAuditBO.setOfflineBizStallNumChangeReasonDesc(offlineBizStallNumChangeReasonEnum == null ? "" : offlineBizStallNumChangeReasonEnum.getName());

        canteenAuditBO.setGravityId(auditDO.getGravityId());
        canteenAuditBO.setAuditTaskType(auditDO.getAuditTaskType());
        canteenAuditBO.setAuditNode(auditDO.getAuditNode());

        return canteenAuditBO;
    }
}
