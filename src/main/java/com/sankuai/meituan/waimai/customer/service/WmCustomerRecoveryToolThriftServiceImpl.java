package com.sankuai.meituan.waimai.customer.service;

import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRecoveryToolService;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerRecoveryToolThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WmCustomerRecoveryToolThriftServiceImpl implements WmCustomerRecoveryToolThriftService {

    @Autowired
    private WmCustomerRecoveryToolService wmCustomerRecoveryToolService;

    @Override
    public void recoverConfirmBind(long taskId, int taskStatus) throws TException, WmCustomerException {
        wmCustomerRecoveryToolService.recoverConfirmBind(taskId, taskStatus);
    }

    @Override
    public void recoverConfirmUnBind(long taskId, int taskStatus) throws TException, WmCustomerException {
        wmCustomerRecoveryToolService.recoverConfirmUnBind(taskId,taskStatus);
    }
}
