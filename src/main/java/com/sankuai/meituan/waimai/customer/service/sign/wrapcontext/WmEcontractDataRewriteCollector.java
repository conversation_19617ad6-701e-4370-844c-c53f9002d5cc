package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import java.util.List;

import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignFlowConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import lombok.extern.slf4j.Slf4j;

/**
 * 实现数据重写,处理特殊逻辑
 */
@Service
@Slf4j
public class WmEcontractDataRewriteCollector implements IWmEcontractDataCollector {

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext)
            throws WmCustomerException, IllegalAccessException {
        log.info("#WmEcontractDataRewriteCollector");
        //各个模块的流程和H5布局有映射关系，需要做出映射
        List<String> moduleList = middleContext.getModuleList();
        String econtractType = targetContext.getEcontractType();
        String actuallyEcontractType = "";
        //1.独立模块
        if (moduleList.size() == 1) {
            switch (moduleList.get(0)) {
                case SignTemplateConstant.MODEL_C1CONTRACT:
                    actuallyEcontractType = SignFlowConstant.SINGLE_C1CONTRACT_MAP.get(econtractType);
                    break;
                case SignTemplateConstant.MODEL_SETTLE:
                    actuallyEcontractType = SignFlowConstant.SINGLE_SETTLE_MAP.get(econtractType);
                    break;
                case SignTemplateConstant.MODEL_DELIVERY:
                case SignTemplateConstant.MODEL_NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY:
                    actuallyEcontractType = SignFlowConstant.SINGLE_DELIVERY_MAP.get(econtractType);
                    break;
            }
        }
        //2.打包模块
        else {
            actuallyEcontractType = SignFlowConstant.BATCH_MAP.get(econtractType);
        }

        if (StringUtils.isEmpty(actuallyEcontractType)) {
            log.error("签约流程模板组装失败 originContext={},middleContext={},targetContext={}", JSONObject.toJSONString(originContext),
                    JSONObject.toJSONString(middleContext), JSONObject.toJSONString(targetContext));
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "签约流程模板组装失败");
        }
        targetContext.setEcontractType(actuallyEcontractType);
    }
}
