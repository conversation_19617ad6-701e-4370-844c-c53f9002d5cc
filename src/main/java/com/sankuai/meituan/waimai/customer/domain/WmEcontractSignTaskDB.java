package com.sankuai.meituan.waimai.customer.domain;

import com.sankuai.meituan.waimai.customer.annotation.Encryption;
import com.sankuai.meituan.waimai.customer.constant.EncryptionTypeConstant;
import lombok.Data;

/**
 * 签约任务信息
 */
@Data
@Encryption(fields = {"applyContext"}, recordType = EncryptionTypeConstant.ECONTRACT_SIGN_TASK_RECORD, isJSON = true)
public class WmEcontractSignTaskDB {

    private Long id;

    private String applyState;

    private Integer customerId;

    private Integer bizId;

    private Long batchId;

    private String applyType;

    private String recordId;

    private String applyContext;

    private String resultContext;

    private Byte valid;

    private Long ctime;

    private Long utime;

    private Integer version;

    private Integer commitUid;

    private Long manualBatchId;

    private String objectName;

    private Integer upstreamStatus;

}
