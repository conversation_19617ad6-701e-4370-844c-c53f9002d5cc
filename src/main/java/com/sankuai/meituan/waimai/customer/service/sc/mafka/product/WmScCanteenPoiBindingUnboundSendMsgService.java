package com.sankuai.meituan.waimai.customer.service.sc.mafka.product;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.DbusUtils;
import com.sankuai.meituan.waimai.customer.constant.sc.ScMafkaTopicEnum;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAttributeDO;
import com.sankuai.meituan.waimai.customer.service.sc.mafka.product.msg.ScCanteenPoiBindingAndUnboundMsg;
import com.sankuai.meituan.waimai.customer.service.sc.mafka.product.msg.ScCommonMsg;
import com.sankuai.meituan.waimai.customer.service.sc.mafka.product.strategy.ScMafkaSend;
import com.sankuai.meituan.waimai.customer.service.sc.mafka.product.strategy.ScMafkaSendHandleService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScCommonMsgTopic;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ValidEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 门店绑定和解绑食堂发送mq消息
 */
@Slf4j
@Service
public class WmScCanteenPoiBindingUnboundSendMsgService {


    @Autowired
    private ScMafkaSendHandleService scMafkaSendHandleService;

    /**
     * 逻辑删除字段
     */
    private final String VALID_COLUMN = "valid";

    /**
     * 食堂主键Id字段
     */
    private final String CANTEEN_PRIMARY_ID_COLUMN = "canteen_primary_id";


    /**
     * 当新增的时候发送门店绑定/解绑消息
     *
     * @param utils DTS中转换的对象
     */
    public void sendMsgWhenTableInsert(DbusUtils utils) {
        try {
            if (utils == null || MapUtils.isEmpty(utils.getAftMap())) {
                return;
            }

            WmScCanteenPoiAttributeDO attributeDO = transToBo(utils);
            if (attributeDO == null) {
                return;
            }

            ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg> msg = makeMsg(attributeDO);
            ScMafkaSend send = new ScMafkaSend();
            send.setTopic(ScMafkaTopicEnum.MAFKA_WAIMAI_SC_COMMON_MESSAGE_TOPIC);
            send.setMsg(JSON.toJSONString(msg));
            scMafkaSendHandleService.send(send);
        } catch (Exception e) {
            log.error("sendMsgWhenTableInsert utils={}", JSON.toJSONString(utils), e);
        }
    }

    /**
     * 获取绑定/解绑消息类型
     *
     * @param bean
     * @return
     */
    private String getMsgTopic(WmScCanteenPoiAttributeDO bean) {
        if (bean.getValid() != null && ValidEnum.VALID.getTypeInt() == bean.getValid()) {
            return ScCommonMsgTopic.SC_CANTEEN_POI_BINDING.getCode();
        }
        return ScCommonMsgTopic.SC_CANTEEN_POI_UNBOUND.getCode();
    }

    /**
     * 当更新的时候发送绑定和解绑MQ消息
     *
     * @param utils DTS中转换的对象
     */
    public void sendMsgWhenTableUpdate(DbusUtils utils) {
        try {
            if (utils == null || MapUtils.isEmpty(utils.getAftMap()) || MapUtils.isEmpty(utils.getDiffMap())) {
                return;
            }

            Map<String, Object> diffMap = utils.getDiffMap();
            if (!isEffectChange(diffMap)) {
                return;
            }

            WmScCanteenPoiAttributeDO attributeDO = transToBo(utils);
            ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg> msg = makeMsg(attributeDO);

            ScMafkaSend send = new ScMafkaSend();
            send.setTopic(ScMafkaTopicEnum.MAFKA_WAIMAI_SC_COMMON_MESSAGE_TOPIC);
            send.setMsg(JSON.toJSONString(msg));
            scMafkaSendHandleService.send(send);
        } catch (Exception e) {
            log.error("[WmScCanteenPoiBindingUnboundSendMsgService.sendMsgWhenTableUpdate] Exception. utils = {}", JSON.toJSONString(utils), e);
        }
    }

    /**
     * 当更新的时候发送换绑MQ消息
     *
     * @param utils DTS中转换的对象
     */
    public void sendChangeBindMsgWhenTableUpdate(DbusUtils utils) {
        try {
            if (utils == null || MapUtils.isEmpty(utils.getAftMap()) || MapUtils.isEmpty(utils.getDiffMap())) {
                return;
            }

            Map<String, Object> diffMap = utils.getDiffMap();
            if (!isChangeBindChange(diffMap)) {
                return;
            }

            Map<String, Object> aftMap = utils.getAftMap();
            String aftJsonString = JSON.toJSONString(aftMap);
            WmScCanteenPoiAttributeDO afterBean = JSON.parseObject(aftJsonString, WmScCanteenPoiAttributeDO.class);

            Map<String, Object> preMap = utils.getPreMap();
            String preJsonString = JSON.toJSONString(preMap);
            WmScCanteenPoiAttributeDO preBean = JSON.parseObject(preJsonString, WmScCanteenPoiAttributeDO.class);

            ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg> msg = makeChangeBindMsg(afterBean, preBean);
            ScMafkaSend send = new ScMafkaSend();
            send.setTopic(ScMafkaTopicEnum.MAFKA_WAIMAI_SC_COMMON_MESSAGE_TOPIC);
            send.setMsg(JSON.toJSONString(msg));
            scMafkaSendHandleService.send(send);
        } catch (Exception e) {
            log.error("[WmScCanteenPoiBindingUnboundSendMsgService.sendChangeBindMsgWhenTableUpdate] utils = {}", JSON.toJSONString(utils), e);
        }
    }

    private ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg> makeMsg(WmScCanteenPoiAttributeDO bean) {
        ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg> msg = new ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg>();
        msg.setId(bean.getWmPoiId());
        msg.setTopic(getMsgTopic(bean));
        msg.setUtime(bean.getUtime());
        if (msg.getTopic().equals(ScCommonMsgTopic.SC_CANTEEN_POI_BINDING.getCode())) {
            // 绑定
            ScCanteenPoiBindingAndUnboundMsg after = new ScCanteenPoiBindingAndUnboundMsg();
            after.setCanteenId(bean.getCanteenId());
            after.setWmPoiId(bean.getWmPoiId());
            msg.setAfter(after);
            msg.setBefore(new ScCanteenPoiBindingAndUnboundMsg());
        } else {
            // 解绑
            ScCanteenPoiBindingAndUnboundMsg before = new ScCanteenPoiBindingAndUnboundMsg();
            before.setCanteenId(bean.getCanteenId());
            before.setWmPoiId(bean.getWmPoiId());
            msg.setBefore(before);
            msg.setAfter(new ScCanteenPoiBindingAndUnboundMsg());
        }
        return msg;
    }

    private ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg> makeChangeBindMsg(WmScCanteenPoiAttributeDO afterBean,
                                                                            WmScCanteenPoiAttributeDO beforeBean) {
        // 换绑后门店信息
        ScCanteenPoiBindingAndUnboundMsg after = new ScCanteenPoiBindingAndUnboundMsg();
        after.setCanteenId(afterBean.getCanteenId());
        // 换绑前门店信息
        ScCanteenPoiBindingAndUnboundMsg before = new ScCanteenPoiBindingAndUnboundMsg();
        before.setCanteenId(beforeBean.getCanteenId());

        ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg> msg = new ScCommonMsg<ScCanteenPoiBindingAndUnboundMsg>();
        msg.setId(afterBean.getWmPoiId());
        msg.setTopic(ScCommonMsgTopic.SC_CANTEEN_POI_CHANGE_BINDING.getCode());
        msg.setUtime(afterBean.getUtime());
        msg.setAfter(after);
        msg.setBefore(before);
        return msg;
    }


    /**
     * 是否绑定/解绑变更
     *
     * @param diffMap
     * @return
     * @throws WmSchCantException
     */
    private boolean isEffectChange(Map<String, Object> diffMap) throws WmSchCantException {
        if (diffMap.containsKey(VALID_COLUMN)) {
            return true;
        }
        return false;
    }

    /**
     * 是否换绑变更
     *
     * @param diffMap
     * @return
     * @throws WmSchCantException
     */
    private boolean isChangeBindChange(Map<String, Object> diffMap) throws WmSchCantException {
        if (diffMap.containsKey(CANTEEN_PRIMARY_ID_COLUMN)) {
            return true;
        }
        return false;
    }


    private WmScCanteenPoiAttributeDO transToBo(DbusUtils utils) {
        Map<String, Object> aftMap = utils.getAftMap();
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmScCanteenPoiAttributeDO.class);
    }

}
