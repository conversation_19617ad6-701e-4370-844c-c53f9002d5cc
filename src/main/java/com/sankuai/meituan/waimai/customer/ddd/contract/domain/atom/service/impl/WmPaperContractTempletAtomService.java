package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.RejectReasonUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditCommitObj;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.vo.WmFrameContractCommitData;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class WmPaperContractTempletAtomService extends AbstractWmContractTempletAtomService {

    private static Logger logger = LoggerFactory.getLogger(WmPaperContractTempletAtomService.class);

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws
            WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "纸质合同不需要签约");
    }

    @Override
    public Boolean signFail(long templetContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "纸质合同不需要签约");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        Integer update = super.update(contractBo, opUid, opName);
        wmTempletContractDBMapper.updateNumberById(contractBo.getBasicBo().getTempletContractId(), contractBo.getBasicBo().getContractNum());
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer commitAudit(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("合同提审开始 contractBo：{}  opUid:{}  opUname:{}", JSON.toJSON(contractBo), opUid, opName);
        ContractCheckFilter.contractCommitAuditValidFilter().filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = WmContractAggre.Factory.make(contractBo.getBasicBo().getTempletContractId())
                .getWmCustomerContractBoById(false, opUid, opName);
        Integer contractId = insertOrUpdate(contractBo, opUid, opName);
        contractBo.getBasicBo().setTempletContractId(contractId);
        contractLogAggre.logUpdate(oldBo, contractBo, opUid, opName);

        WmAuditCommitObj wmAuditCommitObj = buildWmAuditCommitObj(contractBo, opUid);
        try {
            wmAuditApiService.commitAudit(wmAuditCommitObj);
        } catch (WmServerException e) {
            logger.info("提审失败，原因：" + e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        }
        toNextContractStatus(contractId, CustomerContractStatus.AUDITING.getCode(), opName);
        contractBo.getBasicBo().setStatus(CustomerContractStatus.AUDITING.getCode());
        contractLogAggre.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);
        logger.info("合同提审成功 templetId：{}  opUid:{}  opUname:{}", contractBo.getBasicBo().getTempletContractId(), opUid, opName);
        return contractId;
    }

    private WmAuditCommitObj buildWmAuditCommitObj(WmCustomerContractBo contractBo, int opUid)
            throws WmCustomerException {
        WmAuditCommitObj wmAuditCommitObj = new WmAuditCommitObj();

        WmFrameContractCommitData data = buildWmFrameContractData(contractBo);

        wmAuditCommitObj.setData(JSON.toJSONString(data));
        wmAuditCommitObj.setBiz_id((int) contractBo.getBasicBo().getTempletContractId());
        if (new WmTempletContractTypeBo(contractBo.getBasicBo().getType()).getCooperateMode() == WmTempletContractTypeBo.TYPE_C1) {
            wmAuditCommitObj.setBiz_type(WmAuditTaskBizTypeConstant.FRAME_CONTRACT_C1);
        } else {
            wmAuditCommitObj.setBiz_type(WmAuditTaskBizTypeConstant.FRAME_CONTRACT_C2);
        }
        wmAuditCommitObj.setSubmit_uid(opUid);
        wmAuditCommitObj.setCustomer_id(contractBo.getBasicBo().getParentId());
        return wmAuditCommitObj;
    }

    private WmFrameContractCommitData buildWmFrameContractData(WmCustomerContractBo contractBo)
            throws WmCustomerException {

        WmContractSignAggre wmContractSignAggre = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList());
        WmTempletContractSignBo partyASigner = wmContractSignAggre.getPartyASignerBo();
        WmTempletContractSignBo partyBSigner = wmContractSignAggre.getPartyBSignerBo();

        CustomerPaperContractRemarkBo remarkBo = JSON.parseObject(contractBo.getBasicBo().getExtStr(), CustomerPaperContractRemarkBo.class);

        WmFrameContractCommitData data = new WmFrameContractCommitData();
        data.setContractId((int) contractBo.getBasicBo().getTempletContractId());
        data.setContractNo(contractBo.getBasicBo().getContractNum());
        data.setContractExpireDate(contractBo.getBasicBo().getDueDate());
        data.setContractUrl(remarkBo.getContractScan().getList().get(0).getUrl());

        if (remarkBo.getOtherContractScan() != null && remarkBo.getOtherContractScan().getList() != null) {
            List<String> otherContractScan = Lists.newArrayList();
            for (MultiFileJsonBo.CustomerFile s : remarkBo.getOtherContractScan().getList()) {
                otherContractScan.add(s.getUrl());
            }
            data.setOtherContractScans(otherContractScan);
        }
        WmCustomerDB customerDB = wmCustomerService.selectCustomerById(contractBo.getBasicBo().getParentId());
        data.setFirstPartyAddress(customerDB.getAddress());
        data.setOwnerName(customerDB.getLegalPerson());
        if (customerDB.getCustomerType().equals(CustomerType.CUSTOMER_TYPE_IDCARD.getCode())) {
            data.setOwnerName(customerDB.getCustomerName());
        }
        fillPartyASigner(partyASigner, data);

        fillPartyBSigner(partyBSigner, data);

        return data;
    }

    private void fillPartyBSigner(WmTempletContractSignBo partyBSigner, WmFrameContractCommitData data) {
        data.setSecondParty(partyBSigner.getSignName());
        data.setSecondPartyPeople(partyBSigner.getSignPeople());
        data.setSecondPartyPhone(partyBSigner.getSignPhone());
        data.setSecondPartySignDate(DateUtil.date2Unixtime(DateUtil.string2DateDay(partyBSigner.getSignTime())));
    }

    private void fillPartyASigner(WmTempletContractSignBo partyASigner, WmFrameContractCommitData data) {
        data.setFirstParty(partyASigner.getSignName());
        data.setFirstPartyPeople(partyASigner.getSignPeople());
        data.setFirstPartyPhone(partyASigner.getSignPhone());
        data.setFirstPartySignDate(DateUtil.date2Unixtime(DateUtil.string2DateDay(partyASigner.getSignTime())));
    }

    @Override
    public void toNextStatus(int contractId, int toStatus, int opUid, String opUname) throws WmCustomerException {
        logger.info("##toNextStatus# contractId:{} toStatus:{} opUid:{} opUname:{}",
                contractId, toStatus, opUid, opUname);
        toNextContractStatus(contractId, toStatus, opUname);
    }

    @Override
    public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname)
            throws WmCustomerException, TException {
        logger.info("合同审核被驳回  contractId:{}, rejectReason:{} , opUid:{} ,  opUname:{}",
                templetContractId, rejectReason, opUid, opUname);
        WmTempletContractBasicBo basic = WmContractAggre.Factory.make(templetContractId).getBasicById(false, opUid, opUname);
        if (basic == null || CustomerContractStatus.isInvalid(basic.getStatus())) {
            logger.info("审核驳回合同, 该合同已废除! templetContractId = {}, opUid = {}, opUname = {}", templetContractId, opUid, opUname);
            return true;
        }
        int oldContractStatus = basic.getStatus();
        toNextStatus((int) templetContractId, CustomerContractStatus.REJECT.getCode(), opUid, opUname);
        basic.setStatus(CustomerContractStatus.REJECT.getCode());
        rejectReason = RejectReasonUtil.getTempletC1C2RejectReason(rejectReason);
        contractLogAggre.logStatusChangeForReject(oldContractStatus, basic, rejectReason, opUid, opUname);
        return true;
    }

}
