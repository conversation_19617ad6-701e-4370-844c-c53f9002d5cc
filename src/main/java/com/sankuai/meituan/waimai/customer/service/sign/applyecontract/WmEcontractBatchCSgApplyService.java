package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAQDBWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampQDBWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class WmEcontractBatchCSgApplyService extends AbstractWmEcontractApplyAdapterService{

    private static final String FLOW_C1CONTRACT = "c1contract";

    private static final String FLOW_SETTLE = "settle";

    public static final String TYPE_WM_CUSTOMER_BATCH_C1_SETTLEGENGRAL = "type_framecontract_settle_qdb";

    private static List<String> flowList = Lists.newArrayList();

    private static List<String> mtStampList = Lists.newArrayList();

    private static List<String> qdbStampList = Lists.newArrayList();

    private static List<String> poiStampList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_C1CONTRACT);
        flowList.add(FLOW_SETTLE);

        mtStampList.add(FLOW_C1CONTRACT);

        qdbStampList.add(FLOW_C1CONTRACT);

        poiStampList.add(FLOW_C1CONTRACT);

        dataWrapperMap.put(FLOW_C1CONTRACT, EcontractDataWrapperEnum.C1_CONTRACT);
        dataWrapperMap.put(FLOW_SETTLE, EcontractDataWrapperEnum.SETTLE);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAMTWrapperService wmEcontractCAMTWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Resource
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Resource
    private WmEcontractCAQDBWrapperService wmEcontractCAQDBWrapperService;

    @Resource
    private WmEcontractStampQDBWrapperService wmEcontractStampQDBWrapperService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractCAMTWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractCAQDBWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampMTWrapperService.wrap(batchContextBo, mtStampList));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, poiStampList));
        batchInfoBoList.add(wmEcontractStampQDBWrapperService.wrap(batchContextBo, qdbStampList));

        return new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(TYPE_WM_CUSTOMER_BATCH_C1_SETTLEGENGRAL)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .callBackUrl(getCallbackUrl())
                .build();
    }
}
