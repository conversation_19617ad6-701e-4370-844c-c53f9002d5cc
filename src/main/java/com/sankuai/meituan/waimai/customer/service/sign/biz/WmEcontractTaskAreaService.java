package com.sankuai.meituan.waimai.customer.service.sign.biz;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Collections;
import java.util.concurrent.*;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskAreaDB;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskAreaDBMapper;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import java.util.List;

@Service
public class WmEcontractTaskAreaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractTaskAreaService.class);

    private static final ExecutorService insertExecutorService = TraceExecutors
            .getTraceExecutorService(new ThreadPoolExecutor(10, 100, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy()));

    private static final ExecutorService queryExecutorService = TraceExecutors
            .getTraceExecutorService(new ThreadPoolExecutor(10, 100, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy()));

    @Autowired
    private WmEcontractTaskAreaDBMapper wmEcontractTaskAreaDBMapper;

    public void batchInsert(Long taskId, List<WmEcontractSignTaskAreaDB> signTaskAreaDBList) {
        if (CollectionUtils.isEmpty(signTaskAreaDBList)) {
            return;
        }
        List<List<WmEcontractSignTaskAreaDB>> signTaskAreaDBListList = Lists.partition(signTaskAreaDBList, MccConfig.taskAreaParitionSize());
        final CountDownLatch countDownLatch = new CountDownLatch(signTaskAreaDBListList.size());
        long beginTime = System.currentTimeMillis();
        for (List<WmEcontractSignTaskAreaDB> subSignTaskAreaDBList : signTaskAreaDBListList) {
            insertExecutorService.execute(() -> {
                try {
                    wmEcontractTaskAreaDBMapper.batchInsert(subSignTaskAreaDBList);
                } catch (Exception e) {
                    LOGGER.error("#并发写入配送范围异常 subSignTaskAreaDBList:{}", JSON.toJSONString(subSignTaskAreaDBList), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("多线程等待结果超时异常", e);
        }
        long endTime = System.currentTimeMillis();
        LOGGER.info("WmEcontractTaskAreaService#batchInsert taskId:{} cost:{} ms", taskId, endTime - beginTime);
    }

    public List<WmEcontractSignTaskAreaDB> queryAreaDBListByTask(WmEcontractSignTaskDB signTaskDB) {
        List<WmEcontractSignTaskAreaDB> signTaskAreaDBList = Collections.synchronizedList(new ArrayList<>());
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(signTaskDB.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<Long> wmPoiIdList = batchDeliveryInfoBo.getWmPoiIdList();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return signTaskAreaDBList;
        }
        List<List<Long>> wmPoiIdListList = Lists.partition(wmPoiIdList, MccConfig.taskAreaParitionSize());
        final CountDownLatch countDownLatch = new CountDownLatch(wmPoiIdListList.size());
        long beginTime = System.currentTimeMillis();
        for (List<Long> subWmPoiIdList : wmPoiIdListList) {
            queryExecutorService.execute(() -> {
                try {
                    signTaskAreaDBList.addAll(wmEcontractTaskAreaDBMapper.selectByTaskIdAndWmPoiIds(Long.valueOf(signTaskDB.getId()), subWmPoiIdList));
                } catch (Exception e) {
                    LOGGER.error("#并发获取配送范围异常 taskId:{}, subWmPoiIdList:{}", signTaskDB.getId(), subWmPoiIdList, e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("多线程等待结果超时异常", e);
        }
        long endTime = System.currentTimeMillis();
        LOGGER.info("WmEcontractTaskAreaService#queryAreaDBListByTask taskId:{} cost:{} ms", signTaskDB.getId(), endTime - beginTime);
        return signTaskAreaDBList;
    }
}
