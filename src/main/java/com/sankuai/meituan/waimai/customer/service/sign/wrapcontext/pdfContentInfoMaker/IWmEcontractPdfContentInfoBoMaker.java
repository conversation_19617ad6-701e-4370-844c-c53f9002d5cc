package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker;

import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

/**
 * pdf模板维度构造PdfContentInfoBo
 */
public interface IWmEcontractPdfContentInfoBoMaker {
    PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException, TException;
}
