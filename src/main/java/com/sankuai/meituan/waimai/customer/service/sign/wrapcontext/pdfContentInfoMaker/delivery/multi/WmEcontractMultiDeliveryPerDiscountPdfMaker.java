package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.multi;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 【Author】: 李学鹏
 * 【MIS】: lixuepeng
 * 【Date】: 2022-08-30
 * 【Desc】: 配送服务费折扣-多店，默认存在，具体判断逻辑在econtract，原因：customer只维护外卖数据，履约侧数据需要econtract获取
 */
@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.DELIVERY_MULTI_PER_DISCOUNT)
@Slf4j
@Service
public class WmEcontractMultiDeliveryPerDiscountPdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker implements IWmEcontractPdfContentInfoBoMaker {

    public static final String SUPPORT_MARK = "support";

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException {

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        List<Map<String, String>> pdfBizContent = Lists.newArrayList();
        Map<String, String> pdfMetaContent = Maps.newHashMap();

        for (EcontractDeliveryInfoBo temp : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            if (SUPPORT_MARK.equals(temp.getSupportNewZS()) && SUPPORT_MARK.equals(temp.getSupportPerDiscountInfo())) {
                Map<String, String> subMap = new HashMap<>();
                subMap.put("wmPoiId", temp.getWmPoiId());
                subMap.put("wmPoiInfo", temp.getPoiInfo());
                pdfBizContent.add(subMap);
            }
        }

        //pdf中的非业务动态字段
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMetaContent.put("partA", StringUtils.defaultIfEmpty(originContext.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMetaContent.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        pdfMetaContent.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMetaContent.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("DELIVERY_PER_DISCOUNT_TEMPLATE_ID", 140));
        pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("DELIVERY_PER_DISCOUNT_TEMPLATE_VERSION", 0));
        pdfInfoBo.setPdfBizContent(pdfBizContent);
        pdfInfoBo.setPdfMetaContent(pdfMetaContent);
        log.info("#WmEcontractMultiDeliveryPerDiscountPdfMaker,pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }
}
