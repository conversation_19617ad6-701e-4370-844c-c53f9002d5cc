package com.sankuai.meituan.waimai.customer.mq.service;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.mq.CustomerSwitchResultMsg;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.FromCustomerSwitchResultMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客户切换不下线发送消息服务
 */
@Slf4j
@Service
public class CustomerSwitchSendService {

    @Resource(name = "fromCustomerConfirmProducer")
    private MafkaProducer fromCustomerConfirmProducer;

    @Resource(name = "customerSwitchProducer")
    private MafkaProducer customerSwitchProducer;

    /**
     * 客户切换不下线-原客户确认结果同步
     * @param msg
     */
    public void sendFromCustomerConfirmNotify(FromCustomerSwitchResultMsg msg) {
        String message = JSONObject.toJSONString(msg);
        log.info("#sendFromCustomerConfirmNotify,msg={}",message);
        try {
            fromCustomerConfirmProducer.sendMessage(message);
        } catch (Exception e) {
            log.error("fromCustomerConfirmProducer 异常：data={}", message, e);
        }
    }

    /**
     * 客户切换不下线-客户切换结果同步
     * @param msg
     */
    public void sendCustomerSwitchNotify(CustomerSwitchResultMsg msg) {
        String message = JSONObject.toJSONString(msg);
        try {
            if (MccCustomerConfig.customerSwitchCompleteNoticePartitionSwitch()) {
                customerSwitchProducer.sendMessage(message, msg.getTaskId());
            } else {
                customerSwitchProducer.sendMessage(message);
            }
        } catch (Exception e) {
            log.error("customerSwitchProducer 异常：data={}", message, e);
        }
    }

}
