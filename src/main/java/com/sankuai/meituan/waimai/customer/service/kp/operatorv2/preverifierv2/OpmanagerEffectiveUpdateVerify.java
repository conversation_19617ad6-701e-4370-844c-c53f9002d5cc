package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.google.common.base.Function;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-03 19:59
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class OpmanagerEffectiveUpdateVerify extends KpPreverify {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpmanagerEffectiveUpdateVerify.class);

    @Autowired
    private KpDataVerify kpDataVerify;

    @Autowired
    private OpmanagerRelPoiVerify opmanagerRelPoiVerify;

    @Autowired
    private KpRealNameVerify kpRealNameVerify;

    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        if (updateKp != null) {
            if (updateKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
                // 获取KP现有信息，与页面传过来的数据比对
                Map<Integer, WmCustomerKp> oldKpMap = MapUtil.ListTransMap(oldCustomerKpList, new Function<WmCustomerKp, Integer>() {
                    @Nullable
                    @Override
                    public Integer apply(@Nullable WmCustomerKp input) {
                        return input == null ? 0 : input.getId();
                    }
                });
                if (oldKpMap.get(updateKp.getId()) == null) {
                    ThrowUtil.throwClientError("无对应KP记录，更新失败");
                }
                WmCustomerKp oldKp = oldKpMap.get(updateKp.getId());
                boolean isDiff = isDiff(oldKp, updateKp);
                if (isDiff) {
                    // 有变化
                    List<KpPreverify> verifyList = Arrays.asList(kpDataVerify, kpRealNameVerify, opmanagerRelPoiVerify);
                    for (KpPreverify kpPreverify : verifyList) {
                        kpPreverify.verify(wmCustomer, oldCustomerKpList, null, updateKp, null, uid, uname);
                    }
                    return true;
                } else {
                    // 无变化
                    List<KpPreverify> verifyList = Arrays.asList(kpRealNameVerify);
                    for (KpPreverify kpPreverify : verifyList) {
                        kpPreverify.verify(wmCustomer, oldCustomerKpList, null, updateKp, null, uid, uname);
                    }
                    return false;
                }
            }
        } else {
            LOGGER.error("verify#待更新的kp信息为null");
            ThrowUtil.throwClientError("待更新的KP信息为空，无法完成更新");
        }
        // 默认按照有变更的逻辑进行更新
        return true;
    }

    private boolean isDiff(WmCustomerKp oldKp, WmCustomerKp newKp) {
        if ((!oldKp.getCompellation().equals(newKp.getCompellation()))
                || (oldKp.getCertType() != newKp.getCertType())
                || (oldKp.getCertNumber() != newKp.getCertNumber())
                || (!oldKp.getSignTaskType().equals(newKp.getSignTaskType()))
                || (!oldKp.getRelPoiInfo().equals(newKp.getRelPoiInfo()))) {
            return true;
        }
        return false;
    }
}
