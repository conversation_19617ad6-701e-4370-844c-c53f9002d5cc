package com.sankuai.meituan.waimai.customer.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * ASCII码转换
 */
public class AsciiUtil {

    //用于正则去除空格，符号等
    private final static String REGULAR_EXPRESSION = "[`~!^&￥$@%#()|{}':;,\\[\\]》．·./?！…（）【】‘；：”“’。，、？]|\\s*|\t|\r|\n";

    /**
     * 全角空格 12288
     */
    public static final char SBC_SPACE = 12288;
    /**
     * 半角空格 32
     */
    public static final char DBC_SPACE = 32;

    /**
     * 需要转换字符对应的ASCII码，区间开始
     */
    public static final char UNICODE_START = 65281;
    /**
     * 需要转换字符对应的ASCII码，区间结束
     */
    public static final char UNICODE_END = 65374;
    /**
     * 全角半角转换间隔
     */
    public static final char DBC_SBC_STEP = 65248;

    /**
     * Convert from SBC case to DBC case 全角转半角（SBC就是全角、DBC就是半角）
     *
     * @param src SBC case
     * @return DBC case
     */
    public static String toDBCCase(String src) {
        if (src == null) {
            return null;
        }
        char[] c = src.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == SBC_SPACE) {
                c[i] = DBC_SPACE;
            }
            if (c[i] >= UNICODE_START && c[i] <= UNICODE_END) {
                c[i] = (char) (c[i] - DBC_SBC_STEP);
            }
        }
        return new String(c);
    }

    /**
     * 使用正则去除特殊符号，只比对汉字，数字，字母
     *
     * @param str 转换字符串
     * @return 转换后字符串
     */
    public static String replaceSpecialStr(String str) {
        String res = "";
        if (StringUtils.isNotBlank(str)) {
            Pattern p = Pattern.compile(REGULAR_EXPRESSION);
            Matcher m = p.matcher(str);
            res = m.replaceAll("");
        }
        return res;
    }
}
