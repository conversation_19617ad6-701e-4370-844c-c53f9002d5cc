package com.sankuai.meituan.waimai.customer.contract.config.dto;

import lombok.Data;

/**
 * @description: 合同生效时间相关属性
 * @author: liuyunjie05
 * @create: 2024/5/21 10:22
 */
@Data
public class ContractEffectiveTimePropertyDTO {

    // 是否支持立即生效
    private Boolean supportEffectImmediately;

    // 是否支持预约生效
    private Boolean supportEffectNotNow;

    // 最早可选多久
    private TimePropertyDTO leftInterval;

    // 最晚可选多久
    private TimePropertyDTO rightInterval;
}
