package com.sankuai.meituan.waimai.customer.dao.sc.delivery;

import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校交付人员指定生效信息Mapper
 * <AUTHOR>
 * @date 2024/02/08
 * @email <EMAIL>
 **/
@Component
public interface WmSchoolDeliveryAssignmentMapper {

    /**
     * 根据主键ID查询学校交付人员指定生效信息
     * @param id 主键ID
     * @return 学校交付人员指定生效信息
     */
    WmSchoolDeliveryAssignmentDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据学校主键ID查询学校交付人员指定生效信息
     * @param schoolPrimaryId 学校主键ID
     * @return 学校交付人员指定生效信息
     */
    List<WmSchoolDeliveryAssignmentDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据交付ID查询学校交付人员指定生效信息
     * @param deliveryId 交付ID
     * @return 学校交付人员指定生效信息
     */
    WmSchoolDeliveryAssignmentDO selectByDeliveryId(@Param("deliveryId") Integer deliveryId);

    /**
     * 通过客户成功经理UID列表查询学校交付人员指定信息列表
     * @param csmUidList 客户成功经理UID列表
     * @return 学校交付人员指定信息列表
     */
    List<WmSchoolDeliveryAssignmentDO> selectByCSMUidList(@Param("csmUidList") List<Integer> csmUidList);

    /**
     * 通过聚合渠道经理UID列表查询学校交付人员指定信息列表
     * @param acmUidList 聚合渠道经理UID列表
     * @return 学校交付人员指定信息列表
     */
    List<WmSchoolDeliveryAssignmentDO> selectByACMUidList(@Param("acmUidList") List<Integer> acmUidList);

    /**
     * 通过学校对应蜂窝负责人UID列表查询学校交付人员指定信息列表
     * @param aormUidList 学校对应蜂窝负责人UID列表
     * @return 学校交付人员指定信息列表
     */
    List<WmSchoolDeliveryAssignmentDO> selectByAORMUidList(@Param("aormUidList") List<Integer> aormUidList);

    /**
     * 新增学校交付人员指定生效信息
     * @param wmSchoolDeliveryAssignmentDO 交付人员指定生效信息
     * @return 更新行数
     */
    int insertSelective(WmSchoolDeliveryAssignmentDO wmSchoolDeliveryAssignmentDO);

    /**
     * 根据主键ID更新学校交付人员指定生效信息
     * @param wmSchoolDeliveryAssignmentDO 学校交付人员指定生效信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmSchoolDeliveryAssignmentDO wmSchoolDeliveryAssignmentDO);

}
