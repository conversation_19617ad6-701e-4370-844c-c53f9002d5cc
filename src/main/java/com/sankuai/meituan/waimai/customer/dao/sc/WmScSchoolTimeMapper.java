package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeCondition;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmScSchoolTimeMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(WmScSchoolTimeDO record);

    int batchInsert(@Param("list") List<WmScSchoolTimeDO> records);

    WmScSchoolTimeDO selectByPrimaryKey(Long id);

    List<WmScSchoolTimeDO> selectBySchoolId(Integer schoolPrimaryId);

    int updateByPrimaryKeySelective(WmScSchoolTimeDO record);

    WmScSchoolTimeDO selectNewest();

    List<WmScSchoolTimeDO> selectByCondition(WmScSchoolTimeCondition condition);

    int unValidSchoolTimeInfo(@Param("id") Long id, @Param("muid") Long muid);

    int unValidSchoolTimeInfoBySchoolPrimaryId(Integer schoolPrimaryId);

}