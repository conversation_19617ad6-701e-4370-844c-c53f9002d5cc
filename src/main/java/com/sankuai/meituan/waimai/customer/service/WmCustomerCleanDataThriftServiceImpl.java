package com.sankuai.meituan.waimai.customer.service;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.MetricHelper;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService;
import com.sankuai.meituan.waimai.customer.service.customer.clean.WmCustomerDataCleanService;
import com.sankuai.meituan.waimai.customer.service.customer.clean.WmCustomerSmsRecordCleanService;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerMetrixInitDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.job.TempCustomerDataUpdateJobParam;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerDataCleanThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 20230118
 * @desc 客户信息字段数据清洗服务
 */
@Slf4j
@Service
public class WmCustomerCleanDataThriftServiceImpl implements WmCustomerDataCleanThriftService {

    @Autowired
    private WmCustomerDataCleanService wmCustomerDataCleanService;

    @Autowired
    private WmCustomerSmsRecordCleanService wmCustomerSmsRecordCleanService;

    @Autowired
    private CusPoiRelEsBusinessService cusPoiRelEsBusinessService;


    /**
     * 清洗客户类型
     *
     * @param jobParam
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public void cleanCustomerRealTypeAndBizCode(TempCustomerDataUpdateJobParam jobParam) throws WmCustomerException, TException {
        wmCustomerDataCleanService.dealCustomerRealTypeByParams(jobParam);
    }

    /**
     * 短信签约任务与打包签约任务对齐清洗
     *
     * @param idStart 短信任务ID区间最小值
     * @param pageSize   短信任务ID分页大小
     * @throws WmCustomerException 处理流程异常
     * @throws TException          处理流程异常
     */
    @Override
    public int cleanSmsRecordTask(Integer idStart, Integer pageSize, Integer idEnd) throws WmCustomerException, TException {
        return wmCustomerSmsRecordCleanService.cleanSmsRecordTask(idStart, pageSize, idEnd);
    }

    /**
     * 客户门店关系与短信签约任务对齐清洗
     *
     * @param idStart 客户ID区间最小值
     * @param pageSize   客户ID分页大小
     * @throws WmCustomerException 处理流程异常
     * @throws TException          处理流程异常
     * @return
     */
    @Override
    public int cleanBindCustomerRel(Integer idStart, Integer pageSize, Integer idEnd) throws WmCustomerException, TException {
        return wmCustomerSmsRecordCleanService.cleanBindCustomerRel(idStart, pageSize, idEnd);
    }
    @Override
    public void initCustomerBusinessMetrix(CustomerMetrixInitDTO customerMetrixInitDTO) throws WmCustomerException, TException {
        if (customerMetrixInitDTO == null) {
            throw new WmCustomerException(500, "参数错误");
        }
        if (StringUtils.isBlank(customerMetrixInitDTO.getName())) {
            throw new WmCustomerException(500, "参数错误");
        }
        if (customerMetrixInitDTO.getTagValues().isEmpty()) {
            throw new WmCustomerException(500, "参数错误");
        }
        MetricHelper metricHelper = MetricHelper.build().name(customerMetrixInitDTO.getName());
        for (Map.Entry<String, String> data : customerMetrixInitDTO.getTagValues().entrySet()) {
            if (StringUtils.isBlank(data.getKey()) || StringUtils.isBlank(data.getValue())) {
                throw new WmCustomerException(500, "参数错误");
            }
            metricHelper.tag(data.getKey(), data.getValue());
        }
        metricHelper.count();
    }

    /**
     * 根据客户ID列表批量更新"是否子门店"到客户门店ES
     *
     * @param customerIds
     * @return
     * @throws WmCustomerException
     */
    @Override
    public void syncChildPoiFlag2PoiRelES(List<Integer> customerIds) throws WmCustomerException {
        log.info("syncChildPoiFlag2PoiRelES,customerIds={}", JSON.toJSONString(customerIds));
        if (CollectionUtils.isEmpty(customerIds)) {
            throw new WmCustomerException(500, "参数不能为空");
        }
        cusPoiRelEsBusinessService.batchSyncChildPoi2PoiRelES(customerIds);
    }

    /**
     * 根据客户门店关系表主键ID更新是否子门店字段
     * 要求
     * @param minId
     * @param maxId
     * @throws WmCustomerException
     */
    @Override
    public void syncChildPoiFlagByIds(Integer minId, Integer maxId) throws WmCustomerException {
        log.info("syncChildPoiFlagByIds,minId={},maxId={}", minId, maxId);
        if (minId == null || maxId == null || minId > maxId) {
            throw new WmCustomerException(500, "参数不合法");
        }
        if (maxId - minId > MccCustomerConfig.getSyncChildPoi2PoiRelEsSize()) {
            throw new WmCustomerException(500, "每批次处理不能超过" + MccCustomerConfig.getSyncChildPoi2PoiRelEsSize());
        }
        cusPoiRelEsBusinessService.syncChildPoi2EsByIdInterval(minId, maxId);
    }

}
