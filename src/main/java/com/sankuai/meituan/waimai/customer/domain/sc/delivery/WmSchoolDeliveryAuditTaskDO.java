package com.sankuai.meituan.waimai.customer.domain.sc.delivery;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryStreamNodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学校交付审批任务主表DO
 * <AUTHOR>
 * @date 2024/02/15
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmSchoolDeliveryAuditTaskDO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 交付编号ID
     */
    private Integer deliveryId;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * Gravity流程实例ID
     */
    private String gravityId;
    /**
     * 交付节点类型
     * {@link SchoolDeliveryStreamNodeEnum}
     */
    private Integer deliveryNodeType;
    /**
     * 审批任务类型
     * {@link SchoolDeliveryAuditTaskTypeEnum}
     */
    private Integer auditTaskType;
    /**
     * 当前审批节点
     * {@link SchoolDeliveryAuditNodeTypeEnum}
     */
    private Integer auditNode;
    /**
     * 提审前审批状态
     * {@link SchoolDeliveryAuditStatusEnum}
     */
    private Integer lastAuditStatus;
    /**
     * 审批结果
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditResultEnum}
     */
    private Integer auditResult;
    /**
     * 数据版本
     */
    private Integer dataVersion;
    /**
     * 提审前生效数据快照版本
     */
    private Integer lastDataVersion;
    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer valid;
    /**
     * 提审人UID
     */
    private Long cuid;
    /**
     * 提审人MIS
     */
    private String cmis;
    /**
     * 提审时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;

}
