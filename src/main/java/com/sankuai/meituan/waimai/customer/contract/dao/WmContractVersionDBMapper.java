package com.sankuai.meituan.waimai.customer.contract.dao;

import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.datasource.multi.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-5-5.
 */
@Component
@DataSource("dbContractWrite")
public interface WmContractVersionDBMapper {
    public final static String INSERT_KEYS = "wm_contract_id, status, version_number, pdf_url, stamp_pdf_url, stamp_type, customer_id, mcertify_result, party_b_stamp_type, party_b_customer_id, party_b_mcertify_result, type, valid, ctime, utime, op_uid, cuid, transaction_id ,commit_info";
    public final static String SELECT_KEYS = "id, " + INSERT_KEYS;

    int insertSelective(WmContractVersionDB wmContractVersionDB);

    //更新合同版本相关信息
    @Update("update wm_contract_version set status = #{status} where id = #{id}")
    int updateStatus(WmContractVersionDB wmContractVersionDB);

    @Update("update wm_contract_version set pdf_url = #{pdfUrl}, stamp_pdf_url = #{stampPdfUrl} where id = #{id}")
    int updatePdfUrl(@Param("id") Integer id, @Param("pdfUrl") String pdfUrl, @Param("stampPdfUrl") String stampPdfUrl);

    //更新不同签约方的信息
    @Update("update wm_contract_version set stamp_type = #{stampType} where id = #{id}")
    int updatePartyAStampType(@Param("id") int id, @Param("stampType") int stampType);

    @Update("update wm_contract_version set customer_id = #{customerId} where id = #{id}")
    int updatePartyACustomerId(@Param("id") Integer id, @Param("customerId") String customerId);

    @Update("update wm_contract_version set mcertify_result = #{mcertifyResult} where id = #{id}")
    int updatePartyAMcertifyResult(@Param("id") Integer id, @Param("mcertifyResult") Integer mcertifyResult);

    @Update("update wm_contract_version set party_b_customer_id = #{partyBCustomerId} where id = #{id}")
    int updatePartyBCustomerId(@Param("id") Integer id, @Param("partyBCustomerId") String partyBCustomerId);

    @Update("update wm_contract_version set party_b_stamp_type = #{partyBStampType} where id = #{id}")
    int updatePartyBStampType(@Param("id") Integer id, @Param("partyBStampType") Integer partyBStampType);

    @Update("update wm_contract_version set party_b_mcertify_result = #{partyBMcertifyResult} where id = #{id}")
    int updatePartyBMcertifyResult(@Param("id") Integer id, @Param("partyBMcertifyResult") Integer partyBMcertifyResult);

    @Update("update wm_contract_version set transaction_id = #{transactionId} where version_number = #{versionNum}")
    int updateTransactionId(@Param("transactionId") String transactionId, @Param("versionNum") String versionNum);

    //基础查询
    @Select("select " + SELECT_KEYS + " from wm_contract_version where id = #{id} and valid = 1")
    @DataSource("dbContractRead")
    WmContractVersionDB getById(@Param("id") Integer id);

    @Select("/*master*/select " + SELECT_KEYS + " from wm_contract_version where id = #{id} and valid = 1")
    WmContractVersionDB getByIdMaster(@Param("id") Integer id);

    @Select("select " + SELECT_KEYS + " from wm_contract_version where wm_contract_id = #{wmContractId} and type = #{type} and status = #{status} and valid = 1 order by id desc limit 1")
    @DataSource("dbContractRead")
    WmContractVersionDB getLastByWmContractIdAndTypeAndStatus(@Param("wmContractId") Integer wmContractId, @Param("type") Byte type, @Param("status") Byte status);

    @Select("select " + SELECT_KEYS + " from wm_contract_version where wm_contract_id = #{wmContractId} and type = #{type} and valid = 1 order by id desc limit 1")
    @DataSource("dbContractRead")
    WmContractVersionDB getLastWmContractVersionByWmContractId(@Param("wmContractId") Integer wmContractId, @Param("type") Byte type);

    @Select("/*master*/select " + SELECT_KEYS + " from wm_contract_version where wm_contract_id = #{wmContractId} and type = #{type} and valid = 1 order by id desc limit 1")
    @DataSource("dbContractWrite")
    WmContractVersionDB getLastWmContractVersionByWmContractIdMaster(@Param("wmContractId") Integer wmContractId, @Param("type") Byte type);

    @Select("/*master*/select " + SELECT_KEYS + " from wm_contract_version where version_number = #{versionNumber} and valid = 1 order by id desc limit 1")
    @DataSource("dbContractWrite")
    WmContractVersionDB getLastWmContractVersionByVersionNumberMaster(@Param("versionNumber") String versionNumber);


    @DataSource("dbContractRead")
    @Select("select wm_contract_id from wm_contract_version where transaction_id = #{transactionId} order by id desc limit 1")
    Integer getTempletIdByTransactionId(@Param("transactionId") String transactionId);

    @Select("/*master*/select wm_contract_id from wm_contract_version where transaction_id = #{transactionId} order by id desc limit 1")
    Integer getTempletIdByTransactionIdMaster(@Param("transactionId") String transactionId);

    @Select("/*master*/select " +SELECT_KEYS+ " from wm_contract_version where transaction_id = #{transactionId} order by id desc limit 1")
    WmContractVersionDB getByTransactionIdMaster(@Param("transactionId") String transactionId);

    @Select("/*master*/select " +SELECT_KEYS+ " from wm_contract_version where transaction_id = #{transactionId} and type = #{type} and valid = 1 order by id desc limit 1")
    WmContractVersionDB getLastByTransactionIdAndTypeMaster(@Param("transactionId") String transactionId, @Param("type") int type);

    @DataSource("dbContractRead")
    List<WmContractVersionDB> getByWmContractIdAndStatus(@Param("wmContractId") int templetContractId, @Param("type") int type, @Param("statusList") List<Byte> statusList);

    @DataSource("dbContractRead")
    List<WmContractVersionDB> getByWmContractIdAndStatusAndTypes(@Param("wmContractId") int templetContractId, @Param("typeList") List<Integer> typeList, @Param("statusList") List<Byte> statusList);

    @DataSource("dbContractRead")
    List<WmContractVersionDB> queryByWmContractIdAndType(@Param("wmContractId") int templetContractId, @Param("type") Byte type);

    @DataSource("dbContractRead")
    List<WmContractVersionDB> getByWmContractIdsAndStatus(@Param("wmContractIds") List<Integer> templetContractIds, @Param("type") int type, @Param("statusList") List<Byte> statusList);


    @DataSource("dbContractRead")
    List<WmContractVersionDB> getByWmContractIdAndTypeAndStatus(@Param("wmContractId") int templetContractId, @Param("typeList") List<Byte> typeList, @Param("statusList") List<Byte> statusList);

    WmContractVersionDB getLastWmContractVersionByWmContractIdAndTypesMaster(@Param("wmContractId") Integer wmContractId, @Param("typeList") List<Byte> typeList);

    @DataSource("dbContractRead")
    WmContractVersionDB getLastByWmContractIdAndTypeAndStatusList(@Param("wmContractId") Integer wmContractId, @Param("type") Byte type, @Param("statusList") List<Byte> statusList);
}
