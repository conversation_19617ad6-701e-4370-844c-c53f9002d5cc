package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractAggre;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.C1_PAPER})
public class WmC1PaperContractTempletAtomService extends WmPaperContractTempletAtomService {

    private static Logger logger = LoggerFactory.getLogger(WmC1PaperContractTempletAtomService.class);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
        Boolean effect = super.effect(templetContractId, opUid, opUname);
        WmContractAggre.Factory.make(templetContractId).saveWmContractFtlTagDB(opUid, opUname);
        notifyEcontractPlatform(templetContractId, opUid, opUname);
        return effect;
    }

    //通知电子合同平台，以免打包流程等待合同走不下去
    private void notifyEcontractPlatform(long templetContractId, int opUid, String opUname)
            throws WmCustomerException, TException {
        WmTempletContractBasicBo basic = WmContractAggre.Factory.make(templetContractId).getBasicById(false, opUid, opUname);
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(UUID.randomUUID().toString().replaceAll("-", ""));
        applyBo.setBizId(String.valueOf(basic.getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);
        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.getConfigBo().setCommitConfirm(false);
        applyBo.setCommitUid(opUid);
        logger.info("纸质合同生效，通知电子合同平台 applyBo：{}", JSON.toJSON(applyBo));
        try {
            LongResult taskId = wmEcontractTaskApplyService.applyTask(applyBo);
            logger.info("纸质合同生效，通知电子合同平台 taskId：{}", taskId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        throw new UnsupportedOperationException("非法操作，不支持废除操作。合同id:" + contractId + ", opUid:" + opUid + ", opUname:" + opUname);
    }
}
