package com.sankuai.meituan.waimai.customer.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 20240426
 * @desc  监听门店资质生效信息使用DTO对象
 */
@Data
public class WmPoiQualificationInfoDTO {

    private int id;

    private int type;

    private String url;

    private String url_with_watermark;

    private long validate_date;

    private String address;

    private String number;

    private long wm_poi_id;

    private int city_id;

    private int client_show;

    private int valid;

    private int ctime;

    private int utime;

    private byte source;

    private String name;

    private int second_subtype;
    private String legal_person;
    private String owner;
    private String business_scope;

    /**
     * 证件编号密文
     */
    private String number_encryption;

    /**
     * 证件编号密文TOKEN
     */
    private String number_token;


    // 资质3.2新增字段
    /**
     * 性别
     */
    private int sex;
    /**
     * 民族
     */
    private String nation;
    /**
     * 出生日期 or 成立日期
     */
    private long birthday;
    /**
     * 发证机关 or 登记机关
     */
    private String issuing_authority;
    /**
     * 生效日期开始时间
     */
    private long valid_start_date;
    /**
     * 注册资本
     */
    private String capital;
    /**
     * 核准日期
     */
    private long approved_date;
    /**
     * 食品经营许可证编号
     *
     */
    private String food_license_number;
    /**
     * 社会信用代码
     *
     */
    private String credit_code;
    /**
     * 住所
     *
     */
    private String live_address;
    /**
     * 主体业态
     *
     */
    private String main_bussiness;
    /**
     * 日常监督管理机构
     *
     */
    private String supervisory_department;
    /**
     * 日常监督管理人员
     *
     */
    private String supervisor;
    /**
     * 签发人
     *
     */
    private String sign_person;
    /**
     * 投诉举报电话
     *
     */
    private String complaint_telephone;

    /**
     * 扩展字段
     */
    private String ext_data;

    /**
     * 执照类型（默认为1纸质，2 电子）
     * @return
     */
    private Integer license_type;

    /**
     * 是否过期（默认为0否，1 是）
     * @return
     */
    private Integer is_expire;

    /**
     * 总局状态（0-无，1续存，2注销，3吊销)
     * @return
     */
    private Integer government_state;

    /**
     * 法人是否变更（默认为0否，1 是）
     * @return
     */
    private Integer legal_person_change;

    /**
     * 资质标签
     */
    private String qualification_label;

    /**
     * 过期状态
     * @return
     */
    private Integer expire_state;
}
