package com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.autoSignCalTask;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.DateUtils;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchModulePackInfo;
import com.sankuai.meituan.waimai.poibizflow.constant.customerSwitch.CustomerSwitchModuleTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * C2合同自动创建task
 *
 * Created by lixuepeng on 2021/6/29
 */
public class AutoSignC1CalTask extends AutoSignCalTask {

    public AutoSignC1CalTask(int customerId, List<Long> wmPoiIds, long switchId, int opUid, String opName) {
        super(customerId, wmPoiIds, switchId, opUid, opName);
    }

    @Override
    public boolean preSign() {
        try {
            //基本参数校验
            Preconditions.checkArgument(customerId > 0 && CollectionUtils.isNotEmpty(wmPoiIds) && StringUtils.isNotEmpty(opName),"参数不合法");
            //客户类型校验
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
            Preconditions.checkArgument(wmCustomerDB.getSignMode() == 2, "新客户签约模式不为电子签约");
            //签约KP校验
            WmCustomerKp wmCustomerKp =  wmCustomerKpService.getCustomerSignerKp(customerId);
            Preconditions.checkArgument(wmCustomerKp != null, "签约KP人信息为空");
            //已经签约C1合同
            Preconditions.checkArgument(!hasSignC1Contract(), "新客户已经签约C1合同");
            return true;
        } catch (Exception e) {
            //通知切换任务系统
            syncToPoiSwitch(Arrays.asList(-1l));
            logger.error("自动发起C1合同-前置校验失败，customerId:{}, wmPoiIds:{}, opName:{}, 原因:{}", customerId, wmPoiIds, opName, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void fillSignData() throws Exception {
        logger.info("自动发起C1合同-填充数据，customerId:{}, wmPoiIds:{}, opName:{}", customerId, wmPoiIds, opName);
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        WmCustomerKp wmCustomerKp =  wmCustomerKpService.getCustomerSignerKp(customerId);
        WmPoiSignSubjectBo signSubjectBo =  wmContractService.getSignSubjectBo(customerId, WmTempletContractTypeEnum.C1_E.getCode());
        CollectionUtils.addIgnoreNull(contractBoList, initSaveContractBo(wmCustomerDB, wmCustomerKp, signSubjectBo));
    }

    @Override
    public void doSign() {
        try {
            if (CollectionUtils.isEmpty(contractBoList)) {
                return;
            }
            for (WmCustomerContractBo contractBo : contractBoList) {
                resultList.add(wmContractService.saveAndStartSignForManualPack(contractBo, opUid, opName));
            }
        } catch (Exception e) {
            logger.error("AutoSignCalTask doSign contractBoList:{}, opUid:{}, opName:{}", JSON.toJSONString(contractBoList), opUid, opName, e);
        }
    }

    /**
     * C1合同需要周知
     */
    @Override
    public void afterSign() {
        logger.info("自动发起C1合同-后置动作 customerId:{}, wmPoiIds:{}, resultList:{}", customerId, wmPoiIds, resultList);
        try {
            if (CollectionUtils.isEmpty(resultList)) {
                return;
            }
            syncToPoiSwitch(resultList);
        } catch (Exception e) {
            logger.error("自动发起C1合同-后置动作 异常 customerId:{}, wmPoiIds:{}, resultList:{}", customerId, wmPoiIds, resultList, e);
        }
    }

    /**
     * 是否已经签约C1-线上or线下表存在即可
     * @return
     */
    private boolean hasSignC1Contract() {
        logger.info("自动发起C1合同-是否已经签约C1，customerId:{}, wmPoiIds:{}, opName:{}", customerId, wmPoiIds, opName);
        try {
            List<WmCustomerContractBo> wmCustomerContractBos = wmContractService.getContractBoListByCusIdAndType((long) customerId,
                    Lists.newArrayList(WmTempletContractTypeEnum.C1_PAPER.getCode(), WmTempletContractTypeEnum.C1_E.getCode()), opUid, opName);
            if (CollectionUtils.isNotEmpty(wmCustomerContractBos)) {
                return true;
            }
        } catch (Exception e) {
            logger.error("自动发起C1合同-是否已经签约C1 异常 customerId:{}, wmPoiIds:{}, opName:{}", customerId, wmPoiIds, opName, e);
        }
        return false;
    }

    private WmCustomerContractBo initSaveContractBo(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, WmPoiSignSubjectBo signSubjectBo) {
        logger.info("自动发起C1合同-填充数据 初始化 customerId:{}", customerId);
        try {
            WmCustomerContractBo contractBo = new WmCustomerContractBo();

            //基础Bo
            WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
            basicBo.setContractNum("电子合同保存后自动生成编号");
            basicBo.setDueDate(DateUtils.getNYearLastSecond(1));//有效期为1年
            CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
            basicBo.setExtStr(JSON.toJSONString(paperContractRemarkBo));
            basicBo.setLogisticsSubject(signSubjectBo.getPartlogisticsName());
            basicBo.setParentId(customerId);
            basicBo.setType(WmTempletContractTypeEnum.C1_E.getCode());

            //签约甲乙方Bo
            List<WmTempletContractSignBo> signList = Lists.newArrayList();
            //甲方
            WmTempletContractSignBo partyA = new WmTempletContractSignBo();
            partyA.setSignId(customerId);
            partyA.setSignName(wmCustomerDB.getCustomerName());
            partyA.setSignPeople(wmCustomerKp.getCompellation());
            partyA.setSignPhone(wmCustomerKp.getPhoneNum());
            partyA.setSignTime(DateUtils.getNDay(0));
            partyA.setSignType("A");
            signList.add(partyA);
            //乙方
            WmTempletContractSignBo partyB = new WmTempletContractSignBo();
            partyB.setSignId(0);
            partyB.setSignName(signSubjectBo.getPartBName());
            partyB.setSignPeople(opName);
            partyB.setSignPhone(empServiceAdaptor.getPhone(opUid));
            partyB.setSignTime(DateUtils.getNDay(0));
            partyB.setSignType("B");
            signList.add(partyB);

            //封装
            contractBo.setBasicBo(basicBo);
            contractBo.setSignBoList(signList);
            contractBo.setIgnoreExistAnotherSignTypeContract(true);
            contractBo.setPackWay(SignPackWay.WAIT_HAND_PACK.getCode());

            return contractBo;
        } catch (Exception e) {
            logger.error("自动发起C1合同-填充数据 初始化异常 customerId:{}", customerId, e);
        }
        return null;
    }

    private void syncToPoiSwitch(List<Long> resultList) {
        try {
            wmPoiSwitchThriftService.saveSwitchModulePack(assemblyInfo(resultList));
        } catch (Exception e) {
            logger.error("自动发起C1合同-同步状态至任务系统 异常 customerId:{}, switchId:{}, resultList:{}", customerId, switchId, resultList, e);
        }
    }

    private SwitchModulePackInfo assemblyInfo(List<Long> resultList) {
        SwitchModulePackInfo switchModulePackInfo = new SwitchModulePackInfo();
        switchModulePackInfo.setSwitchBasicId(switchId);
        switchModulePackInfo.setModuleId(CustomerSwitchModuleTypeEnum.C1_CONTRACT.getCode());
        switchModulePackInfo.setTaskIds(StringUtils.join(resultList,","));
        switchModulePackInfo.setOpUid(opUid);
        switchModulePackInfo.setOpName(opName);
        return switchModulePackInfo;
    }
}
