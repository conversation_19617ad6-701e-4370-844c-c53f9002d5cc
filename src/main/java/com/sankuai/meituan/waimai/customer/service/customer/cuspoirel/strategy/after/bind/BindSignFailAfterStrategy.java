package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.bind;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.BindSignNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 20240119
 * @desc 签约绑定回调通知失败后置处理策略
 */
@Service
@Slf4j
public class BindSignFailAfterStrategy implements IBindAfterStrategy {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private CustomerTaskService customerTaskService;

    /**
     * 直接绑定后置操作
     *
     * @param context
     * @throws WmCustomerException
     */
    @Override
    public void execute(CustomerPoiBindFlowContext context) throws WmCustomerException {
        log.info("BindSignFailAfterStrategy,绑定签约回调通知失败，开始执行后置行为");
        bindSignFailAfterAction(context);
    }


    /**
     * 预绑定发起成功的后置操作
     *
     * @param context
     */
    public void bindSignFailAfterAction(CustomerPoiBindFlowContext context) {
        Integer customerId = context.getCustomerId();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        BindSignNoticeDTO bindSignNoticeDTO = context.getBindSignNoticeDTO();
        try {
            CustomerTaskStatusEnum customerTaskStatusEnum = CustomerTaskStatusEnum.FAIL;
            String userName = CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT;
            String logMsg = CustomerConstants.CUSTOMER_LOG_TEMPLATE_CANCEL_PRE_BIND_BY_MERCHANT;
            if (bindSignNoticeDTO.getSignResult() == EcontractTaskStateEnum.CANCEL.getType()) {
                customerTaskStatusEnum = CustomerTaskStatusEnum.CANCEL;
                userName = CustomerConstants.CUSTOMER_LOG_OP_NAME_BD;
                logMsg = CustomerConstants.CUSTOMER_LOG_TEMPLATE_CANCEL_PRE_BIND;
            }

            // 发送MQ
            wmCustomerService.sendCustomerStatusNoticeMQ(customerId, CustomerMQEventEnum.CUSTOMER_PRE_BIND_POI_FAIL,
                    wmPoiIdSet, 0, userName);
            //同步任务状态
            customerTaskService.updateStatusByCustomerIdAndPoiIdAndType(customerId, wmPoiIdSet, 0L,
                    CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode(), customerTaskStatusEnum.getCode());

            // 记录操作日志
            wmCustomerService.insertCustomerOpLog(customerId, 0, userName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                    String.format(logMsg, StringUtils.join(wmPoiIdSet, ",")));
        } catch (Exception e) {
            log.error("bindSignFailAfterAction,客户门店发起预绑定完成，记录日志并发送消息处理异常,customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet), e);
        }
    }

}
