package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAQDBWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampQDBWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * C1合同申请电子合同平台
 * C1电子合同流程:
 * 生成pdf -> 美团CA认证 -> 商家CA认证 —> 美团签章 -> 发短信 —> 商家签章 -> 合同归档 -> 完成合同
 * 电子合同包括：C1电子合同
 */
@Service
public class WmEcontractC1ContractApplyService extends AbstractWmEcontractApplyAdapterService {

    private static final String FLOW_C1CONTRACT = EcontractTaskApplyTypeEnum.C1CONTRACT.getName();

    private static List<String> flowList = Lists.newArrayList();

    private static List<String> poiStampList = Lists.newArrayList();

    private static List<String> mtStampList = Lists.newArrayList();

    private static List<String> qdbStampList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_C1CONTRACT);

        poiStampList.add(FLOW_C1CONTRACT);

        mtStampList.add(FLOW_C1CONTRACT);

        qdbStampList.add(FLOW_C1CONTRACT);

        dataWrapperMap.put(FLOW_C1CONTRACT, EcontractDataWrapperEnum.C1_CONTRACT);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAMTWrapperService wmEcontractCAMTWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Resource
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Resource
    private WmEcontractCAQDBWrapperService wmEcontractCAQDBWrapperService;

    @Resource
    private WmEcontractStampQDBWrapperService wmEcontractStampQDBWrapperService;

    String type_framecontract_c1_qdb = "type_framecontract_c1_qdb";

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
        throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractCAMTWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractCAQDBWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, poiStampList));
        batchInfoBoList.add(wmEcontractStampMTWrapperService.wrap(batchContextBo, mtStampList));
        batchInfoBoList.add(wmEcontractStampQDBWrapperService.wrap(batchContextBo, qdbStampList));

        return new EcontractBatchBo.Builder()
            .token(getToken())
            .econtractBizId(getBizId(batchContextBo))
            .econtractType(type_framecontract_c1_qdb)
            .stageInfoBoList(batchInfoBoList)
            .flowList(flowList)
            .callBackUrl(getCallbackUrl())
            .build();
    }
}
