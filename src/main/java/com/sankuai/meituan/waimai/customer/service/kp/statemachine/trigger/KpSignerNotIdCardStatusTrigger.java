package com.sankuai.meituan.waimai.customer.service.kp.statemachine.trigger;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer.*;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.condition.KpSignerCommitCondition;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpLegalEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerIdCardUnEffectSM;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerNotIdCardEffectSM;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerNotIdCardUnEffectSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.squirrelframework.foundation.fsm.StateMachineBuilder;
import org.squirrelframework.foundation.fsm.StateMachineBuilderFactory;

/**
 * <AUTHOR>
 * @date 20240416
 * @desc KP签约人非身份证证件类型-流程触发器
 */
@Service
@Slf4j
public class KpSignerNotIdCardStatusTrigger implements InitializingBean {

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    private volatile boolean isInit = false;

    /**
     * 未生效签约人KP-证件类型非身份证的状态模型
     */
    private final StateMachineBuilder<KpSignerBaseSM, KpSignerStateMachine, KpSignerEventEnum, KpSignerStatusMachineContext> notIdCardUnEffectStateMachineBuilder;

    /**
     * 生效过签约人KP-证件类型非身份证的状态模型
     */
    private final StateMachineBuilder<KpSignerBaseSM, KpSignerStateMachine, KpSignerEventEnum, KpSignerStatusMachineContext> notIdCardEffectStateMachineBuilder;

    @Autowired
    private KpSignerCommitCondition kpSignerCommitCondition;

    @Autowired
    private KpSignerInitCreateAction kpSignerInitCreateAction;

    @Autowired
    private CommitSpecialAuditAction commitSpecialAuditAction;

    @Autowired
    private AuditSuc2EffectAction auditSuc2EffectAction;

    @Autowired
    private AuditSuc2LegalAuthAction auditSuc2LegalAuthAction;

    @Autowired
    private SpecialAuditFailAction specialAuditFailAction;

    @Autowired
    private AuthSuc2EffectAction authSuc2EffectAction;

    @Autowired
    private AuthFailAction authFailAction;

    @Autowired
    private KpSignerTempStoreAction kpSignerTempStoreAction;

    @Autowired
    private AuditSuc2OldSignerAuthAction auditSuc2OldSignerAuthAction;

    public KpSignerNotIdCardStatusTrigger() {
        this.notIdCardUnEffectStateMachineBuilder = StateMachineBuilderFactory.create(KpSignerNotIdCardUnEffectSM.class, KpSignerStateMachine.class, KpSignerEventEnum.class, KpSignerStatusMachineContext.class);
        this.notIdCardEffectStateMachineBuilder = StateMachineBuilderFactory.create(KpSignerNotIdCardEffectSM.class, KpSignerStateMachine.class, KpSignerEventEnum.class, KpSignerStatusMachineContext.class);
    }

    /**
     * 初始化设置身份证类型以及非身份证的状态变更事件状态流转
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        initNotIdCardUnEffectStatusMachine();
        initNotIdCardEffectKpStatusMachine();
    }



    /**
     * 初始化设置证件类型为身份证类型集合-状态机流转
     */
    private void initNotIdCardUnEffectStatusMachine() {
        //0-5 初始化创建：无-有
        notIdCardUnEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.NO_DATA).to(KpSignerStateMachine.RECORDED)
                .on(KpSignerEventEnum.INIT_CREATE).when(kpSignerCommitCondition).perform(kpSignerInitCreateAction);

        //5-5 信息暂存
        notIdCardUnEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.RECORDED).to(KpSignerStateMachine.RECORDED)
                .on(KpSignerEventEnum.TEMP_STORE).perform(kpSignerTempStoreAction);

        // 5/40/107/60/20-30 特批提审中
        notIdCardUnEffectStateMachineBuilder.externalTransitions()
                .fromAmong(KpSignerStateMachine.RECORDED, KpSignerStateMachine.SPECILA_AUDIT_REJECT, KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL,
                        KpSignerStateMachine.AGENT_AUDIT_REJECT, KpSignerStateMachine.PREAUTH_FAIL)
                .to(KpSignerStateMachine.SPECILA_AUDIT_ING)
                .on(KpSignerEventEnum.COMMIT_SPECIAL_AUDIT).perform(commitSpecialAuditAction);

        //30-70 特批提审通过生效
        notIdCardUnEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.SPECILA_AUDIT_ING).to(KpSignerStateMachine.EFFECT)
                .on(KpSignerEventEnum.AUDIT_SUC_2_EFFECT).perform(auditSuc2EffectAction);

        //30-40 特批提审驳回
        notIdCardUnEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.SPECILA_AUDIT_ING).to(KpSignerStateMachine.SPECILA_AUDIT_REJECT)
                .on(KpSignerEventEnum.SPECIAL_AUDIT_FAIL).perform(specialAuditFailAction);

        //30-120 特批提审成功短信授权
        notIdCardUnEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.SPECILA_AUDIT_ING).to(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING)
                .on(KpSignerEventEnum.AUDIT_SUC_2_LEGAL_AUTH).perform(auditSuc2LegalAuthAction);

        //120-70 短信授权成功生效
        notIdCardUnEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING).to(KpSignerStateMachine.EFFECT)
                .on(KpSignerEventEnum.SIGN_AUTH_PASS).perform(authSuc2EffectAction);

        //120-107 短信授权失败
        notIdCardUnEffectStateMachineBuilder.externalTransitions()
                .fromAmong(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING, KpSignerStateMachine.SPECILA_AUDIT_ING)
                .to(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL)
                .on(KpSignerEventEnum.SIGN_AUTH_FAIL).perform(authFailAction);

    }

    /**
     * 初始化设置证件类型为非身份证类型集合-状态机流转
     */
    private void initNotIdCardEffectKpStatusMachine() {
        // 0->5 初始化创建：无-有
        notIdCardEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.NO_DATA).to(KpSignerStateMachine.RECORDED)
                .on(KpSignerEventEnum.INIT_CREATE).when(kpSignerCommitCondition).perform(kpSignerInitCreateAction);

        // 5->5 信息暂存
        notIdCardEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.RECORDED).to(KpSignerStateMachine.RECORDED)
                .on(KpSignerEventEnum.TEMP_STORE).perform(kpSignerTempStoreAction);

        // 5/70/103/107/105/101->102 变更特批审核中
        notIdCardEffectStateMachineBuilder.externalTransitions()
                .fromAmong(KpSignerStateMachine.RECORDED, KpSignerStateMachine.EFFECT, KpSignerStateMachine.CHANGE_SPECILA_AUDIT_REJECT,
                        KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL, KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT,
                        KpSignerStateMachine.CHANGE_PREAUTH_FAIL)
                .to(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING)
                .on(KpSignerEventEnum.COMMIT_SPECIAL_AUDIT).perform(commitSpecialAuditAction);

        // 102->103 变更特批审核驳回
        notIdCardEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING).to(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_REJECT)
                .on(KpSignerEventEnum.SPECIAL_AUDIT_FAIL).perform(specialAuditFailAction);

        // 102->106 变更特批审核通过原签约人授权
        notIdCardEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING).to(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING)
                .on(KpSignerEventEnum.AUDIT_SUC_2_OLD_SIGNER_AUTH).perform(auditSuc2OldSignerAuthAction);

        // 102->120 变更特批审核通过短信授权
        notIdCardEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING).to(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING)
                .on(KpSignerEventEnum.AUDIT_SUC_2_LEGAL_AUTH).perform(auditSuc2LegalAuthAction);

        // 102->70 变更特批审核通过生效
        notIdCardEffectStateMachineBuilder.externalTransition()
                .from(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING)
                .to(KpSignerStateMachine.EFFECT)
                .on(KpSignerEventEnum.AUDIT_SUC_2_EFFECT).perform(auditSuc2EffectAction);

        // 106/120/102->107 变更特批审核通过授权失败
        notIdCardEffectStateMachineBuilder.externalTransitions()
                .fromAmong(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING, KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING, KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING)
                .to(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL)
                .on(KpSignerEventEnum.SIGN_AUTH_FAIL).perform(authFailAction);

        // 120/106 ->70 授权成功生效
        notIdCardEffectStateMachineBuilder.externalTransitions()
                .fromAmong(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING, KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING)
                .to(KpSignerStateMachine.EFFECT)
                .on(KpSignerEventEnum.SIGN_AUTH_PASS).perform(authSuc2EffectAction);
    }


    /**
     * 触发器操作
     *
     * @param kpSignerEvent
     * @param context
     */
    public void trigger(KpSignerEventEnum kpSignerEvent, KpSignerStatusMachineContext context) throws WmCustomerException {
        log.info("KpSignerNotIdCardStatusTrigger.trigger,kpSignerEvent={},context={}", JSON.toJSONString(kpSignerEvent), JSON.toJSONString(context));
        if (kpSignerEvent == null || KpSignerEventEnum.of(kpSignerEvent.getCode()) == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "无效KP签约人事件");
        }

        //新增场景oldWmCustomerKp为空，兼容新增
        WmCustomerKp oldWmCustomerKp = context.getOldCustomerKp();
        if (oldWmCustomerKp == null) {
            oldWmCustomerKp = context.getWmCustomerKp();
        }

        //默认是当前KP状态
        KpSignerStateMachine currentState = KpSignerStateMachine.getByState(oldWmCustomerKp.getState());
        //存在中间表数据则当前状态需要重置
        WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(oldWmCustomerKp.getId());
        if (wmCustomerKpTemp != null) {
            currentState = KpSignerStateMachine.getByState(wmCustomerKpTemp.getState());
            context.setTempKp(wmCustomerKpTemp);
        }

        KpSignerBaseSM kpSignerBaseSM = null;
        if (!context.getExistEffectiveFlag()) {
            kpSignerBaseSM = notIdCardUnEffectStateMachineBuilder.newStateMachine(currentState);
        } else {
            kpSignerBaseSM = notIdCardEffectStateMachineBuilder.newStateMachine(currentState);
        }
        kpSignerBaseSM.start();
        kpSignerBaseSM.fire(kpSignerEvent, context);
    }
}
