package com.sankuai.meituan.waimai.customer.domain;

import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class WmCustomerKpOperateParam {
    private WmCustomerDB wmCustomer;
    private WmCustomerKp wmCustomerKp;
    private int operateType;
    private int uid;
    private String uname;
    private List<WmCustomerKp> oldCustomerKpList;

    public WmCustomerKpOperateParam(WmCustomerKp wmCustomerKp, int operateType) {
        this.wmCustomerKp = wmCustomerKp;
        this.operateType = operateType;
    }
}
