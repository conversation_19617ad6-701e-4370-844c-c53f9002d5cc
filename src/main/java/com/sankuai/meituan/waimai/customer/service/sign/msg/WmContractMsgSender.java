package com.sankuai.meituan.waimai.customer.service.sign.msg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.meituan.waimai.customer.bo.sign.ContractMsg;
import com.sankuai.meituan.waimai.customer.service.AutoWireBase;
import com.sankuai.meituan.waimai.settle.WmSettleMsgBo;
import com.sankuai.meituan.waimai.thrift.config.ConfigUtilAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WmContractMsgSender extends AutoWireBase {
    private static Logger log = LoggerFactory.getLogger(WmContractMsgSender.class);

    @Resource(name = "customerContractMafkaProducer")
    private MafkaProducer customerContractMafkaProducer;


    public static final String SEND_SETTLECHANGEMSG_MAFKA_OPEN = "send_contractChangeMsg_mafka_open";


    /**
     * 电销外呼传递的数据
     */
    public void sendContractChangeMsg(ContractMsg contractMsg) {
        log.info("电销外呼传递的数据:发送进入的数据:{}",  JSON.toJSONString(contractMsg));
        JSONObject jo = new JSONObject();
        jo.put("customerId", contractMsg.getCustomerId());
        jo.put("kpPhoneNum", contractMsg.getKpPhoneNum());
        jo.put("time", System.currentTimeMillis()/1000);

        if (ConfigUtilAdapter.getBoolean(SEND_SETTLECHANGEMSG_MAFKA_OPEN, true)) {
            try {
                customerContractMafkaProducer.sendMessage(jo.toJSONString());
            } catch (Exception e) {
                log.error("customerSettlePoiMafkaProducer sendMsg exception");
            }
        }
        log.info("电销外呼传递的数据:最终发送数据:{}", jo.toJSONString());
    }


}
