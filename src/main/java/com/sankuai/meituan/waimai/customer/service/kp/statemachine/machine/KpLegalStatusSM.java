package com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpLegalStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpLegalEventEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpLegalStateMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.squirrelframework.foundation.fsm.impl.AbstractStateMachine;

/**
 * <AUTHOR>
 * @date 20240408
 * @desc KP法人默认状态机
 */
@Slf4j
public class KpLegalStatusSM extends AbstractStateMachine<KpLegalStatusSM, KpLegalStateMachine, KpLegalEventEnum, KpLegalStatusMachineContext> {

    /**
     * 异常处理
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void afterTransitionCausedException(KpLegalStateMachine fromState, KpLegalStateMachine toState, KpLegalEventEnum event, KpLegalStatusMachineContext context) {
        log.info("afterTransitionCausedException,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        super.afterTransitionCausedException(fromState, toState, event, context);
    }

    /**
     * 每次流转完成时
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void afterTransitionCompleted(KpLegalStateMachine fromState, KpLegalStateMachine toState, KpLegalEventEnum event, KpLegalStatusMachineContext context) {
        log.info("afterTransitionCompleted,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        // 更新KP状态
        super.afterTransitionCompleted(fromState, toState, event, context);
    }

    /**
     * 流程实例初始节点开始前
     *
     * @param fromState
     * @param event
     * @param context
     */
    @Override
    protected void beforeTransitionBegin(KpLegalStateMachine fromState, KpLegalEventEnum event, KpLegalStatusMachineContext context) {
        log.info("beforeTransitionBegin,fromState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    @Override
    protected void afterTransitionEnd(KpLegalStateMachine fromState, KpLegalStateMachine toState, KpLegalEventEnum event, KpLegalStatusMachineContext context) {
        log.info("afterTransitionEnd,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 每一个action执行前
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void beforeActionInvoked(KpLegalStateMachine fromState, KpLegalStateMachine toState, KpLegalEventEnum event, KpLegalStatusMachineContext context) {
        log.info("beforeActionInvoked,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 每一个action执行后
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void afterActionInvoked(KpLegalStateMachine fromState, KpLegalStateMachine toState, KpLegalEventEnum event, KpLegalStatusMachineContext context) {
        log.info("afterActionInvoked,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    @Override
    public KpLegalStateMachine getCurrentState() {
        return super.getCurrentState();
    }

    @Override
    public void start() {
        super.start();
    }

}
