package com.sankuai.meituan.waimai.customer.domain.poi;

import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

/**
 * 客户门店关系解绑确认处理对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WmCustomerPoiUnbindConfirmBo {
    /**
     * 待解绑客户id
     */
    private int customerId;
    /**
     * 待解绑门店
     */
    private Set<Long> wmPoiIdSet;
    /**
     * 操作人id
     */
    private Integer opUid;
    /**
     * 操作人姓名
     */
    private String opName;
    /**
     * 客户门店解绑操作请求类型
     */
    private CustomerPoiUnBindTypeEnum unBindTypeEnum;
    /**
     * 操作日志类型
     */
    private WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum;
    /**
     * 短信记录ID
     */
    private int smsId;
}
