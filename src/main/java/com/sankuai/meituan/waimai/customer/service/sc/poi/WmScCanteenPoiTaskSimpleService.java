package com.sankuai.meituan.waimai.customer.service.sc.poi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.Json;
import com.dianping.lion.client.util.StringUtils;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.crm.ticket.thrift.exception.WmTicketServerException;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskNodeDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTairService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.check.WmCanteenStallCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.check.WmCanteenPoiCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenDao;
import com.sankuai.meituan.waimai.customer.service.sc.workflow.WmAuditPersonService;
import com.sankuai.meituan.waimai.customer.service.sc.workflow.WmCanteenPoiTaskService;
import com.sankuai.meituan.waimai.customer.service.sc.workflow.WmCanteenPoiTaskSimpleService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditProgressEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditResultEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanteenPoiSimpleProgressDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanteenPoiSimpleStreamDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmPoiTransferLimitDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallAuditProgressDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallAuditStreamDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScCanteenPoiTaskDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

@Slf4j
@Service
public class WmScCanteenPoiTaskSimpleService {


    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmScCanteenDao wmScCanteenDao;

    @Autowired
    private WmScCanteenPoiTaskMapper wmScCanteenPoiTaskMapper;

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    @Autowired
    private List<WmCanteenPoiTaskSimpleService> wmCanteenPoiTaskSimpleServiceList;

    @Autowired
    private WmCanteenPoiCheckService wmCanteenPoiCheckService;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;


    @Autowired
    private WmPoiAttributesMapper wmPoiAttributesMapper;

    @Autowired
    private WmAuditPersonService wmAuditPersonService;

    @Autowired
    private WmScCanteenPoiTaskSimpleService wmScCanteenPoiTaskSimpleService;

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Autowired
    private WmCanteenStallCheckService wmCanteenStallCheckService;

    @Autowired
    private WmScTairService wmScTairService;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;



    public static final Integer  MAX_UNBIND_BIND_LIMIT = 50;

    public static final String CANTEEN_POI_TASK_LOCKKEY_PREFIX = "canteen_ubind_transfer_task_";


    /**
     * 提交食堂绑定/换绑门店任务。
     * 该方法负责将 DTO 转换为业务对象（BO），进行校验、创建任务并根据审核状态执行相应操作。
     *
     * @param wmScCanteenPoiTaskDTO 数据传输对象，包含任务相关信息
     * @return 返回生成的任务ID
     * @throws WmSchCantException 当任务处理服务未找到或其他处理异常时抛出
     * @throws TException Thrift 异常，处理远程服务调用的异常
     */
    public Long submitTaskSimple(WmScCanteenPoiTaskDTO wmScCanteenPoiTaskDTO) throws WmSchCantException, TException {
        log.info("[WmScCanteenPoiTaskSimpleService.submitTaskSimple] input: wmScCanteenPoiTaskDTO = {}", JSONObject.toJSONString(wmScCanteenPoiTaskDTO));

        // 启动一个计时器来跟踪方法执行时间
        Stopwatch sw = Stopwatch.createStarted();

        // 1. 将 DTO 转换为业务对象
        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = buildTaskInfo(wmScCanteenPoiTaskDTO);

        // 2. 获取处理当前任务类型的具体服务
        Optional<WmCanteenPoiTaskSimpleService> taskService = getTaskService(wmCanteenPoiTaskBO.getTaskType());
        if (!taskService.isPresent()) {
            // 如果对应的处理服务不存在，抛出异常提示服务错误
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "创建任务异常:处理服务未找到");
        }

        // 3. 信息校验
        taskService.get().checkTask(wmCanteenPoiTaskBO);

        // 4. 创建任务
        taskService.get().createTask(wmCanteenPoiTaskBO);

        // 5. 检查任务是否已经生效，如果生效，则执行生效处理
        if (wmCanteenPoiTaskBO.getAuditStatus() != null
                && wmCanteenPoiTaskBO.getAuditStatus() == CanteenPoiAuditStatusV2Enum.EFFECTED.getCode()) {
            taskService.get().effectTask(wmCanteenPoiTaskBO);
        }

        // 将结果及执行时间
        log.info("[WmScCanteenPoiTaskSimpleService.submitTaskSimple] output: task id = {}, time(ms) = {}", wmCanteenPoiTaskBO.getId(), sw.elapsed(TimeUnit.MILLISECONDS));
        return wmCanteenPoiTaskBO.getId();
    }


    /**
     * 构建任务信息，将输入的 DTO 转换为 BO，并进行必要的校验。
     *
     * @param wmScCanteenPoiTaskDTO 包含任务信息的数据传输对象
     * @return 返回构建好的任务业务对象（BO）
     * @throws WmSchCantException 当参数异常或任务配置不正确时抛出
     */
    private WmCanteenPoiTaskBO buildTaskInfo(WmScCanteenPoiTaskDTO wmScCanteenPoiTaskDTO) throws WmSchCantException {
        log.info("[WmScCanteenPoiTaskSimpleService.buildTaskInfo] input: wmScCanteenPoiTaskDTO = {}", JSONObject.toJSONString(wmScCanteenPoiTaskDTO));
        if (wmScCanteenPoiTaskDTO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常");
        }

        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = new WmCanteenPoiTaskBO();
        CanteenPoiTaskTypeEnum canteenPoiTaskTypeEnum = CanteenPoiTaskTypeEnum.of(wmScCanteenPoiTaskDTO.getTaskType());

        // 检查任务类型是否为未知类型
        if (canteenPoiTaskTypeEnum == CanteenPoiTaskTypeEnum.UNKNOW) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常");
        }

        // 检查任务原因类型是否为空
        if (wmScCanteenPoiTaskDTO.getTaskReasonType() == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:任务原因必填");
        }

        // 设置任务类型
        wmCanteenPoiTaskBO.setTaskType(wmScCanteenPoiTaskDTO.getTaskType());

        /** 换绑逻辑特判 */
        if (canteenPoiTaskTypeEnum == CanteenPoiTaskTypeEnum.TRANSFER_BIND) {
            // 检查换绑原因是否提供
            if (wmScCanteenPoiTaskDTO.getTaskReasonType() == null && StringUtils.isBlank(wmScCanteenPoiTaskDTO.getTaskReason())) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:换绑原因必填");
            }
            // 获取待绑定的食堂信息并检查存在
            WmCanteenDB wmCanteenDB = wmScCanteenDao.getById(wmScCanteenPoiTaskDTO.getCanteenIdTo());
            if (wmCanteenDB == null) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:待绑食堂未找到");
            }
            wmCanteenPoiTaskBO.setCanteenTo(wmCanteenDB);
            wmCanteenPoiTaskBO.setCanteenIdTo(wmScCanteenPoiTaskDTO.getCanteenIdTo());

            // 获取待绑定食堂的学校信息并检查存在
            WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());
            if (wmSchoolDB == null) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:待绑食堂学校未找到");
            }
            wmCanteenPoiTaskBO.setSchoolTo(wmSchoolDB);

            // 检查和处理换绑原因类型为其他的情况
            if (wmScCanteenPoiTaskDTO.getTaskReasonType().equals(SwitchTaskReasonTypeEnum.OTHER.getCode())) {
                if (StringUtils.isBlank(wmScCanteenPoiTaskDTO.getTaskReason())) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:选择原因其他时，具体原因必填");
                }
                if (wmScCanteenPoiTaskDTO.getTaskReason().length() > 50) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:选择原因其他时，具体原因字数限制小于等于50");
                }
                wmCanteenPoiTaskBO.setTaskReason("其他-" + wmScCanteenPoiTaskDTO.getTaskReason());
            } else {
                SwitchTaskReasonTypeEnum switchTaskReasonTypeEnum = SwitchTaskReasonTypeEnum.of(wmScCanteenPoiTaskDTO.getTaskReasonType());
                wmCanteenPoiTaskBO.setTaskReason(switchTaskReasonTypeEnum != null ? switchTaskReasonTypeEnum.getDescription() : "");
            }
        }

        // 处理解绑理由设置
        if (canteenPoiTaskTypeEnum == CanteenPoiTaskTypeEnum.UNBIND) {
            if (wmScCanteenPoiTaskDTO.getTaskReasonType().equals(UnBindReasonTypeEnum.OTHER.getCode())) {
                if (StringUtils.isBlank(wmScCanteenPoiTaskDTO.getTaskReason())) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:选择原因其他时，具体原因必填");
                }
                if (wmScCanteenPoiTaskDTO.getTaskReason().length() > 50) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:选择原因其他时，具体原因字数限制小于等于50");
                }
                wmCanteenPoiTaskBO.setTaskReason("其他-" + wmScCanteenPoiTaskDTO.getTaskReason());
            } else {
                UnBindReasonTypeEnum TaskTypeEnum = UnBindReasonTypeEnum.of(wmScCanteenPoiTaskDTO.getTaskReasonType());
                wmCanteenPoiTaskBO.setTaskReason(TaskTypeEnum != null ? TaskTypeEnum.getDescription() : "");
            }
        }

        // 处理删除食堂理由设置
        if (canteenPoiTaskTypeEnum == CanteenPoiTaskTypeEnum.DELETE) {
            // 检查删除原因是否提供
            if (wmScCanteenPoiTaskDTO.getTaskReasonType() == null && StringUtils.isBlank(wmScCanteenPoiTaskDTO.getTaskReason())) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:删除原因必填");
            }
            if (wmScCanteenPoiTaskDTO.getTaskReasonType().equals(DeleteCanteenReasonTypeEnum.OTHER.getCode())) {
                if (StringUtils.isBlank(wmScCanteenPoiTaskDTO.getTaskReason())) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:选择原因其他时，具体原因必填");
                }
                if (wmScCanteenPoiTaskDTO.getTaskReason().length() > 50) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:选择原因其他时，具体原因字数限制小于等于50");
                }
                wmCanteenPoiTaskBO.setTaskReason("其他-" + wmScCanteenPoiTaskDTO.getTaskReason());
            } else {
                DeleteCanteenReasonTypeEnum switchTaskReasonTypeEnum = DeleteCanteenReasonTypeEnum.of(wmScCanteenPoiTaskDTO.getTaskReasonType());
                wmCanteenPoiTaskBO.setTaskReason(switchTaskReasonTypeEnum.getDescription());
            }
        }

        // 原食堂 ID 校验
        if (wmScCanteenPoiTaskDTO.getCanteenIdFrom() == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:原食堂id必填");
        }
        WmCanteenDB wmCanteenFrom = wmScCanteenDao.getById(wmScCanteenPoiTaskDTO.getCanteenIdFrom());
        if (wmCanteenFrom == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:原食堂未找到");
        }

        // 任务流程和证明材料的校验
        if (wmScCanteenPoiTaskDTO.getAuditNodeType() == null || CanteenAuditProgressEnum.of(wmScCanteenPoiTaskDTO.getAuditNodeType()) == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:任务流程必填");
        }
        if (wmScCanteenPoiTaskDTO.getProofMaterialImage() == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:证明材料必填");
        }

        // 设置基本的任务信息
        wmCanteenPoiTaskBO.setProofMaterialImage(wmScCanteenPoiTaskDTO.getProofMaterialImage());
        wmCanteenPoiTaskBO.setCanteenFrom(wmCanteenFrom);
        wmCanteenPoiTaskBO.setTaskReasonType(wmScCanteenPoiTaskDTO.getTaskReasonType());
        //wmCanteenPoiTaskBO.setTaskReason(wmScCanteenPoiTaskDTO.getTaskReason());
        wmCanteenPoiTaskBO.setCanteenIdFrom(wmScCanteenPoiTaskDTO.getCanteenIdFrom());
        wmCanteenPoiTaskBO.setUserId(wmScCanteenPoiTaskDTO.getUserId());
        wmCanteenPoiTaskBO.setUserName(wmScCanteenPoiTaskDTO.getUserName());

        // 删除食堂审批流程处理
        if (canteenPoiTaskTypeEnum == CanteenPoiTaskTypeEnum.DELETE) {
            // 删除食堂审批流程不需要处理门店ID列表,当食堂关联的档口绑定任务中，存在“线索绑定状态”、“外卖门店绑定状态”为：“绑定中”或“‘绑定成功”时，则不允许食堂被删除
            // 获取审批人列表
            WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenFrom.getSchoolId());
            List<WmEmploy> auditPersonList = wmAuditPersonService.getDeleteCanteenAuditPersionList(wmSchoolDB);
            if (auditPersonList == null || auditPersonList.isEmpty() || auditPersonList.size() < 2) {
                log.error("buildTaskInfo error, auditPersonList is null,审批人获取失败");
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:审核人获取失败");
            }
            wmCanteenPoiTaskBO.setAuditPersonList(auditPersonList);

            // 在 处理删除食堂理由设置 中新建一个空的门店
            // 设置门店 ID 列表和审核节点类型
            //删除食堂不需要处理门店绑定/解绑业务逻辑
            wmScCanteenPoiTaskDTO.setWmPoiIdList(new ArrayList<>());
            wmScCanteenPoiTaskDTO.setBindIdList(new ArrayList<>());
            wmCanteenPoiTaskBO.setAuditNodeTypeEnum(CanteenAuditProgressEnum.of(wmScCanteenPoiTaskDTO.getAuditNodeType()));
            log.info("wmCanteenPoiTaskBO:{}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
            return wmCanteenPoiTaskBO;
        }

        // 处理门店 ID 列表
        List<Long> wmPoiIdList = wmScCanteenPoiTaskDTO.getWmPoiIdList();
        wmPoiIdList = wmPoiIdList.stream()
                .distinct() // 去重操作
                .collect(Collectors.toList());

        List<WmCanteenStallBindDO> stallList = wmCanteenStallBindMapper.selectByWmPoiIdsAndCanteenPriIdWitchStatus(
                (int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType(), wmPoiIdList, wmScCanteenPoiTaskDTO.getCanteenIdFrom());
        Set<Long> existingWmPoiIds = stallList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .collect(Collectors.toSet());
        List<Long> bindIdList = stallList.stream()
                .map(stall -> Long.valueOf(stall.getId()))
                .collect(Collectors.toList());

        // 检查是否有未找到的门店
        List<Long> missingWmPoiIds = wmPoiIdList.stream()
                .filter(wmPoiId -> !existingWmPoiIds.contains(wmPoiId))
                .collect(Collectors.toList());
        if (!missingWmPoiIds.isEmpty()) {
            // 先判断没有绑定成功的原因是否是正在换绑或者解绑
            List<Long> failWmPoiIdList3 = wmCanteenStallCheckService.checkWmPoiListBind(missingWmPoiIds);
            if (CollectionUtils.isNotEmpty(failWmPoiIdList3)) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "所选门店" +
                        failWmPoiIdList3.stream().map(String::valueOf).collect(Collectors.joining("、")) +
                        "有正在换绑或者解绑的任务");
            }
            // 如果不是 说明没有绑定当前食堂
            throw new WmSchCantException(BIZ_PARA_ERROR, "所选门店" +
                    missingWmPoiIds.stream().map(String::valueOf).collect(Collectors.joining("、")) +
                    "没有绑定当前食堂");
        }
        wmCanteenPoiTaskBO.setBindIdList(bindIdList);

        // 获取审批人列表
        List<WmEmploy> auditPersonList = wmAuditPersonService.getFallbackApproverForRebinding(wmScCanteenPoiTaskDTO.getUserId());
        if (auditPersonList == null || auditPersonList.isEmpty()) {
            log.error("buildTaskInfo error, auditPersonList is null,审批人获取失败，兜底审批人也获取失败");
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:审核人获取失败");
        }
        wmCanteenPoiTaskBO.setAuditPersonList(auditPersonList);

        // 设置门店 ID 列表和审核节点类型
        wmCanteenPoiTaskBO.setWmPoiIdList(wmPoiIdList);
        wmCanteenPoiTaskBO.setAuditNodeTypeEnum(CanteenAuditProgressEnum.of(wmScCanteenPoiTaskDTO.getAuditNodeType()));
        log.info("wmCanteenPoiTaskBO:{}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        return wmCanteenPoiTaskBO;
    }



    private Optional<WmCanteenPoiTaskSimpleService> getTaskService(Integer taskType) {
        if (taskType == null) {
            return Optional.empty();
        }
        return wmCanteenPoiTaskSimpleServiceList.stream().filter(i -> i.getTaskType().getCode() == taskType).findAny();
    }

    /**
     * 处理任务审批结果。
     *
     * @param minutiaBO 必传参数：包含操作系统、操作系统ID、操作结果、操作人信息等
     * @throws WmSchCantException 当参数异常或任务处理出错时抛出
     * @throws TException 当发生 Thrift 调用异常时抛出
     */
    public void commitTask(WmCanteenPoiTaskAuditMinutiaBO minutiaBO) throws WmSchCantException, TException {
        log.info("[WmScCanteenPoiTaskSimpleService.commitTask] input param: minutiaBO = {}", JSONObject.toJSONString(minutiaBO));

        // 检查审批系统ID是否为空
        if (StringUtils.isBlank(minutiaBO.getAuditSystemId())) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "任务审批参数异常");
        }

        // 1. 获取父任务ID以及关联的任务信息
        WmTicketDto ticketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(minutiaBO.getAuditSystemId()));
        if (ticketDto == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }

        // 1.2 从系统中获取实际任务信息
        WmScCanteenPoiTaskDO taskDO = getCanteenTransferBindTaskByTicketId(ticketDto.getParentTicketId());
        if (taskDO == null) {
            log.info("[WmCanteenStallAuditService.getAuditStreamByAuditSystemId] taskDO is null. auditSystemId = {}", ticketDto.getParentTicketId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }

        // 2. 获取处理该任务类型的服务
        Optional<WmCanteenPoiTaskSimpleService> taskService = getTaskService(taskDO.getTaskType());
        if (!taskService.isPresent()) {
            log.info("No task service found for task type: {}", taskDO.getTaskType());
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "创建任务异常:处理服务未找到");
        }

        // 任务审批结果处理
        CanteenPoiAuditStatusV2Enum canteenPoiAuditStatusV2Enum = taskService.get().commitTask(minutiaBO);
        if(canteenPoiAuditStatusV2Enum != null && canteenPoiAuditStatusV2Enum.getCode() == CanteenPoiAuditStatusV2Enum.NO_ACTION.getCode()){
            log.info("状态不匹配，不向下处理");
            return;
        }

        // 3. 检查是否是最后一个审批节点；若是，则完成任务处理
        List<WmTicketDto> sonTicketDtoList = wmCrmTicketThriftServiceAdapter.getSubTicketsByParentTicketId((int) ticketDto.getParentTicketId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(sonTicketDtoList)) {
            log.info("[WmCrmTicketThriftServiceAdapter.getSubTicketsByParentTicketId] Exception. ticketDto.getParentTicketId() = {}", ticketDto.getParentTicketId());
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "任务系统查询任务信息失败，请刷新重试");
        }

        try {
            Thread.sleep(MccScConfig.getRpcProcessingDelaySeconds());
        } catch (Exception e) {
            //若抛异常则重试
            log.error("获取处理等待时间时出错" +  e);
        }

        WmTicketDto parentTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById((int) ticketDto.getParentTicketId());


        if (parentTicketDto == null) {
            log.info("[WmCanteenStallAuditService.getAuditBindListByAuditSystemId] ticketDto is null. auditSystemId = {}", ticketDto.getParentTicketId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }
        // 获取最后一个审批节点
        WmTicketDto lastTicketDto = sonTicketDtoList.get(sonTicketDtoList.size() - 1);

        String tairLockKey = CANTEEN_POI_TASK_LOCKKEY_PREFIX + "_" + ticketDto.getParentTicketId();
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

        // 如果任务通过 任务二级状态码（业务方自定义）
        if (parentTicketDto.getStage() == ProcessSecondStatusEnum.APPROVED.getCode()) {
            log.info("Task approved. Processing final approval actions for task ID: {}", taskDO.getId());
            WmCanteenPoiTaskBO taskBO = taskService.get().buildSimpleTaskBO(taskDO.getId());
            ZebraForceMasterHelper.forceMasterInLocalContext();
            taskService.get().effectTask(taskBO);
            ZebraForceMasterHelper.clearLocalContext();
        }

        // 如果任务被驳回
        if (parentTicketDto.getStage() == ProcessSecondStatusEnum.REJECTED.getCode()) {
            log.info("Task rejected. Processing reject actions for task ID: {}", taskDO.getId());
            ZebraForceMasterHelper.forceMasterInLocalContext();
            WmCanteenPoiTaskBO taskBO = taskService.get().buildSimpleTaskBO(taskDO.getId());
            taskService.get().rejectTask(taskBO);
            ZebraForceMasterHelper.clearLocalContext();
        }

        // 如果任务被终止
        if (parentTicketDto.getStage() == ProcessSecondStatusEnum.TERMINATED.getCode()) {
            log.info("Task terminated. Processing reject actions for task ID: {}", taskDO.getId());
            ZebraForceMasterHelper.forceMasterInLocalContext();
            WmCanteenPoiTaskBO taskBO = taskService.get().buildSimpleTaskBO(taskDO.getId());
            taskService.get().cancelTask(taskBO);
            ZebraForceMasterHelper.clearLocalContext();
        }

        wmScTairService.unLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

    }


    /**
     * 检查给定的 ticketType 和 taskStatus 是否符合需要处理的条件。
     *
     * @param ticketType 当前通知的票据类型
     * @param taskStatus 当前任务的审核状态
     * @return 返回 true 如果符合处理条件，否则返回 false
     */
    public Boolean isAuditNodeToBeProcessed(int ticketType, int taskStatus) {
        // 检查任务状态是否已经完成或被拒绝，不用再处理
        if (taskStatus == CanteenAuditNodeEnum.FINISH_AUDIT.getAuditStatus() ||
                taskStatus == CanteenAuditNodeEnum.REJECT_AUDITING.getAuditStatus()) {
            return false;
        }
        // 检查类型是否为一级审核节点 (换绑的第一级或解绑的第一级)
        boolean isFirstLevelAudit = (ticketType == MccScConfig.getTransferFirstSubTaskAuditNodeId() ||
                ticketType == MccScConfig.getUnbindFirstSubTaskAuditNodeId() ||
                ticketType == MccScConfig.getDeleteFirstTaskAuditNodeId());

        // 检查类型是否为二级审核节点 (换绑的第二级或解绑的第二级)
        boolean isSecondLevelAudit = (ticketType == MccScConfig.getTransferSecondSubTaskAuditNodeId() ||
                ticketType == MccScConfig.getUnbindSecondSubTaskAuditNodeId() ||
                ticketType == MccScConfig.getDeleteSecondTaskAuditNodeId());

        // 判断任务状态是否与任务类型中的审批级别符合
        return (isFirstLevelAudit && taskStatus == CanteenAuditNodeEnum.FIRST_LEVEL_APPROVAL.getAuditStatus()) ||
                (isSecondLevelAudit && taskStatus == CanteenAuditNodeEnum.SECOND_LEVEL_APPROVAL.getAuditStatus());
    }




    /**
     * 根据食堂主键ID获取换绑中的任务。
     *
     * @param canteenPrimaryKey 食堂主键ID
     * @return 换绑中的任务，如果没有找到返回 null
     */
    public WmScCanteenPoiTaskDO getCanteenTransferBindTaskByTicketId(Long canteenPrimaryKey) {
        log.info("[WmScCanteenPoiService.getCanteenTransferBindTaskByTicketId] input param: canteenPrimaryKey = {}", canteenPrimaryKey);

        // 任务搜索条件对象，并设置食堂主键ID
        WmScCanteenPoiTaskSearchCondition taskSearchCondition = new WmScCanteenPoiTaskSearchCondition();
        taskSearchCondition.setParentTicketId(canteenPrimaryKey);

        // 从数据库中查询任务列表
        List<WmScCanteenPoiTaskDO> tasks = wmScCanteenPoiTaskMapper.selectByCondition(taskSearchCondition);

        // 返回任务列表中的第一个任务，如果没有则返回 null
        return tasks.stream().findFirst().orElse(null);
    }

    /**
     * 根据任务ID获取食堂换绑中的任务。
     *
     * @param taskId 任务ID
     * @return 对应的任务对象，如果没有找到返回 null
     */
    public WmScCanteenPoiTaskDO getCanteenTransferBindTaskByTaskId(Long taskId) {
        log.info("[WmScCanteenPoiService.getCanteenTransferBindTaskByTaskId] input param: taskId = {}", taskId);
        WmScCanteenPoiTaskDO task = wmScCanteenPoiTaskMapper.selectByPrimaryKey(taskId);
        return task;
    }



    /**
     * 根据档口绑定任务ID查询审批流信息
     * @param auditSystemId 任务系统任务ID
     * @return WmCanteenStallBindListDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public WmCanteenPoiSimpleStreamDTO getSimpleAuditStreamByAuditSystemId(String auditSystemId) throws WmSchCantException, TException {
        log.info("[WmScCanteenPoiTaskSimpleService.getAuditStreamByAuditSystemId] input param: auditSystemId = {}", auditSystemId);
        if (org.apache.commons.lang3.StringUtils.isBlank(auditSystemId)) {
            log.error("[WmScCanteenPoiTaskSimpleService.getAuditStreamByAuditSystemId] auditSystemId is null. bindId = {}", auditSystemId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        // 1-根据任务系统任务ID查询审批任务
        WmTicketDto tempTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(auditSystemId));
        if (tempTicketDto == null) {
            log.error("[WmScCanteenPoiTaskSimpleService.getAuditStreamByAuditSystemId] ticketDto is null. auditSystemId = {}", auditSystemId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }
        String parames = tempTicketDto.getParams();
        JSONObject paramesJsonObject = JSONObject.parseObject(parames);
        String taskType = paramesJsonObject.getString("taskType");
        String taskId = paramesJsonObject.getString("taskId");
        //审批节点个数
        String auditNodeCodeNum = paramesJsonObject.getString("auditNodeCodeNum");

        //获取主任务信息
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTaskId(Long.valueOf(taskId));
        if (wmScCanteenPoiTaskDO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "查询任务系统查询任务异常");
        }

        // 父任务信息（任务系统）
        WmTicketDto parentTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Math.toIntExact(wmScCanteenPoiTaskDO.getParentTicketId()));
        if (parentTicketDto == null) {
            log.error("[WmScCanteenPoiTaskSimpleService.getAuditStreamByAuditSystemId] ticketDto is null. auditSystemId = {}", auditSystemId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }


        // 查询子任务数量
        List<WmTicketDto> sonTicketDtoList = wmCrmTicketThriftServiceAdapter.getSubTicketsByParentTicketId(Math.toIntExact(wmScCanteenPoiTaskDO.getParentTicketId()));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(sonTicketDtoList)) {
            log.error("[WmScCanteenPoiTaskSimpleService.getAuditStreamByAuditSystemId] Exception. auditSystemId = {}", Integer.valueOf(auditSystemId));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "任务系统查询任务信息失败");
        }

        //获取审批节点
        Integer CanteenAuditProgressCode =  wmScCanteenPoiTaskDO.getAuditNodeType();
        CanteenAuditProgressEnum progressEnum = CanteenAuditProgressEnum.of(CanteenAuditProgressCode);
        if (progressEnum == null) {
            log.error("[WmScCanteenPoiTaskSimpleService.getAuditStreamStatusAuditProgressDTOList] 未找到对应的审批流程枚举, CanteenAuditProgressCode = {}", CanteenAuditProgressCode);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "获取审批节点进度失败");
        }
        List<Integer> nodeList = progressEnum.getProgress();


        WmCanteenPoiSimpleStreamDTO AuditStreamDTO = new WmCanteenPoiSimpleStreamDTO();
        AuditStreamDTO.setAuditTaskType(Integer.valueOf(taskType));
        AuditStreamDTO.setAuditTaskTypeDesc(CanteenPoiTaskTypeEnum.of(Integer.valueOf(taskType)).getName());
        AuditStreamDTO.setAuditTaskSystemId(auditSystemId);
        //setCurrentNodeCode为auditNodeCodeNum和auditNodeCodeNum数值小的那个
        // 设置currentNodeCode为auditNodeCodeNum和ticketDtoList数量中较小的那个
        AuditStreamDTO.setCurrentNodeCode(Integer.valueOf(wmScCanteenPoiTaskDO.getCurrentNodeCode()));
        CanteenPoiAuditStatusV2Enum auditStatusV2Enum = CanteenPoiAuditStatusV2Enum.of(wmScCanteenPoiTaskDO.getAuditStatus());
        AuditStreamDTO.setCurrentAuditStatus(wmScCanteenPoiTaskDO.getAuditStatus());

        AuditStreamDTO.setCurrentAuditStatusDesc(CanteenPoiAuditStatusV2Enum.of(wmScCanteenPoiTaskDO.getAuditStatus()).getName());
        AuditStreamDTO.setAuditProgressList(getAuditStreamStatusAuditProgressDTOList(wmScCanteenPoiTaskDO, parentTicketDto,sonTicketDtoList, Integer.valueOf(auditNodeCodeNum),nodeList));
        log.info("[WmCanteenStallAuditService.getClueFollowUpStatusAuditStremInfo] AuditStreamDTO = {}", JSONObject.toJSONString(AuditStreamDTO));

        return AuditStreamDTO;
    }


    public String getUnbindTransferApprovalTaskLink(String auditSystemId)throws WmSchCantException{
        log.info("[WmScCanteenPoiTaskSimpleService.getUnbindTransferApprovalTaskLink] input param: auditSystemId = {}", auditSystemId);
        if (org.apache.commons.lang3.StringUtils.isBlank(auditSystemId)) {
            log.error("[WmScCanteenPoiTaskSimpleService.getUnbindTransferApprovalTaskLink] auditSystemId is null. auditSystemId = {}", auditSystemId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        // 1-根据任务系统任务ID查询审批任务
        WmTicketDto tempTicketDto = wmCrmTicketThriftServiceAdapter.getTicketById(Integer.valueOf(auditSystemId));
        if (tempTicketDto == null) {
            log.error("[WmScCanteenPoiTaskSimpleService.getAuditStreamByAuditSystemId] ticketDto is null. auditSystemId = {}", auditSystemId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "审批任务查询为空，请刷新重试");
        }
        String parames = tempTicketDto.getParams();
        JSONObject paramesJsonObject = JSONObject.parseObject(parames);
        String taskId = paramesJsonObject.getString("taskId");
        //审批节点个数
        String auditNodeCodeNum = paramesJsonObject.getString("auditNodeCodeNum");

        //获取主任务信息
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTaskId(Long.valueOf(taskId));
        if (wmScCanteenPoiTaskDO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "查询任务系统查询任务异常");
        }

        // 查询子任务数量
        List<WmTicketDto> sonTicketDtoList = wmCrmTicketThriftServiceAdapter.getSubTicketsByParentTicketId(Math.toIntExact(wmScCanteenPoiTaskDO.getParentTicketId()));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(sonTicketDtoList)) {
            //如果任务系统父任务类型为删除食堂,返回父任务url
            if (wmScCanteenPoiTaskDO.getTaskType() == CanteenPoiTaskTypeEnum.DELETE.getCode()) {
                return MccConfig.getApprovalProgressUrl(Long.valueOf(taskId));
            }

            log.error("[WmScCanteenPoiTaskSimpleService.getAuditStreamByAuditSystemId] Exception. auditSystemId = {}", Integer.valueOf(auditSystemId));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "任务系统查询任务信息失败");
        }
        // 审批节点个数和当前list的个数取最小的
        int auditNodeCodeNumInt = Integer.parseInt(auditNodeCodeNum);
        int sonTicketDtoListSize = sonTicketDtoList.size();
        int minNum = Math.min(auditNodeCodeNumInt, sonTicketDtoListSize);
        WmTicketDto nowTickDto = sonTicketDtoList.get(minNum - 1);
        String url = MccConfig.getApprovalProgressUrl(Long.valueOf(nowTickDto.getId()));
        log.info("[WmScCanteenPoiTaskSimpleService.getUnbindTransferApprovalTaskLink] url = {},minNum={},sonTicketDtoListSize={}", url,minNum,sonTicketDtoListSize);
        return url;
    }


    /**
     * 查询审批流各审批节点信息
     */
    private List<WmCanteenPoiSimpleProgressDTO> getAuditStreamStatusAuditProgressDTOList(WmScCanteenPoiTaskDO taskDO, WmTicketDto parentTicketDto,List<WmTicketDto> sonTicketDtoList, Integer auditNodeCodeNum,List<Integer> nodeList) throws WmSchCantException{
        String parames = parentTicketDto.getParams();
        JSONObject paramesJsonObject = JSONObject.parseObject(parames);
        String auditPersonUidListStr = paramesJsonObject.getString("auditPersonUidList");
        auditPersonUidListStr = auditPersonUidListStr.replace("'", "\"");
        List<String> auditPersonUidList = JSONArray.parseArray(auditPersonUidListStr, String.class);

        // 开始节点
        List<WmCanteenPoiSimpleProgressDTO> progressDTOList = new ArrayList<>();
        WmCanteenPoiSimpleProgressDTO firstProgressDTO = new WmCanteenPoiSimpleProgressDTO();
        firstProgressDTO.setOrder(0);
        firstProgressDTO.setAuditNode(0);
        firstProgressDTO.setAuditNodeDesc("申请提交");
        firstProgressDTO.setAuditTime(parentTicketDto.getUnixCtime());
        progressDTOList.add(firstProgressDTO);

        /** 相关数据组装*/  //  组装审批流各审批节点信息，包括审批节点、审批结果、审批人等信息（只会组装确定的审批人节点数）
        for (int i = 0; i < auditNodeCodeNum; i++) {
            WmCanteenPoiSimpleProgressDTO progressDTO = new WmCanteenPoiSimpleProgressDTO();
            progressDTO.setOrder(i + 1);
            CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nodeList.get(i));
            progressDTO.setAuditNode(auditNodeEnum == null ? 0 : auditNodeEnum.getCode());
            progressDTO.setAuditNodeDesc(auditNodeEnum == null ? "未知" : auditNodeEnum.getName());
            // 没有评论的数据不显示，也是为了防止越界
            if (i < sonTicketDtoList.size()) {
                progressDTO.setAuditRemark(sonTicketDtoList.get(i).getRemark());
                progressDTO.setAuditTime(sonTicketDtoList.get(i).getUnixUtime());
                progressDTO.setAuditResult(sonTicketDtoList.get(i).getStage());
                ProcessSecondStatusEnum processSecondStatusEnum = ProcessSecondStatusEnum.of(sonTicketDtoList.get(i).getStage());
                progressDTO.setAuditResultDesc(processSecondStatusEnum.getDescription());
            }
            Integer auditorUid = Integer.valueOf(auditPersonUidList.get(i));
            WmEmploy wmEmploy = wmScEmployAdaptor.getWmEmployByUid(auditorUid);
            progressDTO.setAuditorMis(wmEmploy.getMisId());
            progressDTO.setAuditorUid(auditorUid);
            progressDTO.setAuditorName(wmEmploy.getName());


            //progressDTO.setAuditResult();
            progressDTOList.add(progressDTO);
        }
        WmCanteenPoiSimpleProgressDTO endProgressDTO = new WmCanteenPoiSimpleProgressDTO();
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nodeList.get(nodeList.size() - 1));
        endProgressDTO.setAuditNode(auditNodeEnum == null ? 0 : auditNodeEnum.getCode());
        endProgressDTO.setAuditNodeDesc("审批完成");
        endProgressDTO.setAuditTime(sonTicketDtoList.size() < auditNodeCodeNum ? null : sonTicketDtoList.get(auditNodeCodeNum - 1).getUnixUtime());
        endProgressDTO.setOrder(auditNodeCodeNum + 1);
        progressDTOList.add(endProgressDTO);

        log.info("[getAuditStreamStatusAuditProgressDTOList] progressDTOList = {}", JSONObject.toJSONString(progressDTOList));
        return progressDTOList;
    }


    /**
     * 获取门店的换绑和解绑次数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<WmPoiTransferLimitDTO> getRemainingTransferCount(List<Long> wmPoidIds) throws WmSchCantException {
        log.info("[WmScCanteenPoiTaskSimpleService.getRemainingTransferCount] input param: wmPoidIds = {}", JSONObject.toJSONString(wmPoidIds));
        if (wmPoidIds == null || wmPoidIds.isEmpty()) {
            log.info("validateRemainingTransferCount: wmPoidIds is empty");
            return null;
        }

        List<WmPoiAttributesDO> wmPoiAttributesDOS = wmPoiAttributesMapper.selectByWmPoiIds(wmPoidIds);

        // 将list转化为map
        Map<Long, WmPoiAttributesDO> wmPoiAttributesMap = wmPoiAttributesDOS.stream()
                .collect(Collectors.toMap(WmPoiAttributesDO::getWmPoiId, Function.identity()));

        List<WmPoiTransferLimitDTO> wmPoiTransferLimitDTOS = new ArrayList<>();
        for (Long wmPoiId : wmPoidIds) {
            WmPoiAttributesDO wmPoiAttributesDO = wmPoiAttributesMap.get(wmPoiId);
            WmPoiTransferLimitDTO wmPoiTransferLimitDTO = new WmPoiTransferLimitDTO();
            wmPoiTransferLimitDTO.setWmPoiId(wmPoiId);
            if(wmPoiAttributesDO == null || wmPoiAttributesDO.getRebindCount() == null){
                wmPoiTransferLimitDTO.setChangeCount(0);
                wmPoiTransferLimitDTO.setReachedMaxLimit(0 > MccConfig.getMaxUnbindRebind0perations() ? true: false);
            }else{
                wmPoiTransferLimitDTO.setChangeCount(wmPoiAttributesDO.getRebindCount());
                wmPoiTransferLimitDTO.setReachedMaxLimit(wmPoiAttributesDO.getRebindCount() > MccConfig.getMaxUnbindRebind0perations() ? true: false);
            }
            wmPoiTransferLimitDTOS.add(wmPoiTransferLimitDTO);
        }
        return wmPoiTransferLimitDTOS;
    }




}
