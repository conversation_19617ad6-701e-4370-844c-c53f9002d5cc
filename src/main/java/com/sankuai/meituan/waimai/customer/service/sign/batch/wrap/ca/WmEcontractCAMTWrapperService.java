package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.CertifyConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;

import org.springframework.stereotype.Service;

@Service
public class WmEcontractCAMTWrapperService implements IWmEcontractCAWrapperService {

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo) {
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
            .setCustomerName(CertifyConstant.MEI_TUAN_CA)
            .build();

        return new StageBatchInfoBo.Builder()
            .stageName(WmEcontractConstant.CA_MT)
            .certifyInfoBo(certifyInfoBo)
            .build();
    }
}
