package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check.delivery;

import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CustomerSignModeAtomValidator implements IContractValidator {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        WmCustomerDB customerDB = wmCustomerService.selectCustomerById(contractBo.getBasicBo().getParentId());
        if (customerDB == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在客户");
        }
        if (customerDB.getSignMode() == CustomerSignMode.ELECTTRONIC.getCode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户为电子签约模式，不可录入纸质协议");
        }
        return true;
    }
}
