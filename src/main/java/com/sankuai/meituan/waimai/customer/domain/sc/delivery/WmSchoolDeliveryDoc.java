package com.sankuai.meituan.waimai.customer.domain.sc.delivery;

import lombok.Data;

import java.util.List;

/**
 * 校园交付搜索模型
 */
@Data
public class WmSchoolDeliveryDoc {

    /**
     * 交付ID
     */
    private Long deliveryId;

    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 学校分类,高禀赋、高单量、其他
     */
    private Integer schoolCategory;

    /**
     * 配送方式：聚合配送、自配送
     */
    private Integer deliveryType;

    /**
     * 交付状态: 交付中、交付完成、交付终止
     */
    private Integer deliveryStatus;

    /**
     * 交付发起时间
     */
    private Long createTimestamp;

    /**
     * 合同开始时间
     */
    private Long contractAuthStartTime;

    /**
     * 合同结束时间
     */
    private Long contractAuthEndTime;

    /**
     * 交付完成时间
     */
    private Long finishTimestamp;

    /**
     * 交付完成天数
     */
    private Integer finishDaysCount;

    /**
     * 交付终止时间
     */
    private Long terminateTimestamp;

    /**
     * 交付终止天数
     */
    private Integer terminateDaysCount;

    /**
     * 深度交付完成时间
     */
    private Long deepDeliveryFinishTimestamp;

    /**
     * 深度交付完成天数
     */
    private Integer deepDeliveryFinishDaysCount;

    /**
     * 订单转化率 >= 60% 时间
     */
    private Long order60TargetCompletionTimestamp;

    /**
     * 订单转化率 >= 60% 天数
     */
    private Integer order60TargetCompletionDaysCount;

    /**
     * 订单转化率 >= 90% 时间
     */
    private Long order90TargetCompletionTimestamp;

    /**
     * 订单转化率 >= 90% 天数
     */
    private Integer order90TargetCompletionDaysCount;

    /**
     * 营业档口渗透率
     */
    private Double onlineStallPercent;

    /**
     * 创建人
     */
    private Long creatorUid;

    /**
     * 首批档口交付时间
     */
    private Long firstBatchDeliveryTimestamp;

    /**
     * 第二批档口交付时间from
     */
    private Long secondBatchDeliveryTimestamp;

    /**
     * 校企所属团队
     */
    private List<Integer> campusKaOrgId;

    /**
     * 校企经理
     */
    private Integer campusKaUid;

    /**
     * 校园送所属团队
     */
    private List<Integer> channelOrgId;

    /**
     * 渠道经理
     */
    private Integer channelUid;

    /**
     * 城市所属团队
     */
    private List<Integer> cityOrgId;

    /**
     * 蜂窝负责人
     */
    private Integer aorOwnerUid;

    /**
     * 城市经理
     */
    private Integer cityUid;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 是否有效
     */
    private Integer valid;
}
