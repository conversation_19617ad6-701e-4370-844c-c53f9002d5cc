package com.sankuai.meituan.waimai.customer.service.sc.area;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScAreaPolygon;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class PoiUtil {
    private final static Logger log = LoggerFactory.getLogger(PoiUtil.class);

    public static WmScAreaPolygon getPolygonForPoints(List<WmScPoint> pointList) {
        if (CollectionUtils.isEmpty(pointList)) {
            return null;
        }
        int[] coordinates = new int[pointList.size() * 2];
        int index = 0;
        for (WmScPoint point : pointList) {
            try {
                coordinates[index++] = point.getX();
                coordinates[index++] = point.getY();
            } catch (Exception e) {
                log.error("getPolygonForPoints error, pointList:{}", JSONObject.toJSONString(pointList), e);
                return null;
            }
        }
        return new WmScAreaPolygon(coordinates);
    }

}
