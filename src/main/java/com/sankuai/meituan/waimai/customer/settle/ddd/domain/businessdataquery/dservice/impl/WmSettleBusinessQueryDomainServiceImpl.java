package com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.dservice.impl;

import java.util.List;
import java.util.Map;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.aggre.WmSettleBusinessQueryContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.aggre.factory.WmSettleBusinessQueryContextFactory;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.atom.service.impl.WmSettleBusinessQueryAtomServiceImpl;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.dservice.WmSettleBusinessQueryDomainService;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleStatusBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
public class WmSettleBusinessQueryDomainServiceImpl implements WmSettleBusinessQueryDomainService {

    @Autowired
    private WmSettleBusinessQueryContextFactory  wmSettleBusinessQueryContextFactory;

    @Autowired
    private WmSettleBusinessQueryAtomServiceImpl wmSettleBusinessQueryAtomService;

    @Override
    public Map<Long, WmSettleStatusBo> getWmSettleStatusForSwitchingWmPoiId(List<Long> wmPoiId, Long targetWmCustomerId)
            throws WmCustomerException, TException {
        WmSettleBusinessQueryContext wmSettleBusinessQueryContext = wmSettleBusinessQueryContextFactory.makeContext(wmPoiId, targetWmCustomerId);
        return wmSettleBusinessQueryAtomService.batchGetWmSettleStatus(wmSettleBusinessQueryContext);
    }
}
