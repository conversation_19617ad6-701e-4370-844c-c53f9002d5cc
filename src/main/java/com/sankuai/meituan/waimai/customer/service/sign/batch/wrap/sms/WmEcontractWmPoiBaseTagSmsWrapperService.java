package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms;

import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

@Service
public class WmEcontractWmPoiBaseTagSmsWrapperService extends AbstractWmEcontractSmsWrapperService{

    @Resource
    private EmpServiceAdaptor empServiceAdaptor;

    @Override
    public void assembleSmsParamMap(Map<String, String> smsParamMap, EcontractBatchContextBo contextBo, List<Long> wmPoiIdList) throws TException, WmCustomerException{
        WmEmploy wmEmploy = getOwnerFromCommitUid(contextBo);
        smsParamMap.put("platform", analysisPlatform(contextBo));
        smsParamMap.put("module", analysisModule(contextBo));
        smsParamMap.put("detail", analysisDetail(contextBo));
        smsParamMap.put("other", analysisOther(contextBo));
        smsParamMap.put("name", wmEmploy == null ? "" : wmEmploy.getName());
        smsParamMap.put("phone", wmEmploy == null ? "" : MoreObjects
                .firstNonNull(empServiceAdaptor.getPhone(wmEmploy.getUid()), ""));
    }

}
