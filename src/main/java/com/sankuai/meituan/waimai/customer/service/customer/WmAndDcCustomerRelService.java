package com.sankuai.meituan.waimai.customer.service.customer;

import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.nibcus.inf.customer.client.request.QualificationNumTypeGetCustomerIdRequest;
import lombok.Lombok;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 处理外卖客户和到餐客户的关系
 */
@Service
@Slf4j
public class WmAndDcCustomerRelService {

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;


    /**
     * 根据外卖客户ID对应的资质查询资质在到餐业务线下的平台客户ID
     * 外卖客户ID->资质->到餐侧平台客户ID
     * @return
     */
    public List<Long> getDcPlatformIdByWmCustomerId(Integer wmCustomerId) throws WmCustomerException, TException {
        log.info("#getDcPlatformIdByWmCustomerId-根据外卖客户ID对应的资质查询资质在到餐业务线下的平台客户ID:{}",wmCustomerId);
        // 根据wm客户ID查资质
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(wmCustomerId);
        if (wmCustomerDB == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"外卖客户ID非法");
        }
        if (CustomerBizOrgEnum.WAI_MAI.getCode() != CustomerRealTypeEnum.getByValue(wmCustomerDB.getCustomerRealType()).getBizOrgCode()){
            return new ArrayList<>();
        }
        // 到餐类型客户直接返回表中记录的平台ID
        if (CustomerRealTypeEnum.DAOCAN.getValue() == CustomerRealTypeEnum.getByValue(wmCustomerDB.getCustomerRealType()).getValue()){
            return Collections.singletonList(wmCustomerDB.getMtCustomerId());
        }
        // 根据资质编码查询到餐业务线下的平台客户ID
        QualificationNumTypeGetCustomerIdRequest request = new QualificationNumTypeGetCustomerIdRequest();
        request.setQualificationNum(wmCustomerDB.getCustomerNumber());
        request.setBusinessLineId(BusinessLineEnum.NIB_FOOD.getCode());
        List<Long> mtCustomerIds = mtCustomerThriftServiceAdapter.getCustomerIdByQualificationNum(request);
        log.info("#getDcPlatformIdByWmCustomerId-根据外卖客户ID对应的资质查询资质在到餐业务线下的平台客户ID:{},size:{}",wmCustomerId,mtCustomerIds.size());
        return mtCustomerIds;
    }

    public List<Integer> getWmCustomerIdByDcPlatformId(Long dcPlatformId) throws WmCustomerException {
        log.info("#getWmCustomerIdByDcPlatformId-根据到餐业务线下的平台客户ID查询外卖客户ID:{}",dcPlatformId);
        // 先根据平台ID查外卖侧是否有数据
        WmCustomerDB wmCustomerDB;
        wmCustomerDB = wmCustomerPlatformDataParseService.getCustomerByIdOrMtCustomerIdRT(dcPlatformId);
        if (wmCustomerDB != null){
            return Collections.singletonList(wmCustomerDB.getId());
        }
        // 根据平台ID查资质
        wmCustomerDB = mtCustomerThriftServiceAdapter.getCustomerByIdAndBusinessLine(dcPlatformId,BusinessLineEnum.NIB_FOOD.getCode());
        if (wmCustomerDB == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"平台客户ID不存在");
        }
        // 根据资质查外卖客户ID
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.getCustomerByCustomerNumberAndType(wmCustomerDB.getCustomerNumber(),wmCustomerDB.getCustomerType(),false);
        log.info("#getWmCustomerIdByDcPlatformId-根据到餐业务线下的平台客户ID查询外卖客户ID:{},size{}",dcPlatformId,wmCustomerDBList.size());
        // 1、客户必须是外卖业务线
        return wmCustomerDBList.stream()
                .filter(db -> CustomerBizOrgEnum.WAI_MAI.getCode() == CustomerRealTypeEnum.getByValue(db.getCustomerRealType()).getBizOrgCode())
                .map(WmCustomerDB::getId)
                .filter(id -> id > CustomerConstants.CUSTOMER_DATA_BOUND)
                .collect(Collectors.toList());
    }
}
