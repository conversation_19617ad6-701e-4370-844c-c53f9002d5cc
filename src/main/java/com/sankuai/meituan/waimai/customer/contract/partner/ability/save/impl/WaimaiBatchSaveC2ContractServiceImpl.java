package com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl;

import com.dianping.pigeon.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.adapter.agent.AgentPoiServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.dao.service.TemplateContractVersionService;
import com.sankuai.meituan.waimai.customer.contract.domain.TemplateContractVersionPo;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.partner.ContractSaveSceneTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.DcC1ExchangeInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.ContractOperatorDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractSaveRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractSaveResponseDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @description: 外卖-定时任务-批量保存C2合同
 * @author: liuyunjie05
 * @create: 2024/8/6 11:54
 */
@Slf4j
@Service
public class WaimaiBatchSaveC2ContractServiceImpl extends AbstractWmPartnerCustomerContractSaveAbilityService {

    @Resource
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Resource
    private AgentPoiServiceAdapter agentPoiServiceAdapter;

    @Resource
    private WmContractService wmContractService;

    @Resource
    private TemplateContractVersionService templateContractVersionService;

    @Resource
    private WmCustomerKpService wmCustomerKpService;

    @Resource
    private WmCustomerService wmCustomerService;

    @Resource
    private EmpServiceAdaptor empServiceAdaptor;

    @Override
    public ContractSaveSceneTypeEnum getSupportSceneType() {
        return ContractSaveSceneTypeEnum.WAIMAI_BATCH_SAVE_C2_CONTRACT;
    }

    @Override
    public CustomerContractSaveResponseDTO saveCustomerContract(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        try {
            validateRequestParam(requestDTO);
            List<DcC1ExchangeInfo> needResinContractList = getNeedResinContractList(requestDTO.getMtCustomerId());
            List<Integer> manualTaskIdList = saveDcContract(requestDTO.getMtCustomerId(), requestDTO.getOperatorDTO(), needResinContractList);
            return buildResponse(manualTaskIdList, requestDTO.getMtCustomerId());
        } catch (WmCustomerException e) {
            log.warn("WaimaiBatchSaveC2ContractServiceImpl#saveCustomerContract, warn", e);
            return fail(e.getMsg(), requestDTO.getMtCustomerId());
        } catch (Exception e) {
            log.error("WaimaiBatchSaveC2ContractServiceImpl#saveCustomerContract, error", e);
            return fail("保存失败", requestDTO.getMtCustomerId());
        }
    }

    private void validateRequestParam(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        checkCustomerContractParam(requestDTO);
        Integer contractType = requestDTO.getContractType();
        if (WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode() != contractType) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 该渠道暂时只支持发起到餐C2合同");
        }
    }

    private CustomerContractSaveResponseDTO buildResponse(List<Integer> manualTaskIdList, Long mtCustomerId) throws WmCustomerException {
        CustomerContractSaveResponseDTO responseDTO = success(mtCustomerId);
        responseDTO.setManualTaskIdList(manualTaskIdList);
        return responseDTO;
    }

    private List<Integer> saveDcContract(Long mtCustomerId, ContractOperatorDTO operatorDTO, List<DcC1ExchangeInfo> needResinContractList) throws WmCustomerException {
        List<Integer> manualTaskIdList = new ArrayList<>();
        List<Integer> dcC2ContractManualTaskIdList = saveDaoCanC2Contract(mtCustomerId, operatorDTO.getOpId().intValue(), operatorDTO.getOpName());
        if (!CollectionUtils.isEmpty(needResinContractList)) {
            List<Integer> dcC1ContractManualTaskIdList = saveDaoCanC1Contract(mtCustomerId, operatorDTO, needResinContractList);
            if (!CollectionUtils.isEmpty(dcC1ContractManualTaskIdList)) {
                manualTaskIdList.addAll(dcC1ContractManualTaskIdList);
            }
        }
        if (!CollectionUtils.isEmpty(dcC2ContractManualTaskIdList)) {
            manualTaskIdList.addAll(dcC2ContractManualTaskIdList);
        }
        return manualTaskIdList;
    }

    private List<Integer> saveDaoCanC1Contract(Long mtCustomerId, ContractOperatorDTO operatorDTO, List<DcC1ExchangeInfo> needResinContractList) {
        List<Integer> manualTaskIdList = new ArrayList<>();
        for (DcC1ExchangeInfo exchangeInfo : needResinContractList) {
            Integer manualTaskId = saveSingleDaoCanC1Contract(mtCustomerId, operatorDTO, exchangeInfo);
            if (manualTaskId != null && manualTaskId > 0) {
                manualTaskIdList.add(manualTaskId);
            }
        }
        return manualTaskIdList;
    }

    private Integer saveSingleDaoCanC1Contract(Long mtCustomerId, ContractOperatorDTO operatorDTO, DcC1ExchangeInfo exchangeInfo) {
        try {
            WmCustomerContractBo contractBo = wrapDcC1CustomerContractBo(mtCustomerId, operatorDTO, exchangeInfo);
            return wmContractService.saveAndStartSign(contractBo, Math.toIntExact(operatorDTO.getOpId()), operatorDTO.getOpName());
        } catch (Exception e) {
            log.error("WaimaiBatchSaveC2ContractServiceImpl#saveSingleDaoCanC1Contract, error", e);
            metricAndAlertMsg(ContractSaveSceneTypeEnum.WAIMAI_BATCH_SAVE_C2_CONTRACT.getDesc(), "发起到餐C1合同的待打包任务失败", e);
            return null;
        }
    }

    private WmCustomerContractBo wrapDcC1CustomerContractBo(Long mtCustomerId, ContractOperatorDTO operatorDTO, DcC1ExchangeInfo exchangeInfo) throws WmCustomerException, TException {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();

        long wmCustomerId = getWmCustomerByMtCustomerId(mtCustomerId);

        WmTempletContractBasicBo basicBo = initBasicBo(WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode(), (int) wmCustomerId);
        basicBo.setDaoCanContractInfo(initDcRenewC1ContractInfo(exchangeInfo, mtCustomerId));
        contractBo.setBasicBo(basicBo);

        List<WmTempletContractSignBo> signBoList = new ArrayList<>();
        signBoList.add(initDaoCanPartA((int) wmCustomerId, mtCustomerId));
        signBoList.add(initDaoCanC1PartB(operatorDTO));
        contractBo.setSignBoList(signBoList);

        contractBo.setIgnoreExistAnotherSignTypeContract(false);
        contractBo.setPackWay(SignPackWay.WAIT_HAND_PACK.getCode());
        contractBo.setManualBatchId(0);
        return contractBo;
    }

    private WmTempletContractSignBo initDaoCanC1PartB(ContractOperatorDTO operatorDTO) {
        String today = DateUtil.secondsToString(DateUtil.unixTime());
        WmTempletContractSignBo signBo = new WmTempletContractSignBo();
        signBo.setSignId(0);
        signBo.setTempletContractId(0);
        signBo.setSignName(SignSubjectEnum.BJ_SANKUAI.getDesc());
        signBo.setSignPeople(operatorDTO.getOpName());
        signBo.setSignPhone(empServiceAdaptor.getPhone(Math.toIntExact(operatorDTO.getOpId())));

        signBo.setSignTime(today);
        signBo.setSignType("B");
        return signBo;
    }

    private List<Integer> saveDaoCanC2Contract(Long mtCustomerId, int opId, String opName) throws WmCustomerException {
        Set<Integer> agentIdSet = extractAgentId(mtCustomerId);
        if (CollectionUtils.isEmpty(agentIdSet)) {
            return Collections.emptyList();
        }
        Set<Integer> noC2ContractAgentIdSet = filterNoC2ContractAgentId(mtCustomerId, agentIdSet);
        if (CollectionUtils.isEmpty(noC2ContractAgentIdSet)) {
            return Collections.emptyList();
        }
        List<Integer> manualTaskIdList = new ArrayList<>();
        for (Integer agentId : noC2ContractAgentIdSet) {
            Integer manualTaskId = saveSingleDaoCanC2Contract(mtCustomerId, agentId, opId, opName);
            if (manualTaskId != null && manualTaskId > 0) {
                manualTaskIdList.add(manualTaskId);
            }
        }
        return manualTaskIdList;
    }

    private Set<Integer> extractAgentId(Long mtCustomerId) throws WmCustomerException {
        List<Long> mtPoiIdList = mtCustomerThriftServiceAdapter.getMtPoiIdByMtCustomerId(mtCustomerId, BusinessLineEnum.NIB_FOOD.getCode());
        if (CollectionUtils.isEmpty(mtPoiIdList)) {
            return Collections.emptySet();
        }
        Set<Integer> agentIdSet = new HashSet<>();
        List<List<Long>> partitionList = Lists.partition(mtPoiIdList, 200);
        for (List<Long> partMtPoiIdList : partitionList) {
            Map<Long, Integer> agentIdMap = agentPoiServiceAdapter.getAgentIdMapByPoiIds(partMtPoiIdList);
            agentIdSet.addAll(agentIdMap.values());
        }
        return agentIdSet;
    }

    private Set<Integer> filterNoC2ContractAgentId(Long mtCustomerId, Set<Integer> agentIdSet) {
        int contractType = WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode();
        long currentTimeMillis = System.currentTimeMillis();
        List<TemplateContractVersionPo> templateContractVersionPoList = templateContractVersionService.batchQueryByMtCustomerIdAndAgentId(Collections.singletonList(mtCustomerId), new ArrayList<>(agentIdSet), contractType, currentTimeMillis);
        if (CollectionUtils.isEmpty(templateContractVersionPoList)) {
            return agentIdSet;
        }
        if (MccConfig.repairWmBatchSaveDcC2()) {
            Set<Integer> noC2ContractAgentIdSet = new HashSet<>(agentIdSet);
            for (TemplateContractVersionPo versionPo : templateContractVersionPoList) {
                noC2ContractAgentIdSet.remove(versionPo.getAgentId());
            }
            return noC2ContractAgentIdSet;
        } else {
            Set<Integer> noC2ContractAgentIdSet = new HashSet<>();
            for (TemplateContractVersionPo versionPo : templateContractVersionPoList) {
                if (!agentIdSet.contains(versionPo.getAgentId())) {
                    noC2ContractAgentIdSet.add(versionPo.getAgentId());
                }
            }
            return noC2ContractAgentIdSet;
        }
    }

    private Integer saveSingleDaoCanC2Contract(Long mtCustomerId, Integer agentId, int opId, String opName) {
        try {
            WmCustomerContractBo contractBo = wrapC2CustomerContractBo(mtCustomerId, agentId);
            return wmContractService.saveAndStartSign(contractBo, opId, opName);
        } catch (Exception e) {
            log.warn("DaoCanSelfSaveContractServiceImpl#saveSingleDaoCanC2Contract, mtCustomerId: {}, agentId: {}, error",
                    mtCustomerId, agentId, e);
            metricAndAlertMsg(ContractSaveSceneTypeEnum.WAIMAI_BATCH_SAVE_C2_CONTRACT.getDesc(), "发起到餐C2合同的待打包任务失败", e);
            return null;
        }
    }

    private WmCustomerContractBo wrapC2CustomerContractBo(Long mtCustomerId, int agentId) throws WmCustomerException {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();

        long wmCustomerId = getWmCustomerByMtCustomerId(mtCustomerId);
        WmTempletContractBasicBo basicBo = initBasicBo(WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode(), (int) wmCustomerId);
        basicBo.setDaoCanContractInfo(initDaoCanC2ContractInfo(false, mtCustomerId));
        contractBo.setBasicBo(basicBo);

        List<WmTempletContractSignBo> signBoList = new ArrayList<>();
        signBoList.add(initDaoCanPartA((int) wmCustomerId, mtCustomerId));
        signBoList.add(initPartB(agentId));
        contractBo.setSignBoList(signBoList);

        contractBo.setIgnoreExistAnotherSignTypeContract(false);
        contractBo.setPackWay(SignPackWay.WAIT_HAND_PACK.getCode());
        contractBo.setManualBatchId(0);
        return contractBo;
    }


    private WmTempletContractSignBo initPartB(int agentId) throws WmCustomerException {
        WmTempletContractSignBo signBo = initAgentSignBo(agentId);
        signBo.setSignType("B");
        return signBo;
    }


}
