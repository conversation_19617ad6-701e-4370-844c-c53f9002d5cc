package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaSearchCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmScCanteenPoiTaskAuditMinutiaMapper {

    int insertSelective(WmScCanteenPoiTaskAuditMinutiaDO record);

    int updateByPrimaryKeySelective(WmScCanteenPoiTaskAuditMinutiaDO record);

    List<WmScCanteenPoiTaskAuditMinutiaDO> selectByCanPoiTaskId(long taskId);

    List<WmScCanteenPoiTaskAuditMinutiaDO> selectByCondition(WmScCanteenPoiTaskAuditMinutiaSearchCondition condition);

    WmScCanteenPoiTaskAuditMinutiaDO selectByAuditSystem(@Param("auditSystemType") int auditSystemType, @Param("auditSystemId") String auditSystemId);

    int updateUnValidByTaskId(@Param("canteenPoiTaskId") long canteenPoiTaskId);
}