package com.sankuai.meituan.waimai.customer.adapter;

import com.meituan.gecko.boot.util.JacksonUtils;
import com.meituan.mtrace.Tracer;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.DeliverThriftService;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverInstanceDto;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.dto.DeliverPageDto;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.req.CreateDeliverReq;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.req.DeliverCommonReq;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.req.DeliverPageGetReq;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.req.DeliverSearchReq;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.resp.CreateDeliverResponse;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.resp.DeliverDetailResponse;
import com.sankuai.waimai.wmcrmplatform.saas.deliver.sdk.thrift.resp.DeliverPageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class DeliverThriftServiceAdapter {

    @Autowired
    private DeliverThriftService deliverThriftService;

    public DeliverInstanceDto findDeliverInstanceById(Long deliveryId) {
        init();

        if (Objects.isNull(deliveryId)) {
            return null;
        }

        DeliverCommonReq deliverCommonReq = new DeliverCommonReq();
        deliverCommonReq.setDeliverInstanceId(deliveryId);

        try {
            log.info("根据id获取校园交付实例信息, deliveryId:{}", deliveryId);
            DeliverDetailResponse response = deliverThriftService.detail(deliverCommonReq);
            log.info("根据id获取校园交付实例信息, 结果为:{}", JacksonUtils.toJson(response));

            if (response.getCode() != 0) {
                log.error("查询交付实例异常, code:{}, msg:{}", response.getCode(), response.getMsg());
                throw new RuntimeException(response.getMsg());
            }

            return response.getData();
        } catch (Exception e) {
            log.error("查询交付实例失败", e);
            throw new RuntimeException("查询交付实例失败");
        }
    }

    public DeliverInstanceDto findDeliverInstanceOpportunityId(Long opportunityId) {
        init();

        if (Objects.isNull(opportunityId)) {
            return null;
        }

        DeliverSearchReq deliverSearchReq = new DeliverSearchReq();
        deliverSearchReq.setOpportunityId(opportunityId);

        try {
            log.info("根据商机id获取校园交付实例信息, opportunityId:{}", opportunityId);
            DeliverDetailResponse response = deliverThriftService.getByOpportunityId(deliverSearchReq);
            log.info("根据商机id获取校园交付实例信息, 结果为:{}", JacksonUtils.toJson(response));

            if (response.getCode() != 0) {
                log.error("查询交付实例异常, code:{}, msg:{}", response.getCode(), response.getMsg());
                throw new RuntimeException(response.getMsg());
            }

            return response.getData();
        } catch (Exception e) {
            log.error("根据商机id查询交付实例失败", e);
            return null;
        }
    }

    public Long create(Long opportunityId, String bizApiName, String bizInstanceId, Integer startUid, String extension) {
        try {
            init();

            CreateDeliverReq createDeliverReq = new CreateDeliverReq();
            createDeliverReq.setOpportunityId(opportunityId);
            createDeliverReq.setBizApiName(bizApiName);
            createDeliverReq.setBizInstanceId(bizInstanceId);
            createDeliverReq.setStartUid(startUid);
            createDeliverReq.setExtension(extension);

            log.info("发起交付请求, req:{}", JacksonUtils.toJson(createDeliverReq));
            CreateDeliverResponse response = deliverThriftService.create(createDeliverReq);
            log.info("发起交付请求, resp:{}", JacksonUtils.toJson(response));

            if (response.getCode() != 0) {
                log.error("发起交付失败, code:{}, msg:{}", response.getCode(), response.getMsg());
                throw new RuntimeException(response.getMsg());
            }

            return response.getData();
        } catch (Exception e) {
            log.error("发起交付失败", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    public DeliverPageDto deliverPageGet(Long minId, Integer pageSize) {
        try {
            init();

            DeliverPageGetReq deliverPageGetReq = new DeliverPageGetReq();
            deliverPageGetReq.setMinDeliverInstanceId(minId);
            deliverPageGetReq.setPageSize(pageSize);

            log.info("分页查询交付计划, req:{}", JacksonUtils.toJson(deliverPageGetReq));
            DeliverPageResponse resp = deliverThriftService.deliverPageGet(deliverPageGetReq);
            log.info("分页查询交付计划返回, resp:{}", JacksonUtils.toJson(resp));

            if (resp.getCode() != 0) {
                log.error("分页查询失败, code:{}, msg:{}", resp.getCode(), resp.getMsg());
                throw new RuntimeException(resp.getMsg());
            }
            return resp.getData();
        } catch (Exception e) {
            log.error("分页查询失败", e);
            throw new RuntimeException(e.getMessage());
        }



    }

    private void init() {
        Tracer.putContext("tenantId", "1000008");
        Tracer.putContext("bizId", "8999");
    }

}
