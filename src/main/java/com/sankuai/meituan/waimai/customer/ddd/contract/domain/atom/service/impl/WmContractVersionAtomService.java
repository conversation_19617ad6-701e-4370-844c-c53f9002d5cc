package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;

import com.sankuai.meituan.waimai.customer.contract.dao.WmContractVersionDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WmContractVersionAtomService {

    @Autowired
    private WmContractVersionDBMapper wmContractVersionDBMapper;

    public Integer insert(WmContractVersionDB wmContractVersionDB) {
        return wmContractVersionDBMapper.insertSelective(wmContractVersionDB);
    }

    public WmContractVersionDB getByIdAndTypeMaster(int contractId, byte type) {
        return wmContractVersionDBMapper.getLastWmContractVersionByWmContractIdMaster(contractId, type);
    }

    public WmContractVersionDB getByIdAndType(int contractId, byte type) {
        return wmContractVersionDBMapper.getLastWmContractVersionByWmContractId(contractId, type);
    }

    public WmContractVersionDB getByVersionNumberMaster(String versionNumber) {
        return wmContractVersionDBMapper.getLastWmContractVersionByVersionNumberMaster(versionNumber);
    }

    public WmContractVersionDB getByTransactionIdMaster(String transactionId) {
        return wmContractVersionDBMapper.getLastByTransactionIdAndTypeMaster(transactionId, CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
    }

    public int updateStatus(WmContractVersionDB wmContractVersionDB) {
        return wmContractVersionDBMapper.updateStatus(wmContractVersionDB);
    }

    public int updatePdfUrl(int id, String pdf, String stampPdf) {
        return wmContractVersionDBMapper.updatePdfUrl(id, pdf, stampPdf);
    }

    public List<WmContractVersionDB> getByWmContractIdAndStatus(int templetContractId, int type, List<Byte> statusList) {
        return wmContractVersionDBMapper.getByWmContractIdAndStatus(templetContractId, type, statusList);
    }

    public List<WmContractVersionDB> getByWmContractIdAndTypeAndStatus(int templetContractId, List<Byte> typeList, List<Byte> statusList) {
        return wmContractVersionDBMapper.getByWmContractIdAndTypeAndStatus(templetContractId, typeList, statusList);
    }

    public int updateTransactionId(String transactionId, String versionNum) {
        return wmContractVersionDBMapper.updateTransactionId(transactionId, versionNum);
    }


    public WmContractVersionDB getById(Integer id){
        return  wmContractVersionDBMapper.getById(id);
    }

    public WmContractVersionDB getLastByWmContractIdAndTypeAndStatusList(Integer wmContractId, Byte type, List<Byte> statusList) {
        return wmContractVersionDBMapper.getLastByWmContractIdAndTypeAndStatusList(wmContractId, type, statusList);
    }

}
