package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.*;

public enum SignTemplateEnum {

    //描述 pdf模板-模板所属模块-模板需要的签章

    //合同北京
    C1CONTRACT_INFO_V3("c1contract_info_v3.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP)),

    //c2合同
    C2CONTRACT_INFO_V3("c2contract_info_v3.ftl", "C2合同", MODEL_C2CONTRACT, TAB_C2CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.AGENT_STAMP)),
    //合同北京三快、上海三快智送科技有限公司、百寿
    C1CONTRACT_INFO_V4("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP,
                    StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.SHENZHENBAISHOU_STAMP)),
    //合同百寿、北京三快
    C1CONTRACT_INFO_V4_BAISHOUJIANKANG("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.SHENZHENBAISHOU_STAMP)),

    //合同上海三快智送科技有限公司、北京三快
    C1CONTRACT_INFO_V4_SHSANKUAI("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP,
                    StampConstant.SHANGHAI_SANKUAI_STAMP)),

    //合同上海三快智送科技有限公司
    C1CONTRACT_INFO_V4_ONLY_SHSANKUAI("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP)),

    //合同北京三快、北京三快
    C1CONTRACT_INFO_V4_ONLY_BJSANKUAI("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP)),

    //合同北京三快、上海三快智送科技有限公司、百寿（区分医药非医药客户，新模板不采用name的指定方式，因此name与v4保持一致）
    C1CONTRACT_INFO_V5("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.SHENZHENBAISHOU_STAMP)),

    //合同百寿、北京三快（区分医药非医药客户，新模板不采用name的指定方式，因此name与v4保持一致）
    C1CONTRACT_INFO_V5_BAISHOUJIANKANG("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.SHENZHENBAISHOU_STAMP)),

    //合同上海三快智送科技有限公司、北京三快（区分医药非医药客户，新模板不采用name的指定方式，因此name与v4保持一致）
    C1CONTRACT_INFO_V5_SHSANKUAI("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP)),

    //合同上海三快智送科技有限公司（区分医药非医药客户，新模板不采用name的指定方式，因此name与v4保持一致）
    C1CONTRACT_INFO_V5_ONLY_SHSANKUAI("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP)),

    //合同北京三快、北京三快（区分医药非医药客户，新模板不采用name的指定方式，因此name与v4保持一致）
    C1CONTRACT_INFO_V5_ONLY_BJSANKUAI("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SANKUAI_STAMP)),

    //合同B2C
    C1CONTRACT_INFO_V5_MED_B2C("c1contract_info_v4.ftl", "C1合同", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.MED_B2C_STAMP)),

    //C1钱袋宝补充协议
    C1CONTRACT_INFO_V5_QDB("settle_moon_qdb_info_v3.ftl", "C1合同钱袋宝协议", MODEL_C1CONTRACT, TAB_C1CONTRACT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP)),


    //配送
    TECHNICAL_SERVICE_FEE("technical_service_fee_v1", "技术服务费基本信息（表格）", MODEL_DELIVERY, TAB_DELIVERY, Lists.newArrayList(StampConstant.POI_STAMP)),
    MULTI_TECHNICAL_SERVICE_FEE("multi_technical_service_fee_v1", "技术服务费基本信息（表格）", MODEL_DELIVERY, TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    TECHNICAL_SERVICE_FEE_V2("technical_service_fee_v2", "技术服务费基本信息（表格）", MODEL_DELIVERY, TAB_DELIVERY, Lists.newArrayList(StampConstant.POI_STAMP)),

    TECHNICAL_SERVICE_FEE_V2_1("technical_service_fee_v2.1", "技术服务费基本信息（表格）", MODEL_DELIVERY, TAB_DELIVERY, Lists.newArrayList(StampConstant.POI_STAMP)),

    MULTI_TECHNICAL_SERVICE_FEE_V2("multi_technical_service_fee_v2", "技术服务费基本信息（表格）", MODEL_DELIVERY, TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    MULTI_TECHNICAL_SERVICE_FEE_V2_1("multi_technical_service_fee_v2.1", "技术服务费基本信息（表格）", MODEL_DELIVERY, TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    SLA_OTHER_INFO_V2("delivery_sla_other_info_v2", "SLA其他说明", MODEL_DELIVERY, TAB_DELIVERY, null),

    PREFERENTIAL_POLICY("preferential_policy_v1", "优惠政策（表格）", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_POLICY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    MULTI_PREFERENTIAL_POLICY("multi_preferential_policy_v1", "优惠政策（表格）", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_POLICY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    PREFERENTIAL_POLICY_V2("preferential_policy_v2", "优惠政策（表格）", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_POLICY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    MULTI_PREFERENTIAL_POLICY_V2("multi_preferential_policy_v2", "优惠政策（表格）", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_POLICY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PREFERENTIAL_APPLICATION("delivery_preferential_apply_info_v2", "诚信“战略合作伙伴”优惠政策支持自愿申请书", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_APPLICATION,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    PREFERENTIAL_PERFORMANCE_GUARANTY("delivery_performance_guarance_info_v2", "履约保证函", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_APPLICATION,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    PREFERENTIAL_APPLICATION_V3("delivery_preferential_apply_info_v3", "诚信“战略合作伙伴”优惠政策支持自愿申请书", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_APPLICATION,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PREFERENTIAL_APPLICATION_V3_1("delivery_preferential_apply_info_v3.1", "诚信“战略合作伙伴”优惠政策支持自愿申请书", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_APPLICATION,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PREFERENTIAL_PERFORMANCE_GUARANTY_V3("delivery_performance_guarance_info_v3", "履约保证函", MODEL_DELIVERY, TAB_DELIVERY_PREFERENTIAL_APPLICATION,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PERFORMANCE_SERVICE_FEE("performance_service_fee_v1", "履约服务费收费标准确认（含表格）", MODEL_DELIVERY, TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    MULTI_PERFORMANCE_SERVICE_FEE("multi_performance_service_fee_v1", "履约服务费收费标准确认（含表格）", MODEL_DELIVERY, TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    PERFORMANCE_SERVICE_FEE_V2("performance_service_fee_v2", "履约服务费收费标准确认（含表格）", MODEL_DELIVERY, TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    MULTI_PERFORMANCE_SERVICE_FEE_V2("multi_performance_service_fee_v2", "履约服务费收费标准确认（含表格）", MODEL_DELIVERY, TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    DELIVERY_WHOLE_CITY_INFO("delivery_whole_city_info_v1", "全城送", MODEL_DELIVERY, TAB_DELIVERY_WHOLE_CITY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_MULTI_WHOLE_CITY_INFO("delivery_multi_whole_city_info_v1", "全城送", MODEL_DELIVERY, TAB_DELIVERY_WHOLE_CITY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_WHOLE_CITY_INFO_V2("delivery_whole_city_info_v2", "全城送", MODEL_DELIVERY, TAB_DELIVERY_WHOLE_CITY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_MULTI_WHOLE_CITY_INFO_V2("delivery_multi_whole_city_info_v2", "全城送", MODEL_DELIVERY, TAB_DELIVERY_WHOLE_CITY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    DELIVERY_AGGREGATION_INFO("delivery_aggregation_info", "聚合配送", MODEL_DELIVERY, TAB_DELIVERY_AGGREGATION, null),
    DELIVERY_MULTI_AGGREGATION_INFO("delivery_multi_aggregation_info", "聚合配送", MODEL_DELIVERY, TAB_DELIVERY_AGGREGATION, null),
    DELIVERY_AGGREGATION_SUPPLEMENT_INFO("delivery_aggregation_supplement_info", "聚合配送补充协议", MODEL_DELIVERY, TAB_DELIVERY_AGGREGATION,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SANKUAI_STAMP)),
    DELIVERY_AGGREGATION_INFO_V2("delivery_aggregation_info_v2", "聚合配送", MODEL_DELIVERY, TAB_DELIVERY_AGGREGATION, Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_MULTI_AGGREGATION_INFO_V2("delivery_multi_aggregation_info_v2", "聚合配送", MODEL_DELIVERY, TAB_DELIVERY_AGGREGATION, Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_MULTI_AGGREGATION_INFO_SG20("delivery_multi_aggregation_info_sg20", "闪购2.0聚合配送", MODEL_DELIVERY, TAB_DELIVERY_AGGREGATION, Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_MULTI_AGGREGATION_INFO_SG22("delivery_multi_aggregation_info_sg22", "闪购2.2聚合配送", MODEL_DELIVERY, TAB_DELIVERY_AGGREGATION, Lists.newArrayList(StampConstant.POI_STAMP)),

    DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE("company_customer_long_distance_v1", "企客远距离", MODEL_DELIVERY, TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE, Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_MULTI_COMPANY_CUSTOMER_LONG_DISTANCE("multi_company_customer_long_distance_v1", "企客远距离", MODEL_DELIVERY, TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE, Lists.newArrayList(StampConstant.POI_STAMP)),

    DELIVERY_AGENT_NEW("delivery_agent_new_v1", "合作商服务费", MODEL_DELIVERY, TAB_DELIVERY_DELIVERY_AGENT_NEW, Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_MULTI_AGENT_NEW("multi_delivery_agent_new_v1", "合作商服务费", MODEL_DELIVERY, TAB_DELIVERY_DELIVERY_AGENT_NEW, Lists.newArrayList(StampConstant.POI_STAMP)),

    DELIVERY_PER_DISCOUNT("delivery_per_discount_v1", "配送服务费折扣协议", MODEL_DELIVERY, TAB_DELIVERY_DELIVERY_PER_DISCOUNT, Lists.newArrayList(StampConstant.POI_STAMP)),
    DELIVERY_MULTI_PER_DISCOUNT("multi_delivery_per_discount_v1", "配送服务费折扣协议", MODEL_DELIVERY, TAB_DELIVERY_DELIVERY_PER_DISCOUNT, Lists.newArrayList(StampConstant.POI_STAMP)),

    TECHNICAL_SERVICE_WAIMAIV3_FEEMODE("直营-外卖-费率2.0-佣金协议",
            "直营-外卖-费率2.0-佣金协议",
            MODEL_DELIVERY,
            TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    TECHNICAL_SERVICE_WAIMAIQIKE_FEEMODE("直营-外卖-外卖企客旧费率-佣金协议",
            "直营-外卖-外卖企客旧费率-佣金协议",
            MODEL_DELIVERY,
            TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    TECHNICAL_SERVICE_WAIMAIQIKEV2_FEEMODE("直营-外卖-企客新费率-佣金协议",
            "直营-外卖-企客新费率-佣金协议",
            MODEL_DELIVERY,
            TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    TECHNICAL_SERVICE_QIKEAGENT_FEEMODE("代理-外卖闪购医药-代理企客费率-佣金协议",
            "代理-外卖闪购医药-代理企客费率-佣金协议",
            MODEL_DELIVERY,
            TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    TECHNICAL_SERVICE_SHANGOUV2_2NEC_FEEMODE("闪购V2.2-企客新费率-佣金协议",
            "闪购V2.2-企客新费率-佣金协议",
            MODEL_DELIVERY,
            TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    TECHNICAL_SERVICE_YIYAO_QIKE_FEEMODE("医药-新企客新费率-佣金协议",
            "医药-新企客新费率-佣金协议",
            MODEL_DELIVERY,
            TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    TECHNICAL_SERVICE_AGENT_ACTUAL_PAYMENT("品牌代理保底实付佣金协议",
            "品牌代理保底实付佣金协议",
            MODEL_DELIVERY,
            TAB_DELIVERY,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    PERFORMANCE_SERVICE_WAIMAIV3_FEEMODE("直营-外卖-费率2.0-配送服务费协议",
            "直营-外卖-费率2.0-配送服务费协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PERFORMANCE_SERVICE_WAIMAIQIKE_FEEMODE("直营-外卖-外卖企客旧费率-配送服务费协议",
            "直营-外卖-外卖企客旧费率-配送服务费协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PERFORMANCE_SERVICE_WAIMAIQIKEV2_FEEMODE("直营-外卖-企客新费率-配送服务费协议",
            "直营-外卖-企客新费率-配送服务费协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PERFORMANCE_SERVICE_QIKEAGENT_FEEMODE("代理-外卖闪购医药-代理企客费率-配送服务费协议",
            "代理-外卖闪购医药-代理企客费率-配送服务费协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PERFORMANCE_SERVICE_SHANGOUV2_2NEC_FEEMODE("闪购V2.2-企客新费率-配送服务费协议",
            "闪购V2.2-企客新费率-配送服务费协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    PERFORMANCE_SERVICE_YIYAO_QIKE_FEEMODE("医药-企客新费率-配送服务费协议",
            "医药-企客新费率-配送服务费协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    WHOLECITY_INFO_WAIMAIV3_FEEMODE("直营-外卖-费率2.0-全城送协议",
            "直营-外卖-费率2.0-全城送协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_WHOLE_CITY,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    COLD_CHAIN_INFO_QIKE_FEEMODE("医药企客/闪购企客-冷链即时达配送服务收费标准确认函",
            "医药企客/闪购企客-冷链即时达配送服务收费标准确认函",
            MODEL_DELIVERY,
            TAB_DELIVERY_COMPANY_CUSTOMER_COLD_CHAIN,
            Lists.newArrayList(StampConstant.POI_STAMP)
    ),

    COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKE_FEEMODE("直营-外卖-外卖企客旧费率-远距离协议",
            "直营-外卖-外卖企客旧费率-远距离协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    COMPANYCUSTOMER_LONGDISTANCE_WAIMAIQIKEV2_FEEMODE("直营-外卖-企客新费率-远距离协议",
            "直营-外卖-企客新费率-远距离协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    ENTERPRISE_CUSTOMER_LONG_DISTANCE_SHANGOUV2_2NEC_FEEMODE("闪购V2.2-企客新费率-远距离协议",
            "闪购V2.2-企客新费率-远距离协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE,
            Lists.newArrayList(StampConstant.POI_STAMP)),

    ENTERPRISE_CUSTOMER_LONG_DISTANCE_YIYAO_QIKE_FEEMODE("医药-企客新费率-远距离协议",
            "医药-企客新费率-远距离协议",
            MODEL_DELIVERY,
            TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE,
            Lists.newArrayList(StampConstant.POI_STAMP)),
    //结算
    SETTLE_INFO_V3("settle_info_v3.ftl", "结算信息", MODEL_SETTLE, TAB_SETTLE, null),
    SETTLE_MOON_QDB_INFO_V3("settle_moon_qdb_info_v3.ftl", "钱袋宝协议", MODEL_SETTLE, TAB_SETTLE,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP)),
    SETTLE_MOON_SINGLE_INFO_V3("settle_moon_single_info_v3.ftl", "登月单店", MODEL_SETTLE, TAB_SETTLE, Lists.newArrayList(StampConstant.POI_STAMP)),
    SETTLE_MOON_ADVICE_INFO_V3("settle_moon_advice_info_v3.ftl", "登月多店", MODEL_SETTLE, TAB_SETTLE, Lists.newArrayList(StampConstant.POI_STAMP)),
    SETTLE_MOON_PROXY_INFO_V3("settle_moon_proxy_info_v3.ftl", "登月多店", MODEL_SETTLE, TAB_SETTLE, Lists.newArrayList(StampConstant.POI_STAMP)),
    SETTLE_BUSINESSLOANS_INFO("美团生意贷服务授权书", "生意贷协议", MODEL_SETTLE, TAB_SETTLE_BUSINESSLOANS, null),

    //主体变更补充协议
    SUBJECT_CHANGE_SUPPLEMENT("", "主体变更补充协议", MODEL_SUBJECTCHANGESUPPLEMENT, TAB_SUBJECT_CHANGE_SUPPLEMENT,
            Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.HNLX_STAMP)),

    // 国补-闪购-企客新费率-配送服务费协议
    NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_QIKE_V2_FEEMODE("国补-闪购-企客新费率-配送服务费协议",
            "国补-闪购-企客新费率-配送服务费协议",
            MODEL_NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY,
            TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.POI_STAMP)),
    // 国补-闪购-企客新费率-配送服务费协议
    NATIONAL_SUBSIDY_LONG_DISTANCE_SG_QIKE_V2_FEEMODE("国补-闪购-企客新费率-企客远距离协议",
            "国补-闪购-企客新费率-企客远距离协议",
            MODEL_NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY,
            TAB_NATION_SUBSIDY_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE,
            Lists.newArrayList(StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.POI_STAMP)),
    // 国补-闪购2.2-配送服务费协议
    NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_FEEMODE("国补-闪购2.2-配送服务费协议",
            "国补-闪购2.2-配送服务费协议",
            MODEL_NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY,
            TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.POI_STAMP)),
    // 国补-闪购2.2-新快送-配送服务费协议
    NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_NKS_FEEMODE("国补-闪购2.2-新快送-配送服务费协议",
            "国补-闪购2.2-新快送-配送服务费协议",
            MODEL_NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY,
            TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE,
            Lists.newArrayList(StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.POI_STAMP)),
    // 国补-闪购2.2-全城送协议
    NATIONAL_SUBSIDY_WHOLE_CITY_SG22_FEEMODE("国补-闪购2.2-全城送协议",
            "国补-闪购2.2-全城送协议",
            MODEL_NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY,
            TAB_NATION_SUBSIDY_DELIVERY_WHOLE_CITY,
            Lists.newArrayList(StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.POI_STAMP)),

    PERFORMANCE_SERVICE_SG_22("PERFORMANCE_SERVICE_SG_22", "闪购费率2.2-履约合同", MODEL_DELIVERY, TAB_DELIVERY_PERFORMANCE_SERVICE, Lists.newArrayList(StampConstant.POI_STAMP)),
    TECHNICAL_SERVICE_SG_20("TECHNICAL_SERVICE_SG_20", "闪购费率2.0-配送协议", MODEL_DELIVERY, TAB_DELIVERY, Lists.newArrayList(StampConstant.POI_STAMP)),
    TECHNICAL_SERVICE_SG_22("TECHNICAL_SERVICE_SG_22", "闪购费率2.2-佣金合同", MODEL_DELIVERY, TAB_DELIVERY, Lists.newArrayList(StampConstant.POI_STAMP)),
    PERFORMANCE_SERVICE_SG_22_NKS("PERFORMANCE_SERVICE_SG_22_NKS", "闪购费率2.2-履约合同-新快送", MODEL_DELIVERY, TAB_DELIVERY_PERFORMANCE_SERVICE, Lists.newArrayList(StampConstant.POI_STAMP))
    ;

    private String name;
    private String desc;
    private String model;
    private String tab;
    private List<String> stampList;

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public String getModel() {
        return model;
    }

    public String getTab() {
        return tab;
    }

    public List<String> getStampList() {
        return stampList;
    }

    SignTemplateEnum(String name, String desc, String model, String tab, List<String> stampList) {
        this.name = name;
        this.desc = desc;
        this.model = model;
        this.tab = tab;
        this.stampList = stampList;
    }

    private static Map<String, SignTemplateEnum> nameMap = Maps.newHashMap();

    static {
        for (SignTemplateEnum temp : SignTemplateEnum.values()) {
            nameMap.put(temp.getName(), temp);
        }
    }

    public static SignTemplateEnum getByName(String name) {
        return nameMap.get(name);
    }
}
