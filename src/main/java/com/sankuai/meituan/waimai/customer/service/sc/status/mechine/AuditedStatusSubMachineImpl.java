package com.sankuai.meituan.waimai.customer.service.sc.status.mechine;

import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import com.sankuai.meituan.waimai.customer.service.sc.annotate.AuditField;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusSubMachine;
import com.sankuai.meituan.waimai.customer.util.diff.sc.ScDiffUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenAuditStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenDetailStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenAuditDiffBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.DiffField;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.NO_DIFF_AUDIT_ERROR;

/**
 * 已审核状态流转（修改生效的数据）
 * <AUTHOR>
 * @date 2020/11/24 16:37
 */
@Component("auditedStatusSubMachine")
public class AuditedStatusSubMachineImpl extends AbstractAuditStatusSubMachine implements CanteenStatusSubMachine {

    /**
     * 流转状态
     * @param canteenDB 食堂主表对象
     * @param wmScCanteenAuditDO 食堂审核表对象
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public void changeStatus(WmCanteenDB canteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) throws WmSchCantException {
        ArrayList<DiffField> diffFields = ScDiffUtil.auditDiff(canteenDB, wmScCanteenAuditDO, AuditField.class);
        if (diffFields.size() <= 0) {
            hasNoDiff();
        }

        canteenDB.setAuditStatus((int) CanteenAuditStatusEnum.AUDITING.getType());
        canteenDB.setAuditDetailStatus((int) CanteenDetailStatusEnum.UPDATE_AUDITING_STATUS.getType());
        wmScCanteenAuditDO.setAuditStatus((int) CanteenAuditStatusEnum.AUDITING.getType());
    }

    /**
     * 审核通过的没有diff
     * @param canteenDB 食堂主表对象
     * @return CanteenAuditDiffBo
     */
    @Override
    public CanteenAuditDiffBo getCanteenAuditDiffBo(WmCanteenDB canteenDB) {
        return new CanteenAuditDiffBo();
    }

    @Override
    public ArrayList<DiffField> getCanteenAuditDiffList(WmCanteenDB canteenDB) {
        return new ArrayList<>();
    }

    private void hasNoDiff() throws WmSchCantException {
        throw new WmSchCantException(NO_DIFF_AUDIT_ERROR, "无修改项不允许保存");
    }

    @Override
    public boolean isAudited(WmCanteenDB canteenDB){
        return true;
    }


}
