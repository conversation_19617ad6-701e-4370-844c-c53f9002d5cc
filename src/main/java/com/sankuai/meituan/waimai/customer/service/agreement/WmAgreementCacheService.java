package com.sankuai.meituan.waimai.customer.service.agreement;

import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.dao.WmAgreementModelDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmAgreementModelDicMapper;
import com.sankuai.meituan.waimai.kv.service.bizeapi.WmPoiHybirdEapiKvService;
import com.sankuai.meituan.waimai.kv.service.bizeapi.WmPoiPromiseEapiKvService;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.AgreementTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.agreement.WmAgreementModel;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class WmAgreementCacheService {

    @Autowired
    private WmAgreementModelDicMapper wmAgreementModelDicMapper;

    @Autowired
    private WmAgreementModelDBMapper wmAgreementModelDBMapper;

    @Autowired
    private WmPoiPromiseEapiKvService wmPoiPromiseEapiKvService;

    @Autowired
    private WmPoiHybirdEapiKvService wmPoiHybirdEapiKvService;

    // 30分钟刷新一次
    private LoadingCache<String, Map<AgreementTypeEnum,Integer>> versionCacheBuilder = CacheBuilder
            .newBuilder().maximumSize(1).expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Map<AgreementTypeEnum,Integer>>() {
                @Override
                public Map<AgreementTypeEnum,Integer> load(String str) throws Exception {
                    log.info("versionCacheBuilder into cache,str={}", str);
                    Map<AgreementTypeEnum,Integer> result = getConfiguredVersionFromDB();
                    log.info("versionCacheBuilder result={}",JSONObject.toJSONString(result));
                    return result;
                }
            });


    public Map<AgreementTypeEnum,Integer> getConfiguredVersionFromLocalCache(){
        Map<AgreementTypeEnum, Integer> result = null;
        try {
            result = versionCacheBuilder.get("");
        } catch (Exception e) {
            log.error("getConfiguredVersionFromLocalCache异常",e);
            return getConfiguredVersionFromDB();
        }
        return result;
    }

    public Map<AgreementTypeEnum,Integer> getConfiguredVersionFromDB(){
        Map<AgreementTypeEnum,Integer> result = Maps.newHashMap();
        for(AgreementTypeEnum temp : AgreementTypeEnum.values()){
            Integer currentVersion = wmAgreementModelDicMapper
                    .getCurrentVerisonById(temp.getType());
            if(currentVersion == null){
                continue;
            }
            WmAgreementModel wmAgreementModel = wmAgreementModelDBMapper
                    .selectByAgreementTypeAndVersion(temp.getType(), currentVersion);
            int modleId = 0;
            if(wmAgreementModel == null){
                log.error("无法获取当前配置版本!");
            }
            modleId = wmAgreementModel.getId();
            result.put(temp,modleId);
        }
        return result;
    }

    /**
     * 门店全城协议首次展示时间戳-毫秒
     * @param wmPoiId
     * @return null or "" or 毫秒字符串
     */
    public String getWholeCityFirstReadTime(long wmPoiId){
        return wmPoiPromiseEapiKvService.getWholeCityFirstReadTime(wmPoiId);
    }

    /**
     * 门店混合送协议首次展示时间戳-毫秒
     * @param wmPoiId
     * @return null or "" or 毫秒字符串
     */
    public String getMixDeliveryFirstReadTime(long wmPoiId){
        return wmPoiHybirdEapiKvService.getHybirdFirstReadTime(wmPoiId);
    }

}
