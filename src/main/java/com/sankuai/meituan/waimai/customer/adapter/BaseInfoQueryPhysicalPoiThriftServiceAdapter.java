package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.WmPhysicalPoiRel;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.exception.BaseInfoServerException;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.service.BaseInfoQueryPhysicalPoiThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BaseInfoQueryPhysicalPoiThriftServiceAdapter {
    @Autowired
    private BaseInfoQueryPhysicalPoiThriftService baseInfoQueryPhysicalPoiThriftService;

    /**
     * 获取经营门店集合绑定的物理门店id集合
     *
     * @param wmPoiIds 经营门店集合
     * @return 经营门店与物理门店绑定关系
     * @throws WmCustomerException
     * @throws TException
     */
    public List<WmPhysicalPoiRel> getPhysicalPoiRelList(List<Long> wmPoiIds) throws WmCustomerException {
        log.info("查询经营门店与物理门店绑定关系，wmPoiIds={}", JSON.toJSONString(wmPoiIds));
        List<WmPhysicalPoiRel> physicalPoiRelList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmPoiIds) || (wmPoiIds.size() == 1 && wmPoiIds.get(0) == -1)) {
            return physicalPoiRelList;
        }
        try {
            List<List<Long>> pageList = Lists.partition(wmPoiIds, 100);
            for (List<Long> page : pageList) {
                List<WmPhysicalPoiRel> wmPhysicalPoiRels = baseInfoQueryPhysicalPoiThriftService.getWmPoiPhysicalStoreByWmPoiIds(page);
                if (CollectionUtils.isNotEmpty(wmPhysicalPoiRels)) {
                    physicalPoiRelList.addAll(wmPhysicalPoiRels);
                }
            }
            log.info("getPhysicalPoiRelList, result={}", JSON.toJSONString(physicalPoiRelList));
            if (CollectionUtils.isEmpty(physicalPoiRelList) || physicalPoiRelList.size() != wmPoiIds.size()) {
                log.error("查询经营门店与物理门店绑定关系异常，未查询到物理门店, wmPoiIds={}", JSON.toJSONString(wmPoiIds));
            }
            return physicalPoiRelList;
        } catch (BaseInfoServerException e) {
            log.warn("查询经营门店与物理门店绑定关系业务异常 wmPoiIds={}", JSON.toJSONString(wmPoiIds), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询经营门店与物理门店绑定关系异常:" + e.getMessage());
        } catch (TException e) {
            log.error("查询经营门店与物理门店绑定关系业务异常 wmPoiIds={}", JSON.toJSONString(wmPoiIds), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询经营门店与物理门店绑定关系异常");
        }
    }
}
