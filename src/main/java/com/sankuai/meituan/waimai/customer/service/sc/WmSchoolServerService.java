package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.meituan.sunflower.client.campus.thrift.service.CampusAndHcArchivesThriftService;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolExtensionMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryMetricMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryMetric;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.WmScSchoolSensitiveWordsService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.sankuai.meituan.waimai.customer.util.ScBuildUtil.setBeanDesc;

/**
 * @program: scm
 * @description: 单独Service层
 * @author: jianghuimin02
 * @create: 2020-05-14 14:47
 **/
@Slf4j
@Service
public class WmSchoolServerService {


    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmScSchoolExtensionMapper wmScSchoolExtensionMapper;

    @Autowired
    private WmScSchoolSensitiveWordsService wmScSchoolSensitiveWordsService;

    @Autowired
    private CampusAndHcArchivesThriftService.Iface campusAndHcArchivesThriftService;

    @Autowired
    private WmSchoolDeliveryMetricMapper wmSchoolDeliveryMetricMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    /**
     * 获取单个学校信息
     */
    public SchoolBo getSchoolBySchoolId(int schoolId) throws TException, WmSchCantException {
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        SchoolBo schoolBo = new SchoolBo();
        if (wmSchoolDB == null) {
            return schoolBo;
        }
        BeanUtils.copyProperties(wmSchoolDB, schoolBo);
        setBeanDesc(schoolBo);
        return schoolBo;
    }

    /**
     * 获取单个学校信息
     * @param id 学校主键ID
     * @return SchoolBo
     */
    public SchoolBo selectSchoolById(int id) {
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(id);
        wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDB);
        SchoolBo schoolBo = new SchoolBo();
        if (wmSchoolDB == null) {
            return schoolBo;
        }
        BeanUtils.copyProperties(wmSchoolDB, schoolBo);
        // 根据学校扩展属性表设置相关字段
        setSchoolBoBySchoolExtension(schoolBo);
        // 设置枚举值描述
        setBeanDesc(schoolBo);
        try {
            int schoolAmbassadorNum = campusAndHcArchivesThriftService.getActiveCampusAmountByAorId(schoolBo.getAorId());
            schoolBo.setSchoolAmbassadorNum(schoolAmbassadorNum);
        } catch (TException e) {
            log.error("向日葵系统异常：id={}, aorId={}", id, schoolBo.getAorId(), e);
        }
        return schoolBo;
    }

    public void setSchoolOtherInfo(SchoolBo schoolBo) {
        log.info("[WmSchoolServerService.setSchoolOtherInfo] input param: schoolBo = {}", JSONObject.toJSONString(schoolBo));
        List<WmSchoolDeliveryMetric> wmSchoolDeliveryMetrics = wmSchoolDeliveryMetricMapper.selectBySchoolId((long) schoolBo.getSchoolId());
        if (CollectionUtils.isNotEmpty(wmSchoolDeliveryMetrics)) {
            WmSchoolDeliveryMetric metric = wmSchoolDeliveryMetrics.get(0);
            schoolBo.setPeakStallNumOfMonth(metric.getPeakStallNumOfMonth());
            schoolBo.setPeakOrderNumOfMonth(metric.getPeakOrderNumOfMonth());
            schoolBo.setOnlineStallNum(metric.getPoiNumOpen());
        }
        //档口数
        SchoolStallNumDO schoolStallNumDO = getSchoolStallNum(schoolBo.getId());
        schoolBo.setSchoolStallNum(schoolStallNumDO.getSchoolStallNum());
        schoolBo.setCanyinSchoolStallNum(schoolStallNumDO.getCanyinSchoolStallNum());
        schoolBo.setShangouSchoolStallNum(schoolStallNumDO.getShangouSchoolStallNum());
        schoolBo.setBianlidianSchoolStallNum(schoolStallNumDO.getBianlidianSchoolStallNum());
        schoolBo.setSchoolOfflineBizStallNum(schoolStallNumDO.getSchoolOfflineBizStallNum());
        schoolBo.setCanyinOfflineBizStallNum(schoolStallNumDO.getCanyinOfflineBizStallNum());
        schoolBo.setShangouOfflineBizStallNum(schoolStallNumDO.getShangouOfflineStallNum());
        schoolBo.setBianlidianOfflineBizStallNum(schoolStallNumDO.getBianlidianOfflineStallNum());

        schoolBo.setCanyinCoStallNum(schoolStallNumDO.getCanyinCoStoreNum());
        schoolBo.setShangouCoStallNum(schoolStallNumDO.getShangouCoStoreNum());
        schoolBo.setBianlidianCoStallNum(schoolStallNumDO.getBianlidianCoStoreNum());

        schoolBo.setCanyinPreStallNum(schoolStallNumDO.getCanyinPreOnlineStallNum());
        schoolBo.setShangouPreStallNum(schoolStallNumDO.getShangouPreOnlineStallNum());
        schoolBo.setBianlidianPreStallNum(schoolStallNumDO.getBianlidianPreOnlineStallNum());

        log.info("[WmSchoolServerService.setSchoolOtherInfo] output: schoolBo = {}", JSONObject.toJSONString(schoolBo));
    }

    /**
     * 获取学校线下营业档口数量、可上线档口数量（区分食堂维度）
     */
    public SchoolStallNumDO getSchoolStallNum(Integer schoolId) {
        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setSchoolId(schoolId);
        wmCanteenDB.setValid(1);
        List<WmCanteenDB> wmCanteenDBS = wmCanteenMapper.selectCanteenList(wmCanteenDB);

        int schoolOfflineBizStallNum = 0;
        int schoolPreOnlineStallNum = 0;
        int canyinOfflineBizStallNum = 0;
        int canyinPreOnlineStallNum = 0;
        int shangouOfflineStallNum = 0;
        int shangouPreOnlineStallNum = 0;
        int bianlidianOfflineStallNum = 0;
        int bianlidianPreOnlineStallNum = 0;

        int schoolStallNum = 0;
        int canyinSchoolStallNum = 0;
        int shangouSchoolStallNum = 0;
        int bianlidianSchoolStallNum = 0;

        int schoolCoStoreNum = 0;
        int canyinCoStoreNum = 0;
        int shangouCoStoreNum = 0;
        int bianlidianCoStoreNum = 0;

        if (CollectionUtils.isNotEmpty(wmCanteenDBS)) {
            for (WmCanteenDB canteenDB : wmCanteenDBS) {
                if (!Objects.equals(canteenDB.getEffective(), 1)) {
                    continue;
                }
                if (canteenDB.getOfflineBizStallNum() != -1) {
                    schoolOfflineBizStallNum += canteenDB.getOfflineBizStallNum();
                    if (Objects.equals(canteenDB.getCategory(), 1)) {
                        canyinOfflineBizStallNum += canteenDB.getOfflineBizStallNum();
                    }
                    if (Objects.equals(canteenDB.getCategory(), 2)) {
                        shangouOfflineStallNum += canteenDB.getOfflineBizStallNum();
                    }
                    if (Objects.equals(canteenDB.getCategory(), 3)) {
                        bianlidianOfflineStallNum += canteenDB.getOfflineBizStallNum();
                    }
                }
                if (canteenDB.getPreOnlineStallNum() != -1) {
                    schoolPreOnlineStallNum += canteenDB.getPreOnlineStallNum();
                    if (Objects.equals(canteenDB.getCategory(), 1)) {
                        canyinPreOnlineStallNum += canteenDB.getPreOnlineStallNum();
                    }
                    if (Objects.equals(canteenDB.getCategory(), 2)) {
                        shangouPreOnlineStallNum += canteenDB.getPreOnlineStallNum();
                    }
                    if (Objects.equals(canteenDB.getCategory(), 3)) {
                        bianlidianPreOnlineStallNum += canteenDB.getPreOnlineStallNum();
                    }
                }
                //档口数
                if (canteenDB.getStallNum() >= 0) {
                    schoolStallNum += canteenDB.getStallNum();
                    if (Objects.equals(canteenDB.getCategory(), 1)) {
                        canyinSchoolStallNum += canteenDB.getStallNum();
                    }
                    if (Objects.equals(canteenDB.getCategory(), 2)) {
                        shangouSchoolStallNum += canteenDB.getStallNum();
                    }
                    if (Objects.equals(canteenDB.getCategory(), 3)) {
                        bianlidianSchoolStallNum += canteenDB.getStallNum();
                    }
                }

                //单独计算合作档口数
                int canteenCoStallNum = 0;
                try {
                    List<Long> poiIds = wmScCanteenPoiAttributeService.getNonSubWmPoiIdListByCanteenPrimaryId(canteenDB.getId());
                    canteenCoStallNum = poiIds.size();
                } catch (Exception e) {
                    log.error("查询食堂id:{}合作档口数失败", canteenDB.getId());
                }
                schoolCoStoreNum += canteenCoStallNum;
                if (Objects.equals(canteenDB.getCategory(), 1)) {
                    canyinCoStoreNum += canteenCoStallNum;
                }
                if (Objects.equals(canteenDB.getCategory(), 2)) {
                    shangouCoStoreNum += canteenCoStallNum;
                }
                if (Objects.equals(canteenDB.getCategory(), 3)) {
                    bianlidianCoStoreNum += canteenCoStallNum;
                }
            }
        }

        SchoolStallNumDO schoolStallNumDO = new SchoolStallNumDO();
        schoolStallNumDO.setSchoolOfflineBizStallNum(schoolOfflineBizStallNum);
        schoolStallNumDO.setSchoolPreOnlineStallNum(schoolPreOnlineStallNum);
        schoolStallNumDO.setCanyinOfflineBizStallNum(canyinOfflineBizStallNum);
        schoolStallNumDO.setCanyinPreOnlineStallNum(canyinPreOnlineStallNum);
        schoolStallNumDO.setShangouOfflineStallNum(shangouOfflineStallNum);
        schoolStallNumDO.setShangouPreOnlineStallNum(shangouPreOnlineStallNum);
        schoolStallNumDO.setBianlidianOfflineStallNum(bianlidianOfflineStallNum);
        schoolStallNumDO.setBianlidianPreOnlineStallNum(bianlidianPreOnlineStallNum);

        schoolStallNumDO.setSchoolStallNum(schoolStallNum);
        schoolStallNumDO.setCanyinSchoolStallNum(canyinSchoolStallNum);
        schoolStallNumDO.setShangouSchoolStallNum(shangouSchoolStallNum);
        schoolStallNumDO.setBianlidianSchoolStallNum(canyinSchoolStallNum);

        schoolStallNumDO.setSchoolCoStoreNum(schoolCoStoreNum);
        schoolStallNumDO.setCanyinCoStoreNum(canyinCoStoreNum);
        schoolStallNumDO.setShangouCoStoreNum(shangouCoStoreNum);
        schoolStallNumDO.setBianlidianCoStoreNum(bianlidianCoStoreNum);

        return schoolStallNumDO;
    }

    /**
     * 根据学校扩展属性表设置相关字段
     * @param schoolBo schoolBo
     */
    public void setSchoolBoBySchoolExtension(SchoolBo schoolBo) {
        log.info("[WmSchoolServerService.setSchoolBoBySchoolExtension] input param: schoolBo = {}", JSONObject.toJSONString(schoolBo));
        WmScSchoolExtensionDO wmScSchoolExtensionDO = wmScSchoolExtensionMapper.selectBySchoolId(schoolBo.getSchoolId());
        if (wmScSchoolExtensionDO == null) {
            schoolBo.setResidentUserNum(0);
            schoolBo.setResidentStudentUserNum(0);
            schoolBo.setOutsideStudentUserNum(0);
            return;
        }
        log.info("[setSchoolBoBySchoolExtension] wmScSchoolExtensionDO = {}", JSONObject.toJSONString(wmScSchoolExtensionDO));
        schoolBo.setResidentUserNum(wmScSchoolExtensionDO.getResidentUserNum() == null ? 0 : wmScSchoolExtensionDO.getResidentUserNum());
        schoolBo.setResidentStudentUserNum(wmScSchoolExtensionDO.getResidentStudentUserNum() == null ? 0 : wmScSchoolExtensionDO.getResidentStudentUserNum());
        schoolBo.setOutsideStudentUserNum(wmScSchoolExtensionDO.getOutsideStudentUserNum() == null ? 0 : wmScSchoolExtensionDO.getOutsideStudentUserNum());
        log.info("[WmSchoolServerService.setSchoolBoBySchoolExtension] output: schoolBo = {}", JSONObject.toJSONString(schoolBo));
    }


    /**
     * 更新食堂数量 + 1
     * @param schoolPrimaryId 学校主键ID
     */
    public void addCanteeNumBySchoolId(int schoolPrimaryId) {
        log.info("[WmSchoolServerService.addCanteeNumBySchoolId] schoolPrimaryId = {}", schoolPrimaryId);
        wmSchoolMapper.addCanteeNumById(schoolPrimaryId);
    }

    /**
     * 更新食堂数量 - 1
     * @param id 学校ID（wm_sc_school的主键ID）
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void subCanteeNumBySchoolId(int id) throws WmSchCantException {
        WmSchoolDB wmSchoolDb = wmSchoolMapper.selectSchoolById(id);
        if (wmSchoolDb != null && wmSchoolDb.getCanteenNum() > 0) {
            wmSchoolMapper.subCanteeNumById(id);
            log.info("校园食堂项目:食堂绑定学校减少学校食堂的数量:id:{}", id);
        } else {
            log.error("[subCanteeNumBySchoolId] 当前学校食堂数量<=0无法减少, id = {}, wmSchoolDB = {}", id, JSONObject.toJSONString(wmSchoolDb));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "删除食堂失败-关联学校食堂数量为0");
        }
    }

    /**
     * 逻辑删除学校
     * @param id 学校的自增id（主键）
     */
    public void deleteSchoolById(int id) {
        wmSchoolMapper.deleteSchoolById(id);
        log.info("校园食堂项目:删除学校成功:id:{}",id);
    }

    /**
     * 根据合同编号查询学校信息
     * @param contractNum
     * @return
     */
    public WmSchoolDB getByContractNum(String contractNum) {
        if (StringUtil.isBlank(contractNum)) {
            return null;
        }
        WmSchoolDB condition = new WmSchoolDB();
        condition.setContractNum(contractNum);
        List<WmSchoolDB> list = wmSchoolMapper.selectSchoolList(condition);
        wmScSchoolSensitiveWordsService.readWhenSelect(list);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 根据线索ID查询学校信息
     *
     * @param wdcClueId
     * @return
     */
    public WmSchoolDB getByWdcClueId(Long wdcClueId) {
        if (wdcClueId == null || wdcClueId <= 0) {
            return null;
        }
        WmSchoolDB condition = new WmSchoolDB();
        condition.setWdcClueId(wdcClueId);
        List<WmSchoolDB> list = wmSchoolMapper.selectSchoolList(condition);
        wmScSchoolSensitiveWordsService.readWhenSelect(list);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }


    /**
     * DAO层条件查询的统计封装（目前调用的地方不使用学校上的敏感词信息，如果使用敏感词信息需要加入敏感词的处理逻辑）
     *
     * @param condition
     * @return
     */
    public List<WmSchoolDB> selectByCondition(WmSchoolSearchCondition condition) {
        return wmSchoolMapper.selectByCondition(condition);
    }

    /**
     * 根据ID查询学校信息
     *
     * @param id
     * @return
     */
    public WmSchoolDB selectById(Integer id) {
        if (id == null || id <= 0) {
            return null;
        }
        WmSchoolSearchCondition condition = new WmSchoolSearchCondition();
        condition.setId(id);
        List<WmSchoolDB> list = wmSchoolMapper.selectByCondition(condition);
        wmScSchoolSensitiveWordsService.readWhenSelect(list);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }
}
