package com.sankuai.meituan.waimai.customer.domain.sc.delivery;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryStreamNodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学校交付流程主表DO
 * <AUTHOR>
 * @date 2024/02/15
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmSchoolDeliveryStreamDO {
    /**
     * 主键ID(即交付编号ID)
     */
    private Integer id;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 当前交付流节点
     * {@link SchoolDeliveryStreamNodeEnum}
     */
    private Integer streamNode;
    /**
     * 是否交付终结 0-否 1-是
     */
    private Integer deliveryEnd;
    /**
     * 是否有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;

}
