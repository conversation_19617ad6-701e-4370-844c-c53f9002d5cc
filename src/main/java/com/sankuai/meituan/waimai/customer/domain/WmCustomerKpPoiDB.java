package com.sankuai.meituan.waimai.customer.domain;

import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpPoi;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class WmCustomerKpPoiDB {

    private Long id;
    private Integer kpId;
    private Long wmPoiId;
    private Integer ctime;
    private Integer utime;
    private Integer valid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getKpId() {
        return kpId;
    }

    public void setKpId(Integer kpId) {
        this.kpId = kpId;
    }

    public Long getWmPoiId() {
        return wmPoiId;
    }

    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public static List<WmCustomerKpPoi> trans(List<WmCustomerKpPoiDB> wmCustomerKpPoiDBList) {
        List<WmCustomerKpPoi> wmCustomerKpPoiList = new ArrayList<>();
        for (WmCustomerKpPoiDB wmCustomerKpPoiDB : wmCustomerKpPoiDBList) {
            WmCustomerKpPoi wmCustomerKpPoi = new WmCustomerKpPoi();
            BeanUtils.copyProperties(wmCustomerKpPoiDB, wmCustomerKpPoi);
            wmCustomerKpPoiList.add(wmCustomerKpPoi);
        }
        return wmCustomerKpPoiList;
    }
}
