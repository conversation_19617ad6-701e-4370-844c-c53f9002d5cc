package com.sankuai.meituan.waimai.customer.service.sign.gray;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmLabelAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant.OrgType;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.mtauth.manager.WmAdminDataAuthManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.annotation.Nullable;

import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
public class WmEcontractSignGrayService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractSignGrayService.class);

    @Autowired
    private WmAdminDataAuthManager wmAdminDataAuthManager;
    @Autowired
    private WmEmployeeService wmEmployeeService;
    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmLabelAdaptor wmLabelAdaptor;

    public boolean canUseManualPack(int uid) {
        String misId = wmEmployeeService.getMisId(uid);
        if (StringUtils.isEmpty(misId)) {
            return false;
        }
        //1.超级管理员开关
        String superManager = ConfigUtilAdapter
                .getString("canUseManualPack_super_manager_misId", "");
        if (judgeSuperManager(superManager, misId)) {
            return true;
        }
        //2.正常业务总开关
        boolean businessOpenSwitch = ConfigUtilAdapter.getBoolean("canUseManualPack_businessOpenSwitch_open", false);
        if (businessOpenSwitch) {
            return true;
        }

        //3.总部权限开关
        boolean hqOpenSwitch = ConfigUtilAdapter.getBoolean("canUseManualPack_hq_open", false);
        boolean isHq = wmAdminDataAuthManager.isHQ(uid);
        if (hqOpenSwitch && isHq) {
            return true;
        }

        //4.城市权限开关-和总部权限互斥
        if (isHq) {
            return false;
        }

        //5.组织结构权限判断
        String canUseManualPackOrgId = ConfigUtilAdapter.getString("canUseManualPack_OrgId");
        if (StringUtils.isEmpty(canUseManualPackOrgId)) {
            return false;
        }


        List<Integer> orgIds = wmAdminDataAuthManager.getOrgIds(uid, OrgType.WM_ORG_CITY);//总部权限会查出所有的外卖城市
        LOGGER.info("#canUseManualPack,uid={},orgIds={}", uid, orgIds);
        if (CollectionUtils.isEmpty(orgIds)) {
            return false;
        }

        String[] orgString = canUseManualPackOrgId.split(",|，");
        List<Integer> orgList = Lists.newArrayList(
                Lists.transform(Lists.newArrayList(orgString), new Function<String, Integer>() {
                    @Nullable
                    @Override
                    public Integer apply(@Nullable String input) {
                        return Integer.valueOf(input);
                    }
                }));

        return CollectionUtils.containsAny(orgList, orgIds);
    }

    private boolean judgeSuperManager(String superManager, String misId) {
        return superManager.contains(misId);
    }

    public boolean poifeeTempletTransferNewPlatform(Integer customerId) throws TException, WmCustomerException {
        //1、总开关 线上配置的true
        if(!MccConfig.poifeeTempletTransferMasterSwitch()) {
            return false;
        }
        //2、老平台白名单客户，直接返回false 线上没有配置  线下配置的是1089
        WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerById(customerId);
        int whiteLabelList = MccConfig.poifeeTempletTransferWhiteLableid();
        boolean hasTargetLabel = wmLabelAdaptor.hasTargetLable(Lists.newArrayList(wmCustomerBasicBo.getMtCustomerId()), LabelSubjectTypeEnum.CUSTOMER.getCode(), whiteLabelList);
        if (hasTargetLabel) {
            return false;
        }
        //3、指定客户 线上配置18180883
        String poifeeTempletTransferGrayCustomerIds = MccConfig.poifeeTempletTransferGrayCustomerIds();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(poifeeTempletTransferGrayCustomerIds)) {
            String[] customerIdString = poifeeTempletTransferGrayCustomerIds.split(",|，");
            for (String temp : customerIdString) {
                if (temp.equals(customerId.toString())) {
                    return true;
                }
            }
        }
        //4、客户比例 线上配置10000
        return customerId % 10000 < MccConfig.poifeeTempletTransferGrayCustomerProp();
    }

}
