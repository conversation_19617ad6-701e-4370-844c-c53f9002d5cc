package com.sankuai.meituan.waimai.customer.statemachine.core.flowpath;


/**
 * Created by jinh<PERSON> on 16/8/11.
 */
public class FlowConnectionDefinition {

    private FlowStateDefinition fromStateDefinition;
    private FlowStateDefinition toStateDefinition;

    public FlowStateDefinition getFromStateDefinition() {
        return fromStateDefinition;
    }

    public void setFromStateDefinition(FlowStateDefinition fromStateDefinition) {
        this.fromStateDefinition = fromStateDefinition;
    }

    public FlowStateDefinition getToStateDefinition() {
        return toStateDefinition;
    }

    public void setToStateDefinition(FlowStateDefinition toStateDefinition) {
        this.toStateDefinition = toStateDefinition;
    }
}
