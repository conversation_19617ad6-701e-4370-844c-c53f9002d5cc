package com.sankuai.meituan.waimai.customer.service.sc.status;

import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenDetailStatusEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 食堂审核状态机
 * <AUTHOR>
 * @date 2020/11/24 15:46
 */
@Component
public class CanteenStatusMachineFactory {

    @Resource(name="defaultStatusSubMachine")
    private CanteenStatusSubMachine defaultStatusSubMachine;

    @Resource(name="insertAuditingStatusSubMachine")
    private CanteenStatusSubMachine insertAuditingStatusSubMachine;

    @Resource(name="auditedStatusSubMachine")
    private CanteenStatusSubMachine auditedStatusSubMachine;

    @Resource(name="insertAuditRejectStatusSubMachine")
    private CanteenStatusSubMachine insertAuditRejectStatusSubMachine;

    @Resource(name="updateAuditingStatusSubMachine")
    private CanteenStatusSubMachine updateAuditedStatusSubMachine;

    @Resource(name="updateAuditRejectStatusSubMachine")
    private CanteenStatusSubMachine updateAuditRejectStatusSubMachine;

    @Resource(name="insertStatusSubMachine")
    private CanteenStatusSubMachine insertStatusMachine;

    /**
     * 获取食堂审核状态机
     * @param canteenDB canteenDB
     * @return CanteenStatusSubMachine
     */
    public CanteenStatusSubMachine getCanteenStatusSubMachine(WmCanteenDB canteenDB) {
        switch (CanteenDetailStatusEnum.getByType(canteenDB.getAuditDetailStatus())) {
            case INSERT_STATUS:
                return insertStatusMachine;
            case INSERT_AUDITING_STATUS:
                return insertAuditingStatusSubMachine;
            case AUDITED_STATUS:
                return auditedStatusSubMachine;
            case INSERT_AUDIT_REJECT_STATUS:
                return insertAuditRejectStatusSubMachine;
            case UPDATE_AUDITING_STATUS:
                return updateAuditedStatusSubMachine;
            case UPDATE_AUDIT_REJECT_STATUS:
                return updateAuditRejectStatusSubMachine;
            default:
                return defaultStatusSubMachine;
        }
    }


}
