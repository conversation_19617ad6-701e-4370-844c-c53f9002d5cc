package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil.SUPPORT_MARK;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SHANGOUV2_2NEC_FEEMODE)
public class PerformanceServiceShanGouNewQikeSplit implements DeliveryPdfSplit {

    /**
     * 判断对应deliveryPdfDataType类型的合同，是否需要填充deliveryInfoBo对应的信息
     *
     * @param deliveryInfoBo
     * @param middleContext
     */
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));

        // 判断是不是闪购新企客支持的门店
        if (feeMode == LogisticsFeeModeEnum.SHANGOU_QIKE_V2
                && SUPPORT_MARK.equals(deliveryInfoBo.getSupportSGV2_2DeliveryXQK())) {
            List<String> technicalShanGouNewQikeList = pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SHANGOUV2_2NEC_FEEMODE.getName());
            if (CollectionUtils.isEmpty(technicalShanGouNewQikeList)) {
                // 未加入任何门店，则初始化为空数组
                technicalShanGouNewQikeList = Lists.newArrayList();
                technicalShanGouNewQikeList.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                // 加入当前门店
                technicalShanGouNewQikeList.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SHANGOUV2_2NEC_FEEMODE.getName(), technicalShanGouNewQikeList);
            log.info("ADD TO PERFORMANCE_SERVICE_SHANGOUV2_2NEC_FEEMODE，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}
