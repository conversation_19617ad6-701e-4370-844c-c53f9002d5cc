package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class ContractCheckFilter {

    private static Logger logger = LoggerFactory.getLogger(ContractCheckFilter.class);

    private List<IContractValidator> validators = Lists.newArrayList();

    public boolean filter(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {
        for (IContractValidator validator : validators) {
            try {
                logger.info("ContractCheckFilter#filter, validator: {}", validator.getClass().getSimpleName());
                validator.valid(contractBo, opUid, opName);
            } catch (WmCustomerException e) {
                logger.info(e.getMsg(), e);
                throw e;
            }
        }
        return true;
    }

    private void registry(IContractValidator validator) {
        validators.add(validator);
    }

    private static ContractCheckFilter contractSaveFilter = new ContractCheckFilter();

    private static ContractCheckFilter contractUpdateFilter = new ContractCheckFilter();

    private static ContractCheckFilter deliveryContractSaveFilter = new ContractCheckFilter();
    private static ContractCheckFilter deliveryContractUpdateFilter = new ContractCheckFilter();

    private static ContractCheckFilter contractSignFilter = new ContractCheckFilter();

    private static ContractCheckFilter contractCommitAuditFilter = new ContractCheckFilter();

    static {
        /**  ---------------------  保存校验  -------------------- **/
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("invalidStatusAtomValidator"));
//        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("changingAgentIdAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("duplicateContractAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("paperContractScanAtomValidator"));
//        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("customerEffectiveAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("customerPaperSignModeAtomValidator"));
//        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("onlyOneC2ContractAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("contractDuplicateNumberAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("signerNotNullAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("c2EDependenceC3CAAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("dueDateAndSignTimeAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("c1PaperSignerPhoneAtomValidator"));
        contractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("packWayAtomValidator"));

        deliveryContractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("customerSignModeAtomValidator"));
        deliveryContractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("contractScanNumAtomValidator"));
        deliveryContractSaveFilter.registry((IContractValidator) SpringBeanUtil.getBean("duplicateDeliveryContractAtomValidator"));

        /**  ---------------------  修改校验  -------------------- **/
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("invalidStatusAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("changingAgentIdAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("contractDuplicateNumberAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("signerNotNullAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("c2EDependenceC3CAAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("paperContractScanAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("dueDateAndSignTimeAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("c1PaperSignerPhoneAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("contractTypeAtomValidator"));
//        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("onlyOneC2ContractAtomValidator"));
        contractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("packWayAtomValidator"));


        deliveryContractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("customerSignModeAtomValidator"));
        deliveryContractUpdateFilter.registry((IContractValidator) SpringBeanUtil.getBean("contractScanNumAtomValidator"));


        /**  ---------------------  签约校验  -------------------- **/
        contractSignFilter.registry((IContractValidator) SpringBeanUtil.getBean("startSignStatusAtomValidator"));


        /**  ---------------------  提审校验  -------------------- **/
        contractCommitAuditFilter.registry((IContractValidator) SpringBeanUtil.getBean("commitAuditStatusAtomValidator"));

    }

    public static ContractCheckFilter contractSaveValidFilter(WmTempletContractTypeEnum typeEnum) {
        if (typeEnum == WmTempletContractTypeEnum.DELIVERY_PAPER
                || typeEnum == WmTempletContractTypeEnum.EXCLUSIVE_AGREEMENT_PAPER) {
            return deliveryContractSaveFilter;
        }
        return contractSaveFilter;
    }

    public static ContractCheckFilter contractUpdateValidFilter(WmTempletContractTypeEnum typeEnum) {
        if (typeEnum == WmTempletContractTypeEnum.DELIVERY_PAPER
                || typeEnum == WmTempletContractTypeEnum.EXCLUSIVE_AGREEMENT_PAPER) {
            return deliveryContractUpdateFilter;
        }
        return contractUpdateFilter;
    }

    public static ContractCheckFilter contractSignValidFilter() {
        return contractSignFilter;
    }

    public static ContractCheckFilter contractCommitAuditValidFilter() {
        return contractCommitAuditFilter;
    }

}
