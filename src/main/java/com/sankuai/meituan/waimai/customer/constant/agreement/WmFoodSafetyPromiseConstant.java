package com.sankuai.meituan.waimai.customer.constant.agreement;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.AgreementConstants;
import java.util.Map;

/**
 * <AUTHOR>
 * 商家端协议存储结构常量
 */
public class WmFoodSafetyPromiseConstant {
    /**
     * 同意
     */
    public static final int STATUS_AGREE = 0;
    /**
     * 拒绝
     */
    public static final int STATUS_DENY = 1;

    public static final String SOURCE_SUPPLY_CHAIN = "fromCRM";

    public static final Map<Integer,Integer> MAP_TO_NEW_STATUS = Maps.newHashMap();

    static {
        MAP_TO_NEW_STATUS.put(STATUS_AGREE, AgreementConstants.SUCCESS);
        MAP_TO_NEW_STATUS.put(STATUS_DENY, AgreementConstants.CANCEL);
    }

}
