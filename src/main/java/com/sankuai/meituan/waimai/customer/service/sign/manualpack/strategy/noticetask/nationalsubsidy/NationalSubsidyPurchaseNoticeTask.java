package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.nationalsubsidy;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.NoticeTask;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create:  2025/5/24 18:17
 */
@Slf4j
@Service
public class NationalSubsidyPurchaseNoticeTask implements NoticeTask {

    @Resource
    private WmContractService wmContractService;

    @Override
    public void notice(String module, List<Long> bizIdList, ManualPackNoticeContext context, List<Long> taskIds) throws WmCustomerException, TException {
        log.info("notice#module:{}, taskInfo:{}", module, JSON.toJSONString(context.getAllTaskInfo()));
        for (Long bizId : bizIdList) {
            wmContractService.startSignByWaitingSign(context.getCustomerId(), bizId, context.getCommitUid(), "", context.getManualBatchId());
        }
    }
}
