package com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event;

/**
 * <AUTHOR>
 * @date 20230821
 * @desc KP签约人事件枚举定义
 */
public enum KpLegalEventEnum {

    INIT_CREATE(1, "初始化录入"),
    REAL_NAME_AUTH_FAIL(2, "实名认证失败"),
    EFFECT_KP_REAL_NAME_AUTH_FAIL(3, "已生效KP实名认证失败"),
    REAL_NAME_SUC_2_EFFECTIVE(4, "实名认证通过直接生效"),
    EFFECT_KP_REAL_NAME_SUC_2_EFFECTIVE(5, "已生效KP实名认证通过直接生效");

    private Integer code;
    private String name;

    KpLegalEventEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static KpLegalEventEnum of(int code) {
        for (KpLegalEventEnum kpSignerEventEnum : KpLegalEventEnum.values()) {
            if (kpSignerEventEnum.getCode() == code) {
                return kpSignerEventEnum;
            }
        }
        return null;
    }


}
