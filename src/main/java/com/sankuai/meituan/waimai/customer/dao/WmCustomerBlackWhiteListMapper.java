package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.CustomerBlackWhiteListDBParam;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerBlackWhiteList;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmCustomerBlackWhiteListMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WmCustomerBlackWhiteList record);

    int insertSelective(WmCustomerBlackWhiteList record);

    WmCustomerBlackWhiteList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmCustomerBlackWhiteList record);

    int updateByPrimaryKey(WmCustomerBlackWhiteList record);

    int deleteByParam(CustomerBlackWhiteListDBParam param);

    List<WmCustomerBlackWhiteList> selectByParam(CustomerBlackWhiteListDBParam param);

    int selectCountByParam(CustomerBlackWhiteListDBParam param);

    List<WmCustomerBlackWhiteList> queryCustomerBlackWhiteListDB(@Param("bizType") Integer bizType, @Param("type")
            Integer type);



}