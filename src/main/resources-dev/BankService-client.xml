<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>


    <!--支付服务客户端配置-->
    <bean id="clientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <!-- 测试获取所有支行时打开-->
        <property name="timeout" value="10000"/>
        <property name="maxResponseMessageBytes" value="********"/>
        <property name="serviceInterface" value="com.meituan.payment.bankinfo.thrift.idl.BankInfoService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.pay.fundstransfer.bankservice"/>  <!-- 目标 Server Appkey  -->
        <property name="remoteServerPort" value="9001"/>
    </bean>

    <!--horus.xml-->
    <bean id="ocrServicesBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.horus.service.OCRServices"/> <!-- 接口名 -->
        <property name="remoteServerPort" value="9001"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus "/>  <!-- 目标Server Appkey  -->
        <!--<property name="async" value="true"/>-->
        <property name="async" value="false"/>
        <property name="timeout" value="20000"/>
    </bean>

    <bean id="bankCardBinOuterThriftClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="timeout" value="500"/>
        <!-- thrift rpc 超时时间（毫秒） -->
        <property name="retryRequest" value="true"/>
        <!-- 集群工作模式, ZK/MIX/OCTO/DIRECT, 默认为 ZK, 和之前版本一致 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <!-- 本地 appkey, MIX/OCTO 模式下必须配置 -->
        <property name="remoteAppkey" value="com.sankuai.pay.mcc"/>
        <property name="remoteServerPort" value="4007" />
        <property name="serviceInterface" value="com.meituan.pay.mcc.sdk.bankCardBin.BankCardBinOuterThriftService"/>
    </bean>

</beans>