/*
package com.sankuai.meituan.waimai.service.customer;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;
import com.sankuai.meituan.waimai.infra.service.WmVirtualOrgService;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

*/
/**
 * 远程调用接口
 *
 * <AUTHOR>
 * @date 20200814
 *//*

public class TestWmCustomerThriftRemoveService extends BaseTest {

    private static WmCustomerThriftService wmCustomerThriftService;

    private static WmVirtualOrgService.Iface wmVirtualOrgService;

    @Before
    public void before() {
        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
        this.setRemoteServerPort(8433);
        this.setEnv(ENV_TEST);
        this.setRunMode(RUN_MODE.REMOTE);
        try {
            wmCustomerThriftService = (WmCustomerThriftService) getObject(WmCustomerThriftService.class);
            wmVirtualOrgService = (WmVirtualOrgService.Iface) getObject(WmVirtualOrgService.Iface.class);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testWashCustomerRealType() throws TException, WmCustomerException {
//        setSwimlane("", "9776-fpeej");
        List<Integer> customerIdList = Lists.newArrayList(11806544);
//        List<Integer> customerIdList =Lists.newArrayList();
        int washNum = 0;
        String misId = "xuezhangang";
        wmCustomerThriftService.washCustomerRealType(customerIdList, washNum, misId);
        System.out.println("testWashCustomerRealType = true");
    }

    @Test
    public void testOrgService() throws TException, WmCustomerException {
        int uid = 6324119;

        try {
            List<WmVirtualOrg> org = wmVirtualOrgService.getMajorOwnerOrgsByUid(uid, WmVirtualOrgSourceEnum.WAIMAI.getSource());
            System.out.println(org);

        } catch (WmServerException e) {
            e.printStackTrace();
        }

    }


}
*/
