//package com.sankuai.meituan.waimai.service.settle;
//
//import com.google.common.collect.Lists;
//import com.sankuai.meituan.waimai.BaseSpringTransactionJunit;
//import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
//import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
//import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractSyncService;
//import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
//import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
//import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
//import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
//import com.sankuai.meituan.waimai.thrift.constant.WmContractConstant;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
//import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
//import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleModifyBo;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.annotation.Rollback;
//
//import java.util.UUID;
//
//public class TestSettlePackWayService extends BaseSpringTransactionJunit {
//
//    @Autowired
//    WmContractSyncService wmContractSyncService;
//
//    @Autowired
//    WmContractService wmContractService;
//
//    @Autowired
//    WmTempletContractDBMapper wmTempletContractDBMapper;
//
//    @Autowired
//    private WmCustomerDBMapper wmCustomerDBMapper;
//
//    @Autowired
//    WmCustomerContractThriftService wmCustomerContractThriftService;
//
//    @Autowired
//    WmSettleService wmSettleService;
//
//    @Autowired
//    WmSettleManagerService wmSettleManagerService;
//
//    private int insertCus() {
//        WmCustomerDB customerDB = new WmCustomerDB();
//        customerDB.setAddress("");
//        customerDB.setAuditStatus(1);
//        customerDB.setValid(1);
//        customerDB.setValidateDate(1111l);
//        customerDB.setLegalPerson("qiurunjing");
//        customerDB.setEffective(1);
//        customerDB.setCustomerNumber(UUID.randomUUID().toString());
//        customerDB.setCustomerType(1);
//        customerDB.setCustomerSecondType(2);
//        customerDB.setCustomerName("sss");
//        customerDB.setPicUrl("sssssfff");
//        customerDB.setOwnerUid(444);
//        customerDB.setSignMode(CustomerSignMode.ELECTTRONIC.getCode());
//        wmCustomerDBMapper.insertCustomer(customerDB);
//        return customerDB.getId();
//    }
//
//    @Test
//    @Rollback(false)
//    public void startNoneSign() throws Exception {
//        int cusId = insertCus();
//        WmSettle settle = new WmSettle();
//        settle.setWm_contract_id(cusId);
//        settle.setWmCustomerId(cusId);
//        settle.setSettle_type(WmContractConstant.SETTLETYPE_CIRCLE);
//        settle.setName(UUID.randomUUID().toString());
//        settle.setParty_a_finance_people("财务负责人");
//        settle.setParty_a_finance_phone("13131234321");
//        settle.setAcc_cardno("13131234321");
//        settle.setPay_period_num(3);
//        settle.setPay_period_unit(3);
//        settle.setWmPoiIdList(Lists.<Integer>newArrayList());
//        wmSettleService.saveWmSettleAndCommit(cusId, settle, 2050838, "邱润景测试", false, false, true);
//    }
//
//    @Test
//    @Rollback(false)
//    public void startWaitSign() throws Exception {
//        int cusId = insertCus();
//        WmSettle settle = new WmSettle();
//        settle.setWm_contract_id(cusId);
//        settle.setWmCustomerId(cusId);
//        settle.setSettle_type(WmContractConstant.SETTLETYPE_CIRCLE);
//        settle.setName(UUID.randomUUID().toString());
//        settle.setParty_a_finance_people("财务负责人");
//        settle.setParty_a_finance_phone("13131234321");
//        settle.setAcc_cardno("13131234321");
//        settle.setPay_period_num(3);
//        settle.setPay_period_unit(3);
//        settle.setWmPoiIdList(Lists.<Integer>newArrayList());
//
//        WmSettleModifyBo wmSettleModifyBo = new WmSettleModifyBo();
//        wmSettleModifyBo.setWmCustomerId(cusId);
//        wmSettleModifyBo.setIgnoreDup(true);
//        wmSettleModifyBo.setWmSettle(settle);
//        wmSettleModifyBo.setBrandBD(false);
//        wmSettleModifyBo.setEffective(false);
//        wmSettleModifyBo.setCommit(true);
//        wmSettleModifyBo.setPackWay(SignPackWay.WAIT_HAND_PACK.getCode());
//
//        wmSettleService.saveWmSettleAndCommit(wmSettleModifyBo, 2050838, "邱润景测试");
//    }
//
//    @Test
//    @Rollback(false)
//    public void startSign() throws Exception {
//        int cusId = insertCus();
//
//    }
//
//    @Test
//    @Rollback(false)
//    public void cancelWaitSign() throws Exception {
//        WmTempletContractBasicBo basic = wmContractService.getBasicById(1158, false, 2050838, "邱润景测试");
//        wmContractService.cancelC1SignByWaitingSign(basic.getParentId(), "BD主动取消签约", 2050838, "邱润景测试");
//    }
//}
