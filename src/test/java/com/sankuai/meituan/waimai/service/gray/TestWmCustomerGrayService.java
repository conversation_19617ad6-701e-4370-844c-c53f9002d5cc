package com.sankuai.meituan.waimai.service.gray;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class TestWmCustomerGrayService {

    @InjectMocks
    private WmCustomerGrayService wmCustomerGrayService;

    private MockedStatic<MccCustomerConfig> mccCustomerConfigMockedStatic;

    @Before
    public void setUp() {
        mccCustomerConfigMockedStatic = Mockito.mockStatic(MccCustomerConfig.class);
    }
    @After
    public void after() {
        if (null != mccCustomerConfigMockedStatic) {
            mccCustomerConfigMockedStatic.close();
        }

    }

    @Test
    public void testIsGrayNewCustomerUnBindPoi(){
        // 测试用例1：业务全量，返回有权限
        Map<String, Integer> map = Maps.newHashMap();
        map.put(CustomerPoiUnBindTypeEnum.DIRECT_UNBIND.name(), 1);
        Mockito.when(MccCustomerConfig.getPoiUnBindCustomerNewGrayPercent()).thenReturn(map);
        Assert.assertTrue(wmCustomerGrayService.isGrayNewCustomerUnBindPoi(CustomerPoiUnBindTypeEnum.DIRECT_UNBIND, 0));
    }
}
