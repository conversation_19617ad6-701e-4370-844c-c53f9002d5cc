//package com.sankuai.meituan.waimai.service.sign;
//
//import com.google.common.collect.Lists;
//
//import com.alibaba.fastjson.JSON;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
//import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBaseApplyTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBaseApplyBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractC2ContractInfoBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCancelAuthInfoBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractContractInfoBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerKPBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractKPAuthInfoBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiInfoBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
//
//import org.junit.Test;
//
//import java.util.List;
//
//import javax.annotation.Resource;
//
//public class TestWmEcontractSignThriftService extends BaseSpringJunit {
//
//    @Resource
//    private WmEcontractSignBzService wmEcontractSignBzService;
//
//    @Test
//    public void testApplyTask() throws Exception {
//        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
//        deliveryInfoBo.setPoiInfo("门店名称 ID 5108845");
//        deliveryInfoBo.setValidate("2018年12月10日");
//        deliveryInfoBo.setDeposit("测试");
//        deliveryInfoBo.setPoiName("测试");
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.WM_POI_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.POIFEE);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(true);
//        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(deliveryInfoBo));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//    }
//
//    @Test
//    public void testApplySettleTask() throws Exception {
//        EcontractSettleInfoBo settleInfoBo = new EcontractSettleInfoBo();
//        settleInfoBo.setPoiNames("哎呦店");
//        settleInfoBo.setAccountType("对公");
//        settleInfoBo.setAccountName("酱油");
//        settleInfoBo.setAccountCardNum("********");
//        settleInfoBo.setProvince("北京");
//        settleInfoBo.setCity("酱油村");
//        settleInfoBo.setBank("北京呵呵银行");
//        settleInfoBo.setBranch("吃饭支行");
//        settleInfoBo.setFinancialContact("某某联系人");
//        settleInfoBo.setFinancialContactPhone("***********");
//        settleInfoBo.setSettleType("对公");
//        settleInfoBo.setPayPeriod("一天");
//        settleInfoBo.setMinPayAmount("哎呦我去");
//        List<EcontractPoiInfoBo> poiInfoBoList = Lists.newArrayList();
//        EcontractPoiInfoBo poiInfoBo = new EcontractPoiInfoBo();
//        poiInfoBo.setWmPoiId(********);
//        poiInfoBo.setName("名字");
//        poiInfoBo.setAddress("地址");
//        poiInfoBoList.add(poiInfoBo);
//        settleInfoBo.setPoiInfoBoList(poiInfoBoList);
//        settleInfoBo.setSupportWallet(false);
//
//        List<EcontractSettleInfoBo> settleInfoBoList = Lists.newArrayList();
//        settleInfoBoList.add(settleInfoBo);
//
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.SETTLE);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(true);
//        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(settleInfoBoList));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//    }
//
//    @Test
//    public void testSettleMoon() throws Exception {
//        EcontractSettleInfoBo settleInfoBo = new EcontractSettleInfoBo();
//        settleInfoBo.setPoiNames("哎呦店");
//        settleInfoBo.setAccountType("1");//1:对公，2:对私
//        settleInfoBo.setAccountName("酱油");
//        settleInfoBo.setAccountCardNum("********");
//        settleInfoBo.setProvince("北京");
//        settleInfoBo.setCity("酱油村");
//        settleInfoBo.setBank("北京呵呵银行");
//        settleInfoBo.setBranch("吃饭支行");
//        settleInfoBo.setFinancialContact("某某联系人");
//        settleInfoBo.setFinancialContactPhone("***********");
//        settleInfoBo.setSettleType("对公");
//        settleInfoBo.setPayPeriod("一天");
//
//        settleInfoBo.setMinPayAmount("哎呦我去");
//        List<EcontractPoiInfoBo> poiInfoBoList = Lists.newArrayList();
//        EcontractPoiInfoBo poiInfoBo = new EcontractPoiInfoBo();
//        poiInfoBo.setWmPoiId(********);
//        poiInfoBo.setName("名字");
//        poiInfoBo.setAddress("地址");
//        poiInfoBoList.add(poiInfoBo);
//        settleInfoBo.setPoiInfoBoList(poiInfoBoList);
//
//        settleInfoBo.setSupportWallet(true);
//        settleInfoBo.setQdbNumber("QDB-010-01-********");
//        settleInfoBo.setQuaType("身份证");
//        settleInfoBo.setQuaNum("342221198906240078");
//        settleInfoBo.setReservedPhone("***********");
//        settleInfoBo.setPartB("北京钱袋宝支付技术有限公司");
//        settleInfoBo.setPartBAddress("望京科创大厦");
//        settleInfoBo.setPartBPhone("***********");
//
//
//        List<EcontractSettleInfoBo> settleInfoBoList = Lists.newArrayList();
//        settleInfoBoList.add(settleInfoBo);
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.SETTLE);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(true);
//        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(settleInfoBoList));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//    }
//
//    @Test
//    public void testContractApplyTask() throws Exception {
//        EcontractContractInfoBo contractInfoBo = new EcontractContractInfoBo();
//        contractInfoBo.setContractNum("WMYW-010-********");
//        contractInfoBo.setPartAContact("呵呵");
//        contractInfoBo.setPartAContactPhone("13099248771");
//        contractInfoBo.setPartAName("哎呦我去");
//        contractInfoBo.setPartASignTime("2018-01-01");
//        contractInfoBo.setPartBContact("测试");
//        contractInfoBo.setPartBContactPhone("13988276552");
//        contractInfoBo.setPartBName("哎呦我去B");
//        contractInfoBo.setPartBSignTime("2019-02-12");
//
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(true);
//        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//    }
//
//    @Test
//    public void applyPaperContract() throws Exception {
//        EcontractContractInfoBo contractInfoBo = new EcontractContractInfoBo();
//        contractInfoBo.setContractNum("WMYW-010-********");
//        contractInfoBo.setPartAContact("呵呵");
//        contractInfoBo.setPartAContactPhone("13099248771");
//        contractInfoBo.setPartAName("哎呦我去");
//        contractInfoBo.setPartASignTime("2018-01-01");
//        contractInfoBo.setPartBContact("测试");
//        contractInfoBo.setPartBContactPhone("13988276552");
//        contractInfoBo.setPartBName("哎呦我去B");
//        contractInfoBo.setPartBSignTime("2019-02-12");
//
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(false);
//        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//    }
//
//    @Test
//    public void applyKPTask() throws Exception {
//        EcontractKPAuthInfoBo kpAuthInfoBo = new EcontractKPAuthInfoBo();
//        kpAuthInfoBo.setPoiNames("测试门店");
//        kpAuthInfoBo.setSignerName("签约人");
//        kpAuthInfoBo.setSignerPhone("130********");
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.KP);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(true);
////        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(kpAuthInfoBo));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//    }
//
//    @Test
//    public void applyCancelTask() throws Exception {
//        EcontractCancelAuthInfoBo cancelAuthInfoBo = new EcontractCancelAuthInfoBo();
//        cancelAuthInfoBo.setWmPoiName("门店名称");
//        cancelAuthInfoBo.setCustomerName("客户名称");
//        cancelAuthInfoBo.setCustomerNumber("WMYW-010-01-********");
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.CUSTOMER);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(true);
//        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(cancelAuthInfoBo));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//    }
//
//    @Test
//    public void applyDeliveryTask() throws Exception {
//        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
//        deliveryInfoBo.setPoiName("我勒个去");
//        deliveryInfoBo.setDeposit("10000元");
//        deliveryInfoBo.setValidate("2018-02-02");
//        deliveryInfoBo.setPoiInfo("李东亮覅按揭房");
//        deliveryInfoBo.setChargingDesc("测试测试测试测试测试测试测试测试");
//        deliveryInfoBo.setDeliveryType("商家自配");
////        deliveryInfoBo.setSupportExclusive("support");
//        deliveryInfoBo.setServerSupport("呵呵");
//        deliveryInfoBo.setOther("赫尔");
//        deliveryInfoBo.setFeeInfo("6421 1000%");
//        deliveryInfoBo.setExclusiveFee("了斯蒂芬");
//        deliveryInfoBo.setExclusiveDesc("呵呵");
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.WM_POI_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.POIFEE);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(true);
//        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(deliveryInfoBo));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//
//    }
//
//    @Test
//    public void applyC2ContractTask() throws Exception {
//        EcontractC2ContractInfoBo contractInfoBo = new EcontractC2ContractInfoBo();
//        contractInfoBo.setContractNum("WMYW-010-********");
//        contractInfoBo.setPartAContact("呵呵");
//        contractInfoBo.setPartAContactPhone("13099248771");
//        contractInfoBo.setPartAName("哎呦我去");
//        contractInfoBo.setPartASignTime("2018-01-01");
//        contractInfoBo.setPartBContact("测试");
//        contractInfoBo.setPartBContactPhone("13988276552");
//        contractInfoBo.setPartBName("哎呦我去B");
//        contractInfoBo.setPartBSignTime("2019-02-12");
//
//        contractInfoBo.setAgentId(1000);
//        contractInfoBo.setAgentName("测试代理商");
//        contractInfoBo.setAgentQuaType(CustomerType.CUSTOMER_TYPE_IDCARD);
//        contractInfoBo.setLegalPersonEmail("<EMAIL>");
//        contractInfoBo.setLegalPersonPhone("130********");
//        contractInfoBo.setQuaNum("342221198906240078");
//
//        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
//        applyBo.setBizId("********");
//        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
//        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C2CONTRACT);
//        EcontractTaskConfigBo configBo = new EcontractTaskConfigBo();
//        configBo.setCommitConfirm(true);
//        applyBo.setConfigBo(configBo);
//
//        applyBo.setApplyInfoBo(JSON.toJSONString(contractInfoBo));
//        applyBo.setRecordId("自动化全流程测试");
//
//        wmEcontractSignBzService.applyTask(applyBo);
//    }
//
//    @Test
//    public void applyBase() throws Exception {
//        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
//        customerInfoBo.setQuaNum("342221198906240078");
//        customerInfoBo.setQuaTypeEnum(CustomerType.CUSTOMER_TYPE_IDCARD);
//        customerInfoBo.setCustomerName("测试");
//        customerInfoBo.setAddress("李浩维专属");
//        customerInfoBo.setLegalPerson("李浩维");
//
//        EcontractBaseApplyBo baseApplyBo = new EcontractBaseApplyBo();
//        baseApplyBo.setCustomerId(********);
//        baseApplyBo.setApplyTypeEnum(EcontractBaseApplyTypeEnum.CUSTOMER);
//        baseApplyBo.setApplyInfoBo(JSON.toJSONString(customerInfoBo));
//        wmEcontractSignBzService.applyBase(baseApplyBo);
//    }
//
//    @Test
//    public void applyKpBase() throws Exception {
//        EcontractCustomerKPBo kpBo = new EcontractCustomerKPBo();
//        kpBo.setKpTypeEnum(KpTypeEnum.SIGNER);
//        kpBo.setSignerBankCardNum("6216261000000000018");
//        kpBo.setSignerBankName("工商银行");
//        kpBo.setSignerIDCardNum("360481199010120018");
//        kpBo.setSignerEmail("<EMAIL>");
//        kpBo.setSignerName("李浩维专属");
//        kpBo.setSignerPhoneNum("***********");
//
//        EcontractBaseApplyBo baseApplyBo = new EcontractBaseApplyBo();
//        baseApplyBo.setCustomerId(********);
//        baseApplyBo.setApplyTypeEnum(EcontractBaseApplyTypeEnum.KP);
//        baseApplyBo.setApplyInfoBo(JSON.toJSONString(kpBo));
//        wmEcontractSignBzService.applyBase(baseApplyBo);
//    }
//
//    @Test
//    public void resendMsg() throws Exception {
//        wmEcontractSignBzService.resendMsg(290L);
//    }
//
//    @Test
//    public void cancel() throws Exception {
//        wmEcontractSignBzService.cancelSign(135L);
//    }
//
//
//
//}
