//package com.sankuai.meituan.waimai.service.customer;
//
//import com.alibaba.fastjson.JSON;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
//import com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker;
//import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class TestSignModeSwitchService extends BaseSpringJunit {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TestSignModeSwitchService.class);
//
//    @Autowired
//    private WmCustomerService wmCustomerService;
//
//    @Test
//    public void testElectronicToPaper() throws Exception {
//        ValidateResultBo validateResultBo = new ValidateResultBo();
//        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(10014515);
//        wmCustomerBasicBo.setId(wmCustomerDB.getId());
//        wmCustomerBasicBo.setSignMode(CustomerSignMode.PAPER.getCode());
//        boolean checkSwitchSignMode = new SignModeSwitchChecker(wmCustomerBasicBo, wmCustomerDB, validateResultBo)
//                .check();
//
//        System.out.println("checkSwitchSignMode:" + checkSwitchSignMode);
//        System.out.println("validateResultBo:" + JSON.toJSONString(validateResultBo));
//    }
//
//    @Test
//    public void testPaperToElectronic() throws Exception {
//        ValidateResultBo validateResultBo = new ValidateResultBo();
//        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(10014515);
//        wmCustomerBasicBo.setId(wmCustomerDB.getId());
//        wmCustomerBasicBo.setSignMode(CustomerSignMode.ELECTTRONIC.getCode());
//        boolean checkSwitchSignMode = new SignModeSwitchChecker(wmCustomerBasicBo, wmCustomerDB, validateResultBo)
//                .check();
//
//        System.out.println("checkSwitchSignMode:" + checkSwitchSignMode);
//        System.out.println("validateResultBo:" + JSON.toJSONString(validateResultBo));
//    }
//
//}
