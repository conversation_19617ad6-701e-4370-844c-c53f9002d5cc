//package com.sankuai.meituan.waimai.service;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
//import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
//import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.ArrayList;
//import java.util.List;
//
//public class TestWmCustomerKpService extends BaseSpringJunit {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TestWmCustomerKpService.class);
//
//    @Autowired
//    private WmCustomerKpService wmCustomerKpService;
//
//    @Autowired
//    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;
//
//    @Test
//    public void testGetCustomerKpOfSigner() throws Exception {
//        WmCustomerKp customerKpOfSigner = wmCustomerKpService.getCustomerKpOfEffectiveSigner(1);
//        LOGGER.info("customerKpOfSigner={}", JSON.toJSONString(customerKpOfSigner));
//    }
//
//    @Test
//    public void testBatchSave() throws Exception {
//        int customerId = 11;
//        WmCustomerKp signer = new WmCustomerKp();
//        signer.setId(4);
//        signer.setKpType(KpTypeEnum.SIGNER.getType());
//        signer.setSignerType(KpSignerTypeEnum.SIGNER.getType());
//        signer.setCompellation("全渠道");
//        signer.setCertType(CertTypeEnum.ID_CARD.getType());
//        signer.setCertNumber("341126197709218366");
//        signer.setPhoneNum("***********");
//        signer.setAgentFrontIdcard("xxxxxxxxxx.png");
//        signer.setBankId(1);
//        signer.setBankName("北京银行");
//        signer.setCreditCard("6216261000000000018");
//
//        WmCustomerKp kp = new WmCustomerKp();
//        kp.setId(5);
//        kp.setKpType(KpTypeEnum.OTHER.getType());
//        kp.setCompellation("普通KP名字~~~");
//        kp.setPhoneNum("***********");
//
//        List<WmCustomerKp> list = Lists.newArrayList(signer);
//        try {
//            wmCustomerKpService.batchSaveOrUpdateCustomerKp(customerId, list, 0, "");
//        } catch (WmCustomerException e) {
//            LOGGER.error("code:{}, msg:{}", e.getCode(), e.getMsg(), e);
//            throw e;
//        }
//    }
//
//    @Test
//    public void testGetCustomerSignerKp() throws Exception {
//        WmCustomerKp signer = wmCustomerKpService.getCustomerSignerKp(********);
//        System.out.println(signer);
//    }
//
//    @Test
//    public void testForceAuth() throws Exception {
//        wmCustomerKpService.forceAuth(10001, 0, "junit");
//    }
//
//    @Test
//    public void testCancelAuth() throws Exception {
//        wmCustomerKpService.cancelAuth(10001, 0, "junit");
//    }
//
//    @Test
//    public void testReSendAuthSms() throws Exception {
//        wmCustomerKpService.reSendAuthSms(10001, 0, "junit");
//    }
//
//    @Test
//    public void testSelectKpAudit() throws Exception {
//        WmCustomerKpAudit kpAudit = wmCustomerKpAuditMapper.selectByKpIdAndType(1030, 3);
//        System.out.println(JSON.toJSONString(kpAudit));
//    }
//
//}
