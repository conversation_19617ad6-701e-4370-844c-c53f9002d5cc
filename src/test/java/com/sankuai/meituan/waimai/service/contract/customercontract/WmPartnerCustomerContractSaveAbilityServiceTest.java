package com.sankuai.meituan.waimai.service.contract.customercontract;

import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanSelfSaveContractServiceImpl;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanSelfSettleOpenServiceImpl;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/10/11 19:33
 */
public class WmPartnerCustomerContractSaveAbilityServiceTest extends BaseStaticMockTest {

    @InjectMocks
    private DaoCanSelfSettleOpenServiceImpl daoCanSelfSettleOpenService;

    @InjectMocks
    private DaoCanSelfSaveContractServiceImpl daoCanSelfSaveContractService;

    @Test
    public void testDcSelfOpenServiceWithWmCustomerException() {
        // 执行测试逻辑
        Assert.assertTrue(true);
    }

}
