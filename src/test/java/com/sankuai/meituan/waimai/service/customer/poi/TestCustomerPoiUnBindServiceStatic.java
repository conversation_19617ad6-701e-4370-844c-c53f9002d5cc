package com.sankuai.meituan.waimai.service.customer.poi;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.adapter.StateCenterAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiOplogService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiRelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

public class TestCustomerPoiUnBindServiceStatic extends BaseStaticMockTest {

    @InjectMocks
    private CustomerPoiUnBindService customerPoiUnBindService;

    @Mock
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Mock
    private WmCustomerService wmCustomerService;

    @Mock
    private CustomerPoiBaseValidator customerPoiBaseValidator;

    @Mock
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Mock
    private StateCenterAdapter stateCenterAdapter;

    @Mock
    private WmEmployClient wmEmployClient;

    @Mock
    private WmContractService wmContractService;

    @Mock
    private CustomerTaskService customerTaskService;

    @Mock
    private WmCustomerPoiOplogService wmCustomerPoiOplogService;

    @Before
    public void setUp() {
       super.init();
    }

    @Test
    public void testCustomerDeleteUnBind() throws WmCustomerException {
        int customerId = 1;
        int opUid = 0;
        String opName = "测试";
        List<Long> wmPoiIds = Lists.newArrayList(1L, 2L, 3L, 4L, 5L, 6L);
        when(wmCustomerPoiRelService.selectWmPoiIdsByCustomerId(customerId)).thenReturn(wmPoiIds);
        mccConfigMockedStatic.when(MccConfig::getCustomerDeletePoiBatchNum).thenReturn(5);
        Exception exception = Assert.assertThrows(WmCustomerException.class, () -> {
            customerPoiUnBindService.customerDeleteUnBind(customerId, opUid, opName);
        });
        Assert.assertTrue(exception.getMessage().equals("客户所关联的门店大于" + MccConfig.getCustomerDeletePoiBatchNum() + ",请先解绑关联门店"));

        mccConfigMockedStatic.when(MccConfig::getCustomerDeletePoiBatchNum).thenReturn(6);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        when(wmCustomerService.selectCustomerById(any())).thenReturn(wmCustomerDB);
        customerPoiUnBindService.customerDeleteUnBind(customerId, opUid, opName);
    }

    @Test
    public void testAgentSwitchUnBind() throws WmCustomerException {
        Set<Long> wmPoiIdSet = Sets.newHashSet();
        int customerId = 1;
        int opUid = 0;
        String opName = "测试";
        customerPoiUnBindService.agentSwitchUnBind(customerId, wmPoiIdSet, opUid, opName);
        wmPoiIdSet.add(1L);
        Exception exception = Assert.assertThrows(WmCustomerException.class, () -> {
            customerPoiUnBindService.agentSwitchUnBind(customerId, wmPoiIdSet, opUid, opName);
        });
        Assert.assertTrue(exception.getMessage().equals("客户ID无法查到有效数据"));


        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        when(wmCustomerService.selectCustomerById(anyInt())).thenReturn(wmCustomerDB);
        Map<String, Object> customerUnBIndPoiAppKeyConfigs = Maps.newHashMap();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("opSource", 16);
        jsonObject.put("opDetailSource", "门店代理属性变更");
        jsonObject.put("opSystem", "代理商服务");
        customerUnBIndPoiAppKeyConfigs.put("com.sankuai.waimai.m.agent", jsonObject);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getCustomerUnbindPoiAppKeySourceConfig).thenReturn(customerUnBIndPoiAppKeyConfigs);
//        when(wmCustomerPoiOplogService.getOpSourceType(any(), any())).thenReturn(WmCustomerPoiOplogSourceTypeEnum.UNKNOW);
        Map<Long, Integer> poiAndTaskMaps = Maps.newHashMap();
        poiAndTaskMaps.put(1L, 1);
//        when(customerTaskService.batchAddCustomerUnBindTask(any(), any(), any())).thenReturn(poiAndTaskMaps);
        customerPoiUnBindService.agentSwitchUnBind(customerId, wmPoiIdSet, opUid, opName);
    }
}
