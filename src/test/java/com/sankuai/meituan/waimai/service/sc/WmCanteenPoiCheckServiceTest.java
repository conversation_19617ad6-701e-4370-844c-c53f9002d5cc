package com.sankuai.meituan.waimai.service.sc;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolAreaMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenPoiTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.service.sc.check.WmCanteenPoiCheckService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenAttributeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyInt;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WmCanteenPoiCheckServiceTest {

    @InjectMocks
    private WmCanteenPoiCheckService wmCanteenPoiCheckService;

    @Mock
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    /**
     * 当食堂属性是承包时,校验食堂经理信息完整（信息完整指姓名、证件类型、证件编号、手机号齐全)
     * todo: 有多个异常情况写多个test方法
     */
    @Test
    public void testValidCanteenInfoComplete() {
        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setId(3074);
        wmCanteenDB.setCanteenAttribute((int) CanteenAttributeEnum.CONTRACTOR.getType());
        wmCanteenDB.setCardType(1);

        Exception exception = Assert.assertThrows(
                WmSchCantException.class,
                () -> {wmCanteenPoiCheckService.validCanteenInfoComplete(wmCanteenDB);});
        Assert.assertEquals("请补全食堂经理信息，当前不可保存", exception.getMessage());

        // 仅有姓名
        wmCanteenDB.setManager("Test Manager");
        exception = Assert.assertThrows(
                WmSchCantException.class,
                () -> {wmCanteenPoiCheckService.validCanteenInfoComplete(wmCanteenDB);});
        Assert.assertEquals("请补全食堂经理信息，当前不可保存", exception.getMessage());

        // 仅有手机号+姓名
        wmCanteenDB.setManagerPhone("12345678901");
        exception = Assert.assertThrows(
                WmSchCantException.class,
                () -> {wmCanteenPoiCheckService.validCanteenInfoComplete(wmCanteenDB);});
        Assert.assertEquals("请补全食堂经理信息，当前不可保存", exception.getMessage());
    }

    /**
     * 校验门店所绑定食堂所关联学校是否有生效的范围信息
     * todo: 恒错断言
     */
    @Test
    public void testValidShoolHasEffectArea() throws WmSchCantException {
        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = new WmCanteenPoiTaskBO();
        wmCanteenPoiTaskBO.setId(123L);

        Exception exception = Assert.assertThrows(
                WmSchCantException.class,
                () -> {wmCanteenPoiCheckService.validShoolHasEffectArea(wmCanteenPoiTaskBO);});
        Assert.assertEquals("食堂所属学校没有录入学校范围", exception.getMessage());

        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = new ArrayList<>();
        WmScSchoolAreaDO wmScSchoolAreaDO = new WmScSchoolAreaDO();
        wmScSchoolAreaDO.setArea("");
        wmScSchoolAreaDOList.add(wmScSchoolAreaDO);

        WmSchoolDB wmSchoolDB = new WmSchoolDB();
        wmSchoolDB.setId(1404);
        wmCanteenPoiTaskBO.setSchoolTo(wmSchoolDB);
        when(wmScSchoolAreaMapper.selectBySchoolId(anyInt())).thenReturn(wmScSchoolAreaDOList);

        exception = Assert.assertThrows(
                WmSchCantException.class,
                () -> {wmCanteenPoiCheckService.validShoolHasEffectArea(wmCanteenPoiTaskBO);});
        Assert.assertEquals("食堂所属学校没有录入学校范围", exception.getMessage());

        String area1 = "[{\"x\":39736231,\"y\":116172715},{\"x\":39736223,\"y\":116174954},{\"x\":39734014,\"y\":116174943},{\"x\":39734031,\"y\":116172705}]";
        WmScSchoolAreaDO wmScSchoolAreaDO1 = new WmScSchoolAreaDO();
        wmScSchoolAreaDO1.setArea(area1);
        wmScSchoolAreaDOList.add(wmScSchoolAreaDO1);

        String area2 = "[{\"x\":39731301,\"y\":116167209},{\"x\":39739058,\"y\":116167085},{\"x\":39739173,\"y\":116170825},{\"x\":39738925,\"y\":116172097},{\"x\":39738634,\"y\":116172355},{\"x\":39731265,\"y\":116172370}]";
        WmScSchoolAreaDO wmScSchoolAreaDO2 = new WmScSchoolAreaDO();
        wmScSchoolAreaDO2.setArea(area2);
        wmScSchoolAreaDOList.add(wmScSchoolAreaDO2);

        when(wmScSchoolAreaMapper.selectBySchoolId(anyInt())).thenReturn(wmScSchoolAreaDOList);
        wmCanteenPoiCheckService.validShoolHasEffectArea(wmCanteenPoiTaskBO);
        List<String> expectAreaList = Lists.newArrayList(area1, area2);
        Assert.assertEquals(wmCanteenPoiTaskBO.getSchoolAreaList(), expectAreaList);
    }

}
