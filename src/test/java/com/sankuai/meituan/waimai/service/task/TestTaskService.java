//package com.sankuai.meituan.waimai.service.task;
//
//import static com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant.NOT_PACK;
//import static com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant.TO_NOTIFY;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Lists;
//import com.sankuai.meituan.common.time.TimeUtil;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
//import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignManualBatchDBMapper;
//import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignManualTaskDBMapper;
//import com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam;
//import com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam.Builder;
//import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
//import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualBatchDB;
//import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
//import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
//import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
//import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchQueryThriftParam;
//import java.util.List;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.CollectionUtils;
//
//public class TestTaskService extends BaseSpringJunit {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TestTaskService.class);
//    @Autowired
//    private WmEcontractBatchBizService batchBizService;
//    @Autowired
//    private WmEcontractSignManualTaskDBMapper wmEcontractSignManualTaskDBMapper;
//    @Autowired
//    private WmEcontractSignManualBatchDBMapper wmEcontractSignManualBatchDBMapper;
//
//    @Test
//    public void testQueryWithParam() {
//        SignBatchQueryParam param = new Builder().customerId(10014393)
//                .startTime((long) TimeUtil.unixtime() - 1000).endTime((long) TimeUtil.unixtime())
//                .build();
//        List<WmEcontractSignBatchDB> wmEcontractSignBatchDBS = batchBizService
//                .queryWithParam(param);
//        LOGGER.info(JSONObject.toJSONString(wmEcontractSignBatchDBS));
//    }
//
//    @Test
//    public void testWmEcontractSignManualTaskDBMapper() {
////        wmEcontractSignManualTaskDBMapper.deleteByPrimaryKey(2L);
//        WmEcontractSignManualTaskDB tasnDB  = new WmEcontractSignManualTaskDB();
//        tasnDB.setCustomerId(606060);
//        tasnDB.setWmPoiId(123456L);
//        tasnDB.setApplyContext("");
//        tasnDB.setModule("delivery");
//        tasnDB.setCommitUid(2050838);
//        tasnDB.setValid((byte)1);
//        wmEcontractSignManualTaskDBMapper.insertSelective(tasnDB);
////        int count = wmEcontractSignManualTaskDBMapper
////                .getCountByCustomerIdAndModuleWithWmPoiId(606060,"delivery",
////                        123456L);
////        wmEcontractSignManualTaskDBMapper.deleteByCustomerIdAndModule(606060, "delivery");
////        List<Long> ids = wmEcontractSignManualTaskDBMapper
////                .getManualTaskIdByCustomerIdAndModule(606060, "delivery");
////        List<WmEcontractSignManualTaskDB> taskInfo = wmEcontractSignManualTaskDBMapper
////                .getManualTaskByCustomerIdAndModule(606060,
////                        EcontractTaskApplyTypeEnum.POIFEE.getName());
////        List<WmEcontractSignManualTaskDB> wmEcontractSignManualTaskDBS = wmEcontractSignManualTaskDBMapper
////                .batchGetByManualTaskIds(Lists.newArrayList(1L, 2L, 4L, 5L));
////        int count = wmEcontractSignManualTaskDBMapper
////                .updateManualTaskBatchId(Lists.newArrayList(4L, 5L), 110);
////        int count = wmEcontractSignManualTaskDBMapper.deleteByCustomerId(606060);
////        SignBatchQueryParam queryWithParam = new SignBatchQueryParam();
////        queryWithParam.setCommitUid(2050838);
////        queryWithParam.setCustomerId(606060);
////        queryWithParam.setStartTime(0L);
////        queryWithParam.setEndTime(1549949190L);
////        List<WmEcontractSignManualTaskDB> wmEcontractSignManualTaskDBS = wmEcontractSignManualTaskDBMapper
////                .queryWithParam(queryWithParam);
//
////        List<Long> manualTaskIdByCustomerId = wmEcontractSignManualTaskDBMapper
////                .getManualTaskIdByCustomerId(606060);
//
//        LOGGER.info("#testWmEcontractSignManualTaskDBMapper#tasnDB={}",
//                JSONObject.toJSONString(tasnDB));
//    }
//
//    @Test
//    public void testWmEcontractSignManualBatchDBMapper() {
////        WmEcontractSignManualBatchDB record = new WmEcontractSignManualBatchDB();
////        record.setCustomerId(606060);
////        record.setC1contractStatus(NOT_PACK);
////        record.setSettleStatus(TO_NOTIFY);
////        record.setDeliveryStatus(TO_NOTIFY);
////        record.setCommitUid(11010);
////        record.setVersion(1);
////        record.setValid((byte) 1);
////        int count = wmEcontractSignManualBatchDBMapper.insertSelective(record);
////        WmEcontractSignManualBatchDB wmEcontractSignManualBatchDB = wmEcontractSignManualBatchDBMapper
////                .selectByPrimaryKey(1L);
////        WmEcontractSignManualBatchDB record = new WmEcontractSignManualBatchDB();
////        record.setId(1L);
////        record.setVersion(1);
////        record.setDeliveryStatus(WmEcontractConstant.TO_COMMIT);
////        int count = wmEcontractSignManualBatchDBMapper.updateByPrimaryKeySelective(record);
//        int count = wmEcontractSignManualBatchDBMapper.deleteByCustomerId(606060);
//        LOGGER.info("#testWmEcontractSignManualBatchDBMapper#count={}",
//                JSONObject.toJSONString(count));
//    }
//
//
//}
