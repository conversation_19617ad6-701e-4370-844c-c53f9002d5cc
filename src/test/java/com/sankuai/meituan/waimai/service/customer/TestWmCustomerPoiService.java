//package com.sankuai.meituan.waimai.service.customer;
//
//import org.junit.Test;
//import org.mockito.internal.util.collections.Sets;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.annotation.Rollback;
//
//import com.sankuai.meituan.waimai.BaseSpringTransactionJunit;
//import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
//
//public class TestWmCustomerPoiService extends BaseSpringTransactionJunit {
//
//    private static final Logger  LOGGER = LoggerFactory.getLogger(TestWmCustomerPoiService.class);
//
//    @Autowired
//    private WmCustomerPoiService wmCustomerPoiService;
//
//    @Test
//    @Rollback
//    public void testGetCustomerList() throws Exception {
//        wmCustomerPoiService.customerUnBindPoiForSync(10014898, Sets.newSet(10000472l), 0, "", null);
//    }
//
//}
