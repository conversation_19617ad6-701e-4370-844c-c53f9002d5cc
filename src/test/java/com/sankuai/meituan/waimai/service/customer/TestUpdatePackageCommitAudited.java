//package com.sankuai.meituan.waimai.service.customer;
//
//import com.alibaba.fastjson.JSON;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
//import com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker;
//import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBatchAuditBo;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class TestUpdatePackageCommitAudited extends BaseSpringJunit {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TestUpdatePackageCommitAudited.class);
//
//    @Autowired
//    private WmCustomerService wmCustomerService;
//
//    /**
//     * 测试更新提审信息为已打包提审
//     * @throws Exception
//     */
//    @Test
//    public void testPackageCommitAudite() throws Exception {
//        try{
//            wmCustomerService.packageCommitAudited(10014632);
//            System.out.println("执行完毕");
//
//        }catch (Exception e ){
//            e.printStackTrace();
//        }
//
//    }
//
//    /**
//     * 测试提审客户资质
//     * @throws Exception
//     */
//    @Test
//    public void testCommitAudit() throws Exception {
//        int customerId = 10014632;
//        WmCustomerBatchAuditBo wmCustomerBatchAuditBo = wmCustomerService.commitAudit(customerId,0,"",false);
//
//        System.out.println(JSON.toJSONString(wmCustomerBatchAuditBo)
//        );
//    }
//
//}
