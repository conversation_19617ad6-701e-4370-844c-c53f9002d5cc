//package com.sankuai.meituan.waimai.service.sign;
//
//import com.google.common.collect.Lists;
//import com.meituan.mtrace.Tracer;
//import com.sankuai.meituan.common.json.JSONUtil;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.BaseTest.RUN_MODE;
//import com.sankuai.meituan.waimai.customer.service.WmEcontractSignThriftServiceImpl;
//import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
//import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.CancelSignForSwitchParam;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
//import com.sankuai.meituan.waimai.thrift.customer.service.external.WmEcontractSignExternalThriftService;
//import javax.annotation.Resource;
//import org.junit.Before;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
///**
// * -@author: huangjianlou
// * -@description:
// * -@date: 2022/10/26 8:45 PM
// */
//public class TestWmCancelSignCustomerSwitch extends BaseTest {
//
//
//    private static WmEcontractSignExternalThriftService wmEcontractSignExternalThriftService;
//    private static WmEcontractSignThriftService wmEcontractSignThriftService;
//
//    @Before
//    public void before() {
//        Tracer.setSwimlane("waimai-eci-220921-115107-217");
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8437);
//        this.setEnv(ENV_TEST);
//        this.setRunMode(RUN_MODE.REMOTE);
////        try {
////            wmEcontractSignExternalThriftService = (WmEcontractSignExternalThriftService) getObject(WmEcontractSignExternalThriftService.class);
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
//        try {
//            wmEcontractSignThriftService = (WmEcontractSignThriftService) getObject(WmEcontractSignThriftService.class);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//
//
//    // 按任务维度 WmEcontractSignThriftServiceImpl cancelWmCustomerSwitch
//
//
//
//    /**
//     * 门店维度
//     * @throws Exception 异常
//     */
//    @Test
//    public void testCancelWmPoiCustomerSwitch() throws Exception {
//        System.out.println("my first UT in Waimai via testCancelWmCustomerSwitch ...");
//
//        /**
//         * {
//         *   "fromBizId": "11997862",
//         *   "opName": "杨克川",
//         *   "opUid": 5246687,
//         *   "switchOptions": [
//         *     "FROM_CUSTOMER_CONFRIM_UNBIND_AUTO",
//         *     "TO_CUSTOMER_CREATE_CONTRACT_AUTO",
//         *     "TO_CUSTOMER_PACK_SIGN_AUTO"
//         *   ],
//         *   "taskId": 19956,
//         *   "toBizId": "11998156",
//         *   "wmPoiIds": [
//         *     954975
//         *   ]
//         * }
//         */
//
//        CancelSignForSwitchParam cancelSignForSwitchParam = CancelSignForSwitchParam.builder()
//            .taskId(20005L)
//            .targetCustomerId(11998156)
//            .wmPoiIdList(Lists.newArrayList(956087L))
//            .opUid(2032971)
//            .opUname("闫子奇")
//            .build();
//
//        BooleanResult result = wmEcontractSignThriftService.cancelWmCustomerSwitchForWmPoi(cancelSignForSwitchParam);
//        System.out.println("res = " + JSONUtil.toJSONString(result));
//
//    }
//
//    @Test
//    public void testCancelWmTaskBasedCustomerSwitch() throws Exception {
//        System.out.println("my first UT in Waimai via testCancelWmTaskBasedCustomerSwitch...");
//
//    }
//
//}
