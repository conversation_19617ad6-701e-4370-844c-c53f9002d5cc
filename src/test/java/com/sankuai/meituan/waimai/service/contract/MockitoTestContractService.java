/*
package com.sankuai.meituan.waimai.service.contract;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.BaseMockito;
import com.sankuai.meituan.waimai.contract.thrift.domain.WmAgentContract;
import com.sankuai.meituan.waimai.contract.thrift.service.WmAgentContractThriftService;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.contract.dao.*;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.*;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModuleStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.StatusEnum;
import com.sankuai.meituan.waimai.utils.ObjectMockUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

public class MockitoTestContractService extends BaseMockito {

    @InjectMocks
    WmContractService wmContractService;

    @Mock
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Mock
    WmTempletContractDBMapper wmTempletContractDBMapper;

    @Mock
    WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    @Mock
    WmTempletContractSignDBMapper wmTempletContractSignDBMapper;

    @Mock
    WmTempletContractSignAuditedDBMapper wmTempletContractSignAuditedDBMapper;

    @Mock
    ContractLogService contractLogService;

    @Mock
    WmContractVersionService wmContractVersionService;

    @Mock
    private WmContractFtlTagMapper wmContractFtlTagMapper;

    @Mock
    private WmPoiClient wmPoiClient;

    @Mock
    private WmContractSignService wmContractSignService;

    @Mock
    WmCustomerService wmCustomerService;

    @Mock
    WmCustomerPoiService wmCustomerPoiService;

    @Mock
    WmCustomerKpService wmCustomerKpService;

    @Mock
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Mock
    WmSettleService wmSettleService;

    @Mock
    WmAgentContractThriftService.Iface wmAgentContractThriftService;

    @Mock
    WmContractPoiProduceService wmContractPoiProduceService;

    @Test
    public void 门店上线检查_直营_没有生效合同() throws Exception {

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode())
                )
        ).thenReturn(null);

        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(new WmPoiDomain());

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));
        Assert.assertEquals(StatusEnum.IN_EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_直营_门店已下线_合同过期() throws Exception {

        WmTempletContractDB contractDB1 = ObjectMockUtils.getObject(WmTempletContractDB.class, 0);

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode())
                )
        ).thenReturn(Lists.newArrayList(contractDB1));

        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(new WmPoiDomain());

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));
        Assert.assertEquals(StatusEnum.IN_EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_直营_门店已下线_合同不过期() throws Exception {

        WmTempletContractDB contractDB1 = ObjectMockUtils.getObject(WmTempletContractDB.class, 1);

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode())
                )
        ).thenReturn(Lists.newArrayList(contractDB1));

        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(new WmPoiDomain());

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));

        Assert.assertEquals(StatusEnum.EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_直营_门店未下线_合同过期() throws Exception {

        WmTempletContractDB contractDB1 = ObjectMockUtils.getObject(WmTempletContractDB.class, 0);

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode())
                )
        ).thenReturn(Lists.newArrayList(contractDB1));

        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        wmPoiDomain.setValid(1);
        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(wmPoiDomain);

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));
        Assert.assertEquals(StatusEnum.EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_直营_门店未下线_合同未过期() throws Exception {

        WmTempletContractDB contractDB1 = ObjectMockUtils.getObject(WmTempletContractDB.class, 1);

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode())
                )
        ).thenReturn(Lists.newArrayList(contractDB1));

        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        wmPoiDomain.setValid(1);
        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(wmPoiDomain);

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));

        Assert.assertEquals(StatusEnum.EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_代理非大连锁门店_C1有效_C2有效_C3有效_门店未下线() throws Exception {

        List<WmTempletContractDB> contractDBList = ObjectMockUtils.getObject(WmTempletContractDB.class, 1, 3);

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode())
                )
        ).thenReturn(contractDBList);

        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        wmPoiDomain.setValid(1);
        wmPoiDomain.setAgentId((short) 1);
        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(wmPoiDomain);

        WmTempletContractSignBo partyB = new WmTempletContractSignBo();
        partyB.setSignId(wmPoiDomain.getAgentId());
        Mockito.when(wmContractSignService
                .getAuditedPartyBSigner(contractDBList.get(1).getId()))
                .thenReturn(partyB);

        WmAgentContract wmAgentContract = new WmAgentContract();
        wmAgentContract.setValid(1);
        Mockito.when(wmAgentContractThriftService
                .getWmAgentContractByAgentId(wmPoiDomain.getAgentId()))
                .thenReturn(wmAgentContract);

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));

        Assert.assertEquals(StatusEnum.EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_代理非大连锁门店_C1有效_C2有效_C3有效_门店下线() throws Exception {

        List<WmTempletContractDB> contractDBList = ObjectMockUtils.getObject(WmTempletContractDB.class, 1, 3);

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode())
                )
        ).thenReturn(contractDBList);

        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        wmPoiDomain.setValid(0);
        wmPoiDomain.setAgentId((short) 1);
        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(wmPoiDomain);

        WmTempletContractSignBo partyB = new WmTempletContractSignBo();
        partyB.setSignId(wmPoiDomain.getAgentId());
        Mockito.when(wmContractSignService
                .getAuditedPartyBSigner(contractDBList.get(1).getId()))
                .thenReturn(partyB);

        WmAgentContract wmAgentContract = new WmAgentContract();
        wmAgentContract.setValid(1);
        Mockito.when(wmAgentContractThriftService
                .getWmAgentContractByAgentId(wmPoiDomain.getAgentId()))
                .thenReturn(wmAgentContract);

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));

        Assert.assertEquals(StatusEnum.EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_代理非大连锁门店_C1过期_C2有效_C3有效_门店下线() throws Exception {

        List<WmTempletContractDB> contractDBList = Lists.newArrayList(ObjectMockUtils.getObject(WmTempletContractDB.class, 0),
                ObjectMockUtils.getObject(WmTempletContractDB.class, 2));

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode())
                )
        ).thenReturn(contractDBList);

        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        wmPoiDomain.setValid(0);
        wmPoiDomain.setAgentId((short) 1);
        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(wmPoiDomain);

        WmTempletContractSignBo partyB = new WmTempletContractSignBo();
        partyB.setSignId(wmPoiDomain.getAgentId());
        Mockito.when(wmContractSignService
                .getAuditedPartyBSigner(contractDBList.get(1).getId()))
                .thenReturn(partyB);

        WmAgentContract wmAgentContract = new WmAgentContract();
        wmAgentContract.setValid(1);
        Mockito.when(wmAgentContractThriftService
                .getWmAgentContractByAgentId(wmPoiDomain.getAgentId()))
                .thenReturn(wmAgentContract);

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));

        Assert.assertEquals(StatusEnum.IN_EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_代理非大连锁门店_C1有效_C2有效_C3无效_门店未下线() throws Exception {

        List<WmTempletContractDB> contractDBList = Lists.newArrayList(ObjectMockUtils.getObject(WmTempletContractDB.class, 1),
                ObjectMockUtils.getObject(WmTempletContractDB.class, 2)
        );

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode())
                )
        ).thenReturn(contractDBList);

        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        wmPoiDomain.setValid(0);
        wmPoiDomain.setAgentId((short) 1);
        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(wmPoiDomain);

        WmTempletContractSignBo partyB = new WmTempletContractSignBo();
        partyB.setSignId(wmPoiDomain.getAgentId());
        Mockito.when(wmContractSignService
                .getAuditedPartyBSigner(contractDBList.get(1).getId()))
                .thenReturn(partyB);

        WmAgentContract wmAgentContract = new WmAgentContract();
        wmAgentContract.setValid(0);
        Mockito.when(wmAgentContractThriftService
                .getWmAgentContractByAgentId(wmPoiDomain.getAgentId()))
                .thenReturn(wmAgentContract);

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));

        Assert.assertEquals(StatusEnum.IN_EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_代理大连锁门店_C1有效_C2有效_C3有效_门店未下线() throws Exception {

        List<WmTempletContractDB> contractDBList = Lists.newArrayList(
                ObjectMockUtils.getObject(WmTempletContractDB.class, 1));

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode())
                )
        ).thenReturn(contractDBList);

        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        wmPoiDomain.setValid(1);
        wmPoiDomain.setAgentId((short) 1);
        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(wmPoiDomain);

        Mockito.when(wmPoiClient.isKAPoi(wmPoiDomain))
                .thenReturn(true);

        WmAgentContract wmAgentContract = new WmAgentContract();
        wmAgentContract.setValid(1);
        Mockito.when(wmAgentContractThriftService
                .getWmAgentContractByAgentId(wmPoiDomain.getAgentId()))
                .thenReturn(wmAgentContract);

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));

        Assert.assertEquals(StatusEnum.EFFECTIVE, customerModuleStatus.getStatusEnum());
    }

    @Test
    public void 门店上线检查_代理大连锁门店_C1有效_无C2_C3有效_门店未下线() throws Exception {

        List<WmTempletContractDB> contractDBList = Lists.newArrayList(ObjectMockUtils.getObject(WmTempletContractDB.class, 1));

        Mockito.when(wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(10014392, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode())
                )
        ).thenReturn(contractDBList);

        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        wmPoiDomain.setValid(0);
        wmPoiDomain.setAgentId((short) 1);
        Mockito.when(wmPoiClient.getWmPoiById(10000188))
                .thenReturn(wmPoiDomain);

        Mockito.when(wmPoiClient.isKAPoi(wmPoiDomain))
                .thenReturn(true);

        WmAgentContract wmAgentContract = new WmAgentContract();
        wmAgentContract.setValid(1);
        Mockito.when(wmAgentContractThriftService
                .getWmAgentContractByAgentId(wmPoiDomain.getAgentId()))
                .thenReturn(wmAgentContract);

        CustomerModuleStatus customerModuleStatus = wmContractService
                .checkContractForPoiSetup(10014392, 10000188);
        System.out.println("门店上线检查点" + JSON.toJSON(customerModuleStatus));

        Assert.assertEquals(StatusEnum.EFFECTIVE, customerModuleStatus.getStatusEnum());
    }
}
*/
