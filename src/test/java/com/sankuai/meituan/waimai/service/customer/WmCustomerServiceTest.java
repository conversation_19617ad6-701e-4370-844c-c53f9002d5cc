package com.sankuai.meituan.waimai.service.customer;


import cn.hutool.core.util.RandomUtil;
import com.sankuai.meituan.scmbrand.thrift.constant.OwnerTypeEnum;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.adapter.WmQuaAdapter;
import com.sankuai.meituan.waimai.customer.adapter.dto.QueryRepeatQuaWmPoiIdDTO;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.dto.CustomerMscUsedPoiDetailDTO;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiValidEnum;
import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
//import org.junit.jupiter.api.extension.ExtensionContext;
//import org.junit.jupiter.params.ParameterizedTest;
//import org.junit.jupiter.params.provider.*;
import org.junit.runners.Parameterized;
import org.mockito.*;
import com.sankuai.meituan.waimai.customer.service.customer.dto.MscPoiInfoDTO;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.Assert.*;
import org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class WmCustomerServiceTest extends BaseStaticMockTest {

    @InjectMocks
    private WmCustomerService wmCustomerService;

    @Mock
    private IWmCustomerRealService wmLeafCustomerRealService;

    @Mock
    private WmQuaAdapter wmQuaAdapter;

    @Mock
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Mock
    private WmEmployeeService employeeService;

    @Mock
    private WmCustomerGrayService wmCustomerGrayService;

    


    public List<WmPoiAggre> buidWmPoiList(){
        List<WmPoiAggre> wmPoiList = new ArrayList<>();
        WmPoiAggre wmPoiAggre1 = new WmPoiAggre();
        wmPoiAggre1.setPoi_id(1L);
        wmPoiAggre1.setValid(0);
        wmPoiAggre1.setOwner_uid(1000);
        wmPoiAggre1.setOrigin_brand_id(1000);
        wmPoiAggre1.setBrand_id(1000);
        wmPoiAggre1.setSeller_uid(1000);
        wmPoiAggre1.setOwner_type(1);
        wmPoiAggre1.setSub_wm_poi_type(0);
        wmPoiAggre1.setBiz_org_code(PoiOrgEnum.WAI_MAI.getCode());

        WmPoiAggre wmPoiAggre2 = new WmPoiAggre();
        wmPoiAggre2.setPoi_id(2L);
        wmPoiAggre2.setValid(1);
        wmPoiAggre2.setSub_wm_poi_type(0);
        wmPoiAggre2.setOwner_uid(1000);
        wmPoiAggre2.setOrigin_brand_id(1000);
        wmPoiAggre2.setBrand_id(1000);
        wmPoiAggre2.setSeller_uid(1000);
        wmPoiAggre2.setOwner_type(1);
        wmPoiAggre1.setBiz_org_code(PoiOrgEnum.WAI_MAI.getCode());

        WmPoiAggre wmPoiAggre3 = new WmPoiAggre();
        wmPoiAggre3.setPoi_id(3L);
        wmPoiAggre3.setValid(2);
        wmPoiAggre3.setSub_wm_poi_type(1);
        wmPoiAggre3.setOwner_uid(1000);
        wmPoiAggre3.setOrigin_brand_id(1000);
        wmPoiAggre3.setBrand_id(1000);
        wmPoiAggre3.setSeller_uid(1000);
        wmPoiAggre3.setOwner_type(1);
        wmPoiAggre3.setBiz_org_code(PoiOrgEnum.WAI_MAI.getCode());

        wmPoiList.add(wmPoiAggre1);
        wmPoiList.add(wmPoiAggre2);
        wmPoiList.add(wmPoiAggre3);
        return wmPoiList;
    }


    /**
     * 测试客户为美食城类型，且能正确查询到客户信息和共用资质门店列表的情况
     */
    @Test
    public void testGetMscUsedPoiDetailDTOV2_Success() throws Throwable {
        WmCustomerDB mockCustomerDB = new WmCustomerDB();
        mockCustomerDB.setId(1);
        mockCustomerDB.setCustomerRealType(2); // 美食城类型
        mockCustomerDB.setCustomerRealTypeSpInfo("{\"foodCityPoiCount\":10}");
        mockCustomerDB.setCustomerType(1);
        mockCustomerDB.setCustomerSecondType(1);
        mockCustomerDB.setCustomerNumber("130110100010001111");
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::mscUsedPoiCntNewQuaInterfaceSwitch).thenReturn(false);

        //待讨论问题：参数转换相关逻辑是否要单独进行测试？
        when(wmQuaAdapter.getRepeatWmPoiId(ArgumentMatchers.argThat(new ArgumentMatcher<QueryRepeatQuaWmPoiIdDTO>() {
            @Override
            public boolean matches(QueryRepeatQuaWmPoiIdDTO argument) {
                return (((QueryRepeatQuaWmPoiIdDTO) argument).getNumber().equals(mockCustomerDB.getCustomerNumber())
                        && ((QueryRepeatQuaWmPoiIdDTO) argument).getSubType() == 1 //资质主体类型映射
                        && ((QueryRepeatQuaWmPoiIdDTO) argument).getSecondSubType() == 0 //资质主体子类型映射
                );
            }
        }))).thenReturn(Arrays.asList(1,2,3));

        when(wmLeafCustomerRealService.selectPlatformCustomerByIdFromSlave(1)).thenReturn(mockCustomerDB);
        when(wmPoiQueryAdapter.getWmPoiAggre(ArgumentMatchers.anyList(), ArgumentMatchers.any())).thenReturn(buidWmPoiList());
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(ArgumentMatchers.anyInt())).thenReturn(true);

        CustomerMscUsedPoiDetailDTO result = wmCustomerService.getMscUsedPoiDetailDTOV2(1);

        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getCustomerId());
        assertEquals(Integer.valueOf(10), result.getAllPoiCnt());
        assertEquals(1, result.getUsedPoiCnt().intValue());
    }

//    /**
//     * 测试客户不存在的情况
//     */
//    @ParameterizedTest
//    @ArgumentsSource(ExceptionArgumentProvider.class)
//    public void testGetMscUsedPoiDetailDTOV2_CustomerNotFound(Integer customerId,WmCustomerDB mockObject,String expectErrorMsg) throws Throwable {
//        when(wmLeafCustomerRealService.selectPlatformCustomerByIdFromSlave(customerId)).thenReturn(mockObject);
//
//        Exception exception = Assert.assertThrows(WmCustomerException.class,
//                ()->{wmCustomerService.getMscUsedPoiDetailDTOV2(customerId);
//        });
//        Assert.assertEquals(expectErrorMsg, exception.getMessage());
//    }

    /**
     * 测试客户类型不是美食城的情况
     */
    @Test
    public void testGetMscUsedPoiDetailDTOV2_NotMscCustomer() throws Throwable {
        WmCustomerDB mockCustomerDB = new WmCustomerDB();
        mockCustomerDB.setId(1);
        mockCustomerDB.setCustomerRealType(1); // 美食城类型
        mockCustomerDB.setCustomerRealTypeSpInfo("{\"foodCityPoiCount\":10}");
        mockCustomerDB.setCustomerType(1);
        mockCustomerDB.setCustomerSecondType(1);
        mockCustomerDB.setCustomerNumber("130110100010001111");
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::mscUsedPoiCntNewQuaInterfaceSwitch).thenReturn(false);

        //待讨论问题：参数转换相关逻辑是否要单独进行测试？
        when(wmQuaAdapter.getRepeatWmPoiId(ArgumentMatchers.argThat(new ArgumentMatcher<QueryRepeatQuaWmPoiIdDTO>() {
            @Override
            public boolean matches(QueryRepeatQuaWmPoiIdDTO argument) {
                return (((QueryRepeatQuaWmPoiIdDTO) argument).getNumber().equals(mockCustomerDB.getCustomerNumber())
                        && ((QueryRepeatQuaWmPoiIdDTO) argument).getSubType() == 1 //资质主体类型映射
                        && ((QueryRepeatQuaWmPoiIdDTO) argument).getSecondSubType() == 0 //资质主体子类型映射
                );
            }
        }))).thenReturn(Arrays.asList(1,2,3));

        when(wmLeafCustomerRealService.selectPlatformCustomerByIdFromSlave(1)).thenReturn(mockCustomerDB);
        when(wmPoiQueryAdapter.getWmPoiAggre(ArgumentMatchers.anyList(), ArgumentMatchers.any())).thenReturn(buidWmPoiList());
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(ArgumentMatchers.anyInt())).thenReturn(true);


        CustomerMscUsedPoiDetailDTO result = wmCustomerService.getMscUsedPoiDetailDTOV2(1);

        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getCustomerId());
        assertNull(result.getAllPoiCnt());
        assertEquals(1, result.getUsedPoiCnt().intValue());
    }

    /**
     * 测试统计美食城共用资质门店时排除非外卖门店
     */
    @Test
    @SneakyThrows
    public void testGetMscUsedPoiDetailDTOV2_filterNotWmPoi() {

        WmCustomerDB mockCustomerDB = new WmCustomerDB();
        mockCustomerDB.setId(1);
        mockCustomerDB.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue()); // 美食城类型
        mockCustomerDB.setCustomerRealTypeSpInfo("{\"foodCityPoiCount\":10}");
        mockCustomerDB.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        mockCustomerDB.setCustomerSecondType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        mockCustomerDB.setCustomerNumber("130110100010001111");
        when(wmLeafCustomerRealService.selectPlatformCustomerByIdFromSlave(1)).thenReturn(mockCustomerDB);

        //TODO: 确认下面的mock是否满足预期
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::mscUsedPoiCntNewQuaInterfaceSwitch).thenReturn(false);

        //待讨论问题：参数转换相关逻辑是否要单独进行测试？
        when(wmQuaAdapter.getRepeatWmPoiId(ArgumentMatchers.argThat(new ArgumentMatcher<QueryRepeatQuaWmPoiIdDTO>() {
            @Override
            public boolean matches(QueryRepeatQuaWmPoiIdDTO argument) {
                return (((QueryRepeatQuaWmPoiIdDTO) argument).getNumber().equals(mockCustomerDB.getCustomerNumber())
                        && ((QueryRepeatQuaWmPoiIdDTO) argument).getSubType() == 1 //资质主体类型映射
                        && ((QueryRepeatQuaWmPoiIdDTO) argument).getSecondSubType() == 0 //资质主体子类型映射
                );
            }
        }))).thenReturn(Arrays.asList(1,2,3));


        List<WmPoiAggre> wmPoiAggres = buidWmPoiList();

        WmPoiAggre wmPoiAggreShangou = new WmPoiAggre();
        wmPoiAggreShangou.setPoi_id(RandomUtil.randomLong());
        // 上单中
        wmPoiAggreShangou.setValid(WmPoiValidEnum.CREATING.getValue());
        // 非子门店
        wmPoiAggreShangou.setSub_wm_poi_type(1);
        wmPoiAggreShangou.setOwner_uid(1000);
        wmPoiAggreShangou.setOrigin_brand_id(1000);
        wmPoiAggreShangou.setBrand_id(1000);
        wmPoiAggreShangou.setSeller_uid(1000);
        wmPoiAggreShangou.setOwner_type(1);
        // 闪购类型
        wmPoiAggreShangou.setBiz_org_code(PoiOrgEnum.SHAN_GOU.getCode());
        wmPoiAggres.add(wmPoiAggreShangou);

        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(ArgumentMatchers.anyInt())).thenReturn(true);
        when(wmPoiQueryAdapter.getWmPoiAggre(ArgumentMatchers.anyList(), ArgumentMatchers.any())).thenReturn(buidWmPoiList());
        CustomerMscUsedPoiDetailDTO result = wmCustomerService.getMscUsedPoiDetailDTOV2(1);

        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getUsedPoiCnt());
        assertEquals(Integer.valueOf(1), result.getCustomerId());
        assertEquals(Integer.valueOf(10), result.getAllPoiCnt());
    }

    /**
     * 测试共用资质门店列表为空的情况
     */
    @Test
    public void testGetMscUsedPoiDetailDTOV2_NoRepeatWmPoiId() throws Throwable {
        WmCustomerDB mockCustomerDB = new WmCustomerDB();
        mockCustomerDB.setId(1);
        mockCustomerDB.setCustomerRealType(2); // 美食城类型
        mockCustomerDB.setCustomerRealTypeSpInfo("{\"foodCityPoiCount\":10}");
        mockCustomerDB.setCustomerType(1);
        mockCustomerDB.setCustomerSecondType(1);
        mockCustomerDB.setCustomerNumber("130110100010001111");

        when(wmLeafCustomerRealService.selectPlatformCustomerByIdFromSlave(1)).thenReturn(mockCustomerDB);
        when(wmQuaAdapter.getRepeatWmPoiId(ArgumentMatchers.any(QueryRepeatQuaWmPoiIdDTO.class))).thenReturn(Collections.emptyList());

        CustomerMscUsedPoiDetailDTO result = wmCustomerService.getMscUsedPoiDetailDTOV2(1);

        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getCustomerId());
        assertEquals(Integer.valueOf(10), result.getAllPoiCnt());
        assertEquals(0, result.getUsedPoiCnt().intValue());
    }

    /**
     * 测试公用资质门店不为空，但是过滤poiAggre为空
     */
    @Test
    public void testGetMscUsedPoiDetailDTOV2_NoPoiAggre() throws Throwable {
        WmCustomerDB mockCustomerDB = new WmCustomerDB();
        mockCustomerDB.setId(1);
        mockCustomerDB.setCustomerRealType(2); // 美食城类型
        mockCustomerDB.setCustomerRealTypeSpInfo("{\"foodCityPoiCount\":10}");
        mockCustomerDB.setCustomerType(1);
        mockCustomerDB.setCustomerSecondType(1);
        mockCustomerDB.setCustomerNumber("130110100010001111");


        when(wmLeafCustomerRealService.selectPlatformCustomerByIdFromSlave(1)).thenReturn(mockCustomerDB);
        when(wmQuaAdapter.getRepeatWmPoiId(ArgumentMatchers.any(QueryRepeatQuaWmPoiIdDTO.class))).thenReturn(Arrays.asList(1, 2, 3));
        when(wmPoiQueryAdapter.getWmPoiAggre(ArgumentMatchers.anyList(), ArgumentMatchers.any())).thenReturn(Collections.emptyList());
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(ArgumentMatchers.anyInt())).thenReturn(true);


        CustomerMscUsedPoiDetailDTO result = wmCustomerService.getMscUsedPoiDetailDTOV2(1);

        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getCustomerId());
        assertEquals(Integer.valueOf(10), result.getAllPoiCnt());
        assertEquals(0, result.getUsedPoiCnt().intValue());
    }

    /**
     * 个人证件类型，不计算已占用档口数，返回-1
     */
    @Test
    public void testGetMscUsedPoiDetailDTOV2_person() throws Throwable {
        WmCustomerDB mockCustomerDB = new WmCustomerDB();
        mockCustomerDB.setId(1);
        mockCustomerDB.setCustomerRealType(2); // 美食城类型
        mockCustomerDB.setCustomerRealTypeSpInfo("{\"foodCityPoiCount\":10}");
        mockCustomerDB.setCustomerType(2);
        mockCustomerDB.setCustomerSecondType(1);
        mockCustomerDB.setCustomerNumber("130110100010001111");


        when(wmLeafCustomerRealService.selectPlatformCustomerByIdFromSlave(1)).thenReturn(mockCustomerDB);
        when(wmQuaAdapter.getRepeatWmPoiId(ArgumentMatchers.any(QueryRepeatQuaWmPoiIdDTO.class))).thenReturn(Arrays.asList(1, 2, 3));
        when(wmPoiQueryAdapter.getWmPoiAggre(ArgumentMatchers.anyList(), ArgumentMatchers.any())).thenReturn(Collections.emptyList());

        CustomerMscUsedPoiDetailDTO result = wmCustomerService.getMscUsedPoiDetailDTOV2(1);

        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getCustomerId());
        assertEquals(Integer.valueOf(10), result.getAllPoiCnt());
//        assertEquals(1, result.getUsedPoiCnt().intValue());
    }

    /**
     * 测试 fillOwnerInfo 方法，当 mscPoiInfoDTOList 为空时
     */
    @Test
    public void testFillOwnerInfoWithEmptyList() throws Exception {
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();

        wmCustomerService.fillOwnerInfo(mscPoiInfoDTOList);

        assertTrue(mscPoiInfoDTOList.isEmpty());
    }

    /**
     * 测试 fillOwnerInfo 方法，当 mscPoiInfoDTOList 不为空，但没有匹配的员工信息时
     */
    @Test
    public void testFillOwnerInfoWithNoMatchingEmployees() throws Exception {
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO dto = new MscPoiInfoDTO();
        dto.setPoiOwnerUid(1L);
        dto.setPoiSellerUid(2);
        mscPoiInfoDTOList.add(dto);

        when(employeeService.mgetByUids(ArgumentMatchers.anyList())).thenReturn(new ArrayList<>());

        wmCustomerService.fillOwnerInfo(mscPoiInfoDTOList);

        assertNull(mscPoiInfoDTOList.get(0).getPoiOwnerMis());
        assertNull(mscPoiInfoDTOList.get(0).getPoiSellerMis());
    }

    /**
     * 测试 fillOwnerInfo 方法，当 mscPoiInfoDTOList 不为空，并且有匹配的员工信息时
     */
    @Test
    public void testFillOwnerInfoWithMatchingEmployees() throws Exception {
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO dto = new MscPoiInfoDTO();
        dto.setPoiOwnerUid(1L);
        dto.setPoiSellerUid(2);
        mscPoiInfoDTOList.add(dto);

        List<WmEmploy> wmEmploys = new ArrayList<>();
        WmEmploy employ1 = new WmEmploy();
        employ1.setUid(1);
        employ1.setMisId("ownerMis");
        employ1.setName("ownerName");
        WmEmploy employ2 = new WmEmploy();
        employ2.setUid(2);
        employ2.setMisId("sellerMis");
        employ2.setName("sellerName");
        wmEmploys.add(employ1);
        wmEmploys.add(employ2);

        when(employeeService.mgetByUids(ArgumentMatchers.anyList())).thenReturn(wmEmploys);

        wmCustomerService.fillOwnerInfo(mscPoiInfoDTOList);

        assertEquals("ownerMis", mscPoiInfoDTOList.get(0).getPoiOwnerMis());
        assertEquals("sellerMis", mscPoiInfoDTOList.get(0).getPoiSellerMis());
    }

//    static class ExceptionArgumentProvider implements ArgumentsProvider{
//
//        @Override
//        public Stream<? extends Arguments> provideArguments(ExtensionContext extensionContext) throws Exception {
//            WmCustomerDB wmCustomerDB = new WmCustomerDB();
//            wmCustomerDB.setCustomerRealType(1);
//            return Stream.of(
//                    Arguments.arguments(1, null,"未查询到客户"),
//                    Arguments.arguments(1,wmCustomerDB,"该客户类型不是美食城客户"));
//        }
//    }

}
