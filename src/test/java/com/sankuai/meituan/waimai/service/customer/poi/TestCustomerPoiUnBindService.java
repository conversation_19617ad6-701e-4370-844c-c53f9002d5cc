package com.sankuai.meituan.waimai.service.customer.poi;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.config.configuration.MccConfiguration;
import com.sankuai.meituan.config.configuration.PropertiesConfiguration;
import com.sankuai.meituan.config.configuration.SystemConfiguration;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.adapter.StateCenterAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiOplogThriftServiceAdaptor;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindDecideResultEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnbindConfirmBo;
import com.sankuai.meituan.waimai.customer.domain.task.WmCustomerTask;
import com.sankuai.meituan.waimai.customer.mq.service.CustomerPoiSendService;
import com.sankuai.meituan.waimai.customer.service.companycustomer.CompanyCustomerSyncService;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TestCustomerPoiUnBindService {
    @InjectMocks
    private CustomerPoiUnBindService customerPoiUnBindService;

    @Mock
    private CustomerPoiBaseValidator customerPoiBaseValidator;

    @Mock
    private WmCustomerPoiOplogService wmCustomerPoiOplogService;

    @Mock
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Mock
    private WmCustomerService wmCustomerService;

    @Mock
    private CustomerTaskService customerTaskService;

    @Mock
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Mock
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Mock
    private StateCenterAdapter stateCenterAdapter;

    @Mock
    private WmContractService wmContractService;

    @Mock
    private WmPoiOplogThriftServiceAdaptor wmPoiOplogThriftServiceAdaptor;

    @Mock
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Mock
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Mock
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Mock
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Mock
    private CompanyCustomerSyncService companyCustomerSyncService;

    @Mock
    private CustomerPoiSendService customerPoiSendService;

    static {
        System.setProperty("workdir", "target");
        ConfigUtilAdapter.addConfiguration(new MccConfiguration("com.sankuai.waimai.e.customer", "", ""));
        ConfigUtilAdapter.addConfiguration(new SystemConfiguration());
        ConfigUtilAdapter.addConfiguration(new PropertiesConfiguration(System.getProperty("workdir") + "/classes/conf"));
        ConfigUtilAdapter.init();
    }

    @Test
    public void testUnBind() throws TException, WmCustomerException, IllegalAccessException {
        // 参数合法性校验
        WmCustomerPoiUnBindParamBo wmCustomerPoiUnBindParamBo = new WmCustomerPoiUnBindParamBo();
        Exception exception = Assert.assertThrows(WmCustomerException.class, () -> {
            customerPoiUnBindService.unBind(wmCustomerPoiUnBindParamBo);
        });
        Assert.assertTrue(exception.getMessage().equals("参数不合法"));

        // 结果map为空情况
        wmCustomerPoiUnBindParamBo.setCustomerId(1);
        wmCustomerPoiUnBindParamBo.setOpUid(100);
        wmCustomerPoiUnBindParamBo.setWmPoiIdSet(Sets.newHashSet(1L));
        wmCustomerPoiUnBindParamBo.setSourceTypeEnum(CustomerTaskSourceEnum.UN_KNOWN);
        wmCustomerPoiUnBindParamBo.setTypeEnum(CustomerPoiUnBindTypeEnum.DIRECT_UNBIND);
        when(customerPoiBaseValidator.checkUnbind(ArgumentMatchers.any())).thenReturn(Maps.newHashMap());
        exception = Assert.assertThrows(WmCustomerException.class, () -> {
            customerPoiUnBindService.unBind(wmCustomerPoiUnBindParamBo);
        });
        Assert.assertTrue(exception.getMessage().equals("系统异常"));

        Set<Long> wmPoiIdSet = new HashSet<>();
        wmPoiIdSet.add(1L);
        wmPoiIdSet.add(2L);

        // 预绑定解绑
        Map<CustomerPoiUnBindDecideResultEnum, Set<Long>> map = new HashMap<>();
        map.put(CustomerPoiUnBindDecideResultEnum.CAN_PRE_BIND_FAIL_UNBIND, wmPoiIdSet);
        when(customerPoiBaseValidator.checkUnbind(ArgumentMatchers.any())).thenReturn(map);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        when(wmCustomerService.selectCustomerById(ArgumentMatchers.anyInt())).thenReturn(wmCustomerDB);
        Map<Long, Integer> poiAndTaskMaps = Maps.newHashMap();
        poiAndTaskMaps.put(1L, 1);
        poiAndTaskMaps.put(2L, 2);
        when(customerTaskService.batchAddCustomerUnBindTask(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(poiAndTaskMaps);
        String result = customerPoiUnBindService.unBind(wmCustomerPoiUnBindParamBo);
        Assert.assertTrue(result.equals(CustomerConstants.CUSTOMER_POI_UNBIND_SUCCESS));

        // 直接解绑
        map.clear();
        map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT, wmPoiIdSet);
        when(customerPoiBaseValidator.checkUnbind(ArgumentMatchers.any())).thenReturn(map);
        when(wmCustomerService.selectCustomerById(ArgumentMatchers.anyInt())).thenReturn(wmCustomerDB);
        when(wmCustomerPoiOplogService.getOpSourceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(WmCustomerPoiOplogSourceTypeEnum.UNKNOW);
        when(customerTaskService.batchAddCustomerUnBindTask(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(poiAndTaskMaps);
        result = customerPoiUnBindService.unBind(wmCustomerPoiUnBindParamBo);
        Assert.assertTrue(result.equals(CustomerConstants.CUSTOMER_POI_UNBIND_SUCCESS));

        // 发起电子签约
        map.clear();
        map.put(CustomerPoiUnBindDecideResultEnum.NEED_SIGN_TO_UNBIND, wmPoiIdSet);
        when(customerPoiBaseValidator.checkUnbind(ArgumentMatchers.any())).thenReturn(map);
        when(wmCustomerService.selectCustomerById(ArgumentMatchers.anyInt())).thenReturn(wmCustomerDB);
        when(wmCustomerPoiOplogService.getOpSourceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(WmCustomerPoiOplogSourceTypeEnum.UNKNOW);
        when(customerTaskService.batchAddCustomerUnBindTask(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(poiAndTaskMaps);
        when(wmEcontractSignBzService.applyTask(ArgumentMatchers.any())).thenReturn(new LongResult(1L));
        result = customerPoiUnBindService.unBind(wmCustomerPoiUnBindParamBo);
        Assert.assertTrue(result.equals(CustomerConstants.CUSTOMER_POI_UNBIND_NEED_CHECK));


        // 解绑并取消流程中任务
        map.clear();
        map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_AND_CANCEL_SIGN, wmPoiIdSet);
        when(customerPoiBaseValidator.checkUnbind(ArgumentMatchers.any())).thenReturn(map);
        when(wmCustomerService.selectCustomerById(ArgumentMatchers.anyInt())).thenReturn(wmCustomerDB);
        when(wmCustomerPoiOplogService.getOpSourceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(WmCustomerPoiOplogSourceTypeEnum.UNKNOW);
        when(customerTaskService.batchAddCustomerUnBindTask(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(poiAndTaskMaps);
        List<WmCustomerPoiSmsRecordDB> wmCustomerPoiSmsRecordDBList = Lists.newArrayList();
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = new WmCustomerPoiSmsRecordDB();
        wmCustomerPoiSmsRecordDB.setCustomerId(1);
        wmCustomerPoiSmsRecordDB.setWmPoiIds("1");
        wmCustomerPoiSmsRecordDBList.add(wmCustomerPoiSmsRecordDB);
        when(wmCustomerPoiSmsRecordMapper.selectSmsRecordListByCustomerId(ArgumentMatchers.anyInt())).thenReturn(wmCustomerPoiSmsRecordDBList);
        result = customerPoiUnBindService.unBind(wmCustomerPoiUnBindParamBo);
        Assert.assertTrue(result.equals(CustomerConstants.CUSTOMER_POI_UNBIND_SUCCESS));

        // 纸质客户操作无权限
        map.clear();
        map.put(CustomerPoiUnBindDecideResultEnum.PAGE_UNBIND_NO_AUTH, wmPoiIdSet);
        when(customerPoiBaseValidator.checkUnbind(ArgumentMatchers.any())).thenReturn(map);
        when(wmCustomerService.selectCustomerById(ArgumentMatchers.anyInt())).thenReturn(wmCustomerDB);
        when(wmCustomerPoiOplogService.getOpSourceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(WmCustomerPoiOplogSourceTypeEnum.UNKNOW);
        when(customerTaskService.batchAddCustomerUnBindTask(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(poiAndTaskMaps);
        result = customerPoiUnBindService.unBind(wmCustomerPoiUnBindParamBo);
        Assert.assertTrue(result.equals(String.format(CustomerConstants.CUSTOMER_POI_UNBIND_NEED_AUTH,
                JSONObject.toJSONString(map.get(CustomerPoiUnBindDecideResultEnum.PAGE_UNBIND_NO_AUTH)))));


        // 直接解绑+发起电子签约+纸质客户操作无权限
        map.clear();
        map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT, Sets.newHashSet(1L));
        map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_AND_CANCEL_SIGN, Sets.newHashSet(2L));
        map.put(CustomerPoiUnBindDecideResultEnum.PAGE_UNBIND_NO_AUTH, Sets.newHashSet(3L));
        when(customerPoiBaseValidator.checkUnbind(ArgumentMatchers.any())).thenReturn(map);
        when(wmCustomerService.selectCustomerById(ArgumentMatchers.anyInt())).thenReturn(wmCustomerDB);
        when(wmCustomerPoiOplogService.getOpSourceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(WmCustomerPoiOplogSourceTypeEnum.UNKNOW);
        when(customerTaskService.batchAddCustomerUnBindTask(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(poiAndTaskMaps);
        List<WmCustomerPoiSmsRecordDB> wmCustomerPoiSmsRecordDBList1 = Lists.newArrayList();
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB1 = new WmCustomerPoiSmsRecordDB();
        wmCustomerPoiSmsRecordDB1.setCustomerId(1);
        wmCustomerPoiSmsRecordDB1.setWmPoiIds("2");
        wmCustomerPoiSmsRecordDBList1.add(wmCustomerPoiSmsRecordDB1);
        when(wmCustomerPoiSmsRecordMapper.selectSmsRecordListByCustomerId(ArgumentMatchers.anyInt())).thenReturn(wmCustomerPoiSmsRecordDBList1);
        result = customerPoiUnBindService.unBind(wmCustomerPoiUnBindParamBo);
        Assert.assertFalse(result.equals(String.format(CustomerConstants.CUSTOMER_POI_UNBIND_NEED_AUTH,
                JSONObject.toJSONString(map.get(CustomerPoiUnBindDecideResultEnum.PAGE_UNBIND_NO_AUTH)))));
    }

    @Test
    public void testPoiReleaseUnBind() throws TException, WmCustomerException {
        Set<Long> wmPoiIdSet = new HashSet<>();
        wmPoiIdSet.add(1L);
        wmPoiIdSet.add(2L);
        int opUid = 0;
        String opName = "测试";
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        when(wmCustomerService.selectCustomerByWmPoiId(ArgumentMatchers.anyLong())).thenReturn(wmCustomerDB);
        customerPoiUnBindService.poiReleaseUnBind(wmPoiIdSet, opUid, opName);
        verify(customerPoiBaseValidator, times(1)).validateInCustomerSwitchTask(wmPoiIdSet);
        verify(customerPoiBaseValidator, times(1)).validPreBindStatus(wmPoiIdSet);
    }

    @Test
    public void testConfirmUnBind() throws WmCustomerException {
        WmCustomerPoiUnbindConfirmBo wmCustomerPoiUnbindConfirmBo = new WmCustomerPoiUnbindConfirmBo();
        Exception exception = Assert.assertThrows(WmCustomerException.class, () -> {
            customerPoiUnBindService.confirmUnBind(wmCustomerPoiUnbindConfirmBo);

        });
        Assert.assertTrue(exception.getMessage().equals("参数不合法"));

        // 门店上线状态走拒绝解绑
        wmCustomerPoiUnbindConfirmBo.setCustomerId(1);
        Set<Long> wmPoiIdSet = new HashSet<>();
        wmPoiIdSet.add(1L);
        wmPoiIdSet.add(2L);
        wmCustomerPoiUnbindConfirmBo.setWmPoiIdSet(wmPoiIdSet);
        wmCustomerPoiUnbindConfirmBo.setOpUid(0);
        wmCustomerPoiUnbindConfirmBo.setOpName("测试");
        wmCustomerPoiUnbindConfirmBo.setUnBindTypeEnum(CustomerPoiUnBindTypeEnum.BD_FORCE_UNBIND);
        wmCustomerPoiUnbindConfirmBo.setOplogSourceTypeEnum(WmCustomerPoiOplogSourceTypeEnum.FORCE_UNBIND);
        wmCustomerPoiUnbindConfirmBo.setSmsId(1);
//        when(customerPoiBaseValidator.validWmPoiStatus(anySet())).thenThrow(new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
//                String.format("上线门店不可解绑（门店id：%s）", wmPoiIdSet)));
        customerPoiUnBindService.confirmUnBind(wmCustomerPoiUnbindConfirmBo);

        // 门店绑定客户不一致拒绝解绑
        reset(customerPoiBaseValidator);
        wmCustomerPoiUnbindConfirmBo.setUnBindTypeEnum(CustomerPoiUnBindTypeEnum.BD_FORCE_UNBIND);
        List<WmCustomerPoiDB> notBindCustomerIdList = Lists.newArrayList();
        WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
        wmCustomerPoiDB.setWmPoiId(1L);
        wmCustomerPoiDB.setCustomerId(1);
        notBindCustomerIdList.add(wmCustomerPoiDB);
        when(wmCustomerPoiRelService.selectNotCustomerIdByWmPoiIdsRT(ArgumentMatchers.anySet(), ArgumentMatchers.anyInt())).thenReturn(notBindCustomerIdList);
        customerPoiUnBindService.confirmUnBind(wmCustomerPoiUnbindConfirmBo);

        // 正常确认解绑
        reset(wmCustomerPoiRelService);
        wmCustomerPoiUnbindConfirmBo.setUnBindTypeEnum(CustomerPoiUnBindTypeEnum.BD_FORCE_UNBIND);
        List<WmCustomerTask> taskList = Lists.newArrayList();
        WmCustomerTask wmCustomerTask = new WmCustomerTask();
        wmCustomerTask.setBizId(1);
        wmCustomerTask.setId(1);
        taskList.add(wmCustomerTask);
//        when(customerTaskService.listDoingTaskByCusIdAndSignIdAndTaskType(ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt())).thenReturn(taskList);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
//        when(wmCustomerService.selectCustomerById(ArgumentMatchers.anyInt())).thenReturn(wmCustomerDB);
        customerPoiUnBindService.confirmUnBind(wmCustomerPoiUnbindConfirmBo);
    }


    @Test
    public void testCancelUnBind() throws WmCustomerException {
        WmCustomerPoiUnbindConfirmBo wmCustomerPoiUnbindConfirmBo = new WmCustomerPoiUnbindConfirmBo();
        Exception exception = Assert.assertThrows(WmCustomerException.class, () -> {
            customerPoiUnBindService.cancelUnBind(wmCustomerPoiUnbindConfirmBo);

        });
        Assert.assertTrue(exception.getMessage().equals("参数不合法"));

        // 操作类型不合法
        wmCustomerPoiUnbindConfirmBo.setCustomerId(1);
        Set<Long> wmPoiIdSet = new HashSet<>();
        wmPoiIdSet.add(1L);
        wmPoiIdSet.add(2L);
        wmCustomerPoiUnbindConfirmBo.setWmPoiIdSet(wmPoiIdSet);
        wmCustomerPoiUnbindConfirmBo.setOpUid(0);
        wmCustomerPoiUnbindConfirmBo.setOpName("测试");
        wmCustomerPoiUnbindConfirmBo.setUnBindTypeEnum(CustomerPoiUnBindTypeEnum.BD_FORCE_UNBIND);
        wmCustomerPoiUnbindConfirmBo.setOplogSourceTypeEnum(WmCustomerPoiOplogSourceTypeEnum.FORCE_UNBIND);
        wmCustomerPoiUnbindConfirmBo.setSmsId(1);
        exception = Assert.assertThrows(WmCustomerException.class, () -> {
            customerPoiUnBindService.cancelUnBind(wmCustomerPoiUnbindConfirmBo);
        });
        Assert.assertTrue(exception.getMessage().equals("参数不合法"));


        // 取消解绑
        wmCustomerPoiUnbindConfirmBo.setUnBindTypeEnum(CustomerPoiUnBindTypeEnum.BD_CANCEL_UNBIND);
        wmCustomerPoiUnbindConfirmBo.setOplogSourceTypeEnum(null);
        wmCustomerPoiUnbindConfirmBo.setSmsId(0);
        customerPoiUnBindService.cancelUnBind(wmCustomerPoiUnbindConfirmBo);
    }


}
