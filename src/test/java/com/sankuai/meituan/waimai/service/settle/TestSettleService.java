//package com.sankuai.meituan.waimai.service.settle;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Function;
//import com.google.common.collect.Lists;
//import com.sankuai.meituan.common.time.TimeUtil;
////import com.sankuai.meituan.mtmq.service.Producer;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
//import com.sankuai.meituan.waimai.customer.service.WmCustomerKpThriftServiceImpl;
//import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleAuditedDBMapper;
//import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleDBMapper;
//import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper;
//import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
//import com.sankuai.meituan.waimai.customer.settle.domain.*;
//import com.sankuai.meituan.waimai.customer.settle.message.WmSettleMsgSender;
//import com.sankuai.meituan.waimai.customer.settle.service.WmSettleCleanService;
//import com.sankuai.meituan.waimai.customer.settle.service.WmSettleDiffService;
//import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
//import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
//import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.ChangeInfoContentBo;
//import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
//import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
//import com.sankuai.meituan.waimai.infra.service.WmEmployService;
//import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
//import com.sankuai.meituan.waimai.settle.WmSettleMsgBo;
//import com.sankuai.meituan.waimai.settle.WmSettleMsgBo.OpType;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
//import java.util.List;
//import javax.annotation.Nullable;
//import javax.annotation.Resource;
//
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.Predicate;
//import org.apache.commons.lang3.StringUtils;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleWalletCleanLog;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.Predicate;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.thrift.TException;
//
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleWalletCleanLog;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
//import org.apache.thrift.TException;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class TestSettleService extends BaseSpringJunit {
//    private static final Logger LOGGER = LoggerFactory.getLogger(TestSettleService.class);
//    @Autowired
//    private WmSettleDBMapper wmSettleDBMapper;
//    @Autowired
//    private WmSettleAuditedDBMapper wmSettleAuditedDBMapper;
//    @Autowired
//    private WmPoiSettleDBMapper wmPoiSettleDBMapper;
//    @Autowired
//    private WmPoiSettleAuditedDBMapper wmPoiSettleAuditedDBMapper;
//    @Autowired
//    private WmSettleManagerService wmSettleManagerService;
//    @Autowired
//    private WmSettleMsgSender wmSettleMsgSender;
////    @Resource
////    private Producer producer;
//    @Autowired
//    private WmSettleService wmSettleService;
//    @Autowired
//    private WmCustomerKpThriftServiceImpl kpThriftService;
//    @Autowired
//    private WmSettleCleanService wmSettleCleanService;
//    @Autowired
//    private WmEmployService.Iface wmEmployService;
//    @Autowired
//    private WmSettleDiffService wmSettleDiffService;
//
//
//
//
//    @Test
//    public void testWmSettleDBMapperRead(){
//        int id = 10000730;
//        int wmCustomerId = 123456;
//        WmSettleDB wmSettleDB = wmSettleDBMapper.getById(id);
//        LOGGER.info(JSONObject.toJSONString(wmSettleDB));
//        List<WmSettleDB> wmSettleDB1 = wmSettleDBMapper.getWmSettleByCustomerId(wmCustomerId);
//        LOGGER.info(JSONObject.toJSONString(wmSettleDB1));
//
//        List<Integer> idList = wmSettleDBMapper.getWmSettleIdListByCustomerId(wmCustomerId);
//        LOGGER.info(JSONObject.toJSONString(idList));
//
//        List<WmSettleDB> wmSettleDB2 = wmSettleDBMapper.getWmSettleListByWmSettleIdListWithSort(idList);
//        LOGGER.info(JSONObject.toJSONString(wmSettleDB2));
//
//        Byte status = wmSettleDBMapper.getWmSettleStatusByWmSettleId(wmCustomerId);
//        LOGGER.info(JSONObject.toJSONString(status));
//    }
//
//    @Test
//    public void testWmSettleDBMapperWrite(){
//        WmSettleDB record = new WmSettleDB();
//        record.setWm_contract_id(*********);
//        record.setWmCustomerId(*********);
//        record.setMin_pay_amount(100.00F);
//        record.setPay_period_num(2);
//        record.setPay_period_unit((byte)1);
//        record.setProvince(1001);
//        record.setCity(2001);
//        record.setBankid((short)99);
//        record.setBranchid(1234);
//        record.setBranchname("branchName");
//        record.setAcc_cardno("qweqwe");
//        record.setAcc_name("namename");
//        record.setAcctype((byte)1);
//        record.setValid((byte)1);
//        record.setCuid(110);
//        record.setMuid(110);
//        record.setCtime(TimeUtil.unixtime());
//        record.setUtime(TimeUtil.unixtime());
//        record.setSettle_type((byte)1);
//        record.setPay_day_of_month((byte)1);
//        record.setParty_a_finance_people("zhangsan");
//        record.setParty_a_finance_phone("11010");
//        record.setCert_type((short)1);
//        record.setLegal_cert_num("2001");
//        record.setLegal_person("legal_persion");
//        record.setLegal_id_card("99999");
//        record.setCert_num("66666");
//        record.setReserve_phone("234234");
//        record.setCard_type(1);
//        record.setStatus((byte)4);
//        int result = wmSettleDBMapper.insertSelective(record);
//        LOGGER.info(JSONObject.toJSONString(result));
//
//        result = wmSettleDBMapper.updateStatusByWmCustomerId(*********,(byte)13);
//        LOGGER.info(JSONObject.toJSONString(result));
//    }
//
//    @Test
//    public void testWmSettleAuditedDBMapperRead(){
//        int customerId = 100720;
//        int wmSettleId = 10000721;
//
//        List<WmSettleAuditedDB> auditedList = wmSettleAuditedDBMapper
//            .getByWmCustomerId(customerId);
//        LOGGER.info(JSONObject.toJSONString(auditedList));
//
//        List<Integer> wmSettleIdListByWmCustomerId = wmSettleAuditedDBMapper
//            .getWmSettleIdListByWmCustomerId(customerId);
//        LOGGER.info(JSONObject.toJSONString(wmSettleIdListByWmCustomerId));
//
//        WmSettleAuditedDB settleAuditedBySettleId = wmSettleAuditedDBMapper
//            .getSettleAuditedBySettleId(wmSettleId);
//        LOGGER.info(JSONObject.toJSONString(settleAuditedBySettleId));
//
//        WmSettleAuditedDB bySettleIdMaster = wmSettleAuditedDBMapper
//            .getBySettleIdMaster(wmSettleId);
//        LOGGER.info(JSONObject.toJSONString(bySettleIdMaster));
//
//        List<WmSettleAuditedDB> wmSettleListByWmSettleIdListWithSort = wmSettleAuditedDBMapper
//            .getWmSettleListByWmSettleIdListWithSort(Lists.newArrayList(wmSettleId));
//        LOGGER.info(JSONObject.toJSONString(wmSettleListByWmSettleIdListWithSort));
//    }
//
//    @Test
//    public void testWmSettleAuditedDBMapperWrite(){
//        WmSettleAuditedDB record = new WmSettleAuditedDB();
//        record.setWm_contract_id(*********);
//        record.setWmCustomerId(*********);
//        record.setMin_pay_amount(100.00F);
//        record.setPay_period_num(2);
//        record.setPay_period_unit((byte)1);
//        record.setProvince(1001);
//        record.setCity(2001);
//        record.setBankid((short)99);
//        record.setBranchid(1234);
//        record.setBranchname("branchName");
//        record.setAcc_cardno("qweqwe");
//        record.setAcc_name("namename");
//        record.setAcctype((byte)1);
//        record.setValid((byte)1);
//        record.setCuid(110);
//        record.setMuid(110);
//        record.setCtime(TimeUtil.unixtime());
//        record.setUtime(TimeUtil.unixtime());
//        record.setSettle_type((byte)1);
//        record.setPay_day_of_month((byte)1);
//        record.setParty_a_finance_people("zhangsan");
//        record.setParty_a_finance_phone("11010");
//        record.setCert_type((short)1);
//        record.setLegal_cert_num("2001");
//        record.setLegal_person("legal_persion");
//        record.setLegal_id_card("99999");
//        record.setCert_num("66666");
//        record.setReserve_phone("234234");
//        record.setCard_type(1);
//        record.setWm_settle_id(66666);
//
//        int insertSelective = wmSettleAuditedDBMapper.insertSelective(record);
//        LOGGER.info(JSONObject.toJSONString(insertSelective));
//        record.setCert_num("6060");
//
//        int updateByWmSettleId = wmSettleAuditedDBMapper.updateByWmSettleId(record);
//        LOGGER.info(JSONObject.toJSONString(updateByWmSettleId));
//        int updateWmSettleWalletByCustomerId = wmSettleAuditedDBMapper.updateWmSettleWalletByCustomerId(*********, 66666, 10010);
//        LOGGER.info(JSONObject.toJSONString(updateWmSettleWalletByCustomerId));
//
//    }
//
//    @Test
//    public void testWmPoiSettleDBMapperRead(){
//        int wmSettleId = 10000730;
//        List<Integer> wmSettleIdList = Lists.newArrayList(wmSettleId);
//        int wmPoiId = 10000207;
//        List<Integer> wmPoiIdList = Lists.newArrayList(wmPoiId);
//        List<Integer> wmPoiIdListBySettleId = wmPoiSettleDBMapper
//            .getWmPoiIdListBySettleId(wmSettleId);
//        LOGGER.info(JSONObject.toJSONString(wmPoiIdListBySettleId));
//
//        List<WmPoiSettleDB> byWmSettleIdList = wmPoiSettleDBMapper
//            .getByWmSettleIdList(wmSettleIdList);
//        LOGGER.info(JSONObject.toJSONString(byWmSettleIdList));
//
//        List<Integer> wmPoiIdListByWmSettleIdList = wmPoiSettleDBMapper
//            .getWmPoiIdListByWmSettleIdList(wmSettleIdList);
//        LOGGER.info(JSONObject.toJSONString(wmPoiIdListByWmSettleIdList));
//
//        List<Integer> wmSettleIdListBySettleAndPoi = wmPoiSettleDBMapper
//            .getWmSettleIdListBySettleAndPoi(wmSettleIdList, wmPoiIdList);
//        LOGGER.info(JSONObject.toJSONString(wmSettleIdListBySettleAndPoi));
//
//        List<Integer> wmSettleIdListByWmPoiId = wmPoiSettleDBMapper
//            .getWmSettleIdListByWmPoiId(wmPoiId);
//        LOGGER.info(JSONObject.toJSONString(wmSettleIdListByWmPoiId));
//    }
//
//    @Test
//    public void testWmPoiSettleDBMapperWrite() {
//        WmPoiSettleDB db1 = new WmPoiSettleDB();
//        db1.setWm_poi_id(*********);
//        db1.setWm_contract_id(*********);
//        db1.setWm_settle_id(66666666);
//
//        WmPoiSettleDB db2 = new WmPoiSettleDB();
//        db2.setWm_poi_id(77777777);
//        db2.setWm_contract_id(77777777);
//        db2.setWm_settle_id(77777777);
//
//        int r1 = wmPoiSettleDBMapper.insertSelective(db1);
//        LOGGER.info(JSONObject.toJSONString(r1));
//
//        int r11 = wmPoiSettleDBMapper.insertSelective(db2);
//        LOGGER.info(JSONObject.toJSONString(r11));
//
//        int r2 = wmPoiSettleDBMapper
//            .deleteByWmSettleIdAndWmPoiIdList(66666666, Lists.<Integer>newArrayList(*********));
//        LOGGER.info(JSONObject.toJSONString(r2));
//
//
//        int r3 = wmPoiSettleDBMapper
//            .deleteByWmSettleIdListAndWmPoiIdList(Lists.<Integer>newArrayList(77777777),
//                Lists.<Long>newArrayList(77777777L));
//        LOGGER.info(JSONObject.toJSONString(r3));
//    }
//
//    @Test
//    public void testWmPoiSettleAuditedDBMapperRead() {
//        int wmPoiId = 44345;
//        List<Integer> wmPoiIdList = Lists.newArrayList(wmPoiId);
//        int wmSettleId = 10000721;
//        List<Integer> wmSettleIdList = Lists.newArrayList(wmSettleId);
//        List<WmPoiSettleAuditedDB> bySettleIdList = wmPoiSettleAuditedDBMapper
//            .getBySettleIdList(wmSettleIdList);
//        LOGGER.info(JSONObject.toJSONString(bySettleIdList));
//        List<Integer> wmPoiIdListByWmSettleIdList = wmPoiSettleAuditedDBMapper
//            .getWmPoiIdListByWmSettleIdList(wmSettleIdList);
//        LOGGER.info(JSONObject.toJSONString(wmPoiIdListByWmSettleIdList));
//        List<Integer> wmSettleIdListBySettleAndPoi = wmPoiSettleAuditedDBMapper
//            .getWmSettleIdListBySettleAndPoi(wmSettleIdList, wmPoiIdList);
//        LOGGER.info(JSONObject.toJSONString(wmSettleIdListBySettleAndPoi));
//        List<Integer> wmSettleIdListByWmPoiId = wmPoiSettleAuditedDBMapper
//            .getWmSettleIdListByWmPoiId(wmPoiId);
//        LOGGER.info(JSONObject.toJSONString(wmSettleIdListByWmPoiId));
//        List<Long> longs = wmPoiSettleAuditedDBMapper
//            .batchGetEffectiveWmPoiIdList(Lists.<Long>newArrayList((long) wmPoiId));
//        LOGGER.info(JSONObject.toJSONString(longs));
//    }
//
//    @Test
//    public void testWmPoiSettleAuditedDBMapperWrite() {
//        WmPoiSettleAuditedDB db = new WmPoiSettleAuditedDB();
//        db.setWm_poi_id(6666666);
//        db.setWm_contract_id(7777777);
//        db.setWm_settle_id(8888888);
//        db.setValid((byte) 1);
//
//        WmPoiSettleAuditedDB db2 = new WmPoiSettleAuditedDB();
//        db2.setWm_poi_id(999999);
//        db2.setWm_contract_id(7777777);
//        db2.setWm_settle_id(9999999);
//        db2.setValid((byte) 1);
//
//        int insertSelective1 = wmPoiSettleAuditedDBMapper.insertSelective(db);
//        LOGGER.info(JSONObject.toJSONString(insertSelective1));
//        int insertSelective2 = wmPoiSettleAuditedDBMapper.insertSelective(db2);
//        LOGGER.info(JSONObject.toJSONString(insertSelective2));
//
//        List<Integer> wmsettleIdList = Lists.newArrayList(8888888);
//
//        int deleteByWmSettleIdList = wmPoiSettleAuditedDBMapper.deleteByWmSettleIdList(wmsettleIdList);
//        LOGGER.info(JSONObject.toJSONString(deleteByWmSettleIdList));
//
//        int deleteByWmSettleIdListAndWmPoiIdList = wmPoiSettleAuditedDBMapper
//            .deleteByWmSettleIdListAndWmPoiIdList(Lists.<Integer>newArrayList(9999999),
//                Lists.<Long>newArrayList(999999L));
//        LOGGER.info(JSONObject.toJSONString(deleteByWmSettleIdListAndWmPoiIdList));
//    }
//
//    @Test
//    public void testSetUpEffect() throws Exception{
//        int wmCustomerId = 43;
//        int opUid = 47843;
//        String opUname = "yc";
//        try{
//            wmSettleManagerService.setupEffect(wmCustomerId,opUid,opUname);
//        }catch(Exception e){
//            LOGGER.error("setupEffect异常",e);
//        }
//    }
//
//    @Test
//    public void testSendRabbitMq() {
//        WmSettleMsgBo settleMsgBo = new WmSettleMsgBo();
//        settleMsgBo.setWmSettleId(10000737);
//        settleMsgBo.setWmContractId(10000000);
//        settleMsgBo.setWmCustomerId(10000000);
//        settleMsgBo.setType(OpType.CHANGE);
//
//        LOGGER.info("mq 同步结算数据,同步结算:" + JSON.toJSONString(settleMsgBo));
//
//        JSONObject jo = new JSONObject();
//        jo.put("wmContractId", settleMsgBo.getWmContractId());
//        jo.put("wmCustomerId", settleMsgBo.getWmCustomerId());
//        jo.put("wmSettleId", settleMsgBo.getWmSettleId());
//        jo.put("type", settleMsgBo.getType().getValue());
//
////        producer.send("waimai.customer.settle.change", jo);
//
//        LOGGER.info("mq 同步结算数据,实际数据:{}",jo.toJSONString());
//    }
//
//    @Test
//    public void testDeleteWmSettleAudited(){
//        List<Integer> list = Lists.newArrayList(240,236,276);
//        wmSettleAuditedDBMapper.deleteByWmSettleIdList(list);
//        System.out.println("finsh testDeleteWmSettleAudited");
//    }
//
//    //结算后置 begin
//    @Test
//    public void testGetWmSettleByWmPoiId() throws Exception{
//        long wmPoiId = 104558L;
//        WmSettle wmSettle = wmSettleService.getWmSettleByWmPoiId(wmPoiId);
//        System.out.printf(JSONObject.toJSONString(wmSettle));
//    }
//
//    @Test
//    public void testGetCustomerSignerPhoneByWmPoiId() throws Exception{
//        long wmPoiId = 104558L;
//        String customerSignerPhoneByWmPoiId = kpThriftService
//            .getCustomerSignerPhoneByWmPoiId(wmPoiId);
//        System.out.println(customerSignerPhoneByWmPoiId);
//    }
//
//    //结算后置 end
//
//    @Test
//    public void testSettle_compatible_switch_contract_open() throws Exception {
//        int wmSettleId = 10000575;
//        int wmPoiId = 5108803;
//        int wmContractId = 100587;
//        List<Integer> wmSettleIdList = Lists.newArrayList(10000575);
//        List<Integer> wmPoiIdListBySettleIdAndWmContractId = wmPoiSettleDBMapper
//                .getWmPoiIdListBySettleIdAndWmContractId(wmSettleId, wmContractId);
//        LOGGER.info("#getWmPoiIdListBySettleIdAndWmContractId={}",
//                JSONObject.toJSONString(wmPoiIdListBySettleIdAndWmContractId));
//
//        List<Integer> wmPoiIdListBySettleIdAndWmContractIdMaster = wmPoiSettleDBMapper
//                .getWmPoiIdListBySettleIdAndWmContractIdMaster(wmSettleId, wmContractId);
//        LOGGER.info("#wmPoiIdListBySettleIdAndWmContractIdMaster={}",
//                JSONObject.toJSONString(wmPoiIdListBySettleIdAndWmContractIdMaster));
//
//        List<WmPoiSettleDB> byWmSettleIdListAndWmContractId = wmPoiSettleDBMapper
//                .getByWmSettleIdListAndWmContractId(wmSettleIdList, wmContractId);
//        LOGGER.info("#getByWmSettleIdListAndWmContractId={}",
//                JSONObject.toJSONString(byWmSettleIdListAndWmContractId));
//
//        List<WmPoiSettleDB> byWmSettleIdListAndWmContractIdMaster = wmPoiSettleDBMapper
//                .getByWmSettleIdListAndWmContractIdMaster(wmSettleIdList, wmContractId);
//        LOGGER.info("#getByWmSettleIdListAndWmContractIdMaster={}",
//                JSONObject.toJSONString(byWmSettleIdListAndWmContractIdMaster));
//
//        List<Integer> poiIdsBySettleIdAndWmContractId = wmPoiSettleAuditedDBMapper
//                .getPoiIdsBySettleIdAndWmContractId(wmSettleId, wmContractId);
//        LOGGER.info("#getPoiIdsBySettleIdAndWmContractId={}",
//                JSONObject.toJSONString(poiIdsBySettleIdAndWmContractId));
//
//        List<WmPoiSettleAuditedDB> bySettleIdListAndWmContractId = wmPoiSettleAuditedDBMapper
//                .getBySettleIdListAndWmContractId(wmSettleIdList, wmContractId);
//        LOGGER.info("#getBySettleIdListAndWmContractId={}",
//                JSONObject.toJSONString(bySettleIdListAndWmContractId));
//    }
//
//
//
//    @Test
//    public void testCheckWmPoiOnlineStatus() throws TException, WmCustomerException {
//        checkWmPoiOnlineStatus();
//    }
//
//    private void checkWmPoiOnlineStatus() throws TException, WmCustomerException {
//        List<Long> wmPoiIdList = Lists.newArrayList(1L,2L,3L,4L);
//
//        WmPoiDomain wmPoiDomain1 = new WmPoiDomain();
//        wmPoiDomain1.setValid(1);
//        wmPoiDomain1.setWmPoiId(1);
//        WmPoiDomain wmPoiDomain2 = new WmPoiDomain();
//        wmPoiDomain2.setValid(1);
//        wmPoiDomain2.setWmPoiId(2);
//        WmPoiDomain wmPoiDomain3 = new WmPoiDomain();
//        wmPoiDomain3.setValid(1);
//        wmPoiDomain3.setWmPoiId(3);
//        WmPoiDomain wmPoiDomain4 = new WmPoiDomain();
//        wmPoiDomain4.setValid(1);
//        wmPoiDomain4.setWmPoiId(4);
//
//        List<WmPoiDomain> wmPoiDomainList = Lists.newArrayList(wmPoiDomain1, wmPoiDomain2, wmPoiDomain3, wmPoiDomain4);
//        CollectionUtils.filter(wmPoiDomainList, new Predicate() {
//            @Override
//            public boolean evaluate(Object object) {
//                WmPoiDomain wmPoiDomain = (WmPoiDomain) object;
//                return wmPoiDomain.getValid() == 1;
//            }
//        });
//        List<Long> onlinePoiIdList = Lists.transform(wmPoiDomainList, new Function<WmPoiDomain, Long>() {
//            @Nullable
//            @Override
//            public Long apply(@Nullable WmPoiDomain input) {
//                return (long)input.getWmPoiId();
//            }
//        });
//        List<Long> existSettlePoiIdList = Lists.newArrayList(1L, 2L, 3L);
//        onlinePoiIdList.removeAll(existSettlePoiIdList);
//        if (CollectionUtils.isNotEmpty(onlinePoiIdList)) {
//            String errorMsg = String.format("提交失败:门店id[%s] 未录入结算信息", StringUtils.join(onlinePoiIdList, ","));
//            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, errorMsg);
//        }
//    }
//
//
//    @Test
//    public void testCleanWmSettle() throws WmCustomerException {
//        WmSettleWalletCleanLog db = new WmSettleWalletCleanLog();
//        db.setCityCode("110000");
//        db.setBdMisId("zhuhai04");
//        db.setWmPoiId(10000294L);
//        db.setWmPoiVersion(1);
//        db.setCustomerId("10014595");
//        db.setOpUid(10001);
//        db.setResult(0);
//        db.setException(0);
//        db.setReason("");
//        db.setValid(1);
//        WmSettle wmSettle = wmSettleService.getWmSettleByWmSettleId(10000816);
//        wmSettle.setCard_type(2);
//        wmSettle.setCert_type((short)1001);
//        wmSettle.setCert_num("******************");
//        wmSettle.setReserve_phone("18514428787");
//    }
//
//    @Test
//    public void testGetWmSettleWalletCleanLogByWmPoiIdLIst() throws WmCustomerException, TException, WmServerException {
//        //WmEmploy wmEmploy = wmEmployService.getByMisId("zhuhai04");
//        System.out.println(JSONObject.toJSONString(wmSettleCleanService.getWmSettleWalletCleanLogByWmPoiIdLIst(Lists.newArrayList(10000294L, 18362L))));
//    }
//
//    @Test
//    public void testWmSettleDiffService() throws Exception {
//        //testAdd
//        String wmSettleStr = "[{\"id\":10000961,\"min_pay_amount\":10.00,\"pay_period_num\":3,\"pay_period_unit\":3,\"branchname\":\"平安银行股份有限公司北京朝阳门支行\",\"acc_cardno\":\"6216261000000000018\",\"acc_name\":\"全渠道\",\"acctype\":2,\"settle_type\":1,\"pay_day_of_month\":0,\"party_a_finance_people\":\"财务负责人1\",\"party_a_finance_phone\":\"13552535506\",\"card_type\":2,\"cert_type\":1001,\"cert_num\":\"341126197709218366\",\"reserve_phone\":\"13552535506\",\"wmPoiIdList\":[10000499,10000500]}]";
//        String wmSettleAuditedStr = "[{\"wm_settle_id\":10000962,\"min_pay_amount\":12.00,\"pay_period_num\":3,\"pay_period_unit\":3,\"branchname\":\"平安银行股份有限公司北京朝阳门支行\",\"acc_cardno\":\"6216261000000000018\",\"acc_name\":\"全渠道\",\"acctype\":2,\"settle_type\":1,\"pay_day_of_month\":0,\"party_a_finance_people\":\"财务负责人1\",\"party_a_finance_phone\":\"13552535506\",\"card_type\":2,\"cert_type\":1001,\"cert_num\":\"341126197709218366\",\"reserve_phone\":\"13552535506\",\"wmPoiIdList\":[10000500]}]";
//        List<WmSettle> wmSettleList = JSONArray.parseArray(wmSettleStr, WmSettle.class);
//        List<WmSettleAudited> wmSettleAuditedList = JSONArray
//                .parseArray(wmSettleAuditedStr, WmSettleAudited.class);
//        EcontractSettleInfoBo econtractSettleInfoBo = wmSettleDiffService
//                .genApplyEcontractDiffInfo(wmSettleList, wmSettleAuditedList);
//        LOGGER.info("#testWmSettleDiffService#testAdd#econtractSettleInfoBo={}", JSONObject.toJSONString(econtractSettleInfoBo));
//        LOGGER.info("#testWmSettleDiffService#testAdd#EcontractContentBoList={}", JSONObject.toJSONString(transform(econtractSettleInfoBo)));
//
//        //testOnlyModifyWmPoiId
//        wmSettleStr = "[{\"id\":10000961,\"min_pay_amount\":10.00,\"pay_period_num\":3,\"pay_period_unit\":3,\"branchname\":\"平安银行股份有限公司北京朝阳门支行\",\"acc_cardno\":\"6216261000000000018\",\"acc_name\":\"全渠道\",\"acctype\":2,\"settle_type\":1,\"pay_day_of_month\":0,\"party_a_finance_people\":\"财务负责人1\",\"party_a_finance_phone\":\"13552535506\",\"card_type\":2,\"cert_type\":1001,\"cert_num\":\"341126197709218366\",\"reserve_phone\":\"13552535506\",\"wmPoiIdList\":[10000499,10000500,10000498]}]";
//        wmSettleAuditedStr = "[{\"wm_settle_id\":10000961,\"min_pay_amount\":10.00,\"pay_period_num\":3,\"pay_period_unit\":3,\"branchname\":\"平安银行股份有限公司北京朝阳门支行\",\"acc_cardno\":\"6216261000000000018\",\"acc_name\":\"全渠道\",\"acctype\":2,\"settle_type\":1,\"pay_day_of_month\":0,\"party_a_finance_people\":\"财务负责人1\",\"party_a_finance_phone\":\"13552535506\",\"card_type\":2,\"cert_type\":1001,\"cert_num\":\"341126197709218366\",\"reserve_phone\":\"13552535506\",\"wmPoiIdList\":[10000500,10000493]}]";
//        wmSettleList = JSONArray.parseArray(wmSettleStr, WmSettle.class);
//        wmSettleAuditedList = JSONArray
//                .parseArray(wmSettleAuditedStr, WmSettleAudited.class);
//        econtractSettleInfoBo = wmSettleDiffService
//                .genApplyEcontractDiffInfo(wmSettleList, wmSettleAuditedList);
//        LOGGER.info("#testWmSettleDiffService#testOnlyModifyWmPoiId#econtractSettleInfoBo={}", JSONObject.toJSONString(econtractSettleInfoBo));
//        LOGGER.info("#testWmSettleDiffService#testOnlyModifyWmPoiId#EcontractContentBoList={}", JSONObject.toJSONString(transform(econtractSettleInfoBo)));
//
//        //testBothModify
//        wmSettleStr = "[{\"id\":10000961,\"min_pay_amount\":10.00,\"pay_period_num\":3,\"pay_period_unit\":3,\"branchname\":\"平安银行股份有限公司北京朝阳门支行\",\"acc_cardno\":\"6216261000000000018\",\"acc_name\":\"全渠道\",\"acctype\":2,\"settle_type\":1,\"pay_day_of_month\":0,\"party_a_finance_people\":\"财务负责人1\",\"party_a_finance_phone\":\"13552535506\",\"card_type\":2,\"cert_type\":1001,\"cert_num\":\"341126197709218366\",\"reserve_phone\":\"13552535506\",\"wmPoiIdList\":[10000499,10000500,10000498]}]";
//        wmSettleAuditedStr = "[{\"wm_settle_id\":10000961,\"min_pay_amount\":12.00,\"pay_period_num\":3,\"pay_period_unit\":3,\"branchname\":\"平安银行股份有限公司北京朝阳门支行\",\"acc_cardno\":\"6216261000000000018\",\"acc_name\":\"全渠道\",\"acctype\":2,\"settle_type\":1,\"pay_day_of_month\":0,\"party_a_finance_people\":\"财务负责人1\",\"party_a_finance_phone\":\"13552535506\",\"card_type\":2,\"cert_type\":1001,\"cert_num\":\"341126197709218366\",\"reserve_phone\":\"13552535506\",\"wmPoiIdList\":[10000500,10000493]}]";
//        wmSettleList = JSONArray.parseArray(wmSettleStr, WmSettle.class);
//        wmSettleAuditedList = JSONArray
//                .parseArray(wmSettleAuditedStr, WmSettleAudited.class);
//        econtractSettleInfoBo = wmSettleDiffService
//                .genApplyEcontractDiffInfo(wmSettleList, wmSettleAuditedList);
//        LOGGER.info("#testWmSettleDiffService#testOnlyModifyWmPoiId#econtractSettleInfoBo={}", JSONObject.toJSONString(econtractSettleInfoBo));
//        LOGGER.info("#testWmSettleDiffService#testOnlyModifyWmPoiId#EcontractContentBoList={}", JSONObject.toJSONString(transform(econtractSettleInfoBo)));
//    }
//
//
//    private List<EcontractContentBo> transform(EcontractSettleInfoBo infoBo){
//        List<EcontractContentBo> result = null;
//        if (StringUtils.isNotEmpty(infoBo.getDiffInfo())) {
//            List<ChangeInfoContentBo> changeInfoContentBoList = JSON
//                    .parseArray(infoBo.getDiffInfo(), ChangeInfoContentBo.class);
//            result = Lists.newArrayList(Lists.transform(changeInfoContentBoList,
//                    new Function<ChangeInfoContentBo, EcontractContentBo>() {
//                        @Nullable
//                        @Override
//                        public EcontractContentBo apply(
//                                @Nullable ChangeInfoContentBo input) {
//                            return (EcontractContentBo) input;
//                        }
//                    }));
//        }
//        return result;
//    }
//
//}
