package com.sankuai.meituan.waimai.service.sc.delivery;

import com.sankuai.meituan.waimai.customer.dao.sc.WmScMetadataMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.metadata.WmScMetadataDO;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.basic.WmSchoolDeliveryFollowUpBasicServiceImpl;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolLifecycleEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryGoalStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryOpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryOperationMonitorGoalAchieveEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliveryOpUserDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.mockito.Matchers.anyInt;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WmSchoolDeliveryFollowUpBasicServiceImplTest {

    @InjectMocks
    private WmSchoolDeliveryFollowUpBasicServiceImpl wmSchoolDeliveryFollowUpBasicService;

    @Mock
    private WmScMetadataMapper wmScMetadataMapper;

    /**
     * 测试交付跟进聚合配送 & 食堂档口模块指标当前状态 - 实际完成时间在计划完成时间之前或当天
     */
    @Test
    public void testGetDeliveryFollowUpAggreCanteenGoalStatusWithFinTimeBeforeExpTime() {
        // arrange
        String finTime = "2024-04-13";
        String expTime = "2024-04-14";

        // act
        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpAggreCanteenGoalStatus(finTime, expTime);

        // assert
        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType(), (int) result);
    }

    /**
     * 测试交付跟进聚合配送 & 食堂档口模块指标当前状态 - 实际完成时间在计划完成时间之后
     */
    @Test
    public void testGetDeliveryFollowUpAggreCanteenGoalStatusWithFinTimeAfterExpTime() {
        // arrange
        String finTime = "2024-04-14";
        String expTime = "2024-04-12";

        // act
        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpAggreCanteenGoalStatus(finTime, expTime);

        // assert
        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType(), (int) result);
    }

    /**
     * 测试交付跟进聚合配送 & 食堂档口模块指标当前状态 - 当前时间在计划完成时间5日之前-正常(不含5日)
     */
//    @Test
//    public void testGetDeliveryFollowUpAggreCanteenGoalStatusWithNowBeforeExptimeFiveDays() {
//        // arrange
//        String finTime = "";
//        String expTime = "2024-03-28";
//
//        // act
//        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpAggreCanteenGoalStatus(finTime, expTime);
//
//        // assert
//        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType(), (int) result);
//    }

    /**
     * 测试交付跟进聚合配送 & 食堂档口模块指标当前状态 - 当前时间在计划完成时间0-5日之间
     */
//    @Test
//    public void testGetDeliveryFollowUpAggreCanteenGoalStatusWithNowWithinExptimeFiveDays() {
//        // arrange
//        String finTime = "";
//        String expTime = "2024-04-25";
//
//        // act
//        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpAggreCanteenGoalStatus(finTime, expTime);
//
//        // assert
//        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.WARN.getType(), (int) result);
//    }

    /**
     * 测试交付跟进聚合配送 & 食堂档口模块指标当前状态 - 当前时间在计划完成时间之后
     */
    @Test
    public void testGetDeliveryFollowUpAggreCanteenGoalStatusWithNowAfterExptime() {
        // arrange
        String finTime = "";
        String expTime = "2024-04-17";

        // act
        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpAggreCanteenGoalStatus(finTime, expTime);

        // assert
        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType(), (int) result);
    }


    /**
     * 测试交付跟进运营效果监控模块指标当前状态 - 达标 & 当前时间在计划完成时间之前或当天
     */
//    @Test
//    public void testGetDeliveryFollowUpOperationIndexGoalStatusWithMeetStandardNowBeforeExpTime() {
//        // arrange
//        Integer goalAchieve = (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType();
//        String expTime = "2024-04-29";
//
//        // act
//        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpOperationIndexGoalStatus(goalAchieve, expTime);
//
//        // assert
//        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType(), (int) result);
//    }

    /**
     * 测试交付跟进运营效果监控模块指标当前状态 - 达标 & 当前时间在计划完成时间之后
     */
    @Test
    public void testGetDeliveryFollowUpOperationIndexGoalStatusWithMeetStandardNowAfterExpTime() {
        // arrange
        Integer goalAchieve = (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.MEET_STANDART.getType();
        String expTime = "2024-04-01";

        // act
        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpOperationIndexGoalStatus(goalAchieve, expTime);

        // assert
        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType(), (int) result);
    }

    /**
     * 测试交付跟进运营效果监控模块指标当前状态 - 不达标 & 当前时间在计划完成时间5日之前
     */
//    @Test
//    public void testGetDeliveryFollowUpOperationIndexGoalStatusWithNotMeetStandardNowBeforeExpTimeFiveDays() {
//        // arrange
//        Integer goalAchieve = (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType();
//        String expTime = "2024-04-29";
//
//        // act
//        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpOperationIndexGoalStatus(goalAchieve, expTime);
//
//        // assert
//        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.NORMAL.getType(), (int) result);
//    }

    /**
     * 测试交付跟进运营效果监控模块指标当前状态 - 不达标 & 当前时间在计划完成时间5日之前
     */
//    @Test
//    public void testGetDeliveryFollowUpOperationIndexGoalStatusWithNotMeetStandardNowWithinExpTimeFiveDays() {
//        // arrange
//        Integer goalAchieve = (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType();
//        String expTime = "2024-04-25";
//
//        // act
//        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpOperationIndexGoalStatus(goalAchieve, expTime);
//
//        // assert
//        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.WARN.getType(), (int) result);
//    }

    /**
     * 测试交付跟进运营效果监控模块指标当前状态 - 不达标 & 当前时间在计划完成时间之后
     */
    @Test
    public void testGetDeliveryFollowUpOperationIndexGoalStatusWithNotMeetStandardNowAfterExpTime() {
        // arrange
        Integer goalAchieve = (int) SchoolDeliveryOperationMonitorGoalAchieveEnum.NOT_MEET_STANDART.getType();
        String expTime = "2024-04-20";

        // act
        Integer result = wmSchoolDeliveryFollowUpBasicService.getDeliveryFollowUpOperationIndexGoalStatus(goalAchieve, expTime);

        // assert
        Assert.assertEquals((int) SchoolDeliveryGoalStatusEnum.DELAY.getType(), (int) result);
    }

    /**
     * 测试根据在线渗透率计算学校生命周期
     */
    @Test
    public void testGetSchoolDeliveryLifeCycle() {
        // arrange
        String onlinePenerateLow = "39.113";
        String onlinePenerateMiddle = "74.3";
        String onlinePenerateHigh = "75";

        // act
        Integer resultLow = wmSchoolDeliveryFollowUpBasicService.getSchoolDeliveryLifeCycle(onlinePenerateLow);
        Integer resultMiddle = wmSchoolDeliveryFollowUpBasicService.getSchoolDeliveryLifeCycle(onlinePenerateMiddle);
        Integer resultHigh = wmSchoolDeliveryFollowUpBasicService.getSchoolDeliveryLifeCycle(onlinePenerateHigh);

        // assert
        Assert.assertEquals((int) SchoolLifecycleEnum.COOPERATE_SCHOOL.getType(), (int) resultLow);
        Assert.assertEquals((int) SchoolLifecycleEnum.VALID_COOPERATE_SCHOOL.getType(), (int) resultMiddle);
        Assert.assertEquals((int) SchoolLifecycleEnum.DEEP_COOPERATE_SCHOOL.getType(), (int) resultHigh);
    }

    /**
     * 测试根据交付ID查询交付跟进操作人相关信息 - 暂存数据为空
     */
    @Test
    public void testGetDeliveryOpUserDTOByDeliveryIdWithNull() throws WmSchCantException {
        // arrange
        Integer deliveryId = 123;
        when(wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(anyInt(), anyInt())).thenReturn(null);

        // act
        WmSchoolDeliveryOpUserDTO opUserDTO = wmSchoolDeliveryFollowUpBasicService.getDeliveryOpUserDTOByDeliveryId(deliveryId);

        // assert
        Assert.assertNull(opUserDTO);
    }

    /**
     * 测试根据交付ID查询交付跟进操作人相关信息 - 暂存数据非空
     */
    @Test
    public void testGetDeliveryOpUserDTOByDeliveryIdWithNotNull() throws WmSchCantException {
        // arrange
        Integer deliveryId = 123;
        WmScMetadataDO metadataDO = new WmScMetadataDO();
        metadataDO.setCuid(100L);
        metadataDO.setCtime(191312399);
        metadataDO.setOpType((int) SchoolDeliveryOpTypeEnum.TEMP_SAVE.getType());
        when(wmScMetadataMapper.selectLatestVersionDataByBusinessTypeAndBusinessId(anyInt(), anyInt())).thenReturn(metadataDO);

        // act
        WmSchoolDeliveryOpUserDTO opUserDTO = wmSchoolDeliveryFollowUpBasicService.getDeliveryOpUserDTOByDeliveryId(deliveryId);

        // assert
        Assert.assertEquals(metadataDO.getCuid().intValue(), (int) opUserDTO.getOpUserUid());
        Assert.assertEquals((int) metadataDO.getOpType(), (int) opUserDTO.getOpType());
        Assert.assertEquals((int) metadataDO.getCtime(), (int) opUserDTO.getOpTime());
    }


}
