//package com.sankuai.meituan.waimai.service;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Sets;
//
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerAuditBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerDetailBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerFormBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerListBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPageDate;
//import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModule;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//import org.junit.Before;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.List;
//import java.util.Set;
//
//public class TestWmCustomerThriftService extends BaseTest {
//    private static WmCustomerThriftService wmCustomerThriftService;
//
//    private static Logger                  logger = LoggerFactory.getLogger(TestWmCustomerThriftService.class);
//
//    @Before
//    public void before() {
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8433);
//        this.setEnv(ENV_DEV);
//        this.setRunMode(RUN_MODE.REMOTE);
//        try {
//            wmCustomerThriftService = (WmCustomerThriftService) getObject(WmCustomerThriftService.class);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetCustomerById() {
//        try {
//            setSwimlane("", "8246-dvlhy");
//            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerThriftService.getCustomerById(100117);
//            logger.info(JSONObject.toJSONString(wmCustomerBasicBo, true));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testIDCardSave() {
//        try {
//            Integer.parseInt("123 ");
//            WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//            wmCustomerBasicBo.setCustomerName("郑旭");
//            wmCustomerBasicBo.setCustomerNumber("360103198908113416");
//            wmCustomerBasicBo.setPicUrl("1,2");
//            wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_IDCARD.getCode());
//            wmCustomerBasicBo.setCustomerSecondType(CertTypeEnum.HK_MACAO_REENTRY_PERMIT.getType());
//            ValidateResultBo validateResultBo = wmCustomerThriftService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 2146436, "郑旭04");
//            logger.error(JSONObject.toJSONString(validateResultBo));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testIDCardUpdate() {
//        try {
//            WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//            wmCustomerBasicBo.setId(10014451);
//            wmCustomerBasicBo.setCustomerName("郑旭");
//            wmCustomerBasicBo.setCustomerNumber("360130194404221323");
//            wmCustomerBasicBo.setPicUrl("/download/mos/contract_file_a6e74fbee789bb5f9674a92731c911e6_1525334776.jpg");
//            wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_IDCARD.getCode());
//            wmCustomerBasicBo.setCustomerSecondType(CertTypeEnum.HK_MACAO_REENTRY_PERMIT.getType());
//            ValidateResultBo validateResultBo = wmCustomerThriftService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 2146436, "郑旭04");
//            logger.error(JSONObject.toJSONString(validateResultBo));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testBusinessSave() {
//        try {
//            WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//            wmCustomerBasicBo.setCustomerName("苍南县钱库镇彭直清小吃店12111");
//            wmCustomerBasicBo.setCustomerNumber("92330327MA2954T23221111221");
//            wmCustomerBasicBo.setPicUrl("1");
//            wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
//            wmCustomerBasicBo.setLegalPerson("zx");
//            wmCustomerBasicBo.setAddress("address");
//            wmCustomerBasicBo.setBusinessScope("123");
//            wmCustomerBasicBo.setValidateDate(0);
//            ValidateResultBo validateResultBo = wmCustomerThriftService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 2146436, "郑旭04");
//            logger.error(JSONObject.toJSONString(validateResultBo));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testBusinessUpdate() {
//        try {
//            WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//            wmCustomerBasicBo.setId(2808);
//            wmCustomerBasicBo.setCustomerName("哈尔滨市道里区辣么爱你一人火锅店");
//            wmCustomerBasicBo.setCustomerNumber("74657483");
//            wmCustomerBasicBo.setPicUrl("1");
//            wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
//            wmCustomerBasicBo.setLegalPerson("赵岩");
//            wmCustomerBasicBo.setAddress("address66");
//            wmCustomerBasicBo.setBusinessScope("123");
//            wmCustomerBasicBo.setValidateDate(3311366400L);
//            ValidateResultBo validateResultBo = wmCustomerThriftService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 2146436, "郑旭04");
//            logger.error(JSONObject.toJSONString(validateResultBo));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCommitCustomerAudit() {
//        try {
//            wmCustomerThriftService.commitCustomerAudit(10000015, 2146436, "郑旭04", true);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCustomerAuditCallBack() {
//        try {
//            WmCustomerAuditBo wmCustomerAuditBo = new WmCustomerAuditBo();
//            wmCustomerAuditBo.setBizId(12);
//            wmCustomerAuditBo.setCustomerId(45);
//            wmCustomerAuditBo.setAuditStatus(WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_REJECT);
//            wmCustomerAuditBo.setAuditResult("123");
//            wmCustomerAuditBo.setOpUid(2146436);
//            wmCustomerAuditBo.setOpName("郑旭04");
//            wmCustomerThriftService.auditCustomerCallBack(wmCustomerAuditBo);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetCustomerWithAudit() {
//        try {
//            WmCustomerDetailBo wmCustomerDetailBo = wmCustomerThriftService.getCustomerWithAuditById(10000000);
//            logger.error(JSONObject.toJSONString(wmCustomerDetailBo));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetCustomerByWmPoiId() {
//        try {
//            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerThriftService.getCustomerByWmPoiId(40970L);
//            logger.error(JSONObject.toJSONString(wmCustomerBasicBo));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetCustomerList() {
//        try {
//            WmCustomerFormBo wmCustomerFormBo = new WmCustomerFormBo();
//            wmCustomerFormBo.setCustomerName("郑");
//            wmCustomerFormBo.setPageSize(30);
//            wmCustomerFormBo.setPageNo(1);
//            WmCustomerPageDate wmCustomerPageDate = wmCustomerThriftService.getCustomerList(wmCustomerFormBo);
//            logger.error(JSONObject.toJSONString(wmCustomerPageDate));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testDistributeCustomer() {
//        try {
//            List<Integer> customerIds = Lists.newArrayList(44, 45);
//            wmCustomerThriftService.distributeCustomer(customerIds, 40415, 2146436, "郑旭04");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testChangeCustomerOwnerUid() {
//        try {
//            wmCustomerThriftService.changeCustomerOwnerUid(2146436, 0);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetCustomerOwnUidByCustomerId() {
//        try {
//            List<Integer> customerIds = Lists.newArrayList(44, 45);
//            List<WmCustomerListBo> wmCustomerListBos = wmCustomerThriftService.getCustomerOwnUidByCustomerId(customerIds);
//            logger.info("客户负责人列表,wmCustomerListBos={}", JSONObject.toJSONString(wmCustomerListBos));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testSearchCustomerListByKeyWord() {
//        try {
//            List<WmCustomerListBo> list = wmCustomerThriftService.searchCustomerListByKeyWord("郑", 3);
//            logger.info("客户列表,list={}", JSONObject.toJSONString(list));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testSaveOrUpdateCustomerCommonQua() {
//        try {
//            Integer customerId = 1;
//            Set<String> urlSet = Sets.newHashSet("1", "2", "3");
//            wmCustomerThriftService.saveOrUpdateCustomerCommonQua(customerId, urlSet, 2146436, "郑旭04");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetCustomerCommonQuaList() {
//        try {
//            Integer customerId = 1;
//            Set<String> set = wmCustomerThriftService.getCustomerCommonQuaList(customerId);
//            logger.info("客户列表,set={}", JSONObject.toJSONString(set));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testDeleteCustomer() {
//        try {
//            wmCustomerThriftService.deleteCustomer(10014391, 2146436, "郑旭04");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetCustomerOnlineCheckList() {
//        try {
//            wmCustomerThriftService.getCustomerOnlineCheckList(608623, CustomerModule.ALL);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testSaveOrUpdateCustomerOtherScan() throws TException, WmCustomerException {
//        setSwimlane("", "8246-dvlhy");
//        String url1 = "/download/mos/contract_file_6c9634007003ed203d93ff3ea86af932_1524829204.jpg";
//        String url2 = "/download/mos/contract_file_26dc3c4c600fc1ef2dd41701db9cc032_1524828893.jpg";
//        String url3 = "/download/mos/contract_file_1545a4412dba0107f4d6d6a01ba78c5b_1525684733.jpg";
//        String url4 = "/download/mos/contract_file_6d5855314a2f7744054d016e88d9a930_1525686580.jpg";
//        String url5 = "/download/mos/contract_file_c6fd072d9f9d1e46b42d0085463e283d_1525947895.jpg";
//        String url6 = "/download/mos/contract_file_1f22358ef7c5dacd3cd4a016a0697762_1525947902.jpg";
//        String url7 = "/download/mos/contract_file_f6899e3b5991a1fd0508811cb9a95c96_1525948130.jpg";
//        List urls = Lists.newArrayList(url1, url2, url3, url4, url5, url6, url7);
//        boolean isSaveOrUpdate = wmCustomerThriftService.saveOrUpdateCustomerOtherScan(10014595, urls, 2246505, "朱家琨");
//        System.out.println("isSaveOrUpdate = " + isSaveOrUpdate);
//    }
//
//    @Test
//    public void testGetCustomerOtherScan() throws TException, WmCustomerException {
//        setSwimlane("", "8246-dvlhy");
//        List<String> customerOtherScanList = wmCustomerThriftService.getCustomerOtherScanList(10014595);
//        System.out.println("testGetCustomerOtherScan = " + JSON.toJSONString(customerOtherScanList));
//    }
//
//
//}
