//package com.sankuai.meituan.waimai.service.sign;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.meituan.mtrace.Tracer;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.thrift.customer.domain.OpCustomerResultBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchApplyManualPackResultOutPutDTO;
//import com.sankuai.meituan.waimai.thrift.customer.service.external.WmEcontractSignExternalThriftService;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//import org.junit.Before;
//import org.junit.Test;
//
//import java.util.List;
//
///**
// * 远程调用接口
// */
//public class TestWmEcontractSignExternalThriftService extends BaseTest {
//
//    private static WmEcontractSignExternalThriftService wmEcontractSignExternalThriftService;
//
//    @Before
//    public void before() {
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8449);
//        this.setEnv(ENV_TEST);
//        this.setRunMode(RUN_MODE.REMOTE);
//        try {
//            wmEcontractSignExternalThriftService = (WmEcontractSignExternalThriftService) getObject(WmEcontractSignExternalThriftService.class);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testBatch() throws TException, WmCustomerException {
//        Tracer.setSwimlane("zhujiakun-ijjug");
//
//        List<Long> customerIdList = Lists.newArrayList(1024978547L, 9999999999L, 10000368L);
//        int commitUid = 2246505;
//        OpCustomerResultBo opCustomerResultBo = wmEcontractSignExternalThriftService.batchApplyManualPackForShanGou(customerIdList, commitUid);
//        System.out.println("### testBatch ### = " + JSON.toJSON(opCustomerResultBo));
//    }
//
//    @Test
//    public void testBatchApplyManualPack() throws TException, WmCustomerException {
//        Tracer.setSwimlane("9776-cuaur");
//
//        List<Long> customerIdList = Lists.newArrayList(11769391L,11769419L,11769392L,10000368L);
//
//        int commitUid = 2246505;
//        List<BatchApplyManualPackResultOutPutDTO> batchApplyManualPackResultOutPutDTOList = wmEcontractSignExternalThriftService
//                .batchApplyManualPack(customerIdList, commitUid);
//        System.out.println("testBatchApplyManualPack:" + JSON.toJSONString(batchApplyManualPackResultOutPutDTOList,true));
//    }
//}
