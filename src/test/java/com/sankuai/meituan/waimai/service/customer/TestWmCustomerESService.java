//package com.sankuai.meituan.waimai.service.customer;
//
//import com.alibaba.fastjson.JSONObject;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.customer.service.WmCustomerThriftServiceImpl;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerDetailBo;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerCommonThriftService;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import com.sankuai.nibcus.inf.customer.client.service.CustomerThriftService;
//import org.apache.thrift.TException;
//import org.junit.Before;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
///**
// * 测试ES的修改
// */
//public class TestWmCustomerESService  extends BaseTest {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TestWmCustomerESService.class);
//
//    @Autowired
//    private WmCustomerThriftService wmCustomerThriftService;
//
//
//    @Before
//    public void before(){
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8433);
//        this.setEnv(ENV_TEST);
//        this.setRunMode(RUN_MODE.REMOTE);
//        try{
//            wmCustomerThriftService = (WmCustomerThriftService)getObject(WmCustomerThriftServiceImpl.class);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 测试保存客户信息
//     */
//    @Test
//    public void testSaveCustomer(){
//        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//
//        wmCustomerBasicBo.setCustomerType(1);
//        wmCustomerBasicBo.setCustomerRealType(1);
//        wmCustomerBasicBo.setCustomerNumber("");
//        wmCustomerBasicBo.setCustomerName("测试");
//        wmCustomerBasicBo.setPicUrl("");
//        wmCustomerBasicBo.setLegalPerson("法人");
//        wmCustomerBasicBo.setAddress("地址");
//        wmCustomerBasicBo.setValidateDate(0);
//        wmCustomerBasicBo.setBusinessScope("营业范围");
//        wmCustomerBasicBo.setOwnerUid(2287281);
//
//
//        try {
//            ValidateResultBo validateResultBo = wmCustomerThriftService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2287281, "测试");
//            System.out.println(JSONObject.toJSONString(validateResultBo));
//        } catch (TException e) {
//            e.printStackTrace();
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    /**
//     * 测试更新客户信息
//     */
//    @Test
//    public void testUpdateCustomer(){
//        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//
//        wmCustomerBasicBo.setId(10015813);
//        wmCustomerBasicBo.setCustomerType(1);
//        wmCustomerBasicBo.setCustomerRealType(1);
//        wmCustomerBasicBo.setCustomerNumber("");
//        wmCustomerBasicBo.setCustomerName("测试");
//        wmCustomerBasicBo.setPicUrl("");
//        wmCustomerBasicBo.setLegalPerson("法人");
//        wmCustomerBasicBo.setAddress("地址");
//        wmCustomerBasicBo.setValidateDate(0);
//        wmCustomerBasicBo.setBusinessScope("营业范围");
//        wmCustomerBasicBo.setOwnerUid(2287281);
//
//
//        try {
//            ValidateResultBo validateResultBo = wmCustomerThriftService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2287281, "测试");
//            System.out.println(JSONObject.toJSONString(validateResultBo));
//        } catch (TException e) {
//            e.printStackTrace();
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//
//    /**
//     * 测试获取客户信息
//     */
//    @Test
//    public void testGetCustomerWithAuditById(){
//        int customerId = 10015813;
//        try {
//            WmCustomerDetailBo wmCustomerDetailBo = wmCustomerThriftService.getCustomerWithAuditById(customerId);
//            System.out.println(JSONObject.toJSONString(wmCustomerDetailBo));
//        } catch (TException e) {
//            e.printStackTrace();
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    /**
//     * 测试根据客户主键（customerId）或者mtCustomerId 查询客户对象 ,返回的customer对象中，包含mtCustomerId字段。
//     */
//    @Test
//    public void testGetCustomerByIdOrMtCustomerId(){
//        int customerId = 10015813;
//        try {
//            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerThriftService.getCustomerByIdOrMtCustomerId(customerId);
//            System.out.println(JSONObject.toJSONString(wmCustomerBasicBo));
//        } catch (TException e) {
//            e.printStackTrace();
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        }
//
//
//        long mtCustomerId = 1024659690;
//        try {
//            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerThriftService.getCustomerByIdOrMtCustomerId(mtCustomerId);
//            System.out.println(JSONObject.toJSONString(wmCustomerBasicBo));
//        } catch (TException e) {
//            e.printStackTrace();
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    /**
//     *测试传入客户主键（customerId）或者mtCustomerId，返回的customer对象中，包含mtCustomerId字段
//     */
//    @Test
//    public void testGetCustomerListByIdOrMtCustomerId(){
//        Set<Long> customerIdSet = new HashSet<Long>();
//        customerIdSet.add(10015813l);
//        customerIdSet.add(11758029l);
//        customerIdSet.add(11758028l);
//        customerIdSet.add(11758027l);
//        customerIdSet.add(11758026l);
//        customerIdSet.add(11758025l);
//        customerIdSet.add(11758024l);
//        customerIdSet.add(11758023l);
//        customerIdSet.add(11758022l);
//        customerIdSet.add(11758021l);
//        customerIdSet.add(11758020l);
//        customerIdSet.add(11758019l);
//
//        try {
//            List<WmCustomerBasicBo> wmCustomerBasicBoList = wmCustomerThriftService.getCustomerListByIdOrMtCustomerId(customerIdSet);
//            System.out.println(JSONObject.toJSONString(wmCustomerBasicBoList));
//        } catch (TException e) {
//            e.printStackTrace();
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        }
//
//
//        Set<Long> mtCustomerIdSet = new HashSet<Long>();
//        mtCustomerIdSet.add(1024661938l);
//        mtCustomerIdSet.add(1024661937l);
//        mtCustomerIdSet.add(1024661936l);
//        mtCustomerIdSet.add(1024661932l);
//        mtCustomerIdSet.add(1024661931l);
//        mtCustomerIdSet.add(1024661930l);
//        mtCustomerIdSet.add(1024661926l);
//        mtCustomerIdSet.add(1024661922l);
//        mtCustomerIdSet.add(1024661900l);
//        mtCustomerIdSet.add(1024611849l);
//        mtCustomerIdSet.add(11758020l);
//        mtCustomerIdSet.add(11758019l);
//
//        try {
//            List<WmCustomerBasicBo> wmCustomerBasicBoList = wmCustomerThriftService.getCustomerListByIdOrMtCustomerId(mtCustomerIdSet);
//            System.out.println(JSONObject.toJSONString(wmCustomerBasicBoList));
//        } catch (TException e) {
//            e.printStackTrace();
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        }
//
//
//
//
//        Set<Long> mixCustomerIdSet = new HashSet<Long>();
//        mixCustomerIdSet.add(1024661938l);
//        mixCustomerIdSet.add(1024661937l);
//        mixCustomerIdSet.add(1024661936l);
//        mixCustomerIdSet.add(1024661932l);
//        mixCustomerIdSet.add(1024661931l);
//        mixCustomerIdSet.add(1024661930l);
//        mixCustomerIdSet.add(1024661926l);
//        mixCustomerIdSet.add(1024661922l);
//        mixCustomerIdSet.add(1024661900l);
//        mixCustomerIdSet.add(1024611849l);
//        mixCustomerIdSet.add(11758020l);
//        mixCustomerIdSet.add(11758019l);
//        mixCustomerIdSet.add(10015813l);
//        mixCustomerIdSet.add(11758029l);
//        mixCustomerIdSet.add(11758028l);
//        mixCustomerIdSet.add(11758027l);
//        mixCustomerIdSet.add(11758026l);
//        mixCustomerIdSet.add(11758025l);
//        mixCustomerIdSet.add(11758024l);
//        mixCustomerIdSet.add(11758023l);
//        mixCustomerIdSet.add(11758022l);
//        mixCustomerIdSet.add(11758021l);
//        mixCustomerIdSet.add(11758020l);
//        mixCustomerIdSet.add(11758019l);
//
//        try {
//            List<WmCustomerBasicBo> wmCustomerBasicBoList = wmCustomerThriftService.getCustomerListByIdOrMtCustomerId(mixCustomerIdSet);
//            System.out.println(JSONObject.toJSONString(wmCustomerBasicBoList));
//        } catch (TException e) {
//            e.printStackTrace();
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    /**
//     * 测试同步客户信息到美团客户平台
//     */
//    @Test
//    public void testinsertCustomerToMtCustomer(){
//        int customerId = 10015813;
//        try {
//            boolean bln = wmCustomerThriftService.insertCustomerToMtCustomer(customerId);
//            System.out.println("bln = "+bln);
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//    }
//
//}
