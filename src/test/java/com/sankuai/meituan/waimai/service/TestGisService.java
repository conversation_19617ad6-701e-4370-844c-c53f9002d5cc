//package com.sankuai.meituan.waimai.service;
//
//import com.alibaba.fastjson.JSON;
//import com.sankuai.meituan.gis.remote.service.thrift.TAdminDivisionService;
//import com.sankuai.meituan.gis.remote.vo.thrift.TAdAPIResponse;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.List;
//
//public class TestGisService extends BaseSpringJunit {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TestGisService.class);
//
//    @Autowired
//    private TAdminDivisionService.Iface adminDivisionClientProxy;
//
//    @Test
//    public void testGet() throws Exception {
//        List<TAdAPIResponse> allProvincesInChina = adminDivisionClientProxy.findAllProvincesInChina();
//        System.out.println("中国所有省份：" + JSON.toJSONString(allProvincesInChina));
//    }
//
//}
