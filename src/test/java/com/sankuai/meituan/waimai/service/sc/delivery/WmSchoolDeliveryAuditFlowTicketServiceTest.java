package com.sankuai.meituan.waimai.service.sc.delivery;


import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryAuditTaskMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryAuditTaskDO;
import com.sankuai.meituan.waimai.customer.service.sc.WmSchoolServerService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.auditflow.WmSchoolDeliveryAuditFlowTicketService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WmSchoolDeliveryAuditFlowTicketServiceTest {

    @InjectMocks
    private WmSchoolDeliveryAuditFlowTicketService wmSchoolDeliveryAuditFlowTicketService;

    @Mock
    private WmSchoolServerService wmSchoolServerService;

    @Mock
    private WmSchoolDeliveryAuditTaskMapper wmSchoolDeliveryAuditTaskMapper;

    /**
     * 测试计算并获取任务标题
     */
    @Test
    public void testGetTicketTitle() {
        // arrange
        SchoolBo schoolBo = new SchoolBo();
        schoolBo.setSchoolName("测试学校");
        when(wmSchoolServerService.selectSchoolById(anyInt())).thenReturn(schoolBo);

        WmSchoolDeliveryAuditTaskDO taskDONow = new WmSchoolDeliveryAuditTaskDO();
        taskDONow.setCtime(1713691345);
        when(wmSchoolDeliveryAuditTaskMapper.selectByPrimaryKey(anyLong())).thenReturn(taskDONow);

        WmSchoolDeliveryAuditTaskDO taskDO = new WmSchoolDeliveryAuditTaskDO();
        taskDO.setAuditTaskType((int) SchoolDeliveryAuditTaskTypeEnum.DELIVERY_ASSIGNMENT_CREATE.getType());
        taskDO.setSchoolPrimaryId(123);
        taskDO.setId(123);

        // act
        String result = wmSchoolDeliveryAuditFlowTicketService.getTicketTitle(taskDO);

        // assert
        Assert.assertEquals("学校-测试学校-交付人员指定新建-20240421", result);
    }

}
