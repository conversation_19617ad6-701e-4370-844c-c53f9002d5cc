//package com.sankuai.meituan.waimai.service.customer;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
//import com.sankuai.meituan.waimai.customer.ddd.contract.domain.dservice.IWmContractWriteDomainService;
//import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
//import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService;
//
//import java.util.Collections;
//import java.util.Comparator;
//import java.util.List;
//
//public class TestWmAgentSwitchService
////        extends BaseSpringJunit
//{
//
//    private static final Logger             LOGGER = LoggerFactory.getLogger(TestWmAgentSwitchService.class);
//
//    @Autowired
//    private WmContractService               wmContractService;
//
//    @Autowired
//    IWmContractWriteDomainService wmContractWriteDomainService;
//
//    @Test
//    public void testGetCustomerList() throws Exception {
//        wmContractWriteDomainService.invalidC2ContractForAgentAndWmPoi(512002L, 10014423, 123456, "");
//    }
//
//    @Test
//    public void testCreateContract() throws Exception {
//        WmCustomerContractBo contractBo = wmContractService.getWmCustomerContractBoById(373, true, 0, "");
//        contractBo.getBasicBo().setType(WmTempletContractTypeEnum.C2_E.getCode());
//        contractBo.getBasicBo().setTempletContractId(0);
//        contractBo.getBasicBo().setDueDate(1595575322);
//        contractBo.getSignBoList().get(1).setSignId(1069);
//        wmContractService.saveAndStartSign(contractBo, 123456, "");
//    }
//
//    public static void main(String[] args) {
//        List<WmCustomerContractBo> list = Lists.newArrayList();
//        WmCustomerContractBo customerContractBo = new WmCustomerContractBo();
//        customerContractBo.setBasicBo(new WmTempletContractBasicBo());
//        customerContractBo.getBasicBo().setContractNum("1");
//        customerContractBo.getBasicBo().setStatus(CustomerContractStatus.SIGN_FAIL.getCode());
//        list.add(customerContractBo);
//
//        customerContractBo = new WmCustomerContractBo();
//        customerContractBo.setBasicBo(new WmTempletContractBasicBo());
//        customerContractBo.getBasicBo().setContractNum("2");
//        customerContractBo.getBasicBo().setStatus(CustomerContractStatus.REJECT.getCode());
//        list.add(customerContractBo);
//
//        customerContractBo = new WmCustomerContractBo();
//        customerContractBo.setBasicBo(new WmTempletContractBasicBo());
//        customerContractBo.getBasicBo().setContractNum("3");
//        customerContractBo.getBasicBo().setStatus(CustomerContractStatus.REJECT.getCode());
//        list.add(customerContractBo);
//
//        customerContractBo = new WmCustomerContractBo();
//        customerContractBo.setBasicBo(new WmTempletContractBasicBo());
//        customerContractBo.getBasicBo().setContractNum("4");
//        customerContractBo.getBasicBo().setStatus(CustomerContractStatus.INVALID.getCode());
//        list.add(customerContractBo);
//
//        customerContractBo = new WmCustomerContractBo();
//        customerContractBo.setBasicBo(new WmTempletContractBasicBo());
//        customerContractBo.getBasicBo().setContractNum("5");
//        customerContractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());
//        list.add(customerContractBo);
//
//        customerContractBo = new WmCustomerContractBo();
//        customerContractBo.setBasicBo(new WmTempletContractBasicBo());
//        customerContractBo.getBasicBo().setContractNum("6");
//        customerContractBo.getBasicBo().setStatus(CustomerContractStatus.INVALID.getCode());
//        list.add(customerContractBo);
//        Collections.sort(list, new Comparator<WmCustomerContractBo>() {
//            @Override
//            public int compare(WmCustomerContractBo o1, WmCustomerContractBo o2) {
//                if (o1.getBasicBo().getStatus() == o2.getBasicBo().getStatus() && CustomerContractStatus.isInvalid(o1.getBasicBo().getStatus())) {
//                    return 0;
//                }
//                if (CustomerContractStatus.isInvalid(o1.getBasicBo().getStatus())) {
//                    return 1;
//                }
//                if (CustomerContractStatus.isInvalid(o2.getBasicBo().getStatus())) {
//                    return -1;
//                }
//                return 0;
//            }
//        });
//
//        for (WmCustomerContractBo contractBo : list) {
//            System.out.println(JSON.toJSONString(contractBo));
//        }
//    }
//}
