//package com.sankuai.meituan.waimai.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Sets;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmUnBindingPoiPageDate;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService;
//import org.junit.Before;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.List;
//import java.util.Set;
//
//public class TestWmCustomerPoiService extends BaseTest {
//    private static WmCustomerPoiThriftService wmCustomerPoiThriftService;
//    private static Logger logger = LoggerFactory.getLogger(TestWmCustomerThriftService.class);
//
//    @Before
//    public void before(){
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8435);
//        this.setEnv(ENV_DEV);
//        this.setRunMode(RUN_MODE.DEBUG_LOCAL_WITHSTART);
//        try{
//            wmCustomerPoiThriftService = (WmCustomerPoiThriftService)getObject(WmCustomerPoiThriftService.class);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCustomerBindPoi(){
//        try {
//            Integer customerId=10014451;
//            Set<Integer> wmPoiIdSet = Sets.newHashSet(1);
//            Set<Long> wmPoiIds = Sets.newHashSet();
//            for(Integer poiId:wmPoiIdSet){
//                wmPoiIds.add(poiId.longValue());
//            }
//            wmCustomerPoiThriftService.customerBindPoi(customerId,wmPoiIds,2146436,"郑旭04");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void testCustomerSyncBindPoi(){
//        try {
//            Integer customerId=10014451;
//            Set<Integer> wmPoiIdSet = Sets.newHashSet(1,2,3);
//            Set<Long> wmPoiIds = Sets.newHashSet();
//            for(Integer poiId:wmPoiIdSet){
//                wmPoiIds.add(poiId.longValue());
//            }
//            wmCustomerPoiThriftService.customerBindPoi(customerId,wmPoiIds,2146436,"郑旭04");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void testCustomerUnBindPoi(){
//        try {
//            Integer customerId=10014439;
//            Set<Long> wmPoiIdSet = Sets.newHashSet(10000050L);
//            wmCustomerPoiThriftService.customerUnBindPoi(customerId,wmPoiIdSet,2146436,"郑旭04");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCountUnbindingPoi(){
//        try {
//            Integer customerId=45;
//            logger.info("count:{}",wmCustomerPoiThriftService.countUnbindingPoi(customerId));
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCountCustomerPoi(){
//        try {
//            Integer customerId=45;
//            logger.info("count:{}",wmCustomerPoiThriftService.countCustomerPoi(customerId));
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testSelectWmPoiIdsByCustomerId(){
//        try {
//            List<Long> list = wmCustomerPoiThriftService.selectWmPoiIdsByCustomerId(45);
//            logger.info("门店列表:{}", JSONObject.toJSONString(list));
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testForceUnbind(){
//        try {
//            wmCustomerPoiThriftService.forceUnbind(45,175,2146436,"郑旭04");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCancelUnBind(){
//        try {
//            wmCustomerPoiThriftService.cancelUnBind(45,174,2146436,"郑旭04");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testReSendUnBindSms(){
//        try {
//            wmCustomerPoiThriftService.reSendUnBindSms(45,174,2146436,"郑旭04");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//
//    @Test
//    public void testUnbindingPoiList(){
//        try {
//            WmUnBindingPoiPageDate wmUnBindingPoiPageDate =  wmCustomerPoiThriftService.unbindingPoiList(45,10,1);
//            logger.info("wmUnBindingPoiPageDate={}",JSONObject.toJSONString(wmUnBindingPoiPageDate));
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//}
