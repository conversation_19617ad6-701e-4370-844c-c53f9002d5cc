package com.sankuai.meituan.waimai.service.sc.canteenstall.check;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.WmHighseasThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolAreaMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.check.WmCanteenStallCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiHighSeasPoiInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.apache.logging.log4j.util.Strings;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anySet;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;


@RunWith(MockitoJUnitRunner.class)
public class WmCanteenStallCheckServiceTest {

    @InjectMocks
    private WmCanteenStallCheckService wmCanteenStallCheckService;

    @Mock
    private WmCanteenMapper wmCanteenMapper;

    @Mock
    private WmSchoolMapper wmSchoolMapper;

    @Mock
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    @Mock
    private CanteenStatusMachine canteenStatusMachine;

    @Mock
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Mock
    private WmHighseasThriftServiceAdapter wmHighseasThriftServiceAdapter;

    @Mock
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    /**
     * 测试食堂状态为审核中
     */
    @Test
    public void testCheckCanteenStatusAuditing() throws Throwable {
        // arrange
        WmCanteenDB canteenDB = new WmCanteenDB();
        when(wmCanteenMapper.selectCanteenById(anyInt())).thenReturn(canteenDB);
        when(canteenStatusMachine.isAuditing(canteenDB)).thenReturn(true);

        // act
        String result = wmCanteenStallCheckService.checkCanteenStatusAuditingOrInsertReject(1);

        // assert
        assertEquals("食堂信息未生效或在审批中，不可保存", result);
    }

    /**
     * 测试食堂状态为新建驳回
     */
    @Test
    public void testCheckCanteenStatusInsertReject() throws Throwable {
        // arrange
        WmCanteenDB canteenDB = new WmCanteenDB();
        when(wmCanteenMapper.selectCanteenById(anyInt())).thenReturn(canteenDB);
        when(canteenStatusMachine.isInsertReject(canteenDB)).thenReturn(true);

        // act
        String result = wmCanteenStallCheckService.checkCanteenStatusAuditingOrInsertReject(1);

        // assert
        assertEquals("食堂信息未生效或在审批中，不可保存", result);
    }

    /**
     * 测试食堂信息为空
     */
    @Test(expected = WmSchCantException.class)
    public void testCheckCanteenStatusCanteenDBIsNull() throws Throwable {
        // arrange
        when(wmCanteenMapper.selectCanteenById(anyInt())).thenReturn(null);

        // act
        wmCanteenStallCheckService.checkCanteenStatusAuditingOrInsertReject(1);

        // assert
        // Expected exception
    }

    /**
     * 测试食堂状态正常
     */
    @Test
    public void testCheckCanteenStatusNormal() throws Throwable {
        // arrange
        WmCanteenDB canteenDB = new WmCanteenDB();
        when(wmCanteenMapper.selectCanteenById(anyInt())).thenReturn(canteenDB);
        when(canteenStatusMachine.isAuditing(canteenDB)).thenReturn(false);
        when(canteenStatusMachine.isInsertReject(canteenDB)).thenReturn(false);

        // act
        String result = wmCanteenStallCheckService.checkCanteenStatusAuditingOrInsertReject(1);

        // assert
        assertEquals(Strings.EMPTY, result);
    }


    /**
     * 测试食堂档口数量校验，食堂信息查询为空的情况
     */
    @Test(expected = WmSchCantException.class)
    public void testCheckCanteenStallNumLimit_CanteenDBIsNull() throws Throwable {
        // arrange
        when(wmCanteenMapper.selectCanteenById(anyInt())).thenReturn(null);

        // act
        wmCanteenStallCheckService.checkCanteenStallNumLimit(1, 1);

        // assert
        // Expected exception
    }

    /**
     * 测试食堂档口数量校验，档口数量足够的情况
     */
//    @Test
//    public void testCheckCanteenStallNumLimit_StallNumIsEnough() throws Throwable {
//        // arrange
//        WmCanteenDB canteenDB = new WmCanteenDB();
//        canteenDB.setId(1);
//        canteenDB.setStallNum(10);
//
//        List<WmCanteenStallBindDO> canteenStallBindDOList = new ArrayList<>();
//        for (int i = 0; i < 5; i++) {
//            canteenStallBindDOList.add(new WmCanteenStallBindDO());
//        }
//
//        when(wmCanteenMapper.selectCanteenById(anyInt())).thenReturn(canteenDB);
//        when(wmCanteenStallBindMapper.selectByCanteenPrimaryIdWithClueBindingOrBindSuccess(anyInt())).thenReturn(canteenStallBindDOList);
//
//        // act
//        String result = wmCanteenStallCheckService.checkCanteenStallNumLimit(1, 4);
//
//        // assert
//        assertEquals(Strings.EMPTY, result);
//    }


    /**
     * 测试食堂档口数量校验，档口数量不足的情况
     */
//    @Test
//    public void testCheckCanteenStallNumLimit_StallNumIsNotEnough() throws Throwable {
//        // arrange
//        WmCanteenDB canteenDB = new WmCanteenDB();
//        canteenDB.setId(1);
//        canteenDB.setStallNum(10);
//
//        List<WmCanteenStallBindDO> canteenStallBindDOList = new ArrayList<>();
//        for (int i = 0; i < 5; i++) {
//            canteenStallBindDOList.add(new WmCanteenStallBindDO());
//        }
//
//        when(wmCanteenMapper.selectCanteenById(anyInt())).thenReturn(canteenDB);
//        when(wmCanteenStallBindMapper.selectByCanteenPrimaryIdWithClueBindingOrBindSuccess(anyInt())).thenReturn(canteenStallBindDOList);
//
//        // act
//        String result = wmCanteenStallCheckService.checkCanteenStallNumLimit(1, 6);
//
//        // assert
//        assertEquals("食堂档口数量10，已占用5，本次上传6条数据，数量不足不可保存。请删减或解绑部分档口绑定任务，或修改食堂档口数量后再进行", result);
//    }

    /**
     * 测试场景：档口绑定任务存在，且线索绑定状态为"绑定中"/"绑定成功"
     */
    @Test
    public void testCheckCanteenStallNumLimitByWdcClueId_BindExistAndStatusBindingOrSuccess() throws Throwable {
        // arrange
        Integer canteenPrimaryId = 1;
        Long wdcClueId = 1L;
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BINDING.getType());
        when(wmCanteenStallBindMapper.selectByWdcClueIdAndCanteenPrimaryId(wdcClueId, canteenPrimaryId)).thenReturn(bindDO);

        // act
        String result = wmCanteenStallCheckService.checkCanteenStallNumLimitByWdcClueId(canteenPrimaryId, wdcClueId);

        // assert
        assertEquals(Strings.EMPTY, result);
    }

    /**
     * 测试场景：档口绑定任务存在，食堂档口数量足够
     */
//    @Test
//    public void testCheckCanteenStallNumLimitByWdcClueId_NoBindAndStallNumEnough() throws Throwable {
//        // arrange
//        Integer canteenPrimaryId = 1;
//        Long wdcClueId = 1L;
//        when(wmCanteenStallBindMapper.selectByWdcClueIdAndCanteenPrimaryId(wdcClueId, canteenPrimaryId)).thenReturn(null);
//
//        List<WmCanteenStallBindDO> bindDOList = new ArrayList<>();
//        bindDOList.add(new WmCanteenStallBindDO());
//        when(wmCanteenStallBindMapper.selectByCanteenPrimaryIdWithClueBindingOrBindSuccess(canteenPrimaryId)).thenReturn(bindDOList);
//
//        WmCanteenDB wmCanteenDB = new WmCanteenDB();
//        wmCanteenDB.setStallNum(10);
//        when(wmCanteenMapper.selectCanteenById(canteenPrimaryId)).thenReturn(wmCanteenDB);
//
//        // act
//        String result = wmCanteenStallCheckService.checkCanteenStallNumLimitByWdcClueId(canteenPrimaryId, wdcClueId);
//
//        // assert
//        assertEquals(Strings.EMPTY, result);
//    }

    /**
     * 测试场景：档口绑定任务不存在，食堂档口数量不足
     */
//    @Test
//    public void testCheckCanteenStallNumLimitByWdcClueId_NoBindAndStallNumNotEnough() throws Throwable {
//        // arrange
//        Integer canteenPrimaryId = 1;
//        Long wdcClueId = 1L;
//        when(wmCanteenStallBindMapper.selectByWdcClueIdAndCanteenPrimaryId(wdcClueId, canteenPrimaryId)).thenReturn(null);
//
//        List<WmCanteenStallBindDO> bindDOList = new ArrayList<>();
//        for (int i = 0; i < 10; i++) {
//            bindDOList.add(new WmCanteenStallBindDO());
//        }
//        when(wmCanteenStallBindMapper.selectByCanteenPrimaryIdWithClueBindingOrBindSuccess(canteenPrimaryId)).thenReturn(bindDOList);
//
//        WmCanteenDB wmCanteenDB = new WmCanteenDB();
//        wmCanteenDB.setStallNum(10);
//        when(wmCanteenMapper.selectCanteenById(canteenPrimaryId)).thenReturn(wmCanteenDB);
//
//        // act
//        String result = wmCanteenStallCheckService.checkCanteenStallNumLimitByWdcClueId(canteenPrimaryId, wdcClueId);
//
//        // assert
//        assertNotEquals(Strings.EMPTY, result);
//        assertTrue(result.contains("食堂档口数量10，已占用10，本次上传1条数据，数量不足不可保存。请删减或解绑部分档口绑定任务，或修改食堂档口数量后再进行"));
//    }


    /**
     * 测试线索列表为空
     */
    @Test
    public void testCheckWdcClueListValid_EmptyList() throws WmSchCantException {
        // arrange
        List<Long> wdcClueIdList = new ArrayList<>();
        when(wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList)).thenReturn(new HashMap<>());

        // act
        List<Long> result = wmCanteenStallCheckService.checkWdcClueListValid(wdcClueIdList);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试所有线索均有效
     */
    @Test
    public void testCheckWdcClueListValid_AllValid() throws WmSchCantException {
        // arrange
        List<Long> wdcClueIdList = Arrays.asList(1L, 2L);
        Map<Long, WmPoiHighSeasPoiInfo> resultMap = new HashMap<>();
        resultMap.put(1L, new WmPoiHighSeasPoiInfo());
        resultMap.put(2L, new WmPoiHighSeasPoiInfo());
        when(wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList)).thenReturn(resultMap);

        // act
        List<Long> result = wmCanteenStallCheckService.checkWdcClueListValid(wdcClueIdList);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试部分线索无效
     */
    @Test
    public void testCheckWdcClueListValid_PartialInvalid() throws WmSchCantException {
        // arrange
        List<Long> wdcClueIdList = Arrays.asList(1L, 2L, 3L);
        Map<Long, WmPoiHighSeasPoiInfo> resultMap = new HashMap<>();
        resultMap.put(1L, new WmPoiHighSeasPoiInfo());
        when(wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList)).thenReturn(resultMap);

        // act
        List<Long> result = wmCanteenStallCheckService.checkWdcClueListValid(wdcClueIdList);

        // assert
        assertEquals(Arrays.asList(2L, 3L), result);
    }


    /**
     * 测试所有线索均无效
     */
    @Test
    public void testCheckWdcClueListValid_AllInvalid() throws WmSchCantException {
        // arrange
        List<Long> wdcClueIdList = Arrays.asList(1L, 2L);
        Map<Long, WmPoiHighSeasPoiInfo> resultMap = new HashMap<>();
        when(wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList)).thenReturn(resultMap);

        // act
        List<Long> result = wmCanteenStallCheckService.checkWdcClueListValid(wdcClueIdList);

        // assert
        assertEquals(Arrays.asList(1L, 2L), result);
    }

    /**
     * 测试线索列表不为空，但获取的线索信息为空的情况
     */
    @Test
    public void testCheckWdcClueListAor_NullResultMap() throws WmSchCantException {
        // arrange
        List<Long> wdcClueIdList = Arrays.asList(1L, 2L);
        Integer canteenPrimaryId = 1;
        when(wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList)).thenReturn(null);

        // act
        List<Long> result = wmCanteenStallCheckService.checkWdcClueListAor(wdcClueIdList, canteenPrimaryId);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试线索列表不为空，获取的线索信息也不为空，但所有线索的蜂窝ID与学校的蜂窝ID一致的情况
     */
    @Test
    public void testCheckWdcClueListAor_AllMatch() throws WmSchCantException {
        // arrange
        List<Long> wdcClueIdList = Arrays.asList(1L, 2L);
        int canteenPrimaryId = 1;

        Map<Long, WmPoiHighSeasPoiInfo> resultMap = new HashMap<>();
        resultMap.put(1L, new WmPoiHighSeasPoiInfo(1L, "", 0, "", 0, 1, "", 0L, "", 0L, 0L, 0, 0, 0, 0, 0, null, 0, null, 0));
        resultMap.put(2L, new WmPoiHighSeasPoiInfo(2L, "", 0, "", 0, 1, "", 0L, "", 0L, 0L, 0, 0, 0, 0, 0, null, 0, null, 0));
        when(wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList)).thenReturn(resultMap);

        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setSchoolId(1);
        WmSchoolDB wmSchoolDB = new WmSchoolDB();
        wmSchoolDB.setAorId(1);

        when(wmCanteenMapper.selectCanteenById(canteenPrimaryId)).thenReturn(wmCanteenDB);
        when(wmSchoolMapper.selectSchoolById(1)).thenReturn(wmSchoolDB);

        // act
        List<Long> result = wmCanteenStallCheckService.checkWdcClueListAor(wdcClueIdList, canteenPrimaryId);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试线索列表不为空，获取的线索信息也不为空，但部分线索的蜂窝ID与学校的蜂窝ID不一致的情况
     */
    @Test
    public void testCheckWdcClueListAor_PartialMismatch() throws WmSchCantException {
        // arrange
        List<Long> wdcClueIdList = Arrays.asList(1L, 2L);
        int canteenPrimaryId = 1;
        Map<Long, WmPoiHighSeasPoiInfo> resultMap = new HashMap<>();
        resultMap.put(1L, new WmPoiHighSeasPoiInfo(1L, "", 0, "", 0, 2, "", 0L, "", 0L, 0L, 0, 0, 0, 0, 0, null, 0, null, 0));
        resultMap.put(2L, new WmPoiHighSeasPoiInfo(2L, "", 0, "", 0, 1, "", 0L, "", 0L, 0L, 0, 0, 0, 0, 0, null, 0, null, 0));
        when(wmHighseasThriftServiceAdapter.getWdcClueMapByWdcCludIdList(wdcClueIdList)).thenReturn(resultMap);

        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setSchoolId(1);
        WmSchoolDB wmSchoolDB = new WmSchoolDB();
        wmSchoolDB.setAorId(1);

        when(wmCanteenMapper.selectCanteenById(canteenPrimaryId)).thenReturn(wmCanteenDB);
        when(wmSchoolMapper.selectSchoolById(1)).thenReturn(wmSchoolDB);

        // act
        List<Long> result = wmCanteenStallCheckService.checkWdcClueListAor(wdcClueIdList, canteenPrimaryId);

        // assert
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(1), result.get(0));
    }

    /**
     * 测试当wmPoiIdList不为空，但查询结果为空的情况
     */
    @Test
    public void testCheckWmPoiListCoordinate_EmptyQueryResult() throws WmSchCantException {
        // arrange
        List<Long> wmPoiIdList = Arrays.asList(1L, 2L);
        Integer canteenPrimaryId = 1;
        HashSet<String> set = Sets.newHashSet(
                WM_POI_FIELD_WM_POI_ID,
                WM_POI_FIELD_LATITUDE,
                WM_POI_FIELD_LONGITUDE
        );
        when(wmPoiQueryAdapter.getWmPoiAggreMap(wmPoiIdList, set)).thenReturn(new HashMap<>());

        // act
        List<Long> result = wmCanteenStallCheckService.checkWmPoiListCoordinate(wmPoiIdList, canteenPrimaryId);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试当wmPoiIdList不为空，且所有门店坐标都在学校范围内的情况
     */
    @Test
    public void testCheckWmPoiListCoordinate_AllInSchoolArea() throws WmSchCantException {
        // arrange
        List<Long> wmPoiIdList = Arrays.asList(1L, 2L);
        HashSet<String> set = Sets.newHashSet(
                WM_POI_FIELD_WM_POI_ID,
                WM_POI_FIELD_LATITUDE,
                WM_POI_FIELD_LONGITUDE
        );
        int canteenPrimaryId = 1;
        Map<Long, WmPoiAggre> resMap = new HashMap<>();
        WmPoiAggre wmPoiAggre1 = new WmPoiAggre();
        wmPoiAggre1.setWm_poi_id(1L);
        wmPoiAggre1.setLatitude(39730512);
        wmPoiAggre1.setLongitude(116172156);

        WmPoiAggre wmPoiAggre2 = new WmPoiAggre();
        wmPoiAggre2.setWm_poi_id(1L);
        wmPoiAggre2.setLatitude(39730521);
        wmPoiAggre2.setLongitude(116172165);

        resMap.put(1L, wmPoiAggre1);
        resMap.put(2L, wmPoiAggre2);

        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setSchoolId(1401);

        String area1 = "[{\"x\":39731301,\"y\":116167209},{\"x\":39739058,\"y\":116167085},{\"x\":39739173,\"y\":116170825},{\"x\":39738925,\"y\":116172097},{\"x\":39738634,\"y\":116172355},{\"x\":39731265,\"y\":116172370}]";
        String area2 = "[{\"x\":39736231,\"y\":116172715},{\"x\":39736223,\"y\":116174954},{\"x\":39734014,\"y\":116174943},{\"x\":39734031,\"y\":116172705}]";
        String area3 = "[{\"x\":39728305,\"y\":116172381},{\"x\":39725795,\"y\":116172502},{\"x\":39725810,\"y\":116166934},{\"x\":39728274,\"y\":116167108},{\"x\":39730811,\"y\":116166962},{\"x\":39730854,\"y\":116169725},{\"x\":39730815,\"y\":116172488}]";
        WmScSchoolAreaDO wmScSchoolAreaDO1 = new WmScSchoolAreaDO();
        wmScSchoolAreaDO1.setArea(area1);
        WmScSchoolAreaDO wmScSchoolAreaDO2 = new WmScSchoolAreaDO();
        wmScSchoolAreaDO2.setArea(area2);
        WmScSchoolAreaDO wmScSchoolAreaDO3 = new WmScSchoolAreaDO();
        wmScSchoolAreaDO3.setArea(area3);
        List<WmScSchoolAreaDO> schoolAreaDOList = Lists.newArrayList(wmScSchoolAreaDO1, wmScSchoolAreaDO2, wmScSchoolAreaDO3);

        when(wmPoiQueryAdapter.getWmPoiAggreMap(wmPoiIdList, set)).thenReturn(resMap);
        when(wmCanteenMapper.selectCanteenById(canteenPrimaryId)).thenReturn(wmCanteenDB);
        when(wmScSchoolAreaMapper.selectBySchoolId(wmCanteenDB.getSchoolId())).thenReturn(schoolAreaDOList);

        // act
        List<Long> result = wmCanteenStallCheckService.checkWmPoiListCoordinate(wmPoiIdList, canteenPrimaryId);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试当wmPoiIdList不为空，且部分门店坐标不在学校范围内的情况
     */
    @Test
    public void testCheckWmPoiListCoordinate_SomeNotInSchoolArea() throws WmSchCantException {
        // arrange
        List<Long> wmPoiIdList = Arrays.asList(1L, 2L);
        HashSet<String> set = Sets.newHashSet(
                WM_POI_FIELD_WM_POI_ID,
                WM_POI_FIELD_LATITUDE,
                WM_POI_FIELD_LONGITUDE
        );
        int canteenPrimaryId = 1;
        Map<Long, WmPoiAggre> resMap = new HashMap<>();
        WmPoiAggre wmPoiAggre1 = new WmPoiAggre();
        wmPoiAggre1.setWm_poi_id(1L);
        wmPoiAggre1.setLatitude(39731053);
        wmPoiAggre1.setLongitude(116171295);

        WmPoiAggre wmPoiAggre2 = new WmPoiAggre();
        wmPoiAggre2.setWm_poi_id(1L);
        wmPoiAggre2.setLatitude(39730521);
        wmPoiAggre2.setLongitude(116172165);

        resMap.put(1L, wmPoiAggre1);
        resMap.put(2L, wmPoiAggre2);

        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setSchoolId(1401);

        String area1 = "[{\"x\":39731301,\"y\":116167209},{\"x\":39739058,\"y\":116167085},{\"x\":39739173,\"y\":116170825},{\"x\":39738925,\"y\":116172097},{\"x\":39738634,\"y\":116172355},{\"x\":39731265,\"y\":116172370}]";
        String area2 = "[{\"x\":39736231,\"y\":116172715},{\"x\":39736223,\"y\":116174954},{\"x\":39734014,\"y\":116174943},{\"x\":39734031,\"y\":116172705}]";
        String area3 = "[{\"x\":39728305,\"y\":116172381},{\"x\":39725795,\"y\":116172502},{\"x\":39725810,\"y\":116166934},{\"x\":39728274,\"y\":116167108},{\"x\":39730811,\"y\":116166962},{\"x\":39730854,\"y\":116169725},{\"x\":39730815,\"y\":116172488}]";
        WmScSchoolAreaDO wmScSchoolAreaDO1 = new WmScSchoolAreaDO();
        wmScSchoolAreaDO1.setArea(area1);
        WmScSchoolAreaDO wmScSchoolAreaDO2 = new WmScSchoolAreaDO();
        wmScSchoolAreaDO2.setArea(area2);
        WmScSchoolAreaDO wmScSchoolAreaDO3 = new WmScSchoolAreaDO();
        wmScSchoolAreaDO3.setArea(area3);
        List<WmScSchoolAreaDO> schoolAreaDOList = Lists.newArrayList(wmScSchoolAreaDO1, wmScSchoolAreaDO2, wmScSchoolAreaDO3);

        when(wmPoiQueryAdapter.getWmPoiAggreMap(wmPoiIdList, set)).thenReturn(resMap);
        when(wmCanteenMapper.selectCanteenById(canteenPrimaryId)).thenReturn(wmCanteenDB);
        when(wmScSchoolAreaMapper.selectBySchoolId(wmCanteenDB.getSchoolId())).thenReturn(schoolAreaDOList);

        // act
        List<Long> result = wmCanteenStallCheckService.checkWmPoiListCoordinate(wmPoiIdList, canteenPrimaryId);

        // assert
        assertEquals(1, result.size());
    }
}
