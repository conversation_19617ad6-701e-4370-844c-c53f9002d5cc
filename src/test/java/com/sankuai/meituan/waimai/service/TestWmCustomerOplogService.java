//package com.sankuai.meituan.waimai.service;
//
//import com.alibaba.fastjson.JSON;
//import com.sankuai.meituan.util.DateUtil;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
//import com.sankuai.meituan.waimai.thrift.customer.domain.OplogSearchPageBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.contract.OplogBoPageData;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.annotation.Rollback;
//
//public class TestWmCustomerOplogService extends BaseSpringJunit {
//
//    private static Logger logger = LoggerFactory.getLogger(TestWmCustomerOplogService.class);
//
//    @Autowired
//    WmCustomerOplogService wmCustomerOplogService;
//
//    @Test
//    public void insert() throws Exception {
//        WmCustomerOplogBo oplogBo = new WmCustomerOplogBo();
//        oplogBo.setCustomerId(1);
//        oplogBo.setModuleType(WmCustomerOplogBo.OpModuleType.CONTRACT.type);
//        oplogBo.setModuleId(12);
//        oplogBo.setOpType(WmCustomerOplogBo.OpType.CHANGESTATUS.type);
//        oplogBo.setLog("签约失败原因：12四大皆空哈速度快解放后");
//        oplogBo.setOpUid(1);
//        oplogBo.setOpUname("邱润景测试");
//        wmCustomerOplogService.insert(oplogBo);
//        oplogBo.setLog("审核驳回原因：被驳回了");
//        wmCustomerOplogService.insert(oplogBo);
//    }
//
//    @Test
//    public void query() throws Exception {
//        OplogSearchPageBo searchPageBo = new OplogSearchPageBo();
//        searchPageBo.setCustomerId(1);
//        searchPageBo.setModuleType(WmCustomerOplogBo.OpModuleType.CONTRACT.type);
//        searchPageBo.setModuleId(12);
//        searchPageBo.setOpType(WmCustomerOplogBo.OpType.CHANGESTATUS.type);
//        OplogBoPageData search = wmCustomerOplogService.search(searchPageBo, 1, "");
//        System.out.println("第一次搜索，data:" + JSON.toJSONString(search));
//        searchPageBo.setContent("解放");
//        searchPageBo.setOpTimeStartSeconds(DateUtil.unixTime() - 100000);
//        searchPageBo.setOpTimeEndSeconds(DateUtil.unixTime());
//        searchPageBo.setContent("解放");
//        search = wmCustomerOplogService.search(searchPageBo, 1, "");
//        System.out.println("第二次搜索，data:" + JSON.toJSONString(search));
//    }
//}
