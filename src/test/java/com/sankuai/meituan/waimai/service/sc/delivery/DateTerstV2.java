package com.sankuai.meituan.waimai.service.sc.delivery;

import com.meituan.waimai.common.utils.JacksonUtils;
import org.junit.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class DateTerstV2 {

    @Test
    public void testV2() {
        ArrayList<List<LocalDate>> lists = new ArrayList<>();
        ArrayList<LocalDate> l1 = new ArrayList<>();
        l1.add(LocalDate.now());
        l1.add(LocalDate.now().plusDays(1L));
        lists.add(l1);


        System.out.println(JacksonUtils.toJson(lists));

    }


}
