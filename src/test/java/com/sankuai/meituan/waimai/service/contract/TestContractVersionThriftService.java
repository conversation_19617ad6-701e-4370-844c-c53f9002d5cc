/*
package com.sankuai.meituan.waimai.service.contract;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.BaseSpringTransactionJunit;
import com.sankuai.meituan.waimai.customer.contract.service.impl.thrift.WmCustomerContractVersionThriftServiceImpl;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.ContractVersionPageData;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TestContractVersionThriftService extends BaseSpringTransactionJunit {

    @Autowired
    WmCustomerContractVersionThriftServiceImpl wmCustomerContractVersionThriftService;

    @Autowired
    WmCustomerContractThriftService wmCustomerContractThriftService;

    @Test
    public void testGetC1EffectiveVersionsNotIn2_0In3_0() throws Exception {
        int wmPoiId = 10000250;
        ContractVersionPageData effectiveVersions = wmCustomerContractVersionThriftService
                .getEffectiveVersions(wmPoiId, 1, 1, 20, 0, "");

        System.out.println("查询C1版本：" + JSON.toJSONString(effectiveVersions));
    }

    @Test
    public void testGetC1EffectiveVersionsIn2_0NotIn3_0() throws Exception {
        int wmPoiId = 44260;
        ContractVersionPageData effectiveVersions = wmCustomerContractVersionThriftService
                .getEffectiveVersions(wmPoiId, 1, 1, 20, 0, "");

        System.out.println("查询C1版本：" + JSON.toJSONString(effectiveVersions));
    }

    @Test
    public void testGetC2EffectiveVersionsNotIn2_0In3_0() throws Exception {
        int wmPoiId = 5108730;
        ContractVersionPageData effectiveVersions = wmCustomerContractVersionThriftService
                .getEffectiveVersions(wmPoiId, 2, 1, 20, 0, "");

        System.out.println("查询C2版本：" + JSON.toJSONString(effectiveVersions));
    }

    @Test
    public void testGetC2EffectiveVersionsIn2_0NotIn3_0() throws Exception {
        int wmPoiId = 5108730;
        ContractVersionPageData effectiveVersions = wmCustomerContractVersionThriftService
                .getEffectiveVersions(wmPoiId, 2, 1, 20, 0, "");

        System.out.println("查询C2版本：" + JSON.toJSONString(effectiveVersions));
    }

    @Test
    public void getAuditedContractSignType() throws Exception {
        Integer auditedContractSignType = wmCustomerContractThriftService.getAuditedContractSignType(44260, 1, 0, "");
        System.out.println("生效合同签约类型："+ auditedContractSignType);
    }

    @Test
    public void getC1PaperContractPdf() throws Exception {
        String c1PaperContractPdf = wmCustomerContractThriftService.getC1PaperContractPdf(44260, 0, "");
        System.out.println("C1纸质合同PDF："+ c1PaperContractPdf);
    }
}
*/
