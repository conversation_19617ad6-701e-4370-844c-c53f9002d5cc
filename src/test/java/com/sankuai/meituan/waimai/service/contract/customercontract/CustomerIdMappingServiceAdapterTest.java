package com.sankuai.meituan.waimai.service.contract.customercontract;

import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.adapter.agent.CustomerIdMappingServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.nibcus.inf.idmapping.client.response.OriginCustomerResponse;
import com.sankuai.nibcus.inf.idmapping.client.response.OriginCustomerResponseV2;
import com.sankuai.nibcus.inf.idmapping.client.service.CustomerIdMappingService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/12/4 17:26
 */
public class CustomerIdMappingServiceAdapterTest extends BaseStaticMockTest {

    @InjectMocks
    private CustomerIdMappingServiceAdapter customerIdMappingServiceAdapter;

    @Mock
    private CustomerIdMappingService customerIdMappingService;


    /**
     * 测试当MccConfig.migrateIdMappingInterface()返回true且服务调用成功时的情况
     */
    @Test
    public void testGetOriginCustomerIdByMtCustomerId_WithMigrationTrueAndSuccess() throws Exception {
        // arrange
        Long mtCustomerId = 123L;
        OriginCustomerResponseV2 response = new OriginCustomerResponseV2();
        response.setOriginCustomerId(456L);
        when(customerIdMappingService.getOriginCustomerByCustomerIdAndBusinessLine(mtCustomerId, BusinessLineEnum.NIB_FOOD.getCode())).thenReturn(response);

        // act
        Long result = customerIdMappingServiceAdapter.getOriginCustomerIdByMtCustomerId(mtCustomerId);

        // assert
        assertEquals(Long.valueOf(456), result);
    }

//    /**
//     * 测试当MccConfig.migrateIdMappingInterface()返回false且服务调用成功时的情况
//     */
//    @Test
//    public void testGetOriginCustomerIdByMtCustomerId_WithMigrationFalseAndSuccess() throws Exception {
//        // arrange
//        Long mtCustomerId = 123L;
//        OriginCustomerResponse response = new OriginCustomerResponse();
//        response.setOriginCustomerId(789L);
//        when(customerIdMappingService.getOriginCustomerByCustomerId(mtCustomerId)).thenReturn(response);
//
//        // act
//        Long result = customerIdMappingServiceAdapter.getOriginCustomerIdByMtCustomerId(mtCustomerId);
//
//        // assert
//        assertEquals(Long.valueOf(789), result);
//    }

    /**
     * 测试当服务调用抛出异常时的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testGetOriginCustomerIdByMtCustomerId_WithException() throws Exception {
        // arrange
        Long mtCustomerId = 123L;
        when(customerIdMappingService.getOriginCustomerByCustomerIdAndBusinessLine(mtCustomerId, BusinessLineEnum.NIB_FOOD.getCode())).thenThrow(new RuntimeException());

        // act
        customerIdMappingServiceAdapter.getOriginCustomerIdByMtCustomerId(mtCustomerId);

        // assert is handled by the expected exception
    }
}
