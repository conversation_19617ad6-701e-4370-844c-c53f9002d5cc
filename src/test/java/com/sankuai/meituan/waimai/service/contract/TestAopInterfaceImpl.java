/*
package com.sankuai.meituan.waimai.service.contract;

import com.sankuai.meituan.waimai.customer.aspect.RepeatSubmission;
import org.springframework.stereotype.Service;

@Service
public class TestAopInterfaceImpl implements TestAopInterface {

    @Override
    @RepeatSubmission(seedExp = "a + '_' + aa")
    public void test(int a, int aa) {

    }

    @Override
    @RepeatSubmission(seedExp = "a + '_' + aa + '_' + aaa")
    public void test(int a, int aa, int aaa) {

    }
}
*/
