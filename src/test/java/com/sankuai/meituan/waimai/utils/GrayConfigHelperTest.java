package com.sankuai.meituan.waimai.utils;

import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.util.common.GrayConfigHelper;
import com.sankuai.meituan.waimai.e.graycenter.sdk.GrayConfigClient;
import com.sankuai.meituan.waimai.e.graycenter.sdk.domain.GrayParams;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;


import org.mockito.InjectMocks;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

public class GrayConfigHelperTest extends BaseStaticMockTest {

    @InjectMocks
    private GrayConfigHelper grayConfigHelper;

    @Before
    public void setUp() {
        super.init();
    }

    @Test
    public void testIsQkBusinessCustomerPeriodGrayWithNullCustomerId() {
        // 测试customerId为null的情况
        assertFalse(grayConfigHelper.isQkBusinessCustomerPeriodGray(null));
    }

    @Test
    public void testIsQkBusinessCustomerPeriodGrayWithValidCustomerId() throws Exception {
        // 模拟灰度配置返回true的情况
        grayConfigClientMockedStatic.when(() -> GrayConfigClient.getGrayConfig(eq("QK_BUSINESS_CUSTOMER_PERIOD"), any(GrayParams.class))).thenReturn("true");
        assertTrue(grayConfigHelper.isQkBusinessCustomerPeriodGray(12345));
    }

    @Test(expected = RuntimeException.class)
    public void testIsQkBusinessCustomerPeriodGrayWithException() throws Exception {
        // 模拟获取灰度配置时抛出异常的情况
        grayConfigClientMockedStatic.when(() -> GrayConfigClient.getGrayConfig(eq("QK_BUSINESS_CUSTOMER_PERIOD"), any(GrayParams.class))).thenThrow(new RuntimeException());
        grayConfigHelper.isQkBusinessCustomerPeriodGray(12345);
        Assert.fail();
    }
}
