package com.sankuai.meituan.waimai.utils;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

public class ObjectMockUtils {

    static Logger logger = LogManager.getLogger(ObjectMockUtils.class);
    static Map<Class, List<?>> container = Maps.newHashMap();

    static {
        URL url = ObjectMockUtils.class.getResource("/pool");
        File dir = new File(url.getFile());
        if (dir.exists()) {
            File[] files = dir.listFiles();
            for (File fileInfo : files) {
                Class<?> tClass = convert2Class(fileInfo.getName());
                if (tClass == null) {
                    continue;
                }
                String fileContent = null;
                try {
                    fileContent = new String(Files.readAllBytes(fileInfo.toPath()));
                } catch (IOException e) {
                    logger.error(e);
                }
                List<?> list = JSONArray.parseArray(fileContent, tClass);
                container.put(tClass, list);
            }
        }

    }

    private static Class<?> convert2Class(String text) {
        try {
            return Class.forName(text);
        } catch (ClassNotFoundException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取某个对象池里的第index个数据
     *
     * @param tClass
     * @param index
     * @param <T>
     * @return
     */
    public static <T> T getObject(Class<T> tClass, int index) {
        if (index < 0) {
            return null;
        }
        if (container.containsKey(tClass)) {
            List<?> list = container.get(tClass);
            if (index >= list.size()) {
                logger.warn("对象池数组越界, tClass={}, index={}", tClass.toString(), index);
                return null;
            }
            return (T) list.get(index);
        } else {
            logger.warn("对象池中不存在该对象, tClass={}", tClass.toString());
            return null;
        }
    }

    /**
     * 获取对象池里的从第start(包含)个数据到第end(不包含)个数据
     *
     * @param tClass
     * @param start
     * @param end
     * @param <T>
     * @return
     */
    public static <T> List<T> getObject(Class<T> tClass, int start, int end) {
        List<T> list = Lists.newArrayList();
        for (int i = start; i < end; i++) {
            T o = getObject(tClass, i);
            if (o != null) {
                list.add(getObject(tClass, i));
            }
        }
        return list;
    }

    /**
     * 获取对象池里的所有元素
     *
     * @param tClass
     * @param <T>
     * @return
     */
    public static <T> List<T> getObject(Class<T> tClass) {
        List<T> list = Lists.newArrayList();
        if (container.containsKey(tClass)) {
            List<?> lst = container.get(tClass);
            for (Object o : lst) {
                list.add((T) o);
            }
        }
        return list;
    }

}
