package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PhfTransferContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfAgentSpecialCompareServiceTest {

    @InjectMocks
    @Spy
    private WmEcontractPhfAgentSpecialCompareService service;

    @Mock
    private Logger log;

    private WmEcontractPhfAgentSpecialCompareService wmEcontractPhfAgentSpecialCompareService = new WmEcontractPhfAgentSpecialCompareService();

    /**
     * Test case for normal execution with valid data
     */
    @Test
    public void testCompare_WithValidData() throws Throwable {
        // arrange
        EcontractBatchBo currentData = new EcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        List<PhfTransferContext> phfTransferContextList = new ArrayList<>();
        phfTransferContextList.add(new PhfTransferContext("contractId", "recordKey", 1L));
        batchContextBo.setPhfTransferContextList(phfTransferContextList);
        doNothing().when(service).compareSinglePoiPdf(any(), any());
        // act
        service.compare(currentData, batchContextBo);
        // assert
        verify(service, times(1)).compareSinglePoiPdf(eq(currentData), eq(phfTransferContextList));
    }

    /**
     * Test case for null phfTransferContextList
     */
    @Test
    public void testCompare_WithNullPhfTransferContextList() throws Throwable {
        // arrange
        EcontractBatchBo currentData = new EcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setPhfTransferContextList(null);
        doNothing().when(service).compareSinglePoiPdf(any(), any());
        // act
        service.compare(currentData, batchContextBo);
        // assert
        verify(service, times(1)).compareSinglePoiPdf(eq(currentData), eq(null));
    }

    /**
     * Test case for empty phfTransferContextList
     */
    @Test
    public void testCompare_WithEmptyPhfTransferContextList() throws Throwable {
        // arrange
        EcontractBatchBo currentData = new EcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setPhfTransferContextList(new ArrayList<>());
        doNothing().when(service).compareSinglePoiPdf(any(), any());
        // act
        service.compare(currentData, batchContextBo);
        // assert
        verify(service, times(1)).compareSinglePoiPdf(eq(currentData), eq(new ArrayList<>()));
    }

    /**
     * Test case for null batchContextBo
     */
    @Test(expected = NullPointerException.class)
    public void testCompare_WithNullBatchContextBo() throws Throwable {
        // arrange
        EcontractBatchBo currentData = new EcontractBatchBo();
        // act
        service.compare(currentData, null);
    }

    /**
     * Test case for exception in compareSinglePoiPdf
     */
    @Test(expected = WmCustomerException.class)
    public void testCompare_WhenCompareSinglePoiPdfThrowsException() throws Throwable {
        // arrange
        EcontractBatchBo currentData = new EcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        List<PhfTransferContext> phfTransferContextList = new ArrayList<>();
        phfTransferContextList.add(new PhfTransferContext("contractId", "recordKey", 1L));
        batchContextBo.setPhfTransferContextList(phfTransferContextList);
        // Using the correct constructor with errorCode and message
        doThrow(new WmCustomerException(500, "Compare failed")).when(service).compareSinglePoiPdf(any(), any());
        // act
        service.compare(currentData, batchContextBo);
    }

    /**
     * Tests the getPdfContentInfoBoMapKeyByWmPoiId method when wmPoiId is null.
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiIdWhenWmPoiIdIsNull() throws Throwable {
        // Arrange
        Long wmPoiId = null;
        // Act
        String result = wmEcontractPhfAgentSpecialCompareService.getPdfContentInfoBoMapKeyByWmPoiId(wmPoiId);
        // Assert
        // Adjusted expectation to match the actual behavior
        assertEquals("PHF_AGENT_SPECIAL_PRICE_null", result);
    }

    /**
     * Tests the getPdfContentInfoBoMapKeyByWmPoiId method when wmPoiId is not null.
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiIdWhenWmPoiIdIsNotNull() throws Throwable {
        // Arrange
        Long wmPoiId = 123L;
        // Act
        String result = wmEcontractPhfAgentSpecialCompareService.getPdfContentInfoBoMapKeyByWmPoiId(wmPoiId);
        // Assert
        assertEquals("PHF_AGENT_SPECIAL_PRICE_123", result);
    }
}