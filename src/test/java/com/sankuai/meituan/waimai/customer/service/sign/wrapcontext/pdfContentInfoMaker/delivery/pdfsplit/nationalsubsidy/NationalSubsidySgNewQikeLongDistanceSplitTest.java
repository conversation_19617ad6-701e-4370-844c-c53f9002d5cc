package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit.nationalsubsidy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryCompanyCustomerLongDistanceInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidySgNewQikeLongDistanceSplitTest {

    private NationalSubsidySgNewQikeLongDistanceSplit split = new NationalSubsidySgNewQikeLongDistanceSplit();

    /**
     * Test when fee mode is not SHANGOU_QIKE_V2 - should not add UUID
     */
    @Test
    public void testSplit_WhenWrongFeeMode_ShouldNotAddUUID() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        // Wrong fee mode
        deliveryInfoBo.setFeeMode("12");
        // Try all possible values for supportSGV2_2DeliveryXQK
        deliveryInfoBo.setSupportSGV2_2DeliveryXQK("1");
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid");
        deliveryInfoBo.setEcontractDeliveryCompanyCustomerLongDistanceInfoBo(new EcontractDeliveryCompanyCustomerLongDistanceInfoBo());
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        List<String> resultList = pdfDataMap.get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_LONG_DISTANCE_PERFORMANCE.getName());
        assertNull("Result list should be null", resultList);
    }

    /**
     * Test when long distance info is null - should not add UUID
     */
    @Test
    public void testSplit_WhenLongDistanceInfoNull_ShouldNotAddUUID() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode("13");
        // Try all possible values for supportSGV2_2DeliveryXQK
        deliveryInfoBo.setSupportSGV2_2DeliveryXQK("1");
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid");
        // Null long distance info
        deliveryInfoBo.setEcontractDeliveryCompanyCustomerLongDistanceInfoBo(null);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        List<String> resultList = pdfDataMap.get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_LONG_DISTANCE_PERFORMANCE.getName());
        assertNull("Result list should be null", resultList);
    }

    /**
     * Test when support flag doesn't match SUPPORT_MARK - should not add UUID
     */
    @Test
    public void testSplit_WhenSupportFlagDoesNotMatch_ShouldNotAddUUID() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode("13");
        // Use an unlikely value for supportSGV2_2DeliveryXQK
        deliveryInfoBo.setSupportSGV2_2DeliveryXQK("THIS_IS_DEFINITELY_NOT_THE_SUPPORT_MARK_VALUE");
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid");
        deliveryInfoBo.setEcontractDeliveryCompanyCustomerLongDistanceInfoBo(new EcontractDeliveryCompanyCustomerLongDistanceInfoBo());
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        List<String> resultList = pdfDataMap.get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_LONG_DISTANCE_PERFORMANCE.getName());
        assertNull("Result list should be null", resultList);
    }
}
