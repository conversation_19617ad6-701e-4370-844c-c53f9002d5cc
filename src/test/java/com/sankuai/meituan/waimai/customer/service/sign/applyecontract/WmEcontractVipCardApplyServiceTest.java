package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignFlowConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractRecordSourceEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractVipCardApplyServiceTest {

    @InjectMocks
    private WmEcontractVipCardApplyService wmEcontractVipCardApplyService;

    @Mock
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Mock
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Mock
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Mock
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    private Map<String, EcontractDataWrapperEnum> dataWrapperMap;

    private List<String> poiStampList;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        // Get access to private static fields using reflection
        Field dataWrapperMapField = WmEcontractVipCardApplyService.class.getDeclaredField("dataWrapperMap");
        dataWrapperMapField.setAccessible(true);
        dataWrapperMap = (Map<String, EcontractDataWrapperEnum>) dataWrapperMapField.get(null);
        Field poiStampListField = WmEcontractVipCardApplyService.class.getDeclaredField("poiStampList");
        poiStampListField.setAccessible(true);
        poiStampList = (List<String>) poiStampListField.get(null);
    }

    /**
     * Test wrapEcontractBo when all services return valid data
     */
    @Test
    public void testWrapEcontractBo_AllServicesReturnValidData() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = mock(EcontractBatchContextBo.class);
        when(batchContextBo.getSource()).thenReturn(WmSignConstant.BATCH_PLATFORM);
        when(batchContextBo.getBatchId()).thenReturn(123L);
        StageBatchInfoBo dateInfo = mock(StageBatchInfoBo.class);
        StageBatchInfoBo caInfo = mock(StageBatchInfoBo.class);
        StageBatchInfoBo stampInfo = mock(StageBatchInfoBo.class);
        StageBatchInfoBo smsInfo = mock(StageBatchInfoBo.class);
        when(wmEcontractDateWrapperService.wrap(eq(batchContextBo), any(Map.class))).thenReturn(dateInfo);
        when(wmEcontractCAPoiWrapperService.wrap(batchContextBo)).thenReturn(caInfo);
        when(wmEcontractStampPoiWrapperService.wrap(eq(batchContextBo), any(List.class))).thenReturn(stampInfo);
        when(wmEcontractSmsWrapperService.wrap(batchContextBo)).thenReturn(smsInfo);
        // act
        EcontractBatchBo result = wmEcontractVipCardApplyService.wrapEcontractBo(batchContextBo);
        // assert
        assertNotNull(result);
        assertEquals(4, result.getStageInfoBoList().size());
        assertTrue(result.getStageInfoBoList().contains(dateInfo));
        assertTrue(result.getStageInfoBoList().contains(caInfo));
        assertTrue(result.getStageInfoBoList().contains(stampInfo));
        assertTrue(result.getStageInfoBoList().contains(smsInfo));
        assertEquals(SignFlowConstant.POI_STAMP_FOR_DELIVERY, result.getEcontractType());
        assertEquals("123", result.getEcontractBizId());
    }

    /**
     * Test wrapEcontractBo when source is OTHER
     */
    @Test
    public void testWrapEcontractBo_WhenSourceIsOther() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = mock(EcontractBatchContextBo.class);
        when(batchContextBo.getSource()).thenReturn(WmSignConstant.OTHER);
        when(batchContextBo.getBatchId()).thenReturn(123L);
        StageBatchInfoBo dateInfo = mock(StageBatchInfoBo.class);
        StageBatchInfoBo caInfo = mock(StageBatchInfoBo.class);
        StageBatchInfoBo stampInfo = mock(StageBatchInfoBo.class);
        StageBatchInfoBo smsInfo = mock(StageBatchInfoBo.class);
        when(wmEcontractDateWrapperService.wrap(eq(batchContextBo), any(Map.class))).thenReturn(dateInfo);
        when(wmEcontractCAPoiWrapperService.wrap(batchContextBo)).thenReturn(caInfo);
        when(wmEcontractStampPoiWrapperService.wrap(eq(batchContextBo), any(List.class))).thenReturn(stampInfo);
        when(wmEcontractSmsWrapperService.wrap(batchContextBo)).thenReturn(smsInfo);
        // act
        EcontractBatchBo result = wmEcontractVipCardApplyService.wrapEcontractBo(batchContextBo);
        // assert
        assertNotNull(result);
        assertEquals(EcontractRecordSourceEnum.NORMAL, result.getEcontractBatchSource());
    }

    /**
     * Test wrapEcontractBo when DateWrapperService throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapEcontractBo_DateWrapperServiceThrowsException() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = mock(EcontractBatchContextBo.class);
        when(wmEcontractDateWrapperService.wrap(eq(batchContextBo), any(Map.class))).thenThrow(new WmCustomerException());
        // act
        wmEcontractVipCardApplyService.wrapEcontractBo(batchContextBo);
    }

    /**
     * Test wrapEcontractBo when CAPoiWrapperService throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapEcontractBo_CAPoiWrapperServiceThrowsException() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = mock(EcontractBatchContextBo.class);
        StageBatchInfoBo dateInfo = mock(StageBatchInfoBo.class);
        when(wmEcontractDateWrapperService.wrap(eq(batchContextBo), any(Map.class))).thenReturn(dateInfo);
        when(wmEcontractCAPoiWrapperService.wrap(batchContextBo)).thenThrow(new WmCustomerException());
        // act
        wmEcontractVipCardApplyService.wrapEcontractBo(batchContextBo);
    }

    /**
     * Test wrapEcontractBo with null input
     */
    @Test(expected = NullPointerException.class)
    public void testWrapEcontractBo_NullInput() throws Throwable {
        // act
        wmEcontractVipCardApplyService.wrapEcontractBo(null);
    }
}
