package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet.nationalsubsidy;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliverySG2_2InfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidyPerformanceServiceSg22NewKsPdfMakerTest {

    @InjectMocks
    private NationalSubsidyPerformanceServiceSg22NewKsPdfMaker maker;

    private EcontractBatchContextBo originContext;

    private EcontractBatchMiddleBo middleContext;

    private EcontractDeliveryInfoBo deliveryInfoBo;

    private List<EcontractDeliveryInfoBo> deliveryInfoList;

    @Before
    public void setUp() {
        // Create real objects instead of mocks
        originContext = new EcontractBatchContextBo();
        middleContext = new EcontractBatchMiddleBo();
        // Setup delivery info
        deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setSupportMTDelivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setSupportSGV2_2Delivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setDeliveryTypeUUID("uuid1");
        EcontractDeliverySG2_2InfoBo sg22InfoBo = new EcontractDeliverySG2_2InfoBo();
        sg22InfoBo.setSupportNewKS(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setEcontractDeliverySG2_2InfoBo(sg22InfoBo);
        deliveryInfoList = Lists.newArrayList(deliveryInfoBo);
        // Setup customer info
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerName("TestCustomer");
        originContext.setCustomerInfoBo(customerInfoBo);
        // Setup PDF data map
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_NKS_PERFORMANCE.getName(), Lists.newArrayList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);
        // Create a spy of the maker to mock the extractDeliveryInfo method
        maker = spy(maker);
        try {
            doReturn(deliveryInfoList).when(maker).extractDeliveryInfo(any(EcontractBatchContextBo.class), any(EcontractBatchMiddleBo.class), eq(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_NKS_PERFORMANCE.getName()));
        } catch (WmCustomerException e) {
            e.printStackTrace();
        }
    }

    /**
     * Test normal case with valid delivery info
     */
    @Test
    public void testMakePdfContentInfoBo_NormalCase() throws Throwable {
        // arrange
        deliveryInfoBo.setSpcialFeeExpirationTime(null);
        deliveryInfoBo.setSpcialFeeInfo(null);
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMockedStatic = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<DateUtil> dateUtilMockedStatic = Mockito.mockStatic(DateUtil.class)) {
            // Mock the first ConfigUtilAdapter.getInt call for template ID
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SG_22_NEW_KS_TEMPLATE_ID", 2373)).thenReturn(2373);
            // Mock the second ConfigUtilAdapter.getInt call for template version
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_22_NEW_KS_TEMPLATE_VERSION", 0)).thenReturn(0);
            // 2023-01-01 12:00:00
            // 2023-01-01 12:00:00
            dateUtilMockedStatic.when(DateUtil::unixTime).thenReturn(1672567200);
            dateUtilMockedStatic.when(() -> DateUtil.secondsToString(1672567200)).thenReturn("2023-01-01 12:00:00");
            // act
            PdfContentInfoBo result = maker.makePdfContentInfoBo(originContext, middleContext);
            // assert
            assertNotNull(result);
            assertEquals(2373, result.getPdfTemplateId().intValue());
            assertEquals(0, result.getPdfTemplateVersion().intValue());
            assertEquals("false", result.getPdfMetaContent().get("hasSpecialExpireTime"));
            assertEquals("false", result.getPdfMetaContent().get("hasSpecialFeeInfo"));
            assertEquals("2023-01-01 12:00:00", result.getPdfMetaContent().get("signTime"));
            assertEquals("TestCustomer", result.getPdfMetaContent().get("partAStampName"));
            assertEquals(PdfConstant.POI_SIGNKEY, result.getPdfMetaContent().get("partAEstamp"));
            assertEquals(ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc(), result.getPdfMetaContent().get("partBStampName"));
            assertEquals(PdfConstant.MT_SH_SIGNKEY, result.getPdfMetaContent().get("partBEstamp"));
        }
    }

    /**
     * Test case with special expiration time
     */
    @Test
    public void testMakePdfContentInfoBo_WithSpecialExpirationTime() throws Throwable {
        // arrange
        deliveryInfoBo.setSpcialFeeExpirationTime("2023-12-31");
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMockedStatic = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<DateUtil> dateUtilMockedStatic = Mockito.mockStatic(DateUtil.class)) {
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SG_22_NEW_KS_TEMPLATE_ID", 2373)).thenReturn(2373);
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_22_NEW_KS_TEMPLATE_VERSION", 0)).thenReturn(0);
            dateUtilMockedStatic.when(DateUtil::unixTime).thenReturn(1672567200);
            dateUtilMockedStatic.when(() -> DateUtil.secondsToString(1672567200)).thenReturn("2023-01-01 12:00:00");
            // act
            PdfContentInfoBo result = maker.makePdfContentInfoBo(originContext, middleContext);
            // assert
            assertNotNull(result);
            assertEquals("true", result.getPdfMetaContent().get("hasSpecialExpireTime"));
        }
    }

    /**
     * Test case with special fee info
     */
    @Test
    public void testMakePdfContentInfoBo_WithSpecialFeeInfo() throws Throwable {
        // arrange
        deliveryInfoBo.setSpcialFeeInfo("Special Fee");
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMockedStatic = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<DateUtil> dateUtilMockedStatic = Mockito.mockStatic(DateUtil.class)) {
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SG_22_NEW_KS_TEMPLATE_ID", 2373)).thenReturn(2373);
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_22_NEW_KS_TEMPLATE_VERSION", 0)).thenReturn(0);
            dateUtilMockedStatic.when(DateUtil::unixTime).thenReturn(1672567200);
            dateUtilMockedStatic.when(() -> DateUtil.secondsToString(1672567200)).thenReturn("2023-01-01 12:00:00");
            // act
            PdfContentInfoBo result = maker.makePdfContentInfoBo(originContext, middleContext);
            // assert
            assertNotNull(result);
            assertEquals("true", result.getPdfMetaContent().get("hasSpecialFeeInfo"));
        }
    }

    /**
     * Test case with empty delivery info list
     */
    @Test
    public void testMakePdfContentInfoBo_EmptyDeliveryList() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> emptyList = new ArrayList<>();
        doReturn(emptyList).when(maker).extractDeliveryInfo(any(EcontractBatchContextBo.class), any(EcontractBatchMiddleBo.class), eq(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_NKS_PERFORMANCE.getName()));
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMockedStatic = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<DateUtil> dateUtilMockedStatic = Mockito.mockStatic(DateUtil.class)) {
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SG_22_NEW_KS_TEMPLATE_ID", 2373)).thenReturn(2373);
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_22_NEW_KS_TEMPLATE_VERSION", 0)).thenReturn(0);
            dateUtilMockedStatic.when(DateUtil::unixTime).thenReturn(1672567200);
            dateUtilMockedStatic.when(() -> DateUtil.secondsToString(1672567200)).thenReturn("2023-01-01 12:00:00");
            // act
            PdfContentInfoBo result = maker.makePdfContentInfoBo(originContext, middleContext);
            // assert
            assertNotNull(result);
            assertEquals(0, result.getPdfBizContent().size());
            assertEquals("false", result.getPdfMetaContent().get("hasSpecialExpireTime"));
            assertEquals("false", result.getPdfMetaContent().get("hasSpecialFeeInfo"));
        }
    }

    /**
     * Test case with non-matching delivery type
     */
    @Test
    public void testMakePdfContentInfoBo_NonMatchingDeliveryType() throws Throwable {
        // arrange
        deliveryInfoBo.setSupportMTDelivery("not_support");
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMockedStatic = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<DateUtil> dateUtilMockedStatic = Mockito.mockStatic(DateUtil.class)) {
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SG_22_NEW_KS_TEMPLATE_ID", 2373)).thenReturn(2373);
            configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_22_NEW_KS_TEMPLATE_VERSION", 0)).thenReturn(0);
            dateUtilMockedStatic.when(DateUtil::unixTime).thenReturn(1672567200);
            dateUtilMockedStatic.when(() -> DateUtil.secondsToString(1672567200)).thenReturn("2023-01-01 12:00:00");
            // act
            PdfContentInfoBo result = maker.makePdfContentInfoBo(originContext, middleContext);
            // assert
            assertNotNull(result);
            assertEquals(0, result.getPdfBizContent().size());
        }
    }
}
