package com.sankuai.meituan.waimai.customer.mq.consumer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.crm.ticket.thrift.constant.WmTicketStatusEnumV1;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmTicketNoticeDTO;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.CustomerOwnerApplyAuditService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyAuditListenerTest {

    private static final int ERROR_CODE = -1;

    private static final int CUSTOMER_OWNER_APPLY_TICKET_FLOW_ID = 3971;

    @InjectMocks
    private WmCustomerOwnerApplyAuditListener listener;

    @Mock
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    @Mock
    private CustomerOwnerApplyAuditService customerOwnerApplyAuditService;

    private MockedStatic<MccCustomerConfig> mockedMccCustomerConfig;

    @Before
    public void setUp() {
        // Mock static method of MccCustomerConfig
        mockedMccCustomerConfig = mockStatic(MccCustomerConfig.class);
        mockedMccCustomerConfig.when(MccCustomerConfig::getCustomerOwnerApplyTicketFlowID).thenReturn(CUSTOMER_OWNER_APPLY_TICKET_FLOW_ID);
    }

    @After
    public void tearDown() {
        if (mockedMccCustomerConfig != null) {
            mockedMccCustomerConfig.close();
        }
    }

    /**
     * Test when ticket type doesn't match customer owner apply ticket flow ID
     */
    @Test
    public void testRecvMessage_WhenTicketTypeNotMatch() throws Throwable {
        // arrange
        WmTicketNoticeDTO notice = new WmTicketNoticeDTO();
        // Different from CustomerOwnerApplyTicketFlowID
        notice.setTicketType(9999);
        notice.setTicketStatus(WmTicketStatusEnumV1.TICKET_STATUS_CLOSED.getStatus());
        String messageBody = JSONObject.toJSONString(notice);
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, messageBody);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = listener.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(wmCustomerOwnerApplyAuditDao, never()).getByTicketId(any());
        verify(customerOwnerApplyAuditService, never()).dealApplyAuditResult(any(), any());
    }

    /**
     * Test when audit record is not found by ticket ID
     */
    @Test
    public void testRecvMessage_WhenAuditRecordNotFound() throws Throwable {
        // arrange
        WmTicketNoticeDTO notice = new WmTicketNoticeDTO();
        notice.setTicketType(CUSTOMER_OWNER_APPLY_TICKET_FLOW_ID);
        notice.setTicketStatus(WmTicketStatusEnumV1.TICKET_STATUS_CLOSED.getStatus());
        notice.setTicketId(123);
        String messageBody = JSONObject.toJSONString(notice);
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, messageBody);
        MessagetContext context = new MessagetContext();
        when(wmCustomerOwnerApplyAuditDao.getByTicketId(notice.getTicketId())).thenReturn(null);
        // act
        ConsumeStatus result = listener.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(wmCustomerOwnerApplyAuditDao).getByTicketId(notice.getTicketId());
        verify(customerOwnerApplyAuditService, never()).dealApplyAuditResult(any(), any());
    }

    /**
     * Test when audit record exists and process succeeds
     */
    @Test
    public void testRecvMessage_WhenProcessSucceeds() throws Throwable {
        // arrange
        WmTicketNoticeDTO notice = new WmTicketNoticeDTO();
        notice.setTicketType(CUSTOMER_OWNER_APPLY_TICKET_FLOW_ID);
        notice.setTicketStatus(WmTicketStatusEnumV1.TICKET_STATUS_CLOSED.getStatus());
        notice.setTicketId(123);
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setId(1);
        audit.setTaskId(123);
        String messageBody = JSONObject.toJSONString(notice);
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, messageBody);
        MessagetContext context = new MessagetContext();
        when(wmCustomerOwnerApplyAuditDao.getByTicketId(notice.getTicketId())).thenReturn(audit);
        doNothing().when(customerOwnerApplyAuditService).dealApplyAuditResult(any(), any());
        // act
        ConsumeStatus result = listener.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(wmCustomerOwnerApplyAuditDao).getByTicketId(notice.getTicketId());
        verify(customerOwnerApplyAuditService).dealApplyAuditResult(notice, audit);
    }

    /**
     * Test when exception occurs during processing
     */
    @Test
    public void testRecvMessage_WhenExceptionOccurs() throws Throwable {
        // arrange
        WmTicketNoticeDTO notice = new WmTicketNoticeDTO();
        notice.setTicketType(CUSTOMER_OWNER_APPLY_TICKET_FLOW_ID);
        notice.setTicketStatus(WmTicketStatusEnumV1.TICKET_STATUS_CLOSED.getStatus());
        notice.setTicketId(123);
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setId(1);
        audit.setTaskId(123);
        String messageBody = JSONObject.toJSONString(notice);
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, messageBody);
        MessagetContext context = new MessagetContext();
        when(wmCustomerOwnerApplyAuditDao.getByTicketId(notice.getTicketId())).thenReturn(audit);
        doThrow(new WmCustomerException(ERROR_CODE, "Test exception")).when(customerOwnerApplyAuditService).dealApplyAuditResult(any(), any());
        // act
        ConsumeStatus result = listener.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
        verify(wmCustomerOwnerApplyAuditDao).getByTicketId(notice.getTicketId());
        verify(customerOwnerApplyAuditService).dealApplyAuditResult(notice, audit);
    }
}
