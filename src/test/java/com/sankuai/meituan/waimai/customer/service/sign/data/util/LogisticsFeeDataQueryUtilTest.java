package com.sankuai.meituan.waimai.customer.service.sign.data.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class LogisticsFeeDataQueryUtilTest {

    private EcontractDataSourceBo mockDataSourceBo;

    @Before
    public void setUp() {
        mockDataSourceBo = spy(new EcontractDataSourceBo());
    }

    /**
     * Test case for empty input list
     */
    @Test
    public void testExtractGroupMapWithEmptyList() throws Throwable {
        // Given
        List<EcontractDataSourceBo> dataSourceBoList = Collections.emptyList();
        // When
        Map<Integer, List<List<Long>>> result = LogisticsFeeDataQueryUtil.extractGroupMap(dataSourceBoList);
        // Then
        assertTrue("Result map should be empty for empty input list", result.isEmpty());
    }

    /**
     * Test case for source with empty group list
     */
    @Test
    public void testExtractGroupMapWithEmptyGroupList() throws Throwable {
        // Given
        mockDataSourceBo.setSorceEnum(EcontractDataSourceEnum.LOGISTICS_FEE);
        mockDataSourceBo.setWmPoiIdGroupList(Collections.emptyList());
        List<EcontractDataSourceBo> dataSourceBoList = Collections.singletonList(mockDataSourceBo);
        // When
        Map<Integer, List<List<Long>>> result = LogisticsFeeDataQueryUtil.extractGroupMap(dataSourceBoList);
        // Then
        assertFalse("Result map should not be empty", result.isEmpty());
        assertEquals("Result map should contain one entry", 1, result.size());
        assertTrue("Result should contain LOGISTICS_FEE type key", result.containsKey(EcontractDataSourceEnum.LOGISTICS_FEE.getType()));
        assertTrue("Group list should be empty", result.get(EcontractDataSourceEnum.LOGISTICS_FEE.getType()).isEmpty());
        verify(mockDataSourceBo, times(1)).getSorceEnum();
        verify(mockDataSourceBo, times(1)).getWmPoiIdGroupList();
    }

    /**
     * Test case for source with non-empty group list
     */
    @Test
    public void testExtractGroupMapWithNonEmptyGroupList() throws Throwable {
        // Given
        List<Long> poiGroup = Arrays.asList(1L, 2L, 3L);
        mockDataSourceBo.setSorceEnum(EcontractDataSourceEnum.LOGISTICS_HERON_ALL);
        mockDataSourceBo.setWmPoiIdGroupList(Collections.singletonList(poiGroup));
        List<EcontractDataSourceBo> dataSourceBoList = Collections.singletonList(mockDataSourceBo);
        // When
        Map<Integer, List<List<Long>>> result = LogisticsFeeDataQueryUtil.extractGroupMap(dataSourceBoList);
        // Then
        assertEquals("Result map should contain one entry", 1, result.size());
        assertTrue("Result should contain LOGISTICS_HERON_ALL type key", result.containsKey(EcontractDataSourceEnum.LOGISTICS_HERON_ALL.getType()));
        assertEquals("Group list should contain one group", 1, result.get(EcontractDataSourceEnum.LOGISTICS_HERON_ALL.getType()).size());
        assertEquals("Group should match input group", poiGroup, result.get(EcontractDataSourceEnum.LOGISTICS_HERON_ALL.getType()).get(0));
        verify(mockDataSourceBo, times(1)).getSorceEnum();
        verify(mockDataSourceBo, times(1)).getWmPoiIdGroupList();
    }

    /**
     * Test case for multiple sources
     */
    @Test
    public void testExtractGroupMapWithMultipleSources() throws Throwable {
        // Given
        EcontractDataSourceBo mockSource1 = spy(new EcontractDataSourceBo());
        mockSource1.setSorceEnum(EcontractDataSourceEnum.LOGISTICS_FEE);
        mockSource1.setWmPoiIdGroupList(Collections.singletonList(Arrays.asList(1L, 2L)));
        EcontractDataSourceBo mockSource2 = spy(new EcontractDataSourceBo());
        mockSource2.setSorceEnum(EcontractDataSourceEnum.LOGISTICS_HERON_ALL);
        mockSource2.setWmPoiIdGroupList(Collections.singletonList(Arrays.asList(3L, 4L)));
        List<EcontractDataSourceBo> dataSourceBoList = Arrays.asList(mockSource1, mockSource2);
        // When
        Map<Integer, List<List<Long>>> result = LogisticsFeeDataQueryUtil.extractGroupMap(dataSourceBoList);
        // Then
        assertEquals("Result map should contain two entries", 2, result.size());
        assertTrue("Result should contain both source type keys", result.containsKey(EcontractDataSourceEnum.LOGISTICS_FEE.getType()) && result.containsKey(EcontractDataSourceEnum.LOGISTICS_HERON_ALL.getType()));
        assertEquals("First group should match first source group", Arrays.asList(1L, 2L), result.get(EcontractDataSourceEnum.LOGISTICS_FEE.getType()).get(0));
        assertEquals("Second group should match second source group", Arrays.asList(3L, 4L), result.get(EcontractDataSourceEnum.LOGISTICS_HERON_ALL.getType()).get(0));
        verify(mockSource1, times(1)).getSorceEnum();
        verify(mockSource1, times(1)).getWmPoiIdGroupList();
        verify(mockSource2, times(1)).getSorceEnum();
        verify(mockSource2, times(1)).getWmPoiIdGroupList();
    }

    /**
     * Test case for null group list
     */
    @Test
    public void testExtractGroupMapWithNullGroupList() throws Throwable {
        // Given
        mockDataSourceBo.setSorceEnum(EcontractDataSourceEnum.LOGISTICS_FEE);
        mockDataSourceBo.setWmPoiIdGroupList(null);
        List<EcontractDataSourceBo> dataSourceBoList = Collections.singletonList(mockDataSourceBo);
        // When
        Map<Integer, List<List<Long>>> result = LogisticsFeeDataQueryUtil.extractGroupMap(dataSourceBoList);
        // Then
        assertFalse("Result map should not be empty", result.isEmpty());
        assertEquals("Result map should contain one entry", 1, result.size());
        assertTrue("Result should contain LOGISTICS_FEE type key", result.containsKey(EcontractDataSourceEnum.LOGISTICS_FEE.getType()));
        assertNull("Group list should be null", result.get(EcontractDataSourceEnum.LOGISTICS_FEE.getType()));
        verify(mockDataSourceBo, times(1)).getSorceEnum();
        verify(mockDataSourceBo, times(1)).getWmPoiIdGroupList();
    }

    @Test
    public void testExtractGroupMapWithNonZeroType() throws Throwable {
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        dataSourceBo.setSorceEnum(EcontractDataSourceEnum.LOGISTICS_HERON_ALL);
        dataSourceBo.setWmPoiIdGroupList(Arrays.asList(Arrays.asList(1L, 2L, 3L)));
        List<EcontractDataSourceBo> dataSourceBoList = Arrays.asList(dataSourceBo);
        Map<Integer, List<List<Long>>> result = LogisticsFeeDataQueryUtil.extractGroupMap(dataSourceBoList);
        assertEquals(1, result.size());
        assertEquals(Arrays.asList(Arrays.asList(1L, 2L, 3L)), result.get(EcontractDataSourceEnum.LOGISTICS_HERON_ALL.getType()));
    }

    @Test
    public void testAssembleWmPoiIdGroupListNormal() throws Throwable {
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        EcontractDataPoiBizBo econtractDataPoiBizBo = new EcontractDataPoiBizBo();
        econtractDataPoiBizBo.setWmPoiId(1L);
        List<EcontractDataPoiBizBo> wmPoiIdAndBizIdList = Arrays.asList(econtractDataPoiBizBo);
        dataSourceBo.setWmPoiIdAndBizIdList(wmPoiIdAndBizIdList);
        LogisticsFeeDataQueryUtil.assembleWmPoiIdGroupList(dataSourceBo);
        assertNotNull(dataSourceBo.getWmPoiIdGroupList());
        assertEquals(1, dataSourceBo.getWmPoiIdGroupList().size());
        assertEquals(1L, dataSourceBo.getWmPoiIdGroupList().get(0).get(0).longValue());
    }

    @Test(expected = IndexOutOfBoundsException.class)
    public void testAssembleWmPoiIdGroupListEmptyList() throws Throwable {
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        dataSourceBo.setWmPoiIdAndBizIdList(Collections.emptyList());
        LogisticsFeeDataQueryUtil.assembleWmPoiIdGroupList(dataSourceBo);
    }

    @Test
    public void testAssembleWmPoiIdGroupListNullElement() throws Throwable {
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        EcontractDataPoiBizBo econtractDataPoiBizBo = new EcontractDataPoiBizBo();
        econtractDataPoiBizBo.setWmPoiId(null);
        List<EcontractDataPoiBizBo> wmPoiIdAndBizIdList = Arrays.asList(econtractDataPoiBizBo);
        dataSourceBo.setWmPoiIdAndBizIdList(wmPoiIdAndBizIdList);
        // Act
        LogisticsFeeDataQueryUtil.assembleWmPoiIdGroupList(dataSourceBo);
        // Assert: Verify that the method handles null wmPoiId gracefully
        assertNotNull(dataSourceBo.getWmPoiIdGroupList());
        assertEquals(1, dataSourceBo.getWmPoiIdGroupList().size());
        assertNull(dataSourceBo.getWmPoiIdGroupList().get(0).get(0));
    }

    @Test
    public void testExtractPoiBizBoGroupListWhenWmPoiIdGroupListIsNull() throws Throwable {
        List<List<Long>> wmPoiIdGroupList = Collections.emptyList();
        List<EcontractDataPoiBizBo> allWmPoiBizBoList = Arrays.asList(new EcontractDataPoiBizBo());
        List<List<EcontractDataPoiBizBo>> result = LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(wmPoiIdGroupList, allWmPoiBizBoList);
        assertTrue(result.isEmpty());
    }

    @Test(expected = WmCustomerException.class)
    public void testExtractPoiBizBoGroupListWhenAllWmPoiBizBoListIsNull() throws Throwable {
        List<List<Long>> wmPoiIdGroupList = Arrays.asList(Arrays.asList(1L));
        List<EcontractDataPoiBizBo> allWmPoiBizBoList = Collections.emptyList();
        LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(wmPoiIdGroupList, allWmPoiBizBoList);
    }

    @Test(expected = WmCustomerException.class)
    public void testExtractPoiBizBoGroupListWhenWmPoiIdListIsEmpty() throws Throwable {
        List<List<Long>> wmPoiIdGroupList = Arrays.asList(Collections.emptyList());
        List<EcontractDataPoiBizBo> allWmPoiBizBoList = Arrays.asList(new EcontractDataPoiBizBo());
        LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(wmPoiIdGroupList, allWmPoiBizBoList);
    }

    @Test(expected = WmCustomerException.class)
    public void testExtractPoiBizBoGroupListWhenPoiIdNotFound() throws Throwable {
        List<List<Long>> wmPoiIdGroupList = Arrays.asList(Arrays.asList(1L));
        EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
        poiBizBo.setWmPoiId(2L);
        List<EcontractDataPoiBizBo> allWmPoiBizBoList = Arrays.asList(poiBizBo);
        LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(wmPoiIdGroupList, allWmPoiBizBoList);
    }

    @Test
    public void testExtractPoiBizBoGroupListNormal() throws Throwable {
        EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
        poiBizBo.setWmPoiId(1L);
        List<EcontractDataPoiBizBo> allWmPoiBizBoList = Arrays.asList(poiBizBo);
        List<List<Long>> wmPoiIdGroupList = Arrays.asList(Arrays.asList(1L));
        List<List<EcontractDataPoiBizBo>> result = LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(wmPoiIdGroupList, allWmPoiBizBoList);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).size());
        assertEquals(poiBizBo, result.get(0).get(0));
    }
}