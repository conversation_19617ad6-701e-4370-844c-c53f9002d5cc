package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test cases for {@link WmCustomerOwnerApplyBusService#getAuditRemark}
 */
@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyBusServiceGetAuditRemarkTest {

    @InjectMocks
    private WmCustomerOwnerApplyBusService wmCustomerOwnerApplyBusService;

    @Mock
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    private Method getAuditRemarkMethod;

    @Before
    public void setUp() throws Exception {
        getAuditRemarkMethod = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("getAuditRemark", Integer.class);
        getAuditRemarkMethod.setAccessible(true);
    }

    /**
     * Test getAuditRemark when audit record exists
     * Should return audit result successfully
     */
    @Test
    public void testGetAuditRemark_WhenAuditExists_ShouldReturnRemark() throws Throwable {
        // arrange
        Integer applyId = 123;
        String expectedRemark = "Test Audit Result";
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setAuditResult(expectedRemark);
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId)).thenReturn(audit);
        // act
        String actualRemark = (String) getAuditRemarkMethod.invoke(wmCustomerOwnerApplyBusService, applyId);
        // assert
        assertEquals(expectedRemark, actualRemark);
    }

    /**
     * Test getAuditRemark when audit record does not exist
     * Should throw WmCustomerException
     */
    @Test
    public void testGetAuditRemark_WhenAuditNotExists_ShouldThrowException() throws Throwable {
        // arrange
        Integer applyId = 123;
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId)).thenReturn(null);
        try {
            // act
            getAuditRemarkMethod.invoke(wmCustomerOwnerApplyBusService, applyId);
            fail("Expected WmCustomerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            WmCustomerException actualException = (WmCustomerException) e.getTargetException();
            assertEquals(CustomerConstants.RESULT_CODE_ERROR, actualException.getCode());
            assertEquals("未查询到客户申请单审批信息", actualException.getMessage());
        } catch (Exception e) {
            fail("Unexpected exception: " + e);
        }
    }

    /**
     * Test getAuditRemark with null applyId
     * Should throw WmCustomerException
     */
    @Test
    public void testGetAuditRemark_WhenApplyIdNull_ShouldThrowException() throws Throwable {
        // arrange
        Integer applyId = null;
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId)).thenReturn(null);
        try {
            // act
            getAuditRemarkMethod.invoke(wmCustomerOwnerApplyBusService, applyId);
            fail("Expected WmCustomerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            WmCustomerException actualException = (WmCustomerException) e.getTargetException();
            assertEquals(CustomerConstants.RESULT_CODE_ERROR, actualException.getCode());
            assertEquals("未查询到客户申请单审批信息", actualException.getMessage());
        } catch (Exception e) {
            fail("Unexpected exception: " + e);
        }
    }

    /**
     * Test getAuditRemark when audit exists but has null result
     * Should return null
     */
    @Test
    public void testGetAuditRemark_WhenAuditExistsButNullResult_ShouldReturnNull() throws Throwable {
        // arrange
        Integer applyId = 123;
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setAuditResult(null);
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId)).thenReturn(audit);
        // act
        String actualRemark = (String) getAuditRemarkMethod.invoke(wmCustomerOwnerApplyBusService, applyId);
        // assert
        assertEquals(null, actualRemark);
    }

    /**
     * Verify exception message when audit not found
     */
    @Test
    public void testGetAuditRemark_WhenAuditNotExists_ShouldThrowExceptionWithCorrectMessage() throws Throwable {
        // arrange
        Integer applyId = 123;
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId)).thenReturn(null);
        try {
            // act
            getAuditRemarkMethod.invoke(wmCustomerOwnerApplyBusService, applyId);
            fail("Expected WmCustomerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            WmCustomerException actualException = (WmCustomerException) e.getTargetException();
            assertEquals(CustomerConstants.RESULT_CODE_ERROR, actualException.getCode());
            assertEquals("未查询到客户申请单审批信息", actualException.getMessage());
        } catch (Exception e) {
            fail("Unexpected exception: " + e);
        }
    }
}
