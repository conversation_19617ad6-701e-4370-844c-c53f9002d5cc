package com.sankuai.meituan.waimai.customer.ddd.adapter;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.dto.CustomerDTO;
import com.sankuai.nibcus.inf.customer.client.dto.QualificationDTO;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.nibcus.inf.customer.client.request.CustomerBasicRequiredField;
import com.sankuai.nibcus.inf.customer.client.request.CustomerGetByQualificationNumberRequest;
import com.sankuai.nibcus.inf.customer.client.response.CustomersGetResponse;
import com.sankuai.nibcus.inf.customer.client.service.CustomerThriftService;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class MtCustomerThriftServiceAdapterImplTest {

    @InjectMocks
    private MtCustomerThriftServiceAdapterImpl mtCustomerThriftServiceAdapter;

    @Mock
    private CustomerThriftService customerThriftService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private CustomerGetByQualificationNumberRequest buildRequest(String customerNumber) {
        CustomerGetByQualificationNumberRequest request = new CustomerGetByQualificationNumberRequest();
        request.setQualificationNumber(customerNumber);
        request.setBusinessLineId(BusinessLineEnum.WAI_MAI.getCode());
        CustomerBasicRequiredField requiredField = new CustomerBasicRequiredField();
        requiredField.setQualification(true);
        request.setCustomerBasicRequiredField(requiredField);
        return request;
    }

    /**
     * Test case for scenario where the service throws an exception.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumber_ServiceExceptionCase() throws Throwable {
        // arrange
        String customerNumber = "12345";
        CustomerGetByQualificationNumberRequest request = new CustomerGetByQualificationNumberRequest();
        request.setQualificationNumber(customerNumber);
        request.setBusinessLineId(BusinessLineEnum.WAI_MAI.getCode());
        when(customerThriftService.getCustomerByQualificationNumber(any(CustomerGetByQualificationNumberRequest.class))).thenThrow(new RuntimeException("Service Exception"));
        // act
        mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        // assert
        // Exception is expected
    }

    /**
     * Test case for scenario where the input customer number is null.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumber_NullInputCase() throws Throwable {
        // arrange
        String customerNumber = null;
        // act
        mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        // assert
        // Exception is expected
    }

    /**
     * Test case for scenario where the input customer number is empty.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumber_EmptyInputCase() throws Throwable {
        // arrange
        String customerNumber = "";
        // act
        mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        // assert
        // Exception is expected
    }

    /**
     * Test case for empty response scenario where no customer data is found.
     */
    @Test
    public void testGetCustomerByCustomerNumber_EmptyResponse() throws Throwable {
        // arrange
        String customerNumber = "12345";
        CustomerGetByQualificationNumberRequest request = buildRequest(customerNumber);
        CustomersGetResponse response = new CustomersGetResponse();
        response.setCustomers(new ArrayList<>());
        when(customerThriftService.getCustomerByQualificationNumber(any(CustomerGetByQualificationNumberRequest.class))).thenReturn(response);
        // act
        List<WmCustomerDB> result = mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(customerThriftService, times(1)).getCustomerByQualificationNumber(any(CustomerGetByQualificationNumberRequest.class));
    }

    /**
     * Test case for exception scenario where an exception is thrown by the thrift service.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumber_ExceptionScenario() throws Throwable {
        // arrange
        String customerNumber = "12345";
        CustomerGetByQualificationNumberRequest request = buildRequest(customerNumber);
        when(customerThriftService.getCustomerByQualificationNumber(any(CustomerGetByQualificationNumberRequest.class))).thenThrow(new RuntimeException("Service unavailable"));
        // act
        mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        // assert
        // Exception is expected
    }

    /**
     * Test case for null input scenario where the customer number is null.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumber_NullInput() throws Throwable {
        // arrange
        String customerNumber = null;
        // act
        mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        // assert
        // Exception is expected
    }

    /**
     * Test case for invalid business line scenario where the business line ID is invalid.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumber_InvalidBusinessLine() throws Throwable {
        // arrange
        String customerNumber = "12345";
        CustomerGetByQualificationNumberRequest request = new CustomerGetByQualificationNumberRequest();
        request.setQualificationNumber(customerNumber);
        request.setBusinessLineId(-1L);
        when(customerThriftService.getCustomerByQualificationNumber(any(CustomerGetByQualificationNumberRequest.class))).thenThrow(new RuntimeException("Invalid business line"));
        // act
        mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        // assert
        // Exception is expected
    }

    /**
     * Test case for null response scenario where the response from the thrift service is null.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumber_NullResponse() throws Throwable {
        // arrange
        String customerNumber = "12345";
        CustomerGetByQualificationNumberRequest request = buildRequest(customerNumber);
        when(customerThriftService.getCustomerByQualificationNumber(any(CustomerGetByQualificationNumberRequest.class))).thenReturn(null);
        // act
        mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        // assert
        // Exception is expected
    }
}
