package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTSHWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignFlowConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfVirtualSelfSubApplyServiceTest extends BaseStaticMockTest  {

    @Spy
    @InjectMocks
    private WmEcontractPhfVirtualSelfSubApplyService service;

    @Mock
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Mock
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Mock
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Mock
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Mock
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Mock
    private WmEcontractStampMTSHWrapperService wmEcontractStampMTSHWrapperService;

    private EcontractBatchContextBo batchContextBo;

    private List<String> flowList;

    private StageBatchInfoBo stageBatchInfoBo;

    @Before
    public void setUp() {
        super.init();
        mccConfigMockedStatic.when(MccConfig::getPhfEcontractToken).thenReturn("USR_phf_17ee8afd-b69f-4c");

        batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchId(123L);
        batchContextBo.setSource("BATCH_PLATFORM");
        // Setup task map with proper context
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName());
        // Create delivery info
        EcontractBatchDeliveryInfoBo deliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        List<EcontractDeliveryInfoBo> deliveryInfoBoList = new ArrayList<>();
        EcontractDeliveryInfoBo infoBo = new EcontractDeliveryInfoBo();
        EcontractDeliveryPhfInfoBo phfInfoBo = new EcontractDeliveryPhfInfoBo();
        phfInfoBo.setWmPoiId("12345");
        infoBo.setEcontractDeliveryPhfInfoBo(phfInfoBo);
        deliveryInfoBoList.add(infoBo);
        deliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryInfoBoList);
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        taskMap.put(1L, taskBo);
        batchContextBo.setTaskIdAndTaskMap(taskMap);
        flowList = Lists.newArrayList("phf_virtual_self_12345");
        stageBatchInfoBo = new StageBatchInfoBo();
    }

    /**
     * Test successful execution with all parameters
     */
    @Test
    public void testWrapEcontractBoSuccess() throws Throwable {
        // arrange
        // act
        EcontractBatchBo result = service.wrapEcontractBo(batchContextBo);
        // assert
        assertNotNull(result);
        assertEquals("123", result.getEcontractBizId());
        assertEquals(SignFlowConstant.TYPE_PHF_BATCH_SIGN_FLOW, result.getEcontractType());
        assertEquals(6, result.getStageInfoBoList().size());
    }

    /**


    /**
     * Test when input batchContextBo is null
     */
    @Test(expected = NullPointerException.class)
    public void testWrapEcontractBoWithNullInput() throws Throwable {
        // act
        service.wrapEcontractBo(null);
    }

    /**
     * Test with empty flow list
     */
    @Test
    public void testWrapEcontractBoWithEmptyFlowList() throws Throwable {
        // arrange
        EcontractBatchDeliveryInfoBo emptyDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        emptyDeliveryInfoBo.setEcontractDeliveryInfoBoList(new ArrayList<>());
        EcontractTaskBo taskBo = batchContextBo.getTaskIdAndTaskMap().get(1L);
        taskBo.setApplyContext(JSON.toJSONString(emptyDeliveryInfoBo));
        // act & assert
        try {
            service.wrapEcontractBo(batchContextBo);
            fail("Expected WmCustomerException");
        } catch (WmCustomerException e) {
            assertEquals("原始数据缺失，pdf封装失败", e.getMessage());
        }
    }

    /**
     * Test with different source value
     */
    @Test
    public void testWrapEcontractBoWithDifferentSource() throws Throwable {
        // arrange
        batchContextBo.setSource("OTHER");
        // act
        EcontractBatchBo result = service.wrapEcontractBo(batchContextBo);
        // assert
        assertNotNull(result);
        assertEquals("NORMAL", result.getEcontractBatchSource().name());
    }
}