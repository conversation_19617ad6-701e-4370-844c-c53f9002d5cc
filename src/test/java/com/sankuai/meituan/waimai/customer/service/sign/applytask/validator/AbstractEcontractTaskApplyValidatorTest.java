package com.sankuai.meituan.waimai.customer.service.sign.applytask.validator;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractEcontractTaskApplyValidatorTest {

    @Mock
    private WmPoiClient wmPoiClient;

    @InjectMocks
    private AbstractEcontractTaskApplyValidator validator = new AbstractEcontractTaskApplyValidator() {

        @Override
        public void validate(EcontractTaskApplyBo applyBo) throws WmCustomerException {
        }

        @Override
        public EcontractTaskApplyTypeEnum getApplyType() {
            return null;
        }
    };

    /**
     * Test case: Normal case with mixed POIs (some with label, some without)
     */
    @Test
    public void testGetNoLabelIdWmPoiId_MixedPOIs() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo applyBo = createApplyBo(Arrays.asList(1L, 2L, 3L));
        List<WmPoiDomain> poiDomains = Arrays.asList(createWmPoiDomain(1, "1,2,3"), createWmPoiDomain(2, "2,4"), createWmPoiDomain(3, "3,4,5"));
        when(wmPoiClient.pageGetWmPoiByWmPoiIdList(Arrays.asList(1L, 2L, 3L))).thenReturn(poiDomains);
        // act
        List<Long> result = validator.getNoLabelIdWmPoiId(applyBo, 1);
        // assert
        assertEquals(Arrays.asList(2L, 3L), result);
    }

    /**
     * Test case: Empty POI list
     */
    @Test
    public void testGetNoLabelIdWmPoiId_EmptyPOIList() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo applyBo = createApplyBo(Collections.emptyList());
        when(wmPoiClient.pageGetWmPoiByWmPoiIdList(Collections.emptyList())).thenReturn(Collections.emptyList());
        // act
        List<Long> result = validator.getNoLabelIdWmPoiId(applyBo, 1);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case: All POIs have the specified label
     */
    @Test
    public void testGetNoLabelIdWmPoiId_AllPOIsHaveLabel() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo applyBo = createApplyBo(Arrays.asList(1L, 2L));
        List<WmPoiDomain> poiDomains = Arrays.asList(createWmPoiDomain(1, "1,2,3"), createWmPoiDomain(2, "1,4,5"));
        when(wmPoiClient.pageGetWmPoiByWmPoiIdList(Arrays.asList(1L, 2L))).thenReturn(poiDomains);
        // act
        List<Long> result = validator.getNoLabelIdWmPoiId(applyBo, 1);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case: No POIs have the specified label
     */
    @Test
    public void testGetNoLabelIdWmPoiId_NoPOIsHaveLabel() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo applyBo = createApplyBo(Arrays.asList(1L, 2L));
        List<WmPoiDomain> poiDomains = Arrays.asList(createWmPoiDomain(1, "2,3,4"), createWmPoiDomain(2, "4,5,6"));
        when(wmPoiClient.pageGetWmPoiByWmPoiIdList(Arrays.asList(1L, 2L))).thenReturn(poiDomains);
        // act
        List<Long> result = validator.getNoLabelIdWmPoiId(applyBo, 1);
        // assert
        assertEquals(Arrays.asList(1L, 2L), result);
    }

    /**
     * Test case: WmCustomerException from wmPoiClient
     */
    @Test(expected = WmCustomerException.class)
    public void testGetNoLabelIdWmPoiId_WmCustomerException() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo applyBo = createApplyBo(Arrays.asList(1L));
        when(wmPoiClient.pageGetWmPoiByWmPoiIdList(Arrays.asList(1L))).thenThrow(new WmCustomerException());
        // act
        validator.getNoLabelIdWmPoiId(applyBo, 1);
    }

    private EcontractTaskApplyBo createApplyBo(List<Long> poiIds) {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        List<EcontractDataPoiBizBo> poiBizBoList = new ArrayList<>();
        for (Long poiId : poiIds) {
            EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
            poiBizBo.setWmPoiId(poiId);
            poiBizBoList.add(poiBizBo);
        }
        dataSourceBo.setWmPoiIdAndBizIdList(poiBizBoList);
        dataSourceBoList.add(dataSourceBo);
        applyBo.setDataSourceBoList(dataSourceBoList);
        return applyBo;
    }

    private WmPoiDomain createWmPoiDomain(int poiId, String labelIds) {
        WmPoiDomain domain = new WmPoiDomain();
        domain.setWmPoiId(poiId);
        domain.setLabelIds(labelIds);
        return domain;
    }
}
