package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TechnicalServiceSg20DeleteTest {

    private static final String TAB_DELIVERY = "delivery";

    @Mock
    private Map<String, Collection<SignTemplateEnum>> tabPdfMap;

    @Mock
    private Map<String, List<String>> pdfDataMap;

    private TechnicalServiceSg20Delete technicalServiceSg20Delete;

    @Before
    public void setUp() {
        technicalServiceSg20Delete = new TechnicalServiceSg20Delete();
    }

    /**
     * 测试pdfDataMap中TECHNICAL_SERVICE_SG_20的值为空，tabPdfMap中TAB_DELIVERY的集合为空的情况
     */
    @Test
    public void testDeleteWhenTabListIsNull() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_20.getName())).thenReturn(null);
        when(tabPdfMap.get(TAB_DELIVERY)).thenReturn(null);
        // act
        technicalServiceSg20Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, times(1)).get(TAB_DELIVERY);
        verifyNoMoreInteractions(tabPdfMap);
    }

    /**
     * 测试pdfDataMap中TECHNICAL_SERVICE_SG_20的值为空，tabPdfMap中TAB_DELIVERY的集合不为空的情况
     */
    @Test
    public void testDeleteWhenTabListContainsSg20() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_20.getName())).thenReturn(null);
        Collection<SignTemplateEnum> tabList = new ArrayList<>();
        tabList.add(SignTemplateEnum.TECHNICAL_SERVICE_SG_20);
        when(tabPdfMap.get(TAB_DELIVERY)).thenReturn(tabList);
        // act
        technicalServiceSg20Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        assertTrue(tabList.isEmpty());
    }

    /**
     * 测试pdfDataMap中TECHNICAL_SERVICE_SG_20的值不为空的情况
     */
    @Test
    public void testDeleteWhenPdfDataNotEmpty() throws Throwable {
        // arrange
        List<String> pdfData = Arrays.asList("test");
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_20.getName())).thenReturn(pdfData);
        // act
        technicalServiceSg20Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, never()).get(anyString());
        verifyNoMoreInteractions(tabPdfMap);
    }

    /**
     * 测试pdfDataMap为null的边界情况
     */
    @Test
    public void testDeleteWhenPdfDataMapIsNull() throws Throwable {
        // arrange
        Map<String, List<String>> nullPdfDataMap = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> technicalServiceSg20Delete.delete(tabPdfMap, nullPdfDataMap));
    }

    /**
     * 测试tabPdfMap为null的边界情况
     */
    @Test
    public void testDeleteWhenTabPdfMapIsNull() throws Throwable {
        // arrange
        Map<String, Collection<SignTemplateEnum>> nullTabPdfMap = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> technicalServiceSg20Delete.delete(nullTabPdfMap, pdfDataMap));
    }

    /**
     * 测试pdfDataMap中TECHNICAL_SERVICE_SG_20的值为空，tabPdfMap中TAB_DELIVERY的集合为空的情况
     */
    @Test
    public void testDeleteCase1() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_20.getName())).thenReturn(null);
        // act
        technicalServiceSg20Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, never()).remove(any(SignTemplateEnum.class));
    }

    /**
     * 测试pdfDataMap中TECHNICAL_SERVICE_SG_20的值不为空的情况
     */
    @Test
    public void testDeleteCase3() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_20.getName())).thenReturn(Collections.singletonList("value"));
        // act
        technicalServiceSg20Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, never()).remove(any(SignTemplateEnum.class));
    }
}