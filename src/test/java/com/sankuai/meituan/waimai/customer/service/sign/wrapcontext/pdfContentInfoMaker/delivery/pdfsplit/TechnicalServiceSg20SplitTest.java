package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class TechnicalServiceSg20SplitTest extends BaseStaticMockTest  {

    private TechnicalServiceSg20Split technicalServiceSg20Split;

    private EcontractDeliveryInfoBo deliveryInfoBo;

    private EcontractBatchMiddleBo middleContext;

    public static String MCC_CONFIG = "{\"TECHNICAL_SERVICE_SG_20\":true,\"TECHNICAL_SERVICE_SG_22\":true,\"PERFORMANCE_SERVICE_SG_22\":true,\"PERFORMANCE_SERVICE_SG_22_NKS\":true}";

    public static Map<String, Boolean> getGrayNewPlatformTemplateEnumMap() {
        return JSONObject.parseObject(MCC_CONFIG,
                new com.alibaba.fastjson.TypeReference<Map<String, Boolean>>() {});
    }

    @Before
    public void setUp() {
        super.setUp();
        technicalServiceSg20Split = new TechnicalServiceSg20Split();
        deliveryInfoBo = mock(EcontractDeliveryInfoBo.class);
        middleContext = mock(EcontractBatchMiddleBo.class);
    }

    /**
     * Test case where the fee mode is not SHANGOU.
     */
    @Test
    public void testSplit_NotShangou() throws Throwable {
        // arrange
        // WAIMAI
        when(deliveryInfoBo.getFeeMode()).thenReturn("1");
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        when(middleContext.getTabPdfMap()).thenReturn(tabPdfMap);
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        when(middleContext.getPdfDataMap()).thenReturn(pdfDataMap);
        when(MccConfig.getGrayNewPlatformTemplateEnumMap()).thenReturn(getGrayNewPlatformTemplateEnumMap());

        // act
        technicalServiceSg20Split.split(deliveryInfoBo, middleContext);
        // assert
        assertTrue(pdfDataMap.isEmpty());
    }

    /**
     * Test case where the tabPdfMap does not contain TECHNICAL_SERVICE_SG_20.
     */
    @Test
    public void testSplit_NoTechnicalServiceSg20() throws Throwable {
        // arrange
        // SHANGOU
        when(deliveryInfoBo.getFeeMode()).thenReturn("2");
        when(deliveryInfoBo.getDeliveryTypeUUID()).thenReturn("UUID-789");
        when(deliveryInfoBo.getWmPoiId()).thenReturn("WM-POI-3");
        when(MccConfig.getGrayNewPlatformTemplateEnumMap()).thenReturn(getGrayNewPlatformTemplateEnumMap());


        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        tabPdfMap.put("TAB_DELIVERY", new ArrayList<>());
        when(middleContext.getTabPdfMap()).thenReturn(tabPdfMap);
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        when(middleContext.getPdfDataMap()).thenReturn(pdfDataMap);
        // act
        technicalServiceSg20Split.split(deliveryInfoBo, middleContext);
        // assert
        assertTrue(pdfDataMap.isEmpty());
    }
}