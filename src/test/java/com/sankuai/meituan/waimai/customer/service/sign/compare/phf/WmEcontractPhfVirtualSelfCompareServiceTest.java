package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfVirtualSelfCompareServiceTest {

    private WmEcontractPhfVirtualSelfCompareService wmEcontractPhfVirtualSelfCompareService;

    @Before
    public void setUp() {
        wmEcontractPhfVirtualSelfCompareService = new WmEcontractPhfVirtualSelfCompareService();
    }

    /**
     * 测试 getPdfContentInfoBoMapKeyByWmPoiId 方法，当 wmPoiId 为 null 时
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiIdWhenWmPoiIdIsNull() throws Throwable {
        // arrange
        Long wmPoiId = null;
        // act
        String result = wmEcontractPhfVirtualSelfCompareService.getPdfContentInfoBoMapKeyByWmPoiId(wmPoiId);
        // assert
        assertNotNull(result);
        assertTrue(result.contains(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL_SELF.name()));
        assertTrue(result.contains("null"));
    }

    /**
     * 测试 getPdfContentInfoBoMapKeyByWmPoiId 方法，当 wmPoiId 为正常值时
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiIdWhenWmPoiIdIsNormal() throws Throwable {
        // arrange
        Long wmPoiId = 123L;
        // act
        String result = wmEcontractPhfVirtualSelfCompareService.getPdfContentInfoBoMapKeyByWmPoiId(wmPoiId);
        // assert
        assertNotNull(result);
        assertTrue(result.contains(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL_SELF.name()));
        assertTrue(result.contains(String.valueOf(wmPoiId)));
    }
}