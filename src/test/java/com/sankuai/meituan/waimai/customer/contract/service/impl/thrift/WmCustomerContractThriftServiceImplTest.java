package com.sankuai.meituan.waimai.customer.contract.service.impl.thrift;


import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.ContractLogService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.thrift.agent.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.agent.service.WmCommercialAgentInfoThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.request.ContractQueryReqParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.response.ContractQueryResp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

/**
 * WmCustomerContractThriftServiceImplTest
 *
 * @Author: wangyongfang
 * @Date: 2025-02-08
 */
public class WmCustomerContractThriftServiceImplTest {

    @InjectMocks
    private WmCustomerContractThriftServiceImpl service;

    @Mock
    private IWmCustomerRealService wmLeafCustomerRealService;
    @Mock
    private WmContractService wmContractService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试门店ID为负数的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testGetAuditedContractBoListByCusIdAndType_InvalidWmPoiId() throws Throwable {
        ContractQueryReqParam reqParam = new ContractQueryReqParam(-1L, Collections.singletonList(1), 1, "test");
        service.getAuditedContractBoListByCusIdAndType(reqParam);
        Assert.fail();
    }

    /**
     * 测试合同类型为空的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testGetAuditedContractBoListByCusIdAndType_EmptyTypes() throws Throwable {
        ContractQueryReqParam reqParam = new ContractQueryReqParam(1L, Collections.emptyList(), 1, "test");
        service.getAuditedContractBoListByCusIdAndType(reqParam);
        Assert.fail();
    }

    /**
     * 测试门店未关联客户的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testGetAuditedContractBoListByCusIdAndType_NoCustomer() throws Throwable {
        ContractQueryReqParam reqParam = new ContractQueryReqParam(1L, Collections.singletonList(1), 1, "test");
        when(wmLeafCustomerRealService.selectWmCustomerIdByWmPoiId(anyLong())).thenReturn(null);
        service.getAuditedContractBoListByCusIdAndType(reqParam);
        Assert.fail();
    }

    /**
     * 测试查询合同异常的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testGetAuditedContractBoListByCusIdAndType_QueryException() throws Throwable {
        ContractQueryReqParam reqParam = new ContractQueryReqParam(1L, Collections.singletonList(1), 1, "test");
        when(wmLeafCustomerRealService.selectWmCustomerIdByWmPoiId(anyLong())).thenReturn(1);
        when(wmContractService.getAuditedContractBoListByCusIdAndType(anyLong(), anyList(), anyInt(), anyString())).thenThrow(new WmCustomerException(1, "查询合同异常"));
        service.getAuditedContractBoListByCusIdAndType(reqParam);
        Assert.fail();
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testGetAuditedContractBoListByCusIdAndType_Success() throws Throwable {
        ContractQueryReqParam reqParam = new ContractQueryReqParam(1L, Collections.singletonList(1), 1, "test");
        when(wmLeafCustomerRealService.selectWmCustomerIdByWmPoiId(anyLong())).thenReturn(1);
        when(wmContractService.getAuditedContractBoListByCusIdAndType(anyLong(), anyList(), anyInt(), anyString())).thenReturn(Collections.singletonList(new WmCustomerContractBo()));
        ContractQueryResp resp = service.getAuditedContractBoListByCusIdAndType(reqParam);
        verify(wmContractService, times(1)).getAuditedContractBoListByCusIdAndType(anyLong(), anyList(), anyInt(), anyString());
        assert resp.getContractList() != null && !resp.getContractList().isEmpty();
    }
}