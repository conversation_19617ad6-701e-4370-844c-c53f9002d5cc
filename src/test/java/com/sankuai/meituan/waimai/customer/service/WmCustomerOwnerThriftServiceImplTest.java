package com.sankuai.meituan.waimai.customer.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.WmCustomerOwnerApplyBusService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.WmCustomerOwnerApplyService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyListQueryBO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for WmCustomerOwnerThriftServiceImpl#getDoingCustomerListByTime
 */
@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerThriftServiceImplTest {

    @InjectMocks
    private WmCustomerOwnerThriftServiceImpl wmCustomerOwnerThriftService;

    @Mock
    private WmCustomerOwnerApplyBusService wmCustomerOwnerApplyBusService;

    @Mock
    private WmCustomerOwnerApplyService wmCustomerOwnerApplyService;

    /**
     * Test when input parameter is null
     */
    @Test(expected = WmCustomerException.class)
    public void testGetDoingCustomerListByTime_NullInput() throws Throwable {
        // arrange
        CustomerOwnerApplyListQueryBO queryDTO = null;
        // act
        wmCustomerOwnerThriftService.getDoingCustomerListByTime(queryDTO);
    }

    /**
     * Test when service returns empty list
     */
    @Test
    public void testGetDoingCustomerListByTime_EmptyResult() throws Throwable {
        // arrange
        CustomerOwnerApplyListQueryBO queryDTO = new CustomerOwnerApplyListQueryBO();
        when(wmCustomerOwnerApplyBusService.getDoingCustomerListByTime(queryDTO)).thenReturn(Collections.emptyList());
        // act
        List<Integer> result = wmCustomerOwnerThriftService.getDoingCustomerListByTime(queryDTO);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
        verify(wmCustomerOwnerApplyBusService).getDoingCustomerListByTime(queryDTO);
    }

    /**
     * Test when service returns valid customer list
     */
    @Test
    public void testGetDoingCustomerListByTime_ValidResult() throws Throwable {
        // arrange
        CustomerOwnerApplyListQueryBO queryDTO = new CustomerOwnerApplyListQueryBO();
        List<Integer> expectedList = Arrays.asList(1, 2, 3);
        when(wmCustomerOwnerApplyBusService.getDoingCustomerListByTime(queryDTO)).thenReturn(expectedList);
        // act
        List<Integer> result = wmCustomerOwnerThriftService.getDoingCustomerListByTime(queryDTO);
        // assert
        assertNotNull(result);
        assertEquals(expectedList.size(), result.size());
        assertEquals(expectedList, result);
        verify(wmCustomerOwnerApplyBusService).getDoingCustomerListByTime(queryDTO);
    }

    /**
     * Test when service throws WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testGetDoingCustomerListByTime_ServiceException() throws Throwable {
        // arrange
        CustomerOwnerApplyListQueryBO queryDTO = new CustomerOwnerApplyListQueryBO();
        when(wmCustomerOwnerApplyBusService.getDoingCustomerListByTime(queryDTO)).thenThrow(new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "Service error"));
        // act
        wmCustomerOwnerThriftService.getDoingCustomerListByTime(queryDTO);
    }

    /**
     * Test successful auto reject owner apply with valid customer ID
     */
    @Test
    public void testAutoRejectOwnerApplySuccess() throws Throwable {
        // arrange
        Integer customerId = 123;
        doNothing().when(wmCustomerOwnerApplyService).autoRejectOwnerApply(customerId);
        // act
        wmCustomerOwnerThriftService.autoRejectOwnerApply(customerId);
        // assert
        verify(wmCustomerOwnerApplyService, times(1)).autoRejectOwnerApply(customerId);
    }

    /**
     * Test auto reject owner apply with null customer ID
     */
    @Test(expected = WmCustomerException.class)
    public void testAutoRejectOwnerApplyWithNullCustomerId() throws Throwable {
        // arrange
        Integer customerId = null;
        // act
        wmCustomerOwnerThriftService.autoRejectOwnerApply(customerId);
        // assert
        verify(wmCustomerOwnerApplyService, never()).autoRejectOwnerApply(any());
    }

    /**
     * Test auto reject owner apply with zero customer ID
     */
    @Test(expected = WmCustomerException.class)
    public void testAutoRejectOwnerApplyWithZeroCustomerId() throws Throwable {
        // arrange
        Integer customerId = 0;
        // act
        wmCustomerOwnerThriftService.autoRejectOwnerApply(customerId);
        // assert
        verify(wmCustomerOwnerApplyService, never()).autoRejectOwnerApply(any());
    }

    /**
     * Test auto reject owner apply with negative customer ID
     */
    @Test(expected = WmCustomerException.class)
    public void testAutoRejectOwnerApplyWithNegativeCustomerId() throws Throwable {
        // arrange
        Integer customerId = -1;
        // act
        wmCustomerOwnerThriftService.autoRejectOwnerApply(customerId);
        // assert
        verify(wmCustomerOwnerApplyService, never()).autoRejectOwnerApply(any());
    }

    /**
     * Test auto reject owner apply when service throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testAutoRejectOwnerApplyWhenServiceThrowsException() throws Throwable {
        // arrange
        Integer customerId = 123;
        doThrow(new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "Service error")).when(wmCustomerOwnerApplyService).autoRejectOwnerApply(customerId);
        // act
        wmCustomerOwnerThriftService.autoRejectOwnerApply(customerId);
        // assert
        verify(wmCustomerOwnerApplyService, times(1)).autoRejectOwnerApply(customerId);
    }
}
