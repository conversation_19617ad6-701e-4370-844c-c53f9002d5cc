package com.sankuai.meituan.waimai.customer.util.business;

import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import org.junit.Assert;
import org.junit.Test;

public class WmCustomerUtilTest {

    /**
     * 测试 isSgDanDian 方法，当 customerRealType 为 null 时，应返回 false
     */
    @Test
    public void testIsSgDanDianWhenCustomerRealTypeIsNull() {
        // arrange
        Integer customerRealType = null;
        // act
        boolean result = WmCustomerUtil.isSgDanDian(customerRealType);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试 isSgDanDian 方法，当 customerRealType 不为 null，但不等于 DANDIAN_SG 的 value 时，应返回 false
     */
    @Test
    public void testIsSgDanDianWhenCustomerRealTypeIsNotDandanSg() {
        // arrange
        // 不等于 DANDIAN_SG 的 value
        Integer customerRealType = CustomerRealTypeEnum.QINGXUANZE.getValue();
        // act
        boolean result = WmCustomerUtil.isSgDanDian(customerRealType);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试 isSgDanDian 方法，当 customerRealType 不为 null，且等于 DANDIAN_SG 的 value 时，应返回 true
     */
    @Test
    public void testIsSgDanDianWhenCustomerRealTypeIsDandanSg() {
        // arrange
        // 等于 DANDIAN_SG 的 value
        Integer customerRealType = CustomerRealTypeEnum.DANDIAN_SG.getValue();
        // act
        boolean result = WmCustomerUtil.isSgDanDian(customerRealType);
        // assert
        Assert.assertTrue(result);
    }
}