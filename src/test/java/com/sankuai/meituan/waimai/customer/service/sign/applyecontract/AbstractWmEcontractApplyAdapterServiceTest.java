package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import cn.hutool.core.lang.Assert;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.ProcessTagEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.validation.constraints.AssertTrue;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractApplyAdapterServiceTest {

    @InjectMocks
    private AbstractWmEcontractApplyAdapterService service = new AbstractWmEcontractApplyAdapterService() {

        @Override
        public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo) throws TException, IllegalAccessException, WmCustomerException {
            return new EcontractBatchBo();
        }
    };

    @Mock
    private WmEcontractApplyService wmEcontractApplyService;

    /**
     * Test normal case with all parameters
     */
    @Test
    public void testApplyEContractNormalCase() throws TException, IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        String expectedRecordKey = "test-record-key";
        when(wmEcontractApplyService.applyBatchEcontract(any())).thenReturn(expectedRecordKey);
        // act
        String result = service.applyEContract(batchContextBo, "callbackDsl", true, true, "recordId");
        // assert
        assertEquals(expectedRecordKey, result);
        verify(wmEcontractApplyService).applyBatchEcontract(any());
    }

    /**
     * Test when notApplyEcontract returns true
     */
    @Test
    public void testApplyEContractWhenNotApplyEcontract() throws TException, IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(EcontractBatchTypeEnum.PHF_DELIVERY);
        batchContextBo.setTag(ProcessTagEnum.DR_TAG.getType());
        // act
        String result = service.applyEContract(batchContextBo, "callbackDsl", true, true, "recordId");
        // assert
        assertTrue(StringUtils.isBlank(result));
        verify(wmEcontractApplyService, never()).applyBatchEcontract(any());
    }

    /**
     * Test when wrapEcontractBo throws TException
     */
    @Test(expected = TException.class)
    public void testApplyEContractWhenWrapEcontractBoThrowsTException() throws TException, IllegalAccessException, WmCustomerException {
        // arrange
        AbstractWmEcontractApplyAdapterService errorService = new AbstractWmEcontractApplyAdapterService() {

            @Override
            public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo) throws TException {
                throw new TException("Test exception");
            }
        };
        // act
        errorService.applyEContract(new EcontractBatchContextBo(), "callbackDsl", true, true, "recordId");
    }

    /**
     * Test when applyBatchEcontract throws WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testApplyEContractWhenApplyBatchEcontractThrowsException() throws TException, IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        when(wmEcontractApplyService.applyBatchEcontract(any())).thenThrow(new WmCustomerException());
        // act
        service.applyEContract(batchContextBo, "callbackDsl", true, true, "recordId");
    }

    /**
     * Test with area separate save scenario
     */
    @Test
    public void testApplyEContractWithAreaSeparateSave() throws TException, IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        batchContextBo.setWmPoiIdList(Arrays.asList(1L, 2L));
        String expectedRecordKey = "test-record-key";
        when(wmEcontractApplyService.applyBatchEcontract(any())).thenReturn(expectedRecordKey);
        // act
        String result = service.applyEContract(batchContextBo, "callbackDsl", true, true, "recordId");
        // assert
        assertEquals(expectedRecordKey, result);
        verify(wmEcontractApplyService).applyBatchEcontract(any());
    }

    /**
     * Test with null econtractRecordId
     */
    @Test
    public void testApplyEContractWithNullRecordId() throws TException, IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        String expectedRecordKey = "test-record-key";
        when(wmEcontractApplyService.applyBatchEcontract(any())).thenReturn(expectedRecordKey);
        // act
        String result = service.applyEContract(batchContextBo, "callbackDsl", true, true, null);
        // assert
        assertEquals(expectedRecordKey, result);
        verify(wmEcontractApplyService).applyBatchEcontract(any());
    }
}