package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for AbstractWmEcontractPhfSubApplyAdapterService#updatePdfMetaContent
 */
@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractPhfSubApplyAdapterServiceUpdatePdfMetaContentTest {

    private final AbstractWmEcontractPhfSubApplyAdapterService service = new AbstractWmEcontractPhfSubApplyAdapterService() {

        @Override
        public EcontractTaskApplySubTypeEnum getSubTypeEnum() {
            return null;
        }
    };

    /**
     * Test case for updating PDF metadata content with all fields present
     */
    @Test
    public void testUpdatePdfMetaContent_AllFieldsPresent() {
        // arrange
        PdfContentInfoBo contentInfoBo = new PdfContentInfoBo();
        Map<String, String> originalMap = new HashMap<>();
        originalMap.put("partAName", "Company A");
        originalMap.put("partBName", "Company B");
        originalMap.put("partCName", "Company C");
        originalMap.put("wmPoiId", "12345");
        originalMap.put("poiInfo", "Test POI Info");
        contentInfoBo.setPdfMetaContent(originalMap);
        // act
        service.updatePdfMetaContent(contentInfoBo);
        // assert
        Map<String, String> result = contentInfoBo.getPdfMetaContent();
        assertEquals("Company A", result.get("partAName"));
        assertEquals("Company B", result.get("partBName"));
        assertEquals("12345", result.get("wmPoiId"));
        assertEquals("Test POI Info", result.get("poiInfo"));
    }

    /**
     * Test case for updating PDF metadata content with empty original map
     */
    @Test
    public void testUpdatePdfMetaContent_EmptyMap() {
        // arrange
        PdfContentInfoBo contentInfoBo = new PdfContentInfoBo();
        contentInfoBo.setPdfMetaContent(new HashMap<>());
        // act
        service.updatePdfMetaContent(contentInfoBo);
        // assert
        Map<String, String> result = contentInfoBo.getPdfMetaContent();
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for updating PDF metadata content with only POI related fields
     */
    @Test
    public void testUpdatePdfMetaContent_OnlyPoiFields() {
        // arrange
        PdfContentInfoBo contentInfoBo = new PdfContentInfoBo();
        Map<String, String> originalMap = new HashMap<>();
        originalMap.put("wmPoiId", "12345");
        originalMap.put("poiInfo", "Test POI Info");
        contentInfoBo.setPdfMetaContent(originalMap);
        // act
        service.updatePdfMetaContent(contentInfoBo);
        // assert
        Map<String, String> result = contentInfoBo.getPdfMetaContent();
        assertEquals("12345", result.get("wmPoiId"));
        assertEquals("Test POI Info", result.get("poiInfo"));
        assertNull(result.get("partAName"));
        assertNull(result.get("partBName"));
    }

    /**
     * Test case for updating PDF metadata content with only party name fields
     */
    @Test
    public void testUpdatePdfMetaContent_OnlyPartyNames() {
        // arrange
        PdfContentInfoBo contentInfoBo = new PdfContentInfoBo();
        Map<String, String> originalMap = new HashMap<>();
        originalMap.put("partAName", "Company A");
        originalMap.put("partBName", "Company B");
        originalMap.put("partCName", "Company C");
        contentInfoBo.setPdfMetaContent(originalMap);
        // act
        service.updatePdfMetaContent(contentInfoBo);
        // assert
        Map<String, String> result = contentInfoBo.getPdfMetaContent();
        assertEquals("Company A", result.get("partAName"));
        assertEquals("Company B", result.get("partBName"));
        assertNull(result.get("wmPoiId"));
        assertNull(result.get("poiInfo"));
    }

    /**
     * Test case for updating PDF metadata content with irrelevant fields
     */
    @Test
    public void testUpdatePdfMetaContent_IrrelevantFields() {
        // arrange
        PdfContentInfoBo contentInfoBo = new PdfContentInfoBo();
        Map<String, String> originalMap = new HashMap<>();
        originalMap.put("irrelevantField1", "value1");
        originalMap.put("irrelevantField2", "value2");
        contentInfoBo.setPdfMetaContent(originalMap);
        // act
        service.updatePdfMetaContent(contentInfoBo);
        // assert
        Map<String, String> result = contentInfoBo.getPdfMetaContent();
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for updating PDF metadata content with empty values
     */
    @Test
    public void testUpdatePdfMetaContent_EmptyValues() {
        // arrange
        PdfContentInfoBo contentInfoBo = new PdfContentInfoBo();
        Map<String, String> originalMap = new HashMap<>();
        originalMap.put("partAName", "");
        originalMap.put("partBName", "");
        originalMap.put("wmPoiId", "");
        originalMap.put("poiInfo", "");
        contentInfoBo.setPdfMetaContent(originalMap);
        // act
        service.updatePdfMetaContent(contentInfoBo);
        // assert
        Map<String, String> result = contentInfoBo.getPdfMetaContent();
        assertEquals("", result.get("partAName"));
        assertEquals("", result.get("partBName"));
        assertEquals("", result.get("wmPoiId"));
        assertEquals("", result.get("poiInfo"));
    }
}