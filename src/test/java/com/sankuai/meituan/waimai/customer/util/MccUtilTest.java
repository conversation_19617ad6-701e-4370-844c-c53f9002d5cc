package com.sankuai.meituan.waimai.customer.util;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test cases for {@link MccUtil#isGrayTemplateEnum}
 */
@RunWith(MockitoJUnitRunner.class)
public class MccUtilTest {

    /**
     * Test when input template enum is null
     */
    @Test
    public void testIsGrayTemplateEnum_NullInput() {
        // arrange
        SignTemplateEnum templateEnum = null;
        // act
        boolean result = MccUtil.isGrayTemplateEnum(templateEnum);
        // assert
        assertFalse(result);
    }

    /**
     * Test when template enum exists in map with true value
     */
    @Test
    public void testIsGrayTemplateEnum_ExistsWithTrueValue() {
        // arrange
        SignTemplateEnum templateEnum = SignTemplateEnum.C1CONTRACT_INFO_V3;
        Map<String, Boolean> grayMap = new HashMap<>();
        grayMap.put(templateEnum.name(), true);
        try (MockedStatic<MccConfig> mockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mockedStatic.when(MccConfig::getGrayNewPlatformTemplateEnumMap).thenReturn(grayMap);
            // act
            boolean result = MccUtil.isGrayTemplateEnum(templateEnum);
            // assert
            assertTrue(result);
        }
    }

    /**
     * Test when template enum exists in map with false value
     */
    @Test
    public void testIsGrayTemplateEnum_ExistsWithFalseValue() {
        // arrange
        SignTemplateEnum templateEnum = SignTemplateEnum.C1CONTRACT_INFO_V3;
        Map<String, Boolean> grayMap = new HashMap<>();
        grayMap.put(templateEnum.name(), false);
        try (MockedStatic<MccConfig> mockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mockedStatic.when(MccConfig::getGrayNewPlatformTemplateEnumMap).thenReturn(grayMap);
            // act
            boolean result = MccUtil.isGrayTemplateEnum(templateEnum);
            // assert
            assertFalse(result);
        }
    }

    /**
     * Test when template enum does not exist in map
     */
    @Test
    public void testIsGrayTemplateEnum_NotExists() {
        // arrange
        SignTemplateEnum templateEnum = SignTemplateEnum.C1CONTRACT_INFO_V3;
        Map<String, Boolean> grayMap = new HashMap<>();
        try (MockedStatic<MccConfig> mockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mockedStatic.when(MccConfig::getGrayNewPlatformTemplateEnumMap).thenReturn(grayMap);
            // act
            boolean result = MccUtil.isGrayTemplateEnum(templateEnum);
            // assert
            assertFalse(result);
        }
    }

    /**
     * Test when gray map is empty
     */
    @Test
    public void testIsGrayTemplateEnum_EmptyMap() {
        // arrange
        SignTemplateEnum templateEnum = SignTemplateEnum.C1CONTRACT_INFO_V3;
        Map<String, Boolean> grayMap = new HashMap<>();
        try (MockedStatic<MccConfig> mockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mockedStatic.when(MccConfig::getGrayNewPlatformTemplateEnumMap).thenReturn(grayMap);
            // act
            boolean result = MccUtil.isGrayTemplateEnum(templateEnum);
            // assert
            assertFalse(result);
        }
    }
}