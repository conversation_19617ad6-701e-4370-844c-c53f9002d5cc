package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfDeliveryApplyServiceGetPdfSubTypeEnumNameTest {

    private WmEcontractPhfDeliveryApplyService service = new WmEcontractPhfDeliveryApplyService();

    /**
     * Test when input key exactly matches the longest enum name (PHF_AGENT_SPECIAL_PRICE)
     */
    @Test
    public void testGetPdfSubTypeEnumNameExactMatchLongest() throws Throwable {
        // arrange
        String key = "PHF_AGENT_SPECIAL_PRICE";
        // act
        String result = service.getPdfSubTypeEnumName(key);
        // assert
        assertEquals("phf_agent_special_price", result);
    }

    /**
     * Test when input key starts with an enum name plus additional text
     */
    @Test
    public void testGetPdfSubTypeEnumNamePartialMatchLongest() throws Throwable {
        // arrange
        String key = "PHF_AGENT_SPECIAL_PRICE_EXTRA";
        // act
        String result = service.getPdfSubTypeEnumName(key);
        // assert
        assertEquals("phf_agent_special_price", result);
    }

    /**
     * Test when input key matches a shorter enum name (PHF_VIRTUAL)
     */
    @Test
    public void testGetPdfSubTypeEnumNameMatchShorter() throws Throwable {
        // arrange
        String key = "PHF_VIRTUAL";
        // act
        String result = service.getPdfSubTypeEnumName(key);
        // assert
        assertEquals("phf_virtual", result);
    }

    /**
     * Test when input key doesn't match any enum name
     */
    @Test
    public void testGetPdfSubTypeEnumNameNoMatch() throws Throwable {
        // arrange
        String key = "unknown_key";
        // act
        String result = service.getPdfSubTypeEnumName(key);
        // assert
        assertEquals("unknown_key", result);
    }

    /**
     * Test when input key is null - expecting NPE
     */
    @Test(expected = NullPointerException.class)
    public void testGetPdfSubTypeEnumNameNullInput() throws Throwable {
        // arrange
        String key = null;
        // act
        service.getPdfSubTypeEnumName(key);
    }

    /**
     * Test when input key is empty string
     */
    @Test
    public void testGetPdfSubTypeEnumNameEmptyInput() throws Throwable {
        // arrange
        String key = "";
        // act
        String result = service.getPdfSubTypeEnumName(key);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when input key matches multiple enum names but picks longest
     */
    @Test
    public void testGetPdfSubTypeEnumNameMultipleMatchesPicksLongest() throws Throwable {
        // arrange
        String key = "PHF_FORMAL_SELF_EXTRA";
        // act
        String result = service.getPdfSubTypeEnumName(key);
        // assert
        assertEquals("phf_formal_self", result);
    }

    /**
     * Test when input key is shorter than all enum names
     */
    @Test
    public void testGetPdfSubTypeEnumNameShorterThanAll() throws Throwable {
        // arrange
        String key = "phf";
        // act
        String result = service.getPdfSubTypeEnumName(key);
        // assert
        assertEquals("phf", result);
    }

    /**
     * Test when input key exactly matches the shortest enum name (JHS)
     */
    @Test
    public void testGetPdfSubTypeEnumNameExactMatchShortest() throws Throwable {
        // arrange
        String key = "JHS";
        // act
        String result = service.getPdfSubTypeEnumName(key);
        // assert
        assertEquals("jhs", result);
    }
}