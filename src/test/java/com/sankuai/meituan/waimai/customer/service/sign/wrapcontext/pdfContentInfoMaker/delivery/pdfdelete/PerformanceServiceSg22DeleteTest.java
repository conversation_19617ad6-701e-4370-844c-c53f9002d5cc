package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PerformanceServiceSg22DeleteTest {

    // 使用与被测类相同的常量值
    private static final String TAB_DELIVERY_PERFORMANCE_SERVICE = "delivery_performance_service";

    @Mock
    private Map<String, Collection<SignTemplateEnum>> tabPdfMap;

    @Mock
    private Map<String, List<String>> pdfDataMap;

    private PerformanceServiceSg22Delete performanceServiceSg22Delete;

    @Before
    public void setUp() {
        performanceServiceSg22Delete = new PerformanceServiceSg22Delete();
    }

    /**
     * 测试当pdfDataMap中包含PERFORMANCE_SERVICE_SG_22数据时，不应删除模板
     */
    @Test
    public void testDeleteWhenPdfDataMapContainsKey() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22.getName())).thenReturn(Arrays.asList("someData"));
        // act
        performanceServiceSg22Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, never()).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
    }

    /**
     * 测试当pdfDataMap中不包含数据且tabPdfMap中包含需要删除的模板时
     */
    @Test
    public void testDeleteWhenShouldRemoveTemplate() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22.getName())).thenReturn(null);
        Set<SignTemplateEnum> templateSet = new HashSet<>();
        templateSet.add(SignTemplateEnum.PERFORMANCE_SERVICE_SG_22);
        when(tabPdfMap.get(TAB_DELIVERY_PERFORMANCE_SERVICE)).thenReturn(templateSet);
        // act
        performanceServiceSg22Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
        assertTrue(templateSet.isEmpty());
    }

    /**
     * 测试当pdfDataMap中不包含数据且tabPdfMap中的集合为空时
     */
    @Test
    public void testDeleteWhenTabListEmpty() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22.getName())).thenReturn(null);
        when(tabPdfMap.get(TAB_DELIVERY_PERFORMANCE_SERVICE)).thenReturn(new HashSet<>());
        // act
        performanceServiceSg22Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
        verify(tabPdfMap, times(1)).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
    }

    /**
     * 测试当pdfDataMap中不包含数据且tabPdfMap中的集合为null时
     */
    @Test
    public void testDeleteWhenTabListNull() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22.getName())).thenReturn(null);
        when(tabPdfMap.get(TAB_DELIVERY_PERFORMANCE_SERVICE)).thenReturn(null);
        // act
        performanceServiceSg22Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
        verify(tabPdfMap, times(1)).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
    }

    /**
     * 测试当pdfDataMap为null时的情况
     */
    @Test
    public void testDeleteWhenPdfDataMapIsNull() throws Throwable {
        // arrange
        when(pdfDataMap.get(anyString())).thenReturn(null);
        Set<SignTemplateEnum> templateSet = new HashSet<>();
        templateSet.add(SignTemplateEnum.PERFORMANCE_SERVICE_SG_22);
        when(tabPdfMap.get(TAB_DELIVERY_PERFORMANCE_SERVICE)).thenReturn(templateSet);
        // act
        performanceServiceSg22Delete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
        assertTrue(templateSet.isEmpty());
    }
}