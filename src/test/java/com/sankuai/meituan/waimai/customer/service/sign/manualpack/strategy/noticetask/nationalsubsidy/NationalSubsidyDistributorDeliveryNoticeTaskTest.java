package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.nationalsubsidy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsGatewayThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.NoticeTask;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidyDistributorDeliveryNoticeTaskTest {

    @InjectMocks
    private NationalSubsidyDistributorDeliveryNoticeTask nationalSubsidyDistributorDeliveryNoticeTask;

    @Mock
    private WmLogisticsGatewayThriftServiceAdapter wmLogisticsGatewayThriftServiceAdapter;

    private String module;

    private List<Long> bizIdList;

    private ManualPackNoticeContext context;

    private List<Long> taskIds;

    @Before
    public void setUp() {
        module = "testModule";
        bizIdList = Arrays.asList(1L, 2L, 3L);
        taskIds = Arrays.asList(1L, 2L, 3L);
    }

    /**
     * 测试场景：context.getTaskInfo()或taskIds为空
     */
    @Test(expected = WmCustomerException.class)
    public void testNoticeTaskInfoOrTaskIdsIsNull() throws Throwable {
        context = ManualPackNoticeContext.builder().taskInfo(null).build();
        nationalSubsidyDistributorDeliveryNoticeTask.notice(module, bizIdList, context, taskIds);
    }

    /**
     * 测试场景：context.getNationalSubsidyDistributorWmPoiIdMap()为空
     */
    @Test(expected = WmCustomerException.class)
    public void testNoticeWmPoiIdMapIsNull() throws Throwable {
        context = ManualPackNoticeContext.builder().taskInfo(Collections.singletonMap("key", Arrays.asList(1L, 2L))).nationalSubsidyDistributorWmPoiIdMap(null).build();
        nationalSubsidyDistributorDeliveryNoticeTask.notice(module, bizIdList, context, taskIds);
    }

    /**
     * 测试场景：正常情况，所有参数都有效
     */
    @Test
    public void testNoticeNormal() throws Throwable {
        Map<Long, Long> wmPoiIdMap = new HashMap<>();
        wmPoiIdMap.put(1L, 1L);
        context = ManualPackNoticeContext.builder().taskInfo(Collections.singletonMap("key", Arrays.asList(1L, 2L))).nationalSubsidyDistributorWmPoiIdMap(wmPoiIdMap).commitUid(123).manualBatchId(456L).build();
        nationalSubsidyDistributorDeliveryNoticeTask.notice(module, bizIdList, context, taskIds);
        verify(wmLogisticsGatewayThriftServiceAdapter, times(1)).deliveryBatchApplySignUseNewIface(any());
    }

    /**
     * 测试场景：在执行过程中发生异常
     */
    @Test(expected = WmCustomerException.class)
    public void testNoticeException() throws Throwable {
        Map<Long, Long> wmPoiIdMap = new HashMap<>();
        wmPoiIdMap.put(1L, 1L);
        context = ManualPackNoticeContext.builder().taskInfo(Collections.singletonMap("key", Arrays.asList(1L, 2L))).nationalSubsidyDistributorWmPoiIdMap(wmPoiIdMap).commitUid(123).manualBatchId(456L).build();
        doThrow(new WmCustomerException()).when(wmLogisticsGatewayThriftServiceAdapter).deliveryBatchApplySignUseNewIface(any());
        nationalSubsidyDistributorDeliveryNoticeTask.notice(module, bizIdList, context, taskIds);
    }
}
