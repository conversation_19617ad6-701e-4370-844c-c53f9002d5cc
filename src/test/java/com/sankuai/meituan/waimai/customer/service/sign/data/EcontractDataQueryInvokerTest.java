package com.sankuai.meituan.waimai.customer.service.sign.data;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PhfTransferContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractDataQueryInvokerTest {

    @InjectMocks
    private EcontractDataQueryInvoker econtractDataQueryInvoker;

    @Mock
    private List<EcontractDataQueryHandler> econtractDataQueryHandlers;

    @Mock
    private EcontractDataQueryHandler<EcontractDeliveryInfoBo> econtractDataQueryHandler;

    private Method initApplyInfoBoMethod;

    @Before
    public void setUp() throws Exception {
        // 初始化私有方法
        initApplyInfoBoMethod = EcontractDataQueryInvoker.class.getDeclaredMethod("initApplyInfoBo", EcontractTaskApplyTypeEnum.class, List.class, long.class);
        initApplyInfoBoMethod.setAccessible(true);
        // 初始化handler map
        Map<EcontractDataSourceEnum, EcontractDataQueryHandler> handlerMap = new HashMap<>();
        handlerMap.put(EcontractDataSourceEnum.LOGISTICS_HERON_ALL, econtractDataQueryHandler);
        Field handlerMapField = EcontractDataQueryInvoker.class.getDeclaredField("econtractDataQueryHandlerMap");
        handlerMapField.setAccessible(true);
        handlerMapField.set(econtractDataQueryInvoker, handlerMap);
        // 设置基础mock行为
    }

    private EcontractDataSourceBo createDataSourceBo() {
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        dataSourceBo.setSorceEnum(EcontractDataSourceEnum.LOGISTICS_HERON_ALL);
        List<EcontractDataPoiBizBo> poiBizBoList = new ArrayList<>();
        EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
        poiBizBo.setWmPoiId(1L);
        poiBizBo.setBizId(1L);
        poiBizBoList.add(poiBizBo);
        dataSourceBo.setWmPoiIdAndBizIdList(poiBizBoList);
        List<List<Long>> groupList = new ArrayList<>();
        List<Long> group = new ArrayList<>();
        group.add(1L);
        groupList.add(group);
        dataSourceBo.setWmPoiIdGroupList(groupList);
        return dataSourceBo;
    }

    private List<EcontractDataSourceBo> createDataSourceBoList() {
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        dataSourceBoList.add(createDataSourceBo());
        return dataSourceBoList;
    }

    private Map<Long, EcontractDeliveryInfoBo> createMockResultMap() {
        Map<Long, EcontractDeliveryInfoBo> resultMap = new HashMap<>();
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        resultMap.put(1L, deliveryInfoBo);
        return resultMap;
    }

    /**
     * Test normal case with all fields populated
     */
    @Test
    public void testAssemblyApplyBo_NormalCase() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo originApplyBo = new EcontractTaskApplyBo();
        originApplyBo.setBizId("test_biz_id");
        originApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.WM_POI_ID);
        originApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);
        originApplyBo.setCommitUid(123);
        originApplyBo.setManualBatchId(456L);
        originApplyBo.setWmCustomerId(789);
        originApplyBo.setTag(1);
        List<PhfTransferContext> phfList = new ArrayList<>();
        phfList.add(new PhfTransferContext("contract1", "key1", 1L));
        originApplyBo.setPhfTransferContextList(phfList);
        List<EcontractDataSourceBo> dataSourceList = new ArrayList<>();
        originApplyBo.setDataSourceBoList(dataSourceList);
        // act
        EcontractTaskApplyBo result = econtractDataQueryInvoker.assemblyApplyBo(originApplyBo);
        // assert
        assertNotNull(result);
        assertEquals(originApplyBo.getBizId(), result.getBizId());
        assertEquals(originApplyBo.getBizTypeEnum(), result.getBizTypeEnum());
        assertEquals(originApplyBo.getApplyTypeEnum(), result.getApplyTypeEnum());
        assertEquals(originApplyBo.getCommitUid(), result.getCommitUid());
        assertEquals(originApplyBo.getManualBatchId(), result.getManualBatchId());
        assertEquals(originApplyBo.getWmCustomerId(), result.getWmCustomerId());
        assertEquals(originApplyBo.getTag(), result.getTag());
        assertEquals(originApplyBo.getPhfTransferContextList(), result.getPhfTransferContextList());
        assertEquals(originApplyBo.getDataSourceBoList(), result.getDataSourceBoList());
        assertNotNull(result.getConfigBo());
    }

    /**
     * Test case with minimal required fields
     */
    @Test
    public void testAssemblyApplyBo_MinimalFields() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo originApplyBo = new EcontractTaskApplyBo();
        originApplyBo.setBizId("test_biz_id");
        originApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.WM_POI_ID);
        originApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);
        // act
        EcontractTaskApplyBo result = econtractDataQueryInvoker.assemblyApplyBo(originApplyBo);
        // assert
        assertNotNull(result);
        assertEquals(originApplyBo.getBizId(), result.getBizId());
        assertEquals(originApplyBo.getBizTypeEnum(), result.getBizTypeEnum());
        assertEquals(originApplyBo.getApplyTypeEnum(), result.getApplyTypeEnum());
        assertNotNull(result.getConfigBo());
    }

    /**
     * Test case with null input
     */
    @Test(expected = NullPointerException.class)
    public void testAssemblyApplyBo_NullInput() throws WmCustomerException {
        // act
        econtractDataQueryInvoker.assemblyApplyBo(null);
    }

    /**
     * Test case with null bizId
     */
    @Test
    public void testAssemblyApplyBo_NullBizId() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo originApplyBo = new EcontractTaskApplyBo();
        originApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.WM_POI_ID);
        originApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);
        // act
        EcontractTaskApplyBo result = econtractDataQueryInvoker.assemblyApplyBo(originApplyBo);
        // assert
        assertNotNull(result);
        assertEquals(null, result.getBizId());
        assertEquals(originApplyBo.getBizTypeEnum(), result.getBizTypeEnum());
        assertEquals(originApplyBo.getApplyTypeEnum(), result.getApplyTypeEnum());
    }

    /**
     * Test case with null bizTypeEnum
     */
    @Test
    public void testAssemblyApplyBo_NullBizTypeEnum() throws WmCustomerException {
        // arrange
        EcontractTaskApplyBo originApplyBo = new EcontractTaskApplyBo();
        originApplyBo.setBizId("test_biz_id");
        originApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.C1CONTRACT);
        // act
        EcontractTaskApplyBo result = econtractDataQueryInvoker.assemblyApplyBo(originApplyBo);
        // assert
        assertNotNull(result);
        assertEquals(originApplyBo.getBizId(), result.getBizId());
        assertEquals(null, result.getBizTypeEnum());
        assertEquals(originApplyBo.getApplyTypeEnum(), result.getApplyTypeEnum());
    }

    /**
     * Test POIFEE type normal case
     */
    @Test
    public void testInitApplyInfoBo_POIFEE_Normal() throws Throwable {
        // arrange
        Map<Long, EcontractDeliveryInfoBo> mockResult = createMockResultMap();
        when(econtractDataQueryHandler.queryData(anyMap(), anyLong())).thenReturn(mockResult);
        // act
        String result = (String) initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.POIFEE, createDataSourceBoList(), 1L);
        // assert
        assertNotNull("Result should not be null", result);
        verify(econtractDataQueryHandler, times(1)).queryData(anyMap(), anyLong());
    }

    /**
     * Test BATCHPOIFEE type normal case
     */
    @Test
    public void testInitApplyInfoBo_BATCHPOIFEE_Normal() throws Throwable {
        // arrange
        Map<Long, EcontractDeliveryInfoBo> mockResult = createMockResultMap();
        when(econtractDataQueryHandler.queryData(anyMap(), anyLong())).thenReturn(mockResult);
        // act
        String result = (String) initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.BATCHPOIFEE, createDataSourceBoList(), 1L);
        // assert
        assertNotNull("Result should not be null", result);
        verify(econtractDataQueryHandler, times(1)).queryData(anyMap(), anyLong());
    }

    /**
     * Test PHF_DELIVERY type normal case
     */
    @Test
    public void testInitApplyInfoBo_PHF_DELIVERY_Normal() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            Map<Long, EcontractDeliveryInfoBo> resultMap = invocation.getArgument(2);
            resultMap.putAll(createMockResultMap());
            return null;
        }).when(econtractDataQueryHandler).queryAndAssembleData(anyMap(), anyLong(), anyMap());
        // act
        String result = (String) initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.PHF_DELIVERY, createDataSourceBoList(), 1L);
        // assert
        assertNotNull("Result should not be null", result);
        verify(econtractDataQueryHandler, times(1)).queryAndAssembleData(anyMap(), anyLong(), anyMap());
    }

    /**
     * Test unknown type case
     */
    @Test
    public void testInitApplyInfoBo_Unknown_Type() throws Throwable {
        // act
        String result = (String) initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.UNKNOW, createDataSourceBoList(), 1L);
        // assert
        assertNull("Result should be null for unknown type", result);
        verify(econtractDataQueryHandler, never()).queryData(anyMap(), anyLong());
        verify(econtractDataQueryHandler, never()).queryAndAssembleData(anyMap(), anyLong(), anyMap());
    }

    /**
     * Test queryData throws exception case
     */
    @Test
    public void testInitApplyInfoBo_QueryData_Exception() throws Throwable {
        // arrange
        when(econtractDataQueryHandler.queryData(anyMap(), anyLong())).thenThrow(new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "Test Exception"));
        try {
            // act
            initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.POIFEE, createDataSourceBoList(), 1L);
            fail("Expected WmCustomerException was not thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof WmCustomerException);
            WmCustomerException wce = (WmCustomerException) e.getCause();
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, wce.getCode());
        }
    }

    /**
     * Test queryAndAssembleData throws exception case
     */
    @Test
    public void testInitApplyInfoBo_QueryAndAssembleData_Exception() throws Throwable {
        // arrange
        doThrow(new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "Test Exception")).when(econtractDataQueryHandler).queryAndAssembleData(anyMap(), anyLong(), anyMap());
        try {
            // act
            initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.PHF_DELIVERY, createDataSourceBoList(), 1L);
            fail("Expected WmCustomerException was not thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof WmCustomerException);
            WmCustomerException wce = (WmCustomerException) e.getCause();
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, wce.getCode());
        }
    }

    /**
     * Test null handler case
     */
    @Test
    public void testInitApplyInfoBo_NullHandler() throws Throwable {
        // arrange
        EcontractDataSourceBo dataSourceBo = createDataSourceBo();
        dataSourceBo.setSorceEnum(EcontractDataSourceEnum.LOGISTICS_CONTRACT_ALL);
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        dataSourceBoList.add(dataSourceBo);
        try {
            // act
            initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.POIFEE, dataSourceBoList, 1L);
            fail("Expected NoSuchElementException was not thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof NoSuchElementException);
            verify(econtractDataQueryHandler, never()).queryData(anyMap(), anyLong());
        }
    }

    /**
     * Test duplicate DataSourceEnum case
     */
    @Test
    public void testInitApplyInfoBo_DuplicateDataSourceEnum() throws Throwable {
        // arrange
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        dataSourceBoList.add(createDataSourceBo());
        dataSourceBoList.add(createDataSourceBo());
        try {
            // act
            initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.PHF_DELIVERY, dataSourceBoList, 1L);
            fail("Expected WmCustomerException was not thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof WmCustomerException);
            WmCustomerException wce = (WmCustomerException) e.getCause();
            assertEquals(WmContractErrorCodeConstant.INPUT_ERROR, wce.getCode());
        }
    }

    /**
     * Test null dataSourceBoList case
     */
    @Test
    public void testInitApplyInfoBo_NullDataSourceBoList() throws Throwable {
        try {
            // act
            initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.POIFEE, null, 1L);
            fail("Expected NullPointerException was not thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Test empty dataSourceBoList case
     */
    @Test
    public void testInitApplyInfoBo_EmptyDataSourceBoList() throws Throwable {
        try {
            // act
            initApplyInfoBoMethod.invoke(econtractDataQueryInvoker, EcontractTaskApplyTypeEnum.POIFEE, new ArrayList<>(), 1L);
            fail("Expected NoSuchElementException was not thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof NoSuchElementException);
            verify(econtractDataQueryHandler, never()).queryData(anyMap(), anyLong());
        }
    }
}