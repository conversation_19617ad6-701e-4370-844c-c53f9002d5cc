package com.sankuai.meituan.waimai.customer.service.sign.applytask;

import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.*;

/**
 * @description:
 * @author: zhangyuanhao02
 * @create: 2025/2/18 10:02
 */
@RunWith(MockitoJUnitRunner.class)
public class WmEcontractTaskApplyServiceTest extends BaseStaticMockTest {

    @InjectMocks
    private WmEcontractTaskApplyService wmEcontractTaskApplyService;

    @Mock
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    @Mock
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;


    @Before
    public void setUp() {
        super.init();
        mccConfigMockedStatic.when(MccConfig::isSupportSortByPriority).thenReturn(true);
    }

    @Test
    public void testIsNeedResendByPriority_CustomerInfoBoIsNull() {
        WmEcontractTaskApplyService service = new WmEcontractTaskApplyService();
        EcontractBatchContextBo contextBo = Mockito.mock(EcontractBatchContextBo.class);
        when(contextBo.getCustomerInfoBo()).thenReturn(null);

        boolean result = service.isNeedResendByPriority(contextBo);
        Assert.assertFalse(result);
    }

    @Test
    public void testIsNeedResendByPriority_CustomerRealTypeIsNull() {
        WmEcontractTaskApplyService service = new WmEcontractTaskApplyService();
        EcontractBatchContextBo contextBo = Mockito.mock(EcontractBatchContextBo.class);
        EcontractCustomerInfoBo customerInfoBo = Mockito.mock(EcontractCustomerInfoBo.class);
        when(contextBo.getCustomerInfoBo()).thenReturn(customerInfoBo);
        when(customerInfoBo.getCustomerRealType()).thenReturn(null);

        boolean result = service.isNeedResendByPriority(contextBo);

        Assert.assertFalse(result);
    }

    @Test
    public void testIsNeedResendByPriority_CustomerRealTypeMatches() {
        WmEcontractTaskApplyService service = new WmEcontractTaskApplyService();
        EcontractBatchContextBo contextBo = Mockito.mock(EcontractBatchContextBo.class);
        EcontractCustomerInfoBo customerInfoBo = Mockito.mock(EcontractCustomerInfoBo.class);
        when(contextBo.getCustomerInfoBo()).thenReturn(customerInfoBo);
        when(customerInfoBo.getCustomerRealType()).thenReturn(CustomerRealTypeEnum.DANDIAN_SG.getValue());

        boolean result = service.isNeedResendByPriority(contextBo);

        Assert.assertTrue(result);
    }

    @Test
    public void testIsNeedResendByPriority_CustomerRealTypeDoesNotMatch() {
        WmEcontractTaskApplyService service = new WmEcontractTaskApplyService();
        EcontractBatchContextBo contextBo = Mockito.mock(EcontractBatchContextBo.class);
        EcontractCustomerInfoBo customerInfoBo = Mockito.mock(EcontractCustomerInfoBo.class);
        when(contextBo.getCustomerInfoBo()).thenReturn(customerInfoBo);
        when(customerInfoBo.getCustomerRealType()).thenReturn(999); // 不匹配的值

        boolean result = service.isNeedResendByPriority(contextBo);

        Assert.assertFalse(result);
    }

    @Test
    public void testResendMsgPackByPriority_Success() throws WmCustomerException, TException {
        Long batchId = 1L;
        Long packId = 2L;

        WmEcontractSignBatchDB batchDB = new WmEcontractSignBatchDB();
        batchDB.setPackId(packId);

        List<WmEcontractSignBatchDB> signBatchDBList = new ArrayList<>();
        WmEcontractSignBatchDB batch1 = new WmEcontractSignBatchDB();
        batch1.setBatchContext("{\"priority\": 2}");
        batch1.setId(3L);
        signBatchDBList.add(batch1);

        WmEcontractSignBatchDB batch2 = new WmEcontractSignBatchDB();
        batch2.setBatchContext("{\"priority\": 1}");
        batch2.setId(4L);
        signBatchDBList.add(batch2);

        RetrySmsResponse expectedResponse = new RetrySmsResponse();

        // 使用spy对wmEcontractTaskApplyService进行部分Mock
        WmEcontractTaskApplyService spyService = spy(wmEcontractTaskApplyService);

        when(wmEcontractBatchBaseService.queryByBatchId(batchId)).thenReturn(batchDB);
        when(wmEcontractBigBatchParseService.querySignBatchListByPackId(packId)).thenReturn(signBatchDBList);

        // Mock resendMsgPack方法
        doReturn(expectedResponse).when(spyService).resendMsgPack(3L);

        // 调用spy对象的resendMsgPackByPriority方法
        RetrySmsResponse actualResponse = spyService.resendMsgPackByPriority(batchId);

        assertEquals(expectedResponse, actualResponse);
        verify(wmEcontractBatchBaseService).queryByBatchId(batchId);
        verify(wmEcontractBigBatchParseService).querySignBatchListByPackId(packId);
        verify(spyService).resendMsgPack(3L);
    }


    @Test
    public void testResendMsgPackByPriority_NonManualTask() {
        Long batchId = 1L;

        WmEcontractSignBatchDB batchDB = new WmEcontractSignBatchDB();
        batchDB.setPackId(0L);

        when(wmEcontractBatchBaseService.queryByBatchId(batchId)).thenReturn(batchDB);

        WmCustomerException exception = assertThrows(WmCustomerException.class, () -> {
            wmEcontractTaskApplyService.resendMsgPackByPriority(batchId);
        });

        assertEquals("非手动打包任务", exception.getMessage());
        verify(wmEcontractBatchBaseService).queryByBatchId(batchId);
    }

    @Test
    public void testResendMsgPackByPriority_EmptyBatchList() {
        Long batchId = 1L;
        Long packId = 2L;

        WmEcontractSignBatchDB batchDB = new WmEcontractSignBatchDB();
        batchDB.setPackId(packId);

        when(wmEcontractBatchBaseService.queryByBatchId(batchId)).thenReturn(batchDB);
        when(wmEcontractBigBatchParseService.querySignBatchListByPackId(packId)).thenReturn(new ArrayList<>());

        WmCustomerException exception = assertThrows(WmCustomerException.class, () -> {
            wmEcontractTaskApplyService.resendMsgPackByPriority(batchId);
        });

        assertEquals("手动打包任务为空", exception.getMessage());
        verify(wmEcontractBatchBaseService).queryByBatchId(batchId);
        verify(wmEcontractBigBatchParseService).querySignBatchListByPackId(packId);
    }
}
