package com.sankuai.meituan.waimai.customer.service.sign;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractTaskApplyService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchPdfUrlContentBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.contracttask.PdfUrlRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.contracttask.SmsShortLinkQueryParam;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Test class for WmEcontractSignBzService#getSmsShortLink
 */
@RunWith(MockitoJUnitRunner.class)
public class WmEcontractSignBzServiceTest {

    private static final Logger log = LoggerFactory.getLogger(WmEcontractSignBzServiceTest.class);

    @InjectMocks
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Mock
    private WmEcontractTaskApplyService wmEcontractTaskApplyService;

    @Mock
    private WmEcontractTaskBizService wmEcontractTaskService;

    @Mock
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    @Before
    public void setup() {
        // Ensure the service is properly initialized
        assertNotNull(wmEcontractTaskApplyService);
    }

    /**
     * Test successful retrieval of SMS short link
     */
    @Test
    public void testGetSmsShortLink_Success() throws Throwable {
        // arrange
        SmsShortLinkQueryParam queryParam = new SmsShortLinkQueryParam();
        queryParam.setId(123);
        queryParam.setType("NORMAL");
        String expectedLink = "http://short.link/abc";
        when(wmEcontractTaskApplyService.getSmsShortLink(any())).thenReturn(expectedLink);
        // act
        String result = wmEcontractSignBzService.getSmsShortLink(queryParam);
        // assert
        assertNotNull(result);
        assertEquals(expectedLink, result);
    }

    /**
     * Test exception handling when service throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testGetSmsShortLink_ServiceException() throws Throwable {
        // arrange
        SmsShortLinkQueryParam queryParam = new SmsShortLinkQueryParam();
        queryParam.setId(123);
        doThrow(new RuntimeException("Service error")).when(wmEcontractTaskApplyService).getSmsShortLink(any());
        // act
        wmEcontractSignBzService.getSmsShortLink(queryParam);
    }

    /**
     * Test with null query parameter
     */
    @Test(expected = WmCustomerException.class)
    public void testGetSmsShortLink_NullParam() throws Throwable {
        // arrange
        doThrow(new NullPointerException()).when(wmEcontractTaskApplyService).getSmsShortLink(null);
        // act
        wmEcontractSignBzService.getSmsShortLink(null);
    }

    /**
     * Test with empty query parameter
     */
    @Test
    public void testGetSmsShortLink_EmptyParam() throws Throwable {
        // arrange
        SmsShortLinkQueryParam queryParam = new SmsShortLinkQueryParam();
        String expectedLink = "http://short.link/empty";
        when(wmEcontractTaskApplyService.getSmsShortLink(any())).thenReturn(expectedLink);
        // act
        String result = wmEcontractSignBzService.getSmsShortLink(queryParam);
        // assert
        assertNotNull(result);
        assertEquals(expectedLink, result);
    }

    /**
     * Test exception message when service throws exception
     */
    @Test
    public void testGetSmsShortLink_ExceptionMessage() throws Throwable {
        // arrange
        SmsShortLinkQueryParam queryParam = new SmsShortLinkQueryParam();
        queryParam.setId(123);
        doThrow(new RuntimeException("Service error")).when(wmEcontractTaskApplyService).getSmsShortLink(any());
        try {
            // act
            wmEcontractSignBzService.getSmsShortLink(queryParam);
        } catch (WmCustomerException e) {
            // assert
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getCode());
            assertEquals("获取签约链接异常", e.getMessage());
        }
    }

    /**
     * Test queryPdfUrlInfo with null confirmId
     * Should throw WmCustomerException with PARAM_ERROR
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryPdfUrlInfo_NullConfirmId() throws Throwable {
        // arrange
        PdfUrlRequestDTO requestDTO = new PdfUrlRequestDTO();
        requestDTO.setConfirmId(null);
        // act
        wmEcontractSignBzService.queryPdfUrlInfo(requestDTO);
    }

    /**
     * Test queryPdfUrlInfo when task not found
     * Should throw WmCustomerException with BUSINESS_ERROR
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryPdfUrlInfo_TaskNotFound() throws Throwable {
        // arrange
        PdfUrlRequestDTO requestDTO = new PdfUrlRequestDTO();
        requestDTO.setConfirmId(1L);
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(1L)).thenReturn(null);
        // act
        wmEcontractSignBzService.queryPdfUrlInfo(requestDTO);
    }

    /**
     * Test queryPdfUrlInfo when task query throws exception
     * Should throw WmCustomerException with BUSINESS_ERROR
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryPdfUrlInfo_TaskQueryException() throws Throwable {
        // arrange
        PdfUrlRequestDTO requestDTO = new PdfUrlRequestDTO();
        requestDTO.setConfirmId(1L);
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(1L)).thenThrow(new RuntimeException("DB Error"));
        // act
        wmEcontractSignBzService.queryPdfUrlInfo(requestDTO);
    }

    /**
     * Test queryPdfUrlInfo with valid input
     * Should return BatchPdfUrlContentBo successfully
     */
    @Test
    public void testQueryPdfUrlInfo_Success() throws Throwable {
        // arrange
        PdfUrlRequestDTO requestDTO = new PdfUrlRequestDTO();
        requestDTO.setConfirmId(1L);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setBatchId(100L);
        BatchPdfUrlContentBo expectedResult = new BatchPdfUrlContentBo();
        // Since BatchPdfUrlContentBo is a BO class, we need to set its properties
        // Add any necessary properties based on the actual BO class definition
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(1L)).thenReturn(taskBo);
        when(wmEcontractBatchBaseService.getDownloadUrlByBatchId(100L)).thenReturn(expectedResult);
        // act
        BatchPdfUrlContentBo result = wmEcontractSignBzService.queryPdfUrlInfo(requestDTO);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(wmEcontractTaskService).queryByIdIfNullFromMaster(1L);
        verify(wmEcontractBatchBaseService).getDownloadUrlByBatchId(100L);
    }

    /**
     * Test queryPdfUrlInfo when batch service returns null
     * Should return null
     */
    @Test
    public void testQueryPdfUrlInfo_BatchServiceReturnsNull() throws Throwable {
        // arrange
        PdfUrlRequestDTO requestDTO = new PdfUrlRequestDTO();
        requestDTO.setConfirmId(1L);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setBatchId(100L);
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(1L)).thenReturn(taskBo);
        when(wmEcontractBatchBaseService.getDownloadUrlByBatchId(100L)).thenReturn(null);
        // act
        BatchPdfUrlContentBo result = wmEcontractSignBzService.queryPdfUrlInfo(requestDTO);
        // assert
        assertNull(result);
        verify(wmEcontractTaskService).queryByIdIfNullFromMaster(1L);
        verify(wmEcontractBatchBaseService).getDownloadUrlByBatchId(100L);
    }
}