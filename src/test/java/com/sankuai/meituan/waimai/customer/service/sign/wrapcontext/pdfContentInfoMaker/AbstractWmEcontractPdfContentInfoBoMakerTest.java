package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractPdfContentInfoBoMakerTest {

    /**
     * Test implementation of the abstract class for testing
     */
    private static class TestWmEcontractPdfContentInfoBoMaker extends AbstractWmEcontractPdfContentInfoBoMaker {

        private EcontractTaskBo taskBoToReturn;

        public void setTaskBoToReturn(EcontractTaskBo taskBo) {
            this.taskBoToReturn = taskBo;
        }

        @Override
        public SignTemplateEnum extractSignTemplateEnum() {
            // Not used in our test
            return null;
        }

        // Override the method that calls the static utility method
        @Override
        public List<EcontractDeliveryInfoBo> extractDeliveryInfo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, String pdfTempletType) throws WmCustomerException {
            // Skip the static method call and use our mocked taskBo
            List<EcontractDeliveryInfoBo> originDataList = Lists.newArrayList();
            if (taskBoToReturn == null) {
                throw new WmCustomerException(1, "Task not found");
            }
            if (middleContext.getSignDataFactor().isDeliveryMultiWmPoi()) {
                EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBoToReturn.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
                originDataList.addAll(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList());
            } else {
                EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBoToReturn.getApplyContext(), EcontractDeliveryInfoBo.class);
                originDataList.add(deliveryInfoBo);
            }
            Map<String, EcontractDeliveryInfoBo> originDataMap = originDataList.stream().collect(java.util.stream.Collectors.toMap(EcontractDeliveryInfoBo::getDeliveryTypeUUID, econtractDeliveryInfoBo -> econtractDeliveryInfoBo));
            // From middle context get uuid
            Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
            if (pdfDataMap == null || !pdfDataMap.containsKey(pdfTempletType)) {
                return Lists.newArrayList();
            }
            List<String> deliveryInfoUUIDList = pdfDataMap.get(pdfTempletType);
            // Extract and return from original data
            List<EcontractDeliveryInfoBo> resultList = deliveryInfoUUIDList.stream().filter(info -> originDataMap.get(info) != null).map(info -> originDataMap.get(info)).collect(java.util.stream.Collectors.toList());
            return resultList;
        }
    }

    /**
     * Test single poi delivery case
     */
    @Test
    public void testExtractDeliveryInfoSinglePoi() throws Throwable {
        // arrange
        TestWmEcontractPdfContentInfoBoMaker maker = new TestWmEcontractPdfContentInfoBoMaker();
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setDeliveryTypeUUID("uuid1");
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        maker.setTaskBoToReturn(taskBo);
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setSignDataFactor(new EcontractSignDataFactor());
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        pdfDataMap.put("testTemplate", Lists.newArrayList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        List<EcontractDeliveryInfoBo> result = maker.extractDeliveryInfo(originContext, middleContext, "testTemplate");
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("uuid1", result.get(0).getDeliveryTypeUUID());
    }

    /**
     * Test multi poi delivery case
     */
    @Test
    public void testExtractDeliveryInfoMultiPoi() throws Throwable {
        // arrange
        TestWmEcontractPdfContentInfoBoMaker maker = new TestWmEcontractPdfContentInfoBoMaker();
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        List<EcontractDeliveryInfoBo> deliveryList = Lists.newArrayList();
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setDeliveryTypeUUID("uuid1");
        deliveryList.add(deliveryInfoBo);
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryList);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        maker.setTaskBoToReturn(taskBo);
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(true);
        middleContext.setSignDataFactor(signDataFactor);
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        pdfDataMap.put("testTemplate", Lists.newArrayList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        List<EcontractDeliveryInfoBo> result = maker.extractDeliveryInfo(originContext, middleContext, "testTemplate");
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("uuid1", result.get(0).getDeliveryTypeUUID());
    }

    /**
     * Test case when pdfDataMap is empty
     */
    @Test
    public void testExtractDeliveryInfoEmptyPdfDataMap() throws Throwable {
        // arrange
        TestWmEcontractPdfContentInfoBoMaker maker = new TestWmEcontractPdfContentInfoBoMaker();
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setDeliveryTypeUUID("uuid1");
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        maker.setTaskBoToReturn(taskBo);
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setSignDataFactor(new EcontractSignDataFactor());
        middleContext.setPdfDataMap(Maps.newHashMap());
        // act
        List<EcontractDeliveryInfoBo> result = maker.extractDeliveryInfo(originContext, middleContext, "testTemplate");
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case with invalid UUID in pdfDataMap
     */
    @Test
    public void testExtractDeliveryInfoInvalidUUID() throws Throwable {
        // arrange
        TestWmEcontractPdfContentInfoBoMaker maker = new TestWmEcontractPdfContentInfoBoMaker();
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setDeliveryTypeUUID("uuid1");
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        maker.setTaskBoToReturn(taskBo);
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setSignDataFactor(new EcontractSignDataFactor());
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        pdfDataMap.put("testTemplate", Lists.newArrayList("invalid_uuid"));
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        List<EcontractDeliveryInfoBo> result = maker.extractDeliveryInfo(originContext, middleContext, "testTemplate");
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for null parameters
     */
    @Test(expected = WmCustomerException.class)
    public void testExtractDeliveryInfoNullParams() throws Throwable {
        // arrange
        TestWmEcontractPdfContentInfoBoMaker maker = new TestWmEcontractPdfContentInfoBoMaker();
        maker.setTaskBoToReturn(null);
        // act & assert
        maker.extractDeliveryInfo(null, null, null);
    }

    /**
     * Test case with multiple UUIDs in pdfDataMap
     */
    @Test
    public void testExtractDeliveryInfoMultipleUUIDs() throws Throwable {
        // arrange
        TestWmEcontractPdfContentInfoBoMaker maker = new TestWmEcontractPdfContentInfoBoMaker();
        // Create multiple delivery info objects
        List<EcontractDeliveryInfoBo> deliveryList = new ArrayList<>();
        EcontractDeliveryInfoBo deliveryInfoBo1 = new EcontractDeliveryInfoBo();
        deliveryInfoBo1.setDeliveryTypeUUID("uuid1");
        deliveryList.add(deliveryInfoBo1);
        EcontractDeliveryInfoBo deliveryInfoBo2 = new EcontractDeliveryInfoBo();
        deliveryInfoBo2.setDeliveryTypeUUID("uuid2");
        deliveryList.add(deliveryInfoBo2);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryList);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        maker.setTaskBoToReturn(taskBo);
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(true);
        middleContext.setSignDataFactor(signDataFactor);
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        pdfDataMap.put("testTemplate", Lists.newArrayList("uuid1", "uuid2"));
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        List<EcontractDeliveryInfoBo> result = maker.extractDeliveryInfo(originContext, middleContext, "testTemplate");
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("uuid1", result.get(0).getDeliveryTypeUUID());
        assertEquals("uuid2", result.get(1).getDeliveryTypeUUID());
    }

    /**
     * Test case with null pdfDataMap
     */
    @Test
    public void testExtractDeliveryInfoNullPdfDataMap() throws Throwable {
        // arrange
        TestWmEcontractPdfContentInfoBoMaker maker = new TestWmEcontractPdfContentInfoBoMaker();
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setDeliveryTypeUUID("uuid1");
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        maker.setTaskBoToReturn(taskBo);
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setSignDataFactor(new EcontractSignDataFactor());
        middleContext.setPdfDataMap(null);
        // act
        List<EcontractDeliveryInfoBo> result = maker.extractDeliveryInfo(originContext, middleContext, "testTemplate");
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
