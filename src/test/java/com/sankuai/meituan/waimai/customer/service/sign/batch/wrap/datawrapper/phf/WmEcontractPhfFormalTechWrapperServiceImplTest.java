package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf.WmEcontractPhfFormalTechWrapperServiceImpl;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryPhfInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryPhfTechInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfFormalTechWrapperServiceImplTest {

    @InjectMocks
    private WmEcontractPhfFormalTechWrapperServiceImpl wmEcontractPhfFormalTechWrapperService;

    @Mock
    private EcontractCustomerInfoBo customerInfoBo;

    @Mock
    private EcontractDeliveryPhfInfoBo infoBo;

    @Mock
    private EcontractDeliveryInfoBo econtractDeliveryInfoBo;

    @Mock
    private EcontractDeliveryPhfTechInfoBo techInfoBo;

    private WmEcontractPhfFormalTechWrapperServiceImpl service;

    private Method generatePdfBizContentMethod;

    @Before
    public void setUp() throws Exception {
        service = new WmEcontractPhfFormalTechWrapperServiceImpl();
        generatePdfBizContentMethod = WmEcontractPhfFormalTechWrapperServiceImpl.class.getDeclaredMethod("generatePdfBizContent", List.class);
        generatePdfBizContentMethod.setAccessible(true);
    }

    private void invokeFillBaseInfo(Map<String, String> bizContentMap, EcontractDeliveryPhfInfoBo infoBo) throws Exception {
        Method method = WmEcontractPhfFormalTechWrapperServiceImpl.class.getDeclaredMethod("fillBaseInfo", Map.class, EcontractDeliveryPhfInfoBo.class);
        method.setAccessible(true);
        method.invoke(wmEcontractPhfFormalTechWrapperService, bizContentMap, infoBo);
    }

    /**
     * Tests the generatePdfMetaContent method under normal conditions.
     */
    @Test
    public void testGeneratePdfMetaContentNormal() throws Throwable {
        // Arrange
        when(customerInfoBo.getCustomerName()).thenReturn("testCustomerName");
        // Act
        Map<String, String> result = wmEcontractPhfFormalTechWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        // Assert
        assertNotNull(result);
        assertEquals("testCustomerName", result.get("partAName"));
        assertEquals("北京三快在线科技有限公司", result.get("partBName"));
    }

    /**
     * 测试generatePdfBizContent方法，正常场景
     */
    @Test
    public void testGeneratePdfBizContentNormal() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos = new ArrayList<>();
        econtractDeliveryInfoBos.add(econtractDeliveryInfoBo);
        when(econtractDeliveryInfoBo.getEcontractDeliveryPhfInfoBo()).thenReturn(infoBo);
        when(infoBo.getWmPoiId()).thenReturn("123");
        when(infoBo.getWmPoiName()).thenReturn("testPoi");
        Map<Integer, EcontractDeliveryPhfTechInfoBo> techInfo = new HashMap<>();
        when(infoBo.getTechInfo()).thenReturn(techInfo);
        // act
        List<Map<String, String>> result = (List<Map<String, String>>) generatePdfBizContentMethod.invoke(service, econtractDeliveryInfoBos);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        Map<String, String> contentMap = result.get(0);
        assertEquals("123", contentMap.get("wmPoiId"));
        assertEquals("testPoi", contentMap.get("wmPoiName"));
    }

    /**
     * 测试generatePdfBizContent方法，异常场景，econtractDeliveryInfoBos为空
     */
    @Test
    public void testGeneratePdfBizContentEmptyList() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos = new ArrayList<>();
        // act
        List<Map<String, String>> result = (List<Map<String, String>>) generatePdfBizContentMethod.invoke(service, econtractDeliveryInfoBos);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试generatePdfBizContent方法，异常场景，EcontractDeliveryPhfInfoBo为空
     */
    @Test
    public void testGeneratePdfBizContentNullPhfInfo() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos = new ArrayList<>();
        econtractDeliveryInfoBos.add(econtractDeliveryInfoBo);
        when(econtractDeliveryInfoBo.getEcontractDeliveryPhfInfoBo()).thenReturn(null);
        try {
            // act
            generatePdfBizContentMethod.invoke(service, econtractDeliveryInfoBos);
            fail("Expected NullPointerException");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * 测试generatePdfBizContent方法，异常场景，fillTechFeeInfo抛出异常
     */
    @Test
    public void testGeneratePdfBizContentTechFeeException() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos = new ArrayList<>();
        econtractDeliveryInfoBos.add(econtractDeliveryInfoBo);
        when(econtractDeliveryInfoBo.getEcontractDeliveryPhfInfoBo()).thenReturn(infoBo);
        when(infoBo.getWmPoiId()).thenReturn("123");
        when(infoBo.getWmPoiName()).thenReturn("testPoi");
        // Setup invalid tech info to trigger WmCustomerException
        Map<Integer, EcontractDeliveryPhfTechInfoBo> techInfo = new HashMap<>();
        // Invalid delivery type to trigger exception
        techInfo.put(999999, techInfoBo);
        when(infoBo.getTechInfo()).thenReturn(techInfo);
        try {
            // act
            generatePdfBizContentMethod.invoke(service, econtractDeliveryInfoBos);
            fail("Expected WmCustomerException");
        } catch (Exception e) {
            // assert
            Throwable cause = e.getCause();
            assertTrue("Expected WmCustomerException but got: " + cause.getClass().getName(), cause instanceof WmCustomerException);
            assertEquals("拼好饭合同技术费率类型异常", cause.getMessage());
        }
    }

    /**
     * Test fillBaseInfo with normal input values
     * Scenario: All input parameters are valid
     */
    @Test
    public void testFillBaseInfo_WithValidInput() throws Throwable {
        // arrange
        Map<String, String> bizContentMap = new HashMap<>();
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        infoBo.setWmPoiId("12345");
        infoBo.setWmPoiName("Test Restaurant");
        // act
        invokeFillBaseInfo(bizContentMap, infoBo);
        // assert
        Assert.assertEquals("12345", bizContentMap.get("wmPoiId"));
        Assert.assertEquals("Test Restaurant", bizContentMap.get("wmPoiName"));
        Assert.assertEquals("Test Restaurant ID 12345", bizContentMap.get("wmPoiNameAndId"));
    }

    /**
     * Test fillBaseInfo with empty strings
     * Scenario: WmPoiId and WmPoiName are empty strings
     */
    @Test
    public void testFillBaseInfo_WithEmptyStrings() throws Throwable {
        // arrange
        Map<String, String> bizContentMap = new HashMap<>();
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        infoBo.setWmPoiId("");
        infoBo.setWmPoiName("");
        // act
        invokeFillBaseInfo(bizContentMap, infoBo);
        // assert
        Assert.assertEquals("", bizContentMap.get("wmPoiId"));
        Assert.assertEquals("", bizContentMap.get("wmPoiName"));
        Assert.assertEquals(" ID ", bizContentMap.get("wmPoiNameAndId"));
    }

    /**
     * Test fillBaseInfo with null values in InfoBo
     * Scenario: WmPoiId and WmPoiName are null
     */
    @Test
    public void testFillBaseInfo_WithNullValues() throws Throwable {
        // arrange
        Map<String, String> bizContentMap = new HashMap<>();
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        infoBo.setWmPoiId(null);
        infoBo.setWmPoiName(null);
        // act
        invokeFillBaseInfo(bizContentMap, infoBo);
        // assert
        Assert.assertNull(bizContentMap.get("wmPoiId"));
        Assert.assertNull(bizContentMap.get("wmPoiName"));
        Assert.assertEquals("null ID null", bizContentMap.get("wmPoiNameAndId"));
    }

    /**
     * Test fillBaseInfo with special characters
     * Scenario: WmPoiId and WmPoiName contain special characters
     */
    @Test
    public void testFillBaseInfo_WithSpecialCharacters() throws Throwable {
        // arrange
        Map<String, String> bizContentMap = new HashMap<>();
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        infoBo.setWmPoiId("123!@#");
        infoBo.setWmPoiName("Test@Restaurant#$%");
        // act
        invokeFillBaseInfo(bizContentMap, infoBo);
        // assert
        Assert.assertEquals("123!@#", bizContentMap.get("wmPoiId"));
        Assert.assertEquals("Test@Restaurant#$%", bizContentMap.get("wmPoiName"));
        Assert.assertEquals("Test@Restaurant#$% ID 123!@#", bizContentMap.get("wmPoiNameAndId"));
    }

    /**
     * Test fillBaseInfo with null bizContentMap
     * Scenario: bizContentMap parameter is null
     */
    @Test
    public void testFillBaseInfo_WithNullMap() throws Throwable {
        // arrange
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        infoBo.setWmPoiId("12345");
        infoBo.setWmPoiName("Test Restaurant");
        try {
            // act
            invokeFillBaseInfo(null, infoBo);
            Assert.fail("Expected NullPointerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            Assert.assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Test fillBaseInfo with null InfoBo
     * Scenario: InfoBo parameter is null
     */
    @Test
    public void testFillBaseInfo_WithNullInfoBo() throws Throwable {
        // arrange
        Map<String, String> bizContentMap = new HashMap<>();
        try {
            // act
            invokeFillBaseInfo(bizContentMap, null);
            Assert.fail("Expected NullPointerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            Assert.assertTrue(e.getCause() instanceof NullPointerException);
        }
    }
}