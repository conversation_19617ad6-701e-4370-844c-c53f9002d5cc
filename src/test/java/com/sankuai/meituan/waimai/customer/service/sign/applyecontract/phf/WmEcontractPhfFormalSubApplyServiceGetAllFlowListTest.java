package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for WmEcontractPhfFormalSubApplyService.getAllFlowList method
 */
@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfFormalSubApplyServiceGetAllFlowListTest {

    @InjectMocks
    private WmEcontractPhfFormalSubApplyService service;

    private Method getAllFlowListMethod;

    @Before
    public void setUp() throws Exception {
        getAllFlowListMethod = WmEcontractPhfFormalSubApplyService.class.getDeclaredMethod("getAllFlowList", Map.class);
        getAllFlowListMethod.setAccessible(true);
    }

    /**
     * Test normal case with both enum values present
     */
    @Test
    public void testGetAllFlowList_WithBothEnumValues() throws Throwable {
        // arrange
        Map<EcontractDataWrapperEnum, List<String>> flowListMap = new HashMap<>();
        List<String> techFlows = new ArrayList<>();
        techFlows.add("tech1");
        techFlows.add("tech2");
        List<String> perFlows = new ArrayList<>();
        perFlows.add("per1");
        perFlows.add("per2");
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_TECH, techFlows);
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_PER, perFlows);
        // act
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) getAllFlowListMethod.invoke(service, flowListMap);
        // assert
        assertEquals(4, result.size());
        assertTrue(result.containsAll(techFlows));
        assertTrue(result.containsAll(perFlows));
    }

    /**
     * Test case with only PHF_FORMAL_TECH present
     */
    @Test
    public void testGetAllFlowList_OnlyTechFlows() throws Throwable {
        // arrange
        Map<EcontractDataWrapperEnum, List<String>> flowListMap = new HashMap<>();
        List<String> techFlows = new ArrayList<>();
        techFlows.add("tech1");
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_TECH, techFlows);
        // Need to add empty list for PHF_FORMAL_PER to avoid NPE
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_PER, new ArrayList<>());
        // act
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) getAllFlowListMethod.invoke(service, flowListMap);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.containsAll(techFlows));
    }

    /**
     * Test case with only PHF_FORMAL_PER present
     */
    @Test
    public void testGetAllFlowList_OnlyPerFlows() throws Throwable {
        // arrange
        Map<EcontractDataWrapperEnum, List<String>> flowListMap = new HashMap<>();
        List<String> perFlows = new ArrayList<>();
        perFlows.add("per1");
        // Need to add empty list for PHF_FORMAL_TECH to avoid NPE
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_TECH, new ArrayList<>());
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_PER, perFlows);
        // act
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) getAllFlowListMethod.invoke(service, flowListMap);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.containsAll(perFlows));
    }

    /**
     * Test case with empty map
     */
    @Test
    public void testGetAllFlowList_EmptyMap() throws Throwable {
        // arrange
        Map<EcontractDataWrapperEnum, List<String>> flowListMap = new HashMap<>();
        // Need to add empty lists for both enums to avoid NPE
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_TECH, new ArrayList<>());
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_PER, new ArrayList<>());
        // act
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) getAllFlowListMethod.invoke(service, flowListMap);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case with null map
     */
    @Test(expected = NullPointerException.class)
    public void testGetAllFlowList_NullMap() throws Throwable {
        try {
            // act
            getAllFlowListMethod.invoke(service, new Object[] { null });
        } catch (Exception e) {
            if (e.getCause() instanceof NullPointerException) {
                throw (NullPointerException) e.getCause();
            }
            throw e;
        }
    }

    /**
     * Test case with null values for keys
     */
    @Test
    public void testGetAllFlowList_NullValues() throws Throwable {
        // arrange
        Map<EcontractDataWrapperEnum, List<String>> flowListMap = new HashMap<>();
        // Need to add empty lists instead of null to avoid NPE
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_TECH, new ArrayList<>());
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_PER, new ArrayList<>());
        // act
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) getAllFlowListMethod.invoke(service, flowListMap);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case with empty lists for keys
     */
    @Test
    public void testGetAllFlowList_EmptyLists() throws Throwable {
        // arrange
        Map<EcontractDataWrapperEnum, List<String>> flowListMap = new HashMap<>();
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_TECH, new ArrayList<>());
        flowListMap.put(EcontractDataWrapperEnum.PHF_FORMAL_PER, new ArrayList<>());
        // act
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) getAllFlowListMethod.invoke(service, flowListMap);
        // assert
        assertTrue(result.isEmpty());
    }
}