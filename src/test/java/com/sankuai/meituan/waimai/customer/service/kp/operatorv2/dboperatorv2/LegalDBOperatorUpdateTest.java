package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformNoticeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.DifferentCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.dboperator.KpDBOperateImpl;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.KpDataVerify;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpLegalFlowAbility;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpLegalStatusMachineContext;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpLegalStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class LegalDBOperatorUpdateTest {

    @InjectMocks
    private LegalDBOperator legalDBOperator;

    @Mock
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Mock
    private WmCustomerPlatformNoticeService noticeService;

    @Mock
    private KpDataVerify kpDataVerify;

    @Mock
    private KpLegalFlowAbility kpLegalFlowAbility;

    @Mock
    private DifferentCustomerKpService differentCustomerKpService;

    @Mock
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Mock
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Mock
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Mock
    private KpDBOperateImpl kpDBOperate;

    private WmCustomerDB wmCustomer;

    private List<WmCustomerKp> oldCustomerKpList;

    private List<WmCustomerKp> updateKpList;

    private int uid;

    private String uname;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        wmCustomer = new WmCustomerDB();
        oldCustomerKpList = Arrays.asList(new WmCustomerKp(), new WmCustomerKp());
        updateKpList = Arrays.asList(new WmCustomerKp(), new WmCustomerKp());
        uid = 1;
        uname = "testUser";
    }

    /**
     * 测试 oldCustomerKp 为 null 的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testUpdateOldCustomerKpIsNull() throws Throwable {
        // arrange
        WmCustomerKp updateKp = new WmCustomerKp();
        updateKp.setId(1);
        updateKpList = Collections.singletonList(updateKp);
        oldCustomerKpList = Collections.emptyList();
        // act
        legalDBOperator.update(wmCustomer, oldCustomerKpList, updateKpList, uid, uname);
        // assert
        // Expected WmCustomerException
    }
}
