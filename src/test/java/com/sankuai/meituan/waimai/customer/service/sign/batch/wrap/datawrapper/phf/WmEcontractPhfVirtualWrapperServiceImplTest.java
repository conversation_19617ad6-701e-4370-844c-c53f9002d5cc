package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfVirtualWrapperServiceImplTest extends BaseStaticMockTest  {

    @InjectMocks
    private WmEcontractPhfVirtualWrapperServiceImpl wmEcontractPhfVirtualWrapperService;

    @Mock
    private EcontractCustomerInfoBo customerInfoBo;

    @Mock
    private EcontractDeliveryPhfInfoBo infoBo;

    @Mock
    private EcontractDeliveryPhfTechInfoBo techInfoBo;

    @Mock
    private EcontractDeliveryPhfPerInfoBo perInfoBo;

    @Mock
    private EcontractBatchContextBo contextBo;

    @Before
    public void setUp() {
        super.init();
        mccConfigMockedStatic.when(MccConfig::getPhfContractNameInFormal).thenReturn("美团拼好饭服务费收费协议");
    }

        @Test
    public void testGeneratePdfMetaContentNormal() throws Throwable {
        when(infoBo.getTechInfo()).thenReturn(new HashMap<>());
        when(infoBo.getPerInfo()).thenReturn(new HashMap<>());
        Map<String, String> result = wmEcontractPhfVirtualWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        assertNotNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testGeneratePdfMetaContentCustomerInfoBoNull() throws Throwable {
        wmEcontractPhfVirtualWrapperService.generatePdfMetaContent(null, infoBo);
    }

    @Test(expected = NullPointerException.class)
    public void testGeneratePdfMetaContentInfoBoNull() throws Throwable {
        wmEcontractPhfVirtualWrapperService.generatePdfMetaContent(customerInfoBo, null);
    }

    @Test
    public void testGeneratePdfMetaContentTechInfoNull() throws Throwable {
        when(infoBo.getTechInfo()).thenReturn(new HashMap<>());
        Map<String, String> result = wmEcontractPhfVirtualWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        assertNotNull(result);
    }

    @Test
    public void testGeneratePdfMetaContentPerInfoNull() throws Throwable {
        when(infoBo.getPerInfo()).thenReturn(new HashMap<>());
        Map<String, String> result = wmEcontractPhfVirtualWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        assertNotNull(result);
    }

    /**
     * Test successful case with valid delivery info
     */
    @Test
    public void testWrapSuccess() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName());
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        EcontractDeliveryPhfInfoBo phfInfoBo = new EcontractDeliveryPhfInfoBo();
        phfInfoBo.setWmPoiName("Test POI");
        phfInfoBo.setWmPoiId("123");
        phfInfoBo.setTechInfo(Maps.newHashMap());
        phfInfoBo.setPerInfo(Maps.newHashMap());
        deliveryInfoBo.setEcontractDeliveryPhfInfoBo(phfInfoBo);

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList(deliveryInfoBo));
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));

        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        when(contextBo.getTaskIdAndTaskMap()).thenReturn(taskMap);
        when(contextBo.getCustomerInfoBo()).thenReturn(new EcontractCustomerInfoBo());
        // act
        List<PdfContentInfoBo> result = wmEcontractPhfVirtualWrapperService.wrap(contextBo);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("适用于 Test POI", result.get(0).getContractDesc());
        assertEquals("美团拼好饭服务费收费协议", result.get(0).getContractName());
    }

    /**
     * Test case when delivery info list is empty
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapWithEmptyDeliveryInfo() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName());

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList());
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        // act
        wmEcontractPhfVirtualWrapperService.wrap(contextBo);
    }

    /**
     * Test case with null EcontractSignVersionBo
     */
    @Test
    public void testWrapWithNullSignVersionBo() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName());
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        EcontractDeliveryPhfInfoBo phfInfoBo = new EcontractDeliveryPhfInfoBo();
        phfInfoBo.setWmPoiName("Test POI");
        phfInfoBo.setWmPoiId("123");
        phfInfoBo.setTechInfo(Maps.newHashMap());
        phfInfoBo.setPerInfo(Maps.newHashMap());
        deliveryInfoBo.setEcontractDeliveryPhfInfoBo(phfInfoBo);

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList(deliveryInfoBo));
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));

        // 这里需要用非mock的对象
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setEcontractSignVersionBo(null);

        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        contextBo.setCustomerInfoBo(new EcontractCustomerInfoBo());
        // act
        List<PdfContentInfoBo> result = wmEcontractPhfVirtualWrapperService.wrap(contextBo);
        // assert
        assertNotNull(result);
        assertNotNull(contextBo.getEcontractSignVersionBo());
        assertTrue(contextBo.getEcontractSignVersionBo().getDeliverySignVersion().endsWith("B"));
    }

    /**
     * Test case when task not found
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapTaskNotFound() throws Throwable {
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.SETTLE.getName());
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        EcontractDeliveryPhfInfoBo phfInfoBo = new EcontractDeliveryPhfInfoBo();
        phfInfoBo.setWmPoiName("Test POI");
        phfInfoBo.setWmPoiId("123");
        phfInfoBo.setTechInfo(Maps.newHashMap());
        phfInfoBo.setPerInfo(Maps.newHashMap());
        deliveryInfoBo.setEcontractDeliveryPhfInfoBo(phfInfoBo);

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList(deliveryInfoBo));
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));

        // 这里需要用非mock的对象
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setEcontractSignVersionBo(null);

        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        contextBo.setCustomerInfoBo(new EcontractCustomerInfoBo());

        // act
        wmEcontractPhfVirtualWrapperService.wrap(contextBo);
    }

    /**
     * Test case with multiple delivery infos
     */
    @Test
    public void testWrapWithMultipleDeliveryInfos() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName());

        List<EcontractDeliveryInfoBo> deliveryInfos = Lists.newArrayList();
        // First delivery info
        EcontractDeliveryInfoBo deliveryInfo1 = new EcontractDeliveryInfoBo();
        EcontractDeliveryPhfInfoBo phfInfo1 = new EcontractDeliveryPhfInfoBo();
        phfInfo1.setWmPoiName("Test POI 1");
        phfInfo1.setWmPoiId("123");
        phfInfo1.setTechInfo(Maps.newHashMap());
        phfInfo1.setPerInfo(Maps.newHashMap());
        deliveryInfo1.setEcontractDeliveryPhfInfoBo(phfInfo1);
        deliveryInfos.add(deliveryInfo1);
        // Second delivery info
        EcontractDeliveryInfoBo deliveryInfo2 = new EcontractDeliveryInfoBo();
        EcontractDeliveryPhfInfoBo phfInfo2 = new EcontractDeliveryPhfInfoBo();
        phfInfo2.setWmPoiName("Test POI 2");
        phfInfo2.setWmPoiId("456");
        phfInfo2.setTechInfo(Maps.newHashMap());
        phfInfo2.setPerInfo(Maps.newHashMap());
        deliveryInfo2.setEcontractDeliveryPhfInfoBo(phfInfo2);
        deliveryInfos.add(deliveryInfo2);

        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryInfos);

        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);

        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setTaskIdAndTaskMap(taskMap);
        contextBo.setCustomerInfoBo(new EcontractCustomerInfoBo());
        // act
        List<PdfContentInfoBo> result = wmEcontractPhfVirtualWrapperService.wrap(contextBo);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("适用于 Test POI 1", result.get(0).getContractDesc());
        assertEquals("适用于 Test POI 2", result.get(1).getContractDesc());
    }
}