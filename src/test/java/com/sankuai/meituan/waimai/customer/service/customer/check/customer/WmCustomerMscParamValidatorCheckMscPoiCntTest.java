package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.check.BaseWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSource;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerMscParamValidatorCheckMscPoiCntTest {

    @InjectMocks
    private WmCustomerMscParamValidator validator;

    @Mock
    private WmCustomerGrayService wmCustomerGrayService;

    private ValidateResultBo validateResultBo;
    private WmCustomerBasicBo wmCustomerBasicBo;

    @Before
    public void setUp() {
        validateResultBo = new ValidateResultBo();
        wmCustomerBasicBo = new WmCustomerBasicBo();
    }

    /**
     * 测试客户类型非美食城时直接通过验证
     */
    @Test
    public void testValidCustomerTypeNotMeishiCheng() throws WmCustomerException {
        // arrange
        wmCustomerBasicBo.setCustomerType(CustomerRealTypeEnum.DEFAULT_TYPE.getValue());
        // 默认命中灰度
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);

        // act
        ValidateResultBo result = validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 0);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_PASS, result.getCode());
    }

    /**
     * 测试客户来源为BD时，档口数和视频有一项为空
     */
    @Test
    public void testValidCustomerSourceIsBD() throws WmCustomerException {
        // arrange
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setOwnerUid(1);
        CustomerRealTypeSpInfoBo spInfoBo = new CustomerRealTypeSpInfoBo();
        spInfoBo.setFoodCityPoiCount(5);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(spInfoBo);
        // 默认命中灰度
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);

        // act
        ValidateResultBo result = validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 0);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_ERROR, result.getCode());
        assertEquals("档口数量和美食城视频均不能为空", result.getMsg());
    }

    /**
     * 测试客户责任人组织架构灰度未命中时直接通过验证
     */
    @Test
    public void testValidGrayServiceNotHit() throws WmCustomerException {
        // arrange
        wmCustomerBasicBo.setCustomerType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(false);

        // act
        ValidateResultBo result = validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 0);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_PASS, result.getCode());
        verify(wmCustomerGrayService, times(1)).isGrayMscPoiCntCheckNew(anyInt());
    }

    /**
     * 测试档口数量为负数时校验失败
     */
    @Test
    public void testValidFoodCityPoiCountNegative() throws WmCustomerException {
        // arrange
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        CustomerRealTypeSpInfoBo spInfoBo = new CustomerRealTypeSpInfoBo();
        spInfoBo.setFoodCityPoiCount(-1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(spInfoBo);
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);

        // act
        ValidateResultBo result = validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 0);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_ERROR, result.getCode());
        assertEquals("档口数量必须为正整数", result.getMsg());
    }

    /**
     * 测试档口数量超过10000时校验失败
     */
    @Test
    public void testValidFoodCityPoiCountExceedsLimit() throws WmCustomerException {
        // arrange
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        CustomerRealTypeSpInfoBo spInfoBo = new CustomerRealTypeSpInfoBo();
        spInfoBo.setFoodCityPoiCount(10001);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(spInfoBo);
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);

        // act
        ValidateResultBo result = validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 0);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_ERROR, result.getCode());
        assertEquals("档口数量不能超过10000", result.getMsg());
    }

    /**
     * 入驻渠道不是BD，可为空
     */
    @Test
    public void testValidFoodCityPoiCountNotBd() throws WmCustomerException {
        // arrange
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_ZRZ);
        wmCustomerBasicBo.setCustomerType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);

        // act
        ValidateResultBo result = validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 0);
        // assert
        assertEquals(CustomerConstants.RESULT_CODE_PASS, result.getCode());
    }


}