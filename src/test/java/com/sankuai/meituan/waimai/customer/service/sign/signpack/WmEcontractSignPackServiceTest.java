package com.sankuai.meituan.waimai.customer.service.sign.signpack;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractSignPackServiceTest {

    @InjectMocks
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Mock
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;

    /**
     * Test case: When batch list is empty, should return empty list
     */
    @Test
    public void testQueryRecordKeyBySignPackIdOrderByPriority_EmptyBatchList() throws WmCustomerException {
        // arrange
        Long packId = 1L;
        when(wmEcontractBigBatchParseService.querySignBatchListByPackId(packId)).thenReturn(new ArrayList<>());
        // act
        List<String> result = wmEcontractSignPackService.queryRecordKeyBySignPackIdOrderByPriority(packId);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: When batch list contains records, should return sorted record keys by priority
     */
    @Test
    public void testQueryRecordKeyBySignPackIdOrderByPriority_NormalCase() throws WmCustomerException {
        // arrange
        Long packId = 1L;
        WmEcontractSignBatchDB batch1 = new WmEcontractSignBatchDB();
        batch1.setRecordKey("key1");
        EcontractBatchContextBo contextBo1 = new EcontractBatchContextBo();
        contextBo1.setPriority(1);
        batch1.setBatchContext(JSONObject.toJSONString(contextBo1));
        WmEcontractSignBatchDB batch2 = new WmEcontractSignBatchDB();
        batch2.setRecordKey("key2");
        EcontractBatchContextBo contextBo2 = new EcontractBatchContextBo();
        contextBo2.setPriority(2);
        batch2.setBatchContext(JSONObject.toJSONString(contextBo2));
        List<WmEcontractSignBatchDB> batchList = new ArrayList<>();
        batchList.add(batch1);
        batchList.add(batch2);

        when(wmEcontractBigBatchParseService.querySignBatchListByPackId(packId)).thenReturn(batchList);
        // act
        List<String> result = wmEcontractSignPackService.queryRecordKeyBySignPackIdOrderByPriority(packId);
        // assert
        assertEquals(2, result.size());
        // Higher priority comes first
        assertEquals("key2", result.get(0));
        assertEquals("key1", result.get(1));
    }

    /**
     * Test case: When batch list contains single record, should return single record key
     */
    @Test
    public void testQueryRecordKeyBySignPackIdOrderByPriority_SingleRecord() throws WmCustomerException {
        // arrange
        Long packId = 1L;
        WmEcontractSignBatchDB batch = new WmEcontractSignBatchDB();
        batch.setRecordKey("key1");
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setPriority(1);
        batch.setBatchContext(JSONObject.toJSONString(contextBo));
        List<WmEcontractSignBatchDB> batchList = Arrays.asList(batch);
        when(wmEcontractBigBatchParseService.querySignBatchListByPackId(packId)).thenReturn(batchList);
        // act
        List<String> result = wmEcontractSignPackService.queryRecordKeyBySignPackIdOrderByPriority(packId);
        // assert
        assertEquals(1, result.size());
        assertEquals("key1", result.get(0));
    }

    /**
     * Test case: When batch list contains records with same priority, should maintain order
     */
    @Test
    public void testQueryRecordKeyBySignPackIdOrderByPriority_SamePriority() throws WmCustomerException {
        // arrange
        Long packId = 1L;
        WmEcontractSignBatchDB batch1 = new WmEcontractSignBatchDB();
        batch1.setRecordKey("key1");
        EcontractBatchContextBo contextBo1 = new EcontractBatchContextBo();
        contextBo1.setPriority(1);
        batch1.setBatchContext(JSONObject.toJSONString(contextBo1));
        WmEcontractSignBatchDB batch2 = new WmEcontractSignBatchDB();
        batch2.setRecordKey("key2");
        EcontractBatchContextBo contextBo2 = new EcontractBatchContextBo();
        contextBo2.setPriority(1);
        batch2.setBatchContext(JSONObject.toJSONString(contextBo2));
        List<WmEcontractSignBatchDB> batchList = new ArrayList<>();
        batchList.add(batch1);
        batchList.add(batch2);

        when(wmEcontractBigBatchParseService.querySignBatchListByPackId(packId)).thenReturn(batchList);
        // act
        List<String> result = wmEcontractSignPackService.queryRecordKeyBySignPackIdOrderByPriority(packId);
        // assert
        assertEquals(2, result.size());
        assertEquals("key1", result.get(0));
        assertEquals("key2", result.get(1));
    }
}