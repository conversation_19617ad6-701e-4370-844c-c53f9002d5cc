package com.sankuai.meituan.waimai.customer.service.sign.cancel;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for WmEcontractCancelDRTaskService
 */
@RunWith(MockitoJUnitRunner.class)
public class WmEcontractCancelDRTaskServiceTest {

    @Spy
    @InjectMocks
    private WmEcontractCancelDRTaskService wmEcontractCancelDRTaskService;

    @Mock
    private EcontractTaskBo mockTaskBo;

    /**
     * Test successful cancellation of contract
     */
    @Test
    public void testCancelSuccess() throws Throwable {
        // arrange
        doNothing().when(wmEcontractCancelDRTaskService).cancelPoiState(any(EcontractTaskBo.class));
        doNothing().when(wmEcontractCancelDRTaskService).cancelTask(any(EcontractTaskBo.class));
        doNothing().when(wmEcontractCancelDRTaskService).cancelBatch(any(EcontractTaskBo.class));
        doNothing().when(wmEcontractCancelDRTaskService).cancelRel(any(EcontractTaskBo.class));
        // act
        Boolean result = wmEcontractCancelDRTaskService.cancel(mockTaskBo);
        // assert
        assertTrue(result);
        verify(wmEcontractCancelDRTaskService).cancelPoiState(mockTaskBo);
        verify(wmEcontractCancelDRTaskService).cancelTask(mockTaskBo);
        verify(wmEcontractCancelDRTaskService).cancelBatch(mockTaskBo);
        verify(wmEcontractCancelDRTaskService).cancelRel(mockTaskBo);
    }

    /**
     * Test cancellation when cancelPoiState throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testCancelWhenCancelPoiStateThrowsException() throws Throwable {
        // arrange
        doThrow(new WmCustomerException(500, "Cancel poi state failed")).when(wmEcontractCancelDRTaskService).cancelPoiState(any(EcontractTaskBo.class));
        // act
        wmEcontractCancelDRTaskService.cancel(mockTaskBo);
    }

    /**
     * Test cancellation when cancelTask throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testCancelWhenCancelTaskThrowsException() throws Throwable {
        // arrange
        doNothing().when(wmEcontractCancelDRTaskService).cancelPoiState(any(EcontractTaskBo.class));
        doThrow(new WmCustomerException(500, "Cancel task failed")).when(wmEcontractCancelDRTaskService).cancelTask(any(EcontractTaskBo.class));
        // act
        wmEcontractCancelDRTaskService.cancel(mockTaskBo);
    }

    /**
     * Test cancellation when cancelRel throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testCancelWhenCancelRelThrowsException() throws Throwable {
        // arrange
        doNothing().when(wmEcontractCancelDRTaskService).cancelPoiState(any(EcontractTaskBo.class));
        doNothing().when(wmEcontractCancelDRTaskService).cancelTask(any(EcontractTaskBo.class));
        doNothing().when(wmEcontractCancelDRTaskService).cancelBatch(any(EcontractTaskBo.class));
        doThrow(new WmCustomerException(500, "Cancel rel failed")).when(wmEcontractCancelDRTaskService).cancelRel(any(EcontractTaskBo.class));
        // act
        wmEcontractCancelDRTaskService.cancel(mockTaskBo);
    }

    /**
     * Test cancellation with null task bo
     */
    @Test(expected = NullPointerException.class)
    public void testCancelWithNullTaskBo() throws Throwable {
        // act
        wmEcontractCancelDRTaskService.cancel(null);
    }
}