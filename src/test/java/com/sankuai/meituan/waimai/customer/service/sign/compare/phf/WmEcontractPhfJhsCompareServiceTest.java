package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfJhsCompareServiceTest {

    private WmEcontractPhfJhsCompareService wmEcontractPhfJhsCompareService;

    @Before
    public void setUp() {
        wmEcontractPhfJhsCompareService = new WmEcontractPhfJhsCompareService();
    }

    /**
     * 测试 getPdfContentInfoBoMapKeyByWmPoiId 方法，传入有效的 wmPoiId
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiId_ValidWmPoiId() throws Throwable {
        // arrange
        Long wmPoiId = 123L;
        // act
        String result = wmEcontractPhfJhsCompareService.getPdfContentInfoBoMapKeyByWmPoiId(wmPoiId);
        // assert
        assertEquals("JHS_123", result);
    }

    /**
     * 测试 getPdfContentInfoBoMapKeyByWmPoiId 方法，传入 null 的 wmPoiId
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiId_NullWmPoiId() throws Throwable {
        // arrange
        Long wmPoiId = null;
        // act
        String result = wmEcontractPhfJhsCompareService.getPdfContentInfoBoMapKeyByWmPoiId(wmPoiId);
        // assert
        assertEquals("JHS_null", result);
    }
}