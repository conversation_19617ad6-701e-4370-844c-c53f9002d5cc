package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.aspect.AreaDataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class WmEcontractDeliveryAggregationAreaDataWrapperServiceTest {

    @InjectMocks
    private WmEcontractDeliveryAggregationAreaDataWrapperService service;

    private MockedStatic<ConfigUtilAdapter> configUtilAdapterMockedStatic;
    private MockedStatic<WmEcontractContextUtil> wmEcontractContextUtilMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        wmEcontractContextUtilMockedStatic = mockStatic(WmEcontractContextUtil.class);
        configUtilAdapterMockedStatic = mockStatic(ConfigUtilAdapter.class);
    }

    @After
    public void after() {
        if (null != wmEcontractContextUtilMockedStatic) {
            wmEcontractContextUtilMockedStatic.close();
        }
        if (null != configUtilAdapterMockedStatic) {
            configUtilAdapterMockedStatic.close();
        }
    }

    /**
     * 测试wrap方法，当applyContext解析异常时
     */
    @Test
    public void testWrapApplyContextParseException() throws IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractTaskBo taskBo = mock(EcontractTaskBo.class);
        when(taskBo.getApplyContext()).thenReturn("invalid json");
        wmEcontractContextUtilMockedStatic.when(() -> WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE)).thenReturn(taskBo);

        // act
        List<EcontractContentBo> result = service.wrap(contextBo);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试wrap方法，当deliveryInfoBo为null时
     */
    @Test
    public void testWrapDeliveryInfoBoIsNull() throws IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractTaskBo taskBo = mock(EcontractTaskBo.class);
        when(taskBo.getApplyContext()).thenReturn("{}");
        wmEcontractContextUtilMockedStatic.when(() -> WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE)).thenReturn(taskBo);

        // act
        List<EcontractContentBo> result = service.wrap(contextBo);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试wrap方法，当supportAggregationDelivery不为support时
     */
    @Test
    public void testWrapSupportAggregationDeliveryNotSupport() throws IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractTaskBo taskBo = mock(EcontractTaskBo.class);
        when(taskBo.getApplyContext()).thenReturn("{\"supportAggregationDelivery\":\"not support\"}");
        wmEcontractContextUtilMockedStatic.when(() -> WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE)).thenReturn(taskBo);

        // act
        List<EcontractContentBo> result = service.wrap(contextBo);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试wrap方法，当area为空时
     */
    @Test
    public void testWrapAreaIsEmpty() throws IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractTaskBo taskBo = mock(EcontractTaskBo.class);
        when(taskBo.getApplyContext()).thenReturn("{\"supportAggregationDelivery\":\"support\",\"econtractDeliveryAggregationInfoBo\":{}}");
        wmEcontractContextUtilMockedStatic.when(() -> WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE)).thenReturn(taskBo);

        // act
        List<EcontractContentBo> result = service.wrap(contextBo);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试wrap方法，当selfDeliveryPlanBoList为空时
     */
    @Test
    public void testWrapSelfDeliveryPlanBoListIsEmpty() throws IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractTaskBo taskBo = mock(EcontractTaskBo.class);
        when(taskBo.getApplyContext()).thenReturn("{\"supportAggregationDelivery\":\"support\",\"econtractDeliveryAggregationInfoBo\":{\"deliveryArea\":\"{\\\"selfDeliveryPlanBoList\\\":[]}\"}}");
        wmEcontractContextUtilMockedStatic.when(() -> WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE)).thenReturn(taskBo);
        configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getString(anyString(), anyString())).thenReturn("");
        // act
        List<EcontractContentBo> result = service.wrap(contextBo);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试wrap方法，正常情况
     */
    @Test
    public void testWrapNormal() throws IllegalAccessException, WmCustomerException {
        // arrange
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractTaskBo taskBo = mock(EcontractTaskBo.class);
        when(taskBo.getApplyContext()).thenReturn("{\"supportAggregationDelivery\":\"support\",\"econtractDeliveryAggregationInfoBo\":{\"deliveryArea\":\"{\\\"selfDeliveryPlanBoList\\\":[{}]}\"}}");
        wmEcontractContextUtilMockedStatic.when(() -> WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE)).thenReturn(taskBo);
        configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getString(anyString(), anyString())).thenReturn("");

        // act
        List<EcontractContentBo> result = service.wrap(contextBo);

        // assert
        assertFalse(result.isEmpty());
    }

    // 更多测试用例...
}