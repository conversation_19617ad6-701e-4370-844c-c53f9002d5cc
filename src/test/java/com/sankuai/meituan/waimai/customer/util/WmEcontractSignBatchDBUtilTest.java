package com.sankuai.meituan.waimai.customer.util;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractSignBatchDBUtilTest extends BaseStaticMockTest {

    @Before
    public void setUp() {
        super.init();
        mccConfigMockedStatic.when(MccConfig::isSupportSortByPriority).thenReturn(true);
    }

    /**
     * 测试 sortByPriority 方法，当 batchDBList 为空时，不进行排序
     */
    @Test
    public void testSortByPriorityWhenBatchDBListIsEmpty() throws Throwable {
        // arrange
        List<WmEcontractSignBatchDB> signBatchDBList = new ArrayList<>();
        // act
        List<WmEcontractSignBatchDB> result = WmEcontractSignBatchDBUtil.sortByPriority(signBatchDBList);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 sortByPriority 方法，当 batchDBList 不为空，且 batchContext 字段可以正常解析为 EcontractBatchContextBo 对象时，进行排序
     */
    @Test
    public void testSortByPriorityWhenBatchDBListIsNotEmptyAndBatchContextCanBeParsed() throws Throwable {
        // arrange
        WmEcontractSignBatchDB batchDB1 = new WmEcontractSignBatchDB();
        batchDB1.setBatchContext("{\"ids\":1}");
        WmEcontractSignBatchDB batchDB2 = new WmEcontractSignBatchDB();
        batchDB2.setBatchContext("{\"priority\":1}");
        List<WmEcontractSignBatchDB>  signBatchDBList = new ArrayList<>();
        signBatchDBList.add(batchDB1);
        signBatchDBList.add(batchDB2);

        // act
        List<WmEcontractSignBatchDB> result = WmEcontractSignBatchDBUtil.sortByPriority(signBatchDBList);

        // assert
        // Adjusted to expect descending order
        assertEquals(Arrays.asList(batchDB2, batchDB1), result);
    }

    /**
     * 测试 sortByPriority 方法，当 batchDBList 不为空，但 batchContext 字段无法解析为 EcontractBatchContextBo 对象时，不进行排序
     */
    @Test
    public void testSortByPriorityWhenBatchDBListIsNotEmptyAndBatchContextCannotBeParsed() throws Throwable {
        // arrange
        WmEcontractSignBatchDB batchDB = new WmEcontractSignBatchDB();
        batchDB.setBatchContext("invalid json");
        List<WmEcontractSignBatchDB> signBatchDBList = new ArrayList<>();
        signBatchDBList.add(batchDB);

        // act
        List<WmEcontractSignBatchDB> result = WmEcontractSignBatchDBUtil.sortByPriority(signBatchDBList);
        // assert
        assertEquals(signBatchDBList, result);
    }


    /**
     * test emptyList
     */
    @Test
    public void testIsNeedSortByPriority_EmptyList() {
        List<WmEcontractSignBatchDB> emptyList = new ArrayList<>();
        assertFalse(WmEcontractSignBatchDBUtil.isNeedSortByPriority(emptyList));
    }

    /**
     * test null customerRealType
     */
    @Test
    public void testIsNeedSortByPriority_NullCustomerRealType() {
        List<WmEcontractSignBatchDB> batchDBList = createBatchDBList(null);
        assertFalse(WmEcontractSignBatchDBUtil.isNeedSortByPriority(batchDBList));
    }

    /**
     * test not DANDIAN_SG
     */
    @Test
    public void testIsNeedSortByPriority_NotDandianSG() {
        List<WmEcontractSignBatchDB> batchDBList = createBatchDBList(CustomerRealTypeEnum.DANDIAN.getValue());
        assertFalse(WmEcontractSignBatchDBUtil.isNeedSortByPriority(batchDBList));
    }

    /**
     * test is DanDianSG
     */
    @Test
    public void testIsNeedSortByPriority_DandianSG() {
        List<WmEcontractSignBatchDB> batchDBList = createBatchDBList(CustomerRealTypeEnum.DANDIAN_SG.getValue());
        assertTrue(WmEcontractSignBatchDBUtil.isNeedSortByPriority(batchDBList));
    }


    private List<WmEcontractSignBatchDB> createBatchDBList(Integer customerRealType) {
        WmEcontractSignBatchDB batchDB = new WmEcontractSignBatchDB();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerRealType(customerRealType);
        contextBo.setCustomerInfoBo(customerInfoBo);
        batchDB.setBatchContext(JSON.toJSONString(contextBo));
        return Collections.singletonList(batchDB);
    }
}