package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.adapter.WmCustomerGrayServiceAdapter;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.signTemplet.SignTempletManagerService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PdfTemplet;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

/**
 * WmEcontractDataPdfMappingCollectorTest
 *
 * @Author: wangyongfang
 * @Date: 2024-10-21
 */
public class WmEcontractDataPdfMappingCollectorTest extends BaseStaticMockTest {

    @Mock
    private SignTempletManagerService signTempletManagerService;
    @Mock
    private WmCustomerGrayServiceAdapter wmCustomerGrayServiceAdapter;
    @Mock
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    @InjectMocks
    private WmEcontractDataPdfMappingCollector wmEcontractDataPdfMappingCollector;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        super.init();
        configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getString(anyString())).thenReturn("1");
    }

    /**
     * 测试路径：MccConfig.getAddPackageFeeSwitch() -> !!addPackageFee -> StringUtils.isNotEmpty(signVersion) -> !deliveryPdfTemplet == null -> econtractSignVersionBo == null -> signDataFactor.isDeliverySupporOldModleAndSupportSLA() -> signDataFactor.isDeliverySupportExclusive() -> needAddPerformancePdfTemplate(signDataFactor) -> signDataFactor.isDeliverySupportWholeCity() -> wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(originContext.getCustomerId()) -> signDataFactor.isDeliverySupportWaimaiV3Feemode() -> signDataFactor.isDeliverySupportAggregation() -> signDataFactor.isDeliveryMultiWmPoi() -> signDataFactor.isDeliverySupportAggregationV1() -> signDataFactor.isDeliverySupportCompanyCustomerLongDistanceDelivery() -> signDataFactor.isDeliverySupportColdChain() -> signDataFactor.isDeliverySupportAgentNew() -> signDataFactor.isDeliveryMultiWmPoi() -> signDataFactor.isDeliverySupportXZS() && signDataFactor.isSupportPerDiscountInfo() -> signDataFactor.isDeliveryMultiWmPoi()
     */
    @Test
    public void testProcessDelivery_Path1() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        List<SignTemplateEnum> pdfEnumList = new ArrayList<>();
        PdfTemplet deliveryPdfTemplet = buildPdfTemplet();

        when(signTempletManagerService.getPdfTemplet(anyString(), anyString())).thenReturn(deliveryPdfTemplet);
        when(wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(anyInt())).thenReturn(true);

        // act
        wmEcontractDataPdfMappingCollector.processDelivery(originContext, signDataFactor, pdfEnumList);

        // assert
        assertFalse(pdfEnumList.isEmpty());
    }

    private PdfTemplet buildPdfTemplet() {
        String pdfTempletJson = "{\"feeInfoTemplet\":\"delivery_multi_base_info_v2\"," + "\"performanceGuaranceInfoTemplet\":\"delivery_performance_guarance_info_v2\"," + "\"preferentialApplyInfoTemplet\":\"delivery_preferential_apply_info_v2\"," + "\"slaOtherInfoTemplet\":\"delivery_sla_other_info_v2\"," + "\"aggregationFeeInfoTemplet\":\"delivery_multi_aggregation_info\"," + "\"aggregationSupplementInfoTemplet\":\"delivery_aggregation_supplement_info\"," + "\"technicalServiceFeeTemplet\":\"multi_technical_service_fee_v1\"," + "\"preferentialPolicyTemplet\":\"multi_preferential_policy_v1\"," + "\"performanceServiceFeeTemplet\":\"multi_performance_service_fee_v1\"}";
        return JSONObject.parseObject(pdfTempletJson, PdfTemplet.class);
    }

    /**
     * 测试路径：MccConfig.getAddPackageFeeSwitch() -> !!addPackageFee -> StringUtils.isNotEmpty(signVersion) -> !deliveryPdfTemplet == null -> econtractSignVersionBo == null -> signDataFactor.isDeliverySupporOldModleAndSupportSLA() -> signDataFactor.isDeliverySupportExclusive() -> needAddPerformancePdfTemplate(signDataFactor) -> signDataFactor.isDeliverySupportWholeCity() -> wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(originContext.getCustomerId()) -> signDataFactor.isDeliverySupportWaimaiV3Feemode() -> signDataFactor.isDeliverySupportAggregation() -> signDataFactor.isDeliveryMultiWmPoi() -> signDataFactor.isDeliverySupportAggregationV1() -> signDataFactor.isDeliverySupportCompanyCustomerLongDistanceDelivery() -> signDataFactor.isDeliverySupportColdChain() -> signDataFactor.isDeliverySupportAgentNew() -> signDataFactor.isDeliveryMultiWmPoi() -> signDataFactor.isDeliverySupportXZS() && signDataFactor.isSupportPerDiscountInfo() -> !signDataFactor.isDeliveryMultiWmPoi()
     */
    @Test
    public void testProcessDelivery_Path2() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        List<SignTemplateEnum> pdfEnumList = new ArrayList<>();
        PdfTemplet deliveryPdfTemplet = buildPdfTemplet();
        when(signTempletManagerService.getPdfTemplet(anyString(), anyString())).thenReturn(deliveryPdfTemplet);
        when(wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(anyInt())).thenReturn(true);

        // act
        wmEcontractDataPdfMappingCollector.processDelivery(originContext, signDataFactor, pdfEnumList);

        // assert
        assertFalse(pdfEnumList.isEmpty());
    }

    /**
     * 测试路径：
     */
    @Test
    public void testProcessDelivery_Path3() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliverySupportAggregation(true);
        signDataFactor.setDeliverySupportSGV2_0Aggregation(true);
        signDataFactor.setDeliverySupportSGV2_2Aggregation(true);
        List<SignTemplateEnum> pdfEnumList = new ArrayList<>();
        PdfTemplet deliveryPdfTemplet = buildPdfTemplet();
        when(signTempletManagerService.getPdfTemplet(anyString(), anyString())).thenReturn(deliveryPdfTemplet);
        when(wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(anyInt())).thenReturn(true);

        // act
        wmEcontractDataPdfMappingCollector.processDelivery(originContext, signDataFactor, pdfEnumList);

        // assert
        assertFalse(pdfEnumList.isEmpty());
    }
}