package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf.AbstractWmEcontractPhfSubApplyAdapterService;
import com.sankuai.meituan.waimai.customer.service.sign.compare.phf.WmEcontractPhfCompareService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchPdfUrlContentBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PdfUrlContentBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfDeliveryApplyServiceTest {

    @InjectMocks
    private WmEcontractPhfDeliveryApplyService wmEcontractPhfDeliveryApplyService;

    @Mock
    private AbstractWmEcontractPhfSubApplyAdapterService abstractWmEcontractPhfSubApplyAdapterService;

    @Spy
    @InjectMocks
    private WmEcontractPhfDeliveryApplyService service;

    private Map<EcontractTaskApplySubTypeEnum, AbstractWmEcontractPhfSubApplyAdapterService> adapterServiceMap;

    @Before
    public void setUp() throws Exception {
        adapterServiceMap = new HashMap<>();
        adapterServiceMap.put(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL, abstractWmEcontractPhfSubApplyAdapterService);
        // Use reflection to set the private field
        Field field = WmEcontractPhfDeliveryApplyService.class.getDeclaredField("adapterServiceMap");
        field.setAccessible(true);
        field.set(service, adapterServiceMap);
        // Mock the getPdfSubTypeEnumName method to return proper enum names
        doReturn("phf_virtual").when(service).getPdfSubTypeEnumName("phf_virtual_123");
    }

    private void setAdapterServiceList(List<AbstractWmEcontractPhfSubApplyAdapterService> adapterServiceList) throws NoSuchFieldException, IllegalAccessException {
        Field field = WmEcontractPhfDeliveryApplyService.class.getDeclaredField("adapterServiceList");
        field.setAccessible(true);
        field.set(wmEcontractPhfDeliveryApplyService, adapterServiceList);
    }

    /**
     * 测试init方法，正常情况
     */
    @Test
    public void testInitNormal() throws Throwable {
        // arrange
        List<AbstractWmEcontractPhfSubApplyAdapterService> adapterServiceList = new ArrayList<>();
        adapterServiceList.add(abstractWmEcontractPhfSubApplyAdapterService);
        setAdapterServiceList(adapterServiceList);
        // act
        wmEcontractPhfDeliveryApplyService.init();
        // assert
        verify(abstractWmEcontractPhfSubApplyAdapterService, times(1)).getSubTypeEnum();
    }

    /**
     * 测试init方法，异常情况，adapterServiceList为空
     */
    @Test(expected = NullPointerException.class)
    public void testInitException() throws Throwable {
        // arrange
        setAdapterServiceList(null);
        // act
        wmEcontractPhfDeliveryApplyService.init();
        // assert
        verify(abstractWmEcontractPhfSubApplyAdapterService, never()).getSubTypeEnum();
    }

    /**
     * Tests when downloadUrl is blank should return null
     */
    @Test
    public void testWrapPdfUrlContentBoBlankUrl() throws Throwable {
        // arrange
        EcontractBatchContextBo context = new EcontractBatchContextBo.Builder().downLoadUrl("").build();
        // act
        BatchPdfUrlContentBo result = service.wrapPdfUrlContentBo(context);
        // assert
        assertNull(result);
    }

    /**
     * Tests when downloadUrl is not JSON should return single PDF content
     */
    @Test
    public void testWrapPdfUrlContentBoNonJsonUrl() throws Throwable {
        // arrange
        String downloadUrl = "http://example.com/single.pdf";
        List<Long> poiIds = Arrays.asList(123L, 456L);
        EcontractBatchContextBo context = new EcontractBatchContextBo.Builder().downLoadUrl(downloadUrl).wmPoiIdList(poiIds).build();
        // act
        BatchPdfUrlContentBo result = service.wrapPdfUrlContentBo(context);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getTechPdfInfos().size());
        assertEquals(downloadUrl, result.getTechPdfInfos().get(0).getPdfUrl());
        assertEquals(poiIds, result.getTechPdfInfos().get(0).getWmPoiIds());
    }

    /**
     * Tests when downloadUrl is JSON with valid subType should return content from adapter
     */
    @Test
    public void testWrapPdfUrlContentBoValidJsonWithValidSubType() throws Throwable {
        // arrange
        String json = "{\"phf_virtual_123\":\"http://example.com/phf.pdf\"}";
        EcontractBatchContextBo context = new EcontractBatchContextBo.Builder().downLoadUrl(json).build();
        BatchPdfUrlContentBo expected = new BatchPdfUrlContentBo();
        when(abstractWmEcontractPhfSubApplyAdapterService.getPdfUrlContentBo(any(), any())).thenReturn(expected);
        // act
        BatchPdfUrlContentBo result = service.wrapPdfUrlContentBo(context);
        // assert
        assertSame(expected, result);
    }

    /**
     * Tests when downloadUrl is JSON with invalid subType should throw exception
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapPdfUrlContentBoValidJsonWithInvalidSubType() throws Throwable {
        // arrange
        String json = "{\"invalid_type_123\":\"http://example.com/invalid.pdf\"}";
        EcontractBatchContextBo context = new EcontractBatchContextBo.Builder().downLoadUrl(json).build();
        // Mock to return null for invalid type
        doReturn(null).when(service).getPdfSubTypeEnumName("invalid_type_123");
        // act
        service.wrapPdfUrlContentBo(context);
    }

    /**
     * Tests when downloadUrl is JSON with multiple keys should use first key
     */
    @Test
    public void testWrapPdfUrlContentBoValidJsonWithMultipleKeys() throws Throwable {
        // arrange
        String json = "{\"phf_virtual_123\":\"http://example.com/phf.pdf\",\"phf_formal_456\":\"http://example.com/other.pdf\"}";
        EcontractBatchContextBo context = new EcontractBatchContextBo.Builder().downLoadUrl(json).build();
        BatchPdfUrlContentBo expected = new BatchPdfUrlContentBo();
        when(abstractWmEcontractPhfSubApplyAdapterService.getPdfUrlContentBo(any(), any())).thenReturn(expected);
        // act
        BatchPdfUrlContentBo result = service.wrapPdfUrlContentBo(context);
        // assert
        assertSame(expected, result);
    }

    /**
     * Tests when JSON parsing fails should return false from isJSON check
     */
    @Test
    public void testWrapPdfUrlContentBoInvalidJson() throws Throwable {
        // arrange
        String invalidJson = "{invalid:json}";
        EcontractBatchContextBo context = new EcontractBatchContextBo.Builder().downLoadUrl(invalidJson).build();
        // act
        BatchPdfUrlContentBo result = service.wrapPdfUrlContentBo(context);
        // assert - should treat as non-JSON and return single PDF
        assertNotNull(result);
        assertEquals(1, result.getTechPdfInfos().size());
        assertEquals(invalidJson, result.getTechPdfInfos().get(0).getPdfUrl());
    }
}