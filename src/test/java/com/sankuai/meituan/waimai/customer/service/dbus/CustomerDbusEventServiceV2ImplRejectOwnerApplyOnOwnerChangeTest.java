package com.sankuai.meituan.waimai.customer.service.dbus;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.WmCustomerOwnerApplyService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

/**
 * Test class for CustomerDbusEventServiceV2Impl.rejectOwnerApplyOnOwnerChange method
 */
@RunWith(MockitoJUnitRunner.class)
public class CustomerDbusEventServiceV2ImplRejectOwnerApplyOnOwnerChangeTest {

    @InjectMocks
    private CustomerDbusEventServiceV2Impl customerDbusEventService;

    @Mock
    private WmCustomerOwnerApplyService wmCustomerOwnerApplyService;

    private MockedStatic<ConfigUtilAdapter> mockedConfigUtil;

    private MockedStatic<MccCustomerConfig> mockedMccCustomerConfig;

    @Before
    public void setUp() {
        mockedConfigUtil = mockStatic(ConfigUtilAdapter.class);
        mockedMccCustomerConfig = mockStatic(MccCustomerConfig.class);
    }

    @After
    public void tearDown() {
        mockedConfigUtil.close();
        mockedMccCustomerConfig.close();
    }

    private void invokePrivateMethod(Object target, String methodName, Object... args) throws Exception {
        Method method = CustomerDbusEventServiceV2Impl.class.getDeclaredMethod(methodName, Map.class, WmCustomerDB.class);
        method.setAccessible(true);
        method.invoke(target, args);
    }

    /**
     * Test case: When sync switch is off, method should return early without processing
     */
    @Test
    public void testRejectOwnerApplyOnOwnerChange_WhenSwitchOff() throws Throwable {
        // arrange
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("owner_uid", 123);
        WmCustomerDB db = new WmCustomerDB();
        db.setId(1);
        when(MccCustomerConfig.getSyncCustomerChangeInfo2CustomerOwnerApplySwitch()).thenReturn(false);
        // act
        invokePrivateMethod(customerDbusEventService, "rejectOwnerApplyOnOwnerChange", diffMap, db);
        // assert
        verify(wmCustomerOwnerApplyService, never()).rejectOwnerApplyWhenOwnerChange(any());
    }

    /**
     * Test case: When customer ID is null, method should return early
     */
    @Test
    public void testRejectOwnerApplyOnOwnerChange_WhenCustomerIdNull() throws Throwable {
        // arrange
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("owner_uid", 123);
        WmCustomerDB db = new WmCustomerDB();
        db.setId(null);
        when(MccCustomerConfig.getSyncCustomerChangeInfo2CustomerOwnerApplySwitch()).thenReturn(true);
        // act
        invokePrivateMethod(customerDbusEventService, "rejectOwnerApplyOnOwnerChange", diffMap, db);
        // assert
        verify(wmCustomerOwnerApplyService, never()).rejectOwnerApplyWhenOwnerChange(any());
    }

    /**
     * Test case: When customer ID is <= 0, method should return early
     */
    @Test
    public void testRejectOwnerApplyOnOwnerChange_WhenCustomerIdInvalid() throws Throwable {
        // arrange
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("owner_uid", 123);
        WmCustomerDB db = new WmCustomerDB();
        db.setId(0);
        when(MccCustomerConfig.getSyncCustomerChangeInfo2CustomerOwnerApplySwitch()).thenReturn(true);
        // act
        invokePrivateMethod(customerDbusEventService, "rejectOwnerApplyOnOwnerChange", diffMap, db);
        // assert
        verify(wmCustomerOwnerApplyService, never()).rejectOwnerApplyWhenOwnerChange(any());
    }

    /**
     * Test case: When diffMap doesn't contain owner_uid, method should return early
     */
    @Test
    public void testRejectOwnerApplyOnOwnerChange_WhenNoOwnerUidInDiffMap() throws Throwable {
        // arrange
        Map<String, Object> diffMap = new HashMap<>();
        WmCustomerDB db = new WmCustomerDB();
        db.setId(1);
        when(MccCustomerConfig.getSyncCustomerChangeInfo2CustomerOwnerApplySwitch()).thenReturn(true);
        // act
        invokePrivateMethod(customerDbusEventService, "rejectOwnerApplyOnOwnerChange", diffMap, db);
        // assert
        verify(wmCustomerOwnerApplyService, never()).rejectOwnerApplyWhenOwnerChange(any());
    }

    /**
     * Test case: Happy path - successful rejection
     */
    @Test
    public void testRejectOwnerApplyOnOwnerChange_Success() throws Throwable {
        // arrange
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("owner_uid", 123);
        WmCustomerDB db = new WmCustomerDB();
        db.setId(1);
        when(MccCustomerConfig.getSyncCustomerChangeInfo2CustomerOwnerApplySwitch()).thenReturn(true);
        // act
        invokePrivateMethod(customerDbusEventService, "rejectOwnerApplyOnOwnerChange", diffMap, db);
        // assert
        verify(wmCustomerOwnerApplyService, times(1)).rejectOwnerApplyWhenOwnerChange(1);
    }

    /**
     * Test case: When service throws exception, it should be caught and logged
     */
    @Test
    public void testRejectOwnerApplyOnOwnerChange_WhenExceptionThrown() throws Throwable {
        // arrange
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("owner_uid", 123);
        WmCustomerDB db = new WmCustomerDB();
        db.setId(1);
        when(MccCustomerConfig.getSyncCustomerChangeInfo2CustomerOwnerApplySwitch()).thenReturn(true);
        doThrow(new WmCustomerException(500, "Test exception")).when(wmCustomerOwnerApplyService).rejectOwnerApplyWhenOwnerChange(1);
        // act
        invokePrivateMethod(customerDbusEventService, "rejectOwnerApplyOnOwnerChange", diffMap, db);
        // assert
        verify(wmCustomerOwnerApplyService, times(1)).rejectOwnerApplyWhenOwnerChange(1);
    }
}
