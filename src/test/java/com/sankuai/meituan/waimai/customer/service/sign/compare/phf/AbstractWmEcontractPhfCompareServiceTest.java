package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 * @description:
 * @author: zhangyuanhao02
 * @create: 2025/5/26 09:42
 */
public class AbstractWmEcontractPhfCompareServiceTest {
    @InjectMocks
    private final AbstractWmEcontractPhfCompareService abstractWmEcontractPhfCompareService = new AbstractWmEcontractPhfCompareService() {

        @Override
        void compare(EcontractBatchBo currentData, EcontractBatchContextBo batchContextBo) {
        }

        @Override
        public String getPdfContentInfoBoMapKeyByWmPoiId(Long wmPoiId) {
            return null;
        }
    };


    public AbstractWmEcontractPhfCompareServiceTest() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 contractIds 为 null 的情况
     */
    @Test
    public void testGenerateKeyWithNullContractIds() {
        // arrange
        List<String> contractIds = null;
        int type = 1;

        // act
        String result = abstractWmEcontractPhfCompareService.generateKey(contractIds, type);

        // assert
        assertNull(result);
    }

    /**
     * 测试 contractIds 为空列表的情况
     */
    @Test
    public void testGenerateKeyWithEmptyContractIds() {
        // arrange
        List<String> contractIds = Collections.emptyList();
        int type = 1;

        // act
        String result = abstractWmEcontractPhfCompareService.generateKey(contractIds, type);

        // assert
        assertNull(result);
    }

    /**
     * 测试 contractIds 只有一个元素的情况
     */
    @Test
    public void testGenerateKeyWithSingleContractId() {
        // arrange
        List<String> contractIds = Collections.singletonList("contract1");
        int type = 1;

        // act
        String result = abstractWmEcontractPhfCompareService.generateKey(contractIds, type);

        // assert
        assertEquals("1_contract1", result);
    }

    /**
     * 测试 contractIds 有多个元素的情况
     */
    @Test
    public void testGenerateKeyWithMultipleContractIds() {
        // arrange
        List<String> contractIds = Arrays.asList("contract1", "contract2", "contract3");
        int type = 1;

        // act
        String result = abstractWmEcontractPhfCompareService.generateKey(contractIds, type);

        // assert
        assertEquals("1_contract1,contract3", result);
    }

    /**
     * 测试 type 为负数的情况
     */
    @Test
    public void testGenerateKeyWithNegativeType() {
        // arrange
        List<String> contractIds = Arrays.asList("contract1", "contract2");
        int type = -1;

        // act
        String result = abstractWmEcontractPhfCompareService.generateKey(contractIds, type);

        // assert
        assertEquals("-1_contract1,contract2", result);
    }

    /**
     * 测试 type 为零的情况
     */
    @Test
    public void testGenerateKeyWithZeroType() {
        // arrange
        List<String> contractIds = Arrays.asList("contract1", "contract2");
        int type = 0;

        // act
        String result = abstractWmEcontractPhfCompareService.generateKey(contractIds, type);

        // assert
        assertEquals("0_contract1,contract2", result);
    }

    /**
     * 测试 type 为正数的情况
     */
    @Test
    public void testGenerateKeyWithPositiveType() {
        // arrange
        List<String> contractIds = Arrays.asList("contract1", "contract2");
        int type = 2;

        // act
        String result = abstractWmEcontractPhfCompareService.generateKey(contractIds, type);

        // assert
        assertEquals("2_contract1,contract2", result);
    }
}
