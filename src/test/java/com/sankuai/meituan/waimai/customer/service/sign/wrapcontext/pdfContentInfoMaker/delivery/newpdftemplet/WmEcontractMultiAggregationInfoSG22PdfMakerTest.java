package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * WmEcontractMultiAggregationInfoSG22PdfMakerTest
 *
 * @Author: wangyongfang
 * @Date: 2024-10-22
 */
public class WmEcontractMultiAggregationInfoSG22PdfMakerTest extends BaseStaticMockTest {

    @InjectMocks
    private WmEcontractMultiAggregationInfoSG22PdfMaker wmEcontractMultiAggregationInfoSG22PdfMaker;

    @Mock
    private WmEcontractContextUtil wmEcontractContextUtil;

    private MockedStatic<MapUtil> mapUtil;

    private MockedStatic<WmEcontractContextUtil> wmEcontractContextUtilMockedStatic;

    @Before
    public void setUp() {
        super.init();
        mapUtil = mockStatic(MapUtil.class);
        wmEcontractContextUtilMockedStatic = mockStatic(WmEcontractContextUtil.class);
    }

    @After
    public void after() {
        super.after();
        if (mapUtil != null) {
            mapUtil.close();
        }
        if (null  != wmEcontractContextUtilMockedStatic) {
            wmEcontractContextUtilMockedStatic.close();
        }
    }

    /**
     * 测试 makePdfContentInfoBo 方法，当 middleContext 中的 pdfDataMap 为空时
     */
    @Test(expected = NullPointerException.class)
    public void testMakePdfContentInfoBoWithEmptyPdfDataMap() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        middleContext.setPdfDataMap(new HashMap<>());
        middleContext.setSignDataFactor(signDataFactor);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext("{}");

        // 模拟WmEcontractContextUtil.selectDeliveryEcontractTaskBo方法
        when(WmEcontractContextUtil.selectDeliveryEcontractTaskBo(any(EcontractBatchContextBo.class), any(EcontractSignDataFactor.class))).thenReturn(taskBo);

        // 模拟WmEcontractContextUtil.getCustomerName方法
        when(WmEcontractContextUtil.getCustomerName(any(EcontractBatchContextBo.class))).thenReturn("Test Customer");

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        // act
        wmEcontractMultiAggregationInfoSG22PdfMaker.makePdfContentInfoBo(originContext, middleContext);

        // assert
        // Expected NullPointerException
        Assert.fail();
    }

    /**
     * 测试 makePdfContentInfoBo 方法，正常情况
     */
    @Test
    public void testMakePdfContentInfoBoNormal() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_2.getName(), Collections.singletonList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);

        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        middleContext.setSignDataFactor(signDataFactor);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setDeliveryTypeUUID("uuid1");
        taskBo.setApplyContext(JSONObject.toJSONString(deliveryInfoBo));

        // 模拟WmEcontractContextUtil.selectDeliveryEcontractTaskBo方法
        when(WmEcontractContextUtil.selectDeliveryEcontractTaskBo(any(EcontractBatchContextBo.class), any(EcontractSignDataFactor.class))).thenReturn(taskBo);

        // 模拟WmEcontractContextUtil.getCustomerName方法
        when(WmEcontractContextUtil.getCustomerName(any(EcontractBatchContextBo.class))).thenReturn("Test Customer");

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        // 模拟ConfigUtilAdapter.getInt方法
        when(ConfigUtilAdapter.getInt("DELIVERY_MULTI_AGGREGATION_INFO_SG22_TEMPLATE_ID", 0)).thenReturn(2210);
        when(ConfigUtilAdapter.getInt("DELIVERY_MULTI_AGGREGATION_INFO_SG22_TEMPLATE_VERSION", 0)).thenReturn(0);
        // 模拟MapUtil.Object2Map方法
        Map<String, String> mockResult = Collections.singletonMap("key", "value");
        doReturn(mockResult).when(mapUtil).when(() -> MapUtil.Object2Map(any()));

        // act
        PdfContentInfoBo result = wmEcontractMultiAggregationInfoSG22PdfMaker.makePdfContentInfoBo(originContext, middleContext);

        // assert
        assertNotNull(result);
        assertEquals(2210, result.getPdfTemplateId().intValue());
    }

    /**
     * 测试 makePdfContentInfoBo 方法，当 originContext 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testMakePdfContentInfoBoWithNullOriginContext() throws Throwable {
        // arrange
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        middleContext.setSignDataFactor(signDataFactor);

        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_2.getName(), Collections.singletonList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);

        // act
        wmEcontractMultiAggregationInfoSG22PdfMaker.makePdfContentInfoBo(null, middleContext);

        // assert
        // Expected NullPointerException
        Assert.fail();
    }

    /**
     * 测试 makePdfContentInfoBo 方法，当 middleContext 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testMakePdfContentInfoBoWithNullMiddleContext() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        // act
        wmEcontractMultiAggregationInfoSG22PdfMaker.makePdfContentInfoBo(originContext, null);

        // assert
        // Expected NullPointerException
        Assert.fail();
    }

    /**
     * 测试 makePdfContentInfoBo 方法，当 pdfDataMap 中的 key 对应的 List 为空时
     */
    @Test
    public void testMakePdfContentInfoBoWithEmptyUuidList() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_2.getName(), Collections.emptyList());
        middleContext.setPdfDataMap(pdfDataMap);

        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        middleContext.setSignDataFactor(signDataFactor);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext("{}");

        // 模拟WmEcontractContextUtil.selectDeliveryEcontractTaskBo方法
        when(WmEcontractContextUtil.selectDeliveryEcontractTaskBo(any(EcontractBatchContextBo.class), any(EcontractSignDataFactor.class))).thenReturn(taskBo);

        // 模拟WmEcontractContextUtil.getCustomerName方法
        when(WmEcontractContextUtil.getCustomerName(any(EcontractBatchContextBo.class))).thenReturn("Test Customer");

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        // 模拟ConfigUtilAdapter.getInt方法
        configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("DELIVERY_MULTI_AGGREGATION_INFO_SG22_TEMPLATE_ID", 0)).thenReturn(2210);
        configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt("DELIVERY_MULTI_AGGREGATION_INFO_SG22_TEMPLATE_VERSION", 0)).thenReturn(0);
        // 模拟MapUtil.Object2Map方法
        Map<String, String> mockResult = Collections.singletonMap("key", "value");
        doReturn(mockResult).when(mapUtil).when(() -> MapUtil.Object2Map(any()));

        // act
        PdfContentInfoBo result = wmEcontractMultiAggregationInfoSG22PdfMaker.makePdfContentInfoBo(originContext, middleContext);

        // assert
        Assert.assertEquals(0, result.getPdfBizContent().size());
    }

    /**
     * 测试 makePdfContentInfoBo 方法，当 pdfDataMap 中的 key 不存在时
     */
    @Test(expected = NullPointerException.class)
    public void testMakePdfContentInfoBoWithNonExistentKey() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put("nonExistentKey", Collections.singletonList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);

        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        middleContext.setSignDataFactor(signDataFactor);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext("{}");

        // 模拟WmEcontractContextUtil.selectDeliveryEcontractTaskBo方法
        when(WmEcontractContextUtil.selectDeliveryEcontractTaskBo(any(EcontractBatchContextBo.class), any(EcontractSignDataFactor.class))).thenReturn(taskBo);

        // 模拟WmEcontractContextUtil.getCustomerName方法
        when(WmEcontractContextUtil.getCustomerName(any(EcontractBatchContextBo.class))).thenReturn("Test Customer");

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        // act
        wmEcontractMultiAggregationInfoSG22PdfMaker.makePdfContentInfoBo(originContext, middleContext);

        // assert
        // Expected NullPointerException
        Assert.fail();
    }

    /**
     * 测试 makePdfContentInfoBo 方法，当 originContext 中没有找到对应的任务信息时
     */
    @Test(expected = NullPointerException.class)
    public void testMakePdfContentInfoBoWithNoTaskFound() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_2.getName(), Collections.singletonList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);

        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        middleContext.setSignDataFactor(signDataFactor);

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        // 模拟WmEcontractContextUtil.selectDeliveryEcontractTaskBo方法返回null
        when(WmEcontractContextUtil.selectDeliveryEcontractTaskBo(any(EcontractBatchContextBo.class), any(EcontractSignDataFactor.class))).thenReturn(null);

        // act
        wmEcontractMultiAggregationInfoSG22PdfMaker.makePdfContentInfoBo(originContext, middleContext);

        // assert
        // Expected NullPointerException
        Assert.fail();
    }
}