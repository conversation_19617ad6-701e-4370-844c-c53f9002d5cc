package com.sankuai.meituan.waimai.customer.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.WmCustomerOwnerApplyBusService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.OwnerApplyRecordListDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.OwnerApplyRecordQueryBO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerFrameThriftServiceImplGetCustomerOwnerApplyRecordPageTest {

    // Assuming 0 is the success code
    private static final int RESULT_CODE_SUCCESS = 0;

    // Assuming 1 is the error code
    private static final int RESULT_CODE_ERROR = 1;

    // Define the constant in the test class
    @InjectMocks
    private WmCustomerFrameThriftServiceImpl wmCustomerFrameThriftService;

    @Mock
    private WmCustomerOwnerApplyBusService wmCustomerOwnerApplyBusService;

    private OwnerApplyRecordQueryBO queryBO;

    private OwnerApplyRecordListDTO expectedDTO;

    @Before
    public void setUp() {
        queryBO = new OwnerApplyRecordQueryBO();
        expectedDTO = new OwnerApplyRecordListDTO();
    }

    /**
     * Test case: Input parameter is null
     * Expected: Returns error response with parameter error message
     */
    @Test
    public void testGetCustomerOwnerApplyRecordPage_NullInput() throws Throwable {
        // arrange
        OwnerApplyRecordQueryBO nullQueryBO = null;
        // act
        BaseResponse<OwnerApplyRecordListDTO> response = wmCustomerFrameThriftService.getCustomerOwnerApplyRecordPage(nullQueryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_ERROR, response.getCode());
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * Test case: Normal successful execution
     * Expected: Returns success response with data
     */
    @Test
    public void testGetCustomerOwnerApplyRecordPage_Success() throws Throwable {
        // arrange
        when(wmCustomerOwnerApplyBusService.getCustomerOwnerApplyRecordPage(queryBO)).thenReturn(expectedDTO);
        // act
        BaseResponse<OwnerApplyRecordListDTO> response = wmCustomerFrameThriftService.getCustomerOwnerApplyRecordPage(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_SUCCESS, response.getCode());
        assertEquals(expectedDTO, response.getData());
        verify(wmCustomerOwnerApplyBusService).getCustomerOwnerApplyRecordPage(queryBO);
    }

    /**
     * Test case: WmCustomerException thrown from service
     * Expected: Returns error response with exception message
     */
    @Test
    public void testGetCustomerOwnerApplyRecordPage_WmCustomerException() throws Throwable {
        // arrange
        String errorMsg = "Business validation failed";
        when(wmCustomerOwnerApplyBusService.getCustomerOwnerApplyRecordPage(queryBO)).thenThrow(new WmCustomerException(RESULT_CODE_ERROR, errorMsg));
        // act
        BaseResponse<OwnerApplyRecordListDTO> response = wmCustomerFrameThriftService.getCustomerOwnerApplyRecordPage(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_ERROR, response.getCode());
        assertEquals(errorMsg, response.getMsg());
        verify(wmCustomerOwnerApplyBusService).getCustomerOwnerApplyRecordPage(queryBO);
    }

    /**
     * Test case: Unexpected runtime exception from service
     * Expected: Returns error response with generic error message
     */
    @Test
    public void testGetCustomerOwnerApplyRecordPage_UnexpectedException() throws Throwable {
        // arrange
        when(wmCustomerOwnerApplyBusService.getCustomerOwnerApplyRecordPage(queryBO)).thenThrow(new RuntimeException("Unexpected error"));
        // act
        BaseResponse<OwnerApplyRecordListDTO> response = wmCustomerFrameThriftService.getCustomerOwnerApplyRecordPage(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_ERROR, response.getCode());
        assertEquals("系统异常,请稍后重试", response.getMsg());
        verify(wmCustomerOwnerApplyBusService).getCustomerOwnerApplyRecordPage(queryBO);
    }
}
