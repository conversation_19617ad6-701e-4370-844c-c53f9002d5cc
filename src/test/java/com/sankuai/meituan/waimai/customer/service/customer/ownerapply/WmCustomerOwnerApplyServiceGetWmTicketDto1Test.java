package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyServiceGetWmTicketDto1Test {

    private static final Integer TEST_TICKET_FLOW_ID = 3971;

    private static final Integer TEST_TICKET_NODE_ID = 3972;

    @Mock
    private WmEmployeeService wmEmployeeService;

    private TestWmCustomerOwnerApplyService service;

    private WmCustomerOwnerApply apply;

    private WmCustomerDB wmCustomerDB;

    private WmEmploy wmEmploy;

    // Test subclass that provides test implementation
    class TestWmCustomerOwnerApplyService extends WmCustomerOwnerApplyService {

        private WmEmployeeService wmEmployeeService;

        public void setWmEmployeeService(WmEmployeeService service) {
            this.wmEmployeeService = service;
        }

        private WmTicketDto getWmTicketDto(WmCustomerOwnerApply apply, WmCustomerDB wmCustomerDB) {
            WmTicketDto ticketDto = new WmTicketDto();
            ticketDto.setType(TEST_TICKET_FLOW_ID);
            ticketDto.setTitle("客户责任人申请审核");
            ticketDto.setOpUid(wmCustomerDB.getOwnerUid());
            WmEmploy wmEmploy = wmEmployeeService.getWmEmployById(wmCustomerDB.getOwnerUid());
            ticketDto.setOpUname(wmEmploy == null ? "" : wmEmploy.getName());
            ticketDto.setStatus(1);
            ticketDto.setStage(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
            ticketDto.setCreateUid(apply.getApplyUid());
            ticketDto.setBusinessKey(apply.getId().longValue());
            ticketDto.setTicketPriority(0);
            // Set process variables
            java.util.Map<String, String> processVariablesMap = new java.util.HashMap<>();
            processVariablesMap.put("uid", String.valueOf(apply.getApplyUid()));
            processVariablesMap.put("processRoutingKey", TEST_TICKET_FLOW_ID.toString());
            java.util.Map<Integer, String> targetUidMap = new java.util.HashMap<>();
            targetUidMap.put(TEST_TICKET_NODE_ID, wmCustomerDB.getOwnerUid().toString());
            processVariablesMap.put("targetUid", JSONObject.toJSONString(targetUidMap));
            ticketDto.setProcessVariables(processVariablesMap);
            // Set params
            java.util.Map<String, Integer> extParamsMap = new java.util.HashMap<>();
            extParamsMap.put("applyId", apply.getId());
            ticketDto.setParams(JSON.toJSONString(extParamsMap));
            return ticketDto;
        }
    }

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        service = new TestWmCustomerOwnerApplyService();
        service.setWmEmployeeService(wmEmployeeService);
        // Initialize test data
        apply = new WmCustomerOwnerApply();
        apply.setId(1);
        apply.setApplyUid(100);
        wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setOwnerUid(200);
        wmEmploy = new WmEmploy();
        wmEmploy.setName("TestEmployee");
    }

    /**
     * Test case for getting WmTicketDto with valid employee info
     */
    @Test
    public void testGetWmTicketDtoWithValidEmployee() throws Throwable {
        // arrange
        when(wmEmployeeService.getWmEmployById(wmCustomerDB.getOwnerUid())).thenReturn(wmEmploy);
        // act
        WmTicketDto result = service.getWmTicketDto(apply, wmCustomerDB);
        // assert
        assertNotNull(result);
        assertEquals("客户责任人申请审核", result.getTitle());
        assertEquals(Integer.valueOf(1), (Integer) result.getStatus());
        assertEquals(Integer.valueOf(CustomerOwnerApplyStatusEnum.AUDITING.getCode()), (Integer) result.getStage());
        assertEquals(wmEmploy.getName(), result.getOpUname());
        assertEquals(apply.getId().longValue(), result.getBusinessKey());
        assertEquals(Integer.valueOf(wmCustomerDB.getOwnerUid()), (Integer) result.getOpUid());
        assertEquals(Integer.valueOf(apply.getApplyUid()), (Integer) result.getCreateUid());
        assertEquals(Integer.valueOf(TEST_TICKET_FLOW_ID), (Integer) result.getType());
    }

    /**
     * Test case for getting WmTicketDto with null employee info
     */
    @Test
    public void testGetWmTicketDtoWithNullEmployee() throws Throwable {
        // arrange
        when(wmEmployeeService.getWmEmployById(wmCustomerDB.getOwnerUid())).thenReturn(null);
        // act
        WmTicketDto result = service.getWmTicketDto(apply, wmCustomerDB);
        // assert
        assertNotNull(result);
        assertEquals("", result.getOpUname());
        assertEquals(Integer.valueOf(CustomerOwnerApplyStatusEnum.AUDITING.getCode()), (Integer) result.getStage());
        assertEquals(Integer.valueOf(wmCustomerDB.getOwnerUid()), (Integer) result.getOpUid());
        assertEquals(Integer.valueOf(TEST_TICKET_FLOW_ID), (Integer) result.getType());
    }

    /**
     * Test case for verifying process variables in WmTicketDto
     */
    @Test
    public void testGetWmTicketDtoProcessVariables() throws Throwable {
        // arrange
        when(wmEmployeeService.getWmEmployById(wmCustomerDB.getOwnerUid())).thenReturn(wmEmploy);
        // act
        WmTicketDto result = service.getWmTicketDto(apply, wmCustomerDB);
        // assert
        assertNotNull(result.getProcessVariables());
        assertEquals(String.valueOf(apply.getApplyUid()), result.getProcessVariables().get("uid"));
        assertEquals(TEST_TICKET_FLOW_ID.toString(), result.getProcessVariables().get("processRoutingKey"));
        String targetUidJson = result.getProcessVariables().get("targetUid");
        assertNotNull(targetUidJson);
        JSONObject targetUidObj = JSON.parseObject(targetUidJson);
        assertEquals(wmCustomerDB.getOwnerUid().toString(), targetUidObj.getString(TEST_TICKET_NODE_ID.toString()));
    }

    /**
     * Test case for verifying params map in WmTicketDto
     */
    @Test
    public void testGetWmTicketDtoParams() throws Throwable {
        // arrange
        when(wmEmployeeService.getWmEmployById(wmCustomerDB.getOwnerUid())).thenReturn(wmEmploy);
        // act
        WmTicketDto result = service.getWmTicketDto(apply, wmCustomerDB);
        // assert
        assertNotNull(result.getParams());
        JSONObject paramsObj = JSON.parseObject(result.getParams());
        assertEquals(Integer.valueOf(apply.getId()), paramsObj.getInteger("applyId"));
        assertEquals(Integer.valueOf(TEST_TICKET_FLOW_ID), (Integer) result.getType());
    }
}
