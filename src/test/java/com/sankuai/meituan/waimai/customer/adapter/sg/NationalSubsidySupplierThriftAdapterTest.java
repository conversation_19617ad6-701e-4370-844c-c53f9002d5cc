package com.sankuai.meituan.waimai.customer.adapter.sg;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.shangou.partner.sdk.base.ErrEm;
import com.sankuai.shangou.partner.sdk.dto.NationalSubsidySelfOpEntityDTO;
import com.sankuai.shangou.partner.sdk.response.NationalSubsidySelfOpEntityResp;
import com.sankuai.shangou.partner.sdk.thrift.NationalSubsidySupplierThrift;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidySupplierThriftAdapterTest {

    @Mock
    private NationalSubsidySupplierThrift nationalSubsidySupplierThrift;

    @InjectMocks
    private NationalSubsidySupplierThriftAdapter adapter;

    /**
     * Test successful query with non-empty entity list
     */
    @Test
    public void testQueryNationalSubsidySubjectSuccess() throws Throwable {
        // arrange
        Long customerId = 123456L;
        NationalSubsidySelfOpEntityResp resp = new NationalSubsidySelfOpEntityResp();
        resp.setCode(ErrEm.SUCCESS_CODE.getCode());
        resp.setMessage("Success");
        List<NationalSubsidySelfOpEntityDTO> entityList = new ArrayList<>();
        NationalSubsidySelfOpEntityDTO entity = new NationalSubsidySelfOpEntityDTO();
        entity.setEntityName("Test Entity");
        entity.setEntityCustomerId(789);
        entityList.add(entity);
        resp.setRelatedSelfOpEntityList(entityList);
        when(nationalSubsidySupplierThrift.queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId)).thenReturn(resp);
        // act
        List<NationalSubsidySelfOpEntityDTO> result = adapter.queryNationalSubsidySubject(customerId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test Entity", result.get(0).getEntityName());
        assertEquals(Integer.valueOf(789), result.get(0).getEntityCustomerId());
        verify(nationalSubsidySupplierThrift, times(1)).queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId);
    }

    /**
     * Test successful query with empty entity list
     */
    @Test
    public void testQueryNationalSubsidySubjectEmptyList() throws Throwable {
        // arrange
        Long customerId = 123456L;
        NationalSubsidySelfOpEntityResp resp = new NationalSubsidySelfOpEntityResp();
        resp.setCode(ErrEm.SUCCESS_CODE.getCode());
        resp.setMessage("Success");
        resp.setRelatedSelfOpEntityList(Collections.emptyList());
        when(nationalSubsidySupplierThrift.queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId)).thenReturn(resp);
        // act
        List<NationalSubsidySelfOpEntityDTO> result = adapter.queryNationalSubsidySubject(customerId);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(nationalSubsidySupplierThrift, times(1)).queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId);
    }

    /**
     * Test query with error response code
     */
    @Test
    public void testQueryNationalSubsidySubjectErrorResponse() throws Throwable {
        // arrange
        Long customerId = 123456L;
        NationalSubsidySelfOpEntityResp resp = new NationalSubsidySelfOpEntityResp();
        resp.setCode(ErrEm.SERVICE_ERROR.getCode());
        resp.setMessage("Service Error");
        when(nationalSubsidySupplierThrift.queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId)).thenReturn(resp);
        // act & assert
        try {
            adapter.queryNationalSubsidySubject(customerId);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            // Since we don't have access to getErrorCode(), we can only verify the message
            assertEquals("Service Error", e.getMessage());
        }
        verify(nationalSubsidySupplierThrift, times(1)).queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId);
    }

    /**
     * Test query with null entity list in response
     */
    @Test
    public void testQueryNationalSubsidySubjectNullEntityList() throws Throwable {
        // arrange
        Long customerId = 123456L;
        NationalSubsidySelfOpEntityResp resp = new NationalSubsidySelfOpEntityResp();
        resp.setCode(ErrEm.SUCCESS_CODE.getCode());
        resp.setMessage("Success");
        resp.setRelatedSelfOpEntityList(null);
        when(nationalSubsidySupplierThrift.queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId)).thenReturn(resp);
        // act
        List<NationalSubsidySelfOpEntityDTO> result = adapter.queryNationalSubsidySubject(customerId);
        // assert
        assertNull(result);
        verify(nationalSubsidySupplierThrift, times(1)).queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId);
    }

    /**
     * Test query with null customerId
     */
    @Test
    public void testQueryNationalSubsidySubjectNullCustomerId() throws Throwable {
        // arrange
        Long customerId = null;
        NationalSubsidySelfOpEntityResp resp = new NationalSubsidySelfOpEntityResp();
        resp.setCode(ErrEm.SUCCESS_CODE.getCode());
        resp.setMessage("Success");
        List<NationalSubsidySelfOpEntityDTO> entityList = new ArrayList<>();
        resp.setRelatedSelfOpEntityList(entityList);
        when(nationalSubsidySupplierThrift.queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId)).thenReturn(resp);
        // act
        List<NationalSubsidySelfOpEntityDTO> result = adapter.queryNationalSubsidySubject(customerId);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(nationalSubsidySupplierThrift, times(1)).queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId);
    }

    /**
     * Test query when thrift service throws exception
     */
    @Test
    public void testQueryNationalSubsidySubjectThriftException() throws Throwable {
        // arrange
        Long customerId = 123456L;
        RuntimeException exception = new RuntimeException("Thrift service error");
        when(nationalSubsidySupplierThrift.queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId)).thenThrow(exception);
        // act & assert
        try {
            adapter.queryNationalSubsidySubject(customerId);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException e) {
            assertEquals("Thrift service error", e.getMessage());
        } catch (WmCustomerException e) {
            fail("Unexpected WmCustomerException thrown");
        }
        verify(nationalSubsidySupplierThrift, times(1)).queryNationalSubsidySelfOpEntityBySupplierCustomerId(customerId);
    }
}
