package com.sankuai.meituan.waimai.customer.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.WmCustomerOwnerApplyBusService;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyListDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyQueryBO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for WmCustomerFrameThriftServiceImpl#listCustomerOwnerApply
 */
@RunWith(MockitoJUnitRunner.class)
public class WmCustomerFrameThriftServiceImplListCustomerOwnerApplyTest {

    @InjectMocks
    private WmCustomerFrameThriftServiceImpl wmCustomerFrameThriftService;

    @Mock
    private WmCustomerOwnerApplyBusService wmCustomerOwnerApplyBusService;

    private CustomerOwnerApplyQueryBO queryBO;

    private CustomerOwnerApplyListDTO listDTO;

    private BaseResponse<CustomerOwnerApplyListDTO> expectedResponse;

    @Before
    public void setUp() {
        queryBO = new CustomerOwnerApplyQueryBO();
        listDTO = new CustomerOwnerApplyListDTO();
        expectedResponse = BaseResponse.success(listDTO);
    }

    /**
     * Test successful case for listCustomerOwnerApply
     */
    @Test
    public void testListCustomerOwnerApply_Success() throws Throwable {
        // arrange
        when(wmCustomerOwnerApplyBusService.listCustomerOwnerApply(queryBO)).thenReturn(expectedResponse);
        // act
        BaseResponse<CustomerOwnerApplyListDTO> actualResponse = wmCustomerFrameThriftService.listCustomerOwnerApply(queryBO);
        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(wmCustomerOwnerApplyBusService).listCustomerOwnerApply(queryBO);
    }

    /**
     * Test case when WmCustomerException is thrown
     */
    @Test(expected = WmCustomerException.class)
    public void testListCustomerOwnerApply_ThrowsException() throws Throwable {
        // arrange
        when(wmCustomerOwnerApplyBusService.listCustomerOwnerApply(any(CustomerOwnerApplyQueryBO.class))).thenAnswer(invocation -> {
            throw new WmCustomerException(0, "Test exception");
        });
        // act
        wmCustomerFrameThriftService.listCustomerOwnerApply(queryBO);
        // assert is handled by expected annotation
    }

    /**
     * Test case with null input parameter
     */
    @Test
    public void testListCustomerOwnerApply_NullInput() throws Throwable {
        // arrange
        when(wmCustomerOwnerApplyBusService.listCustomerOwnerApply(null)).thenReturn(expectedResponse);
        // act
        BaseResponse<CustomerOwnerApplyListDTO> actualResponse = wmCustomerFrameThriftService.listCustomerOwnerApply(null);
        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(wmCustomerOwnerApplyBusService).listCustomerOwnerApply(null);
    }

    /**
     * Test case verifying JSON serialization of input parameter
     */
    @Test
    public void testListCustomerOwnerApply_VerifyLogging() throws Throwable {
        // arrange
        queryBO.setUid(123);
        when(wmCustomerOwnerApplyBusService.listCustomerOwnerApply(queryBO)).thenReturn(expectedResponse);
        // act
        BaseResponse<CustomerOwnerApplyListDTO> actualResponse = wmCustomerFrameThriftService.listCustomerOwnerApply(queryBO);
        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(wmCustomerOwnerApplyBusService).listCustomerOwnerApply(argThat(arg -> arg != null && arg.getUid() == 123));
    }
}
