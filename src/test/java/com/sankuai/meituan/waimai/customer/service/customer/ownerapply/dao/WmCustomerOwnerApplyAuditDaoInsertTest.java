package com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerOwnerApplyAuditMapper;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyAuditDaoInsertTest {

    @InjectMocks
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    @Mock
    private WmCustomerOwnerApplyAuditMapper wmCustomerOwnerApplyAuditMapper;

    /**
     * Test insert method when apply is null, expecting a NullPointerException.
     */
    @Test(expected = NullPointerException.class)
    public void testInsertApplyIsNull() throws Throwable {
        wmCustomerOwnerApplyAuditDao.insert(null);
    }

    /**
     * Test insert method when apply is not null, but its id is null, expecting the applyAudit's applyId to be null.
     */
    @Test
    public void testInsertApplyIdIsNull() throws Throwable {
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        WmCustomerOwnerApplyAudit applyAudit = wmCustomerOwnerApplyAuditDao.insert(apply);
        assertNull(applyAudit.getApplyId());
    }

    /**
     * Test insert method when apply is not null, and its id is not null, expecting the applyAudit's applyId to be the apply's id.
     */
    @Test
    public void testInsertApplyIdIsNotNull() throws Throwable {
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(1);
        WmCustomerOwnerApplyAudit applyAudit = wmCustomerOwnerApplyAuditDao.insert(apply);
        assertEquals(apply.getId(), applyAudit.getApplyId());
    }
}
