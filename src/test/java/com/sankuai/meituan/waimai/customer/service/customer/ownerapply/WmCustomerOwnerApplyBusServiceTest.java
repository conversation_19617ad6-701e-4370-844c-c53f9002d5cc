package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.GInfoServiceAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.UDbServiceAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEnterpriseAgentElephantServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyListDbQuery;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyRecordQueryBo;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.UpmAuthCheckService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.check.WmCustomerAuthService;
import com.sankuai.meituan.waimai.customer.service.customer.dto.OwnerApplyStepUpdateDTO;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyDao;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStepStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerStepEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerListAuthInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyListDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyListQueryBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyQueryBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyStep;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.OwnerApplyCustomerResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.OwnerApplyQueryCustomerBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.OwnerApplyRecordQueryBO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * Test class for WmCustomerOwnerApplyBusService#updateStepInfo
 */
@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyBusServiceTest {

    @InjectMocks
    private WmCustomerOwnerApplyBusService wmCustomerOwnerApplyBusService;

    @Mock
    private OwnerApplyStepUpdateDTO mockUpdateDTO;

    @Mock
    private WmEmployeeService wmEmployeeService;

    @Mock
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    private WmCustomerOwnerApply wmCustomerOwnerApply;

    private List<CustomerOwnerApplyStep> applyStepList;

    private Method processCompleteStatusMethod;

    @Mock
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Mock
    private WmCustomerPoiService wmCustomerPoiService;

    private WmCustomerOwnerApplyBusService service = new WmCustomerOwnerApplyBusService();

    private Method convertMethod;

    @Mock
    private WmCustomerOwnerApplyDao wmCustomerOwnerApplyDao;

    private static final Integer CURRENT_TIME = (int) (System.currentTimeMillis() / 1000);

    private static final Integer ONE_HOUR = 3600;

    @Mock
    private UDbServiceAdaptor uDbServiceAdaptor;

    @Mock
    private WmEnterpriseAgentElephantServiceAdaptor wmEnterpriseAgentElephantServiceAdaptor;

    @Mock
    private GInfoServiceAdaptor gInfoServiceAdaptor;

    @Mock
    private UpmAuthCheckService upmAuthCheckService;

    private static final Integer USER_ID = 12345;

    private static final Integer ROLE_ID = 10148632;

    private static MockedStatic<MccCustomerConfig> mockedMccCustomerConfig;

    @Mock
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    private Method getAuditLinkUrlMethod;

    private static final String TICKET_URL_PREFIX = "https://wm.ocrm.meituan.com/page/ticket/taskDetail/taskDetail.html?id=";

    private static final String TICKET_URL_SUFFIX = "&pageSource=MY_RECEIVED_TICKET&enterPage=1";

    private static final int RESULT_CODE_SUCCESS = 0;

    @Mock
    private CustomerOwnerApplyCheckService customerOwnerApplyCheckService;

    @Mock
    private WmCustomerESService wmCustomerESService;

    @Mock
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Mock
    private WmCustomerAuthService wmCustomerAuthService;

    @Before
    public void setUp() throws Exception {
        wmCustomerOwnerApply = new WmCustomerOwnerApply();
        wmCustomerOwnerApply.setId(1);
        wmCustomerOwnerApply.setCustomerOwnerUid(100);
        wmCustomerOwnerApply.setUtime(Integer.valueOf((int) (System.currentTimeMillis() / 1000)));
        applyStepList = new ArrayList<>();
        CustomerOwnerApplyStep step = new CustomerOwnerApplyStep();
        applyStepList.add(step);
        applyStepList.add(new CustomerOwnerApplyStep());
        // Get access to the private method
        processCompleteStatusMethod = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("processCompleteStatus", WmCustomerOwnerApply.class, List.class, CustomerOwnerStepEnum.class);
        processCompleteStatusMethod.setAccessible(true);
    }

    @BeforeClass
    public static void beforeClass() {
        // Mock MccCustomerConfig before all tests
        mockedMccCustomerConfig = mockStatic(MccCustomerConfig.class);
        mockedMccCustomerConfig.when(MccCustomerConfig::getCustomerOwnerApplyRoleId).thenReturn(ROLE_ID);
    }

    @AfterClass
    public static void afterClass() {
        // Close the static mock to prevent memory leaks
        if (mockedMccCustomerConfig != null) {
            mockedMccCustomerConfig.close();
        }
    }

    @Before
    public void setup() throws Exception {
        getAuditLinkUrlMethod = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("getAuditLinkUrl", Integer.class);
        getAuditLinkUrlMethod.setAccessible(true);
    }

    private Method getPrivateMethod() throws Exception {
        Method method = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("convertOwnerApplyCustomerResult", List.class);
        method.setAccessible(true);
        return method;
    }

    private WmCustomerDB createBasicCustomer() {
        WmCustomerDB customer = new WmCustomerDB();
        customer.setId(1);
        customer.setCustomerName("Test Customer");
        customer.setCustomerNumber("C001");
        customer.setMtCustomerId(1001L);
        customer.setCustomerRealType(CustomerRealTypeEnum.DANDIAN.getValue());
        // Set default value to 0
        customer.setOwnerUid(0);
        return customer;
    }

    /**
     * Helper method to invoke private method using reflection
     */
    private WmCustomerOwnerApplyRecordQueryBo invokeConvertMethod(OwnerApplyRecordQueryBO queryBO) throws Exception {
        if (convertMethod == null) {
            convertMethod = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("convertWmCustomerOwnerApplyRecordQueryBo", OwnerApplyRecordQueryBO.class);
            convertMethod.setAccessible(true);
        }
        return (WmCustomerOwnerApplyRecordQueryBo) convertMethod.invoke(service, queryBO);
    }

    /**
     * Test normal case for updateStepInfo method
     * All fields are set with valid values
     */
    @Test
    public void testUpdateStepInfo_NormalCase() throws Throwable {
        // arrange
        CustomerOwnerApplyStep step = new CustomerOwnerApplyStep();
        CustomerOwnerApplyStepStatusEnum status = CustomerOwnerApplyStepStatusEnum.values()[0];
        when(mockUpdateDTO.getStepTime()).thenReturn(123456);
        when(mockUpdateDTO.getStatus()).thenReturn(status);
        when(mockUpdateDTO.getAuditTips()).thenReturn("audit tips");
        when(mockUpdateDTO.isOnStep()).thenReturn(true);
        when(mockUpdateDTO.getAuditRemark()).thenReturn("audit remark");
        when(mockUpdateDTO.getStepName()).thenReturn("step name");
        Method method = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("updateStepInfo", CustomerOwnerApplyStep.class, OwnerApplyStepUpdateDTO.class);
        method.setAccessible(true);
        // act
        method.invoke(wmCustomerOwnerApplyBusService, step, mockUpdateDTO);
        // assert
        assertEquals(Integer.valueOf(123456), step.getStepTime());
        assertEquals(Integer.valueOf(status.getCode()), step.getStatusCode());
        assertEquals("audit tips", step.getAuditTips());
        assertTrue(step.isOnStep());
        assertEquals("audit remark", step.getRemark());
        assertEquals("step name", step.getStepName());
    }

    /**
     * Test case for updateStepInfo method with empty strings
     * Verifies behavior when string fields contain empty values
     */
    @Test
    public void testUpdateStepInfo_WithEmptyStrings() throws Throwable {
        // arrange
        CustomerOwnerApplyStep step = new CustomerOwnerApplyStep();
        CustomerOwnerApplyStepStatusEnum status = CustomerOwnerApplyStepStatusEnum.values()[1];
        when(mockUpdateDTO.getStepTime()).thenReturn(0);
        when(mockUpdateDTO.getStatus()).thenReturn(status);
        when(mockUpdateDTO.getAuditTips()).thenReturn("");
        when(mockUpdateDTO.isOnStep()).thenReturn(false);
        when(mockUpdateDTO.getAuditRemark()).thenReturn("");
        when(mockUpdateDTO.getStepName()).thenReturn("");
        Method method = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("updateStepInfo", CustomerOwnerApplyStep.class, OwnerApplyStepUpdateDTO.class);
        method.setAccessible(true);
        // act
        method.invoke(wmCustomerOwnerApplyBusService, step, mockUpdateDTO);
        // assert
        assertEquals(Integer.valueOf(0), step.getStepTime());
        assertEquals(Integer.valueOf(status.getCode()), step.getStatusCode());
        assertEquals("", step.getAuditTips());
        assertFalse(step.isOnStep());
        assertEquals("", step.getRemark());
        assertEquals("", step.getStepName());
    }

    /**
     * Test case for non-CREATE_TO_AUDIT step
     */
    @Test
    public void testProcessCompleteStatus_NonCreateToAudit() throws Throwable {
        // arrange
        // Using first enum value
        CustomerOwnerStepEnum stepEnum = CustomerOwnerStepEnum.values()[0];
        // act
        processCompleteStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, stepEnum);
        // assert
        CustomerOwnerApplyStep lastStep = applyStepList.get(applyStepList.size() - 1);
        verify(wmEmployeeService, never()).getUserAndId(any());
        assert lastStep.getStatusCode().equals(CustomerOwnerApplyStepStatusEnum.COMPLETE.getCode());
        assert lastStep.isOnStep();
        assert lastStep.getStepTime().equals(wmCustomerOwnerApply.getUtime());
    }

    /**
     * Test case for CREATE_TO_AUDIT step
     */
    @Test
    public void testProcessCompleteStatus_CreateToAudit() throws Throwable {
        // arrange
        CustomerOwnerStepEnum stepEnum = CustomerOwnerStepEnum.CREATE_TO_AUDIT;
        when(wmEmployeeService.getUserAndId(anyInt())).thenReturn("testUser(123)");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(anyInt())).thenReturn(new WmCustomerOwnerApplyAudit() {

            {
                setAuditResult("test audit remark");
            }
        });
        // act
        processCompleteStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, stepEnum);
        // assert
        CustomerOwnerApplyStep secondStep = applyStepList.get(1);
        verify(wmEmployeeService).getUserAndId(wmCustomerOwnerApply.getCustomerOwnerUid());
        assert !secondStep.isOnStep();
        assert secondStep.getStatusCode().equals(CustomerOwnerApplyStepStatusEnum.PASS.getCode());
        assert secondStep.getAuditTips().contains("已通过");
        assert "test audit remark".equals(secondStep.getRemark());
        assert "审批通过".equals(secondStep.getStepName());
    }

    /**
     * Test case for CREATE_TO_AUDIT step when audit remark query fails
     */
    @Test(expected = com.sankuai.meituan.waimai.thrift.exception.WmCustomerException.class)
    public void testProcessCompleteStatus_CreateToAudit_AuditRemarkQueryFails() throws Throwable {
        // arrange
        CustomerOwnerStepEnum stepEnum = CustomerOwnerStepEnum.CREATE_TO_AUDIT;
        when(wmEmployeeService.getUserAndId(anyInt())).thenReturn("testUser(123)");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(anyInt())).thenReturn(null);
        // act
        try {
            processCompleteStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, stepEnum);
        } catch (Exception e) {
            if (e.getCause() instanceof com.sankuai.meituan.waimai.thrift.exception.WmCustomerException) {
                throw (com.sankuai.meituan.waimai.thrift.exception.WmCustomerException) e.getCause();
            }
            throw e;
        }
        // assert: expect WmCustomerException with message "未查询到客户申请单审批信息"
        fail("Should throw WmCustomerException");
    }

    /**
     * Test case for empty step list
     */
    @Test(expected = IndexOutOfBoundsException.class)
    public void testProcessCompleteStatus_EmptyStepList() throws Throwable {
        // arrange
        applyStepList = new ArrayList<>();
        // Using first enum value
        CustomerOwnerStepEnum stepEnum = CustomerOwnerStepEnum.values()[0];
        // act
        try {
            processCompleteStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, stepEnum);
        } catch (Exception e) {
            if (e.getCause() instanceof IndexOutOfBoundsException) {
                throw (IndexOutOfBoundsException) e.getCause();
            }
            throw e;
        }
        // assert: expect IndexOutOfBoundsException
        fail("Should throw IndexOutOfBoundsException");
    }

    /**
     * Test case for empty customer list
     */
    @Test
    public void testConvertOwnerApplyCustomerResult_EmptyList() throws Throwable {
        // arrange
        List<WmCustomerDB> emptyList = new ArrayList<>();
        // act
        List<OwnerApplyCustomerResult> results = (List<OwnerApplyCustomerResult>) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, emptyList);
        // assert
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    /**
     * Test case for single customer with valid owner
     */
    @Test
    public void testConvertOwnerApplyCustomerResult_SingleCustomerValidOwner() throws Throwable {
        // arrange
        WmCustomerDB customer = createBasicCustomer();
        customer.setOwnerUid(100);
        WmEmploy employee = new WmEmploy();
        employee.setMisId("mis001");
        employee.setName("Test Employee");
        when(wmEmployeeService.getWmEmployById(100)).thenReturn(employee);
        // act
        List<OwnerApplyCustomerResult> results = (List<OwnerApplyCustomerResult>) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, Lists.newArrayList(customer));
        // assert
        assertEquals(1, results.size());
        OwnerApplyCustomerResult result = results.get(0);
        assertEquals("Test Customer", result.getCustomerName());
        assertEquals(Integer.valueOf(1), result.getCustomerId());
        assertEquals("C001", result.getCustomerNumber());
        assertEquals(Long.valueOf(1001L), result.getMtCustomerId());
        assertEquals(Integer.valueOf(100), result.getCurrentOwnerUid());
        assertEquals("mis001", result.getCurrentOwnerMisId());
        assertEquals("Test Employee", result.getCurrentOwnerUName());
        assertEquals(CustomerRealTypeEnum.DANDIAN.getValue(), result.getCustomerRealType().intValue());
        assertEquals(CustomerRealTypeEnum.DANDIAN.getName(), result.getCustomerRealTypeDesc());
    }

    /**
     * Test case for single customer with zero owner uid
     */
    @Test
    public void testConvertOwnerApplyCustomerResult_SingleCustomerZeroOwner() throws Throwable {
        // arrange
        WmCustomerDB customer = createBasicCustomer();
        customer.setOwnerUid(0);
        // act
        List<OwnerApplyCustomerResult> results = (List<OwnerApplyCustomerResult>) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, Lists.newArrayList(customer));
        // assert
        assertEquals(1, results.size());
        OwnerApplyCustomerResult result = results.get(0);
        assertNull(result.getCurrentOwnerUid());
        assertEquals("", result.getCurrentOwnerMisId());
        assertEquals("", result.getCurrentOwnerUName());
    }

    /**
     * Test case for single customer with owner but employee not found
     */
    @Test
    public void testConvertOwnerApplyCustomerResult_SingleCustomerEmployeeNotFound() throws Throwable {
        // arrange
        WmCustomerDB customer = createBasicCustomer();
        customer.setOwnerUid(100);
        when(wmEmployeeService.getWmEmployById(100)).thenReturn(null);
        // act
        List<OwnerApplyCustomerResult> results = (List<OwnerApplyCustomerResult>) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, Lists.newArrayList(customer));
        // assert
        assertEquals(1, results.size());
        OwnerApplyCustomerResult result = results.get(0);
        assertEquals(Integer.valueOf(100), result.getCurrentOwnerUid());
        assertEquals("", result.getCurrentOwnerMisId());
        assertEquals("", result.getCurrentOwnerUName());
    }

    /**
     * Test case for multiple customers with different owner scenarios
     */
    @Test
    public void testConvertOwnerApplyCustomerResult_MultipleCustomers() throws Throwable {
        // arrange
        WmCustomerDB customer1 = createBasicCustomer();
        customer1.setId(1);
        customer1.setCustomerName("Customer 1");
        customer1.setOwnerUid(100);
        customer1.setCustomerRealType(CustomerRealTypeEnum.DANDIAN.getValue());
        WmCustomerDB customer2 = createBasicCustomer();
        customer2.setId(2);
        customer2.setCustomerName("Customer 2");
        customer2.setOwnerUid(0);
        customer2.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        WmEmploy employee1 = new WmEmploy();
        employee1.setMisId("mis001");
        employee1.setName("Employee 1");
        when(wmEmployeeService.getWmEmployById(100)).thenReturn(employee1);
        // act
        List<OwnerApplyCustomerResult> results = (List<OwnerApplyCustomerResult>) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, Lists.newArrayList(customer1, customer2));
        // assert
        assertEquals(2, results.size());
        OwnerApplyCustomerResult result1 = results.get(0);
        assertEquals("Customer 1", result1.getCustomerName());
        assertEquals(Integer.valueOf(100), result1.getCurrentOwnerUid());
        assertEquals("mis001", result1.getCurrentOwnerMisId());
        assertEquals("Employee 1", result1.getCurrentOwnerUName());
        assertEquals(CustomerRealTypeEnum.DANDIAN.getName(), result1.getCustomerRealTypeDesc());
        OwnerApplyCustomerResult result2 = results.get(1);
        assertEquals("Customer 2", result2.getCustomerName());
        assertNull(result2.getCurrentOwnerUid());
        assertEquals("", result2.getCurrentOwnerMisId());
        assertEquals("", result2.getCurrentOwnerUName());
        assertEquals(CustomerRealTypeEnum.MEISHICHENG.getName(), result2.getCustomerRealTypeDesc());
    }

    /**
     * Test case for null input
     */
    @Test
    public void testConvertOwnerApplyCustomerResult_NullInput() throws Throwable {
        // act
        List<OwnerApplyCustomerResult> results = (List<OwnerApplyCustomerResult>) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, Lists.newArrayList());
        // assert
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    /**
     * Test querying by customer number when customer exists
     */
    @Test
    public void testGetCustomerResult_QueryByCustomerNumber_Success() throws WmCustomerException {
        // arrange
        OwnerApplyQueryCustomerBO queryBO = new OwnerApplyQueryCustomerBO();
        queryBO.setType(1);
        queryBO.setCustomerNumber("TEST001");
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setId(1);
        customerDB.setCustomerName("TestCustomer");
        customerDB.setCustomerNumber("TEST001");
        customerDB.setMtCustomerId(1001L);
        customerDB.setOwnerUid(2001);
        customerDB.setCustomerRealType(CustomerRealTypeEnum.DANDIAN.getValue());
        WmEmploy wmEmploy = new WmEmploy();
        wmEmploy.setMisId("test_mis");
        wmEmploy.setName("TestOwner");
        when(wmCustomerPlatformDataParseService.getCustomerByCustomerNumber(anyString())).thenReturn(Lists.newArrayList(customerDB));
        when(wmEmployeeService.getWmEmployById(anyInt())).thenReturn(wmEmploy);
        // act
        List<OwnerApplyCustomerResult> results = wmCustomerOwnerApplyBusService.getCustomerResult(queryBO);
        // assert
        assertNotNull(results);
        assertEquals(1, results.size());
        OwnerApplyCustomerResult result = results.get(0);
        assertEquals("TestCustomer", result.getCustomerName());
        assertEquals(Integer.valueOf(1), result.getCustomerId());
        assertEquals("TEST001", result.getCustomerNumber());
        assertEquals(Long.valueOf(1001L), result.getMtCustomerId());
        assertEquals(Integer.valueOf(2001), result.getCurrentOwnerUid());
        assertEquals("test_mis", result.getCurrentOwnerMisId());
        assertEquals("TestOwner", result.getCurrentOwnerUName());
    }

    /**
     * Test querying by customer number when no customer found
     */
    @Test
    public void testGetCustomerResult_QueryByCustomerNumber_NotFound() throws WmCustomerException {
        // arrange
        OwnerApplyQueryCustomerBO queryBO = new OwnerApplyQueryCustomerBO();
        queryBO.setType(1);
        queryBO.setCustomerNumber("TEST002");
        when(wmCustomerPlatformDataParseService.getCustomerByCustomerNumber(anyString())).thenReturn(Lists.newArrayList());
        // act
        List<OwnerApplyCustomerResult> results = wmCustomerOwnerApplyBusService.getCustomerResult(queryBO);
        // assert
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    /**
     * Test querying by POI ID when customer exists
     */
    @Test
    public void testGetCustomerResult_QueryByPoiId_Success() throws WmCustomerException {
        // arrange
        OwnerApplyQueryCustomerBO queryBO = new OwnerApplyQueryCustomerBO();
        queryBO.setType(2);
        queryBO.setWmPoiId(1001L);
        Set<Integer> customerIds = Sets.newHashSet(1);
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setId(1);
        customerDB.setCustomerName("TestCustomer");
        customerDB.setCustomerNumber("TEST001");
        customerDB.setMtCustomerId(1001L);
        customerDB.setOwnerUid(2001);
        customerDB.setCustomerRealType(CustomerRealTypeEnum.DANDIAN.getValue());
        WmEmploy wmEmploy = new WmEmploy();
        wmEmploy.setMisId("test_mis");
        wmEmploy.setName("TestOwner");
        when(wmCustomerPoiService.selectCustomerIdByPoiId(any())).thenReturn(customerIds);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(customerDB);
        when(wmEmployeeService.getWmEmployById(anyInt())).thenReturn(wmEmploy);
        // act
        List<OwnerApplyCustomerResult> results = wmCustomerOwnerApplyBusService.getCustomerResult(queryBO);
        // assert
        assertNotNull(results);
        assertEquals(1, results.size());
        OwnerApplyCustomerResult result = results.get(0);
        assertEquals("TestCustomer", result.getCustomerName());
        assertEquals("TEST001", result.getCustomerNumber());
    }

    /**
     * Test querying by POI ID when no customer IDs found
     */
    @Test
    public void testGetCustomerResult_QueryByPoiId_NoCustomerIds() throws WmCustomerException {
        // arrange
        OwnerApplyQueryCustomerBO queryBO = new OwnerApplyQueryCustomerBO();
        queryBO.setType(2);
        queryBO.setWmPoiId(1001L);
        when(wmCustomerPoiService.selectCustomerIdByPoiId(any())).thenReturn(Sets.newHashSet());
        // act
        List<OwnerApplyCustomerResult> results = wmCustomerOwnerApplyBusService.getCustomerResult(queryBO);
        // assert
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    /**
     * Test querying by POI ID when customer not found in platform
     */
    @Test
    public void testGetCustomerResult_QueryByPoiId_CustomerNotFoundInPlatform() throws WmCustomerException {
        // arrange
        OwnerApplyQueryCustomerBO queryBO = new OwnerApplyQueryCustomerBO();
        queryBO.setType(2);
        queryBO.setWmPoiId(1001L);
        Set<Integer> customerIds = Sets.newHashSet(1);
        when(wmCustomerPoiService.selectCustomerIdByPoiId(any())).thenReturn(customerIds);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(null);
        // act
        List<OwnerApplyCustomerResult> results = wmCustomerOwnerApplyBusService.getCustomerResult(queryBO);
        // assert
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    /**
     * Test querying by POI ID when owner has no employee info
     */
    @Test
    public void testGetCustomerResult_QueryByPoiId_NoOwnerInfo() throws WmCustomerException {
        // arrange
        OwnerApplyQueryCustomerBO queryBO = new OwnerApplyQueryCustomerBO();
        queryBO.setType(2);
        queryBO.setWmPoiId(1001L);
        Set<Integer> customerIds = Sets.newHashSet(1);
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setId(1);
        customerDB.setCustomerName("TestCustomer");
        customerDB.setCustomerNumber("TEST001");
        customerDB.setMtCustomerId(1001L);
        customerDB.setOwnerUid(2001);
        customerDB.setCustomerRealType(CustomerRealTypeEnum.DANDIAN.getValue());
        when(wmCustomerPoiService.selectCustomerIdByPoiId(any())).thenReturn(customerIds);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(customerDB);
        when(wmEmployeeService.getWmEmployById(anyInt())).thenReturn(null);
        // act
        List<OwnerApplyCustomerResult> results = wmCustomerOwnerApplyBusService.getCustomerResult(queryBO);
        // assert
        assertNotNull(results);
        assertEquals(1, results.size());
        OwnerApplyCustomerResult result = results.get(0);
        assertEquals("", result.getCurrentOwnerMisId());
        assertEquals("", result.getCurrentOwnerUName());
    }

    /**
     * Test normal conversion with all fields populated
     */
    @Test
    public void testConvertWmCustomerOwnerApplyRecordQueryBoWithAllFields() throws Throwable {
        // arrange
        OwnerApplyRecordQueryBO queryBO = new OwnerApplyRecordQueryBO();
        queryBO.setPageNo(2);
        queryBO.setPageSize(10);
        queryBO.setModuleId(100);
        queryBO.setOpType(1);
        queryBO.setOpUser(1001);
        queryBO.setOpTimeStart(1000);
        queryBO.setOpTimeEnd(2000);
        queryBO.setContent("test content");
        queryBO.setApplyId(500);
        // act
        WmCustomerOwnerApplyRecordQueryBo result = invokeConvertMethod(queryBO);
        // assert
        // (2-1) * 10
        assertEquals(10, result.getOffset());
        assertEquals(10, result.getPageSize());
        assertEquals(Integer.valueOf(100), result.getModuleId());
        assertEquals(Integer.valueOf(1), result.getOpType());
        assertEquals(Integer.valueOf(1001), result.getOpUid());
        assertEquals(Integer.valueOf(1000), result.getCreateTimeStart());
        assertEquals(Integer.valueOf(2000), result.getCreateTimeEnd());
        assertEquals("test content", result.getContent());
        assertEquals(Integer.valueOf(500), result.getApplyId());
    }

    /**
     * Test conversion with first page
     */
    @Test
    public void testConvertWmCustomerOwnerApplyRecordQueryBoWithFirstPage() throws Throwable {
        // arrange
        OwnerApplyRecordQueryBO queryBO = new OwnerApplyRecordQueryBO();
        queryBO.setPageNo(1);
        queryBO.setPageSize(10);
        // act
        WmCustomerOwnerApplyRecordQueryBo result = invokeConvertMethod(queryBO);
        // assert
        // (1-1) * 10
        assertEquals(0, result.getOffset());
        assertEquals(10, result.getPageSize());
    }

    /**
     * Test conversion with large page number
     */
    @Test
    public void testConvertWmCustomerOwnerApplyRecordQueryBoWithLargePage() throws Throwable {
        // arrange
        OwnerApplyRecordQueryBO queryBO = new OwnerApplyRecordQueryBO();
        queryBO.setPageNo(100);
        queryBO.setPageSize(20);
        // act
        WmCustomerOwnerApplyRecordQueryBo result = invokeConvertMethod(queryBO);
        // assert
        // (100-1) * 20
        assertEquals(1980, result.getOffset());
        assertEquals(20, result.getPageSize());
    }

    /**
     * Test conversion with null fields
     */
    @Test
    public void testConvertWmCustomerOwnerApplyRecordQueryBoWithNullFields() throws Throwable {
        // arrange
        OwnerApplyRecordQueryBO queryBO = new OwnerApplyRecordQueryBO();
        queryBO.setPageNo(1);
        queryBO.setPageSize(10);
        // leave other fields null
        // act
        WmCustomerOwnerApplyRecordQueryBo result = invokeConvertMethod(queryBO);
        // assert
        assertEquals(0, result.getOffset());
        assertEquals(10, result.getPageSize());
        assertNull(result.getModuleId());
        assertNull(result.getOpType());
        assertNull(result.getOpUid());
        assertNull(result.getCreateTimeStart());
        assertNull(result.getCreateTimeEnd());
        assertNull(result.getContent());
        assertNull(result.getApplyId());
    }

    /**
     * Test conversion with minimum values
     */
    @Test
    public void testConvertWmCustomerOwnerApplyRecordQueryBoWithMinValues() throws Throwable {
        // arrange
        OwnerApplyRecordQueryBO queryBO = new OwnerApplyRecordQueryBO();
        queryBO.setPageNo(1);
        queryBO.setPageSize(1);
        queryBO.setModuleId(0);
        queryBO.setOpType(0);
        queryBO.setOpUser(0);
        queryBO.setOpTimeStart(0);
        queryBO.setOpTimeEnd(0);
        queryBO.setContent("");
        queryBO.setApplyId(0);
        // act
        WmCustomerOwnerApplyRecordQueryBo result = invokeConvertMethod(queryBO);
        // assert
        assertEquals(0, result.getOffset());
        assertEquals(1, result.getPageSize());
        assertEquals(Integer.valueOf(0), result.getModuleId());
        assertEquals(Integer.valueOf(0), result.getOpType());
        assertEquals(Integer.valueOf(0), result.getOpUid());
        assertEquals(Integer.valueOf(0), result.getCreateTimeStart());
        assertEquals(Integer.valueOf(0), result.getCreateTimeEnd());
        assertEquals("", result.getContent());
        assertEquals(Integer.valueOf(0), result.getApplyId());
    }

    /**
     * Test getEmployeeById when employee exists
     * Should return valid WmEmploy object
     */
    @Test
    public void testGetEmployeeByIdWhenEmployeeExists() throws Throwable {
        // arrange
        Integer employeeId = 123;
        WmEmploy expectedEmployee = new WmEmploy();
        when(wmEmployeeService.getWmEmployById(employeeId.intValue())).thenReturn(expectedEmployee);
        // Use reflection to invoke the private method
        Method method = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("getEmployeeById", Integer.class);
        method.setAccessible(true);
        WmEmploy result = (WmEmploy) method.invoke(wmCustomerOwnerApplyBusService, employeeId);
        // assert
        assertNotNull(result);
        verify(wmEmployeeService).getWmEmployById(employeeId.intValue());
    }

    /**
     * Test getEmployeeById when employee does not exist
     * Should throw WmCustomerException with proper error message
     */
    @Test
    public void testGetEmployeeByIdWhenEmployeeNotExists() throws Throwable {
        // arrange
        Integer employeeId = 456;
        when(wmEmployeeService.getWmEmployById(employeeId.intValue())).thenReturn(null);
        // Use reflection to invoke the private method
        Method method = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("getEmployeeById", Integer.class);
        method.setAccessible(true);
        try {
            method.invoke(wmCustomerOwnerApplyBusService, employeeId);
            fail("Expected WmCustomerException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof WmCustomerException);
            WmCustomerException customerException = (WmCustomerException) e.getCause();
            assertEquals(CustomerConstants.RESULT_CODE_ERROR, customerException.getCode());
            assertEquals("未查询到客户责任人信息", customerException.getMessage());
        }
        verify(wmEmployeeService).getWmEmployById(employeeId.intValue());
    }

    /**
     * Test getEmployeeById with null employeeId
     * Should throw WmCustomerException
     */
    @Test
    public void testGetEmployeeByIdWithNullId() throws Throwable {
        // arrange
        Integer employeeId = null;
        // Use reflection to invoke the private method
        Method method = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("getEmployeeById", Integer.class);
        method.setAccessible(true);
        try {
            method.invoke(wmCustomerOwnerApplyBusService, employeeId);
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            // Since the method uses Optional.ofNullable(wmEmployeeService.getWmEmployById(employeeId)),
            // a NullPointerException will be thrown when trying to unbox null to int
            assertTrue(e.getCause() instanceof NullPointerException);
        }
        // Verify that the service method is never called
        verify(wmEmployeeService, never()).getWmEmployById(anyInt());
    }

    /**
     * Test case for scenario where the query returns an empty list of WmCustomerOwnerApply objects.
     */
    @Test
    public void testGetDoingCustomerListByTimeEmptyList() throws Throwable {
        // arrange
        CustomerOwnerApplyListQueryBO queryBO = new CustomerOwnerApplyListQueryBO();
        queryBO.setCustomerId(1);
        queryBO.setCreatedTimeStart(CURRENT_TIME - ONE_HOUR);
        queryBO.setCreatedTimeEnd(CURRENT_TIME);
        when(wmCustomerOwnerApplyDao.listDoingApplyByTimeCondition(any(WmCustomerOwnerApplyListDbQuery.class))).thenReturn(Collections.emptyList());
        // act
        List<Integer> result = wmCustomerOwnerApplyBusService.getDoingCustomerListByTime(queryBO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for normal scenario where the query returns a non-empty list of WmCustomerOwnerApply objects.
     */
    @Test
    public void testGetDoingCustomerListByTimeNonEmptyList() throws Throwable {
        // arrange
        CustomerOwnerApplyListQueryBO queryBO = new CustomerOwnerApplyListQueryBO();
        queryBO.setCustomerId(1);
        queryBO.setCreatedTimeStart(CURRENT_TIME - ONE_HOUR);
        queryBO.setCreatedTimeEnd(CURRENT_TIME);
        WmCustomerOwnerApply apply1 = new WmCustomerOwnerApply();
        apply1.setCustomerId(1);
        WmCustomerOwnerApply apply2 = new WmCustomerOwnerApply();
        apply2.setCustomerId(2);
        List<WmCustomerOwnerApply> wmCustomerOwnerApplies = Arrays.asList(apply1, apply2);
        when(wmCustomerOwnerApplyDao.listDoingApplyByTimeCondition(any(WmCustomerOwnerApplyListDbQuery.class))).thenReturn(wmCustomerOwnerApplies);
        // act
        List<Integer> result = wmCustomerOwnerApplyBusService.getDoingCustomerListByTime(queryBO);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(2));
    }

    /**
     * Test successful group creation scenario
     */
    @Test
    public void testCreateRoomOnCustomerOwnerApply_Success() throws Throwable {
        // arrange
        Integer applyId = 1;
        Integer applyUid = 100;
        Integer customerOwnerUid = 200;
        Long moderatorId = 1000L;
        Long oldOwnerDxUid = 2000L;
        Long groupId = 3000L;
        String misId1 = "mis1";
        String misId2 = "mis2";
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(applyUid);
        apply.setCustomerId(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        apply.setCustomerOwnerUid(customerOwnerUid);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setCustomerName("testCustomer");
        wmCustomerDB.setMtCustomerId(123L);
        WmEmploy wmEmploy1 = new WmEmploy();
        wmEmploy1.setMisId(misId1);
        WmEmploy wmEmploy2 = new WmEmploy();
        wmEmploy2.setMisId(misId2);
        when(wmCustomerOwnerApplyDao.getWmCustomerOwnerApplyById(applyId)).thenReturn(apply);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(apply.getCustomerId())).thenReturn(wmCustomerDB);
        when(wmEmployeeService.getWmEmployById(applyUid)).thenReturn(wmEmploy1);
        when(wmEmployeeService.getWmEmployById(customerOwnerUid)).thenReturn(wmEmploy2);
        when(uDbServiceAdaptor.getUidByMisId(misId1)).thenReturn(moderatorId);
        when(uDbServiceAdaptor.getUidByMisId(misId2)).thenReturn(oldOwnerDxUid);
        Set<Long> participantIds = new HashSet<>();
        participantIds.add(moderatorId);
        participantIds.add(oldOwnerDxUid);
        String roomName = String.format("客户%s(%s)责任人申请沟通", wmCustomerDB.getCustomerName(), wmCustomerDB.getMtCustomerId());
        String noticeMsg = String.format("您好，客户%s(%s)责任人申请请于本群内进行沟通", wmCustomerDB.getCustomerName(), wmCustomerDB.getMtCustomerId());
        // Create sorted list to ensure consistent order
        List<Long> participantList = new ArrayList<>(Arrays.asList(oldOwnerDxUid, moderatorId));
        Collections.sort(participantList);
        when(gInfoServiceAdaptor.createOverCompanyRoom(eq(moderatorId), eq(participantIds), eq(roomName))).thenReturn(groupId);
        // act
        BaseResponse<Boolean> response = wmCustomerOwnerApplyBusService.createRoomOnCustomerOwnerApply(applyId);
        // assert
        verify(wmCustomerOwnerApplyDao).updateGroupIdById(applyId, groupId);
        verify(gInfoServiceAdaptor).sendRoomNoticeMsg(eq(groupId), eq(noticeMsg), eq(participantList));
        assertEquals(0, response.getCode());
        assertEquals("成功", response.getMsg());
        assertTrue(response.getData());
    }

    /**
     * Test failed group creation scenario when getDxUidByEmpUid returns null
     */
    @Test
    public void testCreateRoomOnCustomerOwnerApply_GetDxUidFailed() throws Throwable {
        // arrange
        Integer applyId = 1;
        Integer applyUid = 100;
        Integer customerOwnerUid = 200;
        String misId1 = "mis1";
        String misId2 = "mis2";
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(applyUid);
        apply.setCustomerId(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        apply.setCustomerOwnerUid(customerOwnerUid);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setCustomerName("testCustomer");
        wmCustomerDB.setMtCustomerId(123L);
        WmEmploy wmEmploy1 = new WmEmploy();
        wmEmploy1.setMisId(misId1);
        WmEmploy wmEmploy2 = new WmEmploy();
        wmEmploy2.setMisId(misId2);
        when(wmCustomerOwnerApplyDao.getWmCustomerOwnerApplyById(applyId)).thenReturn(apply);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(apply.getCustomerId())).thenReturn(wmCustomerDB);
        when(wmEmployeeService.getWmEmployById(applyUid)).thenReturn(wmEmploy1);
        when(wmEmployeeService.getWmEmployById(customerOwnerUid)).thenReturn(wmEmploy2);
        when(uDbServiceAdaptor.getUidByMisId(misId1)).thenReturn(null);
        when(wmEnterpriseAgentElephantServiceAdaptor.getDxUidByOuterMis(misId1)).thenReturn(null);
        // act
        BaseResponse<Boolean> response = wmCustomerOwnerApplyBusService.createRoomOnCustomerOwnerApply(applyId);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("建群失败", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * Test failed group creation scenario when createOverCompanyRoom returns null
     */
    @Test
    public void testCreateRoomOnCustomerOwnerApply_CreateRoomFailed() throws Throwable {
        // arrange
        Integer applyId = 1;
        Integer applyUid = 100;
        Integer customerOwnerUid = 200;
        Long moderatorId = 1000L;
        Long oldOwnerDxUid = 2000L;
        String misId1 = "mis1";
        String misId2 = "mis2";
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(applyUid);
        apply.setCustomerId(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        apply.setCustomerOwnerUid(customerOwnerUid);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setCustomerName("testCustomer");
        wmCustomerDB.setMtCustomerId(123L);
        WmEmploy wmEmploy1 = new WmEmploy();
        wmEmploy1.setMisId(misId1);
        WmEmploy wmEmploy2 = new WmEmploy();
        wmEmploy2.setMisId(misId2);
        when(wmCustomerOwnerApplyDao.getWmCustomerOwnerApplyById(applyId)).thenReturn(apply);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(apply.getCustomerId())).thenReturn(wmCustomerDB);
        when(wmEmployeeService.getWmEmployById(applyUid)).thenReturn(wmEmploy1);
        when(wmEmployeeService.getWmEmployById(customerOwnerUid)).thenReturn(wmEmploy2);
        when(uDbServiceAdaptor.getUidByMisId(misId1)).thenReturn(moderatorId);
        when(uDbServiceAdaptor.getUidByMisId(misId2)).thenReturn(oldOwnerDxUid);
        Set<Long> participantIds = new HashSet<>();
        participantIds.add(moderatorId);
        participantIds.add(oldOwnerDxUid);
        String roomName = String.format("客户%s(%s)责任人申请沟通", wmCustomerDB.getCustomerName(), wmCustomerDB.getMtCustomerId());
        when(gInfoServiceAdaptor.createOverCompanyRoom(eq(moderatorId), eq(participantIds), eq(roomName))).thenReturn(null);
        // act
        BaseResponse<Boolean> response = wmCustomerOwnerApplyBusService.createRoomOnCustomerOwnerApply(applyId);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("建群失败", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * Test case for the scenario where the user has the required role.
     */
    @Test
    public void testCheckHasCustomerOwnerApplyAuth_UserHasRole() throws Throwable {
        // arrange
        when(upmAuthCheckService.checkHasUpmAuthByCode(anyInt(), any())).thenReturn(true);
        // act
        CustomerListAuthInfoDTO result = wmCustomerOwnerApplyBusService.checkHasCustomerOwnerApplyAuth(USER_ID);
        // assert
        assertNotNull(result);
        assertTrue(result.getHasCustomerOwnerApplyAuth());
        verify(upmAuthCheckService, times(1)).checkHasUpmAuthByCode(anyInt(), any());
    }

    /**
     * Test case for the scenario where the user does not have the required role.
     */
    @Test
    public void testCheckHasCustomerOwnerApplyAuth_UserDoesNotHaveRole() throws Throwable {
        // arrange
        when(upmAuthCheckService.checkHasUpmAuthByCode(anyInt(), any())).thenReturn(false);
        // act
        CustomerListAuthInfoDTO result = wmCustomerOwnerApplyBusService.checkHasCustomerOwnerApplyAuth(USER_ID);
        // assert
        assertNotNull(result);
        assertFalse(result.getHasCustomerOwnerApplyAuth());
        verify(upmAuthCheckService, times(1)).checkHasUpmAuthByCode(anyInt(), any());
    }

    /**
     * Test case for the scenario where an exception is thrown during role check.
     */
    @Test(expected = WmCustomerException.class)
    public void testCheckHasCustomerOwnerApplyAuth_RoleCheckThrowsException() throws Throwable {
        // arrange
        when(upmAuthCheckService.checkHasUpmAuthByCode(anyInt(), any())).thenThrow(new WmCustomerException(500, "Role check failed"));
        // act
        wmCustomerOwnerApplyBusService.checkHasCustomerOwnerApplyAuth(USER_ID);
        // assert
        // Exception is expected to be thrown
    }

    /**
     * Test successful case where audit record exists and ticket info is retrieved
     */
    @Test
    public void testGetAuditLinkUrl_Success() throws Throwable {
        // arrange
        Integer applyId = 123;
        Integer taskId = 456;
        Integer ticketId = 789;
        WmCustomerOwnerApplyAudit audit = mock(WmCustomerOwnerApplyAudit.class);
        when(audit.getTaskId()).thenReturn(taskId);
        WmTicketDto ticketDto = new WmTicketDto();
        ticketDto.setId(ticketId);
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId)).thenReturn(audit);
        when(wmCrmTicketThriftServiceAdapter.getSubTicketByParentTicketId(taskId)).thenReturn(ticketDto);
        try (MockedStatic<MccScConfig> mockedConfig = mockStatic(MccScConfig.class)) {
            // Mock static methods
            mockedConfig.when(MccScConfig::getTicketSystemUrlPrefix).thenReturn(TICKET_URL_PREFIX);
            mockedConfig.when(MccScConfig::getTicketSystemUrlSuffix).thenReturn(TICKET_URL_SUFFIX);
            // act
            String result = (String) getAuditLinkUrlMethod.invoke(wmCustomerOwnerApplyBusService, applyId);
            // assert
            String expectedUrl = TICKET_URL_PREFIX + ticketId + TICKET_URL_SUFFIX;
            assertEquals(expectedUrl, result);
            verify(wmCustomerOwnerApplyAuditDao).getByApplyIdIgnoreValid(applyId);
            verify(wmCrmTicketThriftServiceAdapter).getSubTicketByParentTicketId(taskId);
        }
    }

    /**
     * Test case where audit record is not found
     */
    @Test(expected = WmCustomerException.class)
    public void testGetAuditLinkUrl_NoAuditFound() throws Throwable {
        // arrange
        Integer applyId = 123;
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId)).thenReturn(null);
        try {
            // act
            getAuditLinkUrlMethod.invoke(wmCustomerOwnerApplyBusService, applyId);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                WmCustomerException wce = (WmCustomerException) e.getCause();
                assertEquals(CustomerConstants.RESULT_CODE_ERROR, wce.getCode());
                assertEquals("未查询到客户申请单审批信息", wce.getMsg());
                throw wce;
            }
            throw e;
        }
    }

    /**
     * Test case where ticket service throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testGetAuditLinkUrl_TicketServiceError() throws Throwable {
        // arrange
        Integer applyId = 123;
        Integer taskId = 456;
        WmCustomerOwnerApplyAudit audit = mock(WmCustomerOwnerApplyAudit.class);
        when(audit.getTaskId()).thenReturn(taskId);
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(applyId)).thenReturn(audit);
        when(wmCrmTicketThriftServiceAdapter.getSubTicketByParentTicketId(taskId)).thenThrow(new RuntimeException("Ticket service error"));
        try {
            // act
            getAuditLinkUrlMethod.invoke(wmCustomerOwnerApplyBusService, applyId);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                WmCustomerException wce = (WmCustomerException) e.getCause();
                assertEquals(CustomerConstants.RESULT_CODE_ERROR, wce.getCode());
                assertEquals("查询任务系统异常", wce.getMsg());
                throw wce;
            }
            throw e;
        }
    }

    /**
     * Test case with null applyId
     */
    @Test(expected = WmCustomerException.class)
    public void testGetAuditLinkUrl_NullApplyId() throws Throwable {
        try {
            // act
            getAuditLinkUrlMethod.invoke(wmCustomerOwnerApplyBusService, (Object) null);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                WmCustomerException wce = (WmCustomerException) e.getCause();
                assertEquals(CustomerConstants.RESULT_CODE_ERROR, wce.getCode());
                assertEquals("未查询到客户申请单审批信息", wce.getMsg());
                throw wce;
            }
            throw e;
        }
    }

    /**
     * Test case for customer ID validation failure
     */
    @Test
    public void testListCustomerOwnerApply_CustomerIdValidationFail() throws Throwable {
        // arrange
        CustomerOwnerApplyQueryBO queryBO = new CustomerOwnerApplyQueryBO();
        queryBO.setMtCustomerId(1L);
        queryBO.setCustomerNumber("123");
        queryBO.setPageNo(1);
        queryBO.setPageSize(10);
        List<WmCustomerDB> customerList = new ArrayList<>();
        when(wmCustomerESService.queryByCustomerIdOrCustomerNumber(anyLong(), anyString())).thenReturn(customerList);
        // act
        BaseResponse response = wmCustomerOwnerApplyBusService.listCustomerOwnerApply(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_SUCCESS, response.getCode());
        CustomerOwnerApplyListDTO listDTO = (CustomerOwnerApplyListDTO) response.getData();
        assertEquals(0, listDTO.getTotalCnt().intValue());
    }

    /**
     * Test case for WmPoiId validation failure
     */
    @Test
    public void testListCustomerOwnerApply_WmPoiIdValidationFail() throws Throwable {
        // arrange
        CustomerOwnerApplyQueryBO queryBO = new CustomerOwnerApplyQueryBO();
        queryBO.setWmPoiId(1L);
        queryBO.setPageNo(1);
        queryBO.setPageSize(10);
        when(wmCustomerPoiDBMapper.selectCustomerPoiRelByWmPoiIdWithValid(anyLong())).thenReturn(null);
        // act
        BaseResponse response = wmCustomerOwnerApplyBusService.listCustomerOwnerApply(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_SUCCESS, response.getCode());
        CustomerOwnerApplyListDTO listDTO = (CustomerOwnerApplyListDTO) response.getData();
        assertEquals(0, listDTO.getTotalCnt().intValue());
    }

    /**
     * Test case for WmCustomerException
     */
    @Test
    public void testListCustomerOwnerApply_WmCustomerException() throws Throwable {
        // arrange
        CustomerOwnerApplyQueryBO queryBO = new CustomerOwnerApplyQueryBO();
        queryBO.setPageNo(1);
        queryBO.setPageSize(10);
        doThrow(new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "Test error")).when(customerOwnerApplyCheckService).checkCustomerOwnerApplyQueryBo(any());
        // act
        BaseResponse response = wmCustomerOwnerApplyBusService.listCustomerOwnerApply(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_SUCCESS, response.getCode());
        CustomerOwnerApplyListDTO listDTO = (CustomerOwnerApplyListDTO) response.getData();
        assertEquals(0, listDTO.getTotalCnt().intValue());
    }

    /**
     * Test case for successful flow with customer ID mismatch
     */
    @Test
    public void testListCustomerOwnerApply_CustomerIdMismatch() throws Throwable {
        // arrange
        CustomerOwnerApplyQueryBO queryBO = new CustomerOwnerApplyQueryBO();
        queryBO.setCustomerId(1);
        queryBO.setWmPoiId(1L);
        queryBO.setPageNo(1);
        queryBO.setPageSize(10);
        WmCustomerPoiDB customerPoiDB = new WmCustomerPoiDB();
        customerPoiDB.setCustomerId(2);
        when(wmCustomerPoiDBMapper.selectCustomerPoiRelByWmPoiIdWithValid(anyLong())).thenReturn(customerPoiDB);
        // act
        BaseResponse response = wmCustomerOwnerApplyBusService.listCustomerOwnerApply(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_SUCCESS, response.getCode());
        CustomerOwnerApplyListDTO listDTO = (CustomerOwnerApplyListDTO) response.getData();
        assertEquals(0, listDTO.getTotalCnt().intValue());
    }
}
