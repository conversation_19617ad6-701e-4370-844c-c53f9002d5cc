package com.sankuai.meituan.waimai.customer.contract.service.impl.check.config;

import com.sankuai.meituan.waimai.customer.adapter.config.FoodMachinePoiAuthorizeAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

/**
 * 校验是否可以发起食光机一口价框架合同单测
 *
 * @Author: wangyongfang
 * @Date: 2025-01-03
 */
public class FoodMachineCustomerInfoValidatorTest {

    @InjectMocks
    private FoodMachineCustomerInfoValidator validator;

    @Mock
    private FoodMachinePoiAuthorizeAdapter foodMachinePoiAuthorizeAdapter;

    private MockedStatic<MccConfig> mccConfigMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mccConfigMockedStatic = mockStatic(MccConfig.class);
    }

    @After
    public void after() {
        if (null != mccConfigMockedStatic) {
            mccConfigMockedStatic.close();
        }
    }

    /**
     * 测试合同来源为代码源时，验证通过
     */
    @Test
    public void testValidWithCodeSource() throws Throwable {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setContractSource(ContractSourceEnum.CODE.getCode());
        contractBo.setBasicBo(basicBo);

        boolean result = validator.valid(contractBo, 1, "testOpName");

        assertTrue(result);
    }

    /**
     * 测试合同类型不是食光机一口价合同时，验证通过
     */
    @Test
    public void testValidWithNonFoodMachineContractType() throws Throwable {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setContractSource(ContractSourceEnum.CONFIG.getCode());
        basicBo.setType(999); // 假设999不是食光机一口价合同的ID
        contractBo.setBasicBo(basicBo);
        mccConfigMockedStatic.when(MccConfig::getFoodMachineContractTemplateId).thenReturn(1000);
        boolean result = validator.valid(contractBo, 1, "testOpName");

        assertTrue(result);
    }

    /**
     * 测试客户ID信息异常时，抛出WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testValidWithInvalidCustomerId() throws Throwable {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setContractSource(ContractSourceEnum.CONFIG.getCode());
        basicBo.setParentId(0); // 客户ID设置为0，模拟无效ID
        basicBo.setType(999);
        contractBo.setBasicBo(basicBo);
        mccConfigMockedStatic.when(MccConfig::getFoodMachineContractTemplateId).thenReturn(999);

        validator.valid(contractBo, 1, "testOpName");

        Assert.fail();
    }

    /**
     * 测试客户无法签约食光机一口价合同时，抛出WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testValidWithUnauthorizedCustomer() throws Throwable {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setContractSource(ContractSourceEnum.CONFIG.getCode());
        basicBo.setParentId(1); // 客户ID设置为1
        basicBo.setType(999);
        contractBo.setBasicBo(basicBo);
        mccConfigMockedStatic.when(MccConfig::getFoodMachineContractTemplateId).thenReturn(999);

        when(foodMachinePoiAuthorizeAdapter.isCustomerFoodMachinePoiAuthorized(1L, MccConfig.getFoodMachineContractTemplateId())).thenReturn(false);

        validator.valid(contractBo, 1, "testOpName");
        Assert.fail();
    }

    /**
     * 测试客户可以签约食光机一口价合同时，验证通过
     */
    @Test
    public void testValidWithAuthorizedCustomer() throws Throwable {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setContractSource(ContractSourceEnum.CONFIG.getCode());
        basicBo.setParentId(1); // 客户ID设置为1
        basicBo.setType(999);
        contractBo.setBasicBo(basicBo);
        mccConfigMockedStatic.when(MccConfig::getFoodMachineContractTemplateId).thenReturn(999);

        when(foodMachinePoiAuthorizeAdapter.isCustomerFoodMachinePoiAuthorized(1L, MccConfig.getFoodMachineContractTemplateId())).thenReturn(true);

        boolean result = validator.valid(contractBo, 1, "testOpName");

        assertTrue(result);
    }
}