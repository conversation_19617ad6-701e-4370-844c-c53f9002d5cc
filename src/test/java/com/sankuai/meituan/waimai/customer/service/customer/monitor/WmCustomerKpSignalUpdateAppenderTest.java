/*
package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.sankuai.meituan.waimai.BaseTest;
import org.junit.runner.RunWith;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

*/
/**
 * @Author: xhwang
 * @Date: 2022/8/23
 * @Description:
 **//*

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:application-context.xml")
public class WmCustomerKpSignalUpdateAppenderTest extends BaseTest {

	// 使用getLogger(String name)的方式获取logger。哪条日志要发往哪份日志只需要使用对应的logger就可以了。
	private static final Logger logger = LoggerFactory.getLogger("logger_com.sankuai.waimai.customer.kp.pc.update.signer");

	public static void main(String[] args) {
		String logContent = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

		// 日志会到kafka topic: org.com.sankuai.waimai.customer.kp.pc.update.signer
		logger.info(logContent);
	}

}
*/
