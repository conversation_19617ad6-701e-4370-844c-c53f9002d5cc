package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.nationalsubsidy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.DcContractContext;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidyPurchaseNoticeTaskTest {

    @InjectMocks
    private NationalSubsidyPurchaseNoticeTask nationalSubsidyPurchaseNoticeTask;

    @Mock
    private WmContractService wmContractService;

    @Mock
    private ManualPackNoticeContext context;

    private int customerId = 123;

    private int commitUid = 456;

    private long manualBatchId = 789L;

    @Before
    public void setUp() {
        // Setup the mock context
        when(context.getCustomerId()).thenReturn(customerId);
        when(context.getCommitUid()).thenReturn(commitUid);
        when(context.getManualBatchId()).thenReturn(manualBatchId);
    }

    /**
     * Test normal case with multiple bizIds
     */
    @Test
    public void testNotice_WithMultipleBizIds_Success() throws Throwable {
        // arrange
        String module = "test_module";
        List<Long> bizIdList = Arrays.asList(1L, 2L, 3L);
        List<Long> taskIds = Arrays.asList(100L, 200L);
        // act
        nationalSubsidyPurchaseNoticeTask.notice(module, bizIdList, context, taskIds);
        // assert
        // Use verify with times(1) for each specific bizId
        verify(wmContractService, times(1)).startSignByWaitingSign(customerId, 1L, commitUid, "", manualBatchId);
        verify(wmContractService, times(1)).startSignByWaitingSign(customerId, 2L, commitUid, "", manualBatchId);
        verify(wmContractService, times(1)).startSignByWaitingSign(customerId, 3L, commitUid, "", manualBatchId);
    }

    /**
     * Test case with empty bizIdList
     */
    @Test
    public void testNotice_WithEmptyBizIdList_Success() throws Throwable {
        // arrange
        String module = "test_module";
        List<Long> bizIdList = Collections.emptyList();
        List<Long> taskIds = Collections.emptyList();
        // act
        nationalSubsidyPurchaseNoticeTask.notice(module, bizIdList, context, taskIds);
        // assert
        verify(wmContractService, never()).startSignByWaitingSign(anyInt(), anyLong(), anyInt(), anyString(), anyLong());
    }

    /**
     * Test case when WmContractService throws WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testNotice_WhenWmContractServiceThrowsException_ShouldThrowException() throws Throwable {
        // arrange
        String module = "test_module";
        List<Long> bizIdList = Collections.singletonList(1L);
        List<Long> taskIds = Collections.singletonList(100L);
        doThrow(new WmCustomerException(500, "Test exception")).when(wmContractService).startSignByWaitingSign(customerId, 1L, commitUid, "", manualBatchId);
        // act
        nationalSubsidyPurchaseNoticeTask.notice(module, bizIdList, context, taskIds);
    }

    /**
     * Test case when context is null
     */
    @Test(expected = NullPointerException.class)
    public void testNotice_WhenContextIsNull_ShouldThrowException() throws Throwable {
        // arrange
        String module = "test_module";
        List<Long> bizIdList = Collections.singletonList(1L);
        List<Long> taskIds = Collections.singletonList(100L);
        // act
        nationalSubsidyPurchaseNoticeTask.notice(module, bizIdList, null, taskIds);
    }

    /**
     * Test case when TException is thrown by WmContractService
     */
    @Test(expected = TException.class)
    public void testNotice_WhenWmContractServiceThrowsTException_ShouldThrowException() throws Throwable {
        // arrange
        String module = "test_module";
        List<Long> bizIdList = Collections.singletonList(1L);
        List<Long> taskIds = Collections.singletonList(100L);
        doThrow(new TException("Test TException")).when(wmContractService).startSignByWaitingSign(customerId, 1L, commitUid, "", manualBatchId);
        // act
        nationalSubsidyPurchaseNoticeTask.notice(module, bizIdList, context, taskIds);
    }

    /**
     * Test case when bizIdList is null - should throw NullPointerException
     * This test reflects the actual behavior of the method
     */
    @Test(expected = NullPointerException.class)
    public void testNotice_WithNullBizIdList_ShouldThrowNullPointerException() throws Throwable {
        // arrange
        String module = "test_module";
        List<Long> taskIds = Collections.emptyList();
        // act
        nationalSubsidyPurchaseNoticeTask.notice(module, null, context, taskIds);
    }
}
