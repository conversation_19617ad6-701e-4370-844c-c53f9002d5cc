package com.sankuai.meituan.waimai.customer.service.sign.data;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractBatchDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractSingleDeliveryPerInfoBo;
import com.sankuai.meituan.waimai.customer.adapter.DeliveryContractAdapter;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryPhfInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PhfLogisticsPerPdfQueryHandlerQueryAndAssembleDataTest {

    @InjectMocks
    private PhfLogisticsPerPdfQueryHandler handler;

    @Mock
    private DeliveryContractAdapter deliveryContractAdapter;

    private Map<EcontractDataSourceEnum, EcontractDataSourceBo> inputMap;

    private Map<Long, EcontractDeliveryInfoBo> resultMap;

    private EcontractDataSourceBo dataSourceBo;

    private long manualBatchId = 123L;

    @Before
    public void setUp() {
        inputMap = new HashMap<>();
        resultMap = new HashMap<>();
        dataSourceBo = new EcontractDataSourceBo();
    }

    private void setupSinglePoiScenario() {
        List<EcontractDataPoiBizBo> poiList = new ArrayList<>();
        EcontractDataPoiBizBo poiBo = new EcontractDataPoiBizBo();
        poiBo.setWmPoiId(1L);
        poiBo.setBizId(100L);
        poiList.add(poiBo);
        dataSourceBo.setWmPoiIdAndBizIdList(poiList);
        inputMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE_PDF, dataSourceBo);
    }

    private void setupMultiplePoiScenario() {
        List<EcontractDataPoiBizBo> poiList = new ArrayList<>();
        EcontractDataPoiBizBo poiBo1 = new EcontractDataPoiBizBo();
        poiBo1.setWmPoiId(1L);
        poiBo1.setBizId(100L);
        EcontractDataPoiBizBo poiBo2 = new EcontractDataPoiBizBo();
        poiBo2.setWmPoiId(2L);
        poiBo2.setBizId(200L);
        poiList.add(poiBo1);
        poiList.add(poiBo2);
        List<List<Long>> groupList = new ArrayList<>();
        groupList.add(Arrays.asList(1L));
        groupList.add(Arrays.asList(2L));
        dataSourceBo.setWmPoiIdAndBizIdList(poiList);
        dataSourceBo.setWmPoiIdGroupList(groupList);
        inputMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE_PDF, dataSourceBo);
    }

    private void setupMockSuccess(Long... poiIds) {
        ElectronicContractBatchDeliveryPerInfoBo perInfoBo = new ElectronicContractBatchDeliveryPerInfoBo();
        Map<Long, ElectronicContractSingleDeliveryPerInfoBo> batchPerInfoMap = new HashMap<>();
        for (Long poiId : poiIds) {
            ElectronicContractSingleDeliveryPerInfoBo singlePerInfoBo = new ElectronicContractSingleDeliveryPerInfoBo();
            Map<Integer, String> pdfUrlMap = new HashMap<>();
            pdfUrlMap.put(1, "test.pdf");
            singlePerInfoBo.setPdfUrlMap(pdfUrlMap);
            batchPerInfoMap.put(poiId, singlePerInfoBo);
        }
        perInfoBo.setBatchPerInfoMap(batchPerInfoMap);
        when(deliveryContractAdapter.queryPerFeeWithRetry(any())).thenReturn(perInfoBo);
    }

    /**
     * Test case: Input map is null
     * Expected: Should throw WmCustomerException
     */
    @Test(expected = NullPointerException.class)
    public void testQueryAndAssembleData_NullMap() throws Throwable {
        handler.queryAndAssembleData(null, manualBatchId, resultMap);
    }

    /**
     * Test case: DataSourceBo is null
     * Expected: Should throw WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryAndAssembleData_NullDataSourceBo() throws Throwable {
        inputMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE_PDF, null);
        handler.queryAndAssembleData(inputMap, manualBatchId, resultMap);
    }

    /**
     * Test case: Empty WmPoiIdAndBizIdList
     * Expected: Should throw WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryAndAssembleData_EmptyPoiList() throws Throwable {
        inputMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE_PDF, dataSourceBo);
        handler.queryAndAssembleData(inputMap, manualBatchId, resultMap);
    }

    /**
     * Test case: Single poi scenario with successful query
     * Expected: Should assemble data correctly
     */
    @Test
    public void testQueryAndAssembleData_SinglePoiSuccess() throws Throwable {
        // arrange
        setupSinglePoiScenario();
        setupMockSuccess(1L);
        // act
        handler.queryAndAssembleData(inputMap, manualBatchId, resultMap);
        // assert
        assertEquals(1, resultMap.size());
        assertTrue(resultMap.containsKey(1L));
        assertNotNull(resultMap.get(1L).getEcontractDeliveryPhfInfoBo());
    }

    /**
     * Test case: Multiple poi scenario with successful query
     * Expected: Should assemble data correctly for all pois
     */
    @Test
    public void testQueryAndAssembleData_MultiplePoiSuccess() throws Throwable {
        // arrange
        setupMultiplePoiScenario();
        setupMockSuccess(1L, 2L);
        // act
        handler.queryAndAssembleData(inputMap, manualBatchId, resultMap);
        // assert
        assertEquals(2, resultMap.size());
        assertTrue(resultMap.containsKey(1L));
        assertTrue(resultMap.containsKey(2L));
    }

    /**
     * Test case: Query returns null response
     * Expected: Should throw WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryAndAssembleData_NullQueryResponse() throws Throwable {
        // arrange
        setupSinglePoiScenario();
        when(deliveryContractAdapter.queryPerFeeWithRetry(any())).thenReturn(null);
        // act
        handler.queryAndAssembleData(inputMap, manualBatchId, resultMap);
    }

    /**
     * Test case: Empty WmPoiIdGroupList for multiple poi scenario
     * Expected: Should throw WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryAndAssembleData_EmptyGroupList() throws Throwable {
        // arrange
        setupMultiplePoiScenario();
        dataSourceBo.setWmPoiIdGroupList(new ArrayList<>());
        // act
        handler.queryAndAssembleData(inputMap, manualBatchId, resultMap);
    }

    /**
     * Test case: Existing delivery info in result map
     * Expected: Should update existing info correctly
     */
    @Test
    public void testQueryAndAssembleData_ExistingDeliveryInfo() throws Throwable {
        // arrange
        setupSinglePoiScenario();
        setupMockSuccess(1L);
        EcontractDeliveryInfoBo existingInfo = new EcontractDeliveryInfoBo();
        existingInfo.setWmPoiId("1");
        resultMap.put(1L, existingInfo);
        // act
        handler.queryAndAssembleData(inputMap, manualBatchId, resultMap);
        // assert
        assertNotNull(resultMap.get(1L));
        assertNotNull(resultMap.get(1L).getEcontractDeliveryPhfInfoBo());
        assertEquals("1", resultMap.get(1L).getWmPoiId());
    }
}