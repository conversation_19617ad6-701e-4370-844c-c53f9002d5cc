package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignFlowConstant.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.StampConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractDataSignTypeCollectorTest {

    private WmEcontractDataSignTypeCollector collector;

    private EcontractBatchContextBo originContext;

    private EcontractBatchMiddleBo middleContext;

    private EcontractBatchBo targetContext;

    @Mock
    private SignTemplateEnum mockTemplateNoStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiMtStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiQdbMtStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiQdbMtShanghaistamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiQdbMtShanghaiskuaiAgentStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiQdbMtShanghaiskuaiShenzhenbaishouAgentStamp;

    @Mock
    private SignTemplateEnum mockTemplateInvalidStamp;

    @Before
    public void setUp() {
        collector = new WmEcontractDataSignTypeCollector();
        originContext = new EcontractBatchContextBo();
        middleContext = new EcontractBatchMiddleBo();
        targetContext = new EcontractBatchBo();
        // Setup mock templates with different stamp combinations
        when(mockTemplatePoiStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP));
        when(mockTemplatePoiMtStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SANKUAI_STAMP));
        when(mockTemplatePoiQdbMtStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP));
        when(mockTemplatePoiQdbMtShanghaistamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP));
        when(mockTemplatePoiQdbMtShanghaiskuaiAgentStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.AGENT_STAMP));
        when(mockTemplatePoiQdbMtShanghaiskuaiShenzhenbaishouAgentStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP, StampConstant.SHENZHENBAISHOU_STAMP, StampConstant.AGENT_STAMP));
        when(mockTemplateInvalidStamp.getStampList()).thenReturn(Lists.newArrayList("INVALID_STAMP"));
    }

    /**
     * Test case for national subsidy delivery type
     */
    @Test
    public void testCollect_WhenNationalSubsidyDeliveryType_ThenSetCorrectType() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(NATIONAL_SUBSIDY_PERFORMANCE_DELIVERY_FLOW_TEMPLATE, targetContext.getEcontractType());
    }

    /**
     * Test case for empty stamp list
     */
    @Test
    public void testCollect_WhenEmptyStampList_ThenSetNoStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        middleContext.setPdfEnumList(new ArrayList<>());
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(NO_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for single POI stamp
     */
    @Test
    public void testCollect_WhenSinglePoiStamp_ThenSetPoiStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for POI and MT stamps combination
     */
    @Test
    public void testCollect_WhenPoiAndMtStamps_ThenSetPoiMtStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiMtStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_MT_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for POI, QDB and MT stamps combination
     */
    @Test
    public void testCollect_WhenPoiQdbMtStamps_ThenSetPoiQdbMtStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiQdbMtStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_QDB_MT_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for POI, QDB, MT and Shanghai Sankuai stamps
     */
    @Test
    public void testCollect_WhenFourStamps_ThenSetPoiQdbMtShanghaistamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiQdbMtShanghaistamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_QDB_MT_SHANGHAISANKUAI_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for five stamps combination
     */
    @Test
    public void testCollect_WhenFiveStamps_ThenSetPoiQdbMtShanghaiskuaiAgentStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiQdbMtShanghaiskuaiAgentStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_QDB_MT_SHANGHAISANKUAI_AGENT_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for six stamps combination
     */
    @Test
    public void testCollect_WhenSixStamps_ThenSetAllStampsType() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiQdbMtShanghaiskuaiShenzhenbaishouAgentStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_QDB_MT_SHANGHAISANKUAI_SHENZHENBAISHOU_AGENT_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for POI and QDB stamps combination
     */
    @Test
    public void testCollect_WhenPoiQdbStamps_ThenSetPoiQdbStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        when(mockTemplatePoiQdbStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP));
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiQdbStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_QDB_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for POI and Shanghai Sankuai stamps combination
     */
    @Test
    public void testCollect_WhenPoiShanghaistamps_ThenSetPoiShStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        when(mockTemplatePoiShStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP));
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiShStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_SH_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for POI and B2C stamps combination
     */
    @Test
    public void testCollect_WhenPoiB2cStamps_ThenSetPoiB2cStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        when(mockTemplatePoiB2cStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.MED_B2C_STAMP));
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiB2cStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_B2C_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for invalid stamp combination
     */
    @Test(expected = WmCustomerException.class)
    public void testCollect_WhenInvalidStampCombination_ThenThrowException() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        when(mockTemplateInvalidStamp.getStampList()).thenReturn(Lists.newArrayList("INVALID_STAMP_TYPE"));
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplateInvalidStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert is handled by expected exception
    }

    /**
     * Test case for null context
     */
    @Test(expected = NullPointerException.class)
    public void testCollect_WhenNullContext_ThenThrowException() throws Throwable {
        // act
        collector.collect(null, middleContext, targetContext);
        // assert is handled by expected exception
    }

    /**
     * Test case for null middle context
     */
    @Test(expected = NullPointerException.class)
    public void testCollect_WhenNullMiddleContext_ThenThrowException() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        // act
        collector.collect(originContext, null, targetContext);
        // assert is handled by expected exception
    }

    /**
     * Test case for POI, QDB and Shanghai Sankuai stamps
     */
    @Test
    public void testCollect_WhenPoiQdbShStamps_ThenSetPoiQdbShStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        when(mockTemplatePoiQdbShStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP));
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiQdbShStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_QDB_SH_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for POI, QDB and B2C stamps
     */
    @Test
    public void testCollect_WhenPoiQdbB2cStamps_ThenSetPoiB2cQdbStamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        when(mockTemplatePoiQdbB2cStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.QDB_STAMP, StampConstant.MED_B2C_STAMP));
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiQdbB2cStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_B2C_QDB_STAMP, targetContext.getEcontractType());
    }

    /**
     * Test case for POI, MT and Shanghai Sankuai stamps
     */
    @Test
    public void testCollect_WhenPoiMtShStamps_ThenSetPoiMtShanghaistamp() throws Throwable {
        // arrange
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        when(mockTemplatePoiMtShStamp.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP, StampConstant.SANKUAI_STAMP, StampConstant.SHANGHAI_SANKUAI_STAMP));
        middleContext.setPdfEnumList(Lists.newArrayList(mockTemplatePoiMtShStamp));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        assertEquals(POI_MT_SHANGHAISANKUAI_STAMP, targetContext.getEcontractType());
    }

    @Mock
    private SignTemplateEnum mockTemplatePoiQdbStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiShStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiB2cStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiQdbShStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiQdbB2cStamp;

    @Mock
    private SignTemplateEnum mockTemplatePoiMtShStamp;
}
