package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.adapter.WmAorServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.service.customer.dto.CustomerMscUsedPoiDetailDTO;
import com.sankuai.meituan.waimai.customer.service.customer.dto.MscPoiInfoDTO;
import com.sankuai.meituan.waimai.customer.service.fullstatus.WmCustomerBaseStatusServiceImpl;
import com.sankuai.meituan.waimai.infra.domain.WmUniAor;
import com.sankuai.meituan.waimai.infra.domain.WmUniAorLocationInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)

public class WmCustomerBaseStatusServiceTest extends BaseStaticMockTest {
    @InjectMocks
    private WmCustomerBaseStatusServiceImpl service;

    @Mock
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Mock
    private WmCustomerService wmCustomerService;

    @Mock
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Mock
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;


    /**
     * 测试门店未找到的情况
     */
    @Test
    public void testCheckMscPoiCntRuleV2_StoreNotFound() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        Long wmPoiId = 1L;
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(null);

        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertEquals("门店未找到", result);
    }

    /**
     * 测试子门店直接返回的情况
     */
    @Test
    public void testCheckMscPoiCntRuleV2_SubStoreDirectReturn() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        Long wmPoiId = 1L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("2,3");
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSubPoiTagId).thenReturn(Arrays.asList(2, 3));
        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertNull(result);
    }

    // 其他测试用例按照上述模式继续添加，覆盖所有分支和路径


    /**
     * 测试美食城客户档口数量未维护的情况
     */
    @Test
    public void testCheckMscPoiCntRuleV2_FoodCityPoiCountNotMaintained() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setMtCustomerId(123L);
        Long wmPoiId = 1L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("4,5");
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = new CustomerRealTypeSpInfoBo();
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(customerRealTypeSpInfoBo);
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSubPoiTagId).thenReturn(Arrays.asList(2, 3));

        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertEquals("申请上线门店的资质对应的美食城客户档口数量未维护，不可上线，对应美食城客户ID为123", result);
    }

    /**
     * 测试美食城客户档口数量已超上限的情况
     */
    @Test
    public void testCheckMscPoiCntRuleV2_FoodCityPoiCountExceedLimit() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setMtCustomerId(123L);
        long wmPoiId = 3L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("4,5");
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = new CustomerRealTypeSpInfoBo();
        customerRealTypeSpInfoBo.setFoodCityPoiCount(1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(customerRealTypeSpInfoBo);
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = new CustomerMscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(2);
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO mscPoiInfoDTO = new MscPoiInfoDTO();
        mscPoiInfoDTO.setPoiId(1L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO);
        MscPoiInfoDTO mscPoiInfoDTO2 = new MscPoiInfoDTO();
        mscPoiInfoDTO2.setPoiId(2L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO2);
        mscUsedPoiDetailDTO.setMscPoiInfoDTOList(mscPoiInfoDTOList);
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSubPoiTagId).thenReturn(Arrays.asList(2, 3));
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerBasicBo.getId())).thenReturn(mscUsedPoiDetailDTO);

        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertEquals("申请上线门店对应的美食城客户档口数量已超上限或上线后将超过上限，不许门店上线，对应美食城客户ID为123", result);
    }

    /**
     * 边界值测试
     */
    @Test
    public void testCheckMscPoiCntRuleV2_FoodCityPoiCountBond() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setMtCustomerId(123L);
        long wmPoiId = 1L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("4,5");
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = new CustomerRealTypeSpInfoBo();
        customerRealTypeSpInfoBo.setFoodCityPoiCount(2);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(customerRealTypeSpInfoBo);
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = new CustomerMscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(2);
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO mscPoiInfoDTO = new MscPoiInfoDTO();
        mscPoiInfoDTO.setPoiId(1L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO);
        MscPoiInfoDTO mscPoiInfoDTO2 = new MscPoiInfoDTO();
        mscPoiInfoDTO2.setPoiId(2L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO2);
        mscUsedPoiDetailDTO.setMscPoiInfoDTOList(mscPoiInfoDTOList);
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSubPoiTagId).thenReturn(Arrays.asList(2, 3));
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerBasicBo.getId())).thenReturn(mscUsedPoiDetailDTO);

        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);
        assertNull(result);
    }

    /**
     * 测试资质共用标签不存在的情况
     */
    @Test
    public void testCheckMscPoiCntRuleV2_NoQuaComCustomerTag() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setMtCustomerId(123L);
        Long wmPoiId = 1L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("4,5");
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = new CustomerRealTypeSpInfoBo();
        customerRealTypeSpInfoBo.setFoodCityPoiCount(3);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(customerRealTypeSpInfoBo);
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = new CustomerMscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(2);
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO mscPoiInfoDTO = new MscPoiInfoDTO();
        mscPoiInfoDTO.setPoiId(1L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO);
        MscPoiInfoDTO mscPoiInfoDTO2 = new MscPoiInfoDTO();
        mscPoiInfoDTO2.setPoiId(2L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO2);
        mscUsedPoiDetailDTO.setMscPoiInfoDTOList(mscPoiInfoDTOList);
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        when(MccCustomerConfig.getSubPoiTagId()).thenReturn(Arrays.asList(2, 3));
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerBasicBo.getId())).thenReturn(mscUsedPoiDetailDTO);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerBasicBo.getMtCustomerId())).thenReturn(false);

        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertNull(result);
    }

    /**
     * 测试资质共用标签存在，未查询到蜂窝信息
     */
    @Test
    public void testCheckMscPoiCntRuleV2_QuaComCustomerTagAorNull() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setMtCustomerId(123L);
        Long wmPoiId = 1L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("4,5");
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = new CustomerRealTypeSpInfoBo();
        customerRealTypeSpInfoBo.setFoodCityPoiCount(3);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(customerRealTypeSpInfoBo);
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = new CustomerMscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(2);
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO mscPoiInfoDTO = new MscPoiInfoDTO();
        mscPoiInfoDTO.setPoiId(1L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO);
        MscPoiInfoDTO mscPoiInfoDTO2 = new MscPoiInfoDTO();
        mscPoiInfoDTO2.setPoiId(2L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO2);
        mscUsedPoiDetailDTO.setMscPoiInfoDTOList(mscPoiInfoDTOList);
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        when(MccCustomerConfig.getSubPoiTagId()).thenReturn(Arrays.asList(2, 3));
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerBasicBo.getId())).thenReturn(mscUsedPoiDetailDTO);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerBasicBo.getMtCustomerId())).thenReturn(true);

        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(null);

        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertNull(result);
    }

    /**
     * 测试资质共用标签存在，不在北京
     */
    @Test
    public void testCheckMscPoiCntRuleV2_QuaComCustomerTagNotBeijing() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setMtCustomerId(123L);
        Long wmPoiId = 1L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("4,5");
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = new CustomerRealTypeSpInfoBo();
        customerRealTypeSpInfoBo.setFoodCityPoiCount(3);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(customerRealTypeSpInfoBo);
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = new CustomerMscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(2);
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO mscPoiInfoDTO = new MscPoiInfoDTO();
        mscPoiInfoDTO.setPoiId(1L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO);
        MscPoiInfoDTO mscPoiInfoDTO2 = new MscPoiInfoDTO();
        mscPoiInfoDTO2.setPoiId(2L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO2);
        mscUsedPoiDetailDTO.setMscPoiInfoDTOList(mscPoiInfoDTOList);
        WmUniAor wmUniAor = new WmUniAor();
        WmUniAorLocationInfo wmUniAorLocationInfo = new WmUniAorLocationInfo();
        wmUniAorLocationInfo.setLevel2CityIds(Arrays.asList(2));//非北京
        wmUniAor.setLocationInfos(wmUniAorLocationInfo);
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSubPoiTagId).thenReturn(Arrays.asList(2, 3));
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerBasicBo.getId())).thenReturn(mscUsedPoiDetailDTO);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerBasicBo.getMtCustomerId())).thenReturn(true);
        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(wmUniAor);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));


        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertNull(result);
    }

    /**
     * 测试资质共用标签存在，且北京
     */
    @Test
    public void testCheckMscPoiCntRuleV2_QuaComCustomerTagBeijing() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setMtCustomerId(123L);
        Long wmPoiId = 4L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("4,5");
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = new CustomerRealTypeSpInfoBo();
        customerRealTypeSpInfoBo.setFoodCityPoiCount(5);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(customerRealTypeSpInfoBo);
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = new CustomerMscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(3);
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO mscPoiInfoDTO = new MscPoiInfoDTO();
        mscPoiInfoDTO.setPoiId(1L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO);
        MscPoiInfoDTO mscPoiInfoDTO2 = new MscPoiInfoDTO();
        mscPoiInfoDTO2.setPoiId(2L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO2);
        MscPoiInfoDTO mscPoiInfoDTO3 = new MscPoiInfoDTO();
        mscPoiInfoDTO3.setPoiId(3L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO3);
        mscUsedPoiDetailDTO.setMscPoiInfoDTOList(mscPoiInfoDTOList);
        WmUniAor wmUniAor = new WmUniAor();
        WmUniAorLocationInfo wmUniAorLocationInfo = new WmUniAorLocationInfo();
        wmUniAorLocationInfo.setLevel2CityIds(Arrays.asList(1));//北京
        wmUniAor.setLocationInfos(wmUniAorLocationInfo);
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSubPoiTagId).thenReturn(Arrays.asList(2, 3));
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerBasicBo.getId())).thenReturn(mscUsedPoiDetailDTO);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerBasicBo.getMtCustomerId())).thenReturn(true);
        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(wmUniAor);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));

        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertEquals(result, "申请上线门店的资质对应的客户为“资质共用特殊场景”的美食城客户，且客户物理城市为“北京”，档口数量上限为3。本次门店上线将超过此数量，不可上线");
    }

    /**
     * 测试资质共用标签存在，且北京，且校验通过
     */
    @Test
    public void testCheckMscPoiCntRuleV2_QuaComCustomerTagBeijingPass() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setMtCustomerId(123L);
        Long wmPoiId = 1L;
        WmPoiAggre wmPoiAggre = new WmPoiAggre();
        wmPoiAggre.setLabel_ids("4,5");
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = new CustomerRealTypeSpInfoBo();
        customerRealTypeSpInfoBo.setFoodCityPoiCount(3);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(customerRealTypeSpInfoBo);
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTO = new CustomerMscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(3);
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        MscPoiInfoDTO mscPoiInfoDTO = new MscPoiInfoDTO();
        mscPoiInfoDTO.setPoiId(1L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO);
        MscPoiInfoDTO mscPoiInfoDTO2 = new MscPoiInfoDTO();
        mscPoiInfoDTO2.setPoiId(2L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO2);
        MscPoiInfoDTO mscPoiInfoDTO3 = new MscPoiInfoDTO();
        mscPoiInfoDTO3.setPoiId(3L);
        mscPoiInfoDTOList.add(mscPoiInfoDTO3);
        mscUsedPoiDetailDTO.setMscPoiInfoDTOList(mscPoiInfoDTOList);
        WmUniAor wmUniAor = new WmUniAor();
        WmUniAorLocationInfo wmUniAorLocationInfo = new WmUniAorLocationInfo();
        wmUniAorLocationInfo.setLevel2CityIds(Arrays.asList(1));//北京
        wmUniAor.setLocationInfos(wmUniAorLocationInfo);
        when(wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(wmPoiId)).thenReturn(wmPoiAggre);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSubPoiTagId).thenReturn(Arrays.asList(2, 3));
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerBasicBo.getId())).thenReturn(mscUsedPoiDetailDTO);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerBasicBo.getMtCustomerId())).thenReturn(true);
        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(wmUniAor);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));



        // act
        String result = (String) ReflectionTestUtils.invokeMethod(service, "checkMscPoiCntRuleV2", wmCustomerBasicBo, wmPoiId);

        // assert
        assertNull(result);
    }
}
