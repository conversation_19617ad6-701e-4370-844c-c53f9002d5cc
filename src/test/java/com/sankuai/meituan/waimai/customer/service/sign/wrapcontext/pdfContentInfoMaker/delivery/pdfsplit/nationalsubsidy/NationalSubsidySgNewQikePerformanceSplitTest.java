package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit.nationalsubsidy;

import static org.junit.Assert.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidySgNewQikePerformanceSplitTest {

    @InjectMocks
    private NationalSubsidySgNewQikePerformanceSplit split;

    /**
     * Test when both conditions are met and list doesn't exist
     */
    @Test
    public void testSplit_WhenConditionsMet_AndListNotExists() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        // SHANGOU_QIKE_V2
        deliveryInfoBo.setFeeMode("13");
        deliveryInfoBo.setSupportSGV2_2DeliveryXQK(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid");
        deliveryInfoBo.setWmPoiId("123");
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        String key = DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName();
        assertTrue("Map should contain the key", pdfDataMap.containsKey(key));
        List<String> resultList = pdfDataMap.get(key);
        assertNotNull("Result list should not be null", resultList);
        assertEquals("List should contain one element", 1, resultList.size());
        assertEquals("UUID should match", "test-uuid", resultList.get(0));
    }

    /**
     * Test when both conditions are met and list already exists
     */
    @Test
    public void testSplit_WhenConditionsMet_AndListExists() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        // SHANGOU_QIKE_V2
        deliveryInfoBo.setFeeMode("13");
        deliveryInfoBo.setSupportSGV2_2DeliveryXQK(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid");
        deliveryInfoBo.setWmPoiId("123");
        String key = DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        List<String> existingList = Lists.newArrayList("existing-uuid");
        pdfDataMap.put(key, existingList);
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        List<String> resultList = pdfDataMap.get(key);
        assertNotNull("Result list should not be null", resultList);
        assertEquals("List should contain two elements", 2, resultList.size());
        assertTrue("List should contain existing UUID", resultList.contains("existing-uuid"));
        assertTrue("List should contain new UUID", resultList.contains("test-uuid"));
    }

    /**
     * Test when fee mode is not SHANGOU_QIKE_V2
     */
    @Test
    public void testSplit_WhenFeeModeNotMatch() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        // Not SHANGOU_QIKE_V2
        deliveryInfoBo.setFeeMode("1");
        deliveryInfoBo.setSupportSGV2_2DeliveryXQK(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid");
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        String key = DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName();
        assertFalse("Map should not contain the key", pdfDataMap.containsKey(key));
    }

    /**
     * Test when supportSGV2_2DeliveryXQK is not SUPPORT_MARK
     */
    @Test
    public void testSplit_WhenSupportMarkNotMatch() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        // SHANGOU_QIKE_V2
        deliveryInfoBo.setFeeMode("13");
        deliveryInfoBo.setSupportSGV2_2DeliveryXQK("NOT_SUPPORT");
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid");
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        String key = DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName();
        assertFalse("Map should not contain the key", pdfDataMap.containsKey(key));
    }
}
