package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for WmEcontractDataFlowCollector
 */
@RunWith(MockitoJUnitRunner.class)
public class WmEcontractDataFlowCollectorTest {

    @InjectMocks
    private WmEcontractDataFlowCollector collector;

    @Mock
    private EcontractBatchContextBo originContext;

    /**
     * Test normal case with non-empty pdfEnumList
     */
    @Test
    public void testCollect_WithNonEmptyPdfEnumList() throws Throwable {
        // arrange
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        List<SignTemplateEnum> pdfEnumList = Lists.newArrayList(SignTemplateEnum.C1CONTRACT_INFO_V3, SignTemplateEnum.C2CONTRACT_INFO_V3);
        middleContext.setPdfEnumList(pdfEnumList);
        EcontractBatchBo targetContext = new EcontractBatchBo();
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        List<String> flowList = targetContext.getFlowList();
        assertNotNull("TabPdfMap should not be null", tabPdfMap);
        assertNotNull("FlowList should not be null", flowList);
        assertFalse("FlowList should not be empty", flowList.isEmpty());
        assertEquals("FlowList should contain unique tabs", tabPdfMap.keySet().size(), flowList.size());
    }

    /**
     * Test case with empty pdfEnumList
     */
    @Test
    public void testCollect_WithEmptyPdfEnumList() throws Throwable {
        // arrange
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setPdfEnumList(new ArrayList<>());
        EcontractBatchBo targetContext = new EcontractBatchBo();
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        List<String> flowList = targetContext.getFlowList();
        assertNotNull("TabPdfMap should not be null", tabPdfMap);
        assertNotNull("FlowList should not be null", flowList);
        assertTrue("FlowList should be empty", flowList.isEmpty());
        assertTrue("TabPdfMap should be empty", tabPdfMap.isEmpty());
    }

    /**
     * Test case with null middleContext
     */
    @Test(expected = NullPointerException.class)
    public void testCollect_WithNullMiddleContext() throws Throwable {
        // arrange
        EcontractBatchBo targetContext = new EcontractBatchBo();
        // act
        collector.collect(originContext, null, targetContext);
        // assert - expect NPE
    }

    /**
     * Test case with null targetContext
     */
    @Test(expected = NullPointerException.class)
    public void testCollect_WithNullTargetContext() throws Throwable {
        // arrange
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setPdfEnumList(new ArrayList<>());
        // act
        collector.collect(originContext, middleContext, null);
        // assert - expect NPE
    }

    /**
     * Test case with multiple templates having same tab
     */
    @Test
    public void testCollect_WithMultipleTemplatesInSameTab() throws Throwable {
        // arrange
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        List<SignTemplateEnum> pdfEnumList = Lists.newArrayList(SignTemplateEnum.C1CONTRACT_INFO_V3, SignTemplateEnum.C1CONTRACT_INFO_V4);
        middleContext.setPdfEnumList(pdfEnumList);
        EcontractBatchBo targetContext = new EcontractBatchBo();
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        List<String> flowList = targetContext.getFlowList();
        assertNotNull("TabPdfMap should not be null", tabPdfMap);
        assertNotNull("FlowList should not be null", flowList);
        assertEquals("FlowList should contain unique tabs", 1, flowList.size());
        assertTrue("TabPdfMap should contain multiple templates for same tab", tabPdfMap.values().iterator().next().size() > 1);
    }
}
