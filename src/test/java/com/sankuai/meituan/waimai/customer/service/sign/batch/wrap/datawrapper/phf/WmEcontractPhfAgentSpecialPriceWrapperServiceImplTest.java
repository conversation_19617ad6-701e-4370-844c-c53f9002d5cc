package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfAgentSpecialPriceWrapperServiceImplTest extends BaseStaticMockTest {

    private EcontractBatchContextBo contextBo;

    private EcontractTaskBo taskBo;

    private EcontractBatchDeliveryInfoBo batchDeliveryInfoBo;

    private EcontractCustomerInfoBo customerInfoBo;

    @InjectMocks
    private WmEcontractPhfAgentSpecialPriceWrapperServiceImpl wmEcontractPhfAgentSpecialPriceWrapperService;

    @Mock
    private EcontractDeliveryPhfInfoBo infoBo;

    @Before
    public void setUp() {
        super.init();
        mccConfigMockedStatic.when(MccConfig::getPhfContractNameInFormal).thenReturn("美团拼好饭服务费收费协议");

        contextBo = new EcontractBatchContextBo();
        taskBo = new EcontractTaskBo();
        batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        customerInfoBo = new EcontractCustomerInfoBo();
        contextBo.setCustomerInfoBo(customerInfoBo);
        customerInfoBo.setCustomerName("Test Customer");
        Map<Long, EcontractTaskBo> taskMap = Maps.newHashMap();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName());
    }

    /**
     * Test successful case with valid delivery info
     */
    @Test
    public void testWrapSuccess() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        EcontractDeliveryPhfInfoBo phfInfoBo = new EcontractDeliveryPhfInfoBo();
        phfInfoBo.setWmPoiName("Test POI");
        phfInfoBo.setSpecialPrice("100");
        deliveryInfoBo.setEcontractDeliveryPhfInfoBo(phfInfoBo);
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList(deliveryInfoBo));
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        // act
        List<PdfContentInfoBo> result = wmEcontractPhfAgentSpecialPriceWrapperService.wrap(contextBo);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        PdfContentInfoBo pdfInfo = result.get(0);
        assertEquals("适用于 Test POI", pdfInfo.getContractDesc());
        Map<String, String> pdfMap = pdfInfo.getPdfMetaContent();
        assertEquals("Test POI", pdfMap.get("wmPoiName"));
        assertEquals("100", pdfMap.get("specialPrice"));
        assertEquals("Test Customer", pdfMap.get("partAName"));
    }

    /**
     * Test case when delivery info list is empty
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapWithEmptyDeliveryInfo() throws Throwable {
        // arrange
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList());
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        // act
        wmEcontractPhfAgentSpecialPriceWrapperService.wrap(contextBo);
    }

    /**
     * Test case when task selection fails
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapWithInvalidTask() throws Throwable {
        // arrange
        contextBo.setTaskIdAndTaskMap(Maps.newHashMap());
        // act
        wmEcontractPhfAgentSpecialPriceWrapperService.wrap(contextBo);
    }

    /**
     * Test case with invalid JSON format in applyContext
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapWithInvalidApplyContext() throws Throwable {
        // arrange
        EcontractBatchDeliveryInfoBo emptyBo = new EcontractBatchDeliveryInfoBo();
        taskBo.setApplyContext(JSON.toJSONString(emptyBo));
        // act
        wmEcontractPhfAgentSpecialPriceWrapperService.wrap(contextBo);
    }

    /**
     * Tests generatePdfMetaContent method under normal conditions.
     */
    @Test
    public void testGeneratePdfMetaContentNormal() throws Throwable {
        // Arrange
        when(infoBo.getWmPoiName()).thenReturn("testPoiName");
        when(infoBo.getSpecialPrice()).thenReturn("100");
        customerInfoBo.setCustomerName("testCustomerName");
        // Act
        Map<String, String> result = wmEcontractPhfAgentSpecialPriceWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        // Assert
        assertEquals("testPoiName", result.get("wmPoiName"));
        assertEquals("100", result.get("specialPrice"));
        assertEquals("testCustomerName", result.get("partAName"));
    }

    /**
     * Tests generatePdfMetaContent method under exceptional conditions.
     */
    @Test(expected = NullPointerException.class)
    public void testGeneratePdfMetaContentException() throws Throwable {
        // Arrange
        // Act
        wmEcontractPhfAgentSpecialPriceWrapperService.generatePdfMetaContent(null, null);
    }

    /**
     * Tests generatePdfMetaContent method under boundary conditions.
     */
    @Test
    public void testGeneratePdfMetaContentBoundary() throws Throwable {
        // Arrange
        when(infoBo.getWmPoiName()).thenReturn(null);
        when(infoBo.getSpecialPrice()).thenReturn(null);
        customerInfoBo.setCustomerName(null);
        // Act
        Map<String, String> result = wmEcontractPhfAgentSpecialPriceWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        // Assert
        assertNull(result.get("wmPoiName"));
        assertNull(result.get("specialPrice"));
        assertNull(result.get("partAName"));
    }
}