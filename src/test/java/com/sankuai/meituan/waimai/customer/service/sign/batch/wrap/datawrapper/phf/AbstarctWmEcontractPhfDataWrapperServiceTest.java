package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDeliveryTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryPhfInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstarctWmEcontractPhfDataWrapperServiceTest {

    private final TestWmEcontractPhfDataWrapperService service = new TestWmEcontractPhfDataWrapperService();

    private static final String TEST_FORMAT = "yyyy-MM-dd";

    private static final String EXPECTED_DATE_STRING = "2022-01-01";

    private static class TestWmEcontractPhfDataWrapperService extends AbstarctWmEcontractPhfDataWrapperService {

        @Override
        public java.util.Map<String, String> generatePdfMetaContent(EcontractCustomerInfoBo customerInfoBo, EcontractDeliveryPhfInfoBo infoBo) throws WmCustomerException {
            return new HashMap<>();
        }
    }

    /**
     * Test normal case with single group
     */
    @Test
    public void testExtractWmPoiInfoBoGroupList_NormalSingleGroup() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo1 = new EcontractDeliveryInfoBo();
        infoBo1.setWmPoiId("1");
        EcontractDeliveryInfoBo infoBo2 = new EcontractDeliveryInfoBo();
        infoBo2.setWmPoiId("2");
        List<EcontractDeliveryInfoBo> infoBoList = Arrays.asList(infoBo1, infoBo2);
        List<List<Long>> wmPoiIdGroupList = Collections.singletonList(Arrays.asList(1L, 2L));
        // act
        List<List<EcontractDeliveryInfoBo>> result = service.extractWmPoiInfoBoGroupList(infoBoList, wmPoiIdGroupList);
        // assert
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).size());
        assertEquals("1", result.get(0).get(0).getWmPoiId());
        assertEquals("2", result.get(0).get(1).getWmPoiId());
    }

    /**
     * Test normal case with multiple groups
     */
    @Test
    public void testExtractWmPoiInfoBoGroupList_NormalMultipleGroups() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo1 = new EcontractDeliveryInfoBo();
        infoBo1.setWmPoiId("1");
        EcontractDeliveryInfoBo infoBo2 = new EcontractDeliveryInfoBo();
        infoBo2.setWmPoiId("2");
        List<EcontractDeliveryInfoBo> infoBoList = Arrays.asList(infoBo1, infoBo2);
        List<List<Long>> wmPoiIdGroupList = Arrays.asList(Collections.singletonList(1L), Collections.singletonList(2L));
        // act
        List<List<EcontractDeliveryInfoBo>> result = service.extractWmPoiInfoBoGroupList(infoBoList, wmPoiIdGroupList);
        // assert
        assertEquals(2, result.size());
        assertEquals(1, result.get(0).size());
        assertEquals(1, result.get(1).size());
        assertEquals("1", result.get(0).get(0).getWmPoiId());
        assertEquals("2", result.get(1).get(0).getWmPoiId());
    }

    /**
     * Test empty input lists
     */
    @Test
    public void testExtractWmPoiInfoBoGroupList_EmptyLists() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> infoBoList = Collections.emptyList();
        List<List<Long>> wmPoiIdGroupList = Collections.emptyList();
        // act
        List<List<EcontractDeliveryInfoBo>> result = service.extractWmPoiInfoBoGroupList(infoBoList, wmPoiIdGroupList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test missing wmPoiId case
     */
    @Test(expected = WmCustomerException.class)
    public void testExtractWmPoiInfoBoGroupList_MissingWmPoiId() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo = new EcontractDeliveryInfoBo();
        infoBo.setWmPoiId("1");
        List<EcontractDeliveryInfoBo> infoBoList = Collections.singletonList(infoBo);
        List<List<Long>> wmPoiIdGroupList = Collections.singletonList(Arrays.asList(1L, 2L));
        // act
        service.extractWmPoiInfoBoGroupList(infoBoList, wmPoiIdGroupList);
        // assert: expect WmCustomerException
    }

    /**
     * Test null input parameters
     */
    @Test(expected = NullPointerException.class)
    public void testExtractWmPoiInfoBoGroupList_NullInputs() throws Throwable {
        // act
        service.extractWmPoiInfoBoGroupList(null, null);
        // assert: expect NullPointerException
    }

    /**
     * Test when delivery type is PHF_SELF
     * Expected: Should return "SELF"
     */
    @Test
    public void testGetLogiticsType_WhenPhfSelf_ReturnsSelf() throws Throwable {
        // arrange
        EcontractDeliveryTypeEnum deliveryTypeEnum = EcontractDeliveryTypeEnum.PHF_SELF;
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogiticsType(deliveryTypeEnum);
        // assert
        assertEquals("SELF", result);
    }

    /**
     * Test when delivery type is PHF_SCHOOL_AGGR
     * Expected: Should return "SCHOOL_AGGR"
     */
    @Test
    public void testGetLogiticsType_WhenPhfSchoolAggr_ReturnsSchoolAggr() throws Throwable {
        // arrange
        EcontractDeliveryTypeEnum deliveryTypeEnum = EcontractDeliveryTypeEnum.PHF_SCHOOL_AGGR;
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogiticsType(deliveryTypeEnum);
        // assert
        assertEquals("SCHOOL_AGGR", result);
    }

    /**
     * Test when delivery type is PHS_VIRTUAL
     * Expected: Should return "VIRTUAL_PHS"
     */
    @Test
    public void testGetLogiticsType_WhenPhsVirtual_ReturnsVirtualPhs() throws Throwable {
        // arrange
        EcontractDeliveryTypeEnum deliveryTypeEnum = EcontractDeliveryTypeEnum.PHS_VIRTUAL;
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogiticsType(deliveryTypeEnum);
        // assert
        assertEquals("VIRTUAL_PHS", result);
    }

    /**
     * Test when delivery type is JHS
     * Expected: Should return "JHS"
     */
    @Test
    public void testGetLogiticsType_WhenJhs_ReturnsJhs() throws Throwable {
        // arrange
        EcontractDeliveryTypeEnum deliveryTypeEnum = EcontractDeliveryTypeEnum.JHS;
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogiticsType(deliveryTypeEnum);
        // assert
        assertEquals("JHS", result);
    }

    /**
     * Test when delivery type is PHS_FORMAL
     * Expected: Should return "PHS"
     */
    @Test
    public void testGetLogiticsType_WhenPhsFormal_ReturnsPhs() throws Throwable {
        // arrange
        EcontractDeliveryTypeEnum deliveryTypeEnum = EcontractDeliveryTypeEnum.PHS_FORMAL;
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogiticsType(deliveryTypeEnum);
        // assert
        assertEquals("PHS", result);
    }

    /**
     * Test when delivery type is null
     * Expected: Should return empty string
     */
    @Test
    public void testGetLogiticsType_WhenNull_ReturnsEmpty() throws Throwable {
        // arrange
        EcontractDeliveryTypeEnum deliveryTypeEnum = null;
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogiticsType(deliveryTypeEnum);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    @Test
    public void testGetLogisticsDesc_WhenPhfSelf_ShouldReturnSelfDelivery() throws Throwable {
        EcontractDeliveryTypeEnum deliveryType = EcontractDeliveryTypeEnum.PHF_SELF;
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogisticsDesc(deliveryType);
        assertEquals("商家自配", result);
    }

    @Test
    public void testGetLogisticsDesc_WhenPhfSchoolAggr_ShouldReturnSchoolAggregation() throws Throwable {
        EcontractDeliveryTypeEnum deliveryType = EcontractDeliveryTypeEnum.PHF_SCHOOL_AGGR;
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogisticsDesc(deliveryType);
        assertEquals("校园聚合配", result);
    }

    @Test
    public void testGetLogisticsDesc_WhenPhsVirtual_ShouldReturnVirtualVersion() throws Throwable {
        EcontractDeliveryTypeEnum deliveryType = EcontractDeliveryTypeEnum.PHS_VIRTUAL;
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogisticsDesc(deliveryType);
        assertEquals("拼好送基础版", result);
    }

    @Test
    public void testGetLogisticsDesc_WhenJhs_ShouldReturnJhs() throws Throwable {
        EcontractDeliveryTypeEnum deliveryType = EcontractDeliveryTypeEnum.JHS;
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogisticsDesc(deliveryType);
        assertEquals("聚好送", result);
    }

    @Test
    public void testGetLogisticsDesc_WhenPhsFormal_ShouldReturnFormalVersion() throws Throwable {
        EcontractDeliveryTypeEnum deliveryType = EcontractDeliveryTypeEnum.PHS_FORMAL;
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogisticsDesc(deliveryType);
        assertEquals("拼好送正式版", result);
    }

    @Test
    public void testGetLogisticsDesc_WhenNull_ShouldReturnEmptyString() throws Throwable {
        EcontractDeliveryTypeEnum deliveryType = null;
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogisticsDesc(deliveryType);
        assertEquals("", result);
    }

    @Test
    public void testGetLogisticsDesc_WhenUnsupportedType_ShouldReturnEmptyString() throws Throwable {
        // Assuming NORMAL is considered an unsupported type for this context
        EcontractDeliveryTypeEnum deliveryType = EcontractDeliveryTypeEnum.NORMAL;
        String result = AbstarctWmEcontractPhfDataWrapperService.getLogisticsDesc(deliveryType);
        assertEquals("", result);
    }

    /**
     * Test case for dateToStr when date parameter is null
     */
    @Test
    public void testDateToStrWithNullDate() {
        // arrange
        Date date = null;
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.dateToStr(date, TEST_FORMAT);
        // assert
        assertEquals("Should return empty string for null date", StringUtils.EMPTY, result);
    }

    /**
     * Test case for dateToStr when format parameter is null
     */
    @Test
    public void testDateToStrWithNullFormat() {
        // arrange
        Date mockDate = mock(Date.class);
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.dateToStr(mockDate, null);
        // assert
        assertEquals("Should return empty string for null format", StringUtils.EMPTY, result);
        // Verify date was never used
        verify(mockDate, never()).getTime();
    }

    /**
     * Test case for dateToStr when format parameter is empty
     */
    @Test
    public void testDateToStrWithEmptyFormat() {
        // arrange
        Date mockDate = mock(Date.class);
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.dateToStr(mockDate, "");
        // assert
        assertEquals("Should return empty string for empty format", StringUtils.EMPTY, result);
        // Verify date was never used
        verify(mockDate, never()).getTime();
    }

    /**
     * Test case for dateToStr with valid parameters
     */
    @Test
    public void testDateToStrWithValidParameters() {
        // arrange
        Date mockDate = mock(Date.class);
        // 2022-01-01 00:00:00
        when(mockDate.getTime()).thenReturn(1640995200000L);
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.dateToStr(mockDate, TEST_FORMAT);
        // assert
        assertEquals("Should return correctly formatted date string", EXPECTED_DATE_STRING, result);
        // Verify date.getTime() was called once
        verify(mockDate, times(1)).getTime();
    }

    /**
     * Test case for dateToStr with valid parameters using real Date object
     */
    @Test
    public void testDateToStrWithValidParametersRealDate() {
        // arrange
        // 2022-01-01 00:00:00
        Date date = new Date(1640995200000L);
        // act
        String result = AbstarctWmEcontractPhfDataWrapperService.dateToStr(date, TEST_FORMAT);
        // assert
        assertEquals("Should return correctly formatted date string", EXPECTED_DATE_STRING, result);
    }
}