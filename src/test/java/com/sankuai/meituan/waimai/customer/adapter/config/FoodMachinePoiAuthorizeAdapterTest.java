package com.sankuai.meituan.waimai.customer.adapter.config;

import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.waimai.innovative.supplychain.client.contract.bean.CanSignFixSettlementContractRespDataDto;
import com.sankuai.waimai.innovative.supplychain.client.contract.bean.CanSignFixSettlementContractRespDto;
import com.sankuai.waimai.innovative.supplychain.client.contract.iface.SettlementPriceContractThriftService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * FoodMachinePoiAuthorizeAdapterTest
 *
 * @Author: wangyongfang
 * @Date: 2025-01-03
 */
public class FoodMachinePoiAuthorizeAdapterTest {


    @InjectMocks
    private FoodMachinePoiAuthorizeAdapter foodMachinePoiAuthorizeAdapter;

    @Mock
    private SettlementPriceContractThriftService.Iface settlementPriceContractThriftService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试客户授权成功
     */
    @Test
    public void testIsCustomerFoodMachinePoiAuthorizedSuccess() throws Exception {
        // arrange
        long customerId = 1L;
        int contractType = 2;
        CanSignFixSettlementContractRespDto respDto = new CanSignFixSettlementContractRespDto(0, "success", new CanSignFixSettlementContractRespDataDto(true));
        when(settlementPriceContractThriftService.canSignFixSettlementContract(customerId, String.valueOf(contractType))).thenReturn(respDto);

        // act
        Boolean result = foodMachinePoiAuthorizeAdapter.isCustomerFoodMachinePoiAuthorized(customerId, contractType);

        // assert
        Assert.assertTrue(result);
    }

    /**
     * 测试客户授权失败
     */
    @Test
    public void testIsCustomerFoodMachinePoiAuthorizedFail() throws Exception {
        // arrange
        long customerId = 1L;
        int contractType = 2;
        CanSignFixSettlementContractRespDto respDto = new CanSignFixSettlementContractRespDto(0, "success", new CanSignFixSettlementContractRespDataDto(false));
        when(settlementPriceContractThriftService.canSignFixSettlementContract(customerId, String.valueOf(contractType))).thenReturn(respDto);

        // act
        Boolean result = foodMachinePoiAuthorizeAdapter.isCustomerFoodMachinePoiAuthorized(customerId, contractType);

        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试客户未授权抛出异常
     */
    @Test(expected = WmCustomerException.class)
    public void testIsCustomerFoodMachinePoiAuthorizedUnauthorized() throws Exception {
        // arrange
        long customerId = 1L;
        int contractType = 2;
        CanSignFixSettlementContractRespDto respDto = new CanSignFixSettlementContractRespDto(1, "unauthorized", new CanSignFixSettlementContractRespDataDto(false));
        when(settlementPriceContractThriftService.canSignFixSettlementContract(customerId, String.valueOf(contractType))).thenReturn(respDto);

        // act
        foodMachinePoiAuthorizeAdapter.isCustomerFoodMachinePoiAuthorized(customerId, contractType);

        // assert is handled by the expected exception
        Assert.fail();
    }

    /**
     * 测试服务异常
     */
    @Test(expected = WmCustomerException.class)
    public void testIsCustomerFoodMachinePoiAuthorizedServiceException() throws Exception {
        // arrange
        long customerId = 1L;
        int contractType = 2;
        when(settlementPriceContractThriftService.canSignFixSettlementContract(customerId, String.valueOf(contractType))).thenThrow(new RuntimeException("Service exception"));

        // act
        foodMachinePoiAuthorizeAdapter.isCustomerFoodMachinePoiAuthorized(customerId, contractType);

        // assert is handled by the expected exception
        Assert.fail();
    }
}