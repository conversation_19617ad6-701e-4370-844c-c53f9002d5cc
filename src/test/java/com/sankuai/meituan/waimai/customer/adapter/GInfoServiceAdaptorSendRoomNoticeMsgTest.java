package com.sankuai.meituan.waimai.customer.adapter;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.xm.ginfo.thrift.GinfoOpenServiceI;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class GInfoServiceAdaptorSendRoomNoticeMsgTest {

    @InjectMocks
    private GInfoServiceAdaptor gInfoServiceAdaptor;

    @Mock
    private GinfoOpenServiceI.Iface ginfoOpenService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试sendRoomNoticeMsg方法，正常情况
     */
    @Test
    public void testSendRoomNoticeMsgNormal() throws Throwable {
        // arrange
        Long roomId = 1L;
        String msg = "test message";
        Long uid = 1L;
        // act
        gInfoServiceAdaptor.sendRoomNoticeMsg(roomId, msg, Arrays.asList(uid));
        // assert
        verify(ginfoOpenService, times(1)).sendTextNoticeToMembers(eq(0L), eq(roomId), eq(Arrays.asList(uid)), eq(msg), eq(1));
    }

    /**
     * 测试sendRoomNoticeMsg方法，异常情况
     */
    @Test(expected = WmCustomerException.class)
    public void testSendRoomNoticeMsgException() throws Throwable {
        // arrange
        Long roomId = 1L;
        String msg = "test message";
        Long uid = 1L;
        doThrow(new RuntimeException()).when(ginfoOpenService).sendTextNoticeToMembers(eq(0L), eq(roomId), eq(Arrays.asList(uid)), eq(msg), eq(1));
        // act
        gInfoServiceAdaptor.sendRoomNoticeMsg(roomId, msg, Arrays.asList(uid));
        // assert
        verify(ginfoOpenService, times(1)).sendTextNoticeToMembers(eq(0L), eq(roomId), eq(Arrays.asList(uid)), eq(msg), eq(1));
    }

    /**
     * 测试createOverCompanyRoom方法正常情况
     */
    @Test
    public void testCreateOverCompanyRoomNormal() throws Throwable {
        // arrange
        Long moderatorId = 1L;
        Set<Long> participantIds = new HashSet<>();
        participantIds.add(1L);
        String name = "test";
        Long expectedGroupId = 1001L;
        when(ginfoOpenService.createOverCompanyRoom(anyLong(), anySet(), anySet(), anyInt(), anyString(), anyBoolean())).thenReturn(expectedGroupId);
        // act
        Long actualGroupId = gInfoServiceAdaptor.createOverCompanyRoom(moderatorId, participantIds, name);
        // assert
        assertEquals(expectedGroupId, actualGroupId);
        verify(ginfoOpenService, times(1)).createOverCompanyRoom(anyLong(), anySet(), anySet(), anyInt(), anyString(), anyBoolean());
    }

    /**
     * 测试createOverCompanyRoom方法异常情况
     */
    @Test(expected = WmCustomerException.class)
    public void testCreateOverCompanyRoomException() throws Throwable {
        // arrange
        Long moderatorId = 1L;
        Set<Long> participantIds = new HashSet<>();
        participantIds.add(1L);
        String name = "test";
        when(ginfoOpenService.createOverCompanyRoom(anyLong(), anySet(), anySet(), anyInt(), anyString(), anyBoolean())).thenThrow(new RuntimeException());
        // act
        gInfoServiceAdaptor.createOverCompanyRoom(moderatorId, participantIds, name);
        // assert
        verify(ginfoOpenService, times(1)).createOverCompanyRoom(anyLong(), anySet(), anySet(), anyInt(), anyString(), anyBoolean());
    }
}
