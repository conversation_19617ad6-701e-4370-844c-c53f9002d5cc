package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyRecord;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyRecordDao;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerRecordOpTypeEnum;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyServiceAddCreateOwnerApplyRecordTest {

    @InjectMocks
    private WmCustomerOwnerApplyService wmCustomerOwnerApplyService;

    @Mock
    private WmCustomerOwnerApplyRecordDao wmCustomerOwnerApplyRecordDao;

    @Mock
    private WmEmployeeService wmEmployeeService;

    private WmCustomerOwnerApply apply;

    private Integer opUid;

    private Method addCreateOwnerApplyRecordMethod;

    @Before
    public void setUp() throws Exception {
        apply = new WmCustomerOwnerApply();
        apply.setId(1);
        apply.setCustomerId(100);
        apply.setMtCustomerId(123L);
        opUid = 999;
        addCreateOwnerApplyRecordMethod = WmCustomerOwnerApplyService.class.getDeclaredMethod("addCreateOwnerApplyRecord", WmCustomerOwnerApply.class, Integer.class);
        addCreateOwnerApplyRecordMethod.setAccessible(true);
    }

    /**
     * Test case for normal scenario with existing owner
     */
    @Test
    public void testAddCreateOwnerApplyRecord_WithExistingOwner() throws Throwable {
        // arrange
        apply.setCustomerOwnerUid(888);
        WmEmploy wmEmploy = new WmEmploy();
        wmEmploy.setName("TestOwner");
        wmEmploy.setMisId("MIS123");
        when(wmEmployeeService.getWmEmployById(888)).thenReturn(wmEmploy);
        // act
        addCreateOwnerApplyRecordMethod.invoke(wmCustomerOwnerApplyService, apply, opUid);
        // assert
        ArgumentCaptor<WmCustomerOwnerApplyRecord> recordCaptor = ArgumentCaptor.forClass(WmCustomerOwnerApplyRecord.class);
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(recordCaptor.capture());
        WmCustomerOwnerApplyRecord record = recordCaptor.getValue();
        assertEquals(Long.valueOf(1), record.getApplyId());
        assertEquals(Long.valueOf(100), record.getCustomerId());
        assertEquals(opUid, record.getOpUid());
        assertEquals("", record.getRemark());
        assertEquals(Integer.valueOf(CustomerOwnerRecordOpTypeEnum.CREATE.getCode()), record.getOpType());
        assertEquals(String.format("提交客户责任人申请\n申请编号：%s\n申请客户id:%s\n申请进度：申请中\n当前客户责任人：TestOwner（MIS123）", apply.getId(), apply.getMtCustomerId()), record.getContent());
    }

    /**
     * Test case for null customerOwnerUid
     */
    @Test
    public void testAddCreateOwnerApplyRecord_WithNullOwner() throws Throwable {
        // arrange
        apply.setCustomerOwnerUid(null);
        // act
        addCreateOwnerApplyRecordMethod.invoke(wmCustomerOwnerApplyService, apply, opUid);
        // assert
        ArgumentCaptor<WmCustomerOwnerApplyRecord> recordCaptor = ArgumentCaptor.forClass(WmCustomerOwnerApplyRecord.class);
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(recordCaptor.capture());
        WmCustomerOwnerApplyRecord record = recordCaptor.getValue();
        assertEquals(Long.valueOf(1), record.getApplyId());
        assertEquals(Long.valueOf(100), record.getCustomerId());
        assertEquals(opUid, record.getOpUid());
        assertEquals("", record.getRemark());
        assertEquals(Integer.valueOf(CustomerOwnerRecordOpTypeEnum.CREATE.getCode()), record.getOpType());
        assertEquals(String.format("提交客户责任人申请\n申请编号：%s\n申请客户id:%s\n申请进度：申请中\n当前客户责任人：无", apply.getId(), apply.getMtCustomerId()), record.getContent());
    }

    /**
     * Test case for null WmEmploy response
     */
    @Test
    public void testAddCreateOwnerApplyRecord_WithNullWmEmploy() throws Throwable {
        // arrange
        apply.setCustomerOwnerUid(888);
        when(wmEmployeeService.getWmEmployById(888)).thenReturn(null);
        // act
        addCreateOwnerApplyRecordMethod.invoke(wmCustomerOwnerApplyService, apply, opUid);
        // assert
        ArgumentCaptor<WmCustomerOwnerApplyRecord> recordCaptor = ArgumentCaptor.forClass(WmCustomerOwnerApplyRecord.class);
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(recordCaptor.capture());
        WmCustomerOwnerApplyRecord record = recordCaptor.getValue();
        assertEquals(Long.valueOf(1), record.getApplyId());
        assertEquals(Long.valueOf(100), record.getCustomerId());
        assertEquals(opUid, record.getOpUid());
        assertEquals("", record.getRemark());
        assertEquals(Integer.valueOf(CustomerOwnerRecordOpTypeEnum.CREATE.getCode()), record.getOpType());
        assertEquals(String.format("提交客户责任人申请\n申请编号：%s\n申请客户id:%s\n申请进度：申请中\n当前客户责任人：（）", apply.getId(), apply.getMtCustomerId()), record.getContent());
    }

    /**
     * Test case for exception during insert
     */
    @Test
    public void testAddCreateOwnerApplyRecord_WithInsertException() throws Throwable {
        // arrange
        apply.setCustomerOwnerUid(888);
        doThrow(new RuntimeException("Insert failed")).when(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(any());
        // act
        addCreateOwnerApplyRecordMethod.invoke(wmCustomerOwnerApplyService, apply, opUid);
        // assert
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(any());
    }

    /**
     * Test case for zero customerOwnerUid
     */
    @Test
    public void testAddCreateOwnerApplyRecord_WithZeroOwnerUid() throws Throwable {
        // arrange
        apply.setCustomerOwnerUid(0);
        // act
        addCreateOwnerApplyRecordMethod.invoke(wmCustomerOwnerApplyService, apply, opUid);
        // assert
        ArgumentCaptor<WmCustomerOwnerApplyRecord> recordCaptor = ArgumentCaptor.forClass(WmCustomerOwnerApplyRecord.class);
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(recordCaptor.capture());
        WmCustomerOwnerApplyRecord record = recordCaptor.getValue();
        assertEquals(Long.valueOf(1), record.getApplyId());
        assertEquals(Long.valueOf(100), record.getCustomerId());
        assertEquals(opUid, record.getOpUid());
        assertEquals("", record.getRemark());
        assertEquals(Integer.valueOf(CustomerOwnerRecordOpTypeEnum.CREATE.getCode()), record.getOpType());
        assertEquals(String.format("提交客户责任人申请\n申请编号：%s\n申请客户id:%s\n申请进度：申请中\n当前客户责任人：无", apply.getId(), apply.getMtCustomerId()), record.getContent());
    }

    /**
     * Test case for null opUid
     */
    @Test
    public void testAddCreateOwnerApplyRecord_WithNullOpUid() throws Throwable {
        // arrange
        apply.setCustomerOwnerUid(888);
        // act
        addCreateOwnerApplyRecordMethod.invoke(wmCustomerOwnerApplyService, apply, null);
        // assert
        ArgumentCaptor<WmCustomerOwnerApplyRecord> recordCaptor = ArgumentCaptor.forClass(WmCustomerOwnerApplyRecord.class);
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(recordCaptor.capture());
        WmCustomerOwnerApplyRecord record = recordCaptor.getValue();
        assertNull(record.getOpUid());
    }
}
