package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerStepEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyDetailDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyStep;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyBusServiceFillApplyStepInfoTest {

    @InjectMocks
    private WmCustomerOwnerApplyBusService service;

    @Mock
    private WmEmployeeService wmEmployeeService;

    @Mock
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    @Mock
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    private Method fillApplyStepInfoMethod;

    @Before
    public void setUp() throws Exception {
        // Get private method through reflection
        fillApplyStepInfoMethod = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("fillApplyStepInfo", CustomerOwnerApplyDetailDTO.class, WmCustomerOwnerApply.class);
        fillApplyStepInfoMethod.setAccessible(true);
        // Mock WmEmploy object
        WmEmploy wmEmploy = new WmEmploy();
        wmEmploy.setName("testUser");
        wmEmploy.setMisId("123");
        when(wmEmployeeService.getWmEmployById(anyInt())).thenReturn(wmEmploy);
        when(wmEmployeeService.getUserAndId(anyInt())).thenReturn("testUser(123)");
        // Mock audit related objects with audit result
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setTaskId(1);
        audit.setAuditResult("Test audit result");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(anyInt())).thenReturn(audit);
        // Mock ticket system response
        WmTicketDto ticketDto = new WmTicketDto();
        ticketDto.setId(100);
    }

    /**
     * Helper method to create basic WmCustomerOwnerApply object
     */
    private WmCustomerOwnerApply createBasicApply(CustomerOwnerApplyStatusEnum status) {
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(1);
        apply.setStepId(CustomerOwnerStepEnum.CREATE_TO_AUDIT.getCode());
        apply.setStatus(status.getCode());
        apply.setCustomerOwnerUid(100);
        apply.setApplyUid(200);
        apply.setCtime(1000);
        apply.setUtime(2000);
        return apply;
    }

    /**
     * Test case for invalid step enum
     */
    @Test
    public void testFillApplyStepInfo_WhenStepEnumIsNull() throws Throwable {
        // arrange
        CustomerOwnerApplyDetailDTO detailDTO = new CustomerOwnerApplyDetailDTO();
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        // Invalid step ID
        apply.setStepId(999);
        try {
            // act
            fillApplyStepInfoMethod.invoke(service, detailDTO, apply);
            fail("Should throw exception");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof WmCustomerException);
            WmCustomerException customerException = (WmCustomerException) e.getCause();
            assertEquals(CustomerConstants.RESULT_CODE_ERROR, customerException.getCode());
            assertEquals("未查询到客户申请单步骤信息", customerException.getMsg());
        }
    }

    /**
     * Test case for COMPLETE status
     */
    @Test
    public void testFillApplyStepInfo_WhenStatusIsComplete() throws Throwable {
        // arrange
        CustomerOwnerApplyDetailDTO detailDTO = new CustomerOwnerApplyDetailDTO();
        WmCustomerOwnerApply apply = createBasicApply(CustomerOwnerApplyStatusEnum.COMPLETE);
        // act
        fillApplyStepInfoMethod.invoke(service, detailDTO, apply);
        // assert
        List<CustomerOwnerApplyStep> steps = detailDTO.getApplyStepList();
        assertNotNull(steps);
        CustomerOwnerApplyStep completeStep = steps.get(steps.size() - 1);
        assertTrue(completeStep.isOnStep());
    }

    /**
     * Test case for REJECT status with system auto reject
     */
    @Test
    public void testFillApplyStepInfo_WhenStatusIsRejectWithSystemAutoReject() throws Throwable {
        // arrange
        CustomerOwnerApplyDetailDTO detailDTO = new CustomerOwnerApplyDetailDTO();
        WmCustomerOwnerApply apply = createBasicApply(CustomerOwnerApplyStatusEnum.REJECT);
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setAuditUid(0);
        audit.setAuditResult("系统自动驳回");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(anyInt())).thenReturn(audit);
        // act
        fillApplyStepInfoMethod.invoke(service, detailDTO, apply);
        // assert
        List<CustomerOwnerApplyStep> steps = detailDTO.getApplyStepList();
        assertNotNull(steps);
        CustomerOwnerApplyStep rejectStep = steps.get(1);
        assertEquals("系统驳回", rejectStep.getAuditTips());
        assertEquals("审批驳回", rejectStep.getStepName());
    }

    /**
     * Test case for REJECT status with manual reject
     */
    @Test
    public void testFillApplyStepInfo_WhenStatusIsRejectWithManualReject() throws Throwable {
        // arrange
        CustomerOwnerApplyDetailDTO detailDTO = new CustomerOwnerApplyDetailDTO();
        WmCustomerOwnerApply apply = createBasicApply(CustomerOwnerApplyStatusEnum.REJECT);
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setAuditUid(123);
        audit.setAuditResult("人工驳回");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(anyInt())).thenReturn(audit);
        // act
        fillApplyStepInfoMethod.invoke(service, detailDTO, apply);
        // assert
        List<CustomerOwnerApplyStep> steps = detailDTO.getApplyStepList();
        assertNotNull(steps);
        CustomerOwnerApplyStep rejectStep = steps.get(1);
        assertTrue(rejectStep.getAuditTips().contains("已驳回"));
        assertEquals("审批驳回", rejectStep.getStepName());
    }

    /**
     * Test case for STOP status
     */
    @Test
    public void testFillApplyStepInfo_WhenStatusIsStop() throws Throwable {
        // arrange
        CustomerOwnerApplyDetailDTO detailDTO = new CustomerOwnerApplyDetailDTO();
        WmCustomerOwnerApply apply = createBasicApply(CustomerOwnerApplyStatusEnum.STOP);
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setAuditResult("终止原因");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(anyInt())).thenReturn(audit);
        // act
        fillApplyStepInfoMethod.invoke(service, detailDTO, apply);
        // assert
        List<CustomerOwnerApplyStep> steps = detailDTO.getApplyStepList();
        assertNotNull(steps);
        CustomerOwnerApplyStep stopStep = steps.get(1);
        assertEquals("已终止", stopStep.getAuditTips());
        assertEquals("已终止", stopStep.getStepName());
    }

    /**
     * Test case for CANCEL status
     */
    @Test
    public void testFillApplyStepInfo_WhenStatusIsCancel() throws Throwable {
        // arrange
        CustomerOwnerApplyDetailDTO detailDTO = new CustomerOwnerApplyDetailDTO();
        WmCustomerOwnerApply apply = createBasicApply(CustomerOwnerApplyStatusEnum.CANCEL);
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setAuditResult("撤回原因");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(anyInt())).thenReturn(audit);
        // act
        fillApplyStepInfoMethod.invoke(service, detailDTO, apply);
        // assert
        List<CustomerOwnerApplyStep> steps = detailDTO.getApplyStepList();
        assertNotNull(steps);
        CustomerOwnerApplyStep cancelStep = steps.get(1);
        assertEquals("已撤回", cancelStep.getAuditTips());
        assertEquals("已撤回", cancelStep.getStepName());
    }

    /**
     * Test case for missing audit info when status is REJECT
     */
    @Test
    public void testFillApplyStepInfo_WhenRejectStatusWithMissingAuditInfo() throws Throwable {
        // arrange
        CustomerOwnerApplyDetailDTO detailDTO = new CustomerOwnerApplyDetailDTO();
        WmCustomerOwnerApply apply = createBasicApply(CustomerOwnerApplyStatusEnum.REJECT);
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(anyInt())).thenReturn(null);
        try {
            // act
            fillApplyStepInfoMethod.invoke(service, detailDTO, apply);
            fail("Should throw exception");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof WmCustomerException);
            WmCustomerException customerException = (WmCustomerException) e.getCause();
            assertEquals(CustomerConstants.RESULT_CODE_ERROR, customerException.getCode());
            assertEquals("未查询到客户申请单审批信息", customerException.getMsg());
        }
    }
}
