package com.sankuai.meituan.waimai.customer.mq.consumer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.KpAuditResultConstants;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.domain.KpAuditResultBody;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerFlowAbility;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerIdCardFlowAbility;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerNotIdCardFlowAbility;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpAuditConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmKpAuditResultListenerTest {

    @InjectMocks
    private WmKpAuditResultListener wmKpAuditResultListener;

    @Mock
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Mock
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Mock
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Mock
    private KpSignerIdCardFlowAbility kpSignerIdCardFlowAbility;

    @Mock
    private KpSignerNotIdCardFlowAbility kpSignerNotIdCardFlowAbility;

    @Mock
    private WmCustomerGrayService wmCustomerGrayService;

    @Mock
    private MafkaMessage mafkaMessage;

    @Mock
    private MessagetContext messagetContext;

    /**
     * 测试审核结果类型不正确的情况
     */
    @Test
    public void testRecvMessage_AuditResultTypeIsIncorrect() throws Throwable {
        // arrange
        String validJson = "{\"bizType\": 1, \"auditResult\": 999}";
        when(mafkaMessage.getBody()).thenReturn(validJson);
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试提审记录不存在的情况
     */
    @Test
    public void testRecvMessage_AuditRecordDoesNotExist() throws Throwable {
        // arrange
        String validJson = "{\"bizType\": 1, \"auditResult\": 1, \"bizId\": 123}";
        when(mafkaMessage.getBody()).thenReturn(validJson);
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试审核结果已经被更新的情况
     */
    @Test
    public void testRecvMessage_AuditResultAlreadyUpdated() throws Throwable {
        // arrange
        String validJson = "{\"bizType\": 1, \"auditResult\": 1, \"bizId\": 123}";
        when(mafkaMessage.getBody()).thenReturn(validJson);
        WmCustomerKpAudit audit = new WmCustomerKpAudit();
        audit.setResult("some result");
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试签约人 KP 记录不存在的情况
     */
    @Test
    public void testRecvMessage_SignerKpRecordDoesNotExist() throws Throwable {
        // arrange
        String validJson = "{\"bizType\": 1, \"auditResult\": 1, \"bizId\": 123}";
        when(mafkaMessage.getBody()).thenReturn(validJson);
        WmCustomerKpAudit audit = new WmCustomerKpAudit();
        audit.setResult(null);
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试正常处理流程 - 代理人授权类型
     */
    @Test
    public void testRecvMessage_NormalProcessing_AgentAuth() throws Throwable {
        // arrange
        String validJson = "{\"bizType\": " + WmAuditTaskBizTypeConstant.AGENT_AUTH + ", \"auditResult\": " + KpAuditResultConstants.AUDIT_RESULT_TYPE_APPROVED + ", \"bizId\": 123}";
        when(mafkaMessage.getBody()).thenReturn(validJson);
        WmCustomerKpAudit audit = new WmCustomerKpAudit();
        audit.setResult(null);
        audit.setType(KpAuditConstants.TYPE_AGENT);
        audit.setId(123);
        when(wmCustomerKpAuditMapper.selectByPrimaryKey(any())).thenReturn(audit);
        WmCustomerKp signerKp = new WmCustomerKp();
        signerKp.setState(KpSignerStateMachine.NO_DATA.getState());
        signerKp.setId(456);
        when(wmCustomerKpDBMapper.selectByPrimaryKey(any())).thenReturn(signerKp);
        when(kpSignerIdCardFlowAbility.dealSignerKpAuditResult(any())).thenReturn(true);
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(kpSignerIdCardFlowAbility, times(1)).dealSignerKpAuditResult(any());
    }

    /**
     * 测试审核驳回的情况 - 特批认证类型
     */
    @Test
    public void testRecvMessage_RejectedAudit_SpecialAuth() throws Throwable {
        // arrange
        String validJson = "{\"bizType\": " + WmAuditTaskBizTypeConstant.SPECIAL_AUTH + ", \"auditResult\": " + KpAuditResultConstants.AUDIT_RESULT_TYPE_REJECT + ", \"bizId\": 123}";
        when(mafkaMessage.getBody()).thenReturn(validJson);
        WmCustomerKpAudit audit = new WmCustomerKpAudit();
        audit.setResult(null);
        audit.setType(KpAuditConstants.TYPE_SPECIAL);
        audit.setId(123);
        when(wmCustomerKpAuditMapper.selectByPrimaryKey(any())).thenReturn(audit);
        WmCustomerKp signerKp = new WmCustomerKp();
        signerKp.setState(KpSignerStateMachine.NO_DATA.getState());
        signerKp.setId(456);
        when(wmCustomerKpDBMapper.selectByPrimaryKey(any())).thenReturn(signerKp);
        when(kpSignerNotIdCardFlowAbility.dealSignerKpAuditResult(any())).thenReturn(true);
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(kpSignerNotIdCardFlowAbility, times(1)).dealSignerKpAuditResult(any());
    }

    /**
     * 测试消息体为空的情况
     */
    @Test
    public void testRecvMessage_MessageBodyIsNull() throws Throwable {
        // arrange
        when(mafkaMessage.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试消息体不是有效 JSON 格式的情况
     */
    @Test
    public void testRecvMessage_MessageBodyIsNotValidJson() throws Throwable {
        // arrange
        when(mafkaMessage.getBody()).thenReturn("invalid json");
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试业务类型不符合要求的情况
     */
    @Test
    public void testRecvMessage_BizTypeIsNotSpecialOrAgent() throws Throwable {
        // arrange
        String validJson = "{\"bizType\": 999}";
        when(mafkaMessage.getBody()).thenReturn(validJson);
        // act
        ConsumeStatus result = wmKpAuditResultListener.recvMessage(mafkaMessage, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
