package com.sankuai.meituan.waimai.customer.service.sign.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for WmEcontractTaskBizService.queryByIdIfNullFromMaster method
 */
@RunWith(MockitoJUnitRunner.class)
public class WmEcontractTaskBizServiceTest {

    @InjectMocks
    private WmEcontractTaskBizService wmEcontractTaskBizService;

    @Mock
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;

    @Mock
    private WmFrameContractConfigService wmFrameContractConfigService;

    /**
     * Test when task exists in normal DB with valid batchId
     */
    @Test
    public void testQueryByIdIfNullFromMaster_TaskExistsWithValidBatchId() {
        // arrange
        Long taskId = 1L;
        WmEcontractSignTaskDB taskDB = new WmEcontractSignTaskDB();
        taskDB.setId(taskId);
        taskDB.setBatchId(100L);
        taskDB.setCustomerId(1000);
        when(wmEcontractBigTaskParseService.getById(taskId)).thenReturn(taskDB);
        when(wmFrameContractConfigService.handleSignTaskApplyType(taskDB)).thenReturn("TEST_TYPE");
        // act
        EcontractTaskBo result = wmEcontractTaskBizService.queryByIdIfNullFromMaster(taskId);
        // assert
        assertNotNull(result);
        assertEquals(taskId, result.getId());
        assertEquals(100L, result.getBatchId().longValue());
        verify(wmEcontractBigTaskParseService, never()).getByIdMaster(taskId);
    }

    /**
     * Test when task exists in normal DB but batchId is null
     */
    @Test
    public void testQueryByIdIfNullFromMaster_TaskExistsWithNullBatchId() {
        // arrange
        Long taskId = 1L;
        WmEcontractSignTaskDB normalTaskDB = new WmEcontractSignTaskDB();
        normalTaskDB.setId(taskId);
        normalTaskDB.setBatchId(null);
        WmEcontractSignTaskDB masterTaskDB = new WmEcontractSignTaskDB();
        masterTaskDB.setId(taskId);
        masterTaskDB.setBatchId(200L);
        when(wmEcontractBigTaskParseService.getById(taskId)).thenReturn(normalTaskDB);
        when(wmEcontractBigTaskParseService.getByIdMaster(taskId)).thenReturn(masterTaskDB);
        when(wmFrameContractConfigService.handleSignTaskApplyType(masterTaskDB)).thenReturn("TEST_TYPE");
        // act
        EcontractTaskBo result = wmEcontractTaskBizService.queryByIdIfNullFromMaster(taskId);
        // assert
        assertNotNull(result);
        assertEquals(taskId, result.getId());
        assertEquals(200L, result.getBatchId().longValue());
        verify(wmEcontractBigTaskParseService).getByIdMaster(taskId);
    }

    /**
     * Test when task doesn't exist in normal DB
     */
    @Test
    public void testQueryByIdIfNullFromMaster_TaskNotExistsInNormalDB() {
        // arrange
        Long taskId = 1L;
        WmEcontractSignTaskDB masterTaskDB = new WmEcontractSignTaskDB();
        masterTaskDB.setId(taskId);
        masterTaskDB.setBatchId(300L);
        when(wmEcontractBigTaskParseService.getById(taskId)).thenReturn(null);
        when(wmEcontractBigTaskParseService.getByIdMaster(taskId)).thenReturn(masterTaskDB);
        when(wmFrameContractConfigService.handleSignTaskApplyType(masterTaskDB)).thenReturn("TEST_TYPE");
        // act
        EcontractTaskBo result = wmEcontractTaskBizService.queryByIdIfNullFromMaster(taskId);
        // assert
        assertNotNull(result);
        assertEquals(taskId, result.getId());
        assertEquals(300L, result.getBatchId().longValue());
        verify(wmEcontractBigTaskParseService).getByIdMaster(taskId);
    }

    /**
     * Test when task doesn't exist in either DB
     */
    @Test
    public void testQueryByIdIfNullFromMaster_TaskNotExistsInBothDB() {
        // arrange
        Long taskId = 1L;
        when(wmEcontractBigTaskParseService.getById(taskId)).thenReturn(null);
        when(wmEcontractBigTaskParseService.getByIdMaster(taskId)).thenReturn(null);
        // act
        EcontractTaskBo result = wmEcontractTaskBizService.queryByIdIfNullFromMaster(taskId);
        // assert
        assertNull(result);
        verify(wmEcontractBigTaskParseService).getById(taskId);
        verify(wmEcontractBigTaskParseService).getByIdMaster(taskId);
    }

    /**
     * Test with null taskId input
     */
    @Test
    public void testQueryByIdIfNullFromMaster_NullTaskId() {
        // arrange
        Long taskId = null;
        when(wmEcontractBigTaskParseService.getById(taskId)).thenReturn(null);
        when(wmEcontractBigTaskParseService.getByIdMaster(taskId)).thenReturn(null);
        // act
        EcontractTaskBo result = wmEcontractTaskBizService.queryByIdIfNullFromMaster(taskId);
        // assert
        assertNull(result);
        verify(wmEcontractBigTaskParseService).getById(taskId);
        verify(wmEcontractBigTaskParseService).getByIdMaster(taskId);
    }
}