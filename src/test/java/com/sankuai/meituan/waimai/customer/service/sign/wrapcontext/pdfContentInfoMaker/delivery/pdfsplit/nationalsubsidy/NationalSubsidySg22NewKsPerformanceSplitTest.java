package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit.nationalsubsidy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliverySG2_2InfoBo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidySg22NewKsPerformanceSplitTest {

    @InjectMocks
    private NationalSubsidySg22NewKsPerformanceSplit split;

    /**
     * Test successful case where all conditions are met
     */
    @Test
    public void testSplit_SuccessfulCase() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        deliveryInfoBo.setSupportMTDelivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setSupportSGV2_2Delivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid");
        deliveryInfoBo.setWmPoiId("123");
        EcontractDeliverySG2_2InfoBo sg22InfoBo = new EcontractDeliverySG2_2InfoBo();
        sg22InfoBo.setSupportNewKS(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setEcontractDeliverySG2_2InfoBo(sg22InfoBo);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = Maps.newHashMap();
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, Lists.newArrayList(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_NKS_FEEMODE));
        middleContext.setTabPdfMap(tabPdfMap);
        middleContext.setPdfDataMap(Maps.newHashMap());
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        List<String> resultList = middleContext.getPdfDataMap().get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_NKS_PERFORMANCE.getName());
        assertNotNull(resultList);
        assertEquals(1, resultList.size());
        assertEquals("test-uuid", resultList.get(0));
    }

    /**
     * Test when feeMode is not SHANGOU_2_2
     */
    @Test
    public void testSplit_WrongFeeMode() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode(String.valueOf(LogisticsFeeModeEnum.WAIMAI.getCode()));
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setPdfDataMap(Maps.newHashMap());
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        assertTrue(middleContext.getPdfDataMap().isEmpty());
    }

    /**
     * Test when MTDelivery is not supported
     */
    @Test
    public void testSplit_MTDeliveryNotSupported() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        deliveryInfoBo.setSupportMTDelivery("0");
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setPdfDataMap(Maps.newHashMap());
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        assertTrue(middleContext.getPdfDataMap().isEmpty());
    }

    /**
     * Test when SGV2_2 is not supported
     */
    @Test
    public void testSplit_SGV2_2NotSupported() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        deliveryInfoBo.setSupportMTDelivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setSupportSGV2_2Delivery("0");
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setPdfDataMap(Maps.newHashMap());
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        assertTrue(middleContext.getPdfDataMap().isEmpty());
    }

    /**
     * Test when tabPdfMap doesn't contain required key
     */
    @Test
    public void testSplit_MissingTabPdfMapKey() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        deliveryInfoBo.setSupportMTDelivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setSupportSGV2_2Delivery(WmEcontractContextUtil.SUPPORT_MARK);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        middleContext.setTabPdfMap(Maps.newHashMap());
        middleContext.setPdfDataMap(Maps.newHashMap());
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        assertTrue(middleContext.getPdfDataMap().isEmpty());
    }

    /**
     * Test when SG2_2InfoBo is null
     */
    @Test
    public void testSplit_NullSG2_2InfoBo() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        deliveryInfoBo.setSupportMTDelivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setSupportSGV2_2Delivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setEcontractDeliverySG2_2InfoBo(null);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = Maps.newHashMap();
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, Lists.newArrayList(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_NKS_FEEMODE));
        middleContext.setTabPdfMap(tabPdfMap);
        middleContext.setPdfDataMap(Maps.newHashMap());
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        assertTrue(middleContext.getPdfDataMap().isEmpty());
    }

    /**
     * Test when NewKS is not supported
     */
    @Test
    public void testSplit_NewKSNotSupported() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        deliveryInfoBo.setSupportMTDelivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setSupportSGV2_2Delivery(WmEcontractContextUtil.SUPPORT_MARK);
        EcontractDeliverySG2_2InfoBo sg22InfoBo = new EcontractDeliverySG2_2InfoBo();
        sg22InfoBo.setSupportNewKS("0");
        deliveryInfoBo.setEcontractDeliverySG2_2InfoBo(sg22InfoBo);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = Maps.newHashMap();
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, Lists.newArrayList(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_NKS_FEEMODE));
        middleContext.setTabPdfMap(tabPdfMap);
        middleContext.setPdfDataMap(Maps.newHashMap());
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        assertTrue(middleContext.getPdfDataMap().isEmpty());
    }

    /**
     * Test when existing performance service list already contains items
     */
    @Test
    public void testSplit_ExistingPerformanceServiceList() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setFeeMode(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        deliveryInfoBo.setSupportMTDelivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setSupportSGV2_2Delivery(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setDeliveryTypeUUID("test-uuid-2");
        EcontractDeliverySG2_2InfoBo sg22InfoBo = new EcontractDeliverySG2_2InfoBo();
        sg22InfoBo.setSupportNewKS(WmEcontractContextUtil.SUPPORT_MARK);
        deliveryInfoBo.setEcontractDeliverySG2_2InfoBo(sg22InfoBo);
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = Maps.newHashMap();
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, Lists.newArrayList(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_NKS_FEEMODE));
        middleContext.setTabPdfMap(tabPdfMap);
        Map<String, List<String>> pdfDataMap = Maps.newHashMap();
        List<String> existingList = new ArrayList<>();
        existingList.add("existing-uuid");
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_NKS_PERFORMANCE.getName(), existingList);
        middleContext.setPdfDataMap(pdfDataMap);
        // act
        split.split(deliveryInfoBo, middleContext);
        // assert
        List<String> resultList = middleContext.getPdfDataMap().get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_NKS_PERFORMANCE.getName());
        assertNotNull(resultList);
        assertEquals(2, resultList.size());
        assertTrue(resultList.contains("existing-uuid"));
        assertTrue(resultList.contains("test-uuid-2"));
    }
}
