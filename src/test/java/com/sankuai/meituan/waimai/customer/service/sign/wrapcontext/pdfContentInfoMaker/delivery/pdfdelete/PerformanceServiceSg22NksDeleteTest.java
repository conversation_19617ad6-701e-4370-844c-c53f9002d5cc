package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import java.util.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PerformanceServiceSg22NksDeleteTest {

    private static final String TAB_DELIVERY_PERFORMANCE_SERVICE = "delivery_performance_service";

    @Mock
    private Map<String, Collection<SignTemplateEnum>> tabPdfMap;

    @Mock
    private Map<String, List<String>> pdfDataMap;

    private PerformanceServiceSg22NksDelete performanceServiceSg22NksDelete;

    @Before
    public void setUp() {
        performanceServiceSg22NksDelete = new PerformanceServiceSg22NksDelete();
    }

    /**
     * 测试场景：pdfDataMap中包含PERFORMANCE_SERVICE_SG_22_NKS的数据
     * 预期结果：不应该删除任何模板
     */
    @Test
    public void testDeleteWhenPdfDataMapContainsKey() throws Throwable {
        // arrange
        List<String> dataList = new ArrayList<>();
        dataList.add("someData");
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS.getName())).thenReturn(dataList);
        // act
        performanceServiceSg22NksDelete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, never()).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
    }

    /**
     * 测试场景：pdfDataMap中不包含PERFORMANCE_SERVICE_SG_22_NKS的数据，且tabPdfMap中不包含对应模板
     * 预期结果：应该调用get方法但不进行删除操作
     */
    @Test
    public void testDeleteWhenTabListIsEmpty() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS.getName())).thenReturn(null);
        when(tabPdfMap.get(TAB_DELIVERY_PERFORMANCE_SERVICE)).thenReturn(null);
        // act
        performanceServiceSg22NksDelete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, times(1)).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
    }

    /**
     * 测试场景：pdfDataMap中不包含PERFORMANCE_SERVICE_SG_22_NKS的数据，tabPdfMap中包含需要删除的模板
     * 预期结果：应该删除对应的模板
     */
    @Test
    public void testDeleteWhenShouldRemoveTemplate() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS.getName())).thenReturn(null);
        Collection<SignTemplateEnum> tabList = new ArrayList<>();
        tabList.add(SignTemplateEnum.PERFORMANCE_SERVICE_SG_22_NKS);
        when(tabPdfMap.get(TAB_DELIVERY_PERFORMANCE_SERVICE)).thenReturn(tabList);
        // act
        performanceServiceSg22NksDelete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, times(1)).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
        assertTrue(tabList.isEmpty());
    }

    /**
     * 测试场景：pdfDataMap中不包含PERFORMANCE_SERVICE_SG_22_NKS的数据，tabPdfMap中包含多个模板
     * 预期结果：只删除PERFORMANCE_SERVICE_SG_22_NKS模板，保留其他模板
     */
    @Test
    public void testDeleteWhenMultipleTemplatesExist() throws Throwable {
        // arrange
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS.getName())).thenReturn(null);
        Collection<SignTemplateEnum> tabList = new ArrayList<>();
        tabList.add(SignTemplateEnum.PERFORMANCE_SERVICE_SG_22_NKS);
        tabList.add(SignTemplateEnum.PERFORMANCE_SERVICE_SG_22);
        when(tabPdfMap.get(TAB_DELIVERY_PERFORMANCE_SERVICE)).thenReturn(tabList);
        // act
        performanceServiceSg22NksDelete.delete(tabPdfMap, pdfDataMap);
        // assert
        verify(tabPdfMap, times(1)).get(TAB_DELIVERY_PERFORMANCE_SERVICE);
        assertEquals(1, tabList.size());
        assertTrue(tabList.contains(SignTemplateEnum.PERFORMANCE_SERVICE_SG_22));
    }
}