package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TechnicalServiceSg22PdfMakerTest {

    @InjectMocks
    private TechnicalServiceSg22PdfMaker technicalServiceSg22PdfMaker;

    private Method getAssembleDataMethod() throws Exception {
        Method method = TechnicalServiceSg22PdfMaker.class.getDeclaredMethod("assembleData", List.class, List.class, Map.class, String.class);
        method.setAccessible(true);
        return method;
    }

    /**
     * 测试空列表场景
     */
    @Test
    public void testAssembleDataEmptyList() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> infoBoList = Collections.emptyList();
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        String template = "template";
        // act
        getAssembleDataMethod().invoke(technicalServiceSg22PdfMaker, infoBoList, pdfBizContent, pdfMetaContent, template);
        // assert
        assertTrue(pdfBizContent.isEmpty());
        assertTrue(pdfMetaContent.isEmpty());
    }

    /**
     * 测试单个对象场景
     */
    @Test
    public void testAssembleDataSingleObject() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo = new EcontractDeliveryInfoBo();
        infoBo.setChargingDesc("1");
        infoBo.setSupportExclusive(WmEcontractContextUtil.SUPPORT_MARK);
        infoBo.setSupportSLA(WmEcontractContextUtil.SUPPORT_MARK);
        List<EcontractDeliveryInfoBo> infoBoList = Lists.newArrayList(infoBo);
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        String template = "template";
        // act
        getAssembleDataMethod().invoke(technicalServiceSg22PdfMaker, infoBoList, pdfBizContent, pdfMetaContent, template);
        // assert
        assertEquals(1, pdfBizContent.size());
        assertEquals("1", pdfBizContent.get(0).get("chargingDesc"));
        assertEquals("hasSupport", pdfMetaContent.get(template + "_hasSupport"));
        assertEquals("hasSupport", pdfMetaContent.get(template + "_hasSLASupport"));
        assertEquals("1", pdfMetaContent.get(template + "_sortKeys"));
    }

    /**
     * 测试多个对象场景
     */
    @Test
    public void testAssembleDataMultipleObjects() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo1 = new EcontractDeliveryInfoBo();
        infoBo1.setChargingDesc("2");
        EcontractDeliveryInfoBo infoBo2 = new EcontractDeliveryInfoBo();
        infoBo2.setChargingDesc("1");
        List<EcontractDeliveryInfoBo> infoBoList = Lists.newArrayList(infoBo1, infoBo2);
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        String template = "template";
        // act
        getAssembleDataMethod().invoke(technicalServiceSg22PdfMaker, infoBoList, pdfBizContent, pdfMetaContent, template);
        // assert
        assertEquals(2, pdfBizContent.size());
        assertEquals("1", pdfBizContent.get(0).get("chargingDesc"));
        assertEquals("2", pdfBizContent.get(1).get("chargingDesc"));
        assertEquals("1,2", pdfMetaContent.get(template + "_sortKeys"));
    }

    /**
     * 测试异常场景：infoBoList 包含 null 对象
     */
    @Test
    public void testAssembleDataNullObject() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> infoBoList = Lists.newArrayList((EcontractDeliveryInfoBo) null);
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        String template = "template";
        // act & assert
        try {
            getAssembleDataMethod().invoke(technicalServiceSg22PdfMaker, infoBoList, pdfBizContent, pdfMetaContent, template);
            fail("Expected NullPointerException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof NullPointerException);
        } catch (Exception e) {
            fail("Unexpected exception: " + e);
        }
    }

    /**
     * 测试 hasSupport 为 true 的场景
     */
    @Test
    public void testAssembleDataHasSupportTrue() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo = new EcontractDeliveryInfoBo();
        infoBo.setChargingDesc("1");
        infoBo.setSupportExclusive(WmEcontractContextUtil.SUPPORT_MARK);
        List<EcontractDeliveryInfoBo> infoBoList = Lists.newArrayList(infoBo);
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        String template = "template";
        // act
        getAssembleDataMethod().invoke(technicalServiceSg22PdfMaker, infoBoList, pdfBizContent, pdfMetaContent, template);
        // assert
        assertEquals("hasSupport", pdfMetaContent.get(template + "_hasSupport"));
        assertNull(pdfMetaContent.get(template + "_hasSLASupport"));
    }

    /**
     * 测试 hasSLASupport 为 true 的场景
     */
    @Test
    public void testAssembleDataHasSLASupportTrue() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo = new EcontractDeliveryInfoBo();
        infoBo.setChargingDesc("1");
        infoBo.setSupportSLA(WmEcontractContextUtil.SUPPORT_MARK);
        List<EcontractDeliveryInfoBo> infoBoList = Lists.newArrayList(infoBo);
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        String template = "template";
        // act
        getAssembleDataMethod().invoke(technicalServiceSg22PdfMaker, infoBoList, pdfBizContent, pdfMetaContent, template);
        // assert
        assertNull(pdfMetaContent.get(template + "_hasSupport"));
        assertEquals("hasSupport", pdfMetaContent.get(template + "_hasSLASupport"));
    }

    /**
     * 测试 hasSupport 和 hasSLASupport 都为 true 的场景
     */
    @Test
    public void testAssembleDataBothSupportTrue() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo = new EcontractDeliveryInfoBo();
        infoBo.setChargingDesc("1");
        infoBo.setSupportExclusive(WmEcontractContextUtil.SUPPORT_MARK);
        infoBo.setSupportSLA(WmEcontractContextUtil.SUPPORT_MARK);
        List<EcontractDeliveryInfoBo> infoBoList = Lists.newArrayList(infoBo);
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        String template = "template";
        // act
        getAssembleDataMethod().invoke(technicalServiceSg22PdfMaker, infoBoList, pdfBizContent, pdfMetaContent, template);
        // assert
        assertEquals("hasSupport", pdfMetaContent.get(template + "_hasSupport"));
        assertEquals("hasSupport", pdfMetaContent.get(template + "_hasSLASupport"));
    }

    /**
     * 测试 hasSupport 和 hasSLASupport 都为 false 的场景
     */
    @Test
    public void testAssembleDataBothSupportFalse() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo infoBo = new EcontractDeliveryInfoBo();
        infoBo.setChargingDesc("1");
        List<EcontractDeliveryInfoBo> infoBoList = Lists.newArrayList(infoBo);
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        String template = "template";
        // act
        getAssembleDataMethod().invoke(technicalServiceSg22PdfMaker, infoBoList, pdfBizContent, pdfMetaContent, template);
        // assert
        assertNull(pdfMetaContent.get(template + "_hasSupport"));
        assertNull(pdfMetaContent.get(template + "_hasSLASupport"));
    }
}