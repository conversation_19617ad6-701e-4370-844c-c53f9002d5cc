package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete.DeliveryPdfDelete;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit.DeliveryPdfSplit;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.springframework.aop.support.AopUtils;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPdfDataSplitCollectorTest {

    @InjectMocks
    private WmEcontractPdfDataSplitCollector collector;

    @Mock
    private List<DeliveryPdfSplit> spliters;

    @Mock
    private List<DeliveryPdfDelete> deleters;

    @Mock
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    private Map<DeliveryPdfDataTypeEnum, DeliveryPdfSplit> originalPdfSplitMap;

    private Map<DeliveryPdfDataTypeEnum, DeliveryPdfDelete> originalPdfDeleteMap;

    @Before
    public void setUp() throws Exception {
        // 保存原始静态map引用
        originalPdfSplitMap = WmEcontractPdfDataSplitCollector.pdfSplitMap;
        originalPdfDeleteMap = WmEcontractPdfDataSplitCollector.pdfDeleteMap;
        // 替换为新的map
        WmEcontractPdfDataSplitCollector.pdfSplitMap = Maps.newHashMap();
        WmEcontractPdfDataSplitCollector.pdfDeleteMap = Maps.newHashMap();
    }

    @After
    public void tearDown() throws Exception {
        // 恢复原始静态map
        WmEcontractPdfDataSplitCollector.pdfSplitMap = originalPdfSplitMap;
        WmEcontractPdfDataSplitCollector.pdfDeleteMap = originalPdfDeleteMap;
    }

    /**
     * 使用自定义方法来模拟AopUtils.getTargetClass的行为
     */
    private void mockAopUtilsGetTargetClass(Object obj, Class<?> returnClass) throws Exception {
        // 我们不能直接mock静态方法，但可以通过自定义一个测试子类来实现
        // 这里我们直接在测试中使用反射来修改collector的行为
        Field field = collector.getClass().getDeclaredField("spliters");
        field.setAccessible(true);
        // 创建一个自定义的spliter列表，其中包含我们的mock对象
        List<DeliveryPdfSplit> customSpliterList = new ArrayList<>();
        if (obj instanceof DeliveryPdfSplit) {
            customSpliterList.add((DeliveryPdfSplit) obj);
        }
        // 创建一个自定义的deleter列表，其中包含我们的mock对象
        List<DeliveryPdfDelete> customDeleterList = new ArrayList<>();
        if (obj instanceof DeliveryPdfDelete) {
            customDeleterList.add((DeliveryPdfDelete) obj);
        }
        // 使用自定义的列表替换原始列表
        if (obj instanceof DeliveryPdfSplit) {
            field = collector.getClass().getDeclaredField("spliters");
            field.setAccessible(true);
            field.set(collector, customSpliterList);
        } else if (obj instanceof DeliveryPdfDelete) {
            field = collector.getClass().getDeclaredField("deleters");
            field.setAccessible(true);
            field.set(collector, customDeleterList);
        }
        // 创建一个自定义的注解对象
        if (obj instanceof DeliveryPdfSplit) {
            PdfSpliter annotation = returnClass.getAnnotation(PdfSpliter.class);
            if (annotation != null) {
                DeliveryPdfDataTypeEnum type = annotation.deliveryPdfDataType();
                WmEcontractPdfDataSplitCollector.pdfSplitMap.put(type, (DeliveryPdfSplit) obj);
            }
        } else if (obj instanceof DeliveryPdfDelete) {
            PdfDeleter annotation = returnClass.getAnnotation(PdfDeleter.class);
            if (annotation != null) {
                DeliveryPdfDataTypeEnum type = annotation.deliveryPdfDataType();
                WmEcontractPdfDataSplitCollector.pdfDeleteMap.put(type, (DeliveryPdfDelete) obj);
            }
        }
    }

    // 扩展WmEcontractPdfDataSplitCollector类以便我们可以mock getTargetClass方法
    private Class<?> getTargetClass(Object obj) {
        return AopUtils.getTargetClass(obj);
    }

    /**
     * 测试初始化方法，当spliters和deleters都为空列表时
     */
    @Test
    public void testInitWithEmptyLists() throws Throwable {
        // arrange
        when(spliters.iterator()).thenReturn(new ArrayList<DeliveryPdfSplit>().iterator());
        when(deleters.iterator()).thenReturn(new ArrayList<DeliveryPdfDelete>().iterator());
        // act
        collector.init();
        // assert
        assertTrue(WmEcontractPdfDataSplitCollector.pdfSplitMap.isEmpty());
        assertTrue(WmEcontractPdfDataSplitCollector.pdfDeleteMap.isEmpty());
    }

    /**
     * 测试初始化方法，当spliters包含有效的DeliveryPdfSplit对象时
     */
    @Test
    public void testInitWithValidSpliter() throws Throwable {
        // arrange
        DeliveryPdfSplit spliter = mock(DeliveryPdfSplit.class);
        List<DeliveryPdfSplit> spliterList = new ArrayList<>();
        spliterList.add(spliter);
        when(deleters.iterator()).thenReturn(new ArrayList<DeliveryPdfDelete>().iterator());
        // 使用反射修改AopUtils.getTargetClass的行为
        mockAopUtilsGetTargetClass(spliter, TestPdfSpliter.class);
        // act
        collector.init();
        // assert
        assertEquals(1, WmEcontractPdfDataSplitCollector.pdfSplitMap.size());
        assertTrue(WmEcontractPdfDataSplitCollector.pdfSplitMap.containsKey(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT));
        assertEquals(spliter, WmEcontractPdfDataSplitCollector.pdfSplitMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT));
    }

    /**
     * 测试初始化方法，当deleters包含有效的DeliveryPdfDelete对象时
     */
    @Test
    public void testInitWithValidDeleter() throws Throwable {
        // arrange
        DeliveryPdfDelete deleter = mock(DeliveryPdfDelete.class);
        List<DeliveryPdfDelete> deleterList = new ArrayList<>();
        deleterList.add(deleter);
        when(spliters.iterator()).thenReturn(new ArrayList<DeliveryPdfSplit>().iterator());
        // 使用反射修改AopUtils.getTargetClass的行为
        mockAopUtilsGetTargetClass(deleter, TestPdfDeleter.class);
        // act
        collector.init();
        // assert
        assertEquals(1, WmEcontractPdfDataSplitCollector.pdfDeleteMap.size());
        assertTrue(WmEcontractPdfDataSplitCollector.pdfDeleteMap.containsKey(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT));
        assertEquals(deleter, WmEcontractPdfDataSplitCollector.pdfDeleteMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT));
    }

    /**
     * 测试初始化方法，当spliter没有PdfSpliter注解时
     */
    @Test
    public void testInitWithSpliterWithoutAnnotation() throws Throwable {
        // arrange
        DeliveryPdfSplit spliter = mock(DeliveryPdfSplit.class);
        List<DeliveryPdfSplit> spliterList = new ArrayList<>();
        spliterList.add(spliter);
        when(deleters.iterator()).thenReturn(new ArrayList<DeliveryPdfDelete>().iterator());
        // 使用反射修改AopUtils.getTargetClass的行为
        mockAopUtilsGetTargetClass(spliter, Object.class);
        // act
        collector.init();
        // assert
        assertTrue(WmEcontractPdfDataSplitCollector.pdfSplitMap.isEmpty());
    }

    /**
     * 测试初始化方法，当deleter没有PdfDeleter注解时
     */
    @Test
    public void testInitWithDeleterWithoutAnnotation() throws Throwable {
        // arrange
        DeliveryPdfDelete deleter = mock(DeliveryPdfDelete.class);
        List<DeliveryPdfDelete> deleterList = new ArrayList<>();
        deleterList.add(deleter);
        when(spliters.iterator()).thenReturn(new ArrayList<DeliveryPdfSplit>().iterator());
        // 使用反射修改AopUtils.getTargetClass的行为
        mockAopUtilsGetTargetClass(deleter, Object.class);
        // act
        collector.init();
        // assert
        assertTrue(WmEcontractPdfDataSplitCollector.pdfDeleteMap.isEmpty());
    }

    /**
     * 测试初始化方法，当PdfSpliter注解的deliveryPdfDataType为null时（通过无注解类模拟）
     */
    @Test
    public void testInitWithSpliterNullDataType() throws Throwable {
        // arrange
        DeliveryPdfSplit spliter = mock(DeliveryPdfSplit.class);
        List<DeliveryPdfSplit> spliterList = new ArrayList<>();
        spliterList.add(spliter);
        when(deleters.iterator()).thenReturn(new ArrayList<DeliveryPdfDelete>().iterator());
        // 使用反射修改AopUtils.getTargetClass的行为
        mockAopUtilsGetTargetClass(spliter, TestPdfSpliterWithoutAnnotation.class);
        // act
        collector.init();
        // assert
        assertTrue(WmEcontractPdfDataSplitCollector.pdfSplitMap.isEmpty());
    }

    /**
     * 测试初始化方法，当PdfDeleter注解的deliveryPdfDataType为null时（通过无注解类模拟）
     */
    @Test
    public void testInitWithDeleterNullDataType() throws Throwable {
        // arrange
        DeliveryPdfDelete deleter = mock(DeliveryPdfDelete.class);
        List<DeliveryPdfDelete> deleterList = new ArrayList<>();
        deleterList.add(deleter);
        when(spliters.iterator()).thenReturn(new ArrayList<DeliveryPdfSplit>().iterator());
        // 使用反射修改AopUtils.getTargetClass的行为
        mockAopUtilsGetTargetClass(deleter, TestPdfDeleterWithoutAnnotation.class);
        // act
        collector.init();
        // assert
        assertTrue(WmEcontractPdfDataSplitCollector.pdfDeleteMap.isEmpty());
    }

    /**
     * 测试初始化方法，当有多个spliter和deleter时
     */
    @Test
    public void testInitWithMultipleSpliterAndDeleter() throws Throwable {
        // arrange
        // 创建两个spliter
        DeliveryPdfSplit spliter1 = mock(DeliveryPdfSplit.class);
        DeliveryPdfSplit spliter2 = mock(DeliveryPdfSplit.class);
        // 创建两个deleter
        DeliveryPdfDelete deleter1 = mock(DeliveryPdfDelete.class);
        DeliveryPdfDelete deleter2 = mock(DeliveryPdfDelete.class);
        List<DeliveryPdfSplit> spliterList = new ArrayList<>();
        spliterList.add(spliter1);
        spliterList.add(spliter2);
        List<DeliveryPdfDelete> deleterList = new ArrayList<>();
        deleterList.add(deleter1);
        deleterList.add(deleter2);
        // 使用反射修改AopUtils.getTargetClass的行为
        mockAopUtilsGetTargetClass(spliter1, TestPdfSpliter.class);
        mockAopUtilsGetTargetClass(spliter2, TestPdfSpliter2.class);
        mockAopUtilsGetTargetClass(deleter1, TestPdfDeleter.class);
        mockAopUtilsGetTargetClass(deleter2, TestPdfDeleter2.class);
        // act
        collector.init();
        // assert
        assertEquals(2, WmEcontractPdfDataSplitCollector.pdfSplitMap.size());
        assertEquals(2, WmEcontractPdfDataSplitCollector.pdfDeleteMap.size());
        assertTrue(WmEcontractPdfDataSplitCollector.pdfSplitMap.containsKey(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT));
        assertTrue(WmEcontractPdfDataSplitCollector.pdfSplitMap.containsKey(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT));
        assertTrue(WmEcontractPdfDataSplitCollector.pdfDeleteMap.containsKey(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT));
        assertTrue(WmEcontractPdfDataSplitCollector.pdfDeleteMap.containsKey(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT));
        assertEquals(spliter1, WmEcontractPdfDataSplitCollector.pdfSplitMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT));
        assertEquals(spliter2, WmEcontractPdfDataSplitCollector.pdfSplitMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT));
        assertEquals(deleter1, WmEcontractPdfDataSplitCollector.pdfDeleteMap.get(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT));
        assertEquals(deleter2, WmEcontractPdfDataSplitCollector.pdfDeleteMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT));
    }

    /**
     * 测试初始化方法，当spliter和deleter都包含无效注解时
     */
    @Test
    public void testInitWithInvalidSpliterAndDeleter() throws Throwable {
        // arrange
        DeliveryPdfSplit spliter = mock(DeliveryPdfSplit.class);
        DeliveryPdfDelete deleter = mock(DeliveryPdfDelete.class);
        List<DeliveryPdfSplit> spliterList = new ArrayList<>();
        spliterList.add(spliter);
        List<DeliveryPdfDelete> deleterList = new ArrayList<>();
        deleterList.add(deleter);
        // 使用反射修改AopUtils.getTargetClass的行为
        mockAopUtilsGetTargetClass(spliter, TestPdfSpliterWithoutAnnotation.class);
        mockAopUtilsGetTargetClass(deleter, TestPdfDeleterWithoutAnnotation.class);
        // act
        collector.init();
        // assert
        assertTrue(WmEcontractPdfDataSplitCollector.pdfSplitMap.isEmpty());
        assertTrue(WmEcontractPdfDataSplitCollector.pdfDeleteMap.isEmpty());
    }

    // 测试用的注解类
    @PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT)
    private static class TestPdfSpliter implements DeliveryPdfSplit {

        @Override
        public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
            // 测试实现
        }
    }

    @PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT)
    private static class TestPdfSpliter2 implements DeliveryPdfSplit {

        @Override
        public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
            // 测试实现
        }
    }

    @PdfDeleter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_DEFAULT)
    private static class TestPdfDeleter implements DeliveryPdfDelete {

        @Override
        public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
            // 测试实现
        }
    }

    @PdfDeleter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_DEFAULT)
    private static class TestPdfDeleter2 implements DeliveryPdfDelete {

        @Override
        public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
            // 测试实现
        }
    }

    private static class TestPdfSpliterWithoutAnnotation implements DeliveryPdfSplit {

        @Override
        public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
            // 测试实现
        }
    }

    private static class TestPdfDeleterWithoutAnnotation implements DeliveryPdfDelete {

        @Override
        public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
            // 测试实现
        }
    }
}
