package com.sankuai.meituan.waimai.customer.util.business;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractContextUtilTest {

    /**
     * Test case for NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY batch type
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithNationalSubsidyDistributorDelivery() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo expectedTask = new EcontractTaskBo();
        expectedTask.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        taskMap.put(1L, expectedTask);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        // act
        EcontractTaskBo result = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
        // assert
        assertNotNull(result);
        assertEquals(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName(), result.getApplyType());
    }

    /**
     * Test case for NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY batch type
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithNationalSubsidyHeadquartersDelivery() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo expectedTask = new EcontractTaskBo();
        expectedTask.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName());
        taskMap.put(1L, expectedTask);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        // act
        EcontractTaskBo result = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
        // assert
        assertNotNull(result);
        assertEquals(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName(), result.getApplyType());
    }

    /**
     * Test case for default batch type with multi WmPoi
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithDefaultBatchTypeAndMultiWmPoi() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        // Any type other than the special cases
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo expectedTask = new EcontractTaskBo();
        expectedTask.setApplyType(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName());
        taskMap.put(1L, expectedTask);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(true);
        // act
        EcontractTaskBo result = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
        // assert
        assertNotNull(result);
        assertEquals(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName(), result.getApplyType());
    }

    /**
     * Test case for default batch type with single WmPoi
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithDefaultBatchTypeAndSingleWmPoi() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        // Any type other than the special cases
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo expectedTask = new EcontractTaskBo();
        expectedTask.setApplyType(EcontractTaskApplyTypeEnum.POIFEE.getName());
        taskMap.put(1L, expectedTask);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(false);
        // act
        EcontractTaskBo result = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
        // assert
        assertNotNull(result);
        assertEquals(EcontractTaskApplyTypeEnum.POIFEE.getName(), result.getApplyType());
    }

    /**
     * Test case for empty task map
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithEmptyTaskMap() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        // Empty map
        contextBo.setTaskIdAndTaskMap(new HashMap<>());
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        // act & assert
        try {
            WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            assertEquals("任务信息不能为空", e.getMessage());
        }
    }

    /**
     * Test case for null task map
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithNullTaskMap() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        // Null map
        contextBo.setTaskIdAndTaskMap(null);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        // act & assert
        try {
            WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            assertEquals("任务信息不能为空", e.getMessage());
        }
    }

    /**
     * Test case for task not found in NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY batch type
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithNationalSubsidyDistributorDeliveryTaskNotFound() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo task = new EcontractTaskBo();
        // Different type than expected
        task.setApplyType("wrong_type");
        taskMap.put(1L, task);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        // act & assert
        try {
            WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            assertEquals("不存在" + EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName() + "申请任务", e.getMessage());
        }
    }

    /**
     * Test case for task not found in NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY batch type
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithNationalSubsidyHeadquartersDeliveryTaskNotFound() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo task = new EcontractTaskBo();
        // Different type than expected
        task.setApplyType("wrong_type");
        taskMap.put(1L, task);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        // act & assert
        try {
            WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            assertEquals("不存在" + EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName() + "申请任务", e.getMessage());
        }
    }

    /**
     * Test case for task not found in default batch type with multi WmPoi
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithDefaultBatchTypeAndMultiWmPoiTaskNotFound() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo task = new EcontractTaskBo();
        // Different type than expected
        task.setApplyType("wrong_type");
        taskMap.put(1L, task);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(true);
        // act & assert
        try {
            WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            assertEquals("不存在" + EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName() + "申请任务", e.getMessage());
        }
    }

    /**
     * Test case for task not found in default batch type with single WmPoi
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithDefaultBatchTypeAndSingleWmPoiTaskNotFound() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo task = new EcontractTaskBo();
        // Different type than expected
        task.setApplyType("wrong_type");
        taskMap.put(1L, task);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(false);
        // act & assert
        try {
            WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            assertEquals("不存在" + EcontractTaskApplyTypeEnum.POIFEE.getName() + "申请任务", e.getMessage());
        }
    }

    /**
     * Test case for null task in the map
     */
    @Test
    public void testSelectDeliveryEcontractTaskBoWithNullTaskInMap() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        // Null task
        taskMap.put(1L, null);
        EcontractTaskBo expectedTask = new EcontractTaskBo();
        expectedTask.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        // Valid task
        taskMap.put(2L, expectedTask);
        contextBo.setTaskIdAndTaskMap(taskMap);
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        // act
        EcontractTaskBo result = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(contextBo, signDataFactor);
        // assert
        assertNotNull(result);
        assertEquals(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName(), result.getApplyType());
    }
}
