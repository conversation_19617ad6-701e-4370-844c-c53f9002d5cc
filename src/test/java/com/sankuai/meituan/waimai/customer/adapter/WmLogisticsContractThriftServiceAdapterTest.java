package com.sankuai.meituan.waimai.customer.adapter;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.logistics.contract.client.exception.WmLogisticsContractException;
import com.sankuai.meituan.waimai.logistics.contract.client.service.contractplatform.WmContractPlatformThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test cases for WmLogisticsContractThriftServiceAdapter#getTechFeeSignDataMultiPoiWithRetry
 */
@RunWith(MockitoJUnitRunner.class)
public class WmLogisticsContractThriftServiceAdapterTest {

    @Mock
    private WmContractPlatformThriftService wmContractPlatformThriftService;

    @InjectMocks
    private WmLogisticsContractThriftServiceAdapter adapter;

    private EcontractDataPoiBizBo validPoiBizBo;

    private EcontractTaskApplyTypeEnum applyTypeEnum;

    private MockedStatic<MccConfig> mockedMccConfig;

    private MockedStatic<ConfigUtilAdapter> mockedConfigUtilAdapter;

    @Before
    public void setUp() {
        validPoiBizBo = new EcontractDataPoiBizBo();
        validPoiBizBo.setWmPoiId(12345L);
        validPoiBizBo.setBizId(67890L);
        applyTypeEnum = EcontractTaskApplyTypeEnum.POIFEE;
        // Mock static methods
        mockedMccConfig = Mockito.mockStatic(MccConfig.class);
        mockedConfigUtilAdapter = Mockito.mockStatic(ConfigUtilAdapter.class);
        // Set default behavior
        mockedMccConfig.when(MccConfig::queryEcontractDataRetryTimes).thenReturn(3);
        mockedConfigUtilAdapter.when(() -> ConfigUtilAdapter.getInt(anyString(), anyInt())).thenReturn(3);
    }

    @After
    public void tearDown() {
        if (mockedMccConfig != null) {
            mockedMccConfig.close();
        }
        if (mockedConfigUtilAdapter != null) {
            mockedConfigUtilAdapter.close();
        }
    }

    /**
     * Test successful scenario with valid input parameters
     */
    @Test
    public void testGetTechFeeSignDataMultiPoiWithRetrySuccess() throws Throwable {
        // Arrange
        String expectedResult = "success_result";
        when(wmContractPlatformThriftService.getTechFeeSignDataMultiPoi(anyList())).thenReturn(expectedResult);
        // Act
        String result = adapter.getTechFeeSignDataMultiPoiWithRetry(Arrays.asList(validPoiBizBo), applyTypeEnum);
        // Assert
        assertEquals(expectedResult, result);
        verify(wmContractPlatformThriftService, times(1)).getTechFeeSignDataMultiPoi(anyList());
    }

    /**
     * Test empty input list scenario
     */
    @Test(expected = WmCustomerException.class)
    public void testGetTechFeeSignDataMultiPoiWithRetryEmptyInput() throws Throwable {
        adapter.getTechFeeSignDataMultiPoiWithRetry(Collections.emptyList(), applyTypeEnum);
    }

    /**
     * Test TException retry scenario
     */
    @Test
    public void testGetTechFeeSignDataMultiPoiWithRetryTException() throws Throwable {
        // Arrange
        when(wmContractPlatformThriftService.getTechFeeSignDataMultiPoi(anyList())).thenThrow(new TException("Timeout"));
        // Act
        String result = adapter.getTechFeeSignDataMultiPoiWithRetry(Arrays.asList(validPoiBizBo), applyTypeEnum);
        // Assert
        assertNull(result);
        verify(wmContractPlatformThriftService, times(3)).getTechFeeSignDataMultiPoi(anyList());
    }

    /**
     * Test business error scenario
     */
    @Test
    public void testGetTechFeeSignDataMultiPoiWithRetryBusinessError() throws Throwable {
        // Arrange
        when(wmContractPlatformThriftService.getTechFeeSignDataMultiPoi(anyList())).thenThrow(new TException("Business error"));
        // Act
        String result = adapter.getTechFeeSignDataMultiPoiWithRetry(Arrays.asList(validPoiBizBo), applyTypeEnum);
        // Assert
        assertNull(result);
        verify(wmContractPlatformThriftService, times(3)).getTechFeeSignDataMultiPoi(anyList());
    }

    /**
     * Test null input list scenario
     */
    @Test(expected = WmCustomerException.class)
    public void testGetTechFeeSignDataMultiPoiWithRetryNullInput() throws Throwable {
        adapter.getTechFeeSignDataMultiPoiWithRetry(null, applyTypeEnum);
    }

    /**
     * Test retry mechanism with success after failure
     */
    @Test
    public void testGetTechFeeSignDataMultiPoiWithRetrySuccessAfterFailure() throws Throwable {
        // Arrange
        String expectedResult = "success_result";
        when(wmContractPlatformThriftService.getTechFeeSignDataMultiPoi(anyList())).thenThrow(new TException("Timeout")).thenReturn(expectedResult);
        // Act
        String result = adapter.getTechFeeSignDataMultiPoiWithRetry(Arrays.asList(validPoiBizBo), applyTypeEnum);
        // Assert
        assertEquals(expectedResult, result);
        verify(wmContractPlatformThriftService, times(2)).getTechFeeSignDataMultiPoi(anyList());
    }

    /**
     * Test WmLogisticsContractException scenario
     */
    @Test
    public void testGetTechFeeSignDataMultiPoiWithRetryWmLogisticsContractException() throws Throwable {
        // Arrange
        when(wmContractPlatformThriftService.getTechFeeSignDataMultiPoi(anyList())).thenThrow(new TException("Business error"));
        // Act
        String result = adapter.getTechFeeSignDataMultiPoiWithRetry(Arrays.asList(validPoiBizBo), applyTypeEnum);
        // Assert
        assertNull(result);
        verify(wmContractPlatformThriftService, times(3)).getTechFeeSignDataMultiPoi(anyList());
    }
}