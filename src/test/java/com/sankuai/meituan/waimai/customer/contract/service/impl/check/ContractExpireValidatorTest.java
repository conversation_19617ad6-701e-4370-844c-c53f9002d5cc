package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractExpireValidatorTest {

    @InjectMocks
    private ContractExpireValidator contractExpireValidator;

    @Mock
    private WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    /**
     * Test case: Contract not found
     * Expected: Should throw WmCustomerException with message "不存在合同, 不可操作过期"
     */
    @Test(expected = WmCustomerException.class)
    public void testValidWhenContractNotFound() throws Throwable {
        // arrange
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setTempletContractId(1L);
        contractBo.setBasicBo(basicBo);
        when(wmTempletContractAuditedDBMapper.selectByPrimaryKey(1L)).thenReturn(null);
        // act
        try {
            contractExpireValidator.valid(contractBo, 123, "test");
        } catch (WmCustomerException e) {
            // assert
            assertEquals("不存在合同, 不可操作过期", e.getMessage());
            throw e;
        }
    }

    /**
     * Test case: Unsupported contract type
     * Expected: Should throw WmCustomerException with message containing "不支持过期操作"
     */
    @Test(expected = WmCustomerException.class)
    public void testValidWhenUnsupportedContractType() throws Throwable {
        // arrange
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setTempletContractId(1L);
        contractBo.setBasicBo(basicBo);
        WmTempletContractDB contractDB = new WmTempletContractDB();
        contractDB.setType(WmTempletContractTypeEnum.C1_E.getCode());
        when(wmTempletContractAuditedDBMapper.selectByPrimaryKey(1L)).thenReturn(contractDB);
        // act
        try {
            contractExpireValidator.valid(contractBo, 123, "test");
        } catch (WmCustomerException e) {
            // assert
            assertTrue(e.getMessage().contains("不支持过期操作"));
            throw e;
        }
    }

    /**
     * Test case: Contract not expired yet
     * Expected: Should throw WmCustomerException with message "当前合同未过期，不允许过期操作"
     */
    @Test(expected = WmCustomerException.class)
    public void testValidWhenContractNotExpired() throws Throwable {
        // arrange
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setTempletContractId(1L);
        contractBo.setBasicBo(basicBo);
        WmTempletContractDB contractDB = new WmTempletContractDB();
        contractDB.setType(WmTempletContractTypeEnum.NATIONAL_SUBSIDY_PURCHASE_AGREEMENT.getCode());
        // due date is tomorrow
        contractDB.setDueDate(DateUtil.unixTime() + 86400);
        when(wmTempletContractAuditedDBMapper.selectByPrimaryKey(1L)).thenReturn(contractDB);
        // act
        try {
            contractExpireValidator.valid(contractBo, 123, "test");
        } catch (WmCustomerException e) {
            // assert
            assertEquals("当前合同未过期，不允许过期操作", e.getMessage());
            throw e;
        }
    }

    /**
     * Test case: Valid contract for expiration
     * Expected: Should return true
     */
    @Test
    public void testValidWhenContractCanExpire() throws Throwable {
        // arrange
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setTempletContractId(1L);
        contractBo.setBasicBo(basicBo);
        WmTempletContractDB contractDB = new WmTempletContractDB();
        contractDB.setType(WmTempletContractTypeEnum.NATIONAL_SUBSIDY_PURCHASE_AGREEMENT.getCode());
        // due date was yesterday
        contractDB.setDueDate(DateUtil.unixTime() - 86400);
        contractDB.setStatus(CustomerContractStatus.EFFECT.getCode());
        when(wmTempletContractAuditedDBMapper.selectByPrimaryKey(1L)).thenReturn(contractDB);
        // act
        boolean result = contractExpireValidator.valid(contractBo, 123, "test");
        // assert
        assertTrue(result);
    }

    /**
     * Test case: Contract with invalid status
     * Expected: Should throw WmCustomerException with message "当前合同状态不支持过期操作"
     */
    @Test(expected = WmCustomerException.class)
    public void testValidWhenInvalidStatus() throws Throwable {
        // arrange
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setTempletContractId(1L);
        contractBo.setBasicBo(basicBo);
        WmTempletContractDB contractDB = new WmTempletContractDB();
        contractDB.setType(WmTempletContractTypeEnum.NATIONAL_SUBSIDY_PURCHASE_AGREEMENT.getCode());
        // due date was yesterday
        contractDB.setDueDate(DateUtil.unixTime() - 86400);
        contractDB.setStatus(CustomerContractStatus.INVALID.getCode());
        when(wmTempletContractAuditedDBMapper.selectByPrimaryKey(1L)).thenReturn(contractDB);
        // act
        try {
            contractExpireValidator.valid(contractBo, 123, "test");
        } catch (WmCustomerException e) {
            // assert
            assertEquals("当前合同状态不支持过期操作", e.getMessage());
            throw e;
        }
    }
}
