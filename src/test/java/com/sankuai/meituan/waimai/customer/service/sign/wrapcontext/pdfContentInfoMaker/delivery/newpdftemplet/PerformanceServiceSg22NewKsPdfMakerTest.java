package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Test cases for PerformanceServiceSg22NewKsPdfMaker.makePdfContentInfoBo method
 */
@RunWith(MockitoJUnitRunner.class)
public class PerformanceServiceSg22NewKsPdfMakerTest {

    @InjectMocks
    private PerformanceServiceSg22NewKsPdfMaker performanceServiceSg22NewKsPdfMaker;

    @Mock
    private EcontractDeliveryInfoBo temp;

    @Mock
    private EcontractDeliverySG2_2InfoBo econtractDeliverySG2_2InfoBo;

    private Method fillSupportDataMethod;

    @Before
    public void setUp() throws Exception {
        fillSupportDataMethod = PerformanceServiceSg22NewKsPdfMaker.class.getDeclaredMethod("fillSupportData", EcontractDeliveryInfoBo.class, Map.class, List.class);
        fillSupportDataMethod.setAccessible(true);
    }

    private void setFieldValue(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    /**
     * Test when delivery info list is empty
     */
    @Test
    public void testMakePdfContentInfoBo_EmptyDeliveryInfo() throws Throwable {
        // Arrange
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        // Setup middleContext
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS.getName(), new ArrayList<>());
        middleContext.setPdfDataMap(pdfDataMap);
        // Setup signDataFactor
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(false);
        middleContext.setSignDataFactor(signDataFactor);
        // Setup originContext
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.POIFEE.getName());
        taskBo.setApplyContext("{}");
        taskMap.put(1L, taskBo);
        originContext.setTaskIdAndTaskMap(taskMap);
        // 设置batchTypeEnum，避免空指针异常
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerName("Test Customer");
        originContext.setCustomerInfoBo(customerInfoBo);
        // 使用注入的mock对象
        // Act
        PdfContentInfoBo result = performanceServiceSg22NewKsPdfMaker.makePdfContentInfoBo(originContext, middleContext);
        // Assert
        assertNotNull(result);
        assertNotNull(result.getPdfMetaContent());
        assertTrue(result.getPdfBizContent().isEmpty());
    }

    /**
     * Test when delivery info list contains valid data with SG2.2 and MT delivery support
     */
    @Test
    public void testMakePdfContentInfoBo_NonEmptyDeliveryInfoNoModification() throws Throwable {
        // Arrange
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        // Setup delivery info
        EcontractDeliveryInfoBo deliveryInfo = new EcontractDeliveryInfoBo();
        deliveryInfo.setDeliveryTypeUUID("uuid1");
        deliveryInfo.setSupportMTDelivery("support");
        deliveryInfo.setSupportSGV2_2Delivery("support");
        EcontractDeliverySG2_2InfoBo sg22Info = new EcontractDeliverySG2_2InfoBo();
        sg22Info.setSupportNewKS("support");
        deliveryInfo.setEcontractDeliverySG2_2InfoBo(sg22Info);
        // Setup middleContext
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS.getName(), Arrays.asList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);
        // Setup signDataFactor
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(false);
        middleContext.setSignDataFactor(signDataFactor);
        // Setup originContext
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.POIFEE.getName());
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfo));
        taskMap.put(1L, taskBo);
        originContext.setTaskIdAndTaskMap(taskMap);
        // 设置batchTypeEnum，避免空指针异常
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerName("Test Customer");
        originContext.setCustomerInfoBo(customerInfoBo);
        // Act
        PdfContentInfoBo result = performanceServiceSg22NewKsPdfMaker.makePdfContentInfoBo(originContext, middleContext);
        // Assert
        assertNotNull(result);
        assertNotNull(result.getPdfMetaContent());
        assertFalse(result.getPdfBizContent().isEmpty());
        assertEquals(PdfConstant.POI_SIGNKEY, result.getPdfMetaContent().get("partAEstamp"));
        assertEquals("Test Customer", result.getPdfMetaContent().get("partAStampName"));
    }

    /**
     * Test when WmCustomerException is thrown due to missing task map
     */
    @Test(expected = WmCustomerException.class)
    public void testMakePdfContentInfoBo_ThrowsWmCustomerException() throws Throwable {
        // Arrange
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        // Setup signDataFactor
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(false);
        middleContext.setSignDataFactor(signDataFactor);
        // Setup middleContext with empty pdfDataMap
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        middleContext.setPdfDataMap(pdfDataMap);
        // 设置batchTypeEnum，避免空指针异常
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        // Act - should throw WmCustomerException due to missing task map
        performanceServiceSg22NewKsPdfMaker.makePdfContentInfoBo(originContext, middleContext);
    }

    /**
     * Test when TException is thrown due to invalid task configuration
     */
    @Test(expected = WmCustomerException.class)
    public void testMakePdfContentInfoBo_ThrowsTException() throws Throwable {
        // Arrange
        EcontractBatchContextBo originContext = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleContext = new EcontractBatchMiddleBo();
        // Setup signDataFactor with multi poi
        EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
        signDataFactor.setDeliveryMultiWmPoi(true);
        middleContext.setSignDataFactor(signDataFactor);
        // Setup middleContext
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.PERFORMANCE_SERVICE_SG_22_NKS.getName(), Arrays.asList("uuid1"));
        middleContext.setPdfDataMap(pdfDataMap);
        // Setup originContext with empty task map
        originContext.setTaskIdAndTaskMap(new HashMap<>());
        // 设置batchTypeEnum，避免空指针异常
        originContext.setBatchTypeEnum(EcontractBatchTypeEnum.DELIVERY);
        // Act - should throw WmCustomerException due to missing required task
        performanceServiceSg22NewKsPdfMaker.makePdfContentInfoBo(originContext, middleContext);
    }

    /**
     * Test exception case for fillSupportData method
     * Scenario: When null parameters are provided
     */
    @Test(expected = NullPointerException.class)
    public void testFillSupportDataException() throws Throwable {
        try {
            fillSupportDataMethod.invoke(performanceServiceSg22NewKsPdfMaker, null, null, null);
        } catch (Exception e) {
            if (e.getCause() instanceof NullPointerException) {
                throw (NullPointerException) e.getCause();
            }
            throw e;
        }
    }

    /**
     * Test boundary case for fillSupportData method
     * Scenario: When SG2.2 support conditions are not met
     */
    @Test
    public void testFillSupportDataBoundary() throws Throwable {
        // arrange
        when(temp.getSupportMTDelivery()).thenReturn("N");
        when(temp.getSupportSGV2_2Delivery()).thenReturn("N");
        when(temp.getEcontractDeliverySG2_2InfoBo()).thenReturn(null);
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> pdfMetaContent = new HashMap<>();
        // act
        fillSupportDataMethod.invoke(performanceServiceSg22NewKsPdfMaker, temp, pdfMetaContent, pdfBizContent);
        // assert
        verify(temp, times(1)).setDeliveryArea(null);
        verify(temp, times(1)).setEcontractDeliveryWholeCityInfoBo(null);
        verify(temp, times(1)).setEcontractDeliveryAggregationInfoBo(null);
        assertTrue(pdfBizContent.isEmpty());
    }
}