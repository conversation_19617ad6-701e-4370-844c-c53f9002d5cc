package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.newpdftemplet;

import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;

import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * WmEcontractMultiAggregationInfoSG20PdfMakerTest
 *
 * @Author: wangyongfang
 * @Date: 2024-10-21
 */
public class WmEcontractMultiAggregationInfoSG20PdfMakerTest extends BaseStaticMockTest {

    @InjectMocks
    private WmEcontractMultiAggregationInfoSG20PdfMaker wmEcontractMultiAggregationInfoSG20PdfMaker;

    private MockedStatic<MapUtil> mapUtil;

    private MockedStatic<WmEcontractContextUtil> wmEcontractContextUtilMockedStatic;

    @Before
    public void setUp() {
        super.init();
        mapUtil = mockStatic(MapUtil.class);
        wmEcontractContextUtilMockedStatic = mockStatic(WmEcontractContextUtil.class);
    }
    @After
    public void after() {
        super.after();
        if (mapUtil != null) {
            mapUtil.close();
        }
        if (null  != wmEcontractContextUtilMockedStatic) {
            wmEcontractContextUtilMockedStatic.close();
        }
    }

    /**
     * 测试 makePdfContentInfoBo 方法，正常情况
     */
    @Test
    public void testMakePdfContentInfoBoNormal() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleContext = mock(EcontractBatchMiddleBo.class);
        when(middleContext.getPdfDataMap()).thenReturn(Collections.singletonMap(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_0.getName(), Collections.singletonList("uuid")));

        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        when(middleContext.getSignDataFactor()).thenReturn(signDataFactor);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext("{}");

        // 模拟WmEcontractContextUtil.selectDeliveryEcontractTaskBo方法
        when(WmEcontractContextUtil.selectDeliveryEcontractTaskBo(any(EcontractBatchContextBo.class), any(EcontractSignDataFactor.class))).thenReturn(taskBo);

        // 模拟WmEcontractContextUtil.getCustomerName方法
        when(WmEcontractContextUtil.getCustomerName(any(EcontractBatchContextBo.class))).thenReturn("Test Customer");

        configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt(anyString(), anyInt())).thenReturn(2209, 0);
        when(MapUtil.Object2Map(any())).thenReturn(Collections.singletonMap("key", "value"));

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        // act
        PdfContentInfoBo result = wmEcontractMultiAggregationInfoSG20PdfMaker.makePdfContentInfoBo(originContext, middleContext);

        // assert
        assertNotNull(result);
        assertEquals(Integer.valueOf(2209), result.getPdfTemplateId());
        assertEquals(Integer.valueOf(0), result.getPdfTemplateVersion());
        assertTrue(result.getPdfBizContent().isEmpty());
        assertFalse(result.getPdfMetaContent().isEmpty());
    }

    /**
     * 测试 makePdfContentInfoBo 方法，当 middleContext.getPdfDataMap() 返回空
     */
    @Test(expected = NullPointerException.class)
    public void testMakePdfContentInfoBoWithEmptyPdfDataMap() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleContext = mock(EcontractBatchMiddleBo.class);
        when(middleContext.getPdfDataMap()).thenReturn(Collections.emptyMap());

        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        when(middleContext.getSignDataFactor()).thenReturn(signDataFactor);

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext("{}");

        // 模拟WmEcontractContextUtil.selectDeliveryEcontractTaskBo方法
        when(WmEcontractContextUtil.selectDeliveryEcontractTaskBo(any(EcontractBatchContextBo.class), any(EcontractSignDataFactor.class))).thenReturn(taskBo);

        // act
        wmEcontractMultiAggregationInfoSG20PdfMaker.makePdfContentInfoBo(originContext, middleContext);

        // assert
        // Expect NullPointerException
    }

    /**
     * 测试 makePdfContentInfoBo 方法，当 ConfigUtilAdapter.getInt 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testMakePdfContentInfoBoWithConfigUtilAdapterException() throws Throwable {
        // arrange
        EcontractBatchContextBo originContext = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleContext = mock(EcontractBatchMiddleBo.class);
        when(middleContext.getPdfDataMap()).thenReturn(Collections.singletonMap(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_SHANGOUV2_0.getName(), Collections.singletonList("uuid")));

        EcontractSignDataFactor signDataFactor = mock(EcontractSignDataFactor.class);
        when(middleContext.getSignDataFactor()).thenReturn(signDataFactor);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext("{}");

        // 模拟WmEcontractContextUtil.selectDeliveryEcontractTaskBo方法
        when(WmEcontractContextUtil.selectDeliveryEcontractTaskBo(any(EcontractBatchContextBo.class), any(EcontractSignDataFactor.class))).thenReturn(taskBo);

        // 模拟WmEcontractContextUtil.getCustomerName方法
        when(WmEcontractContextUtil.getCustomerName(any(EcontractBatchContextBo.class))).thenReturn("Test Customer");

        // 设置batchTypeEnum，避免空指针异常
        when(originContext.getBatchTypeEnum()).thenReturn(EcontractBatchTypeEnum.DELIVERY);

        configUtilAdapterMockedStatic.when(() -> ConfigUtilAdapter.getInt(anyString(), anyInt())).thenThrow(new RuntimeException("ConfigUtilAdapter exception"));

        // act
        wmEcontractMultiAggregationInfoSG20PdfMaker.makePdfContentInfoBo(originContext, middleContext);

        // assert
        // Expect RuntimeException
        Assert.fail();
    }
}