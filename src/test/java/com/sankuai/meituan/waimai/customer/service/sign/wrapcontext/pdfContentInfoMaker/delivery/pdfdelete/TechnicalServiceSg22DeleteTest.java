package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TechnicalServiceSg22DeleteTest {

    @Mock
    private Map<String, Collection<SignTemplateEnum>> tabPdfMap;

    @Mock
    private Map<String, List<String>> pdfDataMap;

    private TechnicalServiceSg22Delete technicalServiceSg22Delete;

    @Before
    public void setUp() {
        technicalServiceSg22Delete = new TechnicalServiceSg22Delete();
    }

    @Test
    public void testDeletePdfDataMapContainsKey() throws Throwable {
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName())).thenReturn(Collections.singletonList("TECHNICAL_SERVICE_SG_22"));
        technicalServiceSg22Delete.delete(tabPdfMap, pdfDataMap);
        verify(tabPdfMap, never()).get(anyString());
    }

    @Test
    public void testDeleteTabPdfMapContainsKey() throws Throwable {
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName())).thenReturn(null);
        // Ensure the list is mutable
        when(tabPdfMap.get(SignTemplateEnum.TECHNICAL_SERVICE_SG_22.getTab())).thenReturn(new ArrayList<>(Collections.singletonList(SignTemplateEnum.TECHNICAL_SERVICE_SG_22)));
        technicalServiceSg22Delete.delete(tabPdfMap, pdfDataMap);
        verify(tabPdfMap).get(SignTemplateEnum.TECHNICAL_SERVICE_SG_22.getTab());
    }

    @Test
    public void testDeleteBothMapsNotContainsKey() throws Throwable {
        when(pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName())).thenReturn(null);
        // Ensure the list is mutable
        when(tabPdfMap.get(SignTemplateEnum.TECHNICAL_SERVICE_SG_22.getTab())).thenReturn(null);
        technicalServiceSg22Delete.delete(tabPdfMap, pdfDataMap);
        verify(tabPdfMap).get(SignTemplateEnum.TECHNICAL_SERVICE_SG_22.getTab());
    }
}