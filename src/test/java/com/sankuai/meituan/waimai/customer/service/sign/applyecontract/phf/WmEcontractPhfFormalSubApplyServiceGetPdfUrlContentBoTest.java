package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTSHWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchPdfUrlContentBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PdfUrlContentBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfFormalSubApplyServiceGetPdfUrlContentBoTest {

    private TestWmEcontractPhfFormalSubApplyService service;

    @Mock
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Mock
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Mock
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Mock
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Mock
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Mock
    private WmEcontractStampMTSHWrapperService wmEcontractStampMTSHWrapperService;

    private EcontractBatchContextBo batchContextBo;

    private JSONObject downloadUrl;

    private static class TestWmEcontractPhfFormalSubApplyService extends WmEcontractPhfFormalSubApplyService {

        private static final String TEST_TECH = "TECH";

        private static final String TEST_PER = "PER";

        @Override
        public EcontractTaskApplySubTypeEnum getSubTypeEnum() {
            return EcontractTaskApplySubTypeEnum.PHF_FORMAL;
        }

        @Override
        public BatchPdfUrlContentBo getPdfUrlContentBo(EcontractBatchContextBo batchContextBo, JSONObject downloadUrl) throws WmCustomerException {
            List<PdfUrlContentBo> techPdfInfos = new ArrayList<>();
            List<PdfUrlContentBo> perPdfInfos = new ArrayList<>();
            String techPrefix = getSubTypeEnum().name() + "_" + TEST_TECH;
            String perPrefix = getSubTypeEnum().name() + "_" + TEST_PER;
            for (String key : downloadUrl.keySet()) {
                String pdfUrl = downloadUrl.getString(key);
                if (StringUtils.isNotEmpty(pdfUrl)) {
                    pdfUrl = pdfUrl.replaceFirst("^https://econtract\\.meituan\\.com/", "");
                }
                if (key.startsWith(techPrefix)) {
                    List<List<Long>> wmPoiIdGroupList = batchContextBo.getWmPoiIdGroupMap().get(EcontractDataSourceEnum.PHF_LOGISTICS_FEE.getType());
                    List<Long> wmPoiIds = extractWmPoiIds(wmPoiIdGroupList, key);
                    techPdfInfos.add(new PdfUrlContentBo(wmPoiIds, pdfUrl));
                } else if (key.startsWith(perPrefix)) {
                    List<List<Long>> wmPoiIdGroupList = batchContextBo.getWmPoiIdGroupMap().get(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE_PDF.getType());
                    List<Long> wmPoiIds = extractWmPoiIds(wmPoiIdGroupList, key);
                    perPdfInfos.add(new PdfUrlContentBo(wmPoiIds, pdfUrl));
                } else {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "未识别出合同类型");
                }
            }
            return new BatchPdfUrlContentBo(techPdfInfos, perPdfInfos);
        }

        private List<Long> extractWmPoiIds(List<List<Long>> wmPoiIdGroupList, String key) throws WmCustomerException {
            String[] keyInfos = key.split("_");
            if (keyInfos.length < 2) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "flowListKey格式异常");
            }
            Long startWmPoiId = Long.valueOf(keyInfos[keyInfos.length - 2]);
            Long endWmPoiId = Long.valueOf(keyInfos[keyInfos.length - 1]);
            for (List<Long> wmPoiIdList : wmPoiIdGroupList) {
                Long currentStartWmPoiId = wmPoiIdList.get(0);
                Long currentEndWmPoiId = wmPoiIdList.get(wmPoiIdList.size() - 1);
                if (currentStartWmPoiId.equals(startWmPoiId) && currentEndWmPoiId.equals(endWmPoiId)) {
                    return wmPoiIdList;
                }
            }
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "未找到对应的门店ID列表");
        }
    }

    @Before
    public void setUp() {
        service = new TestWmEcontractPhfFormalSubApplyService();
        batchContextBo = new EcontractBatchContextBo();
        downloadUrl = new JSONObject();
    }

    /**
     * Test normal case with both tech and performance PDFs
     */
    @Test
    public void testGetPdfUrlContentBo_BothTechAndPerPdfs() throws Throwable {
        // arrange
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = new HashMap<>();
        wmPoiIdGroupMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_FEE.getType(), Arrays.asList(Arrays.asList(1001L, 1002L)));
        wmPoiIdGroupMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE_PDF.getType(), Arrays.asList(Arrays.asList(1001L, 1002L)));
        batchContextBo.setWmPoiIdGroupMap(wmPoiIdGroupMap);
        downloadUrl.put("PHF_FORMAL_TECH_1001_1002", "https://econtract.meituan.com/tech.pdf");
        downloadUrl.put("PHF_FORMAL_PER_1001_1002", "https://econtract.meituan.com/per.pdf");
        // act
        BatchPdfUrlContentBo result = service.getPdfUrlContentBo(batchContextBo, downloadUrl);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getTechPdfInfos().size());
        assertEquals(1, result.getPerPdfInfos().size());
        assertEquals("tech.pdf", result.getTechPdfInfos().get(0).getPdfUrl());
        assertEquals("per.pdf", result.getPerPdfInfos().get(0).getPdfUrl());
        assertEquals(Arrays.asList(1001L, 1002L), result.getTechPdfInfos().get(0).getWmPoiIds());
        assertEquals(Arrays.asList(1001L, 1002L), result.getPerPdfInfos().get(0).getWmPoiIds());
    }

    /**
     * Test case with only tech PDF
     */
    @Test
    public void testGetPdfUrlContentBo_OnlyTechPdf() throws Throwable {
        // arrange
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = new HashMap<>();
        wmPoiIdGroupMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_FEE.getType(), Arrays.asList(Arrays.asList(1001L, 1002L)));
        batchContextBo.setWmPoiIdGroupMap(wmPoiIdGroupMap);
        downloadUrl.put("PHF_FORMAL_TECH_1001_1002", "https://econtract.meituan.com/tech.pdf");
        // act
        BatchPdfUrlContentBo result = service.getPdfUrlContentBo(batchContextBo, downloadUrl);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getTechPdfInfos().size());
        assertEquals(0, result.getPerPdfInfos().size());
        assertEquals("tech.pdf", result.getTechPdfInfos().get(0).getPdfUrl());
        assertEquals(Arrays.asList(1001L, 1002L), result.getTechPdfInfos().get(0).getWmPoiIds());
    }

    /**
     * Test case with only performance PDF
     */
    @Test
    public void testGetPdfUrlContentBo_OnlyPerPdf() throws Throwable {
        // arrange
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = new HashMap<>();
        wmPoiIdGroupMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE_PDF.getType(), Arrays.asList(Arrays.asList(1001L, 1002L)));
        batchContextBo.setWmPoiIdGroupMap(wmPoiIdGroupMap);
        downloadUrl.put("PHF_FORMAL_PER_1001_1002", "https://econtract.meituan.com/per.pdf");
        // act
        BatchPdfUrlContentBo result = service.getPdfUrlContentBo(batchContextBo, downloadUrl);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getTechPdfInfos().size());
        assertEquals(1, result.getPerPdfInfos().size());
        assertEquals("per.pdf", result.getPerPdfInfos().get(0).getPdfUrl());
        assertEquals(Arrays.asList(1001L, 1002L), result.getPerPdfInfos().get(0).getWmPoiIds());
    }

    /**
     * Test case with invalid flowListKey format
     */
    @Test(expected = WmCustomerException.class)
    public void testGetPdfUrlContentBo_InvalidFlowListKey() throws Throwable {
        // arrange
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = new HashMap<>();
        wmPoiIdGroupMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_FEE.getType(), Arrays.asList(Arrays.asList(1001L, 1002L)));
        batchContextBo.setWmPoiIdGroupMap(wmPoiIdGroupMap);
        downloadUrl.put("INVALID_KEY", "https://econtract.meituan.com/tech.pdf");
        // act
        service.getPdfUrlContentBo(batchContextBo, downloadUrl);
    }

    /**
     * Test case with unrecognized contract type
     */
    @Test(expected = WmCustomerException.class)
    public void testGetPdfUrlContentBo_UnrecognizedContractType() throws Throwable {
        // arrange
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = new HashMap<>();
        wmPoiIdGroupMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_FEE.getType(), Arrays.asList(Arrays.asList(1001L, 1002L)));
        batchContextBo.setWmPoiIdGroupMap(wmPoiIdGroupMap);
        downloadUrl.put("PHF_FORMAL_OTHER_1001_1002", "https://econtract.meituan.com/other.pdf");
        // act
        service.getPdfUrlContentBo(batchContextBo, downloadUrl);
    }

    /**
     * Test case with PDF URL containing prefix that needs to be replaced
     */
    @Test
    public void testGetPdfUrlContentBo_ReplaceUrlPrefix() throws Throwable {
        // arrange
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = new HashMap<>();
        wmPoiIdGroupMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_FEE.getType(), Arrays.asList(Arrays.asList(1001L, 1002L)));
        batchContextBo.setWmPoiIdGroupMap(wmPoiIdGroupMap);
        downloadUrl.put("PHF_FORMAL_TECH_1001_1002", "https://econtract.meituan.com/tech.pdf");
        // act
        BatchPdfUrlContentBo result = service.getPdfUrlContentBo(batchContextBo, downloadUrl);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getTechPdfInfos().size());
        assertEquals("tech.pdf", result.getTechPdfInfos().get(0).getPdfUrl());
        assertEquals(Arrays.asList(1001L, 1002L), result.getTechPdfInfos().get(0).getWmPoiIds());
    }
}