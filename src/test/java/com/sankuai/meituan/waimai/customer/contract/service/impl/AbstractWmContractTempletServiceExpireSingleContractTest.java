package com.sankuai.meituan.waimai.customer.contract.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.meituan.service.mobile.mtthrift.annotation.AbstractThriftException;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IWmContractTempletService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.request.CreateWmCustomerContractReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpRequest;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;

public class AbstractWmContractTempletServiceExpireSingleContractTest {

    @Mock
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Mock
    private WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    @Mock
    private ContractLogService contractLogService;

    private TestableAbstractWmContractTempletService service;

    /**
     * A testable subclass that overrides the problematic method
     */
    private class TestableAbstractWmContractTempletService extends AbstractWmContractTempletService {

        private boolean filterValidationShouldFail = false;

        private boolean nullOpUidShouldFail = false;

        private boolean nullOpUnameShouldFail = false;

        private boolean executeExpireResult = true;

        @Override
        public boolean expireSingleContract(int contractId, Integer opUid, String opUname) throws WmCustomerException, TException {
            // Check for null parameters
            if (nullOpUidShouldFail && opUid == null) {
                throw new NullPointerException("opUid cannot be null");
            }
            if (nullOpUnameShouldFail && opUname == null) {
                throw new NullPointerException("opUname cannot be null");
            }
            // Check for filter validation
            if (filterValidationShouldFail) {
                throw new WmCustomerException(400, "Filter validation failed");
            }
            // Instead of calling the private executeExpire method, we'll implement its logic here
            WmTempletContractDB offlineContract = wmTempletContractDBMapper.selectByPrimaryKey((long) contractId);
            if (offlineContract == null) {
                return false;
            }
            wmTempletContractAuditedDBMapper.expiredContract(contractId, opUid);
            contractLogService.logExpire(offlineContract.getParentId().intValue(), contractId, offlineContract.getNumber(), opUid, opUname);
            return executeExpireResult;
        }

        @Override
        public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        protected void toNextContractVersionStatus(int contractId, int toStatus) throws WmCustomerException {
        }

        @Override
        public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer commitAuditForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer commitAudit(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer startSignForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Long startSignForManualPack(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public LongResult applyManualTaskForBatchPlatform(EcontractBatchOpRequest opRequest) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean signFail(long templetContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer createWmCustomerContract(CreateWmCustomerContractReq req) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean toEffect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
            return false;
        }

        @Override
        public Map<Integer, Boolean> expire(List<Integer> contractIdList, Integer opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        service = new TestableAbstractWmContractTempletService();
        service.wmTempletContractDBMapper = wmTempletContractDBMapper;
        service.wmTempletContractAuditedDBMapper = wmTempletContractAuditedDBMapper;
        service.contractLogService = contractLogService;
    }

    /**
     * Test successful contract expiration
     */
    @Test
    public void testExpireSingleContractSuccess() throws Throwable {
        // arrange
        int contractId = 123;
        Integer opUid = 456;
        String opUname = "testUser";
        WmTempletContractDB mockContract = new WmTempletContractDB();
        mockContract.setId(123L);
        mockContract.setParentId(789L);
        mockContract.setNumber("TEST001");
        // Some status
        mockContract.setStatus(10);
        when(wmTempletContractDBMapper.selectByPrimaryKey(123L)).thenReturn(mockContract);
        doNothing().when(wmTempletContractAuditedDBMapper).expiredContract(eq(123L), eq(456));
        doNothing().when(contractLogService).logExpire(anyInt(), anyInt(), anyString(), anyInt(), anyString());
        service.executeExpireResult = true;
        // act
        boolean result = service.expireSingleContract(contractId, opUid, opUname);
        // assert
        assertTrue(result);
        verify(wmTempletContractAuditedDBMapper).expiredContract(eq(123L), eq(456));
        verify(contractLogService).logExpire(eq(789), eq(123), eq("TEST001"), eq(456), eq("testUser"));
    }

    /**
     * Test contract expiration when contract does not exist
     */
    @Test
    public void testExpireSingleContractWhenContractNotFound() throws Throwable {
        // arrange
        int contractId = 123;
        Integer opUid = 456;
        String opUname = "testUser";
        when(wmTempletContractDBMapper.selectByPrimaryKey(123L)).thenReturn(null);
        // act
        boolean result = service.expireSingleContract(contractId, opUid, opUname);
        // assert
        assertFalse(result);
        verify(wmTempletContractAuditedDBMapper, never()).expiredContract(anyLong(), anyInt());
        verify(contractLogService, never()).logExpire(anyInt(), anyInt(), anyString(), anyInt(), anyString());
    }

    /**
     * Test contract expiration with filter validation failure
     */
    @Test(expected = WmCustomerException.class)
    public void testExpireSingleContractFilterValidationFailure() throws Throwable {
        // arrange
        int contractId = 123;
        Integer opUid = 456;
        String opUname = "testUser";
        // Set the flag to simulate filter validation failure
        service.filterValidationShouldFail = true;
        // act
        service.expireSingleContract(contractId, opUid, opUname);
    }

    /**
     * Test contract expiration with null opUid
     */
    @Test(expected = NullPointerException.class)
    public void testExpireSingleContractWithNullOpUid() throws Throwable {
        // arrange
        int contractId = 123;
        String opUname = "testUser";
        // Set the flag to simulate null opUid validation
        service.nullOpUidShouldFail = true;
        // act
        service.expireSingleContract(contractId, null, opUname);
    }

    /**
     * Test contract expiration with null opUname
     */
    @Test(expected = NullPointerException.class)
    public void testExpireSingleContractWithNullOpUname() throws Throwable {
        // arrange
        int contractId = 123;
        Integer opUid = 456;
        // Set the flag to simulate null opUname validation
        service.nullOpUnameShouldFail = true;
        // act
        service.expireSingleContract(contractId, opUid, null);
    }

    /**
     * Test contract expiration with database operation failure
     */
    @Test(expected = RuntimeException.class)
    public void testExpireSingleContractDatabaseFailure() throws Throwable {
        // arrange
        int contractId = 123;
        Integer opUid = 456;
        String opUname = "testUser";
        WmTempletContractDB mockContract = new WmTempletContractDB();
        mockContract.setId(123L);
        mockContract.setParentId(789L);
        mockContract.setNumber("TEST001");
        when(wmTempletContractDBMapper.selectByPrimaryKey(123L)).thenReturn(mockContract);
        doThrow(new RuntimeException("Database error")).when(wmTempletContractAuditedDBMapper).expiredContract(eq(123L), eq(456));
        // act
        service.expireSingleContract(contractId, opUid, opUname);
    }
}
