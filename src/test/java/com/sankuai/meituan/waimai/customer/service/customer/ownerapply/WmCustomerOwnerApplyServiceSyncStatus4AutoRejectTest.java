package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.IntResult;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketSyncDto;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyRecord;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyRecordDao;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerRecordOpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Objects;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyServiceSyncStatus4AutoRejectTest {

    @InjectMocks
    private WmCustomerOwnerApplyService wmCustomerOwnerApplyService;

    @Mock
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    private WmCustomerOwnerApplyAudit audit;

    private IntResult expectedResult;

    @Mock
    private WmEmployeeService wmEmployeeService;

    @Mock
    private WmCustomerOwnerApplyRecordDao wmCustomerOwnerApplyRecordDao;

    @Before
    public void setUp() {
        audit = new WmCustomerOwnerApplyAudit();
        audit.setTaskId(123);
        expectedResult = new IntResult(0);
    }

    private IntResult invokePrivateSyncStatus4AutoReject(WmCustomerOwnerApplyAudit audit) throws Throwable {
        Method method = WmCustomerOwnerApplyService.class.getDeclaredMethod("syncStatus4AutoReject", WmCustomerOwnerApplyAudit.class);
        method.setAccessible(true);
        try {
            return (IntResult) method.invoke(wmCustomerOwnerApplyService, audit);
        } catch (InvocationTargetException e) {
            throw e.getTargetException();
        }
    }

    private IntResult syncTicketStatus(Integer opUid, WmCustomerOwnerApplyAudit audit) throws Exception {
        Method method = WmCustomerOwnerApplyService.class.getDeclaredMethod("syncTicketStatus", Integer.class, WmCustomerOwnerApplyAudit.class);
        method.setAccessible(true);
        try {
            return (IntResult) method.invoke(wmCustomerOwnerApplyService, opUid, audit);
        } catch (InvocationTargetException e) {
            Throwable cause = e.getCause();
            if (cause instanceof WmSchCantException) {
                throw (WmSchCantException) cause;
            }
            if (cause instanceof NullPointerException) {
                throw (NullPointerException) cause;
            }
            throw e;
        }
    }

    private Method getPrivateMethod() throws Exception {
        Method method = WmCustomerOwnerApplyService.class.getDeclaredMethod("addCancelApplyRecord", WmCustomerOwnerApply.class, Integer.class);
        method.setAccessible(true);
        return method;
    }

    /**
     * Test successful sync status for auto reject
     */
    @Test
    public void testSyncStatus4AutoReject_Success() throws Throwable {
        // arrange
        when(wmCrmTicketThriftServiceAdapter.syncStatus(any(WmTicketSyncDto.class))).thenReturn(expectedResult);
        // act
        IntResult result = invokePrivateSyncStatus4AutoReject(audit);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(wmCrmTicketThriftServiceAdapter).syncStatus(any(WmTicketSyncDto.class));
    }

    /**
     * Test sync status when WmSchCantException is thrown
     */
    @Test(expected = WmCustomerException.class)
    public void testSyncStatus4AutoReject_WhenWmSchCantException() throws Throwable {
        // arrange
        when(wmCrmTicketThriftServiceAdapter.syncStatus(any(WmTicketSyncDto.class))).thenThrow(new WmSchCantException());
        // act & assert
        invokePrivateSyncStatus4AutoReject(audit);
    }

    /**
     * Test sync status with null audit object
     */
    @Test(expected = NullPointerException.class)
    public void testSyncStatus4AutoReject_WithNullAudit() throws Throwable {
        // act & assert
        invokePrivateSyncStatus4AutoReject(null);
    }

    /**
     * Test sync status verifying WmTicketSyncDto properties
     */
    @Test
    public void testSyncStatus4AutoReject_VerifyTicketSyncDto() throws Throwable {
        // arrange
        when(wmCrmTicketThriftServiceAdapter.syncStatus(any(WmTicketSyncDto.class))).thenReturn(expectedResult);
        // act
        invokePrivateSyncStatus4AutoReject(audit);
        // assert
        verify(wmCrmTicketThriftServiceAdapter).syncStatus(any(WmTicketSyncDto.class));
    }

    /**
     * Test sync status when WmSchCantException contains error message
     */
    @Test
    public void testSyncStatus4AutoReject_WmSchCantExceptionWithMessage() throws Throwable {
        // arrange
        String errorMsg = "Error syncing status";
        when(wmCrmTicketThriftServiceAdapter.syncStatus(any(WmTicketSyncDto.class))).thenThrow(new WmSchCantException(500, errorMsg));
        try {
            // act
            invokePrivateSyncStatus4AutoReject(audit);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            // assert
            assertEquals(CustomerConstants.RESULT_CODE_ERROR, e.getCode());
            assertEquals("同步驳回状态到任务系统异常", e.getMessage());
        }
    }

    /**
     * Test case for successful sync ticket status with valid employee
     */
    @Test
    public void testSyncTicketStatus_WithValidEmployee() throws Throwable {
        // arrange
        Integer opUid = 1001;
        WmEmploy wmEmploy = new WmEmploy();
        wmEmploy.setName("TestEmployee");
        when(wmEmployeeService.getWmEmployById(opUid)).thenReturn(wmEmploy);
        when(wmCrmTicketThriftServiceAdapter.syncStatus(any(WmTicketSyncDto.class))).thenReturn(expectedResult);
        // act
        IntResult result = syncTicketStatus(opUid, audit);
        // assert
        assertEquals(expectedResult, result);
        verify(wmCrmTicketThriftServiceAdapter).syncStatus(argThat(dto -> dto.getTicketId() == audit.getTaskId() && dto.getStatus() == 3 && dto.getStage() == CustomerOwnerApplyStatusEnum.REJECT.getCode() && dto.getOpUid() == opUid && "TestEmployee".equals(dto.getOpName()) && dto.getWmTicketOperationId() == 0 && "撤回申请".equals(dto.getWmTicketOperationName())));
    }

    /**
     * Test case for sync ticket status when employee is null
     */
    @Test
    public void testSyncTicketStatus_WithNullEmployee() throws Throwable {
        // arrange
        Integer opUid = 1001;
        when(wmEmployeeService.getWmEmployById(opUid)).thenReturn(null);
        when(wmCrmTicketThriftServiceAdapter.syncStatus(any(WmTicketSyncDto.class))).thenReturn(expectedResult);
        // act
        IntResult result = syncTicketStatus(opUid, audit);
        // assert
        assertEquals(expectedResult, result);
        verify(wmCrmTicketThriftServiceAdapter).syncStatus(argThat(dto -> dto.getTicketId() == audit.getTaskId() && dto.getStatus() == 3 && dto.getStage() == CustomerOwnerApplyStatusEnum.REJECT.getCode() && dto.getOpUid() == opUid && "".equals(dto.getOpName()) && dto.getWmTicketOperationId() == 0 && "撤回申请".equals(dto.getWmTicketOperationName())));
    }

    /**
     * Test case for sync ticket status when service throws exception
     */
    @Test(expected = WmSchCantException.class)
    public void testSyncTicketStatus_WhenServiceThrowsException() throws Throwable {
        // arrange
        Integer opUid = 1001;
        WmEmploy wmEmploy = new WmEmploy();
        wmEmploy.setName("TestEmployee");
        when(wmEmployeeService.getWmEmployById(opUid)).thenReturn(wmEmploy);
        when(wmCrmTicketThriftServiceAdapter.syncStatus(any(WmTicketSyncDto.class))).thenThrow(new WmSchCantException());
        // act
        syncTicketStatus(opUid, audit);
    }

    /**
     * Test case for sync ticket status with null opUid
     */
    @Test(expected = NullPointerException.class)
    public void testSyncTicketStatus_WithNullOpUid() throws Throwable {
        // arrange
        Integer opUid = null;
        // act
        syncTicketStatus(opUid, audit);
    }

    /**
     * Test successful record creation and insertion
     */
    @Test
    public void testAddCancelApplyRecordSuccess() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(123);
        apply.setCustomerId(456);
        apply.setMtCustomerId(789L);
        Integer opUid = 100;
        ArgumentCaptor<WmCustomerOwnerApplyRecord> recordCaptor = ArgumentCaptor.forClass(WmCustomerOwnerApplyRecord.class);
        // act
        getPrivateMethod().invoke(wmCustomerOwnerApplyService, apply, opUid);
        // assert
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(recordCaptor.capture());
        WmCustomerOwnerApplyRecord capturedRecord = recordCaptor.getValue();
        assertEquals(Long.valueOf(123), capturedRecord.getApplyId());
        assertEquals(Long.valueOf(456), capturedRecord.getCustomerId());
        assertEquals(Integer.valueOf(100), capturedRecord.getOpUid());
        assertEquals("", capturedRecord.getRemark());
        assertEquals(Integer.valueOf(CustomerOwnerRecordOpTypeEnum.EDIT.getCode()), capturedRecord.getOpType());
        assertEquals(Integer.valueOf((int) WmCustomerOplogBo.OpModuleType.CUSTOMER.getType()), capturedRecord.getModuleId());
        assertEquals(String.format("客户责任人申请任务撤回 \n申请编号：%s\n申请客户id: %s\n申请进度：已取消", apply.getId(), apply.getMtCustomerId()), capturedRecord.getContent());
    }

    /**
     * Test handling of exception during record insertion
     */
    @Test
    public void testAddCancelApplyRecordWhenInsertThrowsException() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(123);
        apply.setCustomerId(456);
        apply.setMtCustomerId(789L);
        Integer opUid = 100;
        doThrow(new RuntimeException("DB Error")).when(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(any(WmCustomerOwnerApplyRecord.class));
        // act
        getPrivateMethod().invoke(wmCustomerOwnerApplyService, apply, opUid);
        // assert
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(any(WmCustomerOwnerApplyRecord.class));
    }

    /**
     * Test with null apply parameter
     */
    @Test
    public void testAddCancelApplyRecordWithNullApply() throws Throwable {
        // arrange
        Integer opUid = 100;
        try {
            // act
            getPrivateMethod().invoke(wmCustomerOwnerApplyService, null, opUid);
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof NullPointerException);
            verify(wmCustomerOwnerApplyRecordDao, never()).insertOwnerApplyRecord(any());
            return;
        }
        assertTrue("Expected NullPointerException was not thrown", false);
    }

    /**
     * Test with null opUid parameter
     */
    @Test
    public void testAddCancelApplyRecordWithNullOpUid() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(123);
        apply.setCustomerId(456);
        apply.setMtCustomerId(789L);
        ArgumentCaptor<WmCustomerOwnerApplyRecord> recordCaptor = ArgumentCaptor.forClass(WmCustomerOwnerApplyRecord.class);
        // act
        getPrivateMethod().invoke(wmCustomerOwnerApplyService, apply, null);
        // assert
        verify(wmCustomerOwnerApplyRecordDao).insertOwnerApplyRecord(recordCaptor.capture());
        WmCustomerOwnerApplyRecord capturedRecord = recordCaptor.getValue();
        assertEquals(null, capturedRecord.getOpUid());
    }
}
