package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDeliveryTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfFormalPerWrapperServiceImplTest {

    private WmEcontractPhfFormalPerWrapperServiceImpl wmEcontractPhfFormalPerWrapperService = new WmEcontractPhfFormalPerWrapperServiceImpl();

    private WmEcontractPhfFormalPerWrapperServiceImpl wrapper = new WmEcontractPhfFormalPerWrapperServiceImpl() {

        @Override
        public Map<String, String> generatePdfMetaContent(EcontractCustomerInfoBo customerInfoBo, EcontractDeliveryPhfInfoBo infoBo) throws WmCustomerException {
            Map<String, String> metaContent = new HashMap<>();
            metaContent.put("partAName", customerInfoBo.getCustomerName());
            return metaContent;
        }
    };

    private Method generateContractDescMethod;


    /**
     * Tests the generatePdfMetaContent method under normal conditions.
     */
    @Test
    public void testGeneratePdfMetaContentNormal() throws Throwable {
        // Arrange
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerName("testName");
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        // Act
        Map<String, String> result = wmEcontractPhfFormalPerWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        // Assert
        assertEquals("testName", result.get("partAName"));
        assertEquals("上海三快智送科技有限公司", result.get("partBName"));
    }

    /**
     * Tests the generatePdfMetaContent method when customerInfoBo is null.
     */
    @Test(expected = NullPointerException.class)
    public void testGeneratePdfMetaContentCustomerInfoBoNull() throws Throwable {
        // Arrange
        EcontractCustomerInfoBo customerInfoBo = null;
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        // Act
        wmEcontractPhfFormalPerWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
    }

    /**
     * Tests the generatePdfMetaContent method when infoBo is null.
     * This test case is revised to reflect the correct behavior if the method handles null values gracefully.
     */
    @Test
    public void testGeneratePdfMetaContentInfoBoNull() throws Throwable {
        // Arrange
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerName("testName");
        EcontractDeliveryPhfInfoBo infoBo = null;
        // Act
        Map<String, String> result = wmEcontractPhfFormalPerWrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        // Assert
        assertNotNull(result);
        assertEquals("testName", result.get("partAName"));
        assertEquals("上海三快智送科技有限公司", result.get("partBName"));
    }

    /**
     * Test case: Empty delivery info list
     * Expected: Throw WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapWithEmptyDeliveryInfoList() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(new ArrayList<>());
        taskBo.setApplyContext(JSON.toJSONString(batchDeliveryInfoBo));
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        contextBo.setCustomerInfoBo(new EcontractCustomerInfoBo());
        // act
        wrapper.wrap(contextBo);
    }


    @Test
    public void testGenerateContractDescNormal() throws Throwable {
        // arrange
        generateContractDescMethod = WmEcontractPhfFormalPerWrapperServiceImpl.class.getDeclaredMethod("generateContractDesc", List.class);
        generateContractDescMethod.setAccessible(true);

        EcontractDeliveryPhfInfoBo econtractDeliveryPhfInfoBo = new EcontractDeliveryPhfInfoBo();
        econtractDeliveryPhfInfoBo.setWmPoiName("测试门店");

        EcontractDeliveryInfoBo econtractDeliveryInfoBo = new EcontractDeliveryInfoBo();
        econtractDeliveryInfoBo.setEcontractDeliveryPhfInfoBo(econtractDeliveryPhfInfoBo);

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos = new ArrayList<>();

        econtractDeliveryInfoBos.add(econtractDeliveryInfoBo);
        econtractDeliveryInfoBos.add(econtractDeliveryInfoBo);
        econtractDeliveryInfoBos.add(econtractDeliveryInfoBo);
        // act
        String result = (String) generateContractDescMethod.invoke(wmEcontractPhfFormalPerWrapperService, econtractDeliveryInfoBos);
        // assert
        assertEquals("适用于测试门店,测试门店,测试门店", result);
    }

    @Test
    public void testGenerateContractDescBoundary() throws Throwable {
        // arrange
        generateContractDescMethod = WmEcontractPhfFormalPerWrapperServiceImpl.class.getDeclaredMethod("generateContractDesc", List.class);
        generateContractDescMethod.setAccessible(true);

        EcontractDeliveryPhfInfoBo econtractDeliveryPhfInfoBo = new EcontractDeliveryPhfInfoBo();
        econtractDeliveryPhfInfoBo.setWmPoiName("测试门店");

        EcontractDeliveryInfoBo econtractDeliveryInfoBo = new EcontractDeliveryInfoBo();
        econtractDeliveryInfoBo.setEcontractDeliveryPhfInfoBo(econtractDeliveryPhfInfoBo);

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            // Testing with more than DESC_POI_NAME_NUM (3)
            econtractDeliveryInfoBos.add(econtractDeliveryInfoBo);
        }
        // act
        String result = (String) generateContractDescMethod.invoke(wmEcontractPhfFormalPerWrapperService, econtractDeliveryInfoBos);
        // assert
        assertEquals("适用于测试门店,测试门店,测试门店...", result);
    }

    @Test
    public void testGenerateContractDescSingle() throws Throwable {
        // arrange
        generateContractDescMethod = WmEcontractPhfFormalPerWrapperServiceImpl.class.getDeclaredMethod("generateContractDesc", List.class);
        generateContractDescMethod.setAccessible(true);

        EcontractDeliveryPhfInfoBo econtractDeliveryPhfInfoBo = new EcontractDeliveryPhfInfoBo();
        econtractDeliveryPhfInfoBo.setWmPoiName("测试门店");

        EcontractDeliveryInfoBo econtractDeliveryInfoBo = new EcontractDeliveryInfoBo();
        econtractDeliveryInfoBo.setEcontractDeliveryPhfInfoBo(econtractDeliveryPhfInfoBo);

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos = new ArrayList<>();
        econtractDeliveryInfoBos.add(econtractDeliveryInfoBo);
        // act
        String result = (String) generateContractDescMethod.invoke(wmEcontractPhfFormalPerWrapperService, econtractDeliveryInfoBos);
        // assert
        assertEquals("适用于测试门店", result);
    }

    @Test
    public void testGenerateContractDescEmpty() throws Throwable {
        // arrange
        generateContractDescMethod = WmEcontractPhfFormalPerWrapperServiceImpl.class.getDeclaredMethod("generateContractDesc", List.class);
        generateContractDescMethod.setAccessible(true);

        EcontractDeliveryPhfInfoBo econtractDeliveryPhfInfoBo = new EcontractDeliveryPhfInfoBo();
        econtractDeliveryPhfInfoBo.setWmPoiName("测试门店");

        EcontractDeliveryInfoBo econtractDeliveryInfoBo = new EcontractDeliveryInfoBo();
        econtractDeliveryInfoBo.setEcontractDeliveryPhfInfoBo(econtractDeliveryPhfInfoBo);

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos = new ArrayList<>();
        // act
        String result = (String) generateContractDescMethod.invoke(wmEcontractPhfFormalPerWrapperService, econtractDeliveryInfoBos);
        // assert
        assertEquals("适用于", result);
    }

}