package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryAggregationInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

/**
 * 文档说明
 *
 * @Author: wangyongfang
 * @Date: 2024-10-22
 */
public class AggregationInfoDefaultSplitTest {

    @Mock
    private EcontractBatchMiddleBo middleContextMock;
    @Mock
    private EcontractDeliveryInfoBo deliveryInfoBoMock;
    @Mock
    private EcontractDeliveryAggregationInfoBo deliveryAggregationInfoBoMock;

    private AggregationInfoDefaultSplit aggregationInfoDefaultSplit;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        aggregationInfoDefaultSplit = new AggregationInfoDefaultSplit();
    }

    /**
     * 测试场景：正常情况下添加UUID到AGGREGATION_DELIVERY_DEFAULT
     */
    @Test
    public void testSplit_NormalCase() {
        // arrange
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        tabPdfMap.put("delivery_aggregation", Lists.newArrayList(SignTemplateEnum.DELIVERY_AGGREGATION_INFO));
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_DEFAULT.getName(), Lists.newArrayList());

        when(deliveryInfoBoMock.getFeeMode()).thenReturn("1");
        when(deliveryInfoBoMock.getSupportAggregationDelivery()).thenReturn("support");
        when(deliveryInfoBoMock.getEcontractDeliveryAggregationInfoBo()).thenReturn(deliveryAggregationInfoBoMock);
        when(deliveryAggregationInfoBoMock.getDeliveryAggregationVersion()).thenReturn("1");
        when(deliveryInfoBoMock.getDeliveryTypeUUID()).thenReturn("UUID_TEST");
        when(middleContextMock.getTabPdfMap()).thenReturn(tabPdfMap);
        when(middleContextMock.getPdfDataMap()).thenReturn(pdfDataMap);

        // act
        aggregationInfoDefaultSplit.split(deliveryInfoBoMock, middleContextMock);

        // assert
        verify(middleContextMock, times(1)).getPdfDataMap();
        assert pdfDataMap.get(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_DEFAULT.getName()).contains("UUID_TEST");
    }

    /**
     * 测试场景：当feeMode不是SHANGOU或SHANGOU_2_2时，不添加UUID
     */
    @Test
    public void testSplit_FeeModeNotMatch() {
        // arrange
        when(deliveryInfoBoMock.getFeeMode()).thenReturn("2"); // 不匹配的feeMode
        when(deliveryInfoBoMock.getSupportAggregationDelivery()).thenReturn("support");
        when(deliveryInfoBoMock.getEcontractDeliveryAggregationInfoBo()).thenReturn(deliveryAggregationInfoBoMock);
        when(deliveryAggregationInfoBoMock.getDeliveryAggregationVersion()).thenReturn("1");
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        when(middleContextMock.getTabPdfMap()).thenReturn(tabPdfMap);

        // act
        aggregationInfoDefaultSplit.split(deliveryInfoBoMock, middleContextMock);

        // assert
        Assert.assertEquals(0, tabPdfMap.size());
    }

    /**
     * 测试场景：当supportAggregationDelivery不匹配时，不添加UUID
     */
    @Test
    public void testSplit_SupportAggregationDeliveryNotMatch() {
        // arrange
        when(deliveryInfoBoMock.getFeeMode()).thenReturn("1");
        when(deliveryInfoBoMock.getSupportAggregationDelivery()).thenReturn("NOT_SUPPORT_MARK");
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        when(middleContextMock.getTabPdfMap()).thenReturn(tabPdfMap);

        // act
        aggregationInfoDefaultSplit.split(deliveryInfoBoMock, middleContextMock);

        // assert
//        verify(middleContextMock, never()).getPdfDataMap();
//        Assert.assertTrue();
        Assert.assertEquals(0, tabPdfMap.size());
    }

    /**
     * 测试场景：当deliveryAggregationInfoBo为null时，不添加UUID
     */
    @Test
    public void testSplit_DeliveryAggregationInfoBoIsNull() {
        // arrange
        when(deliveryInfoBoMock.getFeeMode()).thenReturn("1");
        when(deliveryInfoBoMock.getSupportAggregationDelivery()).thenReturn("SUPPORT_MARK");
        when(deliveryInfoBoMock.getEcontractDeliveryAggregationInfoBo()).thenReturn(null);
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        when(middleContextMock.getTabPdfMap()).thenReturn(tabPdfMap);

        // act
        aggregationInfoDefaultSplit.split(deliveryInfoBoMock, middleContextMock);

        // assert
//        verify(middleContextMock, never()).getPdfDataMap();
        Assert.assertEquals(0, tabPdfMap.size());
    }

    /**
     * 测试场景：当DeliveryAggregationVersion不匹配时，不添加UUID
     */
    @Test
    public void testSplit_DeliveryAggregationVersionNotMatch() {
        // arrange
        when(deliveryInfoBoMock.getFeeMode()).thenReturn("1");
        when(deliveryInfoBoMock.getSupportAggregationDelivery()).thenReturn("SUPPORT_MARK");
        when(deliveryInfoBoMock.getEcontractDeliveryAggregationInfoBo()).thenReturn(deliveryAggregationInfoBoMock);
        when(deliveryAggregationInfoBoMock.getDeliveryAggregationVersion()).thenReturn("NOT_MATCH_VERSION");
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        when(middleContextMock.getTabPdfMap()).thenReturn(tabPdfMap);

        // act
        aggregationInfoDefaultSplit.split(deliveryInfoBoMock, middleContextMock);

        // assert
//        verify(middleContextMock, never()).getPdfDataMap();
        Assert.assertEquals(0, tabPdfMap.size());
    }

    /**
     * 测试场景：当tabPdfMap中不包含DELIVERY_AGGREGATION_INFO或DELIVERY_MULTI_AGGREGATION_INFO时，不添加UUID
     */
    @Test
    public void testSplit_TabPdfMapNotContainRequiredTemplate() {
        // arrange
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        tabPdfMap.put("delivery_aggregation", Lists.newArrayList());
        when(deliveryInfoBoMock.getFeeMode()).thenReturn("1");
        when(deliveryInfoBoMock.getSupportAggregationDelivery()).thenReturn("support");
        when(deliveryInfoBoMock.getEcontractDeliveryAggregationInfoBo()).thenReturn(deliveryAggregationInfoBoMock);
        when(deliveryAggregationInfoBoMock.getDeliveryAggregationVersion()).thenReturn("1");
        when(middleContextMock.getTabPdfMap()).thenReturn(tabPdfMap);

        // act
        aggregationInfoDefaultSplit.split(deliveryInfoBoMock, middleContextMock);

        // assert
//        verify(middleContextMock, never()).getPdfDataMap();
        Assert.assertNull(tabPdfMap.get(DeliveryPdfDataTypeEnum.AGGREGATION_DELIVERY_DEFAULT.getName()));
    }
}