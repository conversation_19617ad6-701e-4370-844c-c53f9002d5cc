package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.netflix.config.ConcurrentCompositeConfiguration;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.configuration.AbstractConfiguration;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfJhsWrapperServiceImplTest {

    @InjectMocks
    private WmEcontractPhfJhsWrapperServiceImpl wrapperService;

    @Mock
    private EcontractCustomerInfoBo customerInfoBo;

    @Mock
    private EcontractDeliveryPhfInfoBo infoBo;

    /**
     * Test case when delivery info list is empty
     */
    @Test(expected = WmCustomerException.class)
    public void testWrap_WithEmptyDeliveryInfo_ShouldThrowException() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.PHF_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo deliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        deliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList());
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        wrapperService.wrap(contextBo);
        // Should throw WmCustomerException with message "原始数据缺失，pdf封装失败"
    }

    /**
     * Test case when task info is missing
     */
    @Test(expected = WmCustomerException.class)
    public void testWrap_WithMissingTask_ShouldThrowException() throws Throwable {
        // arrange
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setTaskIdAndTaskMap(new HashMap<>());
        // act
        wrapperService.wrap(contextBo);
        // Should throw WmCustomerException with message "不存在phf_delivery申请任务"
    }

    /**
     * 测试generatePdfMetaContent方法，正常情况
     * 由于无法提供有效的charge type code，预期会抛出WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testGeneratePdfMetaContentNormal() throws Throwable {
        // arrange
        Map<Integer, EcontractDeliveryPhfTechInfoBo> techInfo = new HashMap<>();
        EcontractDeliveryPhfTechInfoBo techInfoBo = new EcontractDeliveryPhfTechInfoBo();
        techInfoBo.setBaseFee("10");
        techInfoBo.setBaseSubsidy("5");
        techInfo.put(EcontractDeliveryTypeEnum.PHF_SELF.getType(), techInfoBo);
        Map<Integer, EcontractDeliveryPhfPerInfoBo> perInfo = new HashMap<>();
        Map<String, EcontractdeliveryPhfChargeInfoBo> chargeInfo = new HashMap<>();
        EcontractdeliveryPhfChargeInfoBo chargeInfoBo = new EcontractdeliveryPhfChargeInfoBo();
        chargeInfoBo.setBaseFee("20");
        chargeInfoBo.setSeasonFee("5");
        chargeInfoBo.setTimeFee("3");
        chargeInfoBo.setDistanceFee("2");
        chargeInfoBo.setBaseSubsidy("1");
        chargeInfoBo.setTopFee("30");
        chargeInfo.put("TEST", chargeInfoBo);
        EcontractDeliveryPhfPerInfoBo perInfoBo = new EcontractDeliveryPhfPerInfoBo(chargeInfo);
        perInfo.put(EcontractDeliveryTypeEnum.PHF_SELF.getType(), perInfoBo);
        when(infoBo.getTechInfo()).thenReturn(techInfo);
        when(infoBo.getPerInfo()).thenReturn(perInfo);
        when(infoBo.getWmPoiId()).thenReturn("123");
        when(infoBo.getWmPoiName()).thenReturn("Test Poi");
        when(customerInfoBo.getCustomerName()).thenReturn("Test Customer");
        // act & assert
        // This will throw WmCustomerException with message "拼好饭合同履约服务费类型异常"
        wrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
    }

    /**
     * 测试generatePdfMetaContent方法，customerInfoBo为空
     */
    @Test(expected = NullPointerException.class)
    public void testGeneratePdfMetaContentCustomerInfoBoNull() throws Throwable {
        // arrange
        // act
        wrapperService.generatePdfMetaContent(null, infoBo);
        // assert
    }

    /**
     * 测试generatePdfMetaContent方法，infoBo为空
     */
    @Test(expected = NullPointerException.class)
    public void testGeneratePdfMetaContentInfoBoNull() throws Throwable {
        // arrange
        // act
        wrapperService.generatePdfMetaContent(customerInfoBo, null);
        // assert
    }

    /**
     * 测试generatePdfMetaContent方法，infoBo中的techInfo或perInfo为空
     */
    @Test
    public void testGeneratePdfMetaContentTechInfoOrPerInfoNull() throws Throwable {
        // arrange
        when(infoBo.getTechInfo()).thenReturn(new HashMap<>());
        when(infoBo.getPerInfo()).thenReturn(new HashMap<>());
        when(infoBo.getWmPoiId()).thenReturn("123");
        when(infoBo.getWmPoiName()).thenReturn("Test Poi");
        when(customerInfoBo.getCustomerName()).thenReturn("Test Customer");
        // act
        Map<String, String> result = wrapperService.generatePdfMetaContent(customerInfoBo, infoBo);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("Test Customer", result.get("partAName"));
        assertEquals("123", result.get("wmPoiId"));
    }
}