package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.adapter.WmAorServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.infra.domain.WmUniAor;
import com.sankuai.meituan.waimai.infra.domain.WmUniAorLocationInfo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSource;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.customer.service.customer.dto.CustomerMscUsedPoiDetailDTO;

import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.Arrays;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
public class WmCustomerMscPoiCntV2ValidatorTest extends BaseStaticMockTest {
    @InjectMocks
    private WmCustomerMscPoiCntV2Validator wmCustomerMscPoiCntV2Validator;

    @Mock
    private WmCustomerGrayService wmCustomerGrayService;
    @Mock
    private WmCustomerService wmCustomerService;
    @Mock
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;
    @Mock
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Mock
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;



    /**
     * 测试场景：灰度检查不通过，直接返回通过结果
     */
    @Test
    public void testValid_GrayCheckFail() throws Throwable {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(false);

        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_PASS, result.getCode());
    }

    /**
     * 测试场景：客户类型不是美食城，直接返回通过结果
     */
    @Test
    public void testValid_NotMeishiCheng() throws Throwable {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.DANDIAN.getValue());
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);

        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_PASS, result.getCode());
    }

    /**
     * 测试场景：新增客户，直接返回通过结果
     */
    @Test
    public void testValid_NewCustomer() throws Throwable {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(0); // 新增客户ID为0
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);

        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_PASS, result.getCode());
    }

    /**
     * 测试场景：未查到有效客户信息，返回失败结果
     */
    @Test(expected = WmCustomerException.class)
    public void testValid_NoCustomerFound() throws Throwable {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(1);
        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(null);

        // act
        wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);
    }

    /**
     * 测试场景：客户具有资质共用标签，物理城市为北京，档口数超过限制，返回失败结果
     */
    @Test
    public void testValid_OverSpecialCityCntLimit() throws Throwable {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(new CustomerRealTypeSpInfoBo());
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityAorId(1);
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityPoiCount(4); // 超过北京限制的3个档口
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);

        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setMtCustomerId(1L);
        wmCustomerDB.setOwnerUid(1);

        WmUniAor wmUniAor = new WmUniAor();
        wmUniAor.setLocationInfos(new WmUniAorLocationInfo(Arrays.asList(1), null)); // 北京的AOR ID

        CustomerMscUsedPoiDetailDTO customerMscUsedPoiDTO = new CustomerMscUsedPoiDetailDTO();
        customerMscUsedPoiDTO.setUsedPoiCnt(2);

        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(wmCustomerDB);

        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(wmUniAor);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(anyLong())).thenReturn(true);
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(anyInt())).thenReturn(customerMscUsedPoiDTO);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));


        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_ERROR, result.getCode());
        assertEquals("该美食城为“资质共用特殊场景”美食城客户，且客户的物理城市为“北京市”，档口数不可超过3。", result.getMsg());
    }

    @Test
    public void testValid_NoQuaTagNotEnoughPoi() throws WmCustomerException {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(new CustomerRealTypeSpInfoBo());
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityPoiCount(2);
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);

        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setOwnerUid(1);

        CustomerMscUsedPoiDetailDTO customerMscUsedPoiDTO = new CustomerMscUsedPoiDetailDTO();
        customerMscUsedPoiDTO.setUsedPoiCnt(3);

        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(wmCustomerDB);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(anyLong())).thenReturn(false);
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(anyInt())).thenReturn(customerMscUsedPoiDTO);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));


        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_ERROR, result.getCode());
        assertTrue(result.getMsg().contains("档口数量不可小于已占用档口数量"));
    }

    @Test
    public void testValid_HasQuaTagNonBeijingNotEnoughPoi() throws WmCustomerException {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(new CustomerRealTypeSpInfoBo());
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityAorId(2);
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityPoiCount(2);
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);

        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setOwnerUid(1);

        WmUniAor wmUniAor = new WmUniAor();
        wmUniAor.setLocationInfos(new WmUniAorLocationInfo(Arrays.asList(2), null)); // 非北京的AOR ID

        CustomerMscUsedPoiDetailDTO customerMscUsedPoiDTO = new CustomerMscUsedPoiDetailDTO();
        customerMscUsedPoiDTO.setUsedPoiCnt(3);

        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(wmCustomerDB);
        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(wmUniAor);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(anyLong())).thenReturn(true);
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(anyInt())).thenReturn(customerMscUsedPoiDTO);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));


        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_ERROR, result.getCode());
        assertTrue(result.getMsg().contains("档口数量不可小于已占用档口数量"));
    }

    @Test
    public void testValid_NoAorInfo() throws WmCustomerException {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(new CustomerRealTypeSpInfoBo());
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityAorId(1);
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);

        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setOwnerUid(1);

        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(wmCustomerDB);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(anyLong())).thenReturn(true);
        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(null);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));


        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_ERROR, result.getCode());
        assertEquals("未查到蜂窝信息", result.getMsg());
    }

    /**
     * 有资质共用标签，超了北京的3，且已占用档口数大于维护档口数
     * @throws WmCustomerException
     */
    @Test
    public void testValid_HasQuaTagOverBeijingAndUsedNotPass() throws WmCustomerException {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(new CustomerRealTypeSpInfoBo());
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityAorId(2);
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityPoiCount(4);
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);

        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);
        wmCustomerDB.setOwnerUid(1);

        WmUniAor wmUniAor = new WmUniAor();
        wmUniAor.setLocationInfos(new WmUniAorLocationInfo(Arrays.asList(1), null)); // 北京的AOR ID

        CustomerMscUsedPoiDetailDTO customerMscUsedPoiDTO = new CustomerMscUsedPoiDetailDTO();
        customerMscUsedPoiDTO.setUsedPoiCnt(5);

        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(wmCustomerDB);
        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(wmUniAor);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(anyLong())).thenReturn(true);
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(anyInt())).thenReturn(customerMscUsedPoiDTO);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));


        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_ERROR, result.getCode());
        assertEquals("该美食城为“资质共用特殊场景”美食城客户，且客户的物理城市为“北京市”，档口数不可超过3。该美食城已占用档口数为5家，已超过3家，请核实门店情况后进行维护",result.getMsg());
    }

    /**
     * 无资质共用标签，校验通过
     * @throws WmCustomerException
     */
    @Test
    public void testValid_NoHasQuaTagPass() throws WmCustomerException {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(new CustomerRealTypeSpInfoBo());
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityAorId(2);
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityPoiCount(11);

        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);


        CustomerMscUsedPoiDetailDTO customerMscUsedPoiDTO = new CustomerMscUsedPoiDetailDTO();
        customerMscUsedPoiDTO.setUsedPoiCnt(10);

        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(wmCustomerDB);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(anyLong())).thenReturn(false);
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(anyInt())).thenReturn(customerMscUsedPoiDTO);


        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_PASS, result.getCode());
    }

    /**
     * 有资质共用标签，校验通过
     * @throws WmCustomerException
     */
    @Test
    public void testValid_HasQuaTagPass() throws WmCustomerException {
        // arrange
        ValidateResultBo validateResultBo = new ValidateResultBo();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setOwnerUid(1);
        wmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.MEISHICHENG.getValue());
        wmCustomerBasicBo.setId(1);
        wmCustomerBasicBo.setCustomerRealTypeSpInfoBo(new CustomerRealTypeSpInfoBo());
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityAorId(2);
        wmCustomerBasicBo.getCustomerRealTypeSpInfoBo().setFoodCityPoiCount(3);

        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setId(1);

        WmUniAor wmUniAor = new WmUniAor();
        wmUniAor.setLocationInfos(new WmUniAorLocationInfo(Arrays.asList(1), null)); // 北京的AOR ID

        CustomerMscUsedPoiDetailDTO customerMscUsedPoiDTO = new CustomerMscUsedPoiDetailDTO();
        customerMscUsedPoiDTO.setUsedPoiCnt(1);

        when(wmCustomerGrayService.isGrayMscPoiCntCheckNew(anyInt())).thenReturn(true);
        when(wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(anyInt())).thenReturn(wmCustomerDB);
        when(wmAorServiceAdapter.getAorInfoById(anyInt())).thenReturn(wmUniAor);
        when(wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(anyLong())).thenReturn(true);
        when(wmCustomerService.getMscUsedPoiDetailDTOV2(anyInt())).thenReturn(customerMscUsedPoiDTO);
        mccCustomerConfigMockedStatic.when(MccCustomerConfig::getSpecialCityPoiCntLimit).thenReturn(JSON.parseObject("{1:3}"));


        // act
        ValidateResultBo result = wmCustomerMscPoiCntV2Validator.valid(validateResultBo, wmCustomerBasicBo, false, false, 1);

        // assert
        assertEquals(CustomerConstants.RESULT_CODE_PASS, result.getCode());
    }

}
