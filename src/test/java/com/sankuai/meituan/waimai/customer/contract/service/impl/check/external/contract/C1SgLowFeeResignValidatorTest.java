package com.sankuai.meituan.waimai.customer.contract.service.impl.check.external.contract;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractSignOpSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.shangou.merchant.compliance.thrift.client.SgPoiContractClient;
import com.sankuai.shangou.merchant.compliance.thrift.param.poiContractReq.CheckResignReq;
import com.sankuai.shangou.merchant.compliance.thrift.param.poiContractReq.HasFlowDataReq;
import com.sankuai.shangou.merchant.compliance.thrift.param.poiContractReq.ResignReq;
import com.sankuai.shangou.merchant.compliance.thrift.result.CheckResignRes;
import com.sankuai.shangou.merchant.compliance.thrift.result.HasFlowDataRes;
import com.sankuai.shangou.merchant.compliance.thrift.result.contract.CheckResignResult;
import com.sankuai.shangou.merchant.compliance.thrift.result.contract.HasFlowDataResult;
import com.sankuai.shangou.merchant.compliance.thrift.result.contract.ResignResult;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;


public class C1SgLowFeeResignValidatorTest extends BaseStaticMockTest {

    @Mock
    private WmCustomerService wmCustomerService;

    @Mock
    private SgPoiContractClient sgPoiContractClient;

    @Mock
    private WmScEmployAdaptor wmScEmployAdaptor;

    @InjectMocks
    private C1SgLowFeeResignValidator c1SgLowFeeResignValidator;

    private WmCustomerContractBo contractBo;
    private WmCustomerBasicBo customerBasicBo;


    @Before
    public void setUp() {
        super.init();
        contractBo = new WmCustomerContractBo();
        contractBo.setBasicBo(new WmTempletContractBasicBo());
        contractBo.setSignBoList(new ArrayList<>());
        customerBasicBo = new WmCustomerBasicBo();
    }

    /**
     * 测试配置不允许续签时，方法应直接返回true
     */
    @Test
    public void testValid_ConfigNotAllowed() throws Throwable {
        // arrange

        mccConfigMockedStatic.when(MccConfig::resignSgLowFee).thenReturn(false);


        // act
        boolean result = c1SgLowFeeResignValidator.valid(contractBo, 1, "opName");

        // assert
        assert(result);
    }

    /**
     * 测试合同类型不在C1_CONTRACT_TYPE_LIST中，方法应直接返回true
     */
    @Test
    public void testValid_ContractTypeNotInList() throws Throwable {
        // arrange
        mccConfigMockedStatic.when(MccConfig::resignSgLowFee).thenReturn(true);

        contractBo.getBasicBo().setType(WmTempletContractTypeEnum.C2_E.getCode());

        // act
        boolean result = c1SgLowFeeResignValidator.valid(contractBo, 1, "opName");

        // assert
        assert(result);
    }

    /**
     * 测试客户业务组织代码不是闪购时，方法应直接返回true
     */
    @Test
    public void testValid_BizOrgCodeNotShanGou() throws Throwable {
        // arrange

        mccConfigMockedStatic.when(MccConfig::resignSgLowFee).thenReturn(true);

        contractBo.getBasicBo().setType(WmTempletContractTypeEnum.C1_E.getCode());
        customerBasicBo.setBizOrgCode(CustomerBizOrgEnum.WAI_MAI.getCode());
//        when(wmCustomerService.getCustomerById(any(Integer.class))).thenReturn(customerBasicBo);

        // act
        boolean result = c1SgLowFeeResignValidator.valid(contractBo, 1, "opName");

        // assert
        assert(result);
    }

    /**
     * 测试存在流程中数据时，抛出WmCustomerException异常
     */
    @Test(expected = WmCustomerException.class)
    public void testValid_HasFlowData() throws Throwable {
        // arrange
        contractBo.setOpSource(EcontractSignOpSourceEnum.QUEEN_BEE);

        mccConfigMockedStatic.when(MccConfig::resignSgLowFee).thenReturn(true);
        contractBo.getBasicBo().setType(WmTempletContractTypeEnum.C1_E.getCode());
        customerBasicBo.setBizOrgCode(CustomerBizOrgEnum.SHAN_GOU.getCode());
        when(wmCustomerService.getCustomerById(any(Integer.class))).thenReturn(customerBasicBo);

        HasFlowDataResult hasFlowDataResult = new HasFlowDataResult();
        HasFlowDataRes hasFlowDataRes = new HasFlowDataRes();
        hasFlowDataResult.setCode(0);
        hasFlowDataRes.setWmPoiId(1L);
        hasFlowDataRes.setHasFlowData(true);
        hasFlowDataResult.setData(Lists.newArrayList(hasFlowDataRes));
        when(sgPoiContractClient.hasFlowData(any(HasFlowDataReq.class))).thenReturn(hasFlowDataResult);

        // act
        c1SgLowFeeResignValidator.valid(contractBo, 1, "opName");
    }

    /**
     * 测试当checkNeedResign返回的门店列表为空时，方法应直接返回true
     */
    @Test
    public void testValid_CheckNeedResignEmptyList() throws Throwable {
        // arrange
        prepareCommonMocks();
        prepareNoHasFlowDataMocks();
        prepareCheckNeedResignMocks();
        CheckResignResult checkResignResult = new CheckResignResult();
        CheckResignRes checkResignRes = new CheckResignRes(1L, "", false, "feilv");
        checkResignResult.setCheckResignResList(Lists.newArrayList(checkResignRes));
//        when(sgPoiContractClient.checkNeedResign(any(CheckResignReq.class))).thenReturn(checkResignResult);

        // act
        boolean result = c1SgLowFeeResignValidator.valid(contractBo, 1, "opName");

        // assert
        assert(result);
    }

    /**
     * 测试当操作来源是MULTI_MODIFY_PLATFORM且resign方法返回成功且reSignResult为true时，应抛出WmCustomerException异常
     */
    @Test(expected = WmCustomerException.class)
    public void testValid_OpSourceMultiModifyPlatformAndResignSuccess() throws Throwable {
        // arrange
        prepareCommonMocks();
        prepareNoHasFlowDataMocks();
        prepareCheckNeedResignMocks();
        contractBo.setOpSource(EcontractSignOpSourceEnum.MULTI_MODIFY_PLATFORM);

        ResignResult resignResult = new ResignResult();
        resignResult.setCode(0);
        resignResult.setReSignResult(true);
        when(sgPoiContractClient.resign(any(ResignReq.class))).thenReturn(resignResult);

        // act
        c1SgLowFeeResignValidator.valid(contractBo, 1, "opName");
    }

    /**
     * 测试当操作来源是MULTI_MODIFY_PLATFORM但resign方法返回失败或reSignResult为false时，应抛出WmCustomerException异常
     */
    @Test(expected = WmCustomerException.class)
    public void testValid_OpSourceMultiModifyPlatformAndResignFail() throws Throwable {
        // arrange
        prepareCommonMocks();
        prepareNoHasFlowDataMocks();
        prepareCheckNeedResignMocks();
        contractBo.setOpSource(EcontractSignOpSourceEnum.MULTI_MODIFY_PLATFORM);

        ResignResult resignResult = new ResignResult();
        resignResult.setCode(-1);
        when(sgPoiContractClient.resign(any(ResignReq.class))).thenReturn(resignResult);

        // act
        c1SgLowFeeResignValidator.valid(contractBo, 1, "opName");
    }

    /**
     * 测试当操作来源不是MULTI_MODIFY_PLATFORM时，应抛出WmCustomerException异常
     */
    @Test(expected = WmCustomerException.class)
    public void testValid_OpSourceNotMultiModifyPlatform() throws Throwable {
        // arrange
        prepareCommonMocks();
        prepareNoHasFlowDataMocks();
        prepareCheckNeedResignMocks();
        contractBo.setOpSource(EcontractSignOpSourceEnum.OTHER);

        // act
        c1SgLowFeeResignValidator.valid(contractBo, 1, "opName");
    }

    // 为了避免重复代码，可以创建一些辅助方法来准备公共的mocks
    private void prepareCommonMocks() throws Exception {
        mccConfigMockedStatic.when( MccConfig::resignSgLowFee).thenReturn(true);
        contractBo.getBasicBo().setType(WmTempletContractTypeEnum.C1_E.getCode());
        customerBasicBo.setBizOrgCode(CustomerBizOrgEnum.SHAN_GOU.getCode());
        when(wmCustomerService.getCustomerById(any(Integer.class))).thenReturn(customerBasicBo);
        WmEmploy wmEmploy = new WmEmploy();
        wmEmploy.setMisId("test123");
        when(wmScEmployAdaptor.getWmEmployByUid(any(Integer.class))).thenReturn(wmEmploy);
    }

    private void prepareCheckNeedResignMocks() throws Exception {
        CheckResignResult checkResignResult = new CheckResignResult();
        checkResignResult.setCode(0);
        CheckResignRes checkResignRes = new CheckResignRes();
        checkResignRes.setNeedResign(true);
        checkResignResult.setCheckResignResList(java.util.Collections.singletonList(checkResignRes));
        when(sgPoiContractClient.checkNeedResign(any(CheckResignReq.class))).thenReturn(checkResignResult);
    }

    private void prepareNoHasFlowDataMocks() throws Exception {
        HasFlowDataResult hasFlowDataResult = new HasFlowDataResult();
        hasFlowDataResult.setCode(0);
        HasFlowDataRes hasFlowDataRes = new HasFlowDataRes();
        hasFlowDataRes.setWmPoiId(1L);
        hasFlowDataRes.setHasFlowData(false);
        hasFlowDataResult.setData(Lists.newArrayList(hasFlowDataRes));
        when(sgPoiContractClient.hasFlowData(any(HasFlowDataReq.class))).thenReturn(hasFlowDataResult);
    }


}
