package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.phf.PhfThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.waimaipoi.bargain.domain.vo.WmPoiBaseinfoBargainVo;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractSmsWrapperServiceGetPhfOwnerTest {

    @InjectMocks
    private AbstractWmEcontractSmsWrapperService service = new AbstractWmEcontractSmsWrapperService() {
    };

    @Mock
    private PhfThriftServiceAdapter phfThriftServiceAdapter;

    @Mock
    private WmEmployClient wmEmployClient;

    private Method getPhfOwnerMethod;

    @Before
    public void setUp() throws Exception {
        getPhfOwnerMethod = AbstractWmEcontractSmsWrapperService.class.getDeclaredMethod("getPhfOwner", List.class);
        getPhfOwnerMethod.setAccessible(true);
    }

    /**
     * Test case for empty wmPoiIdList input
     */
    @Test
    public void testGetPhfOwner_EmptyWmPoiIdList() throws Throwable {
        // arrange
        List<Long> emptyList = Collections.emptyList();
        try {
            // act
            getPhfOwnerMethod.invoke(service, emptyList);
            fail("Expected WmCustomerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            Throwable cause = e.getCause();
            assertNotNull(cause);
            assertEquals(WmCustomerException.class, cause.getClass());
            assertEquals("获取拼好饭客户负责人失败-门店列表为空", ((WmCustomerException) cause).getMsg());
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, ((WmCustomerException) cause).getCode());
        }
    }

    /**
     * Test case for null response from phfThriftServiceAdapter
     */
    @Test
    public void testGetPhfOwner_NullPhfResponse() throws Throwable {
        // arrange
        List<Long> poiIdList = Collections.singletonList(123L);
        when(phfThriftServiceAdapter.queryWmPoiBargainByWmPoiIdList(any())).thenReturn(null);
        try {
            // act
            getPhfOwnerMethod.invoke(service, poiIdList);
            fail("Expected WmCustomerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            Throwable cause = e.getCause();
            assertNotNull(cause);
            assertEquals(WmCustomerException.class, cause.getClass());
            assertEquals("获取拼好饭客户负责人失败", ((WmCustomerException) cause).getMsg());
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, ((WmCustomerException) cause).getCode());
        }
    }

    /**
     * Test case for empty response list from phfThriftServiceAdapter
     */
    @Test
    public void testGetPhfOwner_EmptyPhfResponse() throws Throwable {
        // arrange
        List<Long> poiIdList = Collections.singletonList(123L);
        when(phfThriftServiceAdapter.queryWmPoiBargainByWmPoiIdList(any())).thenReturn(new ArrayList<>());
        try {
            // act
            getPhfOwnerMethod.invoke(service, poiIdList);
            fail("Expected WmCustomerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            Throwable cause = e.getCause();
            assertNotNull(cause);
            assertEquals(WmCustomerException.class, cause.getClass());
            assertEquals("获取拼好饭客户负责人失败", ((WmCustomerException) cause).getMsg());
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, ((WmCustomerException) cause).getCode());
        }
    }

    /**
     * Test case for null ownerUid in WmPoiBaseinfoBargainVo
     */
    @Test
    public void testGetPhfOwner_NullOwnerUid() throws Throwable {
        // arrange
        List<Long> poiIdList = Collections.singletonList(123L);
        WmPoiBaseinfoBargainVo bargainVo = new WmPoiBaseinfoBargainVo();
        bargainVo.setOwnerUid(null);
        List<WmPoiBaseinfoBargainVo> responseList = Collections.singletonList(bargainVo);
        WmEmploy expectedEmploy = new WmEmploy();
        when(phfThriftServiceAdapter.queryWmPoiBargainByWmPoiIdList(any())).thenReturn(responseList);
        when(wmEmployClient.getById(0)).thenReturn(expectedEmploy);
        // act
        WmEmploy result = (WmEmploy) getPhfOwnerMethod.invoke(service, poiIdList);
        // assert
        assertNotNull(result);
    }

    /**
     * Test case for successful scenario with valid ownerUid
     */
    @Test
    public void testGetPhfOwner_Success() throws Throwable {
        // arrange
        List<Long> poiIdList = Collections.singletonList(123L);
        WmPoiBaseinfoBargainVo bargainVo = new WmPoiBaseinfoBargainVo();
        bargainVo.setOwnerUid(456);
        List<WmPoiBaseinfoBargainVo> responseList = Collections.singletonList(bargainVo);
        WmEmploy expectedEmploy = new WmEmploy();
        when(phfThriftServiceAdapter.queryWmPoiBargainByWmPoiIdList(any())).thenReturn(responseList);
        when(wmEmployClient.getById(456)).thenReturn(expectedEmploy);
        // act
        WmEmploy result = (WmEmploy) getPhfOwnerMethod.invoke(service, poiIdList);
        // assert
        assertNotNull(result);
    }
}