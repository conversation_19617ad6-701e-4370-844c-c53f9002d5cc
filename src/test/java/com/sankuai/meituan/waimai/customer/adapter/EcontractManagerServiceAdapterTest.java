package com.sankuai.meituan.waimai.customer.adapter;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoResponseDTO;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractManagerServiceAdapterTest {

    @InjectMocks
    private EcontractManagerServiceAdapter econtractManagerServiceAdapter;

    @Mock
    private EcontractManagerService econtractManagerService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBatchOnlyCreatePdfNormal() throws Throwable {
        // arrange
        List<PdfContentInfoBo> pdfBos = Arrays.asList(new PdfContentInfoBo());
        List<String> pdfUrls = Arrays.asList("pdfUrl");
        when(econtractManagerService.batchOnlyCreatePdf(pdfBos)).thenReturn(pdfUrls);
        // act
        List<String> result = econtractManagerServiceAdapter.batchOnlyCreatePdf(pdfBos);
        // assert
        assertEquals(pdfUrls, result);
        verify(econtractManagerService, times(1)).batchOnlyCreatePdf(pdfBos);
    }

    /**
     * 测试异常情况1：重试次数未达到最大重试次数
     */
    @Test
    public void testBatchOnlyCreatePdfException1() throws Throwable {
        // arrange
        List<PdfContentInfoBo> pdfBos = Arrays.asList(new PdfContentInfoBo());
        when(econtractManagerService.batchOnlyCreatePdf(pdfBos)).thenThrow(new RuntimeException());
        // act & assert
        try {
            econtractManagerServiceAdapter.batchOnlyCreatePdf(pdfBos);
        } catch (WmCustomerException e) {
            verify(econtractManagerService, times(3)).batchOnlyCreatePdf(pdfBos);
        }
    }

    /**
     * 测试异常情况2：重试次数达到最大重试次数
     */
    @Test(expected = WmCustomerException.class)
    public void testBatchOnlyCreatePdfException2() throws Throwable {
        // arrange
        List<PdfContentInfoBo> pdfBos = Arrays.asList(new PdfContentInfoBo());
        when(econtractManagerService.batchOnlyCreatePdf(pdfBos)).thenThrow(new RuntimeException());
        // act
        econtractManagerServiceAdapter.batchOnlyCreatePdf(pdfBos);
    }

    /**
     * 测试 queryStageBatchInfoBoList 方法，正常情况
     */
    @Test
    public void testQueryStageBatchInfoNormal() throws Throwable {
        // arrange
        String token = "testToken";
        String recordKey = "testRecordKey";
        StageBatchInfoResponseDTO expectedDTO = new StageBatchInfoResponseDTO();
        List<StageBatchInfoBo> expected = Arrays.asList(new StageBatchInfoBo());
        expectedDTO.setStageBatchInfoBoList(expected);

        when(econtractManagerService.queryStageBatchInfoBoListByRecordKey(token, recordKey)).thenReturn(expectedDTO);
        // act
        StageBatchInfoResponseDTO stageBatchInfoResponseDTO = econtractManagerServiceAdapter.queryStageBatchInfo(token, recordKey);
        List<StageBatchInfoBo> actual = stageBatchInfoResponseDTO.getStageBatchInfoBoList();

        // assert
        assertEquals(expected, actual);
        verify(econtractManagerService, times(1)).queryStageBatchInfoBoListByRecordKey(token, recordKey);
    }

    /**
     * 测试 queryStageBatchInfoBoList 方法，异常情况
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryStageBatchInfoException() throws Throwable {
        // arrange
        String token = "testToken";
        String recordKey = "testRecordKey";
        when(econtractManagerService.queryStageBatchInfoBoListByRecordKey(token, recordKey)).thenThrow(new RuntimeException());
        // act
        econtractManagerServiceAdapter.queryStageBatchInfo(token, recordKey);
        // assert
        verify(econtractManagerService, times(1)).queryStageBatchInfoBoListByRecordKey(token, recordKey);
    }
}