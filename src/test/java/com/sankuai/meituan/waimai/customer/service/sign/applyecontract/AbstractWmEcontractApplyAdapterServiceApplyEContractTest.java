package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractRecordSourceEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.ProcessTagEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import joptsimple.internal.Strings;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractApplyAdapterServiceApplyEContractTest {

    @Spy
    @InjectMocks
    private AbstractWmEcontractApplyAdapterService service = new AbstractWmEcontractApplyAdapterService() {

        @Override
        public EcontractRecordSourceEnum getSource(EcontractBatchContextBo batchContextBo) {
            return null;
        }
    };

    @Mock
    private WmEcontractApplyService wmEcontractApplyService;

    /**
     * Test case where contract should not be applied (PHF delivery case)
     */
    @Test
    public void testApplyEContractWhenShouldNotApply() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(EcontractBatchTypeEnum.PHF_DELIVERY);
        batchContextBo.setTag(ProcessTagEnum.DR_TAG.getType());
        EcontractBatchBo econtractBatchBo = new EcontractBatchBo();
        doReturn(econtractBatchBo).when(service).wrapEcontractBo(any(EcontractBatchContextBo.class));
        // act
        String result = service.applyEContract(batchContextBo, "callbackDsl", true, true, "recordId");
        // assert
        assertEquals(Strings.EMPTY, result);
    }
}