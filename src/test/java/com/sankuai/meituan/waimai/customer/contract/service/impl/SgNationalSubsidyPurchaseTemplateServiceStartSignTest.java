package com.sankuai.meituan.waimai.customer.contract.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SgNationalSubsidyPurchaseTemplateServiceStartSignTest {

    @Spy
    @InjectMocks
    private SgNationalSubsidyPurchaseTemplateService service;

    @Mock
    private WmContractService wmContractService;

    @Mock
    private ContractLogService contractLogService;

    @Mock
    private WmEcontractSignBzService wmEcontractSignBzService;

    private WmCustomerContractBo contractBo;

    private static final int OP_UID = 123;

    private static final String OP_NAME = "testUser";

    private static final long CONTRACT_ID = 1L;

    private static final int CUSTOMER_ID = 1001;

    private static final int STATUS_COMMIT = 1;

    @Before
    public void setUp() {
        // Initialize contractBo
        contractBo = new WmCustomerContractBo();
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setTempletContractId(CONTRACT_ID);
        basicBo.setStatus(STATUS_COMMIT);
        basicBo.setParentId(CUSTOMER_ID);
        contractBo.setBasicBo(basicBo);
        contractBo.setSignBoList(new ArrayList<>());
    }

    /**
     * Test when contract pack way is DO_SIGN
     */
    @Test
    public void testStartSign_DoSign() throws Throwable {
        // arrange
        contractBo.setPackWay(SignPackWay.DO_SIGN.getCode());
        doReturn((int) CONTRACT_ID).when(service).startSign(contractBo, OP_UID, OP_NAME);
        // act
        Integer result = service.startSign(contractBo, OP_UID, OP_NAME);
        // assert
        assertEquals(Integer.valueOf((int) CONTRACT_ID), result);
    }

    /**
     * Test with null contract bo
     */
    @Test(expected = NullPointerException.class)
    public void testStartSign_NullContractBo() throws Throwable {
        // act
        service.startSign(null, OP_UID, OP_NAME);
    }
}
