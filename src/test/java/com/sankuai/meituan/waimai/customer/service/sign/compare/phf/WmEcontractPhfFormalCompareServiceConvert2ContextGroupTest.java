package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mtrace.Tracer;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmKvTairClientAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.service.sign.compare.CompositeComparator;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PhfTransferContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfFormalCompareServiceConvert2ContextGroupTest {

    @InjectMocks
    private WmEcontractPhfFormalCompareService wmEcontractPhfFormalCompareService;

    @Mock
    private List<PhfTransferContext> currentBatchContext;

    @Mock
    private WmKvTairClientAdapter wmKvTairClientAdapter;

    private static final Long TEST_WM_POI_ID = 123L;

    private static final String TEST_CONTRACT_ID = "PHF001";

    private static final String TEST_TEMPLATE_NAME = "test_template";

    private Method buildPdfCompareStageInfoBoMethod;

    @Before
    public void setUp() throws Exception {
        // Use reflection to access the private method
        buildPdfCompareStageInfoBoMethod = WmEcontractPhfFormalCompareService.class.getDeclaredMethod("buildPdfCompareStageInfoBo", List.class, List.class);
        buildPdfCompareStageInfoBoMethod.setAccessible(true);
    }

    /**
     * Helper method to invoke private methods using reflection
     */
    private Object invokePrivateMethod(Object target, String methodName, Class<?>[] parameterTypes, Object... parameters) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        return method.invoke(target, parameters);
    }

    private Method getParseTechPdfMethod() throws NoSuchMethodException {
        Method method = WmEcontractPhfFormalCompareService.class.getDeclaredMethod("parseTechPdf", List.class);
        method.setAccessible(true);
        return method;
    }

    private void setupCommonMocks(MockedStatic<ProcessInfoUtil> processInfoUtilMock, MockedStatic<Tracer> tracerMock, MockedStatic<MccConfig> mccConfigMock) {
        HostEnv mockHostEnv = mock(HostEnv.class);
        when(mockHostEnv.toString()).thenReturn("test");
        processInfoUtilMock.when(ProcessInfoUtil::getHostEnv).thenReturn(mockHostEnv);
        tracerMock.when(Tracer::id).thenReturn("traceId");
        mccConfigMock.when(MccConfig::getPhfSignDataCompareAlarmMisList).thenReturn(Arrays.asList("test1", "test2"));
    }

    private EcontractBatchContextBo setupBasicBatchContext() {
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        // Setup transfer context
        List<PhfTransferContext> contexts = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext();
        context.setWmPoiId(TEST_WM_POI_ID);
        context.setPhfContractId(TEST_CONTRACT_ID);
        contexts.add(context);
        batchContextBo.setPhfTransferContextList(contexts);
        // Setup POI group map
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = new HashMap<>();
        List<List<Long>> poiGroups = new ArrayList<>();
        poiGroups.add(Collections.singletonList(TEST_WM_POI_ID));
        wmPoiIdGroupMap.put(EcontractDataSourceEnum.PHF_LOGISTICS_FEE.getType(), poiGroups);
        batchContextBo.setWmPoiIdGroupMap(wmPoiIdGroupMap);
        return batchContextBo;
    }

    private EcontractBatchBo setupCurrentDataWithPdfStage(String templateName) {
        EcontractBatchBo currentData = new EcontractBatchBo();
        List<StageBatchInfoBo> stageInfoList = new ArrayList<>();
        StageBatchInfoBo pdfStage = new StageBatchInfoBo();
        pdfStage.setStageName(TaskConstant.CREATE_PDF);
        Map<String, List<PdfContentInfoBo>> pdfContentMap = new HashMap<>();
        PdfContentInfoBo pdfContent = createMockPdfContent(templateName);
        String key = String.format("%d_%d_%s", TEST_WM_POI_ID, TEST_WM_POI_ID, EcontractDataWrapperEnum.PHF_FORMAL_TECH.name());
        pdfContentMap.put(key, Collections.singletonList(pdfContent));
        pdfStage.setPdfContentInfoBoMap(pdfContentMap);
        stageInfoList.add(pdfStage);
        currentData.setStageInfoBoList(stageInfoList);
        return currentData;
    }

    private PdfContentInfoBo createMockPdfContent(String templateName) {
        PdfContentInfoBo pdfContent = new PdfContentInfoBo();
        pdfContent.setPdfTemplateName(templateName);
        pdfContent.setPdfMetaContent(new HashMap<>());
        pdfContent.setPdfBizContent(new ArrayList<>());
        return pdfContent;
    }

    /**
     * Test normal case with valid input
     */
    @Test
    public void testConvert2ContextGroupNormal() throws Throwable {
        // arrange
        List<List<Long>> wmPoiIdGroupList = Arrays.asList(Arrays.asList(1L, 2L), Arrays.asList(3L, 4L));
        List<PhfTransferContext> contexts = Arrays.asList(new PhfTransferContext("1", "key1", 1L), new PhfTransferContext("2", "key2", 2L), new PhfTransferContext("3", "key3", 3L), new PhfTransferContext("4", "key4", 4L));
        when(currentBatchContext.stream()).thenReturn(contexts.stream());
        // act
        List<List<PhfTransferContext>> result = (// Use the mock here
        // Use the mock here
        // Use the mock here
        // Use the mock here
        // Use the mock here
        List<List<PhfTransferContext>>) invokePrivateMethod(wmEcontractPhfFormalCompareService, "convert2ContextGroup", new Class<?>[] { List.class, List.class }, wmPoiIdGroupList, currentBatchContext);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertNotNull(result.get(0));
        assertEquals(2, result.get(0).size());
        assertNotNull(result.get(0).get(0));
        assertEquals(1L, result.get(0).get(0).getWmPoiId().longValue());
        assertEquals(2L, result.get(0).get(1).getWmPoiId().longValue());
        assertEquals(3L, result.get(1).get(0).getWmPoiId().longValue());
        assertEquals(4L, result.get(1).get(1).getWmPoiId().longValue());
    }

    /**
     * Test exception case with null input
     */
    @Test
    public void testConvert2ContextGroupException() throws Throwable {
        try {
            // arrange
            List<List<Long>> wmPoiIdGroupList = null;
            // act
            invokePrivateMethod(wmEcontractPhfFormalCompareService, "convert2ContextGroup", new Class<?>[] { List.class, List.class }, wmPoiIdGroupList, null);
            fail("Expected NullPointerException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof NullPointerException);
        } catch (Exception e) {
            fail("Unexpected exception: " + e);
        }
    }

    /**
     * Test boundary case with empty input list
     */
    @Test
    public void testConvert2ContextGroupBoundary() throws Throwable {
        // arrange
        List<List<Long>> wmPoiIdGroupList = Collections.emptyList();
        when(currentBatchContext.stream()).thenReturn(Collections.<PhfTransferContext>emptyList().stream());
        // act
        List<List<PhfTransferContext>> result = (// Use the mock here
        // Use the mock here
        // Use the mock here
        // Use the mock here
        // Use the mock here
        List<List<PhfTransferContext>>) invokePrivateMethod(wmEcontractPhfFormalCompareService, "convert2ContextGroup", new Class<?>[] { List.class, List.class }, wmPoiIdGroupList, currentBatchContext);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test case with partial data in context
     */
    @Test
    public void testConvert2ContextGroupException2() throws Throwable {
        // arrange
        List<List<Long>> wmPoiIdGroupList = Arrays.asList(Arrays.asList(1L, 2L), Arrays.asList(3L, 4L));
        List<PhfTransferContext> contexts = Arrays.asList(new PhfTransferContext("1", "key1", 1L), new PhfTransferContext("2", "key2", 2L), new PhfTransferContext("3", "key3", 3L), new PhfTransferContext("4", "key4", 4L));
        when(currentBatchContext.stream()).thenReturn(contexts.stream());
        // act
        List<List<PhfTransferContext>> result = (// Use the mock here
        // Use the mock here
        // Use the mock here
        // Use the mock here
        // Use the mock here
        List<List<PhfTransferContext>>) invokePrivateMethod(wmEcontractPhfFormalCompareService, "convert2ContextGroup", new Class<?>[] { List.class, List.class }, wmPoiIdGroupList, currentBatchContext);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertNotNull(result.get(0));
        assertEquals(2, result.get(0).size());
        assertEquals(1L, result.get(0).get(0).getWmPoiId().longValue());
    }

    @Test(expected = WmCustomerException.class)
    public void testParseTechPdf_EmptyContractIds() throws Throwable {
        List<String> contractIds = Collections.emptyList();
        try {
            getParseTechPdfMethod().invoke(wmEcontractPhfFormalCompareService, contractIds);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                throw e.getCause();
            }
            throw e;
        }
    }

    @Test(expected = WmCustomerException.class)
    public void testParseTechPdf_NullPdfParam() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1");
        when(wmKvTairClientAdapter.get(any())).thenReturn(null);
        try {
            getParseTechPdfMethod().invoke(wmEcontractPhfFormalCompareService, contractIds);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                throw e.getCause();
            }
            throw e;
        }
    }

    @Test(expected = WmCustomerException.class)
    public void testParseTechPdf_EmptyPdfParam() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1");
        when(wmKvTairClientAdapter.get(any())).thenReturn("");
        try {
            getParseTechPdfMethod().invoke(wmEcontractPhfFormalCompareService, contractIds);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                throw e.getCause();
            }
            throw e;
        }
    }

    @Test
    public void testParseTechPdf_JsonParseException() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1");
        when(wmKvTairClientAdapter.get(any())).thenReturn("invalid json");
        try {
            getParseTechPdfMethod().invoke(wmEcontractPhfFormalCompareService, contractIds);
            fail("Should throw WmCustomerException");
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                WmCustomerException wce = (WmCustomerException) e.getCause();
                assertEquals("解析PDF参数异常", wce.getMessage());
            } else {
                throw e;
            }
        }
    }

    @Test
    public void testParseTechPdf_GeneralException() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1");
        when(wmKvTairClientAdapter.get(any())).thenThrow(new RuntimeException("Test exception"));
        try {
            getParseTechPdfMethod().invoke(wmEcontractPhfFormalCompareService, contractIds);
            fail("Should throw WmCustomerException");
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                WmCustomerException wce = (WmCustomerException) e.getCause();
                assertEquals("解析PDF参数异常", wce.getMessage());
            } else {
                throw e;
            }
        }
    }

    @Test
    public void testParseTechPdf_Success() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1", "contract2");
        PdfContentInfoBo expectedPdfContent = new PdfContentInfoBo();
        expectedPdfContent.setPdfUrl("test.pdf");
        String jsonValue = JSONObject.toJSONString(expectedPdfContent);
        when(wmKvTairClientAdapter.get(any())).thenReturn(jsonValue);
        Object result = getParseTechPdfMethod().invoke(wmEcontractPhfFormalCompareService, contractIds);
        assertNotNull(result);
        assertTrue(result instanceof PdfContentInfoBo);
        assertEquals(expectedPdfContent.getPdfUrl(), ((PdfContentInfoBo) result).getPdfUrl());
    }

    /**
     * Test case: Empty phfTransferContextList
     * Expected: Method returns early without performing comparison
     */
    @Test
    public void testCompare_EmptyTransferContext() throws Throwable {
        // arrange
        EcontractBatchBo currentData = new EcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setPhfTransferContextList(new ArrayList<>());
        try (MockedStatic<DaxiangUtil> daxiangUtilMock = mockStatic(DaxiangUtil.class)) {
            // act
            wmEcontractPhfFormalCompareService.compare(currentData, batchContextBo);
            // assert
            daxiangUtilMock.verifyNoInteractions();
            verifyNoInteractions(wmKvTairClientAdapter);
        }
    }

    /**
     * Test case: Old process with recordKey
     * Expected: Method returns early without performing comparison
     */
    @Test
    public void testCompare_OldProcess() throws Throwable {
        // arrange
        EcontractBatchBo currentData = new EcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        List<PhfTransferContext> contexts = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext();
        context.setRecordKey("oldKey");
        contexts.add(context);
        batchContextBo.setPhfTransferContextList(contexts);
        try (MockedStatic<DaxiangUtil> daxiangUtilMock = mockStatic(DaxiangUtil.class)) {
            // act
            wmEcontractPhfFormalCompareService.compare(currentData, batchContextBo);
            // assert
            daxiangUtilMock.verifyNoInteractions();
            verifyNoInteractions(wmKvTairClientAdapter);
        }
    }

    /**
     * Test case: Missing PDF stage
     * Expected: Alarm is sent with missing PDF stage message
     */
    @Test
    public void testCompare_MissingPdfStage() throws Throwable {
        // arrange
        EcontractBatchBo currentData = new EcontractBatchBo();
        currentData.setStageInfoBoList(new ArrayList<>());
        EcontractBatchContextBo batchContextBo = setupBasicBatchContext();
        try (MockedStatic<ProcessInfoUtil> processInfoUtilMock = mockStatic(ProcessInfoUtil.class);
            MockedStatic<Tracer> tracerMock = mockStatic(Tracer.class);
            MockedStatic<MccConfig> mccConfigMock = mockStatic(MccConfig.class);
            MockedStatic<DaxiangUtil> daxiangUtilMock = mockStatic(DaxiangUtil.class)) {
            setupCommonMocks(processInfoUtilMock, tracerMock, mccConfigMock);
            // act
            wmEcontractPhfFormalCompareService.compare(currentData, batchContextBo);
            // assert
            verifyNoInteractions(wmKvTairClientAdapter);
            daxiangUtilMock.verify(() -> DaxiangUtil.push(eq("<EMAIL>"), contains("当前流程缺少创建PDF节点"), any(List.class)));
        }
    }

    /**
     * Test normal case with valid inputs
     */
    @Test
    public void testBuildPdfCompareStageInfoBo_Success() throws Throwable {
        // arrange
        List<PhfTransferContext> currentBatchContext = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext("phf1", "key1", 1L);
        currentBatchContext.add(context);
        List<List<Long>> wmPoiIdGroupList = new ArrayList<>();
        wmPoiIdGroupList.add(Arrays.asList(1L));
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
        String pdfJson = JSONObject.toJSONString(pdfContentInfoBo);
        when(wmKvTairClientAdapter.get(anyString())).thenReturn(pdfJson);
        // act
        StageBatchInfoBo result = (StageBatchInfoBo) buildPdfCompareStageInfoBoMethod.invoke(wmEcontractPhfFormalCompareService, currentBatchContext, wmPoiIdGroupList);
        // assert
        assertNotNull(result);
        assertEquals(WmEcontractConstant.CREATE_PDF, result.getStageName());
        assertNotNull(result.getPdfContentInfoBoMap());
    }

    /**
     * Test case with empty wmPoiIdGroupList
     */
    @Test
    public void testBuildPdfCompareStageInfoBo_EmptyWmPoiIdGroupList() throws Throwable {
        // arrange
        List<PhfTransferContext> currentBatchContext = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext("phf1", "key1", 1L);
        currentBatchContext.add(context);
        List<List<Long>> wmPoiIdGroupList = new ArrayList<>();
        // act
        StageBatchInfoBo result = (StageBatchInfoBo) buildPdfCompareStageInfoBoMethod.invoke(wmEcontractPhfFormalCompareService, currentBatchContext, wmPoiIdGroupList);
        // assert
        assertNotNull(result);
        assertEquals(WmEcontractConstant.CREATE_PDF, result.getStageName());
        assertTrue(result.getPdfContentInfoBoMap().isEmpty());
    }

    /**
     * Test case with empty currentBatchContext
     */
    @Test(expected = NullPointerException.class)
    public void testBuildPdfCompareStageInfoBo_EmptyCurrentBatchContext() throws Throwable {
        // arrange
        List<PhfTransferContext> currentBatchContext = new ArrayList<>();
        List<List<Long>> wmPoiIdGroupList = new ArrayList<>();
        wmPoiIdGroupList.add(Arrays.asList(1L));
        // act
        try {
            buildPdfCompareStageInfoBoMethod.invoke(wmEcontractPhfFormalCompareService, currentBatchContext, wmPoiIdGroupList);
        } catch (Exception e) {
            throw (Exception) e.getCause();
        }
    }

    /**
     * Test case when parseTechPdf returns empty result
     */
    @Test(expected = WmCustomerException.class)
    public void testBuildPdfCompareStageInfoBo_EmptyPdfContent() throws Throwable {
        // arrange
        List<PhfTransferContext> currentBatchContext = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext("phf1", "key1", 1L);
        currentBatchContext.add(context);
        List<List<Long>> wmPoiIdGroupList = new ArrayList<>();
        wmPoiIdGroupList.add(Arrays.asList(1L));
        when(wmKvTairClientAdapter.get(anyString())).thenReturn("");
        // act
        try {
            buildPdfCompareStageInfoBoMethod.invoke(wmEcontractPhfFormalCompareService, currentBatchContext, wmPoiIdGroupList);
        } catch (Exception e) {
            throw (WmCustomerException) e.getCause();
        }
    }

    /**
     * Test case when parseTechPdf throws exception
     */
    @Test(expected = WmCustomerException.class)
    public void testBuildPdfCompareStageInfoBo_ParseException() throws Throwable {
        // arrange
        List<PhfTransferContext> currentBatchContext = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext("phf1", "key1", 1L);
        currentBatchContext.add(context);
        List<List<Long>> wmPoiIdGroupList = new ArrayList<>();
        wmPoiIdGroupList.add(Arrays.asList(1L));
        when(wmKvTairClientAdapter.get(anyString())).thenReturn("invalid json");
        // act
        try {
            buildPdfCompareStageInfoBoMethod.invoke(wmEcontractPhfFormalCompareService, currentBatchContext, wmPoiIdGroupList);
        } catch (Exception e) {
            throw (WmCustomerException) e.getCause();
        }
    }

    /**
     * Test case with multiple wmPoiIds in group
     */
    @Test
    public void testBuildPdfCompareStageInfoBo_MultipleWmPoiIds() throws Throwable {
        // arrange
        List<PhfTransferContext> currentBatchContext = new ArrayList<>();
        currentBatchContext.add(new PhfTransferContext("phf1", "key1", 1L));
        currentBatchContext.add(new PhfTransferContext("phf2", "key2", 2L));
        List<List<Long>> wmPoiIdGroupList = new ArrayList<>();
        wmPoiIdGroupList.add(Arrays.asList(1L, 2L));
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
        String pdfJson = JSONObject.toJSONString(pdfContentInfoBo);
        when(wmKvTairClientAdapter.get(anyString())).thenReturn(pdfJson);
        // act
        StageBatchInfoBo result = (StageBatchInfoBo) buildPdfCompareStageInfoBoMethod.invoke(wmEcontractPhfFormalCompareService, currentBatchContext, wmPoiIdGroupList);
        // assert
        assertNotNull(result);
        assertEquals(WmEcontractConstant.CREATE_PDF, result.getStageName());
        Map<String, List<PdfContentInfoBo>> pdfMap = result.getPdfContentInfoBoMap();
        assertNotNull(pdfMap);
        assertEquals(1, pdfMap.size());
    }
}