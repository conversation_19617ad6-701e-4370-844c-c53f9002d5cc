package com.sankuai.meituan.waimai.customer.service.sign.compare;

import static com.sankuai.meituan.waimai.customer.config.MccConfig.SPLITTER;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PdfStageInfoBoComparatorTest extends BaseStaticMockTest  {

    private PdfStageInfoBoComparator pdfStageInfoBoComparator = new PdfStageInfoBoComparator();

    @Before
    public void setUp() {
        super.init();
        mccConfigMockedStatic.when(MccConfig::getPhfCompareKeyWhiteList).thenReturn(SPLITTER.splitToList("computeFormula,_base_order,_top_orde"));

    }

        @Test
    public void testComparePdfContentInfoBoMapBothEmpty() throws Throwable {
        Map<String, List<PdfContentInfoBo>> source = new HashMap<>();
        Map<String, List<PdfContentInfoBo>> target = new HashMap<>();
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBoMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, source, target, failMsg);
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testComparePdfContentInfoBoMapSourceEmpty() throws Throwable {
        Map<String, List<PdfContentInfoBo>> source = new HashMap<>();
        Map<String, List<PdfContentInfoBo>> target = new HashMap<>();
        target.put("flowList", new ArrayList<>());
        // Adjusted to avoid NullPointerException
        source.put("flowList", new ArrayList<>());
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBoMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, source, target, failMsg);
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testComparePdfContentInfoBoMapTargetEmpty() throws Throwable {
        Map<String, List<PdfContentInfoBo>> source = new HashMap<>();
        Map<String, List<PdfContentInfoBo>> target = new HashMap<>();
        source.put("flowList", new ArrayList<>());
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBoMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, source, target, failMsg);
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testComparePdfContentInfoBoMapBothNotEmptyDifferentSize() throws Throwable {
        Map<String, List<PdfContentInfoBo>> source = new HashMap<>();
        Map<String, List<PdfContentInfoBo>> target = new HashMap<>();
        source.put("flowList", new ArrayList<>());
        target.put("flowList", new ArrayList<>());
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBoMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, source, target, failMsg);
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testComparePdfContentInfoBoMapBothNotEmptySameSize() throws Throwable {
        Map<String, List<PdfContentInfoBo>> source = new HashMap<>();
        Map<String, List<PdfContentInfoBo>> target = new HashMap<>();
        List<PdfContentInfoBo> sourceList = new ArrayList<>();
        List<PdfContentInfoBo> targetList = new ArrayList<>();
        source.put("flowList", sourceList);
        target.put("flowList", targetList);
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBoMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, source, target, failMsg);
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testComparePdfBizContentListBothNull() throws Throwable {
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfBizContentList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        StringBuilder failMsg = new StringBuilder();
        method.invoke(pdfStageInfoBoComparator, null, null, failMsg);
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testComparePdfBizContentListSourceNull() throws Throwable {
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfBizContentList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        StringBuilder failMsg = new StringBuilder();
        method.invoke(pdfStageInfoBoComparator, null, Arrays.asList(new HashMap<String, String>() {

            {
                put("key", "value");
            }
        }), failMsg);
        assertTrue(failMsg.toString().startsWith("pdfBizContent不一致对比结果: "));
    }

    @Test
    public void testComparePdfBizContentListTargetNull() throws Throwable {
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfBizContentList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        StringBuilder failMsg = new StringBuilder();
        method.invoke(pdfStageInfoBoComparator, Arrays.asList(new HashMap<String, String>() {

            {
                put("key", "value");
            }
        }), null, failMsg);
        assertTrue(StringUtils.isBlank(failMsg.toString()));
    }

    @Test
    public void testComparePdfBizContentListSizeNotEqual() throws Throwable {
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfBizContentList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        StringBuilder failMsg = new StringBuilder();
        method.invoke(pdfStageInfoBoComparator, Arrays.asList(new HashMap<String, String>() {

            {
                put("key", "value");
            }
        }), Arrays.asList(new HashMap<String, String>() {

            {
                put("key", "value");
                put("key2", "value2");
            }
        }), failMsg);
        assertTrue(StringUtils.isNotBlank(failMsg.toString()));
    }

    @Test
    public void testComparePdfBizContentListElementNotEqual() throws Throwable {
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfBizContentList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        StringBuilder failMsg = new StringBuilder();
        method.invoke(pdfStageInfoBoComparator, Arrays.asList(new HashMap<String, String>() {

            {
                put("key", "value");
            }
        }), Arrays.asList(new HashMap<String, String>() {

            {
                put("key", "value2");
            }
        }), failMsg);
        assertTrue(StringUtils.isNotBlank(failMsg.toString()));
    }

    @Test
    public void testComparePdfBizContentListEqual() throws Throwable {
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfBizContentList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        StringBuilder failMsg = new StringBuilder();
        method.invoke(pdfStageInfoBoComparator, Arrays.asList(new HashMap<String, String>() {

            {
                put("key", "value");
            }
        }), Arrays.asList(new HashMap<String, String>() {

            {
                put("key", "value");
            }
        }), failMsg);
        assertEquals("", failMsg.toString());
    }

    /**
     * 测试源列表和目标列表的大小不一致的情况
     */
    @Test
    public void testComparePdfInfoBoListSizeNotEqual() throws Throwable {
        // arrange
        List<PdfContentInfoBo> source = Arrays.asList(new PdfContentInfoBo());
        List<PdfContentInfoBo> target = Collections.emptyList();
        StringBuilder failMsg = new StringBuilder();
        // Use reflection to invoke the private method
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfInfoBoList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, source, target, failMsg);
        // assert
        assertTrue(StringUtils.isNotBlank(failMsg.toString()));
    }

    /**
     * 测试源列表和目标列表的大小一致，但列表中的元素不一致的情况
     */
    @Test
    public void testComparePdfInfoBoListElementNotEqual() throws Throwable {
        // arrange
        PdfContentInfoBo sourceBo = new PdfContentInfoBo();
        sourceBo.setPdfMetaContent(Collections.singletonMap("key", "value"));
        PdfContentInfoBo targetBo = new PdfContentInfoBo();
        targetBo.setPdfMetaContent(Collections.singletonMap("key", "value2"));
        List<PdfContentInfoBo> source = Arrays.asList(sourceBo);
        List<PdfContentInfoBo> target = Arrays.asList(targetBo);
        StringBuilder failMsg = new StringBuilder();
        // Use reflection to invoke the private method
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfInfoBoList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, source, target, failMsg);
        // assert
        assertTrue(StringUtils.isNotBlank(failMsg.toString()));
    }

    /**
     * 测试源列表和目标列表的大小一致，且列表中的元素一致的情况
     */
    @Test
    public void testComparePdfInfoBoListEqual() throws Throwable {
        // arrange
        PdfContentInfoBo sourceBo = new PdfContentInfoBo();
        sourceBo.setPdfMetaContent(Collections.singletonMap("key", "value"));
        List<PdfContentInfoBo> source = Arrays.asList(sourceBo);
        List<PdfContentInfoBo> target = Arrays.asList(sourceBo);
        StringBuilder failMsg = new StringBuilder();
        // Use reflection to invoke the private method
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfInfoBoList", List.class, List.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, source, target, failMsg);
        // assert
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testCompareTargetStageNameNotEqualCreatePdf() throws Throwable {
        StageBatchInfoBo source = new StageBatchInfoBo();
        source.setStageName("sourceStageName");
        StageBatchInfoBo target = new StageBatchInfoBo();
        target.setStageName("targetStageName");
        String result = pdfStageInfoBoComparator.compare(source, target);
        assertEquals("", result);
    }

    @Test
    public void testCompareSourceStageNameNotEqualTargetStageName() throws Throwable {
        StageBatchInfoBo source = new StageBatchInfoBo();
        source.setStageName("sourceStageName");
        StageBatchInfoBo target = new StageBatchInfoBo();
        target.setStageName(TaskConstant.CREATE_PDF);
        target.setPdfContentInfoBoMap(new HashMap<>());
        String result = pdfStageInfoBoComparator.compare(source, target);
        // Adjusted expectation based on the logic in compare method
        assertTrue(StringUtils.isNotBlank(result));
    }

    @Test(expected = NullPointerException.class)
    public void testComparePdfContentInfoBoMapNotEqual() throws Throwable {
        StageBatchInfoBo source = new StageBatchInfoBo();
        source.setStageName("sourceStageName");
        source.setPdfContentInfoBoMap(new HashMap<>());
        StageBatchInfoBo target = new StageBatchInfoBo();
        target.setStageName(TaskConstant.CREATE_PDF);
        Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap = new HashMap<>();
        pdfContentInfoBoMap.put("key", new ArrayList<>());
        target.setPdfContentInfoBoMap(pdfContentInfoBoMap);
        String result = pdfStageInfoBoComparator.compare(source, target);
        // Adjusted expectation based on the logic in compare method
    }


    /**
     * Test case for comparing PdfMetaContent when they are not equal
     */
    @Test
    public void testComparePdfContentInfoBo_PdfMetaContentNotEqual() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        Map<String, String> sourcePdfMetaContent = new HashMap<>();
        sourcePdfMetaContent.put("key1", "value1");
        sourceInfoBo.setPdfMetaContent(sourcePdfMetaContent);
        Map<String, String> targetPdfMetaContent = new HashMap<>();
        targetPdfMetaContent.put("key2", "value2");
        targetInfoBo.setPdfMetaContent(targetPdfMetaContent);
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertTrue(StringUtils.isNotBlank(failMsg.toString()));
    }

    /**
     * Test case for comparing PdfBizContent when they are not equal
     */
    @Test
    public void testComparePdfContentInfoBo_PdfBizContentNotEqual() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        List<Map<String, String>> sourcePdfBizContent = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        sourcePdfBizContent.add(map);
        sourceInfoBo.setPdfBizContent(sourcePdfBizContent);
        List<Map<String, String>> targetPdfBizContent = new ArrayList<>();
        Map<String, String> map2 = new HashMap<>();
        map2.put("key2", "value2");
        targetPdfBizContent.add(map2);
        targetInfoBo.setPdfBizContent(targetPdfBizContent);
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertTrue(StringUtils.isNotBlank(failMsg.toString()));
    }

    /**
     * Test case for comparing ContractName when they are not equal
     */
    @Test
    public void testComparePdfContentInfoBo_ContractNameNotEqual() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        sourceInfoBo.setContractName("contractName1");
        targetInfoBo.setContractName("contractName2");
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertTrue(StringUtils.isNotBlank(failMsg.toString()));
    }

    /**
     * Test case for comparing ContractDesc when they are not equal
     */
    @Test
    public void testComparePdfContentInfoBo_ContractDescNotEqual() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        sourceInfoBo.setContractDesc("contractDesc1");
        targetInfoBo.setContractDesc("contractDesc2");
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertTrue(StringUtils.isNotBlank(failMsg.toString()));
    }

    /**
     * Test case for comparing PdfMetaContent when they are equal
     */
    @Test
    public void testComparePdfContentInfoBo_PdfMetaContentEqual() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        Map<String, String> pdfMetaContent = new HashMap<>();
        pdfMetaContent.put("key", "value");
        sourceInfoBo.setPdfMetaContent(pdfMetaContent);
        targetInfoBo.setPdfMetaContent(new HashMap<>(pdfMetaContent));
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertEquals("", failMsg.toString());
    }

    /**
     * Test case for comparing PdfBizContent when they are equal
     */
    @Test
    public void testComparePdfContentInfoBo_PdfBizContentEqual() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        List<Map<String, String>> pdfBizContent = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        pdfBizContent.add(map);
        sourceInfoBo.setPdfBizContent(pdfBizContent);
        targetInfoBo.setPdfBizContent(new ArrayList<>(pdfBizContent));
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertEquals("", failMsg.toString());
    }

    /**
     * Test case for comparing ContractName when they are equal
     */
    @Test
    public void testComparePdfContentInfoBo_ContractNameEqual() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        sourceInfoBo.setContractName("contractName");
        targetInfoBo.setContractName("contractName");
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertEquals("", failMsg.toString());
    }

    /**
     * Test case for comparing ContractDesc when they are equal
     */
    @Test
    public void testComparePdfContentInfoBo_ContractDescEqual() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        sourceInfoBo.setContractDesc("contractDesc");
        targetInfoBo.setContractDesc("contractDesc");
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertEquals("", failMsg.toString());
    }

    /**
     * Test case for comparing when all fields are null
     */
    @Test
    public void testComparePdfContentInfoBo_AllFieldsNull() throws Throwable {
        PdfContentInfoBo sourceInfoBo = new PdfContentInfoBo();
        PdfContentInfoBo targetInfoBo = new PdfContentInfoBo();
        StringBuilder failMsg = new StringBuilder();
        Method method = PdfStageInfoBoComparator.class.getDeclaredMethod("comparePdfContentInfoBo", PdfContentInfoBo.class, PdfContentInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(pdfStageInfoBoComparator, sourceInfoBo, targetInfoBo, failMsg);
        assertEquals("", failMsg.toString());
    }
}