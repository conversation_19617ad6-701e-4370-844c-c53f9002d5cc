package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.adapter.WmKvTairClientAdapter;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for AbstractWmEcontractPhfCompareService.queryPdfParam method
 */
@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractPhfCompareServiceQueryPdfParamTest {

    @Mock
    private WmKvTairClientAdapter wmKvTairClientAdapter;

    @InjectMocks
    private AbstractWmEcontractPhfCompareService service = new AbstractWmEcontractPhfCompareService() {

        @Override
        public void compare(EcontractBatchBo currentData, EcontractBatchContextBo batchContextBo) {
        }

        @Override
        public String getPdfContentInfoBoMapKeyByWmPoiId(Long wmPoiId) {
            return null;
        }
    };

    @Test
    public void testPdfParamWithSingleContractId() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1");
        int type = 2;
        String expectedKey = "2_contract1";
        String expectedValue = "singleValue";
        when(wmKvTairClientAdapter.get(expectedKey)).thenReturn(expectedValue);
        String result = service.queryPdfParam(contractIds, type);
        assertEquals(expectedValue, result);
        verify(wmKvTairClientAdapter).get(expectedKey);
    }

    @Test
    public void testQueryPdfParamWithValidInput() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1", "contract2");
        int type = 1;
        String expectedKey = "1_contract1,contract2";
        String expectedValue = "testValue";
        when(wmKvTairClientAdapter.get(expectedKey)).thenReturn(expectedValue);
        String result = service.queryPdfParam(contractIds, type);
        assertEquals(expectedValue, result);
        verify(wmKvTairClientAdapter).get(expectedKey);
    }

    @Test
    public void testQueryPdfParamWithEmptyContractIds() throws Throwable {
        List<String> contractIds = new ArrayList<>();
        int type = 1;
        String result = service.queryPdfParam(contractIds, type);
        assertNull(result);
    }

    @Test
    public void testQueryPdfParamWithNullContractIds() throws Throwable {
        List<String> contractIds = null;
        int type = 1;
        String result = service.queryPdfParam(contractIds, type);
        assertNull(result);
    }

    @Test
    public void testQueryPdfParamWhenAdapterReturnsNull() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1");
        int type = 1;
        String expectedKey = "1_contract1";
        when(wmKvTairClientAdapter.get(expectedKey)).thenReturn(null);
        String result = service.queryPdfParam(contractIds, type);
        assertNull(result);
        verify(wmKvTairClientAdapter).get(expectedKey);
    }

    @Test
    public void testQueryPdfParamWithZeroType() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1", "contract2");
        int type = 0;
        String expectedKey = "0_contract1,contract2";
        String expectedValue = "zeroTypeValue";
        when(wmKvTairClientAdapter.get(expectedKey)).thenReturn(expectedValue);
        String result = service.queryPdfParam(contractIds, type);
        assertEquals(expectedValue, result);
        verify(wmKvTairClientAdapter).get(expectedKey);
    }

    @Test
    public void testQueryPdfParamWithSingleContractId() throws Throwable {
        List<String> contractIds = Arrays.asList("contract1");
        int type = 2;
        String expectedKey = "2_contract1";
        String expectedValue = "singleValue";
        when(wmKvTairClientAdapter.get(expectedKey)).thenReturn(expectedValue);
        String result = service.queryPdfParam(contractIds, type);
        assertEquals(expectedValue, result);
        verify(wmKvTairClientAdapter).get(expectedKey);
    }
}