package com.sankuai.meituan.waimai.customer.service.sign.applytask;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Test class for WmEcontractTaskApplyService
 */
@RunWith(MockitoJUnitRunner.class)
public class WmEcontractTaskApplyServiceInitApplyResultMsgTest {

    @InjectMocks
    @Spy
    private WmEcontractTaskApplyService wmEcontractTaskApplyService;

    private Method initApplyResultMsgMethod;

    @Mock
    private WmEcontractTaskBizService wmEcontractTaskBizService;

    @Mock
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    @Before
    public void setup() throws Exception {
        initApplyResultMsgMethod = WmEcontractTaskApplyService.class.getDeclaredMethod("initApplyResultMsg", EcontractTaskApplyBo.class, Long.class, Boolean.class);
        initApplyResultMsgMethod.setAccessible(true);
        ReflectionTestUtils.setField(wmEcontractTaskApplyService, "wmEcontractTaskService", wmEcontractTaskBizService);
        ReflectionTestUtils.setField(wmEcontractTaskApplyService, "wmEcontractBatchBizService", wmEcontractBatchBaseService);
    }

    private String invokeInitApplyResultMsg(EcontractTaskApplyBo applyBo, Long taskId, Boolean isSuccess) throws Exception {
        return (String) initApplyResultMsgMethod.invoke(wmEcontractTaskApplyService, applyBo, taskId, isSuccess);
    }

    private EcontractTaskApplyBo createBasicApplyBo(EcontractTaskApplyTypeEnum applyType) {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setApplyTypeEnum(applyType);
        applyBo.setDataSourceBoList(new ArrayList<>());
        return applyBo;
    }

    private EcontractDataSourceBo createDataSourceBo(Long wmPoiId, Long bizId) {
        EcontractDataSourceBo sourceBo = new EcontractDataSourceBo();
        EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
        poiBizBo.setWmPoiId(wmPoiId);
        poiBizBo.setBizId(bizId);
        sourceBo.setWmPoiIdAndBizIdList(Arrays.asList(poiBizBo));
        return sourceBo;
    }

    /**
     * Test case for non-delivery type contract
     */
    @Test
    public void testInitApplyResultMsg_NonDeliveryType() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createBasicApplyBo(EcontractTaskApplyTypeEnum.C1CONTRACT);
        Long taskId = 123L;
        Boolean isSuccess = true;
        // act
        String result = invokeInitApplyResultMsg(applyBo, taskId, isSuccess);
        // assert
        JSONObject jsonResult = JSON.parseObject(result);
        assertEquals(EcontractTaskApplyTypeEnum.C1CONTRACT.getType(), jsonResult.getInteger("type").intValue());
        assertEquals(EcontractTaskApplyTypeEnum.C1CONTRACT.getName(), jsonResult.getString("applyType"));
        assertEquals("", jsonResult.getString("resultMsg"));
    }

    /**
     * Test case for POIFEE type contract with success
     */
    @Test
    public void testInitApplyResultMsg_PoiFeeSuccess() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createBasicApplyBo(EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDataSourceBo sourceBo = createDataSourceBo(1001L, 2001L);
        applyBo.setDataSourceBoList(Arrays.asList(sourceBo));
        Long taskId = 123L;
        Boolean isSuccess = true;
        // act
        String result = invokeInitApplyResultMsg(applyBo, taskId, isSuccess);
        // assert
        JSONObject jsonResult = JSON.parseObject(result);
        JSONObject resultMsg = JSON.parseObject(jsonResult.getString("resultMsg"));
        assertEquals(taskId.longValue(), resultMsg.getLong("confirmId").longValue());
        assertTrue(resultMsg.getBoolean("success"));
        assertEquals(1, resultMsg.getJSONArray("signItemInfoList").size());
    }

    /**
     * Test case for PHF_DELIVERY type contract with record key
     */
    @Test
    public void testInitApplyResultMsg_PhfDeliveryWithRecordKey() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createBasicApplyBo(EcontractTaskApplyTypeEnum.PHF_DELIVERY);
        EcontractDataSourceBo sourceBo = createDataSourceBo(1001L, 2001L);
        applyBo.setDataSourceBoList(Arrays.asList(sourceBo));
        Long taskId = 123L;
        Long batchId = 456L;
        Boolean isSuccess = true;
        // Mock task and batch data
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setBatchId(batchId);
        when(wmEcontractTaskBizService.queryByIdIfNullFromMaster(taskId)).thenReturn(taskBo);
        WmEcontractSignBatchDB batchDB = new WmEcontractSignBatchDB();
        batchDB.setRecordKey("test_record_key");
        when(wmEcontractBatchBaseService.queryByBatchIdIfNullFromMaster(batchId)).thenReturn(batchDB);
        // act
        String result = invokeInitApplyResultMsg(applyBo, taskId, isSuccess);
        // assert
        JSONObject jsonResult = JSON.parseObject(result);
        JSONObject resultMsg = JSON.parseObject(jsonResult.getString("resultMsg"));
        assertEquals("test_record_key", resultMsg.getString("recordKey"));
        assertEquals(taskId.longValue(), resultMsg.getLong("confirmId").longValue());
        assertTrue(resultMsg.getBoolean("success"));
        verify(wmEcontractTaskBizService).queryByIdIfNullFromMaster(taskId);
        verify(wmEcontractBatchBaseService).queryByBatchIdIfNullFromMaster(batchId);
    }

    /**
     * Test case for PHF_DELIVERY type contract when task not found
     */
    @Test
    public void testInitApplyResultMsg_PhfDeliveryTaskNotFound() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createBasicApplyBo(EcontractTaskApplyTypeEnum.PHF_DELIVERY);
        EcontractDataSourceBo sourceBo = createDataSourceBo(1001L, 2001L);
        applyBo.setDataSourceBoList(Arrays.asList(sourceBo));
        Long taskId = 123L;
        Boolean isSuccess = true;
        when(wmEcontractTaskBizService.queryByIdIfNullFromMaster(taskId)).thenReturn(null);
        // act
        String result = invokeInitApplyResultMsg(applyBo, taskId, isSuccess);
        // assert
        JSONObject jsonResult = JSON.parseObject(result);
        JSONObject resultMsg = JSON.parseObject(jsonResult.getString("resultMsg"));
        assertEquals("", resultMsg.getString("recordKey"));
        assertEquals(taskId.longValue(), resultMsg.getLong("confirmId").longValue());
        assertTrue(resultMsg.getBoolean("success"));
        verify(wmEcontractTaskBizService).queryByIdIfNullFromMaster(taskId);
        verify(wmEcontractBatchBaseService, never()).queryByBatchIdIfNullFromMaster(anyLong());
    }

    /**
     * Test case for PHF_DELIVERY type contract when batch not found
     */
    @Test
    public void testInitApplyResultMsg_PhfDeliveryBatchNotFound() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createBasicApplyBo(EcontractTaskApplyTypeEnum.PHF_DELIVERY);
        EcontractDataSourceBo sourceBo = createDataSourceBo(1001L, 2001L);
        applyBo.setDataSourceBoList(Arrays.asList(sourceBo));
        Long taskId = 123L;
        Long batchId = 456L;
        Boolean isSuccess = true;
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setBatchId(batchId);
        when(wmEcontractTaskBizService.queryByIdIfNullFromMaster(taskId)).thenReturn(taskBo);
        when(wmEcontractBatchBaseService.queryByBatchIdIfNullFromMaster(batchId)).thenReturn(null);
        // act
        String result = invokeInitApplyResultMsg(applyBo, taskId, isSuccess);
        // assert
        JSONObject jsonResult = JSON.parseObject(result);
        JSONObject resultMsg = JSON.parseObject(jsonResult.getString("resultMsg"));
        assertEquals("", resultMsg.getString("recordKey"));
        assertEquals(taskId.longValue(), resultMsg.getLong("confirmId").longValue());
        assertTrue(resultMsg.getBoolean("success"));
        verify(wmEcontractTaskBizService).queryByIdIfNullFromMaster(taskId);
        verify(wmEcontractBatchBaseService).queryByBatchIdIfNullFromMaster(batchId);
    }

    /**
     * Test case for empty data source list
     */
    @Test
    public void testInitApplyResultMsg_EmptyDataSourceList() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createBasicApplyBo(EcontractTaskApplyTypeEnum.POIFEE);
        Long taskId = 123L;
        Boolean isSuccess = true;
        // act
        String result = invokeInitApplyResultMsg(applyBo, taskId, isSuccess);
        // assert
        JSONObject jsonResult = JSON.parseObject(result);
        JSONObject resultMsg = JSON.parseObject(jsonResult.getString("resultMsg"));
        assertEquals(taskId.longValue(), resultMsg.getLong("confirmId").longValue());
        assertTrue(resultMsg.getBoolean("success"));
        assertEquals(0, resultMsg.getJSONArray("signItemInfoList").size());
    }

    /**
     * Test case for multiple data sources
     */
    @Test
    public void testInitApplyResultMsg_MultipleDataSources() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createBasicApplyBo(EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDataSourceBo sourceBo1 = createDataSourceBo(1001L, 2001L);
        EcontractDataSourceBo sourceBo2 = createDataSourceBo(1002L, 2002L);
        applyBo.setDataSourceBoList(Arrays.asList(sourceBo1, sourceBo2));
        Long taskId = 123L;
        Boolean isSuccess = true;
        // act
        String result = invokeInitApplyResultMsg(applyBo, taskId, isSuccess);
        // assert
        JSONObject jsonResult = JSON.parseObject(result);
        JSONObject resultMsg = JSON.parseObject(jsonResult.getString("resultMsg"));
        assertEquals(taskId.longValue(), resultMsg.getLong("confirmId").longValue());
        assertTrue(resultMsg.getBoolean("success"));
        assertEquals(2, resultMsg.getJSONArray("signItemInfoList").size());
    }
}