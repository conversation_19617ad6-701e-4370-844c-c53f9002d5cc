package com.sankuai.meituan.waimai.customer.contract.service.cusSwitch;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.view.WmContractAutoSignView;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractAgentService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmContractAgentInfo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchModulePackInfo;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmPoiSignSubjectBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class AutoSignContractServiceTest {

    @Mock
    private WmCustomerService wmCustomerService;

    @Mock
    private WmCustomerKpService wmCustomerKpService;

    @Mock
    private WmContractService wmContractService;

    @Mock
    private WmPoiSwitchThriftService wmPoiSwitchThriftService;

    @Mock
    private EmpServiceAdaptor empServiceAdaptor;

    @Mock
    private WmContractAgentService wmContractAgentService;

    @InjectMocks
    private AutoSignContractService autoSignContractService;

    private WmContractAutoSignView validAutoSignView;

    private WmContractAutoSignView invalidAutoSignView;

    @Before
    public void setUp() {
        validAutoSignView = new WmContractAutoSignView();
        validAutoSignView.setCustomerId(1);
        validAutoSignView.setWmPoiIds(Arrays.asList(1001L, 1002L));
        validAutoSignView.setOpName("testOperator");
        validAutoSignView.setOpUid(123);
        validAutoSignView.setSwitchId(456L);
        invalidAutoSignView = new WmContractAutoSignView();
        invalidAutoSignView.setCustomerId(0);
    }

    /**
     * Tests when customer exists but has non-electronic signing mode
     */
    @Test
    public void testAutoSignContractWithNonElectronicSignMode() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        // Non-electronic signing mode
        customerDB.setSignMode(1);
        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        // act
        autoSignContractService.autoSignContract(validAutoSignView);
        // assert
        verify(wmCustomerService).selectCustomerById(1);
        verify(wmCustomerKpService, never()).getCustomerSignerKp(anyInt());
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(any(SwitchModulePackInfo.class));
    }

    /**
     * Tests when customer has electronic signing mode but no KP info
     */
    @Test
    public void testAutoSignContractWithElectronicSignButNoKp() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        // Electronic signing mode
        customerDB.setSignMode(2);
        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        when(wmCustomerKpService.getCustomerSignerKp(1)).thenReturn(null);
        // act
        autoSignContractService.autoSignContract(validAutoSignView);
        // assert
        verify(wmCustomerService).selectCustomerById(1);
        verify(wmCustomerKpService).getCustomerSignerKp(1);
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(any(SwitchModulePackInfo.class));
    }

    /**
     * Tests exception handling during customer lookup
     */
    @Test
    public void testAutoSignContractWhenCustomerLookupFails() throws Throwable {
        // arrange
        when(wmCustomerService.selectCustomerById(1)).thenThrow(new RuntimeException("DB error"));
        // act
        autoSignContractService.autoSignContract(validAutoSignView);
        // assert
        verify(wmCustomerService).selectCustomerById(1);
        verify(wmCustomerKpService, never()).getCustomerSignerKp(anyInt());
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(any(SwitchModulePackInfo.class));
    }

    /**
     * Tests when pre-check fails due to non-electronic signing mode
     */
    @Test
    public void testAutoSignContractWhenNonElectronicSignMode() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        // Non-electronic signing mode
        customerDB.setSignMode(1);
        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        // act
        autoSignContractService.autoSignContract(validAutoSignView);
        // assert
        verify(wmCustomerService).selectCustomerById(1);
        verify(wmCustomerKpService, never()).getCustomerSignerKp(anyInt());
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(any(SwitchModulePackInfo.class));
    }

    /**
     * Tests when pre-check fails due to missing KP info
     */
    @Test
    public void testAutoSignContractWhenMissingKpInfo() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        // Electronic signing mode
        customerDB.setSignMode(2);
        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        when(wmCustomerKpService.getCustomerSignerKp(1)).thenReturn(null);
        // act
        autoSignContractService.autoSignContract(validAutoSignView);
        // assert
        verify(wmCustomerService).selectCustomerById(1);
        verify(wmCustomerKpService).getCustomerSignerKp(1);
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(any(SwitchModulePackInfo.class));
    }

    /**
     * Tests exception handling during execution
     */
    @Test
    public void testAutoSignContractWhenExceptionOccurs() throws Throwable {
        // arrange
        when(wmCustomerService.selectCustomerById(1)).thenThrow(new RuntimeException("Test exception"));
        // act
        autoSignContractService.autoSignContract(validAutoSignView);
        // assert
        verify(wmCustomerService).selectCustomerById(1);
        verify(wmCustomerKpService, never()).getCustomerSignerKp(anyInt());
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(any(SwitchModulePackInfo.class));
    }

    /**
     * Tests when preCheck passes but getManualTaskIdList returns empty list
     */
    @Test
    public void testAutoSignContractWhenNoManualTasksCreated() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setSignMode(2);
        WmCustomerKp customerKp = new WmCustomerKp();
        customerKp.setCompellation("Test KP");
        customerKp.setPhoneNum("12345678901");

        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        when(wmCustomerKpService.getCustomerSignerKp(1)).thenReturn(customerKp);
        // C1合同已签约
        List<WmCustomerContractBo> c1Contracts = new ArrayList<>();
        c1Contracts.add(new WmCustomerContractBo());
        when(wmContractService.getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(c1Contracts);
        // 没有代理商信息
        validAutoSignView.setAgentIds(null);

        // act
        autoSignContractService.autoSignContract(validAutoSignView);

        // assert
        verify(wmCustomerService).selectCustomerById(1);
        verify(wmCustomerKpService).getCustomerSignerKp(1);
        verify(wmContractService).getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        );
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(argThat(info ->
                info.getTaskIds().equals("-1")
        ));
    }

    /**
     * Tests when C1 contract is not signed and task is created successfully
     */
    @Test
    public void testAutoSignContractWhenC1ContractTaskCreated() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setSignMode(2);
        customerDB.setCustomerName("Test Customer");
        WmCustomerKp customerKp = new WmCustomerKp();
        customerKp.setCompellation("Test KP");
        customerKp.setPhoneNum("12345678901");

        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        when(wmCustomerKpService.getCustomerSignerKp(1)).thenReturn(customerKp);
        // C1合同未签约
        when(wmContractService.getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(Collections.emptyList());
        // 签约主体信息
        WmPoiSignSubjectBo signSubjectBo = new WmPoiSignSubjectBo();
        signSubjectBo.setPartBName("美团外卖");
        signSubjectBo.setPartlogisticsName("美团物流");
        when(wmContractService.getSignSubjectBo(
                eq(validAutoSignView.getCustomerId()),
                eq(WmTempletContractTypeEnum.C1_E.getCode())
        )).thenReturn(signSubjectBo);
        // 模拟创建C1合同任务成功
        when(wmContractService.saveAndStartSignForManualPack(
                any(WmCustomerContractBo.class),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(1001L);
        // 没有代理商信息
        validAutoSignView.setAgentIds(null);

        // act
        autoSignContractService.autoSignContract(validAutoSignView);

        // assert
        verify(wmCustomerService, times(2)).selectCustomerById(1);
        verify(wmCustomerKpService, times(2)).getCustomerSignerKp(1);
        verify(wmContractService).getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        );
        verify(wmContractService).getSignSubjectBo(
                eq(validAutoSignView.getCustomerId()),
                eq(WmTempletContractTypeEnum.C1_E.getCode())
        );
        verify(wmContractService).saveAndStartSignForManualPack(
                any(WmCustomerContractBo.class),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        );
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(argThat(info ->
                info.getTaskIds().equals("1001")
        ));
    }

    /**
     * Tests when C2 contract tasks are created successfully
     */
    @Test
    public void testAutoSignContractWhenC2ContractTasksCreated() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setSignMode(2);
        customerDB.setCustomerName("Test Customer");
        WmCustomerKp customerKp = new WmCustomerKp();
        customerKp.setCompellation("Test KP");
        customerKp.setPhoneNum("12345678901");

        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        when(wmCustomerKpService.getCustomerSignerKp(1)).thenReturn(customerKp);
        // C1合同已签约
        List<WmCustomerContractBo> c1Contracts = new ArrayList<>();
        c1Contracts.add(new WmCustomerContractBo());
        when(wmContractService.getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(c1Contracts);
        // 设置代理商信息
        validAutoSignView.setAgentIds(Arrays.asList(101, 102));
        validAutoSignView.setExistAgentIds(Collections.singletonList(103));
        // 模拟代理商信息
        WmContractAgentInfo agentInfo = new WmContractAgentInfo();
        agentInfo.setName("Test Agent");
        agentInfo.setLegalPerson("Agent Legal");
        when(wmContractAgentService.queryAgentInfoById(anyInt())).thenReturn(agentInfo);
        // 模拟创建C2合同任务成功
        when(wmContractService.saveAndStartSignForManualPack(
                any(WmCustomerContractBo.class),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(2001L).thenReturn(2002L);

        // act
        autoSignContractService.autoSignContract(validAutoSignView);

        // assert
        verify(wmCustomerService, times(2)).selectCustomerById(1);
        verify(wmCustomerKpService, times(2)).getCustomerSignerKp(1);
        verify(wmContractService, times(2)).saveAndStartSignForManualPack(
                any(WmCustomerContractBo.class),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        );
        verify(wmContractAgentService, times(2)).queryAgentInfoById(anyInt());
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(argThat(info ->
                info.getTaskIds().equals("2001,2002")
        ));
    }

    /**
     * Tests when both C1 and C2 contract tasks are created
     */
    @Test
    public void testAutoSignContractWhenBothC1AndC2ContractTasksCreated() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setSignMode(2);
        customerDB.setCustomerName("Test Customer");
        WmCustomerKp customerKp = new WmCustomerKp();
        customerKp.setCompellation("Test KP");
        customerKp.setPhoneNum("12345678901");

        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        when(wmCustomerKpService.getCustomerSignerKp(1)).thenReturn(customerKp);
        // C1合同未签约
        when(wmContractService.getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(Collections.emptyList());
        // 签约主体信息
        WmPoiSignSubjectBo signSubjectBo = new WmPoiSignSubjectBo();
        signSubjectBo.setPartBName("美团外卖");
        signSubjectBo.setPartlogisticsName("美团物流");
        when(wmContractService.getSignSubjectBo(
                eq(validAutoSignView.getCustomerId()),
                eq(WmTempletContractTypeEnum.C1_E.getCode())
        )).thenReturn(signSubjectBo);
        // 模拟创建C1合同任务成功
        when(wmContractService.saveAndStartSignForManualPack(
                any(WmCustomerContractBo.class),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(1001L).thenReturn(2001L).thenReturn(2002L);
        // 设置代理商信息
        validAutoSignView.setAgentIds(Arrays.asList(101, 102));
        validAutoSignView.setExistAgentIds(Collections.singletonList(103));
        // 模拟代理商信息
        WmContractAgentInfo agentInfo = new WmContractAgentInfo();
        agentInfo.setName("Test Agent");
        agentInfo.setLegalPerson("Agent Legal");
        when(wmContractAgentService.queryAgentInfoById(anyInt())).thenReturn(agentInfo);

        // act
        autoSignContractService.autoSignContract(validAutoSignView);

        // assert
        verify(wmCustomerService, times(3)).selectCustomerById(1);
        verify(wmCustomerKpService, times(3)).getCustomerSignerKp(1);
        verify(wmContractService).getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        );
        verify(wmContractService).getSignSubjectBo(
                eq(validAutoSignView.getCustomerId()),
                eq(WmTempletContractTypeEnum.C1_E.getCode())
        );
        verify(wmContractService, times(3)).saveAndStartSignForManualPack(
                any(WmCustomerContractBo.class),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        );
        verify(wmContractAgentService, times(2)).queryAgentInfoById(anyInt());
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(argThat(info ->
                info.getTaskIds().equals("1001,2001,2002")
        ));
    }

    /**
     * Tests when C2 contract creation fails due to exception
     */
    @Test
    public void testAutoSignContractWhenC2ContractCreationFails() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setSignMode(2);
        customerDB.setCustomerName("Test Customer");
        WmCustomerKp customerKp = new WmCustomerKp();
        customerKp.setCompellation("Test KP");
        customerKp.setPhoneNum("12345678901");

        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        when(wmCustomerKpService.getCustomerSignerKp(1)).thenReturn(customerKp);
        // C1合同已签约
        List<WmCustomerContractBo> c1Contracts = new ArrayList<>();
        c1Contracts.add(new WmCustomerContractBo());
        when(wmContractService.getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(c1Contracts);
        // 设置代理商信息
        validAutoSignView.setAgentIds(Arrays.asList(101, 102));
        validAutoSignView.setExistAgentIds(Collections.singletonList(103));
        // 模拟代理商信息查询失败
        when(wmContractAgentService.queryAgentInfoById(anyInt())).thenThrow(new WmCustomerException(-1, "Agent not found"));

        // act
        autoSignContractService.autoSignContract(validAutoSignView);

        // assert
        verify(wmCustomerService, times(2)).selectCustomerById(1);
        verify(wmCustomerKpService, times(2)).getCustomerSignerKp(1);
        verify(wmContractService, never()).saveAndStartSignForManualPack(
                any(WmCustomerContractBo.class),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        );
        verify(wmContractAgentService, times(2)).queryAgentInfoById(anyInt());
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(argThat(info ->
                info.getTaskIds().equals("-1")
        ));
    }

    /**
     * Tests when syncToPoiSwitch throws exception
     */
    @Test
    public void testAutoSignContractWhenSyncToPoiSwitchFails() throws Throwable {
        // arrange
        WmCustomerDB customerDB = new WmCustomerDB();
        customerDB.setSignMode(2);
        customerDB.setCustomerName("Test Customer");
        WmCustomerKp customerKp = new WmCustomerKp();
        customerKp.setCompellation("Test KP");
        customerKp.setPhoneNum("12345678901");

        when(wmCustomerService.selectCustomerById(1)).thenReturn(customerDB);
        when(wmCustomerKpService.getCustomerSignerKp(1)).thenReturn(customerKp);
        // C1合同已签约
        List<WmCustomerContractBo> c1Contracts = new ArrayList<>();
        c1Contracts.add(new WmCustomerContractBo());
        when(wmContractService.getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        )).thenReturn(c1Contracts);
        // 没有代理商信息
        validAutoSignView.setAgentIds(null);
        // 模拟同步到PoiSwitch失败
        doThrow(new RuntimeException("Sync failed")).when(wmPoiSwitchThriftService).saveSwitchModulePack(any(SwitchModulePackInfo.class));

        // act
        autoSignContractService.autoSignContract(validAutoSignView);

        // assert
        verify(wmCustomerService).selectCustomerById(1);
        verify(wmCustomerKpService).getCustomerSignerKp(1);
        verify(wmContractService).getContractBoListByCusIdAndType(
                eq((long) validAutoSignView.getCustomerId()),
                anyList(),
                eq(validAutoSignView.getOpUid()),
                eq(validAutoSignView.getOpName())
        );
        verify(wmPoiSwitchThriftService).saveSwitchModulePack(any(SwitchModulePackInfo.class));
    }
}
