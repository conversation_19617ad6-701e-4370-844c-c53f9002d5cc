package com.sankuai.meituan.waimai.customer.service.sign.applytask;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.cancel.WmEcontractCancelService;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Field;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for WmEcontractTaskApplyService
 */
@RunWith(MockitoJUnitRunner.class)
public class WmEcontractTaskApplyServiceCancelSignTest {

    @InjectMocks
    private WmEcontractTaskApplyService wmEcontractTaskApplyService;

    @Mock
    private WmEcontractCancelService wmEcontractCancelService;

    @Mock
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    @Mock
    private WmEcontractTaskBizService wmEcontractTaskService;

    private static final String ACTION_SOURCE = "test_action";

    private static final Long TASK_ID = 1234L;

    private static final Long BATCH_ID = 1L;

    /**
     * Test canceling sign for PHF delivery task with DR tag
     */
    @Test
    public void testCancelSign_PhfDeliveryWithDRTag() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType("phf_delivery");
        taskBo.setBatchId(BATCH_ID);
        taskBo.setId(TASK_ID);
        WmEcontractSignBatchDB batchDB = new WmEcontractSignBatchDB();
        batchDB.setBatchContext("{\"tag\":1}");
        batchDB.setValid(Byte.valueOf((byte) 1));
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(TASK_ID)).thenReturn(taskBo);
        when(wmEcontractBatchBaseService.queryByBatchIdIfNullFromMaster(BATCH_ID)).thenReturn(batchDB);
        when(wmEcontractCancelService.cancelSignWithDRTag(eq(taskBo), eq(ACTION_SOURCE), eq(true))).thenReturn(true);
        // act
        BooleanResult result = wmEcontractTaskApplyService.cancelSign(TASK_ID, ACTION_SOURCE, true);
        // assert
        assertNotNull("Result should not be null", result);
        verify(wmEcontractTaskService).queryByIdIfNullFromMaster(TASK_ID);
        verify(wmEcontractBatchBaseService).queryByBatchIdIfNullFromMaster(BATCH_ID);
        verify(wmEcontractCancelService).cancelSignWithDRTag(eq(taskBo), eq(ACTION_SOURCE), eq(true));
    }

    /**
     * Test canceling sign for normal task
     */
    @Test
    public void testCancelSign_NormalTask() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType("normal_type");
        taskBo.setBatchId(BATCH_ID);
        taskBo.setId(TASK_ID);
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(TASK_ID)).thenReturn(taskBo);
        when(wmEcontractCancelService.cancel(eq(taskBo), anyString(), eq(ACTION_SOURCE), eq(true))).thenReturn(true);
        // act
        BooleanResult result = wmEcontractTaskApplyService.cancelSign(TASK_ID, ACTION_SOURCE, true);
        // assert
        assertNotNull("Result should not be null", result);
        verify(wmEcontractTaskService).queryByIdIfNullFromMaster(TASK_ID);
        verify(wmEcontractCancelService).cancel(eq(taskBo), anyString(), eq(ACTION_SOURCE), eq(true));
    }

    /**
     * Test canceling sign with callback disabled
     */
    @Test
    public void testCancelSign_CallbackDisabled() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType("normal_type");
        taskBo.setBatchId(BATCH_ID);
        taskBo.setId(TASK_ID);
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(TASK_ID)).thenReturn(taskBo);
        when(wmEcontractCancelService.cancel(eq(taskBo), anyString(), eq(ACTION_SOURCE), eq(false))).thenReturn(true);
        // act
        BooleanResult result = wmEcontractTaskApplyService.cancelSign(TASK_ID, ACTION_SOURCE, false);
        // assert
        assertNotNull("Result should not be null", result);
        verify(wmEcontractTaskService).queryByIdIfNullFromMaster(TASK_ID);
        verify(wmEcontractCancelService).cancel(eq(taskBo), anyString(), eq(ACTION_SOURCE), eq(false));
    }

    /**
     * Test canceling sign when cancel service fails
     */
    @Test
    public void testCancelSign_CancelServiceFails() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType("normal_type");
        taskBo.setBatchId(BATCH_ID);
        taskBo.setId(TASK_ID);
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(TASK_ID)).thenReturn(taskBo);
        when(wmEcontractCancelService.cancel(eq(taskBo), anyString(), eq(ACTION_SOURCE), eq(true))).thenReturn(false);
        // act
        BooleanResult result = wmEcontractTaskApplyService.cancelSign(TASK_ID, ACTION_SOURCE, true);
        // assert
        assertNotNull("Result should not be null", result);
        verify(wmEcontractTaskService).queryByIdIfNullFromMaster(TASK_ID);
        verify(wmEcontractCancelService).cancel(eq(taskBo), anyString(), eq(ACTION_SOURCE), eq(true));
    }

    /**
     * Test canceling sign with exception from cancel service
     */
    @Test(expected = WmCustomerException.class)
    public void testCancelSign_CancelServiceException() throws Throwable {
        // arrange
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType("normal_type");
        taskBo.setBatchId(BATCH_ID);
        taskBo.setId(TASK_ID);
        when(wmEcontractTaskService.queryByIdIfNullFromMaster(TASK_ID)).thenReturn(taskBo);
        when(wmEcontractCancelService.cancel(eq(taskBo), anyString(), eq(ACTION_SOURCE), eq(true))).thenThrow(new WmCustomerException(1, "Cancel failed"));
        // act
        wmEcontractTaskApplyService.cancelSign(TASK_ID, ACTION_SOURCE, true);
        // assert - exception expected
        verify(wmEcontractTaskService).queryByIdIfNullFromMaster(TASK_ID);
        verify(wmEcontractCancelService).cancel(eq(taskBo), anyString(), eq(ACTION_SOURCE), eq(true));
    }
}