package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CChargeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDeliveryTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractSignTempletConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfVirtualSelfWrapperServiceImplTest {

    @InjectMocks
    private WmEcontractPhfVirtualSelfWrapperServiceImpl service;

    @Mock
    private EcontractBatchContextBo contextBo;

    @Mock
    private EcontractTaskBo taskBo;

    @Mock
    private EcontractCustomerInfoBo customerInfoBo;

    private MockedStatic<WmEcontractContextUtil> mockedWmEcontractContextUtil;

    private MockedStatic<ConfigUtilAdapter> mockedConfigUtilAdapter;

    private MockedStatic<JSON> mockedJSON;

    private MockedStatic<MccConfig> mockedMccConfig;

    private MockedStatic<CChargeTypeEnum> mockedCChargeTypeEnum;

    @Before
    public void setUp() {
        mockedWmEcontractContextUtil = mockStatic(WmEcontractContextUtil.class);
        mockedConfigUtilAdapter = mockStatic(ConfigUtilAdapter.class);
        mockedJSON = mockStatic(JSON.class);
        mockedMccConfig = mockStatic(MccConfig.class);
        mockedCChargeTypeEnum = mockStatic(CChargeTypeEnum.class);
        // Mock ConfigUtilAdapter
        mockedConfigUtilAdapter.when(() -> ConfigUtilAdapter.getString(eq("phf_virtual_self_pdf_template_version"))).thenReturn("1.0");
        mockedConfigUtilAdapter.when(() -> ConfigUtilAdapter.getInt(eq("phf_virtual_self_pdf_template_id"), anyInt())).thenReturn(17);
        mockedConfigUtilAdapter.when(() -> ConfigUtilAdapter.getString(eq(EcontractSignTempletConstant.CURRENT_DELIVERY_SIGN_VERSION_FOR_NEW_MODULE))).thenReturn("1.0");
        // Mock MccConfig
        mockedMccConfig.when(MccConfig::getPhfContractNameInFormal).thenReturn("美团拼好饭服务费收费协议");
        // Mock CChargeTypeEnum
        CChargeTypeEnum mockChargeType = mock(CChargeTypeEnum.class);
        mockedCChargeTypeEnum.when(() -> CChargeTypeEnum.findByCode(eq("1"))).thenReturn(mockChargeType);
    }

    @After
    public void tearDown() {
        if (mockedWmEcontractContextUtil != null) {
            mockedWmEcontractContextUtil.close();
        }
        if (mockedConfigUtilAdapter != null) {
            mockedConfigUtilAdapter.close();
        }
        if (mockedJSON != null) {
            mockedJSON.close();
        }
        if (mockedMccConfig != null) {
            mockedMccConfig.close();
        }
        if (mockedCChargeTypeEnum != null) {
            mockedCChargeTypeEnum.close();
        }
    }

    private EcontractDeliveryPhfInfoBo createPhfInfoBo(String poiId, String poiName) {
        EcontractDeliveryPhfInfoBo phfInfoBo = new EcontractDeliveryPhfInfoBo();
        phfInfoBo.setWmPoiId(poiId);
        phfInfoBo.setWmPoiName(poiName);
        // Initialize techInfo map
        Map<Integer, EcontractDeliveryPhfTechInfoBo> techInfo = Maps.newHashMap();
        EcontractDeliveryPhfTechInfoBo techInfoBo = new EcontractDeliveryPhfTechInfoBo();
        techInfoBo.setBaseFee("10.0");
        techInfoBo.setBaseSubsidy("5.0");
        techInfo.put(1002, techInfoBo);
        phfInfoBo.setTechInfo(techInfo);
        // Initialize perInfo map with chargePerInfo
        Map<Integer, EcontractDeliveryPhfPerInfoBo> perInfo = Maps.newHashMap();
        EcontractDeliveryPhfPerInfoBo perInfoBo = new EcontractDeliveryPhfPerInfoBo();
        Map<String, EcontractdeliveryPhfChargeInfoBo> chargePerInfo = Maps.newHashMap();
        EcontractdeliveryPhfChargeInfoBo chargeInfoBo = new EcontractdeliveryPhfChargeInfoBo();
        chargeInfoBo.setBaseFee("8.0");
        chargeInfoBo.setTimeFee("2.0");
        chargeInfoBo.setDistanceFee("1.0");
        chargeInfoBo.setBaseSubsidy("3.0");
        chargeInfoBo.setTopFee("15.0");
        chargeInfoBo.setSeasonFee("1.5");
        chargePerInfo.put("1", chargeInfoBo);
        perInfoBo.setChargePerInfo(chargePerInfo);
        perInfo.put(1002, perInfoBo);
        phfInfoBo.setPerInfo(perInfo);
        return phfInfoBo;
    }

    /**
     * Test normal case with complete valid data
     */
    @Test
    public void testGeneratePdfMetaContent_NormalCase() throws Throwable {
        // arrange
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerName("Test Customer");
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        infoBo.setWmPoiId("123");
        infoBo.setWmPoiName("Test POI");
        // Setup tech info
        Map<Integer, EcontractDeliveryPhfTechInfoBo> techInfo = new HashMap<>();
        EcontractDeliveryPhfTechInfoBo techInfoBo = new EcontractDeliveryPhfTechInfoBo();
        techInfoBo.setBaseFee("10");
        techInfoBo.setBaseSubsidy("5");
        techInfo.put(EcontractDeliveryTypeEnum.PHF_SELF.getType(), techInfoBo);
        infoBo.setTechInfo(techInfo);
        // Setup performance info
        Map<Integer, EcontractDeliveryPhfPerInfoBo> perInfo = new HashMap<>();
        EcontractDeliveryPhfPerInfoBo perInfoBo = new EcontractDeliveryPhfPerInfoBo();
        Map<String, EcontractdeliveryPhfChargeInfoBo> chargePerInfo = new HashMap<>();
        perInfoBo.setChargePerInfo(chargePerInfo);
        perInfo.put(EcontractDeliveryTypeEnum.PHF_SELF.getType(), perInfoBo);
        infoBo.setPerInfo(perInfo);
        // act
        Map<String, String> result = service.generatePdfMetaContent(customerInfoBo, infoBo);
        // assert
        assertNotNull(result);
        assertEquals("Test Customer", result.get("partAName"));
        assertEquals("Test POI", result.get("wmPoiName"));
        assertEquals("123", result.get("wmPoiId"));
        assertEquals("Test POI ID 123", result.get("poiInfo"));
        assertEquals("10", result.get("SELF_techServiceFee_base"));
        assertEquals("5", result.get("SELF_techServiceFee_baseSubsidy"));
    }

    /**
     * Test case when customerInfoBo is null
     */
    @Test(expected = NullPointerException.class)
    public void testGeneratePdfMetaContent_NullCustomerInfo() throws Throwable {
        // arrange
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        // act
        service.generatePdfMetaContent(null, infoBo);
    }

    /**
     * Test case when infoBo is null
     */
    @Test(expected = NullPointerException.class)
    public void testGeneratePdfMetaContent_NullInfoBo() throws Throwable {
        // arrange
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        // act
        service.generatePdfMetaContent(customerInfoBo, null);
    }

    /**
     * Test case with empty tech info map
     */
    @Test
    public void testGeneratePdfMetaContent_EmptyTechInfo() throws Throwable {
        // arrange
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerName("Test Customer");
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        infoBo.setWmPoiId("123");
        infoBo.setWmPoiName("Test POI");
        infoBo.setTechInfo(new HashMap<>());
        infoBo.setPerInfo(new HashMap<>());
        // act
        Map<String, String> result = service.generatePdfMetaContent(customerInfoBo, infoBo);
        // assert
        assertNotNull(result);
        assertEquals("Test Customer", result.get("partAName"));
        assertEquals("Test POI", result.get("wmPoiName"));
        assertEquals("123", result.get("wmPoiId"));
        assertEquals("Test POI ID 123", result.get("poiInfo"));
    }

    /**
     * Test case with invalid delivery type in tech info
     */
    @Test(expected = WmCustomerException.class)
    public void testGeneratePdfMetaContent_InvalidDeliveryType() throws Throwable {
        // arrange
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        Map<Integer, EcontractDeliveryPhfTechInfoBo> techInfo = new HashMap<>();
        // Invalid type
        techInfo.put(999999, new EcontractDeliveryPhfTechInfoBo());
        infoBo.setTechInfo(techInfo);
        // act
        service.generatePdfMetaContent(customerInfoBo, infoBo);
    }

    /**
     * Test case with empty performance info map
     */
    @Test
    public void testGeneratePdfMetaContent_EmptyPerformanceInfo() throws Throwable {
        // arrange
        EcontractCustomerInfoBo customerInfoBo = new EcontractCustomerInfoBo();
        customerInfoBo.setCustomerName("Test Customer");
        EcontractDeliveryPhfInfoBo infoBo = new EcontractDeliveryPhfInfoBo();
        infoBo.setWmPoiId("123");
        infoBo.setWmPoiName("Test POI");
        // Only set tech info
        Map<Integer, EcontractDeliveryPhfTechInfoBo> techInfo = new HashMap<>();
        EcontractDeliveryPhfTechInfoBo techInfoBo = new EcontractDeliveryPhfTechInfoBo();
        techInfoBo.setBaseFee("10");
        techInfo.put(EcontractDeliveryTypeEnum.PHF_SELF.getType(), techInfoBo);
        infoBo.setTechInfo(techInfo);
        // Empty performance info
        infoBo.setPerInfo(new HashMap<>());
        // act
        Map<String, String> result = service.generatePdfMetaContent(customerInfoBo, infoBo);
        // assert
        assertNotNull(result);
        assertEquals("Test Customer", result.get("partAName"));
        assertEquals("Test POI", result.get("wmPoiName"));
        assertEquals("10", result.get("SELF_techServiceFee_base"));
    }

    /**
     * Test successful case with single delivery info
     */
    @Test
    public void testWrapSuccessWithSingleDeliveryInfo() throws Throwable {
        // arrange
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setEcontractDeliveryPhfInfoBo(createPhfInfoBo("123", "Test POI"));
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList(deliveryInfoBo));
        when(contextBo.getCustomerInfoBo()).thenReturn(customerInfoBo);
        when(taskBo.getApplyContext()).thenReturn("test_context");
        mockedWmEcontractContextUtil.when(() -> WmEcontractContextUtil.selectByApplyType(any(), eq(EcontractTaskApplyTypeEnum.PHF_DELIVERY))).thenReturn(taskBo);
        mockedJSON.when(() -> JSON.parseObject(eq("test_context"), eq(EcontractBatchDeliveryInfoBo.class))).thenReturn(batchDeliveryInfoBo);
        // act
        List<PdfContentInfoBo> result = service.wrap(contextBo);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        PdfContentInfoBo pdfContentInfoBo = result.get(0);
        assertEquals(17, pdfContentInfoBo.getPdfTemplateId().intValue());
        assertEquals("适用于 Test POI", pdfContentInfoBo.getContractDesc());
        assertEquals("美团拼好饭服务费收费协议", pdfContentInfoBo.getContractName());
    }

    /**
     * Test case when delivery info list is empty
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapWithEmptyDeliveryInfoList() throws Throwable {
        // arrange
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList());
        when(taskBo.getApplyContext()).thenReturn("test_context");
        mockedWmEcontractContextUtil.when(() -> WmEcontractContextUtil.selectByApplyType(any(), eq(EcontractTaskApplyTypeEnum.PHF_DELIVERY))).thenReturn(taskBo);
        mockedJSON.when(() -> JSON.parseObject(eq("test_context"), eq(EcontractBatchDeliveryInfoBo.class))).thenReturn(batchDeliveryInfoBo);
        // act
        service.wrap(contextBo);
    }

    /**
     * Test case when task info is missing
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapWithMissingTaskInfo() throws Throwable {
        // arrange
        mockedWmEcontractContextUtil.when(() -> WmEcontractContextUtil.selectByApplyType(any(), eq(EcontractTaskApplyTypeEnum.PHF_DELIVERY))).thenThrow(new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "不存在phf_delivery申请任务"));
        // act
        service.wrap(contextBo);
    }

    /**
     * Test successful case with multiple delivery info
     */
    @Test
    public void testWrapSuccessWithMultipleDeliveryInfo() throws Throwable {
        // arrange
        List<EcontractDeliveryInfoBo> deliveryInfoBoList = Lists.newArrayList();
        for (int i = 0; i < 3; i++) {
            EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
            deliveryInfoBo.setEcontractDeliveryPhfInfoBo(createPhfInfoBo(String.valueOf(i), "Test POI " + i));
            deliveryInfoBoList.add(deliveryInfoBo);
        }
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryInfoBoList);
        when(contextBo.getCustomerInfoBo()).thenReturn(customerInfoBo);
        when(taskBo.getApplyContext()).thenReturn("test_context");
        mockedWmEcontractContextUtil.when(() -> WmEcontractContextUtil.selectByApplyType(any(), eq(EcontractTaskApplyTypeEnum.PHF_DELIVERY))).thenReturn(taskBo);
        mockedJSON.when(() -> JSON.parseObject(eq("test_context"), eq(EcontractBatchDeliveryInfoBo.class))).thenReturn(batchDeliveryInfoBo);
        // act
        List<PdfContentInfoBo> result = service.wrap(contextBo);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        for (int i = 0; i < 3; i++) {
            PdfContentInfoBo pdfContentInfoBo = result.get(i);
            assertEquals(17, pdfContentInfoBo.getPdfTemplateId().intValue());
            assertEquals("适用于 Test POI " + i, pdfContentInfoBo.getContractDesc());
            assertEquals("美团拼好饭服务费收费协议", pdfContentInfoBo.getContractName());
        }
    }
}