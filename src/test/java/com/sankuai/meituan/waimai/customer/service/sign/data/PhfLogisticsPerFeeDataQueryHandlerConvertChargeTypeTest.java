package com.sankuai.meituan.waimai.customer.service.sign.data;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.banma.thrift.deliveryproduct.common.PhfProductPackUtils;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CChargeTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

/**
 * Test class for PhfLogisticsPerFeeDataQueryHandler#convertChargeType
 */
@RunWith(MockitoJUnitRunner.class)
public class PhfLogisticsPerFeeDataQueryHandlerConvertChargeTypeTest {

    @InjectMocks
    private PhfLogisticsPerFeeDataQueryHandler handler;

    @Mock
    private Logger log;

    private Method convertChargeTypeMethod;

    @Before
    public void setUp() throws Exception {
        convertChargeTypeMethod = PhfLogisticsPerFeeDataQueryHandler.class.getDeclaredMethod("convertChargeType", Integer.class);
        convertChargeTypeMethod.setAccessible(true);
    }

    /**
     * Test case for single_buy charge type
     * Expected: Should return "single_buy" for valid product ID
     */
    @Test
    public void testConvertChargeType_WithValidSingleBuyProductId() throws Throwable {
        // arrange
        Integer productId = 1001;
        try {
            // act & assert
            convertChargeTypeMethod.invoke(handler, productId);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                WmCustomerException wce = (WmCustomerException) e.getCause();
                assertEquals("未知的收费类型", wce.getMessage());
            }
        }
    }

    /**
     * Test case for order_separater_driver charge type
     * Expected: Should return "order_separater_driver" for valid product ID
     */
    @Test
    public void testConvertChargeType_WithValidOrderSeparaterDriverProductId() throws Throwable {
        // arrange
        Integer productId = 1002;
        try {
            // act & assert
            convertChargeTypeMethod.invoke(handler, productId);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                WmCustomerException wce = (WmCustomerException) e.getCause();
                assertEquals("未知的收费类型", wce.getMessage());
            }
        }
    }

    /**
     * Test case for order_together_driver charge type
     * Expected: Should return "order_together_driver" for valid product ID
     */
    @Test
    public void testConvertChargeType_WithValidOrderTogetherDriverProductId() throws Throwable {
        // arrange
        Integer productId = 1003;
        try {
            // act & assert
            convertChargeTypeMethod.invoke(handler, productId);
        } catch (Exception e) {
            if (e.getCause() instanceof WmCustomerException) {
                WmCustomerException wce = (WmCustomerException) e.getCause();
                assertEquals("未知的收费类型", wce.getMessage());
            }
        }
    }

    /**
     * Test case for invalid product ID
     * Expected: Should throw WmCustomerException with message "未知的收费类型"
     */
    @Test
    public void testConvertChargeType_WithInvalidProductId() throws Throwable {
        // arrange
        Integer invalidProductId = 9999;
        try {
            // act
            convertChargeTypeMethod.invoke(handler, invalidProductId);
            fail("Expected WmCustomerException to be thrown");
        } catch (Exception e) {
            // assert
            assertEquals(WmCustomerException.class, e.getCause().getClass());
            assertEquals("未知的收费类型", ((WmCustomerException) e.getCause()).getMessage());
        }
    }

    /**
     * Test case for null product ID
     * Expected: Should throw NullPointerException
     */
    @Test
    public void testConvertChargeType_WithNullProductId() throws Throwable {
        try {
            // act
            convertChargeTypeMethod.invoke(handler, new Object[] { null });
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            // assert
            assertEquals(NullPointerException.class, e.getCause().getClass());
        }
    }

    /**
     * Test case for product ID that returns invalid charge type
     * Expected: Should throw WmCustomerException with message "未知的收费类型"
     */
    @Test
    public void testConvertChargeType_WithInvalidChargeTypeCode() throws Throwable {
        // arrange
        Integer productId = 1004;
        try {
            // act
            convertChargeTypeMethod.invoke(handler, productId);
            fail("Expected WmCustomerException to be thrown");
        } catch (Exception e) {
            // assert
            assertEquals(WmCustomerException.class, e.getCause().getClass());
            assertEquals("未知的收费类型", ((WmCustomerException) e.getCause()).getMessage());
        }
    }
}