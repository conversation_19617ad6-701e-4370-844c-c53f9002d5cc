package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.nationalsubsidy;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsGatewayThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualBatchSignParam;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidyHeadquartersDeliveryNoticeTaskTest {

    @InjectMocks
    private NationalSubsidyHeadquartersDeliveryNoticeTask noticeTask;

    @Mock
    private WmLogisticsGatewayThriftServiceAdapter wmLogisticsGatewayThriftServiceAdapter;

    /**
     * Test case: Empty taskInfo and taskIds should throw WmCustomerException
     */
    @Test
    public void testNotice_WhenEmptyTaskInfoAndTaskIds_ShouldThrowException() throws Throwable {
        // arrange
        String module = "test";
        List<Long> bizIdList = Arrays.asList(1L);
        ManualPackNoticeContext context = ManualPackNoticeContext.builder().taskInfo(new HashMap<>()).build();
        List<Long> taskIds = Arrays.asList();
        try {
            // act
            noticeTask.notice(module, bizIdList, context, taskIds);
            fail("Should throw WmCustomerException");
        } catch (WmCustomerException e) {
            // assert
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getCode());
            assertEquals("国补总部协议参数不合法", e.getMessage());
        } catch (TException e) {
            fail("Unexpected exception: " + e);
        }
    }

    /**
     * Test case: Empty wmPoiIdMap should throw WmCustomerException
     */
    @Test
    public void testNotice_WhenEmptyWmPoiIdMap_ShouldThrowException() throws Throwable {
        // arrange
        String module = "test";
        List<Long> bizIdList = Arrays.asList(1L);
        Map<String, List<Long>> taskInfo = new HashMap<>();
        taskInfo.put("test", Arrays.asList(1L));
        ManualPackNoticeContext context = ManualPackNoticeContext.builder().taskInfo(taskInfo).nationalSubsidyHeadquartersWmPoiIdMap(new HashMap<>()).build();
        List<Long> taskIds = Arrays.asList(1L);
        try {
            // act
            noticeTask.notice(module, bizIdList, context, taskIds);
            fail("Should throw WmCustomerException");
        } catch (WmCustomerException e) {
            // assert
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getCode());
            assertEquals("国补总部协议打包签约发起异常", e.getMessage());
        } catch (TException e) {
            fail("Unexpected exception: " + e);
        }
    }

    /**
     * Test case: Successful execution of notice method
     */
    @Test
    public void testNotice_WhenValidInput_ShouldSucceed() throws Throwable {
        // arrange
        String module = "test";
        List<Long> bizIdList = Arrays.asList(1L);
        Map<String, List<Long>> taskInfo = new HashMap<>();
        taskInfo.put("test", Arrays.asList(1L));
        Map<Long, Long> wmPoiIdMap = new HashMap<>();
        wmPoiIdMap.put(1L, 100L);
        ManualPackNoticeContext context = ManualPackNoticeContext.builder().taskInfo(taskInfo).nationalSubsidyHeadquartersWmPoiIdMap(wmPoiIdMap).commitUid(123).manualBatchId(456L).build();
        List<Long> taskIds = Arrays.asList(1L);
        // act
        noticeTask.notice(module, bizIdList, context, taskIds);
        // assert
        verify(wmLogisticsGatewayThriftServiceAdapter).deliveryBatchApplySignUseNewIface(any());
    }

    /**
     * Test case: WmLogisticsGatewayThriftServiceAdapter throws exception
     */
    @Test
    public void testNotice_WhenAdapterThrowsException_ShouldThrowException() throws Throwable {
        // arrange
        String module = "test";
        List<Long> bizIdList = Arrays.asList(1L);
        Map<String, List<Long>> taskInfo = new HashMap<>();
        taskInfo.put("test", Arrays.asList(1L));
        Map<Long, Long> wmPoiIdMap = new HashMap<>();
        wmPoiIdMap.put(1L, 100L);
        ManualPackNoticeContext context = ManualPackNoticeContext.builder().taskInfo(taskInfo).nationalSubsidyHeadquartersWmPoiIdMap(wmPoiIdMap).commitUid(123).manualBatchId(456L).build();
        List<Long> taskIds = Arrays.asList(1L);
        doThrow(new RuntimeException("Mock exception")).when(wmLogisticsGatewayThriftServiceAdapter).deliveryBatchApplySignUseNewIface(any());
        try {
            // act
            noticeTask.notice(module, bizIdList, context, taskIds);
            fail("Should throw WmCustomerException");
        } catch (WmCustomerException e) {
            // assert
            assertEquals(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getCode());
            assertEquals("国补总部协议打包签约发起异常", e.getMessage());
        }
    }
}
