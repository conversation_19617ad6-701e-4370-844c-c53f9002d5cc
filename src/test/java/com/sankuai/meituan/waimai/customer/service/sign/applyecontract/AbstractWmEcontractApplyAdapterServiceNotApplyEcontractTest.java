package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.ProcessTagEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractApplyAdapterServiceNotApplyEcontractTest {

    @InjectMocks
    private AbstractWmEcontractApplyAdapterService service = new AbstractWmEcontractApplyAdapterService() {
    };

    private boolean invokeNotApplyEcontract(EcontractBatchContextBo batchContextBo) throws Exception {
        Method method = AbstractWmEcontractApplyAdapterService.class.getDeclaredMethod("notApplyEcontract", EcontractBatchContextBo.class);
        method.setAccessible(true);
        return (boolean) method.invoke(service, batchContextBo);
    }

    /**
     * Test case: When batchTypeEnum is not PHF_DELIVERY
     * Expected: Should return false
     */
    @Test
    public void testNotApplyEcontract_NotPhfDelivery() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        // act
        boolean result = invokeNotApplyEcontract(batchContextBo);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: When batchTypeEnum is PHF_DELIVERY but tag is not DR_TAG
     * Expected: Should return false
     */
    @Test
    public void testNotApplyEcontract_PhfDeliveryButNotDrTag() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(EcontractBatchTypeEnum.PHF_DELIVERY);
        batchContextBo.setTag(ProcessTagEnum.SPLIT_TAG.getType());
        // act
        boolean result = invokeNotApplyEcontract(batchContextBo);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: When both batchTypeEnum is PHF_DELIVERY and tag is DR_TAG
     * Expected: Should return true
     */
    @Test
    public void testNotApplyEcontract_PhfDeliveryAndDrTag() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(EcontractBatchTypeEnum.PHF_DELIVERY);
        batchContextBo.setTag(ProcessTagEnum.DR_TAG.getType());
        // act
        boolean result = invokeNotApplyEcontract(batchContextBo);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: When batchContextBo is null
     * Expected: Should throw NullPointerException (wrapped in InvocationTargetException)
     */
    @Test
    public void testNotApplyEcontract_NullInput() throws Throwable {
        try {
            // act
            invokeNotApplyEcontract(null);
            fail("Expected NullPointerException");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Test case: When batchTypeEnum is null
     * Expected: Should return false
     */
    @Test
    public void testNotApplyEcontract_NullBatchType() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setBatchTypeEnum(null);
        // act
        boolean result = invokeNotApplyEcontract(batchContextBo);
        // assert
        assertFalse(result);
    }
}