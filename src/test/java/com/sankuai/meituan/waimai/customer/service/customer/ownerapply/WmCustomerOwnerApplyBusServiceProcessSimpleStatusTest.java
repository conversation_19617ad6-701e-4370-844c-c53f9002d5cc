package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStepStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyStep;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyBusServiceProcessSimpleStatusTest {

    @InjectMocks
    private WmCustomerOwnerApplyBusService wmCustomerOwnerApplyBusService;

    @Mock
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    private WmCustomerOwnerApply wmCustomerOwnerApply;

    private List<CustomerOwnerApplyStep> applyStepList;

    private CustomerOwnerApplyStepStatusEnum status;

    private String auditTips;

    private String stepName;

    private Method processSimpleStatusMethod;

    @Before
    public void setUp() throws Exception {
        wmCustomerOwnerApply = new WmCustomerOwnerApply();
        wmCustomerOwnerApply.setId(1);
        wmCustomerOwnerApply.setUtime(123456);
        applyStepList = new ArrayList<>();
        CustomerOwnerApplyStep step1 = new CustomerOwnerApplyStep();
        CustomerOwnerApplyStep step2 = new CustomerOwnerApplyStep();
        applyStepList.add(step1);
        applyStepList.add(step2);
        status = CustomerOwnerApplyStepStatusEnum.PASS;
        auditTips = "Test audit tips";
        stepName = "Test step name";
        processSimpleStatusMethod = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("processSimpleStatus", WmCustomerOwnerApply.class, List.class, CustomerOwnerApplyStepStatusEnum.class, String.class, String.class);
        processSimpleStatusMethod.setAccessible(true);
    }

    /**
     * Test normal case with valid inputs
     */
    @Test
    public void testProcessSimpleStatus_Success() throws Throwable {
        // arrange
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setAuditResult("Test audit result");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(1)).thenReturn(audit);
        // act
        processSimpleStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, status, auditTips, stepName);
        // assert
        CustomerOwnerApplyStep updatedStep = applyStepList.get(1);
        assertTrue(updatedStep.isOnStep());
        assertEquals(Integer.valueOf(status.getCode()), updatedStep.getStatusCode());
        assertEquals(auditTips, updatedStep.getAuditTips());
        assertEquals(stepName, updatedStep.getStepName());
        assertEquals(Integer.valueOf(wmCustomerOwnerApply.getUtime()), updatedStep.getStepTime());
        assertEquals(audit.getAuditResult(), updatedStep.getRemark());
    }

    /**
     * Test when getAuditRemark throws WmCustomerException
     */
    @Test
    public void testProcessSimpleStatus_AuditRemarkException() throws Throwable {
        // arrange
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(1)).thenReturn(null);
        // act & assert
        try {
            processSimpleStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, status, auditTips, stepName);
            fail("Expected WmCustomerException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof WmCustomerException);
        }
    }

    /**
     * Test with empty applyStepList
     */
    @Test
    public void testProcessSimpleStatus_EmptyApplyStepList() throws Throwable {
        // arrange
        applyStepList = new ArrayList<>();
        // act & assert
        try {
            processSimpleStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, status, auditTips, stepName);
            fail("Expected IndexOutOfBoundsException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof IndexOutOfBoundsException);
        }
    }

    /**
     * Test with null WmCustomerOwnerApply
     */
    @Test
    public void testProcessSimpleStatus_NullWmCustomerOwnerApply() throws Throwable {
        // arrange
        wmCustomerOwnerApply = null;
        // act & assert
        try {
            processSimpleStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, status, auditTips, stepName);
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Test with null applyStepList
     */
    @Test
    public void testProcessSimpleStatus_NullApplyStepList() throws Throwable {
        // arrange
        applyStepList = null;
        // act & assert
        try {
            processSimpleStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, status, auditTips, stepName);
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Test with null status
     */
    @Test
    public void testProcessSimpleStatus_NullStatus() throws Throwable {
        // arrange
        WmCustomerOwnerApplyAudit audit = new WmCustomerOwnerApplyAudit();
        audit.setAuditResult("Test audit result");
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(1)).thenReturn(audit);
        // act & assert
        try {
            processSimpleStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList, null, auditTips, stepName);
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }
}
