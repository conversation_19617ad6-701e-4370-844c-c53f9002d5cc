package com.sankuai.meituan.waimai.customer.service.sign.compare;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SmsStageInfoBoComparatorTest {

    private SmsStageInfoBoComparator smsStageInfoBoComparator = new SmsStageInfoBoComparator();

    @InjectMocks
    private SmsStageInfoBoComparator comparator;

    @Mock
    private SignerInfoBo sourceSignerInfoBo;

    @Mock
    private SignerInfoBo targetSignerInfoBo;

    @Mock
    private CertifyH5InfoBo sourceCertifyH5InfoBo;

    @Mock
    private CertifyH5InfoBo targetCertifyH5InfoBo;

    public SmsStageInfoBoComparatorTest() {
    }

    private void compareCertifyH5Info(CertifyH5InfoBo source, CertifyH5InfoBo target, StringBuilder failMsg) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareCertifyH5Info", CertifyH5InfoBo.class, CertifyH5InfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, source, target, failMsg);
    }

    private void invokePrivateMethod(Object target, String methodName, Object... args) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, SignerInfoBo.class, SignerInfoBo.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(target, args);
    }

    private void setupBasicMatchingMocks() {
        when(sourceSignerInfoBo.getName()).thenReturn("name");
        when(targetSignerInfoBo.getName()).thenReturn("name");
        when(sourceSignerInfoBo.getIdCardNo()).thenReturn("id123");
        when(targetSignerInfoBo.getIdCardNo()).thenReturn("id123");
        setupCertifyH5InfoMocks();
    }

    private void setupCertifyH5InfoMocks() {
        when(sourceSignerInfoBo.getCertifyH5InfoBo()).thenReturn(sourceCertifyH5InfoBo);
        when(targetSignerInfoBo.getCertifyH5InfoBo()).thenReturn(targetCertifyH5InfoBo);
        when(sourceCertifyH5InfoBo.getCertType()).thenReturn(1);
        when(targetCertifyH5InfoBo.getCertType()).thenReturn(1);
    }

    @Test
    public void testCompareCertifyH5InfoAllFieldsEqual() throws Throwable {
        CertifyH5InfoBo source = new CertifyH5InfoBo();
        source.setCertType(1);
        source.setCertPhone("1234567890");
        source.setCompanyName("Company Name");
        source.setCompanyNum("1234567890");
        source.setSignerName("Signer Name");
        source.setSignerCardType(1);
        source.setSignerCardNum("1234567890");
        CertifyH5InfoBo target = new CertifyH5InfoBo();
        target.setCertType(1);
        target.setCertPhone("1234567890");
        target.setCompanyName("Company Name");
        target.setCompanyNum("1234567890");
        target.setSignerName("Signer Name");
        target.setSignerCardType(1);
        target.setSignerCardNum("1234567890");
        StringBuilder failMsg = new StringBuilder();
        compareCertifyH5Info(source, target, failMsg);
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testCompareCertifyH5InfoFieldNotEqual() throws Throwable {
        CertifyH5InfoBo source = new CertifyH5InfoBo();
        source.setCertType(1);
        source.setCertPhone("1234567890");
        source.setCompanyName("Company Name");
        source.setCompanyNum("1234567890");
        source.setSignerName("Signer Name");
        source.setSignerCardType(1);
        source.setSignerCardNum("1234567890");
        CertifyH5InfoBo target = new CertifyH5InfoBo();
        target.setCertType(2);
        target.setCertPhone("0987654321");
        target.setCompanyName("Different Company Name");
        target.setCompanyNum("0987654321");
        target.setSignerName("Different Signer Name");
        target.setSignerCardType(2);
        target.setSignerCardNum("0987654321");
        StringBuilder failMsg = new StringBuilder();
        compareCertifyH5Info(source, target, failMsg);
        assertTrue(failMsg.toString().contains("短信H5认证类型不一致"));
        assertTrue(failMsg.toString().contains("短信H5认证手机号不一致"));
        assertTrue(failMsg.toString().contains("短信H5认证公司名称不一致"));
        assertTrue(failMsg.toString().contains("短信H5认证公司统一信用代码不一致"));
        assertTrue(failMsg.toString().contains("短信H5认证签约人姓名不一致"));
        assertTrue(failMsg.toString().contains("短信H5认证签约人证件类型不一致"));
        assertTrue(failMsg.toString().contains("短信H5认证签约人证件号码不一致"));
    }

    @Test(expected = InvocationTargetException.class)
    public void testCompareCertifyH5InfoNull() throws Throwable {
        CertifyH5InfoBo source = null;
        CertifyH5InfoBo target = new CertifyH5InfoBo();
        target.setCertType(1);
        target.setCertPhone("1234567890");
        target.setCompanyName("Company Name");
        target.setCompanyNum("1234567890");
        target.setSignerName("Signer Name");
        target.setSignerCardType(1);
        target.setSignerCardNum("1234567890");
        StringBuilder failMsg = new StringBuilder();
        compareCertifyH5Info(source, target, failMsg);
    }

    @Test
    public void testCompareSignerInfoBo_NameMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        when(sourceSignerInfoBo.getName()).thenReturn("sourceName");
        when(targetSignerInfoBo.getName()).thenReturn("targetName");
        setupCertifyH5InfoMocks();
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人姓名不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_IdCardNoMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        when(sourceSignerInfoBo.getName()).thenReturn("name");
        when(targetSignerInfoBo.getName()).thenReturn("name");
        when(sourceSignerInfoBo.getIdCardNo()).thenReturn("source123");
        when(targetSignerInfoBo.getIdCardNo()).thenReturn("target123");
        setupCertifyH5InfoMocks();
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人idCardNo不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_PhoneMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        when(sourceSignerInfoBo.getPhone()).thenReturn("***********");
        when(targetSignerInfoBo.getPhone()).thenReturn("***********");
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人手机号不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_BankNameMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        when(sourceSignerInfoBo.getBankName()).thenReturn("Source Bank");
        when(targetSignerInfoBo.getBankName()).thenReturn("Target Bank");
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人银行名称不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_BankCardNoMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        when(sourceSignerInfoBo.getBankCardNo()).thenReturn("*************");
        when(targetSignerInfoBo.getBankCardNo()).thenReturn("*************");
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人银行卡号不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_ClientIdMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        when(sourceSignerInfoBo.getClientId()).thenReturn("sourceClientId");
        when(targetSignerInfoBo.getClientId()).thenReturn("targetClientId");
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人clientId不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_SmsTemplateIdMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        when(sourceSignerInfoBo.getSmsTemplateId()).thenReturn("template1");
        when(targetSignerInfoBo.getSmsTemplateId()).thenReturn("template2");
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人短信模板id不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_SmsParamMapMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        Map<String, String> sourceMap = new HashMap<>();
        sourceMap.put("key", "sourceValue");
        Map<String, String> targetMap = new HashMap<>();
        targetMap.put("key", "targetValue");
        when(sourceSignerInfoBo.getSmsParamMap()).thenReturn(sourceMap);
        when(targetSignerInfoBo.getSmsParamMap()).thenReturn(targetMap);
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信模板参数不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_MobileListMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        when(sourceSignerInfoBo.getMobileList()).thenReturn(Arrays.asList("123"));
        when(targetSignerInfoBo.getMobileList()).thenReturn(Arrays.asList("456"));
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人手机号列表不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_ChannelListMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        when(sourceSignerInfoBo.getChannelList()).thenReturn(Arrays.asList("channel1"));
        when(targetSignerInfoBo.getChannelList()).thenReturn(Arrays.asList("channel2"));
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信接受人短信渠道不一致"));
    }

    @Test
    public void testCompareSignerInfoBo_CertifyH5InfoBoMismatch() throws Throwable {
        StringBuilder failMsg = new StringBuilder();
        setupBasicMatchingMocks();
        when(sourceCertifyH5InfoBo.getCertType()).thenReturn(1);
        when(targetCertifyH5InfoBo.getCertType()).thenReturn(2);
        invokePrivateMethod(comparator, "compareSignerInfoBo", sourceSignerInfoBo, targetSignerInfoBo, failMsg);
        assertTrue(failMsg.toString().contains("短信H5认证类型不一致"));
    }

    /**
     * 测试两个Map都为空的情况
     */
    @Test
    public void testCompareSmsParamMapBothEmpty() throws Throwable {
        // arrange
        Map<String, String> sourceSmsParamMap = new HashMap<>();
        Map<String, String> targetSmsParamMap = new HashMap<>();
        StringBuilder failMsg = new StringBuilder();
        // act
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareSmsParamMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, sourceSmsParamMap, targetSmsParamMap, failMsg);
        // assert
        assertEquals("", failMsg.toString());
    }

    /**
     * 测试source为空，target不为空的情况
     */
    @Test
    public void testCompareSmsParamMapSourceEmpty() throws Throwable {
        // arrange
        Map<String, String> sourceSmsParamMap = new HashMap<>();
        Map<String, String> targetSmsParamMap = new HashMap<>();
        targetSmsParamMap.put("key", "value");
        StringBuilder failMsg = new StringBuilder();
        // act
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareSmsParamMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, sourceSmsParamMap, targetSmsParamMap, failMsg);
        // assert
        assertTrue(failMsg.toString().contains("短信模板参数不一致"));
    }

    /**
     * 测试键值对不匹配的情况
     */
    @Test
    public void testCompareSmsParamMapNoMatch() throws Throwable {
        // arrange
        Map<String, String> sourceSmsParamMap = new HashMap<>();
        sourceSmsParamMap.put("key", "value1");
        Map<String, String> targetSmsParamMap = new HashMap<>();
        targetSmsParamMap.put("key", "value2");
        StringBuilder failMsg = new StringBuilder();
        // act
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareSmsParamMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, sourceSmsParamMap, targetSmsParamMap, failMsg);
        // assert
        assertTrue(failMsg.toString().contains("短信模板参数不一致"));
    }

    /**
     * 测试键值对完全匹配的情况
     */
    @Test
    public void testCompareSmsParamMapMatch() throws Throwable {
        // arrange
        Map<String, String> sourceSmsParamMap = new HashMap<>();
        sourceSmsParamMap.put("key", "value");
        Map<String, String> targetSmsParamMap = new HashMap<>();
        targetSmsParamMap.put("key", "value");
        StringBuilder failMsg = new StringBuilder();
        // act
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareSmsParamMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, sourceSmsParamMap, targetSmsParamMap, failMsg);
        // assert
        assertEquals("", failMsg.toString());
    }

    /**
     * 测试detail键值对不匹配的特殊情况
     */
    @Test
    public void testCompareSmsParamMapDetailDifferent() throws Throwable {
        // arrange
        Map<String, String> sourceSmsParamMap = new HashMap<>();
        sourceSmsParamMap.put("detail", "value1");
        Map<String, String> targetSmsParamMap = new HashMap<>();
        targetSmsParamMap.put("detail", "value2");
        StringBuilder failMsg = new StringBuilder();
        // act
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareSmsParamMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, sourceSmsParamMap, targetSmsParamMap, failMsg);
        // assert
        assertEquals("", failMsg.toString());
    }

    /**
     * 测试多个键值对，其中包含不匹配的情况
     */
    @Test
    public void testCompareSmsParamMapMultipleKeysWithMismatch() throws Throwable {
        // arrange
        Map<String, String> sourceSmsParamMap = new HashMap<>();
        sourceSmsParamMap.put("key1", "value1");
        sourceSmsParamMap.put("key2", "value2");
        Map<String, String> targetSmsParamMap = new HashMap<>();
        targetSmsParamMap.put("key1", "value1");
        targetSmsParamMap.put("key2", "different");
        StringBuilder failMsg = new StringBuilder();
        // act
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareSmsParamMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, sourceSmsParamMap, targetSmsParamMap, failMsg);
        // assert
        assertTrue(failMsg.toString().contains("短信模板参数不一致"));
    }

    @Test
    public void testCompareSmsParamMapTargetEmpty() throws Throwable {
        Map<String, String> sourceSmsParamMap = new HashMap<>();
        sourceSmsParamMap.put("key", "value");
        Map<String, String> targetSmsParamMap = new HashMap<>();
        StringBuilder failMsg = new StringBuilder();
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareSmsParamMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, sourceSmsParamMap, targetSmsParamMap, failMsg);
        assertEquals("", failMsg.toString());
    }

    @Test
    public void testCompareSmsParamMapDetailMatch() throws Throwable {
        Map<String, String> sourceSmsParamMap = new HashMap<>();
        sourceSmsParamMap.put("detail", "value");
        Map<String, String> targetSmsParamMap = new HashMap<>();
        targetSmsParamMap.put("detail", "value");
        StringBuilder failMsg = new StringBuilder();
        Method method = SmsStageInfoBoComparator.class.getDeclaredMethod("compareSmsParamMap", Map.class, Map.class, StringBuilder.class);
        method.setAccessible(true);
        method.invoke(smsStageInfoBoComparator, sourceSmsParamMap, targetSmsParamMap, failMsg);
        assertEquals("", failMsg.toString());
    }
}