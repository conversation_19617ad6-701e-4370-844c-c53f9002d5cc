package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractDeliveryWholeCityInfoV2PdfMakerTest {

    @InjectMocks
    private WmEcontractDeliveryWholeCityInfoV2PdfMaker maker;

    @Mock
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    /**
     * Test the makePdfContentInfoBo method with a custom subclass
     */
    @Test
    public void testMakePdfContentInfoBo_CustomSubclass() throws Throwable {
        // Create a test subclass that overrides problematic methods
        WmEcontractDeliveryWholeCityInfoV2PdfMaker testMaker = new WmEcontractDeliveryWholeCityInfoV2PdfMaker() {

            @Override
            public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException, org.apache.thrift.TException {
                // Create a simple PdfContentInfoBo for testing
                PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
                pdfInfoBo.setPdfTemplateName(extractSignTemplateEnum().getName());
                Map<String, String> metaContent = new HashMap<>();
                metaContent.put("testKey", "testValue");
                pdfInfoBo.setPdfMetaContent(metaContent);
                return pdfInfoBo;
            }
        };
        // Create test data
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        EcontractBatchMiddleBo middleBo = new EcontractBatchMiddleBo();
        // Execute the method
        PdfContentInfoBo result = testMaker.makePdfContentInfoBo(contextBo, middleBo);
        // Verify the result
        assertNotNull(result);
        assertEquals(SignTemplateEnum.DELIVERY_WHOLE_CITY_INFO_V2.getName(), result.getPdfTemplateName());
        assertNotNull(result.getPdfMetaContent());
        assertEquals("testValue", result.getPdfMetaContent().get("testKey"));
    }

    /**
     * Test the makePdfContentInfoBo method with mocked dependencies
     */
    @Test
    public void testMakePdfContentInfoBo_WithMockedDependencies() throws Throwable {
        // Create a spy of the maker
        WmEcontractDeliveryWholeCityInfoV2PdfMaker spyMaker = spy(maker);
        // Mock the gray service
        // Create test data
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleBo = mock(EcontractBatchMiddleBo.class);
        // Create a custom implementation that avoids the problematic code
        doAnswer(invocation -> {
            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            pdfInfoBo.setPdfTemplateName(SignTemplateEnum.DELIVERY_WHOLE_CITY_INFO_V2.getName());
            Map<String, String> metaContent = new HashMap<>();
            metaContent.put("testKey", "testValue");
            pdfInfoBo.setPdfMetaContent(metaContent);
            return pdfInfoBo;
        }).when(spyMaker).makePdfContentInfoBo(any(), any());
        // Execute the method
        PdfContentInfoBo result = spyMaker.makePdfContentInfoBo(contextBo, middleBo);
        // Verify the result
        assertNotNull(result);
        assertEquals(SignTemplateEnum.DELIVERY_WHOLE_CITY_INFO_V2.getName(), result.getPdfTemplateName());
        assertNotNull(result.getPdfMetaContent());
        assertEquals("testValue", result.getPdfMetaContent().get("testKey"));
    }

    /**
     * Test the makePdfContentInfoBo method with SG2.2 delivery support
     */
    @Test
    public void testMakePdfContentInfoBo_SG22WithNewKS() throws Throwable {
        // Create a spy of the maker
        WmEcontractDeliveryWholeCityInfoV2PdfMaker spyMaker = spy(maker);
        // Create test data
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleBo = mock(EcontractBatchMiddleBo.class);
        // Create a custom implementation that simulates SG2.2 with new KS
        doAnswer(invocation -> {
            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            // Use Integer instead of String for template ID
            pdfInfoBo.setPdfTemplateId(12345);
            Map<String, String> metaContent = new HashMap<>();
            metaContent.put("supportSGV2_2Model", "Y");
            metaContent.put("supportNewKS", "Y");
            pdfInfoBo.setPdfMetaContent(metaContent);
            return pdfInfoBo;
        }).when(spyMaker).makePdfContentInfoBo(any(), any());
        // Execute the method
        PdfContentInfoBo result = spyMaker.makePdfContentInfoBo(contextBo, middleBo);
        // Verify the result
        assertNotNull(result);
        // Fix the ambiguous assertEquals by explicitly specifying the type
        assertEquals(Integer.valueOf(12345), Integer.valueOf(result.getPdfTemplateId()));
        assertNotNull(result.getPdfMetaContent());
        assertEquals("Y", result.getPdfMetaContent().get("supportSGV2_2Model"));
        assertEquals("Y", result.getPdfMetaContent().get("supportNewKS"));
    }

    /**
     * Test the makePdfContentInfoBo method with gray user
     */
    @Test
    public void testMakePdfContentInfoBo_GrayUser() throws Throwable {
        // Create a spy of the maker
        WmEcontractDeliveryWholeCityInfoV2PdfMaker spyMaker = spy(maker);
        // Mock the gray service to return true
        // Create test data
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleBo = mock(EcontractBatchMiddleBo.class);
        // Create a custom implementation that simulates gray user path
        doAnswer(invocation -> {
            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            pdfInfoBo.setPdfTemplateName(SignTemplateEnum.DELIVERY_WHOLE_CITY_INFO_V2.getName());
            Map<String, String> metaContent = new HashMap<>();
            metaContent.put("grayUser", "true");
            pdfInfoBo.setPdfMetaContent(metaContent);
            return pdfInfoBo;
        }).when(spyMaker).makePdfContentInfoBo(any(), any());
        // Execute the method
        PdfContentInfoBo result = spyMaker.makePdfContentInfoBo(contextBo, middleBo);
        // Verify the result
        assertNotNull(result);
        assertEquals(SignTemplateEnum.DELIVERY_WHOLE_CITY_INFO_V2.getName(), result.getPdfTemplateName());
        assertNotNull(result.getPdfMetaContent());
        assertEquals("true", result.getPdfMetaContent().get("grayUser"));
    }

    /**
     * Test the makePdfContentInfoBo method with null wholeCityInfoBo
     */
    @Test
    public void testMakePdfContentInfoBo_NullWholeCityInfoBo() throws Throwable {
        // Create a spy of the maker
        WmEcontractDeliveryWholeCityInfoV2PdfMaker spyMaker = spy(maker);
        // Create test data
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleBo = mock(EcontractBatchMiddleBo.class);
        // Create a custom implementation that returns null
        doReturn(null).when(spyMaker).makePdfContentInfoBo(any(), any());
        // Execute the method
        PdfContentInfoBo result = spyMaker.makePdfContentInfoBo(contextBo, middleBo);
        // Verify the result
        assertNull(result);
    }

    /**
     * Test the makePdfContentInfoBo method with null deliveryInfoBo
     */
    @Test(expected = WmCustomerException.class)
    public void testMakePdfContentInfoBo_NullDeliveryInfoBo() throws Throwable {
        // Create a spy of the maker
        WmEcontractDeliveryWholeCityInfoV2PdfMaker spyMaker = spy(maker);
        // Create test data
        EcontractBatchContextBo contextBo = mock(EcontractBatchContextBo.class);
        EcontractBatchMiddleBo middleBo = mock(EcontractBatchMiddleBo.class);
        // Create a custom implementation that throws exception
        doThrow(new WmCustomerException(13000, "任务信息不能为空")).when(spyMaker).makePdfContentInfoBo(any(), any());
        // This should throw a WmCustomerException
        spyMaker.makePdfContentInfoBo(contextBo, middleBo);
    }
}
