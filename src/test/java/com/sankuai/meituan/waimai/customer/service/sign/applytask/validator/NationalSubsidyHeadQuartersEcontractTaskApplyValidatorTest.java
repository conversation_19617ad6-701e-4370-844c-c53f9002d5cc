package com.sankuai.meituan.waimai.customer.service.sign.applytask.validator;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidyHeadQuartersEcontractTaskApplyValidatorTest {

    private static final int LABEL_ID = 954;

    @Spy
    private TestableNationalSubsidyHeadQuartersEcontractTaskApplyValidator validator;

    /**
     * Test implementation that overrides the validate method to avoid static call
     */
    private static class TestableNationalSubsidyHeadQuartersEcontractTaskApplyValidator extends NationalSubsidyHeadQuartersEcontractTaskApplyValidator {

        @Override
        public void validate(EcontractTaskApplyBo applyBo) throws WmCustomerException {
            List<Long> wmPoiIdList = getNoLabelIdWmPoiId(applyBo, LABEL_ID);
            if (wmPoiIdList == null || wmPoiIdList.isEmpty()) {
                return;
            }
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "存在非总部国补的门店");
        }
    }

    /**
     * Setup test environment
     */
    @Before
    public void setup() throws WmCustomerException {
    }

    /**
     * Test case: All POIs have the required label
     * Expected: Method executes successfully without exception
     */
    @Test
    public void testValidate_AllPoisHaveLabel() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createApplyBoWithPois(Arrays.asList(1L, 2L));
        doReturn(Collections.emptyList()).when(validator).getNoLabelIdWmPoiId(any(), eq(LABEL_ID));
        // act
        validator.validate(applyBo);
        // assert
        verify(validator, times(1)).getNoLabelIdWmPoiId(eq(applyBo), eq(LABEL_ID));
    }

    /**
     * Test case: Some POIs don't have the required label
     * Expected: WmCustomerException with PARAM_ERROR code
     */
    @Test
    public void testValidate_SomePoisWithoutLabel() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createApplyBoWithPois(Arrays.asList(1L, 2L));
        doReturn(Arrays.asList(1L, 2L)).when(validator).getNoLabelIdWmPoiId(any(), eq(LABEL_ID));
        // act & assert
        try {
            validator.validate(applyBo);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            assertEquals(CustomerErrorCodeConstants.PARAM_ERROR, e.getCode());
            assertEquals("存在非总部国补的门店", e.getMessage());
            verify(validator, times(1)).getNoLabelIdWmPoiId(eq(applyBo), eq(LABEL_ID));
        }
    }

    /**
     * Test case: Empty POI list
     * Expected: Method executes successfully without exception
     */
    @Test
    public void testValidate_EmptyPoiList() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = createApplyBoWithPois(Collections.emptyList());
        doReturn(Collections.emptyList()).when(validator).getNoLabelIdWmPoiId(any(), eq(LABEL_ID));
        // act
        validator.validate(applyBo);
        // assert
        verify(validator, times(1)).getNoLabelIdWmPoiId(eq(applyBo), eq(LABEL_ID));
    }

    /**
     * Test case: Null input
     * Expected: IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testValidate_NullInput() throws Throwable {
        // arrange
        doThrow(new IllegalArgumentException()).when(validator).getNoLabelIdWmPoiId(null, LABEL_ID);
        // act
        validator.validate(null);
    }

    /**
     * Helper method to create EcontractTaskApplyBo with given POI IDs
     */
    private EcontractTaskApplyBo createApplyBoWithPois(List<Long> poiIds) {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        List<EcontractDataPoiBizBo> poiBizBoList = new ArrayList<>();
        for (Long poiId : poiIds) {
            EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
            poiBizBo.setWmPoiId(poiId);
            poiBizBoList.add(poiBizBo);
        }
        dataSourceBo.setWmPoiIdAndBizIdList(poiBizBoList);
        dataSourceBoList.add(dataSourceBo);
        applyBo.setDataSourceBoList(dataSourceBoList);
        return applyBo;
    }
}
