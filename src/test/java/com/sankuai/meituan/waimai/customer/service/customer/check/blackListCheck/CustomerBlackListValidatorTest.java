package com.sankuai.meituan.waimai.customer.service.customer.check.blackListCheck;


import com.sankuai.meituan.waimai.customer.adapter.WmBlackListAdapter;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.domain.rule.SceneRuleResultDto;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.util.Collections;

import static org.mockito.Mockito.*;

/**
 * 测试CustomerBlackListValidator的checkCustomerBlackList方法
 */
public class CustomerBlackListValidatorTest {

    @Spy
    private CustomerBlackListValidator customerBlackListValidator;

    @Mock
    private WmBlackListAdapter wmBlackListAdapter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试当wmCustomerBasicBo为null时抛出异常
     */
    @Test
    public void testCheckCustomerBlackListWithNullWmCustomerBasicBo() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = null;

        WmCustomerException thrown = Assert.assertThrows(WmCustomerException.class, () -> {
            customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);
        });
        // assert is handled by the expected exception
    }

    /**
     * 测试当客户类型为CUSTOMER_TYPE_BUSINESS且客户编号为空时抛出异常
     */
    @Test(expected = WmCustomerException.class)
    public void testCheckCustomerBlackListWithBusinessTypeAndEmptyCustomerNumber() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        wmCustomerBasicBo.setCustomerNumber("");
        doReturn(true).when(customerBlackListValidator).isCustomerNumberInGrayList(wmCustomerBasicBo.getCustomerNumber());
        // act
        customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);

        // assert is handled by the expected exception
    }

    /**
     * 测试当客户类型为CUSTOMER_TYPE_IDCARD且客户编号为空时抛出异常
     */
    @Test(expected = WmCustomerException.class)
    public void testCheckCustomerBlackListWithIdCardTypeAndEmptyCustomerNumber() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_IDCARD.getCode());
        wmCustomerBasicBo.setCustomerNumber("");
        doReturn(true).when(customerBlackListValidator).isCustomerNumberInGrayList(wmCustomerBasicBo.getCustomerNumber());
        // act
        customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);

        // assert is handled by the expected exception
    }

    /**
     * 测试当客户类型不合法时抛出异常
     */
    @Test(expected = WmCustomerException.class)
    public void testCheckCustomerBlackListWithInvalidCustomerType() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setCustomerType(-1); // 设置一个不存在的客户类型
        doReturn(true).when(customerBlackListValidator).isCustomerNumberInGrayList(wmCustomerBasicBo.getCustomerNumber());
        // act
        customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);

        // assert is handled by the expected exception
    }

    /**
     * 测试客户类型为CUSTOMER_TYPE_BUSINESS，且客户编号非空，黑名单校验通过的情况
     */
//    @Test
//    public void testCheckCustomerBlackListWithBusinessTypeAndValidCustomerNumber() throws Throwable {
//        // arrange
//        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
//        wmCustomerBasicBo.setCustomerNumber("123456");
//        doReturn(true).when(customerBlackListValidator).isCustomerNumberInGrayList(wmCustomerBasicBo.getCustomerNumber());
//        doReturn(new SceneRuleResultDto(0, "Success", true, Collections.emptyList())).when(wmBlackListAdapter).checkBlackList(anyString(), anyMap());
//        //when(wmBlackListAdapter.checkBlackList(anyString(), anyMap())).thenReturn(new SceneRuleResultDto(0, "Success", true, Collections.emptyList()));
//
//        // act
//        customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);
//
//        // assert
//        verify(wmBlackListAdapter, times(1)).checkBlackList(anyString(), anyMap());
//    }
//
//    /**
//     * 测试客户类型为CUSTOMER_TYPE_IDCARD，且客户编号非空，黑名单校验通过的情况
//     */
//    @Test
//    public void testCheckCustomerBlackListWithIdCardTypeAndValidCustomerNumber() throws Throwable {
//        // arrange
//        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
//        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_IDCARD.getCode());
//        wmCustomerBasicBo.setCustomerNumber("123456789");
//        doReturn(true).when(customerBlackListValidator).isCustomerNumberInGrayList(wmCustomerBasicBo.getCustomerNumber());
//
//        when(wmBlackListAdapter.checkBlackList(anyString(), anyMap())).thenReturn(new SceneRuleResultDto(0, "Success", true, Collections.emptyList()));
//
//        // act
//        customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);
//
//        // assert
//        verify(wmBlackListAdapter, times(1)).checkBlackList(anyString(), anyMap());
//    }
}
