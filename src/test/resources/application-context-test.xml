<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:crane="http://code.dianping.com/schema/crane"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
						   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
						   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
						   http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
                           http://code.dianping.com/schema/crane http://code.dianping.com/schema/crane/crane-1.0.xsd">
    <aop:aspectj-autoproxy expose-proxy="true"/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:/conf/database.properties</value>
                <value>classpath:/conf/config.properties</value>
                <value>classpath:/conf/common.properties</value>
            </list>
        </property>
    </bean>

    <bean class="com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil"/>
    <bean class="com.sankuai.meituan.waimai.util.jmonitor.JmonitorSpringBeanUtil"/>
    <!--强制确保jm所需各个资源的加载顺序-->
    <bean class="com.sankuai.meituan.waimai.customer.util.JmonitorInitializingBean">
        <property name="configName" value="jmonitor.properties"></property>
    </bean>

    <bean id="log4jInitializer" class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetClass"
                  value="org.springframework.util.Log4jConfigurer" />
        <property name="targetMethod" value="initLogging" />
        <property name="arguments">
            <list>
                <value>classpath:log4j2.xml</value>
            </list>
        </property>
    </bean>



    <util:properties id="dataSourceProps" location="classpath:/conf/database.properties"/>
    <context:component-scan base-package="com.sankuai.meituan" />
    <crane:annotation-driven />

    <import resource="classpath:database/multi-datasource.xml" />
    <import resource="classpath:waimaiConfig/waimai_service_notice_client.xml"/>
    <import resource="classpath:waimai_service_poi_client.xml"/>
    <import resource="classpath:waimai_service_poi_flowline_client.xml"/>
    <import resource="classpath:waimai_service_poimanager_client.xml"/>
    <import resource="classpath:waimai_service_poisearch_client.xml"/>
    <import resource="classpath:waimai_service_poiquery_client.xml"/>
    <import resource="classpath:waimaiConfig/waimai_service_infra_client.xml"/>
    <import resource="classpath:waimai_service_poiaudit_client.xml"/>
    <import resource="classpath:group-m-tair.xml"/>
    <import resource="classpath:waimai_service_contractmanager_client.xml"/>
    <import resource="classpath:waimai_service_contract_client.xml"/>
    <import resource="classpath:sg-api-sdk-context.xml"/>
    <import resource="classpath:spring-bean/statemachine.xml"/>
    <import resource="classpath:waimai_service_audit_client.xml"/>
    <import resource="classpath:waimaiConfig/waimai_service_agent_client.xml"/>
    <import resource="classpath:waimai_service_poilogistics_client.xml"/>
    <import resource="classpath:waimaiConfig/waimai_service_oplog_client.xml" />
    <import resource="classpath:waimai_service_scm_bizsettle_client.xml"/>
<!--    <import resource="classpath:rabbitmqConfig/applicationContext-waimai-rabbitmq-plugin.xml"/>-->
    <import resource="classpath:eagle.xml"/>
    <import resource="classpath:spring-databus.xml"/>

    <import resource="spring-wallet.xml"/>
    <import resource="GisService-client.xml"/>
    <import resource="BankService-client.xml"/>
    <import resource="classpath:spring-bean/spring-mafka.xml"/>

    <bean id="kmsAuthDataSource" class="com.meituan.service.inf.kms.client.KmsAuthDataSource">
        <property name="appkey" value="com.sankuai.waimai.e.customer"/>
    </bean>
    <bean id="defaultAuthHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultAuthHandler">
        <property name="authDataSource" ref="kmsAuthDataSource" />
    </bean>

<!--    <bean id="producer" class="com.sankuai.meituan.mtmq.service.impl.MtmqProducer">-->
<!--        <property name="nameService" ref="nameService"/>-->
<!--    </bean>-->

    <bean id="customerProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start" destroy-method="close">
        <property name="namespace" value="waimai"/>
        <property name="appkey" value="com.sankuai.waimai.e.customer"/>
        <property name="topic" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('customer_state_notice_topic_name', 'customer.state.notice_dev')}"/>
    </bean>

    <bean id="wmCustomerKpThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerKpThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8430"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerContractThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8431"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerOplogThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerOplogThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerOplogThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8432"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8433"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerBrandThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerBrandThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerBrandThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8434"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerPoiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerPoiThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8435"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerContractVersionThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractVersionThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerContractVersionThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8436"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmEcontractSignThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmEcontractSignThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8437"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmEcontractSignManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignManagerThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmEcontractSignManagerThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8438"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <!-- 电子合同平台API -->
    <bean id="econtractAPIService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="9001" />
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>

    <bean id="wmCustomerSyncThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerSyncThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerSyncThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8441"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerSettleThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettleThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettleThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8439"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmSettleManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettleManagerThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettleManagerThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8440"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmCustomerCommonThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerCommonThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerCommonThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8442"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>
    <!-- 由于AuthUserOperateFilter(模拟用户)使用到upmService, 所以需配置 -->
    <bean id="upmConfig" class="com.sankuai.meituan.auth.vo.UpmConfig">
        <property name="clientId" value="xianfu_waimai"/>
        <property name="secret" ref="kms.upmAuthService.secret"/>
        <property name="timeout" value="1000"/>  <!-- pigeon 超时（ms） -->
        <property name="timeoutRetry" value="true"/>  <!-- pigeon 超时重试开关 -->
        <property name="retries" value="5"/>  <!-- pigeon 超时重试次数 -->
    </bean>
    <bean id="upmService" class="com.sankuai.meituan.auth.service.UpmService">
        <property name="upmConfig" ref="upmConfig"/>
    </bean>

    <bean id="kms.upmAuthService.secret" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="waimai_m_beekeeper"/>
        <property name="name" value="upmAuthService"/>
    </bean>
    <bean id="wmCustomerEsThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerEsThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerEsThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8443"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean id="wmSettleWalletManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettleWalletManagerThriftService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettleWalletManagerThriftServiceImpl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8444"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>

    <bean class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.pay.mwallet.proxy.thrift.MwalletBizEventNotifyService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmSettleWalletChangeNotifyHandleService"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地appkey -->
        <property name="port" value="8445"/> <!-- 监听端口 -->
        <property name="authHandler" ref="defaultAuthHandler" /> <!-- 必须设置authHandler属性鉴权才会生效 -->
    </bean>
</beans>

