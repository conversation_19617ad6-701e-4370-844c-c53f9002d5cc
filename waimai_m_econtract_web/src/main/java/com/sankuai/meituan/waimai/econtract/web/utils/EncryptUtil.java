package com.sankuai.meituan.waimai.econtract.web.utils;

import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyH5InfoBo;
import org.apache.commons.lang3.StringUtils;

/**
 * 加密工具类，从customer server服务拷贝
 *
 * @Author: wangyongfang
 * @Date: 2024-03-12
 */
public class EncryptUtil {

    /**
     * 手机号加密
     *
     * @param mobiles
     * @return
     */
    public static String encryptMobiles(String mobiles) {
        if (mobiles == null || "".equals(mobiles)) {
            return mobiles;
        }
        StringBuilder res = new StringBuilder();
        String[] split = mobiles.split(",");
        for (String mob : split) {
            if (mob.length() >= 11) {
                res.append(mob.substring(0, 3)).append("****")
                        .append(mob.substring(7)).append(",");
            } else {
                res.append(mob).append(",");
            }
        }
        res.setLength(res.length() - 1);
        return res.toString();
    }

    /**
     * 名称加密
     *
     * @param name
     * @return
     */
    public static String encryptName(String name) {
        if (StringUtils.isEmpty(name)) {
            return name;
        }
        StringBuilder builder = new StringBuilder(name);
        for (int i = 1; i < name.length(); i++) {
            builder.replace(i, i + 1, "*");
        }
        return builder.toString();
    }

    /**
     * 身份证号加密
     *
     * @param cardNum
     * @return
     */
    public static String encryptCardNum(String cardNum) {
        if (StringUtils.isEmpty(cardNum) || cardNum.length() == 2) {
            return cardNum;
        }
        StringBuilder builder = new StringBuilder(cardNum);
        for (int i = 1; i < cardNum.length() - 1; i++) {
            builder.replace(i, i + 1, "*");
        }
        return builder.toString();
    }

    /**
     * 对返回的CertifyH5InfoBo信息进行脱敏处理
     *
     * @param certifyH5InfoBo
     * @return
     */
    public static CertifyH5InfoBo encryptCertifyH5InfoBo(CertifyH5InfoBo certifyH5InfoBo) {
        if (certifyH5InfoBo == null) {
            return null;
        }
        certifyH5InfoBo.setCertPhone(EncryptUtil.encryptMobiles(certifyH5InfoBo.getCertPhone()));
        certifyH5InfoBo.setSignerName(EncryptUtil.encryptName(certifyH5InfoBo.getSignerName()));
        certifyH5InfoBo.setSignerCardNum(EncryptUtil.encryptCardNum(certifyH5InfoBo.getSignerCardNum()));
        certifyH5InfoBo.setCompanyNum(EncryptUtil.encryptCardNum(certifyH5InfoBo.getCompanyNum()));
        return certifyH5InfoBo;
    }
}
