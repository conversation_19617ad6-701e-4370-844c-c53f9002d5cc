package com.sankuai.meituan.waimai.econtract.web.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.handler.SimpleMappingExceptionResolver;

import javax.servlet.http.HttpServletRequest;
import java.io.EOFException;

/**
 * 继承SimpleMappingExceptionResolver<br>
 * 修改异常log的级别为error，以便日志监控可以发现某些异常
 * 
 * <AUTHOR>
 * @date 2014年6月20日 下午2:40:28
 * 
 */
public class ErrorLogMappingExceptionResolver extends SimpleMappingExceptionResolver {

	private static Logger logger = LoggerFactory.getLogger(ErrorLogMappingExceptionResolver.class);

	@Override
	protected void logException(Exception ex, HttpServletRequest request) {
        if (ex instanceof EOFException) {
            logger.warn(ex.getMessage(), ex);
            return;
        }

        if(ex instanceof  java.lang.NumberFormatException){
            logger.warn(ex.getMessage(), ex);
            return;
        }

        if(ex instanceof java.io.FileNotFoundException && "/404".equals(request.getAttribute("uri"))){
        	logger.warn(ex.getMessage(), ex);
            return;
        }

        logger.error(ex.getMessage(), ex);
    }

}
