package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.econtract.server.adapter.DeliveryContractPlatformAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.utils.PdfHandlerUtils;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractApplySubDataEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractErrorCode;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDeliveryTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.EcontractDeliveryPerDiscountInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.EcontractDeliveryPerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.batch.EcontractBatchDeliveryAreaInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.batch.EcontractBatchDeliveryPerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.single.EcontractSingleDeliveryPerInfoBo;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

/**
 * Created by lixuepeng on 2023/5/17
 */
@Slf4j
@Service
public class DeliverySinglePerformancePDFHandler implements EcontractApplySubDataHandler {

    @Autowired
    private DeliveryContractPlatformAdapter deliveryContractPlatformAdapter;

    // 申请签约数据类型
    public EcontractApplySubDataEnum applySubDataEnum() {
        return EcontractApplySubDataEnum.DELIVERY_SINGLE_PERFORMANCE_PDF;
    }

    // 调用外部接口获取申请签约数据
    public String queryApplySubData(Map<Long, Long> wmPoiAndBizMap) throws EcontractException {
        if (MapUtils.isEmpty(wmPoiAndBizMap)) {
            return "";
        }
        return deliveryContractPlatformAdapter.queryPerformanceSignDataWithRetry(wmPoiAndBizMap);
    }

    // 将外部签约数据组装至EcontractBatchBo
    public void assembleToBatchBo(String subData, EcontractBatchBo econtractBatchBo) throws EcontractException, IllegalAccessException {
        // 数据为空，则抛出异常
        if (StringUtils.isEmpty(subData)) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "发起电子签约-获取履约服务数据为空");
        }
        EcontractBatchDeliveryPerInfoBo batchPerInfoBo = JSONObject.parseObject(subData, EcontractBatchDeliveryPerInfoBo.class);
        // if (MapUtils.isEmpty(batchPerInfoBo.getBatchPerInfoMap())) {
        //     throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "发起电子签约-获取履约服务数据为空");
        // }
        log.info("DeliverySinglePerformancePDFHandler#assembleToBatchBo，batchPerInfoBo: {}", JSON.toJSONString(batchPerInfoBo));
        // key为门店ID
        Map<Long, EcontractSingleDeliveryPerInfoBo> batchPerInfoMap = batchPerInfoBo.getBatchPerInfoMap();
        // 获取当前履约服务费PDF信息
        StageBatchInfoBo stageBatchInfoBo = econtractBatchBo.getStageInfoBoList().stream()
                .filter(v -> TaskConstant.CREATE_PDF.equals(v.getStageName()))
                .findFirst().get();

        // 组装履约服务费合同信息
        List<PdfContentInfoBo> performancePdfInfoList = stageBatchInfoBo.getPdfContentInfoBoMap().get(EcontractPdfTypeEnum.PERFORMANCE_SERVICE_FEE.getName());
        if (CollectionUtils.isNotEmpty(performancePdfInfoList)) {
            for (PdfContentInfoBo performancePdfInfo : performancePdfInfoList) {
                Map<String, String> pdfMetaContent = performancePdfInfo.getPdfMetaContent();
                // key->门店 value->门店对应的履约信息，组装pdfMetaContent
                for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                    EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.NORMAL.getType());
                    Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                    pdfMetaContent.putAll(map);
                }
                // pdfBizContent
                List<Map<String, String>> pdfBizContent = performancePdfInfo.getPdfBizContent();
                if (CollectionUtils.isNotEmpty(pdfBizContent)) {
                    for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                        // 遍历pdfBizContent,找到对应门店的签约参数并添加履约服务费
                        if (pdfBizContent.stream().anyMatch(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId"))))) {
                            Map<String, String> perPoiMap = pdfBizContent.stream().filter(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId")))).findFirst().get();
                            EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.NORMAL.getType());
                            Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                            perPoiMap.putAll(map);
                        }
                    }
                }
            }
        }

        // 组装全城送信息
        List<PdfContentInfoBo> wholeCityPdfInfoList = stageBatchInfoBo.getPdfContentInfoBoMap().get(EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY.getName());
        if (CollectionUtils.isNotEmpty(wholeCityPdfInfoList)) {
            for (PdfContentInfoBo wholeCityPdfInfo : wholeCityPdfInfoList) {
                Map<String, String> pdfMetaContent = wholeCityPdfInfo.getPdfMetaContent();
                // key->门店 value->门店对应的履约信息，组装pdfMetaContent
                for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                    EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.WHOLE_CITY.getType());
                    Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                    pdfMetaContent.putAll(map);
                }
                // pdfBizContent
                List<Map<String, String>> pdfBizContent = wholeCityPdfInfo.getPdfBizContent();
                if (CollectionUtils.isNotEmpty(pdfBizContent)) {
                    for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                        // 遍历pdfBizContent,找到对应门店的签约参数并添加履约服务费
                        if (pdfBizContent.stream().anyMatch(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId"))))) {
                            Map<String, String> perPoiMap = pdfBizContent.stream().filter(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId")))).findFirst().get();
                            EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.WHOLE_CITY.getType());
                            Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                            perPoiMap.putAll(map);
                        }
                    }
                }
            }
        }


        // 组装聚合配信息
        List<PdfContentInfoBo> aggregationPdfInfoList = stageBatchInfoBo.getPdfContentInfoBoMap().get(EcontractPdfTypeEnum.DELIVERY_AGGREGATION.getName());
        if (CollectionUtils.isNotEmpty(aggregationPdfInfoList)) {
            for (PdfContentInfoBo aggregationPdfInfo : aggregationPdfInfoList) {
                Map<String, String> pdfMetaContent = aggregationPdfInfo.getPdfMetaContent();
                // key->门店 value->门店对应的履约信息，组装pdfMetaContent
                for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                    EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.AGGREGATION.getType());
                    Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                    pdfMetaContent.putAll(map);
                    PdfHandlerUtils.assembleRenameItems(pdfMetaContent, perInfoBo);
                }
                // pdfBizContent
                List<Map<String, String>> pdfBizContent = aggregationPdfInfo.getPdfBizContent();
                if (CollectionUtils.isNotEmpty(pdfBizContent)) {
                    for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                        // 遍历pdfBizContent,找到对应门店的签约参数并添加履约服务费
                        if (pdfBizContent.stream().anyMatch(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId"))))) {
                            Map<String, String> perPoiMap = pdfBizContent.stream().filter(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId")))).findFirst().get();
                            EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.AGGREGATION.getType());
                            Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                            perPoiMap.putAll(map);
                            PdfHandlerUtils.assembleRenameItems(perPoiMap, perInfoBo);
                        }
                    }
                }
            }
        }


        // 组装远距离信息
        List<PdfContentInfoBo> longDistancePdfInfoList = stageBatchInfoBo.getPdfContentInfoBoMap().get(EcontractPdfTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName());
        if (CollectionUtils.isNotEmpty(longDistancePdfInfoList)) {
            for (PdfContentInfoBo longDistancePdfInfo : longDistancePdfInfoList) {
                Map<String, String> pdfMetaContent = longDistancePdfInfo.getPdfMetaContent();
                // key->门店 value->门店对应的履约信息，组装pdfMetaContent
                for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                    EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.LONG_DISTANCE.getType());
                    Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                    pdfMetaContent.putAll(map);
                }
                // pdfBizContent
                List<Map<String, String>> pdfBizContent = longDistancePdfInfo.getPdfBizContent();
                if (CollectionUtils.isNotEmpty(pdfBizContent)) {
                    for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                        // 遍历pdfBizContent,找到对应门店的签约参数并添加履约服务费
                        if (pdfBizContent.stream().anyMatch(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId"))))) {
                            Map<String, String> perPoiMap = pdfBizContent.stream().filter(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId")))).findFirst().get();
                            EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.LONG_DISTANCE.getType());
                            Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                            perPoiMap.putAll(map);
                        }
                    }
                }
            }
        }
        // 组装履约折扣信息
        List<PdfContentInfoBo> performanceDiscountPdfInfoList =stageBatchInfoBo.getPdfContentInfoBoMap().get(EcontractPdfTypeEnum.DELIVERY_PER_DISCOUNT.getName());
        if (CollectionUtils.isNotEmpty(performanceDiscountPdfInfoList)) {
            for (PdfContentInfoBo performanceDiscountPdfInfo : performanceDiscountPdfInfoList) {
                Map<String, String> pdfMetaContent = performanceDiscountPdfInfo.getPdfMetaContent();
                // key->门店 value->门店对应的履约信息，组装pdfMetaContent
                for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                    EcontractDeliveryPerDiscountInfoBo perDiscountInfo = entry.getValue().getPerDiscountInfo();
                    Map<String, String> map = MapUtil.Object2Map(perDiscountInfo);
                    pdfMetaContent.putAll(map);
                }
                // pdfBizContent
                List<Map<String, String>> pdfBizContent = performanceDiscountPdfInfo.getPdfBizContent();
                if (CollectionUtils.isNotEmpty(pdfBizContent)) {
                    for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                        // 遍历pdfBizContent,找到对应门店的签约参数并添加履约服务费
                        if (pdfBizContent.stream().anyMatch(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId"))))) {
                            Map<String, String> perPoiMap = pdfBizContent.stream().filter(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId")))).findFirst().get();
                            EcontractDeliveryPerDiscountInfoBo perDiscountInfo = entry.getValue().getPerDiscountInfo();
                            Map<String, String> map = MapUtil.Object2Map(perDiscountInfo);
                            perPoiMap.putAll(map);
                        }
                    }
                }
            }
        }

        // 组装门店优惠信息
        List<PdfContentInfoBo> discountInfoBoList = stageBatchInfoBo.getPdfContentInfoBoMap().get(EcontractPdfTypeEnum.PREFERENTIAL_POLICY.getName());
        if (CollectionUtils.isNotEmpty(discountInfoBoList)) {
            for (PdfContentInfoBo discountInfo : discountInfoBoList) {
                Map<String, String> pdfMetaContent = discountInfo.getPdfMetaContent();
                // key->门店 value->门店对应的履约信息，组装pdfMetaContent
                for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                    EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.NORMAL.getType());
                    Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                    pdfMetaContent.putAll(map);
                }
                // pdfBizContent
                List<Map<String, String>> pdfBizContent = discountInfo.getPdfBizContent();
                if (CollectionUtils.isNotEmpty(pdfBizContent)) {
                    for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                        // 遍历pdfBizContent,找到对应门店的签约参数并添加履约服务费
                        if (pdfBizContent.stream().anyMatch(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId"))))) {
                            Map<String, String> perPoiMap = pdfBizContent.stream().filter(x -> x.get("wmPoiId") != null && entry.getKey().equals(Long.valueOf(x.get("wmPoiId")))).findFirst().get();
                            EcontractDeliveryPerInfoBo perInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.NORMAL.getType());
                            Map<String, String> map = MapUtil.Object2Map(perInfoBo);
                            perPoiMap.putAll(map);
                        }
                    }
                }
            }
        }
        // 这段代码和上面全城送的处理逻辑是重复的 加一个MCC判断 观察一段时间后删除代码
        if (!MccConfig.deleteDuplicatePerformanceHandle()) {
            log.info("DeliverySinglePerformancePDFHandler#assembleToBatchBo, stageBatchInfoBo: {}", JSON.toJSONString(stageBatchInfoBo));

            if (!stageBatchInfoBo.getPdfContentInfoBoMap().containsKey(EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY.getName())) {
                return;
            }
            PdfContentInfoBo wholeCityPdfInfo = stageBatchInfoBo.getPdfContentInfoBoMap().get(EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY.getName()).get(0);
            List<Map<String, String>> wholeCitPdfBizContent = wholeCityPdfInfo.getPdfBizContent();
            if (CollectionUtils.isEmpty(wholeCitPdfBizContent)) {
                log.error("DeliverySinglePerformancePDFHandler#assembleToBatchBo, PDF构建异常");
                return;
            }
            // TODO: 2023/10/25 优化这个逻辑
            for (Map.Entry<Long, EcontractSingleDeliveryPerInfoBo> entry : batchPerInfoMap.entrySet()) {
                EcontractDeliveryPerInfoBo wholeCityPerInfoBo = entry.getValue().getPerInfoMap().get(EcontractDeliveryTypeEnum.WHOLE_CITY.getType());
                Map<String, String> map = MapUtil.Object2Map(wholeCityPerInfoBo);
                wholeCitPdfBizContent.get(0).putAll(map);
            }
        }
        log.info("DeliverySinglePerformancePDFHandler#assembleToBatchBo, final stageBatchInfoBo: {}", JSON.toJSONString(stageBatchInfoBo));

    }
}
