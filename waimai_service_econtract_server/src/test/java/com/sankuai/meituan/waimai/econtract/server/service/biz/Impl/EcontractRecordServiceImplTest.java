package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/12/6 14:33
 */
@RunWith(MockitoJUnitRunner.class)
public class EcontractRecordServiceImplTest {

    @Mock
    private EcontractRecordEntityMapper econtractRecordEntityMapper;

    @Mock
    private EcontractMetricService econtractMetricService;

    @InjectMocks
    private EcontractRecordServiceImpl econtractRecordService;

    /**
     * 测试updateEcontractRecord方法，当MccConfig.updateEcontractRecordWithMysqlLockCheck()返回true时
     */
    @Test
    public void testUpdateEcontractRecord_WithMysqlLockCheckTrue() {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::updateEcontractRecordWithMysqlLockCheck).thenReturn(true);

            EcontractRecordEntity entity = buildEcontractRecordEntity();
            when(econtractRecordEntityMapper.updateNotFailRecordByPrimaryKeySelective(entity)).thenReturn(1);
            // 执行测试方法
            econtractRecordService.updateEcontractRecord(entity);

            // 验证updateNotFailRecordByPrimaryKeySelective方法被调用
            verify(econtractRecordEntityMapper, times(1)).updateNotFailRecordByPrimaryKeySelective(any(EcontractRecordEntity.class));

            // 验证econtractMetricService.metricUpdateFailRecord没有被调用
            verify(econtractMetricService, never()).metricUpdateFailRecord(anyString());
        }
    }
    @Test(expected = EcontractException.class)
    public void testUpdateEcontractRecord_WithMysqlLockCheckFail() {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::updateEcontractRecordWithMysqlLockCheck).thenReturn(true);

            EcontractRecordEntity entity = buildEcontractRecordEntity();
            when(econtractRecordEntityMapper.updateNotFailRecordByPrimaryKeySelective(entity)).thenReturn(0);
            doNothing().when(econtractMetricService).metricUpdateFailRecord(entity.getRecordKey());

            econtractRecordService.updateEcontractRecord(entity);
            verify(econtractRecordEntityMapper, times(1)).updateNotFailRecordByPrimaryKeySelective(any(EcontractRecordEntity.class));
            verify(econtractMetricService, times(1)).metricUpdateFailRecord(anyString());
        }
    }

    @Test(expected = EcontractException.class)
    public void testUpdateEcontractRecord_QueryNoData() {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::updateEcontractRecordWithMysqlLockCheck).thenReturn(false);

            EcontractRecordEntity targetEntity = buildEcontractRecordEntity();

            when(econtractRecordEntityMapper.queryRecordByRecordKey(targetEntity.getRecordKey())).thenReturn(null);

            econtractRecordService.updateEcontractRecord(targetEntity);
            verify(econtractRecordEntityMapper, times(0)).updateNotFailRecordByPrimaryKeySelective(any(EcontractRecordEntity.class));
            verify(econtractMetricService, times(0)).metricUpdateFailRecord(anyString());
        }
    }

    /**
     * 测试updateEcontractRecord方法，当数据库状态为FAIL且尝试更新为非FAIL状态时
     */
    @Test(expected = EcontractException.class)
    public void testUpdateEcontractRecord_DbStateFailToNonFail() {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::updateEcontractRecordWithMysqlLockCheck).thenReturn(false);

            EcontractRecordEntity entity = buildEcontractRecordEntity();

            // 执行测试方法
            econtractRecordService.updateEcontractRecord(entity);
            verify(econtractRecordEntityMapper, times(1)).updateNotFailRecordByPrimaryKeySelective(any(EcontractRecordEntity.class));
            verify(econtractMetricService, times(1)).metricUpdateFailRecord(anyString());
        }
    }
    
    private EcontractRecordEntity buildEcontractRecordEntity() {
        EcontractRecordEntity entity = new EcontractRecordEntity();
        
        return entity;
    }

}
