package com.sankuai.meituan.waimai.econtrct.client.domain.api;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.io.Serializable;
import java.util.Map;

@ThriftStruct
public class ParamInfoBo implements Serializable {

    private static final long serialVersionUID = -6824653081885571566L;

    //模板名字
    private String templateName;

    //细节信息
    private Map<String, String> metaContent;

    @ThriftField(1)
    public String getTemplateName() {
        return templateName;
    }

    @ThriftField
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    @ThriftField(2)
    public Map<String, String> getMetaContent() {
        return metaContent;
    }

    @ThriftField
    public void setMetaContent(Map<String, String> metaContent) {
        this.metaContent = metaContent;
    }

    public ParamInfoBo() {
    }

    private ParamInfoBo(Builder builder) {
        templateName = builder.templateName;
        metaContent = builder.metaContent;
    }


    public static final class Builder {

        private String templateName;
        private Map<String, String> metaContent;

        public Builder() {
        }

        public Builder templateName(String val) {
            templateName = val;
            return this;
        }

        public Builder metaContent(Map<String, String> val) {
            metaContent = val;
            return this;
        }

        public ParamInfoBo build() {
            return new ParamInfoBo(this);
        }
    }
}
