package com.sankuai.meituan.waimai.econtrct.client.domain.config.signflow;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/6/5 17:04
 */
@ThriftStruct
public class SignFlowTemplateDeleteRequestDTO {

    @FieldDoc(description = "模板id")
    private Integer id;

    @FieldDoc(description = "操作人uid")
    private Integer operatorId;

    @ThriftField(1)
    public Integer getId() {
        return this.id;
    }

    @ThriftField
    public void setId(Integer id) {
        this.id = id;
    }

    @ThriftField(2)
    public Integer getOperatorId() {
        return this.operatorId;
    }

    @ThriftField
    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }
}
